package com.lz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lz.model.ZuulIPRegion;
import com.lz.util.ZuulIpAddressUtil;
import com.netflix.zuul.ZuulFilter;
import com.netflix.zuul.context.RequestContext;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.security.oauth2.common.OAuth2AccessToken;

import javax.annotation.Resource;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public class AccessFilter extends ZuulFilter  {

    private static Logger log = LoggerFactory.getLogger(AccessFilter.class);

    private static final String REQUEST_CHECK = "REQUEST_CHECK";

     List<String> whiteIPList = Arrays.asList(
            "*************", "************", "*************", "*************", "*************", "*************");

    private static final StringRedisSerializer STRING_SERIALIZER = new StringRedisSerializer();

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public String filterType() {
        return "pre";
    }

    @Override
    public int filterOrder() {
        return 0;
    }

    @Override
    public boolean shouldFilter() {
        return true;
    }

    @Override
    public Object run() {
        RequestContext ctx = RequestContext.getCurrentContext();
        HttpServletRequest req = ctx.getRequest();
        log.info("**************************请求开始**************************");
        log.info("REQUEST:: " + req.getScheme() + " " + req.getRemoteAddr() + ":" + req.getRemotePort());
        log.info("REQUEST:: " + req.getScheme() + " " + req.getRemoteAddr() + ":" + req.getRemotePort());
        StringBuilder params = new StringBuilder("?");
        Enumeration<String> names = req.getParameterNames();
        if( req.getMethod().equals("GET") || req.getMethod().equals("POST")) {
            while (names.hasMoreElements()) {
                String name = (String) names.nextElement();
                params.append(name);
                params.append("=");
                params.append(req.getParameter(name));
                params.append("&");
            }
        }
        if (params.length() > 0) {
            params.delete(params.length()-1, params.length());
        }
        log.info("REQUEST:: > " + req.getMethod() + " " + req.getRequestURI() + params + " " + req.getProtocol());
        Enumeration<String> headers = req.getHeaderNames();
        boolean multipart = false;
        String[] ips = new String[]{};
        String sign = null;
        while (headers.hasMoreElements()) {
            String name = headers.nextElement();
            String value = req.getHeader(name);
            if (value.startsWith("multipart/form-data")){
                multipart = true;
            }
            log.info("REQUEST:: > " + name + ":" + value);
            if (name.equals("x-forwarded-for")) {
                ips = value.split(", ");
            }
            if (name.equals("x-sign")) {
                sign = value;
            }
        }
        if (!ctx.isChunkedRequestBody()) {
            ServletInputStream inp = null;
            try {
                inp = ctx.getRequest().getInputStream();
                String body = null;
                if (inp != null) {
                    body = IOUtils.toString(inp);
                    if (multipart){
                        log.info("REQUEST:: > " + "REQUESTBODY IS INPUTSTREAM");
                    }else {
                        log.info("REQUEST:: > " + body);
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        log.info("**************************请求结束**************************");
        if (req.getRequestURI().contains("sendMessageCodeByRegister")
                || req.getRequestURI().contains("sendMessageByLogin")
                || req.getRequestURI().contains("noLoginSendMessageEditPassword")
                || req.getRequestURI().contains("sendMessageByFindPassword")
                || req.getRequestURI().contains("driverChangesPhoneVerificationCode")
                || req.getRequestURI().contains("sendMessageByAddHxyhCard")
                || req.getRequestURI().contains("loginPhoneVerification")
                || req.getRequestURI().contains("sendMessageByUpdatePassword")
                || req.getRequestURI().contains("sendMessageBySetPayPassword")
                || req.getRequestURI().contains("login/smsCode")
                || req.getRequestURI().contains("sendDeleteOrderSms")
                || req.getRequestURI().contains("sendMessageByTiXian")
                || req.getRequestURI().contains("sendMessageByRegister")
        ) {

            try {
                if (null == sign || StringUtils.isBlank(sign)) {
                    log.info("验签内容为空");
                    responseSuccess(ctx, "无法访问");
                    return false;
                }
                JSONObject jsonObject = JSONObject.parseObject(sign);
                Object data = jsonObject.get("data");
                String type = jsonObject.get("type").toString();
                String uuid = null == jsonObject.get("uuid") ? "" : jsonObject.get("uuid").toString();
                String encrypt;
                if ("IOS".equals(type)) {
                    encrypt = RSAUtilProZuul.decryptIOS(data.toString(), RSAUtilProZuul.PRI_KEY);
                    encrypt = URLDecoder.decode(encrypt, "UTF-8");
                } else if ("PC".equals(type) || "H5".equals(type)) {
                    encrypt = RSAUtilProZuul.decryptPC(data.toString(), RSAUtilProZuul.PRI_KEY);
                    encrypt = URLDecoder.decode(encrypt, "UTF-8");
                } else {
                    // 带分段加密
                    encrypt = RSAUtilProZuul.decrypt(data.toString(), RSAUtilProZuul.PRI_KEY);
                }
                log.info("验签内容为：{}", encrypt);
                JSONObject encryptJson = JSONObject.parseObject(encrypt);
                if (null == encryptJson.get("timestamp")) {
                    log.info("验签时间为空");
                    responseSuccess(ctx, "无法访问");
                }
                if (!type.equals("IOS") && !type.equals("android")) {
                    if (null == encryptJson.get("uuid")) {
                        log.info("验签uuid为空");
                        responseSuccess(ctx, "无法访问");
                    }
                    if (!uuid.equals(encryptJson.get("uuid").toString())) {
                        log.info("验签uuid不匹配");
                        responseSuccess(ctx, "无法访问");
                    }
                    String uuidKey = "UUID:" + type + ":" + encryptJson.get("uuid").toString();
                    Boolean b = redisTemplate.hasKey(uuidKey);
                    if (!b) {
                        redisTemplate.opsForValue().set(uuidKey, uuid,1, TimeUnit.DAYS);
                    } else {
                        log.info("验签uuid已存在");
                        responseSuccess(ctx, "无法访问");
                    }
                }
                long time = new Date().getTime() / 1000;
                long timeDelay = 60;
                long clientTime = Long.parseLong(encryptJson.get("timestamp").toString()) / 1000;
                if (clientTime > time + timeDelay || clientTime < time - timeDelay) {
                    log.info("验签时间不匹配");
                    responseSuccess(ctx, "无法访问");
                }
            } catch (Exception e) {
                log.error("解密失败", e);
                responseSuccess(ctx, "无法访问");
            }
        }
        //log.info("send {} request to {}", request.getMethod(), request.getRequestURL().toString());
        //这里return的值没有意义，zuul框架没有使用该返回值
        //update by xyz 2019.6.3 启用zuul的token验证，不在各个服务内部单独认证，由zuul统一验证
        //网商回调不能拦截，账号密码登录、短信验证码登录、发送登录验证码不能拦截,分布式事务
        if (req.getRequestURI().contains("/tx")
                || req.getRequestURI().contains("/oauth")
                || req.getRequestURI().contains("/sms/login")
                || req.getRequestURI().contains("/app/login/smsCode")
                || req.getRequestURI().contains("logout")
                || req.getRequestURI().contains("/notify/callback")
                || req.getRequestURI().contains("/base/notify")
                || req.getRequestURI().contains("/base/balancePay")
                || req.getRequestURI().contains("/base/trade/message")
                || req.getRequestURI().contains("/base/accountServiceFeePay")
                || req.getRequestURI().contains("/wxLogin/sendMessageByFindPassword")
                || req.getRequestURI().contains("/wxLogin/sendMessageByRegister")
                || req.getRequestURI().contains("/wxLogin/sendMessageByLogin")
                || req.getRequestURI().contains("/wxLogin/register")
                || req.getRequestURI().contains("/wxLogin/changePassword")
                || req.getRequestURI().contains("/fastdfs")
                || req.getRequestURI().contains("/oss")
                || req.getRequestURI().contains("/tOrderInfo/exportExcel")
                || req.getRequestURI().contains("/wxLogin/")
                || req.getRequestURI().contains("/wxBank/")
                || req.getRequestURI().contains("/wxCar/")
                || req.getRequestURI().contains("/wxLogin/sendMessageByAddCard")
                || req.getRequestURI().contains("/wxLogin/sendMessageByAddHxyhCard")
                || req.getRequestURI().contains("/goodsSource/wxQRWaybillInfo")//微信查货源
                || req.getRequestURI().contains("/goodsSource/selectProjectByCode")
                || req.getRequestURI().contains("/goodsSource/selectByProjectId")
                || req.getRequestURI().contains("/noLoginSendMessageEditPassword")
                || req.getRequestURI().contains("/noLoginChangePassword")
                || req.getRequestURI().contains("/deleteCachePms")
                || req.getRequestURI().contains("/goodsSource/findGoodsSource")
                || req.getRequestURI().contains("/goodsSource/findGoodsType")
                || req.getRequestURI().contains("/goodsSource/cityTree")
                || req.getRequestURI().contains("/login/phoneVerification")
                || req.getRequestURI().contains("/login/phoneVerificationCode")
                || req.getRequestURI().contains("/login/updateLoginPhone")
                || req.getRequestURI().contains("/login/loginPhoneVerification")
                || req.getRequestURI().contains("/wxLogin/IdcardBeOverdue")
                || req.getRequestURI().contains("/wxLogin/ifAgreement")
                || req.getRequestURI().contains("/wxLogin/updateIfAgreement")
                || req.getRequestURI().contains("/toolbox/getIdWork")
                || req.getRequestURI().contains("/trajectoryApi/idCardLicenseV2")
                || req.getRequestURI().contains("/trajectoryApi/drivingLicenseOCRV2")
                || req.getRequestURI().contains("/contract5GqApi/getTokenResp")
                || req.getRequestURI().contains("/contract5GqApi/getTokenString")
                || req.getRequestURI().contains("/base/carrierBalanceCompanyCharge")
                || req.getRequestURI().contains("/base/txTransferToCarrier")
                || req.getRequestURI().contains("/api/sendMessageByUpdatePassword")
                || req.getRequestURI().contains("/api/forUpdatePassword")
                || req.getRequestURI().contains("/hxyh/notify")
                || req.getRequestURI().contains("/hxyh/trade/message")
                || req.getRequestURI().contains("/hxyh/notifyCompany")
                || req.getRequestURI().contains("/poundRoomApi/queryOrderTrajectory")
                || req.getRequestURI().contains("/poundRoomApi/queryOrderTrajectoryNew")
                || req.getRequestURI().contains("/poundRoomApi/receiveOrderDischargeSingIn")
                || req.getRequestURI().contains("/wxAndAppLogin/wxAndAppReg")
                || req.getRequestURI().contains("/getUserType")
                || req.getRequestURI().contains("/getUserTypeByPhone")
                || req.getRequestURI().contains("/consumer/startAll")
                || req.getRequestURI().contains("/consumer/shutdownPaymentAll")
                || req.getRequestURI().contains("/consumer/shutdownAll")
                || req.getRequestURI().contains("/wxLogin/communication/callback")
                || req.getRequestURI().contains("/payment/callback/trade/message")
                || req.getRequestURI().contains("/sendMessageCodeByRegister")
                || req.getRequestURI().contains("/pcd/trajectory")
                || req.getRequestURI().contains("/api/tOrderMaintenanceRecords/downloadUpdateOrderCaptainInfoExcel")
                || req.getRequestURI().contains("/insurance/getPolicyOfInsurance")
                || req.getRequestURI().contains("/thirdParty/goodsSource/insert")
                || req.getRequestURI().contains("/thirdParty/goodsSource/update")
                || req.getRequestURI().contains("/thirdParty/order/planOrder")
                || req.getRequestURI().contains("/thirdParty/order/loadingSingIn")
                || req.getRequestURI().contains("/thirdParty/order/dischargeSingIn")
                || req.getRequestURI().contains("/thirdParty/order/receiveOrder")

//                ||req.getRequestURI().contains("/lgt-app-complaints")

        ) {

            if (req.getRequestURI().contains("/oauth") || req.getRequestURI().contains("/wxLogin/register")) {
                if (ips.length > 0) {
                    ZuulIPRegion ipRegion = ZuulIpAddressUtil.getIPRegion(ips[0]);
                    if (!"中国".equals(ipRegion.getCountry())) {
                        responseSuccess(ctx, "无法访问");
                    }
                }
            }

            if (req.getRequestURI().contains("/oauth")
                    || req.getRequestURI().contains("/sms/login")) {
                try {
                    String[] paramSplit = params.toString().split("&");
                    List<String> loginTypes = Arrays.stream(paramSplit).filter(param -> param.equals("loginType")).collect(Collectors.toList());
                    List<String> usernames = Arrays.stream(paramSplit).filter(param -> param.contains("username")).collect(Collectors.toList());
                    if (!loginTypes.isEmpty()) {
                        String loginType = loginTypes.get(0);
                        String[] split = loginType.split("=");
                        if (split.length > 1) {
                            // 业务部登录
                            if (split[1].equals("business")) {
                                String userType = getUserType(usernames.get(0).split("business")[1]);
                                // 运营角色只能在公司访问
                                if (null == userType || StringUtils.isBlank(userType)) {
                                    if (!whiteIPList.contains(ips[0])) {
                                        responseSuccess(ctx, "无法访问");
                                    }
                                }
                            }
                        }
                    } else {
                        for (String param : paramSplit) {
                            if (param.contains("username") || param.contains("phone")) {
                                String[] usernameSplit = param.split("=");
                                if (usernameSplit.length > 1) {
                                    String userType = getUserType(usernameSplit[1]);
                                    // 运营角色只能在公司访问
                                    if (null == userType || StringUtils.isBlank(userType)) {
                                        if (!whiteIPList.contains(ips[0])) {
                                            responseSuccess(ctx, "无法访问");
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (req.getRequestURI().contains("/addBankCard")
                    || req.getRequestURI().contains("/delBankCard")) {
                if (!checkPCRequest(sign, req, ctx)) {
                    return null;
                }
            }
            return null;
        }  else {
            if (!checkPCRequest(sign, req, ctx)) {
                return null;
            }
        }
        String token = req.getHeader("Authorization");
        if (!StringUtils.isEmpty(token)) {
            OAuth2AccessToken tokenFromRedis = getTokenFromRedis(token);
            if(tokenFromRedis == null) {
                dealWithToken(ctx,"无效的token");
            } else if(tokenFromRedis.isExpired()) {
                dealWithToken(ctx,"token过期");
            } else {
                if (ips.length > 0 && null != ips[0]) {
                    String userType = queryInfo(getToken(token));
                    if (null == userType || StringUtils.isBlank(userType)) {
                        if (!whiteIPList.contains(ips[0])) {
                            responseSuccess(ctx, "无法访问");
                        } else {
                            ctx.setSendZuulResponse(true); //将请求往后转发
                            ctx.setResponseStatusCode(200);
                        }
                    } else {
                        ctx.setSendZuulResponse(true); //将请求往后转发
                        ctx.setResponseStatusCode(200);
                    }
                }
            }
        } else {
            dealWithToken(ctx,"token为空");
        }
        return null;
    }
    private void dealWithToken(RequestContext ctx,String msg){
        HttpServletResponse response = ctx.getResponse();
        response.setHeader("Content-Type", "application/json;charset=UTF-8");
        ctx.setSendZuulResponse(false); //终止转发，返回响应报文
        ctx.setResponseStatusCode(400);
        Map<String,String> responseMap=new HashMap<String,String>();
        responseMap.put("errorcode", "400");
        responseMap.put("errormsg", msg);
        ctx.setResponseBody(JSON.toJSONString(responseMap));
    }

    private void dealStatusWithToken(RequestContext ctx, String msg, Integer status){
        HttpServletResponse response = ctx.getResponse();
        response.setHeader("Content-Type", "application/json;charset=UTF-8");
        ctx.setSendZuulResponse(false); //终止转发，返回响应报文
        ctx.setResponseStatusCode(status);
        Map<String,String> responseMap=new HashMap<String,String>();
        responseMap.put("errorcode", String.valueOf(status));
        responseMap.put("errormsg", msg);
        ctx.setResponseBody(JSON.toJSONString(responseMap));
    }

    private void responseSuccess(RequestContext ctx, String msg) {
        HttpServletResponse response = ctx.getResponse();
        response.setHeader("Content-Type", "application/json;charset=UTF-8");
        ctx.setSendZuulResponse(false); //终止转发，返回响应报文
        ctx.setResponseStatusCode(200);
        Map<String,String> responseMap=new HashMap<>();
        responseMap.put("msg", msg);
        responseMap.put("code", "error");
        responseMap.put("data", null);
        responseMap.put("count", null);
        ctx.setResponseBody(JSON.toJSONString(responseMap));
    }

    private OAuth2AccessToken getTokenFromRedis(String tokenValue){
        StringRedisSerializer STRING_SERIALIZER = new StringRedisSerializer();
        if (tokenValue.contains("%20")) {
            tokenValue = tokenValue.replace("%20", " ");
            RequestContext requestContext = RequestContext.getCurrentContext();
            requestContext.addZuulRequestHeader("Authorization", tokenValue);
        }

        String[] tokenWithPrefix = tokenValue.split(" ");
        byte[] key = STRING_SERIALIZER.serialize("access:" + tokenWithPrefix[1]);
        byte[] bytes = null;
        RedisConnection conn = redisConnectionFactory.getConnection();

        try {
            bytes = conn.get(key);
        } finally {
            conn.close();
        }
        JdkSerializationRedisSerializer OBJECT_SERIALIZER = new JdkSerializationRedisSerializer();
        OAuth2AccessToken accessToken = (OAuth2AccessToken)OBJECT_SERIALIZER.deserialize(bytes);
        return accessToken;
    }

    private String getToken(String tokenValue){
        if (tokenValue.contains("%20")) {
            tokenValue = tokenValue.replace("%20", " ");
        }
        String[] tokenWithPrefix = tokenValue.split(" ");

        return tokenWithPrefix[1];
    }

    public String queryInfo(String token) {
        // 创建Httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString = "";
        try {
            URIBuilder uri = new URIBuilder("http://ms.lugangtong56.com/zuul-lgt/lgt-app-system/lgt-app-system/api/getUserType");
            // 创建Http Post请求
            HttpGet httpGet = new HttpGet(uri.build());
            //设置请求状态参数
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectionRequestTimeout(3000)
                    .setSocketTimeout(3000)
                    .setConnectTimeout(3000).build();
            httpGet.setConfig(requestConfig);
            httpGet.addHeader("Content-Type", "application/json;charset=utf-8");
            httpGet.addHeader("authorization", "Bearer " + token);

            // 执行http请求
            response = httpClient.execute(httpGet);
            int status = response.getStatusLine().getStatusCode();//获取返回状态值
            if (status == HttpStatus.SC_OK) {//请求成功
                HttpEntity httpEntity = response.getEntity();
                if(httpEntity != null) {
                    resultString = EntityUtils.toString(httpEntity, "utf-8");
                    log.info("用户类型：" + resultString);
                    EntityUtils.consume(httpEntity);//关闭资源
                    return resultString;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (response!=null){
                try {
                    response.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        System.out.println(resultString);
        return null;
    }

    public String queryInfoByPhone(String phone) {
        // 创建Httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString = "";
        try {
            URIBuilder uri = new URIBuilder("http://ms.lugangtong56.com/zuul-lgt/lgt-app-system/lgt-app-system/api/getUserTypeByPhone");
            //get请求带参数
            List<NameValuePair> list = new LinkedList<>();
            BasicNameValuePair username = new BasicNameValuePair("username", phone);
            list.add(username);
            uri.setParameters(list);
            // 创建Http Post请求
            HttpGet httpGet = new HttpGet(uri.build());

            //设置请求状态参数
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectionRequestTimeout(3000)
                    .setSocketTimeout(3000)
                    .setConnectTimeout(3000).build();
            httpGet.setConfig(requestConfig);
            httpGet.addHeader("Content-Type", "application/json;charset=utf-8");

            // 执行http请求
            response = httpClient.execute(httpGet);
            int status = response.getStatusLine().getStatusCode();//获取返回状态值
            if (status == HttpStatus.SC_OK) {//请求成功
                HttpEntity httpEntity = response.getEntity();
                if(httpEntity != null) {
                    resultString = EntityUtils.toString(httpEntity, "utf-8");
                    log.info("登录用户类型：" + resultString);
                    EntityUtils.consume(httpEntity);//关闭资源
                    return resultString;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (response!=null){
                try {
                    response.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        System.out.println(resultString);
        return null;
    }

    /**
     * 查询登录用户的userType
     * @param username 用户名
     * @return userType 用户类型
     */
    private String getUserType(String username) {
        username = username.replace("business", "");
        // 查询redis是否存在userType
        String valueForKey = getValueForKey("USERTYPE" + username);
        if (null == valueForKey || StringUtils.isBlank(valueForKey)) {
            // 根据手机号查询数据库中的userType
            return queryInfoByPhone(username);
        } else {
            return valueForKey;
        }
    }

    /**
     * 判断是否登录
     * @param username 用户名
     * @return 是否登录
     */
    private boolean isLogin(String username) {
        String token = getValueForKey(username);
        if (StringUtils.isNotBlank(token)) {
            return true;
        }
        return false;
    }

    /**
     * 获取redis中值
     * @param redisKey redis中的key
     * @return redis中的值
     */
    private String getValueForKey(String redisKey) {
        RedisConnection conn = redisConnectionFactory.getConnection();
        byte[] bytes;
        try {
            byte[] key = STRING_SERIALIZER.serialize(redisKey);
            bytes = conn.get(key);
        } finally {
            conn.close();
        }
        JdkSerializationRedisSerializer OBJECT_SERIALIZER = new JdkSerializationRedisSerializer();
        return (String)OBJECT_SERIALIZER.deserialize(bytes);
    }

    private boolean checkPCRequest(String sign, HttpServletRequest request, RequestContext ctx) {
        try {
            String valueForKey = getValueForKey(REQUEST_CHECK);
            log.info("valueForKey:{}", valueForKey);
            if (null == valueForKey || StringUtils.isBlank(valueForKey)) {
                if(!unCheckXSignPath(request.getRequestURI())) {
                    boolean isAndroidGET = false;
                    if (null == sign || StringUtils.isBlank(sign)) {
                        String header = request.getHeader("user-agent");
                        String method = request.getMethod();
                        if (method.equals("GET") && header.contains("okhttp")) {
                            isAndroidGET = true;
                        } else {
                            log.info("验签内容为空");
                            responseSuccess(ctx, "无法访问");
                            return false;
                        }
                    }
                    if (!isAndroidGET) {
                        JSONObject jsonObject = JSONObject.parseObject(sign);
                        Object data = jsonObject.get("data");
                        String type = null == jsonObject.get("type") ? "" : jsonObject.get("type").toString();
                        String uuid = null == jsonObject.get("uuid") ? "" : jsonObject.get("uuid").toString();
                        String decryptUrl = null == jsonObject.get("url") ? "" : jsonObject.get("url").toString();
                        String encrypt;
                        if ("IOS".equals(type)) {
                             // encrypt = RSAUtilProZuul.decryptIOS(data.toString(), RSAUtilProZuul.PRI_KEY);
                            // encrypt = URLDecoder.decode(encrypt, "UTF-8");
                            return true;
                        } else if ("PC".equals(type) || "H5".equals(type)) {
                            encrypt = RSAUtilProZuul.decryptPC(data.toString(), RSAUtilProZuul.PRI_KEY);
                            encrypt = URLDecoder.decode(encrypt, "UTF-8");
                        } else {
                            // 带分段加密
                            // encrypt = RSAUtilProZuul.decrypt(data.toString(), RSAUtilProZuul.PRI_KEY);
                            return true;
                        }
                        log.info("验签内容为：{}", encrypt);
                        JSONObject encryptJson = JSONObject.parseObject(encrypt);
                        if (null == encryptJson.get("uuid")) {
                            log.info("验签uuid为空");
                            responseSuccess(ctx, "无法访问");
                            return false;
                        }
                        if (!uuid.equals(encryptJson.get("uuid").toString())) {
                            log.info("验签uuid不匹配");
                            responseSuccess(ctx, "无法访问");
                            return false;
                        }
                        String uuidKey = "UUID:" + type + ":" + encryptJson.get("uuid").toString();
                        Boolean b = redisTemplate.hasKey(uuidKey);
                        if (!b) {
                            redisTemplate.opsForValue().set(uuidKey, uuid,1, TimeUnit.DAYS);
                        } else {
                            log.info("验签uuid已存在");
                            responseSuccess(ctx, "无法访问");
                            return false;
                        }
                        decryptUrl = !decryptUrl.startsWith("/") ? "/" + decryptUrl : decryptUrl;
                        decryptUrl = decryptUrl.split("\\?")[0];
                        String requestURI = request.getRequestURI().split("\\?")[0];
                        if (!requestURI.equals(decryptUrl)) {
                            log.info("验签url不匹配");
                            log.info("验签url不匹配，实际请求地址：{}，验签地址：{}", requestURI, decryptUrl);
                            if ("PC".equals(type)) {
                                log.info("验签终端为PC，请求地址：{}，验签地址：{}，验签地址不匹配，实际请求地址：{}", requestURI, decryptUrl, requestURI);
                                responseSuccess(ctx, "无法访问");
                                return false;
                            }
                        }
                        String token = null == encryptJson.getString("token") ? "" : encryptJson.getString("token");
                        token = token.replace("Bearer ", "");
                        token = token.replace("bearer ", "");
                        String authorization = request.getHeader("Authorization");
                        authorization = authorization.replace("Bearer ", "");
                        authorization = authorization.replace("bearer ", "");
                        if (!authorization.equals(token)) {
                            log.info("验签token不匹配");
                            responseSuccess(ctx, "无法访问");
                            return false;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("解密失败", e);
        }
        return true;
    }

    private boolean unCheckXSignPath(String path) {
        return path.contains("tOrderMaintenanceRecords")
                || path.contains("sendMessageCodeByRegister")
                || path.contains("sendMessageByLogin")
                || path.contains("noLoginSendMessageEditPassword")
                || path.contains("sendMessageByFindPassword")
                || path.contains("driverChangesPhoneVerificationCode")
                || path.contains("sendMessageByAddHxyhCard")
                || path.contains("loginPhoneVerification")
                || path.contains("sendMessageByUpdatePassword")
                || path.contains("sendMessageBySetPayPassword")
                || path.contains("login/smsCode")
                || path.contains("sendDeleteOrderSms")
                || path.contains("sendMessageByTiXian")
                || path.contains("project/findList");
    }

}
