eureka.client.serviceUrl.defaultZone=*********************************************/eureka/
#eureka.instance.prefer-ip-address=true
#eureka.instance.ip-address=*************
#eureka.instance.instance-id=${spring.cloud.client.ipAddress}:${server.port}
spring.cloud.config.discovery.serviceId=lgt-app-config
spring.cloud.config.discovery.enabled=true
spring.cloud.config.profile=test
spring.cloud.config.label=master
security.basic.enabled=false
#hystrix.command.default.execution.timeout.enabled
#hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=60000
#超时时间
#ribbon.ReadTimeout=30000
#ribbon.SocketTimeout=30000
#ribbon.MaxAutoRetries=0
ribbon.eureka.enabled=true
zuul.host.socket-timeout-millis=30000
zuul.host.connect-timeout-millis=30000

spring.rabbitmq.host=************
spring.rabbitmq.port=5672
spring.rabbitmq.username=root
spring.rabbitmq.password=hebei2018root@.COM

spring.redis.host=************
spring.redis.port=6379
spring.redis.password=hebei2018root@.COM
spring.redis.timeout=5000
spring.redis.jedis.pool.max-idle=500
spring.redis.jedis.pool.max-wait=8
server.tomcat.accesslog.buffered=true
server.tomcat.accesslog.directory=/usr/local/src/log/access
server.tomcat.accesslog.enabled=true
server.tomcat.accesslog.file-date-format=.yyyy-MM-dd
server.tomcat.accesslog.pattern=common
server.tomcat.accesslog.prefix=access_log
server.tomcat.accesslog.rename-on-rotate=false
server.tomcat.accesslog.request-attributes-enabled=false
server.tomcat.accesslog.rotate=true
server.tomcat.accesslog.suffix=.log
server.tomcat.basedir = ./temp