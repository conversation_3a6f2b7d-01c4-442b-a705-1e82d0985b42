<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TWalletMapper">
    <select id="selectParentWalletByCarrierRel" parameterType="com.lz.vo.WalletVO" resultType="com.lz.model.TWallet">
    select *
    from t_wallet
    where carrier_enduser_company_id = #{carrierEnduserCompanyId}
    and datasource = #{datasource}
      <if test="purseCategory != null">
          and purse_category = #{purseCategory}
      </if>
    and company_project_id is null
  </select>

    <select id="selectWalletByCarrier" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select *
    from t_wallet wa
    LEFT JOIN t_carrier_enduser_company_rel cecr  ON wa.carrier_enduser_company_id = cecr.id AND wa.datasource = cecr.datasouce
    where cecr.carrier_id = #{id} and cecr.enduser_company_id is null and wa.purse_category = 'PCARRIER' and wa.enable = false
     AND wa.company_project_id IS NULL AND pid IS NULL
  </select>

    <select id="selectWalletByUser" resultMap="BaseResultMap">
    SELECT *
    FROM t_wallet wa
    LEFT JOIN t_carrier_enduser_company_rel cecr  ON wa.carrier_enduser_company_id = cecr.id AND wa.datasource = cecr.datasouce
    WHERE cecr.carrier_id = #{carrierId} AND cecr.enduser_company_id = #{userId} AND cecr.datasouce = #{userType,jdbcType=VARCHAR} AND wa.purse_category = #{walletType,jdbcType=VARCHAR}
     AND wa.company_project_id IS NULL AND wa.pid IS NULL
  </select>
    <select id="selectWalletByUserDriver" resultMap="BaseResultMap">
    SELECT *
    FROM t_wallet wa
    LEFT JOIN t_carrier_enduser_company_rel cecr  ON wa.carrier_enduser_company_id = cecr.id AND wa.datasource = cecr.datasouce
    WHERE cecr.carrier_id = #{carrierId} AND cecr.enduser_company_id = #{enduser_company_id} AND cecr.datasouce = #{userType,jdbcType=VARCHAR} AND wa.purse_category = #{walletType,jdbcType=VARCHAR}
     AND wa.company_project_id IS NULL AND wa.pid IS NULL
  </select>

    <select id="selectWalletByCompany" resultMap="BaseResultMap">
    SELECT *
    FROM t_wallet wa
    LEFT JOIN t_carrier_enduser_company_rel cecr  ON wa.carrier_enduser_company_id = cecr.id AND wa.datasource = cecr.datasouce
    WHERE cecr.carrier_id = #{carrierId} AND cecr.enduser_company_id = #{companyId} AND cecr.datasouce = #{userType,jdbcType=VARCHAR} AND wa.purse_category = #{walletType,jdbcType=VARCHAR}
       AND wa.company_project_id IS NULL AND wa.pid IS NULL
  </select>

    <select id="selectWalletByCompanyProject" resultMap="BaseResultMap">
    SELECT *
    FROM t_wallet wa
    WHERE wa.company_project_id = #{projectId,jdbcType=INTEGER} and pid = #{companyWalletId,jdbcType=INTEGER} and wa.purse_category = 'BCOMPANY'
  </select>

    <update id="freezeWallet">
    update t_wallet wa
           LEFT JOIN t_carrier_enduser_company_rel cecr  ON wa.carrier_enduser_company_id = cecr.id AND wa.datasource = cecr.datasouce
    set
          wa.update_time = now(),
          wa.account_balance = account_balance - #{totalFee,jdbcType=DECIMAL},
          wa.frozen_amount = frozen_amount + #{totalFee,jdbcType=DECIMAL}
    WHERE cecr.carrier_id = #{carrierId} AND cecr.enduser_company_id = #{companyId} AND cecr.datasouce = #{userType,jdbcType=VARCHAR} AND wa.purse_category = #{walletType,jdbcType=VARCHAR}
                    AND wa.company_project_id IS NULL AND wa.pid IS NULL
  </update>

    <update id="freezeProjectWallet">
        update t_wallet wa
               LEFT JOIN t_carrier_enduser_company_rel cecr  ON wa.carrier_enduser_company_id = cecr.id AND wa.datasource = cecr.datasouce
        set
          wa.update_time = now(),
          wa.frozen_amount = frozen_amount + #{totalFee,jdbcType=DECIMAL}
        WHERE cecr.carrier_id = #{carrierId} AND cecr.enduser_company_id = #{companyId} AND cecr.datasouce = #{userType,jdbcType=VARCHAR} AND wa.purse_category = #{walletType,jdbcType=VARCHAR}
              AND wa.company_project_id = #{projectId} AND wa.pid IS not NULL
  </update>

    <update id="unfreezeWallet">
    update t_wallet wa
           LEFT JOIN t_carrier_enduser_company_rel cecr  ON wa.carrier_enduser_company_id = cecr.id AND wa.datasource = cecr.datasouce
    set
          wa.update_time = now(),
          wa.account_balance = account_balance + #{totalFee,jdbcType=DECIMAL},
          wa.frozen_amount = frozen_amount - #{totalFee,jdbcType=DECIMAL}
          WHERE cecr.carrier_id = #{carrierId} AND cecr.enduser_company_id = #{companyId} AND cecr.datasouce = #{userType,jdbcType=VARCHAR} AND wa.purse_category = #{walletType,jdbcType=VARCHAR}
                AND wa.company_project_id IS NULL AND wa.pid IS NULL
  </update>

    <update id="unfreezeProjectWallet">
    update t_wallet wa
           LEFT JOIN t_carrier_enduser_company_rel cecr  ON wa.carrier_enduser_company_id = cecr.id AND wa.datasource = cecr.datasouce
    set
          wa.update_time = now(),
          wa.frozen_amount = frozen_amount - #{totalFee,jdbcType=DECIMAL}
          WHERE cecr.carrier_id = #{carrierId} AND cecr.enduser_company_id = #{companyId} AND cecr.datasouce = #{userType,jdbcType=VARCHAR} AND wa.purse_category = #{walletType,jdbcType=VARCHAR}
                AND wa.company_project_id = #{projectId} AND wa.pid IS not NULL
  </update>

    <update id="batchUnfreeze" parameterType="com.lz.dto.OrderInfoTotalPriceDTO">
        <foreach collection="list" separator=";" item="item">
            update t_wallet wa
                   LEFT JOIN t_carrier_enduser_company_rel cecr ON wa.carrier_enduser_company_id = cecr.id AND wa.datasource = cecr.datasouce
            set
                wa.update_time = now(),
                wa.account_balance = account_balance + #{item.totalPrice,jdbcType=DECIMAL},
                wa.frozen_amount = frozen_amount - #{item.totalPrice,jdbcType=DECIMAL}
            WHERE cecr.carrier_id = #{item.carrierId}
            AND cecr.enduser_company_id = #{item.companyId}
            AND cecr.datasouce = #{item.userType,jdbcType=VARCHAR}
            AND wa.purse_category = #{item.walletType,jdbcType=VARCHAR}
            AND wa.company_project_id IS NULL AND wa.pid IS NULL
        </foreach>
    </update>

    <update id="batchUnfreezeProject" parameterType="com.lz.dto.OrderInfoTotalPriceDTO">
        <foreach collection="list" separator=";" item="item">
            update t_wallet wa
                   LEFT JOIN t_carrier_enduser_company_rel cecr ON wa.carrier_enduser_company_id = cecr.id AND wa.datasource = cecr.datasouce
            set
                wa.update_time = now(),
                wa.frozen_amount = frozen_amount - #{item.totalPrice,jdbcType=DECIMAL}
            WHERE cecr.carrier_id = #{item.carrierId}
            AND cecr.enduser_company_id = #{item.companyId}
            AND cecr.datasouce = #{item.userType,jdbcType=VARCHAR}
            AND wa.purse_category = #{item.walletType,jdbcType=VARCHAR}
            AND wa.company_project_id = #{item.projectId} AND wa.pid IS not NULL
        </foreach>
    </update>

    <update id="freezeByPageage">
        update t_wallet wa
               LEFT JOIN t_carrier_enduser_company_rel cecr  ON wa.carrier_enduser_company_id = cecr.id AND wa.datasource = cecr.datasouce
        set
              wa.update_time = now(),
              wa.account_balance = account_balance - #{totalFee,jdbcType=DECIMAL},
              wa.frozen_amount = frozen_amount + #{totalFee,jdbcType=DECIMAL}
        WHERE cecr.carrier_id = #{carrierId} AND cecr.enduser_company_id = #{companyId} AND cecr.datasouce = 'BD' AND wa.purse_category = 'BCOMPANY'
              AND wa.company_project_id IS NULL AND wa.pid IS NULL
  </update>

    <update id="freezeByPageageProject">
        update t_wallet wa
               LEFT JOIN t_carrier_enduser_company_rel cecr  ON wa.carrier_enduser_company_id = cecr.id AND wa.datasource = cecr.datasouce
        set
              wa.update_time = now(),
              wa.frozen_amount = frozen_amount + #{totalFee,jdbcType=DECIMAL}
          WHERE cecr.carrier_id = #{carrierId} AND cecr.enduser_company_id = #{companyId} AND cecr.datasouce = 'BD' AND wa.purse_category = 'BCOMPANY'
                AND wa.company_project_id = #{projectId} AND wa.pid IS not NULL
     </update>

    <update id="unfreezePayMoneyWallet">
        update t_wallet wa
               LEFT JOIN t_carrier_enduser_company_rel cecr  ON wa.carrier_enduser_company_id = cecr.id AND wa.datasource = cecr.datasouce
        set
              wa.update_time = now(),
              wa.frozen_amount = frozen_amount - #{totalFee,jdbcType=DECIMAL}
       WHERE cecr.carrier_id = #{carrierId} AND cecr.enduser_company_id = #{companyId} AND cecr.datasouce = #{userType,jdbcType=VARCHAR} AND wa.purse_category = #{walletType,jdbcType=VARCHAR}
             AND wa.company_project_id IS NULL AND wa.pid IS NULL
     </update>

    <update id="unfreezePayMoneyProjectWallet">
    update t_wallet wa
           LEFT JOIN t_carrier_enduser_company_rel cecr  ON wa.carrier_enduser_company_id = cecr.id AND wa.datasource = cecr.datasouce
    set
          wa.update_time = now(),
          wa.frozen_amount = frozen_amount - #{totalFee,jdbcType=DECIMAL}
    WHERE cecr.carrier_id = #{carrierId} AND cecr.enduser_company_id = #{companyId} AND cecr.datasouce = #{userType,jdbcType=VARCHAR} AND wa.purse_category = #{walletType,jdbcType=VARCHAR}
          AND wa.company_project_id = #{projectId} AND wa.pid IS not NULL
  </update>


    <update id="batchUnfreezePayMoney" parameterType="com.lz.dto.OrderInfoTotalPriceDTO">
        <foreach collection="list" separator=";" item="item">
            update t_wallet wa
                   LEFT JOIN t_carrier_enduser_company_rel cecr ON wa.carrier_enduser_company_id = cecr.id AND wa.datasource = cecr.datasouce
            set
                wa.update_time = now(),
                wa.entry_amount = entry_amount - #{item.totalPrice,jdbcType=DECIMAL}
            WHERE cecr.carrier_id = #{item.carrierId}
            AND cecr.enduser_company_id = #{item.companyId}
            AND cecr.datasouce = #{item.userType,jdbcType=VARCHAR}
            AND wa.purse_category = #{item.walletType,jdbcType=VARCHAR}
            AND wa.company_project_id IS NULL AND wa.pid IS NULL
        </foreach>
    </update>


    <!--<update id="batchUnfreezePayMoneyProject" parameterType="java.util.List">-->
    <!--<foreach collection="list" separator=";" item="item">-->
    <!--update t_wallet-->
    <!--set-->
    <!--frozen_amount = frozen_amount - #{totalFee,jdbcType=DECIMAL}-->
    <!--where id = (-->
    <!--SELECT wa.id-->
    <!--FROM t_wallet wa-->
    <!--LEFT JOIN t_carrier_enduser_company_rel cecr ON wa.carrier_enduser_company_id = cecr.id AND wa.datasource =-->
    <!--cecr.datasouce-->
    <!--WHERE cecr.carrier_id = #{carrierId} AND cecr.enduser_company_id = #{companyId} AND cecr.datasouce =-->
    <!--#{userType,jdbcType=VARCHAR} AND wa.purse_category = #{walletType,jdbcType=VARCHAR}-->
    <!--AND wa.company_project_id = #{projectId} AND wa.pid IS not NULL-->
    <!--)-->
    <!--</foreach>-->
    <!--</update>-->
    <select id="selectBalance" resultType="java.math.BigDecimal">
        <!--
               SELECT SUM(account_balance)
               FROM `t_wallet`
               WHERE carrier_enduser_company_id IN(SELECT id FROM `t_carrier_enduser_company_rel` WHERE enduser_company_id=#{enduserinfoid} AND datasouce="CD")
               AND datasource="CD" and purse_category = #{purseCategory}-->
        <!--
             SELECT
                IFNULL( ws.accountBalance, 0 ) + IFNULL(jw.accountBalance,0)  + IFNULL(hx.accountBalance,0) AS balance
             FROM
                 t_end_user_info e
                 LEFT JOIN (
             SELECT
                 IFNULL( SUM( w.account_balance ), 0 ) accountBalance,
                 c.enduser_company_id
             FROM
                 `t_wallet` w
                 LEFT JOIN ( SELECT * FROM t_carrier_enduser_company_rel WHERE datasouce = 'CD' ) c ON w.carrier_enduser_company_id = c.id
                 AND w.datasource = c.datasouce
             WHERE
                 w.datasource = "CD"
                 AND w.purse_category = #{purseCategory}
                 GROUP by c.enduser_company_id
                 ) ws ON e.id = ws.enduser_company_id
                 LEFT JOIN (
             SELECT
                 IFNULL( SUM( w.account_balance ), 0 ) accountBalance,
                 c.end_user_id
             FROM
                 t_jd_wallet w
                 LEFT JOIN t_end_user_open_role c ON w.open_role_id = c.id
             WHERE
                 w.ENABLE = 0
                 AND w.data_source = 'CD'
                 AND w.purse_category = #{purseCategory}
                  GROUP by c.id
                 ) jw ON e.id = jw.end_user_id
                LEFT JOIN (
            SELECT
                  IFNULL( SUM( w.account_balance ), 0 ) accountBalance,
                  c.id as end_user_id
            FROM
              t_zt_wallet w
            LEFT JOIN t_enduser_account tea ON tea.account_id = w.account_id
            LEFT JOIN t_end_user_info c ON tea.enduser_id = c.id and c.user_logistics_role like concat('%', #{logisticsRole}, '%' )
            WHERE
              w.ENABLE = 0
              and tea.ENABLE = 0
              and c.ENABLE = 0
              AND w.data_source = 'CD'
              AND w.purse_category = 'CDRIVER'
              GROUP by c.id
              ) hx ON e.id = hx.end_user_id
             WHERE
                 e.ENABLE = FALSE
                 AND e.id = #{enduserinfoid}
           -->
            SELECT
              IFNULL( SUM( w.account_balance ), 0 ) balance,
              c.id as end_user_id
            FROM
              t_zt_wallet w
            LEFT JOIN t_enduser_account tea ON tea.account_id = w.account_id
            LEFT JOIN t_end_user_info c ON tea.enduser_id = c.id and c.user_logistics_role like concat('%', #{logisticsRole}, '%' )
            WHERE
                  w.ENABLE = 0
                  AND tea.ENABLE = 0
                  AND c.ENABLE = 0
                  AND w.data_source = 'CD'
                  AND w.purse_category = 'CDRIVER'
                  AND c.id = #{enduserinfoid}
            GROUP by c.id
           </select>

           <update id="batchUnfreezePayMoneyProject" parameterType="com.lz.dto.OrderInfoTotalPriceDTO">
               <foreach collection="list" separator=";" item="item">
                   update t_wallet wa
                       LEFT JOIN t_carrier_enduser_company_rel cecr ON wa.carrier_enduser_company_id = cecr.id AND wa.datasource = cecr.datasouce
                   set
                       wa.update_time = now(),
                       wa.frozen_amount = frozen_amount - #{item.totalFee,jdbcType=DECIMAL}
                   WHERE cecr.carrier_id = #{item.carrierId}
                   AND cecr.enduser_company_id = #{item.companyId}
                   AND cecr.datasouce = #{item.userType,jdbcType=VARCHAR}
                   AND wa.purse_category = #{item.walletType,jdbcType=VARCHAR}
                   AND wa.company_project_id = #{item.projectId} AND wa.pid IS not NULL
               </foreach>
           </update>

           <update id="PayMoneyWallet">
               update t_wallet wa
                      LEFT JOIN t_carrier_enduser_company_rel cecr  ON wa.carrier_enduser_company_id = cecr.id AND wa.datasource = cecr.datasouce
               set
                     wa.update_time = NOW(),
                     account_exp = account_exp + #{totalFee,jdbcType=DECIMAL},
                     wa.entry_amount = entry_amount - #{totalFee,jdbcType=DECIMAL}
               WHERE cecr.carrier_id = #{carrierId} AND cecr.enduser_company_id = #{companyId} AND cecr.datasouce  = 'BD' AND wa.purse_category = 'BCOMPANY'
                     AND wa.company_project_id IS NULL AND wa.pid IS NULL
         </update>
           <select id="selectWalletByBank" parameterType="com.lz.model.TWallet" resultType="com.lz.model.TBankCard">
           select b.*
           from t_wallet w
           left join t_carrier_enduser_company_rel r on w.carrier_enduser_company_id = r.id
           left join t_enduser_account e on r.enduser_company_id = e.enduser_id
           left join t_bank_card b on b.account_id = e.account_id
           where w.id = #{id,jdbcType=INTEGER} and w.purse_category = #{purse_category,jdbcType=VARCHAR} and b.if_default = true
         </select>

           <update id="addMoney">
               update t_wallet
               set
                   update_time = now(),
                   account_balance = account_balance + #{totalFee,jdbcType=DECIMAL}
                   <!-- account_exp = account_exp + #{totalFee,jdbcType=DECIMAL} -->
        where carrier_enduser_company_id= #{relId } AND  datasource = #{userType,jdbcType=VARCHAR} AND purse_category = #{walletType,jdbcType=VARCHAR}
            AND company_project_id IS NULL AND pid IS NULL
    </update>

    <update id="freezeMoneyCD">
      update t_wallet
      set
          update_time = now(),
          account_balance = account_balance - #{totalFee,jdbcType=DECIMAL},
          frozen_amount = frozen_amount + #{totalFee,jdbcType=DECIMAL}
        where carrier_enduser_company_id= #{relId } AND  datasource = #{userType}
    </update>

    <update id="unfreezeMoneyCD">
        update t_wallet
        set
          update_time = now(),
          frozen_amount = frozen_amount - #{totalFee,jdbcType=DECIMAL}
        where carrier_enduser_company_id= #{relId } AND  datasource = #{userType}
    </update>

    <update id="subtractMoney">
      update t_wallet
        set
          update_time = now(),
          account_exp = account_exp + #{carriageFee,jdbcType=DECIMAL},
          withdraw_amount = withdraw_amount - #{carriageFee,jdbcType=DECIMAL}
        where id = #{walletId};
    </update>

    <select id="getCompanyWalletByRelId" resultMap="BaseResultMap">
      select * from t_wallet where carrier_enduser_company_id = #{relId} and datasource = 'BD' AND purse_category = 'BCOMPANY' AND company_project_id IS NULL AND pid IS NULL;
    </select>

    <update id="addMoneyById">
        update t_wallet
        set
          update_time = now(),
          account_balance = account_balance + #{carriageFee,jdbcType=DECIMAL},
          account_inc = account_inc + #{carriageFee,jdbcType=DECIMAL}
        where id = #{walletId};
    </update>
    <!-- C端提现失败 sangbin -->
    <update id="withdrawFail">
      update t_wallet
        set
          update_time = now(),
          account_balance = account_balance + #{carriageFee,jdbcType=DECIMAL},
          withdraw_amount = withdraw_amount - #{carriageFee,jdbcType=DECIMAL}
        where id = #{walletId};
    </update>
    <!-- C端发起提现 sangbin -->
    <update id="withdrawCD">
      update t_wallet
        set
          update_time = now(),
          account_balance = account_balance - #{carriageFee,jdbcType=DECIMAL},
          withdraw_amount = withdraw_amount + #{carriageFee,jdbcType=DECIMAL}
        where id = #{walletId};
    </update>

    <update id="addCwrzIng">
      update t_wallet
        set
          update_time = now(),
          frozen_amount = frozen_amount - #{money,jdbcType=DECIMAL},
          entry_amount = entry_amount + #{money,jdbcType=DECIMAL}
        where id = #{companyWalletId};
    </update>
    <update id="addCwrzIngProject">
      update t_wallet
        set
          update_time = now(),
          frozen_amount = frozen_amount - #{money,jdbcType=DECIMAL},
          entry_amount = entry_amount + #{money,jdbcType=DECIMAL}
        where id = #{companyProjectWalletId};
    </update>

    <update id="addCwrzIngZQ">
      update t_wallet
        set
          update_time = now(),
          account_balance = account_balance - #{money,jdbcType=DECIMAL},
          entry_amount = entry_amount + #{money,jdbcType=DECIMAL}
        where id = #{companyWalletId};
    </update>
    <update id="addCwrzIngProjectZQ">
      update t_wallet
        set
          update_time = now(),
          entry_amount = entry_amount + #{money,jdbcType=DECIMAL}
        where id = #{companyProjectWalletId};
    </update>

    <update id="companyPayCallBackFailXJ">
      update t_wallet
        set
          update_time = now(),
          frozen_amount = frozen_amount + #{totalFee,jdbcType=DECIMAL},
          entry_amount = entry_amount - #{totalFee,jdbcType=DECIMAL}
        where id = #{companyWalletId};
    </update>

    <update id="companyPayCallBackFailZQ">
      update t_wallet
        set
          update_time = now(),
          account_balance = account_balance + #{totalFee,jdbcType=DECIMAL},
          entry_amount = entry_amount - #{totalFee,jdbcType=DECIMAL}
        where id = #{companyWalletId};
    </update>

    <update id="companyProjectPayCallBackFailZQ">
      update t_wallet
        set
          update_time = now(),
          entry_amount = entry_amount - #{totalFee,jdbcType=DECIMAL}
        where id = #{companyProjecctWalletId};
    </update>

    <update id="companyPayCallBackSuccessXJ">
      update t_wallet
        set
          update_time = now(),
          account_exp = account_exp + #{totalFee,jdbcType=DECIMAL},
          entry_amount = entry_amount - #{totalFee,jdbcType=DECIMAL}
        where id = #{companyWalletId};
    </update>

    <update id="companyPayCallBackSuccessZQ">
      update t_wallet
        set
          update_time = now(),
          account_exp = account_exp + #{totalFee,jdbcType=DECIMAL},
          entry_amount = entry_amount - #{totalFee,jdbcType=DECIMAL}
        where id = #{companyWalletId};
    </update>

    <select id="pcCompanyCwDetail" parameterType="java.lang.Integer" resultType="java.util.HashMap">
        SELECT
        wa.id walletId, com.carrier_id carrierId,com.thrid_pary_sub_account account,ci.carrier_name carrierName,
        cpi.company_name companyName,com.enduser_company_id companyId,
        IFNULL(wa.account_balance,0) accountBalance,IFNULL(wa.frozen_amount,0) frozenAmount,
        IFNULL(wa.entry_amount,0) entryAmount,IFNULL(wa.withdraw_amount,0) withdrawAmount,cpi.company_name
        FROM (SELECT * FROM t_carrier_enduser_company_rel WHERE datasouce = "BD" AND enduser_company_id = #{companyId}) com
        LEFT JOIN t_wallet wa ON com.id = wa.carrier_enduser_company_id AND wa.datasource = com.datasouce
        LEFT JOIN t_carrier_info ci ON ci.id = com.carrier_id
        LEFT JOIN t_company_info cpi ON cpi.id = com.enduser_company_id
        WHERE wa.enable = FALSE AND wa.purse_category = 'BCOMPANY' AND wa.company_project_id IS NULL AND pid IS NULL
        order by carrierName desc
    </select>

    <select id="walletHj" parameterType="java.lang.Integer" resultType="java.util.HashMap">
        SELECT
            cai.carrier_name carrierName,
			cai.id carrierId,
            ci.company_name companyName,
            item.item_value tradeType,
            wc.trade_type tradeTypeCode,
            sum(wc.amount) amount
        FROM t_wallet_change_log wc
            LEFT JOIN t_wallet wa ON wa.id = wc.wallet_id
            LEFT JOIN t_carrier_enduser_company_rel care ON care.id = wa.carrier_enduser_company_id
            LEFT JOIN t_company_info ci ON ci.id = care.enduser_company_id
            LEFT JOIN t_carrier_info cai ON cai.id = care.carrier_id
            LEFT JOIN t_dic_cat_item item ON item.item_code = wc.trade_type
        WHERE
            care.enduser_company_id = #{companyId}
            AND wc.enable = 0
            AND wc.wallet_type = 'BCOMPANY'
        GROUP BY  care.carrier_id,tradeTypeCode,tradeType,companyName
    </select>


    <select id="selectByEndUserId" resultType="java.util.HashMap">
        <!--
        SELECT SUM(withdraw_amount) withdraw_amount,SUM(account_balance) account_balance
        FROM t_carrier_enduser_company_rel ce
        LEFT JOIN `t_wallet` t  ON t.`carrier_enduser_company_id`=ce.`id`
        WHERE ce.enduser_company_id=#{endUserId} AND ce.`datasouce` = 'CD'  AND t.ENABLE=0 and t.purse_category = #{purseCategory}
        -->

       SELECT
           IFNULL(ws.accountBalance,0) + IFNULL(jw.accountBalance,0) AS account_balance,
           IFNULL(ws.withdrawAmount,0) + IFNULL(jw.withdrawAmount,0) AS withdraw_amount
       FROM
           t_end_user_info e
           LEFT JOIN (
       SELECT
           IFNULL( SUM( w.account_balance ), 0 ) accountBalance,
           IFNULL( SUM( w.withdraw_amount ), 0 ) withdrawAmount,
           c.enduser_company_id
       FROM
           `t_wallet` w
           LEFT JOIN ( SELECT * FROM t_carrier_enduser_company_rel WHERE datasouce = 'CD' ) c ON w.carrier_enduser_company_id = c.id
           AND w.datasource = c.datasouce
       WHERE
           w.datasource = "CD"
           AND w.purse_category = #{purseCategory}
           GROUP by c.enduser_company_id
           ) ws ON e.id = ws.enduser_company_id
           LEFT JOIN (
       SELECT
           IFNULL( SUM( w.account_balance ), 0 ) accountBalance,
           IFNULL( SUM( w.withdraw_amount ), 0 ) withdrawAmount,
           c.end_user_id
       FROM
           t_jd_wallet w
           LEFT JOIN t_end_user_open_role c ON w.open_role_id = c.id
       WHERE
           w.ENABLE = 0
           AND w.data_source = 'CD'
           AND w.purse_category = #{purseCategory}
            GROUP by c.id
           ) jw ON e.id = jw.end_user_id
       WHERE
           e.ENABLE = FALSE
           AND e.id = #{endUserId}
  </select>

    <update id="companyProjectPayCallBackSuccessXJ">
      update t_wallet
        set
          update_time = now(),
          account_exp = account_exp + #{money,jdbcType=DECIMAL},
          entry_amount = entry_amount - #{money,jdbcType=DECIMAL}
        where id = #{companyProjectWalletId};
    </update>

    <select id="selectCarrierCompanyWallet" parameterType="com.lz.vo.WalletVO" resultType="com.lz.model.TWallet">
        select *
        from t_wallet
        where carrier_enduser_company_id = #{carrierEnduserCompanyId}
          and datasource = #{datasource} and purse_category = #{purseCategory}
          and company_project_id is null and pid is null and enable = 0
    </select>


    <update id="companyZHPayCallBackFailXJ">
      update t_wallet
        set
          update_time = now(),
          account_exp = account_exp - #{totalFee,jdbcType=DECIMAL},
          frozen_amount = frozen_amount + #{totalFee,jdbcType=DECIMAL}
        where id = #{companyWalletId};
    </update>

    <update id="companyProjectZHPayCallBackFailXJ">
      update t_wallet
        set
          update_time = now(),
          account_exp = account_exp - #{totalFee,jdbcType=DECIMAL},
          frozen_amount = frozen_amount + #{totalFee,jdbcType=DECIMAL}
        where id = #{companyProjectWalletId};
    </update>

    <update id="companyZHPayCallBackFailZQ">
      update t_wallet
        set
          update_time = now(),
          account_exp = account_exp - #{totalFee,jdbcType=DECIMAL},
          account_balance = account_balance + #{totalFee,jdbcType=DECIMAL}
        where id = #{companyWalletId};
    </update>

    <update id="companyProjectZHPayCallBackFailZQ">
      update t_wallet
        set
          update_time = now(),
          account_exp = account_exp - #{totalFee,jdbcType=DECIMAL},
          account_balance = account_balance + #{totalFee,jdbcType=DECIMAL}
        where id = #{companyProjectWalletId};
    </update>
    <select id="selectCompanyCapital" parameterType="java.lang.Integer" resultType="com.lz.vo.CompanyCapitalVo">
        SELECT
            sum( w.account_balance ) AS availableAssets,
            sum( w.account_exp ) AS cumulativeExpenditure,
            sum( w.frozen_amount ) AS frozenAmount
        FROM
            t_wallet w
            LEFT JOIN t_carrier_enduser_company_rel cecr ON cecr.id = w.carrier_enduser_company_id
            LEFT JOIN t_company_info c ON c.id = cecr.enduser_company_id
        WHERE
            w.ENABLE = 0
            AND w.company_project_id IS NULL
            AND w.purse_category = 'BCOMPANY'
            AND c.id = #{companyId}
    </select>

    <!--企业近7天每天充值-->
    <select id="selectCompanyCapitalWeekCz" parameterType="java.lang.Integer" resultType="com.lz.vo.CompanyCapitalWeekVo">
        select a.click_date,ifnull(b.cz,0) as cz
        from (
            SELECT date_sub(curdate(), interval 1 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 2 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 3 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 4 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 5 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 6 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 7 day) as click_date
        ) a left join (
          SELECT
            sum( wc.amount ) AS cz,
            date_format( wc.trade_time, '%Y-%m-%d' ) AS date
        FROM
            t_wallet_change_log wc
            LEFT JOIN t_wallet w ON w.id = wc.wallet_id
            LEFT JOIN t_carrier_enduser_company_rel cecr ON cecr.id = wc.rel_end_id
            LEFT JOIN t_company_info c ON c.id = cecr.enduser_company_id
        WHERE
            wc.ENABLE = 0
            AND w.company_project_id IS NULL
            AND wc.wallet_type = 'BCOMPANY'
            AND wc.trade_type = 'BCHONGZHI'
            AND c.id = #{companyId}
        GROUP BY date
        ORDER BY date DESC
        LIMIT 8
        ) b on a.click_date = b.date ORDER BY a.click_date DESC
    </select>
    <!--企业近7天每天运费支出-->
    <select id="selectCompanyCapitalWeekYfzc" parameterType="java.lang.Integer" resultType="com.lz.vo.CompanyCapitalWeekVo">
        select a.click_date, ifnull(b.yfzc,0) as yfzc
        from (
            SELECT date_sub(curdate(), interval 1 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 2 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 3 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 4 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 5 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 6 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 7 day) as click_date
        ) a left join (
          SELECT
                sum( wc.amount ) AS yfzc,
                date_format( wc.trade_time, '%Y-%m-%d' ) AS date
            FROM
                t_wallet_change_log wc
                LEFT JOIN t_wallet w ON w.id = wc.wallet_id
                LEFT JOIN t_carrier_enduser_company_rel cecr ON cecr.id = wc.rel_end_id
                LEFT JOIN t_company_info c ON c.id = cecr.enduser_company_id
            WHERE
                wc.ENABLE = 0
                AND w.company_project_id IS NULL
                AND wc.wallet_type = 'BCOMPANY'
                AND wc.trade_type = 'BYUNFEIZHICHU'
                AND c.id = #{companyId}
            GROUP BY date
            ORDER BY date DESC
            LIMIT 8
        ) b on a.click_date = b.date ORDER BY a.click_date DESC
    </select>
    <!--企业近7天每天调度费支出-->
    <select id="selectCompanyCapitalWeekDdfzc" parameterType="java.lang.Integer" resultType="com.lz.vo.CompanyCapitalWeekVo">
        select a.click_date,ifnull(b.ddfzc,0) as ddfzc
        from (
            SELECT date_sub(curdate(), interval 1 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 2 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 3 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 4 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 5 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 6 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 7 day) as click_date
        ) a left join (
          SELECT
                sum( wc.amount ) AS ddfzc,
                date_format( wc.trade_time, '%Y-%m-%d' ) AS date
            FROM
                t_wallet_change_log wc
                LEFT JOIN t_wallet w ON w.id = wc.wallet_id
                LEFT JOIN t_carrier_enduser_company_rel cecr ON cecr.id = wc.rel_end_id
                LEFT JOIN t_company_info c ON c.id = cecr.enduser_company_id
            WHERE
                wc.ENABLE = 0
                AND w.company_project_id IS NULL
                AND wc.wallet_type = 'BCOMPANY'
                AND wc.trade_type = 'BDIAODUFEIZHICHU'
                AND c.id = #{companyId}
            GROUP BY date
            ORDER BY date DESC
            LIMIT 8
        ) b on a.click_date = b.date ORDER BY a.click_date DESC
    </select>

    <!--企业近7天每天最新可用余额-->
    <select id="selectCompanyCapitalWeekCarryOverAmount" parameterType="java.lang.Integer" resultType="com.lz.vo.CompanyCapitalWeekVo">
        select a.click_date,ifnull(b.carry_over_amount,0) as carry_over_amount
        from (
            SELECT date_sub(curdate(), interval 1 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 2 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 3 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 4 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 5 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 6 day) as click_date
            union all
            SELECT date_sub(curdate(), interval 7 day) as click_date
        ) a left join (
        select * from (
          SELECT
                wc.trade_time,
                wc.carry_over_amount,
                date_format( wc.trade_time, '%Y-%m-%d' ) AS date
            FROM
                t_wallet_change_log wc
                LEFT JOIN t_wallet w ON w.id = wc.wallet_id
                LEFT JOIN t_carrier_enduser_company_rel cecr ON cecr.id = wc.rel_end_id
                LEFT JOIN t_company_info c ON c.id = cecr.enduser_company_id
            WHERE
                wc.ENABLE = 0
                AND w.company_project_id IS NULL
                AND wc.wallet_type = 'BCOMPANY'
                AND c.id = #{companyId}
                 HAVING 1
             ORDER BY trade_time DESC) t GROUP BY date   ORDER BY date DESC LIMIT 8

        ) b on a.click_date = b.date ORDER BY a.click_date DESC

    </select>
    <select id="selectByWalletAndBankCardInfo" parameterType="java.lang.Integer" resultType="com.lz.model.TBankCard">
      SELECT bc.* from t_wallet w
      left join t_carrier_enduser_company_rel cec on cec.id = w.carrier_enduser_company_id
      left join t_enduser_account eua on eua.enduser_id = cec.enduser_company_id
      left join t_bank_card  bc on bc.account_id = eua.account_id
      where  bc.if_default=1 and bc.enable=0 and w.id= #{id}

    </select>

    <select id="selectWallet" parameterType="com.lz.model.TWallet" resultType="com.lz.model.TWallet">
        select id, carrier_enduser_company_id, company_project_id, pid, purse_category, account_balance, account_exp, account_inc, frozen_amount, entry_amount, withdraw_amount, remark, param1, param2, param3, param4, create_user, create_time, update_user, update_time, enable, datasource
        from t_wallet
        where carrier_enduser_company_id = #{carrierEnduserCompanyId} and company_project_id is null and pid is null and purse_category = #{purseCategory} and datasource= #{datasource}
    </select>

    <select id="selectCompanyProjectWallet" resultType="com.lz.model.TWallet">
        select wa.id, wa.carrier_enduser_company_id, wa.company_project_id, wa.pid, wa.purse_category, wa.account_balance, wa.account_exp,
               wa.account_inc, wa.frozen_amount, wa.entry_amount, wa.withdraw_amount, wa.enable, wa.datasource
        from t_wallet wa
        LEFT JOIN t_carrier_enduser_company_rel cecr  ON wa.carrier_enduser_company_id = cecr.id AND wa.datasource = cecr.datasouce
        WHERE cecr.carrier_id = #{carrierId}
              AND cecr.enduser_company_id = #{companyId}
              AND cecr.datasouce = #{userType,jdbcType=VARCHAR}
              AND wa.purse_category = #{walletType,jdbcType=VARCHAR}
              AND wa.company_project_id = #{projectId} AND wa.pid IS not NULL
    </select>

    <select id="selectCarrierWalletByCarrier" parameterType="java.lang.Integer" resultType="com.lz.dto.CarrierCompanyRelDTO">
        select wa.id walletId, cecr.thrid_pary_sub_account, cecr.uid
        from t_wallet wa
                 LEFT JOIN t_carrier_enduser_company_rel cecr  ON wa.carrier_enduser_company_id = cecr.id AND wa.datasource = cecr.datasouce
        where cecr.carrier_id = #{carrierId} and cecr.enduser_company_id is null and wa.purse_category = 'PCARRIER' and wa.enable = false
          AND wa.company_project_id IS NULL AND pid IS NULL
    </select>

    <select id="selecCompanytWalletByCompany" resultType="com.lz.dto.CarrierCompanyRelDTO">
        SELECT wa.id walletId, cecr.thrid_pary_sub_account, cecr.uid
        FROM t_wallet wa
                 LEFT JOIN t_carrier_enduser_company_rel cecr  ON wa.carrier_enduser_company_id = cecr.id AND wa.datasource = cecr.datasouce
        WHERE cecr.carrier_id = #{carrierId} AND cecr.enduser_company_id = #{companyId} AND cecr.datasouce = #{userType,jdbcType=VARCHAR} AND wa.purse_category = #{walletType,jdbcType=VARCHAR}
          AND wa.company_project_id IS NULL AND wa.pid IS NULL
    </select>
    <!-- 咨询投诉：钱包问题，查询信息 Yan -->
    <select id="getWalletInfo" resultType="com.lz.dto.WalletInfoDTO" parameterType="java.lang.Integer">
        SELECT
            tw.account_balance + tw.frozen_amount as total, <!--总资产-->
            (
                SELECT
                    SUM(wcl.amount)
                FROM t_wallet_change_log wcl
                WHERE wcl.wallet_id = tw.id AND wcl.trade_type = 'BCHONGZHI'
            ) as csum, <!--累计充值-->
            tw.account_exp as accountExp, <!--累计支出-->
            tw.account_balance as balance, <!--可用资产-->
            tw.frozen_amount as amount <!--冻结-->
        FROM t_wallet tw
        WHERE tw.id = #{id};
    </select>

    <select id="selectWalletByEnduserCompanyId" resultType="com.lz.dto.WalletDTO">
        SELECT wa.id walletId, cecr.uid, cecr.thrid_pary_sub_account
    FROM t_wallet wa
    LEFT JOIN t_carrier_enduser_company_rel cecr  ON wa.carrier_enduser_company_id = cecr.id AND wa.datasource = cecr.datasouce
    WHERE cecr.carrier_id = #{carrierId} AND cecr.enduser_company_id = #{enduserCompanyId} AND cecr.datasouce = #{datasource,jdbcType=VARCHAR}
    and wa.purse_category = #{purseCategory} AND wa.company_project_id IS NULL AND wa.pid IS NULL
    </select>

    <select id="selectWalletSum" resultType="java.util.Map">
        SELECT
        sum(account_balance) AS account_balance,
        sum(withdraw_amount) AS withdraw_amount,
        (
        SELECT
        SUM(amount)
        FROM
        t_wallet_change_log
        WHERE
        trade_type = 'MANAGER'
        AND wallet_id IN (
        SELECT
        tw.id
        FROM
        t_carrier_enduser_company_rel tcecr
        LEFT JOIN t_wallet tw ON tcecr.id = tw.carrier_enduser_company_id
        WHERE
        tcecr.enduser_company_id = #{endUserId}
        AND tcecr.datasouce = 'CD'
        AND tcecr. ENABLE = 0
        AND tw. ENABLE = 0
        )
        ) AS account_exp
        FROM
        t_carrier_enduser_company_rel tcecr
        LEFT JOIN t_wallet tw ON tcecr.id = tw.carrier_enduser_company_id
        WHERE
        tcecr.enduser_company_id = #{endUserId}
        AND tcecr.datasouce = 'CD'
        AND tcecr. ENABLE = 0
        AND tw. ENABLE = 0

<!--        SELECT
        	sum(account_balance) AS account_balance,
        	sum(withdraw_amount) AS withdraw_amount,
        	sum(account_exp) AS account_exp
        FROM
        	t_carrier_enduser_company_rel tcecr
        LEFT JOIN t_wallet tw ON tcecr.id = tw.carrier_enduser_company_id
        WHERE
        	tcecr.enduser_company_id = #{endUserId}
        AND tcecr.datasouce = 'CD'
        AND tcecr.enable=0
        AND tw.enable=0-->

    </select>

    <select id="selectWalletAgentInfo" resultType="java.util.Map">
        SELECT
        date_format(twcl.gmt_close,'%Y-%m-%d %H:%i:%S')as gmtClose,<!--交易时间,-->
        twcl.outer_trade_no as outerTradeNo ,<!-- 交易流水号,-->
        carrier_name as carrierName,<!-- 承运方,-->
        tcom.company_name as companyName,<!-- 企业名称,-->
        twcl.amount as amount,<!-- 交易金额,-->
        tdci.item_value as tradeType,<!-- 交易类型,-->
        twcl.bank_no as bankNo,<!-- 提现到银行卡,-->
        toi.order_business_code as orderCode <!--运单号-->
        FROM
        t_carrier_enduser_company_rel tcecr
        LEFT JOIN t_wallet tw ON tcecr.id =tw.carrier_enduser_company_id
        LEFT JOIN t_carrier_info tci on tci.id=tcecr.carrier_id
        LEFT JOIN t_wallet_change_log twcl on tw.id=twcl.wallet_id
        LEFT JOIN t_order_info toi on twcl.inner_trade_no=toi.code
        LEFT JOIN t_company_info tcom on toi.company_id= tcom.id
        LEFT JOIN t_dic_cat_item tdci on twcl.trade_type=tdci.item_code
        where tcecr.enduser_company_id=#{endUserId}
        AND tcecr.datasouce='CD' and tw.datasource='CD' and tw.`enable`=0 and twcl.`enable`=0
        AND twcl.wallet_type='CMANAGER'
        <if test="tradeType != null and tradeType != ''">
             and twcl.trade_type=#{tradeType}
        </if>
        <if test="orderCode != null and orderCode != ''">
            and toi.order_business_code=#{orderCode}
        </if>
        <if test="startTime != null">
            and twcl.trade_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and twcl.trade_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        order by trade_time desc

    </select>
    <select id="selectWalletSumByCarrierId" resultMap="BaseResultMap">
        SELECT
        tw.*
        FROM
        t_carrier_enduser_company_rel tcecr
        LEFT JOIN t_wallet tw ON tcecr.id = tw.carrier_enduser_company_id
        LEFT JOIN t_carrier_info  tci on tcecr.carrier_id=tci.id
        WHERE
        tcecr.enduser_company_id = #{id}
        AND tcecr.datasouce = 'CD'
        AND tci.id=1
        and tcecr.enable=0
        and tw.enable=0
        and tci.enable=0
    </select>

    <select id="selectWalletByCarrierId" resultMap="BaseResultMap">
        SELECT
        	*
        FROM
        	t_wallet tw
        LEFT JOIN t_carrier_enduser_company_rel tcecr ON tw.carrier_enduser_company_id = tcecr.id
        WHERE
        	tcecr.id = #{id,jdbcType=INTEGER}
        and tw.enable=0
        and tcecr.enable=0
    </select>

</mapper>