<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TJdWalletMapper">

    <select id="selectWalletByCarrierCompanyId" resultType="com.lz.model.TJdWallet">
        select
            tdw.*
        from t_jd_wallet tdw
        left join t_carrier_company_open_role tccor on tdw.open_role_id = tccor.id
        where tccor.carrier_company_id = #{carrierCompanyId} and tdw.data_source = #{dataSource}
          and tdw.enable = 0 and tccor.enable = 0
    </select>

    <select id="selectWalletByEndUserId" resultType="com.lz.model.TJdWallet">
        select
            tdw.*
        from t_jd_wallet tdw
        left join t_end_user_open_role teuor on tdw.open_role_id = teuor.id
        where teuor.end_user_id = #{endUserId} and tdw.data_source = 'CD' and purse_category = #{purseCategory}
          and tdw.enable = 0 and teuor.enable = 0
    </select>

    <select id="selectEnduserListWallet" resultType="com.lz.model.TJdWallet">
        select
            tdw.*
        from t_jd_wallet tdw
        left join t_end_user_open_role teuor on tdw.open_role_id = teuor.id
        where  tdw.data_source = 'CD'
            and tdw.enable = 0 and teuor.enable = 0
            and teuor.end_user_id in
            <foreach collection="list" index="index" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
    </select>

    <select id="selectByCarrierWalletPage" resultType="com.lz.vo.TCarrierCompanyOpenRoleVo">
        SELECT
            ca.carrier_name,
            ccor.user_open_role,
            ccor.carrier_company_id,
            ccor.partner_acc_id,
            ccor.bank_account_no,
            ccor.payment_status,
            ccor.open_status,
            wa.account_balance,
            wa.withdraw_amount,
            wa.id AS walletId,
            ccor.id
        FROM
            t_carrier_info ca
            LEFT JOIN t_carrier_company_open_role ccor ON ccor.carrier_company_id = ca.id  AND ccor.ENABLE = 0 AND ccor.user_open_role = 'CA'
            LEFT JOIN t_jd_wallet wa ON wa.open_role_id = ccor.id AND wa.ENABLE = 0 AND wa.data_source = 'CA'
        WHERE
	        ca.ENABLE = 0 and ca.param4 is null
            <if test="carrierName != null" >
                and ca.carrier_name LIKE '%${carrierName}%'
            </if>

    </select>

    <!-- 京东企业钱包详情 -->
    <select id="pcCompanyCwDetail" parameterType="java.lang.Integer" resultType="com.lz.vo.TJdWalletVo">
        SELECT
            wa.id walletId,
            cpi.company_name companyName,
            ccor.carrier_company_id companyId,
            IFNULL(wa.account_balance,0) accountBalance,
            IFNULL(wa.frozen_amount,0) frozenAmount,
            wa.account_balance-wa.frozen_amount canBalance,
            IFNULL(wa.entry_amount,0) entryAmount,
            IFNULL(wa.withdraw_amount,0) withdrawAmount
        FROM (SELECT * FROM t_carrier_company_open_role WHERE user_open_role = 'BD' AND carrier_company_id = #{companyId}) ccor
        LEFT JOIN t_jd_wallet wa ON ccor.id = wa.open_role_id AND wa.data_source = ccor.user_open_role  AND wa.data_source = 'BD'
        LEFT JOIN t_company_info cpi ON cpi.id = ccor.carrier_company_id
        WHERE wa.enable = 0
          AND ccor.enable = 0
          AND cpi.enable = 0
          AND wa.purse_category = 'BCOMPANY'
    </select>

    <select id="walletHj" parameterType="java.lang.Integer" resultType="java.util.HashMap">
        SELECT
            item.item_value tradeType,
            wc.trade_type tradeTypeCode,
            sum(wc.amount) amount
        FROM t_jd_wallet_change_log wc
            LEFT JOIN t_jd_wallet wa ON wa.id = wc.jd_wallet_id AND wa.data_source = 'BD'
            LEFT JOIN t_carrier_company_open_role care ON care.id = wa.open_role_id and care.user_open_role = 'BD'
            LEFT JOIN t_dic_cat_item item ON item.item_code = wc.trade_type
        WHERE
            care.carrier_company_id = #{companyId}
            AND wc.enable = 0
            AND wc.purse_category = 'BCOMPANY'
        GROUP BY  tradeTypeCode,tradeType
    </select>
    <select id="selectByCompanyProject" parameterType="java.lang.Integer" resultType="com.lz.vo.TCompanyProjectVo">
        SELECT
          cp.project_name,
          pcr.dispatch_fee_coefficient AS dispatchFeeCoefficientDg,
          pcr.if_efficient AS stop_flag,
          cp.pay_method,
          cp.credit_line,
          cp.after_use_left_limit,
          cp.credit_days,
          pcr.update_time,
          pcr.create_time,
           IFNULL( toi.noCash, 0 ) AS noCash,
          IFNULL( toi2.withdrawal, 0 ) AS withdrawal,
          IFNULL( toi3.alreadyCash, 0 ) AS alreadyCash,
          IFNULL( toi4.dispatch_fee, 0 ) AS dispatch_fee,
          cp.id
        FROM
          t_company_project cp
        LEFT JOIN t_project_carrier_rel pcr ON pcr.project_id = cp.id
        LEFT JOIN t_carrier_company_open_role tce ON tce.carrier_company_id = pcr.company_id and tce.user_open_role = 'BD'
        LEFT JOIN (
          SELECT
            sum(
              user_confirm_payment_amount
            ) AS noCash,
            company_project_id
          FROM
            t_order_info
          WHERE
            pack_status = 0 and enable = 0
          AND order_execute_status = 'M100'
          AND order_pay_status in('M090','M120')
          AND company_id = #{companyId}
          GROUP BY
            company_project_id
        ) toi ON toi.company_project_id = pcr.project_id
        LEFT JOIN (
          SELECT
            sum(
              user_confirm_payment_amount
            ) AS withdrawal,
            company_project_id
          FROM
            t_order_info
          WHERE
            pack_status = 0 and enable = 0
          AND order_pay_status = 'P110'
          AND company_id = #{companyId}
          GROUP BY
            company_project_id
        ) toi2 ON toi2.company_project_id = cp.id
        LEFT JOIN (
          SELECT
            sum(
              user_confirm_payment_amount
            ) AS alreadyCash,
            company_project_id
          FROM
            t_order_info
          WHERE
            pack_status = 0 and enable = 0
          AND order_pay_status = 'M130'
          AND company_id = #{companyId}
          GROUP BY
            company_project_id
        ) toi3 ON toi3.company_project_id = cp.id
        LEFT JOIN (
          SELECT
            sum(dispatch_fee) AS dispatch_fee,
            company_project_id
          FROM
            t_order_info
          WHERE
            pack_status = 0 and enable = 0
          AND order_execute_status = 'M100'
          AND company_id = #{companyId}
          GROUP BY
            company_project_id
        ) toi4 ON toi4.company_project_id = cp.id
        WHERE
          cp.enable = 0 and pcr.enable = 0 and tce.enable = 0
            AND tce.carrier_company_id = #{companyId}

    </select>
    <select id="selectByCompanyProjectAmp" parameterType="java.lang.Integer" resultType="com.lz.vo.TCompanyProjectVo">
        SELECT
          cp.project_name,
          pcr.dispatch_fee_coefficient AS dispatchFeeCoefficientDg,
          pcr.if_efficient AS stop_flag,
          cp.pay_method,
          cp.credit_line,
          cp.after_use_left_limit,
          cp.credit_days,
          pcr.update_time,
          pcr.create_time,
           IFNULL( toi.noCash, 0 ) AS noCash,
          IFNULL( toi2.withdrawal, 0 ) AS withdrawal,
          IFNULL( toi3.alreadyCash, 0 ) AS alreadyCash,
          IFNULL( toi4.dispatch_fee, 0 ) AS dispatch_fee,
          cp.id
        FROM
          t_company_project cp
        LEFT JOIN t_project_carrier_rel pcr ON pcr.project_id = cp.id
        LEFT JOIN t_carrier_company_open_role tce ON tce.carrier_company_id = pcr.company_id and tce.user_open_role = 'BD'
        LEFT JOIN (
          SELECT
            sum(
             tmp.advance_fee
            ) AS noCash,
            oi.company_project_id
          FROM
            t_order_info oi
            LEFT JOIN t_advance_order_tmp tmp on tmp.order_code = oi.code
          WHERE
            oi.pack_status = 0 and oi.enable = 0 and tmp.enable = 0  AND oi.company_id = #{companyId}
          AND tmp.order_pay_status in('M090','M120')
          GROUP BY
            oi.company_project_id
        ) toi ON toi.company_project_id = pcr.project_id
        LEFT JOIN (
          SELECT
            sum(
             tmp.advance_fee
            ) AS withdrawal,
            oi.company_project_id
          FROM
            t_order_info oi
            LEFT JOIN t_advance_order_tmp tmp on tmp.order_code = oi.code
          WHERE
            oi.pack_status = 0 and oi.enable = 0 and tmp.enable = 0 AND oi.company_id = #{companyId}
          AND tmp.order_pay_status = 'P110'
          GROUP BY
            company_project_id
        ) toi2 ON toi2.company_project_id = cp.id
        LEFT JOIN (
          SELECT
            sum(
             tmp.advance_fee
            ) AS alreadyCash,
            oi.company_project_id
          FROM
            t_order_info oi
            LEFT JOIN t_advance_order_tmp tmp on tmp.order_code = oi.code
          WHERE
            oi.pack_status = 0 and oi.enable = 0 and tmp.enable = 0 AND oi.company_id = #{companyId}
          AND tmp.order_pay_status = 'M130'
          GROUP BY
            company_project_id
        ) toi3 ON toi3.company_project_id = cp.id
        LEFT JOIN (
          SELECT
            sum(tmp.advance_dispatch_fee) AS dispatch_fee,
            oi.company_project_id
          FROM
            t_order_info oi
            LEFT JOIN t_advance_order_tmp tmp on tmp.order_code = oi.code
          WHERE
            oi.pack_status = 0 and oi.enable = 0 and tmp.enable = 0 AND oi.company_id = #{companyId}
          AND tmp.order_pay_status in('M090','M120','M130','P110')
          GROUP BY
            company_project_id
        ) toi4 ON toi4.company_project_id = pcr.project_id
        WHERE
          cp. ENABLE = 0 and pcr.enable = 0 and tce.enable = 0
            AND tce.carrier_company_id = #{companyId}

</select>


    <select id="selectByCompanyProjectPack" parameterType="java.lang.Integer" resultType="com.lz.vo.TCompanyProjectVo">
        SELECT
        cp.id,
        cp.project_name,
        pcr.if_efficient as stop_flag,
        cp.pay_method,
        cp.credit_line,
        cp.after_use_left_limit,
        cp.credit_days,
        pcr.update_time,
        pcr.create_time,
        pcr.dispatch_fee_coefficient as dispatchFeeCoefficientDg,
        IFNULL( a.noCash, 0 ) AS noCash,
        IFNULL( b.withdrawal, 0 ) AS withdrawal,
        IFNULL( c.alreadyCash, 0 ) AS alreadyCash,
        IFNULL( a.dispatch_fee, 0 ) AS dispatch_fee
        FROM
        t_company_project cp
        LEFT JOIN t_project_carrier_rel pcr on pcr.project_id = cp.id
        LEFT JOIN t_carrier_company_open_role tce ON tce.carrier_company_id = pcr.company_id and tce.user_open_role = 'BD'
        LEFT JOIN(
        SELECT
        sum( toi.user_confirm_payment_amount ) AS noCash,
        sum( toi.dispatch_fee ) AS dispatch_fee,
        toi.company_project_id
        FROM
        t_order_pack_info pi
        LEFT JOIN t_order_pack_detail pd ON pi.code = pd.pack_pay_code
        LEFT JOIN  t_order_info toi  ON  pd.order_code = toi.code
        WHERE
        pi.ENABLE = 0 and pd.enable = 0 and toi.enable = 0
        and pi.pack_status = 'PACKPAID'
        AND pi.company_id = #{companyId}
        GROUP BY
        toi.company_project_id
        ) a on a.company_project_id = cp.id
        LEFT JOIN (
        SELECT
        sum( toi.user_confirm_payment_amount ) AS withdrawal,
        toi.company_project_id
        FROM
        t_order_pack_info pi
        LEFT JOIN t_order_pack_detail pd ON pi.code = pd.pack_pay_code
        LEFT JOIN  t_order_info toi  ON  pd.order_code = toi.code
        WHERE
        pi.ENABLE = 0 and pd.enable = 0 and toi.enable = 0
        and pi.pack_status = 'PACKEXTRACTPROCESSED'
        AND pi.company_id = #{companyId}
        GROUP BY
        toi.company_project_id
        ) b ON a.company_project_id = b.company_project_id
        LEFT JOIN (
        SELECT
        sum( toi.user_confirm_payment_amount ) AS alreadyCash,
        toi.company_project_id
        FROM
        t_order_pack_info pi
        LEFT JOIN t_order_pack_detail pd ON pi.code = pd.pack_pay_code
        LEFT JOIN  t_order_info toi  ON  pd.order_code = toi.code
        WHERE
        pi.ENABLE = 0 and pd.enable = 0 and toi.enable = 0
        and pi.pack_status = 'PACKPAID'
        AND pi.company_id = #{companyId}
        GROUP BY
        toi.company_project_id
        ) c ON a.company_project_id = c.company_project_id
        where cp. company_id = #{companyId}  and cp.enable = 0 and pcr.enable = 0 and tce.enable = 0
  </select>
    <select id="companyCapitalFlow" parameterType="com.lz.vo.CompanyCapitalFlowVo" resultType="com.lz.vo.CompanyCapitalFlow">
        SELECT
        wc.trade_no outerTradeNo,
        wc.trade_time tradeTime ,
        item.item_value tradeType,
        wc.trade_type tradeTypeCode,
        wc.amount amount,
        wc.order_business_code  innerTradeNo,
        wc.out_partner_acc_id thridParySubAccountZC,
        wc.in_partner_acc_id thridParySubAccountZR,
        case wc.trade_type
        when 'BPAY' then wc.file_url
        when 'BCHONGZHI' then wc.file_url
        when 'CYKFJDZHICHU' then wc.file_url
        when 'BTIXIAN' then wc.file_url
        when 'BD_SERVICE_PAY' then wc.file_url
        else null end as file_url
        FROM t_jd_wallet_change_log wc
        LEFT JOIN t_jd_wallet wa ON wa.id = wc.jd_wallet_id
        LEFT JOIN t_carrier_company_open_role ccor ON ccor.id = wa.open_role_id and ccor.user_open_role = 'BD'
        LEFT JOIN t_dic_cat_item item ON item.item_code = wc.trade_type
        WHERE ccor.carrier_company_id = #{companyId} AND wc.`enable`=FALSE AND wc.purse_category = 'BCOMPANY'
        <if test="startTime != null">
            and wc.trade_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and wc.trade_time &lt;= #{endTime}
        </if>
        <if test="tradeType != null and tradeType != ''">
            and wc.trade_type = #{tradeType}
        </if>
        <if test="orderBusinessCode != null and orderBusinessCode != ''">
            and wc.order_business_code like '%${orderBusinessCode}%'
        </if>
        <if test="outerTradeNoArray != null and outerTradeNoArray.length != 0">
            AND wc.trade_no IN
            <foreach collection="outerTradeNoArray" index="index" item="outerTradeNo" open="(" close=")" separator=",">
               #{outerTradeNo}
            </foreach>
        </if>
        order by tradeTime desc
    </select>

    <select id="selectDriverInfo" parameterType="java.lang.Integer" resultType="java.util.HashMap">
    SELECT
        com.end_user_id as driverId,
        IFNULL(wa.account_balance,0) as accountBalance,
        IFNULL(wa.frozen_amount,0) as frozenAmount,
        IFNULL(wa.entry_amount,0) as entryAmount,
        IFNULL(wa.withdraw_amount,0) as withdrawAmount
    FROM t_end_user_open_role com
        LEFT JOIN t_jd_wallet wa ON com.id = wa.open_role_id and wa.data_source = 'CD'
    WHERE
        com.end_user_id = #{enduserId}
        AND wa.enable = 0
        AND com.enable = 0
  </select>


    <select id="selectDriverSum" parameterType="java.lang.Integer" resultType="java.util.HashMap">
        SELECT
        ui.id,
        ui.real_name realName,
        ui.phone,
        IFNULL(wa.accountBalance,0)+IFNULL(jw.accountBalance,0)+IFNULL(zt.accountBalance,0) as  withdrawBalance,
        IFNULL(wa.accountBalance,0) as wsAccountBalance,
        IFNULL(jw.accountBalance,0) as jdAccountBalance,
				IFNULL(zt.accountBalance, 0) as ztAccountBalance,
        IFNULL(wa.withdrawAmount,0)+IFNULL(jw.withdrawAmount,0)+IFNULL(zt.withdrawAmount,0) as withdrawAmount,
        IFNULL(wa.totalWithdrawal,0)+IFNULL(jw.totalWithdrawal,0)+IFNULL(zt.totalWithdrawal,0) as totalWithdrawal
        FROM
        t_end_user_info ui
				left join t_enduser_account tea on tea.enduser_id = ui.id
        LEFT JOIN (
        SELECT
        IFNULL( SUM( w.account_balance ), 0 ) accountBalance,
        IFNULL( SUM( w.withdraw_amount ), 0 ) withdrawAmount,
        IFNULL( SUM( l.amount ), 0 ) totalWithdrawal,
        c.enduser_company_id
        FROM t_wallet w
        LEFT JOIN (SELECT * FROM t_carrier_enduser_company_rel WHERE datasouce = 'CD') c
        ON w.carrier_enduser_company_id = c.id and w.datasource = c.datasouce
        LEFT JOIN (
        SELECT wallet_id ,IFNULL( SUM( amount ), 0 ) as amount
        FROM t_wallet_change_log
        WHERE ENABLE = 0
        AND trade_type in('CTIXIAN','CYFKTX','MANAGER')
        GROUP BY wallet_id
        ) l  on w.id = l.wallet_id
        WHERE   w.`enable` = FALSE
        and w.datasource = 'CD'
        GROUP by c.enduser_company_id
        ) wa  	ON ui.id = wa.enduser_company_id
        LEFT JOIN (
        SELECT
        IFNULL( SUM( w.account_balance ), 0 ) accountBalance,
        IFNULL( SUM( w.withdraw_amount ), 0 ) withdrawAmount,
        IFNULL( SUM( l.amount ), 0 ) totalWithdrawal,
        c.end_user_id
        FROM t_jd_wallet w
        LEFT JOIN  t_end_user_open_role c ON w.open_role_id = c.id
        LEFT JOIN (
        SELECT jd_wallet_id ,IFNULL( SUM( amount ), 0 ) as amount
        FROM t_jd_wallet_change_log
        WHERE ENABLE = 0
        AND trade_type in('CTIXIAN','CYFKTX','MANAGER')
        GROUP BY jd_wallet_id
        ) l  on w.id = l.jd_wallet_id
        WHERE   w.enable = 0
        and w.data_source = 'CD'
        GROUP by c.id
        ) jw  	ON ui.id = jw.end_user_id
				LEFT JOIN (
      SELECT
      IFNULL( SUM( w.account_balance ), 0 ) accountBalance,
      IFNULL( SUM( w.withdraw_amount ), 0 ) withdrawAmount,
      IFNULL( SUM( l.amount ), 0 ) totalWithdrawal,
      w.account_id
      FROM
      t_zt_wallet w
      LEFT JOIN t_zt_account_open_info c ON w.account_id = c.account_id
      LEFT JOIN (
      SELECT
      wallet_id,
      IFNULL( SUM( amount ), 0 ) AS amount
      FROM
      t_zt_wallet_change_log
      WHERE
      ENABLE = 0
      AND trade_type IN ( 'CTIXIAN', 'CYFKTX', 'MANAGER' )
      GROUP BY
      wallet_id
      ) l ON w.id = l.wallet_id
      WHERE
      w.ENABLE = 0
      AND w.data_source = 'CD'
      GROUP BY
      c.id
      ) zt ON zt.account_id = tea.account_id
        LEFT JOIN t_dic_cat_item tdci  ON ui.user_logistics_role = tdci.item_code
        WHERE  ui.ENABLE = FALSE and ui.id = #{enduserId}
    </select>


    <select id="driverCapitalFlow" parameterType="com.lz.vo.CompanyCapitalFlowVo" resultType="com.lz.vo.DriverCapitalFlow">
        SELECT
        ui.phone,
        ui.real_name realName,
        wc.trade_no outerTradeNo,
        wc.trade_time tradeTimeDate,
        item.item_value tradeType,
        wc.amount amount,
        wc.order_business_code innerTradeNo,
        wc.out_partner_acc_id thridParySubAccountZC,
        wc.in_partner_acc_id thridParySubAccountZR,
        ui.idcard,
        item2.item_value walletType
        FROM
        t_jd_wallet_change_log wc
        LEFT JOIN t_jd_wallet wa on wa.id = wc.jd_wallet_id
        LEFT JOIN t_end_user_open_role eor ON eor.id = wa.open_role_id
        LEFT JOIN t_end_user_info ui ON ui.id = eor.end_user_id
        LEFT JOIN t_dic_cat_item item ON item.item_code = wc.trade_type
        LEFT JOIN t_dic_cat_item item2 ON item2.item_code = wc.purse_category
        WHERE
        ui.id = #{driverId}
        and wc.enable = 0
        and wa.enable = 0
        and eor.enable = 0
        and ui.enable = 0
        and wc.purse_category in ( 'CDRIVER' , 'CCARBOSS' , 'CMANAGER' , 'CCAPTAIN')
        <if test="startTime != null">
            and wc.trade_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and wc.trade_time &lt;= #{endTime}
        </if>
        <if test="tradeType != null and tradeType != ''">
            and wc.trade_type = #{tradeType}
        </if>
        <if test="innerTradeNo != null and innerTradeNo != ''">
            and wc.order_business_code like '%${innerTradeNo}%'
        </if>
        order by tradeTimeDate desc
    </select>


    <select id="selectByRoleTypeAndComId" resultType="com.lz.model.TJdWallet">
        select wa.*
        from t_jd_wallet wa
        <where> wa.enable = 0
            <if test="openRole != null">
                and wa.data_source = #{openRole}
            </if>
            <if test="openRoleId != null">
                and wa.open_role_id = #{openRoleId}
            </if>
        </where>
    </select>

    <select id="selectAgentWalletSum"  resultType="java.util.HashMap">
        SELECT
        IFNULL(w.account_balance,0) accountBalance,
        IFNULL(w.withdraw_amount,0) withdrawAmount,
        IFNULL(l.amount,0) totalWithdrawal,
        c.id as openRoleId,
        c.end_user_id as endUserId
        FROM t_jd_wallet w
        LEFT JOIN  t_end_user_open_role c ON w.open_role_id = c.id
        LEFT JOIN (
        SELECT jd_wallet_id ,IFNULL( SUM( amount ), 0 ) as amount
        FROM t_jd_wallet_change_log
        WHERE ENABLE = 0
        AND trade_type in('CTIXIAN','CYFKTX','MANAGER')
        GROUP BY jd_wallet_id
        ) l  on w.id = l.jd_wallet_id
        WHERE   w.enable = 0
        and w.data_source = 'CD' and c.end_user_id = #{endUserId}
    </select>

    <select id="selectAgentWalletList" parameterType="com.lz.vo.CompanyCapitalFlowVo" resultType="com.lz.vo.DriverCapitalFlow">
        SELECT
        ui.phone,
        ui.real_name realName,
        wc.trade_no outerTradeNo,
        wc.trade_time tradeTimeDate,
        item.item_value tradeType,
        wc.amount amount,
        wc.order_business_code innerTradeNo,
        wc.out_partner_acc_id thridParySubAccountZC,
        wc.in_partner_acc_id thridParySubAccountZR,
        ui.idcard,
        item2.item_value walletType
        FROM
        t_jd_wallet_change_log wc
        LEFT JOIN t_jd_wallet wa on wa.id = wc.jd_wallet_id
        LEFT JOIN t_end_user_open_role eor ON eor.id = wa.open_role_id
        LEFT JOIN t_end_user_info ui ON ui.id = eor.end_user_id
        LEFT JOIN t_dic_cat_item item ON item.item_code = wc.trade_type
        LEFT JOIN t_dic_cat_item item2 ON item2.item_code = wc.purse_category
        WHERE
        ui.id = #{driverId}
        and wc.enable = 0
        and wa.enable = 0
        and eor.enable = 0
        and ui.enable = 0
        and wc.purse_category = 'CMANAGER'
        <if test="startTime != null">
            and wc.trade_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and wc.trade_time &lt;= #{endTime}
        </if>
        <if test="tradeType != null and tradeType != ''">
            and wc.trade_type = #{tradeType}
        </if>
        <if test="innerTradeNo != null and innerTradeNo != ''">
            and wc.order_business_code like '%${innerTradeNo}%'
        </if>
        order by tradeTimeDate desc
    </select>

    <select id="selectPfWallet"  resultType="com.lz.vo.TJdWalletVo">
       	SELECT
            wa.id walletId,
            ccor.id as openRoleId,
            ccor.user_open_role as userOpenRole,
            ccor.payment_status as paymentStatus,
            ccor.partner_acc_id as partnerAccId,
            ccor.open_status as openStatus,
            ccor.carrier_company_id carrierCompanyId,
            IFNULL(wa.account_balance,0) accountBalance,
            wa.account_balance-wa.frozen_amount canBalance,
            l.amount as sr,
            0 as zc
        FROM (SELECT * FROM t_carrier_company_open_role WHERE user_open_role = 'PF') ccor
        LEFT JOIN t_jd_wallet wa ON ccor.id = wa.open_role_id AND wa.data_source = ccor.user_open_role  AND wa.data_source = 'PF'
        LEFT JOIN (
            SELECT jd_wallet_id ,IFNULL( SUM( amount ), 0 ) as amount
            FROM t_jd_wallet_change_log
            WHERE ENABLE = 0
            AND trade_type in('CAPFPLATFORM','MAPFSERVICESR','BDPFSERVICESR','PBPSFSHOURU','CDPFSERVICESR','CCPFSERVICESR')
            GROUP BY jd_wallet_id
        ) l  on wa.id = l.jd_wallet_id
        WHERE wa.enable = 0
            AND ccor.enable = 0
            AND wa.purse_category = 'PFPLATFORM'
    </select>

    <select id="selectPfWalletList" parameterType="com.lz.vo.CompanyCapitalFlowVo" resultType="com.lz.vo.CompanyCapitalFlow">
        SELECT
        wc.id,
        wc.trade_no outerTradeNo,
        wc.trade_time tradeTime ,
        item.item_value tradeType,
        wc.trade_type tradeTypeCode,
        wc.amount amount,
        wc.out_partner_acc_id thridParySubAccountZC,
        wc.in_partner_acc_id thridParySubAccountZR
        FROM t_jd_wallet_change_log wc
        LEFT JOIN t_jd_wallet wa ON wa.id = wc.jd_wallet_id
        LEFT JOIN t_carrier_company_open_role ccor ON ccor.id = wa.open_role_id and ccor.user_open_role = 'PF'
        LEFT JOIN t_dic_cat_item item ON item.item_code = wc.trade_type
        WHERE  wc.`enable`=FALSE AND wc.purse_category = 'PFPLATFORM' and ccor.carrier_company_id = #{carrierCompanyId}
        <if test="startTime != null">
            and wc.trade_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and wc.trade_time &lt;= #{endTime}
        </if>
        <if test="tradeType != null and tradeType != ''">
            and wc.trade_type = #{tradeType}
        </if>
        <if test="idArray != null and idArray.size != 0 ">
            AND wc.id IN
            <foreach collection="idArray" item="id" index="item" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        order by tradeTime desc
    </select>


    <update id="updateStatusByPayCode" parameterType="com.lz.vo.TOrderPayDetailVO">
    update t_order_pay_detail
    set
      return_time = #{returnTime,jdbcType=TIMESTAMP},
      trade_status = #{tradeStatus,jdbcType=VARCHAR},
      error_code = #{errorCode,jdbcType=VARCHAR},
      error_msg = #{errorMsg,jdbcType=VARCHAR},
      inner_trade_no = #{innerTradeNo,jdbcType=VARCHAR},
      update_time = #{returnTime,jdbcType=TIMESTAMP}
    where code = #{code,jdbcType=VARCHAR}
  </update>

    <update id="updateStatusByCode">
    update t_order_pay_info
    set
      order_pay_status = #{status,jdbcType=VARCHAR},
        update_time = now()
    where code = #{code,jdbcType=INTEGER}
  </update>

    <select id="selectByEnduserPartnerAccId" resultType="com.lz.model.TJdWallet">
        select tjw.id, tjw.open_role_id, tjw.account_balance, tjw.entry_amount, tjw.account_exp, tjw.account_inc, tjw.withdraw_amount,
               tjw.purse_category, tjw.data_source
        from t_jd_wallet tjw
        left join t_end_user_open_role teuor on tjw.open_role_id = teuor.id
        where tjw.enable = 0 and teuor.enable = 0
          and teuor.partner_acc_id = #{partnerAccId} and tjw.data_source = 'CD'
    </select>

</mapper>