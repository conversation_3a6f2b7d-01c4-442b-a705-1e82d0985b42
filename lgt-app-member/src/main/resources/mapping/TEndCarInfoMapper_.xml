<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TEndCarInfoMapper">
  <resultMap id="BaseResultMapCarList" type="com.lz.vo.TEndCarInfoVO">
    <result column="users" jdbcType="VARCHAR" property="carUsers" />
    <result column="audit_status_value" jdbcType="VARCHAR" property="auditStatusValue" />
  </resultMap>
  <select id="selectByPage_COUNT" parameterType="com.lz.vo.TEndCarInfoSearchVO" resultType="Long">
    select count(0)
    FROM
    t_end_car_info eci
    WHERE eci.ENABLE = FALSE
    <if test="idArray != null and idArray.length!=0">
      and eci.id IN
      <foreach collection="idArray" index="index" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
    <if test="carNum != null and carNum != ''">
      and eci.vehicle_number like concat('%', #{carNum}, '%')
    </if>
    <if test="auditStatus != null and auditStatus != ''">
      and eci.audit_status = #{auditStatus}
    </if>
    <if test="updateStartTime != null">
       <![CDATA[ and DATE_FORMAT(eci.update_time,'%Y-%m-%d') >= DATE_FORMAT(#{updateStartTime,jdbcType=TIMESTAMP},'%Y-%m-%d') ]]>
    </if>
    <if test="updateEndTime != null">
       <![CDATA[ and  DATE_FORMAT(eci.update_time,'%Y-%m-%d') <= DATE_FORMAT(#{updateEndTime,jdbcType=TIMESTAMP},'%Y-%m-%d')]]>
    </if>
  </select>
  <select id="selectByPage" parameterType="com.lz.vo.TEndCarInfoSearchVO" resultMap="BaseResultMapCarList">
    SELECT
    tdci.item_value audit_status_value,
    tdci2.item_value uploaded_status,
    eci.*,
    <!-- GROUP_CONCAT( eui.real_name ) users, -->
    CASE
    eci.verass
    WHEN 1 THEN
    '已入网' ELSE '未入网'
    END AS verassState,
    eci.update_time AS updateTimeFormat,
    eci.create_time AS createTimeFormat,
    tdci3.item_value driving_licences_status, tecai.road_transport_operation_opinion,
    tdci4.item_value road_transport_operation_status, tecai.driving_licences_opinion
    FROM
    t_end_car_info eci LEFT JOIN t_end_user_car_rel eucr ON eucr.endcar_id = eci.id
    AND eucr.user_car_relation_type = 'CLSYRSJ'
    AND eucr.data_concel_from IS NULL
    AND eucr.ENABLE = 0
<!-- LEFT JOIN ( SELECT * FROM t_end_user_info WHERE ENABLE = FALSE ) eui ON eui.id = eucr.enduser_id -->
    LEFT JOIN t_dic_cat_item tdci ON eci.audit_status = tdci.item_code
    LEFT JOIN t_dic_cat_item tdci2 ON eci.uploaded_status = tdci2.item_code
    left join t_end_car_audit_info tecai on eci.id = tecai.end_car_id
    left join t_dic_cat_item tdci3 on tecai.driving_licences_status = tdci3.item_code
    left join t_dic_cat_item tdci4 on tecai.road_transport_operation_status = tdci4.item_code
    WHERE eci.ENABLE = FALSE
    <if test="idArray != null and idArray.length!=0">
      and eci.id IN
      <foreach collection="idArray" index="index" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
    <if test="carNum != null and carNum != ''">
      and eci.vehicle_number like concat('%', #{carNum}, '%')
    </if>
    <if test="auditStatus != null and auditStatus != ''">
      and eci.audit_status = #{auditStatus}
    </if>
    <if test="updateStartTime != null">
       <![CDATA[ and DATE_FORMAT(eci.update_time,'%Y-%m-%d') >= DATE_FORMAT(#{updateStartTime,jdbcType=TIMESTAMP},'%Y-%m-%d') ]]>
    </if>
    <if test="updateEndTime != null">
       <![CDATA[ and  DATE_FORMAT(eci.update_time,'%Y-%m-%d') <= DATE_FORMAT(#{updateEndTime,jdbcType=TIMESTAMP},'%Y-%m-%d')]]>
    </if>
    GROUP BY eci.id
    ORDER BY eci.id DESC
  </select>

  <update id="deleteByID" parameterType="java.util.ArrayList">
    update t_end_car_info
    set enable = 1
    <where>
      id IN
      <foreach collection="array" open="(" close=")" separator="," index="index" item="item">
        #{item}
      </foreach>
    </where>
  </update>

  <update id="approval" parameterType="com.lz.vo.ApprovalVO">
    update t_end_car_info
    set
      audit_status = #{status},
      audit_opinion = #{msg}
    where id = #{id}
  </update>
  <select id="selectEndCar_COUNT" parameterType="com.lz.vo.TEndCarInfoSearchVO" resultType="java.lang.Long">
    SELECT
        count( 1 )
    FROM
        t_end_user_info teui
    LEFT JOIN t_end_user_car_rel teucr ON teucr.enduser_id = teui.id
    left join t_end_car_info teci on teucr .endcar_id = teci.id
    WHERE
        teui.ENABLE = 0
        AND teucr.`enable` = 0
        AND teucr.data_concel_from IS NULL
        AND teucr.user_car_relation_type = 'CLSYRSJ'
        <if test="param != null">
          and teci.vehicle_number like concat('%', #{param}, '%')
        </if>

  </select>
  <select id="selectEndCar" parameterType="com.lz.vo.TEndCarInfoSearchVO" resultType="com.lz.dto.EndCarDTO">
    select
    t.*, teui.id endDriverId,
    teui.real_name,
    teui.phone,
    teui.idcard,
    teui2.id enduserIdRel,
    teui2.real_name realNameRel,
    teui2.phone phoneRel,
    teui2.idcard idcardRel,
    case tdci.value_content when 1 then '可用' else '不可用' end carStatus,
    tdci.value_content carUseable,
    tdci2.value_content userUseable,
    t.vehicle_number carVehicleNumber,
    case tdci2.value_content when 1 then '可用' else '不可用' end userStatus,
    case tdci2.value_content when 1 then '' else teus.current_receipts_no end userReceiptsNo,
    case tdci2.value_content when 1 then '' else tosn.page_show_name end userOrderState,
    case tdci2.value_content when 1 then '' else teci2.vehicle_number end userVehicleNumber,
    case tdci.value_content when 1 then '' else tecs.current_receipts_no end carReceiptsNo,
    case tdci.value_content when 1 then '' else tecs.current_driver end carCurrentDriver,
    case tdci.value_content when 1 then '' else tosn2.page_show_name end carOrderState,
    case tdci2.value_content when 1 then '' else teus.current_driver end userCurrentDriver,
    case  when score.score IS NOT NULL then score.score else '暂无评分'  end score,
    case  when score.score IS NOT NULL then score.score else '0'  end score2
    FROM
    (
    SELECT
    teucr.id enduserCarRelId,
    teci.id endCarId,
    teucr.enduser_id,
    teci.`owner`,
    teci.vehicle_number,
    teucr.user_car_relation_type
    FROM
    t_end_user_car_rel teucr
    LEFT JOIN t_end_car_info teci ON teucr.endcar_id = teci.id
    WHERE
    teucr.`enable` = 0
    AND teci.`enable` = 0 and teucr.data_concel_from IS NULL and teucr.user_car_relation_type = 'CLSYRSJ'
    ) t
    LEFT JOIN t_end_user_info teui ON t.enduser_id = teui.id
    LEFT JOIN t_end_user_car_rel teucr2 on t.endCarId = teucr2.endcar_id and teucr2.user_car_relation_type ='CLSYRCLB'
    and teucr2.data_concel_from IS NULL and teucr2.audit_status='PASSNODE' and teucr2.enable = 0
    LEFT JOIN t_end_user_info teui2 on teucr2.enduser_id = teui2.id
    left join t_end_car_status tecs on t.endCarId = tecs.endcar_id
    left join t_dic_cat_item tdci on tecs.car_status = tdci.item_code
    left join t_order_info toi2 on tecs.current_receipts_no = toi2.order_business_code
    left join t_order_state_node tosn2 on tosn2.code = toi2.order_execute_status
    left join t_end_user_status teus on t.enduser_id = teus.enduser_id
    left join t_dic_cat_item tdci2 on teus.user_status = tdci2.item_code
    left join t_order_info toi on teus.current_receipts_no = toi.order_business_code
    left join t_order_state_node tosn on tosn.code = toi.order_execute_status
    left join t_end_car_info teci2 on toi.vehicle_id = teci2.id
    left join t_user_score_info score on score.uid = teui.id and score.user_type = 'CD' and score.enable = 0
    where teui.enable = 0
      <if test="param != null">
        and t.vehicle_number like concat('%', #{param}, '%')
      </if>

  </select>
  <select id="selectByCarNum" parameterType="java.lang.String" resultMap="BaseResultMap">
    select *
    FROM `t_end_car_info`
    WHERE vehicle_number= #{carNum,jdbcType=VARCHAR} and enable = 0
  </select>

  <select id="selectCarInfo" parameterType="java.lang.Integer" resultType="com.lz.dto.EndCarDTO">
    select teci.id, teci.vehicle_number, teci.audit_status, tdci.item_value auditValue
    from t_end_car_info teci
    left join t_dic_cat_item tdci on teci.audit_status = tdci.item_code
where teci.id = #{endcarId}
  </select>
  <!--APP端 会员详情-获取车辆信息 Yan-->
  <select id="getCarDetailInfo" parameterType="java.lang.Integer" resultType="com.lz.dto.AppSearchCarInfoDTO">
    SELECT
    dci.item_value as carStatus, <!-- 车辆审核状态 -->
    eci.audit_opinion, <!--车辆审核意见-->
    eci.vehicle_number, <!--车牌号-->
    eci.road_transport_operation_license_code, <!--道路运输证经营许可证号-->
    eci.road_transport_operation_license_photo1, <!--道路运输证-->
    eci.road_transport_operation_license_photo2, <!--道路运输证-->
    eci.driving_licences_photo1, <!--行驶证照片-->
    eci.driving_licences_photo2, <!--行驶证照片-->
    eui.id as carOwnerId, <!-- 车主Id -->
    eui.idcard, <!--车主身份证号-->
    eui.real_name, <!--车主姓名-->
    eucr.certificate_doc1, <!--车主证明-->
    eucr.certificate_doc2, <!--车主证明-->
    eci.road_transport_certificate_number, <!-- 道路运输许可证号 -->
    eui.phone,
    eci.audit_status
    FROM t_end_car_info eci
    LEFT JOIN t_dic_cat_item dci ON eci.audit_status = dci.item_code
    LEFT JOIN t_end_user_car_rel eucr ON eci.id = eucr.endcar_id  AND eucr.user_car_relation_type = 'CLSYRCLB' and eucr.enable = 0 and eucr.data_concel_from is null
    and eucr.audit_status='PASSNODE'
    LEFT JOIN t_end_user_info eui ON eucr.enduser_id = eui.id
    WHERE
    eci.id = #{carId}
    and eci.enable=0
    AND EXISTS (
    SELECT
    eucr.id
    FROM  t_end_user_car_rel eucr
    WHERE eucr.endcar_id = eci.id AND  eucr.`enable` = 0 and eucr.data_concel_from is null
    LIMIT 1
    )
  </select>
  <select id="getCarInfo" parameterType="java.lang.Integer" resultType="com.lz.dto.AppSearchCarInfoDTO">
    SELECT
    dci.item_value as carStatus, <!-- 车辆审核状态 -->
    eci.audit_opinion, <!--车辆审核意见-->
    eci.vehicle_number, <!--车牌号-->
    eci.road_transport_operation_license_code, <!--道路运输证经营许可证号-->
    eci.road_transport_operation_license_photo1, <!--道路运输证-->
    eci.road_transport_operation_license_photo2, <!--道路运输证-->
    eci.driving_licences_photo1, <!--行驶证照片-->
    eci.driving_licences_photo2, <!--行驶证照片-->
    eci.road_transport_certificate_number, <!-- 道路运输许可证号 -->
    eci.audit_status
    FROM t_end_car_info eci
    LEFT JOIN t_dic_cat_item dci ON eci.audit_status = dci.item_code
    WHERE
    eci.id = #{carId}
    and eci.enable=0
  </select>
  <select id="selectByEndUserId" parameterType="java.lang.Integer" resultType="com.lz.model.TEndCarInfo">
    select c.*
      from
      t_end_user_car_rel r
      left join t_end_car_info c on c.id = r.endcar_id
      where r.enduser_id = #{enduserId} and r.user_car_relation_type = 'CLSYRSJ' and r.data_concel_from IS NULL and r.enable=0 and c.enable=0
      ORDER BY
      c.audit_status,
      c.create_time
  </select>

  <select id="selectPassnodeCarsByEndUserId" parameterType="java.lang.Integer" resultType="com.lz.model.TEndCarInfo">
    select c.*
    from
      t_end_user_car_rel r
        left join t_end_car_info c on c.id = r.endcar_id
    where r.enduser_id = #{enduserId} and r.user_car_relation_type = 'CLSYRSJ' and r.data_concel_from IS NULL and r.enable=0 and c.enable=0 and c.audit_status='PASSNODE'
  </select>

  <select id="selectByEndCarIdList" parameterType="hashmap" resultType="com.lz.model.TEndCarInfo">
    select i.* from t_end_car_info i where
    i.id in
    <foreach collection="carIdList" index="index" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </select>


  <select id="selectByCarId" parameterType="java.lang.Integer" resultType="com.lz.dto.EndCarInfoDTO">
    select
     c.id, c.vehicle_number, c.license_plate_color, c.licenseplate_type_code, c.vehicle_tonnage,
    c.road_transport_certificate_number, c.trailer_vehicle_plate_number, c.`owner`, c.permit_number,
    c.use_property, c.brand_model, c.vehicle_identification_code, engine_code, c.register_date,
    c.issue_date, c.file_code, c.approved_bare_people, c.full_vehicle_quality, c.approved_bare_quality,
    c.approved_motor_full_quality, c.outline_size, c.vehicle_fuel_type, c.valid_period_content,
    c.driving_licences_issue_unit, c.driving_licences_inspection_record, c.driving_licences_photo1, c.driving_licences_photo2, c.driving_licences_year_photo, c.road_transport_operation_license_code,
    c.road_transport_operation_type, c.road_transport_operation_scope, c.road_transport_operation_audit_record,
    c.road_transport_operation_license_issue_date, c.road_transport_operation_license_issue_unit,
    c.road_transport_operation_license_photo1, c.road_transport_operation_license_photo2,
    c.other_photo1, c.other_photo2, c.verass, c.tranass, c.remark, c.param1, c.param2, c.param3, c.param4,
    c.param5, c.workflow_id, c.audit_status,c.audit_time, c.audit_opinion, c.audit_time, c.create_user, c.create_time,
    c.update_user, c.update_time,c.enable,
    c.vehicle_classification_code,
    c.cardriving_licences_valid_until
      from t_end_car_info c
      where c.id = #{id}
  </select>

  <!-- 根据车辆查询关联的司机 Yan -->
  <select id="getCarCorrelationDriver" parameterType="java.lang.Integer" resultType="hashmap">
    SELECT
    eui.id,
    eui.real_name as driverNames<!-- 关联的司机 -->
    FROM t_end_car_info eci
    LEFT JOIN t_end_user_car_rel eucr ON eucr.endcar_id = eci.id and eucr.user_car_relation_type ='CLSYRSJ' and eucr.data_concel_from IS NULL and eucr.enable=0
    LEFT JOIN (SELECT * FROM t_end_user_info WHERE ENABLE = FALSE) eui ON eui.id = eucr.enduser_id
    WHERE eci.id = #{carId}
  </select>

  <!--查询车辆司机的审核状态 Yan-->
  <select id="getCarAndDriverAuditStatus" parameterType="com.lz.vo.TEndCarInfoVO" resultType="com.lz.dto.CarAndDriverStatusDTO">
    SELECT eui.audit_status, eui.audit_opinion, 'driver' as type, eui.real_name as mark
    FROM t_end_user_info eui
    WHERE eui.id = #{userId} AND eui.audit_status &lt;> 'PASSNODE'
    UNION ALL
    SELECT eci.audit_status, eci.audit_opinion, 'car' as type, eci.vehicle_number as mark
    FROM t_end_car_info eci
    WHERE eci.audit_status &lt;> 'PASSNODE' 
    <if test="endCarId != null">
      AND eci.id IN
      <foreach collection="endCarId" item="carId" index="index" open="(" separator="," close=")">
        #{carId}
      </foreach>
    </if>
  </select>

  <!--查询车辆司机的审核状态 Yan-->
  <select id="getCarsAndDriversAuditStatus" parameterType="com.lz.vo.TEndCarInfoVO" resultType="com.lz.dto.CarAndDriverStatusDTO">
    SELECT eui.audit_status, eui.audit_opinion, 'driver' as type, eui.real_name as mark
    FROM t_end_user_info eui
    WHERE eui.audit_status &lt;> 'PASSNODE'
    and eui.id in
    <foreach collection="enduserId" item="userId" index="index" open="(" separator="," close=")">
      #{userId}
    </foreach>
    UNION ALL
    SELECT eci.audit_status, eci.audit_opinion, 'car' as type, eci.vehicle_number as mark
    FROM t_end_car_info eci
    WHERE eci.audit_status &lt;> 'PASSNODE'
    <if test="endCarId != null">
      AND eci.id IN
      <foreach collection="endCarId" item="carId" index="index" open="(" separator="," close=")">
        #{carId}
      </foreach>
    </if>
  </select>


  <select id="carDistribution" parameterType="com.lz.vo.BigCarInfoVo" resultType="com.lz.vo.BigCarInfoVo">
      SELECT DISTINCT
          tpi.provice_name,
          tci.city_name as name,
          tci.city_plates,
          SUBSTR( tec.vehicle_number, 1, 2 ) AS vs,
          count(0) AS value
      FROM
          t_end_car_info tec
          LEFT JOIN t_city_info tci ON  tci.city_plates like concat('%', SUBSTR( tec.vehicle_number, 1, 2 ),'%')
          LEFT JOIN t_provice_info tpi ON tpi.provice_code = tci.provice_code
      GROUP BY
          vs,tpi.provice_name,tci.city_name,tci.city_plates
      ORDER BY
          value DESC
  </select>

  <select id="selectByClDmIsNull"   resultType="com.lz.model.TEndCarInfo" parameterType="java.lang.String">
      SELECT
      t.*
      FROM
          t_end_car_info t
          where t.enable = 0 and t.vehicle_identification_code is null
          and t.param5 is null
          <if test="vln != null and vln != ''">
            and t.vehicle_number = #{vln}
          </if>
      LIMIT 50
  </select>

  <update id="updateAuditStatusById">
    update
        t_end_car_info
        set audit_status = #{auditStatus},
            audit_opinion = #{auditOpinion},
            audit_time = now(),
            update_user = #{updateUser},
            update_time = now()
    where id = #{id} limit 1

  </update>
</mapper>