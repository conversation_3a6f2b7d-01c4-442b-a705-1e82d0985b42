<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TZtBankCardMapper">
    <select id="selectByAcctNo" resultType="com.lz.model.TZtBankCard">
      SELECT
          tbc.*
        FROM t_zt_bank_card tbc
        where 1=1
            and tbc.enable = 0
            and tbc.acct_no = #{acctNo}
    </select>

    <select id="selectBankCards" resultType="com.lz.dto.BandCardDTO">
        select tzbc.acct_no cardNo, tzbu.is_oneself cardOwner, tzbc.id, concat(tzbc.acct_name, ': ', tzbc.acct_no) card
            from
                t_end_user_info teui
            left join t_enduser_account tea on teui.id = tea.enduser_id
            left join t_zt_bank_user tzbu on tea.account_id = tzbu.account_id
            left join t_zt_bank_card tzbc on tzbu.bank_id = tzbc.id
            left join t_zt_bank_bind_relationship tzbbr on tzbbr.account_bank_id = tzbu.id
            where tea.enduser_id = #{endUserId} and tzbc.enable = 0 and tea.enable = 0 and tzbu.enable = 0 and tzbc.enable = 0 and tzbbr.bind_status = 'BIND'
    </select>


    <select id="selectBankCardInfoByAccountId" parameterType="java.lang.Integer" resultType="com.lz.vo.TZtBankCardVo">
        SELECT
            tzbu.account_id,
            tzbu.is_default,
            tzbu.is_oneself,
            tzbu.id as bankUserId,
            tzbc.*
        FROM
            t_zt_bank_user tzbu
            LEFT JOIN t_zt_bank_card tzbc ON tzbc.id = tzbu.bank_id
        WHERE
            tzbu.ENABLE = 0
            AND tzbc.ENABLE = 0
            AND account_id = #{accountId,jdbcType=INTEGER}
    </select>
</mapper>