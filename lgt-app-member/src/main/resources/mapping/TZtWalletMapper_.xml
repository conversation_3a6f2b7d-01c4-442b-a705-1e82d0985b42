<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TZtWalletMapper">

    <select id="selectByAccountId" parameterType="java.lang.Integer" resultType="com.lz.vo.TZtWalletVO">
        SELECT
            id as walletId,
            account_id,
            zt_account_open_id as openRoleId,
            IFNULL(frozen_amount,0) as frozenAmount,
            account_balance as accountBalance,
            account_balance - frozen_amount as canBalance
        FROM
            t_zt_wallet
        WHERE
            data_source = 'BD' and account_id =#{accountId,jdbcType=INTEGER}
    </select>

    <select id="walletHj" parameterType="java.lang.Integer" resultType="java.util.HashMap">
        SELECT
            item.item_value tradeType,
            tzwcl.trade_type tradeTypeCode,
            sum( tzwcl.amount ) amount
        FROM
            t_zt_wallet_change_log tzwcl
            LEFT JOIN t_zt_wallet tzw ON tzwcl.wallet_id = tzw.id
            LEFT JOIN t_dic_cat_item item ON item.item_code = tzwcl.trade_type
        WHERE
            tzw.account_id = #{accountId,jdbcType=INTEGER}
            AND tzw.data_source = 'BD'
            and tzwcl.`enable` = 0 and tzw.`enable` = 0
        GROUP BY tradeTypeCode,tradeType
    </select>

    <select id="companyWalletCapitalFlow" parameterType="com.lz.vo.CompanyWalletCapitalFlowVO" resultType="com.lz.vo.CompanyWalletCapitalFlow">
        SELECT
            tztzwc.trade_no outerTradeNo,
            tztzwc.trade_time tradeTime,
            item.item_value tradeType,
            tztzwc.trade_type tradeTypeCode,
            tztzwc.amount amount,
            tztzwc.order_business_code innerTradeNo,
            tzao.partner_acc_id thridParySubAccountZC,
            case tztzwc.trade_type
                when 'BTIXIAN' then tztzwc.card_no
                else tzao1.partner_acc_id
            end thridParySubAccountZR,
            tztzwc.param2 innerTradeNo,
            tztzwc.outer_trade_no thridParySubAccountZC,
        CASE
                tztzwc.trade_type
                WHEN 'BPAY' THEN tztzwc.file_url
                WHEN 'BCHONGZHI' THEN tztzwc11.file_url
                WHEN 'CYKFJDZHICHU' THEN tztzwc.file_url
                WHEN 'BTIXIAN' THEN tztzwc.file_url
                WHEN 'BD_SERVICE_PAY' THEN tztzwc.file_url
                WHEN 'TOOL_BALANCE_PAY_ZC' THEN tztzwc.file_url
                ELSE NULL
            END AS file_url
        FROM
            t_zt_wallet_change_log tztzwc
            left join t_zt_wallet_change_log tztzwc11 on tztzwc.trade_no = tztzwc11.trade_no and  tztzwc11.id != tztzwc.id
            LEFT JOIN t_zt_wallet tzw ON tzw.id = tztzwc.wallet_id
            left join t_zt_account_open_info tzao on tzao.account_id = tzw.account_id
            left join t_zt_wallet tzw1 on tzw1.id  = tztzwc.trader_wallet_id
            left join t_zt_account_open_info tzao1 on tzao1.account_id = tzw1.account_id
            LEFT JOIN t_dic_cat_item item ON item.item_code = tztzwc.trade_type
        WHERE
            tzw.data_source = 'BD'
            and tzw.`enable` = 0
            AND tzw.account_id = #{accountId,jdbcType=INTEGER}
        <if test="startTime != null">
            and tztzwc.trade_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and tztzwc.trade_time &lt;= #{endTime}
        </if>
        <if test="tradeType != null and tradeType != ''">
            and tztzwc.trade_type = #{tradeType}
        </if>
        <if test="orderBusinessCode != null and orderBusinessCode != ''">
            and tztzwc.order_business_code like '%${orderBusinessCode}%'
        </if>
        <if test="outerTradeNoArray != null and outerTradeNoArray.length > 0">
            AND tztzwc.trade_no IN
            <foreach collection="outerTradeNoArray" index="index" item="outerTradeNo" open="(" close=")" separator=",">
                #{outerTradeNo}
            </foreach>
        </if>
        order by tradeTime desc
    </select>

    <select id="selectDriverInfo" parameterType="java.lang.Integer" resultType="java.util.HashMap">
       SELECT
        tea.enduser_id AS driverId,
        IFNULL( wa.account_balance, 0 ) AS accountBalance,
        IFNULL( wa.frozen_amount, 0 ) AS frozenAmount,
        IFNULL( wa.entry_amount, 0 ) AS entryAmount,
        IFNULL( wa.withdraw_amount, 0 ) AS withdrawAmount
    FROM
        t_zt_account_open_info com
        LEFT JOIN t_zt_wallet wa ON com.account_id = wa.account_id
        AND wa.data_source = 'CD'
        LEFT JOIN t_enduser_account tea ON tea.account_id = com.account_id
    WHERE
        wa.ENABLE = 0 and tea.enduser_id =#{enduserId,jdbcType=INTEGER} and tea.enable = 0
  </select>

    <select id="driverWalletCapitalFlow" parameterType="com.lz.vo.CompanyWalletCapitalFlowVO" resultType="com.lz.vo.DriverWalletCapitaclFlow">
        SELECT
        tea.phone,
        tea.real_name realName,
        wc.trade_no outerTradeNo,
        wc.trade_time tradeTimeDate,
        item.item_value tradeType,
        wc.amount amount,
        wc.order_business_code innerTradeNo,
        eor.partner_acc_id thridParySubAccountZC,
        case wc.trade_type
            when 'CTIXIAN' then wc.card_no
            else tzao1.partner_acc_id
        end thridParySubAccountZR,
        tea.idcard,
        item2.item_value walletType,
        wc.file_url
        FROM
        t_zt_wallet_change_log wc
        LEFT JOIN t_zt_wallet wa ON wa.id = wc.wallet_id
        LEFT JOIN t_zt_account_open_info eor ON eor.account_id = wa.account_id
        left join t_zt_wallet wa1 on wa1.id = wc.trader_wallet_id
        left join t_zt_account_open_info tzao1 on tzao1.account_id = wa1.account_id
        LEFT JOIN (
        SELECT
        teui.id,
        teui.phone,
        teui.real_name,
        teui.idcard,
        tea.account_id,
        teui.ENABLE
        FROM
        t_end_user_info teui
        LEFT JOIN t_enduser_account tea ON tea.enduser_id = teui.id
        AND tea.`enable` = 0
        ) tea ON tea.account_id = wa.account_id
        LEFT JOIN t_dic_cat_item item ON item.item_code = wc.trade_type
        LEFT JOIN t_dic_cat_item item2 ON item2.item_code = wa.purse_category
        WHERE
         wc.ENABLE = 0
        AND wa.ENABLE = 0
        and tea.id = #{driverId}
        AND wa.purse_category IN ('CDRIVER', 'CMANAGER', 'CCAPTAIN' )
        <if test="startTime != null">
            and wc.trade_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and wc.trade_time &lt;= #{endTime}
        </if>
        <if test="tradeType != null and tradeType != ''">
            and wc.trade_type = #{tradeType}
        </if>
        <if test="innerTradeNo != null and innerTradeNo != ''">
            and wc.order_business_code like '%${innerTradeNo}%'
        </if>
        ORDER BY
        tradeTimeDate DESC
    </select>

    <select id="selectByHxCarrierWalletPage" parameterType="com.lz.vo.CarrierHxOpenRoleSearchVo" resultType="com.lz.vo.CarrierCompanyHxOpenRoleVo">
        SELECT
            wa.id AS walletId,
            tca.account_id,
            ccor.id openId,
            ca.id AS carrierId,
            ca.carrier_name,
            tdci.item_value as channel_id,
            ccor.partner_acc_id subAcc,
        CASE

                WHEN ccor.response_code = 'SUCCESS' THEN
                '可用'
                WHEN ccor.response_code = 'FAIL' THEN
                '不可用'
                WHEN ccor.response_code IS NULL THEN
                '不可用'
            END responseCode,
            wa.account_balance,
            wa.withdraw_amount
        FROM
            t_carrier_info ca
            LEFT JOIN t_carrie_account tca ON tca.carrier_id = ca.id
            LEFT JOIN t_zt_account_open_info ccor ON ccor.account_id = tca.account_id
            AND ccor.user_open_role = 'CA'
            LEFT JOIN t_zt_wallet wa ON wa.account_id = tca.account_id
            AND wa.ENABLE = 0
            AND wa.data_source = 'CA'
            left join t_dic_cat_item tdci on tdci.item_code = ccor.channel_id
        WHERE
            ca.ENABLE = 0
            AND ca.param4 IS NULL
            <if test="carrierName != null and carrierName != ''">
               and ca.carrier_name like CONCAT('%',#{carrierName,jdbcType=VARCHAR},'%')
            </if>
            <if test="channelId != null and channelId  != ''">
               and ccor.channel_id = #{channelId,jdbcType=VARCHAR}
            </if>
            <if test="responseCode != null">
                <if test="responseCode  = 'SUCCESS'">
                   and  ccor.response_code = #{responseCode,jdbcType=VARCHAR}
                </if>
                <if test="responseCode != 'SUCCESS'">
                   and (ccor.response_code = 'FAIL' or ccor.response_code is null)
                </if>
            </if>
    </select>

    <select id="selectCarrierHxWalletLogPage" parameterType="com.lz.vo.CarrierHxOpenRoleSearchVo" resultType="com.lz.vo.CarrierCompanyHxOpenRoleVo">
        SELECT
            wcl.id as walletLogId,
            wcl.wallet_id,
            wcl.trade_no AS tradeNo,
            wcl.amount,
            wcl.trade_time,
            item.item_value tradeType,
            CASE wcl.trade_type
            when 'PSHOURU' then tzao1.partner_acc_id
            when 'CAYFKJDSHOURU' then tzao1.partner_acc_id
            else  ccor.partner_acc_id
            end as outerTradeNo,

            CASE wcl.trade_type
            when 'PSHOURU' then ccor.partner_acc_id
            when 'CAYFKJDSHOURU' then ccor.partner_acc_id
            when 'PTIXIAN' then  wcl.card_no
            else tzao1.partner_acc_id
            END as innerTradeNo,
            wcl.order_business_code AS orderBusinessCode,
            CASE
                wcl.trade_type
                WHEN 'PPAY' THEN
                wcl.file_url
                WHEN 'BCHONGZHI' THEN
                wcl.file_url
                WHEN 'CAYFKJDZHICHU' THEN
                wcl.file_url
                WHEN 'PTIXIAN' THEN
                wcl.file_url
                WHEN 'TOOL_BALANCE_PAY_ZC' THEN
                wcl.file_url
                WHEN 'CA_SERVICE_PAY' THEN
                wcl.file_url
                WHEN 'CARRIERBALANCECOMPANYCHARGE_ZC' THEN
                wcl.file_url ELSE NULL
            END AS file_url,
        DATE_FORMAT(wcl.trade_time, '%Y-%m-%d %H:%i:%s') AS tradeTimeStr
        FROM
            t_zt_wallet_change_log wcl
            LEFT JOIN t_zt_wallet wa ON wa.id = wcl.wallet_id
            LEFT JOIN t_zt_account_open_info ccor ON ccor.account_id = wa.account_id AND ccor.user_open_role = 'CA'
            left join t_zt_wallet tzw1 on tzw1.id  = wcl.trader_wallet_id
            left join t_zt_account_open_info tzao1 on tzao1.account_id = tzw1.account_id
            LEFT JOIN t_dic_cat_item item ON item.item_code = wcl.trade_type
        WHERE
            wcl.ENABLE = 0
            AND wa.purse_category = 'PCARRIER'
            AND ccor.account_id = #{accountId,jdbcType=INTEGER}
            <if test="tradeStartTime != null">
                AND wcl.trade_time >= #{tradeStartTime}
            </if>
            <if test="tradeEndTime != null">
                AND wcl.trade_time &lt;= #{tradeEndTime}
            </if>
            <if test="tradeType != null">
                AND wcl.trade_type = #{tradeType}
            </if>
            <if test="orderBusinessCode != null">
                AND wcl.order_business_code like '%${orderBusinessCode}%'
            </if>
            <if test="idArray != null and idArray.size != 0 ">
                AND wcl.id IN
                <foreach collection="idArray" item="id" index="item" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            order by wcl.trade_time desc
    </select>

    <select id="selectHxPlatformWallet" resultType="com.lz.vo.TZtWalletVO">
        SELECT
            wa.id walletId,
            ccor.account_id accountId,
            ccor.id as openRoleId,
            IFNULL( wa.account_balance, 0 ) accountBalance,
            wa.account_balance - wa.frozen_amount canBalance,
            l.amount AS sr,
            0 AS zc
        FROM
            ( SELECT * FROM t_zt_account_open_info WHERE user_open_role = 'PF' ) ccor
            LEFT JOIN t_zt_wallet wa ON ccor.account_id = wa.account_id
            AND wa.data_source = ccor.user_open_role
            AND wa.data_source = 'PF'
            LEFT JOIN (
            SELECT
                wallet_id,
                IFNULL( SUM( amount ), 0 ) AS amount
            FROM
                t_zt_wallet_change_log
            WHERE
                ENABLE = 0
                AND trade_type IN ( 'CAPFPLATFORM', 'MAPFSERVICESR', 'BDPFSERVICESR', 'PBPSFSHOURU', 'CDPFSERVICESR', 'CCPFSERVICESR' )
            GROUP BY
                wallet_id
            ) l ON wa.id = l.wallet_id
        WHERE
            wa.ENABLE = 0
            AND wa.purse_category = 'PFPLATFORM'
    </select>

    <select id="selectHxPlatformWalletList" parameterType="com.lz.vo.CarrierHxOpenRoleSearchVo" resultType="com.lz.vo.CarrierCompanyHxOpenRoleVo">
        SELECT
            wc.id as walletLogId,
            ccor.account_id,
            wc.trade_no outerTradeNo,
            wc.trade_time tradeTime,
            item.item_value tradeType,
            wc.amount amount,
            ccor.partner_acc_id thridParySubAccountZC,
            tzao1.partner_acc_id thridParySubAccountZR
        FROM
            t_zt_wallet_change_log wc
            LEFT JOIN t_zt_wallet wa ON wa.id = wc.wallet_id
            LEFT JOIN t_zt_account_open_info ccor ON ccor.id = wa.zt_account_open_id AND ccor.user_open_role = 'PF'
            left join t_zt_wallet tzw1 on tzw1.id  = wc.trader_wallet_id
            left join t_zt_account_open_info tzao1 on tzao1.account_id = tzw1.account_id
            LEFT JOIN t_dic_cat_item item ON item.item_code = wc.trade_type
        WHERE
            wc.`enable` = FALSE
            AND wa.purse_category = 'PFPLATFORM'
            AND ccor.account_id = #{accountId,jdbcType=INTEGER}
        <if test="startTime != null">
            and wc.trade_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and wc.trade_time &lt;= #{endTime}
        </if>
        <if test="tradeType != null and tradeType != ''">
            and wc.trade_type = #{tradeType}
        </if>
        <if test="idArray != null and idArray.size != 0 ">
            AND wc.id IN
            <foreach collection="idArray" item="id" index="item" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        order by tradeTime desc
    </select>

    <select id="selectHxAgentWalletList" parameterType="com.lz.vo.CarrierHxOpenRoleSearchVo" resultType="com.lz.vo.DriverCapitalFlow">
        SELECT
            ui.phone,
            ui.real_name realName,
            eor.partner_acc_id outerTradeNo,
            wc.trade_time tradeTimeDate,
            item.item_value tradeType,
            wc.trade_type as tradeTypeCode,
            wc.amount amount,
            wc.order_business_code innerTradeNo,
            case wc.trade_type
                when 'MFFESHOURU' then tzao1.partner_acc_id
                else eor.partner_acc_id
            end thridParySubAccountZC,
            case wc.trade_type
                when 'MANAGER' then wc.card_no
                when 'MFFESHOURU' then eor.partner_acc_id
                else tzao1.partner_acc_id
            end thridParySubAccountZR,
            ui.idcard,
            item2.item_value walletType
        FROM
            t_zt_wallet_change_log wc
            LEFT JOIN t_zt_wallet wa on wa.id = wc.wallet_id
            LEFT JOIN t_zt_account_open_info eor ON eor.id = wa.zt_account_open_id
            left join t_zt_wallet tzw1 on tzw1.id  = wc.trader_wallet_id
            left join t_zt_account_open_info tzao1 on tzao1.account_id = tzw1.account_id
            left join t_enduser_account tea on tea.account_id = eor.account_id
            LEFT JOIN t_end_user_info ui ON ui.id = tea.enduser_id
            LEFT JOIN t_dic_cat_item item ON item.item_code = wc.trade_type
            LEFT JOIN t_dic_cat_item item2 ON item2.item_code = wc.wallet_type
        WHERE
        ui.id = #{driverId}
        and wc.enable = 0
        and wa.enable = 0
        and ui.enable = 0
        and wc.wallet_type = 'CMANAGER'
        <if test="startTime != null">
            and wc.trade_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and wc.trade_time &lt;= #{endTime}
        </if>
        <if test="tradeType != null and tradeType != ''">
            and wc.trade_type = #{tradeType}
        </if>
        <if test="innerTradeNo != null and innerTradeNo != ''">
            and wc.order_business_code like '%${innerTradeNo}%'
        </if>
        order by tradeTimeDate desc
    </select>

    <select id="selectHxAgentWalletSum"  resultType="java.util.HashMap">
        SELECT
            IFNULL(w.account_balance,0) accountBalance,
            IFNULL(w.withdraw_amount,0) withdrawAmount,
            IFNULL(l.amount,0) totalWithdrawal,
            c.id as openRoleId,
            tea.account_id as accountId,
            tea.enduser_id as endUserId,
            CASE
            WHEN c.id IS NULL THEN 3
            ELSE c.STATUS
            END AS ifOpenRole,
			c.response_message as openResponseDesc
        FROM t_enduser_account tea
		left join t_zt_account_open_info c on tea.account_id = c.account_id
		left join (select * from t_zt_wallet where enable  = 0 and data_source = 'CD') w ON w.zt_account_open_id = c.id
        LEFT JOIN (
        SELECT wallet_id ,IFNULL( SUM( amount ), 0 ) as amount
        FROM t_zt_wallet_change_log
        WHERE ENABLE = 0
        AND trade_type in('CTIXIAN','CYFKTX','MANAGER')
        GROUP BY wallet_id
        ) l  on w.id = l.wallet_id
        WHERE tea.enable =0 and  tea.enduser_id = #{endUserId,jdbcType=INTEGER}
    </select>

    <select id="selectWalletByCarrierCompanyId" resultType="com.lz.model.TZtWallet">
        select
            tdw.*
        from t_zt_wallet tdw
        where tdw.account_id = #{accountId} and tdw.data_source = #{dataSource}
          and tdw.enable = 0
    </select>

    <select id="selectWalletByEnduserIdList" resultType="com.lz.model.TZtWallet">
        select
            tdw.*
        from t_zt_wallet tdw
        left join t_enduser_account tea on tea.account_id = tdw.account_id
        left join t_end_user_info tei on tei.id = tea.enduser_id
        where tei.id in
            <foreach collection="list" index="index" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
          and tdw.data_source = 'CD'
          and tei.user_logistics_role like concat('%', 'CTYPEDRVIVER', '%')
          and tea.enable = 0
          and tei.enable = 0
          and tdw.enable = 0
    </select>

    <select id="selectByPartnerAccId" resultType="com.lz.model.TZtWallet">
        select
            tzw.*
        from t_zt_wallet tzw
        left join t_zt_account_open_info tzaoi on tzw.zt_account_open_id = tzaoi.id
        where tzaoi.partner_acc_id = #{partnerAccId} and tzw.enable = 0 and tzaoi.status = 1
    </select>
    <update id="updateStatusByPayCode" parameterType="com.lz.vo.TOrderPayDetailVO">
        update t_order_pay_detail
        set
          return_time = #{returnTime,jdbcType=TIMESTAMP},
          trade_status = #{tradeStatus,jdbcType=VARCHAR},
          error_code = #{errorCode,jdbcType=VARCHAR},
          error_msg = #{errorMsg,jdbcType=VARCHAR},
          inner_trade_no = #{innerTradeNo,jdbcType=VARCHAR},
          update_time = #{returnTime,jdbcType=TIMESTAMP}
        where code = #{code,jdbcType=VARCHAR}
  </update>

    <update id="updateStatusByCode">
    update t_order_pay_info
    set
      order_pay_status = #{status,jdbcType=VARCHAR},
        update_time = now()
    where code = #{code,jdbcType=INTEGER}
  </update>

</mapper>