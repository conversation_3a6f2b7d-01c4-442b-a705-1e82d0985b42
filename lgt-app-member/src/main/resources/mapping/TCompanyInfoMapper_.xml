<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lz.dao.TCompanyInfoMapper" >
  <select id="list" parameterType="com.lz.vo.TCompanyInfoQuery" resultType="com.lz.model.TCompanyInfo">
    select *
    from t_company_info
    where enable = 0
    <if test="companyId!= null">
      and id = #{companyId}
    </if>
    <if test="companyName!= null">
      and company_name = #{companyName}
    </if>

  </select>
  <select id="selectRepeatCompany" parameterType="com.lz.vo.TCompanyInfoQuery" resultType="com.lz.model.TCompanyInfo">
    select *
    from t_company_info
    where enable = 0
    <if test="companyId!= null">
      and id != #{companyId}
    </if>
    <if test="companyName!= null">
      and company_name = #{companyName}
    </if>

  </select>

  <select id="selectByPage" parameterType="com.lz.vo.TCompanyInfoQuery" resultType="com.lz.dto.CompanyInfoDTO">
    select t.*, count(tli.id) lineNumber
    from (
    SELECT
    ci.id,
    ci.company_name,
    ci.business_license_no,
    ci.company_legal_person,
    ci.company_contacts,
    ci.company_contacts_phone,
    a.account_no,
    a.nickname,
    (select count(1) from t_company_project where company_id = ci.id and enable = 0) projectNumber,
    item.item_value	AS auditStatusName,
    ci.audit_status,
    ci.audit_opinion,
    IFNULL(sc.score,'暂无评分') as score,
    ci.create_time as registrationTime,
    IFNULL(tbb.business_name,'未分配') AS businessName
    FROM
    t_company_info ci
    LEFT JOIN t_company_account ca ON ci.id = ca.company_id
    LEFT JOIN t_account a ON ca.account_id = a.id
    LEFT JOIN t_carrier_enduser_company_rel tcecr on tcecr.enduser_company_id = ci.id
    LEFT JOIN t_project_carrier_rel pcr ON pcr.carrier_company_id = tcecr.id
    LEFT JOIN t_company_project tcp on tcp.id = pcr.project_id
    LEFT JOIN t_dic_cat_item item ON ci.audit_status = item.item_code
    LEFT JOIN (select * from t_user_score_info where user_type='BD') sc on ci.id = sc.uid
    LEFT JOIN t_business_company tbc ON ci.id = tbc.company_id
    LEFT JOIN t_business_basic tbb ON tbc.business_basic_id = tbb.id
    where 1=1 and ci.enable = 0 and a.if_main_account = 1
    <if test="businessId!= null">
      AND tbb.business_id = #{businessId}
    </if>
    <if test="companyName != null and companyName.length() >0">
      and ci.company_name like concat('%', #{companyName}, '%')
    </if>
    <if test="businessName != null and businessName.length() >0">
      <choose>
        <when test="businessName == '未分配'">
          and tbb.business_name IS NULL
        </when>
        <otherwise>
          and tbb.business_name like concat('%', #{businessName}, '%')
        </otherwise>
      </choose>
    </if>
    <if test="startTime != null">
      <![CDATA[ and DATE_FORMAT(ci.create_time,'%Y-%m-%d') >= DATE_FORMAT(#{startTime,jdbcType=TIMESTAMP},'%Y-%m-%d') ]]>
    </if>
    <if test="endTime != null">
      <![CDATA[ and  DATE_FORMAT(ci.create_time,'%Y-%m-%d') <= DATE_FORMAT(#{endTime,jdbcType=TIMESTAMP},'%Y-%m-%d')]]>
    </if>
    <if test="companyIdArray != null">
      and ci.id in
      <foreach collection="companyIdArray" item="id" index="index" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
    <if test="phone != null">
      AND a.account_no LIKE CONCAT('%', #{phone}, '%')
    </if>
    GROUP
    BY ci.id, a .account_no,item.item_value,a.nickname,sc.score,tbb.business_name
    ) t
    left join t_line_info tli on t.id = tli.company_id and tli.enable = 0
    GROUP BY t.id, t.account_no,t.auditStatusName,t.nickname,t.score,t.businessName
    order by t.id desc
  </select>

  <select id="selectCompanyOpenInfoList" parameterType="com.lz.vo.CompanyOpenInfoListVO" resultType="com.lz.dto.CompanyOpenInfoDTO">
      SELECT
      tci.id,
      ta.usertype,
      ta.id as accountId,
      tci.company_name,
      tci.business_license_no,
      tci.company_detail_address,
      tci.company_contacts,
      tci.company_contacts_phone,
      tci.company_legal_person,
      case
      when tzao.status = 0 then '注册中'
      when tzao.status = 1 then '注册成功'
      when tzao.status = 2 then '注册失败'
      when tzao.status is null then '未注册'
      end openStatus,
      tzao.response_message,
      tzao.sub_acc,
      case
      when tzao.response_code = 'SUCCESS' then '可用'
      when tzao.response_code = 'FAIL' then '不可用'
      when tzao.response_code is null then '不可用'
      end responseCode
      FROM
      t_account ta
      LEFT JOIN t_company_account tca ON ta.id = tca.account_id
      left join t_company_info tci on tci.id = tca.company_id
      left join t_zt_account_open_info tzao on tzao.account_id = ta.id
      WHERE ta.usertype = 'BD' and ta.if_main_account = 1
      <if test="companyName != null and companyName != ''">
          and tci.company_name like CONCAT('%', #{companyName,jdbcType=VARCHAR}, '%')
      </if>
      <if test="responseCode != null and responseCode != ''">
          <if test="responseCode == 'SUCCESS'">
              and tzao.response_code = #{responseCode,jdbcType=VARCHAR}
          </if>
          <if test="responseCode == 'FAIL'">
              and (tzao.response_code = #{responseCode,jdbcType=VARCHAR} or tzao.response_code is null)
          </if>
      </if>
  </select>

  <select id="applyCompanyOpenInfo" parameterType="com.lz.vo.CompanyOpenInfoDetailsVO" resultType="com.lz.dto.CompanyOpenInfoDetailsDTO">
      SELECT
        ta.id as accountId,
        ta.usertype,
        tci.company_name,
        tci.business_license_no,
        tci.company_legal_person,
        tci.company_contacts,
		tci.company_detail_address,
        tci.company_contacts_phone
    FROM
        t_account ta
        LEFT JOIN t_company_account tca ON ta.id = tca.account_id
        left join t_company_info tci on tci.id = tca.company_id
    WHERE
        ta.usertype = 'BD' and ta.if_main_account = 1
        AND tci.id = #{id,jdbcType=INTEGER}
  </select>

  <select id="enterpriseExport" parameterType="com.lz.vo.TCompanyInfoQuery" resultType="com.lz.dto.CompanyInfoDTO">
      select t.*, count(tli.id) lineNumber
      from (
      SELECT
      ci.id,
      ci.company_name,
      ci.business_license_no,
      ci.company_legal_person,
      ci.company_contacts,
      ci.company_contacts_phone,
      a.account_no,
      a.nickname,
      (select count(1) from t_company_project where company_id = ci.id and enable = 0) projectNumber,
      item.item_value	AS auditStatusName,
      ci.audit_status,
      ci.audit_opinion,
      IFNULL(sc.score,'暂无评分') as score,
      ci.create_time as registrationTime,
      IFNULL(tbb.business_name,'未分配') AS businessName
      FROM
      t_company_info ci
      LEFT JOIN t_company_account ca ON ci.id = ca.company_id
      LEFT JOIN t_account a ON ca.account_id = a.id
      LEFT JOIN t_carrier_enduser_company_rel tcecr on tcecr.enduser_company_id = ci.id
      LEFT JOIN t_project_carrier_rel pcr ON pcr.carrier_company_id = tcecr.id
      LEFT JOIN t_company_project tcp on tcp.id = pcr.project_id
      LEFT JOIN t_dic_cat_item item ON ci.audit_status = item.item_code
      LEFT JOIN (select * from t_user_score_info where user_type='BD') sc on ci.id = sc.uid
      LEFT JOIN t_business_company tbc ON ci.id = tbc.company_id
      LEFT JOIN t_business_basic tbb ON tbc.business_basic_id = tbb.id
      where 1=1 and ci.enable = 0 and a.if_main_account = 1
      <if test="businessId!= null">
          AND tbb.business_id = #{businessId}
      </if>
      <if test="companyName != null and companyName.length() >0">
          and ci.company_name like concat('%', #{companyName}, '%')
      </if>
      <if test="businessName != null and businessName.length() >0">
          <choose>
              <when test="businessName == '未分配'">
                  and tbb.business_name IS NULL
              </when>
              <otherwise>
                  and tbb.business_name like concat('%', #{businessName}, '%')
              </otherwise>
          </choose>
      </if>
      <if test="startTime != null">
          <![CDATA[ and DATE_FORMAT(ci.create_time,'%Y-%m-%d') >= DATE_FORMAT(#{startTime,jdbcType=TIMESTAMP},'%Y-%m-%d') ]]>
      </if>
      <if test="endTime != null">
          <![CDATA[ and  DATE_FORMAT(ci.create_time,'%Y-%m-%d') <= DATE_FORMAT(#{endTime,jdbcType=TIMESTAMP},'%Y-%m-%d')]]>
      </if>
      <if test="idArray != null and idArray.length > 0">
          and ci.id in
          <foreach collection="idArray" item="id" index="index" open="(" close=")" separator=",">
              #{id}
          </foreach>
      </if>
      <if test="phone != null">
          AND a.account_no LIKE CONCAT('%', #{phone}, '%')
      </if>
      GROUP
      BY ci.id, a .account_no,item.item_value,a.nickname,sc.score,tbb.business_name
      ) t
      left join t_line_info tli on t.id = tli.company_id and tli.enable = 0
      GROUP BY t.id, t.account_no,t.auditStatusName,t.nickname,t.score,t.businessName
      order by t.id desc
  </select>

  <select id="selectById" parameterType="java.lang.Integer" resultType="com.lz.dto.CompanyInfoByIdDTO">
        select *,
               tdci.item_value company_logistics_role_name, tdci2.item_value checkStatus
        from t_company_info ci
        left join t_dic_cat_item tdci on ci.company_logistics_role = tdci.id
        left join t_dic_cat_item tdci2 on ci.audit_status = tdci2.id
        where ci.id = #{companyId}
  </select>
  <update id="updateStausByDelete" parameterType="com.lz.model.TCompanyInfo">
    update t_company_info
    <set >
      <if test="enable != null" >
        enable = #{enable},
      </if>
      <if test="updateUser != null" >
        update_user = #{updateUser},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime},
      </if>
    </set>
    where id = #{id}
  </update>

  <select id="selectCompany" resultType="com.lz.dto.CompanyInfoDTO">
    select id, company_name
    from t_company_info
    where enable = 0
  </select>
  <!--根据登录人ID查询所属企业信息-->
  <select id="selectCompanyByUserId" resultType="com.lz.model.TCompanyInfo"  parameterType="java.lang.Integer">
    SELECT
        ci.id,
        ci.company_name
    FROM
        t_line_goods_user_rel lgur
    LEFT JOIN t_account ta ON lgur.account_info_id = ta.id
    LEFT JOIN t_company_info ci ON lgur.company_id = ci.id
    WHERE
        ta.user_id = #{userId}
    AND EXISTS (
        SELECT
            *
        FROM
            (
                SELECT
                    'COMMONSENDORDER' AS type
                UNION
                    SELECT
                        'COMPANYSENDORDER' AS type
            ) a
        WHERE
            lgur.role_code = a.type
    )
  </select>
  <!--根据Sys_user表ID 查询出企业-->
  <select id="selectCompanyBySystemId" resultType="com.lz.common.model.TCompanyInfoDTO"  parameterType="java.lang.Integer">
    SELECT
        tci.id,
        tci.company_name
    FROM
        t_company_info tci
    LEFT JOIN t_company_account tca ON tca.company_id = tci.id
    LEFT JOIN t_account ta ON tca.account_id = ta.id
    WHERE
        ta.user_id = #{userId}
  </select>

  <select id="selectCompanyInfoById" resultMap="BaseResultMap">
    select
      *
    from t_company_info
    where  id = #{id}
  </select>


  <select id="selectCompanyByPage" parameterType="com.lz.vo.TCompanyInfoQuery" resultType="com.lz.dto.CompanyInfoDTO">
    SELECT
    ci.id,
    ci.company_name,
    a.account_no,
    ci.business_license_no,
    ci.company_legal_person,
    ci.company_contacts,
    ci.company_contacts_phone,
    a.id as accountId,
    a.nickname
    FROM
    t_company_info ci
    LEFT JOIN t_company_account ca ON ci.id = ca.company_id
    LEFT JOIN t_account a ON ca.account_id = a.id
    WHERE 1=1 AND ci.enable = FALSE AND a.if_main_account = 1
      <if test="companyId != null">
        and ci.id = #{companyId}
      </if>
    <if test="accountNo != null and accountNo != ''">
      and a.account_no like '%${accountNo}%'
    </if>
    <if test="companyName != null and companyName != ''" >
      and ci.company_name like '%${companyName}%'
    </if>
  </select>

  <select id="selectByCarrierId" parameterType="java.lang.Integer" resultType="com.lz.model.TCompanyInfo">
    SELECT
    *
    FROM
    t_company_info a
    LEFT JOIN t_carrier_enduser_company_rel r on a.id = r.enduser_company_id
    WHERE
    a.ENABLE = 0
    AND r.ENABLE = 0
    AND r.datasouce = 'BD'
    <if test="carrierId != null and carrierId != ''">
      AND r.carrier_id = #{carrierId}
    </if>
  </select>


  <select id="selectByCompanyProject" parameterType="java.lang.Integer" resultType="com.lz.vo.TCompanyProjectVo">
SELECT
  cp.project_name,
  ca.carrier_name,
  pcr.dispatch_fee_coefficient AS dispatchFeeCoefficientDg,
  pcr.if_efficient AS stop_flag,
  cp.pay_method,
  cp.credit_line,
  cp.after_use_left_limit,
  cp.credit_days,
  pcr.update_time,
  pcr.create_time,
  w.frozen_amount,
   IFNULL( toi.noCash, 0 ) AS noCash,
  IFNULL( toi2.withdrawal, 0 ) AS withdrawal,
  IFNULL( toi3.alreadyCash, 0 ) AS alreadyCash,
  IFNULL( toi4.dispatch_fee, 0 ) AS dispatch_fee,
  cp.id
FROM
  t_company_project cp
LEFT JOIN t_project_carrier_rel pcr ON pcr.project_id = cp.id
LEFT JOIN t_carrier_enduser_company_rel tce ON tce.id = pcr.carrier_company_id
LEFT JOIN t_carrier_info ca ON ca.id = tce.carrier_id
LEFT JOIN t_wallet w ON w.carrier_enduser_company_id = pcr.carrier_company_id
AND w.company_project_id = cp.id
LEFT JOIN (
  SELECT
    sum(
      user_confirm_payment_amount
    ) AS noCash,
    sum(dispatch_fee) AS dispatch_fee,
    company_project_id ,carrier_id
  FROM
    t_order_info
  WHERE
    pack_status = 0 and enable = 0
  AND order_execute_status = 'M100'
  and order_pay_status in('M090','M120')
  GROUP BY
    company_project_id ,carrier_id
) toi ON toi.company_project_id = pcr.project_id and toi.carrier_id =ca.id
LEFT JOIN (
  SELECT
    sum(
      user_confirm_payment_amount
    ) AS withdrawal,
    company_project_id,carrier_id
  FROM
    t_order_info
  WHERE
    pack_status = 0 and enable = 0
  AND order_pay_status = 'P110'
  GROUP BY
    company_project_id,carrier_id
) toi2 ON toi2.company_project_id = cp.id and toi2.carrier_id =ca.id
LEFT JOIN (
  SELECT
    sum(
      user_confirm_payment_amount
    ) AS alreadyCash,
    company_project_id,carrier_id
  FROM
    t_order_info
  WHERE
    pack_status = 0 and enable = 0
  AND order_pay_status = 'M130'
  GROUP BY
    company_project_id,carrier_id
) toi3 ON toi3.company_project_id = cp.id and toi3.carrier_id =ca.id
LEFT JOIN (
  SELECT
    sum(dispatch_fee) AS dispatch_fee,
    company_project_id ,carrier_id
  FROM
    t_order_info
  WHERE
    pack_status = 0 and enable = 0
  AND order_execute_status = 'M100'
  GROUP BY
    company_project_id ,carrier_id
) toi4 ON toi4.company_project_id = pcr.project_id and toi4.carrier_id =ca.id
WHERE
  cp. ENABLE = 0 and pcr.enable = 0 and tce.enable = 0 and ca.enable = 0 and w.enable = 0
    AND tce.enduser_company_id = #{companyId}

</select>

    <select id="selectByCompanyProjectAmp" parameterType="java.lang.Integer" resultType="com.lz.vo.TCompanyProjectVo">
SELECT
  cp.project_name,
  ca.carrier_name,
  pcr.dispatch_fee_coefficient AS dispatchFeeCoefficientDg,
  pcr.if_efficient AS stop_flag,
  cp.pay_method,
  cp.credit_line,
  cp.after_use_left_limit,
  cp.credit_days,
  pcr.update_time,
  pcr.create_time,
  w.frozen_amount,
   IFNULL( toi.noCash, 0 ) AS noCash,
  IFNULL( toi2.withdrawal, 0 ) AS withdrawal,
  IFNULL( toi3.alreadyCash, 0 ) AS alreadyCash,
  IFNULL( toi4.dispatch_fee, 0 ) AS dispatch_fee,
  cp.id
FROM
  t_company_project cp
LEFT JOIN t_project_carrier_rel pcr ON pcr.project_id = cp.id
LEFT JOIN t_carrier_enduser_company_rel tce ON tce.id = pcr.carrier_company_id
LEFT JOIN t_carrier_info ca ON ca.id = tce.carrier_id
LEFT JOIN t_wallet w ON w.carrier_enduser_company_id = pcr.carrier_company_id
AND w.company_project_id = cp.id
LEFT JOIN (
  SELECT
    sum(
     tmp.advance_fee
    ) AS noCash,
    oi.company_project_id ,
    oi.carrier_id
  FROM
    t_order_info oi
    LEFT JOIN t_advance_order_tmp tmp on tmp.order_code = oi.code
  WHERE
    oi.pack_status = 0 and oi.enable = 0 and tmp.enable = 0
  AND tmp.order_pay_status in('M090','M120')
  GROUP BY
    oi.company_project_id ,oi.carrier_id
) toi ON toi.company_project_id = pcr.project_id and toi.carrier_id =ca.id
LEFT JOIN (
  SELECT
    sum(
     tmp.advance_fee
    ) AS withdrawal,
    oi.company_project_id ,
    oi.carrier_id
  FROM
    t_order_info oi
    LEFT JOIN t_advance_order_tmp tmp on tmp.order_code = oi.code
  WHERE
    oi.pack_status = 0 and oi.enable = 0 and tmp.enable = 0
  AND tmp.order_pay_status = 'P110'
  GROUP BY
    company_project_id,carrier_id
) toi2 ON toi2.company_project_id = cp.id and toi2.carrier_id =ca.id
LEFT JOIN (
  SELECT
    sum(
     tmp.advance_fee
    ) AS alreadyCash,
    oi.company_project_id ,
    oi.carrier_id
  FROM
    t_order_info oi
    LEFT JOIN t_advance_order_tmp tmp on tmp.order_code = oi.code
  WHERE
    oi.pack_status = 0 and oi.enable = 0 and tmp.enable = 0
  AND tmp.order_pay_status = 'M130'
  GROUP BY
    company_project_id,carrier_id
) toi3 ON toi3.company_project_id = cp.id and toi3.carrier_id =ca.id
LEFT JOIN (
  SELECT
    sum(tmp.advance_dispatch_fee) AS dispatch_fee,
    oi.company_project_id ,
    oi.carrier_id
  FROM
    t_order_info oi
    LEFT JOIN t_advance_order_tmp tmp on tmp.order_code = oi.code
  WHERE
    oi.pack_status = 0 and oi.enable = 0 and tmp.enable = 0
  AND tmp.order_pay_status in('M090','M120','M130','P110')
  GROUP BY
    company_project_id ,carrier_id
) toi4 ON toi4.company_project_id = pcr.project_id and toi4.carrier_id =ca.id
WHERE
  cp. ENABLE = 0 and pcr.enable = 0 and tce.enable = 0 and ca.enable = 0 and w.enable = 0
    AND tce.enduser_company_id = #{companyId}

</select>


  <select id="selectByCompanyProjectPack" parameterType="java.lang.Integer" resultType="com.lz.vo.TCompanyProjectVo">
    SELECT
    cp.id,
    cp.project_name,
    pcr.if_efficient as stop_flag,
    cp.pay_method,
    cp.credit_line,
    cp.after_use_left_limit,
    cp.credit_days,
    pcr.update_time,
    pcr.create_time,
    pcr.dispatch_fee_coefficient as dispatchFeeCoefficientDg,
    w.frozen_amount,
    ca.carrier_name,
    IFNULL( a.noCash, 0 ) AS noCash,
    IFNULL( b.withdrawal, 0 ) AS withdrawal,
    IFNULL( c.alreadyCash, 0 ) AS alreadyCash,
    IFNULL( a.dispatch_fee, 0 ) AS dispatch_fee
    FROM
    t_company_project cp
    LEFT JOIN t_project_carrier_rel pcr on pcr.project_id = cp.id
    LEFT JOIN t_carrier_enduser_company_rel tce ON tce.id = pcr.carrier_company_id
    LEFT JOIN t_carrier_info ca ON ca.id = tce.carrier_id
    LEFT JOIN t_wallet w ON w.carrier_enduser_company_id = pcr.carrier_company_id  AND w.company_project_id = cp.id
    LEFT JOIN(
    SELECT
    sum( toi.user_confirm_payment_amount ) AS noCash,
    sum( toi.dispatch_fee ) AS dispatch_fee,
    toi.company_project_id,
    toi.carrier_id
    FROM
    t_order_pack_info pi
    LEFT JOIN t_order_pack_detail pd ON pi.code = pd.pack_pay_code
    LEFT JOIN  t_order_info toi  ON  pd.order_code = toi.code
    WHERE
    pi.ENABLE = 0 and pd.enable = 0 and toi.enable = 0
    and pi.pack_status = 'PACKPAID'
    AND pi.company_id = #{companyId}
    GROUP BY
    toi.company_project_id, toi.carrier_id
    ) a on a.company_project_id = cp.id and a.carrier_id =ca.id
    LEFT JOIN (
    SELECT
    sum( toi.user_confirm_payment_amount ) AS withdrawal,
    toi.company_project_id,
    toi.carrier_id
    FROM
    t_order_pack_info pi
    LEFT JOIN t_order_pack_detail pd ON pi.code = pd.pack_pay_code
    LEFT JOIN  t_order_info toi  ON  pd.order_code = toi.code
    WHERE
    pi.ENABLE = 0 and pd.enable = 0 and toi.enable = 0
    and pi.pack_status = 'PACKEXTRACTPROCESSED'
    AND pi.company_id = #{companyId}
    GROUP BY
    toi.company_project_id,toi.carrier_id
    ) b ON a.company_project_id = b.company_project_id and a.carrier_id =b.carrier_id
    LEFT JOIN (
    SELECT
    sum( toi.user_confirm_payment_amount ) AS alreadyCash,
    toi.company_project_id,
    toi.carrier_id
    FROM
    t_order_pack_info pi
    LEFT JOIN t_order_pack_detail pd ON pi.code = pd.pack_pay_code
    LEFT JOIN  t_order_info toi  ON  pd.order_code = toi.code
    WHERE
    pi.ENABLE = 0 and pd.enable = 0 and toi.enable = 0
    and pi.pack_status = 'PACKPAID'
    AND pi.company_id = #{companyId}
    GROUP BY
    toi.company_project_id,toi.carrier_id
    ) c ON a.company_project_id = c.company_project_id and a.carrier_id =c.carrier_id
    where cp. company_id = #{companyId}  and cp.enable = 0 and pcr.enable = 0 and tce.enable = 0 and ca.enable = 0
    and w.enable = 0

    <!--SELECT
	a.company_project_id as id,
	a.noCash,
	b.withdrawal,
	c.alreadyCash
	FROM (
		SELECT
			sum( toi.user_confirm_payment_amount ) AS noCash,
			toi.company_project_id
		FROM
			t_order_info toi
			LEFT JOIN t_order_pack_detail pd ON pd.order_code = toi.
			CODE LEFT JOIN ( SELECT * FROM t_order_pack_info WHERE pack_status = 'PACKPAID' ) pi ON pi.CODE = pd.pack_pay_code
		WHERE
			toi.ENABLE = 0
            <if test="companyId != null and companyId != ''">
              AND toi.company_id = #{companyId}
            </if>
		GROUP BY
			toi.company_project_id
	) a
	LEFT JOIN (
		SELECT
			sum( toi.user_confirm_payment_amount ) AS withdrawal,
			toi.company_project_id
		FROM
			t_order_info toi
			LEFT JOIN t_order_pack_detail pd ON pd.order_code = toi.
			CODE LEFT JOIN ( SELECT * FROM t_order_pack_info WHERE pack_status = 'PACKEXTRACTPROCESSED' ) pi ON pi.CODE = pd.pack_pay_code
		WHERE
			toi.ENABLE = 0
            <if test="companyId != null and companyId != ''">
              AND toi.company_id = #{companyId}
            </if>
		GROUP BY
			toi.company_project_id
	) b ON a.company_project_id = b.company_project_id
	LEFT JOIN (
			SELECT
			sum( toi.user_confirm_payment_amount ) AS alreadyCash,
			toi.company_project_id
		FROM
			t_order_info toi
			LEFT JOIN t_order_pack_detail pd ON pd.order_code = toi.
			CODE LEFT JOIN ( SELECT * FROM t_order_pack_info WHERE pack_status = 'PACKWITHDRAW' ) pi ON pi.CODE = pd.pack_pay_code
		WHERE
			toi.ENABLE = 0
            <if test="companyId != null and companyId != ''">
              AND toi.company_id = #{companyId}
            </if>
		GROUP BY
			toi.company_project_id
	) c ON a.company_project_id = c.company_project_id-->
  </select>

    <select id="selectByCompanyProjectAnrCarrierId" parameterType="java.lang.Integer" resultType="com.lz.vo.TCompanyProjectVo">
SELECT
  cp.project_name,
  ca.carrier_name,
  pcr.dispatch_fee_coefficient AS dispatchFeeCoefficientDg,
  pcr.if_efficient AS stop_flag,
  cp.pay_method,
  cp.credit_line,
  cp.after_use_left_limit,
  cp.credit_days,
  pcr.update_time,
  pcr.create_time,
  w.frozen_amount,
   IFNULL( toi.noCash, 0 ) AS noCash,
  IFNULL( toi2.withdrawal, 0 ) AS withdrawal,
  IFNULL( toi3.alreadyCash, 0 ) AS alreadyCash,
  IFNULL( toi4.dispatch_fee, 0 ) AS dispatch_fee,
  cp.id
FROM
  t_company_project cp
LEFT JOIN t_project_carrier_rel pcr ON pcr.project_id = cp.id
LEFT JOIN t_carrier_enduser_company_rel tce ON tce.id = pcr.carrier_company_id
LEFT JOIN t_carrier_info ca ON ca.id = tce.carrier_id
LEFT JOIN t_wallet w ON w.carrier_enduser_company_id = pcr.carrier_company_id
AND w.company_project_id = cp.id
LEFT JOIN (
  SELECT
    sum(
      user_confirm_payment_amount
    ) AS noCash,
    company_project_id ,carrier_id
  FROM
    t_order_info
  WHERE
    pack_status = 0 and enable = 0
  AND order_execute_status = 'M100'
  AND order_pay_status in('M090','M120')
  AND company_id = #{companyId} AND carrier_id = #{carrierId}
  GROUP BY
    company_project_id ,carrier_id
) toi ON toi.company_project_id = pcr.project_id and toi.carrier_id =ca.id
LEFT JOIN (
  SELECT
    sum(
      user_confirm_payment_amount
    ) AS withdrawal,
    company_project_id,carrier_id
  FROM
    t_order_info
  WHERE
    pack_status = 0 and enable = 0
  AND order_pay_status = 'P110'
  AND company_id = #{companyId} AND carrier_id = #{carrierId}
  GROUP BY
    company_project_id,carrier_id
) toi2 ON toi2.company_project_id = cp.id and toi2.carrier_id =ca.id
LEFT JOIN (
  SELECT
    sum(
      user_confirm_payment_amount
    ) AS alreadyCash,
    company_project_id,carrier_id
  FROM
    t_order_info
  WHERE
    pack_status = 0 and enable = 0
  AND order_pay_status = 'M130'
  AND company_id = #{companyId} AND carrier_id = #{carrierId}
  GROUP BY
    company_project_id,carrier_id
) toi3 ON toi3.company_project_id = cp.id and toi3.carrier_id =ca.id
LEFT JOIN (
  SELECT
    sum(dispatch_fee) AS dispatch_fee,
    company_project_id,carrier_id
  FROM
    t_order_info
  WHERE
    pack_status = 0 and enable = 0
  AND order_execute_status = 'M100'
  AND company_id = #{companyId} AND carrier_id = #{carrierId}
  GROUP BY
    company_project_id,carrier_id
) toi4 ON toi4.company_project_id = cp.id and toi4.carrier_id =ca.id
WHERE
  cp. ENABLE = 0 and pcr.enable = 0 and tce.enable = 0 and ca.enable = 0 and w.enable = 0
    AND tce.enduser_company_id = #{companyId} AND tce.carrier_id = #{carrierId}

</select>

    <select id="selectByCompanyProjectAmpAnrCarrierId" parameterType="java.lang.Integer" resultType="com.lz.vo.TCompanyProjectVo">
SELECT
  cp.project_name,
  ca.carrier_name,
  pcr.dispatch_fee_coefficient AS dispatchFeeCoefficientDg,
  pcr.if_efficient AS stop_flag,
  cp.pay_method,
  cp.credit_line,
  cp.after_use_left_limit,
  cp.credit_days,
  pcr.update_time,
  pcr.create_time,
  w.frozen_amount,
   IFNULL( toi.noCash, 0 ) AS noCash,
  IFNULL( toi2.withdrawal, 0 ) AS withdrawal,
  IFNULL( toi3.alreadyCash, 0 ) AS alreadyCash,
  IFNULL( toi4.dispatch_fee, 0 ) AS dispatch_fee,
  cp.id
FROM
  t_company_project cp
LEFT JOIN t_project_carrier_rel pcr ON pcr.project_id = cp.id
LEFT JOIN t_carrier_enduser_company_rel tce ON tce.id = pcr.carrier_company_id
LEFT JOIN t_carrier_info ca ON ca.id = tce.carrier_id
LEFT JOIN t_wallet w ON w.carrier_enduser_company_id = pcr.carrier_company_id
AND w.company_project_id = cp.id
LEFT JOIN (
  SELECT
    sum(
     tmp.advance_fee
    ) AS noCash,
    oi.company_project_id ,
    oi.carrier_id
  FROM
    t_order_info oi
    LEFT JOIN t_advance_order_tmp tmp on tmp.order_code = oi.code
  WHERE
    oi.pack_status = 0 and oi.enable = 0 and tmp.enable = 0 AND oi.company_id = #{companyId} AND oi.carrier_id = #{carrierId}
  AND tmp.order_pay_status in('M090','M120')
  GROUP BY
    oi.company_project_id ,oi.carrier_id
) toi ON toi.company_project_id = pcr.project_id and toi.carrier_id =ca.id
LEFT JOIN (
  SELECT
    sum(
     tmp.advance_fee
    ) AS withdrawal,
    oi.company_project_id ,
    oi.carrier_id
  FROM
    t_order_info oi
    LEFT JOIN t_advance_order_tmp tmp on tmp.order_code = oi.code
  WHERE
    oi.pack_status = 0 and oi.enable = 0 and tmp.enable = 0 AND oi.company_id = #{companyId} AND oi.carrier_id = #{carrierId}
  AND tmp.order_pay_status = 'P110'
  GROUP BY
    company_project_id,carrier_id
) toi2 ON toi2.company_project_id = cp.id and toi2.carrier_id =ca.id
LEFT JOIN (
  SELECT
    sum(
     tmp.advance_fee
    ) AS alreadyCash,
    oi.company_project_id ,
    oi.carrier_id
  FROM
    t_order_info oi
    LEFT JOIN t_advance_order_tmp tmp on tmp.order_code = oi.code
  WHERE
    oi.pack_status = 0 and oi.enable = 0 and tmp.enable = 0 AND oi.company_id = #{companyId} AND oi.carrier_id = #{carrierId}
  AND tmp.order_pay_status = 'M130'
  GROUP BY
    company_project_id,carrier_id
) toi3 ON toi3.company_project_id = cp.id and toi3.carrier_id =ca.id
LEFT JOIN (
  SELECT
    sum(tmp.advance_dispatch_fee) AS dispatch_fee,
    oi.company_project_id ,
    oi.carrier_id
  FROM
    t_order_info oi
    LEFT JOIN t_advance_order_tmp tmp on tmp.order_code = oi.code
  WHERE
    oi.pack_status = 0 and oi.enable = 0 and tmp.enable = 0 AND oi.company_id = #{companyId} AND oi.carrier_id = #{carrierId}
  AND tmp.order_pay_status in('M090','M120','M130','P110')
  GROUP BY
    company_project_id ,carrier_id
) toi4 ON toi4.company_project_id = pcr.project_id and toi4.carrier_id =ca.id
WHERE
  cp. ENABLE = 0 and pcr.enable = 0 and tce.enable = 0 and ca.enable = 0 and w.enable = 0
    AND tce.enduser_company_id = #{companyId} AND tce.carrier_id = #{carrierId}

</select>


  <select id="selectByCompanyProjectPackAnrCarrierId" parameterType="java.lang.Integer" resultType="com.lz.vo.TCompanyProjectVo">
    SELECT
    cp.id,
    cp.project_name,
    pcr.if_efficient as stop_flag,
    cp.pay_method,
    cp.credit_line,
    cp.after_use_left_limit,
    cp.credit_days,
    pcr.update_time,
    pcr.create_time,
    pcr.dispatch_fee_coefficient as dispatchFeeCoefficientDg,
    w.frozen_amount,
    ca.carrier_name,
    IFNULL( a.noCash, 0 ) AS noCash,
    IFNULL( b.withdrawal, 0 ) AS withdrawal,
    IFNULL( c.alreadyCash, 0 ) AS alreadyCash,
    IFNULL( a.dispatch_fee, 0 ) AS dispatch_fee
    FROM
    t_company_project cp
    LEFT JOIN t_project_carrier_rel pcr on pcr.project_id = cp.id
    LEFT JOIN t_carrier_enduser_company_rel tce ON tce.id = pcr.carrier_company_id
    LEFT JOIN t_carrier_info ca ON ca.id = tce.carrier_id
    LEFT JOIN t_wallet w ON w.carrier_enduser_company_id = pcr.carrier_company_id  AND w.company_project_id = cp.id
    LEFT JOIN(
    SELECT
    sum( toi.user_confirm_payment_amount ) AS noCash,
    sum( toi.dispatch_fee ) AS dispatch_fee,
    toi.company_project_id,
    toi.carrier_id
    FROM
    t_order_pack_info pi
    LEFT JOIN t_order_pack_detail pd ON pi.code = pd.pack_pay_code
    LEFT JOIN  t_order_info toi  ON  pd.order_code = toi.code
    WHERE
    pi.ENABLE = 0 and pd.enable = 0 and toi.enable = 0
    and pi.pack_status = 'PACKPAID'
    AND pi.company_id = #{companyId}
    AND pi.carrier_id = #{carrierId}
    GROUP BY
    toi.company_project_id, toi.carrier_id
    ) a on a.company_project_id = cp.id and a.carrier_id =ca.id
    LEFT JOIN (
    SELECT
    sum( toi.user_confirm_payment_amount ) AS withdrawal,
    toi.company_project_id,
    toi.carrier_id
    FROM
    t_order_pack_info pi
    LEFT JOIN t_order_pack_detail pd ON pi.code = pd.pack_pay_code
    LEFT JOIN  t_order_info toi  ON  pd.order_code = toi.code
    WHERE
    pi.ENABLE = 0 and pd.enable = 0 and toi.enable = 0
    and pi.pack_status = 'PACKEXTRACTPROCESSED'
    AND pi.company_id = #{companyId}
    AND pi.carrier_id = #{carrierId}
    GROUP BY
    toi.company_project_id,toi.carrier_id
    ) b ON a.company_project_id = b.company_project_id and a.carrier_id =b.carrier_id
    LEFT JOIN (
    SELECT
    sum( toi.user_confirm_payment_amount ) AS alreadyCash,
    toi.company_project_id,
    toi.carrier_id
    FROM
    t_order_pack_info pi
    LEFT JOIN t_order_pack_detail pd ON pi.code = pd.pack_pay_code
    LEFT JOIN  t_order_info toi  ON  pd.order_code = toi.code
    WHERE
    pi.ENABLE = 0 and pd.enable = 0 and toi.enable = 0
    and pi.pack_status = 'PACKPAID'
    AND pi.company_id = #{companyId}
    AND pi.carrier_id = #{carrierId}
    GROUP BY
    toi.company_project_id,toi.carrier_id
    ) c ON a.company_project_id = c.company_project_id and a.carrier_id =c.carrier_id
    where cp. company_id = #{companyId}  AND tce.carrier_id = #{carrierId} and cp.enable = 0 and pcr.enable = 0 and tce.enable = 0 and ca.enable = 0
    and w.enable = 0
  </select>

  <select id="selectByCompanyAnrCarrierProject" parameterType="java.lang.Integer" resultType="com.lz.vo.TCompanyProjectVo">
  SELECT
  cp.project_name,
  cp.stop_flag,
  cp.pay_method,
  cp.credit_line,
  cp.after_use_left_limit,
  cp.credit_days,
  cp.update_time,
  cp.id
  FROM
  t_company_project cp
    LEFT JOIN t_project_carrier_rel pcr on pcr.project_id = cp.id
	LEFT JOIN t_carrier_enduser_company_rel tce ON tce.id = pcr.carrier_company_id
	LEFT JOIN t_carrier_info ca ON ca.id = tce.carrier_id
  WHERE
  cp.enable = 0
    <if test="companyId != null">
      AND tce.enduser_company_id = #{companyId}
    </if>
    <if test="carrierId != null">
      and tce.carrier_id = #{carrierId}
    </if>
</select>

  <select id="selectCompanyCarrierInfo" resultType="com.lz.dto.CarrierCompanyRelDTO">
    SELECT
    tci.company_name, tci.company_did, tcecr.uid, tci.tax_no
    FROM
    t_company_info tci
    LEFT JOIN t_carrier_enduser_company_rel tcecr on tci.id = tcecr.enduser_company_id
    WHERE
    tci.ENABLE = 0 AND tci.ENABLE = 0 AND tcecr.datasouce = 'BD' and tcecr.enable = 0
      AND tcecr.carrier_id = #{carrierId} and tci.id = #{id}
  </select>

  <update id="updateCompanyUploadStatus">
    update t_company_info
    <set >
      <if test="companyDid != null" >
        company_did = #{companyDid,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByBusinessId" parameterType="java.lang.Integer" resultType="com.lz.dto.CompanyInfoDTO">
    SELECT distinct
	  tp.id as companyId,
	  tp.company_name as companyName
    FROM
	  t_company_info tp
    LEFT JOIN t_business_company tc ON tp.id = tc.company_id
    WHERE
	  tc.business_basic_id = #{id} AND tc.`enable` = 0 AND tp.`enable` = 0
  </select>

  <select id="selectByCompanyName" parameterType="java.lang.String" resultType="com.lz.model.TCompanyInfo">
    SELECT * FROM t_company_info where company_name  like concat('%', #{companyName}, '%') AND enable = 0
  </select>

    <select id="selectByBusinessIdOrCompanyName"  resultType="com.lz.dto.CompanyInfoDTO">
        SELECT distinct
            tp.id as companyId,
            tp.company_name as companyName
        FROM
            t_company_info tp
                LEFT JOIN t_business_company tc ON tp.id = tc.company_id
        WHERE
            tc.business_basic_id = #{id} AND tc.`enable` = 0 AND tp.`enable` = 0
        <if test="companyName != null and companyName != ''">
            AND tp.company_name like concat('%', #{companyName}, '%')
        </if>
    </select>
</mapper>