<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TEndUserInfoMapper">
  <resultMap id="BaseResultMapVo" type="com.lz.vo.TEndUserInfoVO" extends="BaseResultMap">
    <collection property="carNumber" column="id" ofType="com.lz.model.TEndCarInfo" select="selectByCarInfo" ></collection>

  </resultMap>

  <resultMap id="BaseResultMapUserCar" type="com.lz.model.TEndCarInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="Vehicle_Number" jdbcType="VARCHAR" property="vehicleNumber" />
    <result column="LicensePlate_Type_Code"  property="licenseplateTypeCode" />
    <result column="Vehicle_Classification_Code"  property="vehicleClassificationCode" />
    <result column="Vehicle_Tonnage" jdbcType="DOUBLE" property="vehicleTonnage" />
    <result column="Road_Transport_Certificate_Number" jdbcType="VARCHAR" property="roadTransportCertificateNumber" />
    <result column="Trailer_Vehicle_Plate_Number" jdbcType="VARCHAR" property="trailerVehiclePlateNumber" />
    <result column="Owner" jdbcType="VARCHAR" property="owner" />
    <result column="Permit_Number" jdbcType="VARCHAR" property="permitNumber" />
    <result column="use_property" jdbcType="VARCHAR" property="useProperty" />
    <result column="brand_model" jdbcType="VARCHAR" property="brandModel" />
    <result column="vehicle_identification_code" jdbcType="VARCHAR" property="vehicleIdentificationCode" />
    <result column="engine_code" jdbcType="VARCHAR" property="engineCode" />
    <result column="register_date" jdbcType="DATE" property="registerDate" />
    <result column="issue_date" jdbcType="DATE" property="issueDate" />
    <result column="file_code" jdbcType="VARCHAR" property="fileCode" />
    <result column="Approved_bare_people" jdbcType="INTEGER" property="approvedBarePeople" />
    <result column="full_vehicle_quality" jdbcType="DOUBLE" property="fullVehicleQuality" />
    <result column="Approved_bare_quality" jdbcType="DOUBLE" property="approvedBareQuality" />
    <result column="Approved_motor_full_quality" jdbcType="DOUBLE" property="approvedMotorFullQuality" />
    <result column="outline_size" jdbcType="VARCHAR" property="outlineSize" />
    <result column="vehicle_fuel_type" jdbcType="VARCHAR" property="vehicleFuelType" />
    <result column="valid_period_content" jdbcType="VARCHAR" property="validPeriodContent" />
    <result column="driving_licences_issue_unit" jdbcType="VARCHAR" property="drivingLicencesIssueUnit" />
    <result column="driving_licences_photo1" jdbcType="VARCHAR" property="drivingLicencesPhoto1" />
    <result column="driving_licences_photo2" jdbcType="VARCHAR" property="drivingLicencesPhoto2" />
    <result column="Road_transport_operation_license_code" jdbcType="VARCHAR" property="roadTransportOperationLicenseCode" />
    <result column="Road_transport_operation_type" jdbcType="VARCHAR" property="roadTransportOperationType" />
    <result column="Road_transport_operation_scope" jdbcType="VARCHAR" property="roadTransportOperationScope" />
    <result column="Road_transport_operation_audit_record" jdbcType="VARCHAR" property="roadTransportOperationAuditRecord" />
    <result column="Road_transport_operation_license_issue_date" jdbcType="DATE" property="roadTransportOperationLicenseIssueDate" />
    <result column="Road_transport_operation_license_issue_unit" jdbcType="VARCHAR" property="roadTransportOperationLicenseIssueUnit" />
    <result column="Road_transport_operation_license_photo1" jdbcType="VARCHAR" property="roadTransportOperationLicensePhoto1" />
    <result column="Road_transport_operation_license_photo2" jdbcType="VARCHAR" property="roadTransportOperationLicensePhoto2" />
    <result column="other_photo1" jdbcType="VARCHAR" property="otherPhoto1" />
    <result column="other_photo2" jdbcType="VARCHAR" property="otherPhoto2" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="param1" jdbcType="VARCHAR" property="param1" />
    <result column="param2" jdbcType="VARCHAR" property="param2" />
    <result column="param3" jdbcType="VARCHAR" property="param3" />
    <result column="param4" jdbcType="VARCHAR" property="param4" />
    <result column="workflow_id" jdbcType="INTEGER" property="workflowId" />
    <result column="audit_status" jdbcType="VARCHAR" property="auditStatus" />
    <result column="audit_opinion" jdbcType="VARCHAR" property="auditOpinion" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="create_user" jdbcType="INTEGER" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="INTEGER" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="enable" jdbcType="BIT" property="enable" />
  </resultMap>

  <resultMap id="BaseResultMapCar" type="com.lz.vo.TEndUserInfoVO" extends="BaseResultMap">
    <collection property="carNumber" column="enduser_id" select="selectByCar" ofType="com.lz.model.TEndCarInfo"></collection>

  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.lz.model.TEndUserInfo">
    <result column="driving_licences_record" jdbcType="LONGVARCHAR" property="drivingLicencesRecord" />
  </resultMap>

  <resultMap extends="ResultMapWithBLOBs" id="ResultMapWithUserList" type="com.lz.vo.TEndUserInfoVO">
    <result column="balance" jdbcType="DOUBLE" property="balance" />
    <result column="cars" jdbcType="VARCHAR" property="cars" />
    <result column="idcard_status" jdbcType="VARCHAR" property="idcardStatus"/>
    <result column="idcard_opinion" jdbcType="VARCHAR" property="idcardOpinion"/>
    <result column="certificate_status" jdbcType="VARCHAR" property="certificateStatus"/>
    <result column="certificate_status" jdbcType="VARCHAR" property="certificateOpinion"/>
    <result column="driving_licences_status" jdbcType="VARCHAR" property="drivingLicencesStatus"/>
    <result column="driving_licences_opinion" jdbcType="VARCHAR" property="drivingLicencesOpinion"/>
  </resultMap>

    <sql id="End_User_Base_Column_List">
    eui.id, eui.driver_did, eui.real_name, eui.short_name, eui.org_name, eui.user_logistics_role, eui.idcard, eui.idcard_valid_beginning, eui.idcard_valid_until,
    eui.idcard_photo1, eui.idcard_photo2, eui.phone, eui.certificate_no, eui.certificate_category_code, eui.certificate_category_name,
    eui.certificate_first_issue_time, eui.certificate_valid_beginning, eui.certificate_valid_until,
    eui.certificate_issue_unit, eui.certificate_photo1, eui.certificate_photo2, eui.approve_driving_type,
    eui.driving_licences_first_issue_time, eui.driving_licences_valid_beginning, eui.driving_licences_valid_until,
    eui.driving_licences_code, eui.driving_licences_issue_unit, eui.driving_licences_photo1, eui.driving_licences_photo2,
    eui.other_photo1, eui.other_photo2, eui.remark, eui.param1, eui.param2, eui.param3, eui.param4, eui.workflow_id,
    eui.audit_status, eui.audit_opinion, eui.audit_time, eui.create_user, eui.create_time, eui.update_user, eui.update_time,
    eui.`enable`, eui.issuer, eui.serial_number, eui.begin_time, eui.end_time, eui.uploaded_status as uploaded_status1, eui.uploaded_time, eui.uploaded_data,eui.address,eui.address_state
  </sql>

    <select id="selectByPage_COUNT" parameterType="com.lz.vo.TEndUserInfoSearchVO" resultType="Long">
        SELECT
          count( 0 )
        FROM
          t_end_user_info eui
        WHERE
          eui.ENABLE = 0
        <if test="userLogisticsRole != null and userLogisticsRole != ''">
            and eui.user_logistics_role like '%${userLogisticsRole}%'
        </if>
        <if test="addressState != null and addressState != ''">
            and eui.address_state = #{addressState}
        </if>
        <if test="startTime != null">
            <![CDATA[ and DATE_FORMAT(eui.create_time,'%Y-%m-%d') >= DATE_FORMAT(#{startTime,jdbcType=TIMESTAMP},'%Y-%m-%d') ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[ and  DATE_FORMAT(eui.create_time,'%Y-%m-%d') <= DATE_FORMAT(#{endTime,jdbcType=TIMESTAMP},'%Y-%m-%d')]]>
        </if>
        <if test="updateStartTime != null">
            <![CDATA[ and DATE_FORMAT(eui.update_time,'%Y-%m-%d') >= DATE_FORMAT(#{updateStartTime,jdbcType=TIMESTAMP},'%Y-%m-%d') ]]>
        </if>
        <if test="updateEndTime != null">
            <![CDATA[ and  DATE_FORMAT(eui.update_time,'%Y-%m-%d') <= DATE_FORMAT(#{updateEndTime,jdbcType=TIMESTAMP},'%Y-%m-%d')]]>
        </if>
        <if test="idArray != null and idArray.length!=0">
            and eui.id IN
            <foreach collection="idArray" index="index" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="idCard != null and idCard != ''">
            and eui.idcard like concat('%',  #{idCard,jdbcType=VARCHAR}, '%')
        </if>
        <if test="username != null and  username != ''">
            and eui.real_name like '%${username}%'
        </if>
        <if test="phone != null and phone != ''">
            and eui.phone like concat('%',  #{phone,jdbcType=VARCHAR}, '%')
        </if>
        <if test="auditStatus != null and auditStatus != ''">
            and eui.audit_status = #{auditStatus,jdbcType=VARCHAR}
        </if>
    </select>

  <select id="selectByPage" parameterType="com.lz.vo.TEndUserInfoSearchVO" resultMap="ResultMapWithUserList">
      SELECT
      <include refid="End_User_Base_Column_List"/>,
      tdci2.item_value uploaded_status,
      tdci.item_value auditStatusValue,
      tdci3.item_value idcard_status, teuai.idcard_opinion,
      tdci4.item_value certificate_status, teuai.certificate_opinion,
      tdci5.item_value driving_licences_status, teuai.driving_licences_opinion
      FROM
      t_end_user_info eui
      LEFT JOIN t_dic_cat_item tdci on tdci.item_code = eui.audit_status
      LEFT JOIN t_dic_cat_item tdci2 on tdci2.item_code = eui.uploaded_status
      left join t_end_user_audit_info teuai on eui.id = teuai.end_user_id
      LEFT JOIN t_dic_cat_item tdci3 on tdci3.item_code = teuai.idcard_status
      LEFT JOIN t_dic_cat_item tdci4 on tdci4.item_code = teuai.certificate_status
      LEFT JOIN t_dic_cat_item tdci5 on tdci5.item_code = teuai.driving_licences_status
      WHERE
      eui.ENABLE = 0
      <if test="userLogisticsRole != null and userLogisticsRole != ''">
          and eui.user_logistics_role like '%${userLogisticsRole}%'
      </if>
      <if test="addressState != null and addressState != ''">
          and eui.address_state = #{addressState}
      </if>
      <if test="startTime != null">
          <![CDATA[ and DATE_FORMAT(eui.create_time,'%Y-%m-%d') >= DATE_FORMAT(#{startTime,jdbcType=TIMESTAMP},'%Y-%m-%d') ]]>
      </if>
      <if test="endTime != null">
          <![CDATA[ and  DATE_FORMAT(eui.create_time,'%Y-%m-%d') <= DATE_FORMAT(#{endTime,jdbcType=TIMESTAMP},'%Y-%m-%d')]]>
      </if>
      <if test="updateStartTime != null">
          <![CDATA[ and DATE_FORMAT(eui.update_time,'%Y-%m-%d') >= DATE_FORMAT(#{updateStartTime,jdbcType=TIMESTAMP},'%Y-%m-%d') ]]>
      </if>
      <if test="updateEndTime != null">
          <![CDATA[ and  DATE_FORMAT(eui.update_time,'%Y-%m-%d') <= DATE_FORMAT(#{updateEndTime,jdbcType=TIMESTAMP},'%Y-%m-%d')]]>
      </if>
      <if test="idArray != null and idArray.length!=0">
          and eui.id IN
          <foreach collection="idArray" index="index" item="id" open="(" close=")" separator=",">
              #{id}
          </foreach>
      </if>
      <if test="idCard != null and idCard != ''">
          and eui.idcard like concat('%',  #{idCard,jdbcType=VARCHAR}, '%')
      </if>
      <if test="username != null and  username != ''">
          and eui.real_name like '%${username}%'
      </if>
      <if test="phone != null and phone != ''">
          and eui.phone like concat('%',  #{phone,jdbcType=VARCHAR}, '%')
      </if>
      <if test="auditStatus != null and auditStatus != ''">
          and eui.audit_status = #{auditStatus,jdbcType=VARCHAR}
      </if>
      order by eui.id DESC
  </select>

  <update id="deleteByUserID" parameterType="java.util.ArrayList">
    update t_end_user_info
    set `enable` = 1
    <where>
      id IN
      <foreach collection="array" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </where>
  </update>

  <update id="approval" parameterType="com.lz.vo.ApprovalVO">
    update t_end_user_info
    set `audit_status` = #{status},
    audit_opinion = #{msg}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectEndUser" parameterType="com.lz.vo.TEndUserInfoSearchVO" resultType="com.lz.dto.EndUserDTO">
    select teui.id enduserId, teui.real_name, teui.phone, teui.idcard enduserIdCard,
    teus.id enduser_status_id, teus.user_status
    from t_end_user_info teui
    LEFT JOIN t_enduser_account tea ON tea.enduser_id = teui.id
    LEFT JOIN t_end_user_status teus ON tea.enduser_id = teus.enduser_id
    left join t_account ta on ta.id = tea.account_id

    where
    teui.user_logistics_role = #{userLogisticsRole}
    <if test="accountNo != null and accountNo.length > 0"> and  ta.account_no like concat(#{accountNo}, '%') </if>
    <if test="userStatus != null"> and teus.user_status = #{userStatus} </if>
    and teui.enable = 0 and tea.enable = 0
  </select>

  <select id="selectManageByCompanyId" parameterType="java.lang.Integer" resultType="com.lz.dto.EndUserDTO">
  select DISTINCT teui.id endAgentId,teui.real_name endAgentName, teui.phone endAgentPhone,
  teui.idcard endAgentIdCard
  from t_company_station_rel tcsr
  left join t_end_user_info teui on tcsr.end_user_station_id = teui.id
  where tcsr.enable = 0 and tcsr.enable = 0 and teui.enable = 0
  <if test="companyId != null ">
    and tcsr.company_id = #{companyId}
  </if>
  </select>

  <select id="selectManage" parameterType="com.lz.vo.TEndUserInfoSearchVO" resultType="com.lz.dto.EndUserDTO">
    select DISTINCT
      teui.id as endAgentId,
      concat(teui.real_name, ' / ', teui.phone) as endAgentName,
      teui.phone as endAgentPhone,
      teui.idcard as endAgentIdCard
    from t_company_station_rel tcsr
    left join t_end_user_info teui on tcsr.end_user_station_id = teui.id
    where tcsr.enable = 0 and teui.enable = 0
    <if test="companyId != null ">
      and tcsr.company_id = #{companyId}
    </if>
    <if test="companyIds != null">
      AND tcsr.company_id IN
      <foreach collection="companyIds" index="index" item="companyId" open="(" separator="," close=")">
        #{companyId}
      </foreach>
    </if>
    <if test="param != null and param.length > 0">
      and (teui.real_name like concat('%', #{param}, '%') or teui.phone like concat('%', #{param}, '%'))
    </if>

  </select>

    <!--设置配置GROUP_CONCAT 参数大小 10240-->
    <select id="setGroupConcatMaxLen">
        SET SESSION group_concat_max_len=10240;
    </select>
  <select id="selectByCarNumber" parameterType="com.lz.vo.TEndUserInfoVO" resultMap="BaseResultMapVo">
      select t1.*, t.cp from t_end_user_info t1
      LEFT JOIN (
      SELECT ss.* FROM (
      SELECT a.*,GROUP_CONCAT(c.vehicle_number) cp FROM t_end_user_info a
      LEFT JOIN t_end_user_car_rel b ON a.id=b.enduser_id
      LEFT JOIN t_end_car_info c ON b.endcar_id = c.id
      WHERE a.user_logistics_role  LIKE '%CTYPEBOSS%'
      and a.enable = 0
      AND b.data_concel_from IS NULL
      and b.user_car_relation_type = 'CLSYRCLB'
      and b.enable = 0
      and c.enable=0
      GROUP BY a.id
      ) ss ) t on t1.id = t.id
      WHERE 1=1 and t1.user_logistics_role LIKE '%CTYPEBOSS%' and t1.enable = 0
      <if test="vehicleNumber != null and vehicleNumber != ''">
          <!-- and FIND_IN_SET(#{vehicleNumber},ss.cp) -->
          and t.cp LIKE '%${vehicleNumber}%'
      </if>
      <if test="realName != null and realName != ''">
          and t1.real_name like '%${realName}%'
      </if>
      <if test="phone != null and phone != ''">
          and t1.phone LIKE '%${phone}%'
      </if>
      <if test="auditStatus != null and auditStatus != ''">
          and t1.audit_status = #{auditStatus}
      </if>
  </select>

  <select id="selectByCarInfo" parameterType="java.lang.Integer" resultMap="BaseResultMapUserCar">
   SELECT
	  b.*
   FROM
	  t_end_car_info b
   LEFT JOIN  t_end_user_car_rel a ON b.id = a.endcar_id

   WHERE
      a.enduser_id= #{endUserid}
  </select>

  <select id="selectEnduserByACoount" parameterType="com.lz.vo.TEndUserInfoSearchVO" resultType="com.lz.dto.EndUserDTO">
    select teui.id enduserId, teui.real_name, teui.phone, teui.org_name, tea.id enduser_account_id,
    tdci.item_code userLogisticsRole, tdci.item_value userLogisticsRoleValue,
    teui.idcard, teui.idcard_valid_until, teui.idcard_photo1, teui.idcard_photo2,teui.audit_status,audit_opinion,teui.audit_time
    from t_end_user_info teui
    left join t_enduser_account tea on teui.id = tea.enduser_id
    left join t_account ta on ta.id = tea.account_id
    left join t_dic_cat_item tdci on tdci.item_code = teui.user_logistics_role
    where ta.account_no = #{accountNo}
    <if test="userLogisticsRole != null">
      and teui.user_logistics_role = #{userLogisticsRole}
    </if>
    and tea.enable = 0
  </select>

  <select id="selectDriverByPage" parameterType="com.lz.vo.DriverListPageVO" resultType="com.lz.dto.TDriverDto">
      SELECT
      ui.id,
      ui.real_name realName,
      ui.phone,
      wa.accountBalance,
      IFNULL(jw.accountBalance, 0) as jdAccountBalance,
      IFNULL(zt.accountBalance, 0) as ztAccountBalance,
      IFNULL(wa.withdrawAmount, 0) + IFNULL(jw.withdrawAmount, 0) + IFNULL(zt.withdrawAmount, 0) as withdrawAmount,
      IFNULL(wa.totalWithdrawal, 0) + IFNULL(jw.totalWithdrawal, 0) + IFNULL(zt.totalWithdrawal, 0) as totalWithdrawal,
      ui.idcard,
      tea.account_id,
      tdci.item_value userLogisticsRole
      FROM
      t_end_user_info ui
      left join t_enduser_account tea on tea.enduser_id = ui.id
      LEFT JOIN (
          SELECT
              IFNULL( SUM( w.account_balance ), 0 ) accountBalance,
              IFNULL( SUM( w.withdraw_amount ), 0 ) withdrawAmount,
              IFNULL( SUM( l.amount ), 0 ) totalWithdrawal,
              c.enduser_company_id
          FROM t_wallet w
          LEFT JOIN (SELECT * FROM t_carrier_enduser_company_rel WHERE datasouce = "CD") c
                ON w.carrier_enduser_company_id = c.id and w.datasource = c.datasouce
          LEFT JOIN (
              SELECT wallet_id ,IFNULL( SUM( amount ), 0 ) as amount
              FROM t_wallet_change_log
              WHERE ENABLE = 0
              AND trade_type in('CTIXIAN','CYFKTX','MANAGER')
              GROUP BY wallet_id
          ) l  on w.id = l.wallet_id
          WHERE   w.`enable` = FALSE
          and w.datasource = 'CD'
          GROUP by c.enduser_company_id
      ) wa  	ON ui.id = wa.enduser_company_id
      LEFT JOIN (
      SELECT
      IFNULL( SUM( w.account_balance ), 0 ) accountBalance,
      IFNULL( SUM( w.withdraw_amount ), 0 ) withdrawAmount,
      IFNULL( SUM( l.amount ), 0 ) totalWithdrawal,
      c.end_user_id
      FROM t_jd_wallet w
      LEFT JOIN  t_end_user_open_role c ON w.open_role_id = c.id
      LEFT JOIN (
      SELECT jd_wallet_id ,IFNULL( SUM( amount ), 0 ) as amount
      FROM t_jd_wallet_change_log
      WHERE ENABLE = 0
      AND trade_type in('CTIXIAN','CYFKTX','MANAGER')
      GROUP BY jd_wallet_id
      ) l  on w.id = l.jd_wallet_id
      WHERE   w.enable = 0
      and w.data_source = 'CD'
      GROUP by c.id
      ) jw  	ON ui.id = jw.end_user_id
      LEFT JOIN (
      SELECT
      IFNULL( SUM( w.account_balance ), 0 ) accountBalance,
      IFNULL( SUM( w.withdraw_amount ), 0 ) withdrawAmount,
      IFNULL( SUM( l.amount ), 0 ) totalWithdrawal,
      w.account_id
      FROM
      t_zt_wallet w
      LEFT JOIN t_zt_account_open_info c ON w.account_id = c.account_id
      LEFT JOIN (
      SELECT
      wallet_id,
      IFNULL( SUM( amount ), 0 ) AS amount
      FROM
      t_zt_wallet_change_log
      WHERE
      ENABLE = 0
      AND trade_type IN ( 'CTIXIAN', 'CYFKTX', 'MANAGER' )
      GROUP BY
      wallet_id
      ) l ON w.id = l.wallet_id
      WHERE
      w.ENABLE = 0
      AND w.data_source = 'CD'
      GROUP BY
      c.id
      ) zt ON zt.account_id = tea.account_id
      LEFT JOIN t_dic_cat_item tdci  ON ui.user_logistics_role = tdci.item_code
      WHERE  ui.ENABLE = FALSE
    <if test="realName != null and realName != ''">
      and ui.real_name like '%${realName}%'
    </if>
    <if test="phone != null and phone != ''">
      and ui.phone like '%${phone}%'
    </if>
  </select>

  <select id="selectDriverByPageLjtx" parameterType="com.lz.vo.DriverListPageVO" resultType="com.lz.dto.TDriverDto">
    SELECT
        ui.id,
        IFNULL( SUM( wcl.amount ), 0 ) totalWithdrawal
    FROM
        t_end_user_info ui
        LEFT JOIN ( SELECT * FROM t_carrier_enduser_company_rel WHERE datasouce = "CD" ) com ON ui.id = com.enduser_company_id
        LEFT JOIN ( SELECT * FROM t_wallet WHERE `enable` = FALSE AND purse_category = 'CDRIVER' ) wa ON com.id = wa.carrier_enduser_company_id
        AND wa.datasource = com.datasouce
        LEFT JOIN ( SELECT * FROM t_wallet_change_log WHERE ENABLE = 0 AND trade_type = 'CTIXIAN' AND wallet_type = 'CDRIVER' ) wcl ON wcl.wallet_id = wa.id
    WHERE
        ui.ENABLE = FALSE
    GROUP BY
        ui.id
  </select>

  <select id="selectDriverInfo" parameterType="java.lang.Integer" resultType="java.util.HashMap">
    SELECT
        com.carrier_id carrierId,
        cpi.id as driverId,
        com.thrid_pary_sub_account account,
        ci.carrier_name carrierName,
        sum( IFNULL( wa.account_balance, 0 ) ) accountBalance,
        sum( IFNULL( wa.frozen_amount, 0 ) ) frozenAmount,
        sum( IFNULL( wa.entry_amount, 0 ) ) entryAmount,
        sum( IFNULL( wa.withdraw_amount, 0 ) ) withdrawAmount,
        cpi.real_name realName
    FROM
        ( SELECT * FROM t_carrier_enduser_company_rel WHERE datasouce = "CD" AND enduser_company_id = #{enduserId} ) com
        LEFT JOIN t_wallet wa ON com.id = wa.carrier_enduser_company_id
        AND wa.datasource = com.datasouce
        LEFT JOIN t_carrier_info ci ON ci.id = com.carrier_id
        LEFT JOIN t_end_user_info cpi ON cpi.id = com.enduser_company_id
    WHERE
        wa.ENABLE = FALSE
        AND wa.purse_category = 'CCARBOSS'
        OR wa.purse_category = 'CDRIVER'
        OR wa.purse_category = 'CMANAGER'
        OR wa.purse_category = 'CCAPTAIN'
        AND wa.company_project_id IS NULL
        AND pid IS NULL
    GROUP BY
        carrierId,
        account,
        carrierName
  </select>

  <!--运单打包： 获取用户所有银行卡 Yan-->
  <select id="getUserBankCard" parameterType="java.lang.Integer" resultType="hashmap" >
    SELECT
        DISTINCT IF (
                      IFNULL(tbc.card_owner, TRUE),
                      tbc.card_no,
                      CONCAT(tbc.card_owner, ': ', tbc.card_no)
                    ) AS card, tbc.card_no cardNo, tbc.id
    FROM t_end_user_info eui
    LEFT JOIN t_enduser_account tea ON tea.enduser_id = eui.id
    LEFT JOIN t_bank_card tbc ON tbc.account_id = tea.account_id
    WHERE eui.id = #{id} and tbc.`enable`= 0 and tea.enable = 0
  </select>
  <!--判断C端用户是否有银行卡 Yan  true没有   false有-->
  <select id="judgeAgentCar" parameterType="java.lang.Integer" resultType="java.lang.Boolean">
    SELECT
      ISNULL(tbc.card_no)
    FROM t_end_user_info eui
    LEFT JOIN t_bank_card tbc ON eui.idcard = tbc.card_owner_idcard
    WHERE eui.id = #{id}
  </select>

  <select id="selectEnduserInfo" parameterType="java.lang.Integer" resultType="com.lz.dto.EndUserDTO">
    select teui.id, teui.real_name, teui.idcard, teui.audit_status, tdci.item_value auditValue
    from t_end_user_info teui
    left join t_dic_cat_item tdci on teui.audit_status = tdci.item_code
    where teui.id = #{enduserId} and teui.enable = 0
  </select>
  <!--APP端 会员详情-获取会员信息 Yan-->
  <select id="getMemberDetailInfo" parameterType="java.lang.Integer" resultType="com.lz.dto.AppSearchMemberInfoDTO">
    SELECT
        dci.item_value as driverStatus, <!-- 司机审核状态 -->
        eui.audit_opinion, <!--审核意见-->
        eui.phone, <!-- 司机手机号-->
        eui.idcard, <!-- 身份证号-->
        eui.real_name, <!-- 姓名-->
        eui.idcard_photo1, <!--身份证-->
        eui.idcard_photo2, <!-- 身份证-->
        eui.driving_licences_photo1, <!--驾驶证-->
        eui.driving_licences_photo2, <!-- 驾驶证-->
        eui.certificate_photo1, <!-- 资格证-->
        eui.certificate_photo2, <!-- 资格证-->
        eui.certificate_valid_until,
        eui.user_logistics_role,
        eui.audit_status,
        tea.account_id
    FROM t_end_user_info eui
    LEFT JOIN t_dic_cat_item dci ON eui.audit_status = dci.item_code
    LEFT JOIN t_enduser_account tea ON tea.enduser_id = eui.id
    WHERE eui.id = #{endUserId}  and eui.enable =0 and tea.enable = 0
    <!-- AND EXISTS (
     SELECT
       eucr.id
     FROM  t_end_user_car_rel eucr
     WHERE eucr.enduser_id = eui.id AND eucr.`enable` = 0
     LIMIT 1
   ) -->
</select>

<select id="getCarOwnerInfo" parameterType="java.lang.Integer" resultType="com.lz.dto.AppSearchMemberInfoDTO">
    SELECT
    dci.item_value as driverStatus, <!-- 车主审核状态 -->
    eui.audit_opinion, <!--审核意见-->
    eui.phone, <!-- 司机手机号-->
    eui.idcard, <!-- 身份证号-->
    eui.real_name, <!-- 姓名-->
    eui.idcard_photo1, <!--身份证-->
    eui.idcard_photo2, <!-- 身份证-->
    eui.driving_licences_photo1, <!--驾驶证-->
    eui.driving_licences_photo2, <!-- 驾驶证-->
    eui.certificate_photo1, <!-- 资格证-->
    eui.certificate_photo2, <!-- 资格证-->
    eui.user_logistics_role,
    eui.audit_status,
    eui.id,
    eucr.certificate_type,
    eucr.certificate_doc1, <!--车主证明-->
    eucr.certificate_doc2, <!--车主证明-->
    tea.account_id
    FROM t_end_user_info eui
    LEFT JOIN t_dic_cat_item dci ON eui.audit_status = dci.item_code
    LEFT JOIN t_end_user_car_rel eucr ON eui.id = eucr.enduser_id
    LEFT JOIN t_enduser_account tea ON tea.enduser_id = eui.id
    WHERE  eucr.endcar_id = #{endCarId}
    and eui.enable = 0
    and eucr.user_car_relation_type = 'CLSYRCLB'
    and eucr.enable = 0
    and tea.enable = 0
    and eucr.data_concel_from is null
    and eucr.audit_status='PASSNODE'
</select>
<select id="findphone" parameterType="java.lang.String" resultMap="BaseResultMap">
 SELECT * FROM `t_end_user_info` WHERE phone= #{phone,jdbcType=VARCHAR} AND enable=0
</select>
<select id="findIdCard" parameterType="java.lang.String" resultMap="BaseResultMap">
 SELECT * FROM `t_end_user_info` WHERE idcard= #{idcard,jdbcType=VARCHAR} AND enable=0
</select>

<select id="findByEndUserId" parameterType="java.lang.Integer" resultType="com.lz.model.TEnduserAccount">
select ea.*
 from t_enduser_account ea
 left join t_end_user_info teui on teui.id = ea.enduser_id
 where ea.enduser_id = #{endUserId} and ea.enable=0 and teui.enable = 0
</select>

<select id="selectByWalltId" resultType="com.lz.model.TWallet">
 select w.* from
    t_carrier_enduser_company_rel cr
 left join t_wallet w on cr.id = w.carrier_enduser_company_id
 where cr.carrier_id = #{carrierId} and  cr.enduser_company_id = #{endUserId} and  cr.enable = 0
   and w.purse_category = #{purseCategory} and w.datasource = 'CD'
</select>


    <select id="selectByEndUserIdAndBankInfo" parameterType="java.lang.Integer" resultType="com.lz.dto.BankCardDTO">
     SELECT
     bc.*, eui.phone
 FROM
     t_end_user_info eui
     LEFT JOIN t_enduser_account ea ON eui.id = ea.enduser_id
     LEFT JOIN t_bank_card bc ON bc.account_id = ea.account_id
     where bc.if_default = 1 and bc.enable = 0 and eui.id = #{endUserId} and ea.enable=0
</select>
<!--判断姓名和身份证号是否存在数据库里 Yan-->
  <select id="judgeRealNameIdcardIsExists" parameterType="java.lang.String" resultType="boolean">
    SELECT NOT ISNULL((
        SELECT
            eui.id
        FROM t_end_user_info eui
        WHERE eui.idcard = #{idcard} AND eui.real_name = #{realName}
    )) AS ISEXIST
  </select>

  <select id="selectByEndUserIdList" parameterType="hashmap" resultType="com.lz.model.TEndUserInfo">
    select i.* from t_end_user_info i where
    i.id in
    <foreach collection="endIdList" index="index" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </select>

  <!--根据手机号判断，用户是否已申请合同电子签名 Yan -->
  <select id="judgeEndUserIfOnlineSign" resultType="com.lz.dto.JudgeEndUserDTO">
    SELECT
        eui.id, <!-- 用户ID -->
        eui.real_name AS realName, <!-- 用户真实姓名 -->
	    eui.idcard AS idcard, <!-- idcard  -->
        eui.if_online_sign AS ifOnlineSign <!-- 是否已申请合同电子签名 -->
    FROM t_account ta
    LEFT JOIN t_enduser_account tea ON ta.id = tea.account_id
    LEFT JOIN t_end_user_info eui ON tea.enduser_id = eui.id
    WHERE ta.account_no = #{phone} and ta.`enable` = 0 and tea.`enable` = 0 and eui.`enable` = 0
      and eui.id = #{endUserId}
  </select>

  <update id="updateEnduserInfoForFeign" parameterType="com.lz.vo.TEndUserInfoVO">
    update t_end_user_info
    set real_name = #{realName},
        phone = #{phone}
    where id = #{id}
  </update>

  <!--判断手机号是否存在end_user_info中 Yan-->
  <select id="judgePhoneExistence" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT
        eui.user_logistics_role as userLogisticsRole
    FROM t_end_user_info eui
    WHERE eui.phone = #{phone} AND eui.`enable` = 0
  </select>


    <select id="selectUserAndCarAuditStatusByPackOrder" parameterType="java.lang.String" resultType="com.lz.dto.TEndcarAndUserMemberDTO">
    select teui.audit_status user_audit_status, teui2.audit_status car_owner_audit_status,
    teui.audit_status car_audit_status, toi.code, tocc.capital_transfer_type, topi.`code` packCode
    from t_order_pack_info topi
	left join t_order_cast_changes tocc on topi.code = tocc.order_code and tocc.user_oper = 'DBPayMent'
    left join t_order_pack_detail topd on topi.code = topd.pack_pay_code
    left join t_order_info toi on topd.order_code = toi.code
    left join t_end_user_info teui on toi.end_driver_id = teui.id
    left join t_end_car_info teci on toi.vehicle_id = teci.id
	left join t_end_user_info teui2 on toi.end_car_owner_id = teui2.id
    where topi.code = #{code}
    </select>
    <select id="selectAgent" parameterType="com.lz.vo.TEndUserInfoSearchVO" resultType="com.lz.dto.EndUserDTO">
        select DISTINCT
        teui.id as endAgentId,
        concat(teui.real_name, ' / ', teui.phone, '/',teui.idcard) as endAgentName,
        teui.phone as endAgentPhone,
        teui.idcard as endAgentIdCard
        from t_end_user_info teui
        where teui.enable = 0 and teui.user_logistics_role in ('CTYPEAGENTPERSON','CTYPEAGENTCOMPANY')
        <if test="param != null and param.length > 0">
            <if test="exactSelect">
                and teui.real_name = #{param}
            </if>
            <if test="!exactSelect">
                and teui.real_name like concat('%', #{param}, '%')
            </if>
        </if>
        union
        select DISTINCT teui.id endAgentId,concat(teui.real_name, ' / ', teui.phone, '/',teui.idcard) as endAgentName, teui.phone
        endAgentPhone,
        teui.idcard endAgentIdCard
        from t_end_user_info teui
        where teui.enable = 0 and teui.user_logistics_role in ('CTYPEAGENTPERSON','CTYPEAGENTCOMPANY')
        <if test="param != null and param.length > 0">
            <if test="exactSelect">
                and teui.phone = #{param}
            </if>
            <if test="!exactSelect">
                and teui.phone like concat('%', #{param}, '%')
            </if>
        </if>
        union
        select DISTINCT teui.id endAgentId,concat(teui.real_name, ' / ', teui.phone, '/',teui.idcard) as endAgentName, teui.phone
        endAgentPhone,
        teui.idcard endAgentIdCard
        from t_end_user_info teui
        where teui.enable = 0 and teui.user_logistics_role in ('CTYPEAGENTPERSON','CTYPEAGENTCOMPANY')
        <if test="param != null and param.length > 0">
            <if test="exactSelect">
                and teui.idcard = #{param}
            </if>
            <if test="!exactSelect">
                and teui.idcard like concat('%', #{param}, '%')
            </if>
        </if>
    </select>

    <update id="updateAuditAgent">
        update
        t_end_user_info
        set
        audit_status=#{auditStatus,jdbcType=VARCHAR},
        audit_opinion=#{auditOpinion,jdbcType=VARCHAR},
        audit_time= now()
        where id=#{id}
    </update>
    <delete id="deleteGoodsManagerRel">
        update
            t_line_goods_manager_rel
        set
           `enable`=1
        where id=#{id}
    </delete>
    <select id="selectByLineGoodsrRelId" resultType="com.lz.model.TLineGoodsManagerRel">
        select *  from t_line_goods_manager_rel where line_goods_rel_id=#{lineGoodsrRelId}
    </select>

    <select id="selectEnduserForDTO" resultType="com.lz.dto.EndUserDTO">
        select id endAgentId, concat(teui.real_name, ' / ', teui.phone, '/',teui.idcard) as endAgentName, teui.phone endAgentPhone,
               teui.idcard endAgentIdCard
        from t_end_user_info teui
        where teui.user_logistics_role = 'CTYPEAGENTPERSON' and teui.id = #{id}
        <if test="enable != null">
            and enable = #{enable}
        </if>
    </select>

    <select id="selectAgentByLineGoodsRelId" resultType="com.lz.dto.EndUserDTO">
        select teui.id endAgentId, concat(teui.real_name, ' / ', teui.phone, '/',teui.idcard) as endAgentName, teui.phone endAgentPhone,
               teui.idcard endAgentIdCard
        from t_end_user_info teui
        join t_line_goods_manager_rel tlgmr on teui.id = tlgmr.manager_id
        where tlgmr.line_goods_rel_id = #{lineGoodsRelId} and teui.enable = 0 and tlgmr.enable = 0
    </select>

    <select id="selectCEnduserCarrierInfo" resultType="com.lz.dto.CarrierCompanyRelDTO">
        SELECT
            teui.real_name driverName, teui.driver_did, tcecr.uid
        FROM
            t_end_user_info teui
            LEFT JOIN t_carrier_enduser_company_rel tcecr on teui.id = tcecr.enduser_company_id
        WHERE
            teui.ENABLE = 0 AND teui.ENABLE = 0 AND tcecr.datasouce = 'CD' and tcecr.enable = 0
          AND tcecr.carrier_id = #{carrierId} and teui.id = #{id}
    </select>

    <select id="selectEndUserId" parameterType="com.lz.model.TEnduserAccount" resultType="com.lz.model.TEnduserAccount">
        select * from  t_enduser_account
        where 1=1
        <if test="enduserId != null">
            and enduser_id =  #{enduserId,jdbcType=INTEGER}
        </if>
        <if test="accountId != null">
            and account_id = #{accountId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectEnduserList" parameterType="com.lz.model.TEnduserAccount" resultType="com.lz.model.TEnduserAccount">
        select * from  t_enduser_account
        where 1=1 AND enable = 0
        <if test="enduserId != null">
            and enduser_id =  #{enduserId,jdbcType=INTEGER}
        </if>
        <if test="accountId != null">
            and account_id = #{accountId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectByAccountNo" parameterType="java.lang.String" resultType="com.lz.model.TEndUserInfo">
      select
        e.*
        from  t_end_user_info e
        left join t_enduser_account ea on ea.enduser_id = e.id
        left join t_account a on a.id = ea.account_id
        where
          e.enable = 0
          and a.account_no = #{accountNo} and ea.enable = 0 and a.enable = 0
    </select>

    <select id="selectCaptainNameList" parameterType="java.lang.String" resultType="com.lz.model.TEndUserInfo">
        select  id,real_name,phone from
        t_end_user_info
        where user_logistics_role='CTYPECAPTAIN'
        <if test="parameter != null">
            and real_name =  #{parameter}
        </if>
        <if test="parameter != null">
            and phone = #{parameter}
        </if>
    </select>
    <select id="selectByCaptainList" parameterType="java.lang.String" resultType="com.lz.model.TEndUserInfo">
        select
        CONCAT( e.real_name,e.phone) as namePhone,e.*
        from  t_end_user_info e
        where
        e.enable = 0
        and e.user_logistics_role = 'CTYPECAPTAIN'
        <if test="np != null and  np != ''">
            and(e.real_name like concat("%", #{np},"%") or e.phone like concat("%", #{np},"%"))
        </if>
    </select>


    <select id="selectCaptainTypeList" parameterType="com.lz.model.TEndUserInfo" resultType="com.lz.model.TEndUserInfo">
        select  id,real_name,phone from
        t_end_user_info
        where user_logistics_role='CTYPECAPTAIN'
        and real_name =  #{realName}
        and phone = #{phone}
        and idcard = #{idcard}
    </select>


    <select id="selectSysUserIfAgreement" parameterType="java.lang.String" resultType="com.lz.system.model.TSysUser">
        select id,if_agreement as ifAgreement from t_sys_user where account_no = #{phone} and enable = 0
    </select>

    <update id="updateSysUserIfAgreement">
        update t_sys_user set if_agreement = 1 where id = #{id}
    </update>

    <select id="selectTransferorRole" parameterType="java.lang.String" resultType="java.lang.String">
        select
            user_logistics_role
        from t_end_user_info
        where
            enable = 0 and idcard = #{idcard}
    </select>

    <select id="selectTransferorPage" resultType="com.lz.dto.TransferorInfoDTO">
        select teui.id, teui.real_name, teui.phone, teui.idcard_valid_beginning, teui.idcard_valid_until, teui.idcard, teui.address
        from t_end_user_info teui
        left join t_enduser_account tea on teui.id = tea.enduser_id
        where teui.enable = 0 and tea.enable = 0 and teui.user_logistics_role = 'CTYPETRANSFEROR'
            <if test="record.realName != null and record.realName.length > 0">
                and teui.real_name like concat('%', #{record.realName}, '%')
            </if>
        <if test="record.phone != null and record.phone.length > 0">
            and teui.phone like concat('%', #{record.phone},  '%')
        </if>
        <if test="record.idCard != null and record.idCard.length() > 0">
            and teui.idcard like concat('%', #{record.idCard},  '%')
        </if>
    </select>

    <select id="selectTransferorInfo" resultType="com.lz.dto.TransferorInfoDTO">
        select teui.id, teui.real_name, teui.phone, tbc.idcard_valid_beginning, tbc.idcard_valid_until,
        tbc. card_owner_idcard_photo1 idcard_photo1, tbc.card_owner_idcard_photo2 idcard_photo2,
        tbc.card_owner_idcard idcard, tbc.id cardId, tbc.card_no, tbc.address, tbc.if_default,
        tbc.card_owner, tbc.card_owner_idcard, tbc.card_owner_phone,tbc.bank_name
        from t_end_user_info teui
        left join t_enduser_account tea on teui.id = tea.enduser_id
        left join t_bank_card tbc on tea.account_id = tbc.account_id
        where teui.enable = 0 and tea.enable = 0 and tbc.enable = 0
            and teui.id = #{endUserId}
            <if test="cardId != null">
                and tbc.id = #{cardId}
            </if>
    </select>

    <select id="selectTransferorInfoById" resultType="com.lz.dto.TransferorInfoDTO">
        SELECT
        teui.id,teui.real_name,teui.phone,teui.idcard,teui.idcard_valid_until,
        teui.idcard_valid_beginning,teui.idcard_photo1,teui.idcard_photo2,
        teui.address,tea.account_id
        FROM
        t_end_user_info teui
        LEFT JOIN t_enduser_account tea ON teui.id = tea.enduser_id
        WHERE
        teui.ENABLE = 0 AND tea.ENABLE = 0
        AND teui.id = #{endUserId,jdbcType=INTEGER}
    </select>

    <select id="selectAgentpersonInfo" resultType="com.lz.dto.AgentpersonInfoDTO">
        select teui.id, teui.real_name, teui.phone, tbc.idcard_valid_beginning, tbc.idcard_valid_until,
        tbc. card_owner_idcard_photo1, tbc.card_owner_idcard_photo2,
        tbc.card_owner_idcard idcard, tbc.id cardId, tbc.card_no, tbc.address, tbc.if_default,
        tbc.card_owner, tbc.card_owner_idcard, tbc.card_owner_phone,tbc.bank_name
        from t_end_user_info teui
        left join t_enduser_account tea on teui.id = tea.enduser_id
        left join t_bank_card tbc on tea.account_id = tbc.account_id
        where teui.enable = 0 and tea.enable = 0 and tbc.enable = 0
        and teui.id = #{endUserId}
        <if test="cardId != null">
            and tbc.id = #{cardId}
        </if>
    </select>

    <select id="selectByPhoneAndIdcard" resultType="com.lz.model.TEndUserInfo">
        select
        <include refid="Base_Column_List" />
        from t_end_user_info
        where phone = #{phone} and idcard = #{idcard} and enable = 0
    </select>

    <select id="selectByIdcard" resultType="com.lz.model.TEndUserInfo">
        select
            <include refid="Base_Column_List" />
        from t_end_user_info
        where idcard = #{idcard} and enable = 0
    </select>

    <select id="notOwnIdcardList" resultType="com.lz.model.TEndUserInfo">
        select * from t_end_user_info where id not in
        (SELECT enduser_id FROM
            t_enduser_account
        WHERE
            account_id = ( SELECT account_id FROM t_enduser_account WHERE enduser_id = #{id} and enable = 0 ) and enable=0 ) and  idcard = #{idcard}
        and `enable` = 0
    </select>

    <select id="selectUserIdByEnduserId" resultType="com.lz.model.TAccount">
        select ta.* from t_enduser_account tea
        left join t_account ta on tea.account_id = ta.id
        where tea.enduser_id  = #{enduserid}  and tea.`enable` = 0  and ta.`enable` = 0
    </select>

    <select id="selectOneselfRealName" resultType="com.lz.model.TEndUserInfo">
        select teui.idcard,teui.real_name,teui.phone from t_enduser_account tea
        left join t_end_user_info teui on tea.enduser_id = teui.id
        where account_id = #{accountid} and tea.enable = 0 and teui.enable = 0
    </select>

    <select id="selectIdcardUserLogisticsRole" resultType="com.lz.model.TEndUserInfo">
        SELECT * FROM
            t_end_user_info
        WHERE
        user_logistics_role like concat('%', #{userLogisticsRole}, '%')
        AND idcard = #{idCard}
         AND `enable` = 0
    </select>

    <select id="selectenduserAccountInfo" resultType="com.lz.vo.TEndUserInfoVO">
        select teui.real_name as realName,tea.account_id as accountId,teui.idcard,teui.phone
        from t_end_user_info teui
        left join t_enduser_account tea on teui.id = tea.enduser_id
        where teui.enable = 0 and tea.enable = 0 and teui.id = #{endUserId}
    </select>

    <update id="updateAuditStatusById">
        update
        t_end_user_info
        set audit_status = #{auditStatus},
            audit_opinion = #{auditOpinion},
            audit_time = now(),
            update_user = #{updateUser},
            update_time = now()
        where id = #{id} limit 1
    </update>

    <select id="driverIdentityVerificationStatus" resultType="com.lz.vo.TEndUserInfoVO">
        select teui.*,teuai.idcard_status from t_end_user_info teui
        left join t_end_user_audit_info teuai on teui.id = teuai.end_user_id
        where teui.`enable`= 0 and teuai.enable = 0
        and teui.phone = #{phone} and teui.user_logistics_role  like CONCAT('%', #{userLogisticsRole}, '%')
    </select>

    <select id="selectenduserinfoByPhoneRole" resultType="com.lz.model.TEndUserInfo">
        SELECT * FROM t_end_user_info WHERE `enable` = 0  AND phone = #{phone} and user_logistics_role  like CONCAT('%', #{userLogisticsRole}, '%')
    </select>
    <select id="getDataByPhoneAndIdcard" resultType="com.lz.model.TEndUserInfo">
        SELECT <include refid="Base_Column_List"/>
        FROM t_end_user_info
        WHERE `enable` = 0
          AND phone = #{phone}
          AND idcard = #{idcard}
          AND real_name = #{realName}
    </select>

    <select id="getDriverVehicleInfo" resultType="com.lz.dto.VehicleInfo">
        SELECT
            teci.vehicle_number
        FROM t_end_user_info teui
        left join t_end_user_car_rel teucr on teui.id = teucr.enduser_id
        left join t_end_car_info teci on teucr.endcar_id = teci.id
        WHERE teui.`enable` = 0
        and teucr.`enable` = 0
        and teci.`enable` = 0
          and user_logistics_role like 'CTYPEDRVIVER' and teucr.data_concel_time is null and teucr.user_car_relation_type = 'CLSYRSJ'
          <if test="param.realName != null and param.realName != ''">
              and real_name = #{param.realName}
          </if>
          <if test="param.phone != null and param.phone != ''">
              and phone = #{param.phone}
          </if>
    </select>

    <select id="getDriverInfo" resultType="com.lz.dto.DriverInfoDto">
        SELECT
            *
        from
            t_end_user_info
        where
            phone = #{param.phone}
            <if test="param.realName != null and param.realName != ''">
                and real_name = #{param.realName}
            </if>
            and user_logistics_role like 'CTYPEDRVIVER' and enable = 0
    </select>

    <update id="updateDriverPhone">
        UPDATE t_end_user_info SET phone = #{newphone,jdbcType=VARCHAR}
        WHERE phone = #{phone,jdbcType=VARCHAR} and enable = 0 and user_logistics_role like CONCAT('%', #{userLogisticsRole}, '%')
    </update>
</mapper>