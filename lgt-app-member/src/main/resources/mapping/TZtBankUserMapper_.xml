<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TZtBankUserMapper">
    <select id="selectImperfectBankCardList"  resultType="com.lz.vo.ImperfectTZtBankCardVO">
        SELECT
        DISTINCT
        tbc.`acct_no`,
        tbc.`acct_name`,
        tbc.`acct_card`,
        tbc.bank_code,
        tbc.`id`,
        tbc.bank_name,
        tbc.occ_bank_phone,
        tbu.is_default,
        tbu.is_oneself,
        t.account_no,
        teui.real_name,
		teui.id as endUserId,
        tea.idcard,
        tzbr.bind_status,
        case
          when tzbr.bind_status = 'BIND' then '绑卡成功'
          when tzbr.bind_status = 'BINDING' then '绑卡处理中'
        else '绑卡失败' end bindStatusStr
        FROM t_zt_bank_user tbu
        LEFT JOIN t_account t ON tbu.account_id=t.id
        LEFT JOIN  t_zt_bank_card tbc ON tbu.bank_id = tbc.id
        LEFT JOIN t_enduser_account tea ON t.id  = tea.account_id
        LEFT JOIN t_end_user_info teui ON teui.id = tea.enduser_id
        LEFT JOIN t_zt_bank_bind_relationship tzbr ON tzbr.account_bank_id = tbu.id
        where tbu.account_id= #{accountId}
        and teui.user_logistics_role LIKE CONCAT('%',  #{userLogisticsRole}, '%')
        and tzbr.bind_status in ('BIND','BINDING')
        and tzbr.request_code in ('1','0')
        and tbc.enable = 0
        and t.enable = 0
        and tea.enable = 0
        and teui.enable = 0
        and tbu.enable = 0
    </select>
    <select id="getTZtBankCardListByCardNo" resultType="com.lz.model.TZtBankCard">
       SELECT tbc.*
       FROM t_zt_bank_user tbu
       LEFT JOIN t_zt_bank_card tbc ON tbc.id = tbu.bank_id
       where 1=1
        <if test="acctNo != null and acctNo !=''">
            and tbc.acct_no = #{acctNo}
        </if>
        <if test="acctName != null and acctName !=''">
            and tbc.acct_name = #{acctName}
        </if>
        <if test="accountId != null and accountId !=''">
            and tbu.account_id = #{accountId}
        </if>
       and tbc.enable=0
       and tbu.enable=0
    </select>
    <update id="updateByAccountId" parameterType="java.lang.Integer">
    update t_zt_bank_user
    set is_default = 0
    where account_id = #{accountId,jdbcType=INTEGER}
  </update>
    <select id="selectByBankNum" resultType="java.lang.Integer">
        SELECT count(*)
        FROM t_zt_bank_user tbu
        LEFT JOIN t_zt_bank_bind_relationship tzbr ON tzbr.account_bank_id = tbu.id
        where 1=1 and tbu.enable = 0
            and tzbr.bind_status = 'BIND'
            and tzbr.request_code = '1'
            and tbu.account_id = #{accountId}
    </select>
    <select id="selectByBankNumAndByStatus" resultType="java.lang.Integer">
        SELECT count(*)
        FROM t_zt_bank_user tbu
        LEFT JOIN t_zt_bank_bind_relationship tzbr ON tzbr.account_bank_id = tbu.id
        where 1=1 and tbu.enable = 0
            and tzbr.bind_status in ('BIND','BINDING')
            and tzbr.request_code in ('1','0')
            and tbu.account_id = #{accountId}
    </select>
    <select id="getHxyhBankCardDetail" resultType="com.lz.vo.TZtBankCardVo">
        SELECT
          tzbc.*,
          tbu.is_default,
          tbu.id  as bankUserId
        FROM t_zt_bank_user tbu
        LEFT JOIN t_zt_bank_card tzbc ON tzbc.id = tbu.bank_id
        where 1=1
            and tbu.enable = 0
            and tzbc.enable = 0
            and tbu.bank_id = #{bankId}
            and tbu.account_id = #{accountId}
    </select>

    <select id="selectByBankIdAndAccountId" resultType="com.lz.model.TZtBankUser">
        SELECT
          tbu.*
        FROM t_zt_bank_user tbu
        where 1=1
            and tbu.enable = 0
            and tbu.bank_id = #{bankId}
            and tbu.account_id = #{accountId}
    </select>
    <select id="hxBankCardList" resultType="com.lz.vo.TZtBankCardVo">
        SELECT
          tzbc.*,
          tbu.is_default,
          tzap.user_open_role,
          tbu.account_id,
          tzbbr.bind_status,
          case
            when tzbbr.bind_status = 'BIND' then '绑卡成功'
            when tzbbr.bind_status = 'BINDING' then '绑卡处理中'
          else '绑卡失败' end bindStatusStr
        FROM t_zt_bank_user tbu
        LEFT JOIN t_zt_bank_card tzbc ON tzbc.id = tbu.bank_id
        LEFT JOIN t_zt_account_open_info tzap ON tzap.account_id = tbu.account_id
        LEFT JOIN t_zt_bank_bind_relationship tzbbr ON tzbbr.account_bank_id = tbu.id
        where 1=1
            and tbu.enable = 0
            and tzbc.enable = 0
            and tzbbr.bind_status in ('BIND','BINDING')
            and tzap.user_open_role = #{userOpenRole}
            and tbu.account_id = #{accountId}
    </select>

    <select id="selectBankByEndUserId" resultType="com.lz.model.TZtBankCard">
        SELECT
          tzbc.*
        FROM t_zt_bank_user tbu
        LEFT JOIN t_zt_bank_card tzbc ON tzbc.id = tbu.bank_id
        LEFT JOIN t_enduser_account tea ON tea.account_id = tbu.account_id
        LEFT JOIN t_zt_bank_bind_relationship tzbbr ON tzbbr.account_bank_id = tbu.id
        where 1=1
            and tbu.enable = 0
            and tzbc.enable = 0
            and tea.enable = 0
            and tzbbr.bind_status in ('BIND')
            and tbu.is_default = 1
            and tea.enduser_id = #{endUserId}
    </select>

    <select id="selectByOpenRoleIdAndBankId" resultType="com.lz.model.TZtBankBindRelationship">
        SELECT
          tzbbr.*
        FROM t_zt_bank_user tbu
        LEFT JOIN t_zt_bank_bind_relationship tzbbr ON tzbbr.account_bank_id = tbu.id
        where 1=1
            and tbu.enable = 0
            and tzbbr.bind_status in ('BIND')
            and tbu.account_id = #{accountId}
            and tbu.bank_id = #{bankId}
    </select>
</mapper>