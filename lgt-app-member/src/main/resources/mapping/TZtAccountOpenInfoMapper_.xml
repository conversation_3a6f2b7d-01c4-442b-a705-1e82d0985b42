<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TZtAccountOpenInfoMapper">
    <select id="selectByAccountId" resultType="com.lz.model.TZtAccountOpenInfo">
        SELECT
          tzaoi.*
        FROM t_zt_account_open_info tzaoi
        where 1=1
            and tzaoi.account_id = #{accountId} and tzaoi.channel_id = 'HXBANK'
            <if test="userOpenRole != null and userOpenRole !=''">
                and tzaoi.user_open_role = #{userOpenRole}
            </if>
    </select>

    <select id="selectNotSelfOpenByAccountId" resultType="com.lz.dto.TZtAccountOpenInfoDTO">
        SELECT
        tzaoi.*, tea.enduser_id endUserId
        FROM t_zt_account_open_info tzaoi
        left join t_enduser_account tea on tzaoi.account_id = tea.account_id

        where 1=1
        and tzaoi.account_id = #{accountId} and tea.enable = 0
        <if test="userOpenRole != null and userOpenRole !=''">
            and tzaoi.user_open_role = #{userOpenRole}
        </if>
    </select>

    <select id="selectOpenInfoByAccountId" parameterType="java.lang.Integer" resultType="com.lz.model.TZtAccountOpenInfo">
        SELECT * FROM
            t_zt_account_open_info
        WHERE
            user_open_role = 'BD' AND account_id = #{accountId,jdbcType=INTEGER}
            and response_code = 'SUCCESS' and status =1
    </select>

    <select id="pcEndUserOpenRoleList" parameterType="com.lz.vo.PcZTEndUserOpenRoleListVO" resultType="com.lz.dto.PcZTEndUserOpenRoleListDTO">
        SELECT
            ta.id AS accountId,
            teui.id AS endUserId,
            tzao.remark,
            teui.real_name,
            teui.phone,
            teui.idcard,
        CASE

                WHEN tzao.STATUS = 0 THEN
                '注册中'
                WHEN tzao.STATUS = 1 THEN
                '注册成功'
                WHEN tzao.STATUS = 2 THEN
                '注册失败'
                WHEN tzao.STATUS IS NULL THEN
                '未注册'
            END openStatus,
            tzao.response_message,
            tzao.sub_acc,
        CASE

                WHEN tzao.response_code = 'SUCCESS' THEN
                '可用'
                WHEN tzao.response_code = 'FAIL' THEN
                '不可用'
                WHEN tzao.response_code IS NULL THEN
                '不可用'
            END responseCode,
            case
                when tzao.if_oneself_open = 0 then '本人开户'
                when tzao.if_oneself_open = 1 then '非本人开户'
            end ifOneselfOpenInfo,
            tzao.if_oneself_open,
            case
            when tzao.STATUS IS NULL then ''
            else IFNULL(tzao.open_real_name,teui.real_name)
            end openRealName,
            case
            when tzao.STATUS IS NULL then ''
            else IFNULL(tzao.open_phone,teui.phone)
            end openPhone,
            case
            when tzao.STATUS IS NULL then ''
            else IFNULL(tzao.open_id_card,teui.idcard)
            end openIdCard
        FROM
            t_account ta
            LEFT JOIN t_enduser_account tea ON ta.id = tea.account_id
            LEFT JOIN t_end_user_info teui ON teui.id = tea.enduser_id
            LEFT JOIN t_zt_account_open_info tzao ON tzao.account_id = ta.id
        WHERE
            ta.usertype = 'CD'
            AND teui.ENABLE = 0
            AND tea.`enable` = 0
            AND teui.user_logistics_role LIKE CONCAT('%',  #{userRole}, '%')
            <if test="userName != null and userName != ''">
                AND teui.real_name LIKE CONCAT('%',  #{userName}, '%')
            </if>
            <if test="userPhone != null and userPhone != ''">
                AND teui.phone LIKE CONCAT('%',  #{userPhone}, '%')
            </if>
            <if test="userIdcard != null and userIdcard != ''">
                AND teui.idcard  LIKE CONCAT('%',  #{userIdcard}, '%')
            </if>
            <if test="openType != null">
                AND (tzao.if_oneself_open like #{openType} and tzao.if_oneself_open is not null)
            </if>
            <if test="openState != null and openState != ''">
                <if test="openState == 'SUCCESS'">
                    and tzao.response_code = #{openState,jdbcType=VARCHAR}
                </if>
                <if test="openState == 'FAIL'">
                    and (tzao.response_code = #{openState,jdbcType=VARCHAR} or tzao.response_code is null)
                </if>
            </if>
    </select>

    <select id="selectByDrviverList" resultType="com.lz.model.TZtAccountOpenInfo">
        SELECT
        tzaoi.*
        FROM t_zt_account_open_info tzaoi
        left join t_enduser_account tea on tea.account_id = tzaoi.account_id
        left join t_end_user_info teai on teai.id = tea.enduser_id
        where 1=1
        and teai.id in
        <foreach collection="list" index="index" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and tzaoi.user_open_role = 'CD'
        and tea.enable = 0
        and teai.enable = 0
        and teai.user_logistics_role like concat('%', 'CTYPEDRVIVER', '%')
    </select>

    <select id="selectByDrviverListStatus" resultType="com.lz.dto.ListOpenRoleStatusDTO">
        SELECT
        tzaoi.status,
        case tzaoi.status
            when 0 then '开户处理中'
            when 1 then '开户成功'
            when 2 then '开户失败'
            else '未开户'
        end openStatus,
        teai.real_name
        FROM t_end_user_info teai
        left join t_enduser_account tea on tea.enduser_id = teai.id
        left join t_zt_account_open_info tzaoi on tea.account_id = tzaoi.account_id and tzaoi.user_open_role = 'CD' and tzaoi.channel_id = 'HXBANK'
        where teai.id in
        <foreach collection="list" index="index" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and tea.enable = 0
        and teai.enable = 0
        and teai.user_logistics_role like concat('%', 'CTYPEDRVIVER', '%')
    </select>

    <select id="selectByWithdrawCairrer" resultType="com.lz.vo.HxWithdrawVo">
        select
         occr.id as openRoleId,
         wa.id as walletId,
         ta.account_no as phone,
         'PTIXIAN' as tradeType
        from t_zt_account_open_info occr
        left join t_carrie_account tca on tca.account_id = occr.account_id and tca.enable = 0
        left join t_account ta on ta.id = tca.account_id and ta.enable = 0
        left join t_zt_wallet wa on wa.zt_account_open_id = occr.id and wa.data_source = occr.user_open_role
         where  wa.enable = 0  and ta.if_main_account = 1 and occr.id = #{openRoleId}
    </select>

    <select id="selectByWithdrawPf" resultType="com.lz.vo.HxWithdrawVo">
        select
         occr.id as openRoleId,
         wa.id as walletId,
         ta.account_no as phone,
         'PFTIXIAN' as tradeType
        from t_zt_account_open_info occr
        left join t_carrie_account tca on tca.account_id = occr.account_id and tca.enable = 0
        left join t_account ta on ta.id = tca.account_id and ta.enable = 0
        left join t_zt_wallet wa on wa.zt_account_open_id = occr.id and wa.data_source = occr.user_open_role
         where  wa.enable = 0  and ta.if_main_account = 1 and occr.id = #{openRoleId}
    </select>

    <select id="selectByWithdrawCompany" resultType="com.lz.vo.HxWithdrawVo">
         select
         occr.id as openRoleId,
         wa.id as walletId,
         ta.account_no as phone,
         'BTIXIAN' as tradeType
        from t_zt_account_open_info occr
        left join t_company_account tca on tca.account_id = occr.account_id and tca.enable = 0
        left join t_account ta on ta.id = tca.account_id and ta.enable = 0
        left join t_zt_wallet wa on wa.zt_account_open_id = occr.id and wa.data_source = occr.user_open_role
         where  wa.enable = 0  and ta.if_main_account = 1 and occr.id = #{openRoleId}
    </select>

    <select id="selectByWithdrawManager" resultType="com.lz.vo.HxWithdrawVo">
        select
         occr.id as openRoleId,
         wa.id as walletId,
         ta.account_no as phone,
         'MANAGER' as tradeType
        from t_zt_account_open_info occr
        left join t_enduser_account tea on tea.account_id = occr.account_id and tea.enable = 0
        left join t_account ta on ta.id = tea.account_id and ta.enable = 0
        left join t_zt_wallet wa on wa.zt_account_open_id = occr.id
        where  wa.enable = 0 and wa.data_source = 'CD' and occr.id = #{openRoleId}
    </select>


    <select id="selectByWithdrawBankList" resultType="com.lz.model.TZtBankCard">
        select
          bc.*
        from t_zt_account_open_info occr
        LEFT JOIN t_zt_bank_user tbu ON tbu.account_id = occr.account_id
        LEFT JOIN t_zt_bank_bind_relationship tbr ON tbr.account_open_id = occr.id and tbr.account_bank_id = tbu.id
	    LEFT JOIN t_zt_bank_card bc ON bc.id = tbu.bank_id AND bc.ENABLE = 0
        where tbu.enable = 0
        and tbr.bind_status = 'BIND'
        and tbr.request_code = '1'
        and occr.id = #{openRoleId}
    </select>

    <select id="selectByIdcard" resultType="com.lz.model.TZtAccountOpenInfo">
        select tzaoi.*
        from t_zt_account_open_info tzaoi
        left join t_account_now tan on tzaoi.account_id = tan.account_id
        left join t_enduser_account tea on tzaoi.account_id = tea.account_id
        left join t_end_user_info teui on tea.enduser_id = teui.id
        where teui.idcard = #{idcard} and tzaoi.partner_acc_id <![CDATA[!=]]> #{partnerAccId} and teui.enable = 0 and tea.enable = 0
    </select>

    <select id="selectByBusinessLicenseNo" resultType="com.lz.model.TZtAccountOpenInfo">
        select tzaoi.*
        from t_zt_account_open_info tzaoi
        left join t_company_account tca on tca.account_id = tzaoi.account_id and tca.enable = 0
        left join t_account ta on tca.account_id = ta.id
        left join t_company_info tci on tca.company_id = tci.id
        where tci.business_license_no = #{businessLicenseNo} and tzaoi.partner_acc_id <![CDATA[!=]]> #{partnerAccId} and ta.if_main_account = 1
        union
        select tzaoi.*
        from t_zt_account_open_info tzaoi
        left join t_carrie_account tca on tca.account_id = tzaoi.account_id and tca.enable = 0
        left join t_account ta on tca.account_id = ta.id
        left join t_carrier_info tci on tca.carrier_id = tci.id
        where tci.business_license_no = #{businessLicenseNo} and tzaoi.partner_acc_id <![CDATA[!=]]> #{partnerAccId} and ta.if_main_account = 1

    </select>

    <select id="selectNotByAccountId" resultType="com.lz.model.TZtAccountOpenInfo">
        select
          occr.*
        from t_zt_account_open_info occr
        where occr.status != 2
        and occr.account_id != #{accountId}
        <if test="openPhone != null and openPhone !=''">
            and occr.open_phone = #{openPhone}
        </if>
        <if test="openIdCard != null and openIdCard !=''">
            and occr.open_id_card = #{openIdCard}
        </if>
    </select>

    <select id="selectByAccountIdAndChannelId" resultType="com.lz.model.TZtAccountOpenInfo">
        SELECT * FROM t_zt_account_open_info
        WHERE 1=1
        <if test="accountId != null and accountId != ''">
            and account_id = #{accountId,jdbcType=INTEGER}
        </if>
        <if test="channelId != null and channelId != ''">
            and channel_id = #{channelId,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectOrderOpenInfoByAccountId" resultType="com.lz.model.TZtAccountOpenInfo">
        SELECT
            tzaoi.*
        FROM
            t_zt_account_open_info tzaoi
            LEFT JOIN t_enduser_account tea ON tea.account_id = tzaoi.account_id
        WHERE
            tea.enduser_id = #{enduserId,jdbcType=INTEGER}
    </select>


    <select id="selectOrderOpenInfoListByAccountId" resultType="com.lz.model.TZtAccountOpenInfo">
        SELECT
            tzaoi.*
        FROM
            t_zt_account_open_info tzaoi
                LEFT JOIN t_enduser_account tea ON tea.account_id = tzaoi.account_id
        WHERE
            tea.enduser_id = #{enduserId,jdbcType=INTEGER}
    </select>
</mapper>