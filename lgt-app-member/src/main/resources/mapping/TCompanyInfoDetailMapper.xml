<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TCompanyInfoDetailMapper">

    <resultMap id="BaseResultMap" type="com.lz.model.TCompanyInfoDetail">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="companyId" column="company_id" jdbcType="INTEGER"/>
            <result property="ip" column="ip" jdbcType="VARCHAR"/>
            <result property="macAddr" column="mac_addr" jdbcType="VARCHAR"/>
            <result property="region" column="region" jdbcType="VARCHAR"/>
            <result property="payStatus" column="pay_status" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,company_id,ip,
        mac_addr,region,payStatus
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_company_info_detail
        where  id = #{id,jdbcType=INTEGER} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from t_company_info_detail
        where  id = #{id,jdbcType=INTEGER} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.lz.model.TCompanyInfoDetail" useGeneratedKeys="true">
        insert into t_company_info_detail
        ( id,company_id,ip
        ,mac_addr,region,pay_status)
        values (#{id,jdbcType=INTEGER},#{companyId,jdbcType=INTEGER},#{ip,jdbcType=VARCHAR}
        ,#{macAddr,jdbcType=VARCHAR},#{region,jdbcType=VARCHAR},#{macAddr,jdbcType=BIT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.lz.model.TCompanyInfoDetail" useGeneratedKeys="true">
        insert into t_company_info_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="companyId != null">company_id,</if>
                <if test="ip != null">ip,</if>
                <if test="macAddr != null">mac_addr,</if>
                <if test="region != null">region,</if>
                <if test="payStatus != null">pay_status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=INTEGER},</if>
                <if test="companyId != null">#{companyId,jdbcType=INTEGER},</if>
                <if test="ip != null">#{ip,jdbcType=VARCHAR},</if>
                <if test="macAddr != null">#{macAddr,jdbcType=VARCHAR},</if>
                <if test="region != null">#{region,jdbcType=VARCHAR},</if>
                <if test="payStatus != null">#{payStatus,jdbcType=BIT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.lz.model.TCompanyInfoDetail">
        update t_company_info_detail
        <set>
                <if test="companyId != null">
                    company_id = #{companyId,jdbcType=INTEGER},
                </if>
                <if test="ip != null">
                    ip = #{ip,jdbcType=VARCHAR},
                </if>
                <if test="macAddr != null">
                    mac_addr = #{macAddr,jdbcType=VARCHAR},
                </if>
                <if test="region != null">
                    region = #{region,jdbcType=VARCHAR},
                </if>
                <if test="payStatus != null">
                    pay_status = #{payStatus,jdbcType=BIT},
                </if>
        </set>
        where   id = #{id,jdbcType=INTEGER} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.lz.model.TCompanyInfoDetail">
        update t_company_info_detail
        set 
            company_id =  #{companyId,jdbcType=INTEGER},
            ip =  #{ip,jdbcType=VARCHAR},
            mac_addr =  #{macAddr,jdbcType=VARCHAR},
            region =  #{region,jdbcType=VARCHAR},
            pay_status =  #{payStatus,jdbcType=BIT}
        where   id = #{id,jdbcType=INTEGER}
    </update>
</mapper>
