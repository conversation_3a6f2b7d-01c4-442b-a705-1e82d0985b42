<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lz.dao.TCarrierInfoMapper" >
  <select id="findList" resultMap="BaseResultMapVo" parameterType="com.lz.vo.TCarrierInfoVo">
    select
    tci.*, tdci.item_value carrier_logistics_role_name, tdci2.item_value logistics_company_type_name,
    com.companyCount,
    case
    when tnsoa.id is null then 1
    when tnsoa.auth_status = 0 then 3
    when tnsoa.auth_status = 1 and tnsoa.seal_no is null then 4
    when tnsoa.seal_no is not null and tnsoa.seal_image is null then 2
    else 0 end signStatus,
    case
    when tnsoa.id is null then '未注册'
    when tnsoa.auth_status = 0 then '未验证'
    when tnsoa.auth_status = 1 and tnsoa.seal_no is null then '未注册'
    when tnsoa.auth_status = 1 and tnsoa.seal_no is not null then '已注册'
    else '已注册' end signStatusMeagess,
    case
    when tnsoa.seal_image is null then '无'
    else '有' end sealImageStatus
    from t_carrier_info tci
    <!--left join t_fifth_generation_sign_open_account tfgsoa on tfgsoa.user_id = tci.id and tfgsoa.user_type = 'CA' and tfgsoa.enable = 0-->
    LEFT JOIN t_net_sign_open_account tnsoa ON tnsoa.user_id = tci.id AND tnsoa.user_type = 'CA'
    left join t_dic_cat_item tdci on tci.carrier_logistics_role = tdci.item_code
    left join t_dic_cat_item tdci2 on tci.logistics_company_type = tdci2.item_code
    left join (SELECT t.carrier_id, COUNT(t.enduser_company_id) companyCount from (
    SELECT
    tcecr.carrier_id, tcecr.enduser_company_id
    FROM
    t_carrier_enduser_company_rel tcecr
    left join t_carrie_account tca on tcecr.carrier_id = tca.carrier_id
    where datasouce = 'BD' and tcecr.enduser_company_id is not null
    GROUP BY carrier_id, enduser_company_id ) t GROUP BY t.carrier_id ) com on tci.id = com.carrier_id
    <where>
      1=1 and tci.enable = #{enable,jdbcType=BIT}
      <if test="param4 == null" >
        and tci.param4 is null
      </if>
      <if test="carrierName != null" >
        and tci.carrier_name LIKE '%${carrierName}%'
      </if>
      <if test="carrierLogogram != null" >
        and tci.carrier_logogram LIKE '%${carrierLogogram}%'
      </if>
      <if test="param4 != null" >
        and tci.param4 = #{param4}
      </if>
      GROUP BY tci.id, tdci.item_value, tdci2.item_value, com.companyCount,signStatus,signStatusMeagess,sealImageStatus
    </where>
    order by tci.create_time desc
  </select>

  <select id="selectCarrier" resultType="com.lz.dto.CarrierInfoDTO">
    select id, carrier_name,third_party_interface_manage_address
    from t_carrier_info
    where enable = 0 and param4 is null
  </select>

  <!--查当前用户的 承运方Id-->
  <select id="selectCarrierBySysUserId" resultMap="BaseResultMap">
    SELECT
        tca.carrier_id
    FROM
        t_account ta
    LEFT JOIN t_carrie_account tca ON ta.id = tca.account_id
    WHERE
        ta.user_id = #{sysUserId}
    AND ta.usertype = 'CA'
  </select>

  <select id="selectCarrierById" parameterType="java.lang.Integer" resultType="com.lz.dto.CarrierInfoDTO">
    select * from
    t_carrier_info tci
  </select>

  <select id="selectById" parameterType="java.lang.Integer" resultType="com.lz.vo.TCarrierInfoVo">
      select tci.*,
      ca.real_position,
      idcard, tcecr.thrid_pary_sub_account,tnsoa.seal_image,
      case
           when tnsoa.id is null then 1
           when tnsoa.auth_status = 0 then 3
           when tnsoa.id is null then 1
           when tnsoa.auth_status = 0 then 3
           when tnsoa.auth_status = 1 and tnsoa.seal_no is null then 4
           when tnsoa.seal_no is not null and tnsoa.seal_image is null then 2
           else 0
    end signStatus
    from
      t_carrier_info tci
      left join t_carrie_account ca on ca.carrier_id = tci.id
      left join t_carrier_enduser_company_rel tcecr on tci.id = tcecr.carrier_id and tcecr.enduser_company_id is null and tcecr.datasouce = 'CA'
      left join t_net_sign_open_account tnsoa on tnsoa.user_id = tci.id and tnsoa.user_type = 'CA'
      where tci.id = #{id}
  </select>

  <select id="selectCarrieOpenInfoList" parameterType="com.lz.vo.CarrierOpenInfoListVO" resultType="com.lz.dto.CarrierOpenInfoDTO">
     SELECT
        tci.id,
        ta.usertype,
        ta.id as accountId,
        tzao.remark,
		tci.carrier_name,
        tci.business_license_no,
        tci.company_detail_address,
        tci.company_contacts,
        tci.company_contacts_phone,
        tci.company_legal_person,
        case
            when tzao.status = 0 then '注册中'
            when tzao.status = 1 then '注册成功'
            when tzao.status = 2 then '注册失败'
            when tzao.status is null then '未注册'
        end openStatus,
		tzao.response_message,
		tzao.sub_acc,
        case
            when tzao.response_code = 'SUCCESS' then '可用'
            when tzao.response_code = 'FAIL' then '不可用'
            when tzao.response_code is null then '不可用'
        end responseCode
     FROM
        t_account ta
        LEFT JOIN t_carrie_account tca ON ta.id = tca.account_id
		left join t_carrier_info tci on tci.id = tca.carrier_id
		left join t_zt_account_open_info tzao on tzao.account_id = ta.id
        WHERE ta.usertype = 'CA' and ta.if_main_account = 1 and tci.enable = 0
      <if test="carrierName != null and carrierName != ''">
            and tci.carrier_name like CONCAT('%', #{carrierName,jdbcType=VARCHAR}, '%')
      </if>
      <if test="param4 != null" >
          and tci.param4 = #{param4}
      </if>
      <if test="responseCode != null and responseCode != ''">
         <if test="responseCode == 'SUCCESS'">
                and tzao.response_code = #{responseCode,jdbcType=VARCHAR}
         </if>
         <if test="responseCode == 'FAIL'">
                and (tzao.response_code = #{responseCode,jdbcType=VARCHAR} or tzao.response_code is null)
         </if>
      </if>
  </select>

  <select id="applyCarrieOpenInfo" parameterType="com.lz.vo.CarrieOpenInfoDetailsVO" resultType="com.lz.dto.CarrieOpenInfoDetailsDTO">
        SELECT
        ta.id as accountId,
        ta.usertype,
        tci.carrier_name,
        tzao.remark,
        tci.business_license_no,
        tci.company_legal_person,
        tci.company_contacts,
        tci.company_detail_address,
        tci.company_contacts_phone
    FROM
        t_account ta
        LEFT JOIN t_carrie_account tca ON tca.account_id = ta.id
        LEFT JOIN t_carrier_info tci ON tca.carrier_id = tci.id
        left join t_zt_account_open_info tzao on tzao.account_id = ta.id
    WHERE
        ta.usertype = 'CA' and ta.if_main_account = 1
        AND tci.id = #{id,jdbcType=INTEGER}
  </select>

  <select id="selectByCompanyContactsPhone" parameterType="com.lz.model.TCarrierInfo" resultType="com.lz.model.TCarrierInfo">
    select
      *
    from
      t_carrier_info
      where 1=1
      <if test="id != null" >
        and id != #{id}
      </if>
      <if test="companyContactsPhone != null" >
        and company_contacts_phone = #{companyContactsPhone}
      </if>
      <if test="businessLicenseNo != null" >
        and business_license_no = #{businessLicenseNo}
      </if>
  </select>



  <select id="selectByCarrierWalle" parameterType="com.lz.vo.TCarrierInfoVo" resultType="com.lz.vo.TCarrierInfoVo">
    SELECT
    c.*,
    rel.uid,
    w.id walletId,
    w.account_balance,
    w.frozen_amount,
    w.entry_amount,
    w.withdraw_amount
    FROM
    t_carrier_info c
    LEFT JOIN t_carrier_enduser_company_rel rel ON rel.carrier_id = c.id
    LEFT JOIN t_wallet w ON w.carrier_enduser_company_id = rel.id
    WHERE c.ENABLE =0 AND w.ENABLE = 0 	AND rel.ENABLE = 0  AND w.purse_category = 'PCARRIER'
    <if test="carrierName != null" >
      and c.carrier_name LIKE '%${carrierName}%'
    </if>
  </select>

  <select id="selectCarrierInfo" parameterType="java.lang.Integer" resultType="com.lz.dto.TCarrierInfoDTO">
    select tci.id, tcecr.uid, tci.bussiness_platform_sign_code, tci.third_party_interface_asynchronous_notification_address,
           tci.third_party_interface_consumer_code, tci.third_party_interface_manage_address, tci.platform_did, tci.carrier_name
    from t_carrier_info tci
    left join t_carrier_enduser_company_rel tcecr on tci.id = tcecr.carrier_id
    where tci.id = #{carrierId} and tci.enable = 0 and tcecr.enable = 0 and tcecr.enduser_company_id is null and tcecr.datasouce='CA'
  </select>
    <select id="getDataByCompanyName" resultType="com.lz.model.TCarrierInfo">
        SELECT
            *
        FROM
            t_carrier_info
        WHERE 1=1
        <if test="companyName != null">
            AND carrier_name = #{companyName}
        </if>
        <if test="businessLicenseNo != null">
            AND business_license_no = #{businessLicenseNo}
        </if>
    </select>

</mapper>