<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TZtBankBindRelationshipMapper">
    <select id="selectByBankUserIdAndAccountOpenId" resultType="com.lz.model.TZtBankBindRelationship">
        SELECT tzbr.*
        FROM t_zt_bank_user tbu
        LEFT JOIN t_zt_bank_bind_relationship tzbr ON tzbr.account_bank_id = tbu.id
        where 1=1 and tbu.enable = 0
            and tzbr.bind_status in ('BIND','BINDING')
            and tzbr.request_code = '1'
            and tbu.account_id = #{accountId}
    </select>
    <select id="selectByAcctNo" resultType="com.lz.dto.BindBankCardRelationDTO">
        SELECT
            tzaoi.user_open_role,
            tzaoi.partner_acc_id,
            tzbc.acct_no
        FROM
            t_zt_account_open_info tzaoi
                LEFT JOIN t_zt_bank_bind_relationship tzbbr ON tzaoi.id = tzbbr.account_open_id
                LEFT JOIN t_zt_bank_user tzbu ON tzbbr.account_bank_id = tzbu.id
                LEFT JOIN t_zt_bank_card tzbc ON tzbu.bank_id = tzbc.id
        WHERE
            tzbc.acct_no = #{acctNo}
          AND tzbbr.bind_status = 'BIND'
          AND tzaoi.`status` = 1
    </select>

</mapper>