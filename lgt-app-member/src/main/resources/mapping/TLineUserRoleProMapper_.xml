<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lz.dao.TLineUserRoleProMapper" >
  <select id="list" resultMap="BaseResultMap" parameterType="com.lz.model.TLineUserRolePro">
    select
    <include refid="Base_Column_List" />
    from t_line_user_role_pro
    <where>
      enable = #{enable,jdbcType=BIT}
    </where>
  </select>

  <update id="updateByRecord" parameterType="com.lz.model.TLineUserRolePro">
    update t_line_user_role_pro
    set enable = #{enable}
    where account_id = #{accountId}
  </update>

  <select id="selectUserLineRole" parameterType="com.lz.model.TLineUserRolePro" resultType="com.lz.dto.LineUserRoleDTO">
    select tlurp.role_code
    from t_line_user_role_pro tlurp
    where tlurp.account_id = #{accountId} and tlurp.enable = 0
    group by tlurp.role_code
  </select>
  <select id="selectUserLineRolePerms" parameterType="com.lz.model.TLineUserRolePro" resultType="com.lz.dto.LineUserRoleDTO">
    select tdci.value_content perms, tlurp.role_code
    from t_line_user_role_pro tlurp
    left join t_dic_cat_item tdci on tlurp.role_code = tdci.item_code
    where tlurp.account_id = #{accountId} and tlurp.enable = 0
    group by tdci.value_content, tlurp.role_code
  </select>
  <update id="updateUserRole">
    update t_line_user_role_pro
    set enable = 1
    where account_id = #{accountId} and role_code =#{roleCode}
  </update>

</mapper>