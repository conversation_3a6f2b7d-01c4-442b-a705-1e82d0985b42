<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TZtAccountOpenInfoMapper">
  <resultMap id="BaseResultMap" type="com.lz.model.TZtAccountOpenInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="partner_acc_id" jdbcType="VARCHAR" property="partnerAccId" />
    <result column="channel_id" jdbcType="VARCHAR" property="channelId" />
    <result column="user_open_role" jdbcType="VARCHAR" property="userOpenRole" />
    <result column="sub_acc" jdbcType="VARCHAR" property="subAcc" />
    <result column="open_phone" jdbcType="VARCHAR" property="openPhone" />
    <result column="open_real_name" jdbcType="VARCHAR" property="openRealName" />
    <result column="open_id_card" jdbcType="VARCHAR" property="openIdCard" />
    <result column="if_oneself_open" jdbcType="BOOLEAN" property="ifOneselfOpen" />
    <result column="request_id" jdbcType="VARCHAR" property="requestId" />
    <result column="response_code" jdbcType="VARCHAR" property="responseCode" />
    <result column="response_message" jdbcType="VARCHAR" property="responseMessage" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, partner_acc_id, channel_id, user_open_role, sub_acc, open_phone, 
    open_real_name, open_id_card, if_oneself_open, request_id, response_code, response_message, 
    `status`, remark, create_user, create_time, update_user, update_time
  </sql>
  <select id="selectByExample" parameterType="com.lz.example.TZtAccountOpenInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_zt_account_open_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_zt_account_open_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from t_zt_account_open_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.lz.example.TZtAccountOpenInfoExample">
    delete from t_zt_account_open_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.lz.model.TZtAccountOpenInfo" useGeneratedKeys="true">
    insert into t_zt_account_open_info (account_id, partner_acc_id, channel_id, 
      user_open_role, sub_acc, open_phone, 
      open_real_name, open_id_card, if_oneself_open, 
      request_id, response_code, response_message, 
      `status`, remark, create_user, 
      create_time, update_user, update_time
      )
    values (#{accountId,jdbcType=INTEGER}, #{partnerAccId,jdbcType=VARCHAR}, #{channelId,jdbcType=VARCHAR}, 
      #{userOpenRole,jdbcType=VARCHAR}, #{subAcc,jdbcType=VARCHAR}, #{openPhone,jdbcType=VARCHAR}, 
      #{openRealName,jdbcType=VARCHAR}, #{openIdCard,jdbcType=VARCHAR}, #{ifOneselfOpen,jdbcType=BOOLEAN}, 
      #{requestId,jdbcType=VARCHAR}, #{responseCode,jdbcType=VARCHAR}, #{responseMessage,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.lz.model.TZtAccountOpenInfo" useGeneratedKeys="true">
    insert into t_zt_account_open_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="partnerAccId != null">
        partner_acc_id,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="userOpenRole != null">
        user_open_role,
      </if>
      <if test="subAcc != null">
        sub_acc,
      </if>
      <if test="openPhone != null">
        open_phone,
      </if>
      <if test="openRealName != null">
        open_real_name,
      </if>
      <if test="openIdCard != null">
        open_id_card,
      </if>
      <if test="ifOneselfOpen != null">
        if_oneself_open,
      </if>
      <if test="requestId != null">
        request_id,
      </if>
      <if test="responseCode != null">
        response_code,
      </if>
      <if test="responseMessage != null">
        response_message,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="partnerAccId != null">
        #{partnerAccId,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=VARCHAR},
      </if>
      <if test="userOpenRole != null">
        #{userOpenRole,jdbcType=VARCHAR},
      </if>
      <if test="subAcc != null">
        #{subAcc,jdbcType=VARCHAR},
      </if>
      <if test="openPhone != null">
        #{openPhone,jdbcType=VARCHAR},
      </if>
      <if test="openRealName != null">
        #{openRealName,jdbcType=VARCHAR},
      </if>
      <if test="openIdCard != null">
        #{openIdCard,jdbcType=VARCHAR},
      </if>
      <if test="ifOneselfOpen != null">
        #{ifOneselfOpen,jdbcType=BOOLEAN},
      </if>
      <if test="requestId != null">
        #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="responseCode != null">
        #{responseCode,jdbcType=VARCHAR},
      </if>
      <if test="responseMessage != null">
        #{responseMessage,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.lz.example.TZtAccountOpenInfoExample" resultType="java.lang.Long">
    select count(*) from t_zt_account_open_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_zt_account_open_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.partnerAccId != null">
        partner_acc_id = #{record.partnerAccId,jdbcType=VARCHAR},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=VARCHAR},
      </if>
      <if test="record.userOpenRole != null">
        user_open_role = #{record.userOpenRole,jdbcType=VARCHAR},
      </if>
      <if test="record.subAcc != null">
        sub_acc = #{record.subAcc,jdbcType=VARCHAR},
      </if>
      <if test="record.openPhone != null">
        open_phone = #{record.openPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.openRealName != null">
        open_real_name = #{record.openRealName,jdbcType=VARCHAR},
      </if>
      <if test="record.openIdCard != null">
        open_id_card = #{record.openIdCard,jdbcType=VARCHAR},
      </if>
      <if test="record.ifOneselfOpen != null">
        if_oneself_open = #{record.ifOneselfOpen,jdbcType=BOOLEAN},
      </if>
      <if test="record.requestId != null">
        request_id = #{record.requestId,jdbcType=VARCHAR},
      </if>
      <if test="record.responseCode != null">
        response_code = #{record.responseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.responseMessage != null">
        response_message = #{record.responseMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_zt_account_open_info
    set id = #{record.id,jdbcType=INTEGER},
      account_id = #{record.accountId,jdbcType=INTEGER},
      partner_acc_id = #{record.partnerAccId,jdbcType=VARCHAR},
      channel_id = #{record.channelId,jdbcType=VARCHAR},
      user_open_role = #{record.userOpenRole,jdbcType=VARCHAR},
      sub_acc = #{record.subAcc,jdbcType=VARCHAR},
      open_phone = #{record.openPhone,jdbcType=VARCHAR},
      open_real_name = #{record.openRealName,jdbcType=VARCHAR},
      open_id_card = #{record.openIdCard,jdbcType=VARCHAR},
      if_oneself_open = #{record.ifOneselfOpen,jdbcType=BOOLEAN},
      request_id = #{record.requestId,jdbcType=VARCHAR},
      response_code = #{record.responseCode,jdbcType=VARCHAR},
      response_message = #{record.responseMessage,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=INTEGER},
      remark = #{record.remark,jdbcType=VARCHAR},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.lz.model.TZtAccountOpenInfo">
    update t_zt_account_open_info
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="partnerAccId != null">
        partner_acc_id = #{partnerAccId,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=VARCHAR},
      </if>
      <if test="userOpenRole != null">
        user_open_role = #{userOpenRole,jdbcType=VARCHAR},
      </if>
      <if test="subAcc != null">
        sub_acc = #{subAcc,jdbcType=VARCHAR},
      </if>
      <if test="openPhone != null">
        open_phone = #{openPhone,jdbcType=VARCHAR},
      </if>
      <if test="openRealName != null">
        open_real_name = #{openRealName,jdbcType=VARCHAR},
      </if>
      <if test="openIdCard != null">
        open_id_card = #{openIdCard,jdbcType=VARCHAR},
      </if>
      <if test="ifOneselfOpen != null">
        if_oneself_open = #{ifOneselfOpen,jdbcType=BOOLEAN},
      </if>
      <if test="requestId != null">
        request_id = #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="responseCode != null">
        response_code = #{responseCode,jdbcType=VARCHAR},
      </if>
      <if test="responseMessage != null">
        response_message = #{responseMessage,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lz.model.TZtAccountOpenInfo">
    update t_zt_account_open_info
    set account_id = #{accountId,jdbcType=INTEGER},
      partner_acc_id = #{partnerAccId,jdbcType=VARCHAR},
      channel_id = #{channelId,jdbcType=VARCHAR},
      user_open_role = #{userOpenRole,jdbcType=VARCHAR},
      sub_acc = #{subAcc,jdbcType=VARCHAR},
      open_phone = #{openPhone,jdbcType=VARCHAR},
      open_real_name = #{openRealName,jdbcType=VARCHAR},
      open_id_card = #{openIdCard,jdbcType=VARCHAR},
      if_oneself_open = #{ifOneselfOpen,jdbcType=BOOLEAN},
      request_id = #{requestId,jdbcType=VARCHAR},
      response_code = #{responseCode,jdbcType=VARCHAR},
      response_message = #{responseMessage,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>