<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TWithdrawalApplicationMapper">

    <select id="selectByPage" resultType="com.lz.vo.TWithdrawalApplicationVo" parameterType="com.lz.vo.TCarrierCompanyOpenRoleSearchVo">
        select
          w.*,
          i.item_value as transactionTypeStr
        from t_withdrawal_application w
        LEFT JOIN t_dic_cat_item i on i.item_code = w.transaction_type
        where 1=1 and w.enable = 0 and w.user_open_role !='CD'
        <if test="tradeType != null" >
            and w.transaction_type = #{tradeType}
        </if>
        order by w.create_time desc
    </select>

    <select id="selectByApprovalStatusNo" resultType="com.lz.model.TWithdrawalApplication" parameterType="com.lz.model.TWithdrawalApplication">
        select
        w.*
        from t_withdrawal_application w
        where 1=1 and w.enable = 0
        <if test="openRoleId != null" >
            and w.open_role_id = #{openRoleId}
        </if>
        <if test="userOpenRole != null" >
            and w.user_open_role = #{userOpenRole}
        </if>
        and w.approval_status is null
        order by w.create_time desc
    </select>

    <select id="selectByBizOrderNo" resultType="com.lz.model.TWithdrawalApplication" >
        select
          w.*
        from t_withdrawal_application w
        where  w.enable = 0 and w.biz_order_no = #{bizOrderNo}
    </select>
    <select id="selectByCode" resultType="com.lz.model.TWithdrawalApplication" >
        select
          w.*
        from t_withdrawal_application w
        where  w.enable = 0 and w.code = #{code}
    </select>
</mapper>