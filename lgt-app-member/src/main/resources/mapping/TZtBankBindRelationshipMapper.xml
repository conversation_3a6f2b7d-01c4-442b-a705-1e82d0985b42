<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TZtBankBindRelationshipMapper">
  <resultMap id="BaseResultMap" type="com.lz.model.TZtBankBindRelationship">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_bank_id" jdbcType="INTEGER" property="accountBankId" />
    <result column="request_id" jdbcType="VARCHAR" property="requestId" />
    <result column="bank_no" jdbcType="VARCHAR" property="bankNo" />
    <result column="link_account_type" jdbcType="INTEGER" property="linkAccountType" />
    <result column="bind_status" jdbcType="VARCHAR" property="bindStatus" />
    <result column="bind_message" jdbcType="VARCHAR" property="bindMessage" />
    <result column="request_code" jdbcType="VARCHAR" property="requestCode" />
    <result column="request_message" jdbcType="VARCHAR" property="requestMessage" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="account_open_id" jdbcType="INTEGER" property="accountOpenId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_bank_id, request_id, bank_no, link_account_type, bind_status, bind_message, 
    request_code, request_message, create_user, create_time, update_user, update_time, 
    account_open_id
  </sql>
  <select id="selectByExample" parameterType="com.lz.example.TZtBankBindRelationshipExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_zt_bank_bind_relationship
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_zt_bank_bind_relationship
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from t_zt_bank_bind_relationship
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.lz.example.TZtBankBindRelationshipExample">
    delete from t_zt_bank_bind_relationship
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.lz.model.TZtBankBindRelationship" useGeneratedKeys="true">
    insert into t_zt_bank_bind_relationship (account_bank_id, request_id, bank_no, 
      link_account_type, bind_status, bind_message, 
      request_code, request_message, create_user, 
      create_time, update_user, update_time, 
      account_open_id)
    values (#{accountBankId,jdbcType=INTEGER}, #{requestId,jdbcType=VARCHAR}, #{bankNo,jdbcType=VARCHAR}, 
      #{linkAccountType,jdbcType=INTEGER}, #{bindStatus,jdbcType=VARCHAR}, #{bindMessage,jdbcType=VARCHAR}, 
      #{requestCode,jdbcType=VARCHAR}, #{requestMessage,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{accountOpenId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.lz.model.TZtBankBindRelationship" useGeneratedKeys="true">
    insert into t_zt_bank_bind_relationship
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountBankId != null">
        account_bank_id,
      </if>
      <if test="requestId != null">
        request_id,
      </if>
      <if test="bankNo != null">
        bank_no,
      </if>
      <if test="linkAccountType != null">
        link_account_type,
      </if>
      <if test="bindStatus != null">
        bind_status,
      </if>
      <if test="bindMessage != null">
        bind_message,
      </if>
      <if test="requestCode != null">
        request_code,
      </if>
      <if test="requestMessage != null">
        request_message,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="accountOpenId != null">
        account_open_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountBankId != null">
        #{accountBankId,jdbcType=INTEGER},
      </if>
      <if test="requestId != null">
        #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="bankNo != null">
        #{bankNo,jdbcType=VARCHAR},
      </if>
      <if test="linkAccountType != null">
        #{linkAccountType,jdbcType=INTEGER},
      </if>
      <if test="bindStatus != null">
        #{bindStatus,jdbcType=VARCHAR},
      </if>
      <if test="bindMessage != null">
        #{bindMessage,jdbcType=VARCHAR},
      </if>
      <if test="requestCode != null">
        #{requestCode,jdbcType=VARCHAR},
      </if>
      <if test="requestMessage != null">
        #{requestMessage,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountOpenId != null">
        #{accountOpenId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.lz.example.TZtBankBindRelationshipExample" resultType="java.lang.Long">
    select count(*) from t_zt_bank_bind_relationship
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_zt_bank_bind_relationship
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountBankId != null">
        account_bank_id = #{record.accountBankId,jdbcType=INTEGER},
      </if>
      <if test="record.requestId != null">
        request_id = #{record.requestId,jdbcType=VARCHAR},
      </if>
      <if test="record.bankNo != null">
        bank_no = #{record.bankNo,jdbcType=VARCHAR},
      </if>
      <if test="record.linkAccountType != null">
        link_account_type = #{record.linkAccountType,jdbcType=INTEGER},
      </if>
      <if test="record.bindStatus != null">
        bind_status = #{record.bindStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.bindMessage != null">
        bind_message = #{record.bindMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.requestCode != null">
        request_code = #{record.requestCode,jdbcType=VARCHAR},
      </if>
      <if test="record.requestMessage != null">
        request_message = #{record.requestMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.accountOpenId != null">
        account_open_id = #{record.accountOpenId,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_zt_bank_bind_relationship
    set id = #{record.id,jdbcType=INTEGER},
      account_bank_id = #{record.accountBankId,jdbcType=INTEGER},
      request_id = #{record.requestId,jdbcType=VARCHAR},
      bank_no = #{record.bankNo,jdbcType=VARCHAR},
      link_account_type = #{record.linkAccountType,jdbcType=INTEGER},
      bind_status = #{record.bindStatus,jdbcType=VARCHAR},
      bind_message = #{record.bindMessage,jdbcType=VARCHAR},
      request_code = #{record.requestCode,jdbcType=VARCHAR},
      request_message = #{record.requestMessage,jdbcType=VARCHAR},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      account_open_id = #{record.accountOpenId,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.lz.model.TZtBankBindRelationship">
    update t_zt_bank_bind_relationship
    <set>
      <if test="accountBankId != null">
        account_bank_id = #{accountBankId,jdbcType=INTEGER},
      </if>
      <if test="requestId != null">
        request_id = #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="bankNo != null">
        bank_no = #{bankNo,jdbcType=VARCHAR},
      </if>
      <if test="linkAccountType != null">
        link_account_type = #{linkAccountType,jdbcType=INTEGER},
      </if>
      <if test="bindStatus != null">
        bind_status = #{bindStatus,jdbcType=VARCHAR},
      </if>
      <if test="bindMessage != null">
        bind_message = #{bindMessage,jdbcType=VARCHAR},
      </if>
      <if test="requestCode != null">
        request_code = #{requestCode,jdbcType=VARCHAR},
      </if>
      <if test="requestMessage != null">
        request_message = #{requestMessage,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountOpenId != null">
        account_open_id = #{accountOpenId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lz.model.TZtBankBindRelationship">
    update t_zt_bank_bind_relationship
    set account_bank_id = #{accountBankId,jdbcType=INTEGER},
      request_id = #{requestId,jdbcType=VARCHAR},
      bank_no = #{bankNo,jdbcType=VARCHAR},
      link_account_type = #{linkAccountType,jdbcType=INTEGER},
      bind_status = #{bindStatus,jdbcType=VARCHAR},
      bind_message = #{bindMessage,jdbcType=VARCHAR},
      request_code = #{requestCode,jdbcType=VARCHAR},
      request_message = #{requestMessage,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      account_open_id = #{accountOpenId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>