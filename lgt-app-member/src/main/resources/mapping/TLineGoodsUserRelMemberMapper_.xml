<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lz.dao.TLineGoodsUserRelMemberMapper" >
  <resultMap id="BaseResultMapVo" type="com.lz.vo.TLineGoodsUserRelVo" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="line_goods_rel_id" property="lineGoodsRelId" jdbcType="INTEGER" />
    <result column="account_info_id" property="accountInfoId" jdbcType="INTEGER" />
    <result column="role_code" property="roleCode" jdbcType="VARCHAR" />
    <result column="role_value" property="roleValue" jdbcType="VARCHAR" />
    <result column="if_principal" property="ifPrincipal" jdbcType="BIT" />
    <result column="current_account_no" property="currentAccountNo" jdbcType="VARCHAR" />
    <result column="current_account_name" property="currentAccountName" jdbcType="VARCHAR" />
    <result column="current_account_phone" property="currentAccountPhone" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="param1" property="param1" jdbcType="VARCHAR" />
    <result column="param2" property="param2" jdbcType="VARCHAR" />
    <result column="param3" property="param3" jdbcType="VARCHAR" />
    <result column="param4" property="param4" jdbcType="VARCHAR" />
    <result column="operate_method" property="operateMethod" jdbcType="INTEGER" />
    <result column="operator_ip" property="operatorIp" jdbcType="VARCHAR" />
    <result column="create_user" property="createUser" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="enable" property="enable" jdbcType="BIT" />
    <result column="company_name" property="companyName" jdbcType="VARCHAR" />
    <result column="line_name" property="lineName" jdbcType="VARCHAR" />
    <result column="line_id" property="lineId" jdbcType="INTEGER" />
    <result column="role_name" property="roleName" jdbcType="VARCHAR" />
    <result column="company_id" property="companyId" jdbcType="VARCHAR" />
  </resultMap>

  <update id="updateRecord" parameterType="com.lz.model.TLineGoodsUserRelMember">
    update t_line_goods_user_rel
    <set >
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="lineGoodsRelId != null" >
        line_goods_rel_id = #{lineGoodsRelId,jdbcType=INTEGER},
      </if>
      <if test="accountInfoId != null" >
        account_info_id = #{accountInfoId,jdbcType=INTEGER},
      </if>
      <if test="roleCode != null" >
        role_code = #{roleCode,jdbcType=VARCHAR},
      </if>
      <if test="ifPrincipal != null" >
        if_principal = #{ifPrincipal,jdbcType=BIT},
      </if>
      <if test="currentAccountNo != null" >
        current_account_no = #{currentAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="currentAccountName != null" >
        current_account_name = #{currentAccountName,jdbcType=VARCHAR},
      </if>
      <if test="currentAccountPhone != null" >
        current_account_phone = #{currentAccountPhone,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="param1 != null" >
        param1 = #{param1,jdbcType=VARCHAR},
      </if>
      <if test="param2 != null" >
        param2 = #{param2,jdbcType=VARCHAR},
      </if>
      <if test="param3 != null" >
        param3 = #{param3,jdbcType=VARCHAR},
      </if>
      <if test="param4 != null" >
        param4 = #{param4,jdbcType=VARCHAR},
      </if>
      <if test="operateMethod != null" >
        operate_method = #{operateMethod,jdbcType=INTEGER},
      </if>
      <if test="operatorIp != null" >
        operator_ip = #{operatorIp,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null" >
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null" >
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null" >
        enable = #{enable,jdbcType=BIT},
      </if>
    </set>
    where 1=1
    <if test="lineGoodsRelId != null">
      and line_goods_rel_id = #{lineGoodsRelId,jdbcType=INTEGER}
    </if>
    <if test="accountInfoId != null">
      and account_info_id = #{accountInfoId,jdbcType=INTEGER}
    </if>
  </update>
  <select id="selectByAccountId" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from t_line_goods_user_rel a
    where account_info_id = #{accountId,jdbcType=INTEGER}
  </select>

  <select id="selectByModel" resultMap="BaseResultMapVo" parameterType="com.lz.model.TLineGoodsUserRelMember" >
    SELECT
      c.company_name AS company_name,
      c.id company_id,
      a.line_goods_rel_id,
      l.line_name AS line_name,
      l.id as line_id,
      a.if_principal,
      GROUP_CONCAT(a.role_code) role_code
    FROM
      t_line_goods_user_rel a
        LEFT JOIN t_line_goods_rel g ON g.id = a.line_goods_rel_id
        LEFT JOIN t_line_info l ON l.id = g.line_id
        LEFT JOIN t_company_info c ON c.id = a.company_id
        left join t_company_line tcl on g.line_id = tcl.line_info_id
      WHERE
          a.account_info_id = #{accountInfoId,jdbcType=INTEGER} and tcl.id is null
      AND a.ENABLE =#{enable,jdbcType=BIT}
        <if test="companyId != null">
          and a.company_id = #{companyId}
        </if>
      GROUP BY
        a.line_goods_rel_id,
        c.company_name,
        c.id,
        l.line_name,
        a.if_principal
  </select>

  <select id="getDataByParam" resultMap="BaseResultMapVo" parameterType="com.lz.model.TLineGoodsUserRelMember" >
    SELECT
    c.company_name AS company_name,
    c.id company_id,
    a.line_goods_rel_id,
    CONCAT("[",g.goods_name,"]",l.line_name) AS line_name,
    l.id as line_id,
    a.if_principal,
    GROUP_CONCAT(a.role_code) role_value,
    GROUP_CONCAT(tdci.item_value) role_code
    FROM
    t_line_goods_user_rel a
    left join t_dic_cat_item tdci on tdci.item_code = a.role_code
    LEFT JOIN t_line_goods_rel g ON g.id = a.line_goods_rel_id
    LEFT JOIN t_line_info l ON l.id = g.line_id
    LEFT JOIN t_company_info c ON c.id = a.company_id
    left join t_company_line tcl on g.line_id = tcl.line_info_id
    WHERE
    a.account_info_id = #{accountInfoId,jdbcType=INTEGER} and tcl.id is null
    AND a.ENABLE =#{enable,jdbcType=BIT}
    <if test="companyId != null">
      and a.company_id = #{companyId}
    </if>
    GROUP BY
    a.line_goods_rel_id,
    c.company_name,
    c.id,
    l.line_name,
    a.if_principal
  </select>

  <select id="selectRoleByAccountId" parameterType="com.lz.model.TLineUserRolePro" resultType="com.lz.model.TLineUserRolePro">
    select *
    from t_line_user_role_pro
    <where>
      <if test="id != null">
        and account_id = #{id}
      </if>
      <if test="roleCode != null">
        and role_code = #{roleCode}
      </if>
      <if test="enable != null">
        and enable = #{enable}
      </if>
    </where>
  </select>

  <select id="selectUserLineButton" parameterType="com.lz.model.TLineGoodsUserRel" resultType="com.lz.model.TLineGoodsUserRel">
    select *
    from t_line_goods_user_rel
    <where>
      enable = 0
      <if test="companyId != null">
        and company_id = #{companyId}
      </if>
      <if test="roleCode != null">
        and role_code =#{roleCode}
      </if>
      <if test="lineGoodsRelId != null">
        and line_goods_rel_id = #{lineGoodsRelId}
      </if>
      <if test="accountInfoId != null">
        and account_info_id = #{accountInfoId}
      </if>
    </where>
  </select>

  <update id="updateLineRoleByAccountId" parameterType="com.lz.model.TAccount">
    update t_line_goods_user_rel
    <set>
      <if test="enable != null">
        enable = #{enable}
      </if>
    </set>
    <where>
      <if test="id!= null">
        and account_info_id = #{id}
      </if>
    </where>
  </update>

  <select id="selectCompany" parameterType="java.lang.Integer" resultType="com.lz.dto.CompanyInfoDTO">
    select tlgur.company_id, tci.company_name, tci.if_rel_entrust, tci.if_rel_client, IFNULL(t.stationCnt,0) stationCnt
    from t_line_goods_user_rel tlgur
    left join t_company_info tci on tlgur.company_id = tci.id
    left join t_company_station_rel tcsr on tci.id = tcsr.company_id
    LEFT JOIN (SELECT company_id,count(*) stationCnt from t_company_station_rel where `enable`=0
    GROUP BY company_id) t on tci.id = t.company_id
    <where>
      tlgur.enable= 0
      <if test="accountId != null">
        and tlgur.account_info_id = #{accountId}
      </if>
    </where>
    group by tlgur.company_id, t.stationCnt
  </select>
  <select id="selectCompanyForAdmin" parameterType="java.lang.Integer" resultType="com.lz.dto.CompanyInfoDTO">
    SELECT
      tci.id company_id,
      tci.company_name,
      tci.if_rel_entrust,
      tci.if_rel_client,
      IFNULL(t.stationCnt, 0) stationCnt
    FROM
      t_company_info tci
        LEFT JOIN (
        SELECT
          company_id,
          count(*) stationCnt
        FROM
          t_company_station_rel
        WHERE
          `enable` = 0
        GROUP BY
          company_id
      ) t ON tci.id = t.company_id
    where tci.enable = 0 and tci.id = #{id}
  </select>
  <select id="selectLineIfPrincipal" resultType="com.lz.model.TLineGoodsUserRel">
        select *
        from t_line_goods_user_rel
        where line_goods_rel_id= #{lineGoodsRelId}
          and company_id = #{companyId}
          and if_principal = 1
          and role_code in
          <foreach collection="roles" index="index" item="role" open="(" close=")" separator=",">
            #{role}
          </foreach>
          and enable = 0
  </select>

  <select id="selectUserLineRole" parameterType="com.lz.model.TLineGoodsUserRelMember" resultType="java.lang.String">
    select role_code as roleCode
    from t_line_goods_user_rel
    where account_info_id = #{accountInfoId} and line_goods_rel_id = #{lineGoodsRelId} and `enable` = 0
  </select>

  <select id="selectUserOtherLineRole" parameterType="com.lz.model.TLineGoodsUserRelMember" resultType="com.lz.model.TLineGoodsUserRelMember">
    select role_code roleCode
    from t_line_goods_user_rel
    where
          account_info_id = #{accountInfoId}
          and line_goods_rel_id <![CDATA[ <> ]]> #{lineGoodsRelId}
          and `enable` = 0
  </select>

  <select id="selectUserLineRoleForCompany" parameterType="com.lz.model.TLineGoodsUserRelMember" resultType="java.lang.String">
    select role_code roleCode
    from t_line_goods_user_rel
    where
    account_info_id = #{accountInfoId}
    and `enable` = 0
    and company_id = #{companyId}
    <if test="lineGoodsRelId != null">
      and line_goods_rel_id = #{lineGoodsRelId}
    </if>
  </select>

  <select id="selectUserAllLineRoleGroupByLine" parameterType="java.lang.Integer" resultType="com.lz.model.TLineGoodsUserRelMember">
    SELECT
      line_goods_rel_id lineGoodsRelId ,GROUP_CONCAT(role_code) roleCode
    FROM
      t_line_goods_user_rel
    WHERE
      account_info_id = #{accountId}
      AND `enable` = 0
    GROUP BY
      line_goods_rel_id
  </select>
  <update id="updateUserLineRoleEnbaleByAccountId" parameterType="com.lz.model.TLineGoodsUserRelMember">
    update t_line_goods_user_rel
    set enable = 1
    <where>
      <if test="accountInfoId != null">
        and account_info_id = #{accountInfoId}
      </if>
    <if test="lineGoodsRelId != null">
      and line_goods_rel_id = #{lineGoodsRelId}
    </if>
    <if test="roleCode != null">
      and role_code= #{roleCode}
    </if>
    </where>
  </update>

  <update id="updateUserLineRoleByAccountId" parameterType="com.lz.model.TLineGoodsUserRelMember">
    update t_line_goods_user_rel
    set authorized_buttons = #{authorizedButtons}, if_principal = #{ifPrincipal}
    where line_goods_rel_id = #{lineGoodsRelId} and account_info_id = #{accountInfoId} and role_code = #{roleCode} and `enable` = 0
  </update>
  <!-- 根据 account_id 查询员工角色 Yan -->
  <select id="getLineGoodsUserRole" resultType="com.lz.dto.LineUserRoleDTO" parameterType="java.lang.Integer">
    SELECT DISTINCT
    lgur.company_id, #<!-- 企业ID -->
    lgur.line_goods_rel_id, #<!-- 线路货物关系 -->
    lgur.role_code, #<!-- 角色code -->
    IFNULL(lgur.`enable`, 0) as enables,
    eui.id, #<!-- id -->
    eui.user_logistics_role, #<!-- c端的类型， 用来判断是经纪人吗 -->
    tdci.param2 roleType #<!-- 角色类型 -->
    FROM t_account ta
    LEFT JOIN t_enduser_account tea ON tea.account_id = ta.id
    LEFT JOIN t_end_user_info eui ON tea.enduser_id = eui.id
    LEFT JOIN t_line_goods_user_rel lgur ON ta.id = lgur.account_info_id
    LEFT JOIN t_dic_cat_item tdci ON tdci.item_code = lgur.role_code
    WHERE ta.id = #{accountInfoId} AND lgur.`enable`=0
  </select>

  <!-- 查询是否有支付权限 Yan -->
  <select id="judgeOrderPayPms" parameterType="com.lz.model.TLineGoodsUserRelMember" resultType="java.lang.Integer">
    SELECT
        lgur.id
    FROM
        t_line_goods_user_rel lgur
    WHERE
        lgur.role_code = 'PAYORDER1'
    AND lgur.line_goods_rel_id = #{lineGoodsRelId}
    AND lgur.account_info_id = #{accountInfoId}
    and lgur.enable = 0
  </select>

  <!-- 查询是否有支付权限 Yan -->
  <select id="selectByCompanyIdAndRole" resultType="java.lang.Integer">
    SELECT
      DISTINCT
        lgur.company_id
    FROM
        t_line_goods_user_rel lgur
    WHERE
        lgur.role_code = 'PAYORDER1' and lgur.enable = 0
        AND lgur.account_info_id = #{accountInfoId}
        and company_id in
        <foreach collection="companyIds" index="index" item="companyId" open="(" close=")" separator=",">
          #{companyId}
        </foreach>
  </select>
  <select id="getRelCodeByAccountId" resultType="com.lz.model.TLineGoodsUserRel">
    select * from t_line_goods_user_rel
    where `enable` = 0
    <if test="accountId!= null">
      and account_info_id = #{accountId}
    </if>
    GROUP BY role_code
  </select>
  <select id="selectResourceDesc" resultType="com.lz.vo.TLineGoodsUserRelVo">
    SELECT
      c.company_name AS company_name,
      c.id company_id,
      a.line_goods_rel_id,
      l.line_name AS line_name,
      l.id as line_id,
      a.if_principal,
      a.account_info_id,
      GROUP_CONCAT(a.role_code) role_code,
      GROUP_CONCAT(tdci.item_value) role_value
    FROM
      t_line_goods_user_rel a
        LEFT JOIN t_dic_cat_item tdci on tdci.item_code = a.role_code
        LEFT JOIN t_line_goods_rel g ON g.id = a.line_goods_rel_id
        LEFT JOIN t_line_info l ON l.id = g.line_id
        LEFT JOIN t_company_info c ON c.id = l.company_id
        LEFT JOIN t_company_line tcl on g.line_id = tcl.line_info_id
    WHERE tcl.id is null AND a.ENABLE = 0
      <if test="companyId!= null">
        AND c.id = #{companyId}
      </if>
      <if test="accountInfoId!= null">
        AND a.account_info_id = #{accountInfoId}
      </if>
      <if test="lineGoodsRelId!= null">
        AND a.line_goods_rel_id = #{lineGoodsRelId}
      </if>
    GROUP BY
      a.line_goods_rel_id,
      c.company_name,
      c.id,
      l.line_name,
      a.if_principal
  </select>

  <select id="getListByCompanyIdAndLineGoodsRelId" resultType="com.lz.model.TLineGoodsUserRel">
    select distinct role_code, account_info_id, if_principal
    from t_line_goods_user_rel
    where enable = 0
      and company_id = #{companyId}
      and line_goods_rel_id = #{lineGoodsRelId}
      and role_code in ('COMPANYSENDORDER', 'RECEIVEORDER')
  </select>
</mapper>