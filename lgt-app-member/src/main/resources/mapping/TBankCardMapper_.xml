<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TBankCardMapper">
    <select id="selectCardByAccountId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT COUNT(*)FROM `t_bank_card`where account_id=#{accountid,jdbcType=INTEGER} and enable=0
    </select>

    <select id="selectCardByAccountIdByType" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT COUNT(*)FROM `t_bank_card`where account_id=#{accountid,jdbcType=INTEGER} and enable=0
        and id not in (
         select DISTINCT tbc.id
           FROM
         t_end_user_open_role teuor
         left join t_end_user_info teui on teuor.end_user_id = teui.id
         LEFT JOIN t_jd_bank_card jdbc ON teuor.id = jdbc.open_role_id
         LEFT JOIN t_bank_card tbc ON jdbc.bank_card_id = tbc.id
         WHERE
         teuor.ENABLE = 0  AND jdbc.ENABLE = 0 AND tbc.ENABLE = 0
		 AND jdbc.user_open_role = 'CD' and jdbc.data_enable = 1 AND teuor.end_user_id = #{enduserId}
        )
    </select>

    <select id="getBankCardListByAccountId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT tbc.`card_no`,tbc.`bank_name`,tbc.`card_owner`,tbc.`id`,tbc.if_default
        FROM `t_bank_card` tbc LEFT JOIN `t_account` t ON tbc.`account_id`=t.`id`
        where tbc.account_id=#{accountid,jdbcType=INTEGER} and tbc.enable=0
    </select>

    <select id="selectImperfectBankCardList"  resultType="com.lz.vo.ImperfectBankCardVO">
        SELECT
        DISTINCT
        tbc.`card_no`,
        tbc.`bank_name`,
        tbc.`card_owner`,
        tbc.card_owner_idcard,
        tbc.`id`,
        tbc.if_default,
        tbc.card_owner_phone,
        tbc.address,
        tbc.idcard_valid_beginning,
        tbc.idcard_valid_until,
        t.account_no,
        teui.real_name,
		teui.id as endUserId,
        tea.idcard
        FROM `t_bank_card` tbc
        LEFT JOIN `t_account` t ON tbc.`account_id`=t.`id`
        left join t_enduser_account tea on t.id  = tea.account_id
        left join t_end_user_info teui on teui.id = tea.enduser_id
        where tbc.account_id= #{accountid}
        and teui.user_logistics_role LIKE CONCAT('%',  #{userLogisticsRole}, '%')
        and tbc.enable=0
        and t.enable=0
        and tea.enable=0
        and teui.enable=0
    </select>
    <select id="getBankCardListByCardId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT tbc.`card_no`,tbc.`bank_name`,tbc.`id`,tbc.card_owner,tbc.if_default, tbc.card_owner_idcard, tbc.card_owner_idcard_photo1,
               tbc.card_owner_idcard_photo2, tbc.idcard_valid_beginning, tbc.idcard_valid_until, tbc.card_owner_phone
        FROM `t_bank_card` tbc LEFT JOIN `t_account` t ON tbc.`account_id`=t.`id`
        where tbc.id=#{id,jdbcType=INTEGER} and tbc.enable=0
    </select>
    <select id="getBankCardListByCardNo" resultMap="BaseResultMap">
       SELECT *
       FROM `t_bank_card`
       where card_no=#{cardNo} and enable=0 and account_id=#{accouontid}
    </select>

    <select id="selectBankCardByEndUserId" resultMap="BaseResultMap">
       SELECT bank.*
       FROM `t_bank_card` bank
       LEFT JOIN t_enduser_account ea ON ea.account_id = bank.account_id
       WHERE ea.enduser_id = #{id} AND bank.enable=FALSE AND bank.if_default=TRUE
    </select>
    <select id="selectIdDefault"  resultMap="BaseResultMap" parameterType="java.lang.Integer">
        SELECT *
        FROM `t_bank_card`
        where enable=0 and account_id=#{accountid} and if_default=1
    </select>

    <update id="updateByAccountId" parameterType="java.lang.Integer">
    update t_bank_card
    set if_default = 0
    where account_id = #{accountid,jdbcType=INTEGER}
  </update>
    <update id="setIsDefault" parameterType="java.lang.Integer">
    update t_bank_card
    set if_default = 1
    where  id = #{id,jdbcType=INTEGER}
  </update>

    <update id="setSomeOneIsDefault" parameterType="java.lang.Integer">
    UPDATE t_bank_card
    SET if_default = 1
    WHERE  ENABLE=0 AND account_id=#{userAccountId,jdbcType=INTEGER} ORDER BY if_default ASC  LIMIT 1
  </update>
    <!--根据C端用户Id查询已有银行卡 Yan-->
    <select id="getEndUserCard" parameterType="java.lang.Integer" resultType="hashmap">
        SELECT
            tbc.id as id,
            tbc.card_no as cardNo, <!-- 卡号 -->
            tbc.card_owner as cardOwner, <!-- 持卡人 -->
            tbc.card_owner_idcard as cardOwnerIdcard, <!-- 持卡人身份证 -->
            tbc.card_owner_phone as cardOwnerPhone, <!-- 持卡人银行预留手机号 -->
            tbc.if_default ifDefault,
            tbc.address as address,
            tbc.idcard_valid_beginning as idcardValidBeginning,
            tbc.idcard_valid_until as idcardValidUntil,
            t.account_no as accountNo,
            tea.idcard as idcard
        FROM t_bank_card tbc
        left join t_account t on t.id = tbc.account_id
        LEFT JOIN t_enduser_account tea ON tbc.account_id = tea.account_id
        WHERE tea.enduser_id = #{endUserId} and tbc.enable = 0 AND tea.enable=0
    </select>

    <select id="selectBankCardListDetail" parameterType="com.lz.vo.TBankCardVo" resultType="com.lz.dto.BankCardDTO">
        SELECT
        tbc.id as id,
        tbc.card_no as cardNo, <!-- 卡号 -->
        tbc.card_owner as cardOwner, <!-- 持卡人 -->
        tbc.card_owner_idcard as cardOwnerIdcard, <!-- 持卡人身份证 -->
        tbc.card_owner_phone as cardOwnerPhone, <!-- 持卡人银行预留手机号 -->
        teui.phone
        FROM t_bank_card tbc
        LEFT JOIN t_enduser_account tea ON tbc.account_id = tea.account_id
        left join t_end_user_info teui on tea.enduser_id = teui.id
        WHERE tbc.id = #{bankId} and tbc.enable = 0
    </select>

    <select id="selectAllBankNoByBankNo" parameterType="com.lz.vo.TBankCardVo" resultType="java.lang.String">
        SELECT
            tbc.card_no
        FROM
            t_bank_card tbc
        WHERE
            tbc.card_owner_idcard = ( SELECT card_owner_idcard FROM t_bank_card WHERE id = #{id} )
            AND tbc.ENABLE = 0
        GROUP BY
            tbc.card_no
    </select>
    <select id="selectBankCardByNowAccount" resultMap="BaseResultMap">
        SELECT
        bank.id,
        bank.card_owner,
        bank.card_owner_idcard,
        bank.bank_name,
        bank.remark,
        bank.if_default,
        bank.card_no
        FROM
        `t_bank_card` bank
        LEFT JOIN t_enduser_account ea ON ea.account_id = bank.account_id
        WHERE
        ea.enduser_id = #{endUserId} AND bank.enable=0 AND ea.enable=0
    </select>
    <select id="selectCarrierByEnduserId" resultType="java.util.Map">
        select
        tai.id as carrierId,
        tai.carrier_name,
        tr.uid,
        tr.id as carrierEnduserCompanyRelId
        from t_carrier_enduser_company_rel  tr
        LEFT JOIN t_end_user_info ti on tr.enduser_company_id=ti.id
        LEFT JOIN t_carrier_info tai on tr.carrier_id=tai.id
        where ti.id=#{id}
        and datasouce='CD'
        and ti.enable=0
        and tr.enable=0
        and tai.enable=0
    </select>

    <select id="selectEnduserByBankId" parameterType="java.lang.Integer" resultType="com.lz.dto.EndUserDTO">
        SELECT
            tea.enduser_id enduserId, teui.user_logistics_role userLogisticsRole
        FROM
            t_bank_card tbc
                left join t_enduser_account tea on tbc.account_id = tea.account_id and tea.enable = 0
                left join t_end_user_info teui on teui.id = tea.enduser_id
        WHERE
                tbc.card_owner_idcard = ( SELECT card_owner_idcard FROM t_bank_card WHERE id = #{bankId})
                AND tbc.ENABLE = 0 and tbc.if_default = 1
        GROUP BY
            tbc.account_id, tea.enduser_id
    </select>

    <select id="selectAllBankNoByBankId" parameterType="com.lz.vo.TBankCardVo" resultType="java.lang.Integer">
        SELECT
            tbc.id
        FROM
            t_bank_card tbc
        WHERE
            tbc.card_owner_idcard = ( SELECT card_owner_idcard FROM t_bank_card WHERE id = #{id} )
            AND tbc.ENABLE = 0
        GROUP BY
            tbc.id
    </select>

    <select id="selectBankCardNotJdList" resultType="com.lz.model.TBankCard">
        SELECT
        DISTINCT
        tbc.*
        FROM
        t_bank_card tbc
        LEFT JOIN t_jd_bank_card tjbc ON tbc.id = tjbc.bank_card_id
        WHERE
        tbc.account_id = #{accountId}
        AND tbc.card_owner = #{cardOwner}
        AND tbc.card_owner_idcard = #{cardOwnerIdcard}
        AND tbc.ENABLE = 0
        AND ( tjbc.ENABLE = 1 OR tjbc.ENABLE IS NULL )
        AND ( tjbc.data_enable = 0 OR tjbc.ENABLE IS NULL )
        AND ( tjbc.bind_status != 'BINDING' OR tjbc.bind_status IS NULL )
    </select>

    <select id="selectOneselfBankcard" resultType="com.lz.model.TBankCard">
      SELECT * FROM t_bank_card
      WHERE account_id = #{accountId} AND card_owner = #{cardOwner}
	  AND card_owner_idcard = #{cardOwnerIdcard} AND ENABLE = 0
    </select>

    <select id="getUserBankCardList" parameterType="java.lang.Integer" resultType="hashmap" >
        SELECT
            DISTINCT IF (
                             IFNULL(tbc.card_owner, TRUE),
                             tbc.card_no,
                             CONCAT(tbc.card_owner, ': ', tbc.card_no)
                         ) AS card, tbc.card_no cardNo, tbc.id
        FROM t_end_user_info eui
                 LEFT JOIN t_enduser_account tea ON tea.enduser_id = eui.id
                 LEFT JOIN t_bank_card tbc ON tbc.account_id = tea.account_id
        WHERE eui.id = #{enduserId} and tbc.`enable`= 0 and tea.enable = 0
    </select>

    <select id="selectEnduserByBankCardId" resultType="com.lz.model.TEndUserInfo">
        select teui.*
        from
            t_bank_card tbc
            left join t_enduser_account tea on tbc.account_id = tea.account_id
            left join t_end_user_info teui on tea.enduser_id = teui.id
        where tbc.id = #{bankCardId} and  tbc.enable = 0 and tea.enable = 0 and teui.enable = 0
    </select>

    <select id="selectByCardNo"  resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT *
        FROM `t_bank_card`
        where enable=0 and card_no=#{cardNo}
    </select>

    <select id="noOneselfBankCardList" resultType="com.lz.model.TBankCard">
        SELECT
            tbc.*
        FROM
            t_bank_card tbc
            LEFT JOIN t_jd_bank_card tjbc ON tbc.id = tjbc.bank_card_id
        WHERE
            tbc.ENABLE = 0
            AND (
            ( tjbc.ENABLE = 1 AND tjbc.data_enable = 0 AND tjbc.bind_status = "BINDING" )
            OR ( tjbc.ENABLE = 0 AND tjbc.data_enable = 1 AND tjbc.bind_status = "BIND" )
            )
            AND tjbc.open_role_id = #{openRoleId}
    </select>
    <select id="selectCarrierCompanyOpenRoleByCardNo" resultType="com.lz.model.TCarrierCompanyOpenRole">
        SELECT
            tccor.*
        FROM
            t_bank_card tbc
                LEFT JOIN t_jd_bank_card tjbc ON tbc.id = tjbc.bank_card_id
                LEFT JOIN t_carrier_company_open_role tccor ON tjbc.open_role_id = tccor.id
        WHERE
            tbc.card_no = #{cardNo} and tbc.enable = 0 and tjbc.enable = 0 and tccor.enable = 0
    </select>

</mapper>