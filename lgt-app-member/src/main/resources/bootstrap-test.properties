eureka.client.serviceUrl.defaultZone=*********************************************/eureka/
spring.cloud.config.discovery.serviceId=lgt-app-config
spring.cloud.config.discovery.enabled=true
spring.cloud.config.profile=test
spring.cloud.config.label=master

logging.level.com.lz.dao=DEBUG

spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL5InnoDBDialect
spring.data.redis.repositories.enabled=false
spring.jpa.open-in-view=true
spring.jpa.hibernate.ddl-auto=update
spring.data.jpa.repositories.enabled=true

mybatis.configuration.map-underscore-to-camel-case=true


spring.rabbitmq.publisher-returns=true
#�����ֶ�Ӧ��
#spring.rabbitmq.listener.simple.acknowledge-mode=manual
#ָ����С������������
spring.rabbitmq.listener.simple.concurrency=1
#ָ����������������
spring.rabbitmq.listener.simple.max-concurrency=1
#�Ƿ�֧������
spring.rabbitmq.listener.simple.retry.enabled=true

rocketmq.groupId.GID_JD_PAY_COMMON_DRIVER=GID_JD_PAY_COMMON_DRIVER
rocketmq.groupId.GID_JD_PAY_COMMON_CAPTAIN=GID_JD_PAY_COMMON_CAPTAIN
rocketmq.groupId.GID_JD_PAY_MANAGER_DRIVER=GID_JD_PAY_MANAGER_DRIVER
rocketmq.groupId.GID_JD_PAY_MANAGER_CAPTAIN=GID_JD_PAY_MANAGER_CAPTAIN
rocketmq.groupId.GID_JD_MEMBER=GID_JD_MEMBER
rocketmq.groupId.GID_DRIVER_AUDIT=GID_DRIVER_AUDIT
rocketmq.groupId.GID_VEHICLE_AUDIT=GID_VEHICLE_AUDIT
rocketmq.groupId.GID_TEST_MQ=GID_TEST_MQ
rocketmq.accessKey=LTAI5tEBqBfojE4kD4KYYg1Y
rocketmq.secretKey=******************************
rocketmq.nameSrvAddr=http://MQ_INST_1364524203166562_BXSHCMfU.mq-internet-access.mq-internet.aliyuncs.com:80

partnerId=71D36F3AF57EF6861E3CA269F0686F2E
merchantCode=M1011252043000000000000000000001


hxyhPartnerId=CAB8CA6F601C4242B66CD261ABE110C6
hxyhChannelId=HXBANK

tmpFilePath=/root/deploy_cmp/temp/