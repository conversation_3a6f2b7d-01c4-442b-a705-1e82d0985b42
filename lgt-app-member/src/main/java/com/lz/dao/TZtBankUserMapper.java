package com.lz.dao;

import com.lz.common.util.ResultUtil;
import com.lz.example.TZtBankUserExample;
import com.lz.model.TZtBankBindRelationship;
import com.lz.model.TZtBankCard;
import com.lz.model.TZtBankUser;
import com.lz.vo.ImperfectTZtBankCardVO;
import com.lz.vo.TZtBankCardVo;
import io.swagger.models.auth.In;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface TZtBankUserMapper {
    long countByExample(TZtBankUserExample example);

    int deleteByExample(TZtBankUserExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TZtBankUser record);

    int insertSelective(TZtBankUser record);

    List<TZtBankUser> selectByExample(TZtBankUserExample example);

    TZtBankUser selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TZtBankUser record, @Param("example") TZtBankUserExample example);

    int updateByExample(@Param("record") TZtBankUser record, @Param("example") TZtBankUserExample example);

    int updateByPrimaryKeySelective(TZtBankUser record);

    int updateByPrimaryKey(TZtBankUser record);

    List<ImperfectTZtBankCardVO> selectImperfectBankCardList(@Param("accountId") Integer accountId,@Param("userLogisticsRole")  String userLogisticsRole);

    List<TZtBankCard> getTZtBankCardListByCardNo(@Param("acctNo") String acctNo,@Param("accountId") Integer accountId,
                                                 @Param("acctName") String acctName);

    Integer updateByAccountId(@Param("accountId") Integer accountId);

    Integer  selectByBankNum(@Param("accountId") Integer accountId);

    Integer selectByBankNumAndByStatus(@Param("accountId") Integer accountId);

    TZtBankCardVo getHxyhBankCardDetail(@Param("bankId")  Integer bankId,@Param("accountId")  Integer accountId);

    TZtBankUser selectByBankIdAndAccountId(@Param("bankId")  Integer bankId,@Param("accountId")  Integer accountId);

    List<TZtBankCardVo> hxBankCardList(@Param("accountId") Integer accountId,@Param("userOpenRole") String userOpenRole);

    TZtBankCard selectBankByEndUserId(@Param("endUserId") Integer endUserId);

    TZtBankBindRelationship selectByOpenRoleIdAndBankId(@Param("accountId") Integer accountId,@Param("bankId") Integer bankId);

}