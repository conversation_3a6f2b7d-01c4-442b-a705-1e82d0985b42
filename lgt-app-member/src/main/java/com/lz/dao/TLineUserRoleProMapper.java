package com.lz.dao;

import com.lz.dto.LineUserRoleDTO;
import com.lz.model.TLineUserRolePro;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TLineUserRoleProMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TLineUserRolePro record);

    int insertSelective(TLineUserRolePro record);

    TLineUserRolePro selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TLineUserRolePro record);

    int updateByPrimaryKeyWithBLOBs(TLineUserRolePro record);

    int updateByPrimaryKey(TLineUserRolePro record);

    List<TLineUserRolePro> list(TLineUserRolePro record);

    int updateByRecord(TLineUserRolePro record);

    List<LineUserRoleDTO> selectUserLineRole(@Param(value = "accountId") Integer accountId);

    List<LineUserRoleDTO> selectUserLineRolePerms(@Param(value = "accountId") Integer accountId);

    int updateUserRole(@Param("accountId") Integer accountId, @Param("roleCode") String roleCode);
}