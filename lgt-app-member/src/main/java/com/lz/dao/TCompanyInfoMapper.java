package com.lz.dao;

import com.lz.common.model.TCompanyInfoDTO;
import com.lz.dto.*;
import com.lz.model.TCompanyInfo;
import com.lz.model.TCompanyInfoWithBLOBs;
import com.lz.vo.*;
import io.swagger.models.auth.In;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TCompanyInfoMapper {

    /**
     * 根据企业ID查询企业信息
     * @param id
     * @return
     */
    TCompanyInfo selectCompanyInfoById(Integer id);

    /**
     * 根据Sys_user表ID 查询出企业
     * @param userId
     * @return
     */
    List<TCompanyInfoDTO> selectCompanyBySystemId(Integer userId);

    /**
     * 根据用户id 查询所属企业id
     * Yan
     * @param userId
     * @return
     */
    List<TCompanyInfo> selectCompanyByUserId(Integer userId);

    int deleteByPrimaryKey(Integer id);

    int insertSelective(TCompanyInfo record);

    TCompanyInfoWithBLOBs selectByPrimaryKey(Integer id);

    List<TCompanyInfo> selectRepeatCompany(TCompanyInfoQuery record);

    List<TCompanyInfo> list(TCompanyInfoQuery record);

    int updateByPrimaryKeySelective(TCompanyInfo record);

    void updateStausByDelete(TCompanyInfo record);

    List<CompanyInfoDTO> selectByPage(TCompanyInfoQuery companyInfoQuery);

    List<CompanyOpenInfoDTO> selectCompanyOpenInfoList(CompanyOpenInfoListVO companyOpenInfoListVO);

    CompanyOpenInfoDetailsDTO applyCompanyOpenInfo(CompanyOpenInfoDetailsVO companyOpenInfoDetailsVO);

    List<CompanyInfoDTO> enterpriseExport(TCompanyInfoQuery record);

    CompanyInfoByIdDTO selectById(Integer companyId);

    List<CompanyInfoDTO> selectCompany();

    List<CompanyInfoDTO> selectCompanyByPage(TCompanyInfoQuery companyInfoQuery);

    List<TCompanyInfo> selectByCarrierId(@Param("carrierId") Integer carrierId);

    List<TCompanyProjectVo> selectByCompanyProject(@Param("companyId") Integer companyId);

    List<TCompanyProjectVo> selectByCompanyProjectAmp(@Param("companyId") Integer companyId);

    List<TCompanyProjectVo> selectByCompanyProjectPack(@Param("companyId") Integer companyId);


    List<TCompanyProjectVo> selectByCompanyProjectAnrCarrierId(@Param("companyId") Integer companyId,@Param("carrierId") Integer carrierId);

    List<TCompanyProjectVo> selectByCompanyProjectAmpAnrCarrierId(@Param("companyId") Integer companyId,@Param("carrierId") Integer carrierId);

    List<TCompanyProjectVo> selectByCompanyProjectPackAnrCarrierId(@Param("companyId") Integer companyId,@Param("carrierId") Integer carrierId);


    List<TCompanyProjectVo> selectByCompanyAnrCarrierProject(@Param("companyId") Integer companyId,@Param("carrierId") Integer carrierId);

    CarrierCompanyRelDTO selectCompanyCarrierInfo(@Param("id") Integer id, @Param("carrierId") Integer carrierId);

    int updateCompanyUploadStatus(TCompanyInfo record);

    List<CompanyInfoDTO> selectByBusinessId(@Param("id") Integer id);

    List<TCompanyInfo> selectByCompanyName(@Param("companyName") String companyName);

    List<CompanyInfoDTO> selectByBusinessIdOrCompanyName(@Param("id") Integer id, @Param("companyName") String companyName);
}