package com.lz.dao;

import com.lz.model.TCompanyInfoDetail;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【t_company_info_detail】的数据库操作Mapper
* @createDate 2023-12-09 16:15:35
* @Entity com.lz.model.TCompanyInfoDetail
*/
public interface TCompanyInfoDetailMapper {

    int deleteByPrimaryKey(Long id);

    int insert(TCompanyInfoDetail record);

    int insertSelective(TCompanyInfoDetail record);

    TCompanyInfoDetail selectByPrimaryKey(Long id);

    TCompanyInfoDetail selectByCompanyId(@Param("companyId") Integer companyId);

    int updateByPrimaryKeySelective(TCompanyInfoDetail record);

    int updateByPrimaryKey(TCompanyInfoDetail record);

}
