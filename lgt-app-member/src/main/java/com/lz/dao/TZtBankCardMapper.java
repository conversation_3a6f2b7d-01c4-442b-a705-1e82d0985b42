package com.lz.dao;

import com.lz.dto.BandCardDTO;
import com.lz.example.TZtBankCardExample;
import com.lz.model.TZtBankCard;
import com.lz.vo.TZtBankCardVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TZtBankCardMapper {
    long countByExample(TZtBankCardExample example);

    int deleteByExample(TZtBankCardExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TZtBankCard record);

    int insertSelective(TZtBankCard record);

    List<TZtBankCard> selectByExample(TZtBankCardExample example);

    TZtBankCard selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TZtBankCard record, @Param("example") TZtBankCardExample example);

    int updateByExample(@Param("record") TZtBankCard record, @Param("example") TZtBankCardExample example);

    int updateByPrimaryKeySelective(TZtBankCard record);

    int updateByPrimaryKey(TZtBankCard record);

    List<TZtBankCardVo> selectBankCardInfoByAccountId(@Param("accountId") Integer accountId);

    TZtBankCard selectByAcctNo(@Param("acctNo") String acctNo);

    List<BandCardDTO> selectBankCards(@Param("endUserId") Integer endUserId);

}