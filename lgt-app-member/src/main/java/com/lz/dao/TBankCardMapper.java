package com.lz.dao;

import com.lz.dto.BankCardDTO;
import com.lz.dto.EndUserDTO;
import com.lz.example.TBankCardExample;
import com.lz.model.TBankCard;
import com.lz.model.TCarrierCompanyOpenRole;
import com.lz.vo.ImperfectBankCardVO;
import com.lz.vo.TBankCardVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface TBankCardMapper {

    /**
     * 根据C端用户Id查询已有银行卡
     * Yan
     *
     * @param endUserId
     * @return
     */
    List<Map<String, Object>> getEndUserCard(@Param("endUserId") Integer endUserId);

    long countByExample(TBankCardExample example);

    int deleteByExample(TBankCardExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TBankCard record);

    int insertSelective(TBankCard record);

    List<TBankCard> selectByExampleWithBLOBs(TBankCardExample example);

    List<TBankCard> selectByExample(TBankCardExample example);

    TBankCard selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TBankCard record, @Param("example") TBankCardExample example);

    int updateByExampleWithBLOBs(@Param("record") TBankCard record, @Param("example") TBankCardExample example);

    int updateByExample(@Param("record") TBankCard record, @Param("example") TBankCardExample example);

    int updateByPrimaryKeySelective(TBankCard record);

    int updateByPrimaryKeyWithBLOBs(TBankCard record);

    int updateByPrimaryKey(TBankCard record);

    int selectCardByAccountId(@Param("accountid") Integer accountid);

    int selectCardByAccountIdByType(@Param("accountid") Integer accountid,@Param("enduserId") Integer enduserId);

    List<TBankCard> getBankCardListByAccountId(@Param("accountid") Integer accountid);

    List<ImperfectBankCardVO> selectImperfectBankCardList(@Param("accountid") Integer accountid,@Param("userLogisticsRole") String userLogisticsRole);

    TBankCard getBankCardListByCardId(@Param("id") Integer id);

    List<TBankCard> getBankCardListByCardNo(@Param("cardNo") String cardNo, @Param("accouontid") Integer accouontid);

    TBankCard selectBankCardByEndUserId(Integer id);

    TBankCard selectIdDefault(@Param("accountid") Integer accountid);

    Integer updateByAccountId(@Param("accountid") Integer accountid);

    Integer setIsDefault(@Param("id") Integer id);

    int setSomeOneIsDefault(Integer userAccountId);

    List<BankCardDTO> selectBankCardListDetail(TBankCardVo record);

    List<String> selectAllBankNoByBankNo(TBankCardVo record);

    List<TBankCard> selectBankCardByNowAccount(@Param("endUserId") Integer endUserId);

    List<Map> selectCarrierByEnduserId(@Param("id") Integer id);

    List<EndUserDTO> selectEnduserByBankId(@Param("bankId") Integer bankId);

    List<Integer> selectAllBankNoByBankId(TBankCardVo record);

    List<TBankCard> selectBankCardNotJdList(@Param("accountId") Integer accountId,@Param("cardOwner") String cardOwner,@Param("cardOwnerIdcard") String cardOwnerIdcard);

    List<TBankCard> selectOneselfBankcard(@Param("accountId") Integer accountId,@Param("cardOwner") String cardOwner,@Param("cardOwnerIdcard") String cardOwnerIdcard);

    List<Map<String, Object>> getUserBankCardList(@Param("enduserId") Integer enduserId);

    List<TBankCard> selectByCardNo(@Param("cardNo") String cardNo);

    List<TBankCard> noOneselfBankCardList(@Param("openRoleId") Integer openRoleId);

    TCarrierCompanyOpenRole selectCarrierCompanyOpenRoleByCardNo(@Param("cardNo") String cardNo);

}