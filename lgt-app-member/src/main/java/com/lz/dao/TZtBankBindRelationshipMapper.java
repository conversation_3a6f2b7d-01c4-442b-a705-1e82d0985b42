package com.lz.dao;

import com.lz.dto.BindBankCardRelationDTO;
import com.lz.example.TZtBankBindRelationshipExample;
import com.lz.model.TZtBankBindRelationship;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TZtBankBindRelationshipMapper {
    long countByExample(TZtBankBindRelationshipExample example);

    int deleteByExample(TZtBankBindRelationshipExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TZtBankBindRelationship record);

    int insertSelective(TZtBankBindRelationship record);

    List<TZtBankBindRelationship> selectByExample(TZtBankBindRelationshipExample example);

    TZtBankBindRelationship selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TZtBankBindRelationship record, @Param("example") TZtBankBindRelationshipExample example);

    int updateByExample(@Param("record") TZtBankBindRelationship record, @Param("example") TZtBankBindRelationshipExample example);

    int updateByPrimaryKeySelective(TZtBankBindRelationship record);

    int updateByPrimaryKey(TZtBankBindRelationship record);

    List<TZtBankBindRelationship> selectByBankUserIdAndAccountOpenId(@Param("accountId") Integer accountId);

    BindBankCardRelationDTO selectByAcctNo(@Param("acctNo") String acctNo);

}