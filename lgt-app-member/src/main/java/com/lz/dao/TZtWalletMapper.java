package com.lz.dao;

import com.lz.example.TZtWalletExample;
import com.lz.model.TZtWallet;
import com.lz.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface TZtWalletMapper {
    long countByExample(TZtWalletExample example);

    int deleteByExample(TZtWalletExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TZtWallet record);

    int insertSelective(TZtWallet record);

    List<TZtWallet> selectByExample(TZtWalletExample example);

    TZtWallet selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TZtWallet record, @Param("example") TZtWalletExample example);

    int updateByExample(@Param("record") TZtWallet record, @Param("example") TZtWalletExample example);

    int updateByPrimaryKeySelective(TZtWallet record);

    int updateByPrimaryKey(TZtWallet record);

    TZtWalletVO selectByAccountId(@Param("accountId") Integer accountId);

    List<Map<String,Object>> walletHj(@Param("accountId") Integer accountId);

    List<CompanyWalletCapitalFlow> companyWalletCapitalFlow(CompanyWalletCapitalFlowVO companyWalletCapitalFlowVO);

    List<DriverWalletCapitaclFlow> driverWalletCapitalFlow(CompanyWalletCapitalFlowVO param);

    Map<String,Object> selectDriverInfo(@Param("enduserId") Integer enduserId);

    List<CarrierCompanyHxOpenRoleVo> selectByHxCarrierWalletPage(CarrierHxOpenRoleSearchVo record);

    List<CarrierCompanyHxOpenRoleVo> selectCarrierHxWalletLogPage(CarrierHxOpenRoleSearchVo record);

    TZtWalletVO selectHxPlatformWallet();

    List<CarrierCompanyHxOpenRoleVo> selectHxPlatformWalletList(CarrierHxOpenRoleSearchVo param);

    List<DriverCapitalFlow> selectHxAgentWalletList(CarrierHxOpenRoleSearchVo param);

    Map<String,Object> selectHxAgentWalletSum(@Param("endUserId") Integer endUserId);


    TZtWallet selectWalletByCarrierCompanyId(@Param("accountId") Integer accountId,
                                             @Param("dataSource") String dataSource);

    List<TZtWallet> selectWalletByEnduserIdList(@Param("list") List<Integer> driverList);

    TZtWallet selectByPartnerAccId(@Param("partnerAccId") String partnerAccId);

    int updateStatusByPayCode(TOrderPayDetailVO ov);

    int updateStatusByCode(@Param("code") String code, @Param("status") String status);


}