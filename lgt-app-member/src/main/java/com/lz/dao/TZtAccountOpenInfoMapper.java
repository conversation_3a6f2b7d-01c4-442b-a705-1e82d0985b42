package com.lz.dao;

import com.lz.dto.PcZTEndUserOpenRoleListDTO;
import com.lz.dto.ListOpenRoleStatusDTO;
import com.lz.dto.TZtAccountOpenInfoDTO;
import com.lz.example.TZtAccountOpenInfoExample;
import com.lz.model.TZtAccountOpenInfo;
import com.lz.vo.PcZTEndUserOpenRoleListVO;
import com.lz.model.TZtBankCard;
import com.lz.vo.HxWithdrawVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TZtAccountOpenInfoMapper {
    long countByExample(TZtAccountOpenInfoExample example);

    int deleteByExample(TZtAccountOpenInfoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TZtAccountOpenInfo record);

    int insertSelective(TZtAccountOpenInfo record);

    List<TZtAccountOpenInfo> selectByExample(TZtAccountOpenInfoExample example);

    TZtAccountOpenInfo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TZtAccountOpenInfo record, @Param("example") TZtAccountOpenInfoExample example);

    int updateByExample(@Param("record") TZtAccountOpenInfo record, @Param("example") TZtAccountOpenInfoExample example);

    int updateByPrimaryKeySelective(TZtAccountOpenInfo record);

    int updateByPrimaryKey(TZtAccountOpenInfo record);

    TZtAccountOpenInfo selectByAccountId(@Param("accountId") Integer accountId,@Param("userOpenRole") String userOpenRole);

    TZtAccountOpenInfoDTO selectNotSelfOpenByAccountId(@Param("accountId") Integer accountId, @Param("userOpenRole") String userOpenRole);

    TZtAccountOpenInfo selectOpenInfoByAccountId(@Param("accountId") Integer accountId);

    List<PcZTEndUserOpenRoleListDTO> pcEndUserOpenRoleList(PcZTEndUserOpenRoleListVO record);

    List<TZtAccountOpenInfo> selectByDrviverList(@Param("list") List<Integer> driverList);

    List<ListOpenRoleStatusDTO> selectByDrviverListStatus(@Param("list") List<Integer> driverList);

    HxWithdrawVo selectByWithdrawCairrer(HxWithdrawVo record);

    HxWithdrawVo selectByWithdrawPf(HxWithdrawVo record);

    HxWithdrawVo selectByWithdrawCompany(HxWithdrawVo record);

    HxWithdrawVo selectByWithdrawManager(HxWithdrawVo record);

    List<TZtBankCard> selectByWithdrawBankList(HxWithdrawVo record);

    List<TZtAccountOpenInfo> selectByIdcard(@Param("idcard") String idcard, @Param("partnerAccId") String partnerAccId);

    List<TZtAccountOpenInfo> selectByBusinessLicenseNo(@Param("businessLicenseNo") String businessLicenseNo, @Param("partnerAccId") String partnerAccId);


    List<TZtAccountOpenInfo> selectNotByAccountId(TZtAccountOpenInfo record);

    TZtAccountOpenInfo selectOrderOpenInfoByAccountId(@Param("enduserId") Integer enduserId);

    List<TZtAccountOpenInfo> selectOrderOpenInfoListByAccountId(@Param("enduserId") Integer enduserId);

    TZtAccountOpenInfo selectByAccountIdAndChannelId(TZtAccountOpenInfo record);
}
