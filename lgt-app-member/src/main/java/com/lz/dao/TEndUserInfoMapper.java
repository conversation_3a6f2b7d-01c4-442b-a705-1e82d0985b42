package com.lz.dao;

import com.lz.dto.*;
import com.lz.example.TEndUserInfoExample;
import com.lz.model.*;
import com.lz.system.model.TSysUser;
import com.lz.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface TEndUserInfoMapper {

    /**
     * @Description: 根据手机号判断，用户是否已申请合同电子签名
     * @Author: Yan
     * @Date: 2019/8/13/013 11:22
     * @Param: phone
     * @Return: JudgeEndUserDTO
     */
    JudgeEndUserDTO judgeEndUserIfOnlineSign(@Param("phone") String phone,@Param("endUserId") Integer endUserId);

    /**
     * @Description: 判断姓名和身份证号是否存在数据库里
     * @Author: Yan
     * @Date: 2019/7/13/013 9:57
     * @Param: idcardm, realName
     * @Return: boolean
     */
    boolean judgeRealNameIdcardIsExists(@Param("idcard") String idcard, @Param("realName") String realName);

    /**
     * APP端 会员详情-获取会员信息
     * Yan
     * @param endUserId
     * @return
     */
    AppSearchMemberInfoDTO getMemberDetailInfo(Integer endUserId);


    /**
    * @Description APP端 会员详情-获取车主信息
    * <AUTHOR>
    * @Date   2019/10/21 10:06
    * @Param
    * @Return
    * @Exception
    *
    */
    AppSearchMemberInfoDTO getCarOwnerInfo(@Param("endCarId")Integer endCarId);

    /**
     * 运单打包： 获取用户所有银行卡
     * Yan
     * @param id
     * @return
     */
    List<Map<String, Object>> getUserBankCard(Integer id);

    /**
     * 判断C端用户是否有银行卡
     * true没有   false有
     * Yan
     * @return
     */
    Boolean judgeAgentCar(Integer id);

    long countByExample(TEndUserInfoExample example);

    int deleteByExample(TEndUserInfoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TEndUserInfo record);

    int insertSelective(TEndUserInfo record);

    List<TEndUserInfo> selectByExampleWithBLOBs(TEndUserInfoExample example);

    List<TEndUserInfo> selectByExample(TEndUserInfoExample example);

    TEndUserInfo selectByPrimaryKey(Integer id);

    TEndUserInfoIdDTO selectById(Integer id);

    int updateByExampleSelective(@Param("record") TEndUserInfo record, @Param("example") TEndUserInfoExample example);

    int updateByExampleWithBLOBs(@Param("record") TEndUserInfo record, @Param("example") TEndUserInfoExample example);

    int updateByExample(@Param("record") TEndUserInfo record, @Param("example") TEndUserInfoExample example);

    TAccount selectUserIdByEnduserId(@Param("enduserid") Integer id);

    int updateByPrimaryKeySelective(TEndUserInfo record);

    int updateByPrimaryKeyWithBLOBs(TEndUserInfo record);

    int updateByPrimaryKey(TEndUserInfo record);

    List<TEndUserInfoVO> selectByPage(TEndUserInfoSearchVO param);

    int deleteByUserID(Integer[] array);

    int approval(ApprovalVO param);

    List<EndUserDTO> selectEndUser(TEndUserInfoSearchVO record);

    /**
     * 根据企业id查询业务部
     * @param record
     * @return
     */
    List<EndUserDTO> selectManage(TEndUserInfoSearchVO record);

    List<EndUserDTO> selectManageByCompanyId(@Param(value = "companyId") Integer companyId);

    List setGroupConcatMaxLen();

    List<TEndUserInfoVO> selectByCarNumber(TEndUserInfoVO vo);

    TEndCarInfo selectByCarInfo(@Param("endUserid") Integer endUserid);

    EndUserDTO selectEnduserByACoount(TEndUserInfoSearchVO record);

    List<TDriverDto> selectDriverByPage(DriverListPageVO param);

    List<Map<String, Object>>  selectDriverInfo(@Param("enduserId") Integer enduserId);

    EndUserDTO selectEnduserInfo(@Param("enduserId") Integer enduserId);

    List<TEndUserInfo> findphone(@Param("phone")String phone);

    List<TEndUserInfo> findIdCard(@Param("idcard")String idcard);

    TEnduserAccount findByEndUserId(@Param("endUserId") Integer endUserId);

    TWallet selectByWalltId(@Param("endUserId") Integer endUserId, @Param("carrierId") Integer carrierId, @Param("purseCategory") String purseCategory);

    BankCardDTO selectByEndUserIdAndBankInfo(@Param("endUserId") Integer endUserId);

    List<TDriverDto> selectDriverByPageLjtx(DriverListPageVO param);

    List<TEndUserInfo> selectByEndUserIdList(@Param("endIdList") List<Integer> endIdList);

    int updateEnduserInfoForFeign(TEndUserInfoVO record);

    /**
     * @Description: 判断手机号是否存在end_user_info中
     * @Author: Yan
     * @Date: 2019/8/27/027 20:29
     * @Param:  phone
     * @Return:
     */
   List<String> judgePhoneExistence(String phone);

   /**
    * @Description: 查询所有车主
    * @Author: Yan
    * @Date: 2019/10/15/015 11:03
    * @Param:
    * @Return:
    */
   List<CarOwnerSelectDTO> getAllCarOwner(@Param("companyIds") List<String> companyIds);

   //查询所有车队长
   List<CarOwnerSelectDTO> getAllCaptain(@Param("companyIds") List<String> companyIds);

   List<TEndcarAndUserMemberDTO> selectUserAndCarAuditStatusByPackOrder(@Param(value = "code") String code);

    /**
     * 根据企业id查询经纪人
     * @param record
     * @return
     */
    List<EndUserDTO> selectAgent(TEndUserInfoSearchVO record);

    void updateAuditAgent(TEndUserInfoVO tEndUserInfoVO);

    void deleteGoodsManagerRel(Integer id);

    TLineGoodsManagerRel selectByLineGoodsrRelId(@Param("lineGoodsrRelId") String lineGoodsrRelId);

    EndUserDTO selectEnduserForDTO(@Param("id") Integer id, @Param("enable") Boolean enable);

    List<EndUserDTO> selectAgentByLineGoodsRelId(@Param("lineGoodsRelId") Integer lineGoodsRelId);

    CarrierCompanyRelDTO selectCEnduserCarrierInfo(@Param("id") Integer id, @Param("carrierId") Integer carrierId);

    List<TEnduserAccount> selectEndUserId(TEnduserAccount vo);

    List<TEndUserInfo> selectCaptainstate(TEndUserInfo vo);

    Integer selectenduserAccount(@Param(value = "id")Integer id);

    Integer CaptainEndUserAccount(TEndUserInfoVO vo);

    List<TEndUserInfoVO> selectCaptainByPage(TEndUserInfoVO vo);

    List<TEndUserInfo> selectCaptainOrderInfo(@Param(value = "id")Integer id);

    List<TEnduserAccount> selectEnduserList(TEnduserAccount vo);

    List<TEndUserInfo> selectCaptainNameList(@Param("parameter")String parameter);

    List<TEndUserInfo> selectByAccountNo(@Param("accountNo") String accountNo);

    List<TEndUserInfo> selectByCaptainList(@Param("np") String np);

    List<TEndUserInfo> selectCaptainTypeList(TEndUserInfo tEndUserInfo);



    TSysUser selectSysUserIfAgreement(@Param("phone")String phone);

    void updateSysUserIfAgreement(@Param("id")Integer id);

    String selectTransferorRole(@Param("idcard") String idcard);

    List<TransferorInfoDTO> selectTransferorPage(@Param("record") TEndUserInfoSearchVO vo);

    TransferorInfoDTO selectTransferorInfo(@Param("endUserId") Integer endUserId, @Param("cardId") Integer cardId);

    TransferorInfoDTO selectTransferorInfoById(@Param("endUserId") Integer endUserId);

    AgentpersonInfoDTO selectAgentpersonInfo(@Param("endUserId") Integer endUserId, @Param("cardId") Integer cardId);

    List<TEndUserInfo> selectByPhoneAndIdcard(@Param("phone") String phone, @Param("idcard") String idcard);

    List<TEndUserInfo> selectByIdcard(@Param("idcard") String idcard);

    List<TEndUserInfo> notOwnIdcardList(@Param("idcard") String idcard,@Param("id") Integer id);

    List<TEndUserInfo> selectOneselfRealName(@Param("accountid") Integer accountid);

    List<TEndUserInfo> selectIdcardUserLogisticsRole(@Param("idCard") String idCard , @Param("userLogisticsRole") String userLogisticsRole);

    TEndUserInfoVO selectenduserAccountInfo(@Param("endUserId") Integer endUserId);

    int insertEndUserInfo(TEndUserInfo tEndUserInfo);

    void updateDataById(TEndUserInfo tEndUserInfo);

    int updateAuditStatusById(TEndUserInfo endUserInfo);

    TEndUserInfoVO driverIdentityVerificationStatus(@Param("phone") String phone,@Param("userLogisticsRole") String userLogisticsRole);

    TEndUserInfo selectenduserinfoByPhoneRole(@Param("phone") String phone,@Param("userLogisticsRole") String userLogisticsRole);

    int updateDriverPhone(@Param("phone") String phone ,@Param("newphone") String newphone,@Param("userLogisticsRole") String userLogisticsRole);

    DriverInfoDto getDriverInfo(@Param("param") DriverQueryParam param);

    List<VehicleInfo> getDriverVehicleInfo(@Param("param") DriverQueryParam param);

    TEndUserInfo getDataByPhoneAndIdcard(@Param("phone") String phone,
                                        @Param("idcard") String idcard,
                                        @Param("realName") String realName);
}