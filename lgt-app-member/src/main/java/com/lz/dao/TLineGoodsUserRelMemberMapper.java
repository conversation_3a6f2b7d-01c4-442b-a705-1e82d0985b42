package com.lz.dao;

import com.lz.dto.CompanyInfoDTO;
import com.lz.dto.LineUserRoleDTO;
import com.lz.model.TAccount;
import com.lz.model.TLineGoodsUserRel;
import com.lz.model.TLineGoodsUserRelMember;
import com.lz.model.TLineUserRolePro;
import com.lz.vo.TLineGoodsUserRelMemberVo;
import com.lz.vo.TLineGoodsUserRelVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.HashMap;
import java.util.List;

public interface TLineGoodsUserRelMemberMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TLineGoodsUserRelMember record);

    int insertSelective(TLineGoodsUserRelMember record);

    TLineGoodsUserRelMember selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TLineGoodsUserRelMember record);

    int updateByPrimaryKey(TLineGoodsUserRelMember record);

    List<TLineGoodsUserRelMember> selectByAccountId(Integer accountId);

    List<TLineGoodsUserRelVo> selectByModel(TLineGoodsUserRelMember record);

    List<TLineGoodsUserRelVo> getDataByParam(TLineGoodsUserRelMember record);

    int updateRecord(TLineGoodsUserRelMember record);

    int updateLineRoleByAccountId(TAccount userAccount);

    List<TLineUserRolePro> selectRoleByAccountId(TLineUserRolePro lineUserRolePro);

    /**
     * 根据员工id、企业id、货源id、角色coe,查询对应按钮
     * @param record
     * @return
     */
    TLineGoodsUserRel selectUserLineButton(TLineGoodsUserRel record);


    List<CompanyInfoDTO> selectCompany(@Param(value = "accountId") Integer accountId);


    /**
    * @Description 查询线路是否已经有负责人
    * <AUTHOR>
    * @Date   2019/6/8 15:18
    * @Param
    * @Return
    * @Exception
    *
    */
    List<TLineGoodsUserRel> selectLineIfPrincipal(HashMap record);


    /**
    * @Description 查询员工线路角色
    * <AUTHOR>
    * @Date   2019/6/9 16:02
    * @Param
    * @Return
    * @Exception
    *
    */
    List<String> selectUserLineRole(TLineGoodsUserRelMember record);


    /**
    * @Description 查询员工其他线路角色
    * <AUTHOR>
    * @Date   2019/6/9 23:39
    * @Param
    * @Return
    * @Exception
    *
    */
    List<TLineGoodsUserRelMember> selectUserOtherLineRole(TLineGoodsUserRelMember record);


    /**
    * @Description 查询员工线路角色(根据线路分组)
    * <AUTHOR>
    * @Date   2019/6/10 9:57
    * @Param
    * @Return
    * @Exception
    *
    */
    List<TLineGoodsUserRelMember> selectUserAllLineRoleGroupByLine(@Param("accountId") Integer accountId);

    int updateUserLineRoleEnbaleByAccountId(TLineGoodsUserRelMember record);


    /**
    * @Description 查询员工在当前企业下的线路上的角色的信息
    * <AUTHOR>
    * @Date   2019/6/24 20:21
    * @Param
    * @Return
    * @Exception
    *
    */
    List<String> selectUserLineRoleForCompany(TLineGoodsUserRelMember record);


    /**
    * @Description 修改员工线路角色
    * <AUTHOR>
    * @Date   2019/7/1 14:37
    * @Param
    * @Return
    * @Exception
    *
    */
    int updateUserLineRoleByAccountId(TLineGoodsUserRelMember record);


    /**
     * @Description: 根据 account_id 查询员工角色
     * @Author: Yan
     * @Date: 2019/8/17/017 8:54
     * @Param: account_id
     * @Return: String 线路上员工的角色
     */
    List<LineUserRoleDTO> getLineGoodsUserRole(Integer accountId);

    /**
     * @Description: 判断用户是否有支付权限
     * @Author: Yan
     * @Date: 2019/11/20/020 15:53
     * @Param:
     * @Return:
     */
    Integer judgeOrderPayPms(TLineGoodsUserRelMember tLineGoodsUserRelMember);


    /**
    * @Description 发单查询企业管理员企业信息
    * <AUTHOR>
    * @Date   2019/8/20 23:57
    * @param
    * @Return
    * @Exception
    *
    */
    CompanyInfoDTO selectCompanyForAdmin(@Param("id") Integer id);

    List<Integer> selectByCompanyIdAndRole(@RequestParam TLineGoodsUserRelMemberVo record);

    List<TLineGoodsUserRel> getRelCodeByAccountId(@Param("accountId") Integer accountId);

    TLineGoodsUserRelVo selectResourceDesc(TLineGoodsUserRelMember record);

    /**
     * 根据线路id企业ID查询线路用户列表
     * @param companyId 企业ID
     * @param lineGoodsRelId 线路ID
     * @return 用户列表
     */
    List<TLineGoodsUserRel> getListByCompanyIdAndLineGoodsRelId(@Param("companyId") Integer companyId, @Param("lineGoodsRelId") Integer lineGoodsRelId);
}