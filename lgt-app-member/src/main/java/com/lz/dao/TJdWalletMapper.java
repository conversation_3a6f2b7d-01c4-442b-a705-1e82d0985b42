package com.lz.dao;

import com.lz.example.TJdWalletExample;
import com.lz.model.TJdWallet;
import com.lz.vo.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface TJdWalletMapper {

    long countByExample(TJdWalletExample example);

    int deleteByExample(TJdWalletExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TJdWallet record);

    int insertSelective(TJdWallet record);

    List<TJdWallet> selectByExample(TJdWalletExample example);

    TJdWallet selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TJdWallet record, @Param("example") TJdWalletExample example);

    int updateByExample(@Param("record") TJdWallet record, @Param("example") TJdWalletExample example);

    int updateByPrimaryKeySelective(TJdWallet record);

    int updateByPrimaryKey(TJdWallet record);

    TJdWallet selectWalletByCarrierCompanyId(@Param("carrierCompanyId") Integer carrierCompanyId,
                                             @Param("dataSource") String dataSource);

    TJdWallet selectWalletByEndUserId(@Param("endUserId") Integer endUserId,
                                      @Param("purseCategory") String purseCategory);

    List<TCarrierCompanyOpenRoleVo> selectByCarrierWalletPage(TCarrierCompanyOpenRoleSearchVo record);

    //京东企业钱包详情
    TJdWalletVo pcCompanyCwDetail(Integer companyId);
    List<Map<String, Object>> walletHj (Integer companyId);
    List<TCompanyProjectVo> selectByCompanyProject(@Param("companyId") Integer companyId);
    List<TCompanyProjectVo> selectByCompanyProjectAmp(@Param("companyId") Integer companyId);
    List<TCompanyProjectVo> selectByCompanyProjectPack(@Param("companyId") Integer companyId);
    List<CompanyCapitalFlow> companyCapitalFlow(CompanyCapitalFlowVo companyCapitalFlowVo);


    //网商钱包与京东钱包总和
    Map<String,Object> selectDriverSum(@Param("enduserId") Integer enduserId);
    //京东终端钱包
    Map<String,Object> selectDriverInfo(@Param("enduserId") Integer enduserId);

    //京东终端钱包流水
    List<DriverCapitalFlow> driverCapitalFlow(CompanyCapitalFlowVo companyCapitalFlowVo);

    List<TJdWallet> selectEnduserListWallet(@Param("list") List<Integer> driverList);

    TJdWallet selectByRoleTypeAndComId(@Param("openRole") String openRole,@Param("openRoleId") Integer openRoleId);

    Map<String,Object> selectAgentWalletSum(@Param("endUserId") Integer endUserId);

    //京东经纪人钱包流水
    List<DriverCapitalFlow> selectAgentWalletList(CompanyCapitalFlowVo companyCapitalFlowVo);

    TJdWalletVo selectPfWallet();

    List<CompanyCapitalFlow> selectPfWalletList(CompanyCapitalFlowVo companyCapitalFlowVo);

    int updateStatusByPayCode(TOrderPayDetailVO ov);

    int updateStatusByCode(@Param("code") String code, @Param("status") String status);

    TJdWallet selectByEnduserPartnerAccId(@Param("partnerAccId") String partnerAccId);

}