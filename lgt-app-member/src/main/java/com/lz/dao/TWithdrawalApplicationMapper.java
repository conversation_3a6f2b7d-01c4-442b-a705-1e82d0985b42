package com.lz.dao;

import java.util.List;

import com.lz.example.TWithdrawalApplicationExample;
import com.lz.model.TWithdrawalApplication;
import com.lz.vo.TCarrierCompanyOpenRoleSearchVo;
import com.lz.vo.TWithdrawalApplicationVo;
import org.apache.ibatis.annotations.Param;

public interface TWithdrawalApplicationMapper {
    long countByExample(TWithdrawalApplicationExample example);

    int deleteByExample(TWithdrawalApplicationExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TWithdrawalApplication record);

    int insertSelective(TWithdrawalApplication record);

    List<TWithdrawalApplication> selectByExample(TWithdrawalApplicationExample example);

    TWithdrawalApplication selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TWithdrawalApplication record, @Param("example") TWithdrawalApplicationExample example);

    int updateByExample(@Param("record") TWithdrawalApplication record, @Param("example") TWithdrawalApplicationExample example);

    int updateByPrimaryKeySelective(TWithdrawalApplication record);

    int updateByPrimaryKey(TWithdrawalApplication record);

    List<TWithdrawalApplicationVo> selectByPage(TCarrierCompanyOpenRoleSearchVo record);

    List<TWithdrawalApplication> selectByApprovalStatusNo(TWithdrawalApplication record);

    TWithdrawalApplication selectByBizOrderNo(@Param("bizOrderNo") String bizOrderNo);
    TWithdrawalApplication selectByCode(@Param("code") String code);
}