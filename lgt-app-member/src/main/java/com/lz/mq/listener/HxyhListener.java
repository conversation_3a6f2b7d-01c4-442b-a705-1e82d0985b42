package com.lz.mq.listener;

import cn.hutool.json.JSONUtil;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.lz.api.HxRechargeCallbackControllerAPI;
import com.lz.api.MqAPI;
import com.lz.api.TOrderPayDetailAPI;
import com.lz.api.TOrderPayInfoAPI;
import com.lz.common.config.RedisUtil;
import com.lz.common.constants.HXMqMessageTag;
import com.lz.common.constants.HXMqMessageTopic;
import com.lz.common.constants.MqMessageTag;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.JdTradeType;
import com.lz.common.exception.hx.HxCommonResponseEnum;
import com.lz.common.exception.hx.HxTradeResponseEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.MQMessage;
import com.lz.common.model.hxPayment.request.account.OpenQueryAccountInfoReq;
import com.lz.common.model.hxPayment.request.pay.CustomerBalancePayReq;
import com.lz.common.model.hxPayment.request.query.CustomerReceiptReq;
import com.lz.common.model.hxPayment.response.CustomerBalancePayRes;
import com.lz.common.util.*;
import com.lz.dao.*;
import com.lz.example.*;
import com.lz.hxpay.CloudPaymentAPI;
import com.lz.model.*;
import com.lz.schedule.model.TTask;
import com.lz.service.HxBalancePayService;
import com.lz.service.HxWalletService;
import com.lz.vo.TOrderPayDetailVO;
import commonSdk.api.CustomerTradeService;
import commonSdk.requestModel.CustomerQueryTradeRequest;
import commonSdk.responseModel.CustomerBalancePayResponse;
import commonSdk.responseModel.CustomerQueryTradeResponse;
import commonSdk.responseModel.OpenQueryAccountInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdk.model.account.AccountInfo;
import sdk.model.message.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *  @author: dingweibo
 *  @Date: 2022/11/7 13:46
 *  @Description: 华夏银行回调处理
 */
@Slf4j
@Service
public class HxyhListener implements MessageListener {

    @Value("${hxyhPartnerId}")
    private String hxyhPartnerId;
    @Value("${hxyhChannelId}")
    private String hxyhChannelId;
    @Resource
    private TMemberTaskMapper tMemberTaskMapper;
    @Autowired
    private MqAPI mqAPI;
    @Resource
    private TZtAccountOpenInfoMapper tZtAccountOpenInfoMapper;
    @Resource
    private TZtWalletMapper tZtWalletMapper;
    @Resource
    private TZtBankUserMapper tZtBankUserMapper;
    @Resource
    private TZtBankCardMapper tZtBankCardMapper;
    @Resource
    private TZtBankBindRelationshipMapper tZtBankBindRelationshipMapper;
    @Resource
    private TEnduserAccountMapper tEnduserAccountMapper;
    @Autowired
    private HxWalletService hxWalletService;
    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private CloudPaymentAPI cloudPaymentAPI;

    @Autowired
    private HxRechargeCallbackControllerAPI hxRechargeCallbackControllerAPI;

    @Autowired
    private TOrderPayInfoAPI tOrderPayInfoAPI;
    @Autowired
    private TOrderPayDetailAPI tOrderPayDetailAPI;
    @Resource
    private TWithdrawalApplicationMapper tWithdrawalApplicationMapper;
    @Autowired
    private RedisUtil redisUtil;
    @Resource
    private TZtWalletChangeLogMapper tZtWalletChangeLogMapper;

    @Resource
    private TMemberOrderPayDetailMapper orderPayDetailMapper;

    @Autowired
    private HxBalancePayService hxBalancePayService;


    @Override
    @Transactional
    public Action consume(Message message, ConsumeContext consumeContext){
        String tag  = message.getTag();
        try{
            log.info("回调通知消息：{}",JSONUtil.toJsonStr(new String(message.getBody())));
            //开户通知
            if(tag.equals(MqMessageTag.HXYHOPENCALLBACK)){
                AsyncNotifyOpenBankAccountMessageBody messageBody = JSONUtil.toBean(new String(message.getBody()),AsyncNotifyOpenBankAccountMessageBody.class);
                log.info("开户通知消息参数：{}",JSONUtil.toJsonStr( messageBody ) );
                String lockKey = messageBody.getPartnerAccId();
                RLock lock = redissonClient.getLock(lockKey);
                boolean tryLock = lock.tryLock();
                if (tryLock) {
                    try {
                        TZtAccountOpenInfoExample tZtAccountOpenInfoExample = new TZtAccountOpenInfoExample();
                        TZtAccountOpenInfoExample.Criteria cr = tZtAccountOpenInfoExample.createCriteria();
                        cr.andPartnerAccIdEqualTo(messageBody.getPartnerAccId());
                        TZtAccountOpenInfo tZtAccountOpenInfo = tZtAccountOpenInfoMapper.selectByExample(tZtAccountOpenInfoExample).get(0);
                        if("SUCCESS".equals(messageBody.getStatus())){
                            tZtAccountOpenInfo.setSubAcc(messageBody.getSubAcc());
                            //创建钱包
                            TZtWallet tZtWallet = new TZtWallet();
                            tZtWallet.setAccountId(tZtAccountOpenInfo.getAccountId());
                            tZtWallet.setZtAccountOpenId(tZtAccountOpenInfo.getId());
                            tZtWallet.setDataSource(tZtAccountOpenInfo.getUserOpenRole());
                            if(DictEnum.PF.code.equals(tZtWallet.getDataSource())){
                                tZtWallet.setPurseCategory(DictEnum.PFPLATFORM.code);
                            }else if(DictEnum.CA.code.equals(tZtWallet.getDataSource())){
                                tZtWallet.setPurseCategory(DictEnum.PCARRIER.code);
                            }else if(DictEnum.BD.code.equals(tZtWallet.getDataSource())){
                                tZtWallet.setPurseCategory(DictEnum.BCOMPANY.code);
                            }else if(DictEnum.CD.code.equals(tZtWallet.getDataSource())){
                                List<TEndUserInfo> tEndUserInfoList = tEnduserAccountMapper.selectEnduserAccountUserRole(tZtAccountOpenInfo.getAccountId());
                                for(TEndUserInfo tEndUserInfo: tEndUserInfoList){
                                    if(tEndUserInfo.getUserLogisticsRole().contains(DictEnum.CTYPEDRVIVER.code)){
                                        tZtWallet.setPurseCategory(DictEnum.CDRIVER.code);
                                    }
                                    if(DictEnum.CTYPECAPTAIN.code.equals(tEndUserInfo.getUserLogisticsRole())){
                                        tZtWallet.setPurseCategory(DictEnum.CDRIVER.code);
                                    }
                                    if(DictEnum.CTYPEAGENTPERSON.code.equals(tEndUserInfo.getUserLogisticsRole())){
                                        tZtWallet.setPurseCategory(DictEnum.CMANAGER.code);
                                    }
                                }
                            }
                            TZtWalletExample tZtWalletExample = new TZtWalletExample();
                            TZtWalletExample.Criteria cr2 = tZtWalletExample.createCriteria();
                            cr2.andAccountIdEqualTo(tZtWallet.getAccountId());
                            cr2.andZtAccountOpenIdEqualTo(tZtWallet.getZtAccountOpenId());
                            cr2.andDataSourceEqualTo(tZtWallet.getDataSource());
                            List<TZtWallet> tZtWalletList = tZtWalletMapper.selectByExample(tZtWalletExample);
                            if(tZtWalletList.size()<1){
                                tZtWalletMapper.insertSelective(tZtWallet);
                            }
                            log.info("创建钱包状态结束" );
                            tZtAccountOpenInfo.setStatus(1);
                            tZtAccountOpenInfo.setResponseCode(messageBody.getStatus());
                            tZtAccountOpenInfo.setResponseMessage("开户成功");
                        }else{
                            tZtAccountOpenInfo.setResponseCode(messageBody.getStatus());
                            tZtAccountOpenInfo.setResponseMessage(messageBody.getFailReason());
                            tZtAccountOpenInfo.setStatus(2);
                            if (null != tZtAccountOpenInfo.getIfOneselfOpen() && tZtAccountOpenInfo.getIfOneselfOpen()) {
                                try {
                                    // 查询钱包是否存在，用于区分开户还是变更信息
                                    TZtWallet tZtWallet = tZtWalletMapper.selectByPartnerAccId(tZtAccountOpenInfo.getPartnerAccId());
                                    if (null != tZtWallet) {
                                        // 变更信息失败，恢复开户信息
                                        OpenQueryAccountInfoReq openQueryAccountInfoReq = new OpenQueryAccountInfoReq();
                                        openQueryAccountInfoReq.setPartnerAccId(messageBody.getPartnerAccId());
                                        openQueryAccountInfoReq.setChannelId(hxyhChannelId);
                                        openQueryAccountInfoReq.setPartnerId(hxyhPartnerId);
                                        openQueryAccountInfoReq.setRequestId(IdWorkerUtil.getInstance().nextId());
                                        openQueryAccountInfoReq.setRequestTime(DateUtils.getRequestTime());
                                        ResultUtil execute = cloudPaymentAPI.execute(openQueryAccountInfoReq);
                                        OpenQueryAccountInfoResponse openQueryAccountInfoResponse = BeanTransUtil.ObjToBean(execute, OpenQueryAccountInfoResponse.class);
                                        if (null != openQueryAccountInfoResponse.getCount() && openQueryAccountInfoResponse.getCount() > 0) {
                                            if (null != openQueryAccountInfoResponse.getList() && openQueryAccountInfoResponse.getList().size() > 0) {
                                                AccountInfo accountInfo = openQueryAccountInfoResponse.getList().get(0);
                                                if (null != accountInfo.getPhone()) {
                                                    tZtAccountOpenInfo.setOpenPhone(accountInfo.getPhone());
                                                }
                                                if (null != accountInfo.getTradeMemBerName()) {
                                                    tZtAccountOpenInfo.setOpenRealName(accountInfo.getTradeMemBerName());
                                                }
                                                if (null != accountInfo.getPapersCode()) {
                                                    tZtAccountOpenInfo.setOpenIdCard(accountInfo.getPapersCode());
                                                }
                                            }
                                        }
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    log.error("非本人开户，查询开户信息失败, {}", ThrowableUtil.getStackTrace(e));
                                }
                            }
                        }
                        tZtAccountOpenInfoMapper.updateByPrimaryKeySelective(tZtAccountOpenInfo);
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("开户通知消息异常：{}", ThrowableUtil.getStackTrace(e));
                    } finally {
                        if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                            lock.unlock();
                        }
                    }
                    log.info("开户通知消息处理结束" );
                } else {
                    log.error("开户通知消息锁定失败, 重新放入消息队列" );
                    MQMessage mqMessage = new MQMessage();
                    mqMessage.setTopic(message.getTopic());
                    mqMessage.setTag(message.getTag());
                    mqMessage.setBody(messageBody);
                    mqAPI.sendMessage(mqMessage);
                    return Action.CommitMessage;
                }

            }else if(tag.equals(MqMessageTag.BINDUNBINDCALLBACK)){//绑卡通知或解绑通知
                AsyncNotifyBindBankAccountMessageBody messageBody = JSONUtil.toBean(new String(message.getBody()),AsyncNotifyBindBankAccountMessageBody.class);
                log.info("绑卡通知消息参数：{}",JSONUtil.toJsonStr( messageBody ) );
                TZtAccountOpenInfoExample tZtAccountOpenInfoExample = new TZtAccountOpenInfoExample();
                TZtAccountOpenInfoExample.Criteria cr = tZtAccountOpenInfoExample.createCriteria();
                cr.andPartnerAccIdEqualTo(messageBody.getPartnerAccId());
                TZtAccountOpenInfo tZtAccountOpenInfo = tZtAccountOpenInfoMapper.selectByExample(tZtAccountOpenInfoExample).get(0);
                try{
                    if("BIND".equals(messageBody.getType())){
                        TZtBankCardExample tZtBankCardExample = new TZtBankCardExample();
                        TZtBankCardExample.Criteria cr3 = tZtBankCardExample.createCriteria();
                        cr3.andAcctNoEqualTo(messageBody.getAcctNo());
                        cr3.andEnableEqualTo(false);
                        TZtBankCard tZtBankCard = tZtBankCardMapper.selectByExample(tZtBankCardExample).get(0);

                        TZtBankUserExample tZtBankUserExample = new TZtBankUserExample();
                        TZtBankUserExample.Criteria cr4 = tZtBankUserExample.createCriteria();
                        cr4.andAccountIdEqualTo(tZtAccountOpenInfo.getAccountId());
                        cr4.andBankIdEqualTo(tZtBankCard.getId());
                        cr4.andEnableEqualTo(false);

                        TZtBankUser tZtBankUser = tZtBankUserMapper.selectByExample(tZtBankUserExample).get(0);
                        TZtBankBindRelationshipExample tZtBankBindRelationshipExample = new TZtBankBindRelationshipExample();
                        TZtBankBindRelationshipExample.Criteria cr5 = tZtBankBindRelationshipExample.createCriteria();
                        cr5.andAccountBankIdEqualTo(tZtBankUser.getId());
                        TZtBankBindRelationship tZtBankBindRelationship = tZtBankBindRelationshipMapper.selectByExample(tZtBankBindRelationshipExample).get(0);
                        tZtBankBindRelationship.setBankNo(messageBody.getCardId());
                        if("BIND".equals(messageBody.getBindStatus())){
                            tZtBankBindRelationship.setBindStatus(messageBody.getBindStatus());
                            tZtBankBindRelationship.setRequestCode("1");
                            tZtBankBindRelationship.setRequestMessage("绑卡成功");
                            tZtBankBindRelationship.setBindMessage("绑卡成功");
                        }else if("BINDING".equals(messageBody.getBindStatus())){
                            tZtBankBindRelationship.setBindStatus(messageBody.getBindStatus());
                            tZtBankBindRelationship.setRequestCode("0");
                            tZtBankBindRelationship.setRequestMessage("绑卡处理中");
                            tZtBankBindRelationship.setBindMessage("绑卡处理中");
                        }else{
                            tZtBankBindRelationship.setBindStatus(messageBody.getBindStatus());
                            tZtBankBindRelationship.setRequestCode("2");
                            tZtBankBindRelationship.setRequestMessage(messageBody.getFailReason());
                            tZtBankBindRelationship.setBindMessage(messageBody.getFailReason());
                       /* tZtBankCard.setEnable(true);
                        tZtBankCardMapper.updateByPrimaryKeySelective(tZtBankCard);*/
                            tZtBankUser.setEnable(true);
                            tZtBankUserMapper.updateByPrimaryKeySelective(tZtBankUser);
                        }
                        tZtBankBindRelationshipMapper.updateByPrimaryKeySelective(tZtBankBindRelationship);
                        log.info("绑卡通知消息处理结束" );
                    }else if("UNBIND".equals(messageBody.getType())){ //解绑通知
                        TZtBankCardExample tZtBankCardExample = new TZtBankCardExample();
                        TZtBankCardExample.Criteria cr3 = tZtBankCardExample.createCriteria();
                        cr3.andAcctNoEqualTo(messageBody.getAcctNo());
                        cr3.andEnableEqualTo(false);
                        TZtBankCard tZtBankCard = tZtBankCardMapper.selectByExample(tZtBankCardExample).get(0);

                        TZtBankUserExample tZtBankUserExample = new TZtBankUserExample();
                        TZtBankUserExample.Criteria cr4 = tZtBankUserExample.createCriteria();
                        cr4.andAccountIdEqualTo(tZtAccountOpenInfo.getAccountId());
                        cr4.andBankIdEqualTo(tZtBankCard.getId());
                        tZtBankUserExample.setOrderByClause("create_time desc");
                        TZtBankUser tZtBankUser = tZtBankUserMapper.selectByExample(tZtBankUserExample).get(0);

                        TZtBankBindRelationshipExample tZtBankBindRelationshipExample = new TZtBankBindRelationshipExample();
                        TZtBankBindRelationshipExample.Criteria cr5 = tZtBankBindRelationshipExample.createCriteria();
                        cr5.andAccountBankIdEqualTo(tZtBankUser.getId());
                        TZtBankBindRelationship tZtBankBindRelationship = tZtBankBindRelationshipMapper.selectByExample(tZtBankBindRelationshipExample).get(0);
                        if("UNBIND".equals(messageBody.getBindStatus())){
                            tZtBankBindRelationship.setBindStatus(messageBody.getBindStatus());
                            tZtBankBindRelationship.setRequestCode("1");
                            tZtBankBindRelationship.setRequestMessage("解绑成功");
                            tZtBankBindRelationship.setBindMessage("解绑成功");
                            tZtBankBindRelationshipMapper.updateByPrimaryKeySelective(tZtBankBindRelationship);
                        }else if("UNBINDING".equals(messageBody.getBindStatus())){
                            tZtBankBindRelationship.setBindStatus(messageBody.getBindStatus());
                            tZtBankBindRelationship.setRequestCode("0");
                            tZtBankBindRelationship.setRequestMessage("解绑处理中");
                            tZtBankBindRelationship.setBindMessage("解绑处理中");
                            tZtBankBindRelationshipMapper.updateByPrimaryKeySelective(tZtBankBindRelationship);
                        }else{
                        /*tZtBankCard.setEnable(false);
                        tZtBankCardMapper.updateByPrimaryKeySelective(tZtBankCard);*/
                            tZtBankUser.setEnable(false);
                            tZtBankUserMapper.updateByPrimaryKeySelective(tZtBankUser);
                            tZtBankBindRelationship.setBindMessage("解绑失败:"+messageBody.getFailReason());
                            tZtBankBindRelationship.setRequestMessage("解绑失败:"+messageBody.getFailReason());
                            tZtBankBindRelationshipMapper.updateByPrimaryKeySelective(tZtBankBindRelationship);
                        }
                        log.info("解绑通知消息处理结束" );
                    }
                }catch (Exception e){
                    log.error("华夏绑卡或解绑失败：",e);
                    MQMessage mqMessage = new MQMessage();
                    mqMessage.setTopic(message.getTopic());
                    mqMessage.setTag(message.getTag());
                    mqMessage.setBody(messageBody);
                    mqAPI.sendMessage(mqMessage);
                    return Action.CommitMessage;
                }

            } else if (tag.equals(MqMessageTag.TRANSFERDEPOSITCALLBACK)) {
                //转账入金
                AsyncNotifyVirtualMemberRechargeMessageBody messageBody = JSONUtil.toBean(new String(message.getBody()), AsyncNotifyVirtualMemberRechargeMessageBody.class);
                log.info("华夏转账入金通知消息参数：{}", JSONUtil.toJsonStr(messageBody));
                String walletKey = messageBody.getPartnerAccId();
                RLock lock = redissonClient.getLock(walletKey);
                try {
                    boolean in = lock.tryLock();
                    if (in) {
                        // 处理华夏充值回调
                        hxWalletService.rechargeCallback(messageBody);
                        log.info("华夏修改钱包表转账入金结束");
                    } else {
                        log.error("华夏转账入金处理修改钱包获取锁失败");
                        MQMessage mqMessage = new MQMessage();
                        mqMessage.setTopic(message.getTopic());
                        mqMessage.setTag(message.getTag());
                        mqMessage.setBody(messageBody);
                        mqAPI.sendMessage(mqMessage);
                        return Action.CommitMessage;
                    }
                } catch (Exception e) {
                    log.error("华夏转账入金处理修改钱包获取锁失败：", e);
                    throw e;
                } finally {
                    if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }

            } else if (HXMqMessageTag.HX_CARRIERBALANCEBDCHARGE.equals(tag)) {
                String body = new String(message.getBody());
                log.info("华夏支付 - 承运方发起余额支付请求(企业充值), {}", body);
                CustomerBalancePayReq customerBalancePayReq = JSONUtil.toBean(body, CustomerBalancePayReq.class);
                ResultUtil resultUtil = cloudPaymentAPI.execute(customerBalancePayReq);
                log.info("华夏支付 - 承运方发起余额支付请求(企业充值)结果，{}", JSONUtil.toJsonStr(resultUtil));
                CustomerBalancePayResponse response = BeanTransUtil.ObjToBean(resultUtil, CustomerBalancePayResponse.class);;
                if (DictEnum.ACCEPT_SUCC.code.equals(response.getOrderStatus())
                        || DictEnum.PAY_SUCC.code.equals(response.getOrderStatus())
                        || DictEnum.PAY_ING.code.equals(response.getOrderStatus())) {
                    return Action.CommitMessage;
                } else {
                    Boolean result = true;
                    // 通用异常，查询交易状态
                    if (HxCommonResponseEnum.hasCode(response.getResponseCode())) {
                        CustomerQueryTradeRequest tradeRequest = new CustomerQueryTradeRequest();
                        tradeRequest.setRequestId(IdWorkerUtil.getInstance().nextId());
                        tradeRequest.setRequestTime(DateUtils.getRequestTime());
                        tradeRequest.setPartnerId(customerBalancePayReq.getPartnerId());
                        tradeRequest.setTradeType(DictEnum.SALE.code);
                        CustomerTradeService service = new CustomerTradeService();
                        CustomerQueryTradeResponse tradeResponse = service.queryTrade(tradeRequest);
                        if (null == tradeResponse.getResponseCode()
                                || StringUtils.isBlank(tradeResponse.getResponseCode())
                                || HxTradeResponseEnum.hasCode(tradeResponse.getResponseCode())) {
                            result = false;
                        }
                    }
                    if (null == response.getResponseCode()
                            || StringUtils.isBlank(response.getResponseCode())
                            || HxTradeResponseEnum.hasCode(response.getResponseCode())
                            || !DictEnum.REQUEST_SUCCESS.code.equals(response.getResponseCode())
                            || !result) {
                        // 修改支付子表
                        CustomerBalancePayRes res = new CustomerBalancePayRes();
                        BeanUtils.copyProperties(response, res);
                        res.setBizOrderNo(customerBalancePayReq.getBizOrderNo());
                        res.setOrderAmount(customerBalancePayReq.getOrderAmount());
                        res.setOutPartnerAccId(customerBalancePayReq.getOutPartnerAccId());
                        hxRechargeCallbackControllerAPI.payFail(res);
                    }

                    throw new RuntimeException(JSONUtil.toJsonStr(resultUtil));
                }

            } else if (HXMqMessageTag.HX_CARRIERBALANCEBDCHARGECALLBACK.equals(tag)) {
                String body = new String(message.getBody());
                log.info("华夏支付 - 承运方发起余额支付请求(企业充值)回调, {}", body);
                AsyncNotifyVirtualBalancePayMessageBody balancePayMessageBody = JSONUtil.toBean(body, AsyncNotifyVirtualBalancePayMessageBody.class);
                String outPartnerAccId = balancePayMessageBody.getOutPartnerAccId();
                String inPartnerAccId = balancePayMessageBody.getInPartnerAccId();
                RLock outLock = redissonClient.getLock(outPartnerAccId);
                RLock inLock = redissonClient.getLock(inPartnerAccId);
                log.info("分布式锁:lock:" + outLock.toString() + ",interrupted:" +
                        Thread.currentThread().isInterrupted() + ",hold:" +
                        outLock.isHeldByCurrentThread() + ",threadId:" +
                        Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                log.info("分布式锁:lock:" + inLock.toString() + ",interrupted:" +
                        Thread.currentThread().isInterrupted() + ",hold:" +
                        inLock.isHeldByCurrentThread() + ",threadId:" +
                        Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                try {
                    boolean out = outLock.tryLock();
                    boolean in = inLock.tryLock();
                    log.info("分布式锁:lock:" + outLock.toString() + ",interrupted:" +
                            Thread.currentThread().isInterrupted() + ",hold:" +
                            outLock.isHeldByCurrentThread() + ",threadId:" +
                            Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                    log.info("分布式锁:lock:" + inLock.toString() + ",interrupted:" +
                            Thread.currentThread().isInterrupted() + ",hold:" +
                            inLock.isHeldByCurrentThread() + ",threadId:" +
                            Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                    if (out && in) {
                        ResultUtil resultUtil = hxRechargeCallbackControllerAPI.carrierBalanceCompanyChargeCallback(balancePayMessageBody);
                        if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
                            throw new RuntimeException(resultUtil.getMsg());
                        }
                    } else {
                        log.info("华夏支付 - 承运方发起余额支付请求(企业充值)回调, 未获取锁，放回消息队列");
                        MQMessage mqMessage = new MQMessage();
                        mqMessage.setTopic(message.getTopic());
                        mqMessage.setTag(message.getTag());
                        mqMessage.setBody(balancePayMessageBody);
                        mqAPI.sendMessage(mqMessage);
                    }
                } catch (Exception e) {
                    log.error("华夏支付 - 承运方发起余额支付请求(企业充值)回调, error, {}", ThrowableUtil.getStackTrace(e));
                    MQMessage mqMessage = new MQMessage();
                    mqMessage.setTopic(message.getTopic());
                    mqMessage.setTag(message.getTag());
                    mqMessage.setBody(balancePayMessageBody);
                    mqAPI.sendMessage(mqMessage);
                } finally {
                    if (outLock.isLocked() && outLock.isHeldByCurrentThread()) {
                        log.info("分布式锁:lock:" + outLock.toString() + ",interrupted:" +
                                Thread.currentThread().isInterrupted() + ",hold:" +
                                outLock.isHeldByCurrentThread() + ",threadId:" +
                                Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                        outLock.unlock();
                    }
                    if (inLock.isLocked() && inLock.isHeldByCurrentThread()) {
                        log.info("分布式锁:lock:" + inLock.toString() + ",interrupted:" +
                                Thread.currentThread().isInterrupted() + ",hold:" +
                                inLock.isHeldByCurrentThread() + ",threadId:" +
                                Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                        inLock.unlock();
                    }
                }
                return Action.CommitMessage;
            }else if(tag.equals(HXMqMessageTag.HX_WITHDRAWCALLBACK)){ //提现回调
                AsyncNotifyVirtualMemberWithdrawMessageBody messageBody = JSONUtil.toBean(new String(message.getBody()),AsyncNotifyVirtualMemberWithdrawMessageBody.class);
                log.info("提现通知消息参数：{}",JSONUtil.toJsonStr(messageBody) );
                TOrderPayInfo tOrderPayInfo = tOrderPayInfoAPI.selectByBizOrderNo(messageBody.getBizOrderNo());
                log.info("支付主表数据：{}",JSONUtil.toJsonStr(tOrderPayInfo) );
                TOrderPayDetail tOrderPayDetail = tOrderPayDetailAPI.selectByCode(messageBody.getBizOrderNo());
                log.info("支付子表数据：{}",JSONUtil.toJsonStr(tOrderPayDetail) );
                TWithdrawalApplication tWithdrawalApplication = tWithdrawalApplicationMapper.selectByCode(messageBody.getBizOrderNo());
                log.info("提现申请表数据：{}",JSONUtil.toJsonStr(tWithdrawalApplication));
                if(null == tOrderPayDetail.getTradeStatus() || DictEnum.TRADE_FAILED.code.equals(tOrderPayDetail.getTradeStatus())){
                    TZtWallet tZtWallet =  tZtWalletMapper.selectByPrimaryKey(tWithdrawalApplication.getWalletId());
                    log.info("华夏钱包表数据：{}",JSONUtil.toJsonStr( tZtWallet ) );
                    String partnerAccId = messageBody.getPartnerAccId();
                    RLock lock = redissonClient.getLock(partnerAccId);
                    Boolean walletLock = false;
                    try{
                        walletLock = lock.tryLock();
                        if (walletLock) {
                            //提现成功
                            if("PAY_SUCC".equals(messageBody.getOrderStatus())){
                                //修改支付子表
                                TOrderPayDetailVO ov = new TOrderPayDetailVO();
                                ov.setCode(messageBody.getBizOrderNo());
                                ov.setErrorCode(messageBody.getResponseCode());
                                ov.setErrorMsg(messageBody.getResponseDesc());
                                ov.setInnerTradeNo(messageBody.getBizOrderNo());
                                ov.setTradeStatus(DictEnum.TRADE_FINISHED.code);
                                ov.setReturnTime(new Date());
                                tZtWalletMapper.updateStatusByPayCode(ov);
                                //修改钱包
                                BigDecimal withdrawAmount=tZtWallet.getWithdrawAmount().subtract(tOrderPayInfo.getOrderTotalPayment());
                                tZtWallet.setWithdrawAmount(withdrawAmount);
                                tZtWalletMapper.updateByPrimaryKey(tZtWallet);

                                //修改钱包金额并记录钱包记录表
                                String memo = "";
                                if (DictEnum.PTIXIAN.code.equals(tOrderPayDetail.getTradeType())) {
                                    memo = "华夏(承运方)提现";
                                }
                                if (DictEnum.PFTIXIAN.code.equals(tOrderPayDetail.getTradeType())) {
                                    memo = "华夏(平台)提现";
                                }
                                if (DictEnum.BTIXIAN.code.equals(tOrderPayDetail.getTradeType())) {
                                    memo = "华夏(企业)提现";
                                }
                                if (DictEnum.MANAGER.code.equals(tOrderPayDetail.getTradeType())){
                                    memo="华夏(经纪人)提现";
                                }
                                //钱包变动记录
                                TZtWalletChangeLog walletChangeLog = new TZtWalletChangeLog();
                                walletChangeLog.setWalletId(tZtWallet.getId());
                                walletChangeLog.setWalletType(tZtWallet.getPurseCategory());
                                walletChangeLog.setTradeType(tOrderPayDetail.getTradeType());
                                walletChangeLog.setAmount(tOrderPayInfo.getOrderTotalPayment());
                                walletChangeLog.setTradeTime(tOrderPayDetail.getCreateTime());
                                walletChangeLog.setTradeNo(messageBody.getBizOrderNo());
                                walletChangeLog.setCardHolder(tWithdrawalApplication.getAccountName());
                                walletChangeLog.setCardNo(tWithdrawalApplication.getBankAccountNo());
                                walletChangeLog.setBankCardId(tWithdrawalApplication.getBankId());
                                walletChangeLog.setOuterTradeNo(messageBody.getBankOrderNo());
                                walletChangeLog.setGmtClose(new Date());
                                walletChangeLog.setRemark(memo);
                                walletChangeLog.setEnable(false);
                                tZtWalletChangeLogMapper.insertSelective(walletChangeLog);
                                //电子回单添加到任务中
                                saveReceiptApplyTask(messageBody.getPartnerAccId(),messageBody.getBizOrderNo(),"WIDR",walletChangeLog.getId());
                                //修改支付主表状态
                                tZtWalletMapper.updateStatusByCode(tOrderPayInfo.getCode(),DictEnum.M130.code);
                                tWithdrawalApplication.setWithdrawalStatus(DictEnum.PACKWITHDRAW.code);
                                tWithdrawalApplicationMapper.updateByPrimaryKeySelective(tWithdrawalApplication);

                            }else{
                                //修改支付子表
                                TOrderPayDetailVO ov = new TOrderPayDetailVO();
                                ov.setCode(messageBody.getBizOrderNo());
                                ov.setErrorCode(messageBody.getResponseCode());
                                ov.setErrorMsg(messageBody.getResponseDesc());
                                ov.setInnerTradeNo(messageBody.getBizOrderNo());
                                ov.setTradeStatus(DictEnum.TRADE_FAILED.code);
                                ov.setReturnTime(new Date());
                                tZtWalletMapper.updateStatusByPayCode(ov);
                                //提现失败
                                BigDecimal accountBalance = tZtWallet.getAccountBalance().add(tOrderPayInfo.getOrderTotalPayment());
                                BigDecimal withdrawAmount = tZtWallet.getWithdrawAmount().subtract(tOrderPayInfo.getOrderTotalPayment());
                                tZtWallet.setAccountBalance(accountBalance);
                                tZtWallet.setWithdrawAmount(withdrawAmount);
                                //修改钱包
                                tZtWalletMapper.updateByPrimaryKey(tZtWallet);
                                //修改支付主表状态
                                tZtWalletMapper.updateStatusByCode(tOrderPayInfo.getCode(),DictEnum.M120.code);
                                tWithdrawalApplication.setWithdrawalStatus(DictEnum.PACKEWITHDRAWERROR.code);
                                tWithdrawalApplication.setWithdrawalInfo(messageBody.getResponseDesc());
                                tWithdrawalApplicationMapper.updateByPrimaryKeySelective(tWithdrawalApplication);
                            }
                            log.info("华夏提现处理修改钱包结束" );
                        }else {
                            log.error("华夏提现处理修改钱包获取锁失败");
                            MQMessage mqMessage = new MQMessage();
                            mqMessage.setTopic(message.getTopic());
                            mqMessage.setTag(message.getTag());
                            mqMessage.setBody(messageBody);
                            mqAPI.sendMessage(mqMessage);
                            return Action.CommitMessage;
                        }
                    }catch (Exception e) {
                        log.error("提现回调失败, {}", e);
                        mqAPI.saveMessage(message);
                    }finally {
                        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                            log.info("分布式锁:finally:" + lock.toString() + ",interrupted:" +
                                    Thread.currentThread().isInterrupted() + ",hold:" +
                                    lock.isHeldByCurrentThread() + ",threadId:" +
                                    Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                            lock.unlock();
                        }
                    }
                }
            }else if(tag.equals(HXMqMessageTag.HX_BALANCEPAYCALLBACK)){ //小工具余额支付回调
                AsyncNotifyVirtualBalancePayMessageBody messageBody = JSONUtil.toBean(new String(message.getBody()),AsyncNotifyVirtualBalancePayMessageBody.class);
                log.info("小工具余额支付回调通知消息参数：{}",JSONUtil.toJsonStr(messageBody) );

                String outPartnerAccId = messageBody.getOutPartnerAccId();
                String inPartnerAccId = messageBody.getInPartnerAccId();
                RLock outLock = redissonClient.getLock(outPartnerAccId);
                RLock inLock = redissonClient.getLock(inPartnerAccId);
                try {
                    boolean outTry = outLock.tryLock();
                    boolean inTry = inLock.tryLock();
                    if (outTry && inTry) {
                        // 查询支付子表
                        TOrderPayDetail tOrderPayDetail = orderPayDetailMapper.selectByCode(messageBody.getBizOrderNo());
                        if (null == tOrderPayDetail) {
                            // 新版小工具余额支付
                            hxBalancePayService.balancePayToolCallback(messageBody);
                        } else {
                            if (null == tOrderPayDetail.getTradeStatus() || StringUtils.isBlank(tOrderPayDetail.getTradeStatus())) {
                                // 旧版小工具余额支付
                                oldBalancePayTool(message);
                            } else {
                                log.info("华夏支付小工具余额支付回调, 支付子表信息, {}", JSONUtil.toJsonStr(tOrderPayDetail));
                                log.error("华夏支付小工具余额支付回调已处理, 不再处理");
                                return Action.CommitMessage;
                            }
                        }
                    } else {
                        log.error("华夏支付小工具余额支付处理修改钱包获取锁失败");
                        MQMessage mqMessage = new MQMessage();
                        mqMessage.setTopic(HXMqMessageTopic.HXYHOPENKIND);
                        mqMessage.setTag(HXMqMessageTag.HX_BALANCEPAYCALLBACK);
                        mqMessage.setBody(messageBody);
                        mqAPI.sendMessage(mqMessage);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("小工具余额支付回调通知消息异常：{}", ThrowableUtil.getStackTrace(e));
                } finally {
                    if (outLock.isLocked() && outLock.isHeldByCurrentThread()) {
                        outLock.unlock();
                    }
                    if (inLock.isLocked() && inLock.isHeldByCurrentThread()) {
                        inLock.unlock();
                    }
                }
                return Action.CommitMessage;
            }
        }catch (Exception e){
            log.error("回调通知消息异常：{}",JSONUtil.toJsonStr(new String(message.getBody())));
            log.error("回调通知消息异常",e);
            mqAPI.saveMessage(message);
        }
        return Action.CommitMessage;
    }

    private void oldBalancePayTool(Message message) {
        AsyncNotifyVirtualBalancePayMessageBody messageBody = JSONUtil.toBean(new String(message.getBody()),AsyncNotifyVirtualBalancePayMessageBody.class);
        log.info("小工具余额支付回调通知消息参数：{}",JSONUtil.toJsonStr(messageBody) );

        TOrderPayInfo tOrderPayInfo = tOrderPayInfoAPI.selectByBizOrderNo(messageBody.getBizOrderNo());
        log.info("支付主表数据：{}",JSONUtil.toJsonStr(tOrderPayInfo) );
        TOrderPayDetail tOrderPayDetail = tOrderPayDetailAPI.selectByCode(messageBody.getBizOrderNo());
        log.info("支付子表数据：{}",JSONUtil.toJsonStr(tOrderPayDetail) );
        if(null == tOrderPayDetail.getTradeStatus() || "".equals(tOrderPayDetail.getTradeStatus())){
            TZtWallet outWallet = tZtWalletMapper.selectByPartnerAccId(messageBody.getOutPartnerAccId());
            TZtWallet inWallet = tZtWalletMapper.selectByPartnerAccId(messageBody.getInPartnerAccId());
            //修改支付子表
            TOrderPayDetailVO ov = new TOrderPayDetailVO();
            ov.setCode(messageBody.getBizOrderNo());
            ov.setErrorCode(messageBody.getResponseCode());
            ov.setErrorMsg(messageBody.getResponseDesc());
            ov.setInnerTradeNo(messageBody.getBizOrderNo());
            ov.setTradeStatus(messageBody.getOrderStatus());
            ov.setReturnTime(new Date());
            tZtWalletMapper.updateStatusByPayCode(ov);
            String partnerAccId = messageBody.getOutPartnerAccId();
            RLock lock = redissonClient.getLock(partnerAccId);
            Boolean walletLock = false;
            try{
                walletLock = lock.tryLock();
                if (walletLock) {
                    if(messageBody.getOrderStatus().equals("PAY_SUCC")){
                        //修改支付主表状态
                        tZtWalletMapper.updateStatusByCode(tOrderPayInfo.getCode(),DictEnum.M090.code);
                        //修改付款方钱包
                        BigDecimal entryAmount=outWallet.getEntryAmount().subtract(tOrderPayInfo.getOrderActualPayment());
                        outWallet.setEntryAmount(entryAmount);
                        tZtWalletMapper.updateByPrimaryKey(outWallet);

                        //修改收款方钱包
                        BigDecimal ccountBalance=inWallet.getAccountBalance().add(tOrderPayInfo.getOrderActualPayment());
                        inWallet.setAccountBalance(ccountBalance);
                        tZtWalletMapper.updateByPrimaryKeySelective(inWallet);

                        //钱包变动记录
                        TZtWalletChangeLog outwalletChangeLog = new TZtWalletChangeLog();
                        outwalletChangeLog.setWalletId(outWallet.getId());
                        outwalletChangeLog.setWalletType(outWallet.getPurseCategory());
                        outwalletChangeLog.setTradeType(JdTradeType.TOOL_BALANCE_PAY_ZC.code);
                        outwalletChangeLog.setAmount(tOrderPayInfo.getOrderTotalPayment());
                        outwalletChangeLog.setTradeTime(tOrderPayDetail.getCreateTime());
                        outwalletChangeLog.setTradeNo(messageBody.getBizOrderNo());
                        outwalletChangeLog.setOuterTradeNo(messageBody.getBankOrderNo());
                        outwalletChangeLog.setGmtClose(new Date());
                        outwalletChangeLog.setTraderWalletId(inWallet.getId());
                        outwalletChangeLog.setRemark("小工具余额支付支出");
                        outwalletChangeLog.setEnable(false);
                        outwalletChangeLog.setCarryOverAmount(messageBody.getCarryOverAmount());
                        tZtWalletChangeLogMapper.insertSelective(outwalletChangeLog);

                        //电子回单添加到任务中
                        saveReceiptApplyTask(messageBody.getOutPartnerAccId(),messageBody.getBizOrderNo(),"SALE",outwalletChangeLog.getId());

                        //入款钱包变动记录
                        TZtWalletChangeLog inWalletChangeLog = new TZtWalletChangeLog();
                        inWalletChangeLog.setWalletId(inWallet.getId());
                        inWalletChangeLog.setWalletType(inWallet.getPurseCategory());
                        inWalletChangeLog.setTradeType(JdTradeType.TOOL_BALANCE_PAY_SR.code);
                        inWalletChangeLog.setAmount(tOrderPayInfo.getOrderTotalPayment());
                        inWalletChangeLog.setTradeTime(tOrderPayDetail.getCreateTime());
                        inWalletChangeLog.setTradeNo(messageBody.getBizOrderNo());
                        inWalletChangeLog.setOuterTradeNo(messageBody.getBankOrderNo());
                        inWalletChangeLog.setGmtClose(new Date());
                        inWalletChangeLog.setTraderWalletId(outWallet.getId());
                        inWalletChangeLog.setRemark("小工具余额支付收入");
                        inWalletChangeLog.setEnable(false);
                        tZtWalletChangeLogMapper.insertSelective(inWalletChangeLog);
                    }else{
                        //支付回调失败
                        BigDecimal accountBalance = outWallet.getAccountBalance().add(tOrderPayInfo.getOrderActualPayment());
                        BigDecimal entryAmount = outWallet.getEntryAmount().subtract(tOrderPayInfo.getOrderActualPayment());
                        outWallet.setAccountBalance(accountBalance);
                        outWallet.setEntryAmount(entryAmount);
                        //修改钱包
                        tZtWalletMapper.updateByPrimaryKey(outWallet);
                        //修改支付主表状态
                        tZtWalletMapper.updateStatusByCode(tOrderPayInfo.getCode(),DictEnum.M080.code);
                    }
                }else{
                    log.error("华夏支付小工具余额支付处理修改钱包获取锁失败");
                    MQMessage mqMessage = new MQMessage();
                    mqMessage.setTopic(message.getTopic());
                    mqMessage.setTag(message.getTag());
                    mqMessage.setBody(messageBody);
                    mqAPI.sendMessage(mqMessage);
                }
            }catch (Exception e) {
                log.error("小工具华夏余额支付修改钱包失败：",e);
                throw e;
            }finally {
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    log.info("分布式锁:finally:" + lock.toString() + ",interrupted:" +
                            Thread.currentThread().isInterrupted() + ",hold:" +
                            lock.isHeldByCurrentThread() + ",threadId:" +
                            Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                    lock.unlock();
                }
            }
        }
    }


    /**
     * 京东电子回单请求写入任务表
     * @param partnerAccId
     * @param bizOrderNo
     * @param tradeType
     * @param walletLogId
     */
    public void saveReceiptApplyTask(String partnerAccId, String bizOrderNo, String tradeType, Integer walletLogId) {
        CustomerReceiptReq req = new CustomerReceiptReq();
        req.setPartnerId(hxyhPartnerId);
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setChannelId(hxyhChannelId);
        req.setPartnerAccId(partnerAccId);
        req.setBizOrderNo(bizOrderNo);
        req.setTradeType(tradeType);
        TTask tTask = new TTask();
        tTask.setTaskId(IdWorkerUtil.getInstance().nextId());
        tTask.setTaskType(DictEnum.HXRECEIPT.code);
        tTask.setTaskTypeNode("FQ");
        tTask.setBusinessType("ZF");
        tTask.setSourceTablename("t_zt_wallet_log");
        tTask.setSourcekeyFieldname("id");
        tTask.setSourceFieldname("id");
        tTask.setSourceFieldvalue(bizOrderNo);
        tTask.setRequestParameter(JSONUtil.toJsonStr(req));
        // 记录钱包流水ID
        tTask.setParam1(String.valueOf(walletLogId));
        tTask.setRequestTimes(0);
        tTask.setCreateTime(new Date());
        tTask.setRequestDate(new Date());
        tTask.setIsSuccessed(false);
        tTask.setEnable(false);
        tMemberTaskMapper.insert(tTask);
    }
}
