package com.lz.service;

import cn.agile.anysign.sdk.api.param.ReqSealParam;
import com.lz.common.util.ResultUtil;
import com.lz.model.TCarrierInfo;
import com.lz.model.TEndUserInfo;
import com.lz.model.TFifthGenerationSignOpenAccount;
import com.lz.model.ht5Gq.resp.ContractCreatUrlResp;
import com.lz.vo.TCarrierInfoVo;
import com.lz.vo.TEndUserInfoVO;

public interface TFifthGenerationSignOpenAccountService {

     TFifthGenerationSignOpenAccount selectByUserIdAndType(Integer userId, String userType);

     ResultUtil registerSign(TCarrierInfo record);

     ResultUtil updateRegisterSign(TCarrierInfo record);

     ResultUtil createSeal(TCarrierInfoVo record);

     ResultUtil diverRegisterSign(TEndUserInfo record);

     ResultUtil updateDiverRegisterSign(TEndUserInfo record);

     ResultUtil diverCreateSeal (TEndUserInfoVO record);

     ContractCreatUrlResp createUrl(ReqSealParam record);

}
