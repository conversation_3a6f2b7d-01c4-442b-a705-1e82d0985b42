package com.lz.service;

import com.lz.common.util.ResultUtil;
import com.lz.dto.TAccountDTO;
import com.lz.dto.TAccountUserInfoDTO;
import com.lz.model.*;
import com.lz.system.model.TSysUser;
import com.lz.vo.*;
import com.lz.vo.certificatevo.IDCardVO;
import com.lz.vo.certificatevo.CertificationVO;
import com.lz.vo.certificatevo.DrivingLicenceVO;

import java.util.List;
import java.util.Map;

/**
 * auth dingweibo
 * 账号表
 */
public interface TAccountService {

    /**
     * 企业端获取本企业下的员工
     * Yan
     * @param account
     * @return
     */
    ResultUtil getEmployeeByCompany(TAccountVo account);

    /**
     * 根据 t_sys_user id 查询 t_account 中的 信息
     * Yan
     * @param id
     * @return
     */
    ResultUtil selectBySysUserId(Integer id);

    public ResultUtil selectByPage(TAccountVo record);

    public ResultUtil selectBusinessByPage(TAccountVo record);

    public ResultUtil selectByPageInfo(TAccountVo record);

    ResultUtil selectManagerByPageInfo(TAccountVo record);

    public TAccountVo getById(String id, String type);

    TAccountVo getPCById(String id, String type);

    public ResultUtil getAccById(TAccountVo vo);

    ResultUtil selectByPhone(String phone);

    public ResultUtil PCsave(TAccountVo record);

    ResultUtil save(TAccountVo record);

    ResultUtil saveManager(TAccountVo record);

    public ResultUtil update(TAccountVo record);

    public ResultUtil delete(List<Map<String,Object>> list);

    ResultUtil businessDelete(List<Map<String,Object>> list);

    //查询企业列表
    public ResultUtil selectByCompany(TCompanyInfoQuery record);

    //根据企业查询线路
    public ResultUtil selectByLine(String companyId);

    //查询线路员工权限
    public ResultUtil selectByLineUserRolePro(TLineUserRolePro record);

    /**
     * 根据t_sys_user ID 查询出 企业 ID
     * @auth Yan
     * @param systemId
     * @return
     */
    Integer selectAccountId(Integer systemId);

    List<TAccount> findOpenId(Integer openId);

    List<TAccount> findUser(String username);

    public ResultUtil saveAccount(TUserVo record);

    public int updateOpenId(TAccount account);

    public int updateUser(TAccount account);

    List<TAccount> findphone(String phone);

    List<TEndUserInfo> findIdCard(String idcard);

    public int updatePhone(TAccount tAccount);

    int updatePassword(Integer userid,String password);

    public TAccountVo selectByThridParyId(String thridParyId);

    ResultUtil getData(Integer accountid,Integer enduserinfoid);

    TAccount selectByPrimaryKey(Integer accountid);

    int updateByPrimaryKey(TAccount tAccount);

    ResultUtil updateAccountNo(TAccount account, TSysUser tSysUser, TEndUserInfo tEndUserInfo, TEnduserAccount tEnduserAccount, String newPhone);

    List<TBankCard> getBankCardListByCardNo(String cardNo,Integer accouontid);

    List<TZtBankCard> getTZtBankCardListByCardNo(String cardNo,Integer accouontId);

    ResultUtil updateAccount(TUserVo resources);

    int updateIfUsed(String accountNo, String code);

    List<TEnduserAccount> selectByEndUserInfoIdAndAccountId(Integer id, Integer id1);

    ResultUtil selectAccountByPhone(TAccountVo record);

    int updateOpenId(TAccountVo record);

    List<TAccountDTO> selectOpenIdByEnduserId(List<Integer> enduserIds);

    ResultUtil updateAccountForFeign(TAccount record);

    ResultUtil deleteManager(Integer[] accountIds);

    /*
     * <AUTHOR>
     * @Description 修改密码
     * @Date 2019/11/4 11:42
     * @Param
     * @return
    **/
    ResultUtil updateSystemPassword(Integer userid,String password);


    /**
     * 修改员工
     * @param record
     * @return
     */
    ResultUtil updateMember(TAccountVo record);

    ResultUtil updateMemberNew(TAccountVo record);

    /*
     * <AUTHOR>
     * @Description 新增经纪人
     * @Date 2020/2/7 10:46
     * @Param
     * @returndeleteAgent
    **/
    ResultUtil saveAgent(TAccountVo record);

    /*
     * <AUTHOR>
     * @Description 经纪人列表查询
     * @Date 2020/2/7 15:11
     * @Param
     * @return
    **/
    ResultUtil selectAgentPageInfo(TAccountVo record);

    ResultUtil deleteAgent(List<TEndUserInfoVO> endUserInfoVOS);

    ResultUtil auditAgent(TAccountVo tAccountV);

    ResultUtil updateAgent(TAccountVo record);

    ResultUtil selectCompany();

    ResultUtil queryOpenIdByEnduserId(Integer enduserId);

    TAccountUserInfoDTO selectUserInfoByUserId(Integer userId);
    int selectCountStatus(TEndUserCarRel tEndUserCarRel);

    //判断手机号是否是企业管理员
    Boolean getDataByPhone(String phone);

    //根据手机号新增收货人
    void insertConsignee(ConsigneeAccountVo consigneeAccountVo);

    void addConsignee(ConsigneeAccountVo consigneeAccountVo);

    TAccount selectAccountById(Integer accountId);

    ResultUtil insertAccount(RegisterEndUserVO record);

    ResultUtil saveIDCard(IDCardVO vo);

    ResultUtil saveDrivingLicence(DrivingLicenceVO vo);

    ResultUtil saveQualificationCard(CertificationVO vo);

    ResultUtil saveUserRole(String userLogisticsRole);

    ResultUtil getIDCardDesc(Integer endUserInfoId);

    ResultUtil getCertificationDesc(Integer endUserId);

    ResultUtil getDrivingLicenceDesc(Integer endUserId);

    ResultUtil updateDriverPhone(TAccountVo record);

    ResultUtil saveSourceRelevanceAcc(TAccountVo record);

    ResultUtil updateSourceRelevanceAcc(TAccountVo record);

    ResultUtil deleteSourceRelevanceAcc(List<Map<String,Object>> mapList);

    ResultUtil selectResourceDesc(TLineGoodsUserRelMember record);

    TLineGoodsUserRel selectUserLineButton(TLineGoodsUserRel userRel);

    ResultUtil verifyPassword(TAccountVo vo);
}
