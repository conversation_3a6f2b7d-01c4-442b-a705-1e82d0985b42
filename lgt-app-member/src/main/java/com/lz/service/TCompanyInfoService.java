package com.lz.service;

import com.lz.common.util.ResultUtil;
import com.lz.dto.CompanyOpenInfoDTO;
import com.lz.dto.CompanyOpenInfoDetailsDTO;
import com.lz.model.TCompanyAccount;
import com.lz.model.TCompanyInfo;
import com.lz.dto.CompanyInfoDTO;
import com.lz.vo.*;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019/4/15 - 19:20
 **/

public interface TCompanyInfoService {

    TCompanyInfo selectByPrimaryKey(Integer id);

    /**
     * 根据企业ID查询企业信息
     * @param companyInfoQuery
     * @return
     */
    ResultUtil selectCompanyInfoById(TCompanyInfoQuery companyInfoQuery);


    /**
     * 根据Sysuser表Id 查询所属企业
     * @param userId
     * @return
     */
    ResultUtil selectCompanyIdBySysUserId(Integer userId);

    /**
     * 根据用户id 查询所属企业
     * Yan
     * @param userId
     * @return
     */
    ResultUtil selectCompanyIdByUserId(Integer userId);

    ResultUtil selectBusinessLeader();

    ResultUtil selectByPage(TCompanyInfoQuery companyInfoQuery);

    ResultUtil selectCompanyOpenInfoList(CompanyOpenInfoListVO companyOpenInfoListVO);

    ResultUtil applyCompanyOpenInfo(CompanyOpenInfoDetailsVO companyOpenInfoDetailsVO);

    ResultUtil enterpriseExport(TCompanyInfoQuery companyInfoQuery);

    ResultUtil selectById(Integer companyId);

    ResultUtil selectByBusinessId(Integer id, String companyName);

    List<TCompanyInfo> selectByCompanyName(String companyName);

    TCompanyInfo selectByCompanyIdAndAccountId(Integer companyId);

    ResultUtil selectBusiness(Integer id);

    ResultUtil save(TCompanyInfoAddVO companyInfoAddVO) throws Exception ;

    ResultUtil update(TCompanyInfoAddVO companyInfoAddVO) throws Exception;

    ResultUtil selectCityTree();

    ResultUtil deleteById(CompanyDeleteVO deleteVO);

    ResultUtil selectCompany();


    ResultUtil selectCompanyByPage(TCompanyInfoQuery companyInfoQuery);

    ResultUtil applySubAccount(Integer companyId, Integer carrierId);
    /**
     *  @author: dingweibo
     *  @Date: 2019/6/24 15:18
     *  @Description: 企业资金总览
     */
    ResultUtil selectCompanyCapital();

    /**
     *  @author: dingweibo
     *  @Date: 2021/3/24 9:34
     *  @Description: 近7日资金流动汇总
     */
    ResultUtil selectCompanyCapitalWeek();

    /**
     * @Description 单笔项目资金明细
     * <AUTHOR>
     * @Date   2019/7/23 14:13
     * @Param
     * @Return
     * @Exception
     *
     */

    public ResultUtil selectCompanyCapitalSingleDetailed(TCompanyInfoQuery record);


    /**
     * @Description 打包项目资金明细
     * <AUTHOR>
     * @Date   2019/7/23 14:13
     * @Param
     * @Return
     * @Exception
     *
     */
    public ResultUtil selectCompanyCapitalPackDetailed(TCompanyInfoQuery record);


    /**
     *  @author: dingweibo
     *  @Date: 2020/2/19 15:31
     *  @Description: 根据企业ID  承运方 ID 查询企业名称 承运方名称
     */
    public TCompanyInfoVo selectByCarrierIdAnrCompanyId(TCompanyInfoVo record);

    ResultUtil updateCompanyUploadedInfo(TCompanyInfo record);
}
