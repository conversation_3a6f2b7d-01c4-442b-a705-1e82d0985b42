package com.lz.service;

import com.lz.common.util.ResultUtil;
import com.lz.model.TZtWalletChangeLog;
import com.lz.vo.TZtWalletChangeLogVo;

import java.util.List;

public interface TZtWalletChangeLogService {

    int updateById(TZtWalletChangeLog log);

    ResultUtil loadReceiptApply(TZtWalletChangeLogVo logVo);
    TZtWalletChangeLog selectByEndUserId(Integer endUserId, String orderBusinessCode);

    TZtWalletChangeLog selectByOrderBusinessCode(String orderBusinessCode, String tradeType);
    List<TZtWalletChangeLog> seelctByTradeTypeWalletIdOrderBusinessCode(String tradeType, Integer walletId, String orderBusinessCode);

    int updateByPrimaryKeySelective(TZtWalletChangeLog log);

    List<TZtWalletChangeLog> paymentFeesUpdateTransactionType(String tradeNo);

    int updateWalletChangeLogById(TZtWalletChangeLog log);

    TZtWalletChangeLog selectOldOrderCarCaptainByOrderBusinessCode(Integer walletId, String orderBusinessCode);

}
