package com.lz.service;


import com.lz.common.util.ResultUtil;
import com.lz.dto.BankCardDTO;
import com.lz.dto.EndUserDTO;
import com.lz.model.TBankCard;
import com.lz.vo.TBankCardVo;
import com.lz.vo.TOrderPayInfoVO;

import java.util.List;

/**
* <AUTHOR>
* @date 2019-04-10
*/
public interface TBankCardService {


    TBankCard selectBankCardByEndUserId(Integer id);

    TBankCard selectById(Integer bankCardId);

    int deleteById(Integer id);

    int updateById(Integer id);

    Object selectIdDefault(Integer accountid);

    ResultUtil selectBankCards(Integer enduserId);

    ResultUtil getUserBankCardList();

    List<BankCardDTO> selectBankCardListDetail(TBankCardVo record);

    BankCardDTO selectAllBankNoByBankId(TBankCardVo record);

    ResultUtil addAgentBankCard(TBankCardVo tBankCard);

    ResultUtil selectBankCardByNowAccount();

    ResultUtil selectCarrierByEnduserId();

    ResultUtil bankcardUntying(TBankCard tWallet);

    ResultUtil bankcardTX(TOrderPayInfoVO tOrderPayInfoVO);

    List<EndUserDTO> selectEnduserByBankId(Integer bankId);

    List<TBankCard> selectOneselfBankCardList(Integer accountId,String cardOwner,String cardOwnerIdcard);

    int selectUniqueBankCardNUms(Integer endUserId, Integer accountId);

    int updateById(TBankCard bankCard);

}