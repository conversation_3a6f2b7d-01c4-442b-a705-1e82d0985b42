package com.lz.service;

import com.lz.common.util.ResultUtil;
import com.lz.model.TZtAccountOpenInfo;

import java.util.List;

public interface TZtAccountOpenInfoService {

    TZtAccountOpenInfo selectByPrimaryKey(Integer id);

    ResultUtil ifOpenRole(Integer accountId);

    TZtAccountOpenInfo selectByCarrierIdAndCompanyIdAndEndUserIdByStatus(Integer carrierIdAndCompanyIdAndEndUserId,String userOpenRole);

    TZtAccountOpenInfo selectByAccountId(Integer accountId,String userOpenRole);

    ResultUtil selectNotByAccountId(TZtAccountOpenInfo tZtAccountOpenInfo);

    TZtAccountOpenInfo selectOrderOpenInfoByAccountId(Integer enduserId);

    List<TZtAccountOpenInfo> selectOrderOpenInfoListByAccountId(Integer enduserId);

}
