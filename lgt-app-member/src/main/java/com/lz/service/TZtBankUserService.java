package com.lz.service;

import com.lz.common.util.ResultUtil;
import com.lz.model.TZtBankCard;
import com.lz.model.TZtBankUser;
import com.lz.vo.TZtBankCardVo;

public interface TZtBankUserService {
     ResultUtil hxyhGetBankCardList(Integer accountId);

     int selectByBankNum(Integer accountId);

     TZtBankCardVo getHxyhBankCardDetail(Integer bankId, Integer accountId);

     int setHxyhDefault(Integer bankId,Integer accountId);

     ResultUtil delHxyhCard(Integer bankId,Integer accountId);

     TZtBankCard selectBankByEndUserId(Integer endUserId);

     TZtBankCard selectById(Integer bankCardId);

     ResultUtil selectBankCards(Integer endUserId);

     ResultUtil updateBankCardDefaultById(TZtBankUser tZtBankUser);

}
