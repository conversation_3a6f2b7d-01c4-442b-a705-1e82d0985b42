package com.lz.service;


import com.lz.common.model.datareport.hbdr.carreport.CarReportDTO;
import com.lz.common.util.ResultUtil;
import com.lz.dto.EndCarInfoDTO;
import com.lz.model.TBankCard;
import com.lz.model.TEndCarInfo;
import com.lz.vo.ApprovalVO;
import com.lz.vo.TEndCarInfoSearchVO;
import com.lz.vo.TEndCarInfoVO;
import com.lz.vo.certificatevo.TransportLicenseVO;
import com.lz.vo.certificatevo.TravelAndTransportLicenseVO;
import com.lz.vo.certificatevo.TravelCardVO;

import java.util.List;

/**
* <AUTHOR>
* @date 2019-04-10
*/
public interface TEndCarInfoService {

    /**
     * findById
     * @param id
     * @return
     */
    EndCarInfoDTO findById(Integer id);

    /**
     * create
     * @param resources
     * @return
     */
    int add(TEndCarInfo resources);

    /**
     * update
     * @param resources
     */
    void update(TEndCarInfo resources);

    void updateEndCarInfo(TEndCarInfoVO vo);

    /**
     * delete
     * @param id
     */
    void delete(Integer id);

    List<TEndCarInfoVO> selectByPage(TEndCarInfoSearchVO params);

    int deleteByID(Integer[] id);

    int approval(ApprovalVO param);

    public int BindingCar(TEndCarInfoVO tEndCarInfo);

    public int BindingCarUpdate(TEndCarInfoVO tEndCarInfo);

    public int perfectData(TEndCarInfoVO tEndCarInfo);

    ResultUtil getCarList(Integer enduserinfoid);

    ResultUtil getCarDetail(Integer carId,Integer enduserinfoid);

    ResultUtil selectEndCar(TEndCarInfoSearchVO record);

    /**
     * 上报监管平台
     * 上报汽车信息
     * @return
     */
    ResultUtil carBasicInformation(CarReportDTO dto);

    List<TEndCarInfo> selectByCarNum(String carNum);

    int updateByPrimaryKey(TEndCarInfo tEndCarInfo);

    TEndCarInfo selectByPrimaryKey(Integer id);

    ResultUtil getBankCardList(Integer accountid);

    List<TBankCard> getBankCardByAccountId(Integer accountid);

    TBankCard selectById(Integer id);

    Integer updateByAccountId(Integer accountid);

    Integer setIsDefault(Integer id);

    ResultUtil selectEndcarMember(TEndCarInfoSearchVO record);

    List<TEndCarInfo> selectByEndCarIdListFeign(String endCarIdList);

    /**
     * 车辆导出
     * @param param
     * <AUTHOR>
     * @return
     */
    ResultUtil exportCar(TEndCarInfoSearchVO param);

    TEndCarInfo selectByVehicleNumberFeign(String vehicleNumber);

    ResultUtil getCarAndDriverStatus(TEndCarInfoVO endCarInfoVO);

    /**
     * @Description 车辆分布图
     * <AUTHOR>
     * @Date   2019/11/18 14:38
     * @Param
     * @Return
     * @Exception
     *
     */
    ResultUtil carDistribution();

    int updateByPrimaryKeySelective(TEndCarInfo tEndCarInfo);

    List<TEndCarInfo> selectByClDmIsNull(String vln);

    ResultUtil getCarOwner(TEndCarInfoVO endCarInfoVO);

    ResultUtil checkTruckExist(TEndCarInfoVO vo);

    ResultUtil bindingCarNew(TravelAndTransportLicenseVO vo);

    ResultUtil saveTravelCard(TravelCardVO vo);

    ResultUtil saveTransportLicense(TransportLicenseVO vo);

    ResultUtil getTravelCardDesc(Integer id);

    ResultUtil getTransportLicenseDesc(Integer id);
}
