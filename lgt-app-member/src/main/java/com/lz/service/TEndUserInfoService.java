package com.lz.service;


import com.lz.common.model.datareport.hbdr.driverreport.DriverReportDTO;
import com.lz.common.util.ResultUtil;
import com.lz.dto.DriverInfoDto;
import com.lz.dto.JudgeEndUserDTO;
import com.lz.dto.TEndUserInfoDto;
import com.lz.model.*;
import com.lz.vo.*;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @date 2019-04-10
*/
public interface TEndUserInfoService {


    /**
     * @Description: 修改C端用户是否已申请合同电子签名
     * @Author: Yan
     * @Date: 2019/8/13/013 15:17
     * @Param: endUserInfo
     * @Return:
     */
    ResultUtil updateEndUserIfOnlineSign(TEndUserInfo endUserInfo);

    /**
     * @Description: Feign接口： 根据手机号判断，用户是否已申请合同电子签名
     * @Author: Yan
     * @Date: 2019/8/13/013 11:22
     * @Param: phone
     * @Return: boolean
     */
    JudgeEndUserDTO judgeEndUserIfOnlineSign(String phone,Integer endUserId);

    /**
     * APP端 会员详情-获取车辆、会员信息
     * Yan
     * @param userCar
     * @return
     */
    ResultUtil getMemberCarDetailInfo(EnduserCarStatus userCar);

    /**
     * 运单打包： 获取用户所有银行卡
     * Yan
     * @param id
     * @return
     */
    ResultUtil getUserBankCards(Integer id);

    /**
     * 判断C端用户是否有银行卡
     * true没有   false有
     * Yan
     * @param id
     * @return
     */
    ResultUtil judgeUserCard(Integer id);

    /**
     * findById
     * @param id
     * @return
     */
    TEndUserInfoVO findById(Integer id);

    /**
     * create
     * @param resources
     * @return
     */
    int add(TEndUserInfo resources);

    /**
     * update
     * @param resources
     */
    ResultUtil update(EndUserInfoUpdate resources);

    /**
     * delete
     * @param id
     */
    void delete(Integer id);

    ResultUtil selectByPage(TEndUserInfoSearchVO params);

    ResultUtil deleteByUserID(TEndUserInfoDto record);

    int insertCard(TBankCard tBankCard);

    ResultUtil addCard(PerfectCardInfoVO vo);

    ResultUtil addHxyhBankCard(TZtBankCardVo vo);

    ResultUtil addOpenRoleCard(TBankCard tBankCard);

    /**
     * C端用户
     * <AUTHOR>
     * @return
     */
    ResultUtil selectEndUser(TEndUserInfoSearchVO record);

    ResultUtil selectMamage(TEndUserInfoSearchVO record);

    /**
     * author dingweibo
     * 车老板列表查询
     * @param tEndUserInfoVO
     * @return
     */
    public ResultUtil selectEndCarOwnerByPage(TEndUserInfoVO tEndUserInfoVO);


    public TEndUserInfoVO selectEndCarOwnerById(Integer id);

    /**
     * 车老板信息修改
     * @param tEndUserInfoVO
     * @return
     */
    public ResultUtil updateEndCarOwner(TEndUserInfoVO tEndUserInfoVO);

    /**
     * 车老板信息新增
     * @param tEndUserInfoVO
     * @return
     */
    public int addEndCarOwner(TEndUserInfoVO tEndUserInfoVO);

    /**
     * 车老板银行卡新增
     * @param
     * @return
     */
    public ResultUtil addEndCarOwnerBank(TBankCardVo tBankCardVo);

    TEndUserInfo selectByPrimaryKey(Integer id);

    int updataData(TEndUserInfo resources);

    ResultUtil  findByUserId(Integer id);

    ResultUtil findByCardId(Integer id);

    int approval(ApprovalVO param);

    /**
     * 上传监管平台
     *上报司机信息
     * @return
     */
    ResultUtil driverBasicInformation(DriverReportDTO dto);

    ResultUtil selectDriverByPage(DriverListPageVO param);

    Map<String,Object> selectDriverInfo(Integer enduserId);

    ResultUtil selectEnduserForScanSource(TEndUserInfoSearchVO record);

    /**
     * @des 发单查询车辆司机状态
     * <AUTHOR>
     * @param record
     * @return
     */
    ResultUtil selectEnduserCarStatus(TEnduserCarStatus record);

    /**
     * @Description 司机扫码查询司机状态
     * <AUTHOR>
     * @Date   2019/6/13 18:00
     * @Param
     * @Return
     * @Exception
     *
     */
    ResultUtil selectEnduserStatusForScan(TEndUserInfo record);

    List<TEndUserInfo> findphone(String phone);

    List<TEndUserInfo> findIdCard(String idcard);

    /**
     *  @author: dingweibo
     *  @Date: 2019/6/24 13:44
     *  @Description: 查询车老板列表
     */
    ResultUtil findCarOwnerList();

    /**
     *  @author: dingweibo
     *  @Date: 2021/1/5 10:49
     *  @Description: 查询车队长列表
     */
    ResultUtil findCaptainList();

    /**
     * @Description 车主身份回显
     * <AUTHOR>
     * @Date   2019/7/9 14:16
     * @Param
     * @Return
     * @Exception
     *
     */
    public ResultUtil selectCarOwnerShip(TEndUserInfoVO vo);


    /**
     * @Description 司机身份回显
     * <AUTHOR>
     * @Date   2019/7/9 14:16
     * @Param
     * @Return
     * @Exception
     *
     */
    public ResultUtil selectDriverIdentity(TEndUserInfoVO vo);

    /**
     * @Description 添加车主身份
     * <AUTHOR>
     * @Date   2019/7/9 14:16
     * @Param
     * @Return
     * @Exception
     *
     */
    public ResultUtil saveCarOwnerShip(TEndUserInfoVO vo);


    /**
     * @Description 添加司机身份
     * <AUTHOR>
     * @Date   2019/7/9 14:16
     * @Param
     * @Return
     * @Exception
     *
     */
    public ResultUtil saveDriverIdentity(TEndUserInfoVO vo);

    /**
     * @Description 根据承运方id和enduserid查询钱包id
     * <AUTHOR>
     * @Date   2019/7/10 17:28
     * @Param
     * @Return
     * @Exception
     *
     */
    ResultUtil selectByWalltId(Integer endUserId,Integer carrierId, String purseCategory);

    /**
     * @Description 根据enduserId获取到默认银行卡
     * <AUTHOR>
     * @Date   2019/7/11 10:36
     * @Param
     * @Return
     * @Exception
     *
     */
    public ResultUtil selectByEndUserIdAndBankInfo(Integer endUserId);

    TEndUserInfo selectById(Integer id);

    List<TEndUserInfo> selectByEndUserIdListFeign(String endUserIdList);

    ResultUtil carOwnerAudit(TEndUserCarRelVo record);

    ResultUtil updateEnduserInfo(TEndUserInfoVO record);

    List<String> judgeEndUserByPhone(String phone);

    /**
     * 司机管理excel导出
     * @param params
     * @return
     */
    ResultUtil export(TEndUserInfoSearchVO params);

    /*
     * <AUTHOR>
     * @Description 查询车辆司机审核状态
     * @Date 2019/11/14 16:36
     * @Param
     * @return
    **/
    ResultUtil selectUserAndCarAuditStatus(TEndUserInfoSearchVO record);

    /*
     * <AUTHOR>
     * @Description 查询经纪人
     * @Date 2020/2/8 5:31 下午 
     * @Param 
     * @return 5:31 下午
    **/

    ResultUtil selectAgent(TEndUserInfoSearchVO record);

    TLineGoodsManagerRel selectByLineGoodsrRelId(String lineGoodsrRelId);

    ResultUtil orderExamineSelectAgent(TAgentSearchVo record);

    int updateEnduserUploadedInfo(TEndUserInfo record);

    ResultUtil selectDriverIfCaptain(TEndUserInfo vo);

    ResultUtil addAddress(TEndUserInfo vo) throws Exception;

    ResultUtil excelBatchAddAddress(TEndUserInfo vo) throws Exception;

    ResultUtil insertCaptain(TEndUserInfo vo);

    ResultUtil selectCaptainIfDriver(TEndUserInfoVO vo);

    ResultUtil insertDirver(TEndUserInfoVO vo);

    ResultUtil selectCaptainPage(TEndUserInfoVO vo);

    TEndUserInfoVO selectCaptainOwnerById(Integer id);

    ResultUtil updateCaptainPage(EndUserInfoUpdate vo);

    ResultUtil deleteCaptain(TEndUserInfoDto vo);

    ResultUtil selectCaptainNameList(String parameter);

    List<TEndUserInfo> selectByAccountNo(String accountNo);

    public int updateEndUserLogisticsRole(TEndUserInfo record);

    ResultUtil selectByCaptainListPage(TEndUserInfoVO vo);

    ResultUtil selectByCaptainList(TEndUserInfoVO vo);

    public TEndUserInfoVO selectEndCaptainById(Integer id);
    int insterDriverZzh(TEndUserInfoSqZzhVO record);

    ResultUtil selectIfAgreement(String phone);

    ResultUtil updateIfAgreement(String phone);

    ResultUtil perfectCard(PerfectCardInfoVO vo);

    ResultUtil selectTransferorPage(TEndUserInfoSearchVO vo);

    ResultUtil selectTransferorInfo(Integer endUserId);

    ResultUtil updateTransferorInfo(TransferorInfoVO vo);

    ResultUtil transferorIdentityChange(Integer id,String userLogisticsRole);

    ResultUtil perfectCardInfo(TransferorInfoQueryVO vo);

    List<TEndUserInfo> selectByPhoneAndIdcard(String phone, String Idcard);

    List<TEndUserInfo> selectByIdcard(String Idcard);

    List<TEndUserInfo> selectIdcardUserLogisticsRole(String idcard,String userLogisticsRole);

    ResultUtil updateUserOpenRoleInfo(TEndUserInfo tEndUserInfo);

    ResultUtil userInfoIfComplete(Integer endUserId);

    ResultUtil insertOcrOpenRoleInfo(TOcrOpenRole tOcrOpenRole);

    String queryCardOwnerUserLogisticsRole(String cardOwnerPhone, String cardOwnerIdcacrd);

    String queryCardOwnerWhetherSelfOpenRole(Integer bankCardId);

    TEndUserOpenRole queryEnduserOpenRoleStatus(Integer enduserId);

    List<TEndCarInfo> queryEnduserAllCar(Integer enduserId);


    TEndUserInfoVO driverIdentityVerificationStatus(String phone);

    ResultUtil updateDriverPhone(String phone,String newphone);

    DriverInfoDto getDriverVehicleInfo(DriverQueryParam param);

    TEndUserInfo getDataByPhoneAndIdcard(String phone, String idcard,String realName);

}
