package com.lz.service;

import com.lz.common.util.ResultUtil;
import com.lz.model.TZtBankCard;
import com.lz.vo.*;
public interface TZtWalletService {

    public ResultUtil hxBankCardList(TZtBankCardVo record);

    public ResultUtil bindingBank(TZtBankCardVo record);

    public ResultUtil unbindBank(TZtBankCardVo record);

    public ResultUtil agentHxBankCardList(TZtBankCardVo record);

    public ResultUtil agentBindingBank(TZtBankCardVo record);
}
