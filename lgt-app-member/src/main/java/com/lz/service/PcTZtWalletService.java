package com.lz.service;

import com.lz.common.util.ResultUtil;
import com.lz.vo.CarrierHxOpenRoleSearchVo;
import com.lz.vo.CompanyWalletCapitalFlowVO;
import com.lz.vo.PcCompanyWalletVO;

import java.util.Map;

public interface PcTZtWalletService {

    ResultUtil pcCompanyWalletDetail(PcCompanyWalletVO record);

    ResultUtil companyWalletCapitalFlow(CompanyWalletCapitalFlowVO companyWalletCapitalFlowVO);

    ResultUtil companyWalletCapitalFlowExcel(CompanyWalletCapitalFlowVO companyWalletCapitalFlowVO);

    ResultUtil driverWalletCapitalFlow(CompanyWalletCapitalFlowVO companyWalletCapitalFlowVo);

    ResultUtil selectByHxCarrierWalletPage(CarrierHxOpenRoleSearchVo record);

    ResultUtil selectCarrierHxWalletLogPage(CarrierHxOpenRoleSearchVo record);

    ResultUtil carrierHxWalletExcel(CarrierHxOpenRoleSearchVo record);

    ResultUtil selectHxPlatformWallet();

    ResultUtil selectHxPlatformWalletList(CarrierHxOpenRoleSearchVo record);

    ResultUtil platformHxWalletExcel(CarrierHxOpenRoleSearchVo record);

    ResultUtil selectHxAgentWalletList(CarrierHxOpenRoleSearchVo record);

    ResultUtil selectHxAgentWallet();
}
