package com.lz.service.impl;

import cn.hutool.json.JSONUtil;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.HXTradeTypeEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.hxPayment.request.query.CustomerQueryTradeReq;
import com.lz.common.model.hxPayment.request.query.CustomerReceiptReq;
import com.lz.common.model.jdPayment.util.CloudPayFormatUtil;
import com.lz.common.util.DateUtils;
import com.lz.common.util.ResultUtil;
import com.lz.dao.*;
import com.lz.hxpay.CloudPaymentAPI;
import com.lz.model.TOrderPayDetail;
import com.lz.model.TOrderPayInfo;
import com.lz.model.TZtWallet;
import com.lz.model.TZtWalletChangeLog;
import com.lz.schedule.model.TTask;
import com.lz.service.HxBalancePayService;
import com.lz.vo.TOrderPayDetailVO;
import commonSdk.responseModel.CustomerQueryTradeResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdk.model.message.AsyncNotifyVirtualBalancePayMessageBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Service
public class HxBalancePayServiceImpl implements HxBalancePayService {

    @Value("${hxyhPartnerId}")
    private String hxyhPartnerId;

    @Value("${hxyhChannelId}")
    private String hxyhChannelId;

    @Resource
    private TZtWalletMapper tZtWalletMapper;

    @Resource
    private TZtWalletChangeLogMapper tZtWalletChangeLogMapper;

    @Resource
    private TMemberOrderPayInfoMapper orderPayInfoMapper;

    @Resource
    private TMemberOrderPayDetailMapper orderPayDetailMapper;

    @Resource
    private TMemberTaskMapper tMemberTaskMapper;

    @Autowired
    private CloudPaymentAPI cloudPaymentAPI;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil balancePayToolCallback(AsyncNotifyVirtualBalancePayMessageBody messageBody) {
        TZtWallet outWallet = tZtWalletMapper.selectByPartnerAccId(messageBody.getOutPartnerAccId());
        if (null == outWallet) {
            log.error("华夏小工具余额支付 - 查询转出钱包信息失败，{}", JSONUtil.toJsonStr(messageBody));
            return ResultUtil.error("华夏小工具余额支付 - 查询转出钱包信息失败");
        }
        TZtWallet inWallet = tZtWalletMapper.selectByPartnerAccId(messageBody.getInPartnerAccId());
        if (null == inWallet) {
            log.error("华夏小工具余额支付 - 查询转入钱包信息失败，{}", JSONUtil.toJsonStr(messageBody));
            return ResultUtil.error("华夏小工具余额支付 - 查询转入钱包信息失败");
        }

        TOrderPayInfo tOrderPayInfo = orderPayInfoMapper.selectByBizOrderNo(messageBody.getBizOrderNo());
        if (null == tOrderPayInfo) {
            // 查询交易信息
            CustomerQueryTradeReq request = new CustomerQueryTradeReq();
            request.setRequestId(IdWorkerUtil.getInstance().nextId());
            request.setRequestTime(DateUtils.getRequestTime());
            request.setPartnerId(hxyhPartnerId);
            request.setChannelId(hxyhChannelId);
            request.setBizOrderNo(messageBody.getBizOrderNo());
            request.setTradeType(DictEnum.SALE.code);
            ResultUtil resultUtil = cloudPaymentAPI.execute(request);
            log.info("华夏小工具余额支付 - 查询交易状态，{}", JSONUtil.toJsonStr(resultUtil));
            CustomerQueryTradeResponse response = CloudPayFormatUtil.ObjToBean(resultUtil, CustomerQueryTradeResponse.class);

            log.info("华夏小工具余额支付 - 查询交易状态结果：{}", response);
            if (null == response || !DictEnum.SUCCESSCODE.code.equals(response.getResponseCode())) {
                log.error("华夏小工具余额支付 - 查询交易状态失败，{}", JSONUtil.toJsonStr(resultUtil));
                return ResultUtil.error("华夏小工具余额支付 - 查询交易状态失败");
            }
            tOrderPayInfo = new TOrderPayInfo();
            tOrderPayInfo.setCode(IdWorkerUtil.getInstance().nextId());
            tOrderPayInfo.setOrderPayStatus(DictEnum.P070.code);
            tOrderPayInfo.setPaymentPlatforms(DictEnum.HXPLATFORMS.code);
            tOrderPayInfo.setOrderActualPayment(response.getOrderAmount());
            tOrderPayInfo.setOrderTotalPayment(response.getOrderAmount());
            tOrderPayInfo.setCreateTime(response.getTradeTime());
            tOrderPayInfo.setUpdateTime(response.getTradeTime());
            // 创建支付主表
            orderPayInfoMapper.insertSelective(tOrderPayInfo);

            //创建支付子表
            TOrderPayDetail orderPayDetail = new TOrderPayDetail();
            orderPayDetail.setCode(messageBody.getBizOrderNo());
            orderPayDetail.setOrderPayCode(tOrderPayInfo.getCode());
            orderPayDetail.setTradeType(HXTradeTypeEnum.HX_TOOL_BALANCE_PAY.code);
            orderPayDetail.setOperateState(HXTradeTypeEnum.RZ.code);
            orderPayDetail.setOperateTime(response.getTradeTime());
            orderPayDetail.setRemark(response.getBankOrderNo());
            orderPayDetail.setParam1(String.valueOf(outWallet.getId()));
            orderPayDetail.setCreateTime(response.getTradeTime());
            orderPayDetail.setUpdateTime(response.getTradeTime());
            orderPayDetailMapper.insertSelective(orderPayDetail);
        }

        if (messageBody.getOrderStatus().equals(DictEnum.PAY_SUCC.code)) {
            // 支付成功
            tOrderPayInfo.setOrderPayStatus(DictEnum.M090.code);

            //修改收款方钱包
            outWallet.setAccountBalance(outWallet.getAccountBalance().subtract(tOrderPayInfo.getOrderActualPayment()));
            tZtWalletMapper.updateByPrimaryKey(outWallet);

            //修改入款方钱包
            BigDecimal accountBalance = inWallet.getAccountBalance().add(tOrderPayInfo.getOrderActualPayment());
            inWallet.setAccountBalance(accountBalance);
            tZtWalletMapper.updateByPrimaryKeySelective(inWallet);

            //钱包变动记录
            TZtWalletChangeLog outwalletChangeLog = new TZtWalletChangeLog();
            outwalletChangeLog.setWalletId(outWallet.getId());
            outwalletChangeLog.setWalletType(outWallet.getPurseCategory());
            outwalletChangeLog.setTradeType(HXTradeTypeEnum.HX_TOOL_BALANCE_PAY_ZC.code);
            outwalletChangeLog.setAmount(tOrderPayInfo.getOrderTotalPayment());
            outwalletChangeLog.setTradeTime(tOrderPayInfo.getCreateTime());
            outwalletChangeLog.setTradeNo(messageBody.getBizOrderNo());
            outwalletChangeLog.setOuterTradeNo(messageBody.getBankOrderNo());
            if (null != messageBody.getCarryOverAmount()) {
                outwalletChangeLog.setCarryOverAmount(messageBody.getCarryOverAmount());
            }
            outwalletChangeLog.setGmtClose(new Date());
            outwalletChangeLog.setTraderWalletId(inWallet.getId());
            outwalletChangeLog.setRemark("小工具余额支付支出");
            outwalletChangeLog.setEnable(false);
            tZtWalletChangeLogMapper.insertSelective(outwalletChangeLog);

            //电子回单添加到任务中
            saveReceiptApplyTask(messageBody.getOutPartnerAccId(), messageBody.getBizOrderNo(), DictEnum.SALE.code, outwalletChangeLog.getId());

            //入款钱包变动记录
            TZtWalletChangeLog inWalletChangeLog = new TZtWalletChangeLog();
            inWalletChangeLog.setWalletId(inWallet.getId());
            inWalletChangeLog.setWalletType(inWallet.getPurseCategory());
            inWalletChangeLog.setTradeType(HXTradeTypeEnum.HX_TOOL_BALANCE_PAY_SR.code);
            inWalletChangeLog.setAmount(tOrderPayInfo.getOrderTotalPayment());
            inWalletChangeLog.setTradeTime(tOrderPayInfo.getCreateTime());
            inWalletChangeLog.setTradeNo(messageBody.getBizOrderNo());
            inWalletChangeLog.setOuterTradeNo(messageBody.getBankOrderNo());
            inWalletChangeLog.setGmtClose(new Date());
            inWalletChangeLog.setTraderWalletId(outWallet.getId());
            inWalletChangeLog.setRemark("小工具余额支付收入");
            inWalletChangeLog.setEnable(false);
            tZtWalletChangeLogMapper.insertSelective(inWalletChangeLog);
        } else {
            // 支付失败
            tOrderPayInfo.setOrderPayStatus(DictEnum.M080.code);
        }
        // 修改支付主表
        orderPayInfoMapper.updateByPrimaryKeySelective(tOrderPayInfo);

        // 修改支付子表
        TOrderPayDetailVO ov = new TOrderPayDetailVO();
        ov.setCode(messageBody.getBizOrderNo());
        ov.setErrorCode(messageBody.getResponseCode());
        ov.setErrorMsg(messageBody.getResponseDesc());
        ov.setInnerTradeNo(messageBody.getBizOrderNo());
        ov.setTradeStatus(messageBody.getOrderStatus());
        ov.setReturnTime(new Date());
        orderPayDetailMapper.updateStatusByPayCode(ov);

        return ResultUtil.ok();
    }

    /**
     * 华夏电子回单请求写入任务表
     *
     * @param partnerAccId
     * @param bizOrderNo
     * @param tradeType
     * @param walletLogId
     */
    public void saveReceiptApplyTask(String partnerAccId, String bizOrderNo, String tradeType, Integer walletLogId) {
        CustomerReceiptReq req = new CustomerReceiptReq();
        req.setPartnerId(hxyhPartnerId);
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setChannelId(hxyhChannelId);
        req.setPartnerAccId(partnerAccId);
        req.setBizOrderNo(bizOrderNo);
        req.setTradeType(tradeType);
        TTask tTask = new TTask();
        tTask.setTaskId(IdWorkerUtil.getInstance().nextId());
        tTask.setTaskType(DictEnum.HXRECEIPT.code);
        tTask.setTaskTypeNode("FQ");
        tTask.setBusinessType("ZF");
        tTask.setSourceTablename("t_zt_wallet_log");
        tTask.setSourcekeyFieldname("id");
        tTask.setSourceFieldname("id");
        tTask.setSourceFieldvalue(bizOrderNo);
        tTask.setRequestParameter(JSONUtil.toJsonStr(req));
        // 记录钱包流水ID
        tTask.setParam1(String.valueOf(walletLogId));
        tTask.setRequestTimes(0);
        tTask.setCreateTime(new Date());
        tTask.setRequestDate(new Date());
        tTask.setIsSuccessed(false);
        tTask.setEnable(false);
        tMemberTaskMapper.insert(tTask);
    }

}
