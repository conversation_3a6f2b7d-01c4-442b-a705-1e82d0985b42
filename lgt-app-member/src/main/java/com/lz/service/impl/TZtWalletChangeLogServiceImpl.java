package com.lz.service.impl;

import com.lz.api.OssAPI;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.JdTradeType;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.util.DateUtils;
import com.lz.common.util.ResultUtil;
import com.lz.dao.TZtWalletChangeLogMapper;
import com.lz.example.TZtWalletChangeLogExample;
import com.lz.model.TZtWalletChangeLog;
import com.lz.service.TZtWalletChangeLogService;
import com.lz.vo.TZtWalletChangeLogVo;
import org.springframework.beans.factory.annotation.Value;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
@Service
public class TZtWalletChangeLogServiceImpl implements TZtWalletChangeLogService {
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private OssAPI ossAPI;
    @Value("${tmpFilePath}")
    private String tmpFilePath;

    @Resource
    private TZtWalletChangeLogMapper ztWalletChangeLogMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateById(TZtWalletChangeLog ztWalletChangeLog) {
        try {
            TZtWalletChangeLog tZtWalletChangeLog = ztWalletChangeLogMapper.selectByPrimaryKey(ztWalletChangeLog.getId());
            // 承运余额支付，企业充值
            if (null != tZtWalletChangeLog && JdTradeType.CARRIERBALANCECOMPANYCHARGE_ZC.code.equals(tZtWalletChangeLog.getTradeType())) {
                TZtWalletChangeLog companyLog = new TZtWalletChangeLog();
                companyLog.setWalletType(DictEnum.BCOMPANY.code);
                companyLog.setTradeType(DictEnum.BCHONGZHI.code);
                companyLog.setTradeNo(tZtWalletChangeLog.getTradeNo());
                companyLog = ztWalletChangeLogMapper.selectByModel(companyLog);
                companyLog.setFileUrl(ztWalletChangeLog.getFileUrl());
                // 添加企业流水的电子回单
                ztWalletChangeLogMapper.updateByPrimaryKeySelective(companyLog);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("华夏承运余额支付，企业充值，更新电子回单失败： {}", e);
        }
        return ztWalletChangeLogMapper.updateByPrimaryKeySelective(ztWalletChangeLog);
    }

    @Override
    public TZtWalletChangeLog selectByEndUserId(Integer endUserId, String orderBusinessCode) {
        return ztWalletChangeLogMapper.selectByEndUserId(endUserId,orderBusinessCode);
    }

    @Override
    public TZtWalletChangeLog selectByOrderBusinessCode(String orderBusinessCode, String tradeType) {
        return ztWalletChangeLogMapper.selectByOrderBusinessCode(orderBusinessCode,tradeType);
    }

    @Override
    public List<TZtWalletChangeLog> seelctByTradeTypeWalletIdOrderBusinessCode(String tradeType,Integer carrierId,String orderBusinessCode) {
        List<TZtWalletChangeLog> tZtWalletChangeLog = ztWalletChangeLogMapper.seelctByTradeTypeWalletIdOrderBusinessCode(tradeType,carrierId,orderBusinessCode);
        return tZtWalletChangeLog;
    }

    @Override
    public int updateByPrimaryKeySelective(TZtWalletChangeLog log) {
        return ztWalletChangeLogMapper.updateByPrimaryKeySelective(log);
    }

    @Override
    public List<TZtWalletChangeLog> paymentFeesUpdateTransactionType(String tradeNo) {
        List<TZtWalletChangeLog> tZtWalletChangeLog = ztWalletChangeLogMapper.selectByTradeNo(tradeNo);
        return tZtWalletChangeLog;
    }

    @Override
    public int updateWalletChangeLogById(TZtWalletChangeLog log) {
        return ztWalletChangeLogMapper.updateByPrimaryKeySelective(log);
    }

    @Override
    public TZtWalletChangeLog selectOldOrderCarCaptainByOrderBusinessCode(Integer walletId, String orderBusinessCode) {
        return ztWalletChangeLogMapper.selectOldOrderCarCaptainByOrderBusinessCode(walletId,orderBusinessCode);
    }

    @Override
    public ResultUtil loadReceiptApply(TZtWalletChangeLogVo logVo){
        try{
            String receiptDocuments = "电子回单"+ DateUtils.formatDate(logVo.getStartDate(),"yyyy-MM-dd")+"-"+DateUtils.formatDate(logVo.getEndDate(),"yyyy-MM-dd")+".zip";
            OutputStream os  = new FileOutputStream(receiptDocuments);
            TZtWalletChangeLogExample example = new TZtWalletChangeLogExample();
            TZtWalletChangeLogExample.Criteria cr = example.createCriteria();
            cr.andCreateTimeBetween(logVo.getStartDate(),logVo.getEndDate());
            List<TZtWalletChangeLog> ztWalletChangeLogList =  ztWalletChangeLogMapper.selectByExample(example);
            if(ztWalletChangeLogList.size()>0){
                List<String> fileIds = new ArrayList<>();
                for(TZtWalletChangeLog ztWalletChangeLog:ztWalletChangeLogList){
                    if(null!=ztWalletChangeLog.getFileUrl() &&!"".equals(ztWalletChangeLog.getFileUrl())){
                        fileIds.add(ztWalletChangeLog.getFileUrl());
                    }
                }
                List<byte[]> ret = downloadFiles(fileIds);

                // 2.封装zip压缩包,需要先创建文件夹
                File file = new File(tmpFilePath);
                if(!file.isDirectory()){
                    file.mkdirs();
                }
                ZipOutputStream zipOut = new ZipOutputStream(new FileOutputStream(tmpFilePath+File.separator+receiptDocuments));
                InputStream is = new FileInputStream(tmpFilePath+File.separator+receiptDocuments);
                for (byte[] bytes : ret) {
                    String fileName = IdWorkerUtil.getInstance().nextId();
                    zipOut.putNextEntry(new ZipEntry(fileName+".pdf"));
                    zipOut.write(bytes,0,bytes.length);
                    zipOut.flush();
                }
                zipOut.close();
                //3.将zip输出
                byte[] zipBytes = new byte[1024];
                int len = -1;
                while((len = is.read(zipBytes)) != -1){
                    os.write(zipBytes,0,len);

                }
                os.flush();
                os.close();
                is.close();
                //4.删除生成的临时zip
                File tmpZip = new File(tmpFilePath+ File.separator+receiptDocuments);
                String urlPath = tmpFilePath+ File.separator+receiptDocuments;
                ResultUtil fastResult = ossAPI.filePath(receiptDocuments,urlPath);
                String  receiptPath = fastResult.getData().toString();
                tmpZip.delete();
                return ResultUtil.ok(receiptPath);
            }else {
                return ResultUtil.ok("当前时间段无电子回单");
            }
        }catch (Exception e){
            log.error("文件下载失败：",e);
            return ResultUtil.error("文件下载失败");
        }
    }
    private List<byte[]> downloadFiles(List<String> fileIds){
        List<byte[]> ret = new ArrayList<>();
        fileIds.stream().forEach(fileId->{
            byte[] bs = restTemplate.getForObject(fileId,byte[].class);
            ret.add(bs);
        });
        return ret;
    }

}
