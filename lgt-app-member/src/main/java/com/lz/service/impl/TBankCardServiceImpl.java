package com.lz.service.impl;

import cn.hutool.json.JSONUtil;
import com.lz.api.TOrderPayInfoAPI;
import com.lz.common.config.RedisUtil;
import com.lz.common.model.BankCardReq;
import com.lz.common.model.BankCardResp;
import com.lz.common.model.CodeEnum;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.IntegrationBankCard;
import com.lz.common.util.ResultUtil;
import com.lz.dao.*;
import com.lz.dto.BankCardDTO;
import com.lz.dto.EndUserDTO;
import com.lz.example.TBankCardExample;
import com.lz.example.TEnduserAccountExample;
import com.lz.model.*;
import com.lz.service.TAccountService;
import com.lz.service.TBankCardService;
import com.lz.service.TJDBankCardService;
import com.lz.vo.TBankCardVo;
import com.lz.vo.TBankcardMonthlyVo;
import com.lz.vo.TJdBankCardVo;
import com.lz.vo.TOrderPayInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @date 2019-04-10
*/
@Slf4j
@Service
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class TBankCardServiceImpl implements TBankCardService {

    @Resource
    private TBankCardMapper tBankCardMapper;

    @Autowired
    private TAccountService tAccountService;

    @Autowired
    private TWalletMapper tWalletMapper;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private TOrderPayInfoAPI tOrderPayInfoAPI;

    @Autowired
    private TVerificationCodeLogMapper tVerificationCodeLogMapper;

    @Autowired
    private TBankcardMonthlyMapper bankcardMonthlyMapper;

    @Autowired
    private TEndUserOpenRoleMapper tEndUserOpenRoleMapper;

    @Autowired
    private TJDBankCardService tjdBankCardService;

    @Resource
    private TJdBankCardMapper tJdBankCardMapper;

    @Resource
    private TEnduserAccountMapper tEnduserAccountMapper;

    @Override
    public TBankCard selectBankCardByEndUserId(Integer id) {
        return tBankCardMapper.selectBankCardByEndUserId(id);
    }

    @Override
    public TBankCard selectById(Integer bankCardId) {
        return tBankCardMapper.selectByPrimaryKey(bankCardId);
    }

    @Override
    public int deleteById(Integer id) {
        TBankCard tBankCard = new TBankCard();
        tBankCard.setId(id);
        tBankCard.setEnable(true);
        TBankCard card = tBankCardMapper.selectByPrimaryKey(id);
        int count = tBankCardMapper.updateByPrimaryKeySelective(tBankCard);
        TBankCardExample example = new TBankCardExample();
        TBankCardExample.Criteria cr = example.createCriteria();
        cr.andAccountIdEqualTo(card.getAccountId());
        cr.andIfDefaultEqualTo(true);
        cr.andEnableEqualTo(false);
        List<TBankCard> tBankCardList = tBankCardMapper.selectByExample(example);
        if(tBankCardList.size()<1){
            TBankCardExample example2 = new TBankCardExample();
            TBankCardExample.Criteria cr2 = example2.createCriteria();
            cr2.andAccountIdEqualTo(card.getAccountId());
            cr2.andEnableEqualTo(false);
            List<TBankCard> tBankCardList2 = tBankCardMapper.selectByExample(example2);
            for(int i=0;i<tBankCardList2.size();i++){
                TBankCard tBankCard2 = tBankCardList2.get(i);
                if(i==0){
                    tBankCard2.setIfDefault(1);
                } else{
                    tBankCard2.setIfDefault(0);
                }
                tBankCardMapper.updateByPrimaryKeySelective(tBankCard2);
            }

        }
        return count;
    }

    @Override
    public int updateById(Integer id) {
        //设置默认银行 ，其他设为非默认的
        TBankCard card = tBankCardMapper.selectByPrimaryKey(id);
        card.setIfDefault(1);
        TBankCardExample example = new TBankCardExample();
        TBankCardExample.Criteria cr = example.createCriteria();
        cr.andAccountIdEqualTo(card.getAccountId());
        List<TBankCard> tBankCardList = tBankCardMapper.selectByExample(example);
        for(TBankCard tBank:tBankCardList){
            tBank.setIfDefault(0);
            tBankCardMapper.updateByPrimaryKey(tBank);
        }
        return tBankCardMapper.updateByPrimaryKeySelective(card);
    }

    @Override
    public TBankCard selectIdDefault(Integer accountid) {
        // 先查询京东银行卡
        List<TBankCard> list = tJdBankCardMapper.selectJdBankCardList(CurrentUser.getEndUserId());
        if (!list.isEmpty()) {
            List<TBankCard> collect = list
                    .stream()
                    .filter((bankCard) -> null != bankCard.getIfDefault() && bankCard.getIfDefault() == 1)
                    .collect(Collectors.toList());
            // 京东默认银行卡
            if (!collect.isEmpty()) {
                return collect.get(0);
            }
            // 京东银行卡
            return list.get(0);
        }
        return tBankCardMapper.selectIdDefault(accountid);
    }

    @Override
    public ResultUtil selectBankCards(Integer enduserId) {
        List<Map<String, Object>> endUserCard = tBankCardMapper.getEndUserCard(enduserId);
        return ResultUtil.ok(endUserCard);
    }

    @Override
    public List<BankCardDTO> selectBankCardListDetail(TBankCardVo record) {
        List<BankCardDTO> bankCardDTOS = tBankCardMapper.selectBankCardListDetail(record);
        return bankCardDTOS;
    }

    /*
     * <AUTHOR>
     * @Description 根据银行卡id, 查找当前持卡人所有绑定的银行卡
     * @Date 2019/12/11 13:44
     * @Param
     * @return
     **/
    @Override
    public BankCardDTO selectAllBankNoByBankId(TBankCardVo record) {
        BankCardDTO bankCardDTO = new BankCardDTO();
        TBankCard tBankCard = tBankCardMapper.selectByPrimaryKey(record.getId());
        if (null != tBankCard) {
            if (null != tBankCard.getEnable()) {
                bankCardDTO.setId(tBankCard.getId());
            }
            // 是否检测身份证
            if (null != record.getCheckIdcard() && record.getCheckIdcard()) {
                if (null == tBankCard.getCardOwnerIdcard() || com.lz.common.util.StringUtils.isBlank(tBankCard.getCardOwnerIdcard())) {
                    throw new RuntimeException("持卡人未绑定身份证，请先绑定身份证");
                }
            }
        }
        List<String> cardNos = tBankCardMapper.selectAllBankNoByBankNo(record);
        List<Integer> bankIds = tBankCardMapper.selectAllBankNoByBankId(record);
        if (cardNos.size() > 0) {
            bankCardDTO.setCardNos(cardNos);
        }
        if (!bankIds.isEmpty()) {
            bankCardDTO.setBankIds(bankIds);
        }
        return bankCardDTO;
    }



    @Override
    public ResultUtil addAgentBankCard(TBankCardVo tBankCard) {


        Integer userAccountId = CurrentUser.getUserAccountId();
        tBankCard.setAccountId(userAccountId);
        tBankCard.setCreateTime(new Date());
        tBankCard.setCreateUser(CurrentUser.getUserNickname());
        tBankCard.setCardOwnerPhone(tBankCard.getPhone());


        String code = "";
        TAccount t =  tAccountService.selectByPrimaryKey(userAccountId);
        if (t==null){
            ResultUtil.error("该账户尚未注册");
        }

        /* 判断验证码， 正确添加错误返回*/
        if(tBankCard.getBankName().equals(tBankCard.getCardOwner())){
            code = ObjectUtils.toString(redisUtil.get("ADDBRCARD" + tBankCard.getPhone()));
        }else{
            code = ObjectUtils.toString(redisUtil.get("ADDCARD" + tBankCard.getPhone()));
        }
        /*测试*/
        /*测试*/

        List<TAccount> tAccounts = tAccountService.findphone(tBankCard.getPhone());
        String verificationCode = tBankCard.getVerificationCode();

        if (tAccounts.size()==0){
            ResultUtil.error("该账户尚未注册");
        }else if (StringUtils.isEmpty(verificationCode)){
            ResultUtil.error("请输入验证码");
        }else if (StringUtils.isEmpty(code)) {
            return ResultUtil.error("请重新发送验证码");
        }else if (!tBankCard.getVerificationCode().equals(code)) {
            return ResultUtil.error("验证码错误");
        }

        //是否如果没有银行卡，添加的银行卡为默认银行卡
        boolean ifDefault=false;
        List<TBankCard> bankCardList = tBankCardMapper.selectBankCardByNowAccount(CurrentUser.getEndUserId());
        if (bankCardList!=null && bankCardList.size()>0){
            for (TBankCard bankCard : bankCardList) {
                if (tBankCard.getCardNo().equals(bankCard.getCardNo())){
                    return ResultUtil.error("当前银行卡已经添加存在");
                }
            }
        }else {
            ifDefault=true;
        }



        //银行三要素认证
        ResultUtil resultUtil = new ResultUtil();
        IntegrationBankCard ibc = new IntegrationBankCard();
        BankCardReq bankCardReq = new BankCardReq();
        bankCardReq.setBankCard(tBankCard.getCardNo());
        bankCardReq.setIdCard(tBankCard.getCardOwnerIdcard());
        bankCardReq.setName(tBankCard.getCardOwner());
        bankCardReq.setPhone(tBankCard.getPhone());
        BankCardResp bankCardRespa = ibc.checkBankCard(bankCardReq);
        if (StringUtils.isNotBlank(bankCardRespa.getResult())
                && !BankCardResp.RESULT_UNANIMOUS_CODE.equals(bankCardRespa.getResult())) {
            resultUtil.setCode("error");
            resultUtil.setMsg(bankCardRespa.getDesc());
            resultUtil.setData(bankCardRespa);
            return  resultUtil;
        }

        int i = tBankCardMapper.insertSelective(tBankCard);
        //设置默认银行卡
        if (null!=tBankCard.getIfDefault() && tBankCard.getIfDefault()==1){
            Integer id = tBankCard.getId();
            this.updateById(id);
        }




        if (i!=0){
            try {
                TVerificationCodeLog tVerificationCodeLogDB = tVerificationCodeLogMapper.selectByPhoneAndCode(tBankCard.getPhone(), tBankCard.getVerificationCode());
                if (tVerificationCodeLogDB!=null){
                    TVerificationCodeLog tVerificationCodeLog = new TVerificationCodeLog();
                    tVerificationCodeLog.setId(tVerificationCodeLogDB.getId());
                    tVerificationCodeLog.setIfUsed("1");
                    tVerificationCodeLogMapper.updateByPrimaryKeySelective(tVerificationCodeLog);
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        return ResultUtil.ok();
    }

    @Override
    public ResultUtil selectBankCardByNowAccount() {
        Integer endUserId = CurrentUser.getEndUserId();
        List<TBankCard> bankCardList =tBankCardMapper.selectBankCardByNowAccount(endUserId);
        for (TBankCard tBankCard : bankCardList) {
            String cardNo = tBankCard.getCardNo();
            String cardOwnerIdcard = tBankCard.getCardOwnerIdcard();
            //持卡人
            String cardOwner = tBankCard.getCardOwner();
            cardNo = "***************"+cardNo.substring(cardNo.length() - 4);
            tBankCard.setCardNo(cardNo);
            tBankCard.setRemark(cardOwner+" "+cardNo);
            if (tBankCard.getCardOwnerIdcard()!=null){
                 tBankCard.setCardOwnerIdcard(cardOwnerIdcard.substring(0,14)+"****");
            }
        }
        return ResultUtil.ok(bankCardList);
    }

    @Override
    public ResultUtil selectCarrierByEnduserId() {
        HashMap hashMap = new HashMap();
        //当前承运方
        List<Map>carrierList=tBankCardMapper.selectCarrierByEnduserId(CurrentUser.getEndUserId());
        if (carrierList!=null && carrierList.size()>0) {
            for (Map carrier : carrierList) {
                Long l = (Long) carrier.get("carrierId");
                Integer id = l.intValue();
                Long carrierEnduserCompanyRelId = (Long) carrier.get("carrierEnduserCompanyRelId");
                Integer cecrId = carrierEnduserCompanyRelId.intValue();
                //查询每个承运方账户余额
                TWallet tWallet = tWalletMapper.selectWalletByCarrierId(cecrId);
                carrier.put("accountBalance", tWallet.getAccountBalance());
                carrier.put("walletId", tWallet.getId());
                carrier.put("carrierId", id);
            }
            hashMap.put("CarrierBankBalance",carrierList);
        }else {
            return ResultUtil.error("当前账户无承运方信息");
        }
        //当前账户银行卡列表
        ResultUtil resultUtil = this.selectBankCardByNowAccount();
        if (resultUtil.getData()!=null ){
            List<TBankCard> tBankCardList = (List<TBankCard>) resultUtil.getData();
            if (tBankCardList.size()==0){
                return ResultUtil.error("此账号无绑定银行卡信息");
            }
            hashMap.put("banks",tBankCardList);
        }else {
            return ResultUtil.error("此账号无绑定银行卡信息");
        }
        return ResultUtil.ok(hashMap);
    }

    @Override
    public ResultUtil bankcardUntying(TBankCard tWallet) {

        //jdBankCardVos有值就是京东绑过的卡先走京东解绑再删卡
        //deleteStatus为0证明删除的是非京东绑卡的银行卡
        int deleteStatus = 0;

        //查询开户表数据
        Integer endUserId = CurrentUser.getEndUserId();
        List<TJdBankCardVo> jdBankCardVos = tJdBankCardMapper.openRoleUserJdBankList(endUserId);
        for(TJdBankCardVo tJdBankCard : jdBankCardVos){
            if(tJdBankCard.getBankCardId().equals(tWallet.getId())){
                if(!tJdBankCard.getDataEnable()){
                    return ResultUtil.error("当前银行卡正在进行京东绑定处理中，不能删除！");
                }
                if(jdBankCardVos.size() == 1){
                    return ResultUtil.error("当前京东银行卡只有一张，不能删除！");
                }
                deleteStatus++;
                //京东银行卡解绑

                TJdBankCardVo tJdBankCardVo = new TJdBankCardVo();
                tJdBankCardVo.setOpenRoleId(tJdBankCard.getOpenRoleId());
                tJdBankCardVo.setPartnerAccId(tJdBankCard.getPartnerAccId());
                tJdBankCardVo.setCardId(tJdBankCard.getCardId());
                tJdBankCardVo.setJdBankCardId(tJdBankCard.getJdBankCardId());
                ResultUtil jdResultUtil = tjdBankCardService.unbindOpenRoleUserBindingBank(tJdBankCardVo);


                if(null != jdResultUtil && null != jdResultUtil.getCode() && jdResultUtil.getCode().equals("success")){

                    //如果京东解绑是处理中那么删卡操作就放到回调里
                    if(null != jdResultUtil.getData() && String.valueOf(jdResultUtil.getData()).equals("PROCESS")){
                        log.info(tWallet.getId()+":银行卡解绑处理中!");
                        return ResultUtil.ok("银行卡解绑处理中!");
                    }else {

                        TBankCard tBankCard = new TBankCard();
                        tBankCard.setEnable(true);
                        tBankCard.setId(tWallet.getId());
                        tWallet.setEnable(true);
                        tBankCardMapper.updateByPrimaryKeySelective(tBankCard);

                    }
                }else {
                    return jdResultUtil;
                }
            }
        }

        if(deleteStatus == 0){

            TBankCard tBankCard = new TBankCard();
            tBankCard.setEnable(true);
            tBankCard.setId(tWallet.getId());
            tWallet.setEnable(true);
            tBankCardMapper.updateByPrimaryKeySelective(tBankCard);

        }

        return ResultUtil.ok("解绑成功");
    }

    @Override
    public ResultUtil bankcardTX(TOrderPayInfoVO tOrderPayInfoVO) {
        /**
         * 钱包ID  walletId
         * uid
         * 承运方ID
         * amount提现金额
         * 提现类型 tradeType  MANAGER
         * */

        //验证码
        String VerificationCodeDB = (String) redisUtil.get("AGENTTIXIAN" + tOrderPayInfoVO.getPhone());
        //根据ID查询当前银行卡信息
        TBankCard tBankCard = tBankCardMapper.selectByPrimaryKey(tOrderPayInfoVO.getBankId());


        if (tOrderPayInfoVO.getAmount()==null){
            return ResultUtil.error("请输入提现金额");
        }else if (tOrderPayInfoVO.getCarrierId()==null){
            return ResultUtil.error("请选择承运方");
        }else if (StringUtils.isEmpty(tOrderPayInfoVO.getVerificationCode())){
            return ResultUtil.error("请输入验证码");
        }else if (StringUtils.isEmpty(VerificationCodeDB)){
            return ResultUtil.error("请重新发送验证码");
        }else if (!tOrderPayInfoVO.getVerificationCode().equals(VerificationCodeDB)){
            return ResultUtil.error("验证码错误,请重新输入");
        }else if (tBankCard==null){
            return ResultUtil.error("此银行卡不存在");
        }

        TWallet tWallet = tWalletMapper.selectByPrimaryKey(tOrderPayInfoVO.getWalletId());
        if (tOrderPayInfoVO.getAmount().compareTo(tWallet.getAccountBalance())==1){
            return ResultUtil.error("提现金额不能超过当前余额");
        }else if (new BigDecimal(0.00).compareTo(tWallet.getAccountBalance())==0){
            return ResultUtil.error("当前钱包余额为"+tWallet.getAccountBalance()+"不可提现");
        }

        tOrderPayInfoVO.setAccountName(tBankCard.getCardOwner());//户名
        tOrderPayInfoVO.setBankAccountNo(tBankCard.getCardNo());//银行卡号
        tOrderPayInfoVO.setTradeType("MANAGER");//提现类型

        ResultUtil resultUtil = tOrderPayInfoAPI.carrierAndComapnyWithdraw(tOrderPayInfoVO);

        if (CodeEnum.SUCCESS.getCode().equals(resultUtil.getCode())){
            try {
                TVerificationCodeLog tVerificationCodeLogDB = tVerificationCodeLogMapper.selectByPhoneAndCode(tOrderPayInfoVO.getPhone(), tOrderPayInfoVO.getVerificationCode());
                if (tVerificationCodeLogDB!=null){
                    TVerificationCodeLog tVerificationCodeLog = new TVerificationCodeLog();
                    tVerificationCodeLog.setId(tVerificationCodeLogDB.getId());
                    tVerificationCodeLog.setIfUsed("1");
                    tVerificationCodeLogMapper.updateByPrimaryKeySelective(tVerificationCodeLog);
                }
            }catch (Exception e){
                e.printStackTrace();
            }
            if (null != resultUtil.getData()) {
                LinkedHashMap data = (LinkedHashMap) resultUtil.getData();
                if (null != data.get("code")) {
                    // 保存提现金额
                    TBankcardMonthlyVo bankcardMonthlyVo = new TBankcardMonthlyVo();
                    bankcardMonthlyVo.setAmount(tOrderPayInfoVO.getAmount());
                    bankcardMonthlyVo.setCardId(tBankCard.getId());
                    bankcardMonthlyVo.setCardNo(tBankCard.getCardNo());
                    bankcardMonthlyVo.setCardOwnerIdcard(tBankCard.getCardOwnerIdcard());
                    bankcardMonthlyVo.setCardOwner(tBankCard.getCardOwner());
                    bankcardMonthlyVo.setCurrentTime(new Date());
                    bankcardMonthlyVo.setCreateUser(CurrentUser.getUserNickname());
                    bankcardMonthlyVo.setCreateTime(new Date());
                    bankcardMonthlyVo.setUpdateUser(CurrentUser.getUserNickname());
                    bankcardMonthlyVo.setUpdateTime(new Date());
                    bankcardMonthlyVo.setEnable(false);
                    bankcardMonthlyVo.setParam1(String.valueOf(data.get("code")));
                    log.info("记录提现金额，{}", JSONUtil.toJsonStr(bankcardMonthlyVo));
                    bankcardMonthlyMapper.insertSelective(bankcardMonthlyVo);
                }
            }
        }

        return resultUtil;
    }

    /**
     * <AUTHOR>
     * @Description 根据银行卡id查询绑定当前银行卡号的司机、车主
     * @Date 2020/4/29 3:49 下午
     * @Param 
     * @return 3:49 下午
    **/
    @Override
    public List<EndUserDTO> selectEnduserByBankId(Integer bankId) {
        return tBankCardMapper.selectEnduserByBankId(bankId);
    }

    @Override
    public List<TBankCard> selectOneselfBankCardList(Integer accountId, String cardOwner, String cardOwnerIdcard) {
        return tBankCardMapper.selectOneselfBankcard(accountId,cardOwner,cardOwnerIdcard);
    }

    @Override
    public ResultUtil getUserBankCardList() {
        Integer endUserId = CurrentUser.getEndUserId();
        List<Map<String, Object>> userBankCardList = tBankCardMapper.getUserBankCardList(endUserId);
        return ResultUtil.ok(userBankCardList);
    }

    /**
     * 查询未被其他人绑定的银行卡数
     * @param endUserId
     * @param accountId
     * @return
     */
    @Override
    public int selectUniqueBankCardNUms(Integer endUserId, Integer accountId) {
        int bankNum = tBankCardMapper.selectCardByAccountId(accountId);//银行卡数量
        //去除本账号另一个身份的进行过开户绑卡的表
        TEnduserAccountExample tEnduserAccountExample = new TEnduserAccountExample();
        TEnduserAccountExample.Criteria cr1 = tEnduserAccountExample.createCriteria();
        cr1.andAccountIdEqualTo(accountId);
        cr1.andEnableEqualTo(false);
        List<TEnduserAccount> tEnduserAccountList = tEnduserAccountMapper.selectByExample(tEnduserAccountExample);
        for(TEnduserAccount tEnduserAccount : tEnduserAccountList){
            if(!endUserId.equals(tEnduserAccount.getEnduserId())){
                return bankNum = tBankCardMapper.selectCardByAccountIdByType(tEnduserAccount.getAccountId(),tEnduserAccount.getEnduserId());
            }
        }
        return bankNum;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateById(TBankCard bankCard) {
        tBankCardMapper.updateByPrimaryKeySelective(bankCard);
        return 1;
    }

}