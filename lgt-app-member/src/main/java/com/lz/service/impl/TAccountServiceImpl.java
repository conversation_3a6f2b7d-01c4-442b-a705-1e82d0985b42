package com.lz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.codingapi.txlcn.tc.annotation.DTXPropagation;
import com.codingapi.txlcn.tc.annotation.LcnTransaction;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lz.api.*;
import com.lz.common.config.RedisUtil;
import com.lz.common.dbenum.BankNameEnum;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.model.CodeEnum;
import com.lz.common.util.*;
import com.lz.dao.*;
import com.lz.dto.*;
import com.lz.example.TEndUserInfoExample;
import com.lz.example.TEnduserAccountExample;
import com.lz.model.*;
import com.lz.model.contract.req.ContractApplyCertReq;
import com.lz.model.contract.resp.ContractApplyCertResp;
import com.lz.service.TAccountService;
import com.lz.service.TCarrierCompanyOpenRoleService;
import com.lz.system.api.DicCatItemAPI;
import com.lz.system.api.SystemAPI;
import com.lz.system.dto.DicCatItemDTO;
import com.lz.system.dto.DictItemLineRoleDTO;
import com.lz.system.model.TSysRole;
import com.lz.system.model.TSysUser;
import com.lz.system.model.TUserInfo;
import com.lz.system.model.TUserRole;
import com.lz.system.vo.TSysUserForAddVO;
import com.lz.system.vo.TSysUserVO;
import com.lz.vo.*;
import com.lz.vo.certificatevo.CertificationVO;
import com.lz.vo.certificatevo.DrivingLicenceVO;
import com.lz.vo.certificatevo.IDCardVO;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * auth dingweibo
 * 账号表
 */
@Service("tAccountService")
@Transactional
@Component
public class TAccountServiceImpl implements TAccountService {

    private static final Logger log = LoggerFactory.getLogger(TAccountServiceImpl.class);

    /**
     * 企业发单员
     */
    private static final String COMPANYSENDORDER = "COMPANYSENDORDER";
    /**
     * 普通发单员
     */
    private static final String COMMONSENDORDER = "COMMONSENDORDER";
    /**
     * 收单员
     */
    private static final String RECEIVEORDER = "RECEIVEORDER";

    private enum ROLE {

        COMPANYSENDORDER("COMPANYSENDORDER", "企业发单员"),
        COMMONSENDORDER("COMMONSENDORDER", "普通发单员"),
        RECEIVEORDER("RECEIVEORDER", "收单员"),
        SENDEREXIST("SENDEREXIST", "已有发单负责人"),
        RECEIVEEXIST("RECEIVEEXIST", "已有收单负责人"),
        NEWSAVE("NEWSAVE", "NEWSAVE"),
        MODSAVE("MODSAVE", "MODSAVE");
        public String code;
        public String value;

        ROLE(String code, String value) {
            this.code = code;
            this.value = value;
        }
    }

    @Autowired
    private RedisUtil redisUtil;

    @Resource
    private TAccountMapper tAccountMapper;

    @Resource
    private SystemAPI systemAPI;

    @Autowired
    private DicCatItemAPI dicCatItemAPI;

    @Autowired
    private LinkGoodsRelAPI linkGoodsRelAPI;
    @Resource
    private TEndCarInfoMapper tEndCarInfoMapper;
    @Resource
    private TWalletMapper walletMapper;
    @Resource
    private TEndUserCarRelMapper tEndUserCarRelMapper;
    @Resource
    private TBankCardMapper tBankCardMapper;
    @Resource
    private TOrderInfoAPI tOrderInfoAPI;

    //企业
    @Resource
    private TCompanyInfoMapper tCompanyInfoMapper;

    @Resource
    private TLineUserRoleProMapper tLineUserRoleProMapper;

    @Resource
    private TLineGoodsUserRelMemberMapper tLineGoodsUserRelMemberMapper;

    @Resource
    private TLineGoodsRelToolMapper tLineGoodsRelMapper;
    @Resource
    TEndUserInfoMapper tEndUserInfoMapper;
    @Resource
    TEnduserAccountMapper tEnduserAccountMapper;
    @Resource
    private TEndUserStatusMapper tEndUserStatusMapper;
    @Resource
    private ContractAPI contractAPI;

    @Resource
    private Contract5GqAPI contract5GqAPI;

    @Resource
    private TCompanyStationRelMapper companyStationRelMapper;

    @Resource
    private TCompanyAccountMapper companyAccountMapper;
    @Resource
    private TVerificationCodeLogMapper verificationCodeLogMapper;

    @Autowired
    private HttpServletRequest request;

    @Resource
    private TMemberUserInfoMapper memberUserInfoMapper;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private TBusinessAPI tBusinessAPI;

    @Resource
    private TZtBankUserMapper tZtBankUserMapper;
    @Resource
    private TZtAccountOpenInfoMapper tZtAccountOpenInfoMapper;

    @Autowired
    private MqAPI mqAPI;

    @Resource
    private TEndUserAuditInfoMapper endUserAuditInfoMapper;

    @Resource
    private TEndCarAuditInfoMapper endCarAuditInfoMapper;

    @Resource
    private  TFifthGenerationSignOpenAccountMapper tFifthGenerationSignOpenAccountMapper;

    @Resource
    private TCarrierCompanyOpenRoleService tCarrierCompanyOpenRoleService;

    @Resource
    private TEndUserInfoUpdateRecordMapper tEndUserInfoUpdateRecordMapper;

    @Resource
    private TMemberOrderInfoMapper memberOrderInfoMapper;

    @Resource
    private TLineGoodsRelToolMapper tLineGoodsRelToolMapper;

    @Resource
    private TBusinessCompanyMapper tBusinessCompanyMapper;

    @Resource
    private TCarInsuranceAPI carInsuranceAPI;

    /**
     * 查询列表
     *
     * @param record
     * @return
     */
    @Override
    public ResultUtil selectByPage(TAccountVo record) {
        Page<Object> objectPage = PageHelper.startPage(record.getPage(), record.getSize());
        //删除状态
        record.setEnable(false);
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        if(userCompanyId!=null){
            String companyId = userCompanyId.get(0);
            record.setCompanyId(Integer.parseInt(companyId));
        }
        List<ManageAccountDTO> list = tAccountMapper.selectByPage(record);

        list.forEach((manageAccountDTO -> {
            TOrderInfoVO tOrderInfoVO = new TOrderInfoVO();
            if(manageAccountDTO.getAccountId() != null){
                tOrderInfoVO.setAccountId(manageAccountDTO.getAccountId());
            }
            if(manageAccountDTO.getLineId() != null){
                tOrderInfoVO.setLineId(manageAccountDTO.getLineId());
            }
            Long orderCount = memberOrderInfoMapper.selectByAccountIdAndLineId(tOrderInfoVO);
            if (null != orderCount && orderCount > 0) {
                manageAccountDTO.setIfFlag("1");
            } else {
                manageAccountDTO.setIfFlag("0");
            }
        }));
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), objectPage, objectPage.getTotal());
    }

    /**
     * 查询业务部人员负责的员工列表
     *
     * @param record
     * @return
     */
    @Override
    public ResultUtil selectBusinessByPage(TAccountVo record) {
        //删除状态
        record.setEnable(false);
        if (record.getIsLeader()){
            List<CompanyInfoDTO> companyInfoDTOS = tCompanyInfoMapper.selectByBusinessId(CurrentUser.getCurrentUserID());
            if (null!=companyInfoDTOS && !companyInfoDTOS.isEmpty()){
                //当前登录的业务员负责人所拥有企业
                List<Integer> companyids = new ArrayList<>();
                for (CompanyInfoDTO companyInfoDTO: companyInfoDTOS){
                    companyids.add(companyInfoDTO.getCompanyId());
                }
                record.setCompanyIds(companyids);
            }
        }else {
            //业务人员所负责的项目
            List<Integer> projectids = tBusinessCompanyMapper.selectProjectByBusinessId(CurrentUser.getCurrentUserID());
            if (null!=projectids && !projectids.isEmpty()){
                record.setProjectIds(projectids);
            }
        }
        Page<Object> objectPage = PageHelper.startPage(record.getPage(), record.getSize());

        //判定是否是业务部负责人
        if (record.getIsLeader()){
            tAccountMapper.selectBusinessByPage(record);
            return  new ResultUtil(CodeEnum.SUCCESS.getCode(), objectPage, objectPage.getTotal());
        }else {
            tAccountMapper.selectBusinessByPage(record);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), objectPage, objectPage.getTotal());
        }
    }

    /**
     * 查询员工信息列表
     *
     * @param record
     * @return
     */
    @Override
    public ResultUtil selectByPageInfo(TAccountVo record) {
        Page<Object> objectPage = PageHelper.startPage(record.getPage(), record.getSize());
        //删除状态
        record.setEnable(false);
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        if (userCompanyId != null && userCompanyId.size() > 0){
            String companyId = userCompanyId.get(0);
            if (StringUtils.isNotEmpty(companyId)){
                record.setCompanyId(Integer.valueOf(companyId));
            }
        }
        List<ManageAccountDTO> list = tAccountMapper.selectByPageInfo(record);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), objectPage, objectPage.getTotal());
    }

    /**
     * 查询员工信息列表
     *
     * @param record
     * @return
     */
    @Override
    public ResultUtil selectManagerByPageInfo(TAccountVo record) {
        Page<Object> objectPage = PageHelper.startPage(record.getPage(), record.getSize());
        //删除状态
        record.setEnable(false);
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        if (userCompanyId != null && userCompanyId.size() > 0){
            String companyId = userCompanyId.get(0);
            if (StringUtils.isNotEmpty(companyId)){
                record.setCompanyId(Integer.valueOf(companyId));
            }
        }
        List<ManageAccountDTO> list = tAccountMapper.selectManagerByPageInfo(record);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), objectPage, objectPage.getTotal());
    }


    /**
     * 企业端获取本企业下的员工
     * Yan
     *
     * @param account
     * @return
     */
    @Override
    public ResultUtil getEmployeeByCompany(TAccountVo account) {
        Page<Object> objectPage = PageHelper.startPage(account.getPage(), account.getSize());
        Integer companyIdInteger = null;
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        if (null != userCompanyId && userCompanyId.size() > 0){
            String companyId = userCompanyId.get(0);
            companyIdInteger = Integer.valueOf(companyId);
        }
        account.setCompanyId(companyIdInteger);
        Boolean superAdmin = CurrentUser.isSuperAdmin();
        account.setIsAdmin(superAdmin);
        List<ManageAccountDTO> employeeByCompanyId = tAccountMapper.getEmployeeByCompanyId(account);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), objectPage, objectPage.getTotal());
    }

    @Override
    public ResultUtil selectByPhone(String phone) {
        Boolean b = isEnterpriseAdministrator(phone);
        if(b){
            return ResultUtil.error("检测失败，该手机号为企业管理员账号。");
        }
        TAccount account = new TAccount();
        account.setAccountNo(phone.trim());
        List<TAccount> accounts = tAccountMapper.selectRepeatAccountNo(account);
        if (null != accounts && accounts.size() > 0) {
            return ResultUtil.ok(this.getById(accounts.get(0).getId().toString(),null));
        }else {
            return ResultUtil.ok();
        }
    }
    /**
     * 根据id获取对象
     *
     * @param id
     * @return
     */
    @Override
    public TAccountVo getPCById(String id, String type) {
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        TAccountVo vo = new  TAccountVo();
        if(userCompanyId!=null){
            String companyId = userCompanyId.get(0);
            List<TAccountVo> list = tAccountMapper.selectById(Integer.valueOf(id),Integer.parseInt(companyId));
            if(list.size()>0){
                vo = list.get(0);
        }
        }else{
            List<TAccountVo> list = tAccountMapper.selectById(Integer.valueOf(id),null);
            if(list.size()>0){
                vo = list.get(0);
            }
        }
        TLineGoodsUserRelMember gur = new TLineGoodsUserRelMember();
        gur.setAccountInfoId(vo.getId());
        gur.setEnable(false);

        List<String> userCompanyIdList = CurrentUser.getUserCompanyId();
        if (null != userCompanyIdList && userCompanyIdList.size() > 0){
            String companyId = userCompanyIdList.get(0);
            if (StringUtils.isNotEmpty(companyId)){
                gur.setCompanyId(Integer.valueOf(companyId));
            }
        }

        List<TLineGoodsUserRelVo> list = tLineGoodsUserRelMemberMapper.selectByModel(gur);
        //查询企业线路
        Set<GoodsSourceDTO> lines = new HashSet<>();
        for (TLineGoodsUserRelVo tLineGoodsUserRelVo : list) {
            /*TLineGoodsRel lineGoodsRel = new TLineGoodsRel();
            lineGoodsRel.setCompanyId(tLineGoodsUserRelVo.getCompanyId());
            ResultUtil goodsSourceByCompanyId = linkGoodsRelAPI.getGoodsSourceByCompanyId(lineGoodsRel);
            ArrayList lineGoods = (ArrayList) goodsSourceByCompanyId.getData();*/
            List<GoodsSourceDTO> goodsSourceDTOS = tLineGoodsRelToolMapper.selectDataByCompanyId(tLineGoodsUserRelVo.getCompanyId());

            lines.addAll(goodsSourceDTOS);
            tLineGoodsUserRelVo.setLineList(goodsSourceDTOS);
            tLineGoodsUserRelVo.setState(0);
        }
        vo.setLines(lines);
        vo.setList(list);
        /*if (StringUtils.isNotEmpty(type)) {
            TEndUserInfoSearchVO endUserInfo = new TEndUserInfoSearchVO();
            endUserInfo.setAccountNo(vo.getAccountNo());
            endUserInfo.setUserLogisticsRole(type);
            EndUserDTO endUserDTOS = tEndUserInfoMapper.selectEnduserByACoount(endUserInfo);
            if (null != endUserDTOS) {
                vo.setEnduserId(endUserDTOS.getEnduserId());
                vo.setEnduserAccountId(endUserDTOS.getEnduserAccountId());
                vo.setOrgName(endUserDTOS.getOrgName());
                vo.setUserLogisticsRole(endUserDTOS.getUserLogisticsRole());
                vo.setIdcard(endUserDTOS.getIdcard());
                vo.setIdcardValidUntil(endUserDTOS.getIdcardValidUntil());
                vo.setIdcardPhoto1(endUserDTOS.getIdcardPhoto1());
                vo.setIdcardPhoto2(endUserDTOS.getIdcardPhoto2());
                vo.setAuditStatus(endUserDTOS.getAuditStatus());
                vo.setAuditOpinion(endUserDTOS.getAuditOpinion());
                vo.setAuditTime(endUserDTOS.getAuditTime());
            }
        }
        if (!DictEnum.CTYPEAGENTPERSON.code.equals(type)
            && !DictEnum.CTYPEAGENTCOMPANY.code.equals(type)) {
            TLineGoodsUserRelMember gur = new TLineGoodsUserRelMember();
            gur.setAccountInfoId(vo.getId());
            gur.setEnable(false);

            List<String> userCompanyIdList = CurrentUser.getUserCompanyId();
            if (null != userCompanyIdList && userCompanyIdList.size() > 0){
                String companyId = userCompanyIdList.get(0);
                if (StringUtils.isNotEmpty(companyId)){
                    gur.setCompanyId(Integer.valueOf(companyId));
                }
            }

            List<TLineGoodsUserRelVo> list = tLineGoodsUserRelMemberMapper.selectByModel(gur);
            Set<TLineGoodsRel> lines = new HashSet<>();

            if (null != list && list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    ArrayList<String[]> lists = new ArrayList<>();
                    TLineGoodsUserRelVo tLineGoodsUserRelVo = list.get(i);
                    TOrderInfoVO tOrderInfoVO = new TOrderInfoVO();
                    tOrderInfoVO.setAccountId(Integer.parseInt(id));
                    tOrderInfoVO.setLineGoodsRelId(tLineGoodsUserRelVo.getLineGoodsRelId());
                    ResultUtil resultUtil = tOrderInfoAPI.selectByAccountIdAnrLineGoodsRelIdCountFeign(tOrderInfoVO);
                    if(null!=resultUtil && !"".equals(resultUtil)){
                        if(resultUtil.getCount()>0){
                            tLineGoodsUserRelVo.setIfFlag("1");
                        }else{
                            tLineGoodsUserRelVo.setIfFlag("0");
                        }
                    }else{
                        tLineGoodsUserRelVo.setIfFlag("0");
                    }
                    ArrayList<String[]> roleBtn = new ArrayList<>();
                    String roleCode = tLineGoodsUserRelVo.getRoleCode();
                    if (null != roleCode && roleCode.length() > 0) {
                        String[] roles = roleCode.split(",");
                        for (int k = 0; k < roles.length; k++) {
                            String rolecode = roles[k];
                            TLineGoodsUserRel lineGoodsUserRel = new TLineGoodsUserRel();
                            lineGoodsUserRel.setAccountInfoId(vo.getId());
                            lineGoodsUserRel.setLineGoodsRelId(tLineGoodsUserRelVo.getLineGoodsRelId());
                            lineGoodsUserRel.setCompanyId(lineGoodsUserRel.getCompanyId());
                            lineGoodsUserRel.setRoleCode(rolecode);
                            TLineGoodsUserRel tLineGoodsUserRels = tLineGoodsUserRelMemberMapper.selectUserLineButton(lineGoodsUserRel);
                            if (StringUtils.isNotEmpty(roleCode)) {
                                if (null != tLineGoodsUserRels.getAuthorizedButtons() && StringUtils.isNotEmpty(tLineGoodsUserRels.getAuthorizedButtons())){
                                    String[] btn = tLineGoodsUserRels.getAuthorizedButtons().split(",");
                                    for (int j = 0; j < btn.length; j++) {
                                        if (StringUtils.isNotEmpty(btn[j])) {
                                            String[] objects = new String[]{rolecode, btn[j]};
                                            lists.add(objects);
                                        }
                                    }
                                } else {
                                    String[] objects = new String[]{rolecode, "DEAFLUTBUTTON"};
                                    lists.add(objects);
                                }

                            }


                        }
                        tLineGoodsUserRelVo.setRoleCodes(lists);
                    }
                    System.out.println(roleBtn);
                    //查询企业线路
                    TLineGoodsRel lineGoodsRel = new TLineGoodsRel();
                    lineGoodsRel.setCompanyId(tLineGoodsUserRelVo.getCompanyId());
                    ResultUtil goodsSourceByCompanyId = linkGoodsRelAPI.getGoodsSourceByCompanyId(lineGoodsRel);
                    ArrayList lineGoods = (ArrayList) goodsSourceByCompanyId.getData();

                    lines.addAll(lineGoods);
                    tLineGoodsUserRelVo.setLineList(lineGoods);
                    tLineGoodsUserRelVo.setState(0);
                }
                vo.setList(list);
            }
            vo.setLines(lines);
        }*/
        return vo;
    }

    @Override
    public TAccountVo getById(String id, String type) {
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        TAccountVo vo = new  TAccountVo();
        if(userCompanyId!=null){
            String companyId = userCompanyId.get(0);
            List<TAccountVo> list = tAccountMapper.selectById(Integer.valueOf(id),Integer.parseInt(companyId));
            if(list.size()>0){
                vo = list.get(0);
            }
        }else{
            List<TAccountVo> list = tAccountMapper.selectById(Integer.valueOf(id),null);
            if(list.size()>0){
                vo = list.get(0);
            }
        }
        if (StringUtils.isNotEmpty(type)) {
            TEndUserInfoSearchVO endUserInfo = new TEndUserInfoSearchVO();
            endUserInfo.setAccountNo(vo.getAccountNo());
            endUserInfo.setUserLogisticsRole(type);
            EndUserDTO endUserDTOS = tEndUserInfoMapper.selectEnduserByACoount(endUserInfo);
            if (null != endUserDTOS) {
                vo.setEnduserId(endUserDTOS.getEnduserId());
                vo.setEnduserAccountId(endUserDTOS.getEnduserAccountId());
                vo.setOrgName(endUserDTOS.getOrgName());
                vo.setUserLogisticsRole(endUserDTOS.getUserLogisticsRole());
                vo.setIdcard(endUserDTOS.getIdcard());
                vo.setIdcardValidUntil(endUserDTOS.getIdcardValidUntil());
                vo.setIdcardPhoto1(endUserDTOS.getIdcardPhoto1());
                vo.setIdcardPhoto2(endUserDTOS.getIdcardPhoto2());
                vo.setAuditStatus(endUserDTOS.getAuditStatus());
                vo.setAuditOpinion(endUserDTOS.getAuditOpinion());
                vo.setAuditTime(endUserDTOS.getAuditTime());
            }
        }
        if (!DictEnum.CTYPEAGENTPERSON.code.equals(type)
                && !DictEnum.CTYPEAGENTCOMPANY.code.equals(type)) {
            TLineGoodsUserRelMember gur = new TLineGoodsUserRelMember();
            gur.setAccountInfoId(vo.getId());
            gur.setEnable(false);

            List<String> userCompanyIdList = CurrentUser.getUserCompanyId();
            if (null != userCompanyIdList && userCompanyIdList.size() > 0){
                String companyId = userCompanyIdList.get(0);
                if (StringUtils.isNotEmpty(companyId)){
                    gur.setCompanyId(Integer.valueOf(companyId));
                }
            }
            List<TLineGoodsUserRelVo> list = tLineGoodsUserRelMemberMapper.selectByModel(gur);
            Set<GoodsSourceDTO> lines = new HashSet<>();

            if (null != list && list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    ArrayList<String[]> lists = new ArrayList<>();
                    TLineGoodsUserRelVo tLineGoodsUserRelVo = list.get(i);
                    TOrderInfoVO tOrderInfoVO = new TOrderInfoVO();
                    tOrderInfoVO.setAccountId(Integer.parseInt(id));
                    tOrderInfoVO.setLineGoodsRelId(tLineGoodsUserRelVo.getLineGoodsRelId());
                    ResultUtil resultUtil = tOrderInfoAPI.selectByAccountIdAnrLineGoodsRelIdCountFeign(tOrderInfoVO);
                    if(null!=resultUtil && !"".equals(resultUtil)){
                        if(resultUtil.getCount()>0){
                            tLineGoodsUserRelVo.setIfFlag("1");
                        }else{
                            tLineGoodsUserRelVo.setIfFlag("0");
                        }
                    }else{
                        tLineGoodsUserRelVo.setIfFlag("0");
                    }
                    ArrayList<String[]> roleBtn = new ArrayList<>();
                    String roleCode = tLineGoodsUserRelVo.getRoleCode();
                    if (null != roleCode && roleCode.length() > 0) {
                        String[] roles = roleCode.split(",");
                        for (int k = 0; k < roles.length; k++) {
                            String rolecode = roles[k];
                            TLineGoodsUserRel lineGoodsUserRel = new TLineGoodsUserRel();
                            lineGoodsUserRel.setAccountInfoId(vo.getId());
                            lineGoodsUserRel.setLineGoodsRelId(tLineGoodsUserRelVo.getLineGoodsRelId());
                            lineGoodsUserRel.setCompanyId(lineGoodsUserRel.getCompanyId());
                            lineGoodsUserRel.setRoleCode(rolecode);
                            TLineGoodsUserRel tLineGoodsUserRels = tLineGoodsUserRelMemberMapper.selectUserLineButton(lineGoodsUserRel);
                            if (StringUtils.isNotEmpty(roleCode)) {
                                if (null != tLineGoodsUserRels.getAuthorizedButtons() && StringUtils.isNotEmpty(tLineGoodsUserRels.getAuthorizedButtons())){
                                    String[] btn = tLineGoodsUserRels.getAuthorizedButtons().split(",");
                                    for (int j = 0; j < btn.length; j++) {
                                        if (StringUtils.isNotEmpty(btn[j])) {
                                            String[] objects = new String[]{rolecode, btn[j]};
                                            lists.add(objects);
                                        }
                                    }
                                } else {
                                    String[] objects = new String[]{rolecode, "DEAFLUTBUTTON"};
                                    lists.add(objects);
                                }

                            }


                        }
                        tLineGoodsUserRelVo.setRoleCodes(lists);
                    }
                    System.out.println(roleBtn);
                    //查询企业线路
                    List<GoodsSourceDTO> goodsSourceDTOS = tLineGoodsRelToolMapper.selectDataByCompanyId(tLineGoodsUserRelVo.getCompanyId());

                    lines.addAll(goodsSourceDTOS);
                    tLineGoodsUserRelVo.setLineList(goodsSourceDTOS);
                    tLineGoodsUserRelVo.setState(0);
                }
                vo.setList(list);
            }
            vo.setLines(lines);
        }
        return vo;
    }

    /*
    分页显示员工关联的货源
     */
    @Override
    public ResultUtil getAccById(TAccountVo record) {
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        TAccountVo vo = new  TAccountVo();
        if(userCompanyId!=null){
            String companyId = userCompanyId.get(0);
            List<TAccountVo> list = tAccountMapper.selectById(record.getId(),Integer.parseInt(companyId));
            if(list.size()>0){
                vo = list.get(0);
            }
        }else{
            List<TAccountVo> list = tAccountMapper.selectById(record.getId(),null);
            if(list.size()>0){
                vo = list.get(0);
            }
        }
        if (StringUtils.isNotEmpty(record.getUsertype())) {
            TEndUserInfoSearchVO endUserInfo = new TEndUserInfoSearchVO();
            endUserInfo.setAccountNo(vo.getAccountNo());
            endUserInfo.setUserLogisticsRole(record.getUsertype());
            EndUserDTO endUserDTOS = tEndUserInfoMapper.selectEnduserByACoount(endUserInfo);
            if (null != endUserDTOS) {
                vo.setEnduserId(endUserDTOS.getEnduserId());
                vo.setEnduserAccountId(endUserDTOS.getEnduserAccountId());
                vo.setOrgName(endUserDTOS.getOrgName());
                vo.setUserLogisticsRole(endUserDTOS.getUserLogisticsRole());
                vo.setIdcard(endUserDTOS.getIdcard());
                vo.setIdcardValidUntil(endUserDTOS.getIdcardValidUntil());
                vo.setIdcardPhoto1(endUserDTOS.getIdcardPhoto1());
                vo.setIdcardPhoto2(endUserDTOS.getIdcardPhoto2());
                vo.setAuditStatus(endUserDTOS.getAuditStatus());
                vo.setAuditOpinion(endUserDTOS.getAuditOpinion());
                vo.setAuditTime(endUserDTOS.getAuditTime());
            }
        }
        if (!DictEnum.CTYPEAGENTPERSON.code.equals(record.getUsertype())
                && !DictEnum.CTYPEAGENTCOMPANY.code.equals(record.getUsertype())) {
            TLineGoodsUserRelMember gur = new TLineGoodsUserRelMember();
            gur.setAccountInfoId(vo.getId());
            gur.setEnable(false);

            List<String> userCompanyIdList = CurrentUser.getUserCompanyId();
            if (null != userCompanyIdList && userCompanyIdList.size() > 0){
                String companyId = userCompanyIdList.get(0);
                if (StringUtils.isNotEmpty(companyId)){
                    gur.setCompanyId(Integer.valueOf(companyId));
                }
            }
            //员工管理货源信息（分页显示）
            Page<TLineGoodsUserRelVo> objectPage = PageHelper.startPage(record.getPage(), record.getSize());
            List<TLineGoodsUserRelVo> list = tLineGoodsUserRelMemberMapper.getDataByParam(gur);
            //Set<TLineGoodsRel> lines = new HashSet<>();

            if (null != list && list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    ArrayList<String[]> lists = new ArrayList<>();
                    TLineGoodsUserRelVo tLineGoodsUserRelVo = list.get(i);
                    TOrderInfoVO tOrderInfoVO = new TOrderInfoVO();
                    tOrderInfoVO.setAccountId(record.getId());
                    tOrderInfoVO.setLineGoodsRelId(tLineGoodsUserRelVo.getLineGoodsRelId());
                    Long aLong = memberOrderInfoMapper.selectByAccountIdAndLineId(tOrderInfoVO);
                    if(null!=aLong && aLong>0){
                        tLineGoodsUserRelVo.setIfFlag("1");
                    }else{
                        tLineGoodsUserRelVo.setIfFlag("0");
                    }
                    //ArrayList<String[]> roleBtn = new ArrayList<>();
                    String roleCode = tLineGoodsUserRelVo.getRoleValue();
                    if (null != roleCode && roleCode.length() > 0) {
                        String[] roles = roleCode.split(",");
                        for (int k = 0; k < roles.length; k++) {
                            String rolecode = roles[k];
                            TLineGoodsUserRel lineGoodsUserRel = new TLineGoodsUserRel();
                            lineGoodsUserRel.setAccountInfoId(vo.getId());
                            lineGoodsUserRel.setLineGoodsRelId(tLineGoodsUserRelVo.getLineGoodsRelId());
                            lineGoodsUserRel.setCompanyId(lineGoodsUserRel.getCompanyId());
                            lineGoodsUserRel.setRoleCode(rolecode);
                            TLineGoodsUserRel tLineGoodsUserRels = tLineGoodsUserRelMemberMapper.selectUserLineButton(lineGoodsUserRel);
                            if (StringUtils.isNotEmpty(roleCode)) {
                                if (null != tLineGoodsUserRels.getAuthorizedButtons() && StringUtils.isNotEmpty(tLineGoodsUserRels.getAuthorizedButtons())){
                                    String[] btn = tLineGoodsUserRels.getAuthorizedButtons().split(",");
                                    for (int j = 0; j < btn.length; j++) {
                                        if (StringUtils.isNotEmpty(btn[j])) {
                                            String[] objects = new String[]{rolecode, btn[j]};
                                            lists.add(objects);
                                        }
                                    }
                                } else {
                                    String[] objects = new String[]{rolecode, "DEAFLUTBUTTON"};
                                    lists.add(objects);
                                }

                            }


                        }
                        tLineGoodsUserRelVo.setRoleCodes(lists);
                    }
                    //System.out.println(roleBtn);
                    //查询企业线路
                    /*TLineGoodsRel lineGoodsRel = new TLineGoodsRel();
                    lineGoodsRel.setCompanyId(tLineGoodsUserRelVo.getCompanyId());
                    ResultUtil goodsSourceByCompanyId = linkGoodsRelAPI.getGoodsSourceByCompanyId(lineGoodsRel);
                    ArrayList lineGoods = (ArrayList) goodsSourceByCompanyId.getData();

                    lines.addAll(lineGoods);
                    tLineGoodsUserRelVo.setLineList(lineGoods);*/
                    tLineGoodsUserRelVo.setState(0);
                }
            }
            vo.setList(objectPage);
            //vo.setLines(lines);
            vo.setPage(record.getPage());
            vo.setSize(record.getSize());
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), vo, objectPage.getTotal());
        }
        return null;
    }

    //新增员工
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil PCsave(TAccountVo record) {
        Integer companyIdInteger = null;
        Boolean b = isEnterpriseAdministrator(record.getAccountNo());
        if(b){
            return ResultUtil.error("新建员工失败，该手机号为企业管理员账号。");
        }
        TAccount account = new TAccount();
        account.setAccountNo(record.getAccountNo());
        List<TAccount> accounts = tAccountMapper.selectRepeatAccountNo(account);
        if (null != accounts && accounts.size() > 0) {
            return ResultUtil.error("账号已存在");
        }
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        if (null != userCompanyId && userCompanyId.size() > 0){
            String companyId = userCompanyId.get(0);
            companyIdInteger = Integer.valueOf(companyId);
        }else{
            companyIdInteger = record.getCompanyId();
        }

        //向员工发送随机生成的密码
        /*String s = generatePassword();//生成密码
        log.info("随机生成的8位密码："+s);*/
        //新增系统用户 t_sys_user
        TSysUserForAddVO userForAddVO = new TSysUserForAddVO();
        userForAddVO.setUsername(record.getAccountNo());
        userForAddVO.setAccountNo(record.getAccountNo());
        userForAddVO.setNickname(record.getNickname());
        userForAddVO.setRolesCode(null);
        //userForAddVO.setPassword(s);
        userForAddVO.setPassword("123456");
        userForAddVO.setUserType("BD");
        ResultUtil saveUser = systemAPI.saveUserAndPCRole(userForAddVO);
        if (null != saveUser.getCode() && saveUser.getCode().equals("error")){
            throw new RuntimeException("账号已存在");
        }
        LinkedHashMap userSave = (LinkedHashMap) saveUser.getData();
        String userId = String.valueOf(userSave.get("id"));

        //给新建的员工手机号发送短信
        /*try {
            SmsReq req = new SmsReq();
            req.setMobiles(account.getAccountNo());
            req.setType("NEWEMPLOYEE");
            req.setDefaultPassword(s);
            smsApi.sendSmsType(req);
        }catch (Exception e){
            e.printStackTrace();
        }*/

        //新增员工 t_account
        TAccount ac = new TAccount();
        BeanUtil.copyProperties(record, ac);
        String pwd = DigestUtils.md5Hex("123456");
        ac.setPassword(pwd);
        ac.setUserId(Integer.valueOf(userId));
        ac.setNickname(record.getNickname());
        //账号类型
        ac.setAcctype("PhoneNo");
        //用户类型
        ac.setUsertype("BD");
        if (null == companyIdInteger) {
            //数据来源:运营PC
            ac.setDatafrom("OPPC");
        } else {
            //数据来源:企业PC
            ac.setDatafrom("COMPC");
        }
        //注册节点：账号申请完成
        ac.setRegnode("ACCAPCOM");
        ac.setEnable(false);
        tAccountMapper.insertSelective(ac);

        //企业账号关系表 新增t_company_account 表数据
        TCompanyAccount companyAccount = new TCompanyAccount();
        companyAccount.setAccountId(ac.getId());
        companyAccount.setCompanyId(companyIdInteger);
        companyAccount.setRealName(record.getNickname());
        companyAccount.setPhone(record.getAccountNo());
        companyAccount.setRealPosition(record.getRealPosition());
        companyAccount.setIdcard(record.getIdcard());
        companyAccount.setEnable(false);
        companyAccountMapper.insertSelective(companyAccount);

        //保存用户信息表 add by zhangjiji ******** t_user_info
        TUserInfo tUserInfo = new TUserInfo();
        tUserInfo.setAccountId(Integer.valueOf(userId));
        tUserInfo.setStatus(DictEnum.ACCOUNTENABLE.code);
        tUserInfo.setPasswordErrorCount(0);
        tUserInfo.setEnable(false);
        systemAPI.insertUserInfo(tUserInfo);
        return ResultUtil.ok(ac.getId());
    }

    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil save(TAccountVo record) {
        Boolean b = isEnterpriseAdministrator(record.getAccountNo());
        if(b){
            return ResultUtil.error("新建员工失败，该手机号为企业管理员账号。");
        }
        List<TLineGoodsUserRelVo> lineGoodsUserRelVos = record.getList();
        Integer companyIdInteger = null;
        List<String> userCompanyId = CurrentUser.getUserCompanyId();

        TAccount account = new TAccount();
        account.setAccountNo(record.getAccountNo());
        List<TAccount> accounts = tAccountMapper.selectRepeatAccountNo(account);
        if (null != accounts && accounts.size() > 0) {
            return ResultUtil.error("账号已存在");
        }

        //新增系统用户
        TSysUserForAddVO userForAddVO = new TSysUserForAddVO();
        userForAddVO.setUsername(record.getAccountNo());
        userForAddVO.setAccountNo(record.getAccountNo());
        userForAddVO.setNickname(record.getNickname());
        userForAddVO.setUserType("BD");
        HashSet<String> rolesCode = new HashSet<>();
        if (null != lineGoodsUserRelVos && lineGoodsUserRelVos.size() > 0) {
            for (TLineGoodsUserRelVo lineGoodsUserRelVo : lineGoodsUserRelVos){
                String[][] lineRoleCode = lineGoodsUserRelVo.getLineRoleCode();
                for (String[] roles: lineRoleCode){
                    rolesCode.add(roles[0]);
                }
            }
            userForAddVO.setRolesCode(rolesCode);
        } else {
            userForAddVO.setRolesCode(null);
        }
        userForAddVO.setRolesCode(rolesCode);
        ResultUtil saveUser = systemAPI.saveUserAndPCRole(userForAddVO);
        if (null != saveUser.getCode() && saveUser.getCode().equals("error")){
            throw new RuntimeException("账号已存在");
        }
        LinkedHashMap userSave = (LinkedHashMap) saveUser.getData();
        String userId = String.valueOf(userSave.get("id"));

        //添加默认清结算管理员账号
//        TSysRole tSysRole =  systemAPI.selectByParam("QJS");
//        if(tSysRole!=null){
//            TUserRole tUserRole = new TUserRole();
//            tUserRole.setUserId(Integer.parseInt(userId));
//            tUserRole.setRoleId(tSysRole.getId());
//            systemAPI.saveUserRole(tUserRole);
//        }

        //新增员工
        TAccount ac = new TAccount();
        BeanUtil.copyProperties(record, ac);
        String pwd = DigestUtils.md5Hex("123456");
        ac.setPassword(pwd);
        ac.setUserId(Integer.valueOf(userId));
        ac.setNickname(record.getNickname());
        //账号类型
        ac.setAcctype("PhoneNo");
        //用户类型
        ac.setUsertype("BD");
        if (null == companyIdInteger) {
            //数据来源:运营PC
            ac.setDatafrom("OPPC");
        } else {
            //数据来源:企业PC
            ac.setDatafrom("COMPC");
        }
        //注册节点：账号申请完成
        ac.setRegnode("ACCAPCOM");
        ac.setEnable(false);
        tAccountMapper.insertSelective(ac);

        //保存用户信息表 add by zhangjiji ********
        TUserInfo tUserInfo = new TUserInfo();
        tUserInfo.setAccountId(Integer.valueOf(userId));
        tUserInfo.setStatus(DictEnum.ACCOUNTENABLE.code);
        tUserInfo.setPasswordErrorCount(0);
        tUserInfo.setEnable(false);
        systemAPI.insertUserInfo(tUserInfo);

        if (null != userCompanyId && userCompanyId.size() > 0){
            String companyId = userCompanyId.get(0);
            companyIdInteger = Integer.valueOf(companyId);
            // 企业账号关系表
            TCompanyAccount companyAccount = new TCompanyAccount();
            companyAccount.setAccountId(ac.getId());
            companyAccount.setCompanyId(companyIdInteger);
            companyAccount.setRealName(record.getNickname());
            companyAccount.setPhone(record.getAccountNo());
            companyAccount.setRealPosition(record.getRealPosition());
            companyAccount.setIdcard(record.getIdcard());
            companyAccount.setEnable(false);
            companyAccountMapper.insertSelective(companyAccount);
        }
        HashMap[] lineRoles = record.getLineRoles();
        if (null != lineGoodsUserRelVos && lineGoodsUserRelVos.size() > 0) {
            //创建线路货物员工关系、线路货物员工角色
            createLineUserAndRole(ac, lineGoodsUserRelVos, lineRoles, record, "EMPLO");
        }
        return ResultUtil.ok();
    }

    //新增员工关联的货源等信息
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil saveSourceRelevanceAcc(TAccountVo record) {
        TAccount tAccount = tAccountMapper.selectByPrimaryKey(record.getId());
        TSysUserForAddVO userForAddVO = new TSysUserForAddVO();
        userForAddVO.setUserId(tAccount.getUserId());
        TLineGoodsUserRelVo tLineGoodsUserRelVo = record.getGoodsUserRelVo();
        if (null != tLineGoodsUserRelVo ) {
            String[][] lineRoleCodeNew = tLineGoodsUserRelVo.getLineRoleCodeNew();
            List<String> list = new ArrayList<>();
            for (String[] strings : lineRoleCodeNew) {
                list.add(strings[0]);
            }
            List<String> lineRoleCode = new ArrayList<>();
            for (String s : list) {
                if (!lineRoleCode.contains(s)) {
                    lineRoleCode.add(s);
                }
            }
            userForAddVO.setRolesCodeNew(lineRoleCode);
            systemAPI.saveAccountUserRole(userForAddVO);//新增t_user_role 表数据
        }else {
            return ResultUtil.error("未获取到新增的货源数据");
        }
        //创建线路货物员工关系、线路货物员工角色
        return createLineUserAndRoleNew(tAccount, tLineGoodsUserRelVo);
    }

    //修改员工关联的货源等信息
    @Override
    @Transactional
    public ResultUtil updateSourceRelevanceAcc(TAccountVo record) {
        TAccount ac = tAccountMapper.selectByPrimaryKey(record.getId());
        // 删除员工线路关系
        TLineGoodsUserRelMember lineGoodsUserRelMember = new TLineGoodsUserRelMember();
        lineGoodsUserRelMember.setAccountInfoId(record.getId());
        lineGoodsUserRelMember.setLineGoodsRelId(record.getGoodsUserRelVo().getLineGoodsRelId());
        tLineGoodsUserRelMemberMapper.updateUserLineRoleEnbaleByAccountId(lineGoodsUserRelMember);

        // 删除员工所有的线路权限
        TLineUserRolePro lineUserRolePro = new TLineUserRolePro();
        lineUserRolePro.setAccountId(record.getId());
        lineUserRolePro.setEnable(true);
        tLineUserRoleProMapper.updateByRecord(lineUserRolePro);

        TLineGoodsUserRelVo goodsUserRelVo = record.getGoodsUserRelVo();
        goodsUserRelVo.setAccountInfoId(ac.getId());
        goodsUserRelVo.setCurrentAccountNo(ac.getAccountNo());
        goodsUserRelVo.setCurrentAccountName(ac.getNickname());
        goodsUserRelVo.setCurrentAccountPhone(ac.getAccountNo());
        TLineGoodsUserRelMember lineGoodsUserRel = new TLineGoodsUserRelMember();
        BeanUtil.copyProperties(goodsUserRelVo, lineGoodsUserRel);
        //角色线路获取员工角色和按钮(key:role, value:buttons)
        saveLineGoodsUserRelNew(goodsUserRelVo, lineGoodsUserRel);

        //查询线路货物员工关系，获取线路货物员工角色
        List<TLineGoodsUserRel> list = tLineGoodsUserRelMemberMapper.getRelCodeByAccountId(record.getId());
        TSysUserForAddVO userForAddVO = new TSysUserForAddVO();
        userForAddVO.setUserId(ac.getUserId());
        List<String> roleCodes = new ArrayList<>();
        for (TLineGoodsUserRel tLineGoodsUserRel : list) {
            roleCodes.add(tLineGoodsUserRel.getRoleCode());
        }
        if (!roleCodes.isEmpty()) {
            userForAddVO.setRolesCodeNew(roleCodes);
            systemAPI.updateAccountUserRole(userForAddVO);
        }

        return ResultUtil.ok();
    }

    //删除员工关联的货源等信息，将enable修改为1
    @Override
    @Transactional
    public ResultUtil deleteSourceRelevanceAcc(List<Map<String,Object>> mapList) {
        Integer accountId = null;
        for (Map<String,Object> map : mapList) {
            accountId = Integer.valueOf(map.get("accountId").toString());
            TLineGoodsUserRelMember goodsUserRel = new TLineGoodsUserRelMember();
            goodsUserRel.setLineGoodsRelId(Integer.parseInt(map.get("lineGoodsRelId").toString()));
            goodsUserRel.setAccountInfoId(Integer.valueOf(map.get("accountId").toString()));
            tLineGoodsUserRelMemberMapper.updateUserLineRoleEnbaleByAccountId(goodsUserRel);
            TLineUserRolePro lineUserRolePro = new TLineUserRolePro();
            lineUserRolePro.setAccountId(Integer.valueOf(map.get("accountId").toString()));
            lineUserRolePro.setEnable(true);
            tLineUserRoleProMapper.updateByRecord(lineUserRolePro);
            //查询线路货物员工关系，获取线路货物员工角色
            List<TLineGoodsUserRel> list = tLineGoodsUserRelMemberMapper.getRelCodeByAccountId(Integer.valueOf(map.get("accountId").toString()));
            for (TLineGoodsUserRel tLineGoodsUserRel : list) {
                DicCatItemDTO dataByItemCode = dicCatItemAPI.getDataByItemCode(tLineGoodsUserRel.getRoleCode());
                if(null != dataByItemCode){
                    TLineUserRolePro userRolePro = new TLineUserRolePro();
                    userRolePro.setAccountId(Integer.valueOf(map.get("accountId").toString()));
                    userRolePro.setRoleCode(tLineGoodsUserRel.getRoleCode());
                    userRolePro.setRoleName(dataByItemCode.getItemValue());
                    userRolePro.setPerms(dataByItemCode.getValueContent());
                    userRolePro.setButtons(dataByItemCode.getButtonContent());
                    tLineUserRoleProMapper.insertSelective(userRolePro);
                }
            }
        }
        if (null != accountId) {
            TAccount tAccount = tAccountMapper.selectByPrimaryKey(accountId);
            if (null != tAccount) {
                TSysUserForAddVO userForAddVO = new TSysUserForAddVO();
                userForAddVO.setUserId(tAccount.getUserId());
                List<String> roleCodes = new ArrayList<>();
                List<TLineGoodsUserRel> list = tLineGoodsUserRelMemberMapper.getRelCodeByAccountId(accountId);
                for (TLineGoodsUserRel tLineGoodsUserRel : list) {
                    roleCodes.add(tLineGoodsUserRel.getRoleCode());
                }
                if (!roleCodes.isEmpty()) {
                    userForAddVO.setRolesCodeNew(roleCodes);
                    systemAPI.updateAccountUserRole(userForAddVO);
                }
            }
        }
        List<TLineGoodsUserRel> list = tLineGoodsUserRelMemberMapper.getRelCodeByAccountId(accountId);
        List<TCompanyAccount> companyAccounts = companyAccountMapper.getByAccountId(accountId);
        Set<Integer> companyIds = new HashSet<>();
        list.forEach((line) -> {
            companyIds.add(line.getCompanyId());
        });
        for (TCompanyAccount companyAccount : companyAccounts) {
            List<Integer> collect1 = companyIds.stream().filter((companyId) -> companyAccount.getCompanyId().equals(companyId)).collect(Collectors.toList());
            if (collect1.isEmpty()) {
                companyAccount.setEnable(true);
                companyAccountMapper.updateByPrimaryKeySelective(companyAccount);
            }
        }

        return ResultUtil.ok("删除成功");
    }

    @Override
    public ResultUtil selectResourceDesc(TLineGoodsUserRelMember record) {
        TLineGoodsUserRelVo tLineGoodsUserRelVo = tLineGoodsUserRelMemberMapper.selectResourceDesc(record);
        return ResultUtil.ok(tLineGoodsUserRelVo);
    }

    @Override
    public TLineGoodsUserRel selectUserLineButton(TLineGoodsUserRel userRel) {
        return tLineGoodsUserRelMemberMapper.selectUserLineButton(userRel);
    }

    private Boolean isEnterpriseAdministrator(String phone){
        Map<String, String> exists =  tAccountMapper.isEnterpriseAdministrator(phone);
        if(null != exists && !exists.isEmpty()){
           return true;
        }
        return false;
    }

    /**
     * 保存经纪人
     *
     * @param record
     * @return
     */
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil saveManager(TAccountVo record) {
        Integer companyId1 = record.getCompanyId();
        TAccount account = new TAccount();
        account.setAccountNo(record.getAccountNo());
        List<TAccount> accounts = tAccountMapper.selectRepeatAccountNo(account);
        if (null != accounts && accounts.size() > 0) {
            return ResultUtil.error("账号已存在");
        }
        //TODO 新增系统用户
        TSysUser user = new TSysUser();
        user.setUsername(record.getAccountNo());
        user.setAccountNo(record.getAccountNo());
        user.setNickname(record.getNickname());
        String pwd = DigestUtils.md5Hex("123456");
        user.setIfPasswordSecurity(false);
        user.setPassword(pwd);
        user.setEnable(false);
        ResultUtil saveUser = systemAPI.saveUser(user);
        Integer userId = null;
        if (null != saveUser.getCode()){
            if (saveUser.getCode().equals("error")){
                if (null != saveUser.getMsg()){
                    return ResultUtil.error(saveUser.getMsg());
                } else {
                    return ResultUtil.error("添加失败");
                }
            } else if (saveUser.getCode().equals("success")){
                Object data = saveUser.getData();
                if (null != data){
                    LinkedHashMap sysUser = (LinkedHashMap) data;
                    userId = (Integer) sysUser.get("id");
                }
            }
        }

        TSysRole tSysRole =  systemAPI.selectByParam("YWB");
        if(tSysRole!=null){
            TUserRole tUserRole = new TUserRole();
            tUserRole.setUserId(userId);
            tUserRole.setRoleId(tSysRole.getId());
            systemAPI.saveUserRole(tUserRole);
        }

        //TODO 账户表
        TAccount ac = new TAccount();
        BeanUtil.copyProperties(record, ac);
        ac.setPassword(pwd);
        ac.setUserId(userId);
        ac.setNickname(record.getNickname());
        //账号类型
        ac.setAcctype("PhoneNo");
        //用户类型
        ac.setUsertype("CD");
        //TODO 改成OPPC(运营PC)
        //数据来源:企业PC
        if (null != CurrentUser.getUserCompanyId()&& CurrentUser.getUserCompanyId().size() >= 0) {
            ac.setDatafrom("COMPC");
        }else {
            ac.setDatafrom("OPCC");
        }
        //注册节点：账号申请完成
        ac.setRegnode("ACCAPCOM");
        ac.setEnable(false);
        tAccountMapper.insertSelective(ac);

        //保存用户信息表 add by zhangjiji ********
        TUserInfo tUserInfo = new TUserInfo();
        tUserInfo.setAccountId(userId);
        tUserInfo.setStatus(DictEnum.ACCOUNTENABLE.code);
        tUserInfo.setPasswordErrorCount(0);
        tUserInfo.setEnable(false);
        systemAPI.insertUserInfo(tUserInfo);

        //创建C端用户
        TEndUserInfo enduerInfo = createEnduerInfo(record, "CTYPEMANAGER");
        //创建C端用户与账户表关系
        TEnduserAccount enduerAccount = createEnduerAccount(record, enduerInfo.getId(), ac.getId());

        //TODO 企业账号关系改成传值---------------------------
        //企业账号关系表
        TCompanyAccount companyAccount = new TCompanyAccount();
        companyAccount.setAccountId(ac.getId());
        Integer companyIdInteger = null;
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        if (record.getCompanyId()!=null){
            companyIdInteger=record.getCompanyId();
        }else if (null != userCompanyId && userCompanyId.size() > 0) {
            String companyId = userCompanyId.get(0);
            companyIdInteger = Integer.valueOf(companyId);
        }
        companyAccount.setRealName(record.getNickname());
        companyAccount.setPhone(record.getAccountNo());
        companyAccount.setRealPosition(record.getRealPosition());
        companyAccount.setIdcard(record.getIdcard());
        companyAccount.setCompanyId(companyIdInteger);
        //查询用户是否和企业已有关系，如果没有创建
        int count = companyAccountMapper.selectAccountCompany(companyAccount);
        if (count == 0) {
            companyAccount.setEnable(false);
            companyAccountMapper.insertSelective(companyAccount);
        }

        /*
            //企业账号关系表
            TCompanyAccount companyAccount = new TCompanyAccount();
            companyAccount.setAccountId(ac.getId());
            Integer companyIdInteger = null;
            List<String> userCompanyId = CurrentUser.getUserCompanyId();
            if (null != userCompanyId && userCompanyId.size() > 0) {
                String companyId = userCompanyId.get(0);
                companyIdInteger = Integer.valueOf(companyId);
            }
            companyAccount.setRealName(record.getNickname());
            companyAccount.setPhone(record.getAccountNo());
            companyAccount.setRealPosition(record.getRealPosition());
            companyAccount.setIdcard(record.getIdcard());
            companyAccount.setCompanyId(companyIdInteger);
            //查询用户是否和企业已有关系，如果没有创建
            int count = companyAccountMapper.selectAccountCompany(companyAccount);
            if (count == 0) {
                companyAccount.setEnable(false);
                companyAccountMapper.insertSelective(companyAccount);
            }
            //TODO 企业账号关系改成传值---------------------------

*/


        HashMap[] lineRoles = record.getLineRoles();
        List<TLineGoodsUserRelVo> lineGoodsUserRelVos = record.getList();
        //创建线路货物员工关系、线路货物员工角色
        record.setEnduserId(enduerInfo.getId());
        createLineUserAndRole(ac, lineGoodsUserRelVos, lineRoles,record, "CTYPEMANAGER");
        //TODO 创建信息(经纪人)站与企业的关系
        //先判断是否已经存在
        TCompanyStationRel companyStationRel = new TCompanyStationRel();
        companyStationRel.setCompanyId(companyIdInteger);
        //信息站id修改为C端用户表id update by zhangjiji 2019.6.4
        companyStationRel.setEndUserStationId(enduerInfo.getId());
        //添加是否删除：默认为false update by zhangjiji 2019.6.4
        companyStationRel.setEnable(false);
        List<TCompanyStationRel> tCompanyStationRels = companyStationRelMapper.selectCompanyStationRel(companyStationRel);
        if (null == tCompanyStationRels || tCompanyStationRels.size() == 0){
            companyStationRelMapper.insertSelective(companyStationRel);
        }
        return ResultUtil.ok();
    }

    //修改
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil update(TAccountVo record) {
        redisUtil.del("sysUserId-" + record.getUserId());
        Integer companyIdInteger = null;
        List<String> userCompanyId = CurrentUser.getUserCompanyId();

        //修改系统用户
        TAccount account = new TAccount();
        account.setAccountNo(record.getAccountNo());
        account.setId(record.getId());
        List<TAccount> accounts = tAccountMapper.selectRepeatAccountNo(account);
        if (null != accounts && accounts.size() > 0) {
            return ResultUtil.error("账号已存在");
        }
        //如果姓名或账号修改，则修改对应系统用户信息，否则不修改
        TAccount selectAccount = tAccountMapper.selectByPrimaryKey(record.getId());

        //修改员工
        TAccount ac = new TAccount();
        ac.setAccountNo(record.getAccountNo());
        ac.setNickname(record.getNickname());
        ac.setId(record.getId());
        ac.setUpdateUser(record.getUpdateUser());
        ac.setUpdateTime(record.getUpdateTime());
        tAccountMapper.updateByPrimaryKeySelective(ac);

        if (null != userCompanyId && userCompanyId.size() > 0) {
            String companyId = userCompanyId.get(0);
            companyIdInteger = Integer.valueOf(companyId);
            // 企业账号关系表
            TCompanyAccount tCompanyAccount =  companyAccountMapper.selectByAccountId(ac.getId(),companyIdInteger);
            if(tCompanyAccount!=null &&!"".equals(tCompanyAccount)){
                tCompanyAccount.setRealName(record.getNickname());
                tCompanyAccount.setPhone(record.getAccountNo());
                tCompanyAccount.setRealPosition(record.getRealPosition());
                tCompanyAccount.setIdcard(record.getIdcard());
                companyAccountMapper.updateByPrimaryKeySelective(tCompanyAccount);
            }
        }else{
            for (TLineGoodsUserRelVo lineGoodsUserRelVo : record.getList()) {
                TCompanyAccount tCompanyAccount =  companyAccountMapper.selectByAccountId(ac.getId(),lineGoodsUserRelVo.getCompanyId());
                if(tCompanyAccount!=null &&!"".equals(tCompanyAccount)){
                    tCompanyAccount.setRealName(record.getNickname());
                    tCompanyAccount.setPhone(record.getAccountNo());
                    tCompanyAccount.setRealPosition(record.getRealPosition());
                    tCompanyAccount.setIdcard(record.getIdcard());
                    companyAccountMapper.updateByPrimaryKeySelective(tCompanyAccount);
                }
            }
        }
        if (null != record.getUserLogisticsRole() & StringUtils.isNotEmpty(record.getUserLogisticsRole())) {
            //修改C端用户表
            TEndUserInfo endUserInfo = new TEndUserInfo();
            endUserInfo.setId(record.getEnduserId());
            endUserInfo.setRealName(record.getNickname());
            endUserInfo.setOrgName(record.getOrgName());
            endUserInfo.setIdcard(record.getIdcard());
            endUserInfo.setPhone(record.getAccountNo());
            endUserInfo.setIdcardValidUntil(record.getIdcardValidUntil());
            endUserInfo.setIdcardPhoto1(record.getIdcardPhoto1());
            endUserInfo.setIdcardPhoto2(record.getIdcardPhoto2());
            tEndUserInfoMapper.updateByPrimaryKeySelective(endUserInfo);

            //修改C端用户账号表
            TEnduserAccount enduserAccount = new TEnduserAccount();
            enduserAccount.setId(record.getEnduserAccountId());
            enduserAccount.setRealName(record.getNickname());
            enduserAccount.setIdcard(record.getIdcard());
            enduserAccount.setPhone(record.getAccountNo());
            tEnduserAccountMapper.updateByPrimaryKeySelective(enduserAccount);
        }
        //查询员工线路角色(根据线路分组)
        List<TLineGoodsUserRelMember> userAllLineRoleGroupByLine = tLineGoodsUserRelMemberMapper.selectUserAllLineRoleGroupByLine(record.getId());

        //根据删除、新增修改
        //所有线路角色信息(key:role, value:buttons)
        HashMap[] lineRoles = record.getLineRoles();
        //根据删除、新增修改
        for (TLineGoodsUserRelVo lineGoodsUserRelVo : record.getList()) {
            //当前线路员工的角色
            String gotRole = "";
            for (TLineGoodsUserRelMember map : userAllLineRoleGroupByLine) {
                Integer lineGoodsRelId = map.getLineGoodsRelId();
                if (lineGoodsUserRelVo.getLineGoodsRelId().equals(lineGoodsRelId)) {
                    gotRole = map.getRoleCode();
                }
            }
            //修改
            if (lineGoodsUserRelVo.getState() == 0) {
                //员工现有线路角色
                TLineGoodsUserRelMember member = new TLineGoodsUserRelMember();
                member.setLineGoodsRelId(lineGoodsUserRelVo.getLineGoodsRelId());
                member.setAccountInfoId(record.getId());
                List<String> getRoleList = new ArrayList<>();
                for (String mem : gotRole.split(",")) {
                    getRoleList.add(mem);
                }
                //修改后的角色线路获取员工角色和按钮(key:role, value:buttons)
                ArrayList<String[]> roleCodes = lineGoodsUserRelVo.getRoleCodes();
                Map<String, ArrayList<String>> modRoleButton = getModRoleButton(roleCodes);
                //页面修改后的线路角色
                ArrayList<String> modedRole = new ArrayList<>();
                for (Map.Entry map : modRoleButton.entrySet()) {
                    String roleMap = map.getKey().toString();
                    modedRole.add(roleMap);
                }
                //修改过的角色
                List<String> modRole = new ArrayList<>();
                for (String hasModRole: modedRole){
                    if (getRoleList.contains(hasModRole)){
                        modRole.add(hasModRole);
                    }
                }
                //处理修改的角色
                dealwithModRole(ac.getId(), lineGoodsUserRelVo.getLineGoodsRelId(), modRoleButton, modRole,
                        lineGoodsUserRelVo.getIfPrincipal(), lineGoodsUserRelVo.getCompanyId());

                //删除的角色
                List<String> deleteRole = new ArrayList<>();
                for (String role : getRoleList) {
                    if (!modedRole.contains(role)) {
                        deleteRole.add(role);
                    }
                }
                //处理删除的角色
                dealwithDeleteRole(record.getId(), lineGoodsUserRelVo.getLineGoodsRelId(), deleteRole);
                //查询其他线路的角色:如果其他线路不包含删除的角色，则删除；包含则不删除
                List<TLineGoodsUserRelMember> otherLineRole = tLineGoodsUserRelMemberMapper.selectUserOtherLineRole(member);
                HashSet<String> roles = new HashSet<>();
                for (TLineGoodsUserRelMember lineGoodsUserRelMember : otherLineRole) {
                    roles.add(lineGoodsUserRelMember.getRoleCode());
                }
                for (String deleteRoleCode : deleteRole) {
                    if (!roles.contains(deleteRoleCode)) {
                        tLineUserRoleProMapper.updateUserRole(record.getId(), deleteRoleCode);
                    }
                }

                //新增的角色
                List<String> newRole = new ArrayList<>();
                for (String role : modedRole) {
                    if (!gotRole.contains(role)) {
                        newRole.add(role);
                    }
                }
                //处理新增角色
                dealwithNewRole(ac, lineRoles, lineGoodsUserRelVo, modRoleButton, newRole, record);
            }
            //删除
            if (lineGoodsUserRelVo.getState() == 1) {
                String roleCode = lineGoodsUserRelVo.getRoleCode();
                String[] roleCodes = roleCode.split(",");
                TLineUserRolePro lineUserRolePro = new TLineUserRolePro();
                lineUserRolePro.setId(record.getId());
                lineUserRolePro.setEnable(false);
                for (String code : roleCodes) {
                    lineUserRolePro.setRoleCode(code);
                    List<TLineUserRolePro> accountList = tLineGoodsUserRelMemberMapper.selectRoleByAccountId(lineUserRolePro);
                    //如果员工只在这条货源有这个角色，则删除；否则不删除，保留当前角色。
                    if (null != accountList && accountList.size() == 1) {
                        TLineUserRolePro userRolePro = new TLineUserRolePro();
                        userRolePro.setAccountId(ac.getId());
                        userRolePro.setEnable(true);
                        tLineUserRoleProMapper.updateByRecord(userRolePro);
                    }
                }

                TLineGoodsUserRelMember lineGoodsUserRel = new TLineGoodsUserRelMember();
                lineGoodsUserRel.setLineGoodsRelId(lineGoodsUserRelVo.getLineGoodsRelId());
                lineGoodsUserRel.setAccountInfoId(record.getId());
                lineGoodsUserRel.setEnable(true);
                tLineGoodsUserRelMemberMapper.updateRecord(lineGoodsUserRel);
                //查询员工在这个企业是否还有其他的线路角色，如果没有，则删除企业账号关系信息
                TLineGoodsUserRelMember member = new TLineGoodsUserRelMember();
                member.setAccountInfoId(record.getId());
                member.setCompanyId(lineGoodsUserRelVo.getCompanyId());
                List<String> members = tLineGoodsUserRelMemberMapper.selectUserLineRoleForCompany(member);
                if(null == members || members.size() == 0){
                    TCompanyAccount companyAccount = new TCompanyAccount();
                    companyAccount.setCompanyId(lineGoodsUserRelVo.getCompanyId());
                    companyAccount.setAccountId(record.getId());
                    companyAccount.setRealName(record.getNickname());
                    companyAccount.setPhone(record.getAccountNo());
                    companyAccount.setRealPosition(record.getRealPosition());
                    companyAccount.setIdcard(record.getIdcard());
                    companyAccountMapper.deleteByAccountCompany(companyAccount);
                    //如果当前角色是经纪人
                    if (StringUtils.isNotEmpty(record.getUserLogisticsRole())
                            && record.getUserLogisticsRole().equals(DictEnum.CTYPEMANAGER.code)){
                        //删除经纪人和企业关系
                        TCompanyStationRel companyStationRel = new TCompanyStationRel();
                        companyStationRel.setCompanyId(companyIdInteger);
                        companyStationRel.setEndUserStationId(record.getEnduserId());
                        companyStationRelMapper.updateCompanyStationRelStatus(companyStationRel);
                    }
                }

            }
            //新增
            if (lineGoodsUserRelVo.getState() == 2) {
                lineGoodsUserRelVo.setAccountInfoId(ac.getId());
                lineGoodsUserRelVo.setCurrentAccountNo(ac.getAccountNo());
                lineGoodsUserRelVo.setCurrentAccountName(ac.getNickname());
                lineGoodsUserRelVo.setCurrentAccountPhone(ac.getAccountNo());
                TLineGoodsUserRelMember lineGoodsUserRel = new TLineGoodsUserRelMember();
                BeanUtil.copyProperties(lineGoodsUserRelVo, lineGoodsUserRel);

                // 线路员工角色权限表
                String[][] lineRoleId = lineGoodsUserRelVo.getLineRoleCode();
                TLineUserRolePro lineUserRolePro = new TLineUserRolePro();
                lineUserRolePro.setAccountId(ac.getId());
                HashMap<String, List<String>> roleBtnMap = new HashMap<>();
                for (String[] strings : lineRoleId) {
                    String role = strings[0];
                    List<String> roleButton = (null == roleBtnMap.get(role) ? new ArrayList<>() : roleBtnMap.get(role));
                    if(strings.length>1){
                        String button = strings[1];
                        roleButton.add(button);
                    }
                    roleBtnMap.put(role, roleButton);
                }
                for (Map.Entry map : roleBtnMap.entrySet()) {
                    String role = (String) map.getKey();
                    StringBuffer stringBuffer = new StringBuffer();
                    //发单员：判断线路是否已经有负责人
                    if (role.equals(ROLE.COMPANYSENDORDER.code) || role.equals(ROLE.COMMONSENDORDER.code)) {
                        if (null != lineGoodsUserRelVo.getIfPrincipal() && lineGoodsUserRelVo.getIfPrincipal()) {
                            //查询线路是否已经有负责人
                            HashMap<String, Object> param = new HashMap();
                            param.put("lineGoodsRelId", lineGoodsUserRelVo.getLineGoodsRelId());
                            param.put("companyId", lineGoodsUserRelVo.getCompanyId());
                            List<String> roles = new ArrayList<>();
                            roles.add(ROLE.COMMONSENDORDER.code);
                            roles.add(ROLE.COMPANYSENDORDER.code);
                            param.put("roles", roles);
                            List<TLineGoodsUserRel> tLineGoodsUserRels = tLineGoodsUserRelMemberMapper.selectLineIfPrincipal(param);
                            if (null != tLineGoodsUserRels && tLineGoodsUserRels.size()> 0) {
                                for (TLineGoodsUserRel tLineGoodsUserRel: tLineGoodsUserRels){
                                    if (null != tLineGoodsUserRel && null != tLineGoodsUserRel.getAccountInfoId()){
                                        if (!record.getId().equals(tLineGoodsUserRel.getAccountInfoId())){
                                            throw new RuntimeException("已有发单负责人");
                                        }
                                    }
                                }
                            }
                        }
                    }
                    //收单员：判断线路是否已经有负责人
                    if (role.equals(ROLE.RECEIVEORDER.code)) {
                        if (null != lineGoodsUserRelVo.getIfPrincipal() && lineGoodsUserRelVo.getIfPrincipal()) {
                            //查询线路是否已经有负责人
                            HashMap<String, Object> param = new HashMap();
                            param.put("lineGoodsRelId", lineGoodsUserRelVo.getLineGoodsRelId());
                            param.put("companyId", lineGoodsUserRelVo.getCompanyId());
                            List<String> roles = new ArrayList<>();
                            roles.add(ROLE.RECEIVEORDER.code);
                            param.put("roles", roles);
                            List<TLineGoodsUserRel> tLineGoodsUserRels = tLineGoodsUserRelMemberMapper.selectLineIfPrincipal(param);
                            if (null != tLineGoodsUserRels && tLineGoodsUserRels.size() > 0) {
                                for (TLineGoodsUserRel tLineGoodsUserRel: tLineGoodsUserRels){
                                    if (null != tLineGoodsUserRel && null != tLineGoodsUserRel.getAccountInfoId()){
                                        if (!record.getId().equals(tLineGoodsUserRel.getAccountInfoId())){
                                            throw new RuntimeException("已有收单负责人");
                                        }
                                    }
                                }
                            }
                        }
                    }

                    List<String> button = (List<String>) map.getValue();
                    for (HashMap hashMap : lineRoles) {
                        if (role.equals(hashMap.get("value"))) {
                            lineUserRolePro.setRoleCode(role);
                            lineUserRolePro.setRoleName(String.valueOf(hashMap.get("label")));
                            String perms = (String) hashMap.get("valueContent");
                            lineUserRolePro.setPerms(perms);
                            String buttons = String.valueOf(hashMap.get("buttonContent"));
                            lineUserRolePro.setButtons(buttons);
                            lineGoodsUserRel.setAuthorizedButtons(String.join(",", button));
                        }
                    }
                    //查询员工在这个企业线路线路是否已有此角色，如果没有，添加；否则不添加
                    TLineGoodsUserRelMember member = new TLineGoodsUserRelMember();
                    member.setAccountInfoId(record.getId());
                    member.setCompanyId(lineGoodsUserRelVo.getCompanyId());
                    member.setLineGoodsRelId(lineGoodsUserRelVo.getLineGoodsRelId());
                    List<String> members = tLineGoodsUserRelMemberMapper.selectUserLineRoleForCompany(member);
                    if (!members.contains(role)){
                        tLineUserRoleProMapper.insertSelective(lineUserRolePro);
                        lineGoodsUserRel.setRoleCode(role);
                        tLineGoodsUserRelMemberMapper.insertSelective(lineGoodsUserRel);
                        lineGoodsUserRel.setId(null);
                    } else {
                        for (HashMap map1 : lineRoles){
                            if (role.equals(map1.get("value"))) {
                                String label = String.valueOf(map1.get("label"));
                                return ResultUtil.error("当前员工在" + lineGoodsUserRelVo.getLineName() +"线路已有" + label + "角色");
                            }
                        }
                    }
                }
                // 企业账号关系表
                TCompanyAccount companyAccount = new TCompanyAccount();
                companyAccount.setAccountId(account.getId());
                companyAccount.setCompanyId(lineGoodsUserRelVo.getCompanyId());
                companyAccount.setRealName(record.getNickname());
                companyAccount.setPhone(record.getAccountNo());
                companyAccount.setRealPosition(record.getRealPosition());
                companyAccount.setIdcard(record.getIdcard());
                //查询用户是否和企业已有关系，如果没有创建
                int count = companyAccountMapper.selectAccountCompany(companyAccount);
                if (count == 0) {
                    companyAccount.setEnable(false);
                    companyAccountMapper.insertSelective(companyAccount);
                }
                //如果当前角色是经纪人
                if (StringUtils.isNotEmpty(record.getUserLogisticsRole())
                        && record.getUserLogisticsRole().equals(DictEnum.CTYPEMANAGER.code)){
                    //TODO 创建信息(经纪人)站与企业的关系
                    TCompanyStationRel companyStationRel = new TCompanyStationRel();
                    companyStationRel.setCompanyId(companyIdInteger);
                    companyStationRel.setEndUserStationId(record.getEnduserId());
                    companyStationRel.setEnable(false);
                    List<TCompanyStationRel> tCompanyStationRels = companyStationRelMapper.selectCompanyStationRel(companyStationRel);
                    if (null == tCompanyStationRels || tCompanyStationRels.size() == 0){
                        companyStationRelMapper.insertSelective(companyStationRel);
                    }
                }

            }
        }

        List<LineUserRoleDTO> lineUserRoleDTOS = tLineUserRoleProMapper.selectUserLineRole(ac.getId());
        HashSet<String> roleCodes = new HashSet<>();
        for(LineUserRoleDTO lineUserRoleDTO: lineUserRoleDTOS){
            if (null != lineUserRoleDTO && null != lineUserRoleDTO.getRoleCode() && StringUtils.isNotEmpty(lineUserRoleDTO.getRoleCode())){
                roleCodes.add(lineUserRoleDTO.getRoleCode());
            }
        }

        TSysUserVO user = new TSysUserVO();
        user.setId(selectAccount.getUserId());
        user.setUsername(record.getAccountNo());
        user.setNickname(record.getNickname());
        user.setRolesCode(roleCodes);
        ResultUtil resultUtil = systemAPI.updateUserByAccountNo(user);
        String code = resultUtil.getCode();
        if (code.equals("error")){
            return ResultUtil.error(resultUtil.getMsg());
        }

        return ResultUtil.ok();
    }


    /**
    * @Description 处理修改的线路角色
    * <AUTHOR>
    * @Date   2019/7/1 14:33
    * @Param
    * @Return
    * @Exception
    *
    */
    public void dealwithModRole(Integer accountId, Integer lineGoodsRelId, Map<String, ArrayList<String>> modRoleButton,
                                List<String> modRole, Boolean ifPrincipal, Integer companyId){
        for (Map.Entry entry : modRoleButton.entrySet()) {
            String role = (String) entry.getKey();
            for (String modeRole : modRole) {
                if (role.equals(modeRole)) {
                    //发单员：判断线路是否已经有负责人
                    if (role.equals(ROLE.COMPANYSENDORDER.code) || role.equals(ROLE.COMMONSENDORDER.code)) {
                        if (null != ifPrincipal && ifPrincipal) {
                            //查询线路是否已经有负责人
                            HashMap<String, Object> param = new HashMap();
                            param.put("lineGoodsRelId", lineGoodsRelId);
                            param.put("companyId", companyId);
                            List<String> roles = new ArrayList<>();
                            roles.add(ROLE.COMMONSENDORDER.code);
                            roles.add(ROLE.COMPANYSENDORDER.code);
                            param.put("roles", roles);
                            List<TLineGoodsUserRel> tLineGoodsUserRels = tLineGoodsUserRelMemberMapper.selectLineIfPrincipal(param);
                            if (null != tLineGoodsUserRels && tLineGoodsUserRels.size() > 0) {
                                for (TLineGoodsUserRel tLineGoodsUserRel: tLineGoodsUserRels){
                                    if (null != tLineGoodsUserRel && null != tLineGoodsUserRel.getAccountInfoId()){
                                        if (!accountId.equals(tLineGoodsUserRel.getAccountInfoId())){
                                            throw new RuntimeException("已有发单负责人");
                                        }
                                    }
                                }
                            }
                        }
                    }
                    //收单员：判断线路是否已经有负责人
                    if (role.equals(ROLE.RECEIVEORDER.code)) {
                        if (null != ifPrincipal && ifPrincipal) {
                            //查询线路是否已经有负责人
                            HashMap<String, Object> param = new HashMap();
                            param.put("lineGoodsRelId", lineGoodsRelId);
                            param.put("companyId", companyId);
                            List<String> roles = new ArrayList<>();
                            roles.add(ROLE.RECEIVEORDER.code);
                            param.put("roles", roles);
                            List<TLineGoodsUserRel> tLineGoodsUserRels = tLineGoodsUserRelMemberMapper.selectLineIfPrincipal(param);
                            if (null != tLineGoodsUserRels && tLineGoodsUserRels.size()  > 0) {
                                for (TLineGoodsUserRel tLineGoodsUserRel: tLineGoodsUserRels){
                                    if (null != tLineGoodsUserRel && null != tLineGoodsUserRel.getAccountInfoId()){
                                        if (!accountId.equals(tLineGoodsUserRel.getAccountInfoId())){
                                            throw new RuntimeException("已有收单负责人");
                                        }
                                    }
                                }
                            }
                        }
                    }
                    //员工线路关系表
                    TLineGoodsUserRelMember lineGoodsUserRel = new TLineGoodsUserRelMember();
                    lineGoodsUserRel.setLineGoodsRelId(lineGoodsRelId);
                    lineGoodsUserRel.setAccountInfoId(accountId);
                    ArrayList<String> button = (ArrayList<String>) entry.getValue();
                    lineGoodsUserRel.setAuthorizedButtons(String.join(",", button));
                    lineGoodsUserRel.setRoleCode(role);
                    lineGoodsUserRel.setIfPrincipal(ifPrincipal);
                    tLineGoodsUserRelMemberMapper.updateUserLineRoleByAccountId(lineGoodsUserRel);
                }
            }
        }
    }


    /**
    * @Description 处理删除的线路角色
    * <AUTHOR>
    * @Date   2019/7/1 14:19
    * @Param
    * @Return
    * @Exception
    *
    */
    public void dealwithDeleteRole(Integer accountId, Integer lineGoodsRelId, List<String> deleteRole) {
        for (String deleterole : deleteRole) {
            //根据1.线路货物关系id, 2.account_id, 3.role_code
            TLineGoodsUserRelMember lineGoodsUserRelMember = new TLineGoodsUserRelMember();
            lineGoodsUserRelMember.setAccountInfoId(accountId);
            lineGoodsUserRelMember.setRoleCode(deleterole);
            lineGoodsUserRelMember.setLineGoodsRelId(lineGoodsRelId);
            lineGoodsUserRelMember.setEnable(true);
            tLineGoodsUserRelMemberMapper.updateUserLineRoleEnbaleByAccountId(lineGoodsUserRelMember);
        }
    }


    /**
    * @Description 处理新增的线路角色
    * <AUTHOR>
    * @Date   2019/7/1 14:17
    * @Param
    * @Return
    * @Exception
    *
    */
    public void dealwithNewRole(TAccount ac, HashMap[] lineRoles, TLineGoodsUserRelVo lineGoodsUserRelVo,
                                Map<String, ArrayList<String>> modRoleButton, List<String> newRole, TAccountVo record) {
        //如果当前角色是经纪人
        if (StringUtils.isNotEmpty(record.getUserLogisticsRole())
                && record.getUserLogisticsRole().equals(DictEnum.CTYPEMANAGER.code)){
            //TODO 创建信息(经纪人)站与企业的关系
            TCompanyStationRel companyStationRel = new TCompanyStationRel();
            companyStationRel.setCompanyId(lineGoodsUserRelVo.getCompanyId());
            companyStationRel.setEndUserStationId(record.getEnduserId());
            companyStationRel.setEnable(false);
            List<TCompanyStationRel> tCompanyStationRels = companyStationRelMapper.selectCompanyStationRel(companyStationRel);
            if (null == tCompanyStationRels || tCompanyStationRels.size() == 0){
                companyStationRelMapper.insertSelective(companyStationRel);
            }
        }
        for (Map.Entry entry : modRoleButton.entrySet()) {
            String role = (String) entry.getKey();
            for (String newRoleCode : newRole) {
                if (role.equals(newRoleCode)) {
                    //发单员：判断线路是否已经有负责人
                    if (role.equals(ROLE.COMPANYSENDORDER.code) || role.equals(ROLE.COMMONSENDORDER.code)) {
                        if (lineGoodsUserRelVo.getIfPrincipal()) {
                            //查询线路是否已经有负责人
                            HashMap<String, Object> param = new HashMap();
                            param.put("lineGoodsRelId", lineGoodsUserRelVo.getLineGoodsRelId());
                            param.put("companyId", lineGoodsUserRelVo.getCompanyId());
                            List<String> roles = new ArrayList<>();
                            roles.add(ROLE.COMMONSENDORDER.code);
                            roles.add(ROLE.COMPANYSENDORDER.code);
                            param.put("roles", roles);
                            List<TLineGoodsUserRel> tLineGoodsUserRels = tLineGoodsUserRelMemberMapper.selectLineIfPrincipal(param);
                            if (null != tLineGoodsUserRels && tLineGoodsUserRels.size()  > 0) {
                                for (TLineGoodsUserRel tLineGoodsUserRel: tLineGoodsUserRels){
                                    if (null != tLineGoodsUserRel && null != tLineGoodsUserRel.getAccountInfoId()){
                                        if (!ac.getId().equals(tLineGoodsUserRel.getAccountInfoId())){
                                            throw new RuntimeException("已有发单负责人");
                                        }
                                    }
                                }
                            }
                        }
                    }
                    //收单员：判断线路是否已经有负责人
                    if (role.equals(ROLE.RECEIVEORDER.code)) {
                        if (lineGoodsUserRelVo.getIfPrincipal()) {
                            //查询线路是否已经有负责人
                            HashMap<String, Object> param = new HashMap();
                            param.put("lineGoodsRelId", lineGoodsUserRelVo.getLineGoodsRelId());
                            param.put("companyId", lineGoodsUserRelVo.getCompanyId());
                            List<String> roles = new ArrayList<>();
                            roles.add(ROLE.RECEIVEORDER.code);
                            param.put("roles", roles);
                            List<TLineGoodsUserRel> tLineGoodsUserRels = tLineGoodsUserRelMemberMapper.selectLineIfPrincipal(param);
                            if (null != tLineGoodsUserRels && tLineGoodsUserRels.size() > 0) {
                                for (TLineGoodsUserRel tLineGoodsUserRel: tLineGoodsUserRels){
                                    if (null != tLineGoodsUserRel && null != tLineGoodsUserRel.getAccountInfoId()){
                                        if (!ac.getId().equals(tLineGoodsUserRel.getAccountInfoId())){
                                            throw new RuntimeException("已有收单负责人");
                                        }
                                    }
                                }
                                //stringBuffer.append(lineGoodsUserRelVo.getLineName() + "已有收单负责人" + ",");
                                //continue;
                            }
                        }
                    }
                    //员工线路关系表
                    TLineGoodsUserRelMember lineGoodsUserRel = new TLineGoodsUserRelMember();
                    BeanUtil.copyProperties(lineGoodsUserRelVo, lineGoodsUserRel);
                    lineGoodsUserRel.setAccountInfoId(ac.getId());
                    lineGoodsUserRel.setEnable(false);
                    lineGoodsUserRel.setCurrentAccountNo(ac.getAccountNo());
                    lineGoodsUserRel.setCurrentAccountName(ac.getNickname());
                    lineGoodsUserRel.setCurrentAccountPhone(ac.getAccountNo());
                    //员工角色权限
                    TLineUserRolePro lineUserRolePro = new TLineUserRolePro();
                    lineUserRolePro.setAccountId(ac.getId());
                    lineUserRolePro.setEnable(false);
                    ArrayList<String> button = (ArrayList<String>) entry.getValue();
                    for (HashMap hashMap : lineRoles) {
                        if (role.equals(hashMap.get("value"))) {
                            lineUserRolePro.setRoleCode(role);
                            lineUserRolePro.setRoleName(String.valueOf(hashMap.get("label")));
                            String valueContent = (String) hashMap.get("valueContent");
                            lineUserRolePro.setPerms(valueContent);
                            lineGoodsUserRel.setAuthorizedButtons(String.join(",", button));
                        }
                    }
                    lineGoodsUserRel.setRoleCode(role);
                    tLineGoodsUserRelMemberMapper.insertSelective(lineGoodsUserRel);
                    lineGoodsUserRel.setId(null);
                    //查询角色权限表是否已经存在此角色:：存在不添加；不存在则添加
                    List<LineUserRoleDTO> lineUserRoleDTOS = tLineUserRoleProMapper.selectUserLineRole(ac.getId());
                    List<String> hasRole = new ArrayList<>();
                    for (LineUserRoleDTO roleDTO : lineUserRoleDTOS) {
                        hasRole.add(roleDTO.getRoleCode());
                    }
                    if (!hasRole.contains(role)) {
                        tLineUserRoleProMapper.insertSelective(lineUserRolePro);
                    }
                }
            }
        }
    }

    /**
     * @Description 解析修改后的员工线路角色和按钮
     * <AUTHOR>
     * @Date 2019/6/9 22:25
     * @Param
     * @Return
     * @Exception
     */
    public Map<String, ArrayList<String>> getModRoleButton(ArrayList<String[]> roleCodes) {
        Map<String, ArrayList<String>> map = new HashMap<>();
        for (String[] strings : roleCodes) {
            String role = strings[0];
            try {
                String button = strings[1];
                ArrayList<String> buttons = (null == map.get(role) ? new ArrayList<>() : map.get(role));
                buttons.add(button);
                map.put(role, buttons);
            } catch (Exception e) {
                log.error("", e);
            }

        }
        return map;
    }

    //删除
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil delete(List<Map<String,Object>> mapList) {
        for (Map<String,Object> map : mapList) {
            // 将TuserInfo置为删除
            TAccount tAccount = tAccountMapper.
                    selectByPrimaryKey(Integer.valueOf(map.get("accountId").toString()));
            if(map.get("relId")==null||"".equals(map.get("relId"))){
                TUserInfo userInfo = new TUserInfo();
                userInfo.setAccountId(tAccount.getUserId());
                userInfo.setEnable(true);
                systemAPI.updateByAccountIdSelective(userInfo);
                TAccount ac = tAccountMapper.
                        selectByPrimaryKey(Integer.valueOf(map.get("accountId").toString()));
                TAccount account = new TAccount();
                account.setId(ac.getId());
                account.setEnable(true);
                int count = tAccountMapper.updateByPrimaryKeySelective(account);
                //企业账号关系表 修改t_company_account 表数据
                companyAccountMapper.updateCompanyAccount(ac.getId());
                //根据账号表获取userid
                TSysUser user = new TSysUser();
                user.setId(ac.getUserId());
                user.setEnable(true);
                systemAPI.updateUser(user);
                //线路货物员工关系表
                List<TLineGoodsUserRelMember> goodsUserRelsList = tLineGoodsUserRelMemberMapper.selectByAccountId(ac.getId());
                for (TLineGoodsUserRelMember goodsUserRel : goodsUserRelsList) {
                    goodsUserRel.setEnable(true);
                    tLineGoodsUserRelMemberMapper.updateByPrimaryKey(goodsUserRel);
                    TLineUserRolePro lineUserRolePro = new TLineUserRolePro();
                    lineUserRolePro.setAccountId(ac.getId());
                    lineUserRolePro.setEnable(true);
                    tLineUserRoleProMapper.updateByRecord(lineUserRolePro);
                }
            }else{
                TLineGoodsUserRelMember goodsUserRel = new TLineGoodsUserRelMember();
                goodsUserRel.setId(Integer.parseInt(map.get("relId").toString()));
                goodsUserRel.setEnable(true);
                tLineGoodsUserRelMemberMapper.updateByPrimaryKey(goodsUserRel);

                TLineUserRolePro lineUserRolePro = new TLineUserRolePro();
                lineUserRolePro.setAccountId(Integer.valueOf(map.get("accountId").toString()));
                lineUserRolePro.setEnable(true);
                tLineUserRoleProMapper.updateByRecord(lineUserRolePro);
            }

        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),"删除成功！");
    }

    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil businessDelete(List<Map<String,Object>> mapList) {
        int total = 0;
        for (Map<String,Object> map : mapList) {
            TOrderInfoVO tOrderInfoVO = new TOrderInfoVO();
            if(map.get("accountId")!=null&&!"".equals(map.get("accountId"))){
                tOrderInfoVO.setAccountId(Integer.valueOf(map.get("accountId").toString()));
            }
            if(map.get("lineId")!=null&&!"".equals(map.get("lineId"))){
                tOrderInfoVO.setLineId(Integer.valueOf(map.get("lineId").toString()));
            }
            List list = tOrderInfoAPI.selectByAccountIdAnrLineIdFeign(tOrderInfoVO);
            if(list.size()>0){
                return ResultUtil.error("当前勾选的员工下已存在运单不允许删除！");
            }

            // 将TuserInfo置为删除
            TUserInfo userInfo = new TUserInfo();
            userInfo.setAccountId(tOrderInfoVO.getAccountId());
            userInfo.setEnable(true);
            memberUserInfoMapper.updateByAccountIdSelective(userInfo);

            if(map.get("relIds")==null||"".equals(map.get("relIds"))){
                TAccount ac = tAccountMapper.selectByPrimaryKey(Integer.valueOf(map.get("accountId").toString()));
                TAccount account = new TAccount();
                account.setId(ac.getId());
                account.setEnable(true);
                int count = tAccountMapper.updateByPrimaryKeySelective(account);
                //根据账号表获取userid
                TSysUser user = new TSysUser();
                user.setId(ac.getUserId());
                user.setEnable(true);
                systemAPI.updateUser(user);
                //线路货物员工关系表
                List<TLineGoodsUserRelMember> goodsUserRelsList = tLineGoodsUserRelMemberMapper.selectByAccountId(ac.getId());
                for (TLineGoodsUserRelMember goodsUserRel : goodsUserRelsList) {
                    goodsUserRel.setEnable(true);
                    tLineGoodsUserRelMemberMapper.updateByPrimaryKey(goodsUserRel);
                    TLineUserRolePro lineUserRolePro = new TLineUserRolePro();
                    lineUserRolePro.setAccountId(ac.getId());
                    lineUserRolePro.setEnable(true);
                    tLineUserRoleProMapper.updateByRecord(lineUserRolePro);
                }
                total = +count;
            }else{
                String[] reList = map.get("relIds").toString().split("/");
                for(String relId : reList){
                    TLineGoodsUserRelMember goodsUserRel = new TLineGoodsUserRelMember();
                    goodsUserRel.setId(Integer.parseInt(relId));
                    goodsUserRel.setEnable(true);
                    tLineGoodsUserRelMemberMapper.updateByPrimaryKey(goodsUserRel);

                    TLineUserRolePro lineUserRolePro = new TLineUserRolePro();
                    lineUserRolePro.setAccountId(Integer.valueOf(map.get("accountId").toString()));
                    lineUserRolePro.setEnable(true);
                    tLineUserRoleProMapper.updateByRecord(lineUserRolePro);
                }

            }

        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),"删除成功！");
    }

    /**
     * 查询企业列表
     *
     * @param record
     * @return
     */
    @Override
    public ResultUtil selectByCompany(TCompanyInfoQuery record) {
        List<String> userCompanyIdList = CurrentUser.getUserCompanyId();
        if (null != userCompanyIdList && userCompanyIdList.size() > 0){
            String companyId = userCompanyIdList.get(0);
            if (StringUtils.isNotEmpty(companyId)){
                record.setCompanyId(Integer.valueOf(companyId));
            }
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), tCompanyInfoMapper.list(record));
    }

    /**
     * 根据企业查询线路
     *
     * @param companyId
     * @return
     */
    @Override
    public ResultUtil selectByLine(String companyId) {
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), tLineGoodsRelMapper.selectByCompany(Integer.parseInt(companyId)));
    }

    //线路员工权限
    @Override
    public ResultUtil selectByLineUserRolePro(TLineUserRolePro record) {
        record.setEnable(true);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), tLineUserRoleProMapper.list(record));
    }


    /**
     * 根据t_sys_user ID 查询出 企业 ID
     *
     * @param systemId
     * @return
     * @auth Yan
     */
    @Override
    public Integer selectAccountId(Integer systemId) {
        return tAccountMapper.selectAccontIdBySystemId(systemId);
    }

    @Override
    public List<TAccount> findOpenId(Integer openId) {
        return tAccountMapper.findOpenId(openId);
    }

    @Override
    public List<TAccount> findUser(String username) {
        return tAccountMapper.findUser(username);
    }

    //新增用户
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil saveAccount(TUserVo record) {
        String enduserKey = "";
        boolean endLock = false;
        try{
            enduserKey = "zc" + record.getAccountNo();
            if (!redisUtil.hasKey(enduserKey)) {
                endLock = redisUtil.set(enduserKey, "lock");
            }else{
                return ResultUtil.error("用户注册正在执行请稍后！");
            }
            if(endLock){
                //sys_user新增系统用户
                TSysUser sysUser = new TSysUser();
                sysUser.setAccountNo(record.getAccountNo());
                sysUser.setUsername(record.getAccountNo());
                sysUser.setNickname(record.getUsername());
                sysUser.setPassword(record.getPassword());//设置默认密码
                sysUser.setUsertype("CD");
                sysUser.setAcctype("Wx");
                sysUser.setRegnode("ACCAPCOM");
                sysUser.setDatafrom(record.getDatafrom());
                sysUser.setThridParyId(record.getThridParyId());
                sysUser.setEnable(false);
                sysUser.setIfAgreement(record.getIfAgreement());
                sysUser.setIfPasswordSecurity(record.getIfPasswordSecurity());
                Integer userId = null;
                List<TSysUser> tSysUserList = systemAPI.selectByPhone(record.getAccountNo());
                if(tSysUserList.size()<1){
                    ResultUtil resultUtil = systemAPI.addUser(sysUser);
                    if (null != resultUtil.getCode()){
                        if (resultUtil.getCode().equals("error")){
                            if (null != resultUtil.getMsg()){
                                return ResultUtil.error(resultUtil.getMsg());
                            } else {
                                return ResultUtil.error("添加失败");
                            }
                        } else if (resultUtil.getCode().equals("success")){
                            Object data = resultUtil.getData();
                            if (null != data){
                                LinkedHashMap user = (LinkedHashMap) data;
                                userId = (Integer) user.get("id");
                            }
                        }
                    }
                }
                if(StringUtils.isNotBlank(record.getItemCode()) && record.getItemCode().equals("CTYPECAPTAIN")){
                    TSysRole tSysRole = systemAPI.selectCaptainSysRole();
                    TUserRole tUserRole = new TUserRole();
                    tUserRole.setUserId(userId);
                    tUserRole.setRoleId(tSysRole.getId());
                    ResultUtil resultUtil = systemAPI.insertCaptainUserRole(tUserRole);
                }

                //account新增用户账号
                TAccount ac = new TAccount();
                ac.setNickname(record.getUsername());
                ac.setAccountNo(record.getAccountNo());
                ac.setUserId(userId);
                ac.setPassword(record.getPassword());
                ac.setAcctype("Wx");
                ac.setUsertype("CD");
                ac.setRegnode("ACCAPCOM");
                ac.setDatafrom(record.getDatafrom());
                ac.setThridParyId(record.getThridParyId());
                ac.setEnable(false);

                TAccountVo accountVo = new TAccountVo();
                accountVo.setAccountNo(ac.getAccountNo());
                TAccount tAccountResult = tAccountMapper.selectAccountByPhone(accountVo);
                if(tAccountResult==null||"".equals(tAccountResult)){
                    int i = tAccountMapper.insertSelective(ac);
                }

                //保存用户信息表 add by zhangjiji ********
                TUserInfo tUserInfo = new TUserInfo();
                tUserInfo.setAccountId(userId);
                tUserInfo.setStatus(DictEnum.ACCOUNTENABLE.code);
                tUserInfo.setPasswordErrorCount(0);
                tUserInfo.setEnable(false);
                if (null == record.getSmsType() || record.getSmsType().equals("WXREFISTERED")) {
                    tUserInfo.setPasswordResetTime(new Date());
                }
                systemAPI.insertUserInfo(tUserInfo);

                //新增c端用户状态
                TEndUserInfo tEndUserInfo = new TEndUserInfo();
                tEndUserInfo.setPhone(record.getAccountNo());
                tEndUserInfo.setRealName(record.getUsername());
                tEndUserInfo.setIdcard(record.getIdcard());
                tEndUserInfo.setIdcardPhoto1(record.getIdcardPhoto1());
                tEndUserInfo.setIdcardPhoto2(record.getIdcardPhoto2());
                tEndUserInfo.setAddress(record.getAddress());
                if (StringUtils.isNotBlank(record.getAddress()) && null!=record.getIdcardValidUntil() &&!"".equals(record.getIdcardValidUntil())){
                    tEndUserInfo.setAddressState(true);
                }else {
                    tEndUserInfo.setAddressState(false);
                }
                tEndUserInfo.setIdcardValidBeginning(record.getIdcardValidUntil()); //身份证有效期至
                tEndUserInfo.setIdcardValidUntil(record.getIdcardValidBeginning());//身份证有效期始
                tEndUserInfo.setIdcardIssueQrganization(record.getIdcardIssueQrganization());
                tEndUserInfo.setIdcardSex(record.getIdcardSex());
                tEndUserInfo.setIdcardEthnicity(record.getIdcardEthnicity());
                tEndUserInfo.setIdcardBirth(record.getIdcardBirth());
                tEndUserInfo.setUserLogisticsRole(record.getItemCode());
                tEndUserInfo.setAuditStatus("MIDNODE");
                tEndUserInfo.setEnable(false);
                if (StringUtils.isNotBlank(record.getItemCode()) && (record.getItemCode().equals("CTYPEDRVIVER")||"CTYPEDRVIVER,CTYPEBOSS".equals(record.getItemCode())||"CTYPEBOSS,CTYPEDRVIVER".equals(record.getItemCode()))) {
                    tEndUserInfo.setCertificatePhoto1(record.getCertificatePhoto1());
                    tEndUserInfo.setCertificatePhoto2(record.getCertificatePhoto2());
                    tEndUserInfo.setDrivingLicencesPhoto1(record.getDrivingLicencesPhoto1());
                    tEndUserInfo.setDrivingLicencesPhoto2(record.getDrivingLicencesPhoto2());
                    tEndUserInfo.setApproveDrivingType(record.getApproveDrivingType());//驾驶证准驾车型
                    tEndUserInfo.setDrivingLicencesIssueUnit(record.getDrivingLicencesIssueUnit());//驾驶证发证机关
                    tEndUserInfo.setDrivingLicencesValidBeginning(record.getDrivingLicencesValidBeginning());//驾驶证有效期始
                    tEndUserInfo.setDrivingLicencesValidUntil(record.getDrivingLicencesValidUntil());//驾驶证有效期至
                    tEndUserInfo.setDrivingLicencesCode(record.getDrivingLicencesCode());//驾驶证档案编号
                    tEndUserInfo.setDrivingLicencesSex(record.getDrivingLicencesSex());
                    tEndUserInfo.setDrivingLicencesNationality(record.getDrivingLicencesNationality());
                    tEndUserInfo.setDrivingLicencesBirthday(record.getDrivingLicencesBirthday());
                    tEndUserInfo.setCertificateNo(record.getCertificateNo());//从业资格证号
                    tEndUserInfo.setCertificateValidBeginning(record.getCertificateValidBeginning());//资格证有效期始
                    tEndUserInfo.setCertificateValidUntil(record.getCertificateValidUntil());//资格证有效期至
                }
                String UserAuditOpinion = "";
                if(null == tEndUserInfo.getIdcardPhoto1() || null == tEndUserInfo.getIdcardPhoto2() ||
                        "".equals(tEndUserInfo.getIdcardPhoto1()) || "".equals(tEndUserInfo.getIdcardPhoto2())){
                    tEndUserInfo.setAuditStatus(DictEnum.PAPERNEEDUPDATE.code);
                    UserAuditOpinion = UserAuditOpinion + "身份证、";
                }
                if("CTYPEDRVIVER".equals(tEndUserInfo.getUserLogisticsRole())){
                    if(null == tEndUserInfo.getDrivingLicencesPhoto1() || null == tEndUserInfo.getDrivingLicencesPhoto2() ||
                            "".equals(tEndUserInfo.getDrivingLicencesPhoto1()) || "".equals(tEndUserInfo.getDrivingLicencesPhoto2())){
                        tEndUserInfo.setAuditStatus(DictEnum.PAPERNEEDUPDATE.code);
                        UserAuditOpinion = UserAuditOpinion + "驾驶证、";
                    }
                    if(null == tEndUserInfo.getCertificatePhoto1() || null ==  tEndUserInfo.getCertificatePhoto2() ||
                            "".equals(tEndUserInfo.getCertificatePhoto1()) || "".equals(tEndUserInfo.getCertificatePhoto2())){
                        tEndUserInfo.setAuditStatus(DictEnum.PAPERNEEDUPDATE.code);
                        UserAuditOpinion = UserAuditOpinion + "从业资格证、";
                    }
                    if(UserAuditOpinion.length() > 1){
                        tEndUserInfo.setAuditOpinion(UserAuditOpinion+"证件不齐全，请补齐后重新提交。");
                    }else {
                        tEndUserInfo.setAuditOpinion("");
                    }
                }

                TEndUserInfoExample tEndUserInfoExample = new TEndUserInfoExample();
                TEndUserInfoExample.Criteria cr = tEndUserInfoExample.createCriteria();
                cr.andPhoneEqualTo(tEndUserInfo.getPhone());
                cr.andRealNameEqualTo(tEndUserInfo.getRealName());
                cr.andEnableEqualTo(false);
                List<TEndUserInfo> tEndUserInfoList = tEndUserInfoMapper.selectByExample(tEndUserInfoExample);
                if(tEndUserInfoList.size()<1){
                    tEndUserInfoMapper.insertSelective(tEndUserInfo);
                }

                // 创建自动化审核消息
                /*try {
                    String currentUsername = CurrentUser.getCurrentUsername();
                    if (StringUtils.isBlank(currentUsername) && StringUtils.isBlank(tEndUserInfo.getAuditOpinion())) {
                        MQMessage mqMessage = new MQMessage();
                        mqMessage.setTopic(MqMessageTopic.DRIVERAUDIT);
                        mqMessage.setTag(MqMessageTag.DRIVER_AUDIT);
                        mqMessage.setKey(String.valueOf(tEndUserInfo.getId()));
                        TEndUserInfo tEndUserInfo1 = new TEndUserInfo();
                        tEndUserInfo1.setId(tEndUserInfo.getId());
                        tEndUserInfo1.setPhone(record.getAccountNo());
                        tEndUserInfo1.setParam2(IdWorkerUtil.getInstance().nextId());
                        mqMessage.setBody(tEndUserInfo1);
                        // redis 添加审核记录
                        redisUtil.set("REDISENDUSERAUDIT" + tEndUserInfo1.getParam2(), "0", 60 * 60 * 24);
                        ResultUtil resultUtil = mqAPI.sendMessage(mqMessage);
                        if (CodeEnum.SUCCESS.getCode().equals(resultUtil.getCode())) {
                            log.info("自动化审核消息发送成功");
                        } else {
                            log.error("自动化审核消息发送失败");
                        }
                    }
                } catch (Exception e) {
                    log.error("自动化审核消息发送失败, {}", ThrowableUtil.getStackTrace(e));
                }*/

                TEnduserAccount tEnduserAccount = new TEnduserAccount();
                tEnduserAccount.setAccountId(ac.getId());
                tEnduserAccount.setEnduserId(tEndUserInfo.getId());
                tEnduserAccount.setRealName(tEndUserInfo.getRealName());
                tEnduserAccount.setIdcard(record.getIdcard());
                tEnduserAccount.setPhone(record.getAccountNo());
                tEnduserAccount.setEnable(false);

                TEnduserAccountExample tEnduserAccountExample = new TEnduserAccountExample();
                TEnduserAccountExample.Criteria cr2 = tEnduserAccountExample.createCriteria();
                cr2.andPhoneEqualTo(tEnduserAccount.getPhone());
                cr2.andRealNameEqualTo(tEnduserAccount.getRealName());
                cr2.andEnableEqualTo(false);
                List<TEnduserAccount> tEnduserAccountList = tEnduserAccountMapper.selectByExample(tEnduserAccountExample);
                if(tEnduserAccountList.size()<1){
                    tEnduserAccountMapper.insert(tEnduserAccount);
                }
                TEndUserStatus tEndUserStatus = new TEndUserStatus();
                tEndUserStatus.setEnduserId(tEndUserInfo.getId());
                tEndUserStatus.setCurrentDriver(tEndUserInfo.getRealName());
                tEndUserStatus.setUserStatus("DISTRIBUTIONREGISTERED");
                tEndUserStatus.setCurrentDriverAccountNo(ac.getAccountNo());
                tEndUserStatus.setEnable(false);
                List<TEndUserStatus> tEndUserStatusList = tEndUserStatusMapper.selectByPhone(tEndUserStatus.getCurrentDriverAccountNo(),tEndUserStatus.getCurrentDriver());
                if(tEndUserStatusList.size()<1){
                    tEndUserStatusMapper.insert(tEndUserStatus);
                }
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("enduserinfoId", tEndUserInfo.getId());
                map.put("accouontid", ac.getId());
                return ResultUtil.ok(map,"用户注册成功，绑定车辆就可以接单了");
            }else{
                return ResultUtil.error("用户注册正在执行请稍后！");
            }
        }catch(Exception e) {
            log.error("用户注册失败, {}", ThrowableUtil.getStackTrace(e));
            throw new RuntimeException("用户注册失败！");
        } finally {
            if (endLock && redisUtil.hasKey(enduserKey)) {
                redisUtil.del(enduserKey);
            }
        }
    }

    public int updateOpenId(TAccount account) {
        return tAccountMapper.updateByPrimaryKeySelective(account);
    }

    public int updateUser(TAccount account) {
        return systemAPI.updateThridID(account.getUserId(), account.getThridParyId());
    }

    public ResultUtil updateAccountNo(TAccount account, TSysUser tSysUser, TEndUserInfo tEndUserInfo, TEnduserAccount tEnduserAccount, String newPhone) {
        Integer id = account.getId();
        if (!account.getAccountNo().equals("") && !account.getNickname().equals("") && !tEndUserInfo.getIdcard().equals("")) {

            ContractApplyCertReq data1 = new ContractApplyCertReq();
            data1.setType("1");//类型 1：个人 、2：企业
            data1.setCardType("0");// 证件类型 0：身份证 1：军官证 ,2：护照、 3：驾驶证、4：工商登记证、5：税务登记证、6：组织机构代码、7：其他证件，8：统一社会信用代码
            data1.setIdCardNum(tEndUserInfo.getIdcard());// 证件号码
            data1.setName(account.getNickname());//企业或者个人真实名称
            data1.setMobilePhone(account.getAccountNo());//企业或者个人联系手机号
            ContractApplyCertResp resp = contractAPI.unwrap(data1);
            if(resp!=null&&!"".equals(resp)){
                if (!resp.getCode().equals("0")) {
                    return ResultUtil.error(resp.getMessage());
                }
            }
            ContractApplyCertReq data = new ContractApplyCertReq();
            data.setType("1");//类型 1：个人 、2：企业
            data.setCardType("0");// 证件类型 0：身份证 1：军官证 ,2：护照、 3：驾驶证、4：工商登记证、5：税务登记证、6：组织机构代码、7：其他证件，8：统一社会信用代码
            data.setIdCardNum(tEndUserInfo.getIdcard());// 证件号码
            data.setName( account.getNickname());//企业或者个人真实名称
            data.setMobilePhone(newPhone);//企业或者个人联系手机号
            ContractApplyCertResp cert = contractAPI.applyCert(data);
            if(cert!=null&&!"".equals(cert)){
                TEndUserInfo t = tEndUserInfoMapper.selectByPrimaryKey(tEndUserInfo.getId());
                if (cert.getCode().equals("0")) {
                    //修改userinfo表里面的手机号
                    t.setIssuer(cert.getIssuer());
                    t.setSerialNumber(cert.getSerialNumber());
                    t.setBeginTime(DateUtils.parseDate(cert.getCertNotBefore()));
                    t.setEndTime(DateUtils.parseDate(cert.getCertNotAfter()));
                }
                t.setPhone(newPhone);
                tEndUserInfoMapper.updateByPrimaryKeySelective(t);
            }
        }
        //修改accout表里面的手机号
        TAccount t = tAccountMapper.selectByPrimaryKey(id);
        t.setAccountNo(newPhone);
        tAccountMapper.updateByPrimaryKeySelective(t);
        //sysuser
        systemAPI.updateAccountNo(account.getUserId(), newPhone);
        //修改enduserAccount表里面的手机号
        TEnduserAccount tEnduserAccount1 = tEnduserAccountMapper.selectByPrimaryKey(tEnduserAccount.getId());
        tEnduserAccount1.setPhone(newPhone);
        tEnduserAccountMapper.updateByPrimaryKeySelective(tEnduserAccount1);
        return ResultUtil.ok();
    }

    public List<TAccount> findphone(String phone) {
        return tAccountMapper.findphone(phone);
    }

    public List<TEndUserInfo> findIdCard(String idcard) {
        return tAccountMapper.findIdCard(idcard);
    }

    @Override
    public List<TBankCard> getBankCardListByCardNo(String cardNo, Integer accouontid) {
        return tBankCardMapper.getBankCardListByCardNo(cardNo, accouontid);
    }

    @Override
    public List<TZtBankCard> getTZtBankCardListByCardNo(String cardNo, Integer accouontId) {
        return tZtBankUserMapper.getTZtBankCardListByCardNo(cardNo, accouontId,null);
    }

    public int updatePhone(TAccount account) {
        return tAccountMapper.updateByPrimaryKeySelective(account);
    }

    public int updatePassword(Integer userid, String password) {
        return systemAPI.updatePassword(userid, password);
    }

    @Override
    public int updateIfUsed(String accountNo, String code) {
        TVerificationCodeLog s = verificationCodeLogMapper.selectByPhoneAndCode(accountNo, code);
        TVerificationCodeLog verificationCodeLog = verificationCodeLogMapper.selectByPrimaryKey(s.getId());
        verificationCodeLog.setIfUsed("1");
        int i = verificationCodeLogMapper.updateByPrimaryKeySelective(s);
        return i;
    }

    @Override
    public List<TEnduserAccount> selectByEndUserInfoIdAndAccountId(Integer enduserinfoid, Integer accountid) {
        return tEnduserAccountMapper.selectByEndUserInfoIdAndAccountId(enduserinfoid, accountid);
    }

    //发起方  fei 禁止调用
    @Override
    @LcnTransaction
    @Transactional
    public ResultUtil updateAccount(TUserVo resources) {
        Integer accountId = CurrentUser.getUserAccountId();
        Integer id = CurrentUser.getCurrentUserID();
        Integer enuserid = CurrentUser.getEndUserId();
        TEndUserInfo endUser = tEndUserInfoMapper.selectByPrimaryKey(enuserid);
        if("PASSNODE".equals(endUser.getAuditStatus())){
            return ResultUtil.error("认证状态已通过，不允许修改个人资料");
        }
        if (!endUser.getPhone().equals(resources.getAccountNo()) || !endUser.getRealName().equals(resources.getUsername()) || !endUser.getIdcard().equals(resources.getIdcard())) {
            //更改userinfo
            TEndUserInfo tEndUserInfo = tEndUserInfoMapper.selectByPrimaryKey(enuserid);
            String  userAuditStatus = "";
            if(null != endUser.getAuditStatus()){
                userAuditStatus = endUser.getAuditStatus();
            }
            tEndUserInfo.setIdcard(resources.getIdcard());
            tEndUserInfo.setRealName(resources.getUsername());
            tEndUserInfo.setIdcardPhoto1(resources.getIdcardPhoto1());
            tEndUserInfo.setIdcardPhoto2(resources.getIdcardPhoto2());
            tEndUserInfo.setUpdateTime(new Date());
            tEndUserInfo.setAuditStatus("MIDNODE");
            tEndUserInfo.setUpdateUser(CurrentUser.getCurrentUsername());
            tEndUserInfo.setPhone(resources.getAccountNo());
            tEndUserInfo.setIdcardValidBeginning(resources.getIdcardValidUntil());//身份证有效期始
            tEndUserInfo.setIdcardValidUntil(resources.getIdcardValidBeginning());//身份证有效期至
            tEndUserInfo.setIdcardIssueQrganization(resources.getIdcardIssueQrganization());
            tEndUserInfo.setIdcardSex(resources.getIdcardSex());
            tEndUserInfo.setIdcardEthnicity(resources.getIdcardEthnicity());
            tEndUserInfo.setIdcardBirth(resources.getIdcardBirth());
            //tEndUserInfo.setUserLogisticsRole(resources.getItemCode());
            tEndUserInfo.setEnable(false);
            if (resources.getItemCode().contains("CTYPEDRVIVER")) {
                tEndUserInfo.setIdcardPhoto1(resources.getIdcardPhoto1());
                tEndUserInfo.setIdcardPhoto2(resources.getIdcardPhoto2());
                tEndUserInfo.setDrivingLicencesPhoto1(resources.getDrivingLicencesPhoto1());
                tEndUserInfo.setDrivingLicencesPhoto2(resources.getDrivingLicencesPhoto2());
                tEndUserInfo.setCertificatePhoto1(resources.getCertificatePhoto1());
                tEndUserInfo.setCertificatePhoto2(resources.getCertificatePhoto2());
                tEndUserInfo.setApproveDrivingType(resources.getApproveDrivingType());//驾驶证准驾车型
                tEndUserInfo.setDrivingLicencesIssueUnit(resources.getDrivingLicencesIssueUnit());//驾驶证发证机关

                tEndUserInfo.setDrivingLicencesValidBeginning(resources.getDrivingLicencesValidBeginning());//驾驶证有效期始
                tEndUserInfo.setDrivingLicencesValidUntil(resources.getDrivingLicencesValidUntil());//驾驶证有效期至
                tEndUserInfo.setDrivingLicencesCode(resources.getDrivingLicencesCode());//驾驶证档案编号
                tEndUserInfo.setDrivingLicencesSex(resources.getDrivingLicencesSex());
                tEndUserInfo.setDrivingLicencesNationality(resources.getDrivingLicencesNationality());
                tEndUserInfo.setDrivingLicencesBirthday(resources.getDrivingLicencesBirthday());
                tEndUserInfo.setCertificateNo(resources.getCertificateNo());//从业资格证号
                tEndUserInfo.setCertificateValidBeginning(resources.getCertificateValidBeginning());//资格证有效期始
                tEndUserInfo.setCertificateValidUntil(resources.getCertificateValidUntil());//资格证有效期至
                if(null != endUser && !userAuditStatus.isEmpty() && (!userAuditStatus.equals("PAPERNEEDUPDATE") && !userAuditStatus.equals("PASSNODE"))){
                    String UserAuditOpinion = "";
                    if(null == tEndUserInfo.getIdcardPhoto1() || null == tEndUserInfo.getIdcardPhoto2() ||
                            "".equals(tEndUserInfo.getIdcardPhoto1()) || "".equals(tEndUserInfo.getIdcardPhoto2())){
                        tEndUserInfo.setAuditStatus(DictEnum.PAPERNEEDUPDATE.code);
                        UserAuditOpinion = UserAuditOpinion + "身份证、";
                    }
                    if(null == tEndUserInfo.getDrivingLicencesPhoto1() || null == tEndUserInfo.getDrivingLicencesPhoto2() ||
                            "".equals(tEndUserInfo.getDrivingLicencesPhoto1()) || "".equals(tEndUserInfo.getDrivingLicencesPhoto2())){
                        tEndUserInfo.setAuditStatus(DictEnum.PAPERNEEDUPDATE.code);
                        UserAuditOpinion = UserAuditOpinion + "驾驶证、";
                    }
                    if(null == tEndUserInfo.getCertificatePhoto1() || null == tEndUserInfo.getCertificatePhoto2() ||
                            "".equals(tEndUserInfo.getCertificatePhoto1()) || "".equals(tEndUserInfo.getCertificatePhoto2())){
                        tEndUserInfo.setAuditStatus(DictEnum.PAPERNEEDUPDATE.code);
                        UserAuditOpinion = UserAuditOpinion + "从业资格证、";
                    }
                    if(UserAuditOpinion.length() > 1){
                        tEndUserInfo.setAuditOpinion(UserAuditOpinion+"证件不齐全，请补齐后重新提交。");
                    }else {
                        tEndUserInfo.setAuditOpinion("");
                    }
                }
            }
            tEndUserInfoMapper.updateByPrimaryKeySelective(tEndUserInfo);
            //更改sysuser
            TSysUser tSysUser = systemAPI.selectByPrimaryKey(id);
            tSysUser.setNickname(resources.getUsername());
            tSysUser.setAccountNo(resources.getAccountNo());
            tSysUser.setRegnode("PAWRCOM");
            tSysUser.setUpdateTime(new Date());
            tSysUser.setUpdateUser(CurrentUser.getCurrentUsername());
            systemAPI.updateUser(tSysUser);
            //更改t_account
            TAccount tAccount = tAccountMapper.selectByPrimaryKey(accountId);
            tAccount.setNickname(resources.getUsername());
            tAccount.setAccountNo(resources.getAccountNo());
            tAccount.setRegnode("PAWRCOM");
            tAccount.setUpdateTime(new Date());
            tAccount.setUpdateUser(CurrentUser.getCurrentUsername());
            tAccountMapper.updateByPrimaryKeySelective(tAccount);
            TEnduserAccountExample exampleClient = new TEnduserAccountExample();
            TEnduserAccountExample.Criteria crClient = exampleClient.createCriteria();
            crClient.andEnduserIdEqualTo(enuserid);
            crClient.andAccountIdEqualTo(accountId);
            List<TEnduserAccount> t = tEnduserAccountMapper.selectByExample(exampleClient);
            TEnduserAccount tEnduserAccount1 = tEnduserAccountMapper.selectByPrimaryKey(t.get(0).getId());
            tEnduserAccount1.setRealName(resources.getUsername());
            tEnduserAccount1.setIdcard(resources.getIdcard());
            tEnduserAccount1.setPhone(resources.getAccountNo());
            tEnduserAccount1.setUpdateTime(new Date());
            tEnduserAccount1.setUpdateUser(CurrentUser.getCurrentUsername());
            tEnduserAccountMapper.updateByPrimaryKeySelective(tEnduserAccount1);
            return ResultUtil.ok();
        } else {
            endUser.setIdcard(resources.getIdcard());
            endUser.setRealName(resources.getUsername());
            endUser.setIdcardPhoto1(resources.getIdcardPhoto1());
            endUser.setIdcardPhoto2(resources.getIdcardPhoto2());
            if (resources.getItemCode().contains("CTYPEDRVIVER")) {
                endUser.setIdcardPhoto1(resources.getIdcardPhoto1());
                endUser.setIdcardPhoto2(resources.getIdcardPhoto2());
                endUser.setDrivingLicencesPhoto1(resources.getDrivingLicencesPhoto1());
                endUser.setDrivingLicencesPhoto2(resources.getDrivingLicencesPhoto2());
                endUser.setCertificatePhoto1(resources.getCertificatePhoto1());
                endUser.setCertificatePhoto2(resources.getCertificatePhoto2());
            }
            tEndUserInfoMapper.updateByPrimaryKeySelective(endUser);
            return ResultUtil.ok();
        }

    }

    /**
     * 个人中心数据
     *
     * @param accountid     account表id
     * @param enduserinfoid enduserinfo 表id
     * @return
     */
    @Override
    public ResultUtil getData(Integer accountid, Integer enduserinfoid) {

        TEndUserCarRel tEndUserCarRel = new TEndUserCarRel();
        tEndUserCarRel.setEnduserId(enduserinfoid);
        String logisticsRole = CurrentUser.getUserLogisticsRole();
        log.info("当前登录角色："+logisticsRole);
        if (null == logisticsRole) {
            Map<Object, String> map = new HashMap<>();
            TEndUserInfo userInfo = tEndUserInfoMapper.selectByPrimaryKey(enduserinfoid);
            map.put("endUserId", String.valueOf(enduserinfoid));
            map.put("phone", userInfo.getPhone());
            map.put("username", "陆港通");
            map.put("auditStatus", userInfo.getAuditStatus());//审核状态
            map.put("userlogisticsRole", "");
            map.put("balance", "0.00");//钱包余额
            map.put("carNum", "0");//车的数量
            map.put("ifPasswordSecurity",String.valueOf(1));
            map.put("uploadInfo", "0");
            // 查询司机、车辆有效期
            // 查询司机、车辆证件有限期
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), map);
        }
        String purseCategory = "";
        if (logisticsRole.equals("CTYPEDRVIVER")) {//司机
            tEndUserCarRel.setUserCarRelationType("CLSYRSJ");
            purseCategory = DictEnum.CDRIVER.code;
        } else if (logisticsRole.equals("CTYPEBOSS")) {//车老板
            tEndUserCarRel.setUserCarRelationType("CLSYRCLB");
            tEndUserCarRel.setAuditStatus("PASSNODE");
            purseCategory = DictEnum.CCARBOSS.code;
        } else if (logisticsRole.equals(DictEnum.CTYPEMANAGER.code)) {//经纪人
            tEndUserCarRel.setUserCarRelationType("CTYPEMANAGER");
            tEndUserCarRel.setAuditStatus("PASSNODE");
            purseCategory = DictEnum.CMANAGER.code;
        }else if (logisticsRole.equals("CTYPECAPTAIN")) {//车队长
            tEndUserCarRel.setUserCarRelationType("CTYPECAPTAIN");
            tEndUserCarRel.setAuditStatus("PASSNODE");
            purseCategory = DictEnum.CCAPTAIN.code;
        }
        Map<Object, String> map = new HashMap<>();
        TAccount account = tAccountMapper.selectByPrimaryKey(accountid);
        TZtAccountOpenInfo tZtAccountOpenInfo = tZtAccountOpenInfoMapper.selectByAccountId(accountid,DictEnum.CD.code);
        TEndUserInfo tEndUserInfo = tEndUserInfoMapper.selectByPrimaryKey(enduserinfoid);
        BigDecimal balance = walletMapper.selectBalance(enduserinfoid, purseCategory,logisticsRole);//钱包余额
        int carNum = tEndUserCarRelMapper.selectCarByEndUserInfoCount(tEndUserCarRel);//车的数量
        int i = tEndUserCarRelMapper.selectCountStatus(tEndUserCarRel);
        TSysUser tSysUser = tAccountMapper.getTSysUserByName(tEndUserInfo.getPhone());
        if (i > 0) {
            map.put("carStatus", "1");//要完善资料
        } else if (i == 0) {
            map.put("carStatus", "0");//没有要完善资料
            if (carNum == 0) {
                // 无车，展示未认证
                map.put("carStatus", "1");//要完善资料
            }
            // 查询是否有审核中的车辆
            int selectCountMidStatus = tEndUserCarRelMapper.selectCountMidStatus(tEndUserCarRel);
            if (selectCountMidStatus > 0) {
                map.put("carStatus", "1");//要完善资料
            }
        }

        //华夏银行卡数量
        int hxyhBankNum = tZtBankUserMapper.selectByBankNum(CurrentUser.getUserAccountId());
        map.put("myBankNum", String.valueOf(hxyhBankNum));//本人银行卡数量

        map.put("endUserId", String.valueOf(enduserinfoid));
        map.put("phone", account.getAccountNo());
        map.put("username", null == tEndUserInfo.getRealName() || StringUtils.isBlank(tEndUserInfo.getRealName()) ? "陆港通" : tEndUserInfo.getRealName());
        map.put("idcard", tEndUserInfo.getIdcard());
        if(null != tZtAccountOpenInfo){
            if(tZtAccountOpenInfo.getIfOneselfOpen()){
                map.put("openRealName", tZtAccountOpenInfo.getOpenRealName());
                map.put("openIdCard", tZtAccountOpenInfo.getOpenIdCard());
            }else{
                map.put("openRealName", tEndUserInfo.getRealName());
                map.put("openIdCard", tEndUserInfo.getIdcard());
            }
            map.put("ifOneselfOpen",String.valueOf(tZtAccountOpenInfo.getIfOneselfOpen()));//是否非本人开户 fasle否 true是
        }
        map.put("auditStatus", tEndUserInfo.getAuditStatus());//审核状态
        map.put("auditOpinion", tEndUserInfo.getAuditOpinion());//审核意见
        map.put("userlogisticsRole", null == tEndUserInfo.getUserLogisticsRole() || StringUtils.isBlank(tEndUserInfo.getUserLogisticsRole()) ? "" : tEndUserInfo.getUserLogisticsRole());//CTYPEDRVIVER司机  CTYPEBOSS车老板
        map.put("balance", String.valueOf(balance) == "" ? "0.00" : String.valueOf(balance));//钱包余额
        map.put("carNum", String.valueOf(carNum));//车的数量
        //map.put("bankNum", String.valueOf(bankNum));//银行卡的数量
        map.put("dzh", String.valueOf(0));//待装货
        map.put("dxh", String.valueOf(0));//待卸货
        map.put("dsh", String.valueOf(0));//待收货
        map.put("dfk", String.valueOf(0));//待付款
        map.put("dpj",String.valueOf(0));//待评价
        map.put("ifPasswordSecurity",String.valueOf(tSysUser.getIfPasswordSecurity()));

        // 查询司机证件有效期
        map.put("driverStatus", DictEnum.PASSNODE.code.equals(tEndUserInfo.getAuditStatus()) ? "0" : "1");
        map.put("driverAuditStatus", null);
        if (DictEnum.PASSNODE.code.equals(tEndUserInfo.getAuditStatus())) {
            checkDriverIdentityValid(map, tEndUserInfo);
        }
        // 查询车辆证件有效期
        map.put("carAuditStatus", null);
        if (i == 0) {
            // 查询是否有审核中的车辆
            int selectCountMidStatus = tEndUserCarRelMapper.selectCountMidStatus(tEndUserCarRel);
            if (selectCountMidStatus > 0) {
                map.put("carStatus", "1"); // 要完善资料
            } else {
                checkCarIdentityValid(CurrentUser.getEndUserId(), map);
            }
        }
        //按登录人查询车辆
        List<TEndCarInfo> tEndCarInfos = tEndCarInfoMapper.selectByEndUserId(CurrentUser.getEndUserId());
        if(tEndCarInfos.size() > 0){
            for (TEndCarInfo tEndCarInfo : tEndCarInfos) {
                if (null != tEndCarInfo.getVehicleNumber()) {
                    TCarInsuranceVO carInsuranceVO = new TCarInsuranceVO();
                    carInsuranceVO.setVehicleNumber(tEndCarInfo.getVehicleNumber());
                    //按车牌号查询车辆投保信息
                    TCarInsuranceVO vo = carInsuranceAPI.selectByVehicleNumber(carInsuranceVO);
                    if (null != vo && null != vo.getAuditStatus() && DictEnum.PASSNODE.code.equals(vo.getAuditStatus())) {
                        Date date = new Date();
                        Date insuranceDateEnd = vo.getInsuranceDateEnd();
                        if (null != insuranceDateEnd && date.getTime() > insuranceDateEnd.getTime()) {
                            TCarInsurance carInsurance = new TCarInsurance();
                            BeanUtil.copyProperties(vo, carInsurance);
                            carInsurance.setAuditOpinion("车险已过期");
                            carInsurance.setAuditStatus(DictEnum.NOTPASSNODE.code);
                            carInsuranceAPI.updateByPrimaryKeySelective(carInsurance);
                        }
                    }
                }
            }
        }
        map.put("walletStatus", "1");
        if (null == tEndUserInfo.getIdcardPhoto1() || null == tEndUserInfo.getIdcardPhoto2()) {
            map.put("walletStatus", "0");
        }
        if (StringUtils.isAnyBlank(tEndUserInfo.getIdcardPhoto1(), tEndUserInfo.getIdcardPhoto2())) {
            map.put("walletStatus", "0");
        }

        return new ResultUtil(CodeEnum.SUCCESS.getCode(), map);
    }

    /**
     * 检查司机证件有效期
     * @param map
     * @param tEndUserInfo
     */
    private void checkDriverIdentityValid(Map<Object, String> map, TEndUserInfo tEndUserInfo) {
        boolean driverAuditStatus = true;
        String driverAuditOpinion = "";
        TEndUserAuditInfo endUserAuditInfo = endUserAuditInfoMapper.selectByEndUserId(tEndUserInfo.getId());
        boolean isNewStatus = true;
        if (null == endUserAuditInfo) {
            isNewStatus = false;
            endUserAuditInfo = new TEndUserAuditInfo();
            endUserAuditInfo.setEndUserId(tEndUserInfo.getId());
            TEndUserInfo endUserInfo = tEndUserInfoMapper.selectByPrimaryKey(tEndUserInfo.getId());
            if (endUserInfo.getUserLogisticsRole().contains(DictEnum.CTYPEDRVIVER.code)) {
                endUserAuditInfo.setUserLogisticsRole(DictEnum.CTYPEDRVIVER.code);
            }
            if (DictEnum.CTYPECAPTAIN.code.equals(endUserInfo.getUserLogisticsRole())) {
                endUserAuditInfo.setUserLogisticsRole(DictEnum.CTYPECAPTAIN.code);
            }

            endUserAuditInfo.setEnable(false);
            endUserAuditInfo.setCreateTime(new Date());
            endUserAuditInfo.setCreateUser(CurrentUser.getUserNickname());
        }
        // 身份证
        if (null == tEndUserInfo.getIdcardValidBeginning() || null == tEndUserInfo.getIdcardValidUntil()) {
            driverAuditStatus = false;
            driverAuditOpinion = StringUtils.addAuditOpinion(driverAuditOpinion, "身份证有效期为空");
            endUserAuditInfo.setIdcardStatus(DictEnum.PAPERNEEDUPDATE.code);
            endUserAuditInfo.setIdcardOpinion("身份证有效期为空");
        } else {
            // 判断身份证是否过期
            Date validUntil = tEndUserInfo.getIdcardValidUntil();
            if (tEndUserInfo.getIdcardValidBeginning().getTime() > tEndUserInfo.getIdcardValidUntil().getTime()) {
                validUntil = tEndUserInfo.getIdcardValidBeginning();
            }
            Integer checkValidDate = DateUtils.checkValidDate(validUntil);
            if (checkValidDate == 0) {
                driverAuditStatus = false;
                driverAuditOpinion = StringUtils.addAuditOpinion(driverAuditOpinion, "身份证已过期");
                endUserAuditInfo.setIdcardStatus(DictEnum.PAPERNEEDUPDATE.code);
                endUserAuditInfo.setIdcardOpinion("身份证已过期");
            } else if (checkValidDate == 2) {
                // 提醒
                map.put("driverAuditStatus", "即将过期");
            }
            if (checkValidDate > 0) {
                endUserAuditInfo.setIdcardStatus(DictEnum.PASSNODE.code);
                endUserAuditInfo.setIdcardOpinion("审核通过");
            }
        }
        if (DictEnum.CTYPEDRVIVER.code.equals(CurrentUser.getUserLogisticsRole())) {
            // 驾驶证
            if (null == tEndUserInfo.getDrivingLicencesValidBeginning() || null == tEndUserInfo.getDrivingLicencesValidUntil()) {
                driverAuditStatus = false;
                driverAuditOpinion = StringUtils.addAuditOpinion(driverAuditOpinion, "驾驶证有效期为空");
                endUserAuditInfo.setDrivingLicencesStatus(DictEnum.PAPERNEEDUPDATE.code);
                endUserAuditInfo.setDrivingLicencesOpinion("驾驶证有效期为空");
            } else {
                Date drivingLicencesValidUntil = tEndUserInfo.getDrivingLicencesValidUntil();
                Integer checkValidDate = DateUtils.checkValidDate(drivingLicencesValidUntil);
                if (checkValidDate == 0) {
                    driverAuditStatus = false;
                    driverAuditOpinion = StringUtils.addAuditOpinion(driverAuditOpinion, "驾驶证已过期");
                    endUserAuditInfo.setDrivingLicencesStatus(DictEnum.PAPERNEEDUPDATE.code);
                    endUserAuditInfo.setDrivingLicencesOpinion("驾驶证已过期");
                } else if (checkValidDate == 2) {
                    // 提醒
                    map.put("driverAuditStatus", "即将过期");
                }
                if (checkValidDate > 0) {
                    endUserAuditInfo.setDrivingLicencesStatus(DictEnum.PASSNODE.code);
                    endUserAuditInfo.setDrivingLicencesOpinion("审核通过");
                }
            }
            // 从业资格证
            if (null == tEndUserInfo.getCertificateValidUntil()) {
                driverAuditStatus = false;
                driverAuditOpinion = StringUtils.addAuditOpinion(driverAuditOpinion, "从业资格证有效期为空");
                endUserAuditInfo.setCertificateStatus(DictEnum.PAPERNEEDUPDATE.code);
                endUserAuditInfo.setCertificateOpinion("从业资格证有效期为空");
            } else {
                Date certificateValidUntil = tEndUserInfo.getCertificateValidUntil();
                Integer checkValidDate = DateUtils.checkValidDate(certificateValidUntil);
                if (checkValidDate == 0) {
                    driverAuditStatus = false;
                    driverAuditOpinion = StringUtils.addAuditOpinion(driverAuditOpinion, "从业资格证已过期");
                    endUserAuditInfo.setCertificateStatus(DictEnum.PAPERNEEDUPDATE.code);
                    endUserAuditInfo.setCertificateOpinion("从业资格证已过期");
                } else if (checkValidDate == 2) {
                    // 提醒
                    map.put("driverAuditStatus", "从业资格证即将过期");
                }
                if (checkValidDate > 0) {
                    endUserAuditInfo.setCertificateStatus(DictEnum.PASSNODE.code);
                    endUserAuditInfo.setCertificateOpinion("审核通过");
                }
            }
        }
        if (!driverAuditStatus) {
            map.put("driverAuditStatus", null);
            map.put("auditStatus", DictEnum.PAPERNEEDUPDATE.code);//审核状态
            map.put("auditOpinion", driverAuditOpinion);//审核意见
            map.put("driverStatus", "1");
            TEndUserInfo endUserInfo = new TEndUserInfo();
            endUserInfo.setId(tEndUserInfo.getId());
            endUserInfo.setAuditStatus(DictEnum.PAPERNEEDUPDATE.code);
            endUserInfo.setAuditOpinion(driverAuditOpinion);
            endUserInfo.setAuditTime(new Date());
            endUserInfo.setUpdateTime(new Date());
            tEndUserInfoMapper.updateByPrimaryKeySelective(endUserInfo);
            endUserAuditInfo.setUpdateTime(new Date());
            endUserAuditInfo.setUpdateUser(CurrentUser.getUserNickname());
            if (isNewStatus) {
                endUserAuditInfoMapper.updateByPrimaryKeySelective(endUserAuditInfo);
            } else {
                endUserAuditInfoMapper.insertSelective(endUserAuditInfo);
            }
        }
    }

    /**
     * 检查车辆证件有效期
     * @param endUserId
     * @param map
     */
    private void checkCarIdentityValid(Integer endUserId, Map<Object, String> map) {
        String carAuditOpinion = "";
        // 查询车辆信息
        List<TEndCarInfo> tEndCarInfos = tEndCarInfoMapper.selectPassnodeCarsByEndUserId(endUserId);
        for (TEndCarInfo tEndCarInfo : tEndCarInfos) {
            TEndCarAuditInfo endCarAuditInfo = endCarAuditInfoMapper.selectByEndCarId(tEndCarInfo.getId());
            boolean isNewStatus = true;
            if (null == endCarAuditInfo) {
                isNewStatus = false;
                endCarAuditInfo = new TEndCarAuditInfo();
                endCarAuditInfo.setEndCarId(tEndCarInfo.getId());
                endCarAuditInfo.setEnable(false);
                endCarAuditInfo.setCreateTime(new Date());
                endCarAuditInfo.setCreateUser(CurrentUser.getUserNickname());
            }
            boolean carAuditStatus = true;
            // TODO 道路运输许可证
            endCarAuditInfo.setRoadTransportOperationStatus(DictEnum.PASSNODE.code);
            endCarAuditInfo.setRoadTransportOperationOpinion("审核通过");
            // 行驶证
            if (null == tEndCarInfo.getCardrivingLicencesValidUntil()) {
                carAuditStatus = false;
                carAuditOpinion = StringUtils.addAuditOpinion(carAuditOpinion, "行驶证有效期为空");
                endCarAuditInfo.setDrivingLicencesStatus(DictEnum.PAPERNEEDUPDATE.code);
                endCarAuditInfo.setDrivingLicencesOpinion("行驶证有效期为空");
            } else {
                Integer checkValidDate = DateUtils.checkValidMonth(tEndCarInfo.getCardrivingLicencesValidUntil());
                if (checkValidDate == 0) {
                    carAuditStatus = false;
                    carAuditOpinion = StringUtils.addAuditOpinion(carAuditOpinion, "行驶证已过期");
                    endCarAuditInfo.setDrivingLicencesStatus(DictEnum.PAPERNEEDUPDATE.code);
                    endCarAuditInfo.setDrivingLicencesOpinion("行驶证已过期");
                } else if (checkValidDate == 2) {
                    // 提醒
                    map.put("carAuditStatus", "即将过期");
                }
            }
            if (!carAuditStatus) {
                map.put("carStatus", "1"); // 要完善资料
                TEndCarInfo endCarInfo = new TEndCarInfo();
                endCarInfo.setId(tEndCarInfo.getId());
                endCarInfo.setAuditStatus(DictEnum.PAPERNEEDUPDATE.code);
                endCarInfo.setAuditOpinion(carAuditOpinion);
                endCarInfo.setAuditTime(new Date());
                endCarInfo.setUpdateTime(new Date());
                tEndCarInfoMapper.updateByPrimaryKeySelective(endCarInfo);
                endCarAuditInfo.setUpdateTime(new Date());
                endCarAuditInfo.setUpdateUser(CurrentUser.getUserNickname());
                if (isNewStatus) {
                    endCarAuditInfoMapper.updateByPrimaryKeySelective(endCarAuditInfo);
                } else {
                    endCarAuditInfoMapper.insertSelective(endCarAuditInfo);
                }
            }
        }
    }

    /**
     * 根据 第三方id 查询相关账号信息
     *
     * @param thridParyId
     * @return
     */
    @Override
    public TAccountVo selectByThridParyId(String thridParyId) {

        return tAccountMapper.selectByThridParyId(thridParyId);
    }

    /**
     * 根据 t_sys_user id 查询 t_account 中的 信息
     * Yan
     *
     * @param id
     * @return
     */
    @Override
    public ResultUtil selectBySysUserId(Integer id) {
        TAccount tAccount = tAccountMapper.selectAccountBySysUserId(id);
        return ResultUtil.ok(tAccount);
    }

    /**
     * 创建C端用户
     *
     * @param accountVo         账号信息
     * @param userLogisticsRole 物流环节用户定位
     * <AUTHOR>
     */
    public TEndUserInfo createEnduerInfo(TAccountVo accountVo, String userLogisticsRole) {
        //TODO C端用户
        TEndUserInfo endUserInfo = new TEndUserInfo();
        endUserInfo.setRealName(accountVo.getNickname());
        endUserInfo.setUserLogisticsRole(userLogisticsRole);
        endUserInfo.setOrgName(accountVo.getOrgName());
        endUserInfo.setPhone(accountVo.getAccountNo());
        endUserInfo.setEnable(false);
        endUserInfo.setIdcard(accountVo.getIdcard());
        endUserInfo.setIdcardPhoto1(accountVo.getIdcardPhoto1());
        endUserInfo.setIdcardPhoto2(accountVo.getIdcardPhoto2());

        endUserInfo.setIdcardValidUntil(accountVo.getIdcardValidUntil());
        if (accountVo.getIdcardValidBeginning()!=null && !"".equals(accountVo.getIdcardValidBeginning())){
            endUserInfo.setIdcardValidBeginning(accountVo.getIdcardValidBeginning());
        }
        //营业执照
        if (accountVo.getCertificatePhoto1()!=null && !"".equals(accountVo.getCertificatePhoto1())){
            endUserInfo.setCertificatePhoto1(accountVo.getCertificatePhoto1());
        }
        endUserInfo.setAddress(accountVo.getAddress());
        if (StringUtils.isNotBlank(accountVo.getAddress())){
            endUserInfo.setAddressState(true);
        }else {
            endUserInfo.setAddressState(false);
        }
        endUserInfo.setAuditStatus("MIDNODE");
        endUserInfo.setCertificateNo(null != accountVo.getCertificateNo() ? accountVo.getCertificateNo() : "");
        tEndUserInfoMapper.insertSelective(endUserInfo);
        return endUserInfo;
    }

    /**
     * 创建C端用户与账户表关系
     * @param accountVo     账号信息
     * @param accountId     账号表id
     * @param endUserInfoId C端用户账户表id
     * <AUTHOR>
     */
    public TEnduserAccount createEnduerAccount(TAccountVo accountVo, Integer endUserInfoId, Integer accountId) {
        //TODO C端用户与账号表关系
        TEnduserAccount enduserAccount = new TEnduserAccount();
        enduserAccount.setEnduserId(endUserInfoId);
        enduserAccount.setAccountId(accountId);
        enduserAccount.setRealName(accountVo.getNickname());
        enduserAccount.setPhone(accountVo.getAccountNo());
        enduserAccount.setIdcard(accountVo.getIdcard());
        enduserAccount.setRealPosition(accountVo.getRealPosition());
        enduserAccount.setEnable(false);
        tEnduserAccountMapper.insert(enduserAccount);
        return enduserAccount;
    }

    /**
     * 创建线路货物员工关系、线路货物员工角色
     *
     * @param account             账号信息
     * @param lineGoodsUserRelVos 线路员工关系
     * @param userRole            线路员工角色
     * <AUTHOR>
     */
    public String createLineUserAndRole(TAccount account, List<TLineGoodsUserRelVo> lineGoodsUserRelVos,
                                        HashMap[] userRole,TAccountVo acVo, String userType) {
        int countResult = 0;
        StringBuffer stringBuffer = new StringBuffer();
        //线路货物员工关系
        for (TLineGoodsUserRelVo lineGoodsUserRelVo : lineGoodsUserRelVos) {
            lineGoodsUserRelVo.setAccountInfoId(account.getId());
            lineGoodsUserRelVo.setEnable(false);
            lineGoodsUserRelVo.setCurrentAccountNo(account.getAccountNo());
            lineGoodsUserRelVo.setCurrentAccountName(account.getNickname());
            lineGoodsUserRelVo.setCurrentAccountPhone(account.getAccountNo());
            TLineGoodsUserRelMember lineGoodsUserRel = new TLineGoodsUserRelMember();
            BeanUtil.copyProperties(lineGoodsUserRelVo, lineGoodsUserRel);

            // 线路员工角色权限表
            String[][] lineRoleId = lineGoodsUserRelVo.getLineRoleCode();
            TLineUserRolePro lineUserRolePro = new TLineUserRolePro();
            lineUserRolePro.setAccountId(account.getId());
            lineUserRolePro.setEnable(false);
            HashMap<String, List<String>> roleBtnMap = new HashMap<>();
            for (String[] strings : lineRoleId) {
                String role = strings[0];
                List<String> roleButton = (null == roleBtnMap.get(role) ? new ArrayList<>() : roleBtnMap.get(role));
                if(strings.length>1){
                    String button = strings[1];
                    roleButton.add(button);
                }
                roleBtnMap.put(role, roleButton);

            }

            for (Map.Entry map : roleBtnMap.entrySet()) {
                String role = (String) map.getKey();
                //发单员：判断线路是否已经有负责人
                if (role.equals(ROLE.COMPANYSENDORDER.code) || role.equals(ROLE.COMMONSENDORDER.code)) {
                    if (null != lineGoodsUserRelVo.getIfPrincipal() && lineGoodsUserRelVo.getIfPrincipal()) {
                        //查询线路是否已经有负责人
                        HashMap<String, Object> param = new HashMap();
                        param.put("lineGoodsRelId", lineGoodsUserRelVo.getLineGoodsRelId());
                        param.put("companyId", lineGoodsUserRelVo.getCompanyId());
                        List<String> roles = new ArrayList<>();
                        roles.add(ROLE.COMMONSENDORDER.code);
                        roles.add(ROLE.COMPANYSENDORDER.code);
                        param.put("roles", roles);
                        List<TLineGoodsUserRel> tLineGoodsUserRels = tLineGoodsUserRelMemberMapper.selectLineIfPrincipal(param);
                        if (null != tLineGoodsUserRels && tLineGoodsUserRels.size() > 0) {
                            for (TLineGoodsUserRel tLineGoodsUserRel: tLineGoodsUserRels){
                                if (null != tLineGoodsUserRel && null != tLineGoodsUserRel.getAccountInfoId()){
                                    if (!account.getId().equals(tLineGoodsUserRel.getAccountInfoId())){
                                        throw new RuntimeException("已有发单负责人");
                                    }
                                }
                            }
                        }
                    }
                }
                //收单员：判断线路是否已经有负责人
                if (role.equals(ROLE.RECEIVEORDER.code)) {
                    if (null != lineGoodsUserRelVo.getIfPrincipal() && lineGoodsUserRelVo.getIfPrincipal()) {
                        //查询线路是否已经有负责人
                        HashMap<String, Object> param = new HashMap();
                        param.put("lineGoodsRelId", lineGoodsUserRelVo.getLineGoodsRelId());
                        param.put("companyId", lineGoodsUserRelVo.getCompanyId());
                        List<String> roles = new ArrayList<>();
                        roles.add(ROLE.RECEIVEORDER.code);
                        param.put("roles", roles);
                        List<TLineGoodsUserRel> tLineGoodsUserRels = tLineGoodsUserRelMemberMapper.selectLineIfPrincipal(param);
                        if (null != tLineGoodsUserRels && tLineGoodsUserRels.size() > 0) {
                            for (TLineGoodsUserRel tLineGoodsUserRel: tLineGoodsUserRels){
                                if (null != tLineGoodsUserRel && null != tLineGoodsUserRel.getAccountInfoId()){
                                    if (!account.getId().equals(tLineGoodsUserRel.getAccountInfoId())){
                                        throw new RuntimeException("已有收单负责人");
                                    }
                                }
                            }
                        }
                    }
                }
                countResult++;
                List<String> button = (List<String>) map.getValue();
                for (HashMap hashMap : userRole) {
                    if (role.equals(hashMap.get("value"))) {
                        lineUserRolePro.setRoleCode(role);
                        lineUserRolePro.setRoleName(String.valueOf(hashMap.get("label")));
                        String valueContent = (String) hashMap.get("valueContent");
                        lineUserRolePro.setPerms(valueContent);
                        lineGoodsUserRel.setAuthorizedButtons(String.valueOf(hashMap.get("buttonContent")));
                    }
                }
                tLineUserRoleProMapper.insertSelective(lineUserRolePro);
                lineGoodsUserRel.setRoleCode(role);
                tLineGoodsUserRelMemberMapper.insertSelective(lineGoodsUserRel);
                lineGoodsUserRel.setId(null);
            }

            //TODO 企业账号关系表
            TCompanyAccount companyAccount = new TCompanyAccount();
            companyAccount.setAccountId(account.getId());
            companyAccount.setCompanyId(lineGoodsUserRelVo.getCompanyId());
            companyAccount.setIdcard(acVo.getIdcard());
            companyAccount.setRealPosition(acVo.getRealPosition());
            //查询用户是否和企业已有关系，如果没有创建
            int count = companyAccountMapper.selectAccountCompany(companyAccount);
            if (count == 0) {
                companyAccount.setEnable(false);
                companyAccountMapper.insertSelective(companyAccount);
            }

            //TODO 创建信息(经纪人)站与企业的关系
            if (userType.equals("CTYPEMANAGER")){
                //先判断是否已经存在
                TCompanyStationRel companyStationRel = new TCompanyStationRel();
                companyStationRel.setCompanyId(lineGoodsUserRelVo.getCompanyId());
                //信息站id修改为C端用户表id update by zhangjiji 2019.6.4
                companyStationRel.setEndUserStationId(acVo.getEnduserId());
                //添加是否删除：默认为false update by zhangjiji 2019.6.4
                companyStationRel.setEnable(false);
                List<TCompanyStationRel> tCompanyStationRels = companyStationRelMapper.selectCompanyStationRel(companyStationRel);
                if (null == tCompanyStationRels || tCompanyStationRels.size() == 0){
                    companyStationRelMapper.insertSelective(companyStationRel);
                }
            }
        }
        return stringBuffer.toString();
    }

    public ResultUtil createLineUserAndRoleNew(TAccount account, TLineGoodsUserRelVo lineGoodsUserRelVo) {
        //线路货物员工关系 t_line_goods_user_rel
        lineGoodsUserRelVo.setAccountInfoId(account.getId());
        lineGoodsUserRelVo.setEnable(false);
        lineGoodsUserRelVo.setCurrentAccountNo(account.getAccountNo());
        lineGoodsUserRelVo.setCurrentAccountName(account.getNickname());
        lineGoodsUserRelVo.setCurrentAccountPhone(account.getAccountNo());
        TLineGoodsUserRelMember lineGoodsUserRel = new TLineGoodsUserRelMember();
        BeanUtil.copyProperties(lineGoodsUserRelVo, lineGoodsUserRel);

        // 线路员工角色权限表 t_line_user_role_pro
        String[][] lineRoleId = lineGoodsUserRelVo.getLineRoleCodeNew();
        TLineUserRolePro lineUserRolePro = new TLineUserRolePro();
        lineUserRolePro.setAccountId(account.getId());
        lineUserRolePro.setEnable(false);
        HashMap<String, List<String>> roleBtnMap = new HashMap<>();
        for (String[] strings : lineRoleId) {
            String role = strings[0];
            List<String> roleButton = (null == roleBtnMap.get(role) ? new ArrayList<>() : roleBtnMap.get(role));
            if(strings.length>1){
                String button = strings[1];
                roleButton.add(button);
            }
            roleBtnMap.put(role, roleButton);
        }

        for (Map.Entry<String, List<String>> map : roleBtnMap.entrySet()) {
            String role = map.getKey();

            //发单员：判断线路是否已经有负责人
            if (role.equals(ROLE.COMPANYSENDORDER.code) || role.equals(ROLE.COMMONSENDORDER.code)) {
                if (null != lineGoodsUserRelVo.getIfPrincipal() && lineGoodsUserRelVo.getIfPrincipal()) {
                    //查询线路是否已经有负责人
                    HashMap<String, Object> param = new HashMap();
                    param.put("lineGoodsRelId", lineGoodsUserRelVo.getLineGoodsRelId());
                    param.put("companyId", lineGoodsUserRelVo.getCompanyId());
                    List<String> roles = new ArrayList<>();
                    roles.add(ROLE.COMMONSENDORDER.code);
                    roles.add(ROLE.COMPANYSENDORDER.code);
                    param.put("roles", roles);
                    List<TLineGoodsUserRel> tLineGoodsUserRels = tLineGoodsUserRelMemberMapper.selectLineIfPrincipal(param);
                    if (null != tLineGoodsUserRels && tLineGoodsUserRels.size() > 0) {
                        for (TLineGoodsUserRel tLineGoodsUserRel: tLineGoodsUserRels){
                            if (null != tLineGoodsUserRel && null != tLineGoodsUserRel.getAccountInfoId()){
                                if (!account.getId().equals(tLineGoodsUserRel.getAccountInfoId())){
                                    throw new RuntimeException("已有发单负责人");
                                }
                            }
                        }
                    }
                }
            }
            //收单员：判断线路是否已经有负责人
            if (role.equals(ROLE.RECEIVEORDER.code)) {
                if (null != lineGoodsUserRelVo.getIfPrincipal() && lineGoodsUserRelVo.getIfPrincipal()) {
                    //查询线路是否已经有负责人
                    HashMap<String, Object> param = new HashMap();
                    param.put("lineGoodsRelId", lineGoodsUserRelVo.getLineGoodsRelId());
                    param.put("companyId", lineGoodsUserRelVo.getCompanyId());
                    List<String> roles = new ArrayList<>();
                    roles.add(ROLE.RECEIVEORDER.code);
                    param.put("roles", roles);
                    List<TLineGoodsUserRel> tLineGoodsUserRels = tLineGoodsUserRelMemberMapper.selectLineIfPrincipal(param);
                    if (null != tLineGoodsUserRels && tLineGoodsUserRels.size() > 0) {
                        for (TLineGoodsUserRel tLineGoodsUserRel: tLineGoodsUserRels){
                            if (null != tLineGoodsUserRel && null != tLineGoodsUserRel.getAccountInfoId()){
                                if (!account.getId().equals(tLineGoodsUserRel.getAccountInfoId())){
                                    throw new RuntimeException("已有收单负责人");
                                }
                            }
                        }
                    }
                }
            }
            DicCatItemDTO dataByItemCode = dicCatItemAPI.getDataByItemCode(role);
            if(null != dataByItemCode){
                lineUserRolePro.setRoleCode(role);
                lineUserRolePro.setRoleName(dataByItemCode.getItemValue());
                lineUserRolePro.setPerms(dataByItemCode.getValueContent());
                lineUserRolePro.setButtons(String.join(",", map.getValue()));
                tLineUserRoleProMapper.insertSelective(lineUserRolePro);
                lineGoodsUserRel.setAuthorizedButtons(dataByItemCode.getButtonContent());
                lineGoodsUserRel.setRoleCode(role);
                tLineGoodsUserRelMemberMapper.insertSelective(lineGoodsUserRel);
                lineGoodsUserRel.setId(null);
            }
            //第一次添加货源时
            TCompanyAccount dataByAccountId = companyAccountMapper.getDataByAccountId(account.getId());
            if(null != dataByAccountId){
                dataByAccountId.setCompanyId(lineGoodsUserRelVo.getCompanyId());
                companyAccountMapper.updateByPrimaryKeySelective(dataByAccountId);
            }else{
                TCompanyAccount companyAccount = companyAccountMapper.selectByAccountId(account.getId(), lineGoodsUserRelVo.getCompanyId());
                List<TCompanyAccount> companyAccounts = companyAccountMapper.getByAccountId(account.getId());
                if(null == companyAccount){
                    companyAccount = new TCompanyAccount();
                    companyAccount.setAccountId(account.getId());
                    companyAccount.setCompanyId(lineGoodsUserRelVo.getCompanyId());
                    companyAccount.setRealName(account.getNickname());
                    companyAccount.setPhone(account.getAccountNo());
                    if(null != companyAccounts && companyAccounts.size() > 0){
                        companyAccount.setRealPosition(companyAccounts.get(0).getRealPosition());
                        companyAccount.setIdcard(companyAccounts.get(0).getIdcard());
                    }
                    companyAccount.setEnable(false);
                    companyAccountMapper.insertSelective(companyAccount);
                }
            }
        }
        return ResultUtil.ok();
    }

    public TAccount selectByPrimaryKey(Integer accountid) {
        return tAccountMapper.selectByPrimaryKey(accountid);
    }

    public int updateByPrimaryKey(TAccount tAccount) {
        return tAccountMapper.updateByPrimaryKey(tAccount);
    }


    /**
     * @Description 根据手机号查询账号信息
     * <AUTHOR>
     * @Date 2019/6/20 11:28
     * @Param
     * @Return
     * @Exception
     */
    @Override
    public ResultUtil selectAccountByPhone(TAccountVo record) {
        TAccount account = tAccountMapper.selectAccountByPhone(record);
        return ResultUtil.ok(account);
    }

    @Override
    public int updateOpenId(TAccountVo record) {
        if (null == record.getThridParyId()
                || StringUtils.isBlank(record.getThridParyId())
                || "undefined".equals(record.getThridParyId())
                || "1".equals(record.getThridParyId())
                || "null".equals(record.getThridParyId())) {
            return 0;
        }
        List<TAccount> tAccounts = tAccountMapper.selectListByThridParyId(record.getThridParyId());
        if (null != tAccounts && !tAccounts.isEmpty()) {
            for (TAccount tAccount : tAccounts) {
                tAccountMapper.updateOpenIdNullByAccountId(tAccount.getId());
            }
        }
        int i = tAccountMapper.updateOpenId(record);
        return i;
    }

    @Override
    public List<TAccountDTO> selectOpenIdByEnduserId(List<Integer> enduserIds) {
        HashMap map = new HashMap();
        map.put("enduserIds", enduserIds);
        List<TAccountDTO> tAccountDTOS = tAccountMapper.selectOpenIdByEnduserId(map);
        return tAccountDTOS;
    }

    @LcnTransaction(propagation = DTXPropagation.SUPPORTS)
    @Transactional
    @Override
    public ResultUtil updateAccountForFeign(TAccount record) {
        int i = tAccountMapper.updateAccountForFeign(record);
        TCompanyAccount companyAccount = new TCompanyAccount();
        companyAccount.setAccountId(record.getId());
        if (null != record.getNickname()) {
            companyAccount.setRealName(record.getNickname());
        }
        if (null != record.getEnable()) {
            companyAccount.setEnable(record.getEnable());
        }
        companyAccountMapper.updateByAccountId(companyAccount);
        return ResultUtil.ok(record);
    }

    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil deleteManager(Integer[] accountIds) {
        List<Integer> acountIds = Arrays.asList(accountIds);
        List<HashMap<String, Integer>> enduserAccounts = tAccountMapper.selectEnduserIdByAccountId(acountIds);
        ResultUtil resultUtil = tOrderInfoAPI.selectAgentManagerUnCompleteOrder(enduserAccounts);
        if (null != resultUtil && null != resultUtil.getCode() && resultUtil.getCode().equals("success")) {
            if (null != resultUtil.getData()){
                List<HashMap<String, Integer>> results = (List<HashMap<String, Integer>>) resultUtil.getData();
                if (results.size() > 0) {
                    for (HashMap<String, Integer> map : results) {
                        Integer count = map.get("count");
                        Integer accountId = map.get("accountId");
                        if (count > 0) {
                            return ResultUtil.error("当前勾选的业务部下存在未完成的运单，不允许删除");
                        }
                        List<Integer> integers = new ArrayList<>();
                        integers.add(accountId);
                        List<HashMap<String, Integer>> hashMaps = tAccountMapper.selectEnduserIdByAccountId(integers);
                        if (hashMaps!=null && hashMaps.size()!=0){
                            HashMap<String, Integer> hashMap = hashMaps.get(0);
                            TEndUserInfo tEndUserInfo = new TEndUserInfo();
                            tEndUserInfo.setId(hashMap.get("enduserId"));
                            tEndUserInfo.setEnable(true);
                            tEndUserInfoMapper.updateByPrimaryKeySelective(tEndUserInfo);
                        }else {
                            return ResultUtil.error("修改C端用户表失败");
                        }

                        TAccount account = new TAccount();
                        account.setId(accountId);
                        account.setEnable(true);
                        tAccountMapper.updateByPrimaryKeySelective(account);

                        TAccount tAccount = tAccountMapper.selectByPrimaryKey(accountId);
                        // 将TuserInfo置为删除
                        TUserInfo tUserInfo = new TUserInfo();
                        tUserInfo.setAccountId(tAccount.getUserId());
                        tUserInfo.setEnable(true);
                        systemAPI.updateByAccountIdSelective(tUserInfo);

                       //根据账号表获取userid
                        TAccount ac = tAccountMapper.selectByPrimaryKey(Integer.valueOf(map.get("accountId").toString()));
                        TSysUser user = new TSysUser();
                        user.setId(ac.getUserId());
                        user.setEnable(true);
                        systemAPI.updateUser(user);
                        tEnduserAccountMapper.updateEnduserAccount(accountId);
                        for (HashMap<String, Integer> hashMap : enduserAccounts) {
                            Integer accountIdMap = hashMap.get("accountId");
                            if (accountIdMap.equals(accountId)) {
                                Integer enduserId = hashMap.get("enduserId");
                                companyStationRelMapper.updateCompanyStationRel(enduserId);
                            }
                        }
                        companyAccountMapper.updateCompanyAccount(accountId);
                        //线路货物员工关系表
                        List<TLineGoodsUserRelMember> goodsUserRelsList = tLineGoodsUserRelMemberMapper.selectByAccountId(accountId);
                        for (TLineGoodsUserRelMember goodsUserRel : goodsUserRelsList) {
                            goodsUserRel.setEnable(true);
                            tLineGoodsUserRelMemberMapper.updateByPrimaryKey(goodsUserRel);
                            TLineUserRolePro lineUserRolePro = new TLineUserRolePro();
                            lineUserRolePro.setAccountId(accountId);
                            lineUserRolePro.setEnable(true);
                            tLineUserRoleProMapper.updateByRecord(lineUserRolePro);
                        }
                    }
                }
            }
        }
        return ResultUtil.ok();
    }

    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil updateSystemPassword(Integer userid, String password) {
        TSysUser tSysUser = systemAPI.selectByPrimaryKey(userid);

        String token = "";
        String header = request.getHeader("Authorization");
        if (StringUtils.isNotEmpty(header)) {
            if (header.contains("%20")) {
                header = header.replace("%20", " ");
            }
            String[] tokenWithPrefix = header.split(" ");
            token = tokenWithPrefix[1];
        } else {
            token = (String) redisUtil.get(tSysUser.getUsername());
        }
        if (StringUtils.isNotEmpty(token)) {
            redisUtil.del("access:" + token);
            redisUtil.del("access_to_refresh:" + token);
            redisUtil.del("auth:" + token);
            MessageDigest digest;
            String currentUsername = tSysUser.getUsername();
            Map<String, String> values = new LinkedHashMap<String, String>();
            values.put("username", currentUsername);
            values.put("client_id", "client");
            values.put("scope", "success");
            String key = "";
            try {
                digest = MessageDigest.getInstance("MD5");
                byte[] bytes = digest.digest(values.toString().getBytes("UTF-8"));
                key = String.format("%032x", new BigInteger(1, bytes));
                log.info(key);
            } catch (Exception e) {
                log.error("", e);
            }
            redisUtil.del("auth_to_access:" + key);
        }
        systemAPI.updatePassword(userid, password);
        return ResultUtil.ok();
    }

    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil updateMember(TAccountVo record) {
        redisUtil.del("sysUserId-" + record.getUserId());
        Integer companyIdInteger = null;
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        if (userCompanyId==null) {
            if (record.getCompanyId()!=null){
                ArrayList<String> arrayList = new ArrayList<>();
                arrayList.add(String.valueOf(record.getCompanyId()));
                userCompanyId=arrayList;
          }
        }
        //修改系统用户
        TAccount account = new TAccount();
        account.setAccountNo(record.getAccountNo());
        account.setId(record.getId());
        List<TAccount> accounts = tAccountMapper.selectRepeatAccountNo(account);
        if (null != accounts && accounts.size() > 0) {
            return ResultUtil.error("账号已存在");
        }
        //如果姓名或账号修改，则修改对应系统用户信息，否则不修改
        TAccount selectAccount = tAccountMapper.selectByPrimaryKey(record.getId());

        //修改员工
        TAccount ac = new TAccount();
        ac.setAccountNo(record.getAccountNo());
        ac.setNickname(record.getNickname());
        ac.setId(record.getId());
        ac.setUpdateUser(record.getUpdateUser());
        ac.setUpdateTime(new Date());
        tAccountMapper.updateByPrimaryKeySelective(ac);

        if (null != userCompanyId && userCompanyId.size() > 0) {
            String companyId = userCompanyId.get(0);
            companyIdInteger = Integer.valueOf(companyId);
            // 企业账号关系表
            TCompanyAccount tCompanyAccount =  companyAccountMapper.selectByAccountId(ac.getId(),companyIdInteger);
            if(tCompanyAccount!=null &&!"".equals(tCompanyAccount)){
                tCompanyAccount.setRealName(record.getNickname());
                tCompanyAccount.setPhone(record.getAccountNo());
                tCompanyAccount.setRealPosition(record.getRealPosition());
                tCompanyAccount.setIdcard(record.getIdcard());
                companyAccountMapper.updateByPrimaryKeySelective(tCompanyAccount);
            }
        }else{
            if(record.getList().size()>0){
                for (TLineGoodsUserRelVo lineGoodsUserRelVo : record.getList()) {
                    //如果是修改员工线路角色
                    if (lineGoodsUserRelVo.getState() == 0) {
                        TCompanyAccount tCompanyAccount =  companyAccountMapper.selectByAccountId(ac.getId(),lineGoodsUserRelVo.getCompanyId());
                        if(tCompanyAccount!=null &&!"".equals(tCompanyAccount)){
                            tCompanyAccount.setRealName(record.getNickname());
                            tCompanyAccount.setPhone(record.getAccountNo());
                            tCompanyAccount.setRealPosition(record.getRealPosition());
                            tCompanyAccount.setIdcard(record.getIdcard());
                            companyAccountMapper.updateByPrimaryKeySelective(tCompanyAccount);
                        }
                    }
                    //如果是删除员工线路角色
                    if (lineGoodsUserRelVo.getState() == 1) {
                        TCompanyAccount companyAccount = new TCompanyAccount();
                        companyAccount.setAccountId(record.getId());
                        companyAccount.setCompanyId(lineGoodsUserRelVo.getCompanyId());

                    }
                }
            }else{
                TCompanyAccount tCompanyAccount =  companyAccountMapper.selectByAccount(ac.getId());
                if(tCompanyAccount!=null &&!"".equals(tCompanyAccount)){
                    tCompanyAccount.setRealName(record.getNickname());
                    tCompanyAccount.setPhone(record.getAccountNo());
                    tCompanyAccount.setRealPosition(record.getRealPosition());
                    tCompanyAccount.setIdcard(record.getIdcard());
                    companyAccountMapper.updateByPrimaryKeySelective(tCompanyAccount);
                }
            }
        }

        String userLogisticsRole = null != record.getUserLogisticsRole() ? record.getUserLogisticsRole() : "";
        if (StringUtils.isNotEmpty(userLogisticsRole)) {
            //修改C端用户表
            TEndUserInfo endUserInfo = new TEndUserInfo();
            endUserInfo.setId(record.getEnduserId());
            endUserInfo.setRealName(record.getNickname());
            endUserInfo.setOrgName(record.getOrgName());
            endUserInfo.setIdcard(record.getIdcard());
            endUserInfo.setPhone(record.getAccountNo());
            endUserInfo.setIdcardValidUntil(record.getIdcardValidUntil());
            endUserInfo.setIdcardPhoto1(record.getIdcardPhoto1());
            endUserInfo.setIdcardPhoto2(record.getIdcardPhoto2());
            endUserInfo.setCertificateNo(null != record.getCertificateNo() ? record.getCertificateNo() : "");
            tEndUserInfoMapper.updateByPrimaryKeySelective(endUserInfo);

            //修改C端用户账号表
            TEnduserAccount enduserAccount = new TEnduserAccount();
            enduserAccount.setId(record.getEnduserAccountId());
            enduserAccount.setRealName(record.getNickname());
            enduserAccount.setIdcard(record.getIdcard());
            enduserAccount.setPhone(record.getAccountNo());
            tEnduserAccountMapper.updateByPrimaryKeySelective(enduserAccount);
        }else{
            if(!"BD".equals(record.getUsertype())){
                TEnduserAccount enduserAccount = tEnduserAccountMapper.selectEnduserAccount(ac.getId());
                TEndUserInfo endUserInfo = tEndUserInfoMapper.selectByPrimaryKey(enduserAccount.getEnduserId());
                endUserInfo.setRealName(record.getNickname());
                endUserInfo.setOrgName(record.getOrgName());
                endUserInfo.setIdcard(record.getIdcard());
                endUserInfo.setPhone(record.getAccountNo());
                endUserInfo.setIdcardValidUntil(record.getIdcardValidUntil());
                endUserInfo.setIdcardPhoto1(record.getIdcardPhoto1());
                endUserInfo.setIdcardPhoto2(record.getIdcardPhoto2());
                tEndUserInfoMapper.updateByPrimaryKeySelective(endUserInfo);

                enduserAccount.setRealName(record.getNickname());
                enduserAccount.setIdcard(record.getIdcard());
                enduserAccount.setPhone(record.getAccountNo());
                tEnduserAccountMapper.updateByPrimaryKeySelective(enduserAccount);
            }
        }
        // 如果不是经纪人个人、经纪人企业
        if (!DictEnum.CTYPEAGENTCOMPANY.code.equals(userLogisticsRole)
                && !DictEnum.CTYPEAGENTPERSON.code.equals(userLogisticsRole)) {
            // 删除员工线路关系
            TLineGoodsUserRelMember lineGoodsUserRelMember = new TLineGoodsUserRelMember();
            lineGoodsUserRelMember.setAccountInfoId(record.getId());
            tLineGoodsUserRelMemberMapper.updateUserLineRoleEnbaleByAccountId(lineGoodsUserRelMember);

            // 删除员工线路权限
            TLineUserRolePro lineUserRolePro = new TLineUserRolePro();
            lineUserRolePro.setAccountId(record.getId());
            lineUserRolePro.setEnable(true);
            tLineUserRoleProMapper.updateByRecord(lineUserRolePro);

            HashMap[] lineRoles = record.getLineRoles();
            HashSet<Integer> newCompanyIds = new HashSet<>();
            for (TLineGoodsUserRelVo lineGoodsUserRelVo : record.getList()) {
                lineGoodsUserRelVo.setAccountInfoId(ac.getId());
                lineGoodsUserRelVo.setCurrentAccountNo(ac.getAccountNo());
                lineGoodsUserRelVo.setCurrentAccountName(ac.getNickname());
                lineGoodsUserRelVo.setCurrentAccountPhone(ac.getAccountNo());
                TLineGoodsUserRelMember lineGoodsUserRel = new TLineGoodsUserRelMember();
                BeanUtil.copyProperties(lineGoodsUserRelVo, lineGoodsUserRel);
                //修改
                if (lineGoodsUserRelVo.getState() == 0) {
                    //角色线路获取员工角色和按钮(key:role, value:buttons)
                    ResultUtil label = saveLineGoodsUserRel(ROLE.MODSAVE.code, lineRoles, lineGoodsUserRelVo, lineGoodsUserRel);
                    if (label != null) return label;
                }
                //新增
                if (lineGoodsUserRelVo.getState() == 2) {
                    newCompanyIds.add(lineGoodsUserRelVo.getCompanyId());
                    ResultUtil label = saveLineGoodsUserRel(ROLE.NEWSAVE.code, lineRoles, lineGoodsUserRelVo, lineGoodsUserRel);
                    if (label != null) return label;
                }
            }
            // 创建信息(经纪人)站与企业的关系 企业账号关系表
            for (Integer companyId : newCompanyIds) {
                record.setCompanyId(companyId);
                // 企业账号关系表
                createCompanyAccount(record);
                if (DictEnum.CTYPEMANAGER.code.equals(record.getUserLogisticsRole())) {
                    // 创建信息(经纪人)站与企业的关系
                    createCompanyStationRel(record);
                }
            }
        }

        TSysUserVO user = new TSysUserVO();
        user.setId(selectAccount.getUserId());
        user.setUsername(record.getAccountNo());
        user.setAccountNo(record.getAccountNo());
        user.setNickname(record.getNickname());
       // user.setRolesCode(roleCodes);
        ResultUtil resultUtil = systemAPI.updateUserByAccountNo(user);
        String code = resultUtil.getCode();
        if (code.equals("error")){
            throw new RuntimeException(resultUtil.getMsg());
        }

        return ResultUtil.ok();
    }

    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil updateMemberNew(TAccountVo record) {
        redisUtil.del("sysUserId-" + record.getUserId());
        Integer companyIdInteger = null;
        TAccount tAccount = tAccountMapper.selectByPrimaryKey(record.getId());//根据ID查询t_account表
        //保存系统用户
        if(null != tAccount){
            List<TSysUser> tSysUsers = systemAPI.selectByPhoneAndId(record.getAccountNo(),tAccount.getUserId());
            if (null != tSysUsers && tSysUsers.size() > 0){
                return ResultUtil.error("用户已存在");
            }
            List<String> userCompanyId = CurrentUser.getUserCompanyId();
            if (null != userCompanyId && userCompanyId.size() > 0){
                String companyId = userCompanyId.get(0);
                companyIdInteger = Integer.valueOf(companyId);
            }else{
                companyIdInteger = record.getCompanyId();
            }
            TSysUser tSysUser = systemAPI.selectByPrimaryKey(tAccount.getUserId());//根据ID查询t_sys_user表
            List<TLineGoodsUserRelMember> list = tLineGoodsUserRelMemberMapper.
                    selectByAccountId(tAccount.getId());//根据accountId查询t_line_goods_user_rel表
            TCompanyAccount tCompanyAccount = new TCompanyAccount();
            if(null != companyIdInteger){
                tCompanyAccount = companyAccountMapper.selectByParam(tAccount.getId(),companyIdInteger);
            }
            //修改 t_account 表数据
            TAccount account = new TAccount();
            account.setAccountNo(record.getAccountNo());
            account.setNickname(record.getNickname());
            account.setId(tAccount.getId());
            tAccountMapper.updateByPrimaryKeySelective(account);
            //修改 t_sys_user 表数据
            if(null != tSysUser){
                tSysUser.setAccountNo(record.getAccountNo());
                tSysUser.setUsername(record.getAccountNo());
                tSysUser.setNickname(record.getNickname());
                systemAPI.updateSysUserById(tSysUser);
            }

            //修改 t_company_account 表数据
            if(null != tCompanyAccount && null != companyIdInteger){
                tCompanyAccount.setPhone(record.getAccountNo());
                tCompanyAccount.setRealName(record.getNickname());
                tCompanyAccount.setRealPosition(record.getRealPosition());
                tCompanyAccount.setIdcard(record.getIdcard());
                companyAccountMapper.updateByPrimaryKeySelective(tCompanyAccount);
            }
            //修改 t_line_goods_user_rel 表数据
            for (TLineGoodsUserRelMember member : list) {
                member.setCurrentAccountNo(record.getAccountNo());
                member.setCurrentAccountPhone(record.getAccountNo());
                member.setCurrentAccountName(record.getNickname());
                tLineGoodsUserRelMemberMapper.updateByPrimaryKeySelective(member);
            }
            // 业务部
            if (DictEnum.CD.code.equals(tAccount.getUsertype())) {
                List<TEndUserInfo> tEndUserInfos = tEnduserAccountMapper.selectEnduserAccountUserRole(tAccount.getId());
                if (null != tEndUserInfos && !tEndUserInfos.isEmpty()) {
                    TEndUserInfo tEndUserInfo = tEndUserInfos.get(0);
                    //修改C端用户表
                    TEndUserInfo endUserInfo = new TEndUserInfo();
                    endUserInfo.setId(tEndUserInfo.getId());
                    endUserInfo.setRealName(record.getNickname());
                    endUserInfo.setIdcard(record.getIdcard());
                    endUserInfo.setPhone(record.getAccountNo());
                    tEndUserInfoMapper.updateByPrimaryKeySelective(endUserInfo);

                    //修改C端用户账号表
                    TEnduserAccount tEnduserAccount = tEnduserAccountMapper.selectByEndUserId(tEndUserInfo.getId());
                    TEnduserAccount enduserAccount = new TEnduserAccount();
                    enduserAccount.setId(tEnduserAccount.getId());
                    enduserAccount.setRealName(record.getNickname());
                    enduserAccount.setIdcard(record.getIdcard());
                    enduserAccount.setPhone(record.getAccountNo());
                    tEnduserAccountMapper.updateByPrimaryKeySelective(enduserAccount);
                }
            }
            return ResultUtil.ok("修改成功");
        }else {
            return ResultUtil.error("未查询到员工数据");
        }
    }

    /*
     * <AUTHOR>
     * @Description 创建信息(经纪人)站与企业的关系
     * @Date 2019/11/27 10:32
     * @Param
     * @return
    **/
    private void createCompanyStationRel(TAccountVo record) {
        //TODO 创建信息(经纪人)站与企业的关系
        //先判断是否已经存在
        TCompanyStationRel companyStationRel = new TCompanyStationRel();
        companyStationRel.setCompanyId(record.getCompanyId());
        //信息站id修改为C端用户表id update by zhangjiji 2019.6.4
        companyStationRel.setEndUserStationId(record.getEnduserId());
        //添加是否删除：默认为false update by zhangjiji 2019.6.4
        companyStationRel.setEnable(false);
        List<TCompanyStationRel> tCompanyStationRels = companyStationRelMapper.selectCompanyStationRel(companyStationRel);
        if (null == tCompanyStationRels || tCompanyStationRels.size() == 0) {
            companyStationRelMapper.insertSelective(companyStationRel);
        }
    }

    /*
     * <AUTHOR>
     * @Description 企业账号关系表
     * @Date 2019/11/27 10:32
     * @Param
     * @return
    **/
    private void createCompanyAccount(TAccountVo record) {
        //TODO 企业账号关系表
        TCompanyAccount companyAccount = new TCompanyAccount();
        companyAccount.setAccountId(record.getId());
        companyAccount.setCompanyId(record.getCompanyId());
        companyAccount.setIdcard(record.getIdcard());
        companyAccount.setRealPosition(record.getRealPosition());
        companyAccount.setRealName("");
        //查询用户是否和企业已有关系，如果没有创建
        int count = companyAccountMapper.selectAccountCompany(companyAccount);
        if (count == 0) {
            companyAccount.setEnable(false);
            companyAccountMapper.insertSelective(companyAccount);
        }
    }

    /*
     * <AUTHOR>
     * @Description 保存员工线路角色
     * @Date 2019/11/27 10:00
     * @Param
     * @return
    **/
    private ResultUtil saveLineGoodsUserRel(String type, HashMap[] lineRoles, TLineGoodsUserRelVo lineGoodsUserRelVo,
                                            TLineGoodsUserRelMember lineGoodsUserRel) {
        ArrayList<String[]> roleCodes = lineGoodsUserRelVo.getRoleCodes();
        Map<String, ArrayList<String>> modRoleButton;
        if (ROLE.MODSAVE.code.equals(type)) {
            modRoleButton = getModRoleButton(roleCodes);
        } else {
            modRoleButton = newRoleButton(lineGoodsUserRelVo);
        }
        for (Map.Entry map : modRoleButton.entrySet()) {
            Integer accountId = lineGoodsUserRelVo.getAccountInfoId();
            String role = (String) map.getKey();
            //发单员：判断线路是否已经有负责人
            List<String> roles = new ArrayList<>();
            roles.add(ROLE.COMMONSENDORDER.code);
            roles.add(ROLE.COMPANYSENDORDER.code);
            checkPrincipal(accountId, lineGoodsUserRelVo, role, roles, ROLE.SENDEREXIST.value);
            //收单员：判断线路是否已经有负责人
            roles.clear();
            roles.add(ROLE.RECEIVEORDER.code);
            checkPrincipal(accountId, lineGoodsUserRelVo, role, roles, ROLE.RECEIVEEXIST.value);

            List<String> button = (List<String>) map.getValue();
            TLineUserRolePro lineUserRolePro = new TLineUserRolePro();
            lineUserRolePro.setAccountId(accountId);
            for (HashMap hashMap : lineRoles) {
                if (role.equals(hashMap.get("value"))) {
                    lineUserRolePro.setRoleCode(role);
                    lineUserRolePro.setRoleName(String.valueOf(hashMap.get("label")));
                    String perms = (String) hashMap.get("valueContent");
                    lineUserRolePro.setPerms(perms);
                    String buttons = String.valueOf(hashMap.get("buttonContent"));
                    lineUserRolePro.setButtons(buttons);
                    lineGoodsUserRel.setAuthorizedButtons(String.join(",", button));
                }
            }
            //查询员工在这个企业线路线路是否已有此角色，如果没有，添加；否则不添加
            TLineGoodsUserRelMember member = new TLineGoodsUserRelMember();
            member.setAccountInfoId(accountId);
            member.setCompanyId(lineGoodsUserRelVo.getCompanyId());
            member.setLineGoodsRelId(lineGoodsUserRelVo.getLineGoodsRelId());
            List<String> members = tLineGoodsUserRelMemberMapper.selectUserLineRoleForCompany(member);
            if (!members.contains(role)) {
                //查询角色权限表是否已经存在此角色：存在不添加；不存在则添加
                List<LineUserRoleDTO> lineUserRoleDTOS = tLineUserRoleProMapper.selectUserLineRole(accountId);
                List<String> hasRole = new ArrayList<>();
                for (LineUserRoleDTO roleDTO : lineUserRoleDTOS) {
                    hasRole.add(roleDTO.getRoleCode());
                }
                if (!hasRole.contains(role)) {
                    tLineUserRoleProMapper.insertSelective(lineUserRolePro);
                }
                lineGoodsUserRel.setRoleCode(role);
                tLineGoodsUserRelMemberMapper.insertSelective(lineGoodsUserRel);
                lineGoodsUserRel.setId(null);
            }
        }
        return null;
    }

    private ResultUtil saveLineGoodsUserRelNew(TLineGoodsUserRelVo lineGoodsUserRelVo, TLineGoodsUserRelMember lineGoodsUserRel) {
        Integer accountId = lineGoodsUserRelVo.getAccountInfoId();
        String[][] lineRoleCodeNew = lineGoodsUserRelVo.getLineRoleCodeNew();
        List<String> list = new ArrayList<>();
        for (String[] strings : lineRoleCodeNew) {
            list.add(strings[0]);
        }
        List<String> lineRoleCode = new ArrayList<>();
        for (String s : list) {
            if (!lineRoleCode.contains(s)) {
                lineRoleCode.add(s);
            }
        }

        // 角色按钮
        Map<String, String> roleButtons = convertToMap(lineRoleCodeNew);

        for (String role : lineRoleCode) {
            //发单员：判断线路是否已经有负责人
            List<String> roles = new ArrayList<>();
            roles.add(ROLE.COMMONSENDORDER.code);
            roles.add(ROLE.COMPANYSENDORDER.code);
            checkPrincipal(accountId, lineGoodsUserRelVo, role, roles, ROLE.SENDEREXIST.value);
            //收单员：判断线路是否已经有负责人
            roles.clear();
            roles.add(ROLE.RECEIVEORDER.code);
            checkPrincipal(accountId, lineGoodsUserRelVo, role, roles, ROLE.RECEIVEEXIST.value);

            TLineUserRolePro lineUserRolePro = new TLineUserRolePro();
            lineUserRolePro.setAccountId(accountId);
            DicCatItemDTO dataByItemCode = dicCatItemAPI.getDataByItemCode(role);
            if(null != dataByItemCode){
                lineUserRolePro.setRoleCode(role);
                lineUserRolePro.setRoleName(dataByItemCode.getItemValue());
                lineUserRolePro.setPerms(dataByItemCode.getValueContent());
                lineUserRolePro.setButtons(dataByItemCode.getButtonContent());
                lineGoodsUserRel.setAuthorizedButtons(roleButtons.get(role));
                tLineUserRoleProMapper.insertSelective(lineUserRolePro);
                lineGoodsUserRel.setRoleCode(role);
                tLineGoodsUserRelMemberMapper.insertSelective(lineGoodsUserRel);
                lineGoodsUserRel.setId(null);
            }
        }
        return null;
    }

    private static Map<String, String> convertToMap(String[][] data) {
        Map<String, String> result = new HashMap<>();

        for (String[] entry : data) {
            // entry[0]作为键，entry[1]作为值
            if (result.containsKey(entry[0])) {
                result.put(entry[0], result.get(entry[0]) + "," + entry[1]);
            } else {
                result.put(entry[0], null == entry[1] ? "" : entry[1]);
            }
        }

        return result;
    }

    /**
     * 检查线路是否已有负责人
     * @param accountId
     * @param lineGoodsUserRelVo
     * @param role
     * @param roles
     */
    private void checkPrincipal(Integer accountId, TLineGoodsUserRelVo lineGoodsUserRelVo,
                                String role, List<String> roles, String error) {
        if (roles.contains(role)) {
            if (null != lineGoodsUserRelVo.getIfPrincipal() && lineGoodsUserRelVo.getIfPrincipal()) {
                //查询线路是否已经有负责人
                HashMap<String, Object> param = new HashMap();
                param.put("lineGoodsRelId", lineGoodsUserRelVo.getLineGoodsRelId());
                param.put("companyId", lineGoodsUserRelVo.getCompanyId());
                param.put("roles", roles);
                List<TLineGoodsUserRel> tLineGoodsUserRels = tLineGoodsUserRelMemberMapper.selectLineIfPrincipal(param);
                if (null != tLineGoodsUserRels && tLineGoodsUserRels.size()> 0) {
                    for (TLineGoodsUserRel tLineGoodsUserRel: tLineGoodsUserRels){
                        if (null != tLineGoodsUserRel && null != tLineGoodsUserRel.getAccountInfoId()){
                            if (!accountId.equals(tLineGoodsUserRel.getAccountInfoId())){
                                throw new RuntimeException(error);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * @Description 解析新增后的员工线路角色和按钮
     * <AUTHOR>
     * @Date 2019/11/27 22:25
     * @Param
     * @Return
     * @Exception
     */
    private HashMap<String, ArrayList<String>> newRoleButton(TLineGoodsUserRelVo record) {
        String[][] lineRoleId = record.getLineRoleCode();
        HashMap<String, ArrayList<String>> roleBtnMap = new HashMap<>();
        for (String[] strings : lineRoleId) {
            String role = strings[0];
            ArrayList<String> roleButton = (null == roleBtnMap.get(role) ? new ArrayList<>() : roleBtnMap.get(role));
            if(strings.length>1){
                String button = strings[1];
                roleButton.add(button);
            }
            roleBtnMap.put(role, roleButton);
        }
        return roleBtnMap;
    }

    /*
     * <AUTHOR>
     * @Description 新增经纪人
     * @Date 2020/2/7 11:10
     * @Param
     * @return
    **/
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil saveAgent(TAccountVo record) {
        if (null != record.getUserLogisticsRole()) {
            /* 0是个人，1是企业*/
            if ("0".equals(record.getUserLogisticsRole())) {
                record.setUserLogisticsRole(DictEnum.CTYPEAGENTPERSON.code);
            } else {
                record.setUserLogisticsRole(DictEnum.CTYPEAGENTCOMPANY.code);
            }
        }
        TAccount account = new TAccount();
        account.setAccountNo(record.getAccountNo());
        // TODO 查询C端手机号是否重复
        // t_end_user  t_accout  t_sys_usr
        List<TAccount> accounts = tAccountMapper.selectRepeatAccountNo(account);
        if (null != accounts && accounts.size() > 0) {
            return ResultUtil.error("账号已存在");
        }
        List<TSysUser> tSysUsers = systemAPI.selectByPhone(record.getAccountNo());
        if (null != accounts && accounts.size() > 0) {
            return ResultUtil.error("系统用户表账号已存在");
        }

        //TODO 新增系统用户
        TSysUser user = new TSysUser();
        user.setUsername(record.getAccountNo());
        user.setAccountNo(record.getAccountNo());
        user.setNickname(record.getNickname());
        String pwd = DigestUtils.md5Hex("123456");
        user.setPassword(pwd);
        user.setEnable(false);
        user.setIfPasswordSecurity(false);
        ResultUtil saveUser = systemAPI.saveUser(user);
        Integer userId = null;
        if (null != saveUser.getCode()){
            if (saveUser.getCode().equals("error")){
                if (null != saveUser.getMsg()){
                    return ResultUtil.error(saveUser.getMsg());
                } else {
                    return ResultUtil.error("添加失败");
                }
            } else if (saveUser.getCode().equals("success")){
                Object data = saveUser.getData();
                if (null != data){
                    LinkedHashMap sysUser = (LinkedHashMap) data;
                    userId = (Integer) sysUser.get("id");
                }
            }
        }

        TSysRole tSysRole =  systemAPI.selectByParam("JJR");
        if(tSysRole!=null){
            TUserRole tUserRole = new TUserRole();
            tUserRole.setUserId(userId);
            tUserRole.setRoleId(tSysRole.getId());
            systemAPI.saveUserRole(tUserRole);
        }

        //保存用户信息表 add by zhangjiji ********
        TUserInfo tUserInfo = new TUserInfo();
        tUserInfo.setAccountId(userId);
        tUserInfo.setStatus(DictEnum.ACCOUNTENABLE.code);
        tUserInfo.setPasswordErrorCount(0);
        tUserInfo.setEnable(false);
        systemAPI.insertUserInfo(tUserInfo);

        //TODO 账户表
        TAccount ac = new TAccount();
        BeanUtil.copyProperties(record, ac);
        ac.setPassword(pwd);
        ac.setUserId(userId);
        ac.setNickname(record.getNickname());
        //账号类型
        ac.setAcctype(DictEnum.PhoneNo.code);
        //用户类型
        ac.setUsertype(DictEnum.CD.code);
        //数据来源:企业PC
        ac.setDatafrom(DictEnum.OPPC.code);
        //注册节点：账号申请完成
        ac.setRegnode(DictEnum.ACCAPCOM.code);
        ac.setEnable(false);
        tAccountMapper.insertSelective(ac);
        //创建C端用户
        TEndUserInfo enduerInfo = createEnduerInfo(record, record.getUserLogisticsRole());
        //创建C端用户与账户表关系
        createEnduerAccount(record, enduerInfo.getId(), ac.getId());

        return ResultUtil.ok();
    }

    @Override
    @LcnTransaction
    @Transactional
    public ResultUtil updateAgent(TAccountVo record) {
        String userNickname = CurrentUser.getUserNickname();
        Date date = new Date();
        String accountNo = record.getAccountNo();
        String nickname = record.getNickname();

        //查询对应字段
        TEndUserInfo tEndUserInfoDB = tEndUserInfoMapper.selectByPrimaryKey(record.getEnduserId());

        List<TEndUserInfo> idcardCount = tEndUserInfoMapper.notOwnIdcardList(record.getIdcard(),record.getEnduserId());
        if(null != idcardCount && idcardCount.size() > 0 && !record.getIdcard().equals(tEndUserInfoDB.getIdcard())){
            return ResultUtil.error("编辑失败，该身份证号已在平台存在!");
        }

        String accountNoDB = tEndUserInfoDB.getPhone();
        TAccount taccount = new TAccount();
        taccount.setAccountNo(accountNoDB);
        List<TAccount> tAccountDB = tAccountMapper.selectRepeatAccountNo(taccount);
        List<TSysUser> tSysUsersDB = systemAPI.selectByPhone(accountNoDB);
        List<TEnduserAccount> tEnduserAccountDB = tEnduserAccountMapper.selectByPhoneAndAccountId(tAccountDB.get(0).getId(), accountNoDB);

        //修改t_accout表信息
        TAccount tAccount = new TAccount();
        tAccount.setId(tAccountDB.get(0).getId());
        tAccount.setNickname(nickname);
        tAccount.setUpdateTime(date);
        tAccount.setUpdateUser(userNickname);

        //修改t_end_user_info
        TEndUserInfo tEndUserInfo = new TEndUserInfo();
        BeanUtil.copyProperties(record, tEndUserInfo);
        if (StringUtils.isNotBlank(record.getAddress())){
            tEndUserInfo.setAddressState(true);
        }else {
            tEndUserInfo.setAddressState(false);
        }
        tEndUserInfo.setId(record.getEnduserId());
        tEndUserInfo.setRealName(nickname);
        tEndUserInfo.setIdcardValidBeginning(record.getIdcardValidBeginning());
        tEndUserInfo.setIdcardValidUntil(record.getIdcardValidUntil());
        //认证调用则不修改 修改时间
        if(StringUtils.isEmpty(record.getRemark())){
              tEndUserInfo.setUpdateTime(date);
        }
        tEndUserInfo.setUpdateUser(userNickname);

        tEndUserInfo.setUserLogisticsRole(("0".equals(record.getUserLogisticsRole())?DictEnum.CTYPEAGENTPERSON.code:DictEnum.CTYPEAGENTCOMPANY.code));

        //修改t_sys_user
        TSysUserVO tSysUserVO = new TSysUserVO();
        tSysUserVO.setId(tSysUsersDB.get(0).getId());
        tSysUserVO.setNickname(nickname);
        tSysUserVO.setUpdateTime(date);
        tSysUserVO.setUpdateUser(userNickname);

        //修改t_end_user_account
        TEnduserAccount tEnduserAccount = new TEnduserAccount();
        tEnduserAccount.setId(tEnduserAccountDB.get(0).getId());
        tEnduserAccount.setRealName(nickname);
        tEnduserAccount.setIdcard(record.getIdcard());
        tEnduserAccount.setUpdateTime(date);
        tEnduserAccount.setUpdateUser(userNickname);


        //判断是否无需更改手机号
        if (accountNo.equals(tEndUserInfoDB.getPhone())){
            //t_end_user_info
            tEndUserInfoMapper.updateByPrimaryKeySelective(tEndUserInfo);
            //t_account
            tAccountMapper.updateByPrimaryKeySelective(tAccount);
            //t_sys_user
            systemAPI.updateUser(tSysUserVO);
            //t_end_user_account
            tEnduserAccountMapper.updateByPrimaryKeySelective(tEnduserAccount);
        }else {

            TAccount taccount1 = new TAccount();
            taccount.setAccountNo(accountNo);
            List<TAccount> tAccountDB1 = tAccountMapper.selectRepeatAccountNo(taccount);
            List<TSysUser> tSysUsersDB1 = systemAPI.selectByPhone(accountNo);
            List<TEnduserAccount> tEnduserAccountDB1 = tEnduserAccountMapper.selectByPhoneAndAccountId(tAccountDB.get(0).getId(), accountNo);
            List<TEndUserInfo> tEndUserInfoDB1 = tEndUserInfoMapper.findphone(accountNo);
            if (null != tAccountDB1 && tAccountDB1.size() > 0) {
                return ResultUtil.error("系统用户表账号已存在");
            }
            if (null != tEnduserAccountDB1 && tEnduserAccountDB1.size() > 0) {
                return ResultUtil.error("C端用户与账号关系表已存在该账号");
            }
            if (null!=tSysUsersDB1 && tSysUsersDB1.size()>0){
                return ResultUtil.error("系统用户表已存在该账号");
            }
            if (null!=tEndUserInfoDB1 && tEndUserInfoDB1.size()>0){
                return ResultUtil.error("C端（终端）信息表存在该账号");
            }
            //更改t_account
            tAccount.setAccountNo(accountNo);
            tAccountMapper.updateByPrimaryKeySelective(tAccount);
            //更改t_end_user_info
            tEndUserInfo.setPhone(accountNo);
            tEndUserInfoMapper.updateByPrimaryKeySelective(tEndUserInfo);

            //更改t_sys_user
            tSysUserVO.setAccountNo(record.getAccountNo());
            tSysUserVO.setUsername(record.getAccountNo());
            tSysUserVO.setNickname(record.getNickname());
            systemAPI.updateUser(tSysUserVO);

            //更改t_enduser_account_info
            tEnduserAccount.setPhone(accountNo);
            tEnduserAccountMapper.updateByPrimaryKeySelective(tEnduserAccount);
        }

        return ResultUtil.ok("更改经纪人信息成功");
    }

    @Override
    public ResultUtil selectCompany() {
        List<CompanyInfoDTO> companyInfoDTOS = tCompanyInfoMapper.selectCompany();
        return ResultUtil.ok(companyInfoDTOS);
    }


    @Override
    public ResultUtil selectAgentPageInfo(TAccountVo record) {
        InetAddress ip4 = null;
        try {
            ip4 = Inet4Address.getLocalHost();
            System.out.println("ip4 = " + ip4);
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }

        try{
            if (record.getId()!=null){
                List<ManageAccountDTO> manageAccountDTOS = tAccountMapper.selectAgentByPageInfo(record);
                if(null != manageAccountDTOS.get(0).getIdcardValidBeginning() && !manageAccountDTOS.get(0).getIdcardValidBeginning().equals("")
                        && null != manageAccountDTOS.get(0).getIdcardValidUntil() && !manageAccountDTOS.get(0).getIdcardValidUntil().equals("")){
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    if(simpleDateFormat.parse(manageAccountDTOS.get(0).getIdcardValidBeginning()).getTime()
                            > simpleDateFormat.parse(manageAccountDTOS.get(0).getIdcardValidUntil()).getTime()){
                        String idcardValidBeginning = manageAccountDTOS.get(0).getIdcardValidBeginning();
                        manageAccountDTOS.get(0).setIdcardValidBeginning(manageAccountDTOS.get(0).getIdcardValidUntil());
                        manageAccountDTOS.get(0).setIdcardValidUntil(idcardValidBeginning);
                    }
                }
                return ResultUtil.ok(manageAccountDTOS.get(0));
            }else {
                Page<Object> objectPage = PageHelper.startPage(record.getPage(), record.getSize());
                List<ManageAccountDTO> manageAccountDTOS = tAccountMapper.selectAgentByPageInfo(record);
                return ResultUtil.ok(objectPage, objectPage.getTotal());
            }
        }catch (Exception e) {
            log.error("dw-011:经纪人信息列表查询失败！", e);
            return ResultUtil.error("dw-011:经纪人信息列表查询失败！");
        }
    }

    @Override
    public ResultUtil deleteAgent(List<TEndUserInfoVO> endUserInfoVOS) {
        for (TEndUserInfoVO endUserInfoVO : endUserInfoVOS) {
            Integer accountId = endUserInfoVO.getAccountId();
            TAccount account = new TAccount();
            account.setId(accountId);
            account.setEnable(true);
            tAccountMapper.updateByPrimaryKeySelective(account);
            //根据账号表获取userid
            TAccount ac = tAccountMapper.selectByPrimaryKey(accountId);
            TSysUser user = new TSysUser();
            user.setId(ac.getUserId());
            user.setEnable(true);
            systemAPI.updateUser(user);
            tEnduserAccountMapper.updateEnduserAccount(accountId);

            // 将TuserInfo置为删除
            TUserInfo tUserInfo = new TUserInfo();
            tUserInfo.setAccountId(ac.getUserId());
            tUserInfo.setEnable(true);
            systemAPI.updateByAccountIdSelective(tUserInfo);

            TEndUserInfo endUserInfo = new TEndUserInfo();
            endUserInfo.setId(endUserInfoVO.getId());
            endUserInfo.setEnable(true);
            tEndUserInfoMapper.updateByPrimaryKeySelective(endUserInfo);
            // TODO 删除货源与C端经纪人关系
            tEndUserInfoMapper.deleteGoodsManagerRel(endUserInfoVO.getId());
        }
        /*List<HashMap<String, Integer>> enduserAccounts = tAccountMapper.selectEnduserIdByAccountId(acountIds);
        ResultUtil resultUtil = tOrderInfoAPI.selectAgentManagerUnCompleteOrder(enduserAccounts);
        if (null != resultUtil && null != resultUtil.getCode() && resultUtil.getCode().equals("success")) {
            if (null != resultUtil.getData()){
                List<HashMap<String, Integer>> results = (List<HashMap<String, Integer>>) resultUtil.getData();
                if (results.size() > 0) {
                    for (HashMap<String, Integer> map : results) {
                        Integer count = map.get("count");
                        Integer accountId = map.get("accountId");
                        if (count > 0) {
                            return ResultUtil.error("当前勾选的经纪人下存在未完成的运单，不允许删除");
                        }
                        TAccount account = new TAccount();
                        account.setId(accountId);
                        account.setEnable(true);
                        tAccountMapper.updateByPrimaryKeySelective(account);
                        //根据账号表获取userid
                        TAccount ac = tAccountMapper.selectByPrimaryKey(Integer.valueOf(map.get("accountId").toString()));
                        TSysUser user = new TSysUser();
                        user.setId(ac.getUserId());
                        user.setEnable(true);
                        systemAPI.updateUser(user);
                        tEnduserAccountMapper.updateEnduserAccount(accountId);
                    }
                }
            }
        }*/
        return ResultUtil.ok();
    }

    @Override
    public ResultUtil auditAgent(TAccountVo tAccountVo) {
        if (tAccountVo.getEnduserId()!=null){
            if (tAccountVo.getAuditStatus()!=null) {
                tAccountVo.setRemark("auditAgent");
                if (tAccountVo.getIsBusinessUnit()==null){
                    //经纪人认证，同时修改信息
                    updateAgent(tAccountVo);
                }else {
                    //业务部认证
                    //修改系统用户
                    TAccount account = new TAccount();
                    account.setAccountNo(tAccountVo.getAccountNo());
                    account.setId(tAccountVo.getId());
                    List<TAccount> accounts = tAccountMapper.selectRepeatAccountNo(account);
                    if (null != accounts && accounts.size() > 0) {
                        return ResultUtil.error("账号已存在");
                    }
                    //如果姓名或账号修改，则修改对应系统用户信息，否则不修改
                    TAccount selectAccount = tAccountMapper.selectByPrimaryKey(tAccountVo.getId());
                    //修改员工
                    TAccount ac = new TAccount();
                    ac.setAccountNo(tAccountVo.getAccountNo());
                    ac.setNickname(tAccountVo.getNickname());
                    ac.setId(tAccountVo.getId());
                    ac.setUpdateUser(tAccountVo.getUpdateUser());
                    ac.setUpdateTime(tAccountVo.getUpdateTime());
                    tAccountMapper.updateByPrimaryKeySelective(ac);
                    String userLogisticsRole = null != tAccountVo.getUserLogisticsRole() ? tAccountVo.getUserLogisticsRole() : "";
                    if (StringUtils.isNotEmpty(userLogisticsRole)) {
                        //修改C端用户表
                        TEndUserInfo endUserInfo = new TEndUserInfo();
                        endUserInfo.setId(tAccountVo.getEnduserId());
                        endUserInfo.setRealName(tAccountVo.getNickname());
                        endUserInfo.setOrgName(tAccountVo.getOrgName());
                        endUserInfo.setIdcard(tAccountVo.getIdcard());
                        endUserInfo.setPhone(tAccountVo.getAccountNo());
                        endUserInfo.setIdcardValidUntil(tAccountVo.getIdcardValidUntil());
                        endUserInfo.setIdcardPhoto1(tAccountVo.getIdcardPhoto1());
                        endUserInfo.setIdcardPhoto2(tAccountVo.getIdcardPhoto2());
                        endUserInfo.setCertificateNo(null != tAccountVo.getCertificateNo() ? tAccountVo.getCertificateNo() : "");
                        tEndUserInfoMapper.updateByPrimaryKeySelective(endUserInfo);

                        //修改C端用户账号表
                        TEnduserAccount enduserAccount = new TEnduserAccount();
                        enduserAccount.setId(tAccountVo.getEnduserAccountId());
                        enduserAccount.setRealName(tAccountVo.getNickname());
                        enduserAccount.setIdcard(tAccountVo.getIdcard());
                        enduserAccount.setPhone(tAccountVo.getAccountNo());
                        tEnduserAccountMapper.updateByPrimaryKeySelective(enduserAccount);
                    }else{
                        if(!"BD".equals(tAccountVo.getUsertype())){
                            TEnduserAccount enduserAccount = tEnduserAccountMapper.selectEnduserAccount(ac.getId());
                            TEndUserInfo endUserInfo = tEndUserInfoMapper.selectByPrimaryKey(enduserAccount.getEnduserId());
                            endUserInfo.setRealName(tAccountVo.getNickname());
                            endUserInfo.setOrgName(tAccountVo.getOrgName());
                            endUserInfo.setIdcard(tAccountVo.getIdcard());
                            endUserInfo.setPhone(tAccountVo.getAccountNo());
                            endUserInfo.setIdcardValidUntil(tAccountVo.getIdcardValidUntil());
                            endUserInfo.setIdcardPhoto1(tAccountVo.getIdcardPhoto1());
                            endUserInfo.setIdcardPhoto2(tAccountVo.getIdcardPhoto2());
                            tEndUserInfoMapper.updateByPrimaryKeySelective(endUserInfo);
                            enduserAccount.setRealName(tAccountVo.getNickname());
                            enduserAccount.setIdcard(tAccountVo.getIdcard());
                            enduserAccount.setPhone(tAccountVo.getAccountNo());
                            tEnduserAccountMapper.updateByPrimaryKeySelective(enduserAccount);
                        }
                    }
                    TSysUserVO user = new TSysUserVO();
                    user.setId(selectAccount.getUserId());
                    user.setUsername(tAccountVo.getAccountNo());
                    user.setAccountNo(tAccountVo.getAccountNo());
                    user.setNickname(tAccountVo.getNickname());
                    ResultUtil resultUtil = systemAPI.updateUserByAccountNo(user);
                    String code = resultUtil.getCode();
                    if (code.equals("error")){
                        throw new RuntimeException(resultUtil.getMsg());
                    }
                }
                TEndUserInfoVO tEndUserInfoVO = new TEndUserInfoVO();
                tEndUserInfoVO.setId(tAccountVo.getEnduserId());
                tEndUserInfoVO.setAuditStatus(tAccountVo.getAuditStatus());
                if (!StringUtils.isEmpty(tAccountVo.getAuditOpinion())&& !"".equals(tAccountVo.getAuditOpinion())){
                    tEndUserInfoVO.setAuditOpinion(tAccountVo.getAuditOpinion());
                }
                tEndUserInfoMapper.updateAuditAgent(tEndUserInfoVO);
            }else {
                return ResultUtil.error("请选中审核状态");
            }
        }else {
            return ResultUtil.error("请输入当前信息Id编号");
        }
        return ResultUtil.ok("认证成功");
    }

    @Override
    public ResultUtil queryOpenIdByEnduserId(Integer enduserId) {
        return ResultUtil.ok(tAccountMapper.queryOpenIdByEnduserId(enduserId));
    }

    @Override
    public TAccountUserInfoDTO selectUserInfoByUserId(Integer userId) {
        return tAccountMapper.selectUserInfoByUserId(userId);
    }

    @Override
    public Boolean getDataByPhone(String phone) {
        Boolean staus=false;
        List<TAccount> list=tAccountMapper.getDataByPhone(phone);
        for (TAccount tAccount : list) {
            if(tAccount.getIfMainAccount()){//表示是主账号--->企业管理员
                staus=true;
                break;
            }
        }
        return staus;
    }

    @LcnTransaction
    @Transactional
    @Override
    public void insertConsignee(ConsigneeAccountVo consigneeAccountVo) {
        TAccountVo record = new TAccountVo();
        record.setAccountNo(consigneeAccountVo.getPhone());
        record.setPassword("123456");
        HashMap[] map=new HashMap[2];
        HashMap<Object, Object> map1 = new HashMap<>();
        HashMap<Object, Object> map2 = new HashMap<>();
        //货源大厅企业收单员
        map1.put(DictEnum.RESOURCECOMPANYRECEIVE,DictEnum.RESOURCECOMPANYRECEIVE.code);
        //货源大厅企业付款员
        map2.put(DictEnum.RESOURCECOMPANYPAYER,DictEnum.RESOURCECOMPANYPAYER.code);
        map[0]=map1;
        map[1]=map2;
        record.setLineRoles(map);
        Integer companyIdInteger = null;
        List<String> userCompanyId = CurrentUser.getUserCompanyId();

        //新增角色
        HashSet<String> set = new HashSet<>();
        set.add(DictEnum.RESOURCECOMPANYRECEIVE.code);
        set.add(DictEnum.RESOURCECOMPANYPAYER.code);
        //新增系统用户--赋值
        TSysUserForAddVO userForAddVO = new TSysUserForAddVO();
        userForAddVO.setUsername(record.getAccountNo());
        userForAddVO.setAccountNo(record.getAccountNo());
        userForAddVO.setNickname(consigneeAccountVo.getNickName());
        userForAddVO.setAcctType(DictEnum.PhoneNo.code);
        userForAddVO.setUserType(DictEnum.BD.code);
        userForAddVO.setDataFrom(DictEnum.COMPC.code);
        userForAddVO.setRegNode(DictEnum.ACCAPCOM.code);
        userForAddVO.setRolesCode(set);
        ResultUtil saveUser = systemAPI.saveUserAndPCRole(userForAddVO);
        if (null != saveUser.getCode() && saveUser.getCode().equals("error")){
            throw new RuntimeException("账号已存在");
        }
        LinkedHashMap userSave = (LinkedHashMap) saveUser.getData();
        String userId = String.valueOf(userSave.get("id"));

        //新增员工
        TAccount ac = new TAccount();
        BeanUtil.copyProperties(record, ac);
        String pwd = DigestUtils.md5Hex(record.getPassword());
        ac.setPassword(pwd);
        ac.setUserId(Integer.valueOf(userId));
        ac.setNickname(consigneeAccountVo.getNickName());
        //账号类型
        ac.setAcctype(DictEnum.PhoneNo.code);
        //用户类型
        ac.setUsertype(DictEnum.BD.code);
        //数据来源:企业PC
        ac.setDatafrom(DictEnum.COMPC.code);
        //注册节点：账号申请完成
        ac.setRegnode(DictEnum.ACCAPCOM.code);
        ac.setParam2(DictEnum.RESOURCE_ACCOUNT.code);
        ac.setEnable(false);
        tAccountMapper.insertSelective(ac);

        //保存用户信息表
        TUserInfo tUserInfo = new TUserInfo();
        tUserInfo.setAccountId(Integer.valueOf(userId));
        tUserInfo.setStatus(DictEnum.ACCOUNTENABLE.code);
        tUserInfo.setPasswordErrorCount(0);
        tUserInfo.setEnable(false);
        systemAPI.insertUserInfo(tUserInfo);

        //企业账号关系表
        String companyId = userCompanyId.get(0);
        companyIdInteger = Integer.valueOf(companyId);
        TCompanyAccount companyAccount = new TCompanyAccount();
        companyAccount.setAccountId(ac.getId());
        companyAccount.setCompanyId(companyIdInteger);
        companyAccount.setRealName(consigneeAccountVo.getNickName());
        companyAccount.setPhone(record.getAccountNo());
        companyAccount.setRealPosition(record.getRealPosition());
        companyAccount.setIdcard(record.getIdcard());
        int count = companyAccountMapper.selectAccountCompany(companyAccount);
        if(count==0){
            //不存在则创建
            companyAccount.setEnable(false);
            companyAccountMapper.insertSelective(companyAccount);
        }
        //创建线路货物员工关系、线路货物员工角色
        insertLineUserAndRole(companyAccount, set, consigneeAccountVo.getLineGoodsRelId());
    }

    @LcnTransaction
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addConsignee(ConsigneeAccountVo consigneeAccountVo) {
        //企业账号关系表
        Integer companyId = CurrentUser.getUserCompanyIdInteger().get(0);

        TAccount tAccount = tAccountMapper.selectByAccountNo(consigneeAccountVo.getPhone());

        TCompanyAccount companyAccount = new TCompanyAccount();
        companyAccount.setAccountId(tAccount.getId());
        companyAccount.setCompanyId(companyId);
        companyAccount.setRealName(tAccount.getNickname());
        companyAccount.setPhone(tAccount.getAccountNo());
        int count = companyAccountMapper.selectAccountCompany(companyAccount);
        if(count==0){
            //不存在则创建
            companyAccount.setEnable(false);
            companyAccountMapper.insertSelective(companyAccount);
        }
        //创建线路货物员工关系、线路货物员工角色
        //新增角色
        HashSet<String> set = new HashSet<>();
        set.add(DictEnum.RESOURCECOMPANYRECEIVE.code);
        set.add(DictEnum.RESOURCECOMPANYPAYER.code);
        insertLineUserAndRole(companyAccount, set, consigneeAccountVo.getLineGoodsRelId());
    }

    /**
     * 创建线路货物员工关系、线路货物员工角色
     */
    public void insertLineUserAndRole(TCompanyAccount companyAccount, HashSet<String> roles, Integer lineGoodsRelId) {
        // 查询货源线路角色
        ResultUtil resultUtil = dicCatItemAPI.lineRole();
        if (null != resultUtil.getCode() && CodeEnum.SUCCESS.getCode().equals(resultUtil.getCode()) && null != resultUtil.getData()) {
            LinkedHashMap linkedHashMap = (LinkedHashMap) resultUtil.getData();
            Object o = linkedHashMap.get("lineRole");
            List<DictItemLineRoleDTO> list = JSONUtil.toList(JSONUtil.parseArray(o), DictItemLineRoleDTO.class);
            for (String role: roles) {
                // 线路员工角色权限表
                TLineUserRolePro lineUserRolePro = new TLineUserRolePro();
                lineUserRolePro.setAccountId(companyAccount.getAccountId());
                lineUserRolePro.setEnable(false);
                lineUserRolePro.setRoleCode(role);
                String roleName = "";
                String valueContent = "";
                String buttonContent = "";
                for (DictItemLineRoleDTO dictItemLineRoleDTO : list) {
                    if (role.equals(dictItemLineRoleDTO.getValue())) {
                        roleName = dictItemLineRoleDTO.getLabel();
                        valueContent = dictItemLineRoleDTO.getValueContent();
                        buttonContent = dictItemLineRoleDTO.getButtonContent();
                        break;
                    }
                }
                lineUserRolePro.setButtons(buttonContent);
                lineUserRolePro.setRoleName(roleName);
                lineUserRolePro.setPerms(valueContent);
                tLineUserRoleProMapper.insertSelective(lineUserRolePro);
                TLineGoodsUserRelMember lineGoodsUserRel = new TLineGoodsUserRelMember();
                lineGoodsUserRel.setCompanyId(companyAccount.getCompanyId());
                lineGoodsUserRel.setLineGoodsRelId(lineGoodsRelId);
                //线路货物员工关系
                lineGoodsUserRel.setAccountInfoId(companyAccount.getAccountId());
                lineGoodsUserRel.setAuthorizedButtons(buttonContent);
                lineGoodsUserRel.setEnable(false);
                lineGoodsUserRel.setCurrentAccountNo(companyAccount.getPhone());
                lineGoodsUserRel.setCurrentAccountName(companyAccount.getRealName());
                lineGoodsUserRel.setCurrentAccountPhone(companyAccount.getPhone());
                lineGoodsUserRel.setRoleCode(role);
                tLineGoodsUserRelMemberMapper.insertSelective(lineGoodsUserRel);
            }
        } else {
            throw new RuntimeException("获取线路角色失败");
        }
    }

    @Override
    public int selectCountStatus(TEndUserCarRel tEndUserCarRel) {
        return tEndUserCarRelMapper.selectCountStatus(tEndUserCarRel);

    }

    @Override
    public TAccount selectAccountById(Integer accountId) {
        return tAccountMapper.selectByPrimaryKey(accountId);
    }

    /*
    2023.5.19 微信小程序、APP 注册接口
     */

    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil insertAccount(RegisterEndUserVO record) {
        //sys_user新增系统用户
        TSysUser sysUser = new TSysUser();
        sysUser.setAccountNo(record.getPhone());
        sysUser.setUsername(record.getPhone());
        sysUser.setPassword(record.getPassword());//设置默认密码
        sysUser.setUsertype(DictEnum.CD.code);
        sysUser.setAcctype(DictEnum.PhoneNo.code);
        sysUser.setRegnode(DictEnum.ACCAPCOM.code);
        sysUser.setDatafrom(record.getDataFrom());
        sysUser.setEnable(false);
       // sysUser.setIfAgreement(record.getIfAgreement());
        sysUser.setIfPasswordSecurity(true);
        Integer userId = null;
        List<TSysUser> tSysUserList = systemAPI.selectByPhone(record.getPhone());
        if(tSysUserList.size()<1){
            ResultUtil resultUtil = systemAPI.addUser(sysUser);
            if (null != resultUtil.getCode()){
                if (resultUtil.getCode().equals("error")){
                    if (null != resultUtil.getMsg()){
                        return ResultUtil.error(resultUtil.getMsg());
                    } else {
                        return ResultUtil.error("添加失败");
                    }
                } else if (resultUtil.getCode().equals("success")){
                    Object data = resultUtil.getData();
                    if (null != data){
                        LinkedHashMap user = (LinkedHashMap) data;
                        userId = (Integer) user.get("id");
                    }
                }
            }
        }

        //account新增用户账号
        TAccount ac = new TAccount();
        ac.setAccountNo(record.getPhone());
        ac.setUserId(userId);
        ac.setPassword(record.getPassword());
        ac.setAcctype(DictEnum.PhoneNo.code);
        ac.setUsertype(DictEnum.CD.code);
        ac.setRegnode(DictEnum.ACCAPCOM.code);
        ac.setDatafrom(record.getDataFrom());
        ac.setEnable(false);

        TAccountVo accountVo = new TAccountVo();
        accountVo.setAccountNo(ac.getAccountNo());
        TAccount tAccountResult = tAccountMapper.selectAccountByPhone(accountVo);
        if(ObjectUtil.isEmpty(tAccountResult)){
            tAccountMapper.insertSelective(ac);
        }

        //保存用户信息表
        TUserInfo tUserInfo = new TUserInfo();
        tUserInfo.setAccountId(userId);
        tUserInfo.setStatus(DictEnum.ACCOUNTENABLE.code);
        tUserInfo.setPasswordErrorCount(0);
        tUserInfo.setEnable(false);
        tUserInfo.setPasswordResetTime(new Date());
        systemAPI.insertUserInfo(tUserInfo);

        //新增c端用户状态
        TEndUserInfo tEndUserInfo = new TEndUserInfo();
        tEndUserInfo.setPhone(record.getPhone());
        tEndUserInfo.setAuditStatus(DictEnum.PAPERNEEDUPDATE.code);
        tEndUserInfo.setEnable(false);

        TEndUserInfoExample tEndUserInfoExample = new TEndUserInfoExample();
        TEndUserInfoExample.Criteria cr = tEndUserInfoExample.createCriteria();
        cr.andPhoneEqualTo(tEndUserInfo.getPhone());
        //cr.andRealNameEqualTo(tEndUserInfo.getRealName());
        cr.andEnableEqualTo(false);
        List<TEndUserInfo> tEndUserInfoList = tEndUserInfoMapper.selectByExample(tEndUserInfoExample);
        if(tEndUserInfoList.size()<1){
            tEndUserInfoMapper.insertSelective(tEndUserInfo);
        }

        TEnduserAccount tEnduserAccount = new TEnduserAccount();
        tEnduserAccount.setAccountId(ac.getId());
        tEnduserAccount.setEnduserId(tEndUserInfo.getId());
        tEnduserAccount.setPhone(record.getPhone());
        tEnduserAccount.setEnable(false);

        TEnduserAccountExample tEnduserAccountExample = new TEnduserAccountExample();
        TEnduserAccountExample.Criteria cr2 = tEnduserAccountExample.createCriteria();
        cr2.andPhoneEqualTo(tEnduserAccount.getPhone());
        //cr2.andRealNameEqualTo(tEnduserAccount.getRealName());
        cr2.andEnableEqualTo(false);
        List<TEnduserAccount> tEnduserAccountList = tEnduserAccountMapper.selectByExample(tEnduserAccountExample);
        if(tEnduserAccountList.size()<1){
            tEnduserAccountMapper.insertSelective(tEnduserAccount);
        }
        // 创建司机状态
        saveTEndUserStatus(tEndUserInfo);

        TEndUserAuditInfo endUserAuditInfo = new TEndUserAuditInfo();
        endUserAuditInfo.setEndUserId(tEndUserInfo.getId());
        endUserAuditInfo.setEnable(false);
        endUserAuditInfoMapper.insertSelective(endUserAuditInfo);

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("endUserInfoId", tEndUserInfo.getId());
        map.put("accountId", ac.getId());
        map.put("accountNo", ac.getAccountNo());
        return ResultUtil.ok(map,"成功");
    }

    /*
    2023.5.19 微信小程序、APP 保存身份证 接口
     */
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil saveIDCard(IDCardVO vo) {
        //根据endUserInfoId从t_end_user_info表查询数据
        TEndUserInfo tEndUserInfo = tEndUserInfoMapper.selectByPrimaryKey(CurrentUser.getEndUserId());
        //根据accouontId从t_account表查询数据
        TAccount tAccount = tAccountMapper.selectByPrimaryKey(CurrentUser.getUserAccountId());
        //根据enduserId和accountId从t_enduser_account表获取数据
        List<TEnduserAccount> list = tEnduserAccountMapper.selectByEndUserInfoIdAndAccountId(tEndUserInfo.getId(), tAccount.getId());
        //属性
        tEndUserInfo.setIdcard(vo.getIdcard());//身份证号
        tEndUserInfo.setIdcardPhoto1(vo.getIdcardPhoto1());//照片1
        tEndUserInfo.setIdcardPhoto2(vo.getIdcardPhoto2());//照片2
        tEndUserInfo.setIdcardValidBeginning(vo.getIdcardValidUntil());//身份证有效期始
        tEndUserInfo.setIdcardValidUntil(vo.getIdcardValidBeginning());//身份证有效期至
        tEndUserInfo.setIdcardIssueQrganization(vo.getIdcardIssueQrganization());//签发机关
        tEndUserInfo.setRealName(vo.getUsername());//用户姓名
        //更新数据
        tEndUserInfoMapper.updateByPrimaryKeySelective(tEndUserInfo);

        tAccount.setNickname(vo.getUsername());
        tAccountMapper.updateByPrimaryKeySelective(tAccount);
        //t_sys_user 保存用户名
        TSysUser sysUserById = systemAPI.getSysUserById(tAccount.getUserId());
        if(null != sysUserById){
            sysUserById.setNickname(vo.getUsername());
            systemAPI.updateSysUserById(sysUserById);
        }
        TEnduserAccount tEnduserAccount=list.get(0);
        tEnduserAccount.setIdcard(vo.getIdcard());
        tEnduserAccount.setRealName(tEndUserInfo.getRealName());
        tEnduserAccountMapper.updateByPrimaryKeySelective(tEnduserAccount);

        return ResultUtil.ok("身份证保存成功");
    }

    /*
    2023.5.19 微信小程序、APP 保存驾驶证 接口
     */
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil saveDrivingLicence(DrivingLicenceVO vo) {
        //根据endUserInfoId从t_end_user_info表查询数据
        TEndUserInfo tEndUserInfo = new TEndUserInfo();
        tEndUserInfo.setId(CurrentUser.getEndUserId());
        tEndUserInfo.setDrivingLicencesPhoto1(vo.getDrivingLicencesPhoto1());//照片1
        tEndUserInfo.setDrivingLicencesPhoto2(vo.getDrivingLicencesPhoto2());//照片2
        tEndUserInfo.setApproveDrivingType(vo.getApproveDrivingType());//驾驶证准驾车型
        tEndUserInfo.setDrivingLicencesValidUntil(vo.getDrivingLicencesValidUntil());//驾驶证有效期至
        tEndUserInfo.setDrivingLicencesCode(vo.getDrivingLicencesCode());//驾驶证档案编号
        //更新数据
        tEndUserInfoMapper.updateByPrimaryKeySelective(tEndUserInfo);

        TEndUserAuditInfo endUserAuditInfo = endUserAuditInfoMapper.selectByEndUserId(tEndUserInfo.getId());
        if(null !=endUserAuditInfo){
            endUserAuditInfo.setDrivingLicencesStatus(DictEnum.MIDNODE.code);
            endUserAuditInfo.setDrivingLicencesOpinion("驾驶证审核中");
            endUserAuditInfoMapper.updateByPrimaryKeySelective(endUserAuditInfo);
        }else{
            endUserAuditInfo = new TEndUserAuditInfo();
            endUserAuditInfo.setEndUserId(tEndUserInfo.getId());
            endUserAuditInfo.setEnable(false);
            endUserAuditInfo.setUserLogisticsRole(tEndUserInfo.getUserLogisticsRole());
            endUserAuditInfo.setDrivingLicencesStatus(DictEnum.MIDNODE.code);
            endUserAuditInfo.setDrivingLicencesOpinion("驾驶证审核中");

            endUserAuditInfo.setIdcardStatus(DictEnum.PAPERNEEDUPDATE.code);
            endUserAuditInfo.setIdcardOpinion("身份证资料需更新");

            endUserAuditInfo.setCertificateStatus(DictEnum.PAPERNEEDUPDATE.code);
            endUserAuditInfo.setCertificateOpinion("从业资格证资料需更新");
            endUserAuditInfoMapper.insertSelective(endUserAuditInfo);
        }
        return ResultUtil.ok("驾驶证保存成功");
    }

    /*
    2023.5.19 微信小程序、APP 保存从业资格证 接口
     */
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil saveQualificationCard(CertificationVO vo) {
        //根据endUserInfoId从t_end_user_info表查询数据
        TEndUserInfo tEndUserInfo = new TEndUserInfo();
        tEndUserInfo.setId(CurrentUser.getEndUserId());
        tEndUserInfo.setCertificatePhoto1(vo.getCertificatePhoto1());
        tEndUserInfo.setCertificatePhoto2(vo.getCertificatePhoto2());
        tEndUserInfo.setCertificateNo(vo.getCertificateNo());//从业资格证号
        tEndUserInfo.setCertificateValidUntil(vo.getCertificateValidUntil());//资格证有效期至
        tEndUserInfo.setCertificateAddress(vo.getCertificateAddress());//资格证地址
        //更新数据
        tEndUserInfoMapper.updateByPrimaryKeySelective(tEndUserInfo);
        return ResultUtil.ok("从业资格证保存成功");
    }

    /*
    微信小程序、APP 保存用户角色信息
     */
    @Transactional
    @Override
    public ResultUtil saveUserRole(String userLogisticsRole) {
        TEndUserInfo tEndUserInfo = tEndUserInfoMapper.selectByPrimaryKey(CurrentUser.getEndUserId());
        tEndUserInfo.setUserLogisticsRole(userLogisticsRole);
        tEndUserInfoMapper.updateByPrimaryKeySelective(tEndUserInfo);
        redisUtil.set("CROLE" + CurrentUser.getEndUserId(), userLogisticsRole);
        return ResultUtil.ok();
    }

    @Override
    public ResultUtil getIDCardDesc(Integer endUserInfoId) {
        if(endUserInfoId == null){
            return ResultUtil.error("获取身份证信息失败，未获取用户ID");
        }
        TEndUserInfo userInfo = tEndUserInfoMapper.selectByPrimaryKey(endUserInfoId);
        IDCardVO vo = new IDCardVO();
        vo.setIdcardPhoto1(userInfo.getIdcardPhoto1());
        vo.setIdcardPhoto2(userInfo.getIdcardPhoto2());
        vo.setUsername(userInfo.getRealName());
        vo.setIdcard(userInfo.getIdcard());
        vo.setIdcardValidBeginning(userInfo.getIdcardValidBeginning());
        vo.setIdcardIssueQrganization(userInfo.getIdcardIssueQrganization());
        vo.setIdcardValidUntil(userInfo.getIdcardValidUntil());
        if (null != userInfo.getIdcardValidBeginning() && null != userInfo.getIdcardValidUntil()) {
            if (userInfo.getIdcardValidBeginning().getTime() > userInfo.getIdcardValidUntil().getTime()) {
                Date startDate = userInfo.getIdcardValidUntil();
                Date endDate = userInfo.getIdcardValidBeginning();
                vo.setIdcardValidBeginning(startDate);
                vo.setIdcardValidUntil(endDate);
            }
        }
        return ResultUtil.ok(vo);
    }

    @Override
    public ResultUtil getCertificationDesc(Integer endUserInfoId) {
        if(endUserInfoId == null){
            return ResultUtil.error("获取从业资格证信息失败，未获取用户ID");
        }
        TEndUserInfo userInfo = tEndUserInfoMapper.selectByPrimaryKey(endUserInfoId);
        CertificationVO vo = new CertificationVO();
        vo.setCertificatePhoto2(userInfo.getCertificatePhoto2());
        vo.setCertificatePhoto1(userInfo.getCertificatePhoto1());
        vo.setCertificateNo(userInfo.getCertificateNo());
        vo.setCertificateValidUntil(userInfo.getCertificateValidUntil());
        vo.setCertificateAddress(userInfo.getCertificateAddress());
        return ResultUtil.ok(vo);
    }

    @Override
    public ResultUtil getDrivingLicenceDesc(Integer endUserInfoId) {
        if(endUserInfoId == null){
            return ResultUtil.error("获取驾驶证信息失败，未获取用户ID");
        }
        TEndUserInfo userInfo = tEndUserInfoMapper.selectByPrimaryKey(endUserInfoId);
        DrivingLicenceVO vo = new DrivingLicenceVO();
        vo.setDrivingLicencesPhoto1(userInfo.getDrivingLicencesPhoto1());
        vo.setDrivingLicencesPhoto2(userInfo.getDrivingLicencesPhoto2());
        vo.setApproveDrivingType(userInfo.getApproveDrivingType());
        vo.setDrivingLicencesValidUntil(userInfo.getDrivingLicencesValidUntil());
        vo.setDrivingLicencesCode(userInfo.getDrivingLicencesCode());
        return ResultUtil.ok(vo);
    }


    private void saveTEndUserStatus(TEndUserInfo tEndUserInfo){
        TEndUserStatus tEndUserStatus = new TEndUserStatus();
        tEndUserStatus.setEnduserId(tEndUserInfo.getId());
        tEndUserStatus.setUserStatus(DictEnum.DISTRIBUTIONREGISTERED.code);
        tEndUserStatus.setEnable(false);
        TEndUserStatus endUserStatus = tEndUserStatusMapper.selectByEndUserId(tEndUserStatus.getEnduserId());
        if(null == endUserStatus){
            tEndUserStatusMapper.insertSelective(tEndUserStatus);
        }
    }

    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil updateDriverPhone(TAccountVo record){
        log.info("司机变更手机号入参：{}", JSONUtil.toJsonStr(record));
        //根据ID查询t_account表数据
        TAccount tAccount = tAccountMapper.selectByAccountNo(record.getOldAccountNo());
        //根据新手机号查询t_account表数据
        TAccount newTAccount = tAccountMapper.selectByAccountNo(record.getAccountNo());
        //判断新旧手机号是否存在
        if(null != tAccount){
            if(null != newTAccount){
                return ResultUtil.error("新手机号已存在");
            }
        }else{
            return ResultUtil.error("旧手机号不存在");
        }
        //根据ID查询t_end_user_info表数据
        List<TEndUserInfo> tEndUserInfos = tEndUserInfoMapper.selectByAccountNo(record.getOldAccountNo());
        if(null != tEndUserInfos && tEndUserInfos.size()>0){
            for (TEndUserInfo endUserInfo : tEndUserInfos) {
                //校验姓名跟身份证号码
                if(!endUserInfo.getRealName().equals(record.getName()) ||
                !endUserInfo.getIdcard().equals(record.getIdcard())){
                    return ResultUtil.error("姓名或身份证不匹配，不能修改。");
                }
            }
        }
        //根据userId和accountId查询t_enduser_account表数据
        List<TEnduserAccount> list = tEnduserAccountMapper.
                selectByPhoneAndAccountId(tAccount.getId(),tAccount.getAccountNo());
        //根据userId查询t_sys_user表数据
        TSysUser tSysUser = systemAPI.selectByPrimaryKey(tAccount.getUserId());
        List<TFifthGenerationSignOpenAccount> list2 = new ArrayList<>();

        /*//查询t_fifth_generation_sign_open_account表
        if(null != tEndUserInfos && tEndUserInfos.size()>0){
            for (TEndUserInfo endUserInfo : tEndUserInfos) {
                TFifthGenerationSignOpenAccount openAccount = tFifthGenerationSignOpenAccountMapper.
                        selectByUserIdAndType(endUserInfo.getId(), DictEnum.CD.code);
                if(openAccount != null){
                    list2.add(openAccount);
                }
            }
        }*/

        //修改表数据
        if(null != tEndUserInfos && tEndUserInfos.size() > 0){
            for (TEndUserInfo endUserInfo : tEndUserInfos) {
                //修改表t_end_user_info
                TEndUserInfo userInfo = new TEndUserInfo();
                userInfo.setId(endUserInfo.getId());
                userInfo.setPhone(record.getAccountNo());
                tEndUserInfoMapper.updateByPrimaryKeySelective(userInfo);
                //新增用户变更手机号记录表
                TEndUserInfoUpdateRecord updateRecord = new TEndUserInfoUpdateRecord();
                userInfo.setPhone(endUserInfo.getPhone());
                userInfo.setIdcard(endUserInfo.getIdcard());
                updateRecord.setEndUserInfoId(endUserInfo.getId());
                updateRecord.setRequestParam(JSONUtil.toJsonStr(record));
                updateRecord.setUserInfo(JSONUtil.toJsonStr(userInfo));
                updateRecord.setCreateUser(CurrentUser.getCurrentUsername());
                updateRecord.setCreateTime(new Date());
                tEndUserInfoUpdateRecordMapper.insertSelective(updateRecord);
            }
        }

        //t_account表
        TAccount account = new TAccount();
        account.setId(tAccount.getId());
        account.setAccountNo(record.getAccountNo());
        tAccountMapper.updateByPrimaryKeySelective(account);

        //t_enduser_account表
        if(null != list && list.size()>0){
            for (TEnduserAccount tEnduserAccount : list) {
                TEnduserAccount userAccount = new TEnduserAccount();
                userAccount.setId(tEnduserAccount.getId());
                userAccount.setPhone(record.getAccountNo());
                tEnduserAccountMapper.updateByPrimaryKeySelective(userAccount);
            }
        }

        //t_sys_user表
        if(null != tSysUser){
            TSysUser sysUser = new TSysUser();
            sysUser.setId(tSysUser.getId());
            sysUser.setAccountNo(record.getAccountNo());
            sysUser.setUsername(record.getAccountNo());
            systemAPI.updateSysUserById(sysUser);
        }

        //修改第三方平台手机号
        /*if(null != list2 && list2.size()>0){
            for (TFifthGenerationSignOpenAccount openAccount : list2) {
                if(StringUtils.isNotEmpty(openAccount.getSignUserId())){
                    ReqUserParam data = new ReqUserParam();
                    data.setPhone(record.getAccountNo());
                    data.setUserId(openAccount.getSignUserId());
                    data.setUserType(1);
                    ContractCreateUserResp resp = contract5GqAPI.updateUserInfo(data);
                    if(null != resp && !"200".equals(resp.getCode())){
                        return ResultUtil.error(resp.getMessage());
                    }
                }
            }
        }*/

        //华夏开户信息变更
        //查询是否有用户信息
        TZtAccountOpenInfo info = new TZtAccountOpenInfo();
        info.setAccountId(tAccount.getId());
        info.setChannelId(BankNameEnum.HXBANK.key());
        TZtAccountOpenInfo accountOpenInfo = tZtAccountOpenInfoMapper.selectByAccountIdAndChannelId(info);
        if(null != accountOpenInfo){
            TAccountOpenInfoVo tAccountOpenInfoVo = new TAccountOpenInfoVo();
            BeanUtil.copyProperties(accountOpenInfo,tAccountOpenInfoVo);
            if(null != tEndUserInfos && tEndUserInfos.size()>0){
                TEndUserInfo tEndUserInfo = tEndUserInfos.get(0);
                tAccountOpenInfoVo.setCarrierCompanyEndUserId(tEndUserInfo.getId());
                tAccountOpenInfoVo.setUserOpenRole(DictEnum.CD.code);
                tAccountOpenInfoVo.setChannelId(BankNameEnum.HXBANK.key());
                ResultUtil resultUtil = tCarrierCompanyOpenRoleService.updateOpenAccount(tAccountOpenInfoVo);
                if(!resultUtil.getCode().equals(CodeEnum.SUCCESS.getCode()) &&
                        null != resultUtil.getMsg()){
                    throw new RuntimeException(resultUtil.getMsg());
                }
            }
        }
        return ResultUtil.ok();
    }

    @Override
    public ResultUtil verifyPassword(TAccountVo vo){
        List<TSysUser> tSysUsers = systemAPI.selectByPhone(vo.getAccountNo());
        if(null == tSysUsers || tSysUsers.size() == 0){
            return ResultUtil.error("用户不存在");
        }else{
            for (TSysUser tSysUser : tSysUsers) {
                String s = DigestUtils.md5Hex(vo.getPassword());
                if(!s.equals(tSysUser.getPassword())){
                    return ResultUtil.error("密码错误");
                }
            }
        }
        return ResultUtil.ok();
    }

}
