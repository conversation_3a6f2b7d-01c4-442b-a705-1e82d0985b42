package com.lz.service.impl;


import com.ancun.netsign.model.ApiRespBody;
import com.ancun.netsign.model.AuthOutput;
import com.ancun.netsign.model.UserSeal;
import com.codingapi.txlcn.tc.annotation.LcnTransaction;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lz.api.CompanyProjectAPI;
import com.lz.api.NetsignAPI;
import com.lz.api.ProjectCarrierAPI;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.CodeEnum;
import com.lz.common.util.PinyinUtil;
import com.lz.common.util.ResultUtil;
import com.lz.dao.*;
import com.lz.dto.*;
import com.lz.model.*;
import com.lz.model.netsign.NetsignRequest;
import com.lz.service.TCarrierInfoService;
import com.lz.service.UserRegisterCommonService;
import com.lz.system.api.DicCatItemAPI;
import com.lz.system.api.SystemAPI;
import com.lz.system.model.TSysRole;
import com.lz.system.model.TSysUser;
import com.lz.system.model.TUserRole;
import com.lz.tpu.api.PaymentAPI;
import com.lz.tpu.api.UserRegisterAPI;
import com.lz.vo.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 *  auto dingweibo
 *  Date 2019-04-10
 *  承运方
 */
@Service("tCarrierInfoService")
public class TCarrierInfoServiceImpl implements TCarrierInfoService {

    @Resource
    private TCarrierInfoMapper tCarrierInfoMapper;

    @Resource
    private SystemAPI systemAPI;

    @Resource
    private TAccountMapper tAccountMapper;

    @Resource
    private TCarrieAccountMapper TCarrieAccountMapper;

    @Resource
    private TWalletMapper tWalletMapper;

    @Resource
    private DicCatItemAPI dicCatItemAPI;

    @Resource
    private CompanyProjectAPI companyProjectAPI;

    @Resource
    private TCarrierEnduserCompanyRelMapper carrierEnduserCompanyRelMapper;

    @Resource
    private UserRegisterAPI userRegisterAPI;

    @Resource
    private PaymentAPI paymentAPI;

    @Resource
    private TCompanyInfoMapper tCompanyInfoMapper;

    @Resource
    private ProjectCarrierAPI projectCarrierAPI;

    @Autowired
    private UserRegisterCommonService userRegisterCommon;

    @Autowired
    private NetsignAPI netsignAPI;//爱签API

    /**
     * 分页查询列表
     * @param record
     * @return
     */
    @Override
    public ResultUtil findList(TCarrierInfo record) {
        Page<Object> objectPage = PageHelper.startPage(record.getPage(), record.getSize());
        //删除状态
        record.setEnable(false);
        List<TCarrierInfoVo> list = tCarrierInfoMapper.findList(record);
        for(TCarrierInfoVo vo:list){
            if(vo.getThirdPartyInterfaceEnableSign()){
                vo.setThirdPartyInterfaceEnableSignStr("已启用");
            }else{
                vo.setThirdPartyInterfaceEnableSignStr("未启用");
            }
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),objectPage,objectPage.getTotal());
    }

    /**
     *
     * 根据id获取对象
     * @param id
     * @return
     */
    @Override
    public TCarrierInfoVo getById(String id){
        return tCarrierInfoMapper.selectById(Integer.parseInt(id));
    }

    @Override
    public ResultUtil uploadCarrierStatusById(Integer id, String carrierName){
        TCarrierInfoVo tCarrierInfoVo = tCarrierInfoMapper.selectById(id);

        TCarrierInfo tCarrierInfo = new TCarrierInfo();
        tCarrierInfo.setId(id);
        tCarrierInfo.setAhuploadedStatus(tCarrierInfoVo.getAhuploadedStatus() + "," + carrierName);
        tCarrierInfoMapper.updateByPrimaryKeySelective(tCarrierInfo);
        return ResultUtil.ok();
    }

    @Override
    public  List<CarrierOpenInfoDTO> selectCarrieOpenInfoList(CarrierOpenInfoListVO carrierOpenInfoListVO){
        List<CarrierOpenInfoDTO> selectCarrieOpenInfoList = tCarrierInfoMapper.selectCarrieOpenInfoList(carrierOpenInfoListVO);
        return selectCarrieOpenInfoList;
    }

    @Override
    public CarrieOpenInfoDetailsDTO applyCarrieOpenInfo(CarrieOpenInfoDetailsVO carrieOpenInfoDetailsVO){
        return tCarrierInfoMapper.applyCarrieOpenInfo(carrieOpenInfoDetailsVO);
    }

    @Override
    public TCarrierInfo selectCarrierById(String id){
        return tCarrierInfoMapper.selectByPrimaryKey(Integer.parseInt(id));
    }

    /**
     * 新增
     * @param record
     * @return
     */
    @Override
    @LcnTransaction
    @Transactional(rollbackFor = Exception.class)
    public ResultUtil save(TCarrierInfoVo record) {
        TCarrieAccount tCarrieAccount= TCarrieAccountMapper.selectByIdcard(record.getIdcard());
        if(tCarrieAccount!=null&&!"".equals(tCarrieAccount)){
            return ResultUtil.error("当前身份证已在其他承运方管理员存在请勿重复添加！");
        }
        TCarrierInfo r = new TCarrierInfo();
        r.setCompanyContactsPhone(record.getCompanyContactsPhone());
        List<TCarrierInfo> tCarrierInfoList = tCarrierInfoMapper.selectByCompanyContactsPhone(r);
        if(tCarrierInfoList.size()>0){
            return ResultUtil.error("当前企业手机号已在其他承运方存在请勿重复添加！");
        }

        TCarrierInfo re = new TCarrierInfo();
        re.setBusinessLicenseNo(record.getBusinessLicenseNo());
        List<TCarrierInfo> tCarrierInfoList2 = tCarrierInfoMapper.selectByCompanyContactsPhone(re);
        if(tCarrierInfoList2.size()>0){
            return ResultUtil.error("当前营业执照注册号已在其他承运方存在请勿重复添加！");
        }
        //机构
        //新增系统用户
        TSysUser user = new TSysUser();
        user.setUsername(record.getCompanyContactsPhone());
        user.setNickname(record.getCompanyContacts());
        user.setUsertype("CA");
        String pwd = DigestUtils.md5Hex("123456");
        user.setPassword(pwd);
        user.setEnable(false);
        user.setIfPasswordSecurity(false);
        ResultUtil saveUser = systemAPI.saveUser(user);
        //添加默认管理员账号
        LinkedHashMap userSave = (LinkedHashMap) saveUser.getData();
        String userId = String.valueOf(userSave.get("id"));
        TSysRole tSysRole =  systemAPI.selectByParam("PADMIN");
        if(tSysRole!=null){
            TUserRole tUserRole = new TUserRole();
            tUserRole.setUserId(Integer.parseInt(userId));
            tUserRole.setRoleId(tSysRole.getId());
            systemAPI.saveUserRole(tUserRole);
        }
        //新增账号表
        TAccount account = new TAccount();
        account.setUserId( Integer.valueOf(String.valueOf(userSave.get("id"))));
        account.setPassword(pwd);
        account.setAccountNo(record.getCompanyContactsPhone());
        account.setNickname(record.getCompanyContacts());
        account.setAcctype("PhoneNo");
        account.setUsertype("CA");
        account.setRegnode("ACCAPCOM");
        account.setDatafrom("OPPC");
        account.setIfMainAccount(true);
        account.setEnable(false);
        tAccountMapper.insertSelective(account);
        //新增承运方
        record.setEnable(false);
        record.setCarrierLogogram(PinyinUtil.toPinyin(record.getCarrierName()));//承运方简称

        tCarrierInfoMapper.insertSelective(record);
        TCarrierEnduserCompanyRel rel = new TCarrierEnduserCompanyRel();
        //承运方子账号
        //判断承运方是否有网商主账号 申请子账号
        /*if(record.getThirdPartyInterfaceMainAccountNo()!=null  &&!"".equals(record.getThirdPartyInterfaceMainAccountNo())){
            UserRequestVo ur = new UserRequestVo();
            ur.setEnterprise_name(record.getCarrierName());
            ur.setDatasouce("CA");
            ur.setCarrierId(record.getId());
            ur.setUid(IdWorkerUtil.getInstance().nextId());
            ur.setIfSave(true);
            rel = userRegisterCommon.enterpriseRegister(ur);
        }*/

        //承运方与账号关系表
        TCarrieAccount tca = new TCarrieAccount();
        tca.setAccountId(account.getId());
        tca.setCarrierId(record.getId());
        tca.setRealPosition(record.getRealPosition());
        tca.setIdcard(record.getIdcard());
        tca.setRealName(record.getCompanyContacts());
        tca.setPhone(record.getCompanyContactsPhone());
        tca.setEnable(false);
        TCarrieAccountMapper.insertSelective(tca);

        //虚拟钱包
        /*TWallet tw = new TWallet();
        tw.setCarrierEnduserCompanyId(rel.getId());
        tw.setPurseCategory("PCARRIER");
        tw.setAccountBalance(BigDecimal.ZERO);
        tw.setFrozenAmount(BigDecimal.ZERO);
        tw.setEntryAmount(BigDecimal.ZERO);
        tw.setEnable(false);
        tw.setDatasource("CA");
        tw.setWithdrawAmount(BigDecimal.ZERO);
        int i = tWalletMapper.insertSelective(tw);*/
        return ResultUtil.ok("新增成功！");
    }

    /**
     * 修改
     * @param record
     * @return
     */
    @Override
    public ResultUtil update(TCarrierInfoVo record) {
        try{
            TCarrieAccount cat = new TCarrieAccount();
            cat.setIdcard(record.getIdcard());
            cat.setCarrierId(record.getId());
            TCarrieAccount tCarrieAccount= TCarrieAccountMapper.selectByIdcardAnyCarrierId(cat);
            if(tCarrieAccount!=null&&!"".equals(tCarrieAccount)){
                return ResultUtil.error("当前身份证已再其他承运方管理员存在请勿重复添加！");
            }

            TCarrierInfo r = new TCarrierInfo();
            r.setId(record.getId());
            r.setCompanyContactsPhone(record.getCompanyContactsPhone());
            List<TCarrierInfo> tCarrierInfoList = tCarrierInfoMapper.selectByCompanyContactsPhone(r);
            if(tCarrierInfoList.size()>0){
                return ResultUtil.error("当前企业手机号已在其他承运方存在请勿重复添加！");
            }

            TCarrierInfo re = new TCarrierInfo();
            re.setId(record.getId());
            re.setBusinessLicenseNo(record.getBusinessLicenseNo());
            List<TCarrierInfo> tCarrierInfoList2 = tCarrierInfoMapper.selectByCompanyContactsPhone(re);
            if(tCarrierInfoList2.size()>0){
                return ResultUtil.error("当前营业执照注册号已在其他承运方存在请勿重复添加！");
            }
            TAccount account = TCarrieAccountMapper.selectByCarrieId(record.getId());
            //判断account 是否为空
            if(account==null||"".equals(account)){
                account = new TAccount();
                //新增系统用户
                TSysUser user = new TSysUser();
                user.setUsername(record.getCompanyContactsPhone());
                user.setNickname(record.getCompanyContacts());
                user.setUsertype("CA");
                String pwd = DigestUtils.md5Hex("123456");
                user.setPassword(pwd);
                user.setEnable(false);
                user.setIfPasswordSecurity(false);
                ResultUtil saveUser = systemAPI.saveUser(user);
                //添加默认管理员账号
                LinkedHashMap userSave = (LinkedHashMap) saveUser.getData();
                String userId = String.valueOf(userSave.get("id"));
                TSysRole tSysRole =  systemAPI.selectByParam("PADMIN");
                if(tSysRole!=null){
                    TUserRole tUserRole = new TUserRole();
                    tUserRole.setUserId(Integer.parseInt(userId));
                    tUserRole.setRoleId(tSysRole.getId());
                    systemAPI.saveUserRole(tUserRole);
                }
                //新增账号表
                account.setUserId( Integer.valueOf(String.valueOf(userSave.get("id"))));
                account.setPassword(pwd);
                account.setAccountNo(record.getCompanyContactsPhone());
                account.setNickname(record.getCompanyContacts());
                account.setAcctype("PhoneNo");
                account.setUsertype("CA");
                account.setRegnode("ACCAPCOM");
                account.setDatafrom("OPPC");
                account.setIfMainAccount(true);
                account.setEnable(false);
                tAccountMapper.insertSelective(account);

                //承运方与账号关系表
                TCarrieAccount tca = new TCarrieAccount();
                tca.setAccountId(account.getId());
                tca.setCarrierId(record.getId());
                tca.setRealPosition(record.getRealPosition());
                tca.setIdcard(record.getIdcard());
                tca.setRealName(record.getCompanyContacts());
                tca.setPhone(record.getCompanyContactsPhone());
                tca.setEnable(false);
                TCarrieAccountMapper.insertSelective(tca);

            }else{
                List<TCarrieAccount> tCarrieAccountList = TCarrieAccountMapper.getByCarrieId(record.getId());
                for(TCarrieAccount ta : tCarrieAccountList){
                    ta.setRealPosition(record.getRealPosition());
                    ta.setIdcard(record.getIdcard());
                    ta.setRealName(record.getCompanyContacts());
                    ta.setPhone(record.getCompanyContactsPhone());
                    TCarrieAccountMapper.updateByPrimaryKeySelective(ta);
                }
                //修改系统用户
                TSysUser user = new TSysUser();
                user.setId(account.getUserId());
                user.setUsername(record.getCompanyContactsPhone());
                user.setNickname(record.getCompanyContacts());
                systemAPI.updateUser(user);
                //修改账号表
                account.setAccountNo(record.getCompanyContactsPhone());
                account.setNickname(record.getCompanyContacts());
                tAccountMapper.updateByPrimaryKeySelective(account);
            }

            tCarrierInfoMapper.updateByPrimaryKeySelective(record);
            return ResultUtil.ok("修改成功！");
        }catch (RuntimeException e){
            throw e;
        }
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @Override
    public ResultUtil delete(String[] id) {
        int total = 0;
        for(int i=0;i<id.length;i++){
            List<CarrierCompanyRelDTO> carrierCompanyRelDTOS = carrierEnduserCompanyRelMapper.selectCarrierCoopCompany(Integer.parseInt(id[i]),null,null);
            if(carrierCompanyRelDTOS.size()>0){
                return ResultUtil.error("勾选承运方下已关联企业不允许删除！");
            }
            TCarrierInfo carrierInfo = new TCarrierInfo();
            carrierInfo.setId(Integer.parseInt(id[i]));
            carrierInfo.setEnable(true);
            int count = tCarrierInfoMapper.updateByPrimaryKeySelective(carrierInfo);
            //根据承运方id查出所关联的账号表id
            List<TCarrieAccount> carrieAccounts = TCarrieAccountMapper.getByCarrieId(Integer.parseInt(id[i]));
            for(TCarrieAccount tca:carrieAccounts){
                tca.setEnable(true);
                TCarrieAccountMapper.updateByPrimaryKeySelective(tca);
                TAccount ta = tAccountMapper.selectByPrimaryKey(tca.getAccountId());
                ta.setEnable(true);
                //根据账号表获取userid
                TSysUser user = new TSysUser();
                user.setId(ta.getUserId());
                user.setEnable(true);
                systemAPI.updateUser(user);
                tAccountMapper.updateByPrimaryKeySelective(ta);
            }
            //删除承运方网商子账号信息
            TCarrierEnduserCompanyRel cer = carrierEnduserCompanyRelMapper.selectByCarrier(Integer.parseInt(id[i]));
            if (cer != null) {
                cer.setEnable(true);
                carrierEnduserCompanyRelMapper.updateByPrimaryKeySelective(cer);
            }

            //删除承运方钱包
            CarrierCompanyRelDTO carrierCompanyRelDTO= tWalletMapper.selectCarrierWalletByCarrier(Integer.parseInt(id[i]));
            if (carrierCompanyRelDTO != null) {
                TWallet tWallet = new TWallet();
                tWallet.setId(carrierCompanyRelDTO.getWalletId());
                tWallet.setEnable(true);
                tWalletMapper.updateByPrimaryKeySelective(tWallet);
            }
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),"删除成功！");
    }

    @Override
    public List<CarrierInfoDTO> selectCarrier() {
        List<CarrierInfoDTO> carrierInfoDTOS = tCarrierInfoMapper.selectCarrier();
        return carrierInfoDTOS;
    }


    /**
     *  @author: dingweibo
     *  @Date: 2019/6/15 13:46
     *  @Description: 根据承运方查询企业
     */
    @Override
    public List<TCompanyInfo> selectByCarrierId(Integer carrierId) {
        return tCompanyInfoMapper.selectByCarrierId(carrierId);
    }

    @Override
    public ResultUtil cooperationCompany(TCarrierInfoVo record) {
        List<CarrierCompanyRelDTO> carrierCompanyRelDTOS = carrierEnduserCompanyRelMapper.selectCarrierCoopCompany(record.getId(),
                record.getCompanyName(),record.getThridParySubAccount());
        Iterator<CarrierCompanyRelDTO> iterator = carrierCompanyRelDTOS.iterator();
        while (iterator.hasNext()){
            CarrierCompanyRelDTO carrierCompanyRelDTO = iterator.next();
            SearchProjectVO searchProjectVO = new SearchProjectVO();
            searchProjectVO.setCarrierId(record.getId());
            searchProjectVO.setCompanyId(carrierCompanyRelDTO.getCompanyId());
           // ResultUtil resultUtil = companyProjectAPI.selectCarrierCompanyProject(searchProjectVO);
            TProjectCarrierInfoVO vo = new TProjectCarrierInfoVO(); //切换承运方展示 update 2019/07/08 dingweibo
            vo.setCarrierId(record.getId());
            vo.setCompanyId(carrierCompanyRelDTO.getCompanyId());
            vo.setProjectName(record.getProjectName());
            ResultUtil resultUtil =projectCarrierAPI.selectByProject(vo);
            ArrayList projects = (ArrayList) resultUtil.getData();
            if (null == projects || projects.size() == 0){
                iterator.remove();
            } else {
                carrierCompanyRelDTO.setProjects(projects);
            }
        }
        return ResultUtil.ok(carrierCompanyRelDTOS);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2019/9/29 10:01
     *  @Description: 承运方钱包
     */
    @Override
    public ResultUtil selectByCarrierWalle(TCarrierInfoVo vo) {
        try{
            Page<Object> objectPage = PageHelper.startPage(vo.getPage(), vo.getSize());
            List<TCarrierInfoVo> list = tCarrierInfoMapper.selectByCarrierWalle(vo);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(),objectPage,objectPage.getTotal());
        }catch (Exception e){
            throw e;
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2019/10/8 15:34
     *  @Description: 承运方提现 对公
     */

    @Override
    public ResultUtil payToCardPublic(TCarrierInfoVo vo){
        try{
            TCarrierInfo tCarrierInfo = tCarrierInfoMapper.selectByPrimaryKey(vo.getId());
            return ResultUtil.ok();
        }catch (Exception e){
            throw e;
        }
    }

    @Override
    public TCarrierInfoDTO selectCarrierInfo(Integer carrierId) {
        TCarrierInfoDTO tCarrierInfoDTO = tCarrierInfoMapper.selectCarrierInfo(carrierId);
        return tCarrierInfoDTO;
    }

    @Override
    public ResultUtil newFindList(TCarrierInfo record) {
        record.setEnable(false);
        List<TCarrierInfoVo> list = tCarrierInfoMapper.findList(record);

        Optional.ofNullable(list).orElse(Collections.emptyList())
                .forEach(vo -> vo.setThirdPartyInterfaceEnableSignStr(vo.getThirdPartyInterfaceEnableSign() ? "已启用" : "未启用"));

        return new ResultUtil(CodeEnum.SUCCESS.getCode(), list != null ? list : Collections.emptyList());
    }

    @Override
    public TCarrierInfo getDataByCarrierName(String carrierName, String businessLicenseNo) {
        return tCarrierInfoMapper.getDataByCompanyName(carrierName, businessLicenseNo);
    }
}
