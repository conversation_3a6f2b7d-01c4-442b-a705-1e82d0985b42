package com.lz.service.impl;

import com.lz.dao.TCompanyInfoDetailMapper;
import com.lz.model.TCompanyInfoDetail;
import com.lz.service.TCompanyInfoDetailService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class TCompanyInfoDetailServiceImpl implements TCompanyInfoDetailService {

    @Resource
    private TCompanyInfoDetailMapper companyInfoDetailMapper;

    @Override
    public TCompanyInfoDetail selectByCompanyId(Integer companyId) {
        return companyInfoDetailMapper.selectByCompanyId(companyId);
    }

    @Override
    public int updateByPrimaryKeySelective(TCompanyInfoDetail record) {
        return companyInfoDetailMapper.updateByPrimaryKeySelective(record);
    }

}
