package com.lz.service.impl;

import cn.hutool.json.JSONUtil;
import com.lz.common.dbenum.JdEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.CodeEnum;
import com.lz.common.model.hxPayment.request.bank.OpenMemberUnbindAccountReq;
import com.lz.common.model.hxPayment.response.bank.OpenMemberBindAccountResp;
import com.lz.common.model.jdPayment.util.CloudPayFormatUtil;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.DateUtils;
import com.lz.common.util.ResultUtil;
import com.lz.dao.TZtAccountOpenInfoMapper;
import com.lz.dao.TZtBankBindRelationshipMapper;
import com.lz.dao.TZtBankCardMapper;
import com.lz.dao.TZtBankUserMapper;
import com.lz.example.TZtBankBindRelationshipExample;
import com.lz.example.TZtBankCardExample;
import com.lz.example.TZtBankUserExample;
import com.lz.hxpay.CloudPaymentAPI;
import com.lz.model.TZtAccountOpenInfo;
import com.lz.model.TZtBankBindRelationship;
import com.lz.model.TZtBankCard;
import com.lz.model.TZtBankUser;
import com.lz.service.TZtBankUserService;
import com.lz.vo.ImperfectTZtBankCardVO;
import com.lz.vo.TZtBankCardVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service("tZtBbankUserService")
public class TZtBankUserServiceImpl implements TZtBankUserService {

    @Resource
    private TZtBankUserMapper tZtBankUserMapper;
    @Autowired
    private CloudPaymentAPI cloudPaymentAPI;
    @Resource
    private TZtAccountOpenInfoMapper tZtAccountOpenInfoMapper;
    @Resource
    private TZtBankBindRelationshipMapper tZtBankBindRelationshipMapper;

    @Resource
    private TZtBankCardMapper tZtBankCardMapper;

    @Value("${hxyhPartnerId}")
    private String hxyhPartnerId;
    @Value("${hxyhChannelId}")
    private String hxyhChannelId;
    @Override
    public ResultUtil hxyhGetBankCardList(Integer accountId) {
        String userLogisticsRole = CurrentUser.getUserLogisticsRole();
        List<ImperfectTZtBankCardVO> caList = tZtBankUserMapper.selectImperfectBankCardList(accountId,userLogisticsRole);
        for(ImperfectTZtBankCardVO tBankCard : caList){
            if(tBankCard.getAcctCard()==null  || !tBankCard.getAcctCard().equals(tBankCard.getIdcard())){
                if(tBankCard.getAcctName() == null || tBankCard.getAcctCard() == null || tBankCard.getOccBankPhone() == null){
                    tBankCard.setPerfectState("NOPERFECT");
                }
            }
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), caList);
    }

    @Override
    public int selectByBankNum(Integer accountId) {
        return tZtBankUserMapper.selectByBankNum(accountId);
    }

    @Override
    public TZtBankCardVo getHxyhBankCardDetail(Integer bankId, Integer accountId) {

        return tZtBankUserMapper.getHxyhBankCardDetail(bankId,accountId);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/9 10:16
     *  @Description: 设置默认银行卡
     */
    @Override
    public int setHxyhDefault(Integer bankId, Integer accountId) {
        tZtBankUserMapper.updateByAccountId(accountId);
        TZtBankUser tZtBankUser = tZtBankUserMapper.selectByBankIdAndAccountId(bankId,accountId);
        tZtBankUser.setIsDefault(true);
        return tZtBankUserMapper.updateByPrimaryKeySelective(tZtBankUser);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/9 10:16
     *  @Description: 司机端解绑银行卡
     */
    @Override
    public ResultUtil  delHxyhCard(Integer bankId, Integer accountId) {
        TZtAccountOpenInfo tZtAccountOpenInfo = tZtAccountOpenInfoMapper.selectByAccountId(accountId,"CD");
        TZtBankUser tZtBankUser = tZtBankUserMapper.selectByBankIdAndAccountId(bankId,accountId);
        TZtBankBindRelationshipExample example = new TZtBankBindRelationshipExample();
        TZtBankBindRelationshipExample.Criteria cr = example.createCriteria();
        cr.andAccountBankIdEqualTo(tZtBankUser.getId());
        cr.andAccountOpenIdEqualTo(tZtAccountOpenInfo.getId());
        TZtBankBindRelationship tZtBankBindRelationship = tZtBankBindRelationshipMapper.selectByExample(example).get(0);
        if(JdEnum.BINDING.code.equals(tZtBankBindRelationship.getBindStatus())){
            return ResultUtil.error("当前银行卡正在绑卡处理中不允许解绑！");
        }
        OpenMemberUnbindAccountReq request = new OpenMemberUnbindAccountReq();
        request.setPartnerId(hxyhPartnerId);
        request.setRequestId(IdWorkerUtil.getInstance().nextId());
        request.setRequestTime(DateUtils.getRequestTime());
        request.setChannelId(hxyhChannelId);
        request.setBankNo(tZtBankBindRelationship.getBankNo());
        request.setPartnerAccId(tZtAccountOpenInfo.getPartnerAccId());
        log.info("华夏银行司机解绑银行卡入参：{}",request);
        ResultUtil resultUtil = cloudPaymentAPI.execute(request);
        log.info("华夏支付 - 华夏银行对公绑卡回参，{}", JSONUtil.toJsonStr(resultUtil));
        OpenMemberBindAccountResp response = CloudPayFormatUtil.ObjToBean(resultUtil, OpenMemberBindAccountResp.class);;

        log.info("华夏银行司机解绑银行卡回参：{}",response);
        if("00000".equals(response.getResponseCode())){
           /* tZtBankCard.setEnable(true);
            tZtBankCardMapper.updateByPrimaryKeySelective(tZtBankCard);*/
            tZtBankUser.setEnable(true);
            tZtBankUserMapper.updateByPrimaryKeySelective(tZtBankUser);
            tZtBankBindRelationship.setRequestMessage("解绑处理中");
            tZtBankBindRelationshipMapper.updateByPrimaryKeySelective(tZtBankBindRelationship);
            return ResultUtil.ok("解绑处理中");
        }else{
            return ResultUtil.error("解绑失败:"+response.getResponseDesc());
        }

    }

    @Override
    public TZtBankCard selectBankByEndUserId(Integer endUserId) {

        return tZtBankUserMapper.selectBankByEndUserId(endUserId);
    }

    @Override
    public TZtBankCard selectById(Integer bankCardId) {
        return tZtBankCardMapper.selectByPrimaryKey(bankCardId);
    }

    @Override
    public ResultUtil selectBankCards(Integer endUserId) {
        return ResultUtil.ok(tZtBankCardMapper.selectBankCards(endUserId));
    }

    @Override
    public ResultUtil updateBankCardDefaultById(TZtBankUser param) {
        //设置默认银行 ，其他设为非默认的
        TZtBankUser tZtBankUser = tZtBankUserMapper.selectByPrimaryKey(param.getId());
        tZtBankUser.setIsDefault(true);
        TZtBankUserExample example = new TZtBankUserExample();
        TZtBankUserExample.Criteria cr = example.createCriteria();
        cr.andAccountIdEqualTo(param.getAccountId());
        List<TZtBankUser> tZtBankUserList = tZtBankUserMapper.selectByExample(example);
        for(TZtBankUser bankUser : tZtBankUserList){
            bankUser.setIsDefault(false);
            tZtBankUserMapper.updateByPrimaryKey(bankUser);
        }
        return ResultUtil.ok(tZtBankUserMapper.updateByPrimaryKeySelective(tZtBankUser));
    }

}
