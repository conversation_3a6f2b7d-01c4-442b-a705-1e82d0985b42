package com.lz.service.impl;

import cn.agile.anysign.sdk.api.param.ReqSealParam;
import cn.agile.anysign.sdk.api.param.ReqUserParam;
import com.lz.api.Contract5GqAPI;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.util.DateUtils;
import com.lz.common.util.ResultUtil;
import com.lz.dao.TCarrierInfoMapper;
import com.lz.dao.TEndUserInfoMapper;
import com.lz.dao.TFifthGenerationSignOpenAccountMapper;
import com.lz.model.TCarrierInfo;
import com.lz.model.TEndUserInfo;
import com.lz.model.TFifthGenerationSignOpenAccount;
import com.lz.model.ht5Gq.resp.ContractCreatUrlResp;
import com.lz.model.ht5Gq.resp.ContractCreateSealResp;
import com.lz.model.ht5Gq.resp.ContractCreateUserResp;
import com.lz.service.TFifthGenerationSignOpenAccountService;
import com.lz.vo.TCarrierInfoVo;
import com.lz.vo.TEndUserInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class TFifthGenerationSignOpenAccountServiceImpl implements TFifthGenerationSignOpenAccountService {
    @Autowired
    private Contract5GqAPI contract5GqAPI;
    @Resource
    private TCarrierInfoMapper tCarrierInfoMapper;

    @Resource
    private TEndUserInfoMapper tEndUserInfoMapper;

    @Resource
    private TFifthGenerationSignOpenAccountMapper tFifthGenerationSignOpenAccountMapper;

    @Resource
    private RedissonClient redissonClient;

    @Override
    public TFifthGenerationSignOpenAccount selectByUserIdAndType(Integer userId, String userType) {
        return tFifthGenerationSignOpenAccountMapper.selectByUserIdAndType(userId,userType);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/4/11 9:35
     *  @Description: 承运方电子注册印章
     */
    @Override
    public ResultUtil registerSign(TCarrierInfo record) {
        ResultUtil resultUtil = new ResultUtil();
        ReqUserParam req = new ReqUserParam();
        TCarrierInfo tCarrierInfo = tCarrierInfoMapper.selectByPrimaryKey(record.getId());
        req.setUserType(2);
        req.setPhone(tCarrierInfo.getCompanyContactsPhone());
        if(null==tCarrierInfo.getEmail()){
            req.setEmail(DateUtils.formatDate(new Date(),"yyMMddHHmmss")+"@luzounet.com");
            tCarrierInfo.setEmail(req.getEmail());
            tCarrierInfoMapper.updateByPrimaryKeySelective(tCarrierInfo);
        }else{
            req.setEmail(tCarrierInfo.getEmail());
        }
        req.setUserName(tCarrierInfo.getCarrierName());
        req.setCardType("7");
        req.setCardNumber(tCarrierInfo.getBusinessLicenseNo());
        ContractCreateUserResp contractCreateUserResp  = contract5GqAPI.createUser(req);
        if("200".equals(contractCreateUserResp.getCode())){
            TFifthGenerationSignOpenAccount tFifthGenerationSignOpenAccount = new TFifthGenerationSignOpenAccount();
            tFifthGenerationSignOpenAccount.setUserId(tCarrierInfo.getId());
            tFifthGenerationSignOpenAccount.setUserType(DictEnum.CA.code);
            tFifthGenerationSignOpenAccount.setSignUserId(contractCreateUserResp.getUserId());
            tFifthGenerationSignOpenAccount.setCertInfo(String.valueOf(contractCreateUserResp.getCertInfo()));
            tFifthGenerationSignOpenAccount.setSerialNo(contractCreateUserResp.getSerialNo());
            tFifthGenerationSignOpenAccount.setBeginTime(DateUtils.parseDate(contractCreateUserResp.getBeginTime()));
            tFifthGenerationSignOpenAccount.setEndTime(DateUtils.parseDate(contractCreateUserResp.getEndTime()));
            tFifthGenerationSignOpenAccount.setIssuer(contractCreateUserResp.getIssuer());
            tFifthGenerationSignOpenAccountMapper.insertSelective(tFifthGenerationSignOpenAccount);
            resultUtil.setCode(DictEnum.SUCCESS.code);
            resultUtil.setMsg(contractCreateUserResp.getMessage());
        }else{
            resultUtil.setCode(DictEnum.ERROR.code);
            resultUtil.setMsg(contractCreateUserResp.getMessage());
        }
        resultUtil.setData(tCarrierInfo.getId());
        return resultUtil;
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/4/11 10:48
     *  @Description: 承运方补充资料手动提交
     */
    @Override
    public ResultUtil updateRegisterSign(TCarrierInfo record) {
        ResultUtil resultUtil = new ResultUtil();
        ReqUserParam req = new ReqUserParam();
        TCarrierInfo tCarrierInfo = tCarrierInfoMapper.selectByPrimaryKey(record.getId());
        tCarrierInfo.setCompanyContactsPhone(record.getCompanyContactsPhone());
        tCarrierInfo.setBusinessLicenseNo(record.getBusinessLicenseNo());
        tCarrierInfo.setCarrierName(record.getCarrierName());
        tCarrierInfo.setEmail(record.getEmail());
        int i = tCarrierInfoMapper.updateByPrimaryKeySelective(tCarrierInfo);
        if(i>0){
            req.setUserType(2);
            req.setPhone(tCarrierInfo.getCompanyContactsPhone());
            req.setEmail(tCarrierInfo.getEmail());
            req.setUserName(tCarrierInfo.getCarrierName());
            req.setCardType("7");
            req.setCardNumber(tCarrierInfo.getBusinessLicenseNo());
            ContractCreateUserResp contractCreateUserResp  = contract5GqAPI.createUser(req);
            if("200".equals(contractCreateUserResp.getCode())){
                TFifthGenerationSignOpenAccount tFifthGenerationSignOpenAccount = new TFifthGenerationSignOpenAccount();
                tFifthGenerationSignOpenAccount.setUserId(tCarrierInfo.getId());
                tFifthGenerationSignOpenAccount.setUserType(DictEnum.CA.code);
                tFifthGenerationSignOpenAccount.setSignUserId(contractCreateUserResp.getUserId());
                tFifthGenerationSignOpenAccount.setCertInfo(String.valueOf(contractCreateUserResp.getCertInfo()));
                tFifthGenerationSignOpenAccount.setSerialNo(contractCreateUserResp.getSerialNo());
                tFifthGenerationSignOpenAccount.setBeginTime(DateUtils.parseDate(contractCreateUserResp.getBeginTime()));
                tFifthGenerationSignOpenAccount.setEndTime(DateUtils.parseDate(contractCreateUserResp.getEndTime()));
                tFifthGenerationSignOpenAccount.setIssuer(contractCreateUserResp.getIssuer());
                tFifthGenerationSignOpenAccountMapper.insertSelective(tFifthGenerationSignOpenAccount);
                resultUtil.setCode(DictEnum.SUCCESS.code);
                resultUtil.setMsg(contractCreateUserResp.getMessage());
            }else{
                resultUtil.setCode(DictEnum.ERROR.code);
                resultUtil.setMsg(contractCreateUserResp.getMessage());
            }
            resultUtil.setData(tCarrierInfo.getId());
        }else{
            resultUtil.setCode(DictEnum.ERROR.code);
            resultUtil.setMsg("资料修改失败");
        }
        return resultUtil;
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/4/11 11:07
     *  @Description: 添加印章
     */
    @Override
    public ResultUtil createSeal(TCarrierInfoVo record) {
        ResultUtil resultUtil = new ResultUtil();
        TFifthGenerationSignOpenAccount tFifthGenerationSignOpenAccount = tFifthGenerationSignOpenAccountMapper.selectByUserIdAndType(record.getId(), DictEnum.CA.code);
        if(null==tFifthGenerationSignOpenAccount){
            return ResultUtil.error("请先注册电子印章");
        }
        ReqSealParam req = new ReqSealParam();
        req.setUserId(tFifthGenerationSignOpenAccount.getSignUserId());
        req.setImage(record.getSealImage());
        req.setSealType(2);
        req.setSealHeight(42);
        ContractCreateSealResp contractCreateSealResp = contract5GqAPI.createSeal(req);
        if("200".equals(contractCreateSealResp.getCode())){
            tFifthGenerationSignOpenAccount.setSealId(contractCreateSealResp.getSealId());
            tFifthGenerationSignOpenAccount.setSealImage(record.getSealImage());
            tFifthGenerationSignOpenAccountMapper.updateByPrimaryKeySelective(tFifthGenerationSignOpenAccount);
            resultUtil.setCode(DictEnum.SUCCESS.code);
            resultUtil.setMsg(contractCreateSealResp.getMessage());
        }else{
            resultUtil.setCode(DictEnum.ERROR.code);
            resultUtil.setMsg(contractCreateSealResp.getMessage());

        }
        return resultUtil;
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/4/12 10:29
     *  @Description: 司机电子注册印章
     */
    @Override
    public ResultUtil diverRegisterSign(TEndUserInfo record) {
        ResultUtil resultUtil = new ResultUtil();
        String walletKey = "diverRegisterSign"+record.getId();
        RLock lock = redissonClient.getLock(walletKey);
        try {
            boolean in = lock.tryLock(5, TimeUnit.SECONDS);
            if (in) {
                TFifthGenerationSignOpenAccount fifthGenerationSignOpenAccount = tFifthGenerationSignOpenAccountMapper.selectByUserIdAndType(record.getId(), DictEnum.CD.code);
                if(null!=fifthGenerationSignOpenAccount){
                    return ResultUtil.error("当前司机已注册电子印章");
                }
                ReqUserParam req = new ReqUserParam();
                TEndUserInfo endUserInfo = tEndUserInfoMapper.selectByPrimaryKey(record.getId());
                req.setUserType(1);
                req.setPhone(endUserInfo.getPhone());
                req.setUserName(endUserInfo.getRealName());
                req.setCardType("0");
                req.setCardNumber(endUserInfo.getIdcard());
                if((null==endUserInfo.getPhone()|| "".equals(endUserInfo.getPhone())) || (null==endUserInfo.getRealName()|| "".equals(endUserInfo.getRealName()))
                        || (null==endUserInfo.getIdcard()|| "".equals(endUserInfo.getIdcard()))){
                    resultUtil.setCode(DictEnum.ERROR.code);
                    resultUtil.setMsg("电子印章注册失败。请更新完善身份证信息后，再次进入装货地签到页面");
                    resultUtil.setData("0");
                    return resultUtil;
                }
                req.setEmail(DateUtils.formatDate(new Date(),"yyMMddHHmmss")+"@luzounet.com");
                ContractCreateUserResp contractCreateUserResp  = contract5GqAPI.createUser(req);
                if("200".equals(contractCreateUserResp.getCode())){
                    TFifthGenerationSignOpenAccount tFifthGenerationSignOpenAccount = new TFifthGenerationSignOpenAccount();
                    tFifthGenerationSignOpenAccount.setUserId(endUserInfo.getId());
                    tFifthGenerationSignOpenAccount.setUserType(DictEnum.CD.code);
                    tFifthGenerationSignOpenAccount.setSignUserId(contractCreateUserResp.getUserId());
                    tFifthGenerationSignOpenAccount.setCertInfo(String.valueOf(contractCreateUserResp.getCertInfo()));
                    tFifthGenerationSignOpenAccount.setSerialNo(contractCreateUserResp.getSerialNo());
                    tFifthGenerationSignOpenAccount.setBeginTime(DateUtils.parseDate(contractCreateUserResp.getBeginTime()));
                    tFifthGenerationSignOpenAccount.setEndTime(DateUtils.parseDate(contractCreateUserResp.getEndTime()));
                    tFifthGenerationSignOpenAccount.setIssuer(contractCreateUserResp.getIssuer());
                    tFifthGenerationSignOpenAccountMapper.insertSelective(tFifthGenerationSignOpenAccount);
                    resultUtil.setCode(DictEnum.SUCCESS.code);
                    resultUtil.setMsg(contractCreateUserResp.getMessage());
                    resultUtil.setData(contractCreateUserResp.getUserId());
                }else{
                    resultUtil.setCode(DictEnum.ERROR.code);
                    resultUtil.setMsg(contractCreateUserResp.getMessage());
                    resultUtil.setData("1");
                }
                return resultUtil;

            } else {
                resultUtil.setCode(DictEnum.ERROR.code);
                resultUtil.setMsg("司机正在注册印章，请稍等");
                resultUtil.setData("1");
                return resultUtil;
            }
        }catch (Exception e){
            log.error("司机注册印章失败",e);
            resultUtil.setCode(DictEnum.ERROR.code);
            resultUtil.setMsg("司机注册印章失败");
            resultUtil.setData("1");
            return resultUtil;
        }finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/4/12 10:41
     *  @Description: 司机补充资料手动提交
     */
    @Override
    public ResultUtil updateDiverRegisterSign(TEndUserInfo record) {
        ResultUtil resultUtil = new ResultUtil();
        ReqUserParam req = new ReqUserParam();
        TEndUserInfo endUserInfo = tEndUserInfoMapper.selectByPrimaryKey(record.getId());
        endUserInfo.setPhone(record.getPhone());
        endUserInfo.setIdcard(record.getIdcard());
        endUserInfo.setRealName(record.getRealName());
        endUserInfo.setIdcardPhoto1(record.getIdcardPhoto1());
        endUserInfo.setIdcardPhoto2(record.getIdcardPhoto2());
        int i = tEndUserInfoMapper.updateByPrimaryKeySelective(endUserInfo);
        if(i>0){
            req.setUserType(1);
            req.setPhone(endUserInfo.getPhone());
            req.setUserName(endUserInfo.getRealName());
            req.setCardType("0");
            req.setCardNumber(endUserInfo.getIdcard());
            req.setEmail(DateUtils.formatDate(new Date(),"yyMMddHHmmss")+"@luzounet.com");
            ContractCreateUserResp contractCreateUserResp  = contract5GqAPI.createUser(req);
            if("200".equals(contractCreateUserResp.getCode())){
                TFifthGenerationSignOpenAccount tFifthGenerationSignOpenAccount = new TFifthGenerationSignOpenAccount();
                tFifthGenerationSignOpenAccount.setUserId(endUserInfo.getId());
                tFifthGenerationSignOpenAccount.setUserType(DictEnum.CD.code);
                tFifthGenerationSignOpenAccount.setSignUserId(contractCreateUserResp.getUserId());
                tFifthGenerationSignOpenAccount.setCertInfo(String.valueOf(contractCreateUserResp.getCertInfo()));
                tFifthGenerationSignOpenAccount.setSerialNo(contractCreateUserResp.getSerialNo());
                tFifthGenerationSignOpenAccount.setBeginTime(DateUtils.parseDate(contractCreateUserResp.getBeginTime()));
                tFifthGenerationSignOpenAccount.setEndTime(DateUtils.parseDate(contractCreateUserResp.getEndTime()));
                tFifthGenerationSignOpenAccount.setIssuer(contractCreateUserResp.getIssuer());
                tFifthGenerationSignOpenAccountMapper.insertSelective(tFifthGenerationSignOpenAccount);
                resultUtil.setCode(DictEnum.SUCCESS.code);
                resultUtil.setMsg(contractCreateUserResp.getMessage());
            }else{
                resultUtil.setCode(DictEnum.ERROR.code);
                resultUtil.setMsg(contractCreateUserResp.getMessage());
            }
            resultUtil.setData(contractCreateUserResp.getUserId());
        }else{
            resultUtil.setCode(DictEnum.ERROR.code);
            resultUtil.setMsg("资料修改失败");
        }
        return resultUtil;
    }


    /**
     *  @author: dingweibo
     *  @Date: 2022/4/12 10:50
     *  @Description: 司机添加印章
     */
    @Override
    public ResultUtil diverCreateSeal(TEndUserInfoVO record) {
        ResultUtil resultUtil = new ResultUtil();
        TFifthGenerationSignOpenAccount tFifthGenerationSignOpenAccount = tFifthGenerationSignOpenAccountMapper.selectByUserIdAndType(record.getId(), DictEnum.CD.code);
        if(null==tFifthGenerationSignOpenAccount){
            return ResultUtil.error("请先注册电子印章");
        }
        ReqSealParam req = new ReqSealParam();
        req.setUserId(tFifthGenerationSignOpenAccount.getSignUserId());
        req.setSealType(1);
        ContractCreateSealResp contractCreateSealResp = contract5GqAPI.getUserSeals(req);
        if("200".equals(contractCreateSealResp.getCode())){
            tFifthGenerationSignOpenAccount.setSealId(contractCreateSealResp.getSealId());
            tFifthGenerationSignOpenAccount.setSealImage(contractCreateSealResp.getImage());
            tFifthGenerationSignOpenAccountMapper.updateByPrimaryKeySelective(tFifthGenerationSignOpenAccount);
            resultUtil.setCode(DictEnum.SUCCESS.code);
            resultUtil.setMsg(contractCreateSealResp.getMessage());
        }else{
            resultUtil.setCode(DictEnum.ERROR.code);
            resultUtil.setMsg(contractCreateSealResp.getMessage());

        }
        return resultUtil;
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/4/12 15:28
     *  @Description: 创建手写签名
     */
    @Override
    public ContractCreatUrlResp createUrl(ReqSealParam record) {
        TFifthGenerationSignOpenAccount tFifthGenerationSignOpenAccount =
                tFifthGenerationSignOpenAccountMapper.selectByUserIdAndType(Integer.parseInt(record.getUserId()), DictEnum.CD.code);
        record.setUserId(tFifthGenerationSignOpenAccount.getSignUserId());
        return contract5GqAPI.createUrl(record);

    }

}
