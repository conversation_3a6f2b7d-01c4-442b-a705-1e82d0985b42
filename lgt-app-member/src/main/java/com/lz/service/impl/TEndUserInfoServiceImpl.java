package com.lz.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.codingapi.txlcn.tc.annotation.DTXPropagation;
import com.codingapi.txlcn.tc.annotation.LcnTransaction;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.util.StringUtil;
import com.lz.api.*;
import com.lz.common.config.RedisUtil;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.JdEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.BankCardReq;
import com.lz.common.model.BankCardResp;
import com.lz.common.model.CodeEnum;
import com.lz.common.model.datareport.hbdr.DataReport;
import com.lz.common.model.datareport.hbdr.ReportResult;
import com.lz.common.model.datareport.hbdr.driverreport.DriverReportDTO;
import com.lz.common.model.hxPayment.request.bank.OpenMemberBindAccountReq;
import com.lz.common.model.hxPayment.response.bank.OpenMemberBindAccountResp;
import com.lz.common.model.jdPayment.util.CloudPayFormatUtil;
import com.lz.common.util.*;
import com.lz.dao.*;
import com.lz.dto.*;
import com.lz.example.*;
import com.lz.hxpay.CloudPaymentAPI;
import com.lz.model.*;
import com.lz.model.contract.req.ContractApplyCertReq;
import com.lz.model.contract.resp.ContractApplyCertResp;
import com.lz.model.trajectory.req.VehicleReq;
import com.lz.model.trajectory.resp.IdCardLicenseV2Resp;
import com.lz.service.*;
import com.lz.system.api.SysParamAPI;
import com.lz.system.api.SystemAPI;
import com.lz.system.model.TSysUser;
import com.lz.system.model.*;
import com.lz.system.vo.TSysUserVO;
import com.lz.tpu.api.UserRegisterAPI;
import com.lz.tpu.web.reqeuest.UserRequest;
import com.lz.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @date 2019-04-10
*/
@Service
@Slf4j
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class TEndUserInfoServiceImpl implements TEndUserInfoService {



    @Resource
    private TEndUserInfoMapper tEndUserInfoMapper;

    @Resource
    private TEndUserCarRelMapper tEndUserCarRelMapper;

    @Resource
    private TEndCarInfoMapper tEndCarInfoMapper;

    @Resource
    private TBankCardMapper tBankCardMapper;
    @Autowired
    private ContractAPI contractAPI;

    @Resource
    private TEndUserStatusMapper endUserStatusMapper;

    @Resource
    private TEndCarStatusMapper endCarStatusMapper;

    @Autowired
    private TOrderInfoAPI orderInfoAPI;

    @Resource
    private TEnduserAccountMapper tEnduserAccountMapper;

    @Resource
    private SystemAPI systemAPI;

    @Resource
    private TAccountMapper tAccountMapper;

    @Resource
    private TEndUserStatusMapper tEndUserStatusMapper;

    @Resource
    private SysParamAPI sysParamAPI;

     @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private TUserScoreInfoAPI tUserScoreInfoAPI;

    @Resource
    private TCarrierEnduserCompanyRelMapper tCarrierEnduserCompanyRelMapper;

    @Resource
    private TWalletService tWalletService;

    @Resource
    private UserRegisterAPI userRegisterAPI;

    @Resource
    private TCarrierInfoMapper tCarrierInfoMapper;

    @Resource
    private TJdWalletMapper tJdWalletMapper;

    @Resource
    private TZtWalletMapper tZtWalletMapper;

    @Resource
    private TOcrOpenRoleMapper tOcrOpenRoleMapper;


    @Autowired
    private TJDBankCardService tjdBankCardService;

    @Resource
    private TEndUserOpenRoleMapper tEndUserOpenRoleMapper;

    @Autowired
    private TAccountService tAccountService;

    @Autowired
    private TEndUserOpenRoleService tEndUserOpenRoleService;

    @Resource
    private TFifthGenerationSignOpenAccountMapper tFifthGenerationSignOpenAccountMapper;

    @Autowired
    private TrajectoryRecentAPI trajectoryRecentAPI;

    @Resource
    private TZtBankUserMapper tZtBankUserMapper;
    @Resource
    private TZtBankCardMapper tZtBankCardMapper;
    @Resource
    private TZtBankBindRelationshipMapper tZtBankBindRelationshipMapper;
    @Resource
    private TZtAccountOpenInfoMapper tZtAccountOpenInfoMapper;
    @Autowired
    private CloudPaymentAPI cloudPaymentAPI;

    @Resource
    private TEndUserAuditInfoMapper endUserAuditInfoMapper;

    @Resource
    private TNetSignOpenAccountMapper netSignOpenAccountMapper;

    @Autowired
    private NetsignAPI netsignAPI;

    @Value("${hxyhPartnerId}")
    private String hxyhPartnerId;
    @Value("${hxyhChannelId}")
    private String hxyhChannelId;

    @Autowired
    private MqAPI mqAPI;

    @Resource
    private TCarInsuranceAPI carInsuranceAPI;

    /**
     * @Description: Feign接口：根据手机号判断，用户是否已申请合同电子签名
     * @Author: Yan
     * @Date: 2019/8/13/013 11:22
     * @Param: phone
     * @Return: boolean
     */
    @Override
    public JudgeEndUserDTO judgeEndUserIfOnlineSign(String phone,Integer endUserId) {
        JudgeEndUserDTO b = tEndUserInfoMapper.judgeEndUserIfOnlineSign(phone,endUserId);
        return b;
    }

    /**
     * @Description: 修改C端用户是否已申请合同电子签名
     * @Author: Yan
     * @Date: 2019/8/13/013 15:17
     * @Param: endUserInfo
     * @Return:
     */
    @Override
    @LcnTransaction(propagation = DTXPropagation.SUPPORTS)
    @Transactional(rollbackFor = Exception.class)
    public ResultUtil updateEndUserIfOnlineSign(TEndUserInfo endUserInfo) {
        int i = tEndUserInfoMapper.updateByPrimaryKeySelective(endUserInfo);
        return ResultUtil.ok(i);
    }

    /**
     * APP端 会员详情-获取车辆、会员信息
     * Yan
     * @param userCar
     * @return
     */
    @Override
    public ResultUtil getMemberCarDetailInfo(EnduserCarStatus userCar) {
        Map<String, Object> map = new HashMap<>();
        try {
            if (userCar.getEnduserId() != null && userCar.getEnduserId() != 0) {
                // 查询司机
                AppSearchMemberInfoDTO memberDetailInfo = tEndUserInfoMapper.getMemberDetailInfo(userCar.getEnduserId());
                //查询accountId
                TEnduserAccount tEnduserAccount = tEndUserInfoMapper.findByEndUserId(userCar.getEnduserId());
                map.put("member", memberDetailInfo);
                map.put("account",tEnduserAccount);
                //查询车主
                AppSearchMemberInfoDTO carOwner = tEndUserInfoMapper.getCarOwnerInfo(userCar.getEndcarId());
                map.put("carOwner",carOwner);
                if (null != carOwner) {
                    // 查询车主的银行卡
                    List<Map<String, Object>> endUserCard = tBankCardMapper.getEndUserCard(carOwner.getId());
                    for(Map<String, Object> tBankCard : endUserCard){
                        if(tBankCard.get("cardOwnerIdcard") == null  || !tBankCard.get("cardOwnerIdcard").equals(tBankCard.get("idcard"))){
                            if(tBankCard.get("cardOwner") == null || tBankCard.get("cardOwnerIdcard") == null || tBankCard.get("cardOwnerPhone") == null ||
                                    tBankCard.get("address") == null || tBankCard.get("idcardValidBeginning") == null || tBankCard.get("idcardValidUntil") == null){
                                tBankCard.put("perfectState","NOPERFECT");
                            }
                        }
                    }
                    map.put("carOwnerCard", endUserCard);
                    List<ImperfectTZtBankCardVO> caList = tZtBankUserMapper.selectImperfectBankCardList(tEnduserAccount.getAccountId(),DictEnum.CTYPECAPTAIN.code);
                    map.put("hxCarOwnerCard", caList);
                }
            }
            if (userCar.getEndcarId() != null && userCar.getEndcarId() != 0) {
                // 查询车辆
                AppSearchCarInfoDTO carDetailInfo = tEndCarInfoMapper.getCarInfo(userCar.getEndcarId());
                map.put("car", carDetailInfo);
            }

            if (userCar.getEnduserId() != null && userCar.getEnduserId() != 0) {
                // 查询司机银行卡
                List<Map<String, Object>> endUserCard = tBankCardMapper.getEndUserCard(userCar.getEnduserId());
                for(Map<String, Object> tBankCard : endUserCard){
                    if(tBankCard.get("cardOwnerIdcard") == null  || !tBankCard.get("cardOwnerIdcard").equals(tBankCard.get("idcard"))){
                        if(tBankCard.get("cardOwner") == null || tBankCard.get("cardOwnerIdcard") == null || tBankCard.get("cardOwnerPhone") == null ||
                                tBankCard.get("address") == null || tBankCard.get("idcardValidBeginning") == null || tBankCard.get("idcardValidUntil") == null){
                            tBankCard.put("perfectState","NOPERFECT");
                        }
                    }
                }
                map.put("driverCard", endUserCard);
                TEnduserAccount tEnduserAccount = tEndUserInfoMapper.findByEndUserId(userCar.getEnduserId());
                List<ImperfectTZtBankCardVO> caList = tZtBankUserMapper.selectImperfectBankCardList(tEnduserAccount.getAccountId(),DictEnum.CTYPEDRVIVER.code);
                map.put("hxDriverCard", caList);
            }
        } catch (Exception e) {
            log.error("司机车辆信息错误:", e);
            return ResultUtil.error("司机车辆信息错误");
        }
        return ResultUtil.ok(map);
    }

    /**
     * 运单打包： 获取用户所有银行卡
     * Yan
     * @param id
     * @return
     */
    @Override
    public ResultUtil getUserBankCards(Integer id) {
        List<Map<String, Object>> userBankCard = tEndUserInfoMapper.getUserBankCard(id);
        return ResultUtil.ok(userBankCard);
    }

    /**
     * 判断C端用户是否有银行卡
     * true没有   false有
     * Yan
     * @param id
     * @return
     */
    @Override
    public ResultUtil judgeUserCard(Integer id) {
        Boolean aBoolean = tEndUserInfoMapper.judgeAgentCar(id);
        return ResultUtil.ok(aBoolean);
    }

    @Override
    public TEndUserInfoVO findById(Integer id) {
        TEndUserInfo tEndUserInfo = tEndUserInfoMapper.selectByPrimaryKey(id);
        TEndUserInfoVO vo = new TEndUserInfoVO();
        BeanUtils.copyProperties(tEndUserInfo,vo);

        TNetSignOpenAccount tNetSignOpenAccount = netSignOpenAccountMapper.selectByUserId(id, DictEnum.CD.code);
        if(null!=tNetSignOpenAccount){
            if (null != tNetSignOpenAccount.getSealNo() && StringUtils.isNotBlank(tNetSignOpenAccount.getSealNo())) {
                if(null!=tNetSignOpenAccount.getSealImage()){
                    vo.setSealImage(tNetSignOpenAccount.getSealImage());
                }
                vo.setSignStatus("已注册");
            } else {
                vo.setSignStatus("未注册");
            }
        }else{
            vo.setSignStatus("未注册");
        }
        //根据id查询与account关系
        TEnduserAccountExample example = new TEnduserAccountExample();
        TEnduserAccountExample.Criteria cr = example.createCriteria();
        cr.andEnduserIdEqualTo(tEndUserInfo.getId());
        cr.andEnableEqualTo(false);
        //根据accountId查询银行卡
        List<TEnduserAccount>  tEnduserAccountList = tEnduserAccountMapper.selectByExample(example);
        if(tEnduserAccountList.size()>0){
            //银行卡列表
            TBankCardExample example2 = new TBankCardExample();
            TBankCardExample.Criteria c = example2.createCriteria();
            c.andAccountIdEqualTo(tEnduserAccountList.get(0).getAccountId());
            c.andEnableEqualTo(false);
            List<TBankCard> tBankCardList = tBankCardMapper.selectByExample(example2);
            vo.setTBankCardList(tBankCardList);

            List<TZtBankCardVo> tZtBankCardVoList = tZtBankCardMapper.selectBankCardInfoByAccountId(tEnduserAccountList.get(0).getAccountId());
            vo.setTZtBankCardVoList(tZtBankCardVoList);
        }
        //查询司机绑定的车辆

        List<TEndCarInfo> tEndCarInfoList = tEndCarInfoMapper.selectByEndUserId(id);
        vo.setTEndCarInfoList(tEndCarInfoList);
        //司机综合评分
        TUserScoreInfoVO tuserscore =new TUserScoreInfoVO();
        tuserscore.setUserType("CD");
        tuserscore.setEndUserId(id);
        ResultUtil  UserScore=tUserScoreInfoAPI.selectDriverScore(tuserscore);
        //Object data = UserScore.getData();
        vo.setTUserScoreInfo(UserScore.getData());

        // 查询各个证件审核状态
        TEndUserAuditInfo endUserAuditInfo = endUserAuditInfoMapper.selectByEndUserId(id);
        if (null != endUserAuditInfo) {
            vo.setIdcardStatus(endUserAuditInfo.getIdcardStatus());
            vo.setIdcardOpinion(endUserAuditInfo.getIdcardOpinion());
            vo.setCertificateStatus(endUserAuditInfo.getCertificateStatus());
            vo.setCertificateOpinion(endUserAuditInfo.getCertificateOpinion());
            vo.setDrivingLicencesStatus(endUserAuditInfo.getDrivingLicencesStatus());
            vo.setDrivingLicencesOpinion(endUserAuditInfo.getDrivingLicencesOpinion());
        }

        return vo;
    }
    @Override
    public ResultUtil findByUserId(Integer id) {
        TEndUserInfo tEndUserInfo = tEndUserInfoMapper.selectByPrimaryKey(id);
        Map<Object, String> map = new HashMap<>();
        map.put("userLogisticsRole",CurrentUser.getUserLogisticsRole());
        map.put("name",tEndUserInfo.getRealName()==null?"":tEndUserInfo.getRealName());
        map.put("idcard",tEndUserInfo.getIdcard()==null?"":tEndUserInfo.getIdcard());
        map.put("idcardPhoto1",tEndUserInfo.getIdcardPhoto1()==null?"":tEndUserInfo.getIdcardPhoto1());
        map.put("idcardPhoto2",tEndUserInfo.getIdcardPhoto2()==null?"":tEndUserInfo.getIdcardPhoto2());
        map.put("drivingLicencesPhoto1",tEndUserInfo.getDrivingLicencesPhoto1()==null?"":tEndUserInfo.getDrivingLicencesPhoto1());
        map.put("drivingLicencesPhoto2",tEndUserInfo.getDrivingLicencesPhoto2()==null?"":tEndUserInfo.getDrivingLicencesPhoto2());
        map.put("certificatePhoto1",tEndUserInfo.getCertificatePhoto1()==null?"":tEndUserInfo.getCertificatePhoto1());
        map.put("certificatePhoto2",tEndUserInfo.getCertificatePhoto2()==null?"":tEndUserInfo.getCertificatePhoto2());
        if(null != tEndUserInfo.getCertificateValidUntil() && !"".equals(tEndUserInfo.getCertificateValidUntil())){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            map.put("certificateValidUntil",sdf.format(tEndUserInfo.getCertificateValidUntil()));
        }else {
            map.put("certificateValidUntil","");
        }
        if(tEndUserInfo.getAuditStatus()!=null&&!"".equals(tEndUserInfo.getAuditStatus())){
            map.put("auditStatus",tEndUserInfo.getAuditStatus());
        }else{
            map.put("auditStatus","");
        }
        if(tEndUserInfo.getAuditOpinion()!=null&&!"".equals(tEndUserInfo.getAuditOpinion())){
            map.put("auditOpinion",tEndUserInfo.getAuditOpinion());
        }else{
            map.put("auditOpinion","");
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), map);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int add(TEndUserInfo resources) {
        return tEndUserInfoMapper.insertSelective(resources);
    }
    @Override
    public int updataData(TEndUserInfo resources) {
        return tEndUserInfoMapper.updateByPrimaryKeySelective(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultUtil update(EndUserInfoUpdate resources) {
        TEndUserInfo endUser = tEndUserInfoMapper.selectByPrimaryKey(resources.getId());

        List<TEndUserInfo> idcardCount = tEndUserInfoMapper.notOwnIdcardList(resources.getIdcard(),resources.getId());
        if(null != idcardCount && !idcardCount.isEmpty() && !resources.getIdcard().equals(endUser.getIdcard())){
            return ResultUtil.error("编辑失败，该身份证号已在平台存在!");
        }

        resources.setAddress(resources.getAddress());
        if (StringUtils.isNotBlank(resources.getAddress()) && null!=resources.getIdcardValidUntil() &&!"".equals(resources.getIdcardValidUntil())){
            resources.setAddressState(true);
        }else {
            resources.setAddressState(false);
        }
        if(!endUser.getPhone().equals(resources.getPhone()) || !endUser.getRealName().equals(resources.getRealName()) || !endUser.getIdcard().equals(resources.getIdcard())){
            ContractApplyCertReq data1 = new ContractApplyCertReq();
            data1.setType("1");//类型 1：个人 、2：企业
            data1.setCardType("0");// 证件类型 0：身份证 1：军官证 ,2：护照、 3：驾驶证、4：工商登记证、5：税务登记证、6：组织机构代码、7：其他证件，8：统一社会信用代码
            data1.setIdCardNum(endUser.getIdcard());// 证件号码
            data1.setName(endUser.getRealName());//企业或者个人真实名称
            data1.setMobilePhone(endUser.getPhone());//企业或者个人联系手机号
            ContractApplyCertResp resp = contractAPI.unwrap(data1);
            if(resp!=null&&!"".equals(resp)){
                if(resp.getCode().equals("0")){
                    ContractApplyCertReq data = new ContractApplyCertReq();
                    data.setType("1");//类型 1：个人 、2：企业
                    data.setCardType("0");// 证件类型 0：身份证 1：军官证 ,2：护照、 3：驾驶证、4：工商登记证、5：税务登记证、6：组织机构代码、7：其他证件，8：统一社会信用代码
                    data.setIdCardNum(resources.getIdcard()); // 证件号码
                    data.setName(resources.getRealName());//企业或者个人真实名称
                    data.setMobilePhone(resources.getPhone());//企业或者个人联系手机号
                    ContractApplyCertResp cert = contractAPI.applyCert(data);
                    if(cert!=null&&!"".equals(cert)){
                        if (cert.getCode().equals("0")) {
                            resources.setIssuer(cert.getIssuer());
                            resources.setSerialNumber(cert.getSerialNumber());
                            resources.setBeginTime(DateUtils.parseDate(cert.getCertNotBefore()));
                            resources.setEndTime(DateUtils.parseDate(cert.getCertNotAfter()));

                          /*TEndUserInfoExample ex = new TEndUserInfoExample();
                            TEndUserInfoExample.Criteria c = ex.createCriteria();
                            c.andIdEqualTo(resources.getId());
                            tEndUserInfoMapper.updateByExampleSelective(resources,ex);
                            return ResultUtil.ok();*/
                        }
                    }
                }
            }

        }
        resources.setUpdateTime(new Date());
        resources.setUpdateUser(CurrentUser.getUserNickname());
        tEndUserInfoMapper.updateByPrimaryKeySelective(resources);
        //其他关系表 姓名 身份证一起修改
        if(!endUser.getRealName().equals(resources.getRealName())|| !endUser.getIdcard().equals(resources.getIdcard())){
            TEnduserAccount tEnduserAccount = tEnduserAccountMapper.selectByEndUserId(resources.getId());
            if(null!=tEnduserAccount &&!"".equals(tEnduserAccount)){
                tEnduserAccount.setRealName(resources.getRealName());
                tEnduserAccount.setIdcard(resources.getIdcard());
                tEnduserAccountMapper.updateByPrimaryKeySelective(tEnduserAccount);
                TAccount tAccount = tAccountMapper.selectByPrimaryKey(tEnduserAccount.getAccountId());
                if(null!=tAccount &&!"".equals(tAccount)){
                    tAccount.setNickname(resources.getRealName());
                    tAccountMapper.updateByPrimaryKeySelective(tAccount);
                    //sysUser表
                    TSysUser tSysUser = new TSysUser();
                    tSysUser.setId(tAccount.getUserId());
                    tSysUser.setNickname(resources.getRealName());
                    systemAPI.updateUser(tSysUser);
                }
            }
        }
        // 认证
        if (null != resources.getUpdateOrAudit() && resources.getUpdateOrAudit() == 1) {
            // 更新司机审核信息表
            TEndUserAuditInfo endUserAuditInfo = endUserAuditInfoMapper.selectByEndUserId(resources.getId());
            if (null == endUserAuditInfo) {
                // 旧数据，添加司机审核信息表
                endUserAuditInfo = new TEndUserAuditInfo();
                endUserAuditInfo.setEndUserId(resources.getId());
                if (endUser.getUserLogisticsRole().contains(DictEnum.CTYPEDRVIVER.code)) {
                    endUserAuditInfo.setUserLogisticsRole(DictEnum.CTYPEDRVIVER.code);
                } else if (endUser.getUserLogisticsRole().equals(DictEnum.CTYPECAPTAIN.code)) {
                    endUserAuditInfo.setUserLogisticsRole(DictEnum.CTYPECAPTAIN.code);
                }
                endUserAuditInfoMapper.insertSelective(endUserAuditInfo);
            }
            // 修改司机审核信息
            endUserAuditInfo.setIdcardStatus(resources.getIdcardStatus());
            endUserAuditInfo.setIdcardOpinion(resources.getIdcardOpinion());
            endUserAuditInfo.setCertificateStatus(resources.getCertificateStatus());
            endUserAuditInfo.setCertificateOpinion(resources.getCertificateOpinion());
            endUserAuditInfo.setDrivingLicencesStatus(resources.getDrivingLicencesStatus());
            endUserAuditInfo.setDrivingLicencesOpinion(resources.getDrivingLicencesOpinion());
            endUserAuditInfo.setEnable(false);
            endUserAuditInfo.setUpdateUser(CurrentUser.getUserNickname());
            endUserAuditInfo.setUpdateTime(new Date());
            endUserAuditInfoMapper.updateByPrimaryKey(endUserAuditInfo);

            // 根据各个证件认证状态，计算总认证状态
            String auditStatus = AuditStatusUtil.status(resources.getIdcardStatus(), resources.getCertificateStatus(), resources.getDrivingLicencesStatus());
            TEndUserInfo endUserInfo = new TEndUserInfo();
            endUserInfo.setId(resources.getId());
            if (StringUtils.isNotBlank(auditStatus)) {
                endUserInfo.setAuditStatus(auditStatus);
            }
            // 拼接审核意见
            String auditOpinions = AuditStatusUtil.auditOpinion(resources.getIdcardOpinion(), resources.getCertificateOpinion(), resources.getDrivingLicencesOpinion());
            if (StringUtils.isNotBlank(auditOpinions)) {
                endUserInfo.setAuditOpinion(auditOpinions);
            }
            if (StringUtils.isNotBlank(auditStatus) || StringUtils.isNotBlank(auditOpinions)) {
                endUserInfo.setUpdateUser(CurrentUser.getUserNickname());
                tEndUserInfoMapper.updateAuditStatusById(endUserInfo);
            }
        }
        return ResultUtil.ok();

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Integer id) {
        tEndUserInfoMapper.deleteByPrimaryKey(id);
    }

    @Override
    public ResultUtil selectByPage(TEndUserInfoSearchVO params) {
        params.setUserLogisticsRole("CTYPEDRVIVER");
        //设置配置GROUP_CONCAT 参数大小 10240
        tEndUserInfoMapper.setGroupConcatMaxLen();
        Page<Object> page = PageHelper.startPage(params.getPage(),params.getSize());
        List<TEndUserInfoVO> tEndUserInfoVOS = tEndUserInfoMapper.selectByPage(params);
        tEndUserInfoVOS.forEach((endUserInfo) -> {
            if(endUserInfo.getCreateTime() != null){
                endUserInfo.setCreateTimeStr(DateUtils.formatDateTime(endUserInfo.getCreateTime()));
            }
            if(endUserInfo.getUpdateTime() != null){
                endUserInfo.setUpdateTimeStr(DateUtils.formatDateTime(endUserInfo.getUpdateTime()));
            }
            if(endUserInfo.getAddressState() != null) {
                if (endUserInfo.getAddressState()){
                    endUserInfo.setAddressStateValue("已填写");
                }else {
                    endUserInfo.setAddressStateValue("未填写");
                }
            }
            // 身份证有效期状态
            if (null != endUserInfo.getIdcardValidUntil() && null != endUserInfo.getIdcardValidBeginning()) {
                Date validUntil = endUserInfo.getIdcardValidUntil();
                if (endUserInfo.getIdcardValidBeginning().getTime() > endUserInfo.getIdcardValidUntil().getTime()) {
                    validUntil = endUserInfo.getIdcardValidBeginning();
                }
                endUserInfo.setIdcardValidStatus(DateUtils.checkValidDate(validUntil));
                endUserInfo.setIdcardValidUntilDate(DateUtils.formatDate(validUntil, "yyyy-MM-dd"));
            }
            // 从业资格证有效期状态
            if (null != endUserInfo.getCertificateValidUntil()) {
                endUserInfo.setCertificateValidStatus(DateUtils.checkValidDate(endUserInfo.getCertificateValidUntil()));
                endUserInfo.setCertificateValidUntilDate(DateUtils.formatDate(endUserInfo.getCertificateValidUntil(), "yyyy-MM-dd"));
            }
            // 驾驶证有效期状态
            if (null != endUserInfo.getDrivingLicencesValidUntil()) {
                endUserInfo.setDrivingLicencesValidStatus(DateUtils.checkValidDate(endUserInfo.getDrivingLicencesValidUntil()));
                endUserInfo.setDrivingLicencesValidUntilDate(DateUtils.formatDate(endUserInfo.getDrivingLicencesValidUntil(), "yyyy-MM-dd"));
            }
        });
        ResultUtil resultUtil = new ResultUtil();
        resultUtil.setData(tEndUserInfoVOS);
        resultUtil.setCount(page.getTotal());
        return resultUtil;
    }

    //@LcnTransaction
    @Transactional
    @Override
    public ResultUtil deleteByUserID(TEndUserInfoDto record) {
        try{
            List idList = new ArrayList();
            //CTYPEDRVIVER //CTYPEBOSS
            for(int i=0;i<record.getIdArray().length;i++){
                idList.add(record.getIdArray()[i]);
            }
            TEndUserInfoExample example = new TEndUserInfoExample();
            TEndUserInfoExample.Criteria cr = example.createCriteria();
            cr.andIdIn(idList);
            List<TEndUserInfo> tEndUserInfoList = tEndUserInfoMapper.selectByExample(example);
            for(TEndUserInfo tEndUserInfo:tEndUserInfoList){
                //司机删除处理
                if("CTYPEDRVIVER".equals(record.getType())){
                    List list = orderInfoAPI.selectByEndDriverIdFeign(tEndUserInfo.getId());
                    if(list.size()>0){
                        return  ResultUtil.error("勾选的司机已存在运单不允许删除！");
                    }
                    //如果有两个角色把司机角色删除，如果只有司机角色直接逻辑删除
                    if(tEndUserInfo.getUserLogisticsRole().split(",").length>1){
                        tEndUserInfo.setUserLogisticsRole("CTYPEBOSS");
                    }else{
                        if (!"CTYPEBOSS".equals(tEndUserInfo.getUserLogisticsRole().trim())){
                            tEndUserInfo.setEnable(true);
                            //账号表
                            TAccountVo tAccountVo = new TAccountVo();
                            tAccountVo.setAccountNo(tEndUserInfo.getPhone());
                            TAccount tAccount = tAccountMapper.selectAccountByPhone(tAccountVo);

                            TEnduserAccount tEnduserAccountReq = new TEnduserAccount();
                            tEnduserAccountReq.setAccountId(tAccount.getId());
                            List<TEnduserAccount> selectEndUser = tEndUserInfoMapper.selectEnduserList(tEnduserAccountReq);
                            if(selectEndUser.size() < 2){
                                if(null!=tAccount &&!"".equals(tAccount)){
                                    tAccount.setEnable(true);
                                    tAccountMapper.updateByPrimaryKeySelective(tAccount);
                                }
                                //sysUser表
                                TSysUser tSysUser = new TSysUser();
                                tSysUser.setId(tAccount.getUserId());
                                tSysUser.setEnable(true);
                                systemAPI.updateUser(tSysUser);
                            }
                            //终端用户与账号关系表
                            List<TEnduserAccount> tEnduserAccountList = tEnduserAccountMapper.selectByEndUserInfoIdAndAccountId(tEndUserInfo.getId(),tAccount.getId());
                            for(TEnduserAccount tEnduserAccount:tEnduserAccountList){
                                tEnduserAccount.setEnable(true);
                                tEnduserAccountMapper.updateByPrimaryKeySelective(tEnduserAccount);
                            }
                            //终端用户状态表
                            TEndUserStatus tEndUserStatus = tEndUserStatusMapper.selectByEndUserId(tEndUserInfo.getId());
                            if(null!=tEndUserStatus &&!"".equals(tEndUserStatus)) {
                                tEndUserStatus.setEnable(true);
                                tEndUserStatusMapper.updateByPrimaryKeySelective(tEndUserStatus);
                            }
                            // 将TuserInfo置为删除
                            TUserInfo userInfo = new TUserInfo();
                            userInfo.setAccountId(tAccount.getUserId());
                            userInfo.setEnable(true);
                            systemAPI.updateByAccountIdSelective(userInfo);
                        }else{
                            return ResultUtil.error("此司机已删除,请勿重复操作！");
                        }
                    }
                    //司机与车辆关系
                    TEndUserCarRel tEndUserCarRel = new TEndUserCarRel();
                    tEndUserCarRel.setEnduserId(tEndUserInfo.getId());
                    tEndUserCarRel.setUserCarRelationType("CLSYRSJ");
                    List<TEndUserCarRelVo> tEndUserCarRelVoList = tEndUserCarRelMapper.selectByEnduserId(tEndUserCarRel);
                    for(TEndUserCarRelVo tEndUserCarRelVo:tEndUserCarRelVoList){
                        tEndUserCarRelVo.setEnable(true);
                        tEndUserCarRelMapper.updateByPrimaryKeySelective(tEndUserCarRelVo);
                    }
                    tEndUserInfoMapper.updateByPrimaryKeySelective(tEndUserInfo);
                }else if("CTYPEBOSS".equals(record.getType())){//车老板删除处理
                    List list = orderInfoAPI.selectByEndCarOwnerIdFeign(tEndUserInfo.getId());
                    if(list.size()>0){
                        return  ResultUtil.error("勾选的车主已存在运单不允许删除！");
                    }
                    //如果有两个角色把车主角色删除，如果只有车主角色直接逻辑删除
                    if(tEndUserInfo.getUserLogisticsRole().split(",").length>1){
                        tEndUserInfo.setUserLogisticsRole("CTYPEDRVIVER");
                    }else{
                        if (!"CTYPEDRVIVER".equals(tEndUserInfo.getUserLogisticsRole().trim())){
                            tEndUserInfo.setEnable(true);
                            //账号表
                            TAccountVo tAccountVo = new TAccountVo();
                            tAccountVo.setAccountNo(tEndUserInfo.getPhone());
                            TAccount tAccount = tAccountMapper.selectAccountByPhone(tAccountVo);

                            TEnduserAccount tEnduserAccountReq = new TEnduserAccount();
                            tEnduserAccountReq.setAccountId(tAccount.getId());
                            List<TEnduserAccount> selectEndUser = tEndUserInfoMapper.selectEnduserList(tEnduserAccountReq);
                            if(selectEndUser.size() < 2){
                                if(null!=tAccount &&!"".equals(tAccount)){
                                    tAccount.setEnable(true);
                                    tAccountMapper.updateByPrimaryKeySelective(tAccount);
                                }
                                //sysUser表
                                TSysUser tSysUser = new TSysUser();
                                tSysUser.setId(tAccount.getUserId());
                                tSysUser.setEnable(true);
                                systemAPI.updateUser(tSysUser);
                            }

                            //终端用户与账号关系表
                            List<TEnduserAccount> tEnduserAccountList = tEnduserAccountMapper.selectByEndUserInfoIdAndAccountId(tEndUserInfo.getId(),tAccount.getId());
                            for(TEnduserAccount tEnduserAccount:tEnduserAccountList){
                                tEnduserAccount.setEnable(true);
                                tEnduserAccountMapper.updateByPrimaryKeySelective(tEnduserAccount);
                            }
                            //终端用户状态表
                            TEndUserStatus tEndUserStatus = tEndUserStatusMapper.selectByEndUserId(tEndUserInfo.getId());
                            if(null!=tEndUserStatus &&!"".equals(tEndUserStatus)){
                                tEndUserStatus.setEnable(true);
                                tEndUserStatusMapper.updateByPrimaryKeySelective(tEndUserStatus);
                            }
                            // 将TuserInfo置为删除
                            TUserInfo userInfo = new TUserInfo();
                            userInfo.setAccountId(tAccount.getUserId());
                            userInfo.setEnable(true);
                            systemAPI.updateByAccountIdSelective(userInfo);
                        }else{
                            return ResultUtil.error("此车主已删除,请勿重复操作！");
                        }
                    }
                    //车主与车辆关系
                    TEndUserCarRel tEndUserCarRel = new TEndUserCarRel();
                    tEndUserCarRel.setEnduserId(tEndUserInfo.getId());
                    tEndUserCarRel.setUserCarRelationType("CLSYRCLB");
                    List<TEndUserCarRelVo> tEndUserCarRelVoList = tEndUserCarRelMapper.selectByEnduserId(tEndUserCarRel);
                    for(TEndUserCarRelVo tEndUserCarRelVo:tEndUserCarRelVoList){
                        tEndUserCarRelVo.setDataConcelFrom("DRIWX");
                        tEndUserCarRelVo.setDataConcelTime(new Date());
                        tEndUserCarRelVo.setEnable(true);
                        tEndUserCarRelMapper.updateByPrimaryKeySelective(tEndUserCarRelVo);
                    }
                    tEndUserInfoMapper.updateByPrimaryKeySelective(tEndUserInfo);
                }
            }
            return ResultUtil.ok("删除成功！");
        }catch (Exception e){
            log.error("终端用户删除失败",e);
            throw e;
        }
    }

    @Override
    public int approval(ApprovalVO param) {
        return tEndUserInfoMapper.approval(param);
    }
    /**
     * 上传监管平台
     * 上传司机信息
     * @return
     */
    public ResultUtil driverBasicInformation(DriverReportDTO dto){
        try{
            TEndUserInfoExample tEndUserInfoExample=new TEndUserInfoExample();
            TEndUserInfoExample.Criteria cr = tEndUserInfoExample.createCriteria();
            TEndUserInfo tEndUserInfo =new TEndUserInfo();
            cr.andIdcardEqualTo(dto.getDrivingLicense());
            cr.andRealNameEqualTo(dto.getDriverName());
            cr.andEnableEqualTo(false);
            List<TEndUserInfo> tEndUserInfos = tEndUserInfoMapper.selectByExample(tEndUserInfoExample);
            if(!tEndUserInfos.isEmpty()){
                if(tEndUserInfos.get(0).getUploadedStatus() != null){
                    if(tEndUserInfos.get(0).getUploadedStatus().equals(DictEnum.UPLOADED.code)){
                        return ResultUtil.error("司机已上报不能重复上报");
                    }
                }
                //司机是否通过平台审核，未通过不能上传信息
                if(DictEnum.PASSNODE.code.equals(tEndUserInfos.get(0).getAuditStatus())){
                    //平台返回信息
                    log.info("河北日志上报请求参数---------------------------------------------:"+JSONObject.toJSONString(dto));
                    ReportResult resultUtil=new DataReport(dto).execute();
                    String jsonObject = JSONObject.toJSONString(dto);
                    log.info("河北日志上报请求返回状态参数---------------------------------------------:"+resultUtil);
                    //是否上报成功
                    if(resultUtil.getSuccess().equals("true")){
                        //上报成功
                        tEndUserInfo.setId(tEndUserInfos.get(0).getId());
                        tEndUserInfo.setUploadedStatus(DictEnum.UPLOADED.code);
                        tEndUserInfo.setUploadedData(jsonObject);
                        tEndUserInfo.setUploadedTime(new Date());
                        tEndUserInfoMapper.updateByPrimaryKeySelective(tEndUserInfo);
                        return ResultUtil.ok();
                    }else{
                        //上传失败
                        tEndUserInfo.setId(tEndUserInfos.get(0).getId());
                        tEndUserInfo.setUploadedStatus(DictEnum.NOTPASS.code);
                        tEndUserInfoMapper.updateByPrimaryKeySelective(tEndUserInfo);
                        return ResultUtil.error(resultUtil.getMsg());
                    }
                }
                return ResultUtil.error("司机未通过审核不能上报!");
            }else {
                return ResultUtil.error("上报监管平台失败!");
            }
        }catch (Exception e){
            throw new RuntimeException(e.getMessage());
        }
    }
    /**
     * 查询司机
     * @param record
     * @return
     */
    @Override
    public ResultUtil selectEndUser(TEndUserInfoSearchVO record) {
        List<EndUserDTO> endUserDTOS = tEndUserInfoMapper.selectEndUser(record);
        //查询司机绑定的车辆
        for (EndUserDTO userDTO: endUserDTOS){

        }
        return ResultUtil.ok(endUserDTOS);
    }

    /**
     * 查询经纪人
     * @param record
     * @return
     */
    @Override
    public ResultUtil selectMamage(TEndUserInfoSearchVO record) {
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        record.setCompanyIds(userCompanyId);
        if (null != record.getPage() && null != record.getSize()){
            Page<Object> page = PageHelper.startPage(record.getPage(), record.getSize());
            List<EndUserDTO> endUserDTOS = tEndUserInfoMapper.selectManage(record);
            return ResultUtil.ok(endUserDTOS, page.getTotal());
        }
        List<EndUserDTO> endUserDTOS = tEndUserInfoMapper.selectManage(record);
        return ResultUtil.ok(endUserDTOS);
    }

    /**
     *  车老板列表
     * @param tEndUserInfoVO
     * @return
     */
    @Override
    public ResultUtil selectEndCarOwnerByPage(TEndUserInfoVO tEndUserInfoVO) {
        //设置配置GROUP_CONCAT 参数大小 10240
        tEndUserInfoMapper.setGroupConcatMaxLen();
        Page<Object> objectPage = PageHelper.startPage(tEndUserInfoVO.getPage(),tEndUserInfoVO.getSize());
        List<TEndUserInfoVO> tEndUserInfoList = tEndUserInfoMapper.selectByCarNumber(tEndUserInfoVO);
        for(TEndUserInfoVO vo:tEndUserInfoList){
            if("NEWNODE".equals(vo.getAuditStatus())){
                vo.setAuditStatusValue("新建");
            }else if("MIDNODE".equals(vo.getAuditStatus())){
                vo.setAuditStatusValue("审核中");
            }else if("NOTPASSNODE".equals(vo.getAuditStatus())){
                vo.setAuditStatusValue("审核不通过");
            }else if("PAPERNEEDUPDATE".equals(vo.getAuditStatus())){
                vo.setAuditStatusValue("资料需更新");
            }else if("PASSNODE".equals(vo.getAuditStatus())){
                vo.setAuditStatusValue("审核通过");
            }
            if(vo.getCp()==null || "".equals(vo.getCp())){
                vo.setCarCount(0);
            }else{
                vo.setCarCount(vo.getCp().split(",").length);
            }
            if(vo.getCreateTime()!=null && !"".equals(vo.getCreateTime())){
                vo.setCreateTimeStr(DateUtils.formatDateTime(vo.getCreateTime()));
            }
        }
      /*  List<TEndUserInfoVO> list = new ArrayList<TEndUserInfoVO>();
        //车老板信息
        TEndUserInfoExample example = new TEndUserInfoExample();
        TEndUserInfoExample.Criteria cr = example.createCriteria();
        cr.andUserLogisticsRoleEqualTo("CCARBOSS");//车老板
        cr.andEnableEqualTo(false);
        if(tEndUserInfoVO.getRealName()!=null&&!"".equals(tEndUserInfoVO.getRealName())){
            cr.andRealNameLike(tEndUserInfoVO.getRealName());
        }
        if(tEndUserInfoVO.getPhone()!=null&&!"".equals(tEndUserInfoVO.getPhone())){
            cr.andPhoneLike(tEndUserInfoVO.getPhone());
        }
        if(tEndUserInfoVO.getAuditStatus()!=null&&!"".equals(tEndUserInfoVO.getAuditStatus())){
            cr.andAuditStatusEqualTo(tEndUserInfoVO.getAuditStatus());
        }
        List<TEndUserInfoVO> tEndUserInfoList = tEndUserInfoMapper.selectByCarNumber(tEndUserInfoVO);
        String[] carNumber = new String[tEndUserInfoList.size()];
        for(int i =0;i<tEndUserInfoList.size();i++){
            TEndUserInfoVO vo = new TEndUserInfoVO();
            BeanUtils.copyProperties(tEndUserInfoList.get(i),vo);
            //车老板与车辆关系表
            TEndUserCarRelExample example2 = new TEndUserCarRelExample();
            TEndUserCarRelExample.Criteria cr2 = example2.createCriteria();
            cr2.andEnduserIdEqualTo(tEndUserInfoList.get(i).getId());
            List<TEndUserCarRel> tEndUserCarRelList = tEndUserCarRelMapper.selectByExample(example2);
            for(TEndUserCarRel uc:tEndUserCarRelList){
                //车辆信息表
                TEndCarInfo ci = tEndCarInfoMapper.selectByPrimaryKey(uc.getEndcarId());
                if(ci!=null){
                    carNumber[i] = ci.getVehicleNumber();
                }

            }
           // vo.setCarNumber();//车牌号 一辆或多辆
            vo.setCarCount(tEndUserCarRelList.size());//车数量
            list.add(vo);
        }*/
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),objectPage,objectPage.getTotal());
    }

    /**
     * 根据id查询车老板信息
     * @param id
     * @return
     */
    @Override
    public TEndUserInfoVO selectEndCarOwnerById(Integer id) {
        //车老板信息
        TEndUserInfo info = tEndUserInfoMapper.selectByPrimaryKey(id);
        TEndUserInfoVO vo = new TEndUserInfoVO();
        BeanUtils.copyProperties(info,vo);
        //车老板车辆信息
        TEndUserCarRel tEndUserCarRel = new TEndUserCarRel();
        tEndUserCarRel.setEnduserId(id);
        tEndUserCarRel.setUserCarRelationType("CLSYRCLB");
        List<TEndUserCarRelVo> list = tEndUserCarRelMapper.selectByEnduserId(tEndUserCarRel);
        vo.setTEndUserCarRelVoList(list);

        //根据id查询与account关系
        TEnduserAccountExample example = new TEnduserAccountExample();
        TEnduserAccountExample.Criteria cr = example.createCriteria();
        cr.andEnduserIdEqualTo(info.getId());
        //根据accountId查询银行卡
        List<TEnduserAccount>  tEnduserAccountList = tEnduserAccountMapper.selectByExample(example);
        if(tEnduserAccountList.size()>0){
            //银行卡列表
            TBankCardExample example2 = new TBankCardExample();
            TBankCardExample.Criteria c = example2.createCriteria();
            c.andAccountIdEqualTo(tEnduserAccountList.get(0).getAccountId());
            c.andEnableEqualTo(false);
            List<TBankCard> tBankCardList = tBankCardMapper.selectByExample(example2);
            vo.setTBankCardList(tBankCardList);

            List<TZtBankCardVo> tZtBankCardVoList = tZtBankCardMapper.selectBankCardInfoByAccountId(tEnduserAccountList.get(0).getAccountId());
            vo.setTZtBankCardVoList(tZtBankCardVoList);
        }
        return vo;
    }

    /**
     * 车老板信息修改
     * @param tEndUserInfoVO
     * @return
     */
    @Override
    @LcnTransaction
    @Transactional
    public ResultUtil updateEndCarOwner(TEndUserInfoVO tEndUserInfoVO) {

        //车老板信息
        TEndUserInfo eui = tEndUserInfoMapper.selectByPrimaryKey(tEndUserInfoVO.getId());
        eui.setRealName(tEndUserInfoVO.getRealName());
        eui.setIdcard(tEndUserInfoVO.getIdcard());
        eui.setIdcardValidUntil(tEndUserInfoVO.getIdcardValidUntil());
        eui.setIdcardPhoto1(tEndUserInfoVO.getIdcardPhoto1());
        eui.setIdcardPhoto2(tEndUserInfoVO.getIdcardPhoto2());
        if(!eui.getAuditStatus().equals(tEndUserInfoVO.getAuditStatus())){
            eui.setAuditTime(new Date());
        }
        eui.setAuditStatus(tEndUserInfoVO.getAuditStatus());
        eui.setAuditOpinion(tEndUserInfoVO.getAuditOpinion());
        eui.setPhone(tEndUserInfoVO.getPhone());
        eui.setUpdateTime(new Date());
        eui.setUpdateUser(CurrentUser.getUserNickname());

        int total = tEndUserInfoMapper.updateByPrimaryKeySelective(eui);
        TEnduserAccount tEnduserAccount = tEnduserAccountMapper.selectByEndUserId(eui.getId());
        tEnduserAccount.setPhone(tEndUserInfoVO.getPhone());
        tEnduserAccount.setRealName(tEndUserInfoVO.getRealName());
        tEnduserAccount.setIdcard(tEndUserInfoVO.getIdcard());
        tEnduserAccountMapper.updateByPrimaryKeySelective(tEnduserAccount);

        TAccount tAccount = tAccountMapper.selectByPrimaryKey(tEnduserAccount.getAccountId());
        tAccount.setAccountNo(tEndUserInfoVO.getPhone());
        tAccount.setNickname(tEndUserInfoVO.getRealName());
        tAccountMapper.updateByPrimaryKeySelective(tAccount);
        TSysUserVO user = new TSysUserVO();
        user.setId(tAccount.getUserId());
        user.setUsername(tAccount.getAccountNo());
        user.setAccountNo(tAccount.getAccountNo());
        user.setNickname(tAccount.getNickname());
        ResultUtil resultUtil = systemAPI.updateUserByAccountNo(user);
        String code = resultUtil.getCode();
        if (code.equals("error")){
            return ResultUtil.error(resultUtil.getMsg());
        }

        //车老板车辆信息
        List<TEndUserCarRelVo> list = tEndUserInfoVO.getTEndUserCarRelVoList();
        for(TEndUserCarRelVo tEndUserCarRelVo:list){
            if (null != tEndUserCarRelVo.getAuditStatus()){
                if (DictEnum.PASSNODE.code.equals(tEndUserCarRelVo.getAuditStatus())){
                    //判断当前车两是否已经有认证通过的车老板
                    List<EndCarDTO> tEndUserCarRels = tEndUserCarRelMapper.selectCarOwnerAuditStatus(tEndUserCarRelVo);
                    for (EndCarDTO rel : tEndUserCarRels){
                    /*if (!rel.getEnduserId().equals(tEndUserCarRelVo.getEnduserId())){
                        return ResultUtil.error("车辆" + rel.getVehicleNumber() + "已有认证的车老板");
                    }*/
                        TEndUserCarRel tEndUserCarRel = new TEndUserCarRel();
                        tEndUserCarRel.setAuditStatus("NOTPASSNODE");
                        tEndUserCarRel.setId(rel.getId());
                        tEndUserCarRelMapper.updateByPrimaryKeySelective(tEndUserCarRel);

                    }
                }
            }
            TEndUserCarRel tEndUserCarRel = tEndUserCarRelMapper.selectByPrimaryKey(tEndUserCarRelVo.getId());
            tEndUserCarRel.setCertificateDoc1(tEndUserCarRelVo.getCertificateDoc1());
            tEndUserCarRel.setCertificateDoc2(tEndUserCarRelVo.getCertificateDoc2());
            tEndUserCarRel.setCertificateType(tEndUserCarRelVo.getCertificateType());
            //tEndUserCarRel.setAuditStatus(tEndUserCarRelVo.getAuditStatus());//add dwb 2019/6/17 车辆状态
            if(!tEndUserCarRel.getAuditStatus().equals(tEndUserCarRelVo.getAuditStatus())){
                tEndUserCarRel.setAuditTime(new Date());
            }
            tEndUserCarRel.setAuditStatus(tEndUserCarRelVo.getAuditStatus());
            tEndUserCarRelMapper.updateByPrimaryKeySelective(tEndUserCarRel);
        }

        return ResultUtil.ok();
    }

    /**
     * 车老板信息新增
     * @param tEndUserInfoVO
     * @return
     */
    @Transactional // add by zhangjiji 2019/6/6
    @Override
    public int addEndCarOwner(TEndUserInfoVO tEndUserInfoVO) {
        //车老板信息
        TEndUserInfo eui = new TEndUserInfo();
        eui.setRealName(tEndUserInfoVO.getRealName());
        eui.setIdcard(tEndUserInfoVO.getIdcard());
        eui.setIdcardValidUntil(tEndUserInfoVO.getIdcardValidUntil());
        eui.setUserLogisticsRole("CTYPEBOSS");
        eui.setIdcardPhoto1(tEndUserInfoVO.getIdcardPhoto1());
        eui.setIdcardPhoto2(tEndUserInfoVO.getIdcardPhoto2());
        eui.setAuditStatus(tEndUserInfoVO.getAuditStatus());
        eui.setAuditOpinion(tEndUserInfoVO.getAuditOpinion());
        eui.setPhone(tEndUserInfoVO.getPhone());
        eui.setCreateTime(new Date());
        eui.setEnable(false);
        int total = tEndUserInfoMapper.insertSelective(eui);
       /* //车老板车辆信息
        List<TEndUserCarRelVo> list = tEndUserInfoVO.getTEndUserCarRelVoList();
        for(TEndUserCarRelVo tEndUserCarRelVo:list){
            //车辆信息表
            TEndCarInfo tEndCarInfo = new TEndCarInfo();
            tEndCarInfo.setVehicleNumber(tEndUserCarRelVo.getVehicleNumber());
            tEndCarInfo.setRoadTransportOperationLicenseCode(tEndUserCarRelVo.getRoadTransportOperationLicenseCode());
            tEndCarInfo.setRoadTransportOperationLicensePhoto1(tEndUserCarRelVo.getRoadTransportOperationLicensePhoto1());
            tEndCarInfo.setRoadTransportOperationLicensePhoto2(tEndUserCarRelVo.getRoadTransportOperationLicensePhoto2());
            tEndCarInfo.setDrivingLicencesPhoto1(tEndUserCarRelVo.getDrivingLicencesPhoto1());
            tEndCarInfo.setDrivingLicencesPhoto2(tEndUserCarRelVo.getDrivingLicencesPhoto2());
            tEndCarInfo.setEnable(false);
            tEndCarInfoMapper.insert(tEndCarInfo);

            //车老板与车关系表
            TEndUserCarRel tEndUserCarRel = new TEndUserCarRel();
            tEndUserCarRel.setEnduserId(eui.getId());
            tEndUserCarRel.setEndcarId(tEndCarInfo.getId());
            tEndUserCarRel.setDatafrom("COMPC");
            tEndUserCarRel.setDataConfirmTime(new Date());
            tEndUserCarRel.setCertificateDoc1(tEndUserCarRelVo.getCertificateDoc1());
            tEndUserCarRel.setCertificateDoc2(tEndUserCarRelVo.getCertificateDoc2());
            tEndUserCarRel.setEnable(false);
            tEndUserCarRelMapper.insertSelective(tEndUserCarRel);
        }*/
        return total;
    }

    /**
     * 车老板银行卡新增
     * @param tBankCardVo
     * @return
     */
    @Transactional
    @Override
    public ResultUtil addEndCarOwnerBank(TBankCardVo tBankCardVo) {
        ResultUtil resultUtil = new ResultUtil();
        IntegrationBankCard ibc = new IntegrationBankCard();
        BankCardReq bankCardReq = new BankCardReq();
        bankCardReq.setBankCard(tBankCardVo.getCardNo());
        bankCardReq.setName(tBankCardVo.getCardOwner());
        bankCardReq.setIdCard(tBankCardVo.getCardOwnerIdcard());

        SysParam sysParam = sysParamAPI.getParamByKey("IFKBIN");
        //是否开启 银行卡三要素校验 0否 1是
        if("1".equals(sysParam.getParamValue())){
            //银行卡三要素综合验证
            BankCardResp bankCardRespa = ibc.checkBankCard(bankCardReq);
            if ( StringUtils.isNotBlank(bankCardRespa.getResult())
                    && !BankCardResp.RESULT_UNANIMOUS_CODE.equals(bankCardRespa.getResult())) {
                resultUtil.setCode("error");
                resultUtil.setMsg(bankCardRespa.getDesc());
                resultUtil.setData(bankCardRespa);
                return  resultUtil;
            }
        }
        TBankCard tBankCard = new TBankCard();
        //根据id查询与account关系
        TEnduserAccountExample example = new TEnduserAccountExample();
        TEnduserAccountExample.Criteria cr = example.createCriteria();
        cr.andEnduserIdEqualTo(tBankCardVo.getEndUserId());
        cr.andEnableEqualTo(false);
        //根据accountId查询银行卡
        List<TEnduserAccount>  tEnduserAccountList = tEnduserAccountMapper.selectByExample(example);
        if(tEnduserAccountList.size()>0) {
            BeanUtils.copyProperties(tBankCardVo, tBankCard);
            tBankCard.setAccountId(tEnduserAccountList.get(0).getAccountId());
            tBankCard.setEnable(false);
            TBankCardExample example2 = new TBankCardExample();
            TBankCardExample.Criteria c2 = example2.createCriteria();
            c2.andAccountIdEqualTo(tBankCard.getAccountId());
            c2.andEnableEqualTo(false);
            if(tBankCardMapper.selectByExample(example2).size()<1){
                tBankCard.setIfDefault(1);
            }else{
                tBankCard.setIfDefault(0);
            }
            TBankCardExample example3 = new TBankCardExample();
            TBankCardExample.Criteria c3 = example3.createCriteria();
            c3.andCardNoEqualTo(tBankCard.getCardNo());
            c3.andAccountIdEqualTo(tBankCard.getAccountId());
            c3.andEnableEqualTo(false);
            if(tBankCardMapper.selectByExample(example3).size()>0){
                resultUtil.setMsg("银行卡已存在请不要重复添加！");
                resultUtil.setCode("error");
            }else{
                tBankCardMapper.insertSelective(tBankCard);
                resultUtil.setMsg("添加成功！");
                resultUtil.setData(tBankCard);
                resultUtil.setCode("success");
            }
        }else{
            resultUtil.setMsg("添加失败！");
            resultUtil.setCode("error");
        }
        return resultUtil;
    }

    @Override
    public TEndUserInfo selectByPrimaryKey(Integer id){
        return tEndUserInfoMapper.selectByPrimaryKey(id);
    }

    @Override
    public ResultUtil findByCardId(Integer id) {
        TBankCard tBankCard = tBankCardMapper.getBankCardListByCardId(id);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), tBankCard);
    }

    @Override
    @Transactional
    public int insertCard(TBankCard param) {
        return tBankCardMapper.insertSelective(param);
    }

    @Override
    @LcnTransaction
    @Transactional
    public ResultUtil addCard(PerfectCardInfoVO vo) {


        TAccount t =  tAccountService.selectByPrimaryKey(vo.getAccountId());

        TBankCardExample tBankCardExamplecr3 = new TBankCardExample();
        TBankCardExample.Criteria cr3 = tBankCardExamplecr3.createCriteria();
        cr3.andAccountIdEqualTo(vo.getAccountId());
        cr3.andCardOwnerEqualTo(t.getNickname());
        cr3.andEnableEqualTo(false);

        List<TBankCard> oneselfTBankCards = tBankCardMapper.selectByExample(tBankCardExamplecr3);

        if(oneselfTBankCards.size() >= 5){
            return ResultUtil.error("本人银行卡绑定不得超过五张！");
        }

//        TBankCardExample tBankCardExamplecr4 = new TBankCardExample();
//        TBankCardExample.Criteria cr4 = tBankCardExamplecr4.createCriteria();
//        cr4.andAccountIdEqualTo(vo.getAccountId());
//        cr4.andCardOwnerNotEqualTo(t.getNickname());
//        cr4.andEnableEqualTo(false);
//
//        List<TBankCard> notOneselfTBankCards = tBankCardMapper.selectByExample(tBankCardExamplecr4);
//
//        if(notOneselfTBankCards.size() >= 5){
//            return  ResultUtil.error("非本人银行卡绑定不得超过五张！");
//        }


        // 添加非本人银行卡
        if (!vo.getSelfCard()) {
            List<TEndUserInfo> tEndUserInfos = tEndUserInfoMapper.selectByIdcard(vo.getCardOwnerIdcard());
            if (null != tEndUserInfos && !tEndUserInfos.isEmpty()) {
                List<TEndUserInfo> list = tEndUserInfos
                        .stream()
                        .filter(info -> info.getUserLogisticsRole().contains(DictEnum.CTYPETRANSFEROR.code))
                        .collect(Collectors.toList());
                if (null != list && !list.isEmpty()) {
                    TEnduserAccountExample tEnduserAccountExample = new TEnduserAccountExample();
                    TEnduserAccountExample.Criteria c1 = tEnduserAccountExample.createCriteria();
                    c1.andEnduserIdEqualTo(list.get(0).getId());
                    c1.andEnableEqualTo(false);

                    List<TEnduserAccount>  tEnduserAccounts = tEnduserAccountMapper.selectByExample(tEnduserAccountExample);

                    TBankCardExample tBankCardExample = new TBankCardExample();
                    TBankCardExample.Criteria cr2 = tBankCardExample.createCriteria();
                    cr2.andAccountIdEqualTo(tEnduserAccounts.get(0).getAccountId());
                    cr2.andEnableEqualTo(false);

                    List<TBankCard> tBankCards = tBankCardMapper.selectByExample(tBankCardExample);
                    Integer alreadyBoundBankCardId = null;
                    Boolean bankCardState = true;
                    for (TBankCard tBankCard : tBankCards){
                        if(tBankCard.getCardNo().equals(vo.getCardNo())){
                            bankCardState= false;
                            alreadyBoundBankCardId = tBankCard.getId();
                        }
                    }
                    TEndUserOpenRoleExample tEndUserOpenRoleExample = new TEndUserOpenRoleExample();
                    TEndUserOpenRoleExample.Criteria cr1 = tEndUserOpenRoleExample.createCriteria();
                    cr1.andEndUserIdEqualTo(list.get(0).getId());
                    cr1.andOpenStatusEqualTo(JdEnum.OPENSUCCESS.code);
                    cr1.andEnableEqualTo(false);

                    List<TEndUserOpenRole> tEndUserOpenRoles = tEndUserOpenRoleMapper.selectByExample(tEndUserOpenRoleExample);

                    if(tEndUserOpenRoles.size() > 0){
                        if(bankCardState){
                            //银行卡同步到转账人名下
                            TBankCard tBankCard = new TBankCard();
                            tBankCard.setCardNo(vo.getCardNo());
                            tBankCard.setCardOwnerPhone(vo.getCardOwnerPhone());
                            tBankCard.setCardOwner(vo.getCardOwner());
                            tBankCard.setCardOwnerIdcard(vo.getCardOwnerIdcard());
                            tBankCard.setAccountId(tEnduserAccounts.get(0).getAccountId());
                            tBankCard.setCardOwnerIdcardPhoto1(vo.getIdcardPhoto1());
                            tBankCard.setCardOwnerIdcardPhoto2(vo.getIdcardPhoto2());
                            tBankCard.setIdcardValidBeginning(vo.getIdcardValidBeginning());
                            tBankCard.setIdcardValidUntil(vo.getIdcardValidUntil());
                            tBankCard.setAddress(vo.getAddress());
                            tBankCard.setCreateUser(CurrentUser.getUserNickname());
                            tBankCard.setCreateTime(new Date());
                            tBankCard.setEnable(true);
                            tBankCard.setIfDefault(0);
                            tBankCardMapper.insertSelective(tBankCard);

                            TJdBankCardVo tJdBankCardVo = new TJdBankCardVo();
                            tJdBankCardVo.setOpenRoleId(tEndUserOpenRoles.get(0).getId());
                            tJdBankCardVo.setPartnerMemberId(tEndUserOpenRoles.get(0).getPartnerMemberId());
                            tJdBankCardVo.setPartnerAccId(tEndUserOpenRoles.get(0).getPartnerAccId());
                            tJdBankCardVo.setBankCardId(tBankCard.getId());
                            tJdBankCardVo.setOperator(tEndUserOpenRoles.get(0).getOperator());
                            tJdBankCardVo.setAcctNo(vo.getCardNo());
                            tJdBankCardVo.setAcctName(vo.getCardOwner());
                            tJdBankCardVo.setOccBankPhone(vo.getCardOwnerPhone());
                            ResultUtil jdResultUtil = tjdBankCardService.openRoleUserBindingBank(tJdBankCardVo);

                            if(null == jdResultUtil || null == jdResultUtil.getCode() || !jdResultUtil.getCode().equals("success")){
                                return jdResultUtil;
                            }
                        }
                    }else {
                        List<TEndUserOpenRole> selectNoOneselfOpenRoleStateList = tEndUserOpenRoleMapper.selectNoOneselfOpenRoleStateList(vo.getCardOwnerIdcard());
                        if(selectNoOneselfOpenRoleStateList.size() > 0){
                              return ResultUtil.error("持卡人身份证已存在!");
                        }
//                        List<TEndUserOpenRole> selectOneselfOpenRoleStateList = tEndUserOpenRoleMapper.selectOneselfOpenRoleStateList(vo.getCardOwnerIdcard());
//                        if(selectOneselfOpenRoleStateList.size() > 0){
//                            return ResultUtil.error("持卡人身份证已存在!");
//                        }
                        List<TEndUserOpenRole> selectOpenRoleSuccessByIdcard = tEndUserOpenRoleMapper.selectOpenRoleSuccessByIdcard(vo.getCardOwnerIdcard());
                        if(selectOpenRoleSuccessByIdcard.size() > 0){
                            return ResultUtil.error("持卡人身份证已存在!!");
                        }

                        //银行卡同步到转账人名下
                        TBankCard tBankCard = new TBankCard();
                        if(bankCardState){
                            tBankCard.setCardNo(vo.getCardNo());
                            tBankCard.setCardOwnerPhone(vo.getCardOwnerPhone());
                            tBankCard.setCardOwner(vo.getCardOwner());
                            tBankCard.setCardOwnerIdcard(vo.getCardOwnerIdcard());
                            tBankCard.setAccountId(tEnduserAccounts.get(0).getAccountId());
                            tBankCard.setCardOwnerIdcardPhoto1(vo.getIdcardPhoto1());
                            tBankCard.setCardOwnerIdcardPhoto2(vo.getIdcardPhoto2());
                            tBankCard.setIdcardValidBeginning(vo.getIdcardValidBeginning());
                            tBankCard.setIdcardValidUntil(vo.getIdcardValidUntil());
                            tBankCard.setAddress(vo.getAddress());
                            tBankCard.setCreateUser(CurrentUser.getUserNickname());
                            tBankCard.setCreateTime(new Date());
                            tBankCard.setEnable(false);
                            tBankCard.setIfDefault(0);
                            tBankCardMapper.insertSelective(tBankCard);
                        }else {
                            tBankCard.setId(alreadyBoundBankCardId);
                        }

                        TEndUserOpenRoleVo tEndUserOpenRoleVo = tEndUserOpenRoleService.selectTransferorOpenRoleByEndUserId(tEndUserInfos.get(0).getId());
                        tEndUserOpenRoleVo.setBankCardId(tBankCard.getId());
                        tEndUserOpenRoleVo.setOpenRoleCardNo(vo.getCardNo());
                        tEndUserOpenRoleVo.setOpenRoleCardOwnerPhone(vo.getCardOwnerPhone());

                        if(tEndUserOpenRoleVo.getIdcardValidBeginning().getTime() > tEndUserOpenRoleVo.getIdcardValidUntil().getTime()){
                            Date idcardValidBeginning = tEndUserOpenRoleVo.getIdcardValidBeginning();
                            tEndUserOpenRoleVo.setIdcardValidBeginning(tEndUserOpenRoleVo.getIdcardValidUntil());
                            tEndUserOpenRoleVo.setIdcardValidUntil(idcardValidBeginning);
                        }
                        ResultUtil openRoleResultUtil = tEndUserOpenRoleService.selectDriveOpenRole(tEndUserOpenRoleVo);

                        if (null != openRoleResultUtil.getCode()){
                            if (openRoleResultUtil.getCode().equals("error")){
                                if (null != openRoleResultUtil.getMsg()){
                                    return ResultUtil.error("银行卡添加失败请重新操作!");
                                } else {
                                    return ResultUtil.error("银行卡添加失败请重新操作!");
                                }
                            }
                        }
                    }
                }
//                else {
//                    return ResultUtil.error("持卡人身份证已存在！");
//                }
            }else {
                // 查询手机号是否存在
//                List<TSysUser> tSysUserList = systemAPI.selectByPhone(vo.getCardOwnerPhone());
//                if (tSysUserList.size() > 0) {
//                    return ResultUtil.error("持卡人手机号已存在, 请更换手机号");
//                }
                List<TEndUserOpenRole> selectNoOneselfOpenRoleStateList = tEndUserOpenRoleMapper.selectNoOneselfOpenRoleStateList(vo.getCardOwnerIdcard());
                if(selectNoOneselfOpenRoleStateList.size() > 0){
                   List<TBankCard> noOneselfBankCardList = tBankCardMapper.noOneselfBankCardList(selectNoOneselfOpenRoleStateList.get(0).getId());
                   Boolean noOneselfBankCardState = true;
                   for(TBankCard tBankCard : noOneselfBankCardList){
                       if(tBankCard.getCardNo().equals(vo.getCardNo())){
                           noOneselfBankCardState = false;
                       }
                   }
                   if(noOneselfBankCardState){
                       TEnduserAccountExample tEnduserAccountExample = new TEnduserAccountExample();
                       TEnduserAccountExample.Criteria c1 = tEnduserAccountExample.createCriteria();
                       c1.andEnduserIdEqualTo(selectNoOneselfOpenRoleStateList.get(0).getEndUserId());
                       c1.andEnableEqualTo(false);

                       List<TEnduserAccount>  tEnduserAccounts = tEnduserAccountMapper.selectByExample(tEnduserAccountExample);
                       TBankCard tBankCard = new TBankCard();
                       tBankCard.setCardNo(vo.getCardNo());
                       tBankCard.setCardOwnerPhone(vo.getCardOwnerPhone());
                       tBankCard.setCardOwner(vo.getCardOwner());
                       tBankCard.setCardOwnerIdcard(vo.getCardOwnerIdcard());
                       tBankCard.setAccountId(tEnduserAccounts.get(0).getAccountId());
                       tBankCard.setCardOwnerIdcardPhoto1(vo.getIdcardPhoto1());
                       tBankCard.setCardOwnerIdcardPhoto2(vo.getIdcardPhoto2());
                       tBankCard.setIdcardValidBeginning(vo.getIdcardValidBeginning());
                       tBankCard.setIdcardValidUntil(vo.getIdcardValidUntil());
                       tBankCard.setAddress(vo.getAddress());
                       tBankCard.setCreateUser(CurrentUser.getUserNickname());
                       tBankCard.setCreateTime(new Date());
                       tBankCard.setEnable(true);
                       tBankCard.setIfDefault(0);
                       tBankCardMapper.insertSelective(tBankCard);

                       TJdBankCardVo tJdBankCardVo = new TJdBankCardVo();
                       tJdBankCardVo.setOpenRoleId(selectNoOneselfOpenRoleStateList.get(0).getId());
                       tJdBankCardVo.setPartnerMemberId(selectNoOneselfOpenRoleStateList.get(0).getPartnerMemberId());
                       tJdBankCardVo.setPartnerAccId(selectNoOneselfOpenRoleStateList.get(0).getPartnerAccId());
                       tJdBankCardVo.setBankCardId(tBankCard.getId());
                       tJdBankCardVo.setOperator(selectNoOneselfOpenRoleStateList.get(0).getOperator());
                       tJdBankCardVo.setAcctNo(vo.getCardNo());
                       tJdBankCardVo.setAcctName(vo.getCardOwner());
                       tJdBankCardVo.setOccBankPhone(vo.getCardOwnerPhone());
                       ResultUtil jdResultUtil = tjdBankCardService.openRoleUserBindingBank(tJdBankCardVo);

                       if(null == jdResultUtil || null == jdResultUtil.getCode() || !jdResultUtil.getCode().equals("success")){
                           return jdResultUtil;
                       }
                   }

                }else {
                    // 查询手机号是否存在
                    List<TSysUser> tSysUserList = systemAPI.selectByPhone(vo.getCardOwnerPhone());
                    if (tSysUserList.size() > 0) {
                        return ResultUtil.error("持卡人手机号已存在, 请更换手机号");
                    }
                    List<TEndUserOpenRole> selectOneselfOpenRoleStateList = tEndUserOpenRoleMapper.selectOneselfOpenRoleStateList(vo.getCardOwnerIdcard());
                    if(selectOneselfOpenRoleStateList.size() > 0){
                        throw new RuntimeException("持卡人身份证已存在");
                    }
                    // 添加非本人银行卡 -> 添加转账人
                     addTransferor(vo);
                }
            }
        }

        List<TBankCard> bankCardList = tBankCardMapper.getBankCardListByAccountId(vo.getAccountId());

        TBankCard tBankCard = new TBankCard();
        tBankCard.setCardNo(vo.getCardNo());
        tBankCard.setCardOwnerPhone(vo.getCardOwnerPhone());
        tBankCard.setCardOwner(vo.getCardOwner());
        tBankCard.setCardOwnerIdcard(vo.getCardOwnerIdcard());
        tBankCard.setAccountId(vo.getAccountId());
        tBankCard.setCardOwnerIdcardPhoto1(vo.getIdcardPhoto1());
        tBankCard.setCardOwnerIdcardPhoto2(vo.getIdcardPhoto2());
        tBankCard.setIdcardValidBeginning(vo.getIdcardValidBeginning());
        tBankCard.setIdcardValidUntil(vo.getIdcardValidUntil());
        tBankCard.setAddress(vo.getAddress());
        tBankCard.setCreateUser(CurrentUser.getUserNickname());
        tBankCard.setCreateTime(new Date());
        tBankCard.setEnable(true);
        if (bankCardList.size() == 0) {//第一张银行卡是默认的
            tBankCard.setIfDefault(1);
        } else if (bankCardList.size()!=0) {//不是第一次添加，判断选择的是不是默认的
            if (vo.getIfDefault() != null && vo.getIfDefault()) {
                tBankCardMapper.updateByAccountId(vo.getAccountId());
            }
            tBankCard.setIfDefault(vo.getIfDefault() ? Integer.valueOf(1) : Integer.valueOf(0));
        }
        tBankCardMapper.insertSelective(tBankCard);

        Integer endUserId = null;
        if(null != vo.getEndUserId()){
            endUserId = vo.getEndUserId();
        }else if(null !=  vo.getEnduserinfoId()) {
            endUserId = vo.getEnduserinfoId();
        }else{
            endUserId = CurrentUser.getEndUserId();
        }
        if(vo.getSelfCard() && null != endUserId){
            //进行京东绑卡
            //查询开户表数据

            TEndUserOpenRoleExample tEndUserOpenRoleExample = new TEndUserOpenRoleExample();
            TEndUserOpenRoleExample.Criteria cr1 = tEndUserOpenRoleExample.createCriteria();
            cr1.andEndUserIdEqualTo(endUserId);
            cr1.andOpenStatusEqualTo(JdEnum.OPENSUCCESS.code);
            cr1.andEnableEqualTo(false);

            List<TEndUserOpenRole> tEndUserOpenRoles = tEndUserOpenRoleMapper.selectByExample(tEndUserOpenRoleExample);

            if(tEndUserOpenRoles.size() > 0){
                TJdBankCardVo tJdBankCardVo = new TJdBankCardVo();
                tJdBankCardVo.setOpenRoleId(tEndUserOpenRoles.get(0).getId());
                tJdBankCardVo.setPartnerMemberId(tEndUserOpenRoles.get(0).getPartnerMemberId());
                tJdBankCardVo.setPartnerAccId(tEndUserOpenRoles.get(0).getPartnerAccId());
                tJdBankCardVo.setBankCardId(tBankCard.getId());
                tJdBankCardVo.setOperator(tEndUserOpenRoles.get(0).getOperator());
                tJdBankCardVo.setAcctNo(vo.getCardNo());
                tJdBankCardVo.setAcctName(vo.getCardOwner());
                tJdBankCardVo.setOccBankPhone(vo.getCardOwnerPhone());
                ResultUtil jdResultUtil = tjdBankCardService.openRoleUserBindingBank(tJdBankCardVo);

                if(null != jdResultUtil && null != jdResultUtil.getCode() && jdResultUtil.getCode().equals("success")){

                    //如果京东绑定成功那么就把银行卡设置为可用如果为绑定中那么就在回调中处理银行卡状态
                    if(null != jdResultUtil.getData() && String.valueOf(jdResultUtil.getData()).equals(JdEnum.BIND.code)){
                        TBankCard record = new TBankCard();
                        record.setEnable(false);
                        TBankCardExample tBankCardExample = new TBankCardExample();
                        TBankCardExample.Criteria cr2 = tBankCardExample.createCriteria();
                        cr2.andIdEqualTo(tBankCard.getId());

                        tBankCardMapper.updateByExampleSelective(record,tBankCardExample);

                        Map<String, Object> map = new HashMap<>();
                        map.put("bankName", tBankCard.getBankName());//姓名
                        map.put("id",tBankCard.getId());

                        return new ResultUtil(CodeEnum.SUCCESS.getCode(), map, "银行卡添加成功");
                    }else {
                        Map<String, Object> map = new HashMap<>();
                        map.put("bankName", tBankCard.getBankName());//姓名
                        map.put("id",tBankCard.getId());

                        return new ResultUtil(CodeEnum.SUCCESS.getCode(), map, "银行卡绑卡处理中!");
                    }
                }else {
                    return jdResultUtil;
                }
            }else {
                TBankCard record = new TBankCard();
                record.setEnable(false);
                TBankCardExample tBankCardExample = new TBankCardExample();
                TBankCardExample.Criteria cr2 = tBankCardExample.createCriteria();
                cr2.andIdEqualTo(tBankCard.getId());

                tBankCardMapper.updateByExampleSelective(record,tBankCardExample);

                Map<String, Object> map = new HashMap<>();
                map.put("bankName", tBankCard.getBankName());//姓名
                map.put("id",tBankCard.getId());

                return new ResultUtil(CodeEnum.SUCCESS.getCode(), map, "银行卡添加成功");
            }
        }else {
            TBankCard record = new TBankCard();
            record.setEnable(false);
            TBankCardExample tBankCardExample = new TBankCardExample();
            TBankCardExample.Criteria cr2 = tBankCardExample.createCriteria();
            cr2.andIdEqualTo(tBankCard.getId());

            tBankCardMapper.updateByExampleSelective(record,tBankCardExample);

            Map<String, Object> map = new HashMap<>();
            map.put("bankName", tBankCard.getBankName());//姓名
            map.put("id",tBankCard.getId());

            return new ResultUtil(CodeEnum.SUCCESS.getCode(), map, "银行卡添加成功");
        }
    }

    @Override
    @Transactional
    public ResultUtil addHxyhBankCard(TZtBankCardVo vo) {
        TZtAccountOpenInfoExample tZtAccountOpenInfoExample = new TZtAccountOpenInfoExample();
        TZtAccountOpenInfoExample.Criteria openCr = tZtAccountOpenInfoExample.createCriteria();
        openCr.andAccountIdEqualTo(vo.getAccountId());
        openCr.andUserOpenRoleEqualTo(DictEnum.CD.code);
        List<TZtAccountOpenInfo> tZtAccountOpenInfoList =tZtAccountOpenInfoMapper.selectByExample(tZtAccountOpenInfoExample);
        if(tZtAccountOpenInfoList.size()<1){
            return ResultUtil.error("华夏银行未开户不允许添加银行卡");
        }
        TZtAccountOpenInfo tZtAccountOpenInfo = tZtAccountOpenInfoList.get(0);
        if(tZtAccountOpenInfo.getStatus()!=1){
            return ResultUtil.error("未开户成功不允许添加银行卡");
        }
        List<TZtBankBindRelationship> tZtBankBindRelationshipList = tZtBankBindRelationshipMapper.selectByBankUserIdAndAccountOpenId(vo.getAccountId());
        if(tZtBankBindRelationshipList.size()==4){
            return ResultUtil.error("用户只可绑定四张银行卡");
        }
        Map<String,Object> map = AliPayCardDetailUtil.getCardDetail(vo.getAcctNo());
        //请求华夏银行添加银行卡
        OpenMemberBindAccountReq request = new OpenMemberBindAccountReq();
        request.setPartnerId(hxyhPartnerId);
        request.setRequestId(IdWorkerUtil.getInstance().nextId());
        request.setRequestTime(DateUtils.getRequestTime());
        request.setCurrency(DictEnum.CNY.code);
        request.setChannelId(hxyhChannelId);
        request.setPartnerAccId(tZtAccountOpenInfo.getPartnerAccId());
        request.setOperType(1);
        request.setOccBankAccount(vo.getAcctNo());
        request.setOccBankAccountName(vo.getAcctName());
        request.setOccBankPhone(vo.getOccBankPhone());
        request.setOccBankCode(String.valueOf(map.get("key")));
        request.setOccBankName(String.valueOf(map.get("value")));
        request.setCardType(10);
        request.setCardNo(vo.getAcctCard());
        request.setIsOther(2);
        request.setAccountSign(0);
        request.setIsSecondAcc(0);
        request.setStrideValidate(0);
        if(String.valueOf(map.get("key")).equals("HXBANK")){
            request.setIsOtherBank(0);
        }else{
            request.setIsOtherBank(1);
        }
        log.info("华夏银行司机绑卡入参：{}",request);
        ResultUtil resultUtil = cloudPaymentAPI.execute(request);
        log.info("华夏支付 - 华夏银行司机绑卡回参，{}", JSONUtil.toJsonStr(resultUtil));
        OpenMemberBindAccountResp response = CloudPayFormatUtil.ObjToBean(resultUtil, OpenMemberBindAccountResp.class);;
        log.info("华夏银行司机绑卡回参：{}",response);
        TZtBankBindRelationship tZtBankBindRelationship = new TZtBankBindRelationship();
        tZtBankBindRelationship.setAccountOpenId(tZtAccountOpenInfo.getId());
        tZtBankBindRelationship.setRequestId(request.getRequestId());
        tZtBankBindRelationship.setLinkAccountType(request.getLinkAccountType());
        if("00000".equals(response.getResponseCode())){
            List<TZtBankCard> bankCardList = tZtBankUserMapper.getTZtBankCardListByCardNo(null,vo.getAccountId(),null);
            TZtBankCardExample tZtBankCardExample = new TZtBankCardExample();
            TZtBankCardExample.Criteria cr = tZtBankCardExample.createCriteria();
            cr.andAcctNoEqualTo(vo.getAcctNo());
            cr.andEnableEqualTo(false);
            List<TZtBankCard> tZtBankCards = tZtBankCardMapper.selectByExample(tZtBankCardExample);
            TZtBankUser tZtBankUser = new TZtBankUser();
            Integer bankId = null;
            if(tZtBankCards.size()<1) {
                TZtBankCard tZtBankCard = new TZtBankCard();
                tZtBankCard.setAcctNo(vo.getAcctNo());
                tZtBankCard.setAcctName(vo.getAcctName());
                tZtBankCard.setAcctCard(vo.getAcctCard());
                tZtBankCard.setBankCode(String.valueOf(map.get("key")));
                tZtBankCard.setBankName(String.valueOf(map.get("value")));
                tZtBankCard.setOccBankPhone(vo.getOccBankPhone());
                tZtBankCard.setCreateUser(CurrentUser.getUserNickname());
                tZtBankCard.setCreateTime(new Date());
                tZtBankCard.setEnable(false);
                if (bankCardList.size() == 0) {//第一张银行卡是默认的
                    tZtBankUser.setIsDefault(true);
                } else if (bankCardList.size() != 0) {//不是第一次添加，判断选择的是不是默认的
                    if (vo.getIsDefault() != null && vo.getIsDefault()) {
                        tZtBankUserMapper.updateByAccountId(vo.getAccountId());
                    }

                }
                tZtBankCardMapper.insertSelective(tZtBankCard);
                bankId = tZtBankCard.getId();
            }else{
                TZtBankCard  tZtBankCard= tZtBankCards.get(0);
                bankId = tZtBankCard.getId();
                tZtBankCard.setOccBankPhone(vo.getOccBankPhone());
                tZtBankCardMapper.updateByPrimaryKeySelective(tZtBankCard);
            }
            tZtBankUser.setIsDefault(vo.getIsDefault() ? true : false);
            tZtBankUser.setIsOneself(vo.getIsOneself());
            tZtBankUser.setAccountId(vo.getAccountId());
            tZtBankUser.setBankId(bankId);
            tZtBankUser.setCreateUser(CurrentUser.getUserNickname());
            tZtBankUser.setCreateTime(new Date());
            tZtBankUser.setEnable(false);
            tZtBankUserMapper.insertSelective(tZtBankUser);
            tZtBankBindRelationship.setAccountBankId(tZtBankUser.getId());
            tZtBankBindRelationship.setBindStatus(JdEnum.BINDING.code);
            tZtBankBindRelationship.setRequestMessage("绑卡处理中");
            tZtBankBindRelationship.setRequestCode("0");
            tZtBankBindRelationshipMapper.insertSelective(tZtBankBindRelationship);
            Map<String, Object> map2 = new HashMap<>();
            map2.put("bankName", vo.getAcctName());//姓名
            map2.put("id",bankId);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), map2, "银行卡绑卡处理中");
        }else{
            tZtBankBindRelationship.setBindStatus(JdEnum.BIND_FAIL.code);
            tZtBankBindRelationship.setRequestCode("2");
            tZtBankBindRelationship.setRequestMessage("绑卡失败:"+response.getResponseDesc());
            tZtBankBindRelationshipMapper.insertSelective(tZtBankBindRelationship);
            Map<String, Object> map2 = new HashMap<>();
            map2.put("bankName", vo.getAcctName());//姓名
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), map2, "银行卡添加失败:"+response.getResponseDesc());
        }

    }


    @Override
    public ResultUtil addOpenRoleCard(TBankCard data) {

        List<TBankCard> bankCardList = tBankCardMapper.getBankCardListByAccountId(data.getAccountId());

        TBankCard tBankCard = new TBankCard();
        tBankCard.setCardNo(data.getCardNo());
        tBankCard.setCardOwnerPhone(data.getCardOwnerPhone());
        tBankCard.setCardOwner(data.getCardOwner());
        tBankCard.setCardOwnerIdcard(data.getCardOwnerIdcard());
        tBankCard.setAccountId(data.getAccountId());
        tBankCard.setCreateUser(CurrentUser.getUserNickname());
        tBankCard.setCreateTime(new Date());
        tBankCard.setEnable(true);
        if (bankCardList.size() == 0) {//第一张银行卡是默认的
            tBankCard.setIfDefault(1);
        }
        tBankCardMapper.insertSelective(tBankCard);

        //进行京东绑卡
        //查询开户表数据
        Integer endUserId = CurrentUser.getEndUserId();

        TEndUserOpenRoleExample tEndUserOpenRoleExample = new TEndUserOpenRoleExample();
        TEndUserOpenRoleExample.Criteria cr1 = tEndUserOpenRoleExample.createCriteria();
        cr1.andEndUserIdEqualTo(endUserId);
        cr1.andOpenStatusEqualTo(JdEnum.OPENSUCCESS.code);
        cr1.andEnableEqualTo(false);

        List<TEndUserOpenRole> tEndUserOpenRoles = tEndUserOpenRoleMapper.selectByExample(tEndUserOpenRoleExample);

        if(tEndUserOpenRoles.size() > 0){
            TJdBankCardVo tJdBankCardVo = new TJdBankCardVo();
            tJdBankCardVo.setOpenRoleId(tEndUserOpenRoles.get(0).getId());
            tJdBankCardVo.setPartnerMemberId(tEndUserOpenRoles.get(0).getPartnerMemberId());
            tJdBankCardVo.setPartnerAccId(tEndUserOpenRoles.get(0).getPartnerAccId());
            tJdBankCardVo.setBankCardId(tBankCard.getId());
            tJdBankCardVo.setOperator(tEndUserOpenRoles.get(0).getOperator());
            tJdBankCardVo.setAcctNo(data.getCardNo());
            tJdBankCardVo.setAcctName(data.getCardOwner());
            tJdBankCardVo.setOccBankPhone(data.getCardOwnerPhone());
            ResultUtil jdResultUtil = tjdBankCardService.openRoleUserBindingBank(tJdBankCardVo);

            if(null != jdResultUtil && null != jdResultUtil.getCode() && jdResultUtil.getCode().equals("success")){

                //如果京东绑定成功那么就把银行卡设置为可用如果为绑定中那么就在回调中处理银行卡状态
                if(null != jdResultUtil.getData() && String.valueOf(jdResultUtil.getData()).equals(JdEnum.BIND.code)){
                    TBankCard record = new TBankCard();
                    record.setEnable(false);
                    TBankCardExample tBankCardExample = new TBankCardExample();
                    TBankCardExample.Criteria cr2 = tBankCardExample.createCriteria();
                    cr2.andIdEqualTo(tBankCard.getId());

                    tBankCardMapper.updateByExampleSelective(record,tBankCardExample);

                    Map<String, Object> map = new HashMap<>();
                    map.put("bankName", tBankCard.getBankName());//姓名
                    map.put("id",tBankCard.getId());

                    return new ResultUtil(CodeEnum.SUCCESS.getCode(), map, "银行卡添加成功");
                }else {
                    Map<String, Object> map = new HashMap<>();
                    map.put("bankName", tBankCard.getBankName());//姓名
                    map.put("id",tBankCard.getId());

                    return new ResultUtil(CodeEnum.SUCCESS.getCode(), map, "银行卡绑卡处理中!");
                }
            }else {
                return jdResultUtil;
            }
        }else {
            TBankCard record = new TBankCard();
            record.setEnable(false);
            TBankCardExample tBankCardExample = new TBankCardExample();
            TBankCardExample.Criteria cr2 = tBankCardExample.createCriteria();
            cr2.andIdEqualTo(tBankCard.getId());

            tBankCardMapper.updateByExampleSelective(record,tBankCardExample);

            Map<String, Object> map = new HashMap<>();
            map.put("bankName", tBankCard.getBankName());//姓名
            map.put("id",tBankCard.getId());

            return new ResultUtil(CodeEnum.SUCCESS.getCode(), map, "银行卡添加成功");
        }
        //return ResultUtil.ok("银行卡添加成功!");
    }

    @Override
    public ResultUtil selectDriverByPage(DriverListPageVO param) {
        Page<Object> page = PageHelper.startPage(param.getPage(),param.getSize());
        List<TDriverDto> derverInfoVOS = tEndUserInfoMapper.selectDriverByPage(param);
        for(TDriverDto dto : derverInfoVOS){
            if(dto.getTotalWithdrawal()==null){
                dto.setTotalWithdrawal(BigDecimal.valueOf(0.00D));
            }
            if(dto.getAccountBalance()==null){
                dto.setAccountBalance(BigDecimal.valueOf(0.00D));
            }
            if(dto.getWithdrawAmount()==null){
                dto.setWithdrawAmount(BigDecimal.valueOf(0.00D));
            }
        }
        /*List<TDriverDto> ljtxList = tEndUserInfoMapper.selectDriverByPageLjtx(param);
        for(TDriverDto map : DerverInfoVOS){
            for(TDriverDto map2:ljtxList){
                if(map.getId().equals(map2.getId())){
                    map.setTotalWithdrawal(map2.getTotalWithdrawal());
                }
            }
        }*/
        ResultUtil resultUtil = ResultUtil.ok(derverInfoVOS);
        resultUtil.setCount(page.getTotal());
        return resultUtil;
    }

    @Override
    public Map<String,Object>  selectDriverInfo(Integer enduserId){
        Map<String,Object> map = new HashMap<>();
        map.put("sum",tJdWalletMapper.selectDriverSum(enduserId));
        map.put("ws",tEndUserInfoMapper.selectDriverInfo(enduserId));
        map.put("jd",tJdWalletMapper.selectDriverInfo(enduserId));
        map.put("zt",tZtWalletMapper.selectDriverInfo(enduserId));
        return map;
    }

    @Override
    public ResultUtil selectEnduserForScanSource(TEndUserInfoSearchVO record) {
        TEndUserInfo endUserInfo = tEndUserInfoMapper.selectByPrimaryKey(record.getEnduserId());
        TNetSignOpenAccount tNetSignOpenAccount = netSignOpenAccountMapper.selectByUserId(record.getEnduserId(), DictEnum.CD.code);

        //司机绑定的车辆
        List<EndCarDTO> cars = tEndUserCarRelMapper.selectCarByEnduser(record.getEnduserId());
        for (EndCarDTO car : cars) {
            TCarInsuranceVO vo = new TCarInsuranceVO();
            if(null != car.getVehicleNumber()){
                vo.setVehicleNumber(car.getVehicleNumber());
                TCarInsuranceVO tCarInsuranceVO = carInsuranceAPI.selectByVehicleNumber(vo);
                if(null != tCarInsuranceVO && null != tCarInsuranceVO.getAuditStatus()){
                    car.setCarInsuranceStatus(tCarInsuranceVO.getAuditStatus());
                }
            }
        }
        //经纪人
        List<EndUserDTO> manager = tEndUserInfoMapper.selectManageByCompanyId(record.getCompanyId());
        HashMap<String, Object> map = new HashMap<>();
        map.put("enduser", endUserInfo);
        map.put("cars", cars);
        map.put("manager", manager);

        if (null != tNetSignOpenAccount) {
            if (tNetSignOpenAccount.getAuthStatus() == 0) {
                // 验证码未验证
                map.put("endDriverSignStatus", "3");
                map.put("endDriverSignSerialNo", tNetSignOpenAccount.getSerialNo());
            } else if (tNetSignOpenAccount.getAuthStatus() == 1 && (null == tNetSignOpenAccount.getSealNo() || StringUtils.isBlank(tNetSignOpenAccount.getSealNo()))) {
                // 实名认证，但未注册
                map.put("endDriverSignStatus", "4");
            } else if (null != tNetSignOpenAccount.getSealNo() && null != tNetSignOpenAccount.getSealImage() && StringUtils.isNotBlank(tNetSignOpenAccount.getSealImage())) {
                map.put("endDriverSealImage", tNetSignOpenAccount.getSealImage());
                map.put("endDriverSignStatus", "0");
            } else {
                map.put("endDriverSignStatus", "2");
            }
            map.put("endDriverSignStatusStr", "已注册");
            map.put("signUserId", tNetSignOpenAccount.getSealNo());
        } else {
            map.put("endDriverSignStatusStr", "未注册");
            map.put("endDriverSignStatus", "1");
        }

        TEndUserAuditInfo tEndUserAuditInfo = endUserAuditInfoMapper.selectByEndUserId(record.getEnduserId());
        if (null != tEndUserAuditInfo && null != tEndUserAuditInfo.getIdcardStatus()) {
            map.put("idcardStatus", tEndUserAuditInfo.getIdcardStatus());
        } else {
            map.put("idcardStatus", endUserInfo.getAuditStatus());
        }

        return ResultUtil.ok(map);
    }

    /**
    * @Description 发单查询车辆司机状态
    * <AUTHOR>
    * @Date   2019/6/2 10:50
    * @param record
    * @Return
    * @Exception
    *
    */
    @Override
    public ResultUtil selectEnduserCarStatus(TEnduserCarStatus record) {
        if (null != record && null != record.getEnduserCarStatuses() && !record.getEnduserCarStatuses().isEmpty()){
            List<EnduserCarStatus> enduserCarStatuses = record.getEnduserCarStatuses();
            for (EnduserCarStatus enduserCarStatus: enduserCarStatuses){
                EnduserCarStatus endCarStatus = endCarStatusMapper.selectCarStatus(enduserCarStatus.getEndcarId());
                EnduserCarStatus enduserStatus = endUserStatusMapper.selectUserStatus(enduserCarStatus.getEnduserId());
                enduserCarStatus.setEndcarUseable(endCarStatus.getEndcarUseable());
                enduserCarStatus.setEnduserUseable(enduserStatus.getEnduserUseable());
                enduserCarStatus.setEndcarStatus(endCarStatus.getEndcarStatus());
                enduserCarStatus.setRealName(endCarStatus.getRealName());
                enduserCarStatus.setCurrentDriver(enduserStatus.getCurrentDriver());
                enduserCarStatus.setEndcarStatusCode(endCarStatus.getEndcarStatusCode());
                enduserCarStatus.setEnduserStatusCode(enduserStatus.getEnduserStatusCode());
                enduserCarStatus.setCurrentReceiptsNo(endCarStatus.getCurrentReceiptsNo());
                enduserCarStatus.setUserCurrentReceiptsNo(enduserStatus.getUserCurrentReceiptsNo());
                enduserCarStatus.setVehicleNumber(endCarStatus.getVehicleNumber());
                enduserCarStatus.setUserVehicleNumber(enduserStatus.getUserVehicleNumber());
                enduserCarStatus.setEnduserStatus(enduserStatus.getEnduserStatus());
                enduserCarStatus.setCarAuditStatus(endCarStatus.getCarAuditStatus());
                enduserCarStatus.setUserAuditStatus(enduserStatus.getUserAuditStatus());
                // 判断司机身份证是否完善
                TEndUserInfo endUserInfo = tEndUserInfoMapper.selectByPrimaryKey(enduserCarStatus.getEnduserId());
                if (null == endUserInfo.getIdcard() || null == endUserInfo.getIdcardPhoto2() || null == endUserInfo.getIdcardPhoto1()
                    || StringUtils.isBlank(endUserInfo.getIdcard()) || StringUtils.isBlank(endUserInfo.getIdcardPhoto1()) || StringUtils.isBlank(endUserInfo.getIdcardPhoto2())) {
                    log.info("司机身份证信息未完善");
                    enduserCarStatus.setIdCardPerfect(false);
                    enduserCarStatus.setDriverName(endUserInfo.getRealName());
                } else {
                    enduserCarStatus.setIdCardPerfect(true);
                }
            }
        }
        return ResultUtil.ok(record);
    }


    @Override
    public List<TEndUserInfo> findIdCard(String idcard) {
        return tEndUserInfoMapper.findIdCard(idcard);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2019/6/24 13:44
     *  @Description: 查询车老板列表
     */
    @Override
    public ResultUtil findCarOwnerList() {
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        List<CarOwnerSelectDTO> allCarOwner = tEndUserInfoMapper.getAllCarOwner(userCompanyId);
        return ResultUtil.ok(allCarOwner);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/1/5 10:49
     *  @Description: 查询车队长列表
     */
    @Override
    public ResultUtil findCaptainList() {
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        List<CarOwnerSelectDTO> allCarOwner = tEndUserInfoMapper.getAllCaptain(userCompanyId);
        return ResultUtil.ok(allCarOwner);
    }
    /**
     * @Description 车主身份回显
     * <AUTHOR>
     * @Date   2019/7/9 14:16
     * @Param
     * @Return
     * @Exception
     *
     */
    @Override
    public ResultUtil selectCarOwnerShip(TEndUserInfoVO vo){
        TEndUserCarRelExample example = new TEndUserCarRelExample();
        TEndUserCarRelExample.Criteria cr = example.createCriteria();
        cr.andEnduserIdEqualTo(vo.getId());
        cr.andUserCarRelationTypeEqualTo("CLSYRCLB");
        cr.andEnableEqualTo(false);
        cr.andDataConcelFromIsNull();
        List<TEndUserCarRel> tEndUserCarRelListOld = tEndUserCarRelMapper.selectByExample(example);
        if(tEndUserCarRelListOld.size()>0){
            return ResultUtil.error("当前司机已添加车主身份，请勿重复添加！");
        }
        TEndUserInfo  tEndUserInfo= tEndUserInfoMapper.selectByPrimaryKey(vo.getId());
        TEndUserInfoVO tEndUserInfoVO = new TEndUserInfoVO();
        BeanUtils.copyProperties(tEndUserInfo,tEndUserInfoVO);
        tEndUserInfoVO.setTEndCarInfoList(tEndCarInfoMapper.selectByEndUserId(vo.getId()));
        return ResultUtil.ok(tEndUserInfoVO);
    }


    /**
     * @Description 司机身份回显
     * <AUTHOR>
     * @Date   2019/7/9 14:16
     * @Param
     * @Return
     * @Exception
     *
     */
    @Override
    public ResultUtil selectDriverIdentity(TEndUserInfoVO vo){

        TEndUserCarRelExample example = new TEndUserCarRelExample();
        TEndUserCarRelExample.Criteria cr = example.createCriteria();
        cr.andEnduserIdEqualTo(vo.getId());
        cr.andUserCarRelationTypeEqualTo("CLSYRSJ");
        cr.andEnableEqualTo(false);
        List<TEndUserCarRel> tEndUserCarRelListOld = tEndUserCarRelMapper.selectByExample(example);
        if(tEndUserCarRelListOld.size()>0){
            return ResultUtil.error("当前车主已添加司机身份，请勿重复添加！");
        }
        return ResultUtil.ok(tEndUserInfoMapper.selectByPrimaryKey(vo.getId()));
    }



    /**
     * @Description 添加车主身份
     * <AUTHOR>
     * @Date   2019/7/9 14:16
     * @Param
     * @Return
     * @Exception
     *
     */
    @Override
    @Transactional
    public ResultUtil saveCarOwnerShip(TEndUserInfoVO vo) {
        TEndUserInfo  tEndUserInfo = tEndUserInfoMapper.selectByPrimaryKey(vo.getId());
        String enduserKey = "";
        boolean orderLock = false;
        try{
            enduserKey = "carBoss" + tEndUserInfo.getId()+tEndUserInfo.getIdcard();
            if (!redisUtil.hasKey(enduserKey)) {
                orderLock = redisUtil.set(enduserKey, "lock");
            }else{
                return ResultUtil.error("添加车主身份正在执行请稍后！");
            }
            if (orderLock && !tEndUserInfo.getUserLogisticsRole().contains("CTYPEBOSS")) {
                if(vo.getCarEndList().size()>0){
                    for(TEndUserInfoDto tEndUserInfoDto:vo.getCarEndList()){
                        TEndUserCarRelExample example2 = new TEndUserCarRelExample();
                        TEndUserCarRelExample.Criteria cr2 = example2.createCriteria();
                        cr2.andEnduserIdEqualTo(vo.getId());
                        cr2.andEndcarIdEqualTo(tEndUserInfoDto.getEndcarId());
                        cr2.andEnableEqualTo(false);
                        List<TEndUserCarRel> tEndUserCarRelList = tEndUserCarRelMapper.selectByExample(example2);
                        for(TEndUserCarRel rel :tEndUserCarRelList){
                            rel.setAuditStatus("NOTPASSNODE");
                            rel.setUserCarRelationType("CLSYRCLB");//车辆所有人（车老板）
                            rel.setCertificateDoc1(tEndUserInfoDto.getCertificateDoc1());//车主证明1
                            rel.setCertificateDoc2(tEndUserInfoDto.getCertificateDoc2());//车主证明2
                            rel.setDatafrom("PC");
                            rel.setEnable(false);
                            TEndUserCarRelExample example3 = new TEndUserCarRelExample();
                            TEndUserCarRelExample.Criteria cr3 = example3.createCriteria();
                            cr3.andEnduserIdEqualTo(vo.getId());
                            cr3.andEndcarIdEqualTo(tEndUserInfoDto.getEndcarId());
                            cr3.andEnableEqualTo(false);
                            cr3.andUserCarRelationTypeEqualTo("CLSYRCLB");
                            List<TEndUserCarRel> list = tEndUserCarRelMapper.selectByExample(example3);
                            if(list.size()<1){
                                tEndUserCarRelMapper.insertSelective(rel);
                            }
                        }
               /* TEndCarInfo tEndCarInfo = tEndCarInfoMapper.selectByPrimaryKey(tEndUserInfoVO.getEndcarId());
                tEndCarInfo.setDrivingLicencesPhoto1(tEndUserInfoVO.getDrivingLicencesPhotoXsz1());//行驶证
                tEndCarInfo.setDrivingLicencesPhoto2(tEndUserInfoVO.getDrivingLicencesPhotoXsz2());
                tEndCarInfo.setRoadTransportOperationLicensePhoto1(tEndUserInfoVO.getRoadTransportOperationLicensePhoto1());//道路运输许可证
                tEndCarInfo.setRoadTransportOperationLicensePhoto2(tEndUserInfoVO.getRoadTransportOperationLicensePhoto2());*/
                    }
                }else{
                    return ResultUtil.error("请选择车辆并上传车主证明文件！");
                }
                tEndUserInfo.setIdcardPhoto2(vo.getIdcardPhoto2());//身份证
                tEndUserInfo.setIdcardPhoto1(vo.getIdcardPhoto1());
                tEndUserInfo.setUserLogisticsRole(tEndUserInfo.getUserLogisticsRole()+","+"CTYPEBOSS");
                tEndUserInfoMapper.updateByPrimaryKeySelective(tEndUserInfo);
            }else{
                return ResultUtil.error("添加车主身份正在执行请稍后！");
            }
            return ResultUtil.ok();
        }catch(Exception e){
            log.error("添加车主身份失败",e);
            throw new RuntimeException("添加车主身份失败！");
        }finally {
            if (orderLock && redisUtil.hasKey(enduserKey)) {
                redisUtil.del(enduserKey);
            }
        }
    }

    /**
     * @Description 添加司机身份
     * <AUTHOR>
     * @Date   2019/7/9 14:16
     * @Param
     * @Return
     * @Exception
     *
     */
    @Override
    public ResultUtil saveDriverIdentity(TEndUserInfoVO vo) {

        TEndUserCarRelExample example2 = new TEndUserCarRelExample();
        TEndUserCarRelExample.Criteria cr2 = example2.createCriteria();
        cr2.andEnduserIdEqualTo(vo.getId());
        cr2.andEnableEqualTo(false);
        List<TEndUserCarRel> tEndUserCarRelList = tEndUserCarRelMapper.selectByExample(example2);
        if(tEndUserCarRelList.size()<1){
            return ResultUtil.error("当前车主无绑定车辆，不可添加司机身份！");
        }
        for(TEndUserCarRel rel :tEndUserCarRelList){
            rel.setUserCarRelationType("CLSYRSJ");//车辆使用人（司机）
            tEndUserCarRelMapper.insertSelective(rel);
        }
        TEndUserInfo  tEndUserInfo = tEndUserInfoMapper.selectByPrimaryKey(vo.getId());
        tEndUserInfo.setUserLogisticsRole(tEndUserInfo.getUserLogisticsRole()+","+"CTYPEDRVIVER");
        tEndUserInfo.setCertificatePhoto1(vo.getRoadTransportOperationLicensePhoto1());//资格证照片
        tEndUserInfo.setCertificatePhoto2(vo.getRoadTransportOperationLicensePhoto2());
        tEndUserInfo.setDrivingLicencesPhoto1(vo.getDrivingLicencesPhotoXsz1());//驾驶证
        tEndUserInfo.setDrivingLicencesPhoto2(vo.getDrivingLicencesPhotoXsz2());
        tEndUserInfo.setIdcardPhoto1(vo.getIdcardPhoto1());//身份证
        tEndUserInfo.setIdcardPhoto2(vo.getIdcardPhoto2());
        tEndUserInfoMapper.updateByPrimaryKeySelective(tEndUserInfo);
        return ResultUtil.ok();
    }

    @Override
    public List<TEndUserInfo> findphone(String phone) {
        return tEndUserInfoMapper.findphone(phone);
    }

    /**
    * @Description 司机扫码查询司机状态
    * <AUTHOR>
    * @Date   2019/6/13 18:00
    * @Param
    * @Return
    * @Exception
    *
    */
    @Override
    public ResultUtil selectEnduserStatusForScan(TEndUserInfo record) {
        try {
            ResultUtil resultUtil = ResultUtil.ok();
            //司机状态
            EnduserCarStatus enduserCarStatus = endUserStatusMapper.selectUserStatus(record.getId());
            if (null != enduserCarStatus && null != enduserCarStatus.getEnduserStatus()){
                if (null != enduserCarStatus.getEnduserUseable()){
                    HashMap<String, String> msg = new HashMap<>();
                    if (!enduserCarStatus.getEnduserUseable()){
                        TOrderInfoVO orderInfo = new TOrderInfoVO();
                        orderInfo.setOrderBusinessCode(enduserCarStatus.getCurrentReceiptsNo());
                        ResultUtil orderResult = orderInfoAPI.selectOrderInfoByOrderCode(orderInfo);
                        if (null != orderResult.getCode() && orderResult.getCode().equals("success")){
                            LinkedHashMap orderResultData = (LinkedHashMap) orderResult.getData();
                            String code = String.valueOf(orderResultData.get("code"));
                            msg.put("code", code);
                            msg.put("orderBussinessCode", enduserCarStatus.getCurrentReceiptsNo().substring(enduserCarStatus.getCurrentReceiptsNo().length() - 7));
                        }
                    }
                    if(!enduserCarStatus.getEnduserUseable()){
                        resultUtil.setCode("error");
                        if (enduserCarStatus.getEnduserStatusCode().equals(DictEnum.NOTRECIEVEORDER.code)
                                || enduserCarStatus.getEnduserStatusCode().equals(DictEnum.NOTCHECKED.code)){
                            msg.put("status", enduserCarStatus.getEnduserStatusCode());
                        } else {
                            resultUtil.setMsg(enduserCarStatus.getEnduserStatus());
                        }
                    }
                    resultUtil.setData(msg);
                    return resultUtil;
                }
            }
            return resultUtil;
        } catch (Exception e) {
            log.error("扫码抢单查询司机车辆信息失败, {}", ThrowableUtil.getStackTrace(e));
            return ResultUtil.error("查询司机车辆信息失败");
        }
    }

    /**
     * @Description 根据承运方id和enduserid查询钱包id
     * <AUTHOR>
     * @Date   2019/7/10 17:28
     * @Param
     * @Return
     * @Exception
     *
     */
    @Override
    public ResultUtil selectByWalltId(Integer endUserId, Integer carrierId, String purseCategory) {
        return ResultUtil.ok(tEndUserInfoMapper.selectByWalltId(endUserId, carrierId, purseCategory));
    }


    /**
     * @Description 根据enduserId获取到默认银行卡
     * <AUTHOR>
     * @Date   2019/7/11 10:36
     * @Param
     * @Return
     * @Exception
     *
     */
    @Override
    public ResultUtil selectByEndUserIdAndBankInfo(Integer endUserId) {

        return ResultUtil.ok(tEndUserInfoMapper.selectByEndUserIdAndBankInfo(endUserId));
    }

    @Override
    public TEndUserInfo selectById(Integer id) {
        TEndUserInfo endUserInfo = tEndUserInfoMapper.selectByPrimaryKey(id);
        return endUserInfo;
    }

    @Override
    public List<TEndUserInfo> selectByEndUserIdListFeign(String endUserIdList) {
       List<Integer> endIdList =  JSONObject.parseArray(endUserIdList,Integer.class);
        return tEndUserInfoMapper.selectByEndUserIdList(endIdList);
    }

    @Override
    public ResultUtil carOwnerAudit(TEndUserCarRelVo record) {
        List<TEndUserInfoVO> tEndUserCarRels = tEndUserCarRelMapper.selectCarOwnerList(record);
        List<TEndUserInfoVO> tEndUserCarRelList = new ArrayList<TEndUserInfoVO>();
        for(TEndUserInfoVO vo:tEndUserCarRels){
            if("SYQ".equals(vo.getCertificateType())){
                vo.setCertificateType("受益权");
            }else if("OWNERSHIP".equals(vo.getCertificateType())){
                vo.setCertificateType("所有权");
            }
            if(!vo.getId().equals(record.getEnduserId())){
                tEndUserCarRelList.add(vo);
            }
        }
        return ResultUtil.ok(tEndUserCarRelList);
    }

    @LcnTransaction(propagation = DTXPropagation.SUPPORTS)
    @Transactional
    @Override
    public ResultUtil updateEnduserInfo(TEndUserInfoVO record) {
        TEnduserAccount tEnduserAccount = tEnduserAccountMapper.selectEnduserAccount(record.getAccountId());
        if (null != tEnduserAccount && null != tEnduserAccount.getEnduserId()){
            record.setId(tEnduserAccount.getEnduserId());
            tEndUserInfoMapper.updateEnduserInfoForFeign(record);
        }
        return null;
    }

    @Override
    public List<String> judgeEndUserByPhone(String phone) {
        return tEndUserInfoMapper.judgePhoneExistence(phone);
    }

    @Override
    public ResultUtil export(TEndUserInfoSearchVO params) {
        params.setUserLogisticsRole("CTYPEDRVIVER");
        List<TEndUserInfoVO> list = tEndUserInfoMapper.selectByPage(params);
        for(TEndUserInfoVO vo:list){
            if(vo.getCreateTime()!=null&&!"".equals(vo.getCreateTime())){
                vo.setCreateTimeStr(DateUtils.formatDateTime(vo.getCreateTime()));
            }
        }
        Map<String,Object> map = new HashMap<>();
        String[] headers ={ "司机姓名","身份证号","注册时间","手机号","认证状态","审核意见","绑定的车辆"};
        String[] names ={"realName","idCard","createTime","phone","auditStatusValue","auditOpinion","vehicleNumber" };
        map.put("headers",headers);
        map.put("names",names);
        map.put("list",list);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), map);
    }

    /*
     * <AUTHOR>
     * @Description 查询司机车辆审核状态
     * @Date 2019/11/14 16:36
     * @Param
     * @return
    **/
    @Override
    public ResultUtil selectUserAndCarAuditStatus(TEndUserInfoSearchVO record) {
        if (null != record.getType()) {
            // 实体运单
            if (1 == record.getType()) {
                StringBuffer errorMsg = new StringBuffer();
                TEndUserInfo tEndUserInfo = tEndUserInfoMapper.selectByPrimaryKey(record.getEnduserId());
                TEndCarInfo tEndCarInfo = tEndCarInfoMapper.selectByPrimaryKey(record.getVehicleId());
                String msg = "当前运单的#$信息审核未通过，不能提现。";

                String userLogisticsRole = CurrentUser.getUserLogisticsRole();
                if (null != tEndUserInfo) {
                    if (null != tEndUserInfo.getAuditStatus() && !DictEnum.PASSNODE.code.equals(tEndUserInfo.getAuditStatus())) {
                        if (!DictEnum.CTYPEMANAGER.code.equals(userLogisticsRole)) {
                            errorMsg.append("您当前的个人信息审核未通过，不能提现。");
                        } else {
                            msg = msg.replace("#", "司机");
                        }
                    }
                }
                if (null != tEndCarInfo) {
                    if (null != tEndCarInfo.getAuditStatus() && !DictEnum.PASSNODE.code.equals(tEndCarInfo.getAuditStatus())) {
                        if (!DictEnum.CTYPEMANAGER.code.equals(userLogisticsRole)) {
                            errorMsg.append("您当前的车辆信息审核未通过，不能提现。");
                        } else {
                            msg = msg.replace("$", "车辆");
                        }
                    }
                }
                if (DictEnum.CTYPEMANAGER.code.equals(userLogisticsRole)) {
                    msg = msg.replace("#", "");
                    msg = msg.replace("$", "");
                    errorMsg.append(msg);
                }

                if (errorMsg.length() > 0) {
                    return ResultUtil.error(errorMsg.toString());
                }
            }
            // 打包运单
            if (2 == record.getType()) {
                Boolean auditStatus = true;
                List<TEndcarAndUserMemberDTO> tEndcarAndUserMemberDTOS = tEndUserInfoMapper.selectUserAndCarAuditStatusByPackOrder(record.getCode());
                if (null != tEndcarAndUserMemberDTOS && tEndcarAndUserMemberDTOS.size() > 0) {
                    for (TEndcarAndUserMemberDTO endcarAndUserMemberDTO : tEndcarAndUserMemberDTOS) {
                        if (null != endcarAndUserMemberDTO.getCarAuditStatus()
                                && !DictEnum.PASSNODE.code.equals(endcarAndUserMemberDTO.getCarAuditStatus())) {
                            auditStatus = false;
                            break;
                        }
                        if (endcarAndUserMemberDTO.getCapitalTransferType().contains("PAYTODRIVER")) {
                            if (null != endcarAndUserMemberDTO.getUserAuditStatus()
                                    && !DictEnum.PASSNODE.code.equals(endcarAndUserMemberDTO.getUserAuditStatus())) {
                                auditStatus = false;
                                break;
                            }
                        }
                        if (endcarAndUserMemberDTO.getCapitalTransferType().contains("BELONGER")) {
                            if (null != endcarAndUserMemberDTO.getUserAuditStatus() && !DictEnum.PASSNODE.code.equals(endcarAndUserMemberDTO.getUserAuditStatus())) {
                                auditStatus = false;
                                break;
                            }
                        }
                    }
                }
                if (!auditStatus) {
                    return ResultUtil.error("当前运单的司机或车辆信息审核未通过，不能提现");
                }
            }
        }
        return ResultUtil.ok();
    }

    /*
     * <AUTHOR>
     * @Description  查询经纪人
     * @Date 2020/2/8 5:32 下午
     * @Param
     * @return 5:32 下午
    **/

    @Override
    public ResultUtil selectAgent(TEndUserInfoSearchVO record) {
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        record.setCompanyIds(userCompanyId);
        if (null != record.getPage() && null != record.getSize()) {
            Page<Object> page = PageHelper.startPage(record.getPage(), record.getSize());
            List<EndUserDTO> endUserDTOS = tEndUserInfoMapper.selectAgent(record);
            return ResultUtil.ok(endUserDTOS, page.getTotal());
        }
        List<EndUserDTO> endUserDTOS = tEndUserInfoMapper.selectAgent(record);
        return ResultUtil.ok(endUserDTOS);
    }

    @Override
    public TLineGoodsManagerRel selectByLineGoodsrRelId(String lineGoodsrRelId) {
        return tEndUserInfoMapper.selectByLineGoodsrRelId(lineGoodsrRelId);
    }

    @Override
    public ResultUtil orderExamineSelectAgent(TAgentSearchVo record) {
        List<EndUserDTO> endUserDTO = tEndUserInfoMapper.selectAgentByLineGoodsRelId(record.getLineGoodsRelId());
        if (null != record.getAgentId()) {
            List<EndUserDTO> endUserDTOS = endUserDTO.parallelStream().filter(dto -> record.getAgentId().equals(dto.getEndAgentId())).collect(Collectors.toList());
            if (endUserDTOS.isEmpty()) {
                EndUserDTO userDTO = tEndUserInfoMapper.selectEnduserForDTO(record.getAgentId(), null);
                if (null != userDTO) {
                    endUserDTO.add(userDTO);
                }
            }

        }
        return ResultUtil.ok(endUserDTO);
    }

    @Transactional
    @Override
    public int updateEnduserUploadedInfo(TEndUserInfo record) {
        return tEndUserInfoMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public ResultUtil selectDriverIfCaptain(TEndUserInfo vo){
        TEndUserInfo tEndUserInfoList = tEndUserInfoMapper.selectByPrimaryKey(vo.getId());
        TEnduserAccount tEnduser = new TEnduserAccount();
        tEnduser.setEnduserId(vo.getId());
        List<TEnduserAccount> tEnduserAccounts =tEndUserInfoMapper.selectEndUserId(tEnduser);
        if(tEnduserAccounts.size()>0){
            tEnduser.setEnduserId(null);
            tEnduser.setAccountId(tEnduserAccounts.get(0).getAccountId());
            List<TEnduserAccount> tEnduserAccountList =tEndUserInfoMapper.selectEndUserId(tEnduser);

            TEndUserInfo tEndUserInfovo = new TEndUserInfo();
            tEndUserInfovo.setUserLogisticsRole("CTYPECAPTAIN");
            for (TEnduserAccount tEnduserAccount :tEnduserAccountList){
                tEndUserInfovo.setId(tEnduserAccount.getEnduserId());
                List<TEndUserInfo> tEndUserInfo = tEndUserInfoMapper.selectCaptainstate(tEndUserInfovo);
                if(tEndUserInfo.size() > 0){
                    return ResultUtil.error("当前司机已添加车队长身份，请勿重复添加！");
                }
            }
        }
        return ResultUtil.ok(tEndUserInfoList);
    }

    @Override
    public ResultUtil addAddress(TEndUserInfo record) throws Exception {
        SimpleDateFormat formatter = new SimpleDateFormat( "yyyy-MM-dd");
        TEndUserInfo tEndUserInfo = tEndUserInfoMapper.selectByPrimaryKey(record.getId());
        if(StringUtils.isNotBlank(tEndUserInfo.getIdcardPhoto1()) && StringUtils.isNotBlank(tEndUserInfo.getIdcardPhoto2())){
            VehicleReq vehicleReq = new VehicleReq();
            vehicleReq.setFaceData(tEndUserInfo.getIdcardPhoto1());
            vehicleReq.setBackData(tEndUserInfo.getIdcardPhoto2());
            IdCardLicenseV2Resp idCardLicenseV2Resp = trajectoryRecentAPI.idCardLicenseV2(vehicleReq);
            if(idCardLicenseV2Resp.getCode().equals(DictEnum.SUCCESS.code)){
                tEndUserInfo.setAddress(idCardLicenseV2Resp.getAddress());
                tEndUserInfo.setIdcardValidUntil(formatter.parse(idCardLicenseV2Resp.getValidFrom()));
                if(null != idCardLicenseV2Resp.getValidTo() && idCardLicenseV2Resp.getValidTo().contains("长期")){
                    tEndUserInfo.setIdcardValidBeginning(formatter.parse("2099"+idCardLicenseV2Resp.getValidFrom().substring(4,idCardLicenseV2Resp.getValidFrom().length())));
                }else if(null != idCardLicenseV2Resp.getValidTo()){
                    tEndUserInfo.setIdcardValidBeginning(formatter.parse(idCardLicenseV2Resp.getValidTo()));
                }
                tEndUserInfo.setIdcardIssueQrganization(idCardLicenseV2Resp.getIssue());
                tEndUserInfo.setIdcardSex(idCardLicenseV2Resp.getSex());
                tEndUserInfo.setIdcardEthnicity(idCardLicenseV2Resp.getEthnicity());
                tEndUserInfo.setIdcardBirth(formatter.parse(idCardLicenseV2Resp.getBirth()));
                if (StringUtils.isNotBlank(tEndUserInfo.getAddress()) && null!=tEndUserInfo.getIdcardValidUntil() &&!"".equals(tEndUserInfo.getIdcardValidUntil())){
                    tEndUserInfo.setAddressState(true);
                }else {
                    tEndUserInfo.setAddressState(false);
                }
                tEndUserInfoMapper.updateByPrimaryKeySelective(tEndUserInfo);
            }else {
                return ResultUtil.error(idCardLicenseV2Resp.getMsg());
            }
        }else {
            return ResultUtil.error("请先上传身份证信息后再进行此操作");
        }
        return ResultUtil.ok();
    }

    @Override
    public ResultUtil excelBatchAddAddress(TEndUserInfo record) throws Exception {
        try{
            SimpleDateFormat formatter = new SimpleDateFormat( "yyyy-MM-dd");
            TEndUserInfo tEndUserInfo = tEndUserInfoMapper.selectByPrimaryKey(record.getId());
            if(StringUtils.isNotBlank(tEndUserInfo.getIdcardPhoto1()) && StringUtils.isNotBlank(tEndUserInfo.getIdcardPhoto2())){
                VehicleReq vehicleReq = new VehicleReq();
                vehicleReq.setFaceData(tEndUserInfo.getIdcardPhoto1());
                vehicleReq.setBackData(tEndUserInfo.getIdcardPhoto2());
                IdCardLicenseV2Resp idCardLicenseV2Resp =trajectoryRecentAPI.idCardLicenseV2(vehicleReq);
                if(idCardLicenseV2Resp.getCode().equals(DictEnum.SUCCESS.code)){
                    tEndUserInfo.setAddress(idCardLicenseV2Resp.getAddress());
                    tEndUserInfo.setIdcardValidUntil(formatter.parse(idCardLicenseV2Resp.getValidFrom()));
                    if(null != idCardLicenseV2Resp.getValidTo() && idCardLicenseV2Resp.getValidTo().contains("长期")){
                        tEndUserInfo.setIdcardValidBeginning(formatter.parse("2099"+idCardLicenseV2Resp.getValidFrom().substring(4,idCardLicenseV2Resp.getValidFrom().length())));
                    }else if(null != idCardLicenseV2Resp.getValidTo()){
                        tEndUserInfo.setIdcardValidBeginning(formatter.parse(idCardLicenseV2Resp.getValidTo()));
                    }
                    tEndUserInfo.setIdcardIssueQrganization(idCardLicenseV2Resp.getIssue());
                    tEndUserInfo.setIdcardSex(idCardLicenseV2Resp.getSex());
                    tEndUserInfo.setIdcardEthnicity(idCardLicenseV2Resp.getEthnicity());
                    tEndUserInfo.setIdcardBirth(formatter.parse(idCardLicenseV2Resp.getBirth()));
                    if (StringUtils.isNotBlank(tEndUserInfo.getAddress()) && null!=tEndUserInfo.getIdcardValidUntil() &&!"".equals(tEndUserInfo.getIdcardValidUntil())){
                        tEndUserInfo.setAddressState(true);
                    }else {
                        tEndUserInfo.setAddressState(false);
                    }
                    tEndUserInfoMapper.updateByPrimaryKeySelective(tEndUserInfo);
                    return ResultUtil.ok();
                }else {
                    return ResultUtil.error(idCardLicenseV2Resp.getMsg());
                }
            }else {
                return ResultUtil.error("请先上传身份证信息后再进行此操作");
            }
        }catch (Exception e){
            log.error("身份证地址识别失败！请检查用户信息,{}",e);
            return ResultUtil.error("身份证地址识别失败，请检查图片是否存在!");
        }
    }

    @Override
    public ResultUtil insertCaptain(TEndUserInfo vo){
        TEndUserInfo tEndUserInfovo = tEndUserInfoMapper.selectByPrimaryKey(vo.getId());
        tEndUserInfovo.setId(null);
        tEndUserInfovo.setEnable(false);
        tEndUserInfovo.setIdcardPhoto1(result(vo.getIdcardPhoto1()));
        tEndUserInfovo.setIdcardPhoto2(result(vo.getIdcardPhoto2()));
        tEndUserInfovo.setUserLogisticsRole("CTYPECAPTAIN");
        tEndUserInfoMapper.insertSelective(tEndUserInfovo);

        TAccount tAccount = tEndUserInfoMapper.selectUserIdByEnduserId(vo.getId());

        //司机添加车队长身份-给车队长添加角色
        TSysRole tSysRole =  systemAPI.selectByParam("cdz");
        if(tSysRole!=null){
            TUserRole tUserRole = new TUserRole();
            tUserRole.setUserId(tAccount.getUserId());
            tUserRole.setRoleId(tSysRole.getId());
            systemAPI.saveUserRole(tUserRole);
        }

        Integer integer = tEndUserInfoMapper.selectenduserAccount(vo.getId());
        TEndUserInfoVO tEndUserInfoVO = new TEndUserInfoVO();
        tEndUserInfoVO.setEnduserId(tEndUserInfovo.getId());
        tEndUserInfoVO.setAccountId(integer);
        tEndUserInfoVO.setRealName(tEndUserInfovo.getRealName());
        tEndUserInfoVO.setIdcard(tEndUserInfovo.getIdcard());
        tEndUserInfoVO.setPhone(tEndUserInfovo.getPhone());
        tEndUserInfoMapper.CaptainEndUserAccount(tEndUserInfoVO);

        TEndUserStatus tEndUserStatus = tEndUserStatusMapper.selectByEnduserId(vo.getId());
        tEndUserStatus.setId(null);
        tEndUserStatus.setEnable(false);
        tEndUserStatus.setEnduserId(tEndUserInfovo.getId());
        tEndUserStatusMapper.insertSelective(tEndUserStatus);
        return  ResultUtil.ok();
    }

    public ResultUtil selectCaptainIfDriver(TEndUserInfoVO vo){
        TEndUserInfo tEndUserInfoList = tEndUserInfoMapper.selectByPrimaryKey(vo.getId());
        TEnduserAccount tEnduser = new TEnduserAccount();
        tEnduser.setEnduserId(vo.getId());
        List<TEnduserAccount> tEnduserAccounts =tEndUserInfoMapper.selectEndUserId(tEnduser);
        if(tEnduserAccounts.size()>0){
            tEnduser.setEnduserId(null);
            tEnduser.setAccountId(tEnduserAccounts.get(0).getAccountId());
            List<TEnduserAccount> tEnduserAccountList =tEndUserInfoMapper.selectEndUserId(tEnduser);

            TEndUserInfo tEndUserInfovo = new TEndUserInfo();
            tEndUserInfovo.setUserLogisticsRole("CTYPEDRVIVER");
            for (TEnduserAccount tEnduserAccount :tEnduserAccountList){
                tEndUserInfovo.setId(tEnduserAccount.getEnduserId());
                List<TEndUserInfo> tEndUserInfo = tEndUserInfoMapper.selectCaptainstate(tEndUserInfovo);
                if(tEndUserInfo.size() > 0){
                    return ResultUtil.error("当前车队长已添加司机身份，请勿重复添加！");
                }
            }
        }
        return ResultUtil.ok(tEndUserInfoList);
    }

    public ResultUtil insertDirver(TEndUserInfoVO vo){
        TEndUserInfo tEndUserInfovo = tEndUserInfoMapper.selectByPrimaryKey(vo.getId());
        tEndUserInfovo.setId(null);
        tEndUserInfovo.setEnable(false);
        tEndUserInfovo.setIdcardPhoto1(result(vo.getIdcardPhoto1()));
        tEndUserInfovo.setIdcardPhoto2(result(vo.getIdcardPhoto2()));
        tEndUserInfovo.setDrivingLicencesPhoto1(result(vo.getDrivingLicencesPhotoXsz1()));
        tEndUserInfovo.setDrivingLicencesPhoto2(result(vo.getDrivingLicencesPhotoXsz2()));
        tEndUserInfovo.setCertificatePhoto1(result(vo.getRoadTransportOperationLicensePhoto1()));
        tEndUserInfovo.setCertificatePhoto2(result(vo.getRoadTransportOperationLicensePhoto2()));
        tEndUserInfovo.setUserLogisticsRole("CTYPEDRVIVER");
        int insertSelective = tEndUserInfoMapper.insertSelective(tEndUserInfovo);
        Integer integer = tEndUserInfoMapper.selectenduserAccount(vo.getId());
        TEndUserInfoVO tEndUserInfoVO = new TEndUserInfoVO();
        tEndUserInfoVO.setEnduserId(tEndUserInfovo.getId());
        tEndUserInfoVO.setAccountId(integer);
        tEndUserInfoVO.setRealName(tEndUserInfovo.getRealName());
        tEndUserInfoVO.setIdcard(tEndUserInfovo.getIdcard());
        tEndUserInfoVO.setPhone(tEndUserInfovo.getPhone());
        Integer CaptainAccount = tEndUserInfoMapper.CaptainEndUserAccount(tEndUserInfoVO);
        TEndUserStatus tEndUserStatus = new TEndUserStatus();
        tEndUserStatus.setUserStatus("DISTRIBUTIONREGISTERED");
        tEndUserStatus.setEnable(false);
        tEndUserStatus.setCurrentDriverAccountNo(tEndUserInfovo.getPhone());
        tEndUserStatus.setEnduserId(tEndUserInfovo.getId());
        tEndUserStatusMapper.insertSelective(tEndUserStatus);
        return  ResultUtil.ok();
    }

    private String result(String param){
        if(StringUtil.isNotEmpty(param)){
            return param;
        }else{
            return null;
        }
    }

    public ResultUtil selectCaptainPage(TEndUserInfoVO tEndUserInfoVO){
        tEndUserInfoVO.setUserLogisticsRole("CTYPECAPTAIN");
        //设置配置GROUP_CONCAT 参数大小 10240
        tEndUserInfoMapper.setGroupConcatMaxLen();
        Page<Object> objectPage = PageHelper.startPage(tEndUserInfoVO.getPage(),tEndUserInfoVO.getSize());
        List<TEndUserInfoVO> tEndUserInfoList = tEndUserInfoMapper.selectCaptainByPage(tEndUserInfoVO);
        tEndUserInfoList.forEach((vo) -> {
            if("NEWNODE".equals(vo.getAuditStatus())){
                vo.setAuditStatusValue("新建");
            }else if("MIDNODE".equals(vo.getAuditStatus())){
                vo.setAuditStatusValue("审核中");
            }else if("NOTPASSNODE".equals(vo.getAuditStatus())){
                vo.setAuditStatusValue("审核不通过");
            }else if("PAPERNEEDUPDATE".equals(vo.getAuditStatus())){
                vo.setAuditStatusValue("资料需更新");
            }else if("PASSNODE".equals(vo.getAuditStatus())){
                vo.setAuditStatusValue("审核通过");
            }
            if(vo.getCreateTime() != null){
                vo.setCreateTimeStr(DateUtils.formatDateTime(vo.getCreateTime()));
            }
            if(vo.getUpdateTime() != null){
                vo.setUpdateTimeStr(DateUtils.formatDateTime(vo.getUpdateTime()));
            }
            if (vo.getAddressState() != null){
                if (vo.getAddressState()){
                    vo.setAddressStateValue("已填写");
                }else{
                    vo.setAddressStateValue("未填写");
                }
            }
            // 身份证有效期状态
            if (null != vo.getIdcardValidBeginning() && null != vo.getIdcardValidUntil()) {
                Date validUntilDate = vo.getIdcardValidUntil();
                if (vo.getIdcardValidBeginning().getTime() > vo.getIdcardValidUntil().getTime()) {
                    validUntilDate = vo.getIdcardValidBeginning();
                }
                vo.setIdcardValidStatus(DateUtils.checkValidDate(validUntilDate));
                vo.setIdcardValidUntilDate(DateUtils.formatDate(validUntilDate, "yyyy-MM-dd"));
            }

        });
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),objectPage,objectPage.getTotal());
    }

    public TEndUserInfoVO selectCaptainOwnerById(Integer id){
        //车老板信息
        TEndUserInfo info = tEndUserInfoMapper.selectByPrimaryKey(id);
        TEndUserInfoVO vo = new TEndUserInfoVO();
        BeanUtils.copyProperties(info,vo);
        if("NEWNODE".equals(vo.getAuditStatus())){
            vo.setAuditStatusValue("新建");
        }else if("MIDNODE".equals(vo.getAuditStatus())){
            vo.setAuditStatusValue("审核中");
        }else if("NOTPASSNODE".equals(vo.getAuditStatus())){
            vo.setAuditStatusValue("审核不通过");
        }else if("PAPERNEEDUPDATE".equals(vo.getAuditStatus())){
            vo.setAuditStatusValue("资料需更新");
        }else if("PASSNODE".equals(vo.getAuditStatus())){
            vo.setAuditStatusValue("审核通过");
        }
        //根据id查询与account关系
        TEnduserAccountExample example = new TEnduserAccountExample();
        TEnduserAccountExample.Criteria cr = example.createCriteria();
        cr.andEnduserIdEqualTo(info.getId());
        //根据accountId查询银行卡
        List<TEnduserAccount>  tEnduserAccountList = tEnduserAccountMapper.selectByExample(example);
        if(tEnduserAccountList.size()>0){
            //银行卡列表
            TBankCardExample example2 = new TBankCardExample();
            TBankCardExample.Criteria c = example2.createCriteria();
            c.andAccountIdEqualTo(tEnduserAccountList.get(0).getAccountId());
            c.andEnableEqualTo(false);
            List<TBankCard> tBankCardList = tBankCardMapper.selectByExample(example2);
            vo.setTBankCardList(tBankCardList);

            List<TZtBankCardVo> tZtBankCardVoList = tZtBankCardMapper.selectBankCardInfoByAccountId(tEnduserAccountList.get(0).getAccountId());
            vo.setTZtBankCardVoList(tZtBankCardVoList);
        }
        TEndUserAuditInfo endUserAuditInfo = endUserAuditInfoMapper.selectByEndUserId(id);
        if (null != endUserAuditInfo) {
            vo.setIdcardStatus(endUserAuditInfo.getIdcardStatus());
            vo.setIdcardOpinion(endUserAuditInfo.getIdcardOpinion());
        }
        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil updateCaptainPage(EndUserInfoUpdate vo){
        //车老板信息
        TEndUserInfo eui = tEndUserInfoMapper.selectByPrimaryKey(vo.getId());

        List<TEndUserInfo> idcardCount = tEndUserInfoMapper.notOwnIdcardList(vo.getIdcard(), vo.getId());
        if (null != idcardCount && !idcardCount.isEmpty() && !vo.getIdcard().equals(eui.getIdcard())) {
            return ResultUtil.error("编辑失败，该身份证号已在平台存在!");
        }

        if (!eui.getPhone().equals(vo.getPhone())) {
            return ResultUtil.error("车队长不可修改手机号！");
        }

        if (StringUtils.isNotBlank(vo.getAddress()) && null != vo.getIdcardValidBeginning()) {
            eui.setAddressState(true);
        } else {
            eui.setAddressState(false);
        }
        eui.setAddress(vo.getAddress());
        eui.setRealName(vo.getRealName());
        eui.setIdcard(vo.getIdcard());
        eui.setIdcardValidUntil(vo.getIdcardValidUntil());
        eui.setIdcardValidBeginning(vo.getIdcardValidBeginning());
        eui.setIdcardPhoto1(vo.getIdcardPhoto1());
        eui.setIdcardPhoto2(vo.getIdcardPhoto2());
        eui.setPhone(vo.getPhone());
        eui.setUpdateTime(new Date());
        eui.setUpdateUser(CurrentUser.getUserNickname());
        tEndUserInfoMapper.updateByPrimaryKeySelective(eui);

        //保证车队长和司机除了角色状态/银行卡信息/审核状态不同其他都相同
        //车队长t_enduser_account
        TEnduserAccount tEnduserAccount = tEnduserAccountMapper.selectByEndUserId(eui.getId());
        //司机t_enduser_account
        TEnduserAccount enduserAccount = tEnduserAccountMapper.selectEnduserAccountCtypecaptain(tEnduserAccount.getAccountId());
        if (!tEnduserAccount.getRealName().equals(vo.getRealName())
                || !tEnduserAccount.getPhone().equals(vo.getPhone())
                || !tEnduserAccount.getIdcard().equals(vo.getIdcard())) {
            // 更新车队长t_enduser_account信息
            tEnduserAccount.setPhone(vo.getPhone());
            tEnduserAccount.setRealName(vo.getRealName());
            tEnduserAccount.setIdcard(vo.getIdcard());
            tEnduserAccount.setUpdateTime(new Date());
            tEnduserAccount.setUpdateUser(CurrentUser.getUserNickname());
            tEnduserAccountMapper.updateByPrimaryKeySelective(tEnduserAccount);

            // 更新司机t_enduser_account信息
            if (null != enduserAccount) {
                enduserAccount.setPhone(vo.getPhone());
                enduserAccount.setRealName(vo.getRealName());
                enduserAccount.setIdcard(vo.getIdcard());
                enduserAccount.setUpdateTime(new Date());
                enduserAccount.setUpdateUser(CurrentUser.getUserNickname());
                tEnduserAccountMapper.updateByPrimaryKeySelective(enduserAccount);
            }
        }
        //t_account
        TAccount tAccount = tAccountMapper.selectByPrimaryKey(tEnduserAccount.getAccountId());
        if (!tAccount.getNickname().equals(vo.getRealName()) || !tAccount.getAccountNo().equals(vo.getPhone())) {
            tAccount.setAccountNo(vo.getPhone());
            tAccount.setNickname(vo.getRealName());
            tAccountMapper.updateByPrimaryKeySelective(tAccount);
        }

        //司机t_end_user_info
        if (null != enduserAccount) {
            TEndUserInfo tEndUserInfo = tEndUserInfoMapper.selectByPrimaryKey(enduserAccount.getEnduserId());
            tEndUserInfo.setRealName(vo.getRealName());
            tEndUserInfo.setIdcard(vo.getIdcard());
            tEndUserInfo.setIdcardValidUntil(vo.getIdcardValidUntil());
            tEndUserInfo.setIdcardPhoto1(vo.getIdcardPhoto1());
            tEndUserInfo.setIdcardPhoto2(vo.getIdcardPhoto2());
            tEndUserInfo.setPhone(vo.getPhone());
            tEndUserInfo.setUpdateTime(new Date());
            tEndUserInfo.setUpdateUser(CurrentUser.getUserNickname());
            tEndUserInfoMapper.updateByPrimaryKeySelective(tEndUserInfo);
        }

        if (null != vo.getUpdateOrAudit()) {
            // 更新车队长审核信息表
            TEndUserAuditInfo endUserAuditInfo = endUserAuditInfoMapper.selectByEndUserId(vo.getId());
            if (null == endUserAuditInfo) {
                // 旧数据，添加车队长审核信息表
                endUserAuditInfo = new TEndUserAuditInfo();
                endUserAuditInfo.setEndUserId(vo.getId());
                endUserAuditInfo.setUserLogisticsRole(DictEnum.CTYPECAPTAIN.code);
                endUserAuditInfoMapper.insertSelective(endUserAuditInfo);
            }
            if (StringUtils.isNotBlank(vo.getIdcardStatus()) || StringUtils.isNotBlank(vo.getIdcardOpinion())) {
                // 修改车队长审核信息
                endUserAuditInfo.setIdcardStatus(vo.getIdcardStatus());
                endUserAuditInfo.setIdcardOpinion(vo.getIdcardOpinion());
                endUserAuditInfo.setEnable(false);
                endUserAuditInfo.setUpdateUser(CurrentUser.getUserNickname());
                endUserAuditInfo.setUpdateTime(new Date());
                endUserAuditInfoMapper.updateByPrimaryKey(endUserAuditInfo);

                // 根据各个证件认证状态，计算总认证状态
                String auditStatus = AuditStatusUtil.status(vo.getIdcardStatus());
                TEndUserInfo endUserInfo = new TEndUserInfo();
                if (StringUtils.isNotBlank(vo.getIdcardStatus())) {
                    endUserInfo.setAuditStatus(auditStatus);
                }
                // 审核意见
                if (StringUtils.isNotBlank(vo.getIdcardOpinion())) {
                    endUserInfo.setAuditOpinion(vo.getIdcardOpinion());
                }
                if (StringUtils.isNotBlank(auditStatus) || StringUtils.isNotBlank(vo.getIdcardOpinion())) {
                    endUserInfo.setId(vo.getId());
                    endUserInfo.setUpdateTime(new Date());
                    endUserInfo.setUpdateUser(CurrentUser.getUserNickname());
                    tEndUserInfoMapper.updateByPrimaryKeySelective(endUserInfo);
                }

            }
        }
        return ResultUtil.ok();
    }

    public ResultUtil deleteCaptain(TEndUserInfoDto record){
        try{
            List<Integer> list2 = Arrays.asList(record.getIdArray());
            TEndUserInfoExample example = new TEndUserInfoExample();
            TEndUserInfoExample.Criteria cr = example.createCriteria();
            cr.andIdIn(list2);
            List<TEndUserInfo> tEndUserInfoList = tEndUserInfoMapper.selectByExample(example);
            for(TEndUserInfo tEndUserInfo:tEndUserInfoList){
                if("CTYPECAPTAIN".equals(record.getType())){

                    List<TEndUserInfo> integer = tEndUserInfoMapper.selectCaptainOrderInfo(tEndUserInfo.getId());
                    if (integer.size() > 0){
                        return ResultUtil.error("当前车队长有运单不能进行删除操作");
                    }
                    //---------------------------------------------------------------------------------
                    //车队长和司机是一个账号
                    tEndUserInfo.setEnable(true);
                    //账号表
                    TAccountVo tAccountVo = new TAccountVo();
                    tAccountVo.setAccountNo(tEndUserInfo.getPhone());
                    TAccount tAccount = tAccountMapper.selectAccountByPhone(tAccountVo);
                    TEnduserAccount tEnduserAccount = new TEnduserAccount();
                    tEnduserAccount.setAccountId(tAccount.getId());
                    List<TEnduserAccount> selectEndUser = tEndUserInfoMapper.selectEnduserList(tEnduserAccount);
                    if(selectEndUser.size() < 2){
                        if(null!=tAccount &&!"".equals(tAccount)){
                            tAccount.setEnable(true);
                            tAccountMapper.updateByPrimaryKeySelective(tAccount);
                        }
                        //----------------------------------------------------------------------------------
                        //用的是一个账号删除的话两个一块都删除了
                        //sysUser表
                        TSysUser tSysUser = new TSysUser();
                        tSysUser.setId(tAccount.getUserId());
                        tSysUser.setEnable(true);
                        tSysUser.setPersonalizedSignature("CTYPECAPTAIN");
                        systemAPI.updateUser(tSysUser);
                    }
                    //------------------------------------------------------------------------------------
                    //终端用户状态表
                    TEndUserStatus tEndUserStatus = tEndUserStatusMapper.selectByEndUserId(tEndUserInfo.getId());
                    if(null!=tEndUserStatus &&!"".equals(tEndUserStatus)) {
                        tEndUserStatus.setEnable(true);
                        tEndUserStatusMapper.updateByPrimaryKeySelective(tEndUserStatus);
                    }
                    //-----------------------------------------------------------------------------------
                    //终端用户与账号关系表
                    List<TEnduserAccount> tEnduserAccountList = tEnduserAccountMapper.selectByEndUserInfoIdAndAccountId(tEndUserInfo.getId(),tAccount.getId());
                    for(TEnduserAccount tEnduserAccount1:tEnduserAccountList){
                        tEnduserAccount1.setEnable(true);
                        tEnduserAccountMapper.updateByPrimaryKeySelective(tEnduserAccount1);
                    }
                    //直接逻辑删除
                    tEndUserInfo.setEnable(true);
                    tEndUserInfoMapper.updateByPrimaryKeySelective(tEndUserInfo);
                }
            }
            return ResultUtil.ok("删除成功！");
        }catch (Exception e){
            log.error("终端用户删除失败",e);
            throw e;
        }
    }

    @Override
    public ResultUtil selectCaptainNameList(String parameter){
        List<TEndUserInfo> captainNameList = tEndUserInfoMapper.selectCaptainNameList(parameter);
        return ResultUtil.ok(captainNameList);
    }

    @Override
    public List<TEndUserInfo> selectByAccountNo(String accountNo){

        return tEndUserInfoMapper.selectByAccountNo(accountNo);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2020/12/11 10:31
     *  @Description: postman 处理司机与车队长数据
     */
    @Override
    @Transactional
    public int updateEndUserLogisticsRole(TEndUserInfo record){
        TEndUserInfoExample example = new TEndUserInfoExample();
        TEndUserInfoExample.Criteria cr = example.createCriteria();
        cr.andUserLogisticsRoleLike("%,%");
        cr.andEnableEqualTo(false);
        List<TEndUserInfo> list = tEndUserInfoMapper.selectByExample(example);
        for(TEndUserInfo tEndUserInfo:list){

            TEndUserInfo tEndUserInfoNew  = new TEndUserInfo();
            BeanUtils.copyProperties(tEndUserInfo,tEndUserInfoNew);

            tEndUserInfoNew.setUserLogisticsRole(DictEnum.CTYPECAPTAIN.code);
            tEndUserInfoNew.setEnable(false);
            tEndUserInfoNew.setId(null);

            TEnduserAccount tEnduserAccount = tEnduserAccountMapper.selectByEndUserId(tEndUserInfo.getId());

            List<TEndUserInfo> tEndUserInfoNews = tEndUserInfoMapper.selectCaptainTypeList(tEndUserInfoNew);
            if(tEndUserInfoNews.size()<1){
                tEndUserInfoMapper.insertSelective(tEndUserInfoNew);

                TEnduserAccount tEnduserAccountNew = new TEnduserAccount();
                BeanUtils.copyProperties(tEnduserAccount,tEnduserAccountNew);
                tEnduserAccountNew.setId(null);
                tEnduserAccountNew.setEnable(false);
                tEnduserAccountNew.setEnduserId(tEndUserInfoNew.getId());
                tEnduserAccountMapper.insertSelective(tEnduserAccountNew);

            }
        }

        TEndUserInfoExample example2 = new TEndUserInfoExample();
        TEndUserInfoExample.Criteria cr2 = example2.createCriteria();
        cr2.andUserLogisticsRoleEqualTo("CTYPEBOSS");
        cr2.andEnableEqualTo(false);
        List<TEndUserInfo> list2 = tEndUserInfoMapper.selectByExample(example2);
        for(TEndUserInfo endUserInfo : list2){
            TEndUserInfo tEndUserInfoNew  = new TEndUserInfo();
            BeanUtils.copyProperties(endUserInfo,tEndUserInfoNew);
            tEndUserInfoNew.setUserLogisticsRole(DictEnum.CTYPECAPTAIN.code);
            tEndUserInfoNew.setId(null);
            tEndUserInfoNew.setEnable(false);
            List<TEndUserInfo> tEndUserInfoNews = tEndUserInfoMapper.selectCaptainTypeList(tEndUserInfoNew);
            if(tEndUserInfoNews.size()<1){
                tEndUserInfoMapper.insertSelective(tEndUserInfoNew);
                TEnduserAccount tEnduserAccount = tEnduserAccountMapper.selectByEndUserId(endUserInfo.getId());
                if(null!=tEnduserAccount && !"".equals(tEnduserAccount)){
                    TEnduserAccount tEnduserAccountNew = new TEnduserAccount();
                    BeanUtils.copyProperties(tEnduserAccount,tEnduserAccountNew);
                    tEnduserAccountNew.setId(null);
                    tEnduserAccountNew.setEnable(false);
                    tEnduserAccountNew.setEnduserId(tEndUserInfoNew.getId());
                    tEnduserAccountMapper.insertSelective(tEnduserAccountNew);
                }
            }

        }
        return list.size()+list2.size();
    }

    /**
     *  @author: dingweibo
     *  @Date: 2020/7/29 11:41
     *  @Description: 车队长列表查询
     */
    @Override
    public ResultUtil selectByCaptainListPage(TEndUserInfoVO vo){
        try{
            Page<Object> objectPage = PageHelper.startPage(vo.getPage(),vo.getSize());
            List<TEndUserInfo> list = tEndUserInfoMapper.selectByCaptainList(vo.getParam());
            return new ResultUtil(CodeEnum.SUCCESS.getCode(),objectPage,objectPage.getTotal());
        }catch (Exception e){
            log.error("车队长列表查询失败",e);
            return ResultUtil.error("车队长列表查询失败");
        }
    }

    @Override
    public ResultUtil selectByCaptainList(TEndUserInfoVO vo){
        try{
            List<TEndUserInfo> list = tEndUserInfoMapper.selectByCaptainList(vo.getParam());
            return ResultUtil.ok(list);
        }catch (Exception e){
            log.error("车队长列表查询失败",e);
            return ResultUtil.error("车队长列表查询失败");
        }
    }

    @Override
    public TEndUserInfoVO selectEndCaptainById(Integer id) {

        //车老板信息
        TEndUserInfo info = tEndUserInfoMapper.selectByPrimaryKey(id);
        TEndUserInfoVO vo = new TEndUserInfoVO();
        BeanUtils.copyProperties(info,vo);
        //根据id查询与account关系
        TEnduserAccountExample example = new TEnduserAccountExample();
        TEnduserAccountExample.Criteria cr = example.createCriteria();
        cr.andEnduserIdEqualTo(info.getId());
        //根据accountId查询银行卡
        List<TEnduserAccount>  tEnduserAccountList = tEnduserAccountMapper.selectByExample(example);
        if(tEnduserAccountList.size()>0){
            List<TZtBankCardVo> tZtBankCardVoList = tZtBankUserMapper.hxBankCardList(tEnduserAccountList.get(0).getAccountId(),DictEnum.CD.code);
            List<TBankCard> tBankCardList = new ArrayList<>();
            for(TZtBankCardVo tZtBankCardVo : tZtBankCardVoList){
                if("BIND".equals(tZtBankCardVo.getBindStatus())){
                    TBankCard tBankCard = new TBankCard();
                    tBankCard.setCardNo(tZtBankCardVo.getAcctNo());
                    tBankCard.setCardOwner(tZtBankCardVo.getAcctName());
                    tBankCard.setCardOwnerIdcard(tZtBankCardVo.getAcctCard());
                    tBankCard.setCardOwnerPhone(tZtBankCardVo.getOccBankPhone());
                    tBankCardList.add(tBankCard);
                }
            }
            vo.setTBankCardList(tBankCardList);
            vo.setAccountId(tEnduserAccountList.get(0).getAccountId());
            vo.setEnduserId(tEnduserAccountList.get(0).getEnduserId());
        }
        return vo;
    }


    @Transactional
    @Override
    public int insterDriverZzh(TEndUserInfoSqZzhVO record) {
        int i= 0;
        try{
            Integer carrierId = Integer.parseInt(record.getCarrierId());
            String purseCategory = record.getPurseCategory();
            String[] endUserIdArray = record.getEndUserId().split(",");
            for(String enduserCompanyId :endUserIdArray){
                TEndUserInfo tEndUserInfo = tEndUserInfoMapper.selectByPrimaryKey(Integer.parseInt(enduserCompanyId));
                //创建司机与承运方的关系
                TCarrierEnduserCompanyRel carrierEnduserRel = tCarrierEnduserCompanyRelMapper.selectByEndUser(carrierId,Integer.parseInt(enduserCompanyId));
                if(carrierEnduserRel==null || "".equals(carrierEnduserRel)){
                    carrierEnduserRel = new TCarrierEnduserCompanyRel();
                    carrierEnduserRel.setCarrierId(carrierId);
                    carrierEnduserRel.setEnduserCompanyId(Integer.parseInt(enduserCompanyId));
                    carrierEnduserRel.setDatasouce("CD");
                    carrierEnduserRel.setUid(IdWorkerUtil.getInstance().nextId());
                    carrierEnduserRel.setEnable(false);
                    carrierEnduserRel.setUid(IdWorkerUtil.getInstance().nextId());
                    int o =  tCarrierEnduserCompanyRelMapper.insertSelective(carrierEnduserRel);
                    i=i+o;
                }
                //创建钱包
                String dataSource = "CD";
                TWallet wallet = new TWallet();
                wallet.setCarrierEnduserCompanyId(carrierEnduserRel.getId());
                wallet.setPurseCategory(purseCategory);
                wallet.setDatasource(dataSource);
                tWalletService.selectAndSave(wallet);

                //申请子账号
                TCarrierInfo  tCarrierInfo = tCarrierInfoMapper.selectByPrimaryKey(carrierId);
                UserRequest ur = new UserRequest();
                ur.setKeyStoreName(tCarrierInfo.getThirdPartyInterfaceManageAddress());//承运方表中第三方接口处理地址
                ur.setPartner_id(tCarrierInfo.getBussinessPlatformSignCode());//业务平台签约编号
                ur.setUid(carrierEnduserRel.getUid());//承运方与企业关联表Uid
                ur.setMember_name(tEndUserInfo.getPhone());//会员名称。用户昵称(平台个人会员登录名)
                ur.setReal_name(tEndUserInfo.getRealName());//真实姓名
                ur.setCertificate_type("ID_CARD");//证件类型（见附录）。目前只支持身份证。
                ur.setCertificate_no(tEndUserInfo.getIdcard());//作为会员实名认证通过后的证件号
                ResultUtil resultUtil = userRegisterAPI.personalRegister(ur);
                JSONObject jo = JSONObject.parseObject(resultUtil.getData().toString());
                if ("T".equals(jo.get("is_success"))) {
                    String sub_account_no = jo.get("sub_account_no").toString();
                    //承运放子账号信息
                    TCarrierEnduserCompanyRel rel = tCarrierEnduserCompanyRelMapper.selectByUid(ur.getUid());
                    TCarrierEnduserCompanyRel rel2 = new TCarrierEnduserCompanyRel();
                    rel2.setId(rel.getId());
                    rel2.setThridParySubAccount(tCarrierInfo.getThirdPartyInterfaceMainAccountNo() + sub_account_no);
                    tCarrierEnduserCompanyRelMapper.updateByPrimaryKeySelective(rel2);
                    log.info("执行成功！");
                }
            }
        }catch (Exception e){
            log.error("司机申请子账号接口失败",e);
        }
        return i;
    }

    @Override
    public ResultUtil selectIfAgreement(String phone){
        TSysUser tSysUser = tEndUserInfoMapper.selectSysUserIfAgreement(phone);
        if(null != tSysUser && null != tSysUser.getIfAgreement()){
            return ResultUtil.ok(tSysUser.getIfAgreement());
        }else{
            return ResultUtil.error("请输入正确手机号");
        }
    }

    @Override
    public ResultUtil updateIfAgreement(String phone){
        TSysUser tSysUser = tEndUserInfoMapper.selectSysUserIfAgreement(phone);
        tEndUserInfoMapper.updateSysUserIfAgreement(tSysUser.getId());
        return ResultUtil.ok();
    }

    @Transactional
    @Override
    public ResultUtil perfectCard(PerfectCardInfoVO vo) {
        // 完善非本人银行卡
        if (!vo.getSelfCard()) {
            // 1. 查询手机号和身份证号是否同一人
            List<TEndUserInfo> userInfos = tEndUserInfoMapper.selectByPhoneAndIdcard(vo.getCardOwnerPhone(), vo.getCardOwnerIdcard());
            if (null == userInfos || userInfos.isEmpty()) {
                // 2. 查询身份证是否存在
                List<TEndUserInfo> tEndUserInfos = tEndUserInfoMapper.selectByIdcard(vo.getCardOwnerIdcard());
                if (null != tEndUserInfos && !tEndUserInfos.isEmpty()) {
                    // 2.1 检测信息是否一致
                    // 取司机
                    List<TEndUserInfo> list = tEndUserInfos
                            .stream()
                            .filter(info -> info.getUserLogisticsRole().contains(DictEnum.CTYPEDRVIVER.code))
                            .collect(Collectors.toList());
                    if (null != list && !list.isEmpty()) {
                        ResultUtil resultUtil = checkInfo(vo, list);
                        if (null != resultUtil) {
                            return resultUtil;
                        }
                    } else {
                        // 取车队长
                        list = tEndUserInfos
                                .stream()
                                .filter(info -> info.getUserLogisticsRole().contains(DictEnum.CTYPECAPTAIN.code))
                                .collect(Collectors.toList());
                        if (null != list && !list.isEmpty()) {
                            ResultUtil resultUtil = checkInfo(vo, list);
                            if (null != resultUtil) {
                                return resultUtil;
                            }
                        }else {
                            // 转账人  //银行卡修改信息同步到转账人
                            list = tEndUserInfos
                                    .stream()
                                    .filter(info -> info.getUserLogisticsRole().contains(DictEnum.CTYPETRANSFEROR.code))
                                    .collect(Collectors.toList());
                            if (null != list && !list.isEmpty()) {
                                TEnduserAccountExample tEnduserAccountExample = new TEnduserAccountExample();
                                TEnduserAccountExample.Criteria c1 = tEnduserAccountExample.createCriteria();
                                c1.andEnduserIdEqualTo(list.get(0).getId());
                                c1.andEnableEqualTo(false);

                                List<TEnduserAccount>  tEnduserAccounts = tEnduserAccountMapper.selectByExample(tEnduserAccountExample);

                                TBankCardExample tBankCardExample = new TBankCardExample();
                                TBankCardExample.Criteria c2 = tBankCardExample.createCriteria();
                                c2.andAccountIdEqualTo(tEnduserAccounts.get(0).getAccountId());
                                c2.andCardNoEqualTo(vo.getCardNo());

                                List<TBankCard> tBankCards = tBankCardMapper.selectByExample(tBankCardExample);

                                PerfectCardInfoVO perfectCardInfoVO = new PerfectCardInfoVO();
                                BeanUtils.copyProperties(perfectCardInfoVO, vo);
                                perfectCardInfoVO.setCardId(tBankCards.get(0).getId());
                                perfectCardInfoVO.setIfDefault(false);

                                perfectCardInfo(perfectCardInfoVO);
                            }
                        }
                    }
                } else {
                    // 查询手机号是否存在
//                    List<TSysUser> tSysUserList = systemAPI.selectByPhone(vo.getCardOwnerPhone());
//                    if (tSysUserList.size() > 0) {
//                        return ResultUtil.error("持卡人手机号已存在, 请更换手机号");
//                    }
                    // 完善非本人银行卡 -> 添加转账人
                    List<TEndUserOpenRole> selectNoOneselfOpenRoleStateList = tEndUserOpenRoleMapper.selectNoOneselfOpenRoleStateList(vo.getCardOwnerIdcard());
                    if(selectNoOneselfOpenRoleStateList.size() > 0){
                        List<TBankCard> noOneselfBankCardList = tBankCardMapper.noOneselfBankCardList(selectNoOneselfOpenRoleStateList.get(0).getId());
                        Boolean noOneselfBankCardState = true;
                        for(TBankCard tBankCard : noOneselfBankCardList){
                            if(tBankCard.getCardNo().equals(vo.getCardNo())){
                                noOneselfBankCardState = false;
                            }
                        }
                        if(noOneselfBankCardState){
                            TEnduserAccountExample tEnduserAccountExample = new TEnduserAccountExample();
                            TEnduserAccountExample.Criteria c1 = tEnduserAccountExample.createCriteria();
                            c1.andEnduserIdEqualTo(selectNoOneselfOpenRoleStateList.get(0).getEndUserId());
                            c1.andEnableEqualTo(false);

                            List<TEnduserAccount>  tEnduserAccounts = tEnduserAccountMapper.selectByExample(tEnduserAccountExample);
                            TBankCard tBankCard = new TBankCard();
                            tBankCard.setCardNo(vo.getCardNo());
                            tBankCard.setCardOwnerPhone(vo.getCardOwnerPhone());
                            tBankCard.setCardOwner(vo.getCardOwner());
                            tBankCard.setCardOwnerIdcard(vo.getCardOwnerIdcard());
                            tBankCard.setAccountId(tEnduserAccounts.get(0).getAccountId());
                            tBankCard.setCardOwnerIdcardPhoto1(vo.getIdcardPhoto1());
                            tBankCard.setCardOwnerIdcardPhoto2(vo.getIdcardPhoto2());
                            tBankCard.setIdcardValidBeginning(vo.getIdcardValidBeginning());
                            tBankCard.setIdcardValidUntil(vo.getIdcardValidUntil());
                            tBankCard.setAddress(vo.getAddress());
                            tBankCard.setCreateUser(CurrentUser.getUserNickname());
                            tBankCard.setCreateTime(new Date());
                            tBankCard.setEnable(true);
                            tBankCard.setIfDefault(0);
                            tBankCardMapper.insertSelective(tBankCard);

                            TJdBankCardVo tJdBankCardVo = new TJdBankCardVo();
                            tJdBankCardVo.setOpenRoleId(selectNoOneselfOpenRoleStateList.get(0).getId());
                            tJdBankCardVo.setPartnerMemberId(selectNoOneselfOpenRoleStateList.get(0).getPartnerMemberId());
                            tJdBankCardVo.setPartnerAccId(selectNoOneselfOpenRoleStateList.get(0).getPartnerAccId());
                            tJdBankCardVo.setBankCardId(tBankCard.getId());
                            tJdBankCardVo.setOperator(selectNoOneselfOpenRoleStateList.get(0).getOperator());
                            tJdBankCardVo.setAcctNo(vo.getCardNo());
                            tJdBankCardVo.setAcctName(vo.getCardOwner());
                            tJdBankCardVo.setOccBankPhone(vo.getCardOwnerPhone());
                            ResultUtil jdResultUtil = tjdBankCardService.openRoleUserBindingBank(tJdBankCardVo);

                            if(null == jdResultUtil || null == jdResultUtil.getCode() || !jdResultUtil.getCode().equals("success")){
                                return jdResultUtil;
                            }
                        }

                    }else {
                        // 查询手机号是否存在
                        List<TSysUser> tSysUserList = systemAPI.selectByPhone(vo.getCardOwnerPhone());
                        if (tSysUserList.size() > 0) {
                            return ResultUtil.error("持卡人手机号已存在, 请更换手机号");
                        }
                        List<TEndUserOpenRole> selectOneselfOpenRoleStateList = tEndUserOpenRoleMapper.selectOneselfOpenRoleStateList(vo.getCardOwnerIdcard());
                        if(selectOneselfOpenRoleStateList.size() > 0){
                            throw new RuntimeException("持卡人身份证已存在");
                        }
                        // 添加非本人银行卡 -> 添加转账人
                        addTransferor(vo);
                    }
                }
            }
        }

        if (vo.getIfDefault() != null && vo.getIfDefault()) {
            tBankCardMapper.updateByAccountId(vo.getAccountId());
        }

        // 完善银行卡信息
        perfectCardInfo(vo);

        return ResultUtil.ok();
    }

    private ResultUtil checkInfo(PerfectCardInfoVO vo, List<TEndUserInfo> list) {
            TEndUserInfo endUserInfo = list.get(0);
            if (null != endUserInfo.getIdcardPhoto1() && null != endUserInfo.getIdcardPhoto2()
                    && null != endUserInfo.getIdcardValidBeginning() && null != endUserInfo.getIdcardValidUntil()
                    && null != endUserInfo.getAddress()) {
                if (vo.getCardOwner().equals(endUserInfo.getRealName()) && vo.getAddress().equals(endUserInfo.getAddress())
                        && DateUtils.isSameDay(vo.getIdcardValidBeginning(), endUserInfo.getIdcardValidBeginning())
                        && DateUtils.isSameDay(vo.getIdcardValidUntil(), endUserInfo.getIdcardValidUntil())) {
                } else {
                    return ResultUtil.error("上传的身份证照片与该持卡人不一致");
                }
            } else {
                return ResultUtil.error("上传的身份证照片与该持卡人不一致");
            }

        return null;
    }

    /**
    * @description 完善银行卡信息
    * <AUTHOR>
    * @date 2021/4/8 14:58
    * @param
    * @return
    */
    private void perfectCardInfo(PerfectCardInfoVO vo) {
        TBankCard bankCard = tBankCardMapper.selectByPrimaryKey(vo.getCardId());
        bankCard.setAddress(vo.getAddress());
        bankCard.setCardOwnerPhone(vo.getCardOwnerPhone());
        bankCard.setCardOwnerIdcardPhoto1(vo.getIdcardPhoto1());
        bankCard.setCardOwnerIdcardPhoto2(vo.getIdcardPhoto2());
        bankCard.setIdcardValidBeginning(vo.getIdcardValidBeginning());
        bankCard.setIdcardValidUntil(vo.getIdcardValidUntil());
        bankCard.setIfDefault(vo.getIfDefault() ? Integer.valueOf(1) : Integer.valueOf(0));
        bankCard.setUpdateUser(CurrentUser.getUserNickname());
        bankCard.setUpdateTime(new Date());
        if(null != vo.getBankName()){
            bankCard.setBankName(vo.getBankName());
        }
        tBankCardMapper.updateByPrimaryKey(bankCard);
    }

    /**
    * @description 添加转账人
    * <AUTHOR>
    * @date 2021/4/8 14:59
    * @param
    * @return
    */
    public void addTransferor(PerfectCardInfoVO vo) {
//        List<TEndUserOpenRole> selectNoOneselfOpenRoleStateList = tEndUserOpenRoleMapper.selectNoOneselfOpenRoleStateList(vo.getCardOwnerIdcard());
//        if(selectNoOneselfOpenRoleStateList.size() > 0){
//            throw new RuntimeException("持卡人身份证已存在");
//        }
//        List<TEndUserOpenRole> selectOneselfOpenRoleStateList = tEndUserOpenRoleMapper.selectOneselfOpenRoleStateList(vo.getCardOwnerIdcard());
//        if(selectOneselfOpenRoleStateList.size() > 0){
//            throw new RuntimeException("持卡人身份证已存在");
//        }

        // 新增C端用户信息
        TEndUserInfo endUserInfo = new TEndUserInfo();
        endUserInfo.setRealName(vo.getCardOwner());
        endUserInfo.setPhone(vo.getCardOwnerPhone());
        endUserInfo.setAddress(vo.getAddress());
        endUserInfo.setAddressState(true);
        endUserInfo.setUserLogisticsRole("CTYPETRANSFEROR");
        endUserInfo.setIdcard(vo.getCardOwnerIdcard());
        endUserInfo.setIdcardPhoto1(vo.getIdcardPhoto1());
        endUserInfo.setIdcardPhoto2(vo.getIdcardPhoto2());
        endUserInfo.setIdcardValidBeginning(vo.getIdcardValidBeginning());
        endUserInfo.setIdcardValidUntil(vo.getIdcardValidUntil());
        endUserInfo.setCreateUser(CurrentUser.getUserNickname());
        endUserInfo.setCreateTime(new Date());
        tEndUserInfoMapper.insertSelective(endUserInfo);

        //sys_user新增系统用户
        TSysUser sysUser = new TSysUser();
        sysUser.setAccountNo(vo.getCardOwnerPhone());
        sysUser.setUsername(vo.getCardOwnerPhone());
        sysUser.setNickname(vo.getCardOwner());
        sysUser.setPassword(DigestUtils.md5Hex("123456"));//设置默认密码
        sysUser.setUsertype(DictEnum.CD.code);
        sysUser.setAcctype(DictEnum.PhoneNo.code);
        sysUser.setRegnode(DictEnum.ACCAPCOM.code);
        sysUser.setDatafrom(vo.getDatafrom());
        sysUser.setEnable(false);
        sysUser.setIfAgreement(false);
        Integer userId = null;
        ResultUtil resultUtil = systemAPI.addUser(sysUser);
        if (null != resultUtil.getCode()){
            if (resultUtil.getCode().equals("error")){
                if (null != resultUtil.getMsg()){
                    throw new RuntimeException(resultUtil.getMsg());
                } else {
                    throw new RuntimeException("添加失败");
                }
            } else if (resultUtil.getCode().equals("success")){
                Object data = resultUtil.getData();
                if (null != data){
                    LinkedHashMap user = (LinkedHashMap) data;
                    userId = (Integer) user.get("id");
                }
            }
        }

        //account新增用户账号
        TAccount ac = new TAccount();
        ac.setNickname(vo.getCardOwner());
        ac.setAccountNo(vo.getCardOwnerPhone());
        ac.setUserId(userId);
        ac.setPassword(sysUser.getPassword());
        ac.setAcctype(sysUser.getAcctype());
        ac.setUsertype(sysUser.getUsertype());
        ac.setRegnode(sysUser.getRegnode());
        ac.setDatafrom(sysUser.getDatafrom());
        ac.setEnable(false);
        tAccountMapper.insertSelective(ac);

        TEnduserAccount tEnduserAccount = new TEnduserAccount();
        tEnduserAccount.setAccountId(ac.getId());
        tEnduserAccount.setEnduserId(endUserInfo.getId());
        tEnduserAccount.setRealName(endUserInfo.getRealName());
        tEnduserAccount.setIdcard(endUserInfo.getIdcard());
        tEnduserAccount.setPhone(vo.getCardOwnerPhone());
        tEnduserAccount.setEnable(false);
        tEnduserAccountMapper.insert(tEnduserAccount);

        //保存用户信息表
        TUserInfo tUserInfo = new TUserInfo();
        tUserInfo.setAccountId(userId);
        tUserInfo.setStatus(DictEnum.ACCOUNTENABLE.code);
        tUserInfo.setPasswordErrorCount(0);
        tUserInfo.setEnable(false);
        systemAPI.insertUserInfo(tUserInfo);

        // 银行卡
        TBankCard bankCard = new TBankCard();
        bankCard.setAccountId(ac.getId());
        bankCard.setCardNo(vo.getCardNo());
        bankCard.setCardOwner(vo.getCardOwner());
        bankCard.setCardOwnerPhone(vo.getCardOwnerPhone());
        bankCard.setCardOwnerIdcard(vo.getCardOwnerIdcard());
        bankCard.setCardOwnerIdcardPhoto1(vo.getIdcardPhoto1());
        bankCard.setCardOwnerIdcardPhoto2(vo.getIdcardPhoto2());
        bankCard.setIdcardValidBeginning(vo.getIdcardValidBeginning());
        bankCard.setIdcardValidUntil(vo.getIdcardValidUntil());
        bankCard.setIfDefault(1);
        bankCard.setAddress(vo.getAddress());
        bankCard.setEnable(false);
        tBankCardMapper.insertSelective(bankCard);

        TEndUserOpenRoleSearchVo tEndUserOpenRoleSearchVo = new TEndUserOpenRoleSearchVo();
        tEndUserOpenRoleSearchVo.setEndUserId(endUserInfo.getId());
        List<TEndUserOpenRoleVo> tEndUserOpenRoleVoList = tEndUserOpenRoleService.selectByPage(tEndUserOpenRoleSearchVo);
        tEndUserOpenRoleVoList.get(0).setBankCardId(bankCard.getId());
        tEndUserOpenRoleVoList.get(0).setOpenRoleCardNo(vo.getCardNo());
        tEndUserOpenRoleVoList.get(0).setOpenRoleCardOwnerPhone(vo.getCardOwnerPhone());

        if(tEndUserOpenRoleVoList.get(0).getIdcardValidBeginning().getTime() > tEndUserOpenRoleVoList.get(0).getIdcardValidUntil().getTime()){
            Date idcardValidBeginning = tEndUserOpenRoleVoList.get(0).getIdcardValidBeginning();
            tEndUserOpenRoleVoList.get(0).setIdcardValidBeginning(tEndUserOpenRoleVoList.get(0).getIdcardValidUntil());
            tEndUserOpenRoleVoList.get(0).setIdcardValidUntil(idcardValidBeginning);
        }
        ResultUtil openRoleResultUtil = tEndUserOpenRoleService.selectDriveOpenRole(tEndUserOpenRoleVoList.get(0));

        if (null != openRoleResultUtil.getCode()){
            if (openRoleResultUtil.getCode().equals("error")){
                if (null != openRoleResultUtil.getMsg()){
                    throw new RuntimeException(openRoleResultUtil.getMsg());
                } else {
                    throw new RuntimeException("用户添加添加失败!");
                }
            }
        }
    }

    @Override
    public ResultUtil selectTransferorPage(TEndUserInfoSearchVO vo) {
        Page<Object> objectPage = PageHelper.startPage(vo.getPage(), vo.getSize());
        tEndUserInfoMapper.selectTransferorPage(vo);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),objectPage,objectPage.getTotal());
    }

    @Override
    public ResultUtil selectTransferorInfo(Integer endUserId) {
        TransferorInfoDTO transferorInfoDTO = tEndUserInfoMapper.selectTransferorInfoById(endUserId);
        List<TBankCard> tBankCardList = tBankCardMapper.getBankCardListByAccountId(transferorInfoDTO.getAccountId());
        if(null != tBankCardList && tBankCardList.size() > 0){
            transferorInfoDTO.setCardNo(tBankCardList.get(0).getCardNo());
        }
        return ResultUtil.ok(transferorInfoDTO);
    }

    @Transactional
    @Override
    public ResultUtil updateTransferorInfo(TransferorInfoVO vo) {
        TEndUserInfo endUserInfo = tEndUserInfoMapper.selectByPrimaryKey(vo.getId());
        if (null == endUserInfo) {
            return ResultUtil.error("转账人不存在");
        }

        // 修改系统账号并检测手机号是否已存在
        TAccount tAccount = tAccountMapper.selectByAccountNo(endUserInfo.getPhone());
        TSysUserVO sysUserVO = new TSysUserVO();
        sysUserVO.setId(tAccount.getUserId());
        sysUserVO.setUsername(vo.getPhone());
        sysUserVO.setNickname(vo.getRealName());
        sysUserVO.setAccountNo(vo.getPhone());
        systemAPI.updateUserByAccountNo(sysUserVO);
        // 修改账号表
        tAccount.setAccountNo(vo.getPhone());
        tAccount.setNickname(vo.getRealName());
        tAccount.setUpdateUser(CurrentUser.getUserNickname());
        tAccount.setUpdateTime(new Date());
        tAccountMapper.updateByPrimaryKeySelective(tAccount);
        // 修改
        TEnduserAccount tEnduserAccount = tEnduserAccountMapper.selectByEndUserId(vo.getId());
        tEnduserAccount.setIdcard(vo.getIdcard());
        tEnduserAccount.setPhone(vo.getPhone());
        tEnduserAccount.setRealName(vo.getRealName());
        tEnduserAccount.setUpdateUser(CurrentUser.getUserNickname());
        tEnduserAccount.setUpdateTime(new Date());
        tEnduserAccountMapper.updateByPrimaryKeySelective(tEnduserAccount);

        endUserInfo.setRealName(vo.getRealName());
        endUserInfo.setAddress(vo.getAddress());
        endUserInfo.setAddressState(true);
        endUserInfo.setIdcard(vo.getIdcard());
        endUserInfo.setPhone(vo.getPhone());
        endUserInfo.setIdcardPhoto1(vo.getIdcardPhoto1());
        endUserInfo.setIdcardPhoto2(vo.getIdcardPhoto2());
        endUserInfo.setIdcardValidBeginning(vo.getIdcardValidBeginning());
        endUserInfo.setIdcardValidUntil(vo.getIdcardValidUntil());
        endUserInfo.setUpdateUser(CurrentUser.getUserNickname());
        endUserInfo.setUpdateTime(new Date());
        tEndUserInfoMapper.updateByPrimaryKeySelective(endUserInfo);

        TBankCard bankCard = tBankCardMapper.selectByPrimaryKey(vo.getCardId());
        bankCard.setAddress(vo.getAddress());
        bankCard.setCardNo(vo.getCardNo());
        bankCard.setCardOwnerIdcard(vo.getIdcard());
        bankCard.setCardOwnerPhone(vo.getPhone());
        bankCard.setIdcardValidBeginning(vo.getIdcardValidBeginning());
        bankCard.setIdcardValidUntil(vo.getIdcardValidUntil());
        bankCard.setCardOwnerIdcardPhoto1(vo.getIdcardPhoto1());
        bankCard.setCardOwnerIdcardPhoto2(vo.getIdcardPhoto2());
        bankCard.setUpdateUser(CurrentUser.getUserNickname());
        bankCard.setUpdateTime(new Date());
        tBankCardMapper.updateByPrimaryKeySelective(bankCard);

        return ResultUtil.ok();
    }

    @Override
    public ResultUtil transferorIdentityChange(Integer id,String userLogisticsRole){
        if(null == id || null == userLogisticsRole){
            return  ResultUtil.error("身份变更失败，请检查信息是否齐全。");
        }
        TEndUserInfo endUserInfo = tEndUserInfoMapper.selectByPrimaryKey(id);
        if (null == endUserInfo) {
            return ResultUtil.error("转账人不存在");
        }

        TAccount tAccount = tEndUserInfoMapper.selectUserIdByEnduserId(id);

        TEndUserInfo tEndUserInfo = new TEndUserInfo();
        tEndUserInfo.setId(id);
        tEndUserInfo.setUserLogisticsRole(userLogisticsRole);
        if(userLogisticsRole.equals("CTYPEDRVIVER")){
            tEndUserInfo.setAuditStatus("NOTPASSNODE");
            tEndUserInfo.setAuditOpinion("驾驶证、从业资格证、证件不齐全，请补齐后重新提交。");
            //库里以前存反了--现在只能反着用了
            tEndUserInfo.setIdcardValidBeginning(endUserInfo.getIdcardValidUntil());
            tEndUserInfo.setIdcardValidUntil(endUserInfo.getIdcardValidBeginning());
        }else if(userLogisticsRole.equals("CTYPECAPTAIN")){
            tEndUserInfo.setAuditStatus("MIDNODE");
            //司机添加车队长身份-给车队长添加角色
            TSysRole tSysRole =  systemAPI.selectByParam("cdz");
            if(tSysRole!=null){
                TUserRole tUserRole = new TUserRole();
                tUserRole.setUserId(tAccount.getUserId());
                tUserRole.setRoleId(tSysRole.getId());
                systemAPI.saveUserRole(tUserRole);
            }
            //库里以前存反了--现在只能反着用了
            tEndUserInfo.setIdcardValidBeginning(endUserInfo.getIdcardValidUntil());
            tEndUserInfo.setIdcardValidUntil(endUserInfo.getIdcardValidBeginning());
        }else if(userLogisticsRole.equals("CTYPEAGENTPERSON")){
            TSysRole tSysRole =  systemAPI.selectByParam("JJR");
            if(tSysRole!=null){
                TUserRole tUserRole = new TUserRole();
                tUserRole.setUserId(tAccount.getUserId());
                tUserRole.setRoleId(tSysRole.getId());
                systemAPI.saveUserRole(tUserRole);
            }
        }else {
            return  ResultUtil.error("身份变更失败，请检查信息是否正确。");
        }

        tEndUserInfoMapper.updateByPrimaryKeySelective(tEndUserInfo);

        TEndUserStatus tEndUserStatus = new TEndUserStatus();
        tEndUserStatus.setUserStatus("DISTRIBUTIONREGISTERED");
        tEndUserStatus.setEnable(false);
        tEndUserStatus.setCurrentDriverAccountNo(endUserInfo.getPhone());
        tEndUserStatus.setEnduserId(endUserInfo.getId());
        tEndUserStatus.setCreateUser(endUserInfo.getRealName());
        tEndUserStatus.setCreateTime(new Date());
        tEndUserStatusMapper.insertSelective(tEndUserStatus);

        TJdWallet tJdWallet = tJdWalletMapper.selectWalletByEndUserId(id,DictEnum.TRANSFEROR.code);
        if(null != tJdWallet && null != tJdWallet.getId()){
            if(DictEnum.CTYPEDRVIVER.code.equals(userLogisticsRole)){
                tJdWallet.setPurseCategory(DictEnum.CDRIVER.code);
            }else if(DictEnum.CTYPEAGENTPERSON.code.equals(userLogisticsRole)){
                tJdWallet.setPurseCategory(DictEnum.CMANAGER.code);
            }else if(DictEnum.CTYPECAPTAIN.code.equals(userLogisticsRole)){
                tJdWallet.setPurseCategory(DictEnum.CCAPTAIN.code);
            }else if(DictEnum.CTYPETRANSFEROR.code.equals(userLogisticsRole)){
                tJdWallet.setPurseCategory(DictEnum.TRANSFEROR.code);
            }
            tJdWalletMapper.updateByPrimaryKeySelective(tJdWallet);
        }
        return ResultUtil.ok();
    }

    @Override
    public ResultUtil perfectCardInfo(TransferorInfoQueryVO vo) {
        return ResultUtil.ok(tEndUserInfoMapper.selectTransferorInfo(vo.getEndUserId(), vo.getCardId()));
    }

    @Override
    public List<TEndUserInfo> selectByPhoneAndIdcard(String phone, String Idcard) {
        return tEndUserInfoMapper.selectByPhoneAndIdcard(phone, Idcard);
    }

    @Override
    public List<TEndUserInfo> selectByIdcard(String Idcard) {
        return tEndUserInfoMapper.selectByIdcard(Idcard);
    }

    @Override
    public List<TEndUserInfo> selectIdcardUserLogisticsRole(String idcard, String userLogisticsRole) {
        return tEndUserInfoMapper.selectIdcardUserLogisticsRole(idcard,userLogisticsRole);
    }

    @Override
    public ResultUtil updateUserOpenRoleInfo(TEndUserInfo tEndUserInfo) {
        if(tEndUserInfo.getIdcardValidUntil().getTime() > tEndUserInfo.getIdcardValidBeginning().getTime()){
            Date idcardValidBeginning = tEndUserInfo.getIdcardValidBeginning();
            tEndUserInfo.setIdcardValidBeginning(tEndUserInfo.getIdcardValidUntil());
            tEndUserInfo.setIdcardValidUntil(idcardValidBeginning);
        }
        tEndUserInfoMapper.updateByPrimaryKeySelective(tEndUserInfo);
        return ResultUtil.ok();
    }

    @Override
    public ResultUtil userInfoIfComplete(Integer endUserId) {
        ResultUtil resultUtil = new ResultUtil();
        Integer ifComplete = 0;
        TEndUserInfo tEndUserInfo = tEndUserInfoMapper.selectByPrimaryKey(endUserId);
        if(null  == tEndUserInfo.getIdcard() || tEndUserInfo.getIdcard().equals("")){
             ifComplete = 1;
        }
        if(null == tEndUserInfo.getIdcardValidBeginning() || tEndUserInfo.getIdcardValidBeginning().equals("")){
             ifComplete = 1;
        }
        if(null == tEndUserInfo.getIdcardValidUntil() || tEndUserInfo.getIdcardValidUntil().equals("")){
            ifComplete = 1;
        }else {
            if(tEndUserInfo.getIdcardValidBeginning().getTime() <= new Date().getTime()){
                ifComplete = 1;
            }
        }
        if(null == tEndUserInfo.getAddress() || tEndUserInfo.getAddress().equals("")){
            ifComplete = 1;
        }
        resultUtil.setCode(CodeEnum.SUCCESS.getCode());
        resultUtil.setMsg("成功");
        resultUtil.setData(ifComplete);
        return resultUtil;
    }

    @Override
    public ResultUtil insertOcrOpenRoleInfo(TOcrOpenRole tOcrOpenRole) {
        tOcrOpenRoleMapper.insertSelective(tOcrOpenRole);
        return ResultUtil.ok();
    }

    /**
    * @description 查询持卡人角色
    * <AUTHOR>
    * @date 2021/11/23 13:33
    */
    @Override
    public String queryCardOwnerUserLogisticsRole(String cardOwnerPhone, String cardOwnerIdcacrd) {
        // 1. 查询手机号和身份证号是否同一人
        List<TEndUserInfo> userInfos = tEndUserInfoMapper.selectByPhoneAndIdcard(cardOwnerPhone, cardOwnerIdcacrd);
        if (null != userInfos && !userInfos.isEmpty()) {
            // 2. 查询身份证是否存在
            List<TEndUserInfo> tEndUserInfos = tEndUserInfoMapper.selectByIdcard(cardOwnerIdcacrd);
            if (null != tEndUserInfos && !tEndUserInfos.isEmpty()) {
                // 2.1 检测信息是否一致
                // 取司机
                List<TEndUserInfo> list = tEndUserInfos
                        .stream()
                        .filter(info -> info.getUserLogisticsRole().contains(DictEnum.CTYPEDRVIVER.code))
                        .collect(Collectors.toList());
                if (null != list && !list.isEmpty()) {
                    return DictEnum.CTYPEDRVIVER.code;
                } else {
                    // 取车队长
                    list = tEndUserInfos
                            .stream()
                            .filter(info -> info.getUserLogisticsRole().contains(DictEnum.CTYPECAPTAIN.code))
                            .collect(Collectors.toList());
                    if (null != list && !list.isEmpty()) {
                        return DictEnum.CTYPECAPTAIN.code;
                    } else {
                        // 取转账人
                        list = tEndUserInfos
                                .stream()
                                .filter(info -> info.getUserLogisticsRole().contains(DictEnum.CTYPETRANSFEROR.code))
                                .collect(Collectors.toList());
                        if (null != list && !list.isEmpty()) {
                            return DictEnum.CTYPETRANSFEROR.code;
                        }
                    }
                }
            }
        }
        return "";
    }

    @Override
    public String queryCardOwnerWhetherSelfOpenRole(Integer bankCardId) {
        TBankCard bankCard = tBankCardMapper.selectByPrimaryKey(bankCardId);
        // 根据身份证号查询账户是否存在
        List<TEndUserInfo> tEndUserInfos = tEndUserInfoMapper.selectByIdcard(bankCard.getCardOwnerIdcard());
        if (null != tEndUserInfos && !tEndUserInfos.isEmpty()) {
            // 账户存在
            // 是否转账人
            List<TEndUserInfo> results = tEndUserInfos.stream()
                    .filter((enduserInfo) -> DictEnum.CTYPETRANSFEROR.code.equals(enduserInfo.getUserLogisticsRole()))
                    .collect(Collectors.toList());
            if (!results.isEmpty()) {
                return DictEnum.TRANSFERORACCOUNT.code;
            }
            // 查询账户是否本人开户
            List<TEndUserOpenRoleVo> endUserOpenRole = tEndUserOpenRoleMapper.selectEnduserOpenRole(bankCard.getCardOwnerIdcard());
            List<TEndUserOpenRoleVo> openRoleVos = endUserOpenRole.stream()
                    .filter((enduserOpenRoleVo) -> null != enduserOpenRoleVo && !enduserOpenRoleVo.getIfOneselfOpen()).collect(Collectors.toList());
            if (!openRoleVos.isEmpty()) {
                return DictEnum.MYACCOUNT.code;
            }
        }
        // 非本人开户
        TEndUserOpenRole endUserOpenRole = tEndUserOpenRoleMapper.selectByOpenIdCard(bankCard.getCardOwnerIdcard());
        if (null != endUserOpenRole) {
            return DictEnum.NONPERSONALACCOUNT.code;
        }

        return "";
    }

    @Override
    public TEndUserOpenRole queryEnduserOpenRoleStatus(Integer enduserId) {
        return tEndUserOpenRoleMapper.selectByEndUserId(enduserId);
    }


    @Override
    public List<TEndCarInfo> queryEnduserAllCar(Integer enduserId) {
         return tEndCarInfoMapper.selectByEndUserId(enduserId);
    }

    @Override
    public TEndUserInfoVO driverIdentityVerificationStatus(String phone) {
        log.info("角色身份："+CurrentUser.getUserLogisticsRole());
        TEndUserInfoVO tEndUserInfoVO = tEndUserInfoMapper.driverIdentityVerificationStatus(phone,CurrentUser.getUserLogisticsRole());
        if(null == tEndUserInfoVO || null == tEndUserInfoVO.getId()){

            TEndUserInfo tEndUserInfoVO1 = tEndUserInfoMapper.selectenduserinfoByPhoneRole(phone,CurrentUser.getUserLogisticsRole());

            if(null == tEndUserInfoVO1 || null == tEndUserInfoVO1.getId()){
                return tEndUserInfoVO;
            }
            tEndUserInfoVO = new TEndUserInfoVO();
            if(null != tEndUserInfoVO1.getId()){
                tEndUserInfoVO.setId(tEndUserInfoVO1.getId());
            }
            if(null != tEndUserInfoVO1.getAuditStatus()){
                tEndUserInfoVO.setAuditStatus(tEndUserInfoVO1.getAuditStatus());
            }
            if(null != tEndUserInfoVO1.getIdcard()){
                tEndUserInfoVO.setIdcard(tEndUserInfoVO1.getIdcard());
            }
            if(null != tEndUserInfoVO1.getRealName()){
                tEndUserInfoVO.setRealName(tEndUserInfoVO1.getRealName());
            }
        }
        return tEndUserInfoVO;
    }

    @Override
    public ResultUtil updateDriverPhone(String phone,String newphone) {
        log.info("角色身份："+CurrentUser.getUserLogisticsRole());
        TEndUserInfoVO tEndUserInfoVO = new TEndUserInfoVO();
        tEndUserInfoVO = tEndUserInfoMapper.driverIdentityVerificationStatus(phone,CurrentUser.getUserLogisticsRole());
        if(null == tEndUserInfoVO || null == tEndUserInfoVO.getId()){

            TEndUserInfo tEndUserInfoVO1 = tEndUserInfoMapper.selectenduserinfoByPhoneRole(phone,CurrentUser.getUserLogisticsRole());
            if(null == tEndUserInfoVO1 || null == tEndUserInfoVO1.getId()){
                return ResultUtil.error();
            }
            tEndUserInfoVO = new TEndUserInfoVO();
            tEndUserInfoVO.setId(tEndUserInfoVO1.getId());
        }
        TEnduserAccount tEnduserAccount = tEnduserAccountMapper.selectByEndUserId(tEndUserInfoVO.getId());
        if(null == tEnduserAccount || null == tEnduserAccount.getAccountId()){
            return ResultUtil.error();
        }
        List<TEndUserInfo> tEndUserInfos = tEndUserInfoMapper.findphone(phone);
        if(null != tEndUserInfos && tEndUserInfos.size() > 0){
            for (TEndUserInfo tEndUserIn: tEndUserInfos){
                if(null != tEndUserIn && null != tEndUserIn.getId() && !tEndUserIn.getId().equals(tEndUserInfoVO.getId())){
                    TEndUserInfo tEndUserInfoUpdate = new TEndUserInfo();
                    tEndUserInfoUpdate.setId(tEndUserIn.getId());
                    tEndUserInfoUpdate.setPhone(newphone);
                    tEndUserInfoMapper.updateByPrimaryKeySelective(tEndUserInfoUpdate);
                }
            }
        }

        tEndUserInfoMapper.updateDriverPhone(phone,newphone,CurrentUser.getUserLogisticsRole());
        tEnduserAccountMapper.updateDriverPhone(tEndUserInfoVO.getId(),newphone);
        tAccountMapper.updateDriverPhone(tEnduserAccount.getAccountId(),newphone);
        return ResultUtil.ok();
    }

    @Override
    public DriverInfoDto getDriverVehicleInfo(DriverQueryParam param) {
        DriverInfoDto driverInfo = tEndUserInfoMapper.getDriverInfo(param);
        if (null != driverInfo) {
            List<VehicleInfo> driverVehicleInfo = tEndUserInfoMapper.getDriverVehicleInfo(param);
            if (null != driverVehicleInfo && !driverVehicleInfo.isEmpty()) {
                driverInfo.setVehicleInfos(driverVehicleInfo);
            }
            return driverInfo;
        }
        return null;
    }

    @Override
    public TEndUserInfo getDataByPhoneAndIdcard(String phone, String idcard, String realName) {

        return tEndUserInfoMapper.getDataByPhoneAndIdcard(phone,idcard,realName);
    }

}
