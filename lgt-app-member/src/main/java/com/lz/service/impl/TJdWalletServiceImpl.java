package com.lz.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jd.jr.jropen.sdk.model.message.AsyncNotifyVirtualMemberRechargeMessageBody;
import com.jd.jr.jropen.unifySdk.respModel.CustomerBalancePayResponse;
import com.jd.jr.jropen.unifySdk.respModel.OpenMemberBindAccountResponse;
import com.jd.jr.jropen.unifySdk.respModel.OpenMemberUnbindAccountResponse;
import com.jd.jr.jropen.unifySdk.respModel.VitualAccountVerifyResponse;
import com.jd.jr.jropen.unifySdk.respModel.test.BindCardAmountResponse;
import com.lz.api.MqAPI;
import com.lz.api.TOrderPayDetailAPI;
import com.lz.api.TOrderPayInfoAPI;
import com.lz.common.constants.MqMessageTag;
import com.lz.common.constants.MqMessageTopic;
import com.lz.common.dbenum.*;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.CodeEnum;
import com.lz.common.model.MQMessage;
import com.lz.common.model.jdPayment.request.bank.OpenMemberBindAccountReq;
import com.lz.common.model.jdPayment.request.bank.OpenMemberUnbindAccountReq;
import com.lz.common.model.jdPayment.request.bank.QueryVerifyAmtReq;
import com.lz.common.model.jdPayment.request.bank.VitualAccountVerifyReq;
import com.lz.common.model.jdPayment.request.pay.CustomerBalancePayReq;
import com.lz.common.model.jdPayment.util.CloudPayFormatUtil;
import com.lz.common.util.*;
import com.lz.dao.*;
import com.lz.example.TServicefeeRecordExample;
import com.lz.jdpay.CloudPayAPI;
import com.lz.model.*;
import com.lz.service.TEndUserOpenRoleService;
import com.lz.service.TJdWalletService;
import com.lz.service.TServicefeeInfoService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;

/**
 *  @author: dingweibo
 *  @Date: 2021/8/9 10:03
 *  @Description: 京东钱包
 */
@Service("tJdWalletService")
@Slf4j
public class TJdWalletServiceImpl implements TJdWalletService {

    @Resource
    private TBankCardMapper tBankCardMapper;

    @Resource
    private TJdBankCardMapper tJdBankCardMapper;

    @Resource
    private TCarrierCompanyOpenRoleMapper tCarrierCompanyOpenRoleMapper;

    @Resource
    private TJdWalletMapper tJdWalletMapper;

    @Resource
    private TJdWalletChangeLogMapper tJdWalletChangeLogMapper;

    @Resource
    private TCompanyInfoMapper tCompanyInfoMapper;

    @Autowired
    private CloudPayAPI cloudPayAPI;
    @Autowired
    private SysParamAPI sysParamAPI;
    @Autowired
    private TOrderPayDetailAPI tOrderPayDetailAPI;
    @Autowired
    private TOrderPayInfoAPI tOrderPayInfoAPI;
    @Resource
    private TServicefeeRecordMapper tServicefeeRecordMapper;

    @Autowired
    private TEndUserOpenRoleService tEndUserOpenRoleService;

    @Autowired
    private TEndUserInfoMapper tEndUserInfoMapper;

    @Autowired
    private TServicefeeInfoService tServicefeeInfoService;

    @Value("${partnerId}")
    private String partnerId;
    @Value("${merchantCode}")
    private String merchantCode;
    @Resource
    private TJdBizAmountMapper tJdBizAmountMapper;

    @Autowired
    private MqAPI mqAPI;

    @Autowired
    private TOrderPayInfoAPI orderPayInfoAPI;

    @Autowired
    private TOrderPayDetailAPI orderPayDetailAPI;

    @Override
    public ResultUtil selectByStatus(TCarrierCompanyOpenRoleVo record) {
        TCarrierCompanyOpenRole tCarrierCompanyOpenRole = tCarrierCompanyOpenRoleMapper.selectByStatus(record);
        tCarrierCompanyOpenRole.setOpenStatus(JdEnumStr.getDesc(tCarrierCompanyOpenRole.getOpenStatus()));
        tCarrierCompanyOpenRole.setPaymentStatus(JdEnumStr.getDesc(tCarrierCompanyOpenRole.getPaymentStatus()));
        return ResultUtil.ok(tCarrierCompanyOpenRole);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/9 10:04
     *  @Description: 企业或承运方绑卡
     */
    @Override
    public ResultUtil bindingBank(TJdBankCardVo record) {
        List<TBankCard> tBankCardList = tBankCardMapper.selectByCardNo(record.getAcctNo());
        if(tBankCardList.size()>0){
            return ResultUtil.error("此卡已绑定，请勿重复绑定！");
        }
        TCarrierCompanyOpenRole tCarrierCompanyOpenRole = tCarrierCompanyOpenRoleMapper.selectByPrimaryKey(record.getOpenRoleId());
        List<TJdBankCardVo> tJdBankCardVoList= tJdBankCardMapper.selectByRoleTypeAndComId(tCarrierCompanyOpenRole.getUserOpenRole(),record.getOpenRoleId());
        if(tJdBankCardVoList.size()==5){
            return ResultUtil.error("仅支持添加5个对公账户，可删除无用对公户后，再添加。");
        }
        OpenMemberBindAccountReq req = new OpenMemberBindAccountReq();
        req.setPartnerId(partnerId);
        String requestId = "com"+ IdWorkerUtil.getInstance().nextId().substring(3,32);
        req.setRequestId(requestId);
        req.setRequestTime(DateUtils.getRequestTime());
        req.setPartnerMemberId(tCarrierCompanyOpenRole.getUserOpenRole()+tCarrierCompanyOpenRole.getCarrierCompanyId());
        req.setChannelId(JdEnum.channelId.code);
        req.setPartnerAccId(tCarrierCompanyOpenRole.getPartnerAccId());
        req.setAcctType(record.getAcctType());
        req.setAcctNo(record.getAcctNo());
        req.setAcctName(record.getAcctName());
        req.setBankCode(record.getBankCode());
        req.setAcctUnionBankCode(record.getAcctUnionBankCode());
        ResultUtil resultUtil1 = cloudPayAPI.cloudPay(req);
        OpenMemberBindAccountResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, OpenMemberBindAccountResponse.class);
        TBankCard bank = new TBankCard();
        TJdBankCard jdBank = new TJdBankCard();
        if(response.getResponseCode().equals(JdEnum.JDSUCCESSCODE.code)){
            bank.setCardOwner(record.getAcctName());
            bank.setCardNo(record.getAcctNo());
            bank.setBankCode(record.getBankCode());
            bank.setBankCardCategroy(req.getAcctType());
            bank.setSubCode(record.getAcctUnionBankCode());
            tBankCardMapper.insertSelective(bank);
            jdBank.setCode(requestId);
            jdBank.setBankCardId(bank.getId());
            jdBank.setUserOpenRole(tCarrierCompanyOpenRole.getUserOpenRole());
            jdBank.setOpenRoleId(record.getOpenRoleId());
            jdBank.setBindStatus(response.getBindStatus());
            jdBank.setPaymentStatus(response.getBindStatus());
            jdBank.setPaymentResponseDesc(response.getResponseDesc());
            jdBank.setCardId(response.getCardId());
            tJdBankCardMapper.insertSelective(jdBank);
            tCarrierCompanyOpenRole.setPaymentStatus(response.getBindStatus());
            tCarrierCompanyOpenRole.setUpdateTime(new Date());
            tCarrierCompanyOpenRoleMapper.updateByPrimaryKeySelective(tCarrierCompanyOpenRole);
            return ResultUtil.ok("对公账户信息完善成功");
        }else{
            return ResultUtil.error("对公账户信息完善失败"+response.getResponseDesc());
        }
    }


    /**
     *  @author: dingweibo
     *  @Date: 2022/5/11 9:27
     *  @Description: 承运方或企业解绑银行卡
     */
    @Override
    public ResultUtil unbindBank(TJdBankCardVo record) {
        TCarrierCompanyOpenRole tCarrierCompanyOpenRole = tCarrierCompanyOpenRoleMapper.selectByPrimaryKey(record.getOpenRoleId());
        TJdBankCard tJdBankCard =  tJdBankCardMapper.selectByCardId(record.getCardId());

        List<TJdBankCardVo> tJdBankCardVoList= tJdBankCardMapper.selectByRoleTypeAndComId(tCarrierCompanyOpenRole.getUserOpenRole(),record.getOpenRoleId());
        if(tJdBankCardVoList.size()==1){
            return ResultUtil.error("该会员只绑定了一张卡，不能进行解绑");
        }
        if(JdEnum.BIND_FAIL.equals(tJdBankCard.getPaymentStatus())){
                TBankCard tBankCard = tBankCardMapper.selectByPrimaryKey(tJdBankCard.getBankCardId());
                tBankCard.setEnable(true);
                tBankCardMapper.updateByPrimaryKeySelective(tBankCard);
                tJdBankCard.setEnable(true);
                tJdBankCardMapper.updateByPrimaryKeySelective(tJdBankCard);
            return ResultUtil.ok("删除成功");
        }else{
            OpenMemberUnbindAccountReq req = new OpenMemberUnbindAccountReq();
            req.setPartnerId(partnerId);
            String requestId = "com"+ IdWorkerUtil.getInstance().nextId().substring(3,32);
            req.setRequestId(requestId);
            req.setRequestTime(DateUtils.getRequestTime());
            req.setBizOrderNo(IdWorkerUtil.getInstance().nextId());
            req.setPartnerAccId(tCarrierCompanyOpenRole.getPartnerAccId());
            req.setCardId(tJdBankCard.getCardId());
            ResultUtil resultUtil1 = cloudPayAPI.cloudPay(req);
            OpenMemberUnbindAccountResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, OpenMemberUnbindAccountResponse.class);
            if(response.getResponseCode().equals(JdEnum.JDSUCCESSCODE.code)){
                return ResultUtil.ok("解绑银行卡申请中");
            }else{
                return ResultUtil.error("解绑银行卡失败"+response.getResponseDesc());
            }
        }
    }


    /**
     *  @author: dingweibo
     *  @Date: 2021/8/9 11:29
     *  @Description: 打款验证
     */
    @Override
    public ResultUtil vitual(TJdBankCardVo record) {
        TCarrierCompanyOpenRole tCarrierCompanyOpenRole = tCarrierCompanyOpenRoleMapper.selectByPrimaryKey(record.getOpenRoleId());
        VitualAccountVerifyReq req = new VitualAccountVerifyReq();
        req.setPartnerId(partnerId);
        String requestId = "com"+ IdWorkerUtil.getInstance().nextId().substring(3,32);
        req.setRequestId(requestId);
        req.setRequestTime(DateUtils.getRequestTime());
        req.setPartnerAccId(tCarrierCompanyOpenRole.getPartnerAccId());
        req.setCardId(record.getCardId());
        req.setReceiveAmt(String.valueOf(record.getAmount().multiply(new BigDecimal(100)).setScale(0,BigDecimal.ROUND_DOWN)));
        ResultUtil resultUtil1 = cloudPayAPI.cloudPay(req);
        VitualAccountVerifyResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, VitualAccountVerifyResponse.class);
        if(response.getResponseCode().equals(JdEnum.JDSUCCESSCODE.code)){
           if(response.getStatus().equals("FAIL")){
                tCarrierCompanyOpenRole.setPaymentStatus(JdEnum.PAYMENTERROR.code);
                tCarrierCompanyOpenRole.setPaymentResponseDesc(response.getResponseDesc());
                tCarrierCompanyOpenRoleMapper.updateByPrimaryKeySelective(tCarrierCompanyOpenRole);
           }
            return ResultUtil.ok("打款验证成功");
        }else {
            return ResultUtil.error("打款验证失败"+response.getResponseDesc());
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/5/11 14:57
     *  @Description: 对公账户查询回显（承运方与企业）
     */
    @Override
    public TJdBankCardVo selectJdBankCardId(TJdBankCardVo record) {
        TJdBankCard tJdBankCard = tJdBankCardMapper.selectByCardId(record.getCardId());
        TBankCard bankCard = tBankCardMapper.selectByPrimaryKey(tJdBankCard.getBankCardId());
        TCarrierCompanyOpenRole tCarrierCompanyOpenRole = tCarrierCompanyOpenRoleMapper.selectByPrimaryKey(tJdBankCard.getOpenRoleId());
        TJdBankCardVo jdBankCardVo = new TJdBankCardVo();
        jdBankCardVo.setAcctName(bankCard.getCardOwner());
        jdBankCardVo.setAcctNo(bankCard.getCardNo());
        jdBankCardVo.setBankCode(bankCard.getBankCode());
        jdBankCardVo.setAcctType(bankCard.getBankCardCategroy());
        jdBankCardVo.setAcctUnionBankCode(bankCard.getSubCode());
        jdBankCardVo.setOpenRoleId(tCarrierCompanyOpenRole.getId());
        jdBankCardVo.setUserOpenRole(tCarrierCompanyOpenRole.getUserOpenRole());
        jdBankCardVo.setPaymentStatus(JdEnumStr.getDesc(tJdBankCard.getPaymentStatus()));
        return jdBankCardVo;
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/5/11 10:39
     *  @Description: 对公账户管理页面（承运方与企业）
     */
    @Override
    public ResultUtil jdBankCardList(TJdBankCardVo record) {
        TCarrierCompanyOpenRole tCarrierCompanyOpenRole = tCarrierCompanyOpenRoleMapper.selectByPrimaryKey(record.getOpenRoleId());
        List<TJdBankCardVo> tJdBankCardList =  tJdBankCardMapper.selectByRoleTypeAndComId(tCarrierCompanyOpenRole.getUserOpenRole(),record.getOpenRoleId());
        return ResultUtil.ok(tJdBankCardList);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/17 19:08
     *  @Description: 查询打款验证金额接口
     */
    @Override
    public ResultUtil queryVerifyAmount(QueryVerifyAmtReq req) {
        req.setPartnerId(partnerId);
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        ResultUtil resultUtil1 = cloudPayAPI.cloudPay(req);
        BindCardAmountResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, BindCardAmountResponse.class);
        return ResultUtil.ok(response);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/9 15:11
     *  @Description: 承运方京东钱包列表查询
     */
    @Override
    public ResultUtil selectByCarrierWalletPage(TCarrierCompanyOpenRoleSearchVo record) {
        Page<Object> objectPage = PageHelper.startPage(record.getPage(), record.getSize());
        List<TCarrierCompanyOpenRoleVo> list = tJdWalletMapper.selectByCarrierWalletPage(record);
        for(TCarrierCompanyOpenRoleVo vo:list){
            vo.setUserOpenRole(DictEnum.CA.code);
            if(vo.getOpenStatus()!=null && vo.getPaymentStatus()!=null){
                if(vo.getOpenStatus().equals(JdEnum.OPENSUCCESS.code) && vo.getPaymentStatus().equals(JdEnum.BIND.code)){
                    vo.setAccountStatus("可用");
                }else{
                    vo.setAccountStatus("不可用");
                }
            }else{
                vo.setAccountStatus("不可用");
            }
            vo.setOpenStatus(JdEnumStr.getDesc(vo.getOpenStatus()));
            vo.setPaymentStatus(JdEnumStr.getDesc(vo.getPaymentStatus()));
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),objectPage,objectPage.getTotal());
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/9 19:15
     *  @Description: 承运方京东钱包流水查询
     */
    @Override
    public ResultUtil selectByCarrierWalletChangeLogPage(TCarrierCompanyOpenRoleSearchVo record) {
        Date[] tardeTime = record.getTradeTime();
        if (tardeTime != null) {
            record.setTradeStartTime(tardeTime[0]);
            record.setTradeEndTime(tardeTime[1]);
        }
        Double sumTxAmount = 0.0;
        List<TJdWalletChangeLogVo> listHj = tJdWalletChangeLogMapper.selectByCarrierWalletChangeLogPage(record);
        for(TJdWalletChangeLogVo tjdw :listHj) {
            if("(承运方)提现".equals(tjdw.getTradeType())){
                sumTxAmount +=Double.parseDouble(tjdw.getAmount().toString());
            }
        }
        Page<Object> objectPage = PageHelper.startPage(record.getPage(), record.getSize());
        List<TJdWalletChangeLogVo> list = tJdWalletChangeLogMapper.selectByCarrierWalletChangeLogPage(record);
        for(TJdWalletChangeLogVo map :list) {
            if(map.getTradeTime()!=null){
                map.setTradeTimeStr(DateUtils.formatDateTime((Date) map.getTradeTime()));
            }
        }
        if(list.size()>0){
            list.get(0).setSumTxAmount(sumTxAmount);
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),list,objectPage.getTotal());
    }


    /**
     *  @author: dingweibo
     *  @Date: 2022/6/13 14:16
     *  @Description: 承运方京东钱包流水导出
     */
    @Override
    public ResultUtil selectByCarrierWalletChangeLogExcel(TCarrierCompanyOpenRoleSearchVo record) {
        Date[] tardeTime = record.getTradeTime();
        if (tardeTime != null) {
            record.setTradeStartTime(tardeTime[0]);
            record.setTradeEndTime(tardeTime[1]);
        }
        List<TJdWalletChangeLogVo> list = tJdWalletChangeLogMapper.selectByCarrierWalletChangeLogPage(record);
        Map<String,Object> map = new HashMap<>();
        String[] headers =
                {
                        "交易流水号", "交易时间","交易类型","金额（元）","转出账号","转入账号","运单号"
                };
        String[] names =
                {
                        "tradeNo", "tradeTime","tradeType","amount","outPartnerAccId","inPartnerAccId","orderBusinessCode"
                };
        map.put("headers",headers);
        map.put("names",names);
        map.put("list",list);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), map);
    }
    /**
     *  @author: dingweibo
     *  @Date: 2021/8/10 10:00
     *  @Description: 京东 pc运营管理中财务管理 中的企业钱包详情
     */
    @Override
    public Map<String, Object> pcCompanyCwDetail(Integer companyId) {
        Map<String, Object> map = new HashMap<>();
        TJdWalletVo tJdWalletVo = new TJdWalletVo();
        tJdWalletVo.setAccountBalance(BigDecimal.ZERO);
        tJdWalletVo.setFrozenAmount(BigDecimal.ZERO);
        tJdWalletVo.setEntryAmount(BigDecimal.ZERO);
        tJdWalletVo.setCanBalance(BigDecimal.ZERO);
        TCarrierCompanyOpenRole tCarrierCompanyOpenRole = tCarrierCompanyOpenRoleMapper.selectByCardId(companyId);
        if(null == tCarrierCompanyOpenRole){
            return map;
        }
       // TBankCard tBankCard = tBankCardMapper.selectByPrimaryKey(tCarrierCompanyOpenRole.getBankCardId());
        TCompanyInfo tCompanyInfo = tCompanyInfoMapper.selectByPrimaryKey(companyId);
        if(null!=tJdWalletMapper.pcCompanyCwDetail(companyId)){
            tJdWalletVo = tJdWalletMapper.pcCompanyCwDetail(companyId);
        }
        if(null!=tCarrierCompanyOpenRole && !"".equals(tCarrierCompanyOpenRole)){
            tJdWalletVo.setPaymentStatus(JdEnumStr.getDesc(tCarrierCompanyOpenRole.getPaymentStatus()));
            tJdWalletVo.setPartnerAccId(tCarrierCompanyOpenRole.getPartnerAccId());
            tJdWalletVo.setOpenStatus(JdEnumStr.getDesc(tCarrierCompanyOpenRole.getOpenStatus()));
            tJdWalletVo.setOpenRoleId(tCarrierCompanyOpenRole.getId());
            tJdWalletVo.setUserOpenRole(tCarrierCompanyOpenRole.getUserOpenRole());
        }

        /*if(null!=tBankCard && !"".equals(tBankCard)){
            tJdWalletVo.setCardNo(tBankCard.getCardNo());
            tJdWalletVo.setCardOwner(tBankCard.getCardOwner());
            tJdWalletVo.setBankName(tBankCard.getBankName());

        }*/
        tJdWalletVo.setCompanyName(tCompanyInfo.getCompanyName());
        map.put("sum",tJdWalletVo);
        List<Map<String,Object>> listHj = tJdWalletMapper.walletHj(companyId);

        //企业项目额度
        //单笔提现钱
        List<TCompanyProjectVo> tCompanyProjectVoList = tJdWalletMapper.selectByCompanyProject(companyId);
        //打包支付提现
        List<TCompanyProjectVo> tCompanyProjectVoListPack = tJdWalletMapper.selectByCompanyProjectPack(companyId);
        //预支付提现
        List<TCompanyProjectVo> tCompanyProjectVoListAmp =tJdWalletMapper.selectByCompanyProjectAmp(companyId);
        //最终数据
        //查出后累加
        if(tCompanyProjectVoListPack.size()<1){
            for(TCompanyProjectVo v:tCompanyProjectVoList){
                if(v.getDispatchFeeCoefficientDg()!=null&&!"".equals(v.getDispatchFeeCoefficientDg())){
                    v.setDispatchFeeCoefficient(Double.parseDouble(v.getDispatchFeeCoefficientDg().toString()));
                }
            }
        }else{
            for(TCompanyProjectVo vo:tCompanyProjectVoList){
                if(vo.getDispatchFeeCoefficientDg()!=null&&!"".equals(vo.getDispatchFeeCoefficientDg())){
                    vo.setDispatchFeeCoefficient(Double.parseDouble(vo.getDispatchFeeCoefficientDg().toString()));
                }
                for(TCompanyProjectVo pack:tCompanyProjectVoListPack){
                    if((pack!=null && !"".equals(pack)) && (vo!=null && !"".equals(vo))){
                        log.info(JSONObject.toJSONString(pack)+"------------------vo:"+JSONObject.toJSONString(vo));
                        if(vo.getId().equals(pack.getId())){
                            vo.setNoCash(vo.getNoCash().add(pack.getNoCash()));
                            vo.setWithdrawal(vo.getWithdrawal().add(pack.getWithdrawal()));
                            vo.setAlreadyCash(vo.getAlreadyCash().add(pack.getAlreadyCash()));
                            vo.setDispatchFee(vo.getDispatchFee().add(pack.getDispatchFee()));
                        }
                    }
                }
                Date createTime = vo.getCreateTime();
                Date updateTime = vo.getUpdateTime();
                Date projectTime = null != updateTime ? updateTime : createTime;
                vo.setUpdateTime(projectTime);
            }
        }
        //预支付提现 相加
        if(tCompanyProjectVoListAmp.size()>0){
            for(TCompanyProjectVo vo:tCompanyProjectVoList){
                for(TCompanyProjectVo amp:tCompanyProjectVoListAmp){
                    if((amp!=null && !"".equals(amp)) && (vo!=null && !"".equals(vo))){
                        log.info(JSONObject.toJSONString(amp)+"------------------vo:"+JSONObject.toJSONString(vo));
                        if(vo.getId().equals(amp.getId())){
                            vo.setNoCash(vo.getNoCash().add(amp.getNoCash()));
                            vo.setWithdrawal(vo.getWithdrawal().add(amp.getWithdrawal()));
                            vo.setAlreadyCash(vo.getAlreadyCash().add(amp.getAlreadyCash()));
                            vo.setDispatchFee(vo.getDispatchFee().add(amp.getDispatchFee()));
                        }
                    }
                }

            }
        }
        map.put("projectList",tCompanyProjectVoList);
        tJdWalletVo.setCzHj(BigDecimal.ZERO);//合计充值
        tJdWalletVo.setZcHj(BigDecimal.ZERO);//合计支出
        Double zcHj = 0.0;
        Double czHj = 0.0;
        //(企业)充值 BCHONGZHI
        //(企业)调度费召回 BDIAODUFEIZH
        //(企业)调度费支出 BDIAODUFEIZHICHU
        //(企业)运费召回	BYUNFEIZH
        //(企业)运费支出	BYUNFEIZHICHU
        //累计支出 = 运费支出+调度费支出-运费召回-调度费召回
        for(Map<String, Object> map2:listHj){
            if(map2!=null ){
                if("BCHONGZHI".equals(map2.get("tradeTypeCode"))){//充值
                    czHj+=Double.parseDouble(map2.get("amount").toString());
                }
                if("BPAYZH".equals(map2.get("tradeTypeCode"))|| "BPAY".equals(map2.get("tradeTypeCode")) ||
                        "BD_SERVICE_PAY".equals(map2.get("tradeTypeCode")) || "CYKFJDZHICHU".equals(map2.get("tradeTypeCode"))){
                    if("BPAY".equals(map2.get("tradeTypeCode"))){//运费支出
                        if(map2.get("amount")!=null&&!"".equals(map2.get("amount"))){
                            zcHj+=Double.parseDouble(map2.get("amount").toString());
                        }
                    }
                    if("BD_SERVICE_PAY".equals(map2.get("tradeTypeCode"))){//提现手续费支出
                        if(map2.get("amount")!=null&&!"".equals(map2.get("amount"))){
                            zcHj+=Double.parseDouble(map2.get("amount").toString());
                        }
                    }
                    if("CYKFJDZHICHU".equals(map2.get("tradeTypeCode"))){//提现手续费支出
                        if(map2.get("amount")!=null&&!"".equals(map2.get("amount"))){
                            zcHj+=Double.parseDouble(map2.get("amount").toString());
                        }
                    }
                    if("BPAYZH".equals(map2.get("tradeTypeCode"))){//运费召回
                        if(map2.get("amount")!=null&&!"".equals(map2.get("amount"))){
                            zcHj-=Double.parseDouble(map2.get("amount").toString());
                        }
                    }
                }
            }
        }
        tJdWalletVo.setZcHj(new BigDecimal(zcHj));//合计支出
        tJdWalletVo.setCzHj(new BigDecimal(czHj));//合计充值
        return map;
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/10 14:40
     *  @Description: 获取京东企业资金流水列表
     */
    @Override
    public ResultUtil companyCapitalFlow(CompanyCapitalFlowVo param) {
        //累计充值
        Double hjcz = 0.0;
        //累计提现
        Double hjtx = 0.0;
        //累计运费支出
        Double hjyfzc = 0.0;
        //累计调度费支出
        Double hjddfzc = 0.0;
        //累计调度费召回
        Double hjddfzh = 0.0;
        //累计运费召回
        Double hjyfzh = 0.0;
        //累计退款
        Double hjtk = 0.0;
        List<CompanyCapitalFlow> listHj = tJdWalletMapper.companyCapitalFlow(param);

        for(CompanyCapitalFlow ccf :listHj){
            //累计充值
            if("BCHONGZHI".equals(ccf.getTradeTypeCode())){
                if(ccf.getAmount()!=null&&!"".equals(ccf.getAmount())){
                    hjcz+=Double.parseDouble(ccf.getAmount().toString());
                }
            }
            //累计提现
            if("BTIXIAN".equals(ccf.getTradeTypeCode())){
                if(ccf.getAmount()!=null&&!"".equals(ccf.getAmount())){
                    hjtx+=Double.parseDouble(ccf.getAmount().toString());
                }
            }
            //累计运费支出 + 预付款运费
            if("BYUNFEIZHICHU".equals(ccf.getTradeTypeCode()) || "CYFKZHICHU".equals(ccf.getTradeTypeCode())
                    || "CYKFJDZHICHU".equals(ccf.getTradeTypeCode()) || "BD_SERVICE_PAY".equals(ccf.getTradeTypeCode())){
                if(ccf.getAmount()!=null&&!"".equals(ccf.getAmount())){
                    hjyfzc+=Double.parseDouble(ccf.getAmount().toString());
                }
            }
            //累计调度费支出 +预付款调度费
            if("BDIAODUFEIZHICHU".equals(ccf.getTradeTypeCode())||"CYFKDDFZHICHU".equals(ccf.getTradeTypeCode())){
                if(ccf.getAmount()!=null&&!"".equals(ccf.getAmount())){
                    hjddfzc+=Double.parseDouble(ccf.getAmount().toString());
                }
            }
            //累计调度费召回
            if("BDIAODUFEIZH".equals(ccf.getTradeTypeCode())){
                if(ccf.getAmount()!=null&&!"".equals(ccf.getAmount())){
                    hjddfzh+=Double.parseDouble(ccf.getAmount().toString());
                }
            }
            //累计运费召回
            if("BYUNFEIZH".equals(ccf.getTradeTypeCode())){
                if(ccf.getAmount()!=null&&!"".equals(ccf.getAmount())){
                    hjyfzh+=Double.parseDouble(ccf.getAmount().toString());
                }
            }
            //累计退款
            if("BTUIKUAN".equals(ccf.getTradeTypeCode())){
                if(ccf.getAmount()!=null&&!"".equals(ccf.getAmount())){
                    hjtk+=Double.parseDouble(ccf.getAmount().toString());
                }
            }

        }
        Page<List<CompanyCapitalFlow>> page = PageHelper.startPage(param.getPage(), param.getSize());
        if (param.getTradeTime() != null) {
            param.setStartTime(param.getTradeTime()[0]);
            param.setEndTime(param.getTradeTime()[1]);
        }
        List<CompanyCapitalFlow> list = tJdWalletMapper.companyCapitalFlow(param);
        for(CompanyCapitalFlow map :list){
            if(map.getTradeTime()!=null&&!"".equals(map.getTradeTime())){
                map.setTradeTimeStr(DateUtils.formatDateTime((Date)map.getTradeTime()));
            }
            if(DictEnum.BDIAODUFEIZH.code.equals(map.getTradeTypeCode()) || DictEnum.BYUNFEIZH.code.equals(map.getTradeTypeCode())){
                String zc =  map.getThridParySubAccountZC();
                String zr = map.getThridParySubAccountZR();
                map.setThridParySubAccountZC(zr);
                map.setThridParySubAccountZR(zc);
            }
        }
        Map<String,Object> resp = new HashMap();
        resp.put("hjcz",hjcz);//累计充值
        resp.put("hjtx",hjtx);//累计提现
        resp.put("hjyfzc",hjyfzc);//累计运费支出
        resp.put("hjddfzc",hjddfzc);//累计调度费支出
        resp.put("hjddfzh",hjddfzh);//累计调度费召回
        resp.put("hjyfzh",hjyfzh);//累计运费召回
        resp.put("hjtk",hjtk);//累计退款
        resp.put("data",list);
        return ResultUtil.ok(resp, page.getTotal());
    }


    /**
     *  @author: dingweibo
     *  @Date: 2022/3/11 9:22
     *  @Description: 导出京东企业资金流水列表
     */
    @Override
    public ResultUtil companyCapitalFlowExcel(CompanyCapitalFlowVo param) {

        ResultUtil resultUtil = new ResultUtil();
        if (param.getTradeTime() != null) {
            param.setStartTime(param.getTradeTime()[0]);
            param.setEndTime(param.getTradeTime()[1]);
        }
        List<CompanyCapitalFlow> list = tJdWalletMapper.companyCapitalFlow(param);
        for(CompanyCapitalFlow map :list){
            if(map.getTradeTime()!=null&&!"".equals(map.getTradeTime())){
                map.setTradeTimeStr(DateUtils.formatDateTime((Date)map.getTradeTime()));
            }
            if(DictEnum.BDIAODUFEIZH.code.equals(map.getTradeTypeCode()) || DictEnum.BYUNFEIZH.code.equals(map.getTradeTypeCode())){
                String zc =  map.getThridParySubAccountZC();
                String zr = map.getThridParySubAccountZR();
                map.setThridParySubAccountZC(zr);
                map.setThridParySubAccountZR(zc);
            }
        }
        String[] headers =
                { "交易流水号", "交易时间", "交易类型", "金额（元）", "转出账号","转入账号","运单号"};
        String[] names = {"outerTradeNo","tradeTimeStr","tradeType","amount","thridParySubAccountZC","thridParySubAccountZR","innerTradeNo"};
        Map<String,Object> map = new HashMap<>();
        map.put("headers",headers);
        map.put("names",names);
        map.put("list",list);
        resultUtil.setData(map);
        resultUtil.setCode(DictEnum.SUCCESS.code);
        return resultUtil;
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/11 16:58
     *  @Description: 京东 终端钱包流水
     */
    @Override
    public ResultUtil driverCapitalFlow(CompanyCapitalFlowVo param) {
        Page<List<DriverCapitalFlow>> page = PageHelper.startPage(param.getPage(), param.getSize());
        if (param.getTradeTime() != null) {
            param.setStartTime(param.getTradeTime()[0]);
            param.setEndTime(param.getTradeTime()[1]);
        }
        List<DriverCapitalFlow> list = tJdWalletMapper.driverCapitalFlow(param);
        for(DriverCapitalFlow map :list) {
            if (DictEnum.CTIXIAN.code.equals(map.getTradeType())|| DictEnum.CYUNFEIZH.code.equals(map.getTradeType())||"CYFKTX".equals(map.getTradeType())) {
                String zc = map.getThridParySubAccountZC();
                String zr = map.getThridParySubAccountZR();
                map.setThridParySubAccountZC(zr);
                map.setThridParySubAccountZR(zc);
            }
            if(map.getTradeTimeDate()!=null){
                map.setTradeTime(DateUtils.formatDateTime((Date) map.getTradeTimeDate()));
            }
        }
        return ResultUtil.ok(list, page.getTotal());
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/28 14:22
     *  @Description: 京东经纪人钱包
     */
    @Override
    public ResultUtil selectAgentWallet() {
        Integer endUserId = CurrentUser.getEndUserId();
        //获得当前总余额
        Map walletSum = tJdWalletMapper.selectAgentWalletSum(endUserId);
        if(null == walletSum){
            walletSum = new HashMap();
        }
        ResultUtil resultUtil = tEndUserOpenRoleService.ifOpenRole(endUserId);
        JSONObject json = (JSONObject) JSON.toJSON(resultUtil.getData());
        walletSum.put("ifOpenRole", json.get("ifOpenRole"));
        walletSum.put("openResponseDesc",json.get("openResponseDesc"));
//        ResultUtil userFeeInfo = tServicefeeInfoService.selectEnduserAgreementFee();
//        walletSum.put("userFee",userFeeInfo.getData());
        TEndUserInfoVO tEndUserInfoVO = tEndUserInfoMapper.selectenduserAccountInfo(endUserId);
        List<TBankCard> tBankCardList = tBankCardMapper.selectOneselfBankcard(tEndUserInfoVO.getAccountId(),
                tEndUserInfoVO.getRealName(),tEndUserInfoVO.getIdcard());
        walletSum.put("idCardQuantity", tBankCardList.size());
        walletSum.put("bankCard",tBankCardList);
        return ResultUtil.ok(walletSum);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/28 14:45
     *  @Description: 经纪人钱包流水
     */
    @Override
    public ResultUtil selectAgentWalletList(CompanyCapitalFlowVo param) {
        Integer endUserId = CurrentUser.getEndUserId();
        param.setDriverId(String.valueOf(endUserId));
        Page<List<DriverCapitalFlow>> page = PageHelper.startPage(param.getPage(), param.getSize());
        if (param.getTradeTime() != null) {
            param.setStartTime(param.getTradeTime()[0]);
            param.setEndTime(param.getTradeTime()[1]);
        }
        List<DriverCapitalFlow> list = tJdWalletMapper.selectAgentWalletList(param);
        for(DriverCapitalFlow map :list) {
            if (DictEnum.MANAGER.code.equals(map.getTradeType())) {
                String zc = map.getThridParySubAccountZC();
                String zr = map.getThridParySubAccountZR();
                map.setThridParySubAccountZC(zr);
                map.setThridParySubAccountZR(zc);
            }
            if(map.getTradeTimeDate()!=null){
                map.setTradeTime(DateUtils.formatDateTime((Date) map.getTradeTimeDate()));
            }
        }
        return ResultUtil.ok(list, page.getTotal());
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/9/6 11:38
     *  @Description: 平台钱包查询
     */
    @Override
    public ResultUtil selectPfWallet() {
        TJdWalletVo jdWalletVo = tJdWalletMapper.selectPfWallet();
        if(null!=jdWalletVo){
            jdWalletVo.setPaymentStatus(JdEnumStr.getDesc(jdWalletVo.getPaymentStatus()));
            jdWalletVo.setOpenStatus(JdEnumStr.getDesc(jdWalletVo.getOpenStatus()));
        }
        List<TJdWalletVo> list = new ArrayList<>();
        list.add(jdWalletVo);
        return ResultUtil.ok(list);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/9/6 14:26
     *  @Description: 平台钱包流水
     */
    @Override
    public ResultUtil selectPfWalletList(CompanyCapitalFlowVo param) {
        Page<List<CompanyCapitalFlow>> page = PageHelper.startPage(param.getPage(), param.getSize());
        if (param.getTradeTime() != null) {
            param.setStartTime(param.getTradeTime()[0]);
            param.setEndTime(param.getTradeTime()[1]);
        }
        List<CompanyCapitalFlow> list = tJdWalletMapper.selectPfWalletList(param);
        for(CompanyCapitalFlow map :list){
            if(map.getTradeTime()!=null&&!"".equals(map.getTradeTime())){
                map.setTradeTimeStr(DateUtils.formatDateTime((Date)map.getTradeTime()));
            }
        }
        return ResultUtil.ok(list, page.getTotal());
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/6/13 14:29
     *  @Description: 平台钱包流水导出
     */
    @Override
    public ResultUtil selectPfWalletListExcel(CompanyCapitalFlowVo param) {
        if (param.getTradeTime() != null) {
            param.setStartTime(param.getTradeTime()[0]);
            param.setEndTime(param.getTradeTime()[1]);
        }
        List<CompanyCapitalFlow> list = tJdWalletMapper.selectPfWalletList(param);
        Map<String,Object> map = new HashMap<>();
        String[] headers =
                {
                        "交易流水号", "交易时间","交易类型","金额（元）","转出账号","转入账号"
                };
        String[] names =
                {
                        "outerTradeNo", "tradeTime","tradeType","amount","thridParySubAccountZC","thridParySubAccountZR"
                };
        map.put("headers",headers);
        map.put("names",names);
        map.put("list",list);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), map);
    }

    @Override
    public ResultUtil accountServiceFee(TEndUserOpenRoleVo record) throws ParseException {
        try{
            CustomerBalancePayReq req = new CustomerBalancePayReq();
            req.setPartnerId(partnerId);
            req.setRequestId(IdWorkerUtil.getInstance().nextId());
            req.setRequestTime(DateUtils.getRequestTime());
            req.setMerchantCode(merchantCode);
            req.setBizOrderNo(IdWorkerUtil.getInstance().nextId());
            req.setOutPartnerAccId(record.getPartnerAccId());
            TCarrierCompanyOpenRoleVo repf = new TCarrierCompanyOpenRoleVo();
            repf.setUserOpenRole(DictEnum.PF.code);
            TCarrierCompanyOpenRole pfResult = tCarrierCompanyOpenRoleMapper.selectByRoleBy(repf);
            long orderAmount = (record.getAccountServicefee().multiply(BigDecimal.valueOf(100)).longValue());
            req.setInPartnerAccId(pfResult.getPartnerAccId());
            req.setOrderAmount(orderAmount);
            req.setGoodsInfo("账户服务费");
            SysParam sysParam = sysParamAPI.getParamByKey("ACCOUNTSERVICEFEEPAY");
            req.setNotifyUrl(sysParam.getParamValue());
            ResultUtil resultUtil1 = cloudPayAPI.cloudPay(req);
            CustomerBalancePayResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, CustomerBalancePayResponse.class);
            if(JdEnum.JDSUCCESSCODE.code.equals(response.getResponseCode())){
                if(!"PAY_FAIL".equals(response.getOrderStatus())){
                    TOrderPayInfo orderPayInfo = new TOrderPayInfo();
                    orderPayInfo.setCode(IdWorkerUtil.getInstance().nextId());
                    orderPayInfo.setOrderActualPayment(record.getAccountServicefee());
                    orderPayInfo.setPaymentPlatforms(DictEnum.JDPLATFORMS.code);
                    orderPayInfo.setOrderPayStatus(DictEnum.P070.code);
                    tOrderPayInfoAPI.save(orderPayInfo);
                    TOrderPayDetail orderPayDetail = new TOrderPayDetail();
                    orderPayDetail.setCode(req.getBizOrderNo());
                    orderPayDetail.setOrderPayCode(orderPayInfo.getCode());
                    orderPayDetail.setTradeType(record.getUserOpenType());
                    orderPayDetail.setOperateState(JdTradeType.RZ.code);
                    orderPayDetail.setOperateTime(new Date());
                    orderPayDetail.setParam1(String.valueOf(record.getWalletId()));
                    tOrderPayDetailAPI.save(orderPayDetail);
                    //修改钱包
                    TJdWallet tJdWallet= tJdWalletMapper.selectByPrimaryKey(record.getWalletId());
                    if(null != tJdWallet){
                        //添加支付金额
                        BigDecimal entryAmount=tJdWallet.getEntryAmount().add(record.getAccountServicefee());
                        tJdWallet.setEntryAmount(entryAmount);
                        BigDecimal accountBalance = tJdWallet.getAccountBalance().subtract(record.getAccountServicefee());
                        tJdWallet.setAccountBalance(accountBalance);
                        tJdWalletMapper.updateByPrimaryKey(tJdWallet);
                    }else {
                        log.error("账户服务费支付失败：未找到钱包 ----------");
                        tOrderPayInfoAPI.updateStatusByCode(req.getBizOrderNo(),DictEnum.M080.code);
                        return ResultUtil.error("账户服务费支付失败：未找到钱包");
                    }
                }
            }else{
                TOrderPayInfo orderPayInfo = new TOrderPayInfo();
                orderPayInfo.setCode(IdWorkerUtil.getInstance().nextId());
                orderPayInfo.setOrderActualPayment(record.getAccountServicefee());
                orderPayInfo.setPaymentPlatforms(DictEnum.JDPLATFORMS.code);
                orderPayInfo.setOrderPayStatus(DictEnum.M080.code);
                orderPayInfo.setRemark(response.getResponseDesc());
                tOrderPayInfoAPI.save(orderPayInfo);
                TOrderPayDetail orderPayDetail = new TOrderPayDetail();
                orderPayDetail.setCode(req.getBizOrderNo());
                orderPayDetail.setOrderPayCode(orderPayInfo.getCode());
                orderPayDetail.setTradeType(record.getUserOpenType());
                orderPayDetail.setTradeStatus(response.getOrderStatus());
                orderPayDetail.setErrorMsg(response.getResponseDesc());
                orderPayDetail.setErrorCode(response.getResponseCode());
                orderPayDetail.setOperateState(JdTradeType.RZ.code);
                orderPayDetail.setOperateTime(new Date());
                orderPayDetail.setParam1(String.valueOf(record.getWalletId()));
                tOrderPayDetailAPI.save(orderPayDetail);

                TServicefeeRecordExample example = new TServicefeeRecordExample();
                TServicefeeRecordExample.Criteria cr = example.createCriteria();
                cr.andOpenRoleIdEqualTo(record.getId());
                cr.andOpenTypeEqualTo(DictEnum.CD.code);
                cr.andServicefeeTypeEqualTo(DictEnum.ACCOUNTSERVICE.code);
                cr.andServicefeeTimeBetween(DateUtils.forDateStart(),DateUtils.forDateEnd());
                List<TServicefeeRecord> tServicefeeRecordList  = tServicefeeRecordMapper.selectByExample(example);
                if(tServicefeeRecordList.size()>0){
                    TServicefeeRecord tServicefeeRecord = tServicefeeRecordList.get(0);
                    tServicefeeRecord.setRemark(response.getResponseDesc());
                    tServicefeeRecord.setIfSuccess(false);
                    tServicefeeRecordMapper.updateByPrimaryKeySelective(tServicefeeRecord);
                }
                return ResultUtil.error("账户服务费支付失败");
            }
        }catch (Exception e){
            log.error("账户服务费支付失败：",e);
            throw e;
        }
        return ResultUtil.ok();
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/12/27 9:48
     *  @Description: 京东账户充值钱包修改
     */
    @Override
    @Transactional
    public ResultUtil updateWalletCallBack(AsyncNotifyVirtualMemberRechargeMessageBody messageBody) {
        TCarrierCompanyOpenRoleVo rec = new TCarrierCompanyOpenRoleVo();
        rec.setPartnerAccId(messageBody.getPartnerAccId());
        TCarrierCompanyOpenRole tCarrierCompanyOpenRole = tCarrierCompanyOpenRoleMapper.selectByRoleBy(rec);
        TJdWallet tJdWallet;
        if (null != tCarrierCompanyOpenRole) {
            tJdWallet = tJdWalletMapper.selectByRoleTypeAndComId(tCarrierCompanyOpenRole.getUserOpenRole(),tCarrierCompanyOpenRole.getId());
        } else {
            // 查询C端钱包
            tJdWallet = tJdWalletMapper.selectByEnduserPartnerAccId(messageBody.getPartnerAccId());
        }

        log.info("转账入金通知消息, 钱包数据, {}", JSONUtil.toJsonStr(tJdWallet));
        TJdBizAmount tJdBizAmount = new TJdBizAmount();
        BeanUtils.copyProperties(messageBody,tJdBizAmount);
        tJdBizAmount.setOrderAmount(new BigDecimal(messageBody.getOrderAmount()).divide(new BigDecimal(100)));
        tJdBizAmount.setCreateTime(new Date());
        tJdBizAmount.setUpdateTime(new Date());
        tJdBizAmount.setEnable(false);
        tJdBizAmountMapper.insertSelective(tJdBizAmount);
        BigDecimal balance = tJdWallet.getAccountBalance().add(new BigDecimal(messageBody.getOrderAmount()).divide(new BigDecimal(100)));
        tJdWallet.setAccountBalance(balance);
        tJdWalletMapper.updateByPrimaryKeySelective(tJdWallet);
        TJdWalletChangeLog tJdWalletChangeLog = new TJdWalletChangeLog();
        tJdWalletChangeLog.setJdWalletId(tJdWallet.getId());
        tJdWalletChangeLog.setPurseCategory(tJdWallet.getPurseCategory());
        tJdWalletChangeLog.setTradeNo(messageBody.getBizOrderNo());
        tJdWalletChangeLog.setTradeType(DictEnum.BCHONGZHI.code);
        tJdWalletChangeLog.setAmount(new BigDecimal(messageBody.getOrderAmount()).divide(new BigDecimal(100)));
        tJdWalletChangeLog.setTradeTime(messageBody.getFinishDate());
        tJdWalletChangeLog.setBankOrderNo(messageBody.getBankOrderNo());
        tJdWalletChangeLog.setInPartnerAccId(messageBody.getPartnerAccId());
        tJdWalletChangeLog.setCreateTime(new Date());
        tJdWalletChangeLog.setUpdateTime(new Date());
        tJdWalletChangeLog.setEnable(false);
        tJdWalletChangeLogMapper.insertSelective(tJdWalletChangeLog);
        try {
            // 企业对公账户充值到承运方
            if (DictEnum.CA.code.equals(tJdWallet.getDataSource()) && DictEnum.PCARRIER.code.equals(tJdWallet.getPurseCategory())) {
                // 承运方调账到企业
                String othAccountNo = messageBody.getOthAccountNo();
                // 查询企业开户信息
                TCarrierCompanyOpenRole carrierCompanyOpenRole = tBankCardMapper.selectCarrierCompanyOpenRoleByCardNo(othAccountNo);
                if (null != carrierCompanyOpenRole) {
                    if (DictEnum.BD.code.equals(carrierCompanyOpenRole.getUserOpenRole())) {
                        // 修改承运方钱包
                        balance = tJdWallet.getAccountBalance().subtract(tJdWalletChangeLog.getAmount());
                        tJdWallet.setAccountBalance(balance);
                        BigDecimal entryAmount = tJdWallet.getEntryAmount().add(tJdWalletChangeLog.getAmount());
                        tJdWallet.setEntryAmount(entryAmount);
                        tJdWallet.setUpdateTime(new Date());
                        tJdWalletMapper.updateByPrimaryKey(tJdWallet);


                        TOrderPayInfo orderPay = new TOrderPayInfo();
                        orderPay.setCode(IdWorkerUtil.getInstance().nextId());
                        orderPay.setPaymentPlatforms(DictEnum.JDPLATFORMS.code);
                        orderPay.setOrderPayStatus(DictEnum.P070.code);
                        orderPay.setOrderPrepayAmount(OrderMoneyUtil.changeF2Y(Long.parseLong(messageBody.getOrderAmount())));
                        orderPay.setOrderTotalPayment(orderPay.getOrderPrepayAmount());
                        // 京东支付
                        orderPay.setParam1(DictEnum.JDPAY.code);
                        orderPayInfoAPI.save(orderPay);

                        TOrderPayDetail tOrderPayDetail = new TOrderPayDetail();
                        tOrderPayDetail.setCode(IdWorkerUtil.getInstance().nextId());
                        tOrderPayDetail.setOrderPayCode(orderPay.getCode());
                        tOrderPayDetail.setTradeType(JDTradeTypeEnum.CABALANCE_BD_CHARGE.code);
                        tOrderPayDetail.setOperateState(JdTradeType.RZ.code);
                        tOrderPayDetail.setOperateTime(new Date());
                        orderPayDetailAPI.save(tOrderPayDetail);

                        CustomerBalancePayReq req = new CustomerBalancePayReq();
                        req.setPartnerId(partnerId);
                        req.setRequestId(IdWorkerUtil.getInstance().nextId());
                        req.setRequestTime(DateUtils.getRequestTime());
                        req.setMerchantCode(merchantCode);
                        req.setBizOrderNo(tOrderPayDetail.getCode());
                        req.setOutPartnerAccId(messageBody.getPartnerAccId());
                        req.setInPartnerAccId(carrierCompanyOpenRole.getPartnerAccId());
                        req.setOrderAmount(OrderMoneyUtil.changeY2F(orderPay.getOrderTotalPayment()));
                        req.setGoodsInfo("企业充值");
                        // 查询回调地址
                        SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.CARRIERBALANCEBDCHARGENOTIFYURL.code);
                        req.setNotifyUrl(paramByKey.getParamValue());

                        MQMessage mqMessage = new MQMessage();
                        mqMessage.setTopic(MqMessageTopic.COMPANYKIND);
                        mqMessage.setTag(MqMessageTag.CARRIERBALANCEBDCHARGE);
                        mqMessage.setKey(messageBody.getBizOrderNo());
                        mqMessage.setBody(req);
                        ResultUtil resultUtil = mqAPI.sendMessage(mqMessage);
                        log.info("承运方余额支付(企业充值)发送MQ消息结果，{}", JSONUtil.toJsonStr(resultUtil));
                        if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
                            log.error("承运方余额支付(企业充值)发送MQ消息失败，{}", JSONUtil.toJsonStr(resultUtil));
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("承运方余额支付(企业充值)失败, {}", e);
        }
        return ResultUtil.ok(tJdWalletChangeLog.getId());
    }

    @Override
    public TJdWallet selectWalletByEnduserParnterAccId(String endUserPartnerAccId) {
        return tJdWalletMapper.selectByEnduserPartnerAccId(endUserPartnerAccId);
    }

}
