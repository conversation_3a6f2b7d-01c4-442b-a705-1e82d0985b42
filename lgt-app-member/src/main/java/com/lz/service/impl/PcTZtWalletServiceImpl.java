package com.lz.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.JdEnum;
import com.lz.common.dbenum.JdEnumStr;
import com.lz.common.dbenum.ZtEnumStr;
import com.lz.common.model.CodeEnum;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.DateUtils;
import com.lz.common.util.ResultUtil;
import com.lz.dao.*;
import com.lz.model.*;
import com.lz.service.PcTZtWalletService;
import com.lz.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

import static java.util.stream.Collectors.toList;

@Service
public class PcTZtWalletServiceImpl implements PcTZtWalletService {

    @Autowired
    private TZtAccountOpenInfoMapper tZtAccountOpenInfoMapper;

    @Autowired
    private TZtWalletMapper tZtWalletMapper;

    private TJdWalletMapper tJdWalletMapper;

    @Override
    public ResultUtil pcCompanyWalletDetail(PcCompanyWalletVO record) {
        TZtWalletVO tZtWalletVO = new TZtWalletVO();

        //当前可用资金
        tZtWalletVO.setCanBalance(BigDecimal.ZERO);
        //当前总资金
        tZtWalletVO.setAccountBalance(BigDecimal.ZERO);
        TZtAccountOpenInfo tZtAccountOpenInfo = tZtAccountOpenInfoMapper.selectOpenInfoByAccountId(record.getAccountId());
        if(null == tZtAccountOpenInfo){
            return ResultUtil.ok();
        }
        if(null!=tZtAccountOpenInfo && !"".equals(tZtAccountOpenInfo)){
            tZtWalletVO = tZtWalletMapper.selectByAccountId(record.getAccountId());
        }
        tZtWalletVO.setSubAcc(tZtAccountOpenInfo.getSubAcc());
        tZtWalletVO.setPartnerAccId(tZtAccountOpenInfo.getPartnerAccId());
        tZtWalletVO.setCzHj(BigDecimal.ZERO);//合计充值
        tZtWalletVO.setZcHj(BigDecimal.ZERO);//合计支出
        Double zcHj = 0.0;
        Double czHj = 0.0;
        List<Map<String,Object>> listHj = tZtWalletMapper.walletHj(record.getAccountId());
        //(企业)充值 BCHONGZHI
        //(企业)调度费召回 BDIAODUFEIZH
        //(企业)调度费支出 BDIAODUFEIZHICHU
        //(企业)运费召回	BYUNFEIZH
        //(企业)运费支出	BYUNFEIZHICHU
        //累计支出 = 运费支出+调度费支出-运费召回-调度费召回
        for(Map<String, Object> map2:listHj){
            if(map2!=null ){
                if("BCHONGZHI".equals(map2.get("tradeTypeCode"))){//充值
                    czHj+=Double.parseDouble(map2.get("amount").toString());
                }
                if("BPAYZH".equals(map2.get("tradeTypeCode"))|| "BPAY".equals(map2.get("tradeTypeCode")) ||
                        "BD_SERVICE_PAY".equals(map2.get("tradeTypeCode")) || "CYKFJDZHICHU".equals(map2.get("tradeTypeCode"))){
                    if("BPAY".equals(map2.get("tradeTypeCode"))){//运费支出
                        if(map2.get("amount")!=null&&!"".equals(map2.get("amount"))){
                            zcHj+=Double.parseDouble(map2.get("amount").toString());
                        }
                    }
                    if("BD_SERVICE_PAY".equals(map2.get("tradeTypeCode"))){//提现手续费支出
                        if(map2.get("amount")!=null&&!"".equals(map2.get("amount"))){
                            zcHj+=Double.parseDouble(map2.get("amount").toString());
                        }
                    }
                    if("CYKFJDZHICHU".equals(map2.get("tradeTypeCode"))){//提现手续费支出
                        if(map2.get("amount")!=null&&!"".equals(map2.get("amount"))){
                            zcHj+=Double.parseDouble(map2.get("amount").toString());
                        }
                    }
                    if("BPAYZH".equals(map2.get("tradeTypeCode"))){//运费召回
                        if(map2.get("amount")!=null&&!"".equals(map2.get("amount"))){
                            zcHj-=Double.parseDouble(map2.get("amount").toString());
                        }
                    }
                }
            }
        }
        tZtWalletVO.setZcHj(new BigDecimal(zcHj));//合计支出
        tZtWalletVO.setCzHj(new BigDecimal(czHj));//合计充值
        return ResultUtil.ok(tZtWalletVO);
    }

    @Override
    public ResultUtil companyWalletCapitalFlow(CompanyWalletCapitalFlowVO companyWalletCapitalFlowVO) {
        //累计充值
        Double hjcz = 0.0;
        //累计提现
        Double hjtx = 0.0;
        //累计运费支出
        Double hjyfzc = 0.0;
        //累计调度费支出
        Double hjddfzc = 0.0;
        //累计调度费召回
        Double hjddfzh = 0.0;
        //累计运费召回
        Double hjyfzh = 0.0;
        //累计退款
        Double hjtk = 0.0;
        List<CompanyWalletCapitalFlow> listHj = tZtWalletMapper.companyWalletCapitalFlow(companyWalletCapitalFlowVO);

        for(CompanyWalletCapitalFlow cwcf :listHj){
            //累计充值
            if("BCHONGZHI".equals(cwcf.getTradeTypeCode())){
                if(cwcf.getAmount()!=null&&!"".equals(cwcf.getAmount())){
                    hjcz+=Double.parseDouble(cwcf.getAmount().toString());
                }
            }
            //累计提现
            if("BTIXIAN".equals(cwcf.getTradeTypeCode())){
                if(cwcf.getAmount()!=null&&!"".equals(cwcf.getAmount())){
                    hjtx+=Double.parseDouble(cwcf.getAmount().toString());
                }
            }
            //累计运费支出 + 预付款运费
            if("BYUNFEIZHICHU".equals(cwcf.getTradeTypeCode()) || "CYFKZHICHU".equals(cwcf.getTradeTypeCode())
                    || "CYKFJDZHICHU".equals(cwcf.getTradeTypeCode()) || "BD_SERVICE_PAY".equals(cwcf.getTradeTypeCode())){
                if(cwcf.getAmount()!=null&&!"".equals(cwcf.getAmount())){
                    hjyfzc+=Double.parseDouble(cwcf.getAmount().toString());
                }
            }
            //累计调度费支出 +预付款调度费
            if("BDIAODUFEIZHICHU".equals(cwcf.getTradeTypeCode())||"CYFKDDFZHICHU".equals(cwcf.getTradeTypeCode())){
                if(cwcf.getAmount()!=null&&!"".equals(cwcf.getAmount())){
                    hjddfzc+=Double.parseDouble(cwcf.getAmount().toString());
                }
            }
            //累计调度费召回
            if("BDIAODUFEIZH".equals(cwcf.getTradeTypeCode())){
                if(cwcf.getAmount()!=null&&!"".equals(cwcf.getAmount())){
                    hjddfzh+=Double.parseDouble(cwcf.getAmount().toString());
                }
            }
            //累计运费召回
            if("BYUNFEIZH".equals(cwcf.getTradeTypeCode())){
                if(cwcf.getAmount()!=null&&!"".equals(cwcf.getAmount())){
                    hjyfzh+=Double.parseDouble(cwcf.getAmount().toString());
                }
            }
            //累计退款
            if("BTUIKUAN".equals(cwcf.getTradeTypeCode())){
                if(cwcf.getAmount()!=null&&!"".equals(cwcf.getAmount())){
                    hjtk+=Double.parseDouble(cwcf.getAmount().toString());
                }
            }

        }
        Page<List<CompanyCapitalFlow>> page = PageHelper.startPage(companyWalletCapitalFlowVO.getPage(), companyWalletCapitalFlowVO.getSize());
        if (companyWalletCapitalFlowVO.getTradeTime() != null) {
            companyWalletCapitalFlowVO.setStartTime(companyWalletCapitalFlowVO.getTradeTime()[0]);
            companyWalletCapitalFlowVO.setEndTime(companyWalletCapitalFlowVO.getTradeTime()[1]);
        }
        List<CompanyWalletCapitalFlow> list = tZtWalletMapper.companyWalletCapitalFlow(companyWalletCapitalFlowVO);
        for(CompanyWalletCapitalFlow cwcf :list){
            if(cwcf.getTradeTime()!=null&&!"".equals(cwcf.getTradeTime())){
                cwcf.setTradeTimeStr(DateUtils.formatDateTime(cwcf.getTradeTime()));
            }
            if(cwcf.getTradeType().contains("召回")){
                String zc =  cwcf.getThridParySubAccountZC();
                String zr = cwcf.getThridParySubAccountZR();
                cwcf.setThridParySubAccountZC(zr);
                cwcf.setThridParySubAccountZR(zc);
            }
        }
        Map<String,Object> resp = new HashMap();
        resp.put("hjcz",hjcz);//累计充值
        resp.put("hjtx",hjtx);//累计提现
        resp.put("hjyfzc",hjyfzc);//累计运费支出
        resp.put("hjddfzc",hjddfzc);//累计调度费支出
        resp.put("hjddfzh",hjddfzh);//累计调度费召回
        resp.put("hjyfzh",hjyfzh);//累计运费召回
        resp.put("hjtk",hjtk);//累计退款
        resp.put("data",list);
        return ResultUtil.ok(resp, page.getTotal());
    }

    @Override
    public ResultUtil companyWalletCapitalFlowExcel(CompanyWalletCapitalFlowVO param) {
        ResultUtil resultUtil = new ResultUtil();
        if (param.getTradeTime() != null) {
            param.setStartTime(param.getTradeTime()[0]);
            param.setEndTime(param.getTradeTime()[1]);
        }
        param.setOuterTradeNoArray(deleteNullParam(param.getOuterTradeNoArray()));
        List<CompanyWalletCapitalFlow> list = tZtWalletMapper.companyWalletCapitalFlow(param);
        for(CompanyWalletCapitalFlow cwcf :list){
            if(cwcf.getTradeTime()!=null&&!"".equals(cwcf.getTradeTime())){
                cwcf.setTradeTimeStr(DateUtils.formatDateTime((Date)cwcf.getTradeTime()));
            }
            if(DictEnum.BDIAODUFEIZH.code.equals(cwcf.getTradeTypeCode()) || DictEnum.BYUNFEIZH.code.equals(cwcf.getTradeTypeCode())){
                String zc =  cwcf.getThridParySubAccountZC();
                String zr = cwcf.getThridParySubAccountZR();
                cwcf.setThridParySubAccountZC(zr);
                cwcf.setThridParySubAccountZR(zc);
            }
        }
        String[] headers =
                { "交易流水号", "交易时间", "交易类型", "金额（元）", "转出账号","转入账号","运单号"};
        String[] names = {"outerTradeNo","tradeTimeStr","tradeType","amount","thridParySubAccountZC","thridParySubAccountZR","innerTradeNo"};
        Map<String,Object> map = new HashMap<>();
        map.put("headers",headers);
        map.put("names",names);
        map.put("list",list);
        resultUtil.setData(map);
        resultUtil.setCode(DictEnum.SUCCESS.code);
        return resultUtil;
    }

    private String[] deleteNullParam(String[] number){
        List OuterTradeNoArray = new ArrayList();
        for(String param : number){
            if(null != param && !param.equals("")){
                OuterTradeNoArray.add(param);
            }
        }
        return (String[])OuterTradeNoArray.toArray(new String[OuterTradeNoArray.size()]);
    }

    @Override
    public ResultUtil driverWalletCapitalFlow(CompanyWalletCapitalFlowVO param) {
        Page<List<DriverWalletCapitaclFlow>> page = PageHelper.startPage(param.getPage(), param.getSize());
        if (param.getTradeTime() != null) {
            param.setStartTime(param.getTradeTime()[0]);
            param.setEndTime(param.getTradeTime()[1]);
        }

        List<DriverWalletCapitaclFlow>  list = tZtWalletMapper.driverWalletCapitalFlow(param);
        for(DriverWalletCapitaclFlow map :list) {
            if (map.getTradeType().contains("收入")) {
                String zc = map.getThridParySubAccountZC();
                String zr = map.getThridParySubAccountZR();
                map.setThridParySubAccountZC(zr);
                map.setThridParySubAccountZR(zc);
            }
            if(map.getTradeTimeDate()!=null){
                map.setTradeTime(DateUtils.formatDateTime(map.getTradeTimeDate()));
            }
        }
        return ResultUtil.ok(list, page.getTotal());
    }

    @Override
    public ResultUtil selectByHxCarrierWalletPage(CarrierHxOpenRoleSearchVo record) {
        Page<Object> objectPage = PageHelper.startPage(record.getPage(), record.getSize());
        List<CarrierCompanyHxOpenRoleVo> list = tZtWalletMapper.selectByHxCarrierWalletPage(record);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),objectPage,objectPage.getTotal());
    }

    @Override
    public ResultUtil selectCarrierHxWalletLogPage(CarrierHxOpenRoleSearchVo record) {
        Date[] tardeTime = record.getTradeTime();
        if (tardeTime != null) {
            record.setTradeStartTime(tardeTime[0]);
            record.setTradeEndTime(tardeTime[1]);
        }

        Page<Object> objectPage = PageHelper.startPage(record.getPage(), record.getSize());
        List<CarrierCompanyHxOpenRoleVo> list = tZtWalletMapper.selectCarrierHxWalletLogPage(record);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),list,objectPage.getTotal());
    }

    @Override
    public ResultUtil carrierHxWalletExcel(CarrierHxOpenRoleSearchVo record) {
        ResultUtil resultUtil = new ResultUtil();
        Date[] tardeTime = record.getTradeTime();
        if (tardeTime != null) {
            record.setTradeStartTime(tardeTime[0]);
            record.setTradeEndTime(tardeTime[1]);
        }
        List<CarrierCompanyHxOpenRoleVo> list = tZtWalletMapper.selectCarrierHxWalletLogPage(record);
        String[] headers =
                { "交易流水号", "交易时间", "交易类型", "金额（元）", "转出账号","转入账号","运单号"};
        String[] names = {"tradeNo","tradeTimeStr","tradeType","amount","outerTradeNo","innerTradeNo","orderBusinessCode"};
        Map<String,Object> map = new HashMap<>();
        map.put("headers",headers);
        map.put("names",names);
        map.put("list",list);
        resultUtil.setData(map);
        resultUtil.setCode(DictEnum.SUCCESS.code);
        return resultUtil;
    }

    @Override
    public ResultUtil selectHxPlatformWallet() {
        TZtWalletVO tZtWalletVO = tZtWalletMapper.selectHxPlatformWallet();
        List<TZtWalletVO> list = new ArrayList<>();
        list.add(tZtWalletVO);
        return ResultUtil.ok(list);
    }

    @Override
    public ResultUtil selectHxPlatformWalletList(CarrierHxOpenRoleSearchVo param) {
        Page<List<CarrierCompanyHxOpenRoleVo>> page = PageHelper.startPage(param.getPage(), param.getSize());
        if (param.getTradeTime() != null) {
            param.setStartTime(param.getTradeTime()[0]);
            param.setEndTime(param.getTradeTime()[1]);
        }
        List<CarrierCompanyHxOpenRoleVo> list = tZtWalletMapper.selectHxPlatformWalletList(param);
        for(CarrierCompanyHxOpenRoleVo map :list){
            if(map.getTradeTime()!=null&&!"".equals(map.getTradeTime())){
                map.setTradeTimeStr(DateUtils.formatDateTime((Date)map.getTradeTime()));
            }
        }
        return ResultUtil.ok(list, page.getTotal());
    }

    @Override
    public ResultUtil platformHxWalletExcel(CarrierHxOpenRoleSearchVo record) {
        ResultUtil resultUtil = new ResultUtil();
        if (record.getTradeTime() != null) {
            record.setStartTime(record.getTradeTime()[0]);
            record.setEndTime(record.getTradeTime()[1]);
        }
        List<CarrierCompanyHxOpenRoleVo> list = tZtWalletMapper.selectHxPlatformWalletList(record);
        for(CarrierCompanyHxOpenRoleVo map :list){
            if(map.getTradeTime()!=null&&!"".equals(map.getTradeTime())){
                map.setTradeTimeStr(DateUtils.formatDateTime((Date)map.getTradeTime()));
            }
        }
        String[] headers =
                { "交易流水号", "交易时间", "交易类型", "金额（元）", "转出账号","转入账号"};
        String[] names = {"outerTradeNo","tradeTimeStr","tradeType","amount","thridParySubAccountZC","thridParySubAccountZR"};
        Map<String,Object> map = new HashMap<>();
        map.put("headers",headers);
        map.put("names",names);
        map.put("list",list);
        resultUtil.setData(map);
        resultUtil.setCode(DictEnum.SUCCESS.code);
        return resultUtil;
    }

    @Override
    public ResultUtil selectHxAgentWalletList(CarrierHxOpenRoleSearchVo param) {
        Integer endUserId = CurrentUser.getEndUserId();
        param.setDriverId(String.valueOf(endUserId));
        Page<List<DriverCapitalFlow>> page = PageHelper.startPage(param.getPage(), param.getSize());
        if (param.getTradeTime() != null) {
            param.setStartTime(param.getTradeTime()[0]);
            param.setEndTime(param.getTradeTime()[1]);
        }
        List<DriverCapitalFlow> list = tZtWalletMapper.selectHxAgentWalletList(param);
        for(DriverCapitalFlow map :list) {
            if (map.getTradeTypeCode().contains("召回")) {
                String zc = map.getThridParySubAccountZC();
                String zr = map.getThridParySubAccountZR();
                map.setThridParySubAccountZC(zr);
                map.setThridParySubAccountZR(zc);
            }
            if(map.getTradeTimeDate()!=null){
                map.setTradeTime(DateUtils.formatDateTime(map.getTradeTimeDate()));
            }
        }
        return ResultUtil.ok(list, page.getTotal());
    }

    @Override
    public ResultUtil selectHxAgentWallet() {
        Integer endUserId = CurrentUser.getEndUserId();
        //获得当前总余额
        Map walletSum = tZtWalletMapper.selectHxAgentWalletSum(endUserId);
        return ResultUtil.ok(walletSum);
    }
}
