package com.lz.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jd.jr.jropen.unifySdk.reqModel.BeneficiaryInfo;
import com.jd.jr.jropen.unifySdk.reqModel.ShareholderInfo;
import com.jd.jr.jropen.unifySdk.respModel.OpenQueryAccountInfoResponse;
import com.jd.jr.jropen.unifySdk.respModel.OpenRealNameUploadResponse;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.JdEnum;
import com.lz.common.dbenum.JdEnumStr;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.CodeEnum;
import com.lz.common.model.hxPayment.request.account.OpenRealNameIdentitySubmitModifyReq;
import com.lz.common.model.hxPayment.request.account.OpenRealNameIdentitySubmitReq;
import com.lz.common.model.hxPayment.request.account.OpenRealNamePersonalApplyReq;
import com.lz.common.model.hxPayment.request.account.OpenRealNamePersonalModifyReq;
import com.lz.common.model.hxPayment.response.account.OpenRealNameApplyResp;
import com.lz.common.model.hxPayment.response.account.OpenRealNameIdentitySubmitResp;
import com.lz.common.model.jdPayment.request.account.OpenQueryAccountInfoReq;
import com.lz.common.model.jdPayment.request.account.OpenRealNameApplyReq;
import com.lz.common.model.jdPayment.request.account.OpenRealNameUploadReq;
import com.lz.common.model.jdPayment.util.CloudPayFormatUtil;
import com.lz.common.model.jdPayment.util.FileBase64Util;
import com.lz.common.rlsdk.sdk.utils.DateUtil;
import com.lz.common.util.AliPayCardDetailUtil;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.DateUtils;
import com.lz.common.util.ResultUtil;
import com.lz.dao.*;
import com.lz.example.TEndUserInfoExample;
import com.lz.example.TZtAccountOpenInfoExample;
import com.lz.hxpay.CloudPaymentAPI;
import com.lz.jdpay.CloudPayAPI;
import com.lz.model.*;
import com.lz.service.TCarrierCompanyOpenRoleService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.vo.TAccountOpenInfoVo;
import com.lz.vo.TCarrierCompanyOpenRoleSearchVo;
import com.lz.vo.TCarrierCompanyOpenRoleVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *  @author: dingweibo
 *  @Date: 2021/8/5 9:20
 *  @Description: 承运方与企业 京东开户表
 */
@Service("tCarrierCompanyOpenRoleService")
@Slf4j
public class TCarrierCompanyOpenRoleServiceImpl implements TCarrierCompanyOpenRoleService {

    @Autowired
    private TCarrierCompanyOpenRoleMapper tCarrierCompanyOpenRoleMapper;

    @Autowired
    private CloudPayAPI cloudPayAPI;

    @Autowired
    private CloudPaymentAPI cloudPaymentAPI;

    @Autowired
    private SysParamAPI sysParamAPI;

    @Resource
    private TZtAccountOpenInfoMapper ztAccountOpenInfoMapper;

    @Resource
    private TCarrierInfoMapper carrierInfoMapper;

    @Resource
    private TCompanyInfoMapper companyInfoMapper;

    @Resource
    private TEndUserInfoMapper endUserInfoMapper;

    @Resource
    private TAccountMapper tAccountMapper;

    @Value("${partnerId}")
    private String partnerId;
    @Value("${merchantCode}")
    private String merchantCode;

    @Value("${hxyhPartnerId}")
    private String hxyhPartnerId;
    @Value("${hxyhChannelId}")
    private String hxyhChannelId;
    /**
     *  @author: dingweibo
     *  @Date: 2021/8/5 15:29
     *  @Description: 承运方京东开户列表查询
     */
    @Override
    public ResultUtil selectByPage(TCarrierCompanyOpenRoleSearchVo record) {
        Page<Object> objectPage = PageHelper.startPage(record.getPage(), record.getSize());
        List<TCarrierCompanyOpenRoleVo>  list = tCarrierCompanyOpenRoleMapper.selectByPage(record);
        for(TCarrierCompanyOpenRoleVo vo:list){
            vo.setUserOpenRole(DictEnum.CA.code);
            if(vo.getOpenStatus()!=null && vo.getPaymentStatus()!=null){
                if(vo.getOpenStatus().equals(JdEnum.OPENSUCCESS.code) && vo.getPaymentStatus().equals(JdEnum.BIND.code)){
                    vo.setAccountStatus("可用");
                }else{
                    vo.setAccountStatus("不可用");
                }
            }else{
                vo.setAccountStatus("不可用");
            }
            vo.setOpenStatus(JdEnumStr.getDesc(vo.getOpenStatus()));
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),objectPage,objectPage.getTotal());
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/5 15:29
     *  @Description: 承运方京东开户回显
     */
    @Override
    public ResultUtil selectById(TCarrierCompanyOpenRoleVo record) {
        TCarrierCompanyOpenRoleVo tCarrierCompanyOpenRoleVo = tCarrierCompanyOpenRoleMapper.selectById(record);
        tCarrierCompanyOpenRoleVo.setPartnerMemberMobile(tCarrierCompanyOpenRoleVo.getComPhone());
        return ResultUtil.ok(tCarrierCompanyOpenRoleVo);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/5 15:29
     *  @Description: 企业京东开户列表查询
     */
    @Override
    public ResultUtil selectCompanyByPage(TCarrierCompanyOpenRoleSearchVo record) {
        Page<Object> objectPage = PageHelper.startPage(record.getPage(), record.getSize());
        List<TCarrierCompanyOpenRoleVo>  list = tCarrierCompanyOpenRoleMapper.selectCompanyByPage(record);
        for(TCarrierCompanyOpenRoleVo vo:list){
            vo.setUserOpenRole(DictEnum.BD.code);
            if(vo.getOpenStatus()!=null && vo.getPaymentStatus()!=null){
                if(vo.getOpenStatus().equals(JdEnum.OPENSUCCESS.code) && vo.getPaymentStatus().equals(JdEnum.BIND.code)){
                    vo.setAccountStatus("可用");
                }else{
                    vo.setAccountStatus("不可用");
                }
            }else{
                vo.setAccountStatus("不可用");
            }
            vo.setOpenStatus(JdEnumStr.getDesc(vo.getOpenStatus()));
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),objectPage,objectPage.getTotal());
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/12 10:54
     *  @Description: 企业开户回显
     */
    @Override
    public ResultUtil selectByCompanyId(TCarrierCompanyOpenRoleVo record) {
        TCarrierCompanyOpenRoleVo tCarrierCompanyOpenRoleVo = tCarrierCompanyOpenRoleMapper.selectByCompanyId(record);
        tCarrierCompanyOpenRoleVo.setPartnerMemberMobile(tCarrierCompanyOpenRoleVo.getComPhone());
        return ResultUtil.ok(tCarrierCompanyOpenRoleVo);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/20 14:06
     *  @Description: 平台京东开户列表查询
     */
    @Override
    public ResultUtil selectPlatformByPage(TCarrierCompanyOpenRoleSearchVo record) {
        Page<Object> objectPage = PageHelper.startPage(record.getPage(), record.getSize());
        List<TCarrierCompanyOpenRoleVo>  list = tCarrierCompanyOpenRoleMapper.selectPlatformByPage(record);
        for(TCarrierCompanyOpenRoleVo vo:list){
            vo.setUserOpenRole(DictEnum.PF.code);
            if(vo.getOpenStatus()!=null && vo.getPaymentStatus()!=null){
                if(vo.getOpenStatus().equals(JdEnum.OPENSUCCESS.code) && vo.getPaymentStatus().equals(JdEnum.BIND.code)){
                    vo.setAccountStatus("可用");
                }else{
                    vo.setAccountStatus("不可用");
                }
            }else{
                vo.setAccountStatus("不可用");
            }
            vo.setOpenStatus(JdEnumStr.getDesc(vo.getOpenStatus()));
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),objectPage,objectPage.getTotal());
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/20 17:09
     *  @Description: 平台开户回显
     */
    @Override
    public ResultUtil selectByPlatformId(TCarrierCompanyOpenRoleVo record) {
        TCarrierCompanyOpenRoleVo tCarrierCompanyOpenRoleVo = tCarrierCompanyOpenRoleMapper.selectByPlatformId(record);
        tCarrierCompanyOpenRoleVo.setPartnerMemberMobile(tCarrierCompanyOpenRoleVo.getComPhone());
        return ResultUtil.ok(tCarrierCompanyOpenRoleVo);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/5 15:29
     *  @Description: 承运方或企业，平台，C端 用户开户
     */
    @Transactional(rollbackOn = Exception.class)
    @Override
    public ResultUtil openAccount(TAccountOpenInfoVo record) {
        if(record.getUserOpenRole().equals(DictEnum.CD.code)){
            if(null == record.getAccountId()){
                record.setAccountId(CurrentUser.getUserAccountId());
            }
        }
        TZtAccountOpenInfoExample example = new TZtAccountOpenInfoExample();
        TZtAccountOpenInfoExample.Criteria cr = example.createCriteria();
        cr.andAccountIdEqualTo(record.getAccountId());
        cr.andUserOpenRoleEqualTo(record.getUserOpenRole());
        List<TZtAccountOpenInfo> ztAccountOpenInfoList = ztAccountOpenInfoMapper.selectByExample(example);
        if(ztAccountOpenInfoList.size()>0){
            if(!ztAccountOpenInfoList.get(0).getStatus().equals(2)){
                return ResultUtil.error("已申请开户！");
            }
        }
        TAccount tAccount = tAccountMapper.selectByPrimaryKey(record.getAccountId());
        //企业开户
        OpenRealNameIdentitySubmitReq realNameIdentitySubmitRequest = null;
        //个人开户
        OpenRealNamePersonalApplyReq realNamePersonalApplyRequest = null;
        if(record.getUserOpenRole().equals(DictEnum.PF.code) || record.getUserOpenRole().equals(DictEnum.CA.code)){
            TCarrierInfo tCarrierInfo = carrierInfoMapper.selectByPrimaryKey(record.getCarrierCompanyEndUserId());
            realNameIdentitySubmitRequest = resultCarrierAndCompany(record.getUserOpenRole(),JSONObject.toJSONString(tCarrierInfo),record.getRemark());
        }else if(record.getUserOpenRole().equals(DictEnum.BD.code)){
            TCompanyInfo tCompanyInfo = companyInfoMapper.selectByPrimaryKey(record.getCarrierCompanyEndUserId());
            realNameIdentitySubmitRequest = resultCarrierAndCompany(record.getUserOpenRole(),JSONObject.toJSONString(tCompanyInfo),record.getRemark());
        }else if(record.getUserOpenRole().equals(DictEnum.CD.code)){
            TEndUserInfo tEndUserInfo = endUserInfoMapper.selectByPrimaryKey(record.getCarrierCompanyEndUserId());
            /*if(null == tEndUserInfo.getIdcardValidBeginning() || "".equals(tEndUserInfo.getIdcardValidBeginning())){
                return ResultUtil.error("开户失败身份证有效期始不能为空");
            }
            if(null == tEndUserInfo.getIdcardValidUntil() || "".equals(tEndUserInfo.getIdcardValidUntil())){
                return ResultUtil.error("开户失败身份证有效期至不能为空");
            }*/
            realNamePersonalApplyRequest = resultEndUserInfo(tEndUserInfo);
        }
        TZtAccountOpenInfo accountOpenInfo = null;
        if(ztAccountOpenInfoList.size()>0){
            accountOpenInfo = ztAccountOpenInfoList.get(0);
        }else{
            accountOpenInfo = new TZtAccountOpenInfo();
        }
        accountOpenInfo.setAccountId(record.getAccountId());
        accountOpenInfo.setUserOpenRole(record.getUserOpenRole());
        if(record.getUserOpenRole().equals(DictEnum.CD.code)){
            //个人开户
            realNamePersonalApplyRequest.setMessagePhone(tAccount.getAccountNo());
            if(ztAccountOpenInfoList.size()>0){
                if(ztAccountOpenInfoList.get(0).getStatus().equals(2)){
                    realNamePersonalApplyRequest.setPartnerAccId(ztAccountOpenInfoList.get(0).getPartnerAccId());
                }
            }
            log.info("华夏个人开户入参：{}",realNamePersonalApplyRequest);
            ResultUtil resultUtil = cloudPaymentAPI.execute(realNamePersonalApplyRequest);
            log.info("华夏支付 - 华夏个人开户回参，{}", JSONUtil.toJsonStr(resultUtil));
            OpenRealNameApplyResp realNameApplyResponse = CloudPayFormatUtil.ObjToBean(resultUtil, OpenRealNameApplyResp.class);;
            log.info("华夏个人开户回参：{}",realNameApplyResponse);
            accountOpenInfo.setPartnerAccId(realNamePersonalApplyRequest.getPartnerAccId());
            if("00000".equals(realNameApplyResponse.getResponseCode())){
                accountOpenInfo.setChannelId(realNamePersonalApplyRequest.getChannelId());
                accountOpenInfo.setRequestId(realNamePersonalApplyRequest.getRequestId());
                accountOpenInfo.setStatus(0);
                if(null != accountOpenInfo.getId()){
                    ztAccountOpenInfoMapper.updateByPrimaryKeySelective(accountOpenInfo);
                }else{
                    ztAccountOpenInfoMapper.insertSelective(accountOpenInfo);
                }
            }else{
                accountOpenInfo.setStatus(2);
                accountOpenInfo.setResponseMessage(realNameApplyResponse.getResponseDesc());
                if(null != accountOpenInfo.getId()){
                    ztAccountOpenInfoMapper.updateByPrimaryKeySelective(accountOpenInfo);
                }else{
                    ztAccountOpenInfoMapper.insertSelective(accountOpenInfo);
                }
                return ResultUtil.error(realNameApplyResponse.getResponseDesc());
            }
        }else{
            //企业开户
            realNameIdentitySubmitRequest.setMessagePhone(tAccount.getAccountNo());
            if(ztAccountOpenInfoList.size()>0){
                if(ztAccountOpenInfoList.get(0).getStatus().equals(2)){
                    realNameIdentitySubmitRequest.setPartnerAccId(ztAccountOpenInfoList.get(0).getPartnerAccId());
                }
            }
            log.info("华夏企业开户入参：{}",realNameIdentitySubmitRequest);
            ResultUtil resultUtil = cloudPaymentAPI.execute(realNameIdentitySubmitRequest);
            log.info("华夏支付 - 华夏企业开户回参，{}", JSONUtil.toJsonStr(resultUtil));
            OpenRealNameIdentitySubmitResp submitResponse = CloudPayFormatUtil.ObjToBean(resultUtil, OpenRealNameIdentitySubmitResp.class);;
            log.info("华夏企业开户回参：{}",submitResponse);
            accountOpenInfo.setRemark(record.getRemark());
            accountOpenInfo.setPartnerAccId(realNameIdentitySubmitRequest.getPartnerAccId());
            if("00000".equals(submitResponse.getResponseCode())){
                accountOpenInfo.setChannelId(realNameIdentitySubmitRequest.getChannelId());
                accountOpenInfo.setRequestId(realNameIdentitySubmitRequest.getRequestId());
                accountOpenInfo.setStatus(0);
                if(null != accountOpenInfo.getId()){
                    ztAccountOpenInfoMapper.updateByPrimaryKeySelective(accountOpenInfo);
                }else{
                    ztAccountOpenInfoMapper.insertSelective(accountOpenInfo);
                }
            }else{
                accountOpenInfo.setStatus(2);
                accountOpenInfo.setResponseMessage(submitResponse.getResponseDesc());
                if(null != accountOpenInfo.getId()){
                    ztAccountOpenInfoMapper.updateByPrimaryKeySelective(accountOpenInfo);
                }else{
                    ztAccountOpenInfoMapper.insertSelective(accountOpenInfo);
                }
                return ResultUtil.error(submitResponse.getResponseDesc());
            }

        }
        return ResultUtil.ok("用户注册处理中");
    }

    @Transactional(rollbackOn = Exception.class)
    @Override
    public ResultUtil updateOpenAccount(TAccountOpenInfoVo record) {
        if (record.getUserOpenRole().equals(DictEnum.CD.code)) {
            if (null == record.getAccountId()) {
                record.setAccountId(CurrentUser.getUserAccountId());
            }
        }
        TZtAccountOpenInfoExample example = new TZtAccountOpenInfoExample();
        TZtAccountOpenInfoExample.Criteria cr = example.createCriteria();
        cr.andAccountIdEqualTo(record.getAccountId());
        cr.andUserOpenRoleEqualTo(record.getUserOpenRole());
        List<TZtAccountOpenInfo> ztAccountOpenInfoList = ztAccountOpenInfoMapper.selectByExample(example);
        TZtAccountOpenInfo accountOpenInfo = null;
        if (ztAccountOpenInfoList.size() > 0) {
            accountOpenInfo = ztAccountOpenInfoList.get(0);
        } else {
            accountOpenInfo = new TZtAccountOpenInfo();
        }
        accountOpenInfo.setAccountId(record.getAccountId());
        accountOpenInfo.setUserOpenRole(record.getUserOpenRole());


        //企业开户
        OpenRealNameIdentitySubmitModifyReq realNameIdentitySubmitModifyRequest = new OpenRealNameIdentitySubmitModifyReq();
        //个人开户
        OpenRealNamePersonalModifyReq openRealNamePersonalModifyReq = new OpenRealNamePersonalModifyReq();
        if(record.getUserOpenRole().equals(DictEnum.PF.code) || record.getUserOpenRole().equals(DictEnum.CA.code)){
            TCarrierInfo tCarrierInfo = carrierInfoMapper.selectByPrimaryKey(record.getCarrierCompanyEndUserId());
            List<TZtAccountOpenInfo> tZtAccountOpenInfos = ztAccountOpenInfoMapper.selectByBusinessLicenseNo(tCarrierInfo.getBusinessLicenseNo(), accountOpenInfo.getPartnerAccId());
            List<TZtAccountOpenInfo> collect = tZtAccountOpenInfos
                    .stream()
                    .filter(ztAccountOpenInfo -> ztAccountOpenInfo.getStatus() == 1 || ztAccountOpenInfo.getStatus() == 0)
                    .collect(Collectors.toList());
            if (collect.size() > 0) {
                return ResultUtil.error("该营业执照已注册");
            }
            realNameIdentitySubmitModifyRequest = new OpenRealNameIdentitySubmitModifyReq();
            realNameIdentitySubmitModifyRequest.setPartnerId(hxyhPartnerId);
            realNameIdentitySubmitModifyRequest.setRequestId(IdWorkerUtil.getInstance().nextId());
            realNameIdentitySubmitModifyRequest.setRequestTime(DateUtils.getRequestTime());
            realNameIdentitySubmitModifyRequest.setChannelId(hxyhChannelId);
            realNameIdentitySubmitModifyRequest.setContact(tCarrierInfo.getCompanyContacts());
            realNameIdentitySubmitModifyRequest.setContactPhone(tCarrierInfo.getCompanyContactsPhone());
            realNameIdentitySubmitModifyRequest.setBlicCompanyName(tCarrierInfo.getCarrierName());
            realNameIdentitySubmitModifyRequest.setBlicAddress(tCarrierInfo.getCompanyDetailAddress());
            realNameIdentitySubmitModifyRequest.setBlicUscc(tCarrierInfo.getBusinessLicenseNo());
            realNameIdentitySubmitModifyRequest.setLepName(tCarrierInfo.getCompanyLegalPerson());

        } else if(record.getUserOpenRole().equals(DictEnum.BD.code)){
            TCompanyInfo tCompanyInfo = companyInfoMapper.selectByPrimaryKey(record.getCarrierCompanyEndUserId());
            List<TZtAccountOpenInfo> tZtAccountOpenInfos = ztAccountOpenInfoMapper.selectByBusinessLicenseNo(tCompanyInfo.getBusinessLicenseNo(), accountOpenInfo.getPartnerAccId());
            List<TZtAccountOpenInfo> collect = tZtAccountOpenInfos
                    .stream()
                    .filter(ztAccountOpenInfo -> ztAccountOpenInfo.getStatus() == 1 || ztAccountOpenInfo.getStatus() == 0)
                    .collect(Collectors.toList());
            if (collect.size() > 0) {
                return ResultUtil.error("该营业执照已注册");
            }
            realNameIdentitySubmitModifyRequest = new OpenRealNameIdentitySubmitModifyReq();
            realNameIdentitySubmitModifyRequest.setPartnerId(hxyhPartnerId);
            realNameIdentitySubmitModifyRequest.setRequestId(IdWorkerUtil.getInstance().nextId());
            realNameIdentitySubmitModifyRequest.setRequestTime(DateUtils.getRequestTime());
            realNameIdentitySubmitModifyRequest.setChannelId(hxyhChannelId);
            realNameIdentitySubmitModifyRequest.setContact(tCompanyInfo.getCompanyContacts());
            realNameIdentitySubmitModifyRequest.setContactPhone(tCompanyInfo.getCompanyContactsPhone());
            realNameIdentitySubmitModifyRequest.setBlicCompanyName(tCompanyInfo.getCompanyName());
            realNameIdentitySubmitModifyRequest.setBlicAddress(tCompanyInfo.getCompanyDetailAddress());
            realNameIdentitySubmitModifyRequest.setBlicUscc(tCompanyInfo.getBusinessLicenseNo());
            realNameIdentitySubmitModifyRequest.setLepName(tCompanyInfo.getCompanyLegalPerson());
        } else if(record.getUserOpenRole().equals(DictEnum.CD.code)){
            openRealNamePersonalModifyReq = new OpenRealNamePersonalModifyReq();
            openRealNamePersonalModifyReq.setPartnerId(hxyhPartnerId);
            openRealNamePersonalModifyReq.setRequestId(IdWorkerUtil.getInstance().nextId());
            openRealNamePersonalModifyReq.setRequestTime(DateUtils.getRequestTime());
            openRealNamePersonalModifyReq.setChannelId(hxyhChannelId);
            if (!accountOpenInfo.getIfOneselfOpen()) {
                // 本人开户
                TEndUserInfo tEndUserInfo = endUserInfoMapper.selectByPrimaryKey(record.getCarrierCompanyEndUserId());
                List<TZtAccountOpenInfo> tZtAccountOpenInfos = ztAccountOpenInfoMapper.selectByIdcard(tEndUserInfo.getIdcard(), accountOpenInfo.getPartnerAccId());
                List<TZtAccountOpenInfo> collect = tZtAccountOpenInfos
                        .stream()
                        .filter(tZtAccountOpenInfo -> tZtAccountOpenInfo.getStatus() == 1 || tZtAccountOpenInfo.getStatus() == 0)
                        .collect(Collectors.toList());
                if (collect.size() > 0) {
                    return ResultUtil.error("该身份证已开户");
                }
                openRealNamePersonalModifyReq.setLepName(tEndUserInfo.getRealName());
                openRealNamePersonalModifyReq.setLepCardNo(tEndUserInfo.getIdcard());
                openRealNamePersonalModifyReq.setLepPhone(tEndUserInfo.getPhone());
            } else {
                // 非本人开户
                openRealNamePersonalModifyReq.setLepName(record.getOpenRealName());
                openRealNamePersonalModifyReq.setLepCardNo(record.getOpenIdCard());
                openRealNamePersonalModifyReq.setLepPhone(record.getOpenPhone());
            }

        }

        if (record.getUserOpenRole().equals(DictEnum.CD.code)) {
            //个人开户
            if (null != accountOpenInfo.getPartnerAccId()) {
                openRealNamePersonalModifyReq.setPartnerAccId(accountOpenInfo.getPartnerAccId());
            }
            log.info("华夏个人开户变更入参：{}", JSONUtil.toJsonStr(openRealNamePersonalModifyReq));
            ResultUtil resultUtil = cloudPaymentAPI.execute(openRealNamePersonalModifyReq);
            log.info("华夏支付 - 华夏个人开户变更回参，{}", JSONUtil.toJsonStr(resultUtil));
            OpenRealNameApplyResp realNameApplyResponse = CloudPayFormatUtil.ObjToBean(resultUtil, OpenRealNameApplyResp.class);
            log.info("华夏个人开户变更回参：{}", realNameApplyResponse);
            accountOpenInfo.setPartnerAccId(openRealNamePersonalModifyReq.getPartnerAccId());
            if (!"00000".equals(realNameApplyResponse.getResponseCode())) {
                accountOpenInfo.setResponseMessage("开户变更失败: " + realNameApplyResponse.getResponseDesc());
                if (null != accountOpenInfo.getId()) {
                    ztAccountOpenInfoMapper.updateByPrimaryKeySelective(accountOpenInfo);
                }
                return ResultUtil.error("开户变更失败: " + realNameApplyResponse.getResponseDesc());
            }
            if (null != record.getIfOneselfOpen() && record.getIfOneselfOpen()) {
                // 非本人开户, 更新开户信息
                accountOpenInfo.setOpenRealName(record.getOpenRealName());
                accountOpenInfo.setOpenIdCard(record.getOpenIdCard());
                accountOpenInfo.setOpenPhone(record.getOpenPhone());
                ztAccountOpenInfoMapper.updateByPrimaryKey(accountOpenInfo);
            }
        } else {
            //企业开户
            TAccount tAccount = tAccountMapper.selectByPrimaryKey(record.getAccountId());
            realNameIdentitySubmitModifyRequest.setMessagePhone(tAccount.getAccountNo());
            if (null != accountOpenInfo.getPartnerAccId()) {
                realNameIdentitySubmitModifyRequest.setPartnerAccId(accountOpenInfo.getPartnerAccId());
            }
            log.info("华夏企业开户变更入参：{}", JSONUtil.toJsonStr(realNameIdentitySubmitModifyRequest));
            ResultUtil resultUtil = cloudPaymentAPI.execute(realNameIdentitySubmitModifyRequest);
            log.info("华夏支付 - 华夏企业开户变更回参，{}", JSONUtil.toJsonStr(resultUtil));
            OpenRealNameIdentitySubmitResp submitResponse = CloudPayFormatUtil.ObjToBean(resultUtil, OpenRealNameIdentitySubmitResp.class);
            log.info("华夏企业开户变更回参：{}", submitResponse);

            if (!"00000".equals(submitResponse.getResponseCode())) {
                accountOpenInfo.setResponseMessage("开户变更失败: " + submitResponse.getResponseDesc());
                if (null != accountOpenInfo.getId()) {
                    ztAccountOpenInfoMapper.updateByPrimaryKeySelective(accountOpenInfo);
                }
                return ResultUtil.error(submitResponse.getResponseDesc());
            }

        }
        return ResultUtil.ok("处理中");
    }


    /**
     *  @author: dingweibo
     *  @Date: 2023/1/9 14:24
     *  @Description: 司机非本人开户回显
     */
    @Override
    public TZtAccountOpenInfo selectNoPersonalOpenRoleById(Integer accountId) {
        return ztAccountOpenInfoMapper.selectByAccountId(accountId,DictEnum.CD.code);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2023/1/9 15:39
     *  @Description: 华夏司机非本人开户
     */
    @Transactional(rollbackOn = Exception.class)
    @Override
    public ResultUtil noPersonalOpenRole(TAccountOpenInfoVo record) {
        if(record.getUserOpenRole().equals(DictEnum.CD.code)){
            if(null == record.getAccountId()){
                record.setAccountId(CurrentUser.getUserAccountId());
            }
        }
        TZtAccountOpenInfoExample example = new TZtAccountOpenInfoExample();
        TZtAccountOpenInfoExample.Criteria cr = example.createCriteria();
        cr.andAccountIdEqualTo(record.getAccountId());
        cr.andUserOpenRoleEqualTo(record.getUserOpenRole());
        List<TZtAccountOpenInfo> ztAccountOpenInfoList = ztAccountOpenInfoMapper.selectByExample(example);
        if(ztAccountOpenInfoList.size()>0){
            if(!ztAccountOpenInfoList.get(0).getStatus().equals(2)){
                return ResultUtil.error("已申请开户！");
            }
        }
        TZtAccountOpenInfo re = new TZtAccountOpenInfo();
        re.setAccountId(record.getAccountId());
        re.setOpenPhone(record.getOpenPhone());
        List<TZtAccountOpenInfo> ztAccountOpenInfoList2 = ztAccountOpenInfoMapper.selectNotByAccountId(re);
        if(ztAccountOpenInfoList2.size()>0){
            return ResultUtil.error("当前手机号已申请开户！");
        }
        TZtAccountOpenInfo re2 = new TZtAccountOpenInfo();
        re2.setAccountId(record.getAccountId());
        re2.setOpenIdCard(record.getOpenIdCard());
        List<TZtAccountOpenInfo> ztAccountOpenInfoList3 = ztAccountOpenInfoMapper.selectNotByAccountId(re2);
        if(ztAccountOpenInfoList3.size()>0){
            return ResultUtil.error("当前身份证号已申请开户！");
        }

        TEndUserInfoExample tEndUserInfoExample = new TEndUserInfoExample();
        TEndUserInfoExample.Criteria cr3 = tEndUserInfoExample.createCriteria();
        cr3.andPhoneEqualTo(record.getOpenPhone());
        List<TEndUserInfo> tEndUserInfoList = endUserInfoMapper.selectByExample(tEndUserInfoExample);
        if(tEndUserInfoList.size()>0){
            return ResultUtil.error("当前手机号已存在平台用户中，不允许非本人开户！");
        }
        List<TEndUserInfo> tEndUserInfoList2 = endUserInfoMapper.selectByIdcard(record.getOpenIdCard());
        if(tEndUserInfoList2.size()>0){
            return ResultUtil.error("当前身份证号已存在平台用户中，不允许非本人开户！");
        }
        TAccount tAccount = tAccountMapper.selectByPrimaryKey(record.getAccountId());
        OpenRealNamePersonalApplyReq realNamePersonalApplyRequest = null;
        TEndUserInfo tEndUserInfo = new TEndUserInfo();
        tEndUserInfo.setRealName(record.getOpenRealName());
        tEndUserInfo.setPhone(record.getOpenPhone());
        tEndUserInfo.setIdcard(record.getOpenIdCard());
        tEndUserInfo.setIdcardValidUntil(null);
        tEndUserInfo.setIdcardValidBeginning(null);
        realNamePersonalApplyRequest = resultEndUserInfo(tEndUserInfo);
        TZtAccountOpenInfo accountOpenInfo = null;
        if(ztAccountOpenInfoList.size()>0){
            accountOpenInfo = ztAccountOpenInfoList.get(0);
        }else{
            accountOpenInfo = new TZtAccountOpenInfo();
        }
        accountOpenInfo.setAccountId(record.getAccountId());
        accountOpenInfo.setUserOpenRole(record.getUserOpenRole());
        accountOpenInfo.setOpenRealName(record.getOpenRealName());
        accountOpenInfo.setOpenPhone(record.getOpenPhone());
        accountOpenInfo.setOpenIdCard(record.getOpenIdCard());
        accountOpenInfo.setIfOneselfOpen(true);
        realNamePersonalApplyRequest.setMessagePhone(tAccount.getAccountNo());
        if(ztAccountOpenInfoList.size()>0){
            if(ztAccountOpenInfoList.get(0).getStatus().equals(2)){
                realNamePersonalApplyRequest.setPartnerAccId(ztAccountOpenInfoList.get(0).getPartnerAccId());
            }
        }
        log.info("华夏非本人开户入参：{}",realNamePersonalApplyRequest);
        ResultUtil resultUtil = cloudPaymentAPI.execute(realNamePersonalApplyRequest);
        log.info("华夏支付 - 华夏非本人开户回参，{}", JSONUtil.toJsonStr(resultUtil));
        OpenRealNameApplyResp realNameApplyResponse = CloudPayFormatUtil.ObjToBean(resultUtil, OpenRealNameApplyResp.class);;
        log.info("华夏非本人开户回参：{}",realNameApplyResponse);
        accountOpenInfo.setPartnerAccId(realNamePersonalApplyRequest.getPartnerAccId());
        if("00000".equals(realNameApplyResponse.getResponseCode())){
            accountOpenInfo.setChannelId(realNamePersonalApplyRequest.getChannelId());
            accountOpenInfo.setRequestId(realNamePersonalApplyRequest.getRequestId());
            accountOpenInfo.setStatus(0);
            if(null != accountOpenInfo.getId()){
                ztAccountOpenInfoMapper.updateByPrimaryKeySelective(accountOpenInfo);
            }else{
                ztAccountOpenInfoMapper.insertSelective(accountOpenInfo);
            }
        }else{
            accountOpenInfo.setStatus(2);
            accountOpenInfo.setResponseMessage(realNameApplyResponse.getResponseDesc());
            if(null != accountOpenInfo.getId()){
                ztAccountOpenInfoMapper.updateByPrimaryKeySelective(accountOpenInfo);
            }else{
                ztAccountOpenInfoMapper.insertSelective(accountOpenInfo);
            }
            return ResultUtil.error(realNameApplyResponse.getResponseDesc());
        }

        return ResultUtil.ok("用户注册处理中");
    }

    public static void main(String[] args){
        String cardNo = "****************";
        String url = "https://ccdcapi.alipay.com/validateAndCacheCardInfo.json?_input_charset=utf-8&cardNo="+cardNo+"&cardBinCheck=false";

        Map<String,Object> map =  AliPayCardDetailUtil.getCardDetail(cardNo);
        System.out.println(JSONObject.toJSONString(map));
    }

    private OpenRealNameIdentitySubmitReq resultCarrierAndCompany(String openUserRole, String jsonString,String remark){
        TCarrierInfo tCarrierInfo = null;
        TCompanyInfo tCompanyInfo = null;
        String blicCompanyName = "";
        String blicUscc = "";
        String blicAddress = "";
        String lepName = "";
        String lepPhone = "";
        String lepCardNo = "";
        String contact = "";
        String contactPhone = "";
        if(openUserRole.equals(DictEnum.CA.code) || openUserRole.equals(DictEnum.PF.code)){
            tCarrierInfo =  JSONObject.parseObject(jsonString,TCarrierInfo.class);
            blicCompanyName = tCarrierInfo.getCarrierName();
            blicUscc = tCarrierInfo.getBusinessLicenseNo();
            blicAddress = tCarrierInfo.getCompanyDetailAddress();
            lepName = tCarrierInfo.getCompanyLegalPerson();
            lepPhone = tCarrierInfo.getCompanyContactsPhone();
            lepCardNo = tCarrierInfo.getCompanyLegalPersonIdcard();
            contact = tCarrierInfo.getCompanyContacts();
            contactPhone = tCarrierInfo.getCompanyContactsPhone();

        }else if(openUserRole.equals(DictEnum.BD.code)){
            tCompanyInfo = JSONObject.parseObject(jsonString,TCompanyInfo.class);
            blicCompanyName = tCompanyInfo.getCompanyName();
            blicUscc = tCompanyInfo.getBusinessLicenseNo();
            blicAddress = tCompanyInfo.getCompanyDetailAddress();
            lepName = tCompanyInfo.getCompanyLegalPerson();
            lepPhone = tCompanyInfo.getCompanyContactsPhone();
            lepCardNo = tCompanyInfo.getCompanyLegalPersonIdcard();
            contact = tCompanyInfo.getCompanyContacts();
            contactPhone = tCompanyInfo.getCompanyContactsPhone();
        }
        OpenRealNameIdentitySubmitReq realNameIdentitySubmitRequest = new OpenRealNameIdentitySubmitReq();
        realNameIdentitySubmitRequest.setPartnerId(hxyhPartnerId);
        realNameIdentitySubmitRequest.setRequestId(IdWorkerUtil.getInstance().nextId());
        realNameIdentitySubmitRequest.setRequestTime(DateUtils.getRequestTime());
        realNameIdentitySubmitRequest.setCurrency(DictEnum.CNY.code);
        realNameIdentitySubmitRequest.setChannelId(hxyhChannelId);
        realNameIdentitySubmitRequest.setPartnerAccId(IdWorkerUtil.getInstance().nextId());
        realNameIdentitySubmitRequest.setBoothNo(IdWorkerUtil.getInstance().nextId());
        realNameIdentitySubmitRequest.setTradeMemberGrade(1);
        realNameIdentitySubmitRequest.setBlicCompanyName(blicCompanyName);
        realNameIdentitySubmitRequest.setBlicUscc(blicUscc);
        realNameIdentitySubmitRequest.setBlicAddress(blicAddress);
        realNameIdentitySubmitRequest.setLepName(lepName);
        realNameIdentitySubmitRequest.setLepPhone(lepPhone);
        realNameIdentitySubmitRequest.setLepCardType(10);
        realNameIdentitySubmitRequest.setLepCardNo(lepCardNo);
        realNameIdentitySubmitRequest.setBlicCardType(16);
        realNameIdentitySubmitRequest.setEmail("<EMAIL>");
        realNameIdentitySubmitRequest.setIsMessager(2);
        realNameIdentitySubmitRequest.setOccBankAccountType(0);
        realNameIdentitySubmitRequest.setOccBankAccount("");
        realNameIdentitySubmitRequest.setOccBankAccountName("");
        realNameIdentitySubmitRequest.setOccBankPhone("");
        realNameIdentitySubmitRequest.setOccBankCode("");
        realNameIdentitySubmitRequest.setOccBankName("");
        realNameIdentitySubmitRequest.setLinkAccountType(0);
        realNameIdentitySubmitRequest.setContact(contact);
        realNameIdentitySubmitRequest.setContactPhone(contactPhone);
        realNameIdentitySubmitRequest.setAccountSign(0);
        realNameIdentitySubmitRequest.setIsOtherBank(0);
        realNameIdentitySubmitRequest.setIsSecondAcc(0);
        realNameIdentitySubmitRequest.setStrideValidate(0);
        realNameIdentitySubmitRequest.setContactAddr(blicAddress);
        realNameIdentitySubmitRequest.setContactRemark(remark);
        return realNameIdentitySubmitRequest;
    }


    private OpenRealNamePersonalApplyReq resultEndUserInfo(TEndUserInfo endUserInfo){
        OpenRealNamePersonalApplyReq personalApplyRequest = new OpenRealNamePersonalApplyReq();
        personalApplyRequest.setPartnerId(hxyhPartnerId);
        personalApplyRequest.setRequestId(IdWorkerUtil.getInstance().nextId());
        personalApplyRequest.setRequestTime(DateUtils.getRequestTime());
        personalApplyRequest.setChannelId(hxyhChannelId);
        personalApplyRequest.setCurrency(DictEnum.CNY.code);
        personalApplyRequest.setPartnerAccId(IdWorkerUtil.getInstance().nextId());
        personalApplyRequest.setBoothNo(IdWorkerUtil.getInstance().nextId());
        personalApplyRequest.setTradeMemberGrade(1);
        personalApplyRequest.setLepName(endUserInfo.getRealName());
        personalApplyRequest.setLepCardType(10);
        personalApplyRequest.setLepCardNo(endUserInfo.getIdcard());
        personalApplyRequest.setLepPhone(endUserInfo.getPhone());
        personalApplyRequest.setEmail("<EMAIL>");
        personalApplyRequest.setIsMessager(2);
        if (null != endUserInfo.getIdcardValidUntil() && null != endUserInfo.getIdcardValidBeginning()) {
            if(endUserInfo.getIdcardValidUntil().getTime() > endUserInfo.getIdcardValidBeginning().getTime()){
                personalApplyRequest.setLepValidityBegin(DateUtils.formatDate(endUserInfo.getIdcardValidBeginning()));
                personalApplyRequest.setLepValidityEnd(DateUtils.formatDate(endUserInfo.getIdcardValidUntil()));
            }else{
                personalApplyRequest.setLepValidityBegin(DateUtils.formatDate(endUserInfo.getIdcardValidUntil()));
                personalApplyRequest.setLepValidityEnd(DateUtils.formatDate(endUserInfo.getIdcardValidBeginning()));
            }
        } else {
            if (null != endUserInfo.getIdcardValidUntil()) {
                personalApplyRequest.setLepValidityEnd(DateUtils.formatDate(endUserInfo.getIdcardValidUntil()));
            } else if (null != endUserInfo.getIdcardValidBeginning()) {
                personalApplyRequest.setLepValidityBegin(DateUtils.formatDate(endUserInfo.getIdcardValidBeginning()));
            }
        }

        personalApplyRequest.setOccBankAccount("");
        personalApplyRequest.setOccBankAccountName("");
        personalApplyRequest.setOccBankPhone("");
        personalApplyRequest.setOccBankCode("");
        personalApplyRequest.setOccBankName("");
        personalApplyRequest.setOccBankAccountType("");
        personalApplyRequest.setLinkAccountType(0);
        personalApplyRequest.setAccountSign(0);
        personalApplyRequest.setIsOtherBank(0);
        personalApplyRequest.setIsSecondAcc(0);
        personalApplyRequest.setStrideValidate(0);
        return personalApplyRequest;
    }
    /**
     *  @author: dingweibo
     *  @Date: 2021/8/6 8:50
     *  @Description: 京东申请企业开户 上传图片（承运方 共用）
     */
    @Override
    public ResultUtil uploadPhoto(TCarrierCompanyOpenRoleVo record) throws IOException {
        TCarrierCompanyOpenRole tCarrierCompanyOpenRole2 = tCarrierCompanyOpenRoleMapper.selectByRoleBy(record);
        if(tCarrierCompanyOpenRole2.equals(JdEnum.OPENSUCCESS.code) || tCarrierCompanyOpenRole2.equals(JdEnum.OPENHANDLE.code)){
            return ResultUtil.error("已申请开户！");
        }
        //身份证人像页
        OpenRealNameUploadReq req = new OpenRealNameUploadReq();
        req.setPartnerId(partnerId);
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        //区分承运方与企业
        req.setPartnerMemberId(record.getUserOpenRole()+record.getCarrierCompanyId());
        req.setImageType(JdEnum.LEPURLA.code);
        SysParam sysParam = sysParamAPI.getParamByKey("DISPLAYIMAGEIP");
       if(record.getLepUrla().indexOf("group")>=0){
           req.setBase64Text(FileBase64Util.getFileBase64(sysParam.getParamValue()+record.getLepUrla()));
       }else{
           req.setBase64Text(FileBase64Util.getFileBase64(record.getLepUrla()));
       }
        ResultUtil resultUtil1 = cloudPayAPI.cloudPay(req);
        OpenRealNameUploadResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, OpenRealNameUploadResponse.class);

        //身份证国徽页
        OpenRealNameUploadReq req2 = new OpenRealNameUploadReq();
        req2.setPartnerId(partnerId);
        req2.setRequestId(IdWorkerUtil.getInstance().nextId());
        req2.setRequestTime(DateUtils.getRequestTime());
        //区分承运方与企业
        req2.setPartnerMemberId(record.getUserOpenRole()+record.getCarrierCompanyId());
        req2.setImageType(JdEnum.LEPURLB.code);

        if(record.getLepUrlb().indexOf("group")>=0){
            req2.setBase64Text(FileBase64Util.getFileBase64(sysParam.getParamValue()+record.getLepUrlb()));
        }else{
            req2.setBase64Text(FileBase64Util.getFileBase64(record.getLepUrlb()));
        }

        ResultUtil resultUtil12 = cloudPayAPI.cloudPay(req2);
        OpenRealNameUploadResponse response2 = CloudPayFormatUtil.ObjToBean(resultUtil12, OpenRealNameUploadResponse.class);

        //营业执照
        OpenRealNameUploadReq req3 = new OpenRealNameUploadReq();
        req3.setPartnerId(partnerId);
        req3.setRequestId(IdWorkerUtil.getInstance().nextId());
        req3.setRequestTime(DateUtils.getRequestTime());
        //区分承运方与企业
        req3.setPartnerMemberId(record.getUserOpenRole()+record.getCarrierCompanyId());
        req3.setImageType(JdEnum.BLICURLA.code);

        if(record.getBlicUrla().indexOf("group")>=0){
            req3.setBase64Text(FileBase64Util.getFileBase64(sysParam.getParamValue()+record.getBlicUrla()));
        }else{
            req3.setBase64Text(FileBase64Util.getFileBase64(record.getBlicUrla()));
        }

        ResultUtil resultUtil13 = cloudPayAPI.cloudPay(req3);
        OpenRealNameUploadResponse response3 = CloudPayFormatUtil.ObjToBean(resultUtil13, OpenRealNameUploadResponse.class);


        //经营场所
        OpenRealNameUploadReq req4 = new OpenRealNameUploadReq();
        req4.setPartnerId(partnerId);
        req4.setRequestId(IdWorkerUtil.getInstance().nextId());
        req4.setRequestTime(DateUtils.getRequestTime());
        //区分承运方与企业
        req4.setPartnerMemberId(record.getUserOpenRole()+record.getCarrierCompanyId());
        req4.setImageType(JdEnum.BUSSLOCATIONURLA.code);
        if(record.getBussLocationUrla().indexOf("group")>=0){
            req4.setBase64Text(FileBase64Util.getFileBase64(sysParam.getParamValue()+record.getBussLocationUrla()));
        }else{
            req4.setBase64Text(FileBase64Util.getFileBase64(record.getBussLocationUrla()));
        }

        ResultUtil resultUtil14 = cloudPayAPI.cloudPay(req4);
        OpenRealNameUploadResponse response4 = CloudPayFormatUtil.ObjToBean(resultUtil14, OpenRealNameUploadResponse.class);


        TCarrierCompanyOpenRole tCarrierCompanyOpenRole = tCarrierCompanyOpenRoleMapper.selectByRoleBy(record);
        tCarrierCompanyOpenRole.setUpdateTime(new Date());
        if(response.getResponseCode().equals(JdEnum.JDSUCCESSCODE.code)
                && response2.getResponseCode().equals(JdEnum.JDSUCCESSCODE.code)
                    && response3.getResponseCode().equals(JdEnum.JDSUCCESSCODE.code)
                        && response4.getResponseCode().equals(JdEnum.JDSUCCESSCODE.code)){

            tCarrierCompanyOpenRole.setLepUrla(record.getLepUrla());
            tCarrierCompanyOpenRole.setAttachkeyLepUrla(response.getAttachKey());

            tCarrierCompanyOpenRole.setLepUrlb(record.getLepUrlb());
            tCarrierCompanyOpenRole.setAttachkeyLepUrlb(response2.getAttachKey());

            tCarrierCompanyOpenRole.setBlicUrla(record.getBlicUrla());
            tCarrierCompanyOpenRole.setAttachkeyBlicurla(response3.getAttachKey());
            tCarrierCompanyOpenRole.setBussLocationUrla(record.getBussLocationUrla());
            tCarrierCompanyOpenRole.setAttachkeyBussLocationUrla(response4.getAttachKey());
            if(!tCarrierCompanyOpenRole.getOpenStatus().equals(JdEnum.OPENSUCCESS.code) || !tCarrierCompanyOpenRole.getOpenStatus().equals(JdEnum.OPENHANDLE.code)){
                tCarrierCompanyOpenRole.setOpenStatus(JdEnum.UPLOADPHOTOSUCCESS.code);
                tCarrierCompanyOpenRole.setOpenResponseDesc(null);
        }
            tCarrierCompanyOpenRoleMapper.updateByPrimaryKeySelective(tCarrierCompanyOpenRole);

            return ResultUtil.ok("上传图片成功");
        }else{
            tCarrierCompanyOpenRole.setOpenStatus(JdEnum.UPLOADPHOTOERROR.code);
            tCarrierCompanyOpenRole.setOpenResponseDesc(response.getResponseDesc()+","+response2.getResponseDesc() +","+response3.getResponseDesc()+","+response4.getResponseDesc());
            tCarrierCompanyOpenRoleMapper.updateByPrimaryKeySelective(tCarrierCompanyOpenRole);
            return ResultUtil.error("上传图片失败:"+response.getResponseDesc());
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/13 9:03
     *  @Description: 提交开户资料申请开户
     */
    @Override
    public ResultUtil openRealNameApply(TCarrierCompanyOpenRoleVo record) {
        TCarrierCompanyOpenRole tCarrierCompanyOpenRole = tCarrierCompanyOpenRoleMapper.selectByRoleBy(record);
        if(tCarrierCompanyOpenRole.getOpenStatus().equals(JdEnum.OPENSUCCESS.code) || tCarrierCompanyOpenRole.getOpenStatus().equals(JdEnum.OPENHANDLE.code)){
             return ResultUtil.error("已申请开户！");
        }
        OpenRealNameIdentitySubmitReq req = new OpenRealNameIdentitySubmitReq();
        req.setPartnerId(partnerId);
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        //req.setPartnerMemberId(record.getUserOpenRole()+record.getCarrierCompanyId());
        req.setRealNameType(JdEnum.ENTER.code);//实名类型
        req.setBlicCompanyName(record.getCarrierName());//企业名称全称
        req.setBlicUscc(record.getComBlicNo());//统一社会信用代码
        req.setBlicValidityEnd(DateUtils.formatDate(record.getComBlicDateEnd()));
        req.setBlicAddress(record.getBlicAddress());
        req.setBlicUrlA(tCarrierCompanyOpenRole.getAttachkeyBlicurla());
        //req.setBussLocationUrlA(tCarrierCompanyOpenRole.getAttachkeyBussLocationUrla());
        req.setLepName(record.getComLepName());
        req.setLepCardNo(record.getComLepIdcard());
        req.setLepValidityBegin(DateUtils.formatDate(record.getLepValidityBegin()));
        req.setLepValidityEnd(DateUtils.formatDate(record.getLepCardValidityEnd()));
        req.setLepUrlA(tCarrierCompanyOpenRole.getAttachkeyLepUrla());
        req.setLepPhone(record.getLepPhone());
        req.setLepUrlB(tCarrierCompanyOpenRole.getAttachkeyLepUrlb());
        //req.setCustomerCategory(record.getCustomerCategory());
        req.setOccBankPhone(record.getComPhone());

        //股东信息
        List<ShareholderInfo> shareholderInfoList = new ArrayList<>();
        ShareholderInfo sh = new ShareholderInfo();
        sh.setShareholderNo(record.getShareholderNo());
        sh.setShareholderName(record.getShareholderName());
        sh.setShareholderPhone(record.getShareholderPhone());
        sh.setCardType(record.getShareholderCardType());
        sh.setCardNo(record.getShareholderCardNo());
        sh.setCardValidityBegin(DateUtil.dateToStr(record.getShareholderCardValidityBegin(),2));
        sh.setCardValidityEnd(DateUtil.dateToStr(record.getShareholderCardValidityEnd(),2));
        shareholderInfoList.add(sh);
        //req.setShareholderList(shareholderInfoList);

        //受益人信息
        List<BeneficiaryInfo> beneficiaryInfoList = new ArrayList<>();
        BeneficiaryInfo be = new BeneficiaryInfo();
        be.setBeneficiaryNo(record.getBeneficiaryNo());
        be.setBeneficiaryName(record.getBeneficiaryName());
        be.setBeneficiaryPhone(record.getBeneficiaryPhone());
        be.setCardType(record.getBeneficiaryCardType());
        be.setCardNo(record.getBeneficiaryCardNo());
        be.setCardValidityBegin(DateUtil.dateToStr(record.getBeneficiaryCardValidityBegin(),2));
        be.setCardValidityEnd(DateUtil.dateToStr(record.getBeneficiaryCardValidityEnd(),2));
        be.setBeneficiaryAddress(record.getBeneficiaryAddress());
        beneficiaryInfoList.add(be);
        //req.setBeneficiaryList(beneficiaryInfoList);
        //调用京东接口 资料上传
        ResultUtil resultUtil1 = cloudPayAPI.cloudPay(req);
        OpenRealNameIdentitySubmitResp response = CloudPayFormatUtil.ObjToBean(resultUtil1, OpenRealNameIdentitySubmitResp.class);
        if(response.getResponseCode().equals(JdEnum.JDSUCCESSCODE.code)){
            tCarrierCompanyOpenRole.setCustomerCategory(record.getCustomerCategory());
            tCarrierCompanyOpenRole.setLepPhone(record.getLepPhone());
            tCarrierCompanyOpenRole.setLepCardValidityEnd(record.getLepCardValidityEnd());
            tCarrierCompanyOpenRole.setShareholderNo(record.getShareholderNo());
            tCarrierCompanyOpenRole.setShareholderName(record.getShareholderName());
            tCarrierCompanyOpenRole.setShareholderCardType(record.getShareholderCardType());
            tCarrierCompanyOpenRole.setShareholderCardNo(record.getShareholderCardNo());
            tCarrierCompanyOpenRole.setShareholderCardValidityBegin(record.getShareholderCardValidityBegin());
            tCarrierCompanyOpenRole.setShareholderCardValidityEnd(record.getShareholderCardValidityEnd());
            tCarrierCompanyOpenRole.setShareholderPhone(record.getShareholderPhone());
            tCarrierCompanyOpenRole.setBeneficiaryNo(record.getBeneficiaryNo());
            tCarrierCompanyOpenRole.setBeneficiaryName(record.getBeneficiaryName());
            tCarrierCompanyOpenRole.setBeneficiaryCardType(record.getBeneficiaryCardType());
            tCarrierCompanyOpenRole.setBeneficiaryCardNo(record.getBeneficiaryCardNo());
            tCarrierCompanyOpenRole.setBeneficiaryCardValidityBegin(record.getBeneficiaryCardValidityBegin());
            tCarrierCompanyOpenRole.setBeneficiaryCardValidityEnd(record.getBeneficiaryCardValidityEnd());
            tCarrierCompanyOpenRole.setBeneficiaryPhone(record.getBeneficiaryPhone());
            tCarrierCompanyOpenRole.setBeneficiaryAddress(record.getBeneficiaryAddress());
            if(!tCarrierCompanyOpenRole.equals(JdEnum.OPENSUCCESS.code) || !tCarrierCompanyOpenRole.equals(JdEnum.OPENHANDLE.code)){
                tCarrierCompanyOpenRole.setOpenStatus(JdEnum.UPLOADMEANSSUCCESS.code);
            }
            tCarrierCompanyOpenRole.setUpdateTime(new Date());
            tCarrierCompanyOpenRoleMapper.updateByPrimaryKeySelective(tCarrierCompanyOpenRole);
            if(tCarrierCompanyOpenRole.equals(JdEnum.OPENSUCCESS.code) || tCarrierCompanyOpenRole.equals(JdEnum.OPENHANDLE.code)){
                return ResultUtil.error("已申请开户！");
            }else{
                //企业开户接口
                OpenRealNameApplyReq req2 = new OpenRealNameApplyReq();
                req2.setPartnerId(partnerId);
                String requestId = "com"+ IdWorkerUtil.getInstance().nextId().substring(3,32);
                req2.setRequestId(requestId);
                req2.setRequestTime(DateUtils.getRequestTime());
                req2.setPartnerMemberId(record.getUserOpenRole()+record.getCarrierCompanyId());
                req2.setChannelId(JdEnum.channelId.code);
                req2.setPartnerAccId(IdWorkerUtil.getInstance().nextId());
                ResultUtil resultUtil12 = cloudPayAPI.cloudPay(req2);
                OpenRealNameApplyResp response2 = CloudPayFormatUtil.ObjToBean(resultUtil12, OpenRealNameApplyResp.class);
                TCarrierCompanyOpenRole openRole = tCarrierCompanyOpenRoleMapper.selectByRoleBy(record);
                openRole.setUpdateTime(new Date());
                if(response2.getResponseCode().equals(JdEnum.JDSUCCESSCODE.code)){
                    openRole.setOpenStatus(JdEnum.OPENHANDLE.code);
                    openRole.setPartnerAccId(req2.getPartnerAccId());
                    tCarrierCompanyOpenRoleMapper.updateByPrimaryKeySelective(openRole);
                    return ResultUtil.ok("开户申请成功");
                }else{
                    openRole.setOpenStatus(JdEnum.OPENERROR.code);
                    tCarrierCompanyOpenRoleMapper.updateByPrimaryKeySelective(openRole);
                    return  ResultUtil.error("开户失败"+response2.getResponseDesc());
                }
            }
        }else{
            tCarrierCompanyOpenRole.setOpenStatus(JdEnum.UPLOADMEANSERROR.code);
            tCarrierCompanyOpenRoleMapper.updateByPrimaryKeySelective(tCarrierCompanyOpenRole);
            return  ResultUtil.error("资料上传失败"/*+response.getItemResult()*/);
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/6 14:05
     *  @Description: 更新开户申请状态
     */
    @Override
    public ResultUtil updateOpenStatus(TCarrierCompanyOpenRoleVo record) {
        OpenQueryAccountInfoReq req = new OpenQueryAccountInfoReq();
        req.setPartnerId(partnerId);
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setPartnerMemberId(record.getUserOpenRole()+record.getCarrierCompanyId());
        req.setPartnerAccId(record.getPartnerAccId());
        ResultUtil resultUtil1 = cloudPayAPI.cloudPay(req);
        OpenQueryAccountInfoResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, OpenQueryAccountInfoResponse.class);
        TCarrierCompanyOpenRole tCarrierCompanyOpenRole = tCarrierCompanyOpenRoleMapper.selectByRoleBy(record);
        if(response.getResponseCode().equals(JdEnum.JDSUCCESSCODE.code)){
            if(response.getStatus().equals("SUCCESS")){
                tCarrierCompanyOpenRole.setOpenStatus(JdEnum.OPENSUCCESS.code);
                tCarrierCompanyOpenRole.setOpenResponseDesc("成功");
                tCarrierCompanyOpenRole.setBankAccountNo(response.getBankAccountNo());
            }else if(response.getStatus().equals("FAIL")){
                tCarrierCompanyOpenRole.setOpenStatus(JdEnum.OPENERROR.code);
                tCarrierCompanyOpenRole.setOpenResponseDesc(response.getFailReason());
            }else if(response.getStatus().equals("PROCESS")){
                tCarrierCompanyOpenRole.setOpenStatus(JdEnum.OPENHANDLE.code);
                tCarrierCompanyOpenRole.setOpenResponseDesc("处理中");
            }
            tCarrierCompanyOpenRole.setUpdateTime(new Date());
            tCarrierCompanyOpenRoleMapper.updateByPrimaryKeySelective(tCarrierCompanyOpenRole);
            return ResultUtil.ok("更新开户申请状态成功");
        }else{
            return  ResultUtil.error("失败："+response.getResponseDesc());
        }
    }
}
