package com.lz.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.lz.api.MqAPI;
import com.lz.common.config.RedisUtil;
import com.lz.common.constants.MqMessageTag;
import com.lz.common.constants.MqMessageTopic;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.CodeEnum;
import com.lz.common.model.MQMessage;
import com.lz.common.model.chinadatabao.CommunicationCallbacklReq;
import com.lz.common.model.chinadatabao.CommunicationPersonalReq;
import com.lz.common.model.chinadatabao.CommunicationPersonalResp;
import com.lz.common.util.*;
import com.lz.dao.TEndUserInfoMapper;
import com.lz.dto.TEndUserInfoIdDTO;
import com.lz.example.TEndUserInfoExample;
import com.lz.model.TEndUserInfo;
import com.lz.service.WxLoginService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.vo.TUserVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class WxLoginServiceImpl implements WxLoginService {

    @Resource
    private TEndUserInfoMapper tEndUserInfoMapper;

    @Autowired
    private MqAPI mqAPI;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private SysParamAPI sysParamAPI;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil perfectData(TUserVo resources) {
        Integer endUserId = CurrentUser.getEndUserId();
        TEndUserInfoIdDTO endUser = tEndUserInfoMapper.selectById(endUserId);
        if(DictEnum.PASSNODE.code.equals(endUser.getAuditStatus())){
            return ResultUtil.error("认证状态已通过，不允许修改个人资料");
        }
        //更改userinfo
        TEndUserInfo tEndUserInfo = tEndUserInfoMapper.selectByPrimaryKey(endUserId);
        String  userAuditStatus = "";
        if(null != endUser.getAuditStatus()){
            userAuditStatus = endUser.getAuditStatus();
        }
        tEndUserInfo.setIdcard(resources.getIdcard());
        tEndUserInfo.setRealName(resources.getUsername());
        tEndUserInfo.setIdcardPhoto1(resources.getIdcardPhoto1());
        tEndUserInfo.setIdcardPhoto2(resources.getIdcardPhoto2());
        tEndUserInfo.setUpdateTime(new Date());
        tEndUserInfo.setAuditStatus(DictEnum.MIDNODE.code);
        tEndUserInfo.setUpdateUser(CurrentUser.getCurrentUsername());
        tEndUserInfo.setPhone(resources.getAccountNo());
        tEndUserInfo.setIdcardValidBeginning(resources.getIdcardValidUntil());//身份证有效期始
        tEndUserInfo.setIdcardValidUntil(resources.getIdcardValidBeginning());//身份证有效期至
        tEndUserInfo.setIdcardIssueQrganization(resources.getIdcardIssueQrganization());
        tEndUserInfo.setIdcardSex(resources.getIdcardSex());
        tEndUserInfo.setIdcardEthnicity(resources.getIdcardEthnicity());
        tEndUserInfo.setIdcardBirth(resources.getIdcardBirth());
        //tEndUserInfo.setUserLogisticsRole(resources.getItemCode());
        tEndUserInfo.setEnable(false);
        String UserAuditOpinion = "";
        if(null == tEndUserInfo.getIdcardPhoto1() || null == tEndUserInfo.getIdcardPhoto2() ||
                "".equals(tEndUserInfo.getIdcardPhoto1()) || "".equals(tEndUserInfo.getIdcardPhoto2())){
            tEndUserInfo.setAuditStatus(DictEnum.PAPERNEEDUPDATE.code);
            UserAuditOpinion = UserAuditOpinion + "身份证、";
        }
        if (resources.getItemCode().contains(DictEnum.CTYPEDRVIVER.code)) {
            tEndUserInfo.setDrivingLicencesPhoto1(resources.getDrivingLicencesPhoto1());
            tEndUserInfo.setDrivingLicencesPhoto2(resources.getDrivingLicencesPhoto2());
            tEndUserInfo.setCertificatePhoto1(resources.getCertificatePhoto1());
            tEndUserInfo.setCertificatePhoto2(resources.getCertificatePhoto2());
            tEndUserInfo.setApproveDrivingType(resources.getApproveDrivingType());//驾驶证准驾车型
            tEndUserInfo.setDrivingLicencesIssueUnit(resources.getDrivingLicencesIssueUnit());//驾驶证发证机关
            tEndUserInfo.setDrivingLicencesValidBeginning(resources.getDrivingLicencesValidBeginning());//驾驶证有效期始
            tEndUserInfo.setDrivingLicencesValidUntil(resources.getDrivingLicencesValidUntil());//驾驶证有效期至
            tEndUserInfo.setDrivingLicencesCode(null != resources.getDrivingLicencesCode() && StringUtils.isNotBlank(resources.getDrivingLicencesCode()) ? resources.getDrivingLicencesCode() : null);//驾驶证档案编号
            tEndUserInfo.setDrivingLicencesSex(resources.getDrivingLicencesSex());
            tEndUserInfo.setDrivingLicencesNationality(resources.getDrivingLicencesNationality());
            tEndUserInfo.setDrivingLicencesBirthday(resources.getDrivingLicencesBirthday());
            tEndUserInfo.setCertificateNo(null != resources.getCertificateNo() && StringUtils.isNotBlank(resources.getCertificateNo()) ? resources.getCertificateNo() : null);//从业资格证号
            tEndUserInfo.setCertificateValidBeginning(resources.getCertificateValidBeginning());//资格证有效期始
            tEndUserInfo.setCertificateValidUntil(resources.getCertificateValidUntil());//资格证有效期至
            if(null == tEndUserInfo.getDrivingLicencesPhoto1() || null == tEndUserInfo.getDrivingLicencesPhoto2() ||
                    "".equals(tEndUserInfo.getDrivingLicencesPhoto1()) || "".equals(tEndUserInfo.getDrivingLicencesPhoto2())){
                tEndUserInfo.setAuditStatus(DictEnum.PAPERNEEDUPDATE.code);
                UserAuditOpinion = UserAuditOpinion + "驾驶证、";
            }
            if(null == tEndUserInfo.getCertificatePhoto1() || null == tEndUserInfo.getCertificatePhoto2() ||
                    "".equals(tEndUserInfo.getCertificatePhoto1()) || "".equals(tEndUserInfo.getCertificatePhoto2())){
                tEndUserInfo.setAuditStatus(DictEnum.PAPERNEEDUPDATE.code);
                UserAuditOpinion = UserAuditOpinion + "从业资格证、";
            }
            if(UserAuditOpinion.length() > 1){
                tEndUserInfo.setAuditOpinion(UserAuditOpinion+"证件不齐全，请补齐后重新提交。");
            }else {
                tEndUserInfo.setAuditOpinion("");
            }
        }
        /*if (StringUtils.isBlank(UserAuditOpinion)) {
            // 创建自动化审核消息
            try {
                log.info("创建自动化审核消息, 司机信息endUser: {}", JSONUtil.toJsonStr(endUser));
                log.info("创建自动化审核消息, 司机信息tEndUserInfo: {}", JSONUtil.toJsonStr(tEndUserInfo));

                Date idcardValidBeginning = null != endUser.getIdcardValidBeginning() ? endUser.getIdcardValidBeginning() : resources.getIdcardValidBeginning();
                Date idcardValidUntil = null != endUser.getIdcardValidUntil() ? endUser.getIdcardValidUntil() : resources.getIdcardValidUntil();
                Date drivingLicencesValidBeginning = null != endUser.getDrivingLicencesValidBeginning() ? endUser.getDrivingLicencesValidBeginning() : resources.getDrivingLicencesValidBeginning();
                Date drivingLicencesValidUntil = null != endUser.getDrivingLicencesValidUntil() ? endUser.getDrivingLicencesValidUntil() : resources.getDrivingLicencesValidUntil();
                if (null == idcardValidBeginning) {
                    log.error("身份证有效期始为空");
                }
                if (null == idcardValidUntil) {
                    log.error("身份证有效期至为空");
                }
                if (DictEnum.CTYPEDRVIVER.code.equals(resources.getItemCode())) {
                    if (null == drivingLicencesValidBeginning) {
                        log.error("驾驶证有效期始为空");
                    }
                    if (null == drivingLicencesValidUntil) {
                        log.error("驾驶证有效期至为空");
                    }
                }

                if (null != tEndUserInfo.getIdcard()
                        && null != tEndUserInfo.getIdcardPhoto1() && null != tEndUserInfo.getIdcardPhoto2()
                        && StringUtils.isNotBlank(tEndUserInfo.getIdcardPhoto1()) && StringUtils.isNotBlank(tEndUserInfo.getIdcardPhoto2())
                        && null != idcardValidBeginning && null != idcardValidUntil) {
                    boolean flag = true;
                    if (DictEnum.CTYPEDRVIVER.code.equals(resources.getItemCode())) {
                        if (null != tEndUserInfo.getCertificatePhoto1() && StringUtils.isNotBlank(tEndUserInfo.getCertificatePhoto1())
                                && null != tEndUserInfo.getCertificatePhoto2() && StringUtils.isNotBlank(tEndUserInfo.getCertificatePhoto2())
                                && null != tEndUserInfo.getDrivingLicencesPhoto1() && null != tEndUserInfo.getDrivingLicencesPhoto2()
                                && StringUtils.isNotBlank(tEndUserInfo.getDrivingLicencesPhoto1()) && StringUtils.isNotBlank(tEndUserInfo.getDrivingLicencesPhoto2())
                                && null != drivingLicencesValidBeginning && null != drivingLicencesValidUntil) {
                        } else {
                            flag = false;
                        }
                    }
                    if (flag) {
                        MQMessage mqMessage = new MQMessage();
                        mqMessage.setTopic(MqMessageTopic.DRIVERAUDIT);
                        mqMessage.setTag(MqMessageTag.DRIVER_AUDIT);
                        mqMessage.setKey(String.valueOf(tEndUserInfo.getId()));
                        TEndUserInfo tEndUserInfo1 = new TEndUserInfo();
                        tEndUserInfo1.setId(tEndUserInfo.getId());
                        tEndUserInfo1.setPhone(tEndUserInfo.getPhone());
                        tEndUserInfo1.setParam2(IdWorkerUtil.getInstance().nextId());
                        mqMessage.setBody(tEndUserInfo1);
                        // redis 添加审核记录
                        redisUtil.set("REDISENDUSERAUDIT" + tEndUserInfo1.getParam2(), "0", 60 * 60 * 24);
                        ResultUtil resultUtil = mqAPI.sendMessage(mqMessage);
                        if (CodeEnum.SUCCESS.getCode().equals(resultUtil.getCode())) {
                            log.info("自动化审核消息发送成功");
                        } else {
                            log.error("自动化审核消息发送失败");
                        }
                    }
                }
            } catch (Exception e) {
                log.error("自动化审核消息发送失败, {}", ThrowableUtil.getStackTrace(e));
            }
        }*/

        tEndUserInfoMapper.updateByPrimaryKeySelective(tEndUserInfo);
        return ResultUtil.ok();
    }

    /**
     *  @author: dingweibo
     *  @Date: 2023/12/14 11:38
     *  @Description: 人脸对比
     */
    @Override
    public void communicationPersonal(CommunicationCallbacklReq callbacklReq) {
        log.info("获取数据宝活体检测回调数据：{}",JSONObject.toJSONString(callbacklReq));
        ResultUtil resultUtil = new ResultUtil();
        String phone = redisUtil.get("TokenObtain"+callbacklReq.getToken()).toString();
        if("10000".equals(callbacklReq.getCode())){
            if("1".equals(callbacklReq.getBackResult())){
                TEndUserInfoExample example = new TEndUserInfoExample();
                TEndUserInfoExample.Criteria cr = example.createCriteria();
                cr.andPhoneEqualTo(phone);
                cr.andEnableEqualTo(false);
                TEndUserInfo endUserInfo = tEndUserInfoMapper.selectByExample(example).get(0);
                ChinaDataBao chinaDataBao = new ChinaDataBao();
                CommunicationPersonalReq req = new CommunicationPersonalReq();
                req.setName(endUserInfo.getRealName());
                req.setIdcard(endUserInfo.getIdcard());
                req.setImageld(callbacklReq.getBackImg());
                CommunicationPersonalResp resp = chinaDataBao.communicationPersonal(req);
                if("10000".equals(resp.getCode())){
                    Double score = Double.parseDouble(resp.getScore());

                    SysParam param = sysParamAPI.getParamByKey("TXSZ");
                    String[] paramResult = param.getParamValue().split(",");
                    Integer paramResultFlage = Integer.parseInt(paramResult[0]);
                    Double paramResultScore = Double.parseDouble(paramResult[1]);
                    Integer paramResultId = Integer.parseInt(paramResult[2]);

                    if(endUserInfo.getId().equals(paramResultId)){
                        if(paramResultFlage.equals(1)){
                            if(score>=paramResultScore){
                                resultUtil.setCode(resp.getCode());
                                resultUtil.setMsg("验证成功");
                            }else{
                                resultUtil.setCode("11111");
                                resultUtil.setMsg("验证失败，请重新提现并确保是本人操作");
                            }
                            resultUtil.setData("");
                            redisUtil.set("rxdb"+phone,resultUtil);
                        }else{
                            if(score>=0.7){
                                resultUtil.setCode(resp.getCode());
                                resultUtil.setMsg("验证成功");
                            }else{
                                resultUtil.setCode("11111");
                                resultUtil.setMsg("验证失败，请重新提现并确保是本人操作");
                            }
                            resultUtil.setData(resp.getScore());
                            redisUtil.set("rxdb"+phone,resultUtil);
                        }
                    }else{
                        if(score>=0.7){
                            resultUtil.setCode(resp.getCode());
                            resultUtil.setMsg("验证成功");
                        }else{
                            resultUtil.setCode("11111");
                            resultUtil.setMsg("验证失败，请重新提现并确保是本人操作");
                        }
                        resultUtil.setData(resp.getScore());
                        redisUtil.set("rxdb"+phone,resultUtil);
                    }
                }else{
                    resultUtil.setMsg(resp.getMessage());
                    resultUtil.setCode(resp.getCode());
                    redisUtil.set("rxdb"+phone,resultUtil);
                }
            }else{
                resultUtil.setMsg(callbacklReq.getMessage());
                resultUtil.setCode("11111");
                redisUtil.set("rxdb"+phone,resultUtil);
            }
        }else{
            resultUtil.setMsg(callbacklReq.getMessage());
            resultUtil.setCode(callbacklReq.getCode());
            redisUtil.set("rxdb"+phone,resultUtil);
        }
    }

}
