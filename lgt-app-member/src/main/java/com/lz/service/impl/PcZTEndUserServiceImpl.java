package com.lz.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lz.common.util.ResultUtil;
import com.lz.dao.TZtAccountOpenInfoMapper;
import com.lz.dto.PcZTEndUserOpenRoleListDTO;
import com.lz.service.PcZTEndUserService;
import com.lz.vo.PcZTEndUserOpenRoleListVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PcZTEndUserServiceImpl implements PcZTEndUserService {

    @Autowired
    private TZtAccountOpenInfoMapper tZtAccountOpenInfoMapper;

    @Override
    public ResultUtil pcEndUserOpenRoleList(PcZTEndUserOpenRoleListVO record) {
        Page<Object> page = PageHelper.startPage(record.getPage(),record.getSize());
        List<PcZTEndUserOpenRoleListDTO> pcZTEndUserOpenRoleListDTOList = tZtAccountOpenInfoMapper.pcEndUserOpenRoleList(record);
        ResultUtil resultUtil = new ResultUtil();
        resultUtil.setData(pcZTEndUserOpenRoleListDTOList);
        resultUtil.setCount(page.getTotal());
        return resultUtil;
    }
}
