package com.lz.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.lz.api.WxOrderControllerAPI;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.dao.TFacialRecognitionRecordsMapper;
import com.lz.model.TFacialRecognitionRecords;
import com.lz.service.TFacialRecognitionRecordsService;
import com.lz.vo.HxOrderTxVO;
import com.lz.vo.TFacialRecognitionRecordsVO;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class TFacialRecognitionRecordsServiceImpl implements TFacialRecognitionRecordsService {

    private final static String PERSON_GET_CHECK_RESULT = "https://api1.chinadatapay.com/person/mixedCheck/getCheckResult";

    //@Value("${chinaDataPay.appKey}")
    private String appKey;

    @Resource
    private TFacialRecognitionRecordsMapper facialRecognitionRecordsMapper;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private WxOrderControllerAPI wxOrderControllerAPI;

    @Override
    public ResultUtil recordResult(TFacialRecognitionRecordsVO vo) {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                .addFormDataPart("key", appKey)
                .addFormDataPart("token", vo.getToken())
                .addFormDataPart("model", null == vo.getModel() ? "1" : vo.getModel())
                .addFormDataPart("sceneType", vo.getSceneType())
                .addFormDataPart("appPath", null == vo.getAppPath() ? "" : vo.getAppPath())
                .addFormDataPart("queryVideoFlag", null == vo.getQueryVideoFlag() ? "true" : vo.getQueryVideoFlag())
                .build();
        log.info("开始请求人脸识别: {}", JSONUtil.toJsonStr(body));
        Request request = new Request.Builder()
                .url(PERSON_GET_CHECK_RESULT)
                .method("POST", body)
                .addHeader("Content-Type", "multipart/form-data")
                .build();
        try {
            Thread.sleep(1000);
            Response response = client.newCall(request).execute();
            ResponseBody responseBody = response.body();
            if (responseBody != null) {
                String responseString = responseBody.string();
                // Use responseString here
                log.info("人脸识别请求结果：{}", responseString);
                JSONObject entries = JSONUtil.parseObj(responseString);
                log.info("人脸识别请求结果：{}", JSONUtil.toJsonStr(entries));
                if (entries.getStr("code").equals("10000")) {
                    TFacialRecognitionRecords records = JSONUtil.toBean(entries.getStr("data"), TFacialRecognitionRecords.class);
                    records.setToken(vo.getToken());
                    records.setImageBase64(null);
                    records.setResultCode(entries.getStr("code", "10000"));
                    records.setResultMsg(entries.getStr("message", ""));
                    facialRecognitionRecordsMapper.insertSelective(records);
                    if (null == records.getPersonImageCheckScore() || StringUtils.isBlank(records.getPersonImageCheckScore())) {
                        return ResultUtil.error("人脸识别失败，请重试");
                    }
                    BigDecimal bigDecimal = new BigDecimal(records.getPersonImageCheckScore());
                    if (bigDecimal.compareTo(new BigDecimal("70")) >= 0) {
                        String requestId = null == records.getRequestId() ? IdWorkerUtil.getInstance().nextId() : records.getRequestId();
                        String requestNo = redisTemplate.opsForValue().get(vo.getToken());
                        if (null == requestNo || requestNo.equals(requestId)) {
                            redisTemplate.opsForValue().set(vo.getToken(), requestId, 1, TimeUnit.HOURS);
                        } else {
                            return ResultUtil.error("人脸识别超时，请重试");
                        }
                        HxOrderTxVO txVO = vo.getTxVO();
                        txVO.setIfRxdb(true);
                        try {
                            return wxOrderControllerAPI.hxTx(vo.getTxVO());
                        } catch (Exception e) {
                            log.error("发起提现失败!", e);
                            return ResultUtil.error("发起提现失败!");
                        }

                    } else {
                        return ResultUtil.error("人脸识别不一致");
                    }
                } else {
                    TFacialRecognitionRecords records = new TFacialRecognitionRecords();
                    records.setToken(vo.getToken());
                    records.setResultCode(entries.getStr("code", "-999"));
                    records.setResultMsg(entries.getStr("message", ""));
                    facialRecognitionRecordsMapper.insertSelective(records);
                }
            }


        } catch (Exception e) {
            log.error("查询人脸识别结果失败", e);
            return ResultUtil.error("查询人脸识别结果失败");
        }
        return ResultUtil.error("查询人脸识别结果失败");
    }

}
