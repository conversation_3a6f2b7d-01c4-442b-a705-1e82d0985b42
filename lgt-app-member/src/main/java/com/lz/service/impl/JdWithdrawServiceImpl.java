package com.lz.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Message;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jd.jr.jropen.unifySdk.respModel.*;
import com.lz.api.*;
import com.lz.common.config.RedisUtil;
import com.lz.common.constants.MqMessageTag;
import com.lz.common.constants.MqMessageTopic;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.JDTradeTypeEnum;
import com.lz.common.dbenum.JdEnum;
import com.lz.common.dbenum.JdTradeType;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.CodeEnum;
import com.lz.common.model.jdPayment.request.account.OpenQueryAccountInfoReq;
import com.lz.common.model.jdPayment.request.bank.OpenMemberBindAccountReq;
import com.lz.common.model.jdPayment.request.bank.VirtualAccountQueryBindStatusReq;
import com.lz.common.model.jdPayment.request.pay.BatchCustomerBalancePayReq;
import com.lz.common.model.jdPayment.request.pay.BatchCustomerBalancePayResp;
import com.lz.common.model.jdPayment.request.pay.CustomerBalancePayReq;
import com.lz.common.model.jdPayment.request.query.*;
import com.lz.common.model.jdPayment.request.trade.CustomerWithdrawReq;
import com.lz.common.model.jdPayment.util.CloudPayFormatUtil;
import com.lz.common.rlsdk.sdk.utils.DateUtil;
import com.lz.common.util.*;
import com.lz.dao.*;
import com.lz.dto.TAccountDTO;
import com.lz.jdpay.CloudPayAPI;
import com.lz.model.*;
import com.lz.service.JdWithdrawService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("jdWithdrawService")
@Slf4j
public class JdWithdrawServiceImpl implements JdWithdrawService {
    @Value("${partnerId}")
    private String partnerId;
    @Value("${merchantCode}")
    private String merchantCode;
    @Autowired
    private CloudPayAPI cloudPayAPI;
    @Resource
    private TCarrierCompanyOpenRoleMapper tCarrierCompanyOpenRoleMapper;
    @Resource
    private TEndUserOpenRoleMapper tEndUserOpenRoleMapper;
    @Autowired
    private TOrderPayInfoAPI tOrderPayInfoAPI;
    @Autowired
    private TOrderPayDetailAPI tOrderPayDetailAPI;
    @Resource
    private TJdWalletMapper tJdWalletMapper;
    @Autowired
    private SysParamAPI sysParamAPI;
    @Resource
    private TWithdrawalApplicationMapper tWithdrawalApplicationMapper;
    @Resource
    private TCarrierInfoMapper tCarrierInfoMapper;
    @Resource
    private TCompanyInfoMapper tCompanyInfoMapper;
    @Resource
    private TEndUserInfoMapper tEndUserInfoMapper;
    @Autowired
    private RedisUtil redisUtil;
    @Resource
    private TServicefeeInfoMapper tServicefeeInfoMapper;
    @Resource
    private TServicefeeDetailsMapper tServicefeeDetailsMapper;
    @Resource
    private TJdBankCardMapper tJdBankCardMapper;
    @Autowired
    private OssAPI ossAPI;
    @Resource
    private TBankCardMapper tBankCardMapper;
    @Resource
    private TEnduserAccountMapper enduserAccountMapper;

    @Resource
    private TMemberOrderPayInfoMapper orderPayInfoMapper;

    @Resource
    private TMemberOrderPayDetailMapper orderPayDetailMapper;

    @Resource
    private TMemberWithdrawBehalfApplicationMapper withdrawBehalfApplicationMapper;

    @Resource
    private TJdWalletChangeLogMapper jdWalletChangeLogMapper;

    @Resource
    private TAccountMapper accountMapper;

    @Autowired
    private MqAPI mqAPI;
    @Resource
    private TCarrieAccountMapper tCarrieAccountMapper;

    @Resource
    private TCompanyAccountMapper tCompanyAccountMapper;

    @Resource
    private TEnduserAccountMapper tEnduserAccountMapper;
    /**
     *  @author: dingweibo
     *  @Date: 2021/9/1 9:18
     *  @Description: 提现申请列表
     */
    @Override
    public ResultUtil selectByPage(TCarrierCompanyOpenRoleSearchVo record) {
        Page<Object> objectPage = PageHelper.startPage(record.getPage(), record.getSize());
        List<TWithdrawalApplicationVo>  list = tWithdrawalApplicationMapper.selectByPage(record);
        for(TWithdrawalApplicationVo re:list){
            re.setTransactionInitiationTimeStr(DateUtils.formatDateTime(re.getTransactionInitiationTime()));
            if(DictEnum.CA.code.equals(re.getUserOpenRole())){
                if(null!=re.getParam1() && "HX".equals(re.getParam1())){
                    TCarrieAccount tCarrieAccount = tCarrieAccountMapper.getByAccountId(re.getCarrierCompanyId());
                    re.setCarrierCompanyId(tCarrieAccount.getCarrierId());
                }
                TCarrierInfo tCarrierInfo = tCarrierInfoMapper.selectByPrimaryKey(re.getCarrierCompanyId());
                re.setUserOpenRoleStr(tCarrierInfo.getCarrierName());
            }else if(DictEnum.PF.code.equals(re.getUserOpenRole())){
                if(null!=re.getParam1() && "HX".equals(re.getParam1())){
                    TCarrieAccount tCarrieAccount = tCarrieAccountMapper.getByAccountId(re.getCarrierCompanyId());
                    re.setCarrierCompanyId(tCarrieAccount.getCarrierId());
                }
                TCarrierInfo tCarrierInfo = tCarrierInfoMapper.selectByPrimaryKey(re.getCarrierCompanyId());
                re.setUserOpenRoleStr(tCarrierInfo.getCarrierName());
            }else if(DictEnum.BD.code.equals(re.getUserOpenRole())){
                if(null!=re.getParam1() && "HX".equals(re.getParam1())){
                    TCompanyAccount tCompanyAccount = tCompanyAccountMapper.selectByAccount(re.getCarrierCompanyId());
                    re.setCarrierCompanyId(tCompanyAccount.getCompanyId());
                }
                TCompanyInfo tCompanyInfo = tCompanyInfoMapper.selectByPrimaryKey(re.getCarrierCompanyId());
                re.setUserOpenRoleStr(tCompanyInfo.getCompanyName());
            }else if(DictEnum.CD.code.equals(re.getUserOpenRole())){
                if(null!=re.getParam1() && "HX".equals(re.getParam1())){
                    TEnduserAccount tEnduserAccount = tEnduserAccountMapper.selectEnduserAccount(re.getCarrierCompanyId());
                    re.setCarrierCompanyId(tEnduserAccount.getEnduserId());
                }
                TEndUserInfo tEndUserInfo = tEndUserInfoMapper.selectByPrimaryKey(re.getCarrierCompanyId());
                re.setUserOpenRoleStr(tEndUserInfo.getRealName());
            }
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),objectPage,objectPage.getTotal());
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/9/1 9:19
     *  @Description: 提现审批回显
     */
    @Override
    public ResultUtil selectByApprove(TWithdrawalApplication record) {
        TWithdrawalApplication tWithdrawalApplication = tWithdrawalApplicationMapper.selectByPrimaryKey(record.getId());
        if(DictEnum.CA.code.equals(tWithdrawalApplication.getUserOpenRole())){
            if(null!=tWithdrawalApplication.getParam1() && "HX".equals(tWithdrawalApplication.getParam1())){
                TCarrieAccount tCarrieAccount = tCarrieAccountMapper.getByAccountId(tWithdrawalApplication.getCarrierCompanyId());
                tWithdrawalApplication.setCarrierCompanyId(tCarrieAccount.getCarrierId());
            }
            TCarrierInfo tCarrierInfo = tCarrierInfoMapper.selectByPrimaryKey(tWithdrawalApplication.getCarrierCompanyId());
            tWithdrawalApplication.setUserOpenRole(tCarrierInfo.getCarrierName());
        }else if(DictEnum.BD.code.equals(tWithdrawalApplication.getUserOpenRole())){
            if(null!=tWithdrawalApplication.getParam1() && "HX".equals(tWithdrawalApplication.getParam1())){
                TCompanyAccount tCompanyAccount = tCompanyAccountMapper.selectByAccount(tWithdrawalApplication.getCarrierCompanyId());
                tWithdrawalApplication.setCarrierCompanyId(tCompanyAccount.getCompanyId());
            }
            TCompanyInfo tCompanyInfo = tCompanyInfoMapper.selectByPrimaryKey(tWithdrawalApplication.getCarrierCompanyId());
            tWithdrawalApplication.setUserOpenRole(tCompanyInfo.getCompanyName());
        }else if(DictEnum.PF.code.equals(tWithdrawalApplication.getUserOpenRole())){
            if(null!=tWithdrawalApplication.getParam1() && "HX".equals(tWithdrawalApplication.getParam1())){
                TCarrieAccount tCarrieAccount = tCarrieAccountMapper.getByAccountId(tWithdrawalApplication.getCarrierCompanyId());
                tWithdrawalApplication.setCarrierCompanyId(tCarrieAccount.getCarrierId());
            }
            TCarrierInfo tCarrierInfo = tCarrierInfoMapper.selectByPrimaryKey(tWithdrawalApplication.getCarrierCompanyId());
            tWithdrawalApplication.setUserOpenRole(tCarrierInfo.getCarrierName());
        }
        return ResultUtil.ok(tWithdrawalApplication);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/9/1 9:38
     *  @Description: 提现审批
     */
    @Override
    public ResultUtil updateApprove(TWithdrawalApplicationVo record) {
        TWithdrawalApplication tWithdrawalApplication = tWithdrawalApplicationMapper.selectByPrimaryKey(record.getId());
        String walletKey = "";
        //承运方提现
        if (DictEnum.CA.code.equals(tWithdrawalApplication.getUserOpenRole())) {
            walletKey = "JDCARRIERTXSP" + record.getWalletId();
        } else if (DictEnum.BD.code.equals(tWithdrawalApplication.getUserOpenRole())) {
            //企业提现
            walletKey = "JDCOMPANYTXSP" + record.getWalletId();
        }else if (DictEnum.PF.code.equals(tWithdrawalApplication.getUserOpenRole())) {
            //平台提现
            walletKey = "JDPFTXSP" + record.getWalletId();
        } else if (DictEnum.CD.code.equals(tWithdrawalApplication.getUserOpenRole())){
            walletKey="JDMANAGERTXSP" + record.getWalletId();
        }
        boolean walletLock = false;
        if (!redisUtil.hasKey(walletKey)) {
            walletLock = redisUtil.set(walletKey, "lock");
        }
        try {
            if (walletLock) {
                if(null!=tWithdrawalApplication.getApprovalStatus()){
                    return ResultUtil.error("当前提现交易已审批不允许再次审批！");
                }
                tWithdrawalApplication.setAmount(record.getAmount());
                tWithdrawalApplication.setApprovalComments(record.getApprovalComments());
                tWithdrawalApplication.setApprovalScreenshot(record.getApprovalScreenshot());
                tWithdrawalApplication.setApprovalStatus(record.getApprovalStatus());
                if(tWithdrawalApplication.getApprovalStatus().equals(DictEnum.PASSNODE.code)){
                    tWithdrawalApplication.setWithdrawalStatus(DictEnum.PACKEXTRACTPROCESSED.code);
                }
                tWithdrawalApplicationMapper.updateByPrimaryKeySelective(tWithdrawalApplication);
                if(tWithdrawalApplication.getApprovalStatus().equals(DictEnum.PASSNODE.code)){
                    JdWithdrawVo jdWithdrawVo = new JdWithdrawVo();
                    jdWithdrawVo.setAmount(tWithdrawalApplication.getAmount());
                    jdWithdrawVo.setBizOrderNo(tWithdrawalApplication.getCode());
                    jdWithdrawVo.setOpenRoleId(tWithdrawalApplication.getOpenRoleId());
                    jdWithdrawVo.setUserOpenRole(tWithdrawalApplication.getUserOpenRole());
                    jdWithdrawVo.setBankId(tWithdrawalApplication.getBankId());
                    jdWithdrawVo.setAccountName(tWithdrawalApplication.getAccountName());
                    jdWithdrawVo.setBankAccountNo(tWithdrawalApplication.getBankAccountNo());
                    jdWithdrawVo.setWalletId(tWithdrawalApplication.getWalletId());
                    jdWithdrawVo.setTradeType(tWithdrawalApplication.getTransactionType());
                    jdWithdrawVo.setTwaId(tWithdrawalApplication.getId());
                    return carrierAndComapnyWithdrawStart(jdWithdrawVo);
                }
            }else{
                log.error("京东提现审批处理修改钱包获取锁失败");
                return ResultUtil.error("京东提现审批处理失败");
            }
            return ResultUtil.ok();
        }catch (Exception e){
            log.error("提现审批失败",e);
            return ResultUtil.error("提现审批失败");
        }finally {
            if (walletLock && redisUtil.hasKey(walletKey)) {
                redisUtil.del(walletKey);
            }
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/11/29 11:21
     *  @Description: 经纪人发起提现
     */
    @Override
    public ResultUtil mangerWithdraw(JdWithdrawVo record) {
        //提现金额
        TWithdrawalApplication tWithdrawalApplication = new TWithdrawalApplication();
        BigDecimal amount = record.getAmount().multiply(BigDecimal.valueOf(100));
        String partnerAccId = "";
        Integer carrierCompanyId = null;
        if(DictEnum.CD.code.equals(record.getUserOpenRole())){
            TEndUserOpenRole tEndUserOpenRole = tEndUserOpenRoleMapper.selectByPrimaryKey(record.getOpenRoleId());
            log.info("查询自然人开户表回参：{}", JSONObject.toJSONString(tEndUserOpenRole));
            record.setBankAccountNo(record.getCardNo());
            partnerAccId = tEndUserOpenRole.getPartnerAccId();
            carrierCompanyId = tEndUserOpenRole.getEndUserId();
        }
        tWithdrawalApplication.setCode(IdWorkerUtil.getInstance().nextId());
        tWithdrawalApplication.setBizOrderNo(IdWorkerUtil.getInstance().nextId());
        tWithdrawalApplication.setTransactionInitiationTime(new Date());
        tWithdrawalApplication.setCarrierCompanyId(carrierCompanyId);
        tWithdrawalApplication.setUserOpenRole(record.getUserOpenRole());
        tWithdrawalApplication.setTransactionType(record.getTradeType());
        tWithdrawalApplication.setAmount(record.getAmount());
        tWithdrawalApplication.setOutAccount(partnerAccId);
        tWithdrawalApplication.setEnterAccount(record.getBankAccountNo());
        tWithdrawalApplication.setWithdrawalStatus(DictEnum.PACKEXTRACTPROCESSED.code);
        tWithdrawalApplication.setOpenRoleId(record.getOpenRoleId());
        tWithdrawalApplication.setBankId(record.getBankId());
        tWithdrawalApplication.setBankAccountNo(record.getBankAccountNo());
        tWithdrawalApplication.setAccountName(record.getAccountName());
        tWithdrawalApplication.setWalletId(record.getWalletId());
        tWithdrawalApplication.setParam1("JD");
        tWithdrawalApplicationMapper.insertSelective(tWithdrawalApplication);
        String walletKey = "";
        if (DictEnum.CD.code.equals(tWithdrawalApplication.getUserOpenRole())){
            walletKey="JDMANAGERTXSP" + record.getWalletId();
        }
        boolean walletLock = false;
        if (!redisUtil.hasKey(walletKey)) {
            walletLock = redisUtil.set(walletKey, "lock");
        }
        try {
            if (walletLock) {
                JdWithdrawVo jdWithdrawVo = new JdWithdrawVo();
                jdWithdrawVo.setAmount(record.getAmount());
                jdWithdrawVo.setBizOrderNo(tWithdrawalApplication.getCode());
                jdWithdrawVo.setOpenRoleId(tWithdrawalApplication.getOpenRoleId());
                jdWithdrawVo.setUserOpenRole(tWithdrawalApplication.getUserOpenRole());
                jdWithdrawVo.setBankId(tWithdrawalApplication.getBankId());
                jdWithdrawVo.setAccountName(tWithdrawalApplication.getAccountName());
                jdWithdrawVo.setBankAccountNo(tWithdrawalApplication.getBankAccountNo());
                jdWithdrawVo.setWalletId(tWithdrawalApplication.getWalletId());
                jdWithdrawVo.setTradeType(tWithdrawalApplication.getTransactionType());
                jdWithdrawVo.setTwaId(tWithdrawalApplication.getId());
                jdWithdrawVo.setParam("1");
                return carrierAndComapnyWithdrawStart(jdWithdrawVo);
            }else{
                log.error("京东提现处理修改钱包获取锁失败");
                return ResultUtil.error("京东提现处理失败");
            }
        }catch (Exception e){
            log.error("提现失败",e);
            return ResultUtil.error("提现失败");
        }finally {
            if (walletLock && redisUtil.hasKey(walletKey)) {
                redisUtil.del(walletKey);
            }
        }
    }

    //提现页面回显
    @Override
    public ResultUtil selectByWithdraw(JdWithdrawVo record) {
        JdWithdrawVo withdrawVo = null;
        if(DictEnum.CA.code.equals(record.getUserOpenRole())){
            withdrawVo = tCarrierCompanyOpenRoleMapper.selectByWithdrawCairrer(record);
            List<TBankCard> tBankCardList = tCarrierCompanyOpenRoleMapper.selectByWithdrawBankList(record);
            withdrawVo.setBankList(tBankCardList);
        }else if(DictEnum.PF.code.equals(record.getUserOpenRole())){
            withdrawVo =tCarrierCompanyOpenRoleMapper.selectByWithdrawPf(record);
            List<TBankCard> tBankCardList = tCarrierCompanyOpenRoleMapper.selectByWithdrawBankList(record);
            withdrawVo.setBankList(tBankCardList);
        }else if(DictEnum.BD.code.equals(record.getUserOpenRole())){
            withdrawVo =tCarrierCompanyOpenRoleMapper.selectByWithdrawCompany(record);
            List<TBankCard> tBankCardList = tCarrierCompanyOpenRoleMapper.selectByWithdrawBankList(record);
            withdrawVo.setBankList(tBankCardList);
        }else if(DictEnum.CD.code.equals(record.getUserOpenRole())){
            withdrawVo =tCarrierCompanyOpenRoleMapper.selectByWithdrawManager(record);
            if(null == withdrawVo || "".equals(withdrawVo)){
                return ResultUtil.ok("当前账户未开户，不允许提现");
            }
            List<TBankCard> tBankCardList = tCarrierCompanyOpenRoleMapper.selectByManagerBankList(record);
            for(TBankCard tBankCard:tBankCardList){
                //持卡人
                String cardOwner = tBankCard.getCardOwner();
                String cardNo = "***************"+tBankCard.getCardNo().substring(tBankCard.getCardNo().length() - 4);
                tBankCard.setRemark(cardOwner+" "+cardNo);
            }
            withdrawVo.setBankList(tBankCardList);
            // 查询是否存在提现中的
            List<TWithdrawBehalfApplication> list = withdrawBehalfApplicationMapper.selectByEndUserId(CurrentUser.getEndUserId());
            if (null != list && !list.isEmpty()) {
                return ResultUtil.error("您的提现申请正在处理中，请耐心等待，勿重复操作。");
            }
        }
        if(null==withdrawVo){
            return ResultUtil.error("请完善银行卡信息！");
        }
        return ResultUtil.ok(withdrawVo);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/9/1 14:28
     *  @Description: 发起提现插入提现申请表中
     */
    @Override
    public ResultUtil carrierAndComapnyWithdraw(JdWithdrawVo record) {
        try {
            log.info("发起提现插入提现申请表中提现开始处理入参1：{}", JSONObject.toJSONString(record));
            //提现金额
            BigDecimal amount = record.getAmount().multiply(BigDecimal.valueOf(100));
            if (null != record.getOpenRoleId() && null != record.getWalletId() && null != record.getUserOpenRole()) {
                TWithdrawalApplication twa = new TWithdrawalApplication();
                twa.setUserOpenRole(record.getUserOpenRole());
                twa.setOpenRoleId(record.getOpenRoleId());
                List<TWithdrawalApplication> tWithdrawalApplicationList = new ArrayList<>();
                if(!DictEnum.CD.code.equals(record.getUserOpenRole())){
                   tWithdrawalApplicationList = tWithdrawalApplicationMapper.selectByApprovalStatusNo(twa);
                }
                if(tWithdrawalApplicationList.size()<1){
                    String partnerAccId = "";
                    Integer carrierCompanyId = null;
                    if(DictEnum.CD.code.equals(record.getUserOpenRole())){
                        TEndUserOpenRole tEndUserOpenRole = tEndUserOpenRoleMapper.selectByPrimaryKey(record.getOpenRoleId());
                        log.info("查询自然人开户表回参：{}", JSONObject.toJSONString(tEndUserOpenRole));
                        record.setBankAccountNo(record.getCardNo());
                        partnerAccId = tEndUserOpenRole.getPartnerAccId();
                        carrierCompanyId = tEndUserOpenRole.getEndUserId();
                    }else{
                        TCarrierCompanyOpenRoleVo tcorVoreq = new  TCarrierCompanyOpenRoleVo();
                        tcorVoreq.setId(record.getOpenRoleId());
                        tcorVoreq.setUserOpenRole(record.getUserOpenRole());
                        tcorVoreq.setBankCardId(record.getBankId());
                        TCarrierCompanyOpenRoleVo tCarrierCompanyOpenRoleVo =  tCarrierCompanyOpenRoleMapper.selectByStatus(tcorVoreq);
                        log.info("查询企业开户表回参：{}", JSONObject.toJSONString(tCarrierCompanyOpenRoleVo));
                        partnerAccId = tCarrierCompanyOpenRoleVo.getPartnerAccId();
                        carrierCompanyId = tCarrierCompanyOpenRoleVo.getCarrierCompanyId();
                    }
                    if (StringUtils.isNotEmpty(partnerAccId)){
                        //判断京东余额是否充足
                        CustomerQueryBalanceReq req = new CustomerQueryBalanceReq();
                        req.setPartnerId(partnerId);
                        req.setRequestId(IdWorkerUtil.getInstance().nextId());
                        req.setRequestTime(DateUtils.getRequestTime());
                        req.setMerchantCode(merchantCode);
                        req.setPartnerAccId(partnerAccId);
                        log.info("查询京东余额入参：{}", JSONObject.toJSONString(req));
                        ResultUtil resultUtil1 = cloudPayAPI.cloudPay(req);
                        CustomerQueryBalanceResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, CustomerQueryBalanceResponse.class);
                        log.info("查询京东余额回参：{}", JSONObject.toJSONString(response));
                        if(JdEnum.JDSUCCESSCODE.code.equals(response.getResponseCode())){
                            if(amount.doubleValue()>response.getAvailableBalance().doubleValue()){
                                return ResultUtil.error("京东实际余额不足,提现失败");
                            }
                        }else{
                            return ResultUtil.error("查询京东余额失败");
                        }
                        TJdWallet tJdWallet = tJdWalletMapper.selectByPrimaryKey(record.getWalletId());
                        if(record.getAmount().doubleValue()>tJdWallet.getAccountBalance().doubleValue()){
                            return ResultUtil.error("钱包余额不足,提现失败");
                        }
                    } else {
                        return ResultUtil.error("提现失败,京东开户表信息不全");
                    }
                    TWithdrawalApplication tWithdrawalApplication = new TWithdrawalApplication();
                    tWithdrawalApplication.setCode(IdWorkerUtil.getInstance().nextId());
                    tWithdrawalApplication.setBizOrderNo(IdWorkerUtil.getInstance().nextId());
                    tWithdrawalApplication.setTransactionInitiationTime(new Date());
                    tWithdrawalApplication.setCarrierCompanyId(carrierCompanyId);
                    tWithdrawalApplication.setUserOpenRole(record.getUserOpenRole());
                    tWithdrawalApplication.setTransactionType(record.getTradeType());
                    tWithdrawalApplication.setAmount(record.getAmount());
                    tWithdrawalApplication.setOutAccount(partnerAccId);
                    tWithdrawalApplication.setEnterAccount(record.getBankAccountNo());
                    tWithdrawalApplication.setWithdrawalStatus(DictEnum.PACKEXTRACTPROCESSED.code);
                    tWithdrawalApplication.setOpenRoleId(record.getOpenRoleId());
                    tWithdrawalApplication.setBankId(record.getBankId());
                    tWithdrawalApplication.setBankAccountNo(record.getBankAccountNo());
                    tWithdrawalApplication.setAccountName(record.getAccountName());
                    tWithdrawalApplication.setWalletId(record.getWalletId());
                    tWithdrawalApplication.setParam1("JD");
                    tWithdrawalApplicationMapper.insertSelective(tWithdrawalApplication);
                    return ResultUtil.ok("提现申请成功");
                }else{
                    return ResultUtil.ok("提现失败，当前账户存在未审批的提现");
                }
            }
        } catch (Exception e) {
            log.error("提现失败", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("提现失败");
            }
        }
        return ResultUtil.ok();
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/31 18:56
     *  @Description: 提现审批通过后发起提现
     */

    @Override
    public ResultUtil carrierAndComapnyWithdrawStart(JdWithdrawVo record) {
        ResultUtil resultUtil = new ResultUtil();
        String walletKey = "";
        if (DictEnum.PFTIXIAN.code.equals(record.getTradeType())) {
            //平台提现
            walletKey = "JDPFWallet" + record.getWalletId();
        }else if (DictEnum.PTIXIAN.code.equals(record.getTradeType())) {
            //承运方提现
            walletKey = "JDCARRIERWallet" + record.getWalletId();
        } else if (DictEnum.BTIXIAN.code.equals(record.getTradeType())) {
            //企业提现
            walletKey = "JDCOMPANYWallet" + record.getWalletId();
        } else if (DictEnum.MANAGER.code.equals(record.getTradeType())){
            walletKey="JDManagerWallet" + record.getWalletId();
        } else {
            log.info("提现类型未知， tradeType");
            TWithdrawalApplication twa = new TWithdrawalApplication();
            twa.setId(record.getTwaId());
            twa.setWithdrawalStatus(DictEnum.PACKEWITHDRAWERROR.code);
            twa.setWithdrawalInfo("提现类型未知失败");
            updateTwa(twa);
        }
        boolean walletLock = false;
        if (!redisUtil.hasKey(walletKey)) {
            walletLock = redisUtil.set(walletKey, "lock");
        }
        try {
            if (walletLock) {
                log.info("提现开始处理入参：{}", JSONObject.toJSONString(record));
                //提现金额
                BigDecimal amount = record.getAmount().multiply(BigDecimal.valueOf(100));
                if (null != record.getOpenRoleId() && null != record.getWalletId() && null != record.getUserOpenRole()) {
                    String partnerAccId = "";
                    String cardId = "";
                    if(DictEnum.CD.code.equals(record.getUserOpenRole())){
                        TEndUserOpenRole tEndUserOpenRole = tEndUserOpenRoleMapper.selectByPrimaryKey(record.getOpenRoleId());
                        log.info("查询自然人开户表回参：{}", JSONObject.toJSONString(tEndUserOpenRole));
                        partnerAccId = tEndUserOpenRole.getPartnerAccId();
                        TJdBankCard jdb = new TJdBankCard();
                        jdb.setUserOpenRole(record.getUserOpenRole());
                        jdb.setBankCardId(record.getBankId());
                        TJdBankCard tJdBankCard = tJdBankCardMapper.selectByBank(jdb);
                        cardId = tJdBankCard.getCardId();
                    }else{
                        TCarrierCompanyOpenRoleVo tcorVoreq = new  TCarrierCompanyOpenRoleVo();
                        tcorVoreq.setId(record.getOpenRoleId());
                        tcorVoreq.setUserOpenRole(record.getUserOpenRole());
                        tcorVoreq.setBankCardId(record.getBankId());
                        TCarrierCompanyOpenRoleVo tCarrierCompanyOpenRoleVo =  tCarrierCompanyOpenRoleMapper.selectByStatus(tcorVoreq);
                        log.info("查询企业开户表回参：{}", JSONObject.toJSONString(tCarrierCompanyOpenRoleVo));
                        partnerAccId = tCarrierCompanyOpenRoleVo.getPartnerAccId();
                        TJdBankCard jdb = new TJdBankCard();
                        jdb.setUserOpenRole(record.getUserOpenRole());
                        jdb.setBankCardId(record.getBankId());
                        TJdBankCard tJdBankCard = tJdBankCardMapper.selectByBank(jdb);
                        cardId = tJdBankCard.getCardId();
                    }
                    if (StringUtils.isNotEmpty(partnerAccId)) {
                        //判断京东余额是否充足
                        CustomerQueryBalanceReq req = new CustomerQueryBalanceReq();
                        req.setPartnerId(partnerId);
                        req.setRequestId(IdWorkerUtil.getInstance().nextId());
                        req.setRequestTime(DateUtils.getRequestTime());
                        req.setMerchantCode(merchantCode);
                        req.setPartnerAccId(partnerAccId);
                        log.info("查询京东余额入参：{}", JSONObject.toJSONString(req));
                        ResultUtil resultUtil1 = cloudPayAPI.cloudPay(req);
                        CustomerQueryBalanceResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, CustomerQueryBalanceResponse.class);
                        log.info("查询京东余额回参：{}", JSONObject.toJSONString(response));
                        if(JdEnum.JDSUCCESSCODE.code.equals(response.getResponseCode())){
                            if(amount.doubleValue()>response.getAvailableBalance().doubleValue()){
                                TWithdrawalApplication twa = new TWithdrawalApplication();
                                twa.setId(record.getTwaId());
                                twa.setWithdrawalStatus(DictEnum.PACKEWITHDRAWERROR.code);
                                twa.setWithdrawalInfo("京东实际余额不足,支付失败");
                                updateTwa(twa);
                                resultUtil.setCode(CodeEnum.ERROR.getCode());
                                resultUtil.setMsg("京东实际余额不足,支付失败");
                                return resultUtil;
                            }
                        }else{
                            TWithdrawalApplication twa = new TWithdrawalApplication();
                            twa.setId(record.getTwaId());
                            twa.setWithdrawalStatus(DictEnum.PACKEWITHDRAWERROR.code);
                            twa.setWithdrawalInfo("查询京东余额失败");
                            updateTwa(twa);
                            resultUtil.setCode(CodeEnum.ERROR.getCode());
                            resultUtil.setMsg("提现失败,京东开户表信息不全");
                            return resultUtil;
                        }
                    } else {
                        TWithdrawalApplication twa = new TWithdrawalApplication();
                        twa.setId(record.getTwaId());
                        twa.setWithdrawalStatus(DictEnum.PACKEWITHDRAWERROR.code);
                        twa.setWithdrawalInfo("提现失败,京东开户表信息不全");
                        updateTwa(twa);
                        resultUtil.setCode(CodeEnum.ERROR.getCode());
                        resultUtil.setMsg("提现失败,京东开户表信息不全");
                        return resultUtil;
                    }
                    //查询服务费配置表
                    TWithdrawalApplication tWithdrawalApplication = tWithdrawalApplicationMapper.selectByPrimaryKey(record.getTwaId());
                    TServicefeeInfoVo seVo = new TServicefeeInfoVo();
                    seVo.setUserType(tWithdrawalApplication.getUserOpenRole());
                    seVo.setUserId(tWithdrawalApplication.getCarrierCompanyId());
                    if(DictEnum.CD.code.equals(tWithdrawalApplication.getUserOpenRole())){
                        seVo.setUserOpenType(DictEnum.MYACCOUNT.code);
                    }
                    TServicefeeInfo tServicefeeInfo = tServicefeeInfoMapper.selectByUserIdAndType(seVo);
                    if(null == tServicefeeInfo || "".equals(tServicefeeInfo)){
                        seVo.setUserId(0);
                        tServicefeeInfo = tServicefeeInfoMapper.selectByUserIdAndType(seVo);
                    }
                    //平台服务费规则为空 只支付富民账户手续费
                    BigDecimal orderAmountBig = new BigDecimal(0);
                    if(null == tServicefeeInfo || "".equals(tServicefeeInfo)){
                        if(DictEnum.BD.code.equals(tWithdrawalApplication.getUserOpenRole())){
                            if(tWithdrawalApplication.getAmount().doubleValue()>50000){
                                orderAmountBig = new BigDecimal(6);
                            }else{
                                orderAmountBig = new BigDecimal(0.5);
                            }
                        }
                    }else{
                        seVo.setId(tServicefeeInfo.getId());
                        List<TServicefeeDetails>  tServicefeeDetailsList = tServicefeeDetailsMapper.selectByServicefeeInfoId(seVo);
                        for(TServicefeeDetails tServicefeeDetails:tServicefeeDetailsList){
                            if(tServicefeeDetails.getStartAmount().doubleValue()<=tWithdrawalApplication.getAmount().doubleValue()
                                    && tServicefeeDetails.getEndAmount().doubleValue()>=tWithdrawalApplication.getAmount().doubleValue()){
                                if(DictEnum.FIXEDLIMIT.code.equals(tServicefeeDetails.getServiceType())){
                                    orderAmountBig = tServicefeeDetails.getServiceValue();
                                }else if(DictEnum.FIXEDPROPORTION.code.equals(tServicefeeDetails.getServiceType())){
                                    orderAmountBig = record.getAmount().multiply(tServicefeeDetails.getServiceValue().divide(new BigDecimal(100))).setScale(2,BigDecimal.ROUND_DOWN);;
                                }
                            }
                        }

                    }
                    BigDecimal amountNew = (record.getAmount().subtract(orderAmountBig)).multiply(BigDecimal.valueOf(100));
                    BigDecimal amountNewBig = record.getAmount().subtract(orderAmountBig);
                    CustomerWithdrawReq req = new CustomerWithdrawReq();
                    req.setPartnerId(partnerId);
                    req.setRequestId(IdWorkerUtil.getInstance().nextId());
                    req.setRequestTime(DateUtils.getRequestTime());
                    req.setMerchantCode(merchantCode);
                    req.setBizOrderNo(record.getBizOrderNo());//商户订单号
                    req.setOrderAmount(amountNew.longValue());//交易金额
                    req.setPartnerAccId(partnerAccId);
                    req.setCardId(cardId);
                    SysParam sysParam = sysParamAPI.getParamByKey("JDWITHDRAWNOTIFYURL");
                    req.setNotifyUrl(sysParam.getParamValue());
                    log.info("发起京东提现入参：{}", JSONObject.toJSONString(req));
                    ResultUtil resultUtil1 = cloudPayAPI.cloudPay(req);
                    CustomerWithdrawResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, CustomerWithdrawResponse.class);
                    log.info("发起京东提现回参：{}", JSONObject.toJSONString(response));
                    if(JdEnum.JDSUCCESSCODE.code.equals(response.getResponseCode())){
                        if(!"PAY_FAIL".equals(response.getOrderStatus())){
                            TOrderPayInfo orderPayInfo = new TOrderPayInfo();
                            orderPayInfo.setCode(IdWorkerUtil.getInstance().nextId());
                            orderPayInfo.setOrderActualPayment(amountNewBig);
                            orderPayInfo.setPaymentPlatforms(DictEnum.JDPLATFORMS.code);
                            orderPayInfo.setOrderPayStatus(DictEnum.P110.code);
                            tOrderPayInfoAPI.save(orderPayInfo);

                            TOrderPayDetail orderPayDetail = new TOrderPayDetail();
                            orderPayDetail.setCode(req.getBizOrderNo());
                            orderPayDetail.setOrderPayCode(orderPayInfo.getCode());
                            orderPayDetail.setBankCardId(record.getBankId());
                            orderPayDetail.setBankNo(record.getBankAccountNo());
                            orderPayDetail.setCardHolder(record.getAccountName());
                            orderPayDetail.setTradeType(record.getTradeType());
                            orderPayDetail.setOperateState(JdTradeType.FQ.code);
                            orderPayDetail.setOperateTime(new Date());
                            orderPayDetail.setParam1(String.valueOf(record.getWalletId()));
                            tOrderPayDetailAPI.save(orderPayDetail);
                            //修改钱包
                            TJdWallet tJdWallet= tJdWalletMapper.selectByPrimaryKey(record.getWalletId());
                            if(null != tJdWallet){
                                //添加提现金额
                                BigDecimal withdrawAmount=tJdWallet.getWithdrawAmount().add(amountNewBig);
                                tJdWallet.setWithdrawAmount(withdrawAmount);

                                //添加支付金额
                                BigDecimal entryAmount=tJdWallet.getEntryAmount().add(orderAmountBig);
                                tJdWallet.setEntryAmount(entryAmount);

                                BigDecimal accountBalance = tJdWallet.getAccountBalance().subtract(record.getAmount());
                                tJdWallet.setAccountBalance(accountBalance);
                                tJdWalletMapper.updateByPrimaryKey(tJdWallet);
                                resultUtil.setCode(CodeEnum.SUCCESS.getCode());
                                resultUtil.setMsg("提现成功");
                                return resultUtil;
                            }else {
                                resultUtil.setCode(CodeEnum.ERROR.getCode());
                                resultUtil.setMsg("未找到钱包");
                                return resultUtil;
                            }
                        }else{
                            TWithdrawalApplication twa = new TWithdrawalApplication();
                            twa.setId(record.getTwaId());
                            twa.setWithdrawalStatus(DictEnum.PACKEWITHDRAWERROR.code);
                            twa.setWithdrawalInfo("提现失败,京东返回状态失败："+response.getResponseDesc());
                            updateTwa(twa);
                            resultUtil.setCode(CodeEnum.ERROR.getCode());
                            resultUtil.setMsg("提现失败："+response.getResponseDesc());
                            return resultUtil;
                        }
                    }else{
                        TWithdrawalApplication twa = new TWithdrawalApplication();
                        twa.setId(record.getTwaId());
                        twa.setWithdrawalStatus(DictEnum.PACKEWITHDRAWERROR.code);
                        twa.setWithdrawalInfo("提现失败,调用京东失败："+response.getResponseDesc());
                        updateTwa(twa);
                        resultUtil.setCode(CodeEnum.ERROR.getCode());
                        resultUtil.setMsg("提现失败："+response.getResponseDesc());
                        return resultUtil;
                    }
                }
            }
        } catch (Exception e) {
            log.error("ZJJ-052,提现失败", e);
            TWithdrawalApplication twa = new TWithdrawalApplication();
            twa.setId(record.getTwaId());
            twa.setWithdrawalStatus(DictEnum.PACKEWITHDRAWERROR.code);
            twa.setWithdrawalInfo("提现失败,服务器异常");
            updateTwa(twa);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("提现失败");
            }
        }finally {
            if (walletLock && redisUtil.hasKey(walletKey)) {
                redisUtil.del(walletKey);
            }
        }
        return resultUtil;
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/11/29 11:27
     *  @Description:  手续费余额支付
     */
    @Override
    public ResultUtil serviceBalancePay(JdWithdrawVo record) {
        boolean flag = true;
        TWithdrawalApplication tWithdrawalApplication = tWithdrawalApplicationMapper.selectByCode(record.getBizOrderNo());
        if(DictEnum.BD.code.equals(tWithdrawalApplication.getUserOpenRole())
                || DictEnum.CD.code.equals(tWithdrawalApplication.getUserOpenRole())){
            //查询服务费配置表
            TServicefeeInfoVo seVo = new TServicefeeInfoVo();
            seVo.setUserType(tWithdrawalApplication.getUserOpenRole());
            seVo.setUserId(tWithdrawalApplication.getCarrierCompanyId());
            TServicefeeInfo tServicefeeInfo = tServicefeeInfoMapper.selectByUserIdAndType(seVo);
            if(DictEnum.CD.code.equals(tWithdrawalApplication.getUserOpenRole())){
                seVo.setUserOpenType(DictEnum.MYACCOUNT.code);
            }
            if(null == tServicefeeInfo || "".equals(tServicefeeInfo)){
                seVo.setUserId(0);
                tServicefeeInfo = tServicefeeInfoMapper.selectByUserIdAndType(seVo);
            }
            //平台服务费规则为空 只支付富民账户手续费
            Long orderAmount = 0l;
            BigDecimal orderAmountBig = null;
            orderAmountBig = record.getAmount();
            orderAmount = (record.getAmount().multiply(BigDecimal.valueOf(100))).longValue();
            if(null == tServicefeeInfo || "".equals(tServicefeeInfo)){
                if(!DictEnum.BD.code.equals(tWithdrawalApplication.getUserOpenRole())){
                    flag = false;
                }
            }
            //为false 时 自然人没配置收手续费不收取
            if(flag){
                CustomerBalancePayReq req = new CustomerBalancePayReq();
                req.setPartnerId(partnerId);
                req.setRequestId(IdWorkerUtil.getInstance().nextId());
                req.setRequestTime(DateUtils.getRequestTime());
                req.setMerchantCode(merchantCode);
                //String bizOrderNo = "com"+ IdWorkerUtil.getInstance().nextId().substring(3,32);
                req.setBizOrderNo(tWithdrawalApplication.getBizOrderNo());
                req.setOutPartnerAccId(tWithdrawalApplication.getOutAccount());
                TCarrierCompanyOpenRoleVo repf = new TCarrierCompanyOpenRoleVo();
                repf.setUserOpenRole(DictEnum.PF.code);
                TCarrierCompanyOpenRole pfResult = tCarrierCompanyOpenRoleMapper.selectByRoleBy(repf);
                if(!JdEnum.BIND.code.equals(pfResult.getPaymentStatus())){
                    log.error("提现手续费支付失败：收款方银行卡未绑定成功 ----------");
                    tWithdrawalApplication.setWithdrawalStatus(DictEnum.PACKEWITHDRAWERROR.code);
                    tWithdrawalApplication.setWithdrawalInfo("提现手续费支付失败：收款方银行卡未绑定成功");
                    tWithdrawalApplicationMapper.updateByPrimaryKeySelective(tWithdrawalApplication);
                    return ResultUtil.ok();
                }
                req.setInPartnerAccId(pfResult.getPartnerAccId());
                req.setOrderAmount(orderAmount);
                req.setGoodsInfo("提现手续费");
                SysParam sysParam = sysParamAPI.getParamByKey("JDWITHDRAWNOTIFYURL");
                req.setNotifyUrl(sysParam.getParamValue());
                ResultUtil resultUtil1 = cloudPayAPI.cloudPay(req);
                CustomerBalancePayResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, CustomerBalancePayResponse.class);
                if(JdEnum.JDSUCCESSCODE.code.equals(response.getResponseCode())){
                    if(!"PAY_FAIL".equals(response.getOrderStatus())){
                        TOrderPayInfo orderPayInfo = new TOrderPayInfo();
                        orderPayInfo.setCode(IdWorkerUtil.getInstance().nextId());
                        orderPayInfo.setOrderActualPayment(orderAmountBig);
                        orderPayInfo.setPaymentPlatforms(DictEnum.JDPLATFORMS.code);
                        orderPayInfo.setOrderPayStatus(DictEnum.P070.code);
                        tOrderPayInfoAPI.save(orderPayInfo);
                        TOrderPayDetail orderPayDetail = new TOrderPayDetail();
                        orderPayDetail.setCode(req.getBizOrderNo());
                        orderPayDetail.setOrderPayCode(orderPayInfo.getCode());
                        orderPayDetail.setBankCardId(tWithdrawalApplication.getBankId());
                        orderPayDetail.setBankNo(tWithdrawalApplication.getBankAccountNo());
                        orderPayDetail.setCardHolder(tWithdrawalApplication.getAccountName());
                        String tradeType = "";
                        if(DictEnum.CD.code.equals(tWithdrawalApplication.getUserOpenRole())){
                            tradeType = JDTradeTypeEnum.MANGER_SERVICE_PAY.code;
                        }else if(DictEnum.BD.code.equals(tWithdrawalApplication.getUserOpenRole())){
                            tradeType = JDTradeTypeEnum.BD_SERVICE_PAY.code;
                        }else if(DictEnum.PF.code.equals(tWithdrawalApplication.getUserOpenRole())){
                            tradeType = JDTradeTypeEnum.PF_SERVICE_PAY.code;
                        }else if(DictEnum.CA.code.equals(tWithdrawalApplication.getUserOpenRole())){
                            tradeType = JDTradeTypeEnum.CA_SERVICE_PAY.code;
                        }
                        orderPayDetail.setTradeType(tradeType);
                        orderPayDetail.setOperateState(JdTradeType.RZ.code);
                        orderPayDetail.setOperateTime(new Date());
                        orderPayDetail.setParam1(String.valueOf(tWithdrawalApplication.getWalletId()));
                        tOrderPayDetailAPI.save(orderPayDetail);
                    }
                }else{
                    tWithdrawalApplication.setWithdrawalStatus(DictEnum.PACKEWITHDRAWERROR.code);
                    tWithdrawalApplication.setWithdrawalInfo("提现手续费支付失败："+response.getResponseDesc());
                    tWithdrawalApplicationMapper.updateByPrimaryKeySelective(tWithdrawalApplication);
                    return ResultUtil.ok();
                }
            }
        }
        return ResultUtil.ok();
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/9/3 15:13
     *  @Description: 小工具查询京东余额
     */
    @Override
    public ResultUtil selectByBalance(CustomerQueryBalanceReq req) {
        req.setPartnerId(partnerId);
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setMerchantCode(merchantCode);
        log.info("查询京东余额入参：{}", JSONObject.toJSONString(req));
        ResultUtil resultUtil1 = cloudPayAPI.cloudPay(req);
        CustomerQueryBalanceResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, CustomerQueryBalanceResponse.class);
        log.info("查询京东余额回参：{}", JSONObject.toJSONString(response));
        return ResultUtil.ok(response);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/9/3 15:26
     *  @Description: 查询交易对账单
     */
    @Override
    public ResultUtil selectByCheckAccountFileDownload(PartnerCheckAccountReq req) {
        req.setCheckDate(DateUtil.dateToStr(req.getDate(),2));
        req.setPartnerId(partnerId);
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setCheckFileType("003");
        log.info("查询交易对账单入参：{}", JSONObject.toJSONString(req));
        ResultUtil resultUtil1 = cloudPayAPI.cloudPay(req);
        PartnerCheckAccountInfoResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, PartnerCheckAccountInfoResponse.class);
        log.info("查询交易对账单回参：{}", JSONObject.toJSONString(response));
        String fileName = DateUtil.dateToStr(req.getDate(),2) + ".csv";
        InputStream inputStream = new ByteArrayInputStream(response.getCheckContent().getBytes());
        MultipartFile multipartFile = null;
        try {
            multipartFile = new MockMultipartFile("file", fileName, "csv", inputStream);
        } catch (IOException e) {
            e.printStackTrace();
        }
        //文件上传
        /*ResultUtil fastResult = fastdfsAPI.uploadFileSample(multipartFile);
        Map<Object, Object> fastResultData = (Map<Object, Object>) fastResult.getData();*/

        ResultUtil fastResult = ossAPI.uploadFileSample(multipartFile);
        //图片下载路径
        String paramPath = fastResult.getData().toString();
        return ResultUtil.ok(paramPath);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/10/5 9:04
     *  @Description: 小工具余额支付
     */
    @Override
    public ResultUtil balancePay(CustomerBalancePayReq req) {
        Integer outWalletId = null;
        TCarrierCompanyOpenRoleVo outOpenVo = tCarrierCompanyOpenRoleMapper.selectByPartnerAccId(req.getOutPartnerAccId());
        TEndUserOpenRoleVo outOpenVoEnd = tEndUserOpenRoleMapper.selectByPartnerAccId(req.getOutPartnerAccId());
        if(null !=outOpenVo && !"".equals(outOpenVo)){
            outWalletId = outOpenVo.getWalletId();
        }else{
            outWalletId = outOpenVoEnd.getWalletId();
        }
        //req.setAmount(new BigDecimal(10));
        req.setPartnerId(partnerId);
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setMerchantCode(merchantCode);
        req.setBizOrderNo(IdWorkerUtil.getInstance().nextId());
        BigDecimal amount = req.getAmount().multiply(BigDecimal.valueOf(100));
        req.setOrderAmount(amount.longValue());
        req.setGoodsInfo("余额支付");
        SysParam sysParam = sysParamAPI.getParamByKey("BALANCEPAYNOTIFYURL");
        req.setNotifyUrl(sysParam.getParamValue());
        ResultUtil resultUtil1 = cloudPayAPI.cloudPay(req);
        CustomerBalancePayResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, CustomerBalancePayResponse.class);
        if(JdEnum.JDSUCCESSCODE.code.equals(response.getResponseCode())) {
            if (!"PAY_FAIL".equals(response.getOrderStatus())) {
                TOrderPayInfo orderPayInfo = new TOrderPayInfo();
                orderPayInfo.setCode(IdWorkerUtil.getInstance().nextId());
                orderPayInfo.setOrderActualPayment(req.getAmount());
                orderPayInfo.setOrderPayStatus(DictEnum.P070.code);
                tOrderPayInfoAPI.save(orderPayInfo);
                TOrderPayDetail orderPayDetail = new TOrderPayDetail();
                orderPayDetail.setCode(req.getBizOrderNo());
                orderPayDetail.setOrderPayCode(orderPayInfo.getCode());
                orderPayDetail.setTradeType(JDTradeTypeEnum.TOOL_BALANCE_PAY.code);
                orderPayDetail.setOperateState(JdTradeType.RZ.code);
                orderPayDetail.setOperateTime(new Date());
                orderPayDetail.setParam1(String.valueOf(outWalletId));
                tOrderPayDetailAPI.save(orderPayDetail);
                //修改钱包
                TJdWallet tJdWallet = tJdWalletMapper.selectByPrimaryKey(outWalletId);
                if (null != tJdWallet) {
                    //添加支付金额
                    BigDecimal entryAmount = tJdWallet.getEntryAmount().add(req.getAmount());
                    tJdWallet.setEntryAmount(entryAmount);

                    /*BigDecimal withdrawAmount = tJdWallet.getWithdrawAmount().add(req.getAmount()).subtract(entryAmount);
                    tJdWallet.setWithdrawAmount(withdrawAmount);*/

                    BigDecimal accountBalance = tJdWallet.getAccountBalance().subtract(req.getAmount());
                    tJdWallet.setAccountBalance(accountBalance);
                    tJdWalletMapper.updateByPrimaryKey(tJdWallet);
                } else {
                    return ResultUtil.error("余额支付失败，付款方钱包为空");
                }
            }
        }
        return ResultUtil.ok();
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/10/5 9:33
     *  @Description: 查询账户收支明细
     */
    @Override
    public ResultUtil queryAccountFlow(CustomerQueryAccountFlowReq req) {
        req.setPartnerId(partnerId);
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setMerchantCode(merchantCode);
        ResultUtil resultUtil1 = cloudPayAPI.cloudPay(req);
        CustomerQueryAccountFlowResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, CustomerQueryAccountFlowResponse.class);
        return ResultUtil.ok(response);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/10/5 9:33
     *  @Description: 查询电子回单
     */
    @Override
    public ResultUtil receipt(CustomerReceiptReq req) {
        req.setPartnerId(partnerId);
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setMerchantCode(merchantCode);
        ResultUtil resultUtil1 = cloudPayAPI.cloudPay(req);
        CustomerReceiptResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, CustomerReceiptResponse.class);
        return ResultUtil.ok(response);
    }

    @Override
    public ResultUtil applyTradeVoucher(CustomerApplyTradeVoucherReq req) {
        req.setPartnerId(partnerId);
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setMerchantCode(merchantCode);
        req.setBankVoucherType("ACCOUNT_BILL");
        req.setBizOrderNo(IdWorkerUtil.getInstance().nextId());
        log.info("BizOrderNo:"+req.getBizOrderNo());
        ResultUtil resultUtil1 = cloudPayAPI.cloudPay(req);
        CustomerApplyTradeVoucherResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, CustomerApplyTradeVoucherResponse.class);
        return ResultUtil.ok(response);
    }

    @Override
    public ResultUtil queryTradeVoucher(CustomerQueryTradeVoucherReq req) {
        req.setPartnerId(partnerId);
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setMerchantCode(merchantCode);
        ResultUtil resultUtil1 = cloudPayAPI.cloudPay(req);
        CustomerQueryTradeVoucherResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, CustomerQueryTradeVoucherResponse.class);
        if(null == response.getUrl() || "".equals(response.getUrl())){
            response.setUrl(String.valueOf(redisUtil.get("VIRTUAL_TRADE_VOUCHER_DOWNLOAD_NOTICE"+req.getBizOrderNo())));
        }
        return ResultUtil.ok(response);
    }

    @Override
    public ResultUtil queryBindCardStatus(VirtualAccountQueryBindStatusReq req) {
        req.setPartnerId(partnerId);
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setBizOrderNo(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        ResultUtil resultUtil1 = cloudPayAPI.cloudPay(req);
        VirtualAccountQueryBindStatusResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, VirtualAccountQueryBindStatusResponse.class);
        return ResultUtil.ok(response);
    }

    @Override
    public ResultUtil bindBankCard(OpenMemberBindAccountReq record) {
        TEndUserOpenRoleVo endUserOpenRoleVo = tEndUserOpenRoleMapper.selectByPartnerAccId(record.getPartnerAccId());
        record.setPartnerId(partnerId);
        record.setRequestId(IdWorkerUtil.getInstance().nextId());
        record.setRequestTime(DateUtils.formatDate(new Date(), "yyyyMMddHHmmss"));
        record.setPartnerMemberId(endUserOpenRoleVo.getPartnerMemberId());
        record.setChannelId(JdEnum.channelId.code);
        record.setAcctType("D");
        record.setAcctNo(record.getAcctNo());
        record.setAcctName(record.getAcctName().trim());
        record.setOccBankPhone(record.getOccBankPhone());
        log.info("小工具对私账户绑卡：入参->" + JSON.toJSONString(record) );
        //jd对私账户绑卡接口
        ResultUtil resultUtil = cloudPayAPI.cloudPay(record);
        OpenMemberBindAccountResponse response = CloudPayFormatUtil.ObjToBean(resultUtil, OpenMemberBindAccountResponse.class);

        TEnduserAccount enduserAccount = enduserAccountMapper.selectByEndUserId(endUserOpenRoleVo.getEndUserId());
        List<TBankCard> bankCardList = tBankCardMapper.getBankCardListByAccountId(enduserAccount.getAccountId());
        TBankCard tBankCard = new TBankCard();
        tBankCard.setCardNo(record.getAcctNo());
        tBankCard.setCardOwnerPhone(record.getOccBankPhone());
        tBankCard.setCardOwner(record.getAcctName().trim());
        tBankCard.setAccountId(enduserAccount.getAccountId());
        tBankCard.setCreateUser(CurrentUser.getUserNickname());
        tBankCard.setCreateTime(new Date());
        tBankCard.setEnable(true);
        if (bankCardList.size() == 0) {//第一张银行卡是默认的
            tBankCard.setIfDefault(1);
        }
        tBankCardMapper.insertSelective(tBankCard);

        //t_jd_bank_card //绑定成功银行卡才可以使用
        if(response.getResponseCode().equals(JdEnum.JDSUCCESSCODE.code)){
            TJdBankCard tJdBankCard = new TJdBankCard();
            tJdBankCard.setUserOpenRole("CD");
            tJdBankCard.setCode(record.getRequestId());
            tJdBankCard.setOpenRoleId(endUserOpenRoleVo.getId());
            tJdBankCard.setBankCardId(tBankCard.getId());
            tJdBankCard.setBindStatus(response.getBindStatus());
            tJdBankCard.setCreateTime(new Date());
            tJdBankCard.setCreateUser(CurrentUser.getUserNickname());
            if(JdEnum.BIND.code.equals(response.getBindStatus())){
                //绑定成功
                tJdBankCard.setDataEnable(true);
                tJdBankCard.setEnable(false);
            }else {
                //绑卡处理中
                tJdBankCard.setDataEnable(false);
                tJdBankCard.setEnable(true);
            }
            tJdBankCardMapper.insertSelective(tJdBankCard);
            return ResultUtil.ok(response.getBindStatus(),"京东银行卡绑卡成功"+response.getResponseDesc());
        }else {
            return ResultUtil.error("京东银行卡绑卡失败"+response.getResponseDesc());
        }

    }


    /**
     *  @author: dingweibo
     *  @Date: 2021/9/1 14:28
     *  @Description: 修改提现申请表状态
     */
    private void updateTwa(TWithdrawalApplication record){
        TWithdrawalApplication tWithdrawalApplication = tWithdrawalApplicationMapper.selectByPrimaryKey(record.getId());
        tWithdrawalApplication.setWithdrawalStatus(record.getWithdrawalStatus());
        tWithdrawalApplication.setWithdrawalInfo(record.getWithdrawalInfo());
        tWithdrawalApplicationMapper.updateByPrimaryKeySelective(tWithdrawalApplication);
    }

    @Override
    public ResultUtil queryOpenStatus(OpenQueryAccountInfoReq req) {
        try {
            req.setPartnerId(partnerId);
            req.setRequestId(IdWorkerUtil.getInstance().nextId());
            req.setRequestTime(DateUtils.getRequestTime());
            ResultUtil resultUtil = cloudPayAPI.cloudPay(req);
            OpenQueryAccountInfoResponse response = CloudPayFormatUtil.ObjToBean(resultUtil, OpenQueryAccountInfoResponse.class);;
            return ResultUtil.ok(response);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询京东开户状态失败, {}", e);
            if (StringUtils.checkChineseCharacter(e.getMessage())) {
                return ResultUtil.error(e.getMessage());
            }
        }
        return ResultUtil.error("查询失败");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil txTransferToCarrier(BatchCustomerBalancePayReq req) {
        try {
            Integer outWalletId = null;
            TEndUserOpenRoleVo outOpenVoEnd = tEndUserOpenRoleMapper.selectByPartnerAccId(req.getOutPartnerAccId());
            if(outOpenVoEnd == null){
                return ResultUtil.error("未找到对应的C端开户信息", req.getHalfApplicationCode());
            }
            TCarrierCompanyOpenRoleVo inOpenRole = tCarrierCompanyOpenRoleMapper.selectByPartnerAccId(req.getInPartnerAccId());
            if (null == inOpenRole) {

                return ResultUtil.error("未找到对应的承运方开户信息", req.getHalfApplicationCode());
            }
            outWalletId = outOpenVoEnd.getWalletId();

            TOrderPayInfo orderPayInfo = new TOrderPayInfo();
            orderPayInfo.setCode(IdWorkerUtil.getInstance().nextId());
            orderPayInfo.setOrderActualPayment(req.getAmount());
            orderPayInfo.setOrderPayStatus(DictEnum.P070.code);
            orderPayInfoMapper.insertSelective(orderPayInfo);
            TOrderPayDetail orderPayDetail = new TOrderPayDetail();
            orderPayDetail.setCode(IdWorkerUtil.getInstance().nextId());
            orderPayDetail.setOrderPayCode(orderPayInfo.getCode());
            orderPayDetail.setTradeType(JDTradeTypeEnum.TX_TRANSFER_TO_CARRIER.code);
            orderPayDetail.setOperateState(JdTradeType.RZ.code);
            orderPayDetail.setOperateTime(new Date());
            orderPayDetail.setParam1(String.valueOf(outWalletId));
            orderPayDetailMapper.insertSelective(orderPayDetail);

            req.setPartnerId(partnerId);
            req.setRequestId(IdWorkerUtil.getInstance().nextId());
            req.setRequestTime(DateUtils.getRequestTime());
            req.setMerchantCode(merchantCode);
            req.setBizOrderNo(orderPayDetail.getCode());
            req.setOrderAmount(OrderMoneyUtil.changeY2F(req.getAmount()));
            req.setGoodsInfo("C端提现转账到承运方");
            SysParam sysParam = sysParamAPI.getParamByKey(DictEnum.TXTRANSFERTOCARRIERNOTIFYURL.code);
            req.setNotifyUrl(sysParam.getParamValue());
            log.info("C端提现转账到承运方, {}", JSONUtil.toJsonStr(req));

            // 保存消息
            Message message = new Message();
            message.setTopic(MqMessageTopic.PERSONKIND);
            message.setTag(MqMessageTag.TXTRANSFERTOCARRIER);
            message.setKey(orderPayDetail.getCode());
            message.setBody(JSONUtil.toJsonStr(req).getBytes(StandardCharsets.UTF_8));
            mqAPI.saveMessage(message);
            ResultUtil resultUtil1 = cloudPayAPI.cloudPay(req);
            CustomerBalancePayResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, CustomerBalancePayResponse.class);
            if(JdEnum.JDSUCCESSCODE.code.equals(response.getResponseCode()) && JdEnum.ACCEPT_SUCC.code.equals(response.getOrderStatus()) ) {
                // 提现转账申请记录支付主表、子表code
                TWithdrawBehalfApplication application = new TWithdrawBehalfApplication();
                application.setCode(req.getHalfApplicationCode());
                application.setOrderPayCode(orderPayInfo.getCode());
                application.setPayDetailCode(orderPayDetail.getCode());
                application.setCarrierId(inOpenRole.getCarrierCompanyId());
                application.setCarrierWalletId(inOpenRole.getWalletId());
                application.setCarrierPartnerAccId(inOpenRole.getPartnerAccId());
                withdrawBehalfApplicationMapper.updateByCodeSelective(application);
            } else {
                // 修改支付主表
                orderPayInfoMapper.updateStatusByCode(orderPayInfo.getCode(), DictEnum.M080.code);
                // 修改支付子表
                TOrderPayDetailVO vo = new TOrderPayDetailVO();
                vo.setCode(response.getBizOrderNo());
                vo.setErrorCode(response.getResponseCode());
                vo.setErrorMsg(response.getResponseDesc());
                vo.setTradeStatus(DictEnum.TRADE_FAILED.code);
                vo.setReturnTime(new Date());
                orderPayDetailMapper.updateStatusByPayCode(vo);
                ResultUtil resultUtil = ResultUtil.error(StringUtils.isNotBlank(response.getResponseDesc()) ? response.getResponseDesc() : "发起转账失败");
                resultUtil.setData(req.getHalfApplicationCode());
                return resultUtil;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("发起转账失败, {}", e);
            if (null != e.getMessage() && StringUtils.checkChineseCharacter(e.getMessage())) {
                throw new RuntimeException(e.getMessage());
            }
            throw e;
        }
        ResultUtil resultUtil = ResultUtil.ok();
        resultUtil.setData(req.getHalfApplicationCode());
        return resultUtil;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil txTransferToCarrierSuccess(BatchCustomerBalancePayResp resp) {
        try {
            TWithdrawBehalfApplication tWithdrawBehalfApplication = withdrawBehalfApplicationMapper.selectByCode(resp.getHalfApplicationCode());
            // 修改支付主表
            orderPayInfoMapper.updateStatusByCode(tWithdrawBehalfApplication.getOrderPayCode(), DictEnum.M090.code);
            // 修改支付子表
            TOrderPayDetailVO vo = new TOrderPayDetailVO();
            vo.setCode(tWithdrawBehalfApplication.getPayDetailCode());
            vo.setTradeStatus(DictEnum.TRADE_FINISHED.code);
            vo.setReturnTime(new Date());
            orderPayDetailMapper.updateStatusByPayCode(vo);
            // 修改钱包
            TJdWallet outWallet = tJdWalletMapper.selectByEnduserPartnerAccId(tWithdrawBehalfApplication.getEndUserPartnerAccId());
            log.info("提现转账成功导入, 修改钱包, {}", JSONUtil.toJsonStr(outWallet));
            BigDecimal withdrawAmount = outWallet.getWithdrawAmount().subtract(tWithdrawBehalfApplication.getAmount());
            outWallet.setWithdrawAmount(withdrawAmount);
            outWallet.setParam1("0");
            outWallet.setUpdateTime(new Date());
            tJdWalletMapper.updateByPrimaryKeySelective(outWallet);

            TBankCard tBankCard = tBankCardMapper.selectByPrimaryKey(tWithdrawBehalfApplication.getBankCardId());
            // 记录钱包流水
            TJdWalletChangeLog walletChangeLog = new TJdWalletChangeLog();
            walletChangeLog.setJdWalletId(outWallet.getId());
            walletChangeLog.setAmount(tWithdrawBehalfApplication.getAmount());
            walletChangeLog.setBankOrderNo(resp.getBankOrderNo());
            walletChangeLog.setOutPartnerAccId(tWithdrawBehalfApplication.getEndUserPartnerAccId());
            walletChangeLog.setInPartnerAccId(tBankCard.getCardNo());
            walletChangeLog.setBankCardId(tWithdrawBehalfApplication.getBankCardId());
            walletChangeLog.setCardNo(tBankCard.getCardNo());
            walletChangeLog.setBankCardName(tBankCard.getCardOwner());
            walletChangeLog.setPurseCategory(outWallet.getPurseCategory());
            walletChangeLog.setTradeNo(tWithdrawBehalfApplication.getTxPayDetailCode());
            if (DictEnum.CMANAGER.code.equals(outWallet.getPurseCategory())) {
                walletChangeLog.setTradeType(DictEnum.MANAGER.code);
            } else {
                walletChangeLog.setTradeType(DictEnum.CTIXIAN.code);
            }
            walletChangeLog.setTradeTime(tWithdrawBehalfApplication.getCreateTime());
            walletChangeLog.setParam1("1");
            walletChangeLog.setCreateTime(new Date());
            walletChangeLog.setUpdateTime(walletChangeLog.getCreateTime());
            jdWalletChangeLogMapper.insertSelective(walletChangeLog);

            TOrderPayDetail tOrderPayDetail = orderPayDetailMapper.selectByCode(tWithdrawBehalfApplication.getTxPayDetailCode());

            // 修改提现支付主表状态
            orderPayInfoMapper.updateStatusByCode(tOrderPayDetail.getOrderPayCode(), DictEnum.M130.code);
            // 修改提现支付子表
            orderPayDetailMapper.updateStatusByCode(tOrderPayDetail.getCode(), DictEnum.TRADE_FINISHED.code, new Date());

            // 修改提现转账申请状态为提现完成
            tWithdrawBehalfApplication.setStatus(2);
            tWithdrawBehalfApplication.setUpdateTime(new Date());
            withdrawBehalfApplicationMapper.updateByPrimaryKeySelective(tWithdrawBehalfApplication);
            // 发送微信通知
            if (!DictEnum.CMANAGER.code.equals(outWallet.getPurseCategory())) {
                sendWXTXMessage(tWithdrawBehalfApplication.getEndUserId(), tWithdrawBehalfApplication.getAmount(), tWithdrawBehalfApplication.getCreateTime(), tBankCard.getCardNo(), tWithdrawBehalfApplication.getCode());
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("导入提现转账回调失败, {}", e);
            throw e;
        }

        ResultUtil resultUtil = ResultUtil.ok();
        resultUtil.setData(resp.getHalfApplicationCode());
        return resultUtil;
    }

    public void sendWXTXMessage(Integer endUserId, BigDecimal amount, Date applyTime, String bankCardNo, String bankOrderNo) {
        //发送微信通知
        try {
            log.info("发起微信通知");
            // 查询C端openId
            TAccountDTO tAccountDTO = accountMapper.queryOpenIdByEnduserId(endUserId);
            SysParam sysParam = sysParamAPI.getParamByKey("WXMessageUrl");
            if (sysParam != null && StringUtils.isNotBlank(tAccountDTO.getOpenId())) {
                sendMsgJDTx(bankOrderNo, bankCardNo, applyTime, amount,
                        tAccountDTO.getOpenId(), tAccountDTO.getRealName(), sysParam.getParamValue());
                log.info("发起微信通知完成");
            }
        } catch (Exception e) {
            log.error("发起微信通知失败", e);
        }
    }

    public boolean sendMsgJDTx(String bankOrderNo, String bankNo, Date date, BigDecimal money,
                                      String openId, String realName, String wxUrl) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
        Map<String, String> querys = new HashMap<String, String>();
        querys.put("TemplateType", "JDTX");
        querys.put("openId", openId);
        querys.put("first", "尊敬的" + realName + "司机，恭喜您提现成功!");
        querys.put("keyword1", money.setScale(2, BigDecimal.ROUND_DOWN) + "元");
        querys.put("keyword2", "提现成功");
        querys.put("keyword3", "银行卡" + bankNo);
        querys.put("keyword4", bankOrderNo);
        querys.put("keyword5", sdf.format(date));
        querys.put("remark", "收到通知后，请及时关注银行卡收款信息，如24小时后仍未到账，请拨打客服热线***********、***********。陆港通平台，创新科技，匠心服务。");
        log.info("请求A参数{}",querys);
        StringBuffer buffer = new StringBuffer(wxUrl);
        buffer.append("?");
        for (Map.Entry<String, String> entry : querys.entrySet()) {
            buffer.append(entry.getKey()).append("=")
                    .append(URLEncoder.encode(String.valueOf(entry.getValue())))
                    .append("&");
        }
        buffer.deleteCharAt(buffer.length() - 1);
        log.info("请求b参数{}",buffer);
        try {
            HttpUtil.sendPost(buffer.toString(), null, "UTF-8");
        } catch (Exception e) {
            log.error("发送提现微信消息失败", e);
            return false;
        }
        return true;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil managerQoutaTx(JdWithdrawVo vo) {
        TEndUserInfo tEndUserInfo = tEndUserInfoMapper.selectByPrimaryKey(CurrentUser.getEndUserId());
        TEndUserOpenRole tEndUserOpenRole = tEndUserOpenRoleMapper.selectByEndUserId(CurrentUser.getEndUserId());
        // 修改钱包
        TJdWallet tJdWallet = tJdWalletMapper.selectWalletByEndUserId(CurrentUser.getEndUserId(), DictEnum.CMANAGER.code);
        if (null == tJdWallet) {
           return ResultUtil.error("京东钱包不存在，请联系运营平台予以解决。");
        }
        log.info("经纪人超额提现, 经纪人钱包, {}", JSONUtil.toJsonStr(tJdWallet));
        // 修改钱包
        BigDecimal accountAmount = tJdWallet.getAccountBalance().subtract(vo.getAmount());
        BigDecimal withdrawAmount = tJdWallet.getWithdrawAmount().add(vo.getAmount());
        tJdWallet.setAccountBalance(accountAmount);
        tJdWallet.setWithdrawAmount(withdrawAmount);
        tJdWallet.setParam1("1");
        tJdWallet.setUpdateTime(new Date());
        tJdWalletMapper.updateByPrimaryKeySelective(tJdWallet);
        TBankCard tBankCard = tBankCardMapper.selectByPrimaryKey(vo.getBankId());
        Boolean selfCard = false;
        if (tEndUserInfo.getRealName().equals(tBankCard.getCardOwner())
                && tEndUserInfo.getIdcard().equals(tBankCard.getCardOwnerIdcard())
                && tEndUserInfo.getPhone().equals(tBankCard.getCardOwnerPhone())) {
            selfCard = true;
        }

        TOrderPayInfo orderPayInfo = new TOrderPayInfo();
        orderPayInfo.setCode(IdWorkerUtil.getInstance().nextId());
        orderPayInfo.setPaymentPlatforms(DictEnum.JDPLATFORMS.code);
        orderPayInfo.setOrderActualPayment(vo.getAmount());
        orderPayInfo.setOrderTotalPayment(vo.getAmount());
        orderPayInfo.setOrderPayStatus(DictEnum.P110.code);
        orderPayInfoMapper.insertSelective(orderPayInfo);

        TOrderPayDetail orderPayDetail = new TOrderPayDetail();
        orderPayDetail.setCode(IdWorkerUtil.getInstance().nextId());
        orderPayDetail.setOrderPayCode(orderPayInfo.getCode());
        orderPayDetail.setBankNo(tBankCard.getCardNo());
        orderPayDetail.setCardHolder(tBankCard.getCardOwner());
        orderPayDetail.setBankCardId(tBankCard.getId());
        orderPayDetail.setTradeType(DictEnum.TX.code);
        orderPayDetail.setOperateState(JdTradeType.RZ.code);
        orderPayDetail.setOperateTime(new Date());
        orderPayDetail.setParam1(String.valueOf(tJdWallet.getId()));
        orderPayDetail.setParam2(String.valueOf(CurrentUser.getEndUserId()));
        if (selfCard) {
            orderPayDetail.setParam3("1");
        } else {
            orderPayDetail.setParam3("0");
        }
        orderPayDetailMapper.insertSelective(orderPayDetail);

        // 添加代付申请
        TWithdrawBehalfApplication application = new TWithdrawBehalfApplication();
        application.setCode(IdWorkerUtil.getInstance().nextId());
        application.setEndUserId(CurrentUser.getEndUserId());
        application.setEndUserWalletId(tJdWallet.getId());
        application.setEndUserPartnerAccId(tEndUserOpenRole.getPartnerAccId());
        application.setBankCardId(tBankCard.getId());
        application.setTxPayDetailCode(orderPayDetail.getCode());
        if (selfCard) {
            application.setSelfCard(true);
        } else {
            application.setSelfCard(false);
        }
        application.setAmount(vo.getAmount());
        application.setStatus(0);
        withdrawBehalfApplicationMapper.insertSelective(application);


        return ResultUtil.ok("您的提现申请已提交，预计于次日到账，请耐心等待。");
    }

}
