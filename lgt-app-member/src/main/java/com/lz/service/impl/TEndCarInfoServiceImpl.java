package com.lz.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.codingapi.txlcn.tc.annotation.LcnTransaction;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lz.api.MqAPI;
import com.lz.api.TOrderInfoAPI;
import com.lz.api.TrajectoryRecentAPI;
import com.lz.common.config.RedisUtil;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.model.CodeEnum;
import com.lz.common.model.datareport.hbdr.DataReport;
import com.lz.common.model.datareport.hbdr.ReportResult;
import com.lz.common.model.datareport.hbdr.carreport.CarReportDTO;
import com.lz.common.util.*;
import com.lz.dao.*;
import com.lz.dto.*;
import com.lz.example.TEndCarInfoExample;
import com.lz.example.TEnduserAccountExample;
import com.lz.model.*;
import com.lz.service.TEndAutomatedAuditingServie;
import com.lz.service.TEndCarInfoService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.vo.*;
import com.lz.vo.certificatevo.TransportLicenseVO;
import com.lz.vo.certificatevo.TravelAndTransportLicenseVO;
import com.lz.vo.certificatevo.TravelCardVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2019-04-10
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class TEndCarInfoServiceImpl implements TEndCarInfoService {

    @Resource
    private TEndCarInfoMapper tEndCarInfoMapper;
    @Resource
    private TEndUserCarRelMapper tEndUserCarRelMapper;
    @Resource
    private TAccountMapper tAccountMapper;

    @Resource
    private TEndUserInfoMapper tEndUserInfoMapper;
    @Resource
    private TBankCardMapper tBankCardMapper;
    @Resource
    private TEndCarStatusMapper tEndCarStatusMapper;

    private final static String[] ORDERSTATE= {DictEnum.M010.code, DictEnum.M020.code, DictEnum.M030.code};

    @Resource
    private TOrderInfoAPI tOrderInfoAPI;

    @Autowired
    private SysParamAPI sysParamAPI;

    @Autowired
    private TrajectoryRecentAPI trajectoryRecentAPI;

    @Autowired
    private TEnduserAccountMapper tEnduserAccountMapper;

    @Autowired
    private TJdBankCardMapper tJdBankCardMapper;

    @Autowired
    private MqAPI mqAPI;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private TEndAutomatedAuditingServie tEndAutomatedAuditingServie;

    @Resource
    private TEndCarAuditInfoMapper endCarAuditInfoMapper;

    @Override
    public EndCarInfoDTO findById(Integer id) {
        EndCarInfoDTO tEndCarInfo = tEndCarInfoMapper.selectByCarId(id);
        List<Map<String, Object>> carCorrelationDriver = tEndCarInfoMapper.getCarCorrelationDriver(id);
        tEndCarInfo.setDriverInfo(carCorrelationDriver);
        TEndCarAuditInfo tEndCarAuditInfo = endCarAuditInfoMapper.selectByEndCarId(id);
        if (null != tEndCarAuditInfo) {
            tEndCarInfo.setDrivingLicencesStatus(tEndCarAuditInfo.getDrivingLicencesStatus());
            tEndCarInfo.setDrivingLicencesOpinion(tEndCarAuditInfo.getDrivingLicencesOpinion());
            tEndCarInfo.setRoadTransportOperationStatus(tEndCarAuditInfo.getRoadTransportOperationStatus());
            tEndCarInfo.setRoadTransportOperationOpinion(tEndCarAuditInfo.getRoadTransportOperationOpinion());
        }
        return tEndCarInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int add(TEndCarInfo resources) {
        return tEndCarInfoMapper.insertSelective(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TEndCarInfo resources) {
        TEndCarInfo tEndCarInfo = tEndCarInfoMapper.selectByPrimaryKey(resources.getId());
        TEndCarInfoExample ex = new TEndCarInfoExample();
        TEndCarInfoExample.Criteria c = ex.createCriteria();
        c.andIdEqualTo(resources.getId());
        if (!tEndCarInfo.getAuditStatus().equals(resources.getAuditStatus())) {
            resources.setAuditTime(new Date());
        }
        resources.setUpdateTime(new Date());
        resources.setUpdateUser(CurrentUser.getUserNickname());
        tEndCarInfoMapper.updateByExample(resources, ex);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEndCarInfo(TEndCarInfoVO vo) {
        TEndCarInfo endCarInfo = new TEndCarInfo();
        BeanUtils.copyProperties(vo, endCarInfo);

        TEndCarInfo tEndCarInfo = tEndCarInfoMapper.selectByPrimaryKey(vo.getId());
        TEndCarInfoExample ex = new TEndCarInfoExample();
        TEndCarInfoExample.Criteria c = ex.createCriteria();
        c.andIdEqualTo(vo.getId());
        if (!tEndCarInfo.getAuditStatus().equals(vo.getAuditStatus())) {
            endCarInfo.setAuditTime(new Date());
        }
        endCarInfo.setUpdateTime(new Date());
        endCarInfo.setUpdateUser(CurrentUser.getUserNickname());
        tEndCarInfoMapper.updateByExample(vo, ex);

        //
        if (null != vo.getUpdateOrAudit() && vo.getUpdateOrAudit() == 1) {
            TEndCarAuditInfo tEndCarAuditInfo = endCarAuditInfoMapper.selectByEndCarId(vo.getId());
            if (null == tEndCarAuditInfo) {
                tEndCarAuditInfo = new TEndCarAuditInfo();
                tEndCarAuditInfo.setEndCarId(vo.getId());
                tEndCarAuditInfo.setEnable(false);
                endCarAuditInfoMapper.insertSelective(tEndCarAuditInfo);
            }
            tEndCarAuditInfo.setRoadTransportOperationStatus(vo.getRoadTransportOperationStatus());
            tEndCarAuditInfo.setRoadTransportOperationOpinion(vo.getRoadTransportOperationOpinion());
            tEndCarAuditInfo.setDrivingLicencesStatus(vo.getDrivingLicencesStatus());
            tEndCarAuditInfo.setDrivingLicencesOpinion(vo.getDrivingLicencesOpinion());
            tEndCarAuditInfo.setUpdateTime(new Date());
            tEndCarAuditInfo.setUpdateUser(CurrentUser.getUserNickname());
            endCarAuditInfoMapper.updateByPrimaryKey(tEndCarAuditInfo);

            TEndCarInfo endCarForUpdate = new TEndCarInfo();
            String auditStatus = AuditStatusUtil.status(vo.getRoadTransportOperationStatus(), vo.getDrivingLicencesStatus());
            if (StringUtils.isNotBlank(auditStatus)) {
                endCarForUpdate.setAuditStatus(auditStatus);
            }
            String auditOpinion = AuditStatusUtil.auditOpinion(vo.getRoadTransportOperationOpinion(), vo.getDrivingLicencesOpinion());
            if (StringUtils.isNotBlank(auditOpinion)) {
                endCarForUpdate.setAuditOpinion(auditOpinion);
            }
            if (StringUtils.isNotBlank(auditStatus) || StringUtils.isNotBlank(auditOpinion)) {
                endCarForUpdate.setId(vo.getId());
                endCarForUpdate.setUpdateUser(CurrentUser.getUserNickname());
                tEndCarInfoMapper.updateAuditStatusById(endCarForUpdate);
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Integer id) {
        tEndCarInfoMapper.deleteByPrimaryKey(id);
    }

    @Override
    public List<TEndCarInfoVO> selectByPage(TEndCarInfoSearchVO params) {
        List<TEndCarInfoVO> tEndCarInfoVOS = tEndCarInfoMapper.selectByPage(params);
        tEndCarInfoVOS.forEach((car) -> {
            if (null != car.getCardrivingLicencesValidUntil()) {
                car.setDrivingLicencesValidUntil(DateUtils.formatDate(car.getCardrivingLicencesValidUntil(), "yyyy-MM"));
            }
            if (null != car.getRoadTransportOperationLicenseIssueDate()) {
                Date roadTransportOperationLicenseIssueDate = car.getRoadTransportOperationLicenseIssueDate();
                roadTransportOperationLicenseIssueDate = DateUtils.addYears(roadTransportOperationLicenseIssueDate, 6);
                car.setRoadTransportOperationValidUntilDate(DateUtils.formatDate(roadTransportOperationLicenseIssueDate, "yyyy-MM-dd"));
            }
            if (null != car.getDrivingLicencesValidUntil()) {
                car.setDrivingLicencesValidStatus(DateUtils.checkValidMonth(car.getCardrivingLicencesValidUntil()));
            }
        });
        return tEndCarInfoVOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByID(Integer[] id) {
        //校验车辆是否已经发过单
        for(Integer endCarId : id)
        {
            List<TOrderInfo> tOrderInfoList = tOrderInfoAPI.selectByEndCarIdFeign(endCarId);
            if(null != tOrderInfoList && tOrderInfoList.size() > 0)
            {
                return -1;
            }
            //逻辑删除车辆关系
            TEndCarInfo tEndCarInfo = tEndCarInfoMapper.selectByPrimaryKey(endCarId);
            List<TEndUserCarRel> tEndUserCarRelList = tEndUserCarRelMapper.selecByEndCarId(tEndCarInfo.getId());
            for(TEndUserCarRel rel:tEndUserCarRelList){
                rel.setEnable(true);
                tEndUserCarRelMapper.updateByPrimaryKey(rel);
            }
        }
        tEndCarInfoMapper.deleteByID(id);

        return tEndCarInfoMapper.deleteByID(id);
    }

    @Override
    public int approval(ApprovalVO param) {
        return tEndCarInfoMapper.approval(param);
    }

    //绑定车辆
    @LcnTransaction
    @Transactional
    @Override
    public int BindingCar(TEndCarInfoVO endCarInfo) {
        Integer account1=null;
        if(endCarInfo.getAccountId()!=null){
            account1 =endCarInfo.getAccountId();
        }else{
            account1 = CurrentUser.getUserAccountId();
        }
        TAccount account = tAccountMapper.selectByPrimaryKey(account1);
        Integer enduserinfoid = null;
        if(endCarInfo.getEnduserinfoId()!=null){
            enduserinfoid=endCarInfo.getEnduserinfoId();
        }else{
            enduserinfoid=CurrentUser.getEndUserId();
        }

        TEndUserInfo tEndUserInfo = tEndUserInfoMapper.selectByPrimaryKey(enduserinfoid);
        String role = tEndUserInfo.getUserLogisticsRole();
        List<TEndCarInfo> list = tEndCarInfoMapper.selectByCarNum(endCarInfo.getVehicleNumber());
        TEndCarInfo tEndCarInfo = new TEndCarInfo();
        int i = 0;
        if (list.size() == 0) {
            if (StringUtils.isNotBlank(tEndUserInfo.getUserLogisticsRole()) && tEndUserInfo.getUserLogisticsRole().equals("CTYPEBOSS")) {
                tEndCarInfo.setOwner(tEndUserInfo.getRealName());
            }else if(tEndUserInfo.getUserLogisticsRole().equals("CTYPEDRVIVER,CTYPEBOSS")||tEndUserInfo.getUserLogisticsRole().equals("CTYPEBOSS,CTYPEDRVIVER")){
                tEndCarInfo.setOwner(tEndUserInfo.getRealName());
            }
            tEndCarInfo.setVehicleNumber(endCarInfo.getVehicleNumber());
            tEndCarInfo.setLicensePlateColor("2");
            tEndCarInfo.setVehicleIdentificationCode(endCarInfo.getVehicleIdentificationCode());
            tEndCarInfo.setLicenseplateTypeCode(endCarInfo.getLicenseplateTypeCode());
            tEndCarInfo.setVehicleClassificationCode(endCarInfo.getVehicleClassificationCode());
            tEndCarInfo.setRoadTransportCertificateNumber(endCarInfo.getRoadTransportCertificateNumber());
            tEndCarInfo.setRoadTransportOperationLicensePhoto1(endCarInfo.getRoadTransportOperationLicensePhoto1());
            tEndCarInfo.setRoadTransportOperationLicensePhoto2(endCarInfo.getRoadTransportOperationLicensePhoto2());
            tEndCarInfo.setDrivingLicencesPhoto1(endCarInfo.getDrivingLicencesPhoto1());
            tEndCarInfo.setDrivingLicencesPhoto2(endCarInfo.getDrivingLicencesPhoto2());
            tEndCarInfo.setVerass(endCarInfo.getVerass());
            tEndCarInfo.setAuditStatus("MIDNODE");
            tEndCarInfo.setRoadTransportCertificateNumber(endCarInfo.getRoadTransportCertificateNumber());//道路运输证号
            tEndCarInfo.setRoadTransportOperationLicenseCode(endCarInfo.getRoadTransportOperationLicenseCode());
            tEndCarInfo.setRoadTransportOperationLicenseIssueUnit(endCarInfo.getRoadTransportOperationLicenseIssueUnit());
            tEndCarInfo.setRoadTransportOperationType(endCarInfo.getRoadTransportOperationType());
            tEndCarInfo.setRoadTransportOperationScope(endCarInfo.getRoadTransportOperationScope());
            tEndCarInfo.setRoadTransportOperationVehicleWeight(endCarInfo.getRoadTransportOperationVehicleWeight());
            tEndCarInfo.setRoadTransportOperationOwnerAddress(endCarInfo.getRoadTransportOperationOwnerAddress());
            tEndCarInfo.setRoadTransportOperationLicenseIssueDate(endCarInfo.getRoadTransportOperationLicenseIssueDate());//道路运输证发证日期
            tEndCarInfo.setApprovedMotorFullQuality(endCarInfo.getApprovedMotorFullQuality());//准牵引总质量
            tEndCarInfo.setVehicleNumber(endCarInfo.getVehicleNumber());//车牌号
            tEndCarInfo.setUseProperty(endCarInfo.getUseProperty());//使用性质
            tEndCarInfo.setOwner(endCarInfo.getOwner());//车辆所有人
            tEndCarInfo.setVehicleIdentificationCode(endCarInfo.getVehicleIdentificationCode());////车辆识别代码
            tEndCarInfo.setDrivingLicencesIssueUnit(endCarInfo.getDrivingLicencesIssueUnit());//行驶证发证机关
            tEndCarInfo.setRegisterDate(endCarInfo.getRegisterDate());//注册日期
            tEndCarInfo.setIssueDate(endCarInfo.getIssueDate());//发证日期
            tEndCarInfo.setCardrivingLicencesValidUntil(endCarInfo.getCardrivingLicencesValidUntil());//行驶证有效期至
            tEndCarInfo.setUseProperty(endCarInfo.getUseProperty());
            tEndCarInfo.setBrandModel(endCarInfo.getBrandModel());
            tEndCarInfo.setEngineCode(endCarInfo.getEngineCode());
            tEndCarInfo.setRegisterDate(endCarInfo.getRegisterDate());
            tEndCarInfo.setFileCode(endCarInfo.getFileCode());
            tEndCarInfo.setApprovedBarePeople(endCarInfo.getApprovedBarePeople());
            tEndCarInfo.setFullVehicleQuality(endCarInfo.getFullVehicleQuality());
            tEndCarInfo.setApprovedBareQuality(endCarInfo.getApprovedBareQuality());
            tEndCarInfo.setRemark(endCarInfo.getRemark());
            tEndCarInfo.setDrivingLicencesAddress(endCarInfo.getDrivingLicencesAddress());
            tEndCarInfo.setDrivingLicencesTotalMass(endCarInfo.getDrivingLicencesTotalMass());
            tEndCarInfo.setDrivingLicencesInspectionRecord(endCarInfo.getDrivingLicencesInspectionRecord());
            tEndCarInfo.setDrivingLicencesBarCode(endCarInfo.getDrivingLicencesBarCode());
            tEndCarInfo.setOutlineSize(endCarInfo.getOutlineSize());
            tEndCarInfo.setVehicleFuelType(endCarInfo.getVehicleFuelType());//车辆燃料类型
            tEndCarInfo.setVehicleTonnage(endCarInfo.getVehicleTonnage());//车辆载质量
            tEndCarInfo.setFullVehicleQuality(endCarInfo.getFullVehicleQuality());//整车质量
            String CarAuditOpinion = "";
            if(null == tEndCarInfo.getRoadTransportOperationLicensePhoto1() || null == tEndCarInfo.getRoadTransportOperationLicensePhoto2() ||
                    "".equals(tEndCarInfo.getRoadTransportOperationLicensePhoto1()) || "".equals(tEndCarInfo.getRoadTransportOperationLicensePhoto2())){
                tEndCarInfo.setAuditStatus(DictEnum.PAPERNEEDUPDATE.code);
                CarAuditOpinion = CarAuditOpinion + "道路运输许可证、";
            }
            if(null == tEndCarInfo.getDrivingLicencesPhoto1() || null == tEndCarInfo.getDrivingLicencesPhoto2() ||
                    "".equals(tEndCarInfo.getDrivingLicencesPhoto1()) || "".equals(tEndCarInfo.getDrivingLicencesPhoto2())){
                tEndCarInfo.setAuditStatus(DictEnum.PAPERNEEDUPDATE.code);
                CarAuditOpinion = CarAuditOpinion + "行驶证、";
            }
            if(CarAuditOpinion.length() > 1){
                tEndCarInfo.setAuditOpinion(CarAuditOpinion+"证件不齐全，请补齐后重新提交。");
            }else {
                tEndCarInfo.setAuditOpinion("");
            }
            i = tEndCarInfoMapper.insertSelective(tEndCarInfo);
            String userLogisticsRoles = CurrentUser.getUserLogisticsRole();


            /*if (StringUtils.isNotBlank(userLogisticsRoles) && StringUtils.isBlank(tEndCarInfo.getAuditOpinion())) {
                // 创建自动化审核消息
                try {
                    MQMessage mqMessage = new MQMessage();
                    mqMessage.setTopic(MqMessageTopic.CARAUDIT);
                    mqMessage.setTag(MqMessageTag.CAR_AUDIT);
                    mqMessage.setKey(String.valueOf(tEndCarInfo.getId()));
                    TEndCarInfo tEndCarInfo1 = new TEndCarInfo();
                    tEndCarInfo1.setId(tEndCarInfo.getId());
                    tEndCarInfo1.setParam1(String.valueOf(enduserinfoid));
                    tEndCarInfo1.setParam2(IdWorkerUtil.getInstance().nextId());
                    mqMessage.setBody(tEndCarInfo1);
                    // redis 添加审核记录
                    redisUtil.set("REDISCARAUDIT" + tEndCarInfo1.getParam2(), "0", 60 * 60 * 24);
                    ResultUtil resultUtil = mqAPI.sendMessage(mqMessage);
                    if (CodeEnum.SUCCESS.getCode().equals(resultUtil.getCode())) {
                        log.error("创建自动化审核消息成功");
                    } else {
                        log.info("创建自动化审核消息失败");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("创建自动化审核消息失败", e);
                }
            }*/
        }
        TEndUserCarRel tEndUserCarRel = new TEndUserCarRel();
        tEndUserCarRel.setEnduserId(enduserinfoid);
        if (list.size() > 0) {
            tEndUserCarRel.setEndcarId(list.get(0).getId());
        } else if (list.size() == 0) {
            tEndUserCarRel.setEndcarId(tEndCarInfo.getId());
        }
        if (StringUtils.isNotBlank(tEndUserInfo.getUserLogisticsRole()) && tEndUserInfo.getUserLogisticsRole().equals("CTYPEDRVIVER")) {//司机
            tEndUserCarRel.setUserCarRelationType("CLSYRSJ");
        } else if (StringUtils.isNotBlank(tEndUserInfo.getUserLogisticsRole()) && tEndUserInfo.getUserLogisticsRole().equals("CTYPEBOSS")) {//车老板
            tEndUserCarRel.setUserCarRelationType("CLSYRCLB");
            tEndUserCarRel.setCertificateType(endCarInfo.getCertificateType());
            tEndUserCarRel.setCertificateDoc1(endCarInfo.getCertificateDoc1());
            tEndUserCarRel.setCertificateDoc2(endCarInfo.getCertificateDoc2());
        }else if (StringUtils.isNotBlank(tEndUserInfo.getUserLogisticsRole()) && (tEndUserInfo.getUserLogisticsRole().equals("CTYPEDRVIVER,CTYPEBOSS")||tEndUserInfo.getUserLogisticsRole().equals("CTYPEBOSS,CTYPEDRVIVER"))) {//车老板
            tEndUserCarRel.setUserCarRelationType("CLSYRCLB");
        }else{
            String logisticsRole = CurrentUser.getUserLogisticsRole();
            if (logisticsRole.equals("CTYPEDRVIVER")) {//司机
                tEndUserCarRel.setUserCarRelationType("CLSYRSJ");
            } else if (logisticsRole.equals("CTYPEBOSS")) {//车老板
                tEndUserCarRel.setUserCarRelationType("CLSYRCLB");
                tEndUserCarRel.setCertificateType(endCarInfo.getCertificateType());
                tEndUserCarRel.setCertificateDoc1(endCarInfo.getCertificateDoc1());
                tEndUserCarRel.setCertificateDoc2(endCarInfo.getCertificateDoc2());
                tEndUserCarRel.setAuditStatus("NEWNODE");
            }
        }
        tEndUserCarRel.setDatafrom(endCarInfo.getDatafrom());
        tEndUserCarRel.setDataConfirmTime(new Date());
        if (StringUtils.isNotBlank(tEndUserInfo.getUserLogisticsRole()) && tEndUserInfo.getUserLogisticsRole().equals("CTYPEBOSS")) {
            tEndUserCarRel.setAuditStatus("NEWNODE");
        } else {
            tEndUserCarRel.setAuditStatus("PASSNODE");
        }
        tEndUserCarRel.setEnable(false);
        if(tEndUserInfo.getUserLogisticsRole().split(",").length>1){
            TEndUserCarRel tEndUserCarRel2 = new TEndUserCarRel();
            BeanUtils.copyProperties(tEndUserCarRel,tEndUserCarRel2);
            tEndUserCarRel.setUserCarRelationType("CLSYRSJ");
            List<TEndUserCarRel> tEndUserCarRelList = tEndUserCarRelMapper.selecByCarIdAnrType(tEndUserCarRel.getEndcarId(),tEndUserCarRel.getEnduserId(),tEndUserCarRel.getUserCarRelationType());
            if(tEndUserCarRelList.size()<1){
                tEndUserCarRel.setAuditStatus("NEWNODE");
                tEndUserCarRelMapper.insertSelective(tEndUserCarRel);
            }
            tEndUserCarRel2.setCertificateType(endCarInfo.getCertificateType());
            tEndUserCarRel2.setCertificateDoc1(endCarInfo.getCertificateDoc1());
            tEndUserCarRel2.setCertificateDoc2(endCarInfo.getCertificateDoc2());
            tEndUserCarRel2.setUserCarRelationType("CLSYRCLB");
            tEndUserCarRel2.setAuditStatus("NEWNODE");
            List<TEndUserCarRel> tEndUserCarRelList2 = tEndUserCarRelMapper.selecByCarIdAnrType(tEndUserCarRel2.getEndcarId(),tEndUserCarRel2.getEnduserId(),tEndUserCarRel2.getUserCarRelationType());
            if(tEndUserCarRelList2.size()<1){
                tEndUserCarRelMapper.insertSelective(tEndUserCarRel2);
            }
        }else{
            List<TEndUserCarRel> tEndUserCarRelList = tEndUserCarRelMapper.selecByCarIdAnrType(tEndUserCarRel.getEndcarId(),tEndUserCarRel.getEnduserId(),tEndUserCarRel.getUserCarRelationType());
            if(tEndUserCarRelList.size()<1){
                tEndUserCarRelMapper.insertSelective(tEndUserCarRel);
            }
        }
        TEndCarStatus tEndCarStatus = new TEndCarStatus();
        tEndCarStatus.setEndcarId(tEndCarInfo.getId());
        tEndCarStatus.setCarStatus("AVAILABLEREGISTERED");
        tEndCarStatus.setCurrentDriver(account.getAccountNo());
        tEndCarStatus.setCurrentDriverPhone(account.getAccountNo());
        tEndCarStatus.setCurrentDriverAccountNo(account.getAccountNo());
        tEndCarStatus.setEnable(false);
        tEndCarStatusMapper.insertSelective(tEndCarStatus);
        return i;
    }

    //绑定车辆修改资料
    @LcnTransaction
    @Transactional
    @Override
    public int BindingCarUpdate(TEndCarInfoVO endCarInfo) {
        if(null != endCarInfo && !"1".equals(endCarInfo.getSourceType())){
            endCarInfo.setVehicleIdentificationCode(null);
        }else if(null != endCarInfo && "1".equals(endCarInfo.getSourceType()) && "".equals(endCarInfo.getVehicleIdentificationCode())){
            endCarInfo.setVehicleIdentificationCode(null);
        }

        Integer enduserinfoid = null;
        if(endCarInfo.getEnduserinfoId()!=null){
            enduserinfoid=endCarInfo.getEnduserinfoId();
        }else{
            enduserinfoid=CurrentUser.getEndUserId();
        }
        TEndUserInfo tEndUserInfo = tEndUserInfoMapper.selectByPrimaryKey(enduserinfoid);
        List<TEndCarInfo> list = tEndCarInfoMapper.selectByCarNum(endCarInfo.getVehicleNumber());
        TEndCarInfo tEndCarInfo = new TEndCarInfo();
        int i = 0;
        if (list.size()>0) {
            tEndCarInfo = list.get(0);
            String carAuditStatus = "";
            if(null != tEndCarInfo.getAuditStatus()){
                carAuditStatus = tEndCarInfo.getAuditStatus();
            }
            tEndCarInfo.setRoadTransportCertificateNumber(endCarInfo.getRoadTransportCertificateNumber());
            tEndCarInfo.setRoadTransportOperationLicensePhoto1(endCarInfo.getRoadTransportOperationLicensePhoto1());
            tEndCarInfo.setRoadTransportOperationLicensePhoto2(endCarInfo.getRoadTransportOperationLicensePhoto2());
            tEndCarInfo.setDrivingLicencesPhoto1(endCarInfo.getDrivingLicencesPhoto1());
            tEndCarInfo.setDrivingLicencesPhoto2(endCarInfo.getDrivingLicencesPhoto2());
            tEndCarInfo.setRoadTransportCertificateNumber(endCarInfo.getRoadTransportCertificateNumber());//道路运输证号
            tEndCarInfo.setRoadTransportOperationLicenseCode(endCarInfo.getRoadTransportOperationLicenseCode());
            tEndCarInfo.setRoadTransportOperationLicenseIssueUnit(endCarInfo.getRoadTransportOperationLicenseIssueUnit());
            tEndCarInfo.setRoadTransportOperationType(endCarInfo.getRoadTransportOperationType());
            tEndCarInfo.setRoadTransportOperationScope(endCarInfo.getRoadTransportOperationScope());
            tEndCarInfo.setRoadTransportOperationVehicleWeight(endCarInfo.getRoadTransportOperationVehicleWeight());
            tEndCarInfo.setRoadTransportOperationOwnerAddress(endCarInfo.getRoadTransportOperationOwnerAddress());
            tEndCarInfo.setRoadTransportOperationLicenseIssueDate(endCarInfo.getRoadTransportOperationLicenseIssueDate());//道路运输证发证日期
            tEndCarInfo.setApprovedMotorFullQuality(endCarInfo.getApprovedMotorFullQuality());//准牵引总质量
            tEndCarInfo.setVehicleNumber(endCarInfo.getVehicleNumber());//车牌号
            tEndCarInfo.setLicensePlateColor("2");//车牌颜色
            tEndCarInfo.setVehicleClassificationCode(endCarInfo.getVehicleClassificationCode());
            tEndCarInfo.setUseProperty(endCarInfo.getUseProperty());//使用性质
            tEndCarInfo.setOwner(endCarInfo.getOwner());//车辆所有人
            tEndCarInfo.setVehicleIdentificationCode(endCarInfo.getVehicleIdentificationCode());//车辆识别代号
            tEndCarInfo.setDrivingLicencesIssueUnit(endCarInfo.getDrivingLicencesIssueUnit());//行驶证发证机关
            tEndCarInfo.setRegisterDate(endCarInfo.getRegisterDate());//注册日期
            tEndCarInfo.setIssueDate(endCarInfo.getIssueDate());//发证日期
            tEndCarInfo.setCardrivingLicencesValidUntil(endCarInfo.getCardrivingLicencesValidUntil());////行驶证有效期至
            tEndCarInfo.setUseProperty(endCarInfo.getUseProperty());
            tEndCarInfo.setBrandModel(endCarInfo.getBrandModel());
            tEndCarInfo.setEngineCode(endCarInfo.getEngineCode());
            tEndCarInfo.setRegisterDate(endCarInfo.getRegisterDate());
            tEndCarInfo.setFileCode(endCarInfo.getFileCode());
            tEndCarInfo.setApprovedBarePeople(endCarInfo.getApprovedBarePeople());
            tEndCarInfo.setFullVehicleQuality(endCarInfo.getFullVehicleQuality());
            tEndCarInfo.setApprovedBareQuality(endCarInfo.getApprovedBareQuality());
            tEndCarInfo.setRemark(endCarInfo.getRemark());
            tEndCarInfo.setDrivingLicencesAddress(endCarInfo.getDrivingLicencesAddress());
            tEndCarInfo.setDrivingLicencesTotalMass(endCarInfo.getDrivingLicencesTotalMass());
            tEndCarInfo.setDrivingLicencesInspectionRecord(endCarInfo.getDrivingLicencesInspectionRecord());
            tEndCarInfo.setDrivingLicencesBarCode(endCarInfo.getDrivingLicencesBarCode());
            tEndCarInfo.setOutlineSize(endCarInfo.getOutlineSize());
            tEndCarInfo.setVehicleFuelType(endCarInfo.getVehicleFuelType());//车辆燃料类型
            tEndCarInfo.setVehicleTonnage(endCarInfo.getVehicleTonnage());//车辆载质量
            tEndCarInfo.setFullVehicleQuality(endCarInfo.getFullVehicleQuality());//整车质量
            if(null != tEndCarInfo && !carAuditStatus.isEmpty() && (!carAuditStatus.equals("PAPERNEEDUPDATE") && !carAuditStatus.equals("PASSNODE"))){
                String CarAuditOpinion = "";
                if(null == tEndCarInfo.getRoadTransportOperationLicensePhoto1() || null == tEndCarInfo.getRoadTransportOperationLicensePhoto2() ||
                        "".equals(tEndCarInfo.getRoadTransportOperationLicensePhoto1()) || "".equals(tEndCarInfo.getRoadTransportOperationLicensePhoto2())){
                    tEndCarInfo.setAuditStatus("NOTPASSNODE");
                    CarAuditOpinion = CarAuditOpinion + "道路运输许可证、";
                }
                if(null == tEndCarInfo.getDrivingLicencesPhoto1() || null == tEndCarInfo.getDrivingLicencesPhoto2() ||
                        "".equals(tEndCarInfo.getDrivingLicencesPhoto1()) || "".equals(tEndCarInfo.getDrivingLicencesPhoto2())){
                    tEndCarInfo.setAuditStatus("NOTPASSNODE");
                    CarAuditOpinion = CarAuditOpinion + "行驶证、";
                }
                if(CarAuditOpinion.length() > 1){
                    tEndCarInfo.setAuditOpinion(CarAuditOpinion+"证件不齐全，请补齐后重新提交。");
                }/*else {
                    tEndCarInfo.setAuditOpinion("");
                    // 创建自动化审核消息
                    try {
                        MQMessage mqMessage = new MQMessage();
                        mqMessage.setTopic(MqMessageTopic.CARAUDIT);
                        mqMessage.setTag(MqMessageTag.CAR_AUDIT);
                        mqMessage.setKey(String.valueOf(tEndCarInfo.getId()));
                        TEndCarInfo tEndCarInfo1 = new TEndCarInfo();
                        tEndCarInfo1.setId(tEndCarInfo.getId());
                        mqMessage.setBody(tEndCarInfo1);
                        ResultUtil resultUtil = mqAPI.sendMessage(mqMessage);
                        if (CodeEnum.SUCCESS.getCode().equals(resultUtil.getCode())) {
                            log.error("创建自动化审核消息成功");
                        } else {
                            log.info("创建自动化审核消息失败");
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("创建自动化审核消息失败", e);
                    }
                }*/
            }
            i = tEndCarInfoMapper.updateByPrimaryKeySelective(tEndCarInfo);
        }
        TEndUserCarRel record = new TEndUserCarRel();
        if (StringUtils.isNotBlank(tEndUserInfo.getUserLogisticsRole()) && tEndUserInfo.getUserLogisticsRole().equals("CTYPEDRVIVER")) {//司机
            record.setUserCarRelationType("CLSYRSJ");
        } else if (StringUtils.isNotBlank(tEndUserInfo.getUserLogisticsRole()) && tEndUserInfo.getUserLogisticsRole().equals("CTYPEBOSS")) {//车老板
            record.setUserCarRelationType("CLSYRCLB");
        }else if (StringUtils.isNotBlank(tEndUserInfo.getUserLogisticsRole()) && (tEndUserInfo.getUserLogisticsRole().equals("CTYPEDRVIVER,CTYPEBOSS")||tEndUserInfo.getUserLogisticsRole().equals("CTYPEBOSS,CTYPEDRVIVER"))) {//车老板
            record.setUserCarRelationType("CLSYRCLB");
        }else{
            String logisticsRole = CurrentUser.getUserLogisticsRole();
            if (logisticsRole.equals("CTYPEDRVIVER")) {//司机
                record.setUserCarRelationType("CLSYRSJ");
            } else if (logisticsRole.equals("CTYPEBOSS")) {//车老板
                record.setUserCarRelationType("CLSYRCLB");
                record.setAuditStatus("NEWNODE");
            }
        }
        record.setEnduserId(enduserinfoid);
        List<TEndUserCarRelVo> tEndUserCarRelList = tEndUserCarRelMapper.selectByEnduserId(record);
        TEndUserCarRel tEndUserCarRel = new TEndUserCarRel();
        tEndUserCarRel.setEnduserId(enduserinfoid);
        for(TEndUserCarRelVo vo : tEndUserCarRelList){
            vo.setCertificateType(endCarInfo.getCertificateType());
            vo.setCertificateDoc1(endCarInfo.getCertificateDoc1());
            vo.setCertificateDoc2(endCarInfo.getCertificateDoc2());
            tEndUserCarRelMapper.updateByPrimaryKeySelective(vo);
        }
        return i;
    }
    //更新资料
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int perfectData(TEndCarInfoVO endCarInfo) {
        if(null != endCarInfo && !"1".equals(endCarInfo.getSourceType())){
            endCarInfo.setVehicleIdentificationCode(null);
        }else if(null != endCarInfo && "1".equals(endCarInfo.getSourceType()) && "".equals(endCarInfo.getVehicleIdentificationCode())){
            endCarInfo.setVehicleIdentificationCode(null);
        }

        Integer enduserinfoid = CurrentUser.getEndUserId();
        TEndUserInfo tEndUserInfo = tEndUserInfoMapper.selectByPrimaryKey(enduserinfoid);
        TEndCarInfo tEndCarInfo = tEndCarInfoMapper.selectByPrimaryKey(endCarInfo.getId());
        String carAuditStatus = "";
        if(null != tEndCarInfo.getAuditStatus()){
            carAuditStatus = tEndCarInfo.getAuditStatus();
        }
        int i = 0;
        tEndCarInfo.setOwner(tEndUserInfo.getRealName());
        tEndCarInfo.setVehicleNumber(endCarInfo.getVehicleNumber());
        tEndCarInfo.setRoadTransportCertificateNumber(endCarInfo.getRoadTransportCertificateNumber());
        tEndCarInfo.setRoadTransportOperationLicensePhoto1(endCarInfo.getRoadTransportOperationLicensePhoto1());
        tEndCarInfo.setRoadTransportOperationLicensePhoto2(endCarInfo.getRoadTransportOperationLicensePhoto2());
        tEndCarInfo.setDrivingLicencesPhoto1(endCarInfo.getDrivingLicencesPhoto1());
        tEndCarInfo.setDrivingLicencesPhoto2(endCarInfo.getDrivingLicencesPhoto2());
        tEndCarInfo.setAuditStatus("MIDNODE");
        tEndCarInfo.setUpdateTime(new Date());
        tEndCarInfo.setUpdateUser(tEndUserInfo.getRealName());
        tEndCarInfo.setRoadTransportCertificateNumber(endCarInfo.getRoadTransportCertificateNumber());//道路运输证号
        tEndCarInfo.setRoadTransportOperationLicenseCode(endCarInfo.getRoadTransportOperationLicenseCode());
        tEndCarInfo.setRoadTransportOperationLicenseIssueUnit(endCarInfo.getRoadTransportOperationLicenseIssueUnit());
        tEndCarInfo.setRoadTransportOperationType(endCarInfo.getRoadTransportOperationType());
        tEndCarInfo.setRoadTransportOperationScope(endCarInfo.getRoadTransportOperationScope());
        tEndCarInfo.setRoadTransportOperationVehicleWeight(endCarInfo.getRoadTransportOperationVehicleWeight());
        tEndCarInfo.setRoadTransportOperationOwnerAddress(endCarInfo.getRoadTransportOperationOwnerAddress());
        tEndCarInfo.setRoadTransportOperationLicenseIssueDate(endCarInfo.getRoadTransportOperationLicenseIssueDate());//道路运输证发证日期
        tEndCarInfo.setApprovedMotorFullQuality(endCarInfo.getApprovedMotorFullQuality());//准牵引总质量
        tEndCarInfo.setVehicleNumber(endCarInfo.getVehicleNumber());//车牌号
        tEndCarInfo.setUseProperty(endCarInfo.getUseProperty());//使用性质
        tEndCarInfo.setOwner(endCarInfo.getOwner());//车辆所有人
        tEndCarInfo.setVehicleIdentificationCode(endCarInfo.getVehicleIdentificationCode());////车辆识别代码
        tEndCarInfo.setDrivingLicencesIssueUnit(endCarInfo.getDrivingLicencesIssueUnit());//行驶证发证机关
        tEndCarInfo.setRegisterDate(endCarInfo.getRegisterDate());//注册日期
        tEndCarInfo.setIssueDate(endCarInfo.getIssueDate());//发证日期
        tEndCarInfo.setCardrivingLicencesValidUntil(endCarInfo.getCardrivingLicencesValidUntil());//行驶证有效期至
        tEndCarInfo.setVehicleClassificationCode(endCarInfo.getVehicleClassificationCode());
        tEndCarInfo.setUseProperty(endCarInfo.getUseProperty());
        tEndCarInfo.setBrandModel(endCarInfo.getBrandModel());
        tEndCarInfo.setEngineCode(endCarInfo.getEngineCode());
        tEndCarInfo.setRegisterDate(endCarInfo.getRegisterDate());
        tEndCarInfo.setFileCode(endCarInfo.getFileCode());
        tEndCarInfo.setApprovedBarePeople(endCarInfo.getApprovedBarePeople());
        tEndCarInfo.setFullVehicleQuality(endCarInfo.getFullVehicleQuality());
        tEndCarInfo.setApprovedBareQuality(endCarInfo.getApprovedBareQuality());
        tEndCarInfo.setRemark(endCarInfo.getRemark());
        tEndCarInfo.setDrivingLicencesAddress(endCarInfo.getDrivingLicencesAddress());
        tEndCarInfo.setDrivingLicencesTotalMass(endCarInfo.getDrivingLicencesTotalMass());
        tEndCarInfo.setDrivingLicencesInspectionRecord(endCarInfo.getDrivingLicencesInspectionRecord());
        tEndCarInfo.setDrivingLicencesBarCode(endCarInfo.getDrivingLicencesBarCode());
        tEndCarInfo.setOutlineSize(endCarInfo.getOutlineSize());
        tEndCarInfo.setVehicleFuelType(endCarInfo.getVehicleFuelType());//车辆燃料类型
        tEndCarInfo.setVehicleTonnage(endCarInfo.getVehicleTonnage());//车辆载质量
        tEndCarInfo.setFullVehicleQuality(endCarInfo.getFullVehicleQuality());//整车质量
        String CarAuditOpinion = "";
        if (null == tEndCarInfo.getRoadTransportOperationLicensePhoto1() || null == tEndCarInfo.getRoadTransportOperationLicensePhoto2() ||
                "".equals(tEndCarInfo.getRoadTransportOperationLicensePhoto1()) || "".equals(tEndCarInfo.getRoadTransportOperationLicensePhoto2())) {
            tEndCarInfo.setAuditStatus(DictEnum.PAPERNEEDUPDATE.code);
            CarAuditOpinion = CarAuditOpinion + "道路运输许可证、";
        }
        if (null == tEndCarInfo.getDrivingLicencesPhoto1() || null == tEndCarInfo.getDrivingLicencesPhoto2() ||
                "".equals(tEndCarInfo.getDrivingLicencesPhoto1()) || "".equals(tEndCarInfo.getDrivingLicencesPhoto2())) {
            tEndCarInfo.setAuditStatus(DictEnum.PAPERNEEDUPDATE.code);
            CarAuditOpinion = CarAuditOpinion + "行驶证、";
        }
        if (CarAuditOpinion.length() > 1) {
            tEndCarInfo.setAuditOpinion(CarAuditOpinion + "证件不齐全，请补齐后重新提交。");
        } else {
            tEndCarInfo.setAuditOpinion("");
        }
        // 创建自动化审核消息
        /*try {
            if (null != tEndCarInfo.getDrivingLicencesPhoto1() && null != tEndCarInfo.getDrivingLicencesPhoto2()
                    && StringUtils.isNotBlank(tEndCarInfo.getDrivingLicencesPhoto1()) && StringUtils.isNotBlank(tEndCarInfo.getDrivingLicencesPhoto2())
                    && null != tEndCarInfo.getRoadTransportOperationLicensePhoto1() && null != tEndCarInfo.getRoadTransportOperationLicensePhoto2()
                    && StringUtils.isNotBlank(tEndCarInfo.getRoadTransportOperationLicensePhoto1()) && StringUtils.isNotBlank(tEndCarInfo.getRoadTransportOperationLicensePhoto2())) {
                MQMessage mqMessage = new MQMessage();
                mqMessage.setTopic(MqMessageTopic.CARAUDIT);
                mqMessage.setTag(MqMessageTag.CAR_AUDIT);
                mqMessage.setKey(String.valueOf(tEndCarInfo.getId()));
                TEndCarInfo tEndCarInfo1 = new TEndCarInfo();
                tEndCarInfo1.setId(tEndCarInfo.getId());
                tEndCarInfo1.setParam1(String.valueOf(enduserinfoid));
                tEndCarInfo1.setParam2(IdWorkerUtil.getInstance().nextId());
                // redis 添加审核记录
                redisUtil.set("REDISCARAUDIT" + tEndCarInfo1.getParam2(), "0", 60 * 60 * 24);
                mqMessage.setBody(tEndCarInfo1);
                ResultUtil resultUtil = mqAPI.sendMessage(mqMessage);
                if (CodeEnum.SUCCESS.getCode().equals(resultUtil.getCode())) {
                    log.error("创建自动化审核消息成功");
                } else {
                    log.info("创建自动化审核消息失败");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("创建自动化审核消息失败", e);
        }*/
        i = tEndCarInfoMapper.updateByPrimaryKeySelective(tEndCarInfo);
        List<TEndUserCarRel> t = tEndUserCarRelMapper.selecByCarId(endCarInfo.getId(), enduserinfoid);
        TEndUserCarRel tEndUserCarRel = t.get(0);
        tEndUserCarRel.setEnduserId(enduserinfoid);
        tEndUserCarRel.setEndcarId(endCarInfo.getId());
        tEndUserCarRel.setDataConfirmTime(new Date());
        tEndUserCarRel.setAuditStatus("MIDNODE");
        tEndUserCarRel.setCertificateType(endCarInfo.getCertificateType());
        tEndUserCarRel.setCertificateDoc1(endCarInfo.getCertificateDoc1());
        tEndUserCarRel.setCertificateDoc2(endCarInfo.getCertificateDoc2());
        tEndUserCarRelMapper.updateByPrimaryKeySelective(tEndUserCarRel);
        return i;
    }

    /**
     * 车辆列表
     *
     * @param enduserinfoid enduserinfo 表id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil getCarList(Integer enduserinfoid) {
        TEndUserCarRel tEndUserCarRel = new TEndUserCarRel();
        tEndUserCarRel.setEnduserId(enduserinfoid);
        String logisticsRole = CurrentUser.getUserLogisticsRole();
        String userCarRelationType = "";
        if (logisticsRole.equals("CTYPEDRVIVER")) {//司机
            userCarRelationType = "CLSYRSJ";
            tEndUserCarRel.setUserCarRelationType("CLSYRSJ");
        } else if (logisticsRole.equals("CTYPEBOSS")) {//车老板
            userCarRelationType = "CLSYRCLB";
            tEndUserCarRel.setUserCarRelationType("CLSYRCLB");
            tEndUserCarRel.setAuditStatus("PASSNODE");
        }
        List<TEndUserCarRelVo> caList = tEndUserCarRelMapper.selectCarListByEndUserIdAnrType(tEndUserCarRel);
        for (TEndUserCarRelVo tEndUserCarRelVo : caList) {
            TEndCarAuditInfo endCarAuditInfo = endCarAuditInfoMapper.selectByEndCarId(tEndUserCarRelVo.getEndcarId());
            if(null == endCarAuditInfo){
                tEndUserCarRelVo.setEndCarDateType("old");
            }else {
                tEndUserCarRelVo.setEndCarDateType("new");
            }
            if (null != tEndUserCarRelVo.getAuditStatus()
                    && DictEnum.PASSNODE.code.equals(tEndUserCarRelVo.getAuditStatus())
                    && null != tEndUserCarRelVo.getCardrivingLicencesValidUntil()) {
                Integer checkValidDate = DateUtils.checkValidMonth(tEndUserCarRelVo.getCardrivingLicencesValidUntil());
                if (checkValidDate == 2) {
                    // 提醒
                    tEndUserCarRelVo.setCarAuditStatus("即将过期");
                }
                if (checkValidDate == 0) {
                    TEndCarInfo tEndCarInfo = new TEndCarInfo();
                    tEndCarInfo.setId(tEndUserCarRelVo.getEndcarId());
                    tEndCarInfo.setAuditStatus(DictEnum.PAPERNEEDUPDATE.code);
                    tEndCarInfo.setAuditOpinion("行驶证已过期，请补齐后重新提交。");
                    tEndCarInfo.setAuditTime(new Date());
                    tEndCarInfoMapper.updateByPrimaryKeySelective(tEndCarInfo);
                    tEndUserCarRelVo.setAuditStatus(DictEnum.PAPERNEEDUPDATE.code);
                    if (tEndUserCarRelVo.getEndCarDateType().equals("new")) {
                        endCarAuditInfo.setDrivingLicencesStatus(DictEnum.PAPERNEEDUPDATE.code);
                        endCarAuditInfo.setDrivingLicencesOpinion("行驶证已过期");
                        endCarAuditInfo.setUpdateUser(CurrentUser.getUserNickname());
                        endCarAuditInfo.setUpdateTime(new Date());
                        endCarAuditInfoMapper.updateByPrimaryKeySelective(endCarAuditInfo);
                    }
                }
            }
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), caList);
    }

    @Override
    public int updateByPrimaryKey(TEndCarInfo tEndCarInfo) {
        return tEndCarInfoMapper.updateByPrimaryKey(tEndCarInfo);
    }

    /**
     * 车辆详情
     *
     * @param carId         t_end_car_info 表id
     * @param enduserinfoid
     * @return
     */
    @Override
    public ResultUtil getCarDetail(Integer carId, Integer enduserinfoid) {
        Map<Object, String> map = new HashMap<>();
        TEndCarInfo carDetail = tEndCarInfoMapper.selectByPrimaryKey(carId);
//        carDetail.setAuditStatus("MIDNODE");
//        tEndCarInfoMapper.updateByPrimaryKeySelective(carDetail);
        TEndUserInfo tEndUserInfo = tEndUserInfoMapper.selectByPrimaryKey(enduserinfoid);
        List<TEndUserCarRel> tEndUserCarRels = tEndUserCarRelMapper.selecByCarId(carId, enduserinfoid);
        map.put("vehicleNumbe", carDetail.getVehicleNumber() != "" ? carDetail.getVehicleNumber() : "");
        map.put("roadTransportCertificateNumber", carDetail.getRoadTransportCertificateNumber() != "" ? carDetail.getRoadTransportCertificateNumber() : "");
        map.put("drivingLicencesPhoto1", carDetail.getDrivingLicencesPhoto1() != "" ? carDetail.getDrivingLicencesPhoto1() : "");
        map.put("drivingLicencesPhoto2", carDetail.getDrivingLicencesPhoto2() != "" ? carDetail.getDrivingLicencesPhoto2() : "");
        map.put("roadTransportOperationLicensePhoto1", carDetail.getRoadTransportOperationLicensePhoto1() != "" ? carDetail.getRoadTransportOperationLicensePhoto1() : "");
        map.put("roadTransportOperationLicensePhoto2", carDetail.getRoadTransportOperationLicensePhoto2() != "" ? carDetail.getRoadTransportOperationLicensePhoto2() : "");
        if(carDetail.getAuditStatus()!=null&&!"".equals(carDetail.getAuditStatus())){
            map.put("auditStatus",carDetail.getAuditStatus());
        }else{
            map.put("auditStatus","");
        }
        if(carDetail.getAuditOpinion()!=null&&!"".equals(carDetail.getAuditOpinion())){
            map.put("auditOpinion",carDetail.getAuditOpinion());
        }else{
            map.put("auditOpinion","");
        }
        if (tEndUserCarRels.size() > 0) {
            map.put("isCarBoss", tEndUserCarRels.get(0).getUserCarRelationType() != "" ? tEndUserCarRels.get(0).getUserCarRelationType() : "");
            map.put("auditOpinion", carDetail.getAuditOpinion() == null ? "" : carDetail.getAuditOpinion());
            if (tEndUserCarRels.get(0).getUserCarRelationType().equals("CLSYRCLB")) {
                map.put("Idcard", tEndUserInfo.getIdcard() != "" ? tEndUserInfo.getIdcard() : "");
                map.put("IdcardPhoto1", tEndUserInfo.getIdcardPhoto1() != "" ? tEndUserInfo.getIdcardPhoto1() : "");
                map.put("IdcardPhoto2", tEndUserInfo.getIdcardPhoto2() != "" ? tEndUserInfo.getIdcardPhoto2() : "");
                map.put("RealName", tEndUserInfo.getRealName() != "" ? tEndUserInfo.getRealName() : "");
                map.put("certificateType", tEndUserCarRels.get(0).getCertificateType() == null ? "" : tEndUserCarRels.get(0).getCertificateType());
                map.put("certificateDoc1", tEndUserCarRels.get(0).getCertificateDoc1() == null ? "" : tEndUserCarRels.get(0).getCertificateDoc1());
                map.put("certificateDoc2", tEndUserCarRels.get(0).getCertificateDoc2() == null ? "" : tEndUserCarRels.get(0).getCertificateDoc2());
            }
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), map);
    }

    @Override
    public ResultUtil getBankCardList(Integer accountid) {
        String userLogisticsRole = CurrentUser.getUserLogisticsRole();
        List<ImperfectBankCardVO> caList = tBankCardMapper.selectImperfectBankCardList(accountid,userLogisticsRole);

        for(ImperfectBankCardVO tBankCard : caList){
            if(tBankCard.getCardOwnerIdcard()==null  || !tBankCard.getCardOwnerIdcard().equals(tBankCard.getIdcard())){
                if(tBankCard.getCardOwner() == null || tBankCard.getCardOwnerIdcard() == null || tBankCard.getCardOwnerPhone() == null
                || tBankCard.getAddress() == null || tBankCard.getIdcardValidBeginning() == null || tBankCard.getIdcardValidUntil() == null){
                    tBankCard.setPerfectState("NOPERFECT");
                }
            }
        }

        Integer accountId = CurrentUser.getUserAccountId();
        Integer endUserId = CurrentUser.getEndUserId();
        TEnduserAccountExample tEnduserAccountExample = new TEnduserAccountExample();
        TEnduserAccountExample.Criteria cr1 = tEnduserAccountExample.createCriteria();
        cr1.andAccountIdEqualTo(accountId);
        cr1.andEnableEqualTo(false);
        List<TEnduserAccount> tEnduserAccountList = tEnduserAccountMapper.selectByExample(tEnduserAccountExample);

        for (TEnduserAccount tEnduserAccount : tEnduserAccountList){
            if(!tEnduserAccount.getEnduserId().equals(endUserId)){
                List<TJdBankCardVo> jdBankCardVos = tJdBankCardMapper.agentOpenRoleUserJdBankList(tEnduserAccount.getEnduserId());

                Iterator<ImperfectBankCardVO> it1 = caList.iterator();
                while (it1.hasNext()){
                    ImperfectBankCardVO imperfectBankCardVO = it1.next();
                     for(TJdBankCardVo tJdBankCardVo : jdBankCardVos){
                         if(imperfectBankCardVO.getId().equals(tJdBankCardVo.getId())){
                             it1.remove();
                         }
                     }

                }
            }
        }


        return new ResultUtil(CodeEnum.SUCCESS.getCode(), caList);
    }

    @Override
    public List<TBankCard> getBankCardByAccountId(Integer accountid) {
        List<TBankCard> caList = tBankCardMapper.getBankCardListByAccountId(accountid);
        return caList;
    }

    //@LcnTransaction(propagation = DTXPropagation.SUPPORTS)
    @Transactional
    @Override
    public ResultUtil selectEndCar(TEndCarInfoSearchVO record) {
        Page<Object> objectPage = PageHelper.startPage(record.getPage(), record.getSize());
        //查询车辆
        if (null == record.getParam() || (null != record.getParam() && StringUtils.isEmpty(record.getParam())) ){
            record.setParam(null);
        }
        List<EndCarDTO> endCarDTOS = tEndCarInfoMapper.selectEndCar(record);
        for (EndCarDTO endCarDTO : endCarDTOS) {
            if (null != endCarDTO) {
                if (null == endCarDTO.getUserUseable()) {
                    endCarDTO.setCarUseableStatus(false);
                    endCarDTO.setCarUseableRemark("未找到车辆状态");
                }
                if (null == endCarDTO.getCarUseable()) {
                    endCarDTO.setUserUseableStatus(false);
                    endCarDTO.setUserUseableRemark("未找到司机状态");
                }
            }
        }
        ResultUtil res = ResultUtil.ok();
        res.setData(endCarDTOS);
        res.setCount(objectPage.getTotal());
        return res;
    }


    /**
     * 上传监管平台
     * 上传车辆信息
     */
    public ResultUtil carBasicInformation(CarReportDTO dto){

        try {
            TEndCarInfoExample tEndCarInfoExample=new TEndCarInfoExample();
            TEndCarInfoExample.Criteria cr = tEndCarInfoExample.createCriteria();
            cr.andVehicleNumberEqualTo(dto.getVehicleNumber());
            cr.andEnableEqualTo(false);
            TEndCarInfo tEndCarInfo = new TEndCarInfo();
            List<TEndCarInfo> tEndCarInfos =tEndCarInfoMapper.selectByExample(tEndCarInfoExample);
            //查看汽车信息是否为空
            if(!tEndCarInfos.isEmpty()){
                //查看汽车审核状态是否通过
                if(tEndCarInfos.get(0).getUploadedStatus() != null){
                    if(tEndCarInfos.get(0).getUploadedStatus().equals(DictEnum.UPLOADED.code)){
                        return ResultUtil.error("车辆信息已上报不能重复上报");
                    }
                }
                if(DictEnum.PASSNODE.code.equals(tEndCarInfos.get(0).getAuditStatus())){
                    //审核平台返回信息
                    ReportResult resultUtil=new DataReport(dto).execute();
                    String jsonObject = JSONObject.toJSONString(dto);
                    //是否上报成功
                    if(resultUtil.getSuccess().equals("true")){
                        //上报成功
                        tEndCarInfo.setId(tEndCarInfos.get(0).getId());
                        tEndCarInfo.setUploadedStatus(DictEnum.UPLOADED.code);
                        tEndCarInfo.setUploadedTime(new Date());
                        tEndCarInfo.setUploadedData(jsonObject);
                        tEndCarInfoMapper.updateByPrimaryKeySelective(tEndCarInfo);
                        return ResultUtil.ok();
                    }else {
                        //上报失败
                        tEndCarInfo.setId(tEndCarInfos.get(0).getId());
                        tEndCarInfo.setUploadedStatus(DictEnum.NOTPASS.code);
                        tEndCarInfoMapper.updateByPrimaryKeySelective(tEndCarInfo);
                        return ResultUtil.error(resultUtil.getMsg());
                    }
                }
                return ResultUtil.error("车辆信息未通过审核不能上报!");
            }else {
                return ResultUtil.error("上报监管平台失败!");
            }
        }catch (Exception e){
            throw new RuntimeException(e.getMessage());
        }
    }

    public List<TEndCarInfo> selectByCarNum(String carNum) {
        return tEndCarInfoMapper.selectByCarNum(carNum);
    }

    public TEndCarInfo selectByPrimaryKey(Integer id) {
        return tEndCarInfoMapper.selectByPrimaryKey(id);
    }

    @Override
    public TBankCard selectById(Integer id) {
        return tBankCardMapper.selectByPrimaryKey(id);
    }

    @Override
    public Integer updateByAccountId(Integer accountid) {
        return tBankCardMapper.updateByAccountId(accountid);
    }

    @Override
    public Integer setIsDefault(Integer id) {
        return tBankCardMapper.setIsDefault(id);
    }

    /**
     * @Description APP端 ：会员
     * <AUTHOR>
     * @Date 2019/6/12 19:53
     * @Param
     * @Return
     * @Exception
     */
    @Override
    public ResultUtil selectEndcarMember(TEndCarInfoSearchVO record) {
        Page<Object> objectPage = PageHelper.startPage(record.getPage(), record.getSize());
        record.setUserCarRelationType("CLSYRSJ");
        List<TEndcarAndUserMemberDTO> tEndUserCarRelVos = tEndUserCarRelMapper.selectEndcarMember(record);
        if (null == tEndUserCarRelVos || tEndUserCarRelVos.size() == 0) {
            //查找车主
            TEndCarInfoSearchVO tEndCarInfoSearchVO = new TEndCarInfoSearchVO();
            BeanUtils.copyProperties(record,tEndCarInfoSearchVO);
            tEndCarInfoSearchVO.setUserCarRelationType("CLSYRCLB");
            Page<Object> carOwnerPage = PageHelper.startPage(record.getPage(), record.getSize());
            List<TEndcarAndUserMemberDTO> endUserCarRelVos = tEndUserCarRelMapper.selectEndcarMember2(tEndCarInfoSearchVO);
            ResultUtil resultUtil = new ResultUtil();
            resultUtil.setCode("error");
            if (null != endUserCarRelVos && endUserCarRelVos.size() > 0){
                resultUtil.setMsg("搜索结果无【"+ record.getParam() +"】司机关联信息，以下为【"+ record.getParam() +"】车主关联的车辆信息");
                resultUtil.setData(endUserCarRelVos);
                resultUtil.setCount(carOwnerPage.getTotal());
            } else {
                resultUtil.setMsg("无结果");
                resultUtil.setData(new ArrayList<>());
            }
            return resultUtil;
        } else {
            for (TEndcarAndUserMemberDTO endcarAndUserMemberDTO: tEndUserCarRelVos){
                if (null !=endcarAndUserMemberDTO.getCarCurrentReceiptsNo() && null != endcarAndUserMemberDTO.getUserCurrentReceiptsNo()){
                    if (endcarAndUserMemberDTO.getCarCurrentReceiptsNo().equals(endcarAndUserMemberDTO.getUserCurrentReceiptsNo())){
                        endcarAndUserMemberDTO.setOrderBusinessCode(endcarAndUserMemberDTO.getCarCurrentReceiptsNo());
                    } else {
                        endcarAndUserMemberDTO.setOrderExecuteStatus("");
                    }
                }else {
                    endcarAndUserMemberDTO.setOrderBusinessCode("");
                    endcarAndUserMemberDTO.setOrderExecuteStatus("");
                }
                if (null == endcarAndUserMemberDTO.getOrderBusinessCode()) {
                    endcarAndUserMemberDTO.setOrderBusinessCode("");
                }
            }
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), tEndUserCarRelVos, objectPage.getTotal());
    }


    /**
    * @Description 根据司机idList 查询司机
    * <AUTHOR>
    * @Date   2019/8/10 9:23
    * @Param
    * @Return
    * @Exception
    *
    */
    @Override
    public List<TEndCarInfo> selectByEndCarIdListFeign(String endCarIdList) {
        List<Integer> carIdList = JSON.parseArray(endCarIdList,Integer.class);
        return tEndCarInfoMapper.selectByEndCarIdList(carIdList);
    }
    @Override
    public ResultUtil exportCar(TEndCarInfoSearchVO param){
        List<TEndCarInfoVO> list =  tEndCarInfoMapper.selectByPage(param);
        Map<String,Object> map = new HashMap<>();
        String[] headers ={ "车牌","注册时间","道路运输许可证号","牵引质量","车辆时别代码","车辆入网标识","认证状态","审核意见","绑定的司机"};
        String[] names ={"vehicleNumber","createTimeFormat","roadTransportCertificateNumber","approvedMotorFullQuality","vehicleIdentificationCode","verassState","auditStatusValue","auditOpinion","carUsers" };
        map.put("headers",headers);
        map.put("names",names);
        map.put("list",list);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), map);
    }

    @Override
    public TEndCarInfo selectByVehicleNumberFeign(String vehicleNumber){
        List<TEndCarInfo> list = tEndCarInfoMapper.selectByCarNum(vehicleNumber);
        if(list.size()>0){
            return list.get(0);
        }else{
            return null;
        }
    }
    /**
     * @Description: 查询车辆司机的审核状态
     * @Author: Yan
     * @Date: 2019/10/25/025 10:41
     * @Param: endCarInfoVO
     * @Return: 司机和车辆的审核状态
     */
    @Override
    public ResultUtil getCarAndDriverStatus(TEndCarInfoVO endCarInfoVO) {
        List<CarAndDriverStatusDTO> carAndDriverAuditStatus = new ArrayList<>();
        if (null == endCarInfoVO.getEnduserId() || endCarInfoVO.getEnduserId().isEmpty()) {
            carAndDriverAuditStatus = tEndCarInfoMapper.getCarAndDriverAuditStatus(endCarInfoVO);
        } else {
            carAndDriverAuditStatus = tEndCarInfoMapper.getCarsAndDriversAuditStatus(endCarInfoVO);
        }

        return ResultUtil.ok(carAndDriverAuditStatus);
    }

    /**
     * @Description 车辆分布图
     * <AUTHOR>
     * @Date   2019/11/18 14:38
     * @Param
     * @Return
     * @Exception
     *
     */
    @Override
    public ResultUtil carDistribution() {
        SysParam sysParam = sysParamAPI.getParamByKey("VEHICLELAYOUT");
        Map<String,Object> map = new HashMap<>();
        List<Object> titleLsit = new ArrayList<Object>();
        List<Map<String,Object>> list2 = new ArrayList<>();
        List<BigCarInfoVo> list = tEndCarInfoMapper.carDistribution();
        //前15条数据
        for(int i=0;i<15;i++){
            titleLsit.add(list.get(i).getName());
            Map<String,Object> m = new HashMap<>();
            if(sysParam.getParamValue()!=null &&!"".equals(sysParam.getParamValue()) &&!"0".equals(sysParam.getParamValue())){
                m.put("value",list.get(i).getValue()*Integer.parseInt(sysParam.getParamValue()));
            }else{
                m.put("value",list.get(i).getValue());
            }
            m.put("name",list.get(i).getName());
            list2.add(m);
        }
        titleLsit.add("其他");
        Integer value = 0;
        Map<String,Object> m2 = new HashMap<>();
        m2.put("name","其他");
        //一条其他
        for(int i=15;i<list.size();i++){
            value+=list.get(i).getValue();
        }
        if(sysParam.getParamValue()!=null &&!"".equals(sysParam.getParamValue()) &&!"0".equals(sysParam.getParamValue())){
            m2.put("value",value*Integer.parseInt(sysParam.getParamValue()));
        }else {
            m2.put("value",value);
        }
        list2.add(m2);
        //调整样式后15条数据
        for(int i=0;i<16;i++){
            Map<String,Object> map3 = new HashMap();
            map3.put("color","transparent");
            Map<String,Object> map2 = new HashMap();
            map2.put("normal",map3);
            Map<String,Object> map1 = new HashMap();
            map1.put("value",0);
            map1.put("name","");
            map1.put("itemStyle",map2);
            list2.add(map1);
        }
        map.put("title",titleLsit);
        map.put("data",list2);
        return  ResultUtil.ok(map);
    }

    @Override
    public int updateByPrimaryKeySelective(TEndCarInfo tEndCarInfo) {

        return tEndCarInfoMapper.updateByPrimaryKeySelective(tEndCarInfo);
    }

    @Override
    public List<TEndCarInfo> selectByClDmIsNull(String vln) {

        return tEndCarInfoMapper.selectByClDmIsNull(vln);
    }

    @Override
    public ResultUtil getCarOwner(TEndCarInfoVO endCarInfoVO) {
        EndUserDTO carOwnerIdByCarId = tEndUserCarRelMapper.getEndCarOwnerIdByCarId(endCarInfoVO.getId());
        return ResultUtil.ok(carOwnerIdByCarId);
    }

    @Override
    public ResultUtil checkTruckExist(TEndCarInfoVO vo) {
        List<TEndCarInfo> tEndCarInfos = tEndCarInfoMapper.selectByCarNum(vo.getVehicleNumber());
        if (null == tEndCarInfos || tEndCarInfos.isEmpty()) {
            return ResultUtil.error("该车辆不存在");
        }
        ResultUtil resultUtil = trajectoryRecentAPI.checkTruckExistV2(vo.getVehicleNumber()+"_"+tEndCarInfos.get(0).getLicensePlateColor());
        if (resultUtil.getMsg().equals("no")) {
            return ResultUtil.error("该车辆未入网");
        } else {
            return ResultUtil.ok("该车辆已入网");
        }
    }

    @Transactional
    @Override
    public ResultUtil bindingCarNew(TravelAndTransportLicenseVO vo) {
        Integer enduserinfoid = CurrentUser.getEndUserId();
        String userCarRelationType = DictEnum.CLSYRSJ.code;
        //判断该车是否已被绑定（平台是否存在）。
        List<TEndCarInfo> list = tEndCarInfoMapper.selectByCarNum(vo.getVehicleNumber());
        if (list.size() > 0) {//表示该车辆已经被绑定过
            TEndCarInfo info = list.get(0);
            List<TEndUserCarRel> tEndCarInfoList = tEndUserCarRelMapper.selecByCarIdAnrType(info.getId(), enduserinfoid, userCarRelationType);
            //判断登录人是否已经绑定过该车辆
            if (tEndCarInfoList.size() > 0) {
                return ResultUtil.error("绑定失败\n您已绑定该车辆,不能重复绑定");
            }

            TEndCarAuditInfo endCarAuditInfo = endCarAuditInfoMapper.selectByEndCarId(info.getId());

            //已被绑定的，判断车辆审核状态，审核通过的，比对提交的数据与数据库的是否一致，
            //判断车辆审核状态
            if(info.getAuditStatus().equals(DictEnum.PASSNODE.code)){
                //比对提交的数据与数据库的是否一致
                TravelAndTransportLicenseVO licenseVO = new TravelAndTransportLicenseVO();
                BeanUtils.copyProperties(info,licenseVO);
                //不一致时
                List<String> strs = equalsFields(vo, licenseVO);
                if(strs.size() > 0){//不一致时
                    StringBuilder err=new StringBuilder();
                    for (String str : strs){
                        err.append(str).append("、");
                    }
                    String errorStr = err.substring(0, err.length() - 1)+"不一致";//审核意见

                    if(null == endCarAuditInfo){ //车辆无子状态 旧数据
                        endCarAuditInfo = new TEndCarAuditInfo();
                        endCarAuditInfo.setEndCarId(info.getId());
                        endCarAuditInfo.setRoadTransportOperationStatus(DictEnum.PAPERNEEDUPDATE.code);
                        endCarAuditInfo.setRoadTransportOperationOpinion(errorStr);
                        endCarAuditInfo.setDrivingLicencesStatus(DictEnum.PAPERNEEDUPDATE.code);
                        endCarAuditInfo.setDrivingLicencesOpinion(errorStr);
                        endCarAuditInfo.setEnable(false);
                        endCarAuditInfoMapper.insertSelective(endCarAuditInfo);

                        //不一致时，绑定成功，更新该车数据，进入自动化审核
                        //判断行驶证跟道路运输许可证的信息，只审核信息发生改变的证件
                        boolean drivingLicencesFlag = null == info.getVehicleIdentificationCode() ||
                                StringUtils.isBlank(info.getVehicleIdentificationCode()) ||
                                !info.getVehicleIdentificationCode().equals(vo.getVehicleIdentificationCode()) ||
                                null == info.getIssueDate() ||
                                !info.getIssueDate().equals(vo.getIssueDate()) ||
                                null == info.getCardrivingLicencesValidUntil() ||
                                !info.getCardrivingLicencesValidUntil().equals(vo.getCardrivingLicencesValidUntil());
                        boolean roadTransportOperationFlag = null == info.getRoadTransportCertificateNumber() ||
                                StringUtils.isBlank(info.getRoadTransportCertificateNumber()) ||
                                !info.getRoadTransportCertificateNumber().equals(vo.getRoadTransportCertificateNumber()) ||
                                null == info.getRoadTransportOperationLicenseCode() || StringUtils.isBlank(info.getRoadTransportOperationLicenseCode()) ||
                                !info.getRoadTransportOperationLicenseCode().equals(vo.getRoadTransportOperationLicenseCode()) ||
                                null == info.getRoadTransportOperationLicenseIssueDate() ||
                                !info.getRoadTransportOperationLicenseIssueDate().equals(vo.getRoadTransportOperationLicenseIssueDate()) ||
                                null == info.getLicensePlateColor() || StringUtils.isBlank(info.getLicensePlateColor()) ||
                                !info.getLicensePlateColor().equals(vo.getLicensePlateColor());
                        vo.setId(info.getId());
                        if(drivingLicencesFlag){
                            TravelCardVO cardVO = new TravelCardVO();
                            BeanUtils.copyProperties(vo,cardVO);
                            saveTravelCard(cardVO);
                        }
                        if(roadTransportOperationFlag){
                            TransportLicenseVO transportLicenseVO = new TransportLicenseVO();
                            BeanUtils.copyProperties(vo,transportLicenseVO);
                            saveTransportLicense(transportLicenseVO);
                        }
                        //添加车辆和人的信息 t_end_user_car_rel
                        TEndUserCarRel tEndUserCarRel = new TEndUserCarRel();
                        tEndUserCarRel.setEnduserId(enduserinfoid);
                        tEndUserCarRel.setEndcarId(info.getId());
                        tEndUserCarRel.setUserCarRelationType(DictEnum.CLSYRSJ.code);
                        tEndUserCarRel.setDatafrom(vo.getDataFrom());
                        tEndUserCarRel.setDataConfirmTime(new Date());
                        tEndUserCarRel.setAuditStatus(DictEnum.PASSNODE.code);
                        tEndUserCarRelMapper.insertSelective(tEndUserCarRel);
                        //更新车辆信息
                        TEndCarInfo tEndCarInfo = new TEndCarInfo();
                        tEndCarInfo.setId(info.getId());
                        tEndCarInfo.setAuditStatus(DictEnum.PAPERNEEDUPDATE.code);
                        tEndCarInfo.setAuditOpinion(errorStr);
                        tEndCarInfo.setAuditTime(new Date());
                        tEndCarInfoMapper.updateByPrimaryKeySelective(tEndCarInfo);
                        // 添加车辆状态
                        addCarStatus(info.getId(), DictEnum.AVAILABLEREGISTERED.code);

                        //创建自动化审核
                        TEndCarInfoVO endCarInfoVO = new TEndCarInfoVO();
                        endCarInfoVO.setId(info.getId());
                        endCarInfoVO.setDrivingLicencesFlag(drivingLicencesFlag);
                        endCarInfoVO.setRoadTransportOperationFlag(roadTransportOperationFlag);
                        tEndAutomatedAuditingServie.carReview(endCarInfoVO);
                        return ResultUtil.ok(CodeEnum.SUCCESS.getCode(),"绑定成功");
                    }else{
                        //新数据，审核通过，但数据不一致时,绑定成功，更新数据，执行自动化审核
                        //判断行驶证跟道路运输许可证的信息，只审核信息发生改变的证件
                        boolean drivingLicencesFlag = false;
                        boolean roadTransportOperationFlag = false;
                        if(endCarAuditInfo.getDrivingLicencesStatus().equals(DictEnum.PASSNODE.code)){
                            drivingLicencesFlag = null == info.getVehicleIdentificationCode() ||
                                    StringUtils.isBlank(info.getVehicleIdentificationCode()) ||
                                    !info.getVehicleIdentificationCode().equals(vo.getVehicleIdentificationCode()) ||
                                    null == info.getIssueDate() ||
                                    !info.getIssueDate().equals(vo.getIssueDate()) ||
                                    null == info.getCardrivingLicencesValidUntil() ||
                                    !info.getCardrivingLicencesValidUntil().equals(vo.getCardrivingLicencesValidUntil());
                        }
                        if(endCarAuditInfo.getRoadTransportOperationStatus().equals(DictEnum.PASSNODE.code)){
                            roadTransportOperationFlag = null == info.getRoadTransportCertificateNumber() ||
                                    StringUtils.isBlank(info.getRoadTransportCertificateNumber()) ||
                                    !info.getRoadTransportCertificateNumber().equals(vo.getRoadTransportCertificateNumber()) ||
                                    null == info.getRoadTransportOperationLicenseCode() || StringUtils.isBlank(info.getRoadTransportOperationLicenseCode()) ||
                                    !info.getRoadTransportOperationLicenseCode().equals(vo.getRoadTransportOperationLicenseCode()) ||
                                    null == info.getRoadTransportOperationLicenseIssueDate() ||
                                    !info.getRoadTransportOperationLicenseIssueDate().equals(vo.getRoadTransportOperationLicenseIssueDate()) ||
                                    null == info.getLicensePlateColor() || StringUtils.isBlank(info.getLicensePlateColor()) ||
                                    !info.getLicensePlateColor().equals(vo.getLicensePlateColor());
                        }
                        vo.setId(info.getId());
                        if(drivingLicencesFlag){
                            TravelCardVO cardVO = new TravelCardVO();
                            BeanUtils.copyProperties(vo,cardVO);
                            saveTravelCard(cardVO);
                        }
                        if(roadTransportOperationFlag){
                            TransportLicenseVO transportLicenseVO = new TransportLicenseVO();
                            BeanUtils.copyProperties(vo,transportLicenseVO);
                            saveTransportLicense(transportLicenseVO);
                        }
                        //添加车辆和人的信息 t_end_user_car_rel
                        TEndUserCarRel tEndUserCarRel = new TEndUserCarRel();
                        tEndUserCarRel.setEnduserId(enduserinfoid);
                        tEndUserCarRel.setEndcarId(info.getId());
                        tEndUserCarRel.setUserCarRelationType(DictEnum.CLSYRSJ.code);
                        tEndUserCarRel.setDatafrom(vo.getDataFrom());
                        tEndUserCarRel.setDataConfirmTime(new Date());
                        tEndUserCarRel.setAuditStatus(DictEnum.PASSNODE.code);
                        tEndUserCarRelMapper.insertSelective(tEndUserCarRel);
                        //更新车辆信息
                        TEndCarInfo tEndCarInfo = new TEndCarInfo();
                        tEndCarInfo.setId(info.getId());
                        tEndCarInfo.setAuditStatus(DictEnum.NOTPASSNODE.code);
                        tEndCarInfo.setAuditOpinion(errorStr);
                        tEndCarInfo.setAuditTime(new Date());
                        tEndCarInfoMapper.updateByPrimaryKeySelective(tEndCarInfo);
                        // 添加车辆状态
                        addCarStatus(info.getId(), DictEnum.AVAILABLEREGISTERED.code);

                        //创建自动化审核
                        TEndCarInfoVO endCarInfoVO = new TEndCarInfoVO();
                        endCarInfoVO.setId(info.getId());
                        endCarInfoVO.setDrivingLicencesFlag(drivingLicencesFlag);
                        endCarInfoVO.setRoadTransportOperationFlag(roadTransportOperationFlag);
                        tEndAutomatedAuditingServie.carReview(endCarInfoVO);
                        return ResultUtil.ok(CodeEnum.SUCCESS.getCode(),"绑定成功");
                    }
                }else {//信息一致时
                    if(null == endCarAuditInfo) { //车辆无子状态 旧数据
                        endCarAuditInfo = new TEndCarAuditInfo();
                        endCarAuditInfo.setEndCarId(info.getId());
                        endCarAuditInfo.setRoadTransportOperationStatus(DictEnum.PASSNODE.code);
                        endCarAuditInfo.setRoadTransportOperationOpinion("审核通过");
                        endCarAuditInfo.setDrivingLicencesStatus(DictEnum.PASSNODE.code);
                        endCarAuditInfo.setDrivingLicencesOpinion("审核通过");
                        endCarAuditInfo.setEnable(false);
                        endCarAuditInfoMapper.insertSelective(endCarAuditInfo);
                    }

                    //一致绑定成功（不更新资料、不自动审核）
                    //添加车辆和人的信息 t_end_user_car_rel
                    TEndUserCarRel tEndUserCarRel = new TEndUserCarRel();
                    tEndUserCarRel.setEnduserId(enduserinfoid);
                    tEndUserCarRel.setEndcarId(info.getId());
                    tEndUserCarRel.setUserCarRelationType(DictEnum.CLSYRSJ.code);
                    tEndUserCarRel.setDatafrom(vo.getDataFrom());
                    tEndUserCarRel.setDataConfirmTime(new Date());
                    tEndUserCarRel.setAuditStatus(DictEnum.PASSNODE.code);
                    tEndUserCarRelMapper.insertSelective(tEndUserCarRel);
                    // 添加车辆状态
                    addCarStatus(info.getId(), DictEnum.AVAILABLEREGISTERED.code);
                    return ResultUtil.ok(CodeEnum.SUCCESS.getCode(),"绑定成功");
                }
            } else {//审核不通过的车
                boolean drivingLicencesFlag;
                boolean roadTransportOperationFlag;
                if(null == endCarAuditInfo) { //车辆无子状态 旧数据
                    endCarAuditInfo = new TEndCarAuditInfo();
                    endCarAuditInfo.setEndCarId(info.getId());
                    endCarAuditInfo.setRoadTransportOperationStatus(DictEnum.NOTPASSNODE.code);
                    endCarAuditInfo.setRoadTransportOperationOpinion("审核不通过");
                    endCarAuditInfo.setDrivingLicencesStatus(DictEnum.NOTPASSNODE.code);
                    endCarAuditInfo.setDrivingLicencesOpinion("审核不通过");
                    endCarAuditInfo.setEnable(false);
                    endCarAuditInfoMapper.insertSelective(endCarAuditInfo);
                    drivingLicencesFlag = true;
                    roadTransportOperationFlag = true;
                }else{
                    //对比行驶证数据
                    drivingLicencesFlag = null == info.getVehicleIdentificationCode() ||
                            StringUtils.isBlank(info.getVehicleIdentificationCode()) ||
                            !info.getVehicleIdentificationCode().equals(vo.getVehicleIdentificationCode()) ||
                            null == info.getIssueDate() ||
                            !info.getIssueDate().equals(vo.getIssueDate()) ||
                            null == info.getCardrivingLicencesValidUntil() ||
                            !info.getCardrivingLicencesValidUntil().equals(vo.getCardrivingLicencesValidUntil());
                    //对比道路运输许可证数据
                    roadTransportOperationFlag = null == info.getRoadTransportCertificateNumber() ||
                            StringUtils.isBlank(info.getRoadTransportCertificateNumber()) ||
                            !info.getRoadTransportCertificateNumber().equals(vo.getRoadTransportCertificateNumber()) ||
                            null == info.getRoadTransportOperationLicenseCode() || StringUtils.isBlank(info.getRoadTransportOperationLicenseCode()) ||
                            !info.getRoadTransportOperationLicenseCode().equals(vo.getRoadTransportOperationLicenseCode()) ||
                            null == info.getRoadTransportOperationLicenseIssueDate() ||
                            !info.getRoadTransportOperationLicenseIssueDate().equals(vo.getRoadTransportOperationLicenseIssueDate()) ||
                            null == info.getLicensePlateColor() || StringUtils.isBlank(info.getLicensePlateColor()) ||
                            !info.getLicensePlateColor().equals(vo.getLicensePlateColor());
                }
                //已被绑定、审核不通过，信息一致时，绑定成功，不更新资料，不进行自动化审核。
                if(!drivingLicencesFlag && !roadTransportOperationFlag){
                    //添加车辆和人的信息 t_end_user_car_rel
                    TEndUserCarRel tEndUserCarRel = new TEndUserCarRel();
                    tEndUserCarRel.setEnduserId(enduserinfoid);
                    tEndUserCarRel.setEndcarId(info.getId());
                    tEndUserCarRel.setUserCarRelationType(DictEnum.CLSYRSJ.code);
                    tEndUserCarRel.setDatafrom(vo.getDataFrom());
                    tEndUserCarRel.setDataConfirmTime(new Date());
                    tEndUserCarRel.setAuditStatus(DictEnum.PASSNODE.code);
                    tEndUserCarRelMapper.insertSelective(tEndUserCarRel);
                    // 添加车辆状态
                    addCarStatus(info.getId(), DictEnum.AVAILABLEREGISTERED.code);
                    return ResultUtil.ok(CodeEnum.SUCCESS.getCode(),"绑定成功");
                }
                //已被绑定、审核不通过的，信息不一致时，更新资料，进行自动化审核。
                vo.setId(info.getId());
                if(drivingLicencesFlag){
                    TravelCardVO cardVO = new TravelCardVO();
                    BeanUtils.copyProperties(vo,cardVO);
                    saveTravelCard(cardVO);
                }
                if(roadTransportOperationFlag){
                    TransportLicenseVO transportLicenseVO = new TransportLicenseVO();
                    BeanUtils.copyProperties(vo,transportLicenseVO);
                    saveTransportLicense(transportLicenseVO);
                }
                //添加车辆和人的信息 t_end_user_car_rel
                TEndUserCarRel tEndUserCarRel = new TEndUserCarRel();
                tEndUserCarRel.setEnduserId(enduserinfoid);
                tEndUserCarRel.setEndcarId(info.getId());
                tEndUserCarRel.setUserCarRelationType(DictEnum.CLSYRSJ.code);
                tEndUserCarRel.setDatafrom(vo.getDataFrom());
                tEndUserCarRel.setDataConfirmTime(new Date());
                tEndUserCarRel.setAuditStatus(DictEnum.PASSNODE.code);
                tEndUserCarRelMapper.insertSelective(tEndUserCarRel);
                // 添加车辆状态
                addCarStatus(info.getId(), DictEnum.AVAILABLEREGISTERED.code);

                //创建自动化审核
                TEndCarInfoVO endCarInfoVO = new TEndCarInfoVO();
                endCarInfoVO.setId(info.getId());
                endCarInfoVO.setDrivingLicencesFlag(drivingLicencesFlag);
                endCarInfoVO.setRoadTransportOperationFlag(roadTransportOperationFlag);
                tEndAutomatedAuditingServie.carReview(endCarInfoVO);

                return ResultUtil.ok(CodeEnum.SUCCESS.getCode(),"绑定成功");
            }
        }

        //没有被绑定，保存车辆信息，进行自动化审核。
        TEndCarInfo tEndCarInfo = new TEndCarInfo();
        tEndCarInfo.setVehicleNumber(vo.getVehicleNumber());
        tEndCarInfo.setLicenseplateTypeCode("01");//车牌类型代码
        //判断入网开关是否已开启 1. 如果是app  IFCARTRUCK 2.微信 WXIFCARTRUCK
        SysParam sysParam = DictEnum.DRIAPP.code.equals(vo.getDataFrom()) ? sysParamAPI.getParamByKey("IFCARTRUCK") : sysParamAPI.getParamByKey("WXIFCARTRUCK");
        //是否开启 车辆入网校验 0否 1是
        ResultUtil resultUtil = checkTruckExist(vo.getVehicleNumber(), vo.getLicensePlateColor());
        if("1".equals(sysParam.getParamValue())){
            if("error".equals(resultUtil.getCode())){
                return resultUtil;
            }
        }else{
            if("error".equals(resultUtil.getCode())){
                tEndCarInfo.setVerass(false);
            }else{
                tEndCarInfo.setVerass(true);
            }
        }
        tEndCarInfoMapper.insertSelective(tEndCarInfo);

        vo.setId(tEndCarInfo.getId());
        TravelCardVO cardVO = new TravelCardVO();
        BeanUtils.copyProperties(vo,cardVO);
        saveTravelCard(cardVO);

        TransportLicenseVO licenseVO = new TransportLicenseVO();
        BeanUtils.copyProperties(vo,licenseVO);
        saveTransportLicense(licenseVO);
        log.info("-------------------------------------------------------"+vo.getRoadTransportCertificateNumber());
        //添加车辆和人的信息 t_end_user_car_rel
        TEndUserCarRel tEndUserCarRel = new TEndUserCarRel();
        tEndUserCarRel.setEnduserId(enduserinfoid);
        tEndUserCarRel.setEndcarId(tEndCarInfo.getId());
        tEndUserCarRel.setUserCarRelationType(DictEnum.CLSYRSJ.code);
        tEndUserCarRel.setDatafrom(vo.getDataFrom());
        tEndUserCarRel.setDataConfirmTime(new Date());
        tEndUserCarRel.setAuditStatus(DictEnum.PASSNODE.code);
        tEndUserCarRelMapper.insertSelective(tEndUserCarRel);
        // 添加车辆状态
        addCarStatus(tEndCarInfo.getId(), DictEnum.AVAILABLEREGISTERED.code);

        TEndCarInfoVO endCarInfoVO = new TEndCarInfoVO();
        endCarInfoVO.setId(tEndCarInfo.getId());
        endCarInfoVO.setDrivingLicencesFlag(true);
        endCarInfoVO.setRoadTransportOperationFlag(true);
        //创建自动化审核
        tEndAutomatedAuditingServie.carReview(endCarInfoVO);
        return ResultUtil.ok(CodeEnum.SUCCESS.getCode(),"绑定成功");
    }

    private ResultUtil checkTruckExist(String vehicleNumber, String licensePlateColor){
        ResultUtil resultUtil = trajectoryRecentAPI.checkTruckExistV2(vehicleNumber+"_"+licensePlateColor);
        if (resultUtil.getMsg().equals("no")) {
            return ResultUtil.error("该车辆未入网");
        }
        return ResultUtil.ok();
    }

    //保存车头行驶证
    @Transactional
    @Override
    public ResultUtil saveTravelCard(TravelCardVO vo) {
        TEndCarInfo tEndCarInfo = new TEndCarInfo();
        tEndCarInfo.setId(vo.getId());
        tEndCarInfo.setDrivingLicencesPhoto1(vo.getDrivingLicencesPhoto1());//行驶证照片1
        tEndCarInfo.setDrivingLicencesPhoto2(vo.getDrivingLicencesPhoto2());//行驶证照片2
        tEndCarInfo.setDrivingLicencesYearPhoto(vo.getDrivingLicencesYearPhoto());//行驶证年检页照片
        tEndCarInfo.setVehicleIdentificationCode(vo.getVehicleIdentificationCode());//车辆识别代码
        tEndCarInfo.setIssueDate(vo.getIssueDate());//行驶证发证日期
        tEndCarInfo.setCardrivingLicencesValidUntil(vo.getCardrivingLicencesValidUntil());//行驶证有效期至
        if(null != vo.getDrivingLicencesInspectionRecord() && !"".equals(vo.getDrivingLicencesInspectionRecord())){
            tEndCarInfo.setDrivingLicencesInspectionRecord(vo.getDrivingLicencesInspectionRecord());
        }
        if(null != vo.getDrivingLicencesIssueUnit() && !"".equals(vo.getDrivingLicencesIssueUnit())){
            tEndCarInfo.setDrivingLicencesIssueUnit(vo.getDrivingLicencesIssueUnit());
        }
        if(null != vo.getOwner() && !"".equals(vo.getOwner())){
            tEndCarInfo.setOwner(vo.getOwner());
        }
        if(null != vo.getRegisterDate() && !"".equals(vo.getRegisterDate())){
            tEndCarInfo.setRegisterDate(vo.getRegisterDate());
        }
        tEndCarInfoMapper.updateByPrimaryKeySelective(tEndCarInfo);
        return ResultUtil.ok();
    }

    //保存道路运输许可证
    @Transactional
    @Override
    public ResultUtil saveTransportLicense(TransportLicenseVO vo) {
        TEndCarInfo tEndCarInfo = new TEndCarInfo();
        tEndCarInfo.setId(vo.getId());
        tEndCarInfo.setRoadTransportOperationLicensePhoto1(vo.getRoadTransportOperationLicensePhoto1());//道路运输证照片1
        tEndCarInfo.setRoadTransportOperationLicensePhoto2(vo.getRoadTransportOperationLicensePhoto2());//道路运输证照片2年检页
        tEndCarInfo.setRoadTransportCertificateNumber(vo.getRoadTransportCertificateNumber());//道路运输证号
        tEndCarInfo.setRoadTransportOperationLicenseCode(vo.getRoadTransportOperationLicenseCode());//道路运输证经营许可证号
        tEndCarInfo.setLicensePlateColor(vo.getLicensePlateColor());//车牌颜色
        tEndCarInfo.setRoadTransportOperationLicenseIssueDate(vo.getRoadTransportOperationLicenseIssueDate());//道路运输证发证日期
        tEndCarInfoMapper.updateByPrimaryKeySelective(tEndCarInfo);
        return ResultUtil.ok();
    }

    @Override
    public ResultUtil getTravelCardDesc(Integer id) {
        EndCarInfoDTO dto = tEndCarInfoMapper.selectByCarId(id);
        TravelCardVO vo = new TravelCardVO();
        vo.setId(dto.getId());
        vo.setDrivingLicencesPhoto1(dto.getDrivingLicencesPhoto1());
        vo.setDrivingLicencesPhoto2(dto.getDrivingLicencesPhoto2());
        vo.setDrivingLicencesYearPhoto(dto.getDrivingLicencesYearPhoto());
        vo.setVehicleNumber(dto.getVehicleNumber());
        vo.setVehicleIdentificationCode(dto.getVehicleIdentificationCode());
        vo.setIssueDate(dto.getIssueDate());
        vo.setCardrivingLicencesValidUntil(dto.getCardrivingLicencesValidUntil());
        return ResultUtil.ok(vo);
    }

    @Override
    public ResultUtil getTransportLicenseDesc(Integer id) {
        EndCarInfoDTO dto = tEndCarInfoMapper.selectByCarId(id);
        TransportLicenseVO vo = new TransportLicenseVO();
        vo.setId(dto.getId());
        vo.setRoadTransportOperationLicensePhoto1(dto.getRoadTransportOperationLicensePhoto1());
        vo.setRoadTransportOperationLicensePhoto2(dto.getRoadTransportOperationLicensePhoto2());
        vo.setRoadTransportCertificateNumber(dto.getRoadTransportCertificateNumber());
        vo.setRoadTransportOperationLicenseCode(dto.getRoadTransportOperationLicenseCode());
        vo.setLicensePlateColor(dto.getLicensePlateColor());
        vo.setRoadTransportOperationLicenseIssueDate(dto.getRoadTransportOperationLicenseIssueDate());
        return ResultUtil.ok(vo);
    }

    /**
     * 添加车辆状态
     * @param carId
     * @param status
     */
    private void addCarStatus(Integer carId, String status) {
        TEndCarStatus tEndCarStatus = new TEndCarStatus();
        tEndCarStatus.setEndcarId(carId);
        tEndCarStatus.setCarStatus(status);
        tEndCarStatus.setEnable(false);
        int i = tEndCarStatusMapper.selectCountByCarId(carId);
        if (i == 0) {
            tEndCarStatusMapper.insertSelective(tEndCarStatus);
        }
    }

    /*
    用于判断属性值是否一致
     */
    private List<String> equalsFields(TravelAndTransportLicenseVO vo1, TravelAndTransportLicenseVO vo2) {
        ArrayList<String> list = new ArrayList<>();
        /*if(!vo1.getDrivingLicencesPhoto1().equals(vo2.getDrivingLicencesPhoto1())){
            list.add("行驶证照片1");
        }
        if(!vo1.getDrivingLicencesPhoto2().equals(vo2.getDrivingLicencesPhoto2())){
            list.add("行驶证照片2");
        }
        if(!vo1.getDrivingLicencesYearPhoto().equals(vo2.getDrivingLicencesYearPhoto())){
            list.add("行驶证年检页");
        }
        if(!vo1.getRoadTransportOperationLicensePhoto1().equals(vo2.getRoadTransportOperationLicensePhoto1())){
            list.add("道路运输许可证正页");
        }
        if(!vo1.getRoadTransportOperationLicensePhoto2().equals(vo2.getRoadTransportOperationLicensePhoto2())){
            list.add("道路运输许可证年审页");
        }*/
        if(!vo1.getVehicleNumber().equals(vo2.getVehicleNumber())){
            list.add("车牌号");
        }
        if(null == vo1.getVehicleIdentificationCode() || null == vo2.getVehicleIdentificationCode() || !vo1.getVehicleIdentificationCode().equals(vo2.getVehicleIdentificationCode())){
            list.add("车辆识别代码");
        }
        if(null == vo1.getIssueDate() || null == vo2.getIssueDate() || vo1.getIssueDate().getTime() != vo2.getIssueDate().getTime()){
            list.add("行驶证发证日期");
        }
        if(null == vo1.getCardrivingLicencesValidUntil() || null == vo2.getCardrivingLicencesValidUntil()){
            list.add("行驶证有效期至");
        } else {
            if(vo1.getCardrivingLicencesValidUntil().getTime() != vo2.getCardrivingLicencesValidUntil().getTime()){
                list.add("行驶证有效期至");
            }
        }

        if(null == vo1.getRoadTransportCertificateNumber() || null == vo2.getRoadTransportCertificateNumber() || !vo1.getRoadTransportCertificateNumber().equals(vo2.getRoadTransportCertificateNumber())){
            list.add("道路运输证号");
        }
        if(null == vo1.getRoadTransportOperationLicenseCode() || null == vo2.getRoadTransportOperationLicenseCode() || !vo1.getRoadTransportOperationLicenseCode().equals(vo2.getRoadTransportOperationLicenseCode())){
            list.add("经营许可证号");
        }
        if(null == vo1.getLicensePlateColor() || null == vo2.getLicensePlateColor() || !vo1.getLicensePlateColor().equals(vo2.getLicensePlateColor())){
            list.add("车牌颜色");
        }
        if (null == vo1.getRoadTransportOperationLicenseIssueDate() || null == vo2.getRoadTransportOperationLicenseIssueDate()) {
            list.add("道路运输许可证发证日期");
        } else {
            if(vo1.getRoadTransportOperationLicenseIssueDate().getTime() != vo2.getRoadTransportOperationLicenseIssueDate().getTime()){
                list.add("道路运输许可证发证日期");
            }
        }

        return list;
    }

}
