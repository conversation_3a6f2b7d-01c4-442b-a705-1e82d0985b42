package com.lz.service.impl;

import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.JdEnum;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.dao.*;
import com.lz.dto.*;
import com.lz.model.*;
import com.lz.service.HXPayOpenRoleService;
import com.lz.service.JDPayOpenRoleService;
import com.lz.system.model.Dict;
import com.lz.vo.SelectOpenRoleInfo;
import com.lz.vo.SelectOpenRoleVO;
import com.lz.vo.SelectOrderPackOpenRoleVO;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
public class HXPayOpenRoleServiceImpl implements HXPayOpenRoleService {


    @Resource
    private TZtAccountOpenInfoMapper tZtAccountOpenInfoMapper;
    @Resource
    private TEnduserAccountMapper tEnduserAccountMapper;
    @Resource
    private TCompanyAccountMapper tCompanyAccountMapper;
    @Resource
    private TCarrieAccountMapper tCarrieAccountMapper;
    @Resource
    private TZtWalletMapper tZtWalletMapper;

    @Resource
    private TCarrierInfoMapper carrierInfoMapper;

    @Resource
    private TCompanyInfoMapper companyInfoMapper;

    @Override
    public ResultUtil selectEnduserOpenRoleInfo(SelectOpenRoleInfo vo) {
        OpenRoleInfoListDTO dto = new OpenRoleInfoListDTO();
        List<TZtAccountOpenInfo> list = new ArrayList<>();
        for (Integer enduserId : vo.getEnduserIds()) {
            TEnduserAccount tEnduserAccount = tEnduserAccountMapper.selectByEndUserId(enduserId);
            TZtAccountOpenInfo tZtAccountOpenInfo = tZtAccountOpenInfoMapper.
                    selectByAccountId(tEnduserAccount.getAccountId(), DictEnum.CD.code);
            list.add(tZtAccountOpenInfo);
        }
        dto.setHxOpenRoleInfoDTOList(list);
        return ResultUtil.ok(dto);
    }

    @Override
    public ResultUtil selectOpenRoleStatus(SelectOpenRoleVO vo) {
        HXOpenRoleStatusListDTO results = new HXOpenRoleStatusListDTO();
        if (null != vo.getCarrierId()) {
            // 查询承运方开户状态
            results.setCarrierStatus(carrierCompanyOpenRoleStatus(vo.getCarrierId(), DictEnum.CA.code,null));
        }
        if (null != vo.getCompanyId()) {
            // 查询企业开户状态
            results.setCompanyStatus(carrierCompanyOpenRoleStatus(vo.getCompanyId(), DictEnum.BD.code,null));
        }
        if (null != vo.getDriverId()) {
            // 查询司机开户状态
            results.setDriverStatus(carrierCompanyOpenRoleStatus(vo.getDriverId(),DictEnum.CD.code,DictEnum.CTYPEDRVIVER.code));
        }
        if (null != vo.getCaptainId()) {
            // 查询车队长开户状态
            results.setCaptionStatus(carrierCompanyOpenRoleStatus(vo.getCaptainId(),DictEnum.CD.code,DictEnum.CTYPECAPTAIN.code));
        }
        if (null != vo.getManagerId()) {
            // 查询经纪人开户状态
            results.setManagerStatus(carrierCompanyOpenRoleStatus(vo.getManagerId(),DictEnum.CD.code,DictEnum.CTYPEAGENTPERSON.code));
        }

        return ResultUtil.ok(results);
    }

    private HXOpenRoleStatusDTO carrierCompanyOpenRoleStatus(Integer id, String openRole,String userLogisticsRole) {
        HXOpenRoleStatusDTO dto = new HXOpenRoleStatusDTO();
        Integer accountId = null;
        if(DictEnum.CA.code.equals(openRole) || DictEnum.PF.code.equals(openRole)){
            TAccount accountId1 = tCarrieAccountMapper.selectByCarrieId(id);
            accountId = accountId1.getId();
            TCarrierInfo tCarrierInfo = carrierInfoMapper.selectByPrimaryKey(id);
            dto.setRealName(tCarrierInfo.getCarrierName());
        }else if(DictEnum.BD.code.equals(openRole)){
            TAccount accountId1 = tCompanyAccountMapper.selectByCompanyId(id);
            accountId = accountId1.getId();
            TCompanyInfoWithBLOBs tCompanyInfoWithBLOBs = companyInfoMapper.selectByPrimaryKey(id);
            dto.setRealName(tCompanyInfoWithBLOBs.getCompanyName());
        }else if(DictEnum.CD.code.equals(openRole)){
            TEnduserAccount accountId1 = tEnduserAccountMapper.selectByEndUserIdByUserRole(id, userLogisticsRole);
            accountId = accountId1.getAccountId();
            dto.setRealName(accountId1.getRealName());
        }
        TZtAccountOpenInfo tZtAccountOpenInfo = tZtAccountOpenInfoMapper.selectByAccountId(accountId,openRole);
        dto.setUserOpenRole(openRole);
        dto.setStatus(false);
        if (null != tZtAccountOpenInfo) {
            if (null != tZtAccountOpenInfo.getStatus()) {
                if (tZtAccountOpenInfo.getStatus().equals(1)) {
                    dto.setStatus(true);
                    if (null != tZtAccountOpenInfo.getPartnerAccId()) {
                        dto.setPartnerAccId(tZtAccountOpenInfo.getPartnerAccId());
                    }
                } else if (tZtAccountOpenInfo.getStatus().equals(2)) {
                    dto.setOpenStatus("开户失败");
                } else if (tZtAccountOpenInfo.getStatus().equals(0)) {
                    dto.setOpenStatus("开户处理中");
                }
            }
        } else {
            dto.setOpenStatus("未开户");
        }
        return dto;
    }



    @Override
    public ResultUtil selectOpenRoleWallet(SelectOpenRoleVO vo) {
        HXOpenRoleWalletListDTO list = new HXOpenRoleWalletListDTO();
        if (null != vo.getCarrierId()) {
            Integer accountId = tCarrieAccountMapper.selectByCarrieId(vo.getCarrierId()).getId();
            list.setCarrierWallet(tZtWalletMapper.selectWalletByCarrierCompanyId(accountId, DictEnum.CA.code));
        }
        if (null != vo.getCompanyId()) {
            Integer accountId = tCompanyAccountMapper.selectByCompanyId(vo.getCompanyId()).getId();
            list.setCompanyWallet(tZtWalletMapper.selectWalletByCarrierCompanyId(accountId, DictEnum.BD.code));
        }
        if (null != vo.getDriverId()) {
            Integer accountId = tEnduserAccountMapper.selectByEndUserIdByUserRole(vo.getDriverId(),DictEnum.CTYPEDRVIVER.code).getAccountId();
            list.setDriverWallet(tZtWalletMapper.selectWalletByCarrierCompanyId(accountId, DictEnum.CD.code));
        }
        if (null != vo.getCaptainId()) {
            Integer accountId = tEnduserAccountMapper.selectByEndUserIdByUserRole(vo.getCaptainId(),DictEnum.CTYPECAPTAIN.code).getAccountId();
            list.setCaptionWallet(tZtWalletMapper.selectWalletByCarrierCompanyId(accountId, DictEnum.CD.code));
        }
        if (null != vo.getManagerId()) {
            Integer accountId = tEnduserAccountMapper.selectByEndUserIdByUserRole(vo.getManagerId(),DictEnum.CTYPEAGENTPERSON.code).getAccountId();
            list.setManagerWallet(tZtWalletMapper.selectWalletByCarrierCompanyId(accountId, DictEnum.CD.code));
        }

        return ResultUtil.ok(list);
    }

    @Override
    public ResultUtil selectOrderPackCarrierCompanyEnduserOpenRoleStatus(SelectOrderPackOpenRoleVO vo) {
        HXOpenRoleStatusListDTO results = new HXOpenRoleStatusListDTO();
        if (null != vo.getCarrierId()) {
            // 查询承运方开户状态
            results.setCarrierStatus(carrierCompanyOpenRoleStatus(vo.getCarrierId(), DictEnum.CA.code,null));
        }
        if (null != vo.getCompanyId()) {
            // 查询企业开户状态
            results.setCompanyStatus(carrierCompanyOpenRoleStatus(vo.getCompanyId(), DictEnum.BD.code,null));
        }
        if (null != vo.getDriverId()) {
            // 查询司机开户状态
            results.setDriverStatus(carrierCompanyOpenRoleStatus(vo.getDriverId(),DictEnum.CD.code,DictEnum.CTYPEDRVIVER.code));
        }
        if (null != vo.getCaptainId()) {
            // 查询车队长开户状态
            results.setCaptionStatus(carrierCompanyOpenRoleStatus(vo.getCaptainId(),DictEnum.CD.code,DictEnum.CTYPECAPTAIN.code));
        }
        if (null != vo.getManagerId()) {
            // 查询经纪人开户状态
            results.setManagerStatus(carrierCompanyOpenRoleStatus(vo.getManagerId(),DictEnum.CD.code,DictEnum.CTYPEAGENTPERSON.code));
        }
        // 司机
        if (null != vo.getDriverList() && !vo.getDriverList().isEmpty()) {
            // results.setDriverListOpenStatus(endUserListOpenRoleStatus(vo.getDriverList()));
            results.setDriverList(tZtAccountOpenInfoMapper.selectByDrviverListStatus(vo.getDriverList()));
        }
        return ResultUtil.ok(results);
    }

    @Override
    public ResultUtil<OpenRoleWalletListDTO> selectOrderPackCarrierCompanyEnduserOPenRoleWallet(SelectOrderPackOpenRoleVO vo) {
        HXOpenRoleWalletListDTO list = new HXOpenRoleWalletListDTO();
        if (null != vo.getCarrierId()) {
            Integer accountId = tCarrieAccountMapper.selectByCarrieId(vo.getCarrierId()).getId();
            list.setCarrierWallet(tZtWalletMapper.selectWalletByCarrierCompanyId(accountId, DictEnum.CA.code));
        }
        if (null != vo.getCompanyId()) {
            Integer accountId = tCompanyAccountMapper.selectByCompanyId(vo.getCompanyId()).getId();
            list.setCompanyWallet(tZtWalletMapper.selectWalletByCarrierCompanyId(accountId, DictEnum.BD.code));
        }
        if (null != vo.getDriverId()) {
            Integer accountId = tEnduserAccountMapper.selectByEndUserIdByUserRole(vo.getDriverId(),DictEnum.CTYPEDRVIVER.code).getAccountId();
            list.setDriverWallet(tZtWalletMapper.selectWalletByCarrierCompanyId(accountId, DictEnum.CD.code));
        }
        if (null != vo.getCaptainId()) {
            Integer accountId = tEnduserAccountMapper.selectByEndUserIdByUserRole(vo.getCaptainId(),DictEnum.CTYPECAPTAIN.code).getAccountId();
            list.setCaptionWallet(tZtWalletMapper.selectWalletByCarrierCompanyId(accountId, DictEnum.CD.code));
        }
        if (null != vo.getManagerId()) {
            Integer accountId = tEnduserAccountMapper.selectByEndUserIdByUserRole(vo.getManagerId(),DictEnum.CTYPEAGENTPERSON.code).getAccountId();
            list.setManagerWallet(tZtWalletMapper.selectWalletByCarrierCompanyId(accountId, DictEnum.CD.code));
        }
        if (null != vo.getDriverList() && !vo.getDriverList().isEmpty()) {
            List<TZtWallet> tZtWallets = tZtWalletMapper.selectWalletByEnduserIdList(vo.getDriverList());
            Set<Integer> enduserIds = new HashSet<>();
            enduserIds.addAll(vo.getDriverList());
            if (tZtWallets.size() == enduserIds.size()) {
                list.setDriverWalletStatus(true);
            } else {
                list.setDriverWalletStatus(false);
            }
        }
        return ResultUtil.ok(list);
    }

    @Override
    public TZtAccountOpenInfo selectByAccountId(Integer accountId) {
        return tZtAccountOpenInfoMapper.selectByAccountId(accountId,null);
    }

    private boolean endUserListOpenRoleStatus(List<Integer> driverList) {
        List<TZtAccountOpenInfo> statusDTOS = tZtAccountOpenInfoMapper.selectByDrviverList(driverList);
        if (null == statusDTOS || statusDTOS.isEmpty()) {
            return false;
        }
        if (driverList.size() != statusDTOS.size()) {
            return false;
        }
        for (TZtAccountOpenInfo statusDTO : statusDTOS) {
            if (null == statusDTO.getStatus() || !statusDTO.getStatus().equals(1)){
                return false;
            }
        }
        return true;
    }
}
