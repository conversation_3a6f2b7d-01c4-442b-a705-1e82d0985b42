package com.lz.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.lz.api.TOrderPayDetailAPI;
import com.lz.api.TOrderPayInfoAPI;
import com.lz.common.config.RedisUtil;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.JDTradeTypeEnum;
import com.lz.common.dbenum.JdEnum;
import com.lz.common.dbenum.JdTradeType;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.CodeEnum;
import com.lz.common.model.hxPayment.request.query.CustomerQueryBalanceReq;
import com.lz.common.model.hxPayment.request.trade.CustomerWithdrawReq;
import com.lz.common.model.jdPayment.util.CloudPayFormatUtil;
import com.lz.common.util.DateUtils;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.dao.*;
import com.lz.hxpay.CloudPaymentAPI;
import com.lz.model.*;
import com.lz.service.HxWithdrawService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.vo.HxWithdrawVo;
import com.lz.vo.TCarrierCompanyOpenRoleVo;
import com.lz.vo.TWithdrawalApplicationVo;
import com.lz.vo.ToolBalancePayRequest;
import commonSdk.responseModel.CustomerBalancePayResponse;
import commonSdk.responseModel.CustomerQueryBalanceResponse;
import commonSdk.responseModel.CustomerWithdrawResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *  @author: dingweibo
 *  @Date: 2022/12/6 17:36
 *  @Description: 华夏提现
 */
@Service("hxWithdrawService")
@Slf4j
public class HxWithdrawServiceImpl implements HxWithdrawService {
    @Value("${hxyhPartnerId}")
    private String hxyhPartnerId;
    @Value("${hxyhChannelId}")
    private String hxyhChannelId;

    @Autowired
    private CloudPaymentAPI cloudPaymentAPI;
    @Autowired
    private TOrderPayInfoAPI tOrderPayInfoAPI;
    @Autowired
    private TOrderPayDetailAPI tOrderPayDetailAPI;
    @Resource
    private TZtWalletMapper tZtWalletMapper;
    @Autowired
    private SysParamAPI sysParamAPI;
    @Resource
    private TWithdrawalApplicationMapper tWithdrawalApplicationMapper;
    @Autowired
    private RedisUtil redisUtil;
    @Resource
    private TZtAccountOpenInfoMapper tZtAccountOpenInfoMapper;
    @Resource
    private TZtBankUserMapper tZtBankUserMapper;

    @Resource
    private TMemberOrderPayInfoMapper orderPayInfoMapper;

    @Resource
    private TMemberOrderPayDetailMapper orderPayDetailMapper;

    /**
     *  @author: dingweibo
     *  @Date: 2022/12/6 17:37
     *  @Description: 华夏提现页面回显
     */
    @Override
    public ResultUtil selectByWithdraw(HxWithdrawVo record) {
        HxWithdrawVo withdrawVo = null;
        if(DictEnum.CA.code.equals(record.getUserOpenRole())){
            withdrawVo = tZtAccountOpenInfoMapper.selectByWithdrawCairrer(record);
            List<TZtBankCard> tBankCardList = tZtAccountOpenInfoMapper.selectByWithdrawBankList(record);
            withdrawVo.setBankList(tBankCardList);
        }else if(DictEnum.PF.code.equals(record.getUserOpenRole())){
            withdrawVo =tZtAccountOpenInfoMapper.selectByWithdrawPf(record);
            List<TZtBankCard> tBankCardList = tZtAccountOpenInfoMapper.selectByWithdrawBankList(record);
            withdrawVo.setBankList(tBankCardList);
        }else if(DictEnum.BD.code.equals(record.getUserOpenRole())){
            withdrawVo =tZtAccountOpenInfoMapper.selectByWithdrawCompany(record);
            List<TZtBankCard> tBankCardList = tZtAccountOpenInfoMapper.selectByWithdrawBankList(record);
            withdrawVo.setBankList(tBankCardList);
        }else if(DictEnum.CD.code.equals(record.getUserOpenRole())){
            withdrawVo =tZtAccountOpenInfoMapper.selectByWithdrawManager(record);
            if(null == withdrawVo || "".equals(withdrawVo)){
                return ResultUtil.ok("当前账户未开户，不允许提现");
            }
            List<TZtBankCard> tBankCardList = tZtAccountOpenInfoMapper.selectByWithdrawBankList(record);
            for(TZtBankCard tBankCard:tBankCardList){
                //持卡人
                String cardOwner = tBankCard.getAcctName();
                String cardNo = "***************"+tBankCard.getAcctNo().substring(tBankCard.getAcctNo().length() - 4);
                tBankCard.setRemark(cardOwner+" "+cardNo);
            }
            withdrawVo.setBankList(tBankCardList);
        }
        if(null==withdrawVo){
            return ResultUtil.error("请完善银行卡信息！");
        }
        return ResultUtil.ok(withdrawVo);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/12/6 17:37
     *  @Description: 华夏经纪人发起提现
     */
    @Override
    public ResultUtil mangerWithdraw(HxWithdrawVo record) {
        //提现金额
        TWithdrawalApplication tWithdrawalApplication = new TWithdrawalApplication();
        String partnerAccId = "";
        if(DictEnum.CD.code.equals(record.getUserOpenRole())){
            TZtAccountOpenInfo tZtAccountOpenInfo = tZtAccountOpenInfoMapper.selectByPrimaryKey(record.getOpenRoleId());
            log.info("查询自然人开户表回参：{}", JSONObject.toJSONString(tZtAccountOpenInfo));
            if(null == tZtAccountOpenInfo || null == tZtAccountOpenInfo.getStatus() || tZtAccountOpenInfo.getStatus()!=1) {
                    return ResultUtil.error("经纪人未开通华夏支付，请联系运营平台予以解决。");
            }
            partnerAccId = tZtAccountOpenInfo.getPartnerAccId();
        }
        if (StringUtils.isNotEmpty(partnerAccId)){
            //判断华夏余额是否充足
            CustomerQueryBalanceReq req = new CustomerQueryBalanceReq();
            req.setPartnerId(hxyhPartnerId);
            req.setRequestId(IdWorkerUtil.getInstance().nextId());
            req.setRequestTime(DateUtils.getRequestTime());
            req.setChannelId(hxyhChannelId);
            req.setPartnerAccId(partnerAccId);
            req.setAccountType(1);
            log.info("查询华夏余额入参：{}", JSONObject.toJSONString(req));
            ResultUtil resultUtil1 = cloudPaymentAPI.execute(req);
            CustomerQueryBalanceResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, CustomerQueryBalanceResponse.class);
            log.info("查询华夏余额回参：{}", JSONObject.toJSONString(response));
            if(JdEnum.JDSUCCESSCODE.code.equals(response.getResponseCode())){
                BigDecimal subAccountMoney = new BigDecimal(response.getSubAccountMoney() / 100);
                if (record.getAmount().compareTo(subAccountMoney) > 0) {
                    return ResultUtil.error("华夏实际余额不足,提现失败");
                }
            }else{
                return ResultUtil.error("查询华夏余额失败");
            }
            TZtWallet tZtWallet = tZtWalletMapper.selectByPrimaryKey(record.getWalletId());
            if(record.getAmount().doubleValue()>tZtWallet.getAccountBalance().doubleValue()){
                return ResultUtil.error("钱包余额不足,提现失败");
            }
        } else {
            return ResultUtil.error("提现失败,华夏开户表信息不全");
        }
        tWithdrawalApplication.setCode(IdWorkerUtil.getInstance().nextId());
        tWithdrawalApplication.setBizOrderNo(IdWorkerUtil.getInstance().nextId());
        tWithdrawalApplication.setTransactionInitiationTime(new Date());
        tWithdrawalApplication.setCarrierCompanyId(record.getAccountId());
        tWithdrawalApplication.setUserOpenRole(record.getUserOpenRole());
        tWithdrawalApplication.setTransactionType(record.getTradeType());
        tWithdrawalApplication.setAmount(record.getAmount());
        tWithdrawalApplication.setOutAccount(partnerAccId);
        tWithdrawalApplication.setEnterAccount(record.getBankAccountNo());
        tWithdrawalApplication.setWithdrawalStatus(DictEnum.PACKEXTRACTPROCESSED.code);
        tWithdrawalApplication.setOpenRoleId(record.getOpenRoleId());
        tWithdrawalApplication.setBankId(record.getBankId());
        tWithdrawalApplication.setBankAccountNo(record.getBankAccountNo());
        tWithdrawalApplication.setAccountName(record.getAccountName());
        tWithdrawalApplication.setWalletId(record.getWalletId());
        tWithdrawalApplication.setParam1("HX");
        tWithdrawalApplicationMapper.insertSelective(tWithdrawalApplication);
        try {
            HxWithdrawVo hxWithdrawVo = new HxWithdrawVo();
            hxWithdrawVo.setAmount(record.getAmount());
            hxWithdrawVo.setBizOrderNo(tWithdrawalApplication.getCode());
            hxWithdrawVo.setOpenRoleId(tWithdrawalApplication.getOpenRoleId());
            hxWithdrawVo.setUserOpenRole(tWithdrawalApplication.getUserOpenRole());
            hxWithdrawVo.setBankId(tWithdrawalApplication.getBankId());
            hxWithdrawVo.setAccountName(tWithdrawalApplication.getAccountName());
            hxWithdrawVo.setBankAccountNo(tWithdrawalApplication.getBankAccountNo());
            hxWithdrawVo.setWalletId(tWithdrawalApplication.getWalletId());
            hxWithdrawVo.setTradeType(tWithdrawalApplication.getTransactionType());
            hxWithdrawVo.setTwaId(tWithdrawalApplication.getId());
            hxWithdrawVo.setAccountId(tWithdrawalApplication.getCarrierCompanyId());
            hxWithdrawVo.setParam("1");
            return carrierAndComapnyWithdrawStart(hxWithdrawVo);
        }catch (Exception e){
            log.error("华夏提现失败",e);
            return ResultUtil.error("提现失败");
        }
    }


    /**
     *  @author: dingweibo
     *  @Date: 2022/12/6 17:37
     *  @Description: 华夏发起提现插入提现申请表中
     */
    @Override
    public ResultUtil carrierAndComapnyWithdraw(HxWithdrawVo record) {
        try {
            log.info("发起提现插入提现申请表中提现开始处理入参1：{}", JSONObject.toJSONString(record));
            //提现金额
            if (null != record.getOpenRoleId() && null != record.getWalletId() && null != record.getUserOpenRole()) {
                TWithdrawalApplication twa = new TWithdrawalApplication();
                twa.setUserOpenRole(record.getUserOpenRole());
                twa.setOpenRoleId(record.getOpenRoleId());
                List<TWithdrawalApplication> tWithdrawalApplicationList = new ArrayList<>();
                if(!DictEnum.CD.code.equals(record.getUserOpenRole())){
                   tWithdrawalApplicationList = tWithdrawalApplicationMapper.selectByApprovalStatusNo(twa);
                }
                if(tWithdrawalApplicationList.size()<1){
                    String partnerAccId = "";
                    TCarrierCompanyOpenRoleVo tcorVoreq = new  TCarrierCompanyOpenRoleVo();
                    tcorVoreq.setId(record.getOpenRoleId());
                    tcorVoreq.setUserOpenRole(record.getUserOpenRole());
                    tcorVoreq.setBankCardId(record.getBankId());
                    TZtAccountOpenInfo tZtAccountOpenInfo = tZtAccountOpenInfoMapper.selectByPrimaryKey(record.getOpenRoleId());
                    if(null == tZtAccountOpenInfo || null == tZtAccountOpenInfo.getStatus() || tZtAccountOpenInfo.getStatus()!=1) {
                        return ResultUtil.error("当前未开通华夏支付，请联系运营平台予以解决。");
                    }
                    log.info("查询对公开户表回参：{}", JSONObject.toJSONString(tZtAccountOpenInfo));
                    partnerAccId = tZtAccountOpenInfo.getPartnerAccId();
                    if (StringUtils.isNotEmpty(partnerAccId)){
                        //判断华夏余额是否充足
                        CustomerQueryBalanceReq req = new CustomerQueryBalanceReq();
                        req.setPartnerId(hxyhPartnerId);
                        req.setRequestId(IdWorkerUtil.getInstance().nextId());
                        req.setRequestTime(DateUtils.getRequestTime());
                        req.setChannelId(hxyhChannelId);
                        req.setPartnerAccId(partnerAccId);
                        req.setAccountType(1);
                        log.info("查询华夏余额入参：{}", JSONObject.toJSONString(req));
                        ResultUtil resultUtil1 = cloudPaymentAPI.execute(req);
                        CustomerQueryBalanceResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, CustomerQueryBalanceResponse.class);
                        log.info("查询华夏余额回参：{}", JSONObject.toJSONString(response));
                        if(JdEnum.JDSUCCESSCODE.code.equals(response.getResponseCode())){
                            BigDecimal subAccountMoney = new BigDecimal(response.getSubAccountMoney() / 100);
                            if (record.getAmount().compareTo(subAccountMoney) > 0) {
                                return ResultUtil.error("华夏实际余额不足,提现失败");
                            }
                        }else{
                            return ResultUtil.error("查询华夏余额失败");
                        }
                        TZtWallet tZtWallet = tZtWalletMapper.selectByPrimaryKey(record.getWalletId());
                        if(record.getAmount().doubleValue()>tZtWallet.getAccountBalance().doubleValue()){
                            return ResultUtil.error("钱包余额不足,提现失败");
                        }
                    } else {
                        return ResultUtil.error("提现失败,华夏开户表信息不全");
                    }
                    TWithdrawalApplication tWithdrawalApplication = new TWithdrawalApplication();
                    tWithdrawalApplication.setCode(IdWorkerUtil.getInstance().nextId());
                    tWithdrawalApplication.setBizOrderNo(IdWorkerUtil.getInstance().nextId());
                    tWithdrawalApplication.setTransactionInitiationTime(new Date());
                    tWithdrawalApplication.setCarrierCompanyId(record.getAccountId() );
                    tWithdrawalApplication.setUserOpenRole(record.getUserOpenRole());
                    tWithdrawalApplication.setTransactionType(record.getTradeType());
                    tWithdrawalApplication.setAmount(record.getAmount());
                    tWithdrawalApplication.setOutAccount(partnerAccId);
                    tWithdrawalApplication.setEnterAccount(record.getBankAccountNo());
                    tWithdrawalApplication.setWithdrawalStatus(DictEnum.PACKEXTRACTPROCESSED.code);
                    tWithdrawalApplication.setOpenRoleId(record.getOpenRoleId());
                    tWithdrawalApplication.setBankId(record.getBankId());
                    tWithdrawalApplication.setBankAccountNo(record.getBankAccountNo());
                    tWithdrawalApplication.setAccountName(record.getAccountName());
                    tWithdrawalApplication.setWalletId(record.getWalletId());
                    tWithdrawalApplication.setParam1("HX");
                    tWithdrawalApplicationMapper.insertSelective(tWithdrawalApplication);
                    return ResultUtil.ok("提现申请成功");
                }else{
                    return ResultUtil.ok("提现失败，当前账户存在未审批的提现");
                }
            }
        } catch (Exception e) {
            log.error("华夏提现失败", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("提现失败");
            }
        }
        return ResultUtil.ok();
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/12/6 17:38
     *  @Description: 华夏提现审批
     */
    @Override
    public ResultUtil updateApprove(TWithdrawalApplicationVo record) {
        TWithdrawalApplication tWithdrawalApplication = tWithdrawalApplicationMapper.selectByPrimaryKey(record.getId());
        try {
            if(null!=tWithdrawalApplication.getApprovalStatus()){
                return ResultUtil.error("当前提现交易已审批不允许再次审批！");
            }
            tWithdrawalApplication.setAmount(record.getAmount());
            tWithdrawalApplication.setApprovalComments(record.getApprovalComments());
            tWithdrawalApplication.setApprovalScreenshot(record.getApprovalScreenshot());
            tWithdrawalApplication.setApprovalStatus(record.getApprovalStatus());
            if(tWithdrawalApplication.getApprovalStatus().equals(DictEnum.PASSNODE.code)){
                tWithdrawalApplication.setWithdrawalStatus(DictEnum.PACKEXTRACTPROCESSED.code);
            }
            tWithdrawalApplicationMapper.updateByPrimaryKeySelective(tWithdrawalApplication);
            if(tWithdrawalApplication.getApprovalStatus().equals(DictEnum.PASSNODE.code)){
                HxWithdrawVo jdWithdrawVo = new HxWithdrawVo();
                jdWithdrawVo.setAmount(tWithdrawalApplication.getAmount());
                jdWithdrawVo.setBizOrderNo(tWithdrawalApplication.getCode());
                jdWithdrawVo.setOpenRoleId(tWithdrawalApplication.getOpenRoleId());
                jdWithdrawVo.setUserOpenRole(tWithdrawalApplication.getUserOpenRole());
                jdWithdrawVo.setBankId(tWithdrawalApplication.getBankId());
                jdWithdrawVo.setAccountName(tWithdrawalApplication.getAccountName());
                jdWithdrawVo.setBankAccountNo(tWithdrawalApplication.getBankAccountNo());
                jdWithdrawVo.setWalletId(tWithdrawalApplication.getWalletId());
                jdWithdrawVo.setTradeType(tWithdrawalApplication.getTransactionType());
                jdWithdrawVo.setTwaId(tWithdrawalApplication.getId());
                jdWithdrawVo.setAccountId(tWithdrawalApplication.getCarrierCompanyId());
                return carrierAndComapnyWithdrawStart(jdWithdrawVo);
            }
            return ResultUtil.ok();
        }catch (Exception e){
            log.error("提现审批失败",e);
            return ResultUtil.error("提现审批失败");
        }
    }
    /**
     *  @author: dingweibo
     *  @Date: 2021/8/31 18:56
     *  @Description: 提现审批通过后发起提现
     */

    @Override
    public ResultUtil carrierAndComapnyWithdrawStart(HxWithdrawVo record) {
        ResultUtil resultUtil = new ResultUtil();
        try {
            log.info("提现开始处理入参：{}", JSONObject.toJSONString(record));
            //提现金额
            if (null != record.getOpenRoleId() && null != record.getWalletId() && null != record.getUserOpenRole()) {

                TZtAccountOpenInfo tZtAccountOpenInfo = tZtAccountOpenInfoMapper.selectByPrimaryKey(record.getOpenRoleId());
                log.info("查询开户表回参：{}", JSONObject.toJSONString(tZtAccountOpenInfo));
                String partnerAccId = tZtAccountOpenInfo.getPartnerAccId();
                TZtBankBindRelationship tZtBankBindRelationship = tZtBankUserMapper.selectByOpenRoleIdAndBankId(record.getAccountId(),record.getBankId());
                String cardId = tZtBankBindRelationship.getBankNo();
                if (StringUtils.isNotEmpty(partnerAccId)) {
                    //判断华夏余额是否充足
                    CustomerQueryBalanceReq req = new CustomerQueryBalanceReq();
                    req.setPartnerId(hxyhPartnerId);
                    req.setRequestId(IdWorkerUtil.getInstance().nextId());
                    req.setRequestTime(DateUtils.getRequestTime());
                    req.setChannelId(hxyhChannelId);
                    req.setPartnerAccId(partnerAccId);
                    req.setAccountType(1);
                    log.info("查询华夏余额入参：{}", JSONObject.toJSONString(req));
                    ResultUtil resultUtil1 = cloudPaymentAPI.execute(req);
                    CustomerQueryBalanceResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, CustomerQueryBalanceResponse.class);
                    log.info("查询华夏余额回参：{}", JSONObject.toJSONString(response));
                    if(JdEnum.JDSUCCESSCODE.code.equals(response.getResponseCode())){
                        if(record.getAmount().doubleValue()>response.getSubAccountMoney().doubleValue()){
                            TWithdrawalApplication twa = new TWithdrawalApplication();
                            twa.setId(record.getTwaId());
                            twa.setWithdrawalStatus(DictEnum.PACKEWITHDRAWERROR.code);
                            twa.setWithdrawalInfo("华夏实际余额不足,支付失败");
                            updateTwa(twa);
                            resultUtil.setCode(CodeEnum.ERROR.getCode());
                            resultUtil.setMsg("华夏实际余额不足,支付失败");
                            return resultUtil;
                        }
                    }else{
                        TWithdrawalApplication twa = new TWithdrawalApplication();
                        twa.setId(record.getTwaId());
                        twa.setWithdrawalStatus(DictEnum.PACKEWITHDRAWERROR.code);
                        twa.setWithdrawalInfo("查询华夏余额失败");
                        updateTwa(twa);
                        resultUtil.setCode(CodeEnum.ERROR.getCode());
                        resultUtil.setMsg("提现失败,华夏开户表信息不全");
                        return resultUtil;
                    }
                } else {
                    TWithdrawalApplication twa = new TWithdrawalApplication();
                    twa.setId(record.getTwaId());
                    twa.setWithdrawalStatus(DictEnum.PACKEWITHDRAWERROR.code);
                    twa.setWithdrawalInfo("提现失败,华夏开户表信息不全");
                    updateTwa(twa);
                    resultUtil.setCode(CodeEnum.ERROR.getCode());
                    resultUtil.setMsg("提现失败,华夏开户表信息不全");
                    return resultUtil;
                }
                CustomerWithdrawReq req = new CustomerWithdrawReq();
                req.setPartnerId(hxyhPartnerId);
                req.setRequestId(IdWorkerUtil.getInstance().nextId());
                req.setRequestTime(DateUtils.getRequestTime());
                req.setChannelId(hxyhChannelId);
                req.setBizOrderNo(record.getBizOrderNo());//商户订单号
                req.setOrderAmount(record.getAmount());//交易金额
                req.setPartnerAccId(partnerAccId);
                req.setCardId(cardId);
                req.setTradeAbstract("提现");
                SysParam sysParam = sysParamAPI.getParamByKey(DictEnum.HXWITHDRAWURLCOMPANY.code);
                req.setNotifyUrl(sysParam.getParamValue());
                log.info("发起华夏提现入参：{}", JSONObject.toJSONString(req));
                ResultUtil resultUtil1 = cloudPaymentAPI.execute(req);
                CustomerWithdrawResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, CustomerWithdrawResponse.class);
                log.info("发起华夏提现回参：{}", JSONObject.toJSONString(response));
                if(JdEnum.JDSUCCESSCODE.code.equals(response.getResponseCode())){
                    if(!"PAY_FAIL".equals(response.getOrderStatus())){
                        TOrderPayInfo orderPayInfo = new TOrderPayInfo();
                        orderPayInfo.setCode(IdWorkerUtil.getInstance().nextId());
                        orderPayInfo.setOrderActualPayment(record.getAmount());
                        orderPayInfo.setOrderPrepayAmount(record.getAmount());
                        orderPayInfo.setOrderTotalPayment(record.getAmount());
                        orderPayInfo.setPaymentPlatforms(DictEnum.HXPLATFORMS.code);
                        orderPayInfo.setOrderPayStatus(DictEnum.P110.code);
                        tOrderPayInfoAPI.save(orderPayInfo);

                        TOrderPayDetail orderPayDetail = new TOrderPayDetail();
                        orderPayDetail.setCode(req.getBizOrderNo());
                        orderPayDetail.setOrderPayCode(orderPayInfo.getCode());
                        orderPayDetail.setBankCardId(record.getBankId());
                        orderPayDetail.setBankNo(record.getBankAccountNo());
                        orderPayDetail.setCardHolder(record.getAccountName());
                        orderPayDetail.setTradeType(record.getTradeType());
                        orderPayDetail.setOperateState(JdTradeType.FQ.code);
                        orderPayDetail.setOperateTime(new Date());
                        orderPayDetail.setParam1(String.valueOf(record.getWalletId()));
                        tOrderPayDetailAPI.save(orderPayDetail);
                        //修改钱包
                        TZtWallet tZtWallet= tZtWalletMapper.selectByPrimaryKey(record.getWalletId());
                        if(null != tZtWallet){
                            //添加提现金额
                            BigDecimal withdrawAmount=tZtWallet.getWithdrawAmount().add(record.getAmount());
                            tZtWallet.setWithdrawAmount(withdrawAmount);

                            BigDecimal accountBalance = tZtWallet.getAccountBalance().subtract(record.getAmount());
                            tZtWallet.setAccountBalance(accountBalance);
                            tZtWalletMapper.updateByPrimaryKey(tZtWallet);
                            resultUtil.setCode(CodeEnum.SUCCESS.getCode());
                            resultUtil.setMsg("提现成功");
                            return resultUtil;
                        }else {
                            resultUtil.setCode(CodeEnum.ERROR.getCode());
                            resultUtil.setMsg("未找到钱包");
                            return resultUtil;
                        }
                    }else{
                        TWithdrawalApplication twa = new TWithdrawalApplication();
                        twa.setId(record.getTwaId());
                        twa.setWithdrawalStatus(DictEnum.PACKEWITHDRAWERROR.code);
                        twa.setWithdrawalInfo("提现失败,华夏返回状态失败："+response.getResponseDesc());
                        updateTwa(twa);
                        resultUtil.setCode(CodeEnum.ERROR.getCode());
                        resultUtil.setMsg("提现失败："+response.getResponseDesc());
                        return resultUtil;
                    }
                }else{
                    TWithdrawalApplication twa = new TWithdrawalApplication();
                    twa.setId(record.getTwaId());
                    twa.setWithdrawalStatus(DictEnum.PACKEWITHDRAWERROR.code);
                    twa.setWithdrawalInfo("提现失败,调用华夏失败："+response.getResponseDesc());
                    updateTwa(twa);
                    resultUtil.setCode(CodeEnum.ERROR.getCode());
                    resultUtil.setMsg("提现失败："+response.getResponseDesc());
                    return resultUtil;
                }
            }
        } catch (Exception e) {
            log.error("ZJJ-052,提现失败", e);
            TWithdrawalApplication twa = new TWithdrawalApplication();
            twa.setId(record.getTwaId());
            twa.setWithdrawalStatus(DictEnum.PACKEWITHDRAWERROR.code);
            twa.setWithdrawalInfo("提现失败,服务器异常");
            updateTwa(twa);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("提现失败");
            }
        }
        return resultUtil;
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/12/21 17:00
     *  @Description: 小工具查询华夏余额
     */
    @Override
    public ResultUtil selectByBalance(CustomerQueryBalanceReq req) {
        req.setPartnerId(hxyhPartnerId);
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setChannelId(hxyhChannelId);
        req.setAccountType(1);
        log.info("查询华夏余额入参：{}", JSONObject.toJSONString(req));
        ResultUtil resultUtil1 = cloudPaymentAPI.execute(req);
        CustomerQueryBalanceResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, CustomerQueryBalanceResponse.class);
        log.info("查询华夏余额回参：{}", JSONObject.toJSONString(response));
        return ResultUtil.ok(response);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/12/21 16:14
     *  @Description: 华夏小工具余额支付
     */
    @Override
    public ResultUtil balancePay(ToolBalancePayRequest req) {
        TZtWallet tZtWallet = tZtWalletMapper.selectByPartnerAccId(req.getOutPartnerAccId());
        if(null == tZtWallet || "".equals(tZtWallet)){
            return  ResultUtil.error("出款方无钱包不允许余额支付");
        }
        TZtWallet inWallet = tZtWalletMapper.selectByPartnerAccId(req.getInPartnerAccId());
        if(null == inWallet || "".equals(inWallet)){
            return  ResultUtil.error("收款方无钱包不允许余额支付");
        }
        //判断华夏余额是否充足
        CustomerQueryBalanceReq req2 = new CustomerQueryBalanceReq();
        req2.setPartnerId(hxyhPartnerId);
        req2.setRequestId(IdWorkerUtil.getInstance().nextId());
        req2.setRequestTime(DateUtils.getRequestTime());
        req2.setChannelId(hxyhChannelId);
        req2.setPartnerAccId(req.getOutPartnerAccId());
        req2.setAccountType(1);
        log.info("查询华夏余额入参：{}", JSONObject.toJSONString(req2));
        ResultUtil resultUtil2 = cloudPaymentAPI.execute(req2);
        CustomerQueryBalanceResponse response2 = CloudPayFormatUtil.ObjToBean(resultUtil2, CustomerQueryBalanceResponse.class);
        log.info("查询华夏余额回参：{}", JSONObject.toJSONString(response2));
        if(JdEnum.JDSUCCESSCODE.code.equals(response2.getResponseCode())){
            if(req.getOrderAmount().doubleValue()>response2.getSubAccountMoney().doubleValue()){
                return ResultUtil.error("出款方华夏余额不足");
            }
        }else{
            return ResultUtil.error("查询华夏余额失败");
        }
        req.setPartnerId(hxyhPartnerId);
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setChannelId(hxyhChannelId);
        req.setBizOrderNo(IdWorkerUtil.getInstance().nextId());
        req.setOrderAmount(req.getOrderAmount());
        req.setTradeAbstract("华夏小工具余额支付："+req.getTradeAbstract());
        SysParam sysParam = sysParamAPI.getParamByKey("HXWITHDRAWURLCOMPANY");
        req.setNotifyUrl(sysParam.getParamValue());
        ResultUtil resultUtil1 = cloudPaymentAPI.execute(req);
        CustomerBalancePayResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, CustomerBalancePayResponse.class);
        if(JdEnum.JDSUCCESSCODE.code.equals(response.getResponseCode())) {
            if (!"PAY_FAIL".equals(response.getOrderStatus())) {
                TOrderPayInfo orderPayInfo = new TOrderPayInfo();
                orderPayInfo.setCode(IdWorkerUtil.getInstance().nextId());
                orderPayInfo.setOrderTotalPayment(req.getOrderAmount());
                orderPayInfo.setOrderPrepayAmount(req.getOrderAmount());
                orderPayInfo.setOrderActualPayment(req.getOrderAmount());
                orderPayInfo.setPaymentPlatforms(DictEnum.HXPLATFORMS.code);
                orderPayInfo.setOrderPayStatus(DictEnum.P070.code);

                orderPayInfoMapper.insertSelective(orderPayInfo);
                TOrderPayDetail orderPayDetail = new TOrderPayDetail();
                orderPayDetail.setCode(req.getBizOrderNo());
                orderPayDetail.setOrderPayCode(orderPayInfo.getCode());
                orderPayDetail.setTradeType(JDTradeTypeEnum.TOOL_BALANCE_PAY.code);
                orderPayDetail.setOperateState(JdTradeType.RZ.code);
                orderPayDetail.setOperateTime(new Date());
                orderPayDetail.setParam1(String.valueOf(tZtWallet.getId()));
                orderPayDetailMapper.insertSelective(orderPayDetail);
                //修改钱包
                if (null != tZtWallet) {
                    //添加支付金额
                    BigDecimal entryAmount = tZtWallet.getEntryAmount().add(req.getOrderAmount());
                    tZtWallet.setEntryAmount(entryAmount);
                    BigDecimal accountBalance = tZtWallet.getAccountBalance().subtract(req.getOrderAmount());
                    tZtWallet.setAccountBalance(accountBalance);
                    tZtWalletMapper.updateByPrimaryKey(tZtWallet);
                } else {
                    return ResultUtil.error("余额支付失败，付款方钱包为空");
                }
            }else {
                return ResultUtil.error(response.getResponseDesc());
            }
        }else{
            return ResultUtil.error(response.getResponseDesc());
        }
        return ResultUtil.ok();
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/12/7 15:32
     *  @Description: 修改提现申请表状态
     */
    private void updateTwa(TWithdrawalApplication record){
        TWithdrawalApplication tWithdrawalApplication = tWithdrawalApplicationMapper.selectByPrimaryKey(record.getId());
        tWithdrawalApplication.setWithdrawalStatus(record.getWithdrawalStatus());
        tWithdrawalApplication.setWithdrawalInfo(record.getWithdrawalInfo());
        tWithdrawalApplicationMapper.updateByPrimaryKeySelective(tWithdrawalApplication);
    }
}
