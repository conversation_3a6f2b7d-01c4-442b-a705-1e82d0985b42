package com.lz.service.impl;

import com.lz.common.dbenum.DictEnum;
import com.lz.common.util.ResultUtil;
import com.lz.dao.TCarrieAccountMapper;
import com.lz.dao.TCompanyAccountMapper;
import com.lz.dao.TEnduserAccountMapper;
import com.lz.dao.TZtAccountOpenInfoMapper;
import com.lz.example.TZtAccountOpenInfoExample;
import com.lz.model.TZtAccountOpenInfo;
import com.lz.service.TZtAccountOpenInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service("tZtAccountOpenInfoService")
public class TZtAccountOpenInfoServiceImpl implements TZtAccountOpenInfoService {
    @Resource
    private TZtAccountOpenInfoMapper tZtAccountOpenInfoMapper;
    @Resource
    private TCarrieAccountMapper tCarrieAccountMapper;
    @Resource
    private TCompanyAccountMapper tCompanyAccountMapper;
    @Resource
    private TEnduserAccountMapper tEnduserAccountMapper;

    @Override
    public TZtAccountOpenInfo selectByPrimaryKey(Integer id) {
        return tZtAccountOpenInfoMapper.selectByPrimaryKey(id);
    }

    @Override
    public ResultUtil ifOpenRole(Integer accountId) {

        Map map = new HashMap();
        ResultUtil resultUtil = new ResultUtil();
        //0未开户  1 开户成功  2开户中  3开户失败
        TZtAccountOpenInfoExample ztAccountOpenInfoExample = new TZtAccountOpenInfoExample();
        TZtAccountOpenInfoExample.Criteria cr1 = ztAccountOpenInfoExample.createCriteria();
        cr1.andAccountIdEqualTo(accountId);
        List<TZtAccountOpenInfo> ztAccountOpenInfoList = tZtAccountOpenInfoMapper.selectByExample(ztAccountOpenInfoExample);
        Integer state = 0;
        if(ztAccountOpenInfoList.size()>0){
            if(ztAccountOpenInfoList.get(0).getStatus()==1){
                state = 1;
            }else if(ztAccountOpenInfoList.get(0).getStatus()==0){
                state=2;
            }else if(ztAccountOpenInfoList.get(0).getStatus()==2){
                state=3;
            }
        }else {
            state=0;
        }
        map.put("ifOpenRole",state);
        if(null != ztAccountOpenInfoList && ztAccountOpenInfoList.size() > 0
                && null != ztAccountOpenInfoList.get(0).getResponseMessage()){
            map.put("openResponseDesc",ztAccountOpenInfoList.get(0).getResponseMessage());
        }else {
            map.put("openResponseDesc","暂无信息!");
        }
        resultUtil.setData(map);
        return resultUtil;
    }

    @Override
    public TZtAccountOpenInfo selectByCarrierIdAndCompanyIdAndEndUserIdByStatus(Integer carrierIdAndCompanyIdAndEndUserId,String userOpenRole) {
        Integer accountId = null;
        if(DictEnum.CA.code.equals(userOpenRole) || DictEnum.PF.code.equals(userOpenRole)){
            accountId = tCarrieAccountMapper.selectByCarrieId(carrierIdAndCompanyIdAndEndUserId).getId();
        }else if(DictEnum.BD.code.equals(userOpenRole)){
            accountId = tCompanyAccountMapper.selectByCompanyId(carrierIdAndCompanyIdAndEndUserId).getId();
        }else if (DictEnum.CD.code.equals(userOpenRole)){
            accountId = tEnduserAccountMapper.selectByEndUserId(carrierIdAndCompanyIdAndEndUserId).getAccountId();
        }
        //0开户中  1 开户成功  2开户失败
        TZtAccountOpenInfoExample ztAccountOpenInfoExample = new TZtAccountOpenInfoExample();
        TZtAccountOpenInfoExample.Criteria cr1 = ztAccountOpenInfoExample.createCriteria();
        cr1.andAccountIdEqualTo(accountId);
        List<TZtAccountOpenInfo> ztAccountOpenInfoList = tZtAccountOpenInfoMapper.selectByExample(ztAccountOpenInfoExample);
        Integer state = 0;
        if (ztAccountOpenInfoList.size() > 0) {
            if (ztAccountOpenInfoList.get(0).getStatus() == 1) {
                state = 1;
            } else if (ztAccountOpenInfoList.get(0).getStatus() == 0) {
                state = 0;
            } else if (ztAccountOpenInfoList.get(0).getStatus() == 2) {
                state = 2;
            }
        } else {
            state = 3;
        }
        TZtAccountOpenInfo tZtAccountOpenInfo = ztAccountOpenInfoList.get(0);
        tZtAccountOpenInfo.setStatus(state);
        return tZtAccountOpenInfo;
    }

    @Override
    public TZtAccountOpenInfo selectByAccountId(Integer accountId, String userOpenRole) {
        return tZtAccountOpenInfoMapper.selectByAccountId(accountId,userOpenRole);
    }

    @Override
    public ResultUtil selectNotByAccountId(TZtAccountOpenInfo ztAccountOpenInfo) {
        List<TZtAccountOpenInfo> ztAccountOpenInfoList2 = tZtAccountOpenInfoMapper.selectNotByAccountId(ztAccountOpenInfo);
        if (ztAccountOpenInfoList2.size() > 0) {
            return ResultUtil.error();
        }
        return ResultUtil.ok();
    }

    @Override
    public TZtAccountOpenInfo selectOrderOpenInfoByAccountId(Integer enduserId) {
        return tZtAccountOpenInfoMapper.selectOrderOpenInfoByAccountId(enduserId);
    }

    @Override
    public List<TZtAccountOpenInfo> selectOrderOpenInfoListByAccountId(Integer enduserId) {
        return tZtAccountOpenInfoMapper.selectOrderOpenInfoListByAccountId(enduserId);
    }

}
