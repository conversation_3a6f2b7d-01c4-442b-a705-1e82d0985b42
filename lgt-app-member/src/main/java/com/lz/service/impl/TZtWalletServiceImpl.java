package com.lz.service.impl;

import cn.hutool.json.JSONUtil;
import com.jd.jr.jropen.unifySdk.respModel.OpenMemberUnbindAccountResponse;
import com.lz.common.config.RedisUtil;
import com.lz.common.dbenum.*;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.CodeEnum;
import com.lz.common.model.hxPayment.request.bank.OpenMemberBindAccountReq;
import com.lz.common.model.hxPayment.request.bank.OpenMemberUnbindAccountReq;
import com.lz.common.model.hxPayment.response.bank.OpenMemberBindAccountResp;
import com.lz.common.model.hxPayment.response.bank.OpenMemberUnbindAccountResp;
import com.lz.common.model.jdPayment.util.CloudPayFormatUtil;
import com.lz.common.util.*;
import com.lz.dao.*;
import com.lz.example.TZtAccountOpenInfoExample;
import com.lz.example.TZtBankBindRelationshipExample;
import com.lz.example.TZtBankCardExample;
import com.lz.hxpay.CloudPaymentAPI;
import com.lz.model.*;
import com.lz.service.TZtWalletService;
import com.lz.vo.*;
import commonSdk.responseModel.OpenMemberBindAccountResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.*;

/**
 *  @author: dingweibo
 *  @Date: 2021/8/9 10:03
 *  @Description: 京东钱包
 */
@Service("tZtWalletService")
@Slf4j
public class TZtWalletServiceImpl implements TZtWalletService {
    @Resource
    private TZtAccountOpenInfoMapper tZtAccountOpenInfoMapper;
    @Resource
    private  TZtBankCardMapper tZtBankCardMapper;
    @Resource
    private TZtBankUserMapper tZtBankUserMapper;
    @Resource
    private TZtBankBindRelationshipMapper tZtBankBindRelationshipMapper;

    @Resource
    private TCarrieAccountMapper tCarrieAccountMapper;
    @Resource
    private TCompanyAccountMapper tCompanyAccountMapper;
    @Resource
    private TCarrierInfoMapper tCarrierInfoMapper;
    @Resource
    private TCompanyInfoMapper tCompanyInfoMapper;
    @Autowired
    private CloudPaymentAPI cloudPaymentAPI;

    @Value("${hxyhPartnerId}")
    private String hxyhPartnerId;
    @Value("${hxyhChannelId}")
    private String hxyhChannelId;
    @Autowired
    private RedisUtil redisUtil;

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/10 16:26
     *  @Description: 对公账户管理页面（承运方与企业）
     */
    @Override
    public ResultUtil hxBankCardList(TZtBankCardVo record) {
        List<TZtBankCardVo> tZtBankCardVoList = tZtBankUserMapper.hxBankCardList(record.getAccountId(),record.getUserOpenRole());
        return ResultUtil.ok(tZtBankCardVoList);
    }
    /**
     *  @author: dingweibo
     *  @Date: 2021/8/9 10:04
     *  @Description: 企业或承运方绑卡
     */
    @Override
    @Transactional
    public ResultUtil bindingBank(TZtBankCardVo record) {
        TZtAccountOpenInfo tZtAccountOpenInfo = tZtAccountOpenInfoMapper.selectByAccountId(record.getAccountId(),record.getUserOpenRole());
        if(tZtAccountOpenInfo.getStatus()!=1){
            return ResultUtil.error("未开户成功不允许绑定银行卡");
        }
        List<TZtBankCard> tZtBankCardResultList = tZtBankUserMapper.getTZtBankCardListByCardNo(record.getAcctNo(), record.getAccountId(),null);
        if(tZtBankCardResultList.size()>0){
            return ResultUtil.error("此卡已绑定，请勿重复绑定！");
        }
        int bankNum = tZtBankUserMapper.selectByBankNumAndByStatus(record.getAccountId());
        if(bankNum==1){
            return ResultUtil.error("仅支持添加1个对公账户，可删除无用对公户后，再添加。");
        }
        //请求华夏银行添加银行卡
        String  cardNo = "";
        if(record.getUserOpenRole().equals(DictEnum.PF.code) || record.getUserOpenRole().equals(DictEnum.CA.code)){
            TCarrieAccount tCarrieAccount = tCarrieAccountMapper.getByAccountId(record.getAccountId());
            TCarrierInfo tCarrierInfo = tCarrierInfoMapper.selectByPrimaryKey(tCarrieAccount.getCarrierId());
            cardNo = tCarrierInfo.getBusinessLicenseNo();
        }else if(record.getUserOpenRole().equals(DictEnum.BD.code)){
            TCompanyAccount tCompanyAccount = tCompanyAccountMapper.selectByAccount(record.getAccountId());
            TCompanyInfo tCompanyInfo = tCompanyInfoMapper.selectByPrimaryKey(tCompanyAccount.getCompanyId());
            cardNo = tCompanyInfo.getBusinessLicenseNo();
        }
        OpenMemberBindAccountReq request = new OpenMemberBindAccountReq();
        request.setPartnerId(hxyhPartnerId);
        request.setRequestId(IdWorkerUtil.getInstance().nextId());
        request.setRequestTime(DateUtils.getRequestTime());
        request.setCurrency(DictEnum.CNY.code);
        request.setChannelId(hxyhChannelId);
        request.setPartnerAccId(tZtAccountOpenInfo.getPartnerAccId());
        request.setOperType(1);
        request.setOccBankAccount(record.getAcctNo());
        request.setOccBankAccountName(record.getAcctName());
        request.setOccBankPhone(record.getOccBankPhone());
        request.setOccBankName(record.getBankName());
        request.setOccBankCode(record.getBankCode());
        request.setAcctUnionBankCode(record.getBankCodeNo());
        request.setCardType(16);
        request.setCardNo(cardNo);
        request.setLinkAccountType(0);
        request.setIsOther(1);
        request.setAccountSign(1);
        request.setIsOtherBank(record.getIsOtherBank());
        request.setIsSecondAcc(0);
        request.setStrideValidate(0);
        log.info("华夏银行对公绑卡入参：{}",request);
        ResultUtil resultUtil = cloudPaymentAPI.execute(request);
        log.info("华夏支付 - 华夏银行对公绑卡回参，{}", JSONUtil.toJsonStr(resultUtil));
        OpenMemberBindAccountResp response = CloudPayFormatUtil.ObjToBean(resultUtil, OpenMemberBindAccountResp.class);;
        log.info("华夏银行对公绑卡回参：{}",response);
        TZtBankBindRelationship tZtBankBindRelationship = new TZtBankBindRelationship();
        tZtBankBindRelationship.setAccountOpenId(tZtAccountOpenInfo.getId());
        tZtBankBindRelationship.setRequestId(request.getRequestId());
        if("00000".equals(response.getResponseCode())){
            TZtBankCard tZtBankCardResult = tZtBankCardMapper.selectByAcctNo(record.getAcctNo());
            Integer bankId = null;
            if(null == tZtBankCardResult){
                TZtBankCard tZtBankCard = new TZtBankCard();
                tZtBankCard.setAcctNo(record.getAcctNo());
                tZtBankCard.setAcctName(record.getAcctName());
                tZtBankCard.setAcctCard(record.getAcctCard());
                tZtBankCard.setBankName(record.getBankName());
                tZtBankCard.setBankCode(record.getBankCode());
                tZtBankCard.setOccBankPhone(record.getOccBankPhone());
                tZtBankCard.setBankCodeNo(record.getBankCodeNo());
                tZtBankCard.setAcctCard(request.getCardNo());
                tZtBankCard.setCreateUser(CurrentUser.getUserNickname());
                tZtBankCard.setCreateTime(new Date());
                tZtBankCard.setEnable(false);
                tZtBankCardMapper.insertSelective(tZtBankCard);
                bankId = tZtBankCard.getId();
            }else{
                bankId = tZtBankCardResult.getId();
            }
            TZtBankUser tZtBankUser = new TZtBankUser();
            tZtBankUser.setIsDefault(true);
            tZtBankUser.setIsOneself(true);
            tZtBankUser.setAccountId(record.getAccountId());
            tZtBankUser.setBankId(bankId);
            tZtBankUser.setCreateUser(CurrentUser.getUserNickname());
            tZtBankUser.setCreateTime(new Date());
            tZtBankUser.setEnable(false);
            tZtBankUserMapper.insertSelective(tZtBankUser);
            tZtBankBindRelationship.setLinkAccountType(0);
            tZtBankBindRelationship.setAccountBankId(tZtBankUser.getId());
            tZtBankBindRelationship.setBindStatus(JdEnum.BINDING.code);
            tZtBankBindRelationship.setRequestCode("0");
            tZtBankBindRelationshipMapper.insertSelective(tZtBankBindRelationship);
            Map<String, Object> map2 = new HashMap<>();
            map2.put("bankName", record.getAcctName());//姓名
            map2.put("id",bankId);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), map2, "银行卡添加成功");
        }else{
            tZtBankBindRelationship.setBindStatus(JdEnum.BIND_FAIL.code);
            tZtBankBindRelationship.setRequestCode("2");
            tZtBankBindRelationship.setRequestMessage("绑卡失败:"+response.getResponseDesc());
            tZtBankBindRelationshipMapper.insertSelective(tZtBankBindRelationship);
            Map<String, Object> map2 = new HashMap<>();
            map2.put("bankName", record.getAcctName());//姓名
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), map2, "银行卡添加失败:"+response.getResponseDesc());
        }
    }


    /**
     *  @author: dingweibo
     *  @Date: 2022/11/9 15:39
     *  @Description: 承运方或企业解绑银行卡
     */
    @Override
    public ResultUtil unbindBank(TZtBankCardVo record) {
        TZtAccountOpenInfo tZtAccountOpenInfo = tZtAccountOpenInfoMapper.selectByAccountId(record.getAccountId(),record.getUserOpenRole());
        TZtBankUser tZtBankUser = tZtBankUserMapper.selectByBankIdAndAccountId(record.getId(),record.getAccountId());
        TZtBankBindRelationshipExample example = new TZtBankBindRelationshipExample();
        TZtBankBindRelationshipExample.Criteria cr = example.createCriteria();
        cr.andAccountBankIdEqualTo(tZtBankUser.getId());
        cr.andAccountOpenIdEqualTo(tZtAccountOpenInfo.getId());
        TZtBankBindRelationship tZtBankBindRelationship = tZtBankBindRelationshipMapper.selectByExample(example).get(0);
        if(JdEnum.BINDING.code.equals(tZtBankBindRelationship.getBindStatus())){
            return ResultUtil.error("当前银行卡正在绑卡处理中不允许解绑！");
        }
        OpenMemberUnbindAccountReq request = new OpenMemberUnbindAccountReq();
        request.setPartnerId(hxyhPartnerId);
        request.setRequestId(IdWorkerUtil.getInstance().nextId());
        request.setRequestTime(DateUtils.getRequestTime());
        request.setChannelId(hxyhChannelId);
        request.setBankNo(tZtBankBindRelationship.getBankNo());
        request.setPartnerAccId(tZtAccountOpenInfo.getPartnerAccId());
        log.info("华夏银行对公解绑入参：{}",request);
        ResultUtil resultUtil = cloudPaymentAPI.execute(request);
        log.info("华夏支付 - 华夏银行对公解绑回参，{}", JSONUtil.toJsonStr(resultUtil));
        OpenMemberUnbindAccountResp response = CloudPayFormatUtil.ObjToBean(resultUtil, OpenMemberUnbindAccountResp.class);;
        log.info("华夏银行对公解绑回参：{}",response);
        if("00000".equals(response.getResponseCode())){
            tZtBankUser.setEnable(true);
            tZtBankUserMapper.updateByPrimaryKeySelective(tZtBankUser);
            tZtBankBindRelationship.setRequestMessage("解绑处理中");
            tZtBankBindRelationshipMapper.updateByPrimaryKeySelective(tZtBankBindRelationship);
            return ResultUtil.ok("解绑处理中");
        }else{
            return ResultUtil.error("解绑失败:"+response.getResponseDesc());
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/19 15:07
     *  @Description: PC端经纪人银行卡列表
     */
    @Override
    public ResultUtil agentHxBankCardList(TZtBankCardVo record) {
        List<ImperfectTZtBankCardVO> caList = tZtBankUserMapper.selectImperfectBankCardList(record.getAccountId(),DictEnum.CTYPEAGENTPERSON.code);
        for(ImperfectTZtBankCardVO tBankCard : caList){
            if(tBankCard.getAcctCard()==null  || !tBankCard.getAcctCard().equals(tBankCard.getIdcard())){
                if(tBankCard.getAcctName() == null || tBankCard.getAcctCard() == null || tBankCard.getOccBankPhone() == null){
                    tBankCard.setPerfectState("NOPERFECT");
                }
            }
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), caList);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/19 15:14
     *  @Description: 经纪人绑卡
     */
    @Override
    public ResultUtil agentBindingBank(TZtBankCardVo record) {
        TZtAccountOpenInfoExample tZtAccountOpenInfoExample = new TZtAccountOpenInfoExample();
        TZtAccountOpenInfoExample.Criteria openCr = tZtAccountOpenInfoExample.createCriteria();
        openCr.andAccountIdEqualTo(record.getAccountId());
        openCr.andUserOpenRoleEqualTo(DictEnum.CD.code);
        List<TZtAccountOpenInfo> tZtAccountOpenInfoList =tZtAccountOpenInfoMapper.selectByExample(tZtAccountOpenInfoExample);
        if(tZtAccountOpenInfoList.size()<1){
            return ResultUtil.error("华夏银行未开户不允许添加银行卡");
        }
        TZtAccountOpenInfo tZtAccountOpenInfo = tZtAccountOpenInfoList.get(0);
        if(tZtAccountOpenInfo.getStatus()!=1){
            return ResultUtil.error("未开户成功不允许添加银行卡");
        }
        List<TZtBankBindRelationship> tZtBankBindRelationshipList = tZtBankBindRelationshipMapper.selectByBankUserIdAndAccountOpenId(record.getAccountId());
        if(tZtBankBindRelationshipList.size()==4){
            return ResultUtil.error("用户只可绑定四张银行卡");
        }
        Map<String,Object> map = AliPayCardDetailUtil.getCardDetail(record.getAcctNo());
        //请求华夏银行添加银行卡
        OpenMemberBindAccountReq request = new OpenMemberBindAccountReq();
        request.setPartnerId(hxyhPartnerId);
        request.setRequestId(IdWorkerUtil.getInstance().nextId());
        request.setRequestTime(DateUtils.getRequestTime());
        request.setCurrency(DictEnum.CNY.code);
        request.setChannelId(hxyhChannelId);
        request.setPartnerAccId(tZtAccountOpenInfo.getPartnerAccId());
        request.setOperType(1);
        request.setOccBankAccount(record.getAcctNo());
        request.setOccBankAccountName(record.getAcctName());
        request.setOccBankPhone(record.getOccBankPhone());
        request.setOccBankCode(String.valueOf(map.get("key")));
        request.setOccBankName(String.valueOf(map.get("value")));
        request.setCardType(10);
        request.setCardNo(record.getAcctCard());
        request.setIsOther(2);
        request.setAccountSign(0);
        request.setIsSecondAcc(0);
        request.setStrideValidate(0);
        if(String.valueOf(map.get("key")).equals("HXBANK")){
            request.setIsOtherBank(0);
        }else{
            request.setIsOtherBank(1);
        }
        log.info("华夏银行经纪人绑卡入参：{}",request);
        ResultUtil resultUtil = cloudPaymentAPI.execute(request);
        log.info("华夏支付 - 华夏银行经纪人绑卡回参，{}", JSONUtil.toJsonStr(resultUtil));
        OpenMemberBindAccountResp response = CloudPayFormatUtil.ObjToBean(resultUtil, OpenMemberBindAccountResp.class);;
        log.info("华夏银行经纪人绑卡回参：{}",response);
        TZtBankBindRelationship tZtBankBindRelationship = new TZtBankBindRelationship();
        tZtBankBindRelationship.setAccountOpenId(tZtAccountOpenInfo.getId());
        tZtBankBindRelationship.setRequestId(request.getRequestId());
        tZtBankBindRelationship.setLinkAccountType(request.getLinkAccountType());
        if("00000".equals(response.getResponseCode())){
            List<TZtBankCard> bankCardList = tZtBankUserMapper.getTZtBankCardListByCardNo(null,record.getAccountId(),null);
            TZtBankCardExample tZtBankCardExample = new TZtBankCardExample();
            TZtBankCardExample.Criteria cr = tZtBankCardExample.createCriteria();
            cr.andAcctNoEqualTo(record.getAcctNo());
            cr.andEnableEqualTo(false);
            List<TZtBankCard> tZtBankCards = tZtBankCardMapper.selectByExample(tZtBankCardExample);
            TZtBankUser tZtBankUser = new TZtBankUser();
            Integer bankId = null;
            if(tZtBankCards.size()<1) {
                TZtBankCard tZtBankCard = new TZtBankCard();
                tZtBankCard.setAcctNo(record.getAcctNo());
                tZtBankCard.setAcctName(record.getAcctName());
                tZtBankCard.setAcctCard(record.getAcctCard());
                tZtBankCard.setBankCode(String.valueOf(map.get("key")));
                tZtBankCard.setBankName(String.valueOf(map.get("value")));
                tZtBankCard.setOccBankPhone(record.getOccBankPhone());
                tZtBankCard.setCreateUser(CurrentUser.getUserNickname());
                tZtBankCard.setCreateTime(new Date());
                tZtBankCard.setEnable(false);
                if (bankCardList.size() == 0) {//第一张银行卡是默认的
                    tZtBankUser.setIsDefault(true);
                } else if (bankCardList.size() != 0) {//不是第一次添加，判断选择的是不是默认的
                    if (record.getIsDefault() != null && record.getIsDefault()) {
                        tZtBankUserMapper.updateByAccountId(record.getAccountId());
                    }

                }
                tZtBankCardMapper.insertSelective(tZtBankCard);
                bankId = tZtBankCard.getId();
            }else{
                TZtBankCard  tZtBankCard= tZtBankCards.get(0);
                bankId = tZtBankCard.getId();
                tZtBankCard.setOccBankPhone(record.getOccBankPhone());
                tZtBankCardMapper.updateByPrimaryKeySelective(tZtBankCard);

            }
            tZtBankUser.setIsDefault(record.getIsDefault() ? true : false);
            tZtBankUser.setIsOneself(record.getIsOneself());
            tZtBankUser.setAccountId(record.getAccountId());
            tZtBankUser.setBankId(bankId);
            tZtBankUser.setCreateUser(CurrentUser.getUserNickname());
            tZtBankUser.setCreateTime(new Date());
            tZtBankUser.setEnable(false);
            tZtBankUserMapper.insertSelective(tZtBankUser);
            tZtBankBindRelationship.setAccountBankId(tZtBankUser.getId());
            tZtBankBindRelationship.setBindStatus(JdEnum.BINDING.code);
            tZtBankBindRelationship.setRequestMessage("绑卡处理中");
            tZtBankBindRelationship.setRequestCode("0");
            tZtBankBindRelationshipMapper.insertSelective(tZtBankBindRelationship);
            Map<String, Object> map2 = new HashMap<>();
            map2.put("bankName", record.getAcctName());//姓名
            map2.put("id",bankId);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), map2, "银行卡绑卡处理中");
        }else{
            tZtBankBindRelationship.setBindStatus(JdEnum.BIND_FAIL.code);
            tZtBankBindRelationship.setRequestCode("2");
            tZtBankBindRelationship.setRequestMessage("绑卡失败:"+response.getResponseDesc());
            tZtBankBindRelationshipMapper.insertSelective(tZtBankBindRelationship);
            Map<String, Object> map2 = new HashMap<>();
            map2.put("bankName", record.getAcctName());//姓名
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), map2, "银行卡添加失败:"+response.getResponseDesc());
        }
    }

}
