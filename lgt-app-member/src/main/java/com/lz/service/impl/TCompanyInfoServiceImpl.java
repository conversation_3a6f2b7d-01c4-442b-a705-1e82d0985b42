package com.lz.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.codingapi.txlcn.tc.annotation.LcnTransaction;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lz.api.*;
import com.lz.common.config.RedisUtil;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.CodeEnum;
import com.lz.common.model.TCompanyInfoDTO;
import com.lz.common.util.*;
import com.lz.dao.*;
import com.lz.dto.*;
import com.lz.model.*;
import com.lz.service.TCompanyInfoService;
import com.lz.service.UserRegisterCommonService;
import com.lz.system.api.CityInfoAPI;
import com.lz.system.api.DicCatItemAPI;
import com.lz.system.api.OperOrgAPI;
import com.lz.system.api.SystemAPI;
import com.lz.system.model.TSysRole;
import com.lz.system.model.TSysUser;
import com.lz.system.model.TUserInfo;
import com.lz.system.model.TUserRole;
import com.lz.system.util.ProvinceCityCounty;
import com.lz.system.vo.DicItemVO;
import com.lz.system.vo.TSysUserAddVO;
import com.lz.system.vo.TSysUserVO;
import com.lz.system.vo.org.OperOrgAddVO;
import com.lz.system.vo.org.OperOrgUpdateVO;
import com.lz.tpu.api.PaymentAPI;
import com.lz.tpu.api.UserRegisterAPI;
import com.lz.tpu.web.reqeuest.UserRequestVo;
import com.lz.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.omg.PortableServer.LIFESPAN_POLICY_ID;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2019/4/15 - 19:20
 **/
@Slf4j
@Service
public class TCompanyInfoServiceImpl implements TCompanyInfoService {

    @Resource
    private TCompanyInfoMapper companyInfoMapper;

    @Resource
    private TAccountMapper accountMapper;

    @Resource
    private TEmployeeMapper employeeMapper;

    @Resource
    private TCompanyAccountMapper companyAccountMapper;

    @Autowired
    private SystemAPI systemAPI;

    @Autowired
    private DicCatItemAPI dicCatItemAPI;

    @Autowired
    private ProjectInvoiceService projectInvoiceService;

    @Autowired
    private OperOrgAPI operOrgAPI;

    @Autowired
    private CompanyProjectAPI companyProjectAPI;

    @Resource
    private TWalletMapper walletMapper;

    @Resource
    private WalletChangeLogAPI walletChangeLogAPI;

    @Autowired
    private CarrierCompanyRelService carrierCompanyRelService;

    @Autowired
    private CarrierService carrierService;

    @Autowired
    private UserRegisterAPI userRegisterAPI;

    @Autowired
    private PaymentAPI paymentAPI;

    @Autowired
    private  TCarrierInfoMapper tCarrierInfoMapper;

    @Autowired
    private  TBusinessBasicMapper tBusinessBasicMapper;

    @Autowired
    private  TBusinessCompanyMapper tBusinessCompanyMapper;

    @Autowired
    private UserRegisterCommonService userRegisterCommon;
    @Autowired
    private TUserScoreInfoAPI tUserScoreInfoAPI;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private CityInfoAPI cityInfoAPI;

    /** 省市县Redis Key */
    private static final String SSX_KEY = "ProviceCityCountry";

    @Override
    public TCompanyInfo selectByPrimaryKey(Integer id) {
        return companyInfoMapper.selectByPrimaryKey(id);
    }

    /**
     * 根据企业ID查询企业信息
     * @param companyInfoQuery
     * @return
     */
    @Override
    public ResultUtil selectCompanyInfoById(TCompanyInfoQuery companyInfoQuery) {
        TCompanyInfo tCompanyInfo = companyInfoMapper.selectCompanyInfoById(companyInfoQuery.getCompanyId());
        return ResultUtil.ok(tCompanyInfo);
    }

    /**
     * 根据Sysuser表Id 查询所属企业
     * @param userId
     * @return
     */
    @Override
    public ResultUtil selectCompanyIdBySysUserId(Integer userId) {
        List<TCompanyInfoDTO> tCompanyInfos = companyInfoMapper.selectCompanyBySystemId(userId);
        return ResultUtil.ok(tCompanyInfos);
    }

    /**
     * 根据用户id 查询所属企业id
     * Yan
     * @param userId
     * @return
     */
    @Override
    public ResultUtil selectCompanyIdByUserId(Integer userId) {
        List<TCompanyInfo> tCompanyInfos = companyInfoMapper.selectCompanyByUserId(userId);
        return ResultUtil.ok(tCompanyInfos);
    }

    @Override
    public ResultUtil selectBusinessLeader() {
        List<BusinessDTO> businessDTOList = tBusinessBasicMapper.selectBusinessLeaders();
        return ResultUtil.ok(businessDTOList);
    }

    @Override
    public ResultUtil selectByPage(TCompanyInfoQuery companyInfoQuery) {
        Page<Object> page = PageHelper.startPage(companyInfoQuery.getPage(), companyInfoQuery.getSize());
        List<CompanyInfoDTO> list = companyInfoMapper.selectByPage(companyInfoQuery);
        ResultUtil resultUtil = ResultUtil.ok();
        resultUtil.setCount(page.getTotal());
        resultUtil.setData(list);
        return resultUtil;
    }

    @Override
    public ResultUtil selectCompanyOpenInfoList(CompanyOpenInfoListVO companyOpenInfoListVO) {
        Page<Object> page = PageHelper.startPage(companyOpenInfoListVO.getPage(), companyOpenInfoListVO.getSize());
        List<CompanyOpenInfoDTO> list = companyInfoMapper.selectCompanyOpenInfoList(companyOpenInfoListVO);
        ResultUtil resultUtil = ResultUtil.ok();
        resultUtil.setCount(page.getTotal());
        resultUtil.setData(list);
        return resultUtil;
    }

    @Override
    public ResultUtil applyCompanyOpenInfo(CompanyOpenInfoDetailsVO companyOpenInfoDetailsVO) {
        CompanyOpenInfoDetailsDTO list = companyInfoMapper.applyCompanyOpenInfo(companyOpenInfoDetailsVO);
        return ResultUtil.ok(list);
    }

    public ResultUtil enterpriseExport(TCompanyInfoQuery companyInfoQuery){
        List<CompanyInfoDTO> list = companyInfoMapper.enterpriseExport(companyInfoQuery);
        Map<String,Object> map = new HashMap<>();
        String[] headers ={ "企业名称","业务部信息","管理员账号","营业执照号","企业法人","企业联系人","联系人电话","管理员姓名","项目数","线路数","审核状态","审核意见","注册时间","综合评分"};
        String[] names ={"companyName","businessName","accountNo","businessLicenseNo","companyLegalPerson","companyContacts","companyContactsPhone","nickname","projectNumber","lineNumber","auditStatusName","auditOpinion","registrationTime","score"};
        map.put("headers",headers);
        map.put("names",names);
        map.put("list",list);
        return  ResultUtil.ok(map);
    }

    @Override
    public ResultUtil selectById(Integer companyId) {
        Map<String, Object> res = new HashMap<>();
        res.put("companyId", companyId);
        //业务部负责人信息

        TBusinessCompany tBusinessCompany = tBusinessCompanyMapper.selectBycompanyId(companyId);
        if (null!=tBusinessCompany){
            res.put("businessId",tBusinessCompany.getBusinessBasicId());
        }
        //企业信息
        TCompanyInfo companyInfo = companyInfoMapper.selectById(companyId);
        String jsonString = JSONObject.toJSONString(companyInfo);
        HashMap companyInfoHashMap = JSON.parseObject(jsonString, HashMap.class);
        String[] paramArray = null;
        if(companyInfo.getParam1()!=null&& !"".equals(companyInfo.getParam1())){
            paramArray =  companyInfo.getParam1().split(",");
        }
        companyInfoHashMap.put("paramArray",paramArray);
        //开票信息
        SearchProjectInvoiceVO projectInvoiceVO = new SearchProjectInvoiceVO();
        projectInvoiceVO.setProjectId(0);
        projectInvoiceVO.setCompanyId(companyId);
        ResultUtil resultUtil = projectInvoiceService.searchProjectInvoiceByProjectId(projectInvoiceVO);
        HashMap projectInvoiceInfoDTO = (HashMap) resultUtil.getData();
        if (null != projectInvoiceInfoDTO) {
            //如果开票信息不为空，返回主键id，修改时使用id
            Integer projectInvoiceInfoId = (Integer) projectInvoiceInfoDTO.get("id");
            res.put("projectInvoiceInfoId", projectInvoiceInfoId);
            res.putAll(projectInvoiceInfoDTO);
        }else {
            res.put("projectInvoiceInfoId", 1);
        }

        //企业管理员信息
        ManageAccountDTO manageAccount = accountMapper.selectManageAccount(companyId);
        if(null != manageAccount){
            HashMap manageAccountDTO = new HashMap();
            manageAccountDTO.put("accountId", manageAccount.getAccountId());
            manageAccountDTO.put("nickname", manageAccount.getNickname());
            manageAccountDTO.put("accountEmail", manageAccount.getAccountEmail());
            manageAccountDTO.put("accountNo", manageAccount.getAccountNo());
            manageAccountDTO.put("userId",manageAccount.getUserId());
            res.putAll(manageAccountDTO);

        }
        TUserScoreInfoVO tuserscore =new TUserScoreInfoVO();
        tuserscore.setUserType("BD");
        tuserscore.setCompanyId(companyId);
        ResultUtil resultUtil1= tUserScoreInfoAPI.selectDriverScore(tuserscore);
        res.put("score",resultUtil1.getData());
        //TODO 企业与承运方的合同(项目)
        /*SearchProjectVO searchProjectVO = new SearchProjectVO();
        searchProjectVO.setCompanyId(companyId);
        ResultUtil projectResult = companyProjectAPI.selectCarrierCompanyProject(searchProjectVO);
        ArrayList projectResultData = (ArrayList) projectResult.getData();
        companyInfoHashMap.put("contract", projectResultData);*/
        res.putAll(companyInfoHashMap);
        return ResultUtil.ok(res);
    }

    @Override
    public ResultUtil selectByBusinessId(Integer id, String companyName) {
        List<CompanyInfoDTO> businessDTOList = companyInfoMapper.selectByBusinessIdOrCompanyName(id, companyName);
        if (null == businessDTOList || businessDTOList.isEmpty()) {
            List<CompanyInfoDTO> companyInfoDTOS = tBusinessBasicMapper.selectLeaderCompany(id, companyName);
            return ResultUtil.ok(companyInfoDTOS);
        }
        return ResultUtil.ok(businessDTOList);
    }
    @Override
    public List<TCompanyInfo> selectByCompanyName(String companyName) {
        List<TCompanyInfo> tCompanyInfoList= companyInfoMapper.selectByCompanyName(companyName);
        return tCompanyInfoList;
    }

    @Override
    public TCompanyInfo selectByCompanyIdAndAccountId(Integer companyId){
        return companyInfoMapper.selectByPrimaryKey(companyId);
    }
    @Override
    public ResultUtil selectBusiness(Integer id) {
        List<BusinessDTO> businessDTOList = tBusinessBasicMapper.selectBusiness(id);
        if (businessDTOList.size()>0){
            for (BusinessDTO businessDTO :businessDTOList){
                businessDTO.setBusinessUsername(businessDTO.getBusinessUsername()+"-"+businessDTO.getAccountNo());
            }
        }
        return ResultUtil.ok(businessDTOList);
    }

    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil save(TCompanyInfoAddVO companyInfoAddVO) {
        TCompanyInfoQuery companyInfoQuery = new TCompanyInfoQuery();
        companyInfoQuery.setCompanyName(companyInfoAddVO.getCompanyName());

        List<TCompanyInfo> list = companyInfoMapper.selectRepeatCompany(companyInfoQuery);
        if (list != null && list.size() >0){
            return ResultUtil.error(companyInfoAddVO.getCompanyName() + "已存在");
        }
        //TODO 查询账号是否存在 已完成
        TAccount accounts = new TAccount();
        accounts.setAccountNo(companyInfoAddVO.getAccountNo());
        List<TAccount> tAccounts = accountMapper.selectRepeatAccountNo(accounts);
        if (null != tAccounts && tAccounts.size() >0){
            return ResultUtil.error("账号已存在");
        }
        //保存企业信息
        TCompanyInfo companyInfo = new TCompanyInfo();
        BeanUtils.copyProperties(companyInfoAddVO, companyInfo);
        //纳税人识别号
        companyInfo.setTaxpayerNumber(companyInfoAddVO.getTaxpayerNumber());
        companyInfo.setEnable(false);
        companyInfo.setCompanyLogogram(PinyinUtil.toPinyin(companyInfo.getCompanyName()));

        companyInfo.setIfOnlineSign(false);

        //合同照片 2019/7/3 11:19 dwb（添加合同照片）
        String param = "";
        if(companyInfoAddVO.getParamArray()!=null && companyInfoAddVO.getParamArray().length>0){
            for(int i=0;i<companyInfoAddVO.getParamArray().length;i++){//多张合同照片
               param+= companyInfoAddVO.getParamArray()[i]+",";
            }
        }
        companyInfo.setParam1(param);//合同照片放备用字段
        if (StringUtils.isNotEmpty(companyInfoAddVO.getTaxpayerNumber())){
            companyInfo.setTaxNo(companyInfoAddVO.getTaxpayerNumber());
        }
        companyInfoMapper.insertSelective(companyInfo);

        //添加业务部负责人与企业关系
        TBusinessCompany tBusinessCompany = new TBusinessCompany();
        tBusinessCompany.setBusinessBasicId(companyInfoAddVO.getBusinessId());
        tBusinessCompany.setCompanyId(companyInfo.getId());
        tBusinessCompanyMapper.insertSelective(tBusinessCompany);

        //TODO 创建机构 已完成
        OperOrgAddVO operOrg = new OperOrgAddVO();
        operOrg.setName(companyInfoAddVO.getCompanyName());
        operOrg.setEnable(false);
        operOrg.setPid(0);
        operOrg.setParam1(String.valueOf(companyInfo.getId()));

        ResultUtil addOperOrg = operOrgAPI.add(operOrg);
        if (addOperOrg.getCode().equals("error")){
            return addOperOrg;
        }
        LinkedHashMap operOrgData = (LinkedHashMap) addOperOrg.getData();
        Integer orgId = (Integer) operOrgData.get("id");

        //保存系统用户
        TSysUserAddVO user = new TSysUserAddVO();
        user.setUsername(companyInfoAddVO.getAccountNo());
        user.setNickname(companyInfoAddVO.getNickname());
        user.setPassword("123456");
        user.setIfMainAccount(true);
        user.setAccountNo(companyInfoAddVO.getAccountNo());
        user.setEnable(false);
        //TODO 账号类型(手机号)、用户类型(B端用户)、注册来源(运营PC)、注册节点(待审核) 已完成
        user.setAcctype("PhoneNo");
        user.setUsertype("BD");
        user.setDatafrom("OPPC");
        user.setRegnode("PAWRCOM");
        user.setOrgId(orgId);
        user.setEmail(companyInfoAddVO.getAccountEmail());
        user.setIfPasswordSecurity(false);

        //TODO 企业管理员的角色, 使用字典表item_code,查出当前企业管理员角色的id 已完成
        DicItemVO dicItemVO = new DicItemVO();
        dicItemVO.setCode("COMPANYROLE");
        ResultUtil resultUtil = dicCatItemAPI.dictSelect(dicItemVO);
        ArrayList data = (ArrayList) resultUtil.getData();
        LinkedHashMap o = (LinkedHashMap) data.get(0);
        String adminRoleId = String.valueOf(o.get("name"));
        Integer roleID = Integer.valueOf(adminRoleId);
        Integer[] roles = new Integer[1];
        roles[0] = roleID;
        user.setRoleIds(roles);
        ResultUtil saveUser = systemAPI.addSave(user);
        String code = saveUser.getCode();
        if (code.equals("error")){
            throw new RuntimeException(saveUser.getMsg());
        }
        LinkedHashMap userSave = (LinkedHashMap) saveUser.getData();
        Integer userId = (Integer) userSave.get("id");

        //添加默认管理员账号
        TSysRole tSysRole =  systemAPI.selectByParam("BADMIN");
        if(tSysRole!=null){
            TUserRole tUserRole = new TUserRole();
            tUserRole.setUserId(userId);
            tUserRole.setRoleId(tSysRole.getId());
            systemAPI.saveUserRole(tUserRole);
        }

        //保存管理员
        TAccount account = new TAccount();
        account.setNickname(companyInfoAddVO.getNickname());
        //账号类型，管理员默认手机号
        account.setAcctype("PhoneNo");
        account.setUsertype("BD");
        account.setDatafrom("OPPC");
        account.setRegnode("PAWRCOM");
        account.setAccountNo(companyInfoAddVO.getAccountNo());
        account.setPassword(DigestUtils.md5Hex("123456"));
        account.setIfMainAccount(true);
        account.setUserId(userId);
        account.setEnable(false);
        account.setEmail(companyInfoAddVO.getAccountEmail());
        accountMapper.insertSelective(account);

        //修改企业信息：添加管理员account_id
        TCompanyInfo updateCompanyMainAccount = new TCompanyInfo();
        updateCompanyMainAccount.setId(companyInfo.getId());
        updateCompanyMainAccount.setAccountInfoId(account.getId());
        companyInfoMapper.updateByPrimaryKeySelective(updateCompanyMainAccount);


        //保存开票信息
        TProjectInvoiceInfo projectInvoiceInfo = new TProjectInvoiceInfo();
        BeanUtils.copyProperties(companyInfoAddVO, projectInvoiceInfo);
        projectInvoiceInfo.setCompanyId(companyInfo.getId());
        projectInvoiceInfo.setProjectId(0);
        projectInvoiceInfo.setEnable(false);
        projectInvoiceService.insertProjectInvoice(projectInvoiceInfo);

        //账号、企业关联
        TCompanyAccount companyAccount = new TCompanyAccount();
        companyAccount.setAccountId(account.getId());
        companyAccount.setCompanyId(companyInfo.getId());
        companyAccount.setRealName(companyInfoAddVO.getNickname());
        companyAccount.setEnable(false);
        companyAccountMapper.insertSelective(companyAccount);

        return ResultUtil.ok();
    }

    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil update(TCompanyInfoAddVO companyInfoAddVO) throws Exception {
        TCompanyInfoQuery companyInfoQuery = new TCompanyInfoQuery();
        companyInfoQuery.setCompanyName(companyInfoAddVO.getCompanyName());
        companyInfoQuery.setCompanyId(companyInfoAddVO.getCompanyId());

        List<TCompanyInfo> list = companyInfoMapper.selectRepeatCompany(companyInfoQuery);
        if (list != null && list.size() >0){
            return ResultUtil.error(companyInfoAddVO.getCompanyName() + "已存在");
        }
        //TODO 修改机构:如果新名称 != 旧名称，则修改
        if (!companyInfoAddVO.getCompanyName().equals(companyInfoAddVO.getCompanyOldName())){
            OperOrgUpdateVO operOrgUpdateVO = new OperOrgUpdateVO();
            operOrgUpdateVO.setName(companyInfoAddVO.getCompanyName());
            operOrgUpdateVO.setAccountNO(companyInfoAddVO.getAccountNo());
            ResultUtil resultUtil = operOrgAPI.updateOrgForMember(operOrgUpdateVO);
            String code = resultUtil.getCode();
            if (code.equals("error")){
                return resultUtil;
            }
        }
        //TODO 查询账号是否存在 已完成
        TAccount accounts = new TAccount();
        accounts.setId(companyInfoAddVO.getAccountId());
        accounts.setAccountNo(companyInfoAddVO.getAccountNo());
        List<TAccount> tAccounts = accountMapper.selectRepeatAccountNo(accounts);
        if (null != tAccounts && tAccounts.size() >0){
            return ResultUtil.error("账号已存在");
        }

        TCompanyInfo companyInfo = new TCompanyInfo();
        companyInfo.setId(companyInfoAddVO.getCompanyId());
        BeanUtils.copyProperties(companyInfoAddVO, companyInfo);

        //修改管理员信息
        TAccount account = new TAccount();
        account.setAccountNo(companyInfoAddVO.getAccountNo());
        account.setNickname(companyInfoAddVO.getNickname());
        account.setEmail(companyInfoAddVO.getAccountEmail());
        account.setId(companyInfoAddVO.getAccountInfoId());
        accountMapper.updateByPrimaryKeySelective(account);

        //TODO　修改系统用户
        if (!(companyInfoAddVO.getAccountNo().equals(companyInfoAddVO.getOldAccountNo())
                && companyInfoAddVO.getNickname().equals(companyInfoAddVO.getOldNickname()))){
            TSysUserVO sysUserVO = new TSysUserVO();
            sysUserVO.setId(companyInfoAddVO.getUserId());
            sysUserVO.setOldAccountNo(companyInfoAddVO.getOldAccountNo());
            sysUserVO.setAccountNo(companyInfoAddVO.getAccountNo());
            sysUserVO.setNickname(companyInfoAddVO.getNickname());
            if(companyInfoAddVO.getOldAccountEmail()!=null&&!"".equals(companyInfoAddVO.getOldAccountEmail())){
                sysUserVO.setEmail(companyInfoAddVO.getAccountEmail());
            }
            ResultUtil resultUtil = systemAPI.updateUserByAccountNo(sysUserVO);
            if (resultUtil.getCode().equals("error")) {
                throw new RuntimeException(resultUtil.getMsg());
            }
        }

        //修改企业
        //合同照片 2019/7/3 11:19 dwb（添加合同照片）
        String param = "";
        if(companyInfoAddVO.getParamArray()!=null && companyInfoAddVO.getParamArray().length>0){
            for(int i=0;i<companyInfoAddVO.getParamArray().length;i++){//多张合同照片
                param+= companyInfoAddVO.getParamArray()[i]+",";
            }
        }
        companyInfo.setParam1(param);//合同照片放备用字段
        if (StringUtils.isNotEmpty(companyInfoAddVO.getTaxpayerNumber())){
            companyInfo.setTaxNo(companyInfoAddVO.getTaxpayerNumber());
        }
        companyInfoMapper.updateByPrimaryKeySelective(companyInfo);

        //修改业务负责人与企业关联表
        TBusinessCompany tBusinessCompany = tBusinessCompanyMapper.selectBycompanyId(companyInfoAddVO.getCompanyId());
        if (null!=tBusinessCompany){
            tBusinessCompany.setBusinessBasicId(companyInfoAddVO.getBusinessId());
            tBusinessCompanyMapper.updateByPrimaryKey(tBusinessCompany);
        }else{
            tBusinessCompany = new TBusinessCompany();
            tBusinessCompany.setBusinessBasicId(companyInfoAddVO.getBusinessId());
            tBusinessCompany.setCompanyId(companyInfoAddVO.getCompanyId());
            tBusinessCompanyMapper.insertSelective(tBusinessCompany);
        }
        //TODO 修改企业账号关系表

        //修改开票信息
        SearchProjectInvoiceVO projectInvoiceVO = new SearchProjectInvoiceVO();
        projectInvoiceVO.setProjectId(0);
        projectInvoiceVO.setCompanyId(companyInfoAddVO.getCompanyId());
        ResultUtil resultUtil = projectInvoiceService.searchProjectInvoiceByProjectId(projectInvoiceVO);
        HashMap projectInvoiceInfoDTO = (HashMap) resultUtil.getData();
        TProjectInvoiceInfo projectInvoiceInfo = new TProjectInvoiceInfo();
        BeanUtils.copyProperties(companyInfoAddVO, projectInvoiceInfo);
        if (null != projectInvoiceInfoDTO) {
            //如果开票信息不为空，则修改
            projectInvoiceInfo.setId(Integer.valueOf(String.valueOf(projectInvoiceInfoDTO.get("id"))));
            projectInvoiceService.updateProjectInvoiceByProjectId(projectInvoiceInfo);
        } else {
            //开票信息为空，新增
            projectInvoiceInfo.setEnable(false);
            projectInvoiceInfo.setProjectId(0);
            projectInvoiceService.insertProjectInvoice(projectInvoiceInfo);
        }

        //TODO 修改线路的是否关联客户和委托方


        return ResultUtil.ok();
    }

    @Override
    public ResultUtil selectCityTree() {
        List<ProvinceCityCounty> list;
        if (redisUtil.hasKey(SSX_KEY)) {
            list = (List<ProvinceCityCounty>) redisUtil.get(SSX_KEY);
        } else {
            cityInfoAPI.loadCityTree();
            list = (List<ProvinceCityCounty>) redisUtil.get(SSX_KEY);
        }
        return ResultUtil.ok(list);
    }

    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil deleteById(CompanyDeleteVO deleteVO) {
        if (null != deleteVO.getDeleteId() && deleteVO.getDeleteId().length > 0){
            Integer[] id = deleteVO.getDeleteId();
            for (int i = 0; i< id.length; i++){
                //修改企业状态
                TCompanyInfo companyInfo = new TCompanyInfo();
                companyInfo.setId(id[i]);
                companyInfo.setEnable(true);
                companyInfoMapper.updateStausByDelete(companyInfo);


                //修改管理员状态
                ManageAccountDTO manageAccountDTO = accountMapper.selectManageAccount(id[i]);

                TAccount tAccount = accountMapper.selectByPrimaryKey(manageAccountDTO.getAccountId());
                TUserInfo userInfo = new TUserInfo();
                userInfo.setAccountId(tAccount.getUserId());
                userInfo.setEnable(true);
                systemAPI.updateByAccountIdSelective(userInfo);

                //根据账号表获取userid
                com.lz.system.model.TSysUser user = new TSysUser();
                user.setId(tAccount.getUserId());
                user.setEnable(true);
                systemAPI.updateUser(user);

                TAccount account = new TAccount();
                account.setEnable(true);
                account.setId(Integer.valueOf(String.valueOf(manageAccountDTO.getAccountId())));
                accountMapper.updateByPrimaryKeySelective(account);

                //修改账号与企业关联的删除状态
                TCompanyAccount companyAccount = new TCompanyAccount();
                companyAccount.setCompanyId(id[i]);
                companyAccount.setAccountId(Integer.valueOf(String.valueOf(manageAccountDTO.getAccountId())));
                companyAccount.setEnable(true);
                companyAccountMapper.deleteByAccountCompany(companyAccount);

                //修改开票信息状态
                //开票信息
                SearchProjectInvoiceVO projectInvoiceVO = new SearchProjectInvoiceVO();
                projectInvoiceVO.setProjectId(0);
                projectInvoiceVO.setCompanyId(id[i]);
                ResultUtil resultUtil = projectInvoiceService.searchProjectInvoiceByProjectId(projectInvoiceVO);
                HashMap projectInvoiceInfoDTO = (HashMap) resultUtil.getData();
                TProjectInvoiceInfo projectInvoiceInfo = new TProjectInvoiceInfo();
                projectInvoiceInfo.setId(Integer.valueOf(String.valueOf(projectInvoiceInfoDTO.get("id"))));
                projectInvoiceInfo.setEnable(true);
                projectInvoiceService.updateProjectInvoiceByProjectId(projectInvoiceInfo);
            }
        }

        return ResultUtil.ok();
    }

    @Override
    public ResultUtil selectCompany() {
        List<CompanyInfoDTO> companyInfoDTOS = companyInfoMapper.selectCompany();
        return ResultUtil.ok(companyInfoDTOS);
    }

    //财务管理--企业钱包--企业列表
    @Override
    public ResultUtil selectCompanyByPage(TCompanyInfoQuery companyInfoQuery) {
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        if (null != userCompanyId && userCompanyId.size() > 0){
            String companyId = userCompanyId.get(0);
            companyInfoQuery.setCompanyId(Integer.valueOf(companyId));
        }
        Page<Object> page = PageHelper.startPage(companyInfoQuery.getPage(), companyInfoQuery.getSize());
        List<CompanyInfoDTO> list = companyInfoMapper.selectCompanyByPage(companyInfoQuery);
        ResultUtil resultUtil = ResultUtil.ok();
        resultUtil.setCount(page.getTotal());
        resultUtil.setData(list);
        return resultUtil;
    }


    /**
     *  @author: dingweibo
     *  @Date: 2019/6/24 15:18
     *  @Description: 企业资金总览
     */
    @Override
    public ResultUtil selectCompanyCapital() {
        Integer companyIdInteger = null;
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        if (null != userCompanyId && userCompanyId.size() > 0){
            String companyId = userCompanyId.get(0);
            companyIdInteger = Integer.valueOf(companyId);
        }
        //tCarrierEnduserCompanyRelMapper.selectByCarrier()
        if(companyIdInteger!=null){
            CompanyCapitalVo capitalVo =  walletMapper.selectCompanyCapital(companyIdInteger);
            TWalletChangeLog tWalletChangeLog = walletChangeLogAPI.selectAccumulativeRecharge(companyIdInteger);
            if(capitalVo!=null && !"".equals(capitalVo)){
                capitalVo.setTotalAssets(capitalVo.getAvailableAssets().add(capitalVo.getFrozenAmount()));//当前总资产
                capitalVo.setAccumulativeRecharge(tWalletChangeLog.getAmount());//累计充值
            }
            //单笔提现钱
            List<TCompanyProjectVo> tCompanyProjectVoList = companyInfoMapper.selectByCompanyProject(companyIdInteger);
            //打包支付提现
            List<TCompanyProjectVo> tCompanyProjectVoListPack = companyInfoMapper.selectByCompanyProjectPack(companyIdInteger);
            //预支付提现
            List<TCompanyProjectVo> tCompanyProjectVoListAmp = companyInfoMapper.selectByCompanyProjectAmp(companyIdInteger);

            //最终数据
            //查出后累加
            if(tCompanyProjectVoListPack.size()<1){
                if(capitalVo!=null && !"".equals(capitalVo)){
                    for(TCompanyProjectVo v:tCompanyProjectVoList){
                        if(v.getDispatchFeeCoefficientDg()!=null&&!"".equals(v.getDispatchFeeCoefficientDg())){
                            v.setDispatchFeeCoefficient(Double.parseDouble(v.getDispatchFeeCoefficientDg().toString()));
                        }
                    }
                    capitalVo.setTCompanyProjectVoList(tCompanyProjectVoList);
                }
            }else{
                for(TCompanyProjectVo vo:tCompanyProjectVoList){
                    if(vo.getDispatchFeeCoefficientDg()!=null&&!"".equals(vo.getDispatchFeeCoefficientDg())){
                        vo.setDispatchFeeCoefficient(Double.parseDouble(vo.getDispatchFeeCoefficientDg().toString()));
                    }
                    for(TCompanyProjectVo pack:tCompanyProjectVoListPack){
                        if((pack!=null && !"".equals(pack)) && (vo!=null && !"".equals(vo))){
                            log.info(JSONObject.toJSONString(pack)+"------------------vo:"+JSONObject.toJSONString(vo));
                            if(vo.getId().equals(pack.getId()) &&vo.getCarrierName().equals(pack.getCarrierName())){
                                vo.setNoCash(vo.getNoCash().add(pack.getNoCash()));
                                vo.setWithdrawal(vo.getWithdrawal().add(pack.getWithdrawal()));
                                vo.setAlreadyCash(vo.getAlreadyCash().add(pack.getAlreadyCash()));
                                vo.setDispatchFee(vo.getDispatchFee().add(pack.getDispatchFee()));
                            }
                        }
                    }

                    Date createTime = vo.getCreateTime();
                    Date updateTime = vo.getUpdateTime();
                    Date projectTime = null != updateTime ? updateTime : createTime;
                    vo.setUpdateTime(projectTime);
                }

            }
            //预付款提现相加
            if(tCompanyProjectVoListAmp.size()>0){
                for(TCompanyProjectVo vo:tCompanyProjectVoList){
                    for(TCompanyProjectVo amp:tCompanyProjectVoListAmp){
                        if((amp!=null && !"".equals(amp)) && (vo!=null && !"".equals(vo))){
                            log.info(JSONObject.toJSONString(amp)+"------------------vo:"+JSONObject.toJSONString(vo));
                            if(vo.getId().equals(amp.getId()) &&vo.getCarrierName().equals(amp.getCarrierName())){
                                vo.setNoCash(vo.getNoCash().add(amp.getNoCash()));
                                vo.setWithdrawal(vo.getWithdrawal().add(amp.getWithdrawal()));
                                vo.setAlreadyCash(vo.getAlreadyCash().add(amp.getAlreadyCash()));
                                vo.setDispatchFee(vo.getDispatchFee().add(amp.getDispatchFee()));
                            }
                        }
                    }
                }
            }
            if(capitalVo!=null && !"".equals(capitalVo)){
                capitalVo.setTCompanyProjectVoList(tCompanyProjectVoList);
            }
            return ResultUtil.ok(capitalVo);
        }else{
            return ResultUtil.error("企业id为空！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/3/24 9:36
     *  @Description: +近7日资金流动汇总
     */
    @Override
    public ResultUtil selectCompanyCapitalWeek() {
        Integer companyIdInteger = null;
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        if (null != userCompanyId && userCompanyId.size() > 0){
            String companyId = userCompanyId.get(0);
            companyIdInteger = Integer.valueOf(companyId);
        }
        if(companyIdInteger!=null){
            List<CompanyCapitalWeekVo> czList =  walletMapper.selectCompanyCapitalWeekCz(companyIdInteger);
            List<CompanyCapitalWeekVo> yfzcList =  walletMapper.selectCompanyCapitalWeekYfzc(companyIdInteger);
            List<CompanyCapitalWeekVo> ddfzcList =  walletMapper.selectCompanyCapitalWeekDdfzc(companyIdInteger);
            for(CompanyCapitalWeekVo a:czList){
                for(CompanyCapitalWeekVo b:yfzcList){
                    if(a.getClickDate().equals(b.getClickDate())){
                        a.setYfzc(b.getYfzc());
                    }
                }
                for(CompanyCapitalWeekVo c:ddfzcList){
                    if(a.getClickDate().equals(c.getClickDate())){
                        a.setDdfzc(c.getDdfzc());
                    }
                }
            }
            return ResultUtil.ok(czList);
        }else{
            return ResultUtil.error("企业id为空！");
        }
    }
    /**
     * @Description 单笔项目资金明细
     * <AUTHOR>
     * @Date   2019/7/23 14:13
     * @Param
     * @Return
     * @Exception
     *
     */
    @Override
    public ResultUtil selectCompanyCapitalSingleDetailed(TCompanyInfoQuery record) {
        Integer companyIdInteger = null;
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        if (null != userCompanyId && userCompanyId.size() > 0){
            String companyId = userCompanyId.get(0);
            companyIdInteger = Integer.valueOf(companyId);
        }
        Page<Object> page = PageHelper.startPage(record.getPage(), record.getSize());
        //单笔提现钱
        List<TCompanyProjectVo> list = companyInfoMapper.selectByCompanyProject(companyIdInteger);
        ResultUtil resultUtil = ResultUtil.ok();
        resultUtil.setCount(page.getTotal());
        resultUtil.setData(list);
        return resultUtil;
    }

    /**
     * @Description 打包项目资金明细
     * <AUTHOR>
     * @Date   2019/7/23 14:13
     * @Param
     * @Return
     * @Exception
     *
     */
    @Override
    public ResultUtil selectCompanyCapitalPackDetailed(TCompanyInfoQuery record) {
        Integer companyIdInteger = null;
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        if (null != userCompanyId && userCompanyId.size() > 0){
            String companyId = userCompanyId.get(0);
            companyIdInteger = Integer.valueOf(companyId);
        }
        Page<Object> page = PageHelper.startPage(record.getPage(), record.getSize());
        //打包支付提现
        List<TCompanyProjectVo> list = companyInfoMapper.selectByCompanyProjectPack(companyIdInteger);
        ResultUtil resultUtil = ResultUtil.ok();
        resultUtil.setCount(page.getTotal());
        resultUtil.setData(list);
        return resultUtil;
    }

    @Override
    public ResultUtil applySubAccount(Integer companyId,Integer carrierId){
        TCarrierEnduserCompanyRelVo carrierEnduserCompanyRelVo = new TCarrierEnduserCompanyRelVo();
        carrierEnduserCompanyRelVo.setCarrierId(carrierId);
        carrierEnduserCompanyRelVo.setEnduserCompanyId(companyId);
        carrierEnduserCompanyRelVo.setDatasource("BD");
        ResultUtil selectCarrierRel = carrierCompanyRelService.selectCarrierRel(carrierEnduserCompanyRelVo);
        Object selectCarrierRelData = selectCarrierRel.getData();
        LinkedHashMap carrierEnduserCompanyRelMap = new LinkedHashMap();
        TCarrierEnduserCompanyRel carrierEnduserCompanyRel = new TCarrierEnduserCompanyRel();

        String uid = IdWorkerUtil.getInstance().nextId();
        if (null == selectCarrierRel){

            carrierEnduserCompanyRel.setCarrierId(carrierId);
            carrierEnduserCompanyRel.setEnduserCompanyId(companyId);
            // 设置配合子账号的uid
            carrierEnduserCompanyRel.setUid(uid);
            carrierEnduserCompanyRel.setEnable(false);
            carrierEnduserCompanyRel.setStopFlag(true);
            //关系类型：承运方与B端
            carrierEnduserCompanyRel.setDatasouce("BD");
            ResultUtil saveCarrierEnduserCompanyRel = carrierCompanyRelService.save(carrierEnduserCompanyRel);
        }else{

            carrierEnduserCompanyRelMap=(LinkedHashMap) selectCarrierRel.getData();
            String string = JSONObject.toJSONString(carrierEnduserCompanyRelMap);
            carrierEnduserCompanyRel = JSONObject.parseObject(string, TCarrierEnduserCompanyRel.class);
            if(StringUtils.isEmpty(carrierEnduserCompanyRel.getUid())){
                carrierEnduserCompanyRel.setUid(uid);
            }
        }
       TCompanyInfo tCompanyInfo=companyInfoMapper.selectCompanyInfoById(companyId);
       UserRequestVo ur = new UserRequestVo();
       ur.setEnterprise_name(tCompanyInfo.getCompanyName());
       ur.setLicense_no(tCompanyInfo.getBusinessLicenseNo());
       ur.setUid(carrierEnduserCompanyRel.getUid());
       ur.setTCarrierEnduserCompanyRelId(carrierEnduserCompanyRel.getId());
       ur.setCarrierId(carrierId);
       ur.setIfSave(false);
       log.info("申请子账号入参：{}",JSONObject.toJSONString(ur));
       carrierEnduserCompanyRel = userRegisterCommon.enterpriseRegister(ur);
        return ResultUtil.ok();
    }
    /**
     *  @author: dingweibo
     *  @Date: 2020/2/19 15:31
     *  @Description: 根据企业ID  承运方 ID 查询企业名称 承运方名称
     */
    @Override
    public TCompanyInfoVo selectByCarrierIdAnrCompanyId(TCompanyInfoVo record){
        TCompanyInfo tCompanyInfo = companyInfoMapper.selectByPrimaryKey(record.getCompanyId());
        TCarrierInfo tCarrierInfo = tCarrierInfoMapper.selectByPrimaryKey(record.getCarrierId());
        record.setCarrierName(tCarrierInfo.getCarrierName());
        record.setCompanyName(tCompanyInfo.getCompanyName());
        return  record;
    }

    @Transactional
    @Override
    public ResultUtil updateCompanyUploadedInfo(TCompanyInfo record) {
        return ResultUtil.ok(companyInfoMapper.updateCompanyUploadStatus(record));
    }
}