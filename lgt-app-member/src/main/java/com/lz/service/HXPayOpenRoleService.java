package com.lz.service;

import com.lz.common.util.ResultUtil;
import com.lz.dto.OpenRoleWalletListDTO;
import com.lz.model.TZtAccountOpenInfo;
import com.lz.vo.SelectOpenRoleInfo;
import com.lz.vo.SelectOpenRoleVO;
import com.lz.vo.SelectOrderPackOpenRoleVO;

public interface HXPayOpenRoleService {

    ResultUtil selectEnduserOpenRoleInfo(SelectOpenRoleInfo vo);

    ResultUtil selectOpenRoleStatus(SelectOpenRoleVO vo);

    ResultUtil selectOpenRoleWallet(SelectOpenRoleVO vo);

    ResultUtil selectOrderPackCarrierCompanyEnduserOpenRoleStatus(SelectOrderPackOpenRoleVO vo);

    ResultUtil<OpenRoleWalletListDTO> selectOrderPackCarrierCompanyEnduserOPenRoleWallet(SelectOrderPackOpenRoleVO vo);

    TZtAccountOpenInfo selectByAccountId(Integer accountId);
}
