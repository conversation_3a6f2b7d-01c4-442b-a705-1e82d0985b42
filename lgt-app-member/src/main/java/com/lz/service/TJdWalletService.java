package com.lz.service;

import com.jd.jr.jropen.sdk.model.message.AsyncNotifyVirtualMemberRechargeMessageBody;
import com.lz.common.model.jdPayment.request.bank.QueryVerifyAmtReq;
import com.lz.common.util.ResultUtil;
import com.lz.model.TJdWallet;
import com.lz.vo.*;

import java.text.ParseException;
import java.util.Map;

public interface TJdWalletService {

    public ResultUtil selectByStatus(TCarrierCompanyOpenRoleVo record);

    public ResultUtil bindingBank(TJdBankCardVo record);

    public ResultUtil unbindBank(TJdBankCardVo record);

    public ResultUtil vitual(TJdBankCardVo record);

    public TJdBankCardVo selectJdBankCardId(TJdBankCardVo record);

    public ResultUtil jdBankCardList(TJdBankCardVo record);

    public ResultUtil queryVerifyAmount(QueryVerifyAmtReq record);

    public ResultUtil selectByCarrierWalletPage(TCarrierCompanyOpenRoleSearchVo record);

    public ResultUtil selectByCarrierWalletChangeLogPage(TCarrierCompanyOpenRoleSearchVo record);

    public ResultUtil selectByCarrierWalletChangeLogExcel(TCarrierCompanyOpenRoleSearchVo record);

    Map<String, Object> pcCompanyCwDetail(Integer companyId);

    ResultUtil companyCapitalFlow(CompanyCapitalFlowVo companyCapitalFlowVo);

    ResultUtil companyCapitalFlowExcel(CompanyCapitalFlowVo companyCapitalFlowVo);

    ResultUtil driverCapitalFlow(CompanyCapitalFlowVo companyCapitalFlowVo);

    ResultUtil selectAgentWallet();
    ResultUtil selectAgentWalletList(CompanyCapitalFlowVo record);

    ResultUtil selectPfWallet();
    ResultUtil selectPfWalletList(CompanyCapitalFlowVo record);

    ResultUtil selectPfWalletListExcel(CompanyCapitalFlowVo record);

    ResultUtil accountServiceFee(TEndUserOpenRoleVo record) throws ParseException;

    ResultUtil updateWalletCallBack(AsyncNotifyVirtualMemberRechargeMessageBody messageBody);

    TJdWallet selectWalletByEnduserParnterAccId(String endUserPartnerAccId);

}
