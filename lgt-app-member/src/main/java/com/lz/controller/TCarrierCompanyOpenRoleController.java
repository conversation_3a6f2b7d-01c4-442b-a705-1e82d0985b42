package com.lz.controller;

import com.lz.common.dbenum.DictEnum;
import com.lz.common.model.CodeEnum;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.common.util.ThrowableUtil;
import com.lz.model.*;
import com.lz.service.*;
import com.lz.vo.TAccountOpenInfoVo;
import com.lz.vo.TCarrierCompanyOpenRoleSearchVo;
import com.lz.vo.TCarrierCompanyOpenRoleVo;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.TimeUnit;

/**
 *  @author: dingweibo
 *  @Date: 2021/8/5 9:06
 *  @Description:
 */
@Slf4j
@RestController
@RequestMapping("/tCarrierCompanyOpenRole")
public class TCarrierCompanyOpenRoleController {

    @Autowired
    private TCarrierCompanyOpenRoleService tCarrierCompanyOpenRoleService;

    @Autowired
    private TZtAccountOpenInfoService ztAccountOpenInfoService;

    @Autowired
    private TCarrierInfoService carrierInfoService;

    @Autowired
    private TCompanyInfoService companyInfoService;

    @Autowired
    private TEndUserInfoService endUserInfoService;

    @Autowired
    private TEnduserAccountService enduserAccountService;

    @Autowired
    private RedissonClient redissonClient;

    /**  京东已下线
     *  @author: dingweibo
     *  @Date: 2021/8/5 16:13
     *  @Description: 承运方京东开户列表查询
     */
    @PostMapping("/selectByPage")
    public ResultUtil selectByPage(@RequestBody TCarrierCompanyOpenRoleSearchVo record){
        try {
            return tCarrierCompanyOpenRoleService.selectByPage(record);
        } catch (Exception e) {
            log.error("承运方京东开户信息列表查询失败！",e);
            return ResultUtil.error("承运方京东开户信息列表查询失败！");
        }
    }

    /** 京东已下线
     *  @author: dingweibo
     *  @Date: 2021/8/5 16:13
     *  @Description: 承运方京东回显
     */
    @PostMapping("/selectById")
    public ResultUtil selectById(@RequestBody TCarrierCompanyOpenRoleVo record){
        try {
            return tCarrierCompanyOpenRoleService.selectById(record);
        } catch (Exception e) {
            log.error("承运方京东开户信息查询失败！",e);
            return ResultUtil.error("承运方京东开户信息查询失败！");
        }
    }

    /** 京东已下线
     *  @author: dingweibo
     *  @Date: 2021/8/6 14:23
     *  @Description: 企业京东开户列表查询
     */
    @PostMapping("/selectCompanyByPage")
    public ResultUtil selectCompanyByPage(@RequestBody TCarrierCompanyOpenRoleSearchVo record){
        try {
            return tCarrierCompanyOpenRoleService.selectCompanyByPage(record);
        } catch (Exception e) {
            log.error("企业京东开户列表查询失败！",e);
            return ResultUtil.error("企业京东开户列表查询失败！");
        }
    }

    /** 京东已下线
     *  @author: dingweibo
     *  @Date: 2021/8/5 16:13
     *  @Description: 企业京东回显
     */
    @PostMapping("/selectByCompanyId")
    public ResultUtil selectByCompanyId(@RequestBody TCarrierCompanyOpenRoleVo record){
        try {
            return tCarrierCompanyOpenRoleService.selectByCompanyId(record);
        } catch (Exception e) {
            log.error("企业京东开户信息查询失败！",e);
            return ResultUtil.error("企业京东开户信息查询失败！");
        }
    }


    /** 京东已下线
     *  @author: dingweibo
     *  @Date: 2021/8/20 14:06
     *  @Description: 平台京东开户列表查询
     */
    @PostMapping("/selectPlatformByPage")
    public ResultUtil selectPlatformByPage(@RequestBody TCarrierCompanyOpenRoleSearchVo record){
        try {
            return tCarrierCompanyOpenRoleService.selectPlatformByPage(record);
        } catch (Exception e) {
            log.error("平台京东开户列表查询失败！",e);
            return ResultUtil.error("平台京东开户列表查询失败！");
        }
    }
    /** 京东已下线
     *  @author: dingweibo
     *  @Date: 2021/8/5 16:13
     *  @Description: 平台京东回显
     */
    @PostMapping("/selectByPlatformId")
    public ResultUtil selectByPlatformId(@RequestBody TCarrierCompanyOpenRoleVo record){
        try {
            return tCarrierCompanyOpenRoleService.selectByPlatformId(record);
        } catch (Exception e) {
            log.error("承运方京东开户信息查询失败！",e);
            return ResultUtil.error("承运方京东开户信息查询失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/5 16:17
     *  @Description: 华夏用户开户
     */
    @PostMapping("/openAccount")
    public ResultUtil openAccount(@RequestBody TAccountOpenInfoVo record){
        try {
            RLock lock;
            String lockKey = "";
            if (DictEnum.CA.code.equals(record.getUserOpenRole()) || DictEnum.PF.code.equals(record.getUserOpenRole())) {
                Integer carrierCompanyEndUserId = record.getCarrierCompanyEndUserId();
                TCarrierInfo tCarrierInfo = carrierInfoService.selectCarrierById(String.valueOf(carrierCompanyEndUserId));
                lockKey = tCarrierInfo.getBusinessLicenseNo();
            } else if (DictEnum.BD.code.equals(record.getUserOpenRole())) {
                Integer carrierCompanyEndUserId = record.getCarrierCompanyEndUserId();
                TCompanyInfo tCompanyInfo = companyInfoService.selectByPrimaryKey(carrierCompanyEndUserId);
                lockKey = tCompanyInfo.getBusinessLicenseNo();
            } else if (DictEnum.CD.code.equals(record.getUserOpenRole())) {
                Integer carrierCompanyEndUserId = record.getCarrierCompanyEndUserId();
                TEndUserInfo tEndUserInfo = endUserInfoService.selectById(carrierCompanyEndUserId);
                lockKey = tEndUserInfo.getIdcard();
            }
            if (StringUtils.isNotBlank(lockKey)) {
                lock = redissonClient.getLock(lockKey);
                boolean tryLock = lock.tryLock();
                if (tryLock) {
                    try {
                        return tCarrierCompanyOpenRoleService.openAccount(record);
                    } finally {
                        if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                            lock.unlock();
                        }
                    }
                } else {
                    return ResultUtil.error("开户处理中，请勿重复操作！");
                }
            } else {
                return ResultUtil.error("用户角色不存在！");
            }
        } catch (Exception e) {
            log.error("开户失败！, {}", ThrowableUtil.getStackTrace(e));
            return ResultUtil.error("开户失败！");
        }
    }

    /**
     * 华夏开户信息变更
     * @param record
     * @return
     */
    @PostMapping("/updateOpenAccount")
    public ResultUtil updateOpenAccount(@RequestBody TAccountOpenInfoVo record) {
        try {
            RLock lock;
            RLock notSelfLock = null;
            boolean notSelfTryLock = true;
            String lockKey = "";
            String notSelfKey = "";
            if (DictEnum.CA.code.equals(record.getUserOpenRole()) || DictEnum.PF.code.equals(record.getUserOpenRole())) {
                Integer carrierCompanyEndUserId = record.getCarrierCompanyEndUserId();
                TCarrierInfo tCarrierInfo = carrierInfoService.selectCarrierById(String.valueOf(carrierCompanyEndUserId));
                lockKey = tCarrierInfo.getBusinessLicenseNo();
            } else if (DictEnum.BD.code.equals(record.getUserOpenRole())) {
                Integer carrierCompanyEndUserId = record.getCarrierCompanyEndUserId();
                TCompanyInfo tCompanyInfo = companyInfoService.selectByPrimaryKey(carrierCompanyEndUserId);
                lockKey = tCompanyInfo.getBusinessLicenseNo();
            } else if (DictEnum.CD.code.equals(record.getUserOpenRole())) {
                TZtAccountOpenInfo tZtAccountOpenInfo = ztAccountOpenInfoService.selectByAccountId(record.getAccountId(), DictEnum.CD.code);
                if (null == record.getIfOneselfOpen() || !record.getIfOneselfOpen()) {
                    // 本人开户
                    Integer carrierCompanyEndUserId = record.getCarrierCompanyEndUserId();
                    TEndUserInfo tEndUserInfo = endUserInfoService.selectById(carrierCompanyEndUserId);
                    lockKey = tEndUserInfo.getIdcard();
                } else if (record.getIfOneselfOpen()) {
                    if (null == record.getOpenRealName() || StringUtils.isBlank(record.getOpenRealName())) {
                        return ResultUtil.error("开户人姓名不能为空！");
                    }
                    if (null == record.getOpenPhone() || StringUtils.isBlank(record.getOpenPhone())) {
                        return ResultUtil.error("开户人手机号不能为空！");
                    }
                    if (null == record.getOpenIdCard() || StringUtils.isBlank(record.getOpenIdCard())) {
                        return ResultUtil.error("开户人身份证号不能为空！");
                    }
                    if (record.getOpenRealName().equals(tZtAccountOpenInfo.getOpenRealName())
                            && record.getOpenPhone().equals(tZtAccountOpenInfo.getOpenPhone())
                            && record.getOpenIdCard().equals(tZtAccountOpenInfo.getOpenIdCard())) {
                        return ResultUtil.error("开户人信息未变更！");
                    }
                    if (!record.getOpenPhone().equals(tZtAccountOpenInfo.getOpenPhone())) {
                        TZtAccountOpenInfo ztAccountOpenInfo = new TAccountOpenInfoVo();
                        ztAccountOpenInfo.setAccountId(record.getAccountId());
                        ztAccountOpenInfo.setOpenPhone(record.getOpenPhone());
                        ResultUtil resultUtil = ztAccountOpenInfoService.selectNotByAccountId(ztAccountOpenInfo);
                        if (!CodeEnum.SUCCESS.getCode().equals(resultUtil.getCode())) {
                            return ResultUtil.error("开户人手机号已存在！");
                        }
                        TEnduserAccount enduserAccount = new TEnduserAccount();
                        enduserAccount.setAccountId(record.getAccountId());
                        enduserAccount.setPhone(record.getOpenPhone());
                        ResultUtil selectByNotSelfOpen = enduserAccountService.selectByNotSelfOpen(enduserAccount);
                        if (!CodeEnum.SUCCESS.getCode().equals(selectByNotSelfOpen.getCode())) {
                            return ResultUtil.error("开户人手机号已存在！");
                        }
                    }
                    if (!record.getOpenIdCard().equals(tZtAccountOpenInfo.getOpenIdCard())) {
                        TZtAccountOpenInfo ztAccountOpenInfo = new TAccountOpenInfoVo();
                        ztAccountOpenInfo.setAccountId(record.getAccountId());
                        ztAccountOpenInfo.setOpenIdCard(record.getOpenIdCard());
                        ResultUtil resultUtil = ztAccountOpenInfoService.selectNotByAccountId(ztAccountOpenInfo);
                        if (!CodeEnum.SUCCESS.getCode().equals(resultUtil.getCode())) {
                            return ResultUtil.error("开户人身份证号已存在！");
                        }
                        TEnduserAccount enduserAccount = new TEnduserAccount();
                        enduserAccount.setAccountId(record.getAccountId());
                        enduserAccount.setIdcard(record.getOpenIdCard());
                        ResultUtil selectByNotSelfOpen = enduserAccountService.selectByNotSelfOpen(enduserAccount);
                        if (!CodeEnum.SUCCESS.getCode().equals(selectByNotSelfOpen.getCode())) {
                            return ResultUtil.error("开户人身份证号已存在！");
                        }
                    }
                    // 非本人开户
                    lockKey = tZtAccountOpenInfo.getOpenIdCard();
                    if (!tZtAccountOpenInfo.getOpenIdCard().equals(record.getOpenIdCard())) {
                        notSelfKey = record.getOpenIdCard();
                    }
                }
            }
            if (StringUtils.isNotBlank(lockKey)) {
                lock = redissonClient.getLock(lockKey);
                if (StringUtils.isNotBlank(notSelfKey)) {
                    notSelfLock = redissonClient.getLock(notSelfKey);
                    notSelfTryLock = notSelfLock.tryLock();
                }
                boolean tryLock = lock.tryLock();
                if (tryLock && notSelfTryLock) {
                    try {
                        return tCarrierCompanyOpenRoleService.updateOpenAccount(record);
                    } finally {
                        if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                            lock.unlock();
                        }
                        if (notSelfLock != null && notSelfLock.isHeldByCurrentThread() && notSelfLock.isLocked()) {
                            notSelfLock.unlock();
                        }
                    }
                } else {
                    return ResultUtil.error("变更处理中，请勿重复操作！");
                }
            } else {
                return ResultUtil.error("用户角色不存在！");
            }
        } catch (Exception e) {
            log.error("变更失败！, {}", ThrowableUtil.getStackTrace(e));
            return ResultUtil.error("变更失败！");
        }
    }
    /**
     *  @author: dingweibo
     *  @Date: 2023/1/9 14:24
     *  @Description: 华夏司机非本人开户回显
     */
    @PostMapping("/selectNoPersonalOpenRoleById")
    public ResultUtil selectNoPersonalOpenRoleById(@RequestParam(value = "accountId") Integer accountId){
        try {
            return ResultUtil.ok(tCarrierCompanyOpenRoleService.selectNoPersonalOpenRoleById(accountId));
        } catch (Exception e) {
            log.error("获取司机开户详情失败！",e);
            return ResultUtil.error("获取司机开户详情失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2023/1/9 15:39
     *  @Description: 华夏司机非本人开户
     */
    @PostMapping("/noPersonalOpenRole")
    public ResultUtil noPersonalOpenRole(@RequestBody TAccountOpenInfoVo record){
        try {
            RLock lock;
            String lockKey = record.getOpenIdCard();
            if (StringUtils.isNotBlank(lockKey)) {
                lock = redissonClient.getLock(lockKey);
                boolean tryLock = lock.tryLock();
                if (tryLock) {
                    try {
                        ResultUtil resultUtil = tCarrierCompanyOpenRoleService.noPersonalOpenRole(record);
                        return resultUtil;
                    } finally {
                        if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                            lock.unlock();
                        }
                    }
                } else {
                    return ResultUtil.error("非本人开户处理中，请勿重复操作！");
                }
            } else {
                return ResultUtil.error("请输入身份证号！");
            }
        } catch (Exception e) {
            log.error("华夏非本人开户失败！",e);
            return ResultUtil.error("华夏非本人开户失败！");
        }
    }

    /** 京东已下线
     *  @author: dingweibo
     *  @Date: 2021/8/5 16:17
     *  @Description: 京东用户上传图片
     */
    @PostMapping("/uploadPhoto")
    public ResultUtil uploadPhoto(@RequestBody TCarrierCompanyOpenRoleVo record){
        try {
            return tCarrierCompanyOpenRoleService.uploadPhoto(record);
        } catch (Exception e) {
            log.error("京东用户上传图片失败！",e);
            return ResultUtil.error("京东用户上传图片失败！");
        }
    }

    /** 京东已下线
     *  @author: dingweibo
     *  @Date: 2021/8/6 8:50
     *  @Description: 京东申请企业开户（承运方 共用）
     */
    @PostMapping("/openRealNameApply")
    public ResultUtil openRealNameApply(@RequestBody TCarrierCompanyOpenRoleVo record){
        try {
            return tCarrierCompanyOpenRoleService.openRealNameApply(record);
        } catch (Exception e) {
            log.error("京东申请企业开户失败！",e);
            return ResultUtil.error("京东申请企业开户失败！");
        }
    }

    /** 京东已下线
     *  @author: dingweibo
     *  @Date: 2021/8/6 14:05
     *  @Description: 更新开户申请状态
     */
    @PostMapping("/updateOpenStatus")
    public ResultUtil updateOpenStatus(@RequestBody TCarrierCompanyOpenRoleVo record){
        try {
            return tCarrierCompanyOpenRoleService.updateOpenStatus(record);
        } catch (Exception e) {
            log.error("更新开户申请状态失败！",e);
            return ResultUtil.error("更新开户申请状态失败！");
        }
    }
}
