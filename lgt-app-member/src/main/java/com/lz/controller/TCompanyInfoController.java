package com.lz.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lz.common.model.CodeEnum;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.dto.*;
import com.lz.model.TCarrierEnduserCompanyRel;
import com.lz.model.TCompanyAccount;
import com.lz.model.TCompanyInfo;
import com.lz.service.TCompanyInfoService;
import com.lz.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019/4/15 - 19:11
 **/
@RestController
@RequestMapping(value = "company")
@Slf4j
public class TCompanyInfoController {

    @Autowired
    private TCompanyInfoService companyInfoService;

    /**
     * 查询承运方与B端、C的关系：
     * <AUTHOR>
     * @param
     * @return
     */
    @PostMapping("/selectCompanyById")
    public ResultUtil selectCompanyInfoById(@RequestBody TCompanyInfoQuery query) {
        return companyInfoService.selectCompanyInfoById(query);
    }

    /**
     * 根据Sysuser表Id 查询所属企业
     * @auth yan
     * @param query
     * @return
     */
    @PostMapping("/selectBySysuserId")
    public ResultUtil getCompanyBySysuserId(@RequestBody TCompanyInfoQuery query) {
        return companyInfoService.selectCompanyIdBySysUserId(query.getSystemUserId());
    }

    /**
     * APP发单： 根据用户Id查询所属的企业
     * @return
     * @auth yan
     */
    @PostMapping("/selectByUserId")
    public  ResultUtil getCompanyByUser() {
        Integer currentUserID = CurrentUser.getCurrentUserID();
        return companyInfoService.selectCompanyIdByUserId(currentUserID);
    }

    /**
     * 获取业务部负责人信息
     * @return
     * @auth yan
     */
    @PostMapping("/selectBusinessLeader")
    public  ResultUtil getBusinessLeader() {
        try{
            return companyInfoService.selectBusinessLeader();
        }catch(Exception e){
            log.error("业务部负责人信息查询失败！",e);
            return  ResultUtil.error("业务部负责人信息查询失败！");
        }

    }

    /**
     * 企业列表
     * @auth zhangjiji
     * @param companyInfoQuery
     * @return
     */
    @PostMapping(value = "/selectByPage")
    public ResultUtil selectByPage(@RequestBody TCompanyInfoQuery companyInfoQuery) {
        try{
            if(null != companyInfoQuery && null != companyInfoQuery.getTime()){
                companyInfoQuery.setStartTime(companyInfoQuery.getTime()[0]);
                companyInfoQuery.setEndTime(companyInfoQuery.getTime()[1]);
            }
            ResultUtil list = companyInfoService.selectByPage(companyInfoQuery);
            return list;
        }catch(Exception e){
            log.error("dw-005:企业列表查询失败！",e);
            return  ResultUtil.error("dw-005:企业列表查询失败！");
        }
    }

    /**
     *获取企业开户列表信息
     * @return
     */
    @PostMapping(value = "/selectCompanyOpenInfoList")
    @ResponseBody
    public ResultUtil selectCompanyOpenInfoList(@RequestBody CompanyOpenInfoListVO companyOpenInfoListVO){
        ResultUtil resultUtil = companyInfoService.selectCompanyOpenInfoList(companyOpenInfoListVO);
        return resultUtil;
    }

    /**
     *获取企业开户详情
     * @return
     */
    @PostMapping(value = "/applyCompanyOpenInfo")
    @ResponseBody
    public ResultUtil applyCompanyOpenInfo(@RequestBody CompanyOpenInfoDetailsVO companyOpenInfoDetailsVO){
        ResultUtil applyCompanyOpenInfo = companyInfoService.applyCompanyOpenInfo(companyOpenInfoDetailsVO);
        return applyCompanyOpenInfo;
    }

    /**
     * 导出企业信息
     */
    @PostMapping(value = "enterprise/export")
    public ResultUtil export(@RequestBody TCompanyInfoQuery companyInfoQuery){
        try {
            if(companyInfoQuery!=null&&companyInfoQuery.getTime()!=null){
                companyInfoQuery.setStartTime(companyInfoQuery.getTime()[0]);
                companyInfoQuery.setEndTime(companyInfoQuery.getTime()[1]);
            }
            ResultUtil export = companyInfoService.enterpriseExport(companyInfoQuery);
            return export;
        }catch (Exception e){
            log.error("企业信息导出失败！",e);
            return  ResultUtil.error("企业信息导出失败！");
        }
    }

    /**
     * 根据企业id查询企业信息
     * @auth zhangjiji
     * @param companyInfoQuery 企业id
     * @return
     */
    @PostMapping(value = "/selectById")
    public ResultUtil selectById(@RequestBody TCompanyInfoQuery companyInfoQuery) {
        ResultUtil resultUtil = ResultUtil.ok();
        if (null != companyInfoQuery && null != companyInfoQuery.getCompanyId()){
            resultUtil = companyInfoService.selectById(companyInfoQuery.getCompanyId());
        }
        return resultUtil;
    }

    /**
     * 根据登录的业务部人员id查询企业信息
     * @auth
     * @param
     * @return
     */
    @PostMapping(value = "/selectCompanyByBusinessId")
    public ResultUtil selectCompanyByBusinessId(@RequestParam(required = false, value = "companyName") String companyName) {
        try {
            ResultUtil resultUtil = ResultUtil.ok();
            Integer id = CurrentUser.getCurrentUserID();
            if (null!=id && !"".equals(id)){
                resultUtil =  companyInfoService.selectByBusinessId(id, companyName);
            }
            return resultUtil;
        }catch (Exception e){
            log.error("业务部人员查询企业信息失败！",e);
            return ResultUtil.error("业务部人员查询企业信息失败！");
        }

    }

    /**
     * 根据企业名称获取企业信息
     * @auth
     * @param
     * @return
     */
    @PostMapping(value = "/selectByCompanyName")
    public List<TCompanyInfo> selectByCompanyName(@RequestBody CreateGoodsResourceVO record) {
        try {
            List<TCompanyInfo>  tCompanyInfoList = companyInfoService.selectByCompanyName(record.getCompanyName());
            return tCompanyInfoList;
        }catch (Exception e){
            log.error("根据企业名称获取企业信息失败！",e);
            return null;
        }

    }

    @PostMapping(value = "/selectByCompanyIdAndAccountId")
    public TCompanyInfo selectByCompanyIdAndAccountId(@RequestParam(value ="conpanyId") Integer companyId){
        try {
            return companyInfoService.selectByCompanyIdAndAccountId(companyId);
        }catch (Exception e){
            log.error("根据企业员工信息失败！",e);
            return null;
        }
    }

    /**
     * 根据登录的业务部负责人id查询下属业务部人员
     * @auth
     * @param
     * @return
     */
    @PostMapping(value = "/selectBusiness")
    public ResultUtil selectBusiness() {
        try {
            ResultUtil resultUtil = ResultUtil.ok();
            Integer id = CurrentUser.getCurrentUserID();
            if (null!=id && !"".equals(id)){
                resultUtil =  companyInfoService.selectBusiness(id);
            }
            return resultUtil;
        }catch (Exception e){
            log.error("业务部负责人查询下属业务部人员失败！",e);
            return ResultUtil.error("业务部负责人查询下属业务部人员失败！");
        }

    }

    /**
     * 新增企业信息
     * @param companyInfoAddVO
     * @return
     */
    @PostMapping(value = "/save")
    public ResultUtil save(@RequestBody TCompanyInfoAddVO companyInfoAddVO) throws Exception {
        try{
            ResultUtil result = companyInfoService.save(companyInfoAddVO);
            return result;
        }catch (Exception e){
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            log.error("dw-006:新增企业失败！",e);
            return ResultUtil.error("dw-006:新增企业失败！");
        }
    }


    /**
     * 修改企业信息
     * @param companyInfoAddVO
     * @return
     */
    @PostMapping(value = "/update")
    public ResultUtil update(@RequestBody TCompanyInfoAddVO companyInfoAddVO) throws Exception {
        try{
            ResultUtil result = companyInfoService.update(companyInfoAddVO);
            return result;
        }catch (Exception e){
            log.error("dw-007:修改企业失败！",e);
            return ResultUtil.error("dw-007:修改企业失败！");
        }
    }

    /**
     * 企业认证
     * @param companyInfoAddVO
     * @return
     * @auth zhangjiji
     */
    @PostMapping(value = "/auth")
    public ResultUtil auth(@RequestBody TCompanyInfoAddVO companyInfoAddVO) throws Exception {
        try{
            ResultUtil result = companyInfoService.update(companyInfoAddVO);
            return result;
        }catch (Exception e){
            log.error("",e);
            return ResultUtil.error("dw-008:企业认证失败！");
        }
    }

    @PostMapping(value = "selectCityTree")
    public ResultUtil selectCityTree(){
        try{
            return companyInfoService.selectCityTree();
        }catch (Exception e){
            log.error("获取省市县失败!", e);
            return ResultUtil.error("获取省市县失败");
        }
    }

    @PostMapping(value = "/delete")
    public ResultUtil delete(@RequestBody CompanyDeleteVO deleteId){
        try{
            companyInfoService.deleteById(deleteId);
            return ResultUtil.ok();
        }catch (Exception e){
            log.error(e.getMessage());
            return ResultUtil.error("dw-009:删除企业失败！");
        }
    }

    @PostMapping(value = "/selectCompany")
    public ResultUtil selectCompany(){
        ResultUtil resultUtil = companyInfoService.selectCompany();
        return resultUtil;
    }


    /**
     *  财务管理--企业钱包--企业列表
     * @param companyInfoQuery
     * @return
     * @auth sangbin
     */
    @PostMapping(value = "/selectCompanyByPage")
    public ResultUtil selectCompanyByPage(@RequestBody TCompanyInfoQuery companyInfoQuery) {
        try{
            ResultUtil list = companyInfoService.selectCompanyByPage(companyInfoQuery);
            return list;
        }catch (Exception e){
            log.error(e.getMessage());
            return ResultUtil.error("dw-040:企业钱包查询失败！");
        }
    }

    @PostMapping(value = "/applySubAccount")
    public ResultUtil applySubAccount(@RequestBody TCarrierEnduserCompanyRel rel) {
        try {
            return companyInfoService.applySubAccount(rel.getEnduserCompanyId(), rel.getCarrierId());
        }catch (RuntimeException e){
            log.error("企业申请子账号失败：",e);
            return ResultUtil.error("申请子账号失败");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2019/6/24 15:09
     *  @Description: 企业资金总览
     */
    @PostMapping(value = "/selectCompanyCapital")
    public ResultUtil selectCompanyCapital(){

        return companyInfoService.selectCompanyCapital();
    }
    /**
     *  @author: dingweibo
     *  @Date: 2021/3/24 9:33
     *  @Description: 近7日资金流动汇总
     */
    @PostMapping(value = "/selectCompanyCapitalWeek")
    public ResultUtil selectCompanyCapitalWeek(){

        return companyInfoService.selectCompanyCapitalWeek();
    }

    /**
    * @Description 单笔项目资金明细
    * <AUTHOR>
    * @Date   2019/7/23 14:13
    * @Param
    * @Return
    * @Exception
    *
    */
    @PostMapping(value = "/selectCompanyCapitalSingleDetailed")
    public ResultUtil selectCompanyCapitalSingleDetailed(@RequestBody TCompanyInfoQuery record){

        return companyInfoService.selectCompanyCapitalSingleDetailed(record);
    }

    /**
     * @Description 打包项目资金明细
     * <AUTHOR>
     * @Date   2019/7/23 14:13
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping(value = "/selectCompanyCapitalPackDetailed")
    public ResultUtil selectCompanyCapitalPackDetailed(@RequestBody TCompanyInfoQuery record){

        return companyInfoService.selectCompanyCapitalPackDetailed(record);
    }
    /**
     *  @author: dingweibo
     *  @Date: 2020/2/19 15:31
     *  @Description: 根据企业ID  承运方 ID 查询企业名称 承运方名称
     */
    @PostMapping(value = "/selectByCarrierIdAnrCompanyId")
    public TCompanyInfoVo selectByCarrierIdAnrCompanyId(@RequestBody TCompanyInfoVo record){

        return companyInfoService.selectByCarrierIdAnrCompanyId(record);
    }

    /**
     * <AUTHOR>
     * @Description 修改企业上链记录信息
     * @Date 2020/5/15 8:32 上午
     * @Param
     * @return
    **/
    @PostMapping("/updateCompanyUploadedInfo")
    public ResultUtil updateCompanyUploadedInfo(@RequestBody TCompanyInfo record) {
        return companyInfoService.updateCompanyUploadedInfo(record);
    }
}
