package com.lz.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lz.common.config.RedisUtil;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.JdEnum;
import com.lz.common.excelImport.ExcelDataProcessing;
import com.lz.common.model.BankCardReq;
import com.lz.common.model.BankCardResp;
import com.lz.common.model.CodeEnum;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.IntegrationBankCard;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.dao.TBankCardMapper;
import com.lz.example.TEnduserAccountExample;
import com.lz.model.*;
import com.lz.service.TBankCardService;
import com.lz.service.TEndUserInfoService;
import com.lz.service.TEndUserOpenRoleService;
import com.lz.service.TVerificationCodeLogService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.vo.TEndUserOpenRoleSearchVo;
import com.lz.vo.TEndUserOpenRoleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.FileInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "TEndUserOpenRole")
public class TEndUserOpenRoleController {

    @Autowired
    TEndUserOpenRoleService tEndUserOpenRoleService;

    @Autowired
    private TEndUserInfoService tEndUserInfoService;

    @Autowired
    private SysParamAPI sysParamAPI;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private TVerificationCodeLogService tVerificationCodeLogService;

    @Autowired
    private TBankCardService tBankCardService;

    @PostMapping("/selectByPageDriver")
    public ResultUtil selectByPageDrive(@RequestBody TEndUserOpenRoleSearchVo record){
        try {
            PageHelper.startPage(record.getPage(),record.getSize());
            List<TEndUserOpenRoleVo> list = tEndUserOpenRoleService.selectByPageDrive(record);
            PageInfo<TEndUserOpenRoleVo> pageInfo = new PageInfo<>(list);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(),pageInfo.getList(),pageInfo.getTotal());
        } catch (Exception e) {
            log.error("获取司机开户列表失败！",e);
            return ResultUtil.error("获取司机开户列表失败！");
        }
    }

    @PostMapping("/selectByPageCarCaptain")
    public ResultUtil selectByPageCarCaptain(@RequestBody TEndUserOpenRoleSearchVo record){
        try {
            PageHelper.startPage(record.getPage(),record.getSize());
            List<TEndUserOpenRoleVo> list = tEndUserOpenRoleService.selectByPageCarCaptain(record);
            PageInfo<TEndUserOpenRoleVo> pageInfo = new PageInfo<>(list);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(),pageInfo.getList(),pageInfo.getTotal());
        } catch (Exception e) {
            log.error("获取车队长开户列表失败！",e);
            return ResultUtil.error("获取车队长开户列表失败！");
        }
    }

    @PostMapping("/selectByPageAgentperson")
    public ResultUtil selectByPageAgentperson(@RequestBody TEndUserOpenRoleSearchVo record){
        try {
            PageHelper.startPage(record.getPage(),record.getSize());
            List<TEndUserOpenRoleVo> list = tEndUserOpenRoleService.selectByPageAgentperson(record);
            PageInfo<TEndUserOpenRoleVo> pageInfo = new PageInfo<>(list);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(),pageInfo.getList(),pageInfo.getTotal());
        } catch (Exception e) {
            log.error("获取经纪人开户列表失败！",e);
            return ResultUtil.error("获取经纪人开户列表失败！");
        }
    }

    @PostMapping("/selectByPageTransferor")
    public ResultUtil selectByPageTransferor(@RequestBody TEndUserOpenRoleSearchVo record){
        try {
            PageHelper.startPage(record.getPage(),record.getSize());
            List<TEndUserOpenRoleVo> list = tEndUserOpenRoleService.selectByPageTransferor(record);
            PageInfo<TEndUserOpenRoleVo> pageInfo = new PageInfo<>(list);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(),pageInfo.getList(),pageInfo.getTotal());
        } catch (Exception e) {
            log.error("获取转账人开户列表失败！",e);
            return ResultUtil.error("获取转账人开户列表失败！");
        }
    }

    @PostMapping("/selectDriverById")
    public ResultUtil selectDriverById(@RequestParam(value = "id") Integer id){
        try {
            TEndUserOpenRoleVo tEndUserOpenRoleVo = tEndUserOpenRoleService.selectDriverById(id);

            return ResultUtil.ok(tEndUserOpenRoleVo);
        } catch (Exception e) {
            log.error("获取司机开户详情失败！",e);
            return ResultUtil.error("获取司机开户详情失败！");
        }
    }

    @PostMapping("/selectCarCaptainById")
    public ResultUtil selectCarCaptainById(@RequestParam(value = "id") Integer id){
        try {
            TEndUserOpenRoleVo tEndUserOpenRoleVo = tEndUserOpenRoleService.selectCarCaptainById(id);

            return ResultUtil.ok(tEndUserOpenRoleVo);
        } catch (Exception e) {
            log.error("获取车队长开户详情失败！",e);
            return ResultUtil.error("获取车队长开户详情失败！");
        }
    }

    @PostMapping("/selectAgentpersonById")
    public ResultUtil selectAgentpersonById(@RequestParam(value = "id") Integer id){
        try {
            TEndUserOpenRoleVo tEndUserOpenRoleVo = tEndUserOpenRoleService.selectAgentpersonById(id);

            return ResultUtil.ok(tEndUserOpenRoleVo);
        } catch (Exception e) {
            log.error("获取经纪人开户详情失败！",e);
            return ResultUtil.error("获取经纪人开户详情失败！");
        }
    }

    @PostMapping("/selectTransferorById")
    public ResultUtil selectTransferorById(@RequestParam(value = "id") Integer id){
        try {
            TEndUserOpenRoleVo tEndUserOpenRoleVo = tEndUserOpenRoleService.selectTransferorById(id);

            return ResultUtil.ok(tEndUserOpenRoleVo);
        } catch (Exception e) {
            log.error("获取转账人开户详情失败！",e);
            return ResultUtil.error("获取转账人开户详情失败！");
        }
    }

    @PostMapping("/selectDriveOpenRole")
    public ResultUtil selectDriveOpenRole(@RequestBody TEndUserOpenRoleVo record){
        try {
            List<String> openRoleStatus = new ArrayList<>();
            if(record.getOpenIdcardArray().length > 10){
                return ResultUtil.error("批量开户不得超过10个！");
            }
            if(null != record.getIdcard() && !"".equals(record.getIdcard())){

                return  tEndUserOpenRoleService.selectDriveOpenRole(record);
            }else {
                List<String> oprenRoleIdcard = new ArrayList();

                for(int i = 0; i <record.getOpenIdcardArray().length;i++){
                    oprenRoleIdcard.add(record.getOpenIdcardArray()[i]);
                }
                for(String idcard : oprenRoleIdcard){
                    boolean ifOpenReal = true;

                    TEndUserOpenRoleVo tEndUserOpenRoleVo = tEndUserOpenRoleService.selectEndUserOpenRoleByIdCard(idcard, DictEnum.CTYPEDRVIVER.code);
                    if(null != tEndUserOpenRoleVo.getOpenStatus() && tEndUserOpenRoleVo.getOpenStatus().equals(JdEnum.OPENSUCCESS.code)){
                        openRoleStatus.add(tEndUserOpenRoleVo.getRealName()+"已开户成功，请勿重复开户!");
                    }else if(null != tEndUserOpenRoleVo.getOpenStatus() && tEndUserOpenRoleVo.getOpenStatus().equals(JdEnum.OPENHANDLE.code)){
                        openRoleStatus.add(tEndUserOpenRoleVo.getRealName()+"开户处理中，请勿重复操作!");
                    }else {
                        String errorInfo = "";
                        if(null != tEndUserOpenRoleVo){
                            if(null == tEndUserOpenRoleVo.getIdcardPhoto1() || "".equals(tEndUserOpenRoleVo.getIdcardPhoto1())
                                    || null == tEndUserOpenRoleVo.getIdcardPhoto2() || "".equals(tEndUserOpenRoleVo.getIdcardPhoto2())
                                    || null == tEndUserOpenRoleVo.getIdcardValidBeginning() || "".equals(tEndUserOpenRoleVo.getIdcardValidBeginning())
                                    || null == tEndUserOpenRoleVo.getIdcardValidUntil() || "".equals(tEndUserOpenRoleVo.getIdcardValidUntil())
                                    || null == tEndUserOpenRoleVo.getAddress() || "".equals(tEndUserOpenRoleVo.getAddress())){
                                errorInfo = tEndUserOpenRoleVo.getRealName();
                                ifOpenReal = false;
                            }
                            if(null == tEndUserOpenRoleVo.getIdcardPhoto1() || "".equals(tEndUserOpenRoleVo.getIdcardPhoto1())){
                                errorInfo = errorInfo+"身份证照片正面信息不全!";
                            }
                            if(null == tEndUserOpenRoleVo.getIdcardPhoto2() || "".equals(tEndUserOpenRoleVo.getIdcardPhoto2())){
                                errorInfo = errorInfo+"身份证照片反面信息不全!";
                            }
                            if(null == tEndUserOpenRoleVo.getIdcardValidBeginning() || "".equals(tEndUserOpenRoleVo.getIdcardValidBeginning())){
                                errorInfo = errorInfo+"身份证有效期结束时间信息不全!";
                            }
                            if(null == tEndUserOpenRoleVo.getIdcardValidUntil() || "".equals(tEndUserOpenRoleVo.getIdcardValidUntil())){
                                errorInfo = errorInfo+"身份证有效期开始时间信息不全!";
                            }
                            if(null == tEndUserOpenRoleVo.getAddress() || "".equals(tEndUserOpenRoleVo.getAddress())){
                                errorInfo = errorInfo+"住址信息不全!";
                            }
                        }

                        //根据id查询与account关系
                        TEnduserAccountExample example = new TEnduserAccountExample();
                        TEnduserAccountExample.Criteria cr = example.createCriteria();
                        cr.andEnduserIdEqualTo(tEndUserOpenRoleVo.getEndUserId());
                        //根据accountId查询银行卡
                        List<TEnduserAccount>  tEnduserAccountList = tEndUserOpenRoleService.tEndUserAccountSelectByExample(example);

                        List<TEndUserInfo> tEndUserInfoList = tEndUserOpenRoleService.selectOneselfRealName(tEnduserAccountList.get(0).getAccountId());
                        if(tEnduserAccountList.size()>0){
                            List<TBankCard> tBankCardList = tEndUserOpenRoleService.selectBankCardNotJdList(tEnduserAccountList.get(0).getAccountId(),
                                    tEndUserInfoList.get(0).getRealName(),tEndUserInfoList.get(0).getIdcard());
                            if(tBankCardList.size() > 0){
                                tEndUserOpenRoleVo.setBankCardId(tBankCardList.get(0).getId());
                                tEndUserOpenRoleVo.setOpenRoleCardNo(tBankCardList.get(0).getCardNo());
                                tEndUserOpenRoleVo.setOpenRoleCardOwnerPhone(tEndUserOpenRoleVo.getPhone());
                            }else {
                                ifOpenReal = false;
                                if(errorInfo.length() > 0){
                                    errorInfo = errorInfo+"名下暂无银行卡不能进行京东开户！";
                                }else {
                                    errorInfo = tEndUserOpenRoleVo.getRealName()+"名下暂无银行卡不能进行京东开户！";
                                }
                            }
                        }
                        //进行京东开户
                        if(ifOpenReal){
                            if(tEndUserOpenRoleVo.getIdcardValidBeginning().getTime() > tEndUserOpenRoleVo.getIdcardValidUntil().getTime()){
                                Date idcardValidBeginning = tEndUserOpenRoleVo.getIdcardValidBeginning();
                                tEndUserOpenRoleVo.setIdcardValidBeginning(tEndUserOpenRoleVo.getIdcardValidUntil());
                                tEndUserOpenRoleVo.setIdcardValidUntil(idcardValidBeginning);
                            }
                            ResultUtil resultUtil = tEndUserOpenRoleService.selectDriveOpenRole(tEndUserOpenRoleVo);
                            if(!resultUtil.getCode().equals(CodeEnum.SUCCESS.getCode())){
                                String msg = tEndUserOpenRoleVo.getRealName()+"："+resultUtil.getMsg()+"!";
                                openRoleStatus.add(msg);
                            }else {
                                String msg = tEndUserOpenRoleVo.getRealName()+"开户申请成功!";
                                openRoleStatus.add(msg);
                            }
                        }else {
                            openRoleStatus.add(errorInfo);
                        }
                    }
                }
                if(oprenRoleIdcard.size() == 0){
                    return ResultUtil.error("京东开户失败请重新进行开户!!");
                }else {
                    return ResultUtil.ok(openRoleStatus);
                }
            }
        } catch (Exception e) {
            log.error("司机开户申请失败！",e);
            return ResultUtil.error("司机开户申请失败！");
        }
    }

    @PostMapping("/selectCarCaptainOpenRole")
    public ResultUtil selectCarCaptainOpenRole(@RequestBody TEndUserOpenRoleVo record){
        try {
            List<String> openRoleStatus = new ArrayList<>();
            if(record.getOpenIdcardArray().length > 10){
                return ResultUtil.error("批量开户不得超过10个！");
            }
            if(null != record.getIdcard() && !"".equals(record.getIdcard())){

                return  tEndUserOpenRoleService.selectDriveOpenRole(record);
            }else {
                List<String> oprenRoleIdcard = new ArrayList();

                for(int i = 0; i <record.getOpenIdcardArray().length;i++){
                    oprenRoleIdcard.add(record.getOpenIdcardArray()[i]);
                }
                for(String idcard : oprenRoleIdcard){
                    boolean ifOpenReal = true;

                    TEndUserOpenRoleVo tEndUserOpenRoleVo = tEndUserOpenRoleService.selectEndUserOpenRoleByIdCard(idcard, DictEnum.CTYPECAPTAIN.code);
                    if(null != tEndUserOpenRoleVo.getOpenStatus() && tEndUserOpenRoleVo.getOpenStatus().equals(JdEnum.OPENSUCCESS.code)){
                        openRoleStatus.add(tEndUserOpenRoleVo.getRealName()+"已开户成功，请勿重复开户!");
                    }else if(null != tEndUserOpenRoleVo.getOpenStatus() && tEndUserOpenRoleVo.getOpenStatus().equals(JdEnum.OPENHANDLE.code)){
                        openRoleStatus.add(tEndUserOpenRoleVo.getRealName()+"开户处理中，请勿重复操作!");
                    }else {
                        String errorInfo = "";
                        if(null != tEndUserOpenRoleVo){
                            if(null == tEndUserOpenRoleVo.getIdcardPhoto1() || "".equals(tEndUserOpenRoleVo.getIdcardPhoto1())
                                    || null == tEndUserOpenRoleVo.getIdcardPhoto2() || "".equals(tEndUserOpenRoleVo.getIdcardPhoto2())
                                    || null == tEndUserOpenRoleVo.getIdcardValidBeginning() || "".equals(tEndUserOpenRoleVo.getIdcardValidBeginning())
                                    || null == tEndUserOpenRoleVo.getIdcardValidUntil() || "".equals(tEndUserOpenRoleVo.getIdcardValidUntil())
                                    || null == tEndUserOpenRoleVo.getAddress() || "".equals(tEndUserOpenRoleVo.getAddress())){
                                errorInfo = tEndUserOpenRoleVo.getRealName();
                                ifOpenReal = false;
                            }
                            if(null == tEndUserOpenRoleVo.getIdcardPhoto1() || "".equals(tEndUserOpenRoleVo.getIdcardPhoto1())){
                                errorInfo = errorInfo+"身份证照片正面信息不全!";
                            }
                            if(null == tEndUserOpenRoleVo.getIdcardPhoto2() || "".equals(tEndUserOpenRoleVo.getIdcardPhoto2())){
                                errorInfo = errorInfo+"身份证照片反面信息不全!";
                            }
                            if(null == tEndUserOpenRoleVo.getIdcardValidBeginning() || "".equals(tEndUserOpenRoleVo.getIdcardValidBeginning())){
                                errorInfo = errorInfo+"身份证有效期结束时间信息不全!";
                            }
                            if(null == tEndUserOpenRoleVo.getIdcardValidUntil() || "".equals(tEndUserOpenRoleVo.getIdcardValidUntil())){
                                errorInfo = errorInfo+"身份证有效期开始时间信息不全!";
                            }
                            if(null == tEndUserOpenRoleVo.getAddress() || "".equals(tEndUserOpenRoleVo.getAddress())){
                                errorInfo = errorInfo+"住址信息不全!";
                            }
                        }

                        //根据id查询与account关系
                        TEnduserAccountExample example = new TEnduserAccountExample();
                        TEnduserAccountExample.Criteria cr = example.createCriteria();
                        cr.andEnduserIdEqualTo(tEndUserOpenRoleVo.getEndUserId());
                        //根据accountId查询银行卡
                        List<TEnduserAccount>  tEnduserAccountList = tEndUserOpenRoleService.tEndUserAccountSelectByExample(example);

                        List<TEndUserInfo> tEndUserInfoList = tEndUserOpenRoleService.selectOneselfRealName(tEnduserAccountList.get(0).getAccountId());
                        if(tEnduserAccountList.size()>0){
                            List<TBankCard> tBankCardList = tEndUserOpenRoleService.selectBankCardNotJdList(tEnduserAccountList.get(0).getAccountId(),
                                    tEndUserInfoList.get(0).getRealName(),tEndUserInfoList.get(0).getIdcard());
                            if(tBankCardList.size() > 0){
                                tEndUserOpenRoleVo.setBankCardId(tBankCardList.get(0).getId());
                                tEndUserOpenRoleVo.setOpenRoleCardNo(tBankCardList.get(0).getCardNo());
                                tEndUserOpenRoleVo.setOpenRoleCardOwnerPhone(tEndUserOpenRoleVo.getPhone());
                            }else {
                                ifOpenReal = false;
                                if(errorInfo.length() > 0){
                                    errorInfo = errorInfo+"名下暂无银行卡不能进行京东开户！";
                                }else {
                                    errorInfo = tEndUserOpenRoleVo.getRealName()+"名下暂无银行卡不能进行京东开户！";
                                }
                            }
                        }
                        //进行京东开户
                        if(ifOpenReal){
                            if(tEndUserOpenRoleVo.getIdcardValidBeginning().getTime() > tEndUserOpenRoleVo.getIdcardValidUntil().getTime()){
                                Date idcardValidBeginning = tEndUserOpenRoleVo.getIdcardValidBeginning();
                                tEndUserOpenRoleVo.setIdcardValidBeginning(tEndUserOpenRoleVo.getIdcardValidUntil());
                                tEndUserOpenRoleVo.setIdcardValidUntil(idcardValidBeginning);
                            }
                            ResultUtil resultUtil = tEndUserOpenRoleService.selectDriveOpenRole(tEndUserOpenRoleVo);
                            if(!resultUtil.getCode().equals(CodeEnum.SUCCESS.getCode())){
                                String msg = tEndUserOpenRoleVo.getRealName()+"："+resultUtil.getMsg()+"!";
                                openRoleStatus.add(msg);
                            }else {
                                String msg = tEndUserOpenRoleVo.getRealName()+"开户申请成功!";
                                openRoleStatus.add(msg);
                            }
                        }else {
                            openRoleStatus.add(errorInfo);
                        }
                    }
                }
                if(oprenRoleIdcard.size() == 0){
                    return ResultUtil.error("京东开户失败请重新进行开户!!");
                }else {
                    return ResultUtil.ok(openRoleStatus);
                }
            }
        } catch (Exception e) {
            log.error("车队长开户申请失败！",e);
            return ResultUtil.error("车队长开户申请失败！");
        }
    }

    @PostMapping("/selectAgentpersonOpenRole")
    public ResultUtil selectAgentpersonOpenRole(@RequestBody TEndUserOpenRoleVo record){
        try {
            List<String> openRoleStatus = new ArrayList<>();
            if(record.getOpenIdcardArray().length > 10){
                return ResultUtil.error("批量开户不得超过10个！");
            }
            if(null != record.getIdcard() && !"".equals(record.getIdcard())){

                return  tEndUserOpenRoleService.selectDriveOpenRole(record);
            }else {
                List<String> oprenRoleIdcard = new ArrayList();

                for(int i = 0; i <record.getOpenIdcardArray().length;i++){
                    oprenRoleIdcard.add(record.getOpenIdcardArray()[i]);
                }
                for(String idcard : oprenRoleIdcard){
                    boolean ifOpenReal = true;

                    TEndUserOpenRoleVo tEndUserOpenRoleVo = tEndUserOpenRoleService.selectEndUserOpenRoleByIdCard(idcard, DictEnum.CTYPEAGENTPERSON.code);
                    if(null != tEndUserOpenRoleVo.getOpenStatus() && tEndUserOpenRoleVo.getOpenStatus().equals(JdEnum.OPENSUCCESS.code)){
                        openRoleStatus.add(tEndUserOpenRoleVo.getRealName()+"已开户成功，请勿重复开户!");
                    }else if(null != tEndUserOpenRoleVo.getOpenStatus() && tEndUserOpenRoleVo.getOpenStatus().equals(JdEnum.OPENHANDLE.code)){
                        openRoleStatus.add(tEndUserOpenRoleVo.getRealName()+"开户处理中，请勿重复操作!");
                    }else {
                        String errorInfo = "";
                        if(null != tEndUserOpenRoleVo){
                            if(null == tEndUserOpenRoleVo.getIdcardPhoto1() || "".equals(tEndUserOpenRoleVo.getIdcardPhoto1())
                                    || null == tEndUserOpenRoleVo.getIdcardPhoto2() || "".equals(tEndUserOpenRoleVo.getIdcardPhoto2())
                                    || null == tEndUserOpenRoleVo.getIdcardValidBeginning() || "".equals(tEndUserOpenRoleVo.getIdcardValidBeginning())
                                    || null == tEndUserOpenRoleVo.getIdcardValidUntil() || "".equals(tEndUserOpenRoleVo.getIdcardValidUntil())
                                    || null == tEndUserOpenRoleVo.getAddress() || "".equals(tEndUserOpenRoleVo.getAddress())){
                                errorInfo = tEndUserOpenRoleVo.getRealName();
                                ifOpenReal = false;
                            }
                            if(null == tEndUserOpenRoleVo.getIdcardPhoto1() || "".equals(tEndUserOpenRoleVo.getIdcardPhoto1())){
                                errorInfo = errorInfo+"身份证照片正面信息不全!";
                            }
                            if(null == tEndUserOpenRoleVo.getIdcardPhoto2() || "".equals(tEndUserOpenRoleVo.getIdcardPhoto2())){
                                errorInfo = errorInfo+"身份证照片反面信息不全!";
                            }
                            if(null == tEndUserOpenRoleVo.getIdcardValidBeginning() || "".equals(tEndUserOpenRoleVo.getIdcardValidBeginning())){
                                errorInfo = errorInfo+"身份证有效期结束时间信息不全!";
                            }
                            if(null == tEndUserOpenRoleVo.getIdcardValidUntil() || "".equals(tEndUserOpenRoleVo.getIdcardValidUntil())){
                                errorInfo = errorInfo+"身份证有效期开始时间信息不全!";
                            }
                            if(null == tEndUserOpenRoleVo.getAddress() || "".equals(tEndUserOpenRoleVo.getAddress())){
                                errorInfo = errorInfo+"住址信息不全!";
                            }
                        }

                        //根据id查询与account关系
                        TEnduserAccountExample example = new TEnduserAccountExample();
                        TEnduserAccountExample.Criteria cr = example.createCriteria();
                        cr.andEnduserIdEqualTo(tEndUserOpenRoleVo.getEndUserId());
                        //根据accountId查询银行卡
                        List<TEnduserAccount>  tEnduserAccountList = tEndUserOpenRoleService.tEndUserAccountSelectByExample(example);

                        List<TEndUserInfo> tEndUserInfoList = tEndUserOpenRoleService.selectOneselfRealName(tEnduserAccountList.get(0).getAccountId());
                        if(tEnduserAccountList.size()>0){
                            List<TBankCard> tBankCardList = tEndUserOpenRoleService.selectBankCardNotJdList(tEnduserAccountList.get(0).getAccountId(),
                                    tEndUserInfoList.get(0).getRealName(),tEndUserInfoList.get(0).getIdcard());
                            if(tBankCardList.size() > 0){
                                tEndUserOpenRoleVo.setBankCardId(tBankCardList.get(0).getId());
                                tEndUserOpenRoleVo.setOpenRoleCardNo(tBankCardList.get(0).getCardNo());
                                tEndUserOpenRoleVo.setOpenRoleCardOwnerPhone(tEndUserOpenRoleVo.getPhone());
                            }else {
                                ifOpenReal = false;
                                if(errorInfo.length() > 0){
                                    errorInfo = errorInfo+"名下暂无银行卡不能进行京东开户！";
                                }else {
                                    errorInfo = tEndUserOpenRoleVo.getRealName()+"名下暂无银行卡不能进行京东开户！";
                                }
                            }
                        }
                        //进行京东开户
                        if(ifOpenReal){
                            if(tEndUserOpenRoleVo.getIdcardValidBeginning().getTime() > tEndUserOpenRoleVo.getIdcardValidUntil().getTime()){
                                Date idcardValidBeginning = tEndUserOpenRoleVo.getIdcardValidBeginning();
                                tEndUserOpenRoleVo.setIdcardValidBeginning(tEndUserOpenRoleVo.getIdcardValidUntil());
                                tEndUserOpenRoleVo.setIdcardValidUntil(idcardValidBeginning);
                            }
                            ResultUtil resultUtil = tEndUserOpenRoleService.selectDriveOpenRole(tEndUserOpenRoleVo);
                            if(!resultUtil.getCode().equals(CodeEnum.SUCCESS.getCode())){
                                String msg = tEndUserOpenRoleVo.getRealName()+"："+resultUtil.getMsg()+"!";
                                openRoleStatus.add(msg);
                            }else {
                                String msg = tEndUserOpenRoleVo.getRealName()+"开户申请成功!";
                                openRoleStatus.add(msg);
                            }
                        }else {
                            openRoleStatus.add(errorInfo);
                        }
                    }
                }
                if(oprenRoleIdcard.size() == 0){
                    return ResultUtil.error("京东开户失败请重新进行开户!!");
                }else {
                    return ResultUtil.ok(openRoleStatus);
                }
            }
        } catch (Exception e) {
            log.error("经纪人开户申请失败！",e);
            return ResultUtil.error("经纪人开户申请失败！");
        }
    }

    @PostMapping("/selectTransferorOpenRole")
    public ResultUtil selectTransferorOpenRole(@RequestBody TEndUserOpenRoleVo record){
        try {
            List<String> openRoleStatus = new ArrayList<>();
            if(record.getOpenIdcardArray().length > 10){
                return ResultUtil.error("批量开户不得超过10个！");
            }
            if(null != record.getIdcard() && !"".equals(record.getIdcard())){

                return  tEndUserOpenRoleService.selectDriveOpenRole(record);
            }else {
                List<String> oprenRoleIdcard = new ArrayList();

                for(int i = 0; i <record.getOpenIdcardArray().length;i++){
                    oprenRoleIdcard.add(record.getOpenIdcardArray()[i]);
                }
                for(String idcard : oprenRoleIdcard){
                    boolean ifOpenReal = true;

                    TEndUserOpenRoleVo tEndUserOpenRoleVo = tEndUserOpenRoleService.selectEndUserOpenRoleByIdCard(idcard, DictEnum.CTYPETRANSFEROR.code);
                    if(null != tEndUserOpenRoleVo.getOpenStatus() && tEndUserOpenRoleVo.getOpenStatus().equals(JdEnum.OPENSUCCESS.code)){
                        openRoleStatus.add(tEndUserOpenRoleVo.getRealName()+"已开户成功，请勿重复开户!");
                    }else if(null != tEndUserOpenRoleVo.getOpenStatus() && tEndUserOpenRoleVo.getOpenStatus().equals(JdEnum.OPENHANDLE.code)){
                        openRoleStatus.add(tEndUserOpenRoleVo.getRealName()+"开户处理中，请勿重复操作!");
                    }else {
                        String errorInfo = "";
                        if(null != tEndUserOpenRoleVo){
                            if(null == tEndUserOpenRoleVo.getIdcardPhoto1() || "".equals(tEndUserOpenRoleVo.getIdcardPhoto1())
                                    || null == tEndUserOpenRoleVo.getIdcardPhoto2() || "".equals(tEndUserOpenRoleVo.getIdcardPhoto2())
                                    || null == tEndUserOpenRoleVo.getIdcardValidBeginning() || "".equals(tEndUserOpenRoleVo.getIdcardValidBeginning())
                                    || null == tEndUserOpenRoleVo.getIdcardValidUntil() || "".equals(tEndUserOpenRoleVo.getIdcardValidUntil())
                                    || null == tEndUserOpenRoleVo.getAddress() || "".equals(tEndUserOpenRoleVo.getAddress())){
                                errorInfo = tEndUserOpenRoleVo.getRealName();
                                ifOpenReal = false;
                            }
                            if(null == tEndUserOpenRoleVo.getIdcardPhoto1() || "".equals(tEndUserOpenRoleVo.getIdcardPhoto1())){
                                errorInfo = errorInfo+"身份证照片正面信息不全!";
                            }
                            if(null == tEndUserOpenRoleVo.getIdcardPhoto2() || "".equals(tEndUserOpenRoleVo.getIdcardPhoto2())){
                                errorInfo = errorInfo+"身份证照片反面信息不全!";
                            }
                            if(null == tEndUserOpenRoleVo.getIdcardValidBeginning() || "".equals(tEndUserOpenRoleVo.getIdcardValidBeginning())){
                                errorInfo = errorInfo+"身份证有效期结束时间信息不全!";
                            }
                            if(null == tEndUserOpenRoleVo.getIdcardValidUntil() || "".equals(tEndUserOpenRoleVo.getIdcardValidUntil())){
                                errorInfo = errorInfo+"身份证有效期开始时间信息不全!";
                            }
                            if(null == tEndUserOpenRoleVo.getAddress() || "".equals(tEndUserOpenRoleVo.getAddress())){
                                errorInfo = errorInfo+"住址信息不全!";
                            }
                        }

                        //根据id查询与account关系
                        TEnduserAccountExample example = new TEnduserAccountExample();
                        TEnduserAccountExample.Criteria cr = example.createCriteria();
                        cr.andEnduserIdEqualTo(tEndUserOpenRoleVo.getEndUserId());
                        //根据accountId查询银行卡
                        List<TEnduserAccount>  tEnduserAccountList = tEndUserOpenRoleService.tEndUserAccountSelectByExample(example);

                        List<TEndUserInfo> tEndUserInfoList = tEndUserOpenRoleService.selectOneselfRealName(tEnduserAccountList.get(0).getAccountId());
                        if(tEnduserAccountList.size()>0){
                            List<TBankCard> tBankCardList = tEndUserOpenRoleService.selectBankCardNotJdList(tEnduserAccountList.get(0).getAccountId(),
                                    tEndUserInfoList.get(0).getRealName(),tEndUserInfoList.get(0).getIdcard());
                            if(tBankCardList.size() > 0){
                                tEndUserOpenRoleVo.setBankCardId(tBankCardList.get(0).getId());
                                tEndUserOpenRoleVo.setOpenRoleCardNo(tBankCardList.get(0).getCardNo());
                                tEndUserOpenRoleVo.setOpenRoleCardOwnerPhone(tEndUserOpenRoleVo.getPhone());
                            }else {
                                ifOpenReal = false;
                                if(errorInfo.length() > 0){
                                    errorInfo = errorInfo+"名下暂无银行卡不能进行京东开户！";
                                }else {
                                    errorInfo = tEndUserOpenRoleVo.getRealName()+"名下暂无银行卡不能进行京东开户！";
                                }
                            }
                        }
                        //进行京东开户
                        if(ifOpenReal){
                            if(tEndUserOpenRoleVo.getIdcardValidBeginning().getTime() > tEndUserOpenRoleVo.getIdcardValidUntil().getTime()){
                                Date idcardValidBeginning = tEndUserOpenRoleVo.getIdcardValidBeginning();
                                tEndUserOpenRoleVo.setIdcardValidBeginning(tEndUserOpenRoleVo.getIdcardValidUntil());
                                tEndUserOpenRoleVo.setIdcardValidUntil(idcardValidBeginning);
                            }
                            ResultUtil resultUtil = tEndUserOpenRoleService.selectDriveOpenRole(tEndUserOpenRoleVo);
                            if(!resultUtil.getCode().equals(CodeEnum.SUCCESS.getCode())){
                                String msg = tEndUserOpenRoleVo.getRealName()+"："+resultUtil.getMsg()+"!";
                                openRoleStatus.add(msg);
                            }else {
                                String msg = tEndUserOpenRoleVo.getRealName()+"开户申请成功!";
                                openRoleStatus.add(msg);
                            }
                        }else {
                            openRoleStatus.add(errorInfo);
                        }
                    }
                }
                if(oprenRoleIdcard.size() == 0){
                    return ResultUtil.error("京东开户失败请重新进行开户!!");
                }else {
                    return ResultUtil.ok(openRoleStatus);
                }
            }
        } catch (Exception e) {
            log.error("转账人开户申请失败！",e);
            return ResultUtil.error("转账人开户申请失败！");
        }
    }

    @PostMapping("/excelBatchOpenRole")
    @ResponseBody
    public ResultUtil excelBatchOpenRole(@RequestBody List<TOcrOpenRole> excelBatchAddAddress){
        List<String> openRoleStatus = new ArrayList<>();

        try{
            for(TOcrOpenRole data : excelBatchAddAddress){
                TOcrOpenRole tOcrOpenRole = new TOcrOpenRole();
                String errorInfo ="";
                Boolean ifSuccess = true;
                if(null != data.getIdcard() || null != data.getIdentityRole()){
                    boolean ifOpenReal = true;
                    TEndUserOpenRoleSearchVo tEndUserOpenRoleSearchVo = new TEndUserOpenRoleSearchVo();
                    tEndUserOpenRoleSearchVo.setIdcard(data.getIdcard());
                    tEndUserOpenRoleSearchVo.setUserLogisticsRole(data.getIdentityRole());

                    List<TEndUserOpenRoleVo> tEndUserOpenRoleVoList = tEndUserOpenRoleService.selectByPage(tEndUserOpenRoleSearchVo);
                    if(tEndUserOpenRoleVoList.size() > 1 || tEndUserOpenRoleVoList.size() == 0){
                        errorInfo = errorInfo + data.getIdcard() + data.getIdentityRole() + tEndUserOpenRoleVoList.size() +"查出多条或者零条数据!";
                        openRoleStatus.add(errorInfo);
                    }else if(tEndUserOpenRoleVoList.get(0).getOpenStatus().equals("申请成功") ||tEndUserOpenRoleVoList.get(0).getOpenStatus().equals("申请中")){
                        errorInfo = errorInfo + data.getIdcard() + data.getIdentityRole() + tEndUserOpenRoleVoList.size() +"该条信息的状态是申请成功或申请中!";
                        openRoleStatus.add(errorInfo);
                    } else{
                        if(null != tEndUserOpenRoleVoList.get(0).getId()){
                            tOcrOpenRole.setEndUserId(tEndUserOpenRoleVoList.get(0).getId());
                        }
                        if(null != tEndUserOpenRoleVoList.get(0)){
                            if(null == tEndUserOpenRoleVoList.get(0).getIdcardPhoto1() || "".equals(tEndUserOpenRoleVoList.get(0).getIdcardPhoto1())
                                    || null == tEndUserOpenRoleVoList.get(0).getIdcardPhoto2() || "".equals(tEndUserOpenRoleVoList.get(0).getIdcardPhoto2())
                                    || null == tEndUserOpenRoleVoList.get(0).getIdcardValidBeginning() || "".equals(tEndUserOpenRoleVoList.get(0).getIdcardValidBeginning())
                                    || null == tEndUserOpenRoleVoList.get(0).getIdcardValidUntil() || "".equals(tEndUserOpenRoleVoList.get(0).getIdcardValidUntil())
                                    || null == tEndUserOpenRoleVoList.get(0).getAddress() || "".equals(tEndUserOpenRoleVoList.get(0).getAddress())){
                                errorInfo = tEndUserOpenRoleVoList.get(0).getRealName();
                                ifOpenReal = false;
                            }
                            if(null == tEndUserOpenRoleVoList.get(0).getIdcardPhoto1() || "".equals(tEndUserOpenRoleVoList.get(0).getIdcardPhoto1())){
                                errorInfo = errorInfo+"身份证照片正面信息不全!";
                            }
                            if(null == tEndUserOpenRoleVoList.get(0).getIdcardPhoto2() || "".equals(tEndUserOpenRoleVoList.get(0).getIdcardPhoto2())){
                                errorInfo = errorInfo+"身份证照片反面信息不全!";
                            }
                            if(null == tEndUserOpenRoleVoList.get(0).getIdcardValidBeginning() || "".equals(tEndUserOpenRoleVoList.get(0).getIdcardValidBeginning())){
                                errorInfo = errorInfo+"身份证有效期结束时间信息不全!";
                            }
                            if(null == tEndUserOpenRoleVoList.get(0).getIdcardValidUntil() || "".equals(tEndUserOpenRoleVoList.get(0).getIdcardValidUntil())){
                                errorInfo = errorInfo+"身份证有效期开始时间信息不全!";
                            }
                            if(null == tEndUserOpenRoleVoList.get(0).getAddress() || "".equals(tEndUserOpenRoleVoList.get(0).getAddress())){
                                errorInfo = errorInfo+"住址信息不全!";
                            }
                        }

                        //根据id查询与account关系
                        TEnduserAccountExample example = new TEnduserAccountExample();
                        TEnduserAccountExample.Criteria cr = example.createCriteria();
                        cr.andEnduserIdEqualTo(tEndUserOpenRoleVoList.get(0).getEndUserId());
                        //根据accountId查询银行卡
                        List<TEnduserAccount>  tEnduserAccountList = tEndUserOpenRoleService.tEndUserAccountSelectByExample(example);

                        List<TEndUserInfo> tEndUserInfoList = tEndUserOpenRoleService.selectOneselfRealName(tEnduserAccountList.get(0).getAccountId());
                        if(tEnduserAccountList.size()>0){
                            List<TBankCard> tBankCardList = tEndUserOpenRoleService.selectBankCardNotJdList(tEnduserAccountList.get(0).getAccountId(),
                                    tEndUserInfoList.get(0).getRealName(),tEndUserInfoList.get(0).getIdcard());
                            if(tBankCardList.size() > 0){
                                tEndUserOpenRoleVoList.get(0).setBankCardId(tBankCardList.get(0).getId());
                                tEndUserOpenRoleVoList.get(0).setOpenRoleCardNo(tBankCardList.get(0).getCardNo());
                                tEndUserOpenRoleVoList.get(0).setOpenRoleCardOwnerPhone(tEndUserOpenRoleVoList.get(0).getPhone());
                            }else {
                                ifOpenReal = false;
                                if(errorInfo.length() > 0){
                                    errorInfo = errorInfo+"名下暂无银行卡不能进行京东开户！";
                                }else {
                                    errorInfo = tEndUserOpenRoleVoList.get(0).getRealName()+"名下暂无银行卡不能进行京东开户！";
                                }
                            }
                        }
                        //进行京东开户
                        if(ifOpenReal){
                            ResultUtil resultUtil = tEndUserOpenRoleService.selectDriveOpenRole(tEndUserOpenRoleVoList.get(0));
                            if(!resultUtil.getCode().equals(CodeEnum.SUCCESS.getCode())){
                                String msg = tEndUserOpenRoleVoList.get(0).getRealName()+"："+resultUtil.getMsg()+"!";
                                openRoleStatus.add(msg);
                            }else {
                                String msg = tEndUserOpenRoleVoList.get(0).getRealName()+"开户申请成功!";
                                openRoleStatus.add(msg);
                            }
                            ifSuccess = false;
                        }else {
                            openRoleStatus.add(errorInfo);
                        }
                    }
                }else {
                    if(null != data.getName()){
                        errorInfo = errorInfo + data.getName();
                    }
                    if(null != data.getPhone()){
                        errorInfo = errorInfo + data.getPhone();
                    }
                    if(null != data.getIdcard()){
                        errorInfo = errorInfo + data.getIdcard();
                    }
                    errorInfo = errorInfo + "信息错误请检查信息!";
                    openRoleStatus.add(errorInfo);
                }
                if(null != data.getIdcard()){
                    tOcrOpenRole.setIdcard(data.getIdcard());
                }
                if(null != data.getIdentityRole()){
                    tOcrOpenRole.setIdentityRole(data.getIdentityRole());
                }
                if(null != data.getName()){
                    tOcrOpenRole.setName(data.getName());
                }
                if(null != data.getPhone()){
                    tOcrOpenRole.setPhone(data.getPhone());
                }
                tOcrOpenRole.setProcessingType("openrole");
                tOcrOpenRole.setProcessingData(errorInfo);
                tOcrOpenRole.setIfSuccess(ifSuccess);
                tOcrOpenRole.setCreateTime(new Date());
                tOcrOpenRole.setUpdateTime(new Date());
                tEndUserInfoService.insertOcrOpenRoleInfo(tOcrOpenRole);

                Thread.sleep(8000);
            }
            log.info("excel批量京东开户返回数据:"+ JSON.toJSONString(openRoleStatus));
            return ResultUtil.ok();
        }catch (Exception e){
            log.error("excel批量开户申请失败！",e);
            return ResultUtil.error("excel批量开户申请失败！");
        }
    }

    @PostMapping("/updateAccountOpeningApplicationStatus")
    public ResultUtil updateAccountOpeningApplicationStatus(@RequestBody TEndUserOpenRoleVo record){
        try {

            ResultUtil resultUtil = tEndUserOpenRoleService.updateAccountOpeningApplicationStatus(record);

            return resultUtil;

        } catch (Exception e) {
            log.error("更新开户状态失败！",e);
            return ResultUtil.error("更新开户状态失败！");
        }
    }


    /**
     *  @author: dingweibo
     *  @Date: 2021/11/25 14:25
     *  @Description: 非本人开户回显
     */
    @PostMapping("/selectNoPersonalOpenRoleById")
    public ResultUtil selectNoPersonalOpenRoleById(@RequestParam(value = "id") Integer id){
        try {
            return ResultUtil.ok(tEndUserOpenRoleService.selectNoPersonalOpenRoleById(id));
        } catch (Exception e) {
            log.error("获取司机开户详情失败！",e);
            return ResultUtil.error("获取司机开户详情失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/11/24 14:55
     *  @Description: 非本人开户
     */
    @PostMapping("/noPersonalOpenRole")
    public ResultUtil noPersonalOpenRole(@RequestBody TEndUserOpenRoleVo record){
        try {
            ResultUtil resultUtil = tEndUserOpenRoleService.noPersonalOpenRole(record);
            return resultUtil;
        } catch (Exception e) {
            log.error("非本人开户失败！",e);
            return ResultUtil.error("非本人开户失败！");
        }
    }
    //用户京东开户回显信息
    @PostMapping("/echoUserOpenRoleInfo")
    public ResultUtil echoUserOpenRoleInfo(){
        try{
            Integer enuserid = CurrentUser.getEndUserId();
            return tEndUserOpenRoleService.echoUserOpenRoleInfo(enuserid);
        }catch (Exception e){
            log.error("查询开设电子账户信息失败!",e);
            return ResultUtil.error("查询开设电子账户信息失败!");
        }
    }


    @PostMapping("/openRoleIntegrationCard")
    public ResultUtil openRoleIntegrationCard(@RequestBody TEndUserOpenRoleVo param){
        try{
            Boolean state = false;
            Integer accountId = CurrentUser.getUserAccountId();
            List<TEndUserInfo> tEndUserInfoList = tEndUserOpenRoleService.selectOneselfRealName(accountId);
            if(tEndUserInfoList.size()>0) {
                List<TBankCard> tBankCardList = tEndUserOpenRoleService.selectBankCardNotJdList(accountId,
                        tEndUserInfoList.get(0).getRealName(), tEndUserInfoList.get(0).getIdcard());
                if(tBankCardList.size() >0){
                    for(TBankCard tBankCard : tBankCardList){
                        state = true;
                        if(tBankCard.getCardNo().equals(param.getOpenRoleCardNo())){
                            state =false;
                        }
                    }
                }else {
                    state = true;
                }
            }else {
                ResultUtil.error("银行卡绑定失败请重新操作");
            }

            if(state){
                SysParam sysParam = sysParamAPI.getParamByKey("IFKBIN");
                //是否开启 银行卡三要素校验 0否 1是
                if("1".equals(sysParam.getParamValue())){
                    IntegrationBankCard ibc = new IntegrationBankCard();
                    BankCardReq bankCardReq = new BankCardReq();
                    bankCardReq.setBankCard(param.getOpenRoleCardNo());
                    bankCardReq.setIdCard(param.getIdcard());
                    bankCardReq.setName(param.getCardOwner());
                    BankCardResp bankCardRespa = ibc.checkBankCard(bankCardReq);
                    if ( StringUtils.isBlank(bankCardRespa.getResult())
                            || !BankCardResp.RESULT_UNANIMOUS_CODE.equals(bankCardRespa.getResult())) {
                        return ResultUtil.error(bankCardRespa.getDesc());
                    }
                }
            }
            return  ResultUtil.ok();
        }catch (Exception e){
            log.error("当前绑定银行卡不是本人银行卡",e);
            return ResultUtil.error("当前绑定银行卡不是本人银行卡");
        }
    }

    //用户京东开户
    @PostMapping("/userOpenRole")
    public ResultUtil userOpenRole(@RequestBody TEndUserOpenRoleVo param){
        try{
            ResultUtil result = new ResultUtil();
            Integer enuserid = CurrentUser.getEndUserId();
            if(null == param.getAccountId()){
                param.setAccountId(CurrentUser.getUserAccountId());
            }
            String code = ObjectUtils.toString(redisUtil.get("OPENROLE" + param.getPhone()));
            String codeKey = "OPENROLE" + param.getPhone();
            if (StringUtils.isEmpty(param.getCode())) {
                return ResultUtil.error("请输入验证码");
            } else if (StringUtils.isEmpty(code)) {
                return ResultUtil.error("请重新发送验证码");
            } else if (!param.getCode().equals(code)) {
                return ResultUtil.error("验证码错误");
            }


            TEndUserOpenRoleVo tEndUserOpenRoleVo = tEndUserOpenRoleService.selectEndUserOpenRoleByEndUserId(enuserid);
            if(null == tEndUserOpenRoleVo){
                result.setCode(CodeEnum.ERROR.getCode());
                result.setMsg("本人信息异常请重新操作!");
            }else if(null != tEndUserOpenRoleVo.getOpenStatus() && tEndUserOpenRoleVo.getOpenStatus().equals(JdEnum.OPENSUCCESS.code)){
                result.setCode(CodeEnum.ERROR.getCode());
                result.setMsg("已开户成功,请勿重复开户!");
            }else if(null != tEndUserOpenRoleVo.getOpenStatus() && tEndUserOpenRoleVo.getOpenStatus().equals(JdEnum.OPENHANDLE.code)){
                result.setCode(CodeEnum.ERROR.getCode());
                result.setMsg("开户处理中,请勿重复操作!");
            }else {
                //根据id查询与account关系
                TEnduserAccountExample example = new TEnduserAccountExample();
                TEnduserAccountExample.Criteria cr = example.createCriteria();
                cr.andEnduserIdEqualTo(tEndUserOpenRoleVo.getEndUserId());
                //根据accountId查询银行卡
                List<TEnduserAccount>  tEnduserAccountList = tEndUserOpenRoleService.tEndUserAccountSelectByExample(example);

                List<TEndUserInfo> tEndUserInfoList = tEndUserOpenRoleService.selectOneselfRealName(tEnduserAccountList.get(0).getAccountId());
                if(tEnduserAccountList.size()>0){
//                    List<TBankCard> tBankCardList = tEndUserOpenRoleService.selectBankCardNotJdList(tEnduserAccountList.get(0).getAccountId(),
//                            tEndUserInfoList.get(0).getRealName(),tEndUserInfoList.get(0).getIdcard());
                    List<TBankCard> tBankCardList = tBankCardService.selectOneselfBankCardList(tEnduserAccountList.get(0).getAccountId(),
                            tEndUserInfoList.get(0).getRealName(),tEndUserInfoList.get(0).getIdcard());
                    Boolean state = true;
                    if(null != tBankCardList && tBankCardList.size() > 0){
                        for(TBankCard tBankCard : tBankCardList){
                            if(tBankCard.getCardNo().equals(param.getOpenRoleCardNo())){
                                  state = false;
                            }
                        }
                        if(state){
                            Integer bankCardId = tEndUserOpenRoleService.addOpenRoleCard(param);
                            tEndUserOpenRoleVo.setBankCardId(bankCardId);
                            tEndUserOpenRoleVo.setOpenRoleCardNo(param.getOpenRoleCardNo());
                            //tEndUserOpenRoleVoList.get(0).setOpenRoleCardOwnerPhone(param.getOpenRoleCardOwnerPhone());
                            tEndUserOpenRoleVo.setOpenRoleCardOwnerPhone(tEndUserInfoList.get(0).getPhone());
                        }else {
                            tEndUserOpenRoleVo.setBankCardId(tBankCardList.get(0).getId());
                            tEndUserOpenRoleVo.setOpenRoleCardNo(tBankCardList.get(0).getCardNo());
                            //tEndUserOpenRoleVoList.get(0).setOpenRoleCardOwnerPhone(tEndUserOpenRoleVoList.get(0).getPhone());
                            tEndUserOpenRoleVo.setOpenRoleCardOwnerPhone(tEndUserInfoList.get(0).getPhone());
                        }
                    }else {
                        Integer bankCardId = tEndUserOpenRoleService.addOpenRoleCard(param);
                        tEndUserOpenRoleVo.setBankCardId(bankCardId);
                        tEndUserOpenRoleVo.setOpenRoleCardNo(param.getOpenRoleCardNo());
                        //tEndUserOpenRoleVoList.get(0).setOpenRoleCardOwnerPhone(param.getOpenRoleCardOwnerPhone());
                        tEndUserOpenRoleVo.setOpenRoleCardOwnerPhone(tEndUserInfoList.get(0).getPhone());
                    }
                }
                if(tEndUserOpenRoleVo.getIdcardValidBeginning().getTime() > tEndUserOpenRoleVo.getIdcardValidUntil().getTime()){
                    Date idcardValidBeginning = tEndUserOpenRoleVo.getIdcardValidBeginning();
                    tEndUserOpenRoleVo.setIdcardValidBeginning(tEndUserOpenRoleVo.getIdcardValidUntil());
                    tEndUserOpenRoleVo.setIdcardValidUntil(idcardValidBeginning);
                }
                log.info("用户开户请求数据:"+JSON.toJSONString(tEndUserOpenRoleVo));
                ResultUtil resultUtil = tEndUserOpenRoleService.selectDriveOpenRole(tEndUserOpenRoleVo);

                if(!resultUtil.getCode().equals(CodeEnum.SUCCESS.getCode())){
                    result.setCode(CodeEnum.ERROR.getCode());
                    result.setMsg(resultUtil.getMsg());
                }else {
                    result.setCode(CodeEnum.SUCCESS.getCode());
                    result.setMsg(resultUtil.getMsg());
                }
            }
            TVerificationCodeLog tVerificationCodeLog=new TVerificationCodeLog();
            tVerificationCodeLog.setReceivePhoneno(param.getPhone());
            tVerificationCodeLog.setVerificationCode(code);
            tVerificationCodeLogService.updateIfUsed(tVerificationCodeLog);
            redisUtil.del(codeKey);
            return result;
        }catch (Exception e){
            log.error("用户开户申请失败，请重新开户!",e);
            return ResultUtil.error("用户开户申请失败，请重新开户!");
        }
    }

    //用户是否京东开户
    @PostMapping("/ifOpenRole")
    public ResultUtil ifOpenRole(@RequestBody Integer endUserId){
        try {

            ResultUtil resultUtil = tEndUserOpenRoleService.ifOpenRole(endUserId);

            return resultUtil;

        } catch (Exception e) {
            log.error("查询是否京东开户失败！",e);
            return ResultUtil.error("查询是否京东开户失败！");
        }
    }
    //用户是否京东开户
    @PostMapping("/userIfAgreement")
    public ResultUtil userIfAgreement(){
        try {
            Integer enuserid = CurrentUser.getEndUserId();
            ResultUtil resultUtil = tEndUserOpenRoleService.userIfAgreement(enuserid);

            return resultUtil;
        } catch (Exception e) {
            log.error("用户协议操作失败！",e);
            return ResultUtil.error("用户协议操作失败！");
        }
    }
    @PostMapping("/selectByEndUserId")
    public TEndUserOpenRole selectByEndUserId(@RequestParam(value = "endUserId") Integer endUserId){
        try {
            return tEndUserOpenRoleService.selectByEndUserId(endUserId);
        } catch (Exception e) {
            log.error("开户信息查询失败",e);
            return null;
        }
    }


}
