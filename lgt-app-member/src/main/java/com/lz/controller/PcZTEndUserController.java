package com.lz.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lz.common.util.ResultUtil;
import com.lz.service.PcZTEndUserService;
import com.lz.vo.PcCompanyWalletVO;
import com.lz.vo.PcZTEndUserOpenRoleListVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("ztEndUser")
public class PcZTEndUserController {

    @Autowired
    private PcZTEndUserService pcZTEndUserService;

    //用户华夏开户列表
    @PostMapping("/pcEndUserOpenRoleList")
    public ResultUtil pcEndUserOpenRoleList(@RequestBody PcZTEndUserOpenRoleListVO record) {
        try {

            return pcZTEndUserService.pcEndUserOpenRoleList(record);
        } catch (Exception e) {
            log.error("获取用户华夏开户列表失败",e);
            return ResultUtil.error("获取用户华夏开户列表失败");
        }
    }
}
