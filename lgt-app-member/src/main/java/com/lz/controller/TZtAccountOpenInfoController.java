package com.lz.controller;

import com.lz.common.util.ResultUtil;
import com.lz.model.TZtAccountOpenInfo;
import com.lz.service.TZtAccountOpenInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/ztAccountOpenInfo")
public class TZtAccountOpenInfoController {
    @Autowired
    private TZtAccountOpenInfoService tZtAccountOpenInfoService;

    //用户是否华夏开户
    @PostMapping("/ifOpenRole")
    public ResultUtil ifOpenRole(@RequestBody Integer endUserId){
        try {
            ResultUtil resultUtil = tZtAccountOpenInfoService.ifOpenRole(endUserId);
            return resultUtil;
        } catch (Exception e) {
            log.error("查询是否华夏开户失败！",e);
            return ResultUtil.error("查询是否华夏开户失败！");
        }
    }

    @PostMapping("/selectByCarrierIdAndCompanyIdAndEndUserIdByStatus")
    public TZtAccountOpenInfo selectByCarrierIdAndCompanyIdAndEndUserIdByStatus(@RequestParam(value = "carrierIdAndCompanyIdAndEndUserId") Integer carrierIdAndCompanyIdAndEndUserId,
                                                                                @RequestParam(value = "userOpenRole") String userOpenRole){

        return tZtAccountOpenInfoService.selectByCarrierIdAndCompanyIdAndEndUserIdByStatus(carrierIdAndCompanyIdAndEndUserId,userOpenRole);
    }

    @PostMapping("/selectOrderOpenInfoByAccountId")
    public TZtAccountOpenInfo selectOrderOpenInfoByAccountId(@RequestParam(value = "enduserId") Integer enduserId){
        return tZtAccountOpenInfoService.selectOrderOpenInfoByAccountId(enduserId);
    }

    @PostMapping("/selectOrderOpenInfoListByAccountId")
    public List<TZtAccountOpenInfo> selectOrderOpenInfoListByAccountId(@RequestParam(value = "enduserId") Integer enduserId){
        return tZtAccountOpenInfoService.selectOrderOpenInfoListByAccountId(enduserId);
    }
}
