package com.lz.controller;

import com.lz.common.util.ResultUtil;
import com.lz.model.TEndCarInfo;
import com.lz.model.TEndUserInfo;
import com.lz.service.TEndAutomatedAuditingServie;
import com.lz.vo.TEndCarInfoVO;
import com.lz.vo.TEndUserInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 *  @author: dingweibo
 *  @Date: 2023/5/19 14:02
 *  @Description: 移动端自动审核
 */
@Slf4j
@RestController
@RequestMapping("/tEndAutomatedAuditing")
public class TEndAutomatedAuditingController {

    @Autowired
    private TEndAutomatedAuditingServie tEndAutomatedAuditingServie;

    /**
     *  @author: dingweibo
     *  @Date: 2023/5/19 14:01
     *  @Description: 个人资料自动审核
     */
    @RequestMapping("/driverReview")
    public ResultUtil driverReview(@RequestBody TEndUserInfoVO record){
        try{
            return tEndAutomatedAuditingServie.driverReview(record);
        }catch (Exception e){
            log.error("个人资料自动审核失败",e);
            return ResultUtil.error("个人资料自动审核失败");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2023/5/20 9:20
     *  @Description: 车辆资料自动审核失败
     */
    @RequestMapping("/carReview")
    public ResultUtil carReview(@RequestBody TEndCarInfoVO record){
        try{
            return tEndAutomatedAuditingServie.carReview(record);
        }catch (Exception e){
            log.error("车辆资料自动审核失败",e);
            return ResultUtil.error("车辆资料自动审核失败");
        }
    }
}
