package com.lz.controller;

import com.lz.common.config.RedisUtil;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.model.hxPayment.request.query.CustomerQueryBalanceReq;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.common.util.ThrowableUtil;
import com.lz.model.TVerificationCodeLog;
import com.lz.model.TZtAccountOpenInfo;
import com.lz.service.HxWithdrawService;
import com.lz.service.TVerificationCodeLogService;
import com.lz.service.TZtAccountOpenInfoService;
import com.lz.sms.model.SmsReq;
import com.lz.sms.service.SmsClientService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.vo.HxWithdrawVo;
import com.lz.vo.TWithdrawalApplicationVo;
import com.lz.vo.ToolBalancePayRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;

@Slf4j
@RestController
@RequestMapping("/hxWithdraw")
public class HxWithdrawController {

    @Value("${hxyhPartnerId}")
    private String hxyhPartnerId;
    @Value("${hxyhChannelId}")
    private String hxyhChannelId;

    @Autowired
    private HxWithdrawService hxWithdrawService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private TVerificationCodeLogService tVerificationCodeLogService;

    @Autowired
    private SysParamAPI sysParamAPI;

    @Autowired
    private SmsClientService smsClientService;

    @Resource
    private TZtAccountOpenInfoService ztAccountOpenInfoService;

    @Resource
    private RedissonClient redissonClient;

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/28 9:14
     *  @Description: 查询提现数据
     */
    @PostMapping("/selectByWithdraw")
    public ResultUtil selectByWithdraw(@RequestBody HxWithdrawVo record) {
        try {
            return hxWithdrawService.selectByWithdraw(record);
        } catch (Exception e) {
            log.error("查询华夏提现参数失败！",e);
            return ResultUtil.error("查询华夏提现参数失败！");
        }
    }


    /**
     *  @author: dingweibo
     *  @Date: 2021/8/25 19:09
     *  @Description: 京东承运方或企业提现插入提现申请表
     */
    @PostMapping("/carrierAndComapnyWithdraw")
    public ResultUtil carrierAndComapnyWithdraw(@RequestBody HxWithdrawVo record) {
        try {
            if(DictEnum.MANAGER.code.equals(record.getTradeType())){
                SysParam sysParam = sysParamAPI.getParamByKey("MANAGERTX");
                if (null != sysParam && null != sysParam.getParamValue() && "1".equals(sysParam.getParamValue())) {
                    return ResultUtil.error("暂不支持提现");
                }
            }
            String code = "";
            if (null == record.getAmount() || record.getAmount().compareTo(BigDecimal.ZERO) <=0) {
                return ResultUtil.error("请输入有效的金额");
            }
            if (DictEnum.BTIXIAN.code.equals(record.getTradeType()) || DictEnum.MANAGER.code.equals(record.getTradeType())) {
                SysParam sysParam = sysParamAPI.getParamByKey("HXTXAMOUNT");
                if (null != sysParam && record.getAmount().longValue()>Long.parseLong(sysParam.getParamValue())) {
                    return ResultUtil.error("提现金额不能大于"+sysParam.getParamValue());
                }
            }
            //承运方提现
            if (DictEnum.PTIXIAN.code.equals(record.getTradeType())) {
                code = ObjectUtils.toString(redisUtil.get("CARRIERTIXIAN" + record.getPhone()));
            } else if (DictEnum.PFTIXIAN.code.equals(record.getTradeType())) {
                //平台提现
                code = ObjectUtils.toString(redisUtil.get("PFTIXIAN" + record.getPhone()));
            } else if (DictEnum.BTIXIAN.code.equals(record.getTradeType())) {
                //企业提现
                code = ObjectUtils.toString(redisUtil.get("COMPANYTIXIAN" + record.getPhone()));
            }else if (DictEnum.MANAGER.code.equals(record.getTradeType())){
                code = ObjectUtils.toString(redisUtil.get("AGENTTIXIAN" + record.getPhone()));
            } else {
                log.info("提现类型未知， tradeType");
                return ResultUtil.error("提现失败");
            }
            if (StringUtils.isBlank(code)) {
                return ResultUtil.error("请输入验证码");
            } else if (StringUtils.isEmpty(code)) {
                return ResultUtil.error("请重新发送验证码");
            } else if (!record.getVerificationCode().equals(code)) {
                return ResultUtil.error("验证码填写错误");
            }else {
                TVerificationCodeLog tVerificationCodeLog = new TVerificationCodeLog();
                tVerificationCodeLog.setReceivePhoneno(record.getPhone());
                tVerificationCodeLog.setVerificationCode(code);
                tVerificationCodeLogService.updateIfUsed(tVerificationCodeLog);
                ResultUtil resultUtil;
                TZtAccountOpenInfo tZtAccountOpenInfo = ztAccountOpenInfoService.selectByPrimaryKey(record.getOpenRoleId());
                if (null != tZtAccountOpenInfo && null != tZtAccountOpenInfo.getPartnerAccId() && StringUtils.isNotBlank(tZtAccountOpenInfo.getPartnerAccId())) {
                    String walletKey = tZtAccountOpenInfo.getPartnerAccId();
                    RLock lock = redissonClient.getLock(walletKey);
                    try {
                        boolean b = lock.tryLock();
                        if (!b) {
                            return ResultUtil.error("当前用户正在操作，请稍后再试");
                        }
                        if (DictEnum.MANAGER.code.equals(record.getTradeType())) {
                            resultUtil = hxWithdrawService.mangerWithdraw(record);
                        } else {
                            resultUtil = hxWithdrawService.carrierAndComapnyWithdraw(record);
                        }
                    } catch (Exception e) {
                        log.error("提现失败", e);
                        String message = e.getMessage();
                        if (StringUtils.checkChineseCharacter(message)) {
                            return ResultUtil.error(message);
                        } else {
                            return ResultUtil.error(" 提现失败");
                        }
                    } finally {
                        if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                            lock.unlock();
                        }
                    }
                    return resultUtil;
                } else {
                    return ResultUtil.error("开户信息不存在");
                }
            }
        } catch (Exception e) {
            log.error("华夏提现失败", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error(" 提现失败");
            }
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/25 19:09
     *  @Description: 京东承运方或企业提现
     */
    @Deprecated
    @PostMapping("/carrierAndComapnyWithdrawStart")
    public ResultUtil carrierAndComapnyWithdrawStart(@RequestBody HxWithdrawVo record) {
        TZtAccountOpenInfo tZtAccountOpenInfo = ztAccountOpenInfoService.selectByPrimaryKey(record.getOpenRoleId());
        if (null != tZtAccountOpenInfo && null != tZtAccountOpenInfo.getPartnerAccId() && StringUtils.isNotBlank(tZtAccountOpenInfo.getPartnerAccId())) {
            String walletKey = tZtAccountOpenInfo.getPartnerAccId();
            RLock lock = redissonClient.getLock(walletKey);
            try {
                boolean b = lock.tryLock();
                if (!b) {
                    return ResultUtil.error("当前用户正在操作，请稍后再试");
                }
                return hxWithdrawService.carrierAndComapnyWithdrawStart(record);
            } catch (Exception e) {
                log.error("提现失败！", e);
                return ResultUtil.error("提现失败！");
            } finally {
                if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                    lock.unlock();
                }
            }
        } else {
            return ResultUtil.error("开户信息不存在");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/28 9:14
     *  @Description: 提现审批
     */
    @PostMapping("/updateApprove")
    public ResultUtil updateApprove(@RequestBody TWithdrawalApplicationVo record) {
        TZtAccountOpenInfo tZtAccountOpenInfo = ztAccountOpenInfoService.selectByPrimaryKey(record.getOpenRoleId());
        if (null != tZtAccountOpenInfo && null != tZtAccountOpenInfo.getPartnerAccId() && StringUtils.isNotBlank(tZtAccountOpenInfo.getPartnerAccId())) {
            String walletKey = tZtAccountOpenInfo.getPartnerAccId();
            RLock lock = redissonClient.getLock(walletKey);
            try {
                boolean b = lock.tryLock();
                if (!b) {
                    return ResultUtil.error("当前用户正在操作，请稍后再试");
                }
                return hxWithdrawService.updateApprove(record);
            } catch (Exception e) {
                log.error("提现审批失败！", e);
                return ResultUtil.error("提现审批失败！");
            } finally {
                if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                    lock.unlock();
                }
            }
        } else {
            return ResultUtil.error("开户信息不存在");
        }

    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/28 9:14
     *  @Description: 小工具查询京东余额
     */
    @PostMapping("/selectByBalance")
    public ResultUtil selectByBalance(@RequestBody CustomerQueryBalanceReq record) {
        try {
            return hxWithdrawService.selectByBalance(record);
        } catch (Exception e) {
            log.error("查询京东余额失败！",e);
            return ResultUtil.error("查询京东余额失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/12/21 16:09
     *  @Description:  小工具华夏余额支付
     */
    @PostMapping("/balancePay")
    public ResultUtil balancePay(@RequestBody ToolBalancePayRequest record) {
        try {
            if (null != CurrentUser.getUsertype() || null != CurrentUser.getUserLogisticsRoles() || null != CurrentUser.getUserCompanyId()) {
                return ResultUtil.error("当前用户角色暂无权限");
            }
            if (null == record.getPhone() || StringUtils.isBlank(record.getPhone())) {
                return ResultUtil.error("手机号不可为空");
            }
            if (null == record.getVerificationCode() || StringUtils.isBlank(record.getVerificationCode())) {
                return ResultUtil.error("验证码不可为空");
            }
            Object o = redisUtil.get(DictEnum.TOOLBALANCEPAY_PHONE.code);
            if (null != o) {
                String phone = o.toString();
                if (!phone.contains(record.getPhone())) {
                    return ResultUtil.error("当前用户暂无权限");
                }
            } else {
                return ResultUtil.error("当前用户暂无权限");
            }
            String code = ObjectUtils.toString(redisUtil.get(DictEnum.TOOLBALANCEPAYSMSTYPE.code + record.getPhone()));
            if (record.getVerificationCode().equals(code)) {
                ResultUtil resultUtil = hxWithdrawService.balancePay(record);
                redisUtil.del(DictEnum.TOOLBALANCEPAYSMSTYPE.code + record.getPhone());
                return resultUtil;
            } else {
                return ResultUtil.error("验证码错误");
            }
        } catch (Exception e) {
            log.error("小工具华夏余额支付失败！, {}", ThrowableUtil.getStackTrace(e));
            return ResultUtil.error("小工具华夏余额支付失败！");
        }
    }

    @PostMapping("/sendVerificationCode")
    public ResultUtil sendVerificationCode(@RequestBody ToolBalancePayRequest record) {
        try {
            if (null == record.getPhone() || StringUtils.isBlank(record.getPhone())) {
                return ResultUtil.error("手机号不能为空");
            }
            Object o = redisUtil.get(DictEnum.TOOLBALANCEPAY_PHONE.code);
            if (null != o) {
                String phone = o.toString();
                if (!phone.contains(record.getPhone())) {
                    return ResultUtil.error("暂无权限");
                }
            } else {
                return ResultUtil.error("暂无权限");
            }
            String code = ObjectUtils.toString(redisUtil.get(DictEnum.TOOLBALANCEPAYSMSTYPE.code + record.getPhone()));
            if (StringUtils.isNotBlank(code)) {
                return ResultUtil.error("验证码已发送，请稍后再试");
            }
            SmsReq smsReq = new SmsReq();
            smsReq.setType(DictEnum.TOOLBALANCEPAYSMSTYPE.code);
            smsReq.setMobiles(record.getPhone());

            // 不保存短信记录
            smsReq.setSaveFlag(false);
            smsClientService.sendSmsType(smsReq);
        } catch (Exception e) {
            log.error("发送验证码失败！{}", ThrowableUtil.getStackTrace(e));
            return ResultUtil.error("发送验证码失败！");
        }
        return ResultUtil.ok();
    }
}
