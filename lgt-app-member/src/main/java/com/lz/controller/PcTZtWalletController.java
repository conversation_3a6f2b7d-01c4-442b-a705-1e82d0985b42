package com.lz.controller;

import com.lz.common.util.ResultUtil;
import com.lz.service.PcTZtWalletService;
import com.lz.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 中台钱包PC查询
 */
@Slf4j
@RestController
@RequestMapping("/tZtWallet")
public class PcTZtWalletController {

    @Autowired
    private PcTZtWalletService pcTZtWalletService;

    //企业钱包列表
    @PostMapping("/pcCompanyWalletDetail")
    public ResultUtil pcCompanyWalletDetail(@RequestBody PcCompanyWalletVO record) {
        try {
            return pcTZtWalletService.pcCompanyWalletDetail(record);
        } catch (Exception e) {
            log.error("获取中台企业钱包详情失败",e);
            return ResultUtil.error("获取中台企业钱包详情失败");
        }
    }
    //华夏企业资金流水
    @PostMapping("/companyWalletCapitalFlow")
    public ResultUtil companyWalletCapitalFlow(@RequestBody CompanyWalletCapitalFlowVO companyWalletCapitalFlowVO) {
        try {
            return pcTZtWalletService.companyWalletCapitalFlow(companyWalletCapitalFlowVO);
        } catch (Exception e) {
            log.error("获取华夏企业资金流水失败！",e);
            return ResultUtil.error("获取华夏企业资金流水失败！");
        }
    }

    //华夏企业钱包流水导出
    @PostMapping("/companyWalletCapitalFlowExcel")
    public ResultUtil companyWalletCapitalFlowExcel(@RequestBody CompanyWalletCapitalFlowVO companyWalletCapitalFlowVO) {
        try {
            return pcTZtWalletService.companyWalletCapitalFlowExcel(companyWalletCapitalFlowVO);
        } catch (Exception e) {
            log.error("导出华夏企业钱包流水失败！",e);
            return ResultUtil.error("导出华夏企业钱包流水失败！");
        }
    }

    //司机华夏资金流水
    @PostMapping("/driverCapitalFlow")
    public ResultUtil driverCapitalFlow(@RequestBody CompanyWalletCapitalFlowVO companyWalletCapitalFlowVo) {
        try {
            return pcTZtWalletService.driverWalletCapitalFlow(companyWalletCapitalFlowVo);
        } catch (Exception e) {
            log.error("获取司机华夏资金流水失败",e);
            return ResultUtil.error("获取司机华夏资金流水失败！");
        }
    }

    //承运方华夏钱包列表
    @PostMapping("/selectByHxCarrierWalletPage")
    public ResultUtil selectByHxCarrierWalletPage(@RequestBody CarrierHxOpenRoleSearchVo record){
        try {
            return pcTZtWalletService.selectByHxCarrierWalletPage(record);
        } catch (Exception e) {
            log.error("承运方华夏钱包列表查询失败！",e);
            return ResultUtil.error("承运方华夏钱包列表查询失败！");
        }
    }

    @PostMapping("/selectCarrierHxWalletLogPage")
    public ResultUtil selectCarrierHxWalletLogPage(@RequestBody CarrierHxOpenRoleSearchVo record){
        try {
            return pcTZtWalletService.selectCarrierHxWalletLogPage(record);
        } catch (Exception e) {
            log.error("承运方华夏钱包流水查询失败！",e);
            return ResultUtil.error("承运方华夏钱包流水查询失败！");
        }
    }

    //华夏承运方钱包流水导出
    @PostMapping("/carrierHxWalletExcel")
    public ResultUtil carrierHxWalletExcel(@RequestBody CarrierHxOpenRoleSearchVo record) {
        try {
            return pcTZtWalletService.carrierHxWalletExcel(record);
        } catch (Exception e) {
            log.error("导出华夏承运方钱包流水失败！",e);
            return ResultUtil.error("导出华夏承运方钱包流水失败！");
        }
    }

    @PostMapping("/selectHxPlatformWallet")
    public ResultUtil selectHxPlatformWallet() {
        try {
            return pcTZtWalletService.selectHxPlatformWallet();
        } catch (Exception e) {
            log.error("华夏平台钱包查询失败",e);
            return ResultUtil.error("华夏平台钱包查询失败！");
        }
    }

    @PostMapping("/selectHxPlatformWalletList")
    public ResultUtil selectHxPlatformWalletList(@RequestBody CarrierHxOpenRoleSearchVo record) {
        try {
            return pcTZtWalletService.selectHxPlatformWalletList(record);
        } catch (Exception e) {
            log.error("华夏平台钱包流水查询失败",e);
            return ResultUtil.error("华夏平台钱包流水查询失败！");
        }
    }

    //华夏平台钱包流水导出
    @PostMapping("/platformHxWalletExcel")
    public ResultUtil platformHxWalletExcel(@RequestBody CarrierHxOpenRoleSearchVo record) {
        try {
            return pcTZtWalletService.platformHxWalletExcel(record);
        } catch (Exception e) {
            log.error("导出华夏平台钱包流水失败！",e);
            return ResultUtil.error("导出华夏平台钱包流水失败！");
        }
    }

    //华夏经纪人钱包流水
    @PostMapping("/selectHxAgentWalletList")
    public ResultUtil selectHxAgentWalletList(@RequestBody CarrierHxOpenRoleSearchVo record) {
        try {
            return pcTZtWalletService.selectHxAgentWalletList(record);
        } catch (Exception e) {
            log.error("经纪华夏钱包查询失败",e);
            return ResultUtil.error("经纪华夏钱包查询失败！");
        }
    }

    //华夏经纪人钱包
    @PostMapping("/selectHxAgentWallet")
    public ResultUtil selectHxAgentWallet() {
        try {
            return pcTZtWalletService.selectHxAgentWallet();
        } catch (Exception e) {
            log.error("华夏经纪人钱包查询失败",e);
            return ResultUtil.error("华夏经纪人钱包查询失败！");
        }
    }

}
