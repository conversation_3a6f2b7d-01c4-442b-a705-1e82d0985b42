package com.lz.controller;

import com.lz.common.config.RedisUtil;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.model.BankCardReq;
import com.lz.common.model.BankCardResp;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.IntegrationBankCard;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.model.TAccount;
import com.lz.model.TVerificationCodeLog;
import com.lz.model.TZtBankCard;
import com.lz.service.TAccountService;
import com.lz.service.TVerificationCodeLogService;
import com.lz.service.TZtBankUserService;
import com.lz.service.TZtWalletService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 *  @author: dingweibo
 *  @Date: 2021/8/9 9:15
 *  @Description: 华夏钱包
 */
@Slf4j
@RestController
@RequestMapping("/tZtWallet")
public class TZtWalletController {

    @Autowired
    private TZtWalletService tZtWalletService;
    @Autowired
    private TVerificationCodeLogService tVerificationCodeLogService;

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private SysParamAPI sysParamAPI;
    @Autowired
    private TAccountService tAccountService;
    @Autowired
    private TZtBankUserService ztBbankUserService;

    /**
     *  @author: dingweibo
     *  @Date: 2022/5/11 10:03
     *  @Description: 对公账户管理页面（承运方与企业）
     */
    @PostMapping("/hxBankCardList")
    public ResultUtil hxBankCardList(@RequestBody TZtBankCardVo record){
        try {
            return tZtWalletService.hxBankCardList(record);
        } catch (Exception e) {
            log.error("对公账户管理列表查询失败！",e);
            return ResultUtil.error("对公账户管理列表查询失败！");
        }
    }
    /**
     *  @author: dingweibo
     *  @Date: 2022/11/9 11:43
     *  @Description: 承运方或企业绑卡
     */
    @PostMapping("/bindingBank")
    public ResultUtil bindingBank(@RequestBody TZtBankCardVo record){
        try {
            return tZtWalletService.bindingBank(record);
        } catch (Exception e) {
            log.error("对公账户信息完善失败！",e);
            return ResultUtil.error("对公账户信息完善失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/9 15:38
     *  @Description: 承运方或企业解绑银行卡
     */
    @PostMapping("/unbindBank")
    public ResultUtil unbindBank(@RequestBody TZtBankCardVo record){
        try {
            return tZtWalletService.unbindBank(record);
        } catch (Exception e) {
            log.error("解绑银行卡失败！",e);
            return ResultUtil.error("解绑银行卡失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/19 14:53
     *  @Description: 经纪人绑卡列表
     */
    @PostMapping("/agentHxBankCardList")
    public ResultUtil agentHxBankCardList(@RequestBody TZtBankCardVo record){
        try {
            Integer accountId = null;
            if(record.getAccountId()!=null){
                accountId = record.getAccountId();
            }else{
                accountId = CurrentUser.getUserAccountId();
            }
            record.setAccountId(accountId);
            return tZtWalletService.agentHxBankCardList(record);
        } catch (Exception e) {
            log.error("经纪人银行卡列表查询失败！",e);
            return ResultUtil.error("经纪人银行卡列表查询失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/19 15:13
     *  @Description: 经纪人绑卡
     */
    @PostMapping("/agentBindingBank")
    public ResultUtil agentBindingBank(@RequestBody TZtBankCardVo record){
        try {
            Integer accouontId =null;
            if(record.getAccountId()!=null){
                accouontId =record.getAccountId();
            }else{
                accouontId = CurrentUser.getUserAccountId();
            }
            record.setAccountId(accouontId);
            TAccount t =  tAccountService.selectByPrimaryKey(accouontId);
            String code = "";
            String codeKey = "";
            if (t.getNickname().equals(record.getAcctName())){
                // 添加本人银行卡
                record.setIsOneself(true);
                code = ObjectUtils.toString(redisUtil.get("ADDBRCARD" + record.getRealPhone()));
                codeKey = "ADDBRCARD" + record.getRealPhone();
            }else {
                return ResultUtil.error("不允许绑定非本人银行卡");
            }
            List<TZtBankCard> b = tAccountService.getTZtBankCardListByCardNo(record.getAcctNo(), accouontId);
            if (StringUtils.isEmpty(record.getRealPhone())) {
                return ResultUtil.error("请输入手机号码");
            } else if (StringUtils.isEmpty(record.getCode())) {
                return ResultUtil.error("请输入验证码");
            } else if (StringUtils.isEmpty(code)) {
                return ResultUtil.error("请重新发送验证码");
            } else if (!record.getCode().equals(code)) {
                return ResultUtil.error("验证码错误");
            } else if (b == null || b.size() == 0) {
                SysParam sysParam = sysParamAPI.getParamByKey("IFKBIN");
                //是否开启 银行卡三要素校验 0否 1是
                if("1".equals(sysParam.getParamValue())){
                    IntegrationBankCard ibc = new IntegrationBankCard();
                    BankCardReq bankCardReq = new BankCardReq();
                    bankCardReq.setBankCard(record.getAcctNo());
                    bankCardReq.setIdCard(record.getAcctCard());
                    bankCardReq.setName(record.getAcctName());
                    BankCardResp bankCardRespa = ibc.checkBankCard(bankCardReq);
                    if ( StringUtils.isBlank(bankCardRespa.getResult())
                            || !BankCardResp.RESULT_UNANIMOUS_CODE.equals(bankCardRespa.getResult())) {
                        return ResultUtil.error(bankCardRespa.getDesc());
                    }
                }
            } else {
                return ResultUtil.error("该银行卡已被添加，请更换新卡");
            }
            ResultUtil resultUtil = tZtWalletService.agentBindingBank(record);
            TVerificationCodeLog tVerificationCodeLog = new TVerificationCodeLog();
            tVerificationCodeLog.setReceivePhoneno(record.getRealPhone());
            tVerificationCodeLog.setVerificationCode(record.getCode());
            tVerificationCodeLogService.updateIfUsed(tVerificationCodeLog);
            redisUtil.del(codeKey);
            return resultUtil;
        } catch (Exception e) {
            log.error("经纪人绑卡失败！",e);
            return ResultUtil.error("经纪人绑卡失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/19 15:13
     *  @Description: 经纪人解绑银行卡
     */
    @PostMapping("/agentUnbindBank")
    public ResultUtil agentUnbindBank(@RequestBody TZtBankCardVo record) {
        try{
            Integer accountId = CurrentUser.getUserAccountId();
            ResultUtil resultUtil = ztBbankUserService.delHxyhCard(record.getId(),accountId);
            return resultUtil;
        } catch (Exception e) {
            log.error("经纪人解绑银行卡失败！",e);
            return ResultUtil.error("经纪人解绑银行卡失败！");
        }
    }
}
