package com.lz.controller;

import com.lz.common.util.ResultUtil;
import com.lz.model.TZtBankCard;
import com.lz.model.TZtBankUser;
import com.lz.service.TZtBankUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping(value = "/ztBankUserController")
public class TZtBankUserController {
    @Autowired
    private TZtBankUserService tZtBankUserService;

    @PostMapping("/selectByBankNum")
    public int selectByBankNum(@RequestParam("accountId") Integer accountId){
        return tZtBankUserService.selectByBankNum(accountId);
    }

    @PostMapping("/selectBankByEndUserId")
    public TZtBankCard selectBankByEndUserId(@RequestParam("endUserId") Integer endUserId){
        return tZtBankUserService.selectBankByEndUserId(endUserId);
    }

    @PostMapping("/selectById")
    public TZtBankCard selectById(@RequestParam("bankCardId") Integer bankCardId){
        return tZtBankUserService.selectById(bankCardId);
    }

    @PostMapping(value = "/selectBankCards")
    ResultUtil selectBankCards(@RequestParam(value = "enduserId") Integer enduserId) {
        return tZtBankUserService.selectBankCards(enduserId);
    }

    //设置默认银行卡
    @PostMapping(value="/updateById")
    public ResultUtil updateBankCardDefaultById(@RequestBody TZtBankUser tZtBankUser){
        return  ResultUtil.ok(tZtBankUserService.updateBankCardDefaultById(tZtBankUser));
    }

}
