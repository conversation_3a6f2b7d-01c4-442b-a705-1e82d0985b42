package com.lz.controller;

import com.lz.common.util.ResultUtil;
import com.lz.model.TZtWalletChangeLog;
import com.lz.service.TZtWalletChangeLogService;
import com.lz.vo.TZtWalletChangeLogVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/hx/wallet/log")
public class TZtWalletChangeLogController {

    @Autowired
    private TZtWalletChangeLogService ztWalletChangeLogService;

    @PostMapping("/update")
    public ResultUtil updateWalletChangeLog(@RequestBody TZtWalletChangeLog log) {
        return ResultUtil.ok(ztWalletChangeLogService.updateById(log));
    }

    @PostMapping("/selectByEndUserId")
    public TZtWalletChangeLog selectByEndUserId(@RequestParam(value = "endUserId") Integer endUserId
                                        ,@RequestParam(value = "orderBusinessCode") String orderBusinessCode) {
        return ztWalletChangeLogService.selectByEndUserId(endUserId,orderBusinessCode);
    }
    @PostMapping("/loadReceiptApply")
    public ResultUtil loadReceiptApply(@RequestBody TZtWalletChangeLogVo logVo) {
        return ResultUtil.ok(ztWalletChangeLogService.loadReceiptApply(logVo));
    }

    @PostMapping("/selectByorderBusinessCode")
    public TZtWalletChangeLog selectByorderBusinessCode(@RequestParam(value = "orderBusinessCode") String orderBusinessCode,
                                                        @RequestParam(value = "tradeType") String tradeType) {
        return ztWalletChangeLogService.selectByOrderBusinessCode(orderBusinessCode,tradeType);
    }

    @PostMapping("/seelctByTradeTypeWalletIdOrderBusinessCode")
    public List<TZtWalletChangeLog> seelctByTradeTypeWalletIdOrderBusinessCode(@RequestParam(value = "tradeType") String tradeType,
                                                                               @RequestParam(value = "walletId") Integer walletId,
                                                                               @RequestParam(value = "orderBusinessCode") String orderBusinessCode) {
        return ztWalletChangeLogService.seelctByTradeTypeWalletIdOrderBusinessCode(tradeType,walletId,orderBusinessCode);
    }

    @PostMapping("/updateByPrimaryKeySelective")
    public ResultUtil updateByPrimaryKeySelective(@RequestBody TZtWalletChangeLog log) {
        return ResultUtil.ok(ztWalletChangeLogService.updateByPrimaryKeySelective(log));
    }

    @PostMapping("/paymentFeesUpdateTransactionType")
    public List<TZtWalletChangeLog> paymentFeesUpdateTransactionType(@RequestParam(value = "tradeNo") String tradeNo) {
        return ztWalletChangeLogService.paymentFeesUpdateTransactionType(tradeNo);
    }

    @PostMapping("/updateWalletChangeLogById")
    public ResultUtil updateWalletChangeLogById(@RequestBody TZtWalletChangeLog log) {
        return ResultUtil.ok(ztWalletChangeLogService.updateWalletChangeLogById(log));
    }

    @PostMapping("/selectOldOrderCarCaptainByOrderBusinessCode")
    public TZtWalletChangeLog selectOldOrderCarCaptainByOrderBusinessCode(@RequestParam(value = "walletId") Integer walletId,
                                                                          @RequestParam(value = "orderBusinessCode") String orderBusinessCode) {
        return ztWalletChangeLogService.selectOldOrderCarCaptainByOrderBusinessCode(walletId, orderBusinessCode);
    }
}
