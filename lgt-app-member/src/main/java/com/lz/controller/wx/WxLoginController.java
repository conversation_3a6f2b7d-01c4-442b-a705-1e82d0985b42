package com.lz.controller.wx;

import com.lz.aop.log.Log;
import com.lz.api.ContractAPI;
import com.lz.api.WxOrderControllerAPI;
import com.lz.common.config.RedisUtil;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.exception.BadRequestException;
import com.lz.common.model.CodeEnum;
import com.lz.common.model.chinadatabao.ChinaDataBaoObtainResp;
import com.lz.common.model.chinadatabao.CommunicationCallbacklReq;
import com.lz.common.util.*;
import com.lz.dto.TAccountUserInfoDTO;
import com.lz.model.*;
import com.lz.model.contract.req.ContractApplyCertReq;
import com.lz.model.contract.resp.ContractApplyCertResp;
import com.lz.service.*;
import com.lz.sms.model.SmsReq;
import com.lz.sms.model.SmsResp;
import com.lz.sms.service.SmsClientService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.api.SystemAPI;
import com.lz.system.model.SysParam;
import com.lz.system.model.TSysUser;
import com.lz.system.vo.UserInfoVO;
import com.lz.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.ObjectUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("/wxLogin")
public class WxLoginController {
    @Autowired
    private TAccountService tAccountService;
    @Resource
    private SmsClientService smsApi;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private TEndUserInfoService tEndUserInfoService;
    @Autowired
    private ContractAPI contractAPI;
    @Autowired
    private SystemAPI systemAPI;
    @Autowired
    private TVerificationCodeLogService tVerificationCodeLogService;
    @Autowired
    private TZtAccountOpenInfoService tZtAccountOpenInfoService;

    @Autowired
    private WxLoginService wxLoginService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private WxOrderControllerAPI wxOrderControllerAPI;

    @Autowired
    private SysParamAPI sysParamAPI;

    /**
     * 根据openid查询用户是否存在(openid如果在数据库里，那么直接登录成功)
     * @auth zhangxin
     *
     * @param openId
     * @return REsultUtil
     */
    @PostMapping("/isOpenIds")
    public ResultUtil searchUserByName(@RequestBody Integer openId) {
        List<TAccount> user = tAccountService.findOpenId(openId);
        if (user != null) {
            return ResultUtil.ok(user);
        } else {
            return ResultUtil.error("用户openId不存在");
        }
    }

    /**
     * 登录
     * @auth zhangxin
     *
     * @param account
     * @return
     */
    @PostMapping("/login")
    public ResultUtil login(@RequestBody TUserVo account) {
        String code = ObjectUtils.toString(redisUtil.get("LOGIN" + account.getAccountNo()));
        List<TAccount> user = tAccountService.findUser(account.getUsername());
        if (StringUtils.isBlank(account.getAccountNo())) {
            return ResultUtil.error("请输入手机号");
        } else if (user == null) {
            return ResultUtil.error("该手机号尚未注册");
        } else if (StringUtils.isBlank(user.get(0).getPassword())) {
            return ResultUtil.error("请输入密码");
        } else if (account.getType() == 1 && !DigestUtils.md5Hex(account.getPassword()).equals(user.get(0).getPassword())) {
            return ResultUtil.error("密码错误");
        } else if (StringUtils.isBlank(account.getCode())) {
            return ResultUtil.error("请输入验证码");
        } else if (account.getType() == 2 && StringUtils.isEmpty(code)) {
            return ResultUtil.error("请重新发送验证码");
        } else if (account.getType() == 2 && !account.getCode().equals(code)) {
            return ResultUtil.error("验证码填写错误");
        } else {
            if(StringUtils.isNotEmpty(account.getCode())){
                TVerificationCodeLog tVerificationCodeLog=new TVerificationCodeLog();
                tVerificationCodeLog.setReceivePhoneno(account.getAccountNo());
                tVerificationCodeLog.setVerificationCode(code);
                tVerificationCodeLogService.updateIfUsed(tVerificationCodeLog);
            }
            sendMessageByLogin(account.getCode());
        }
        if (user != null) {
            TAccount record = user.get(0);
            record.setThridParyId(account.getThridParyId());
            tAccountService.updateOpenId(record);
            tAccountService.updateUser(record);
        }
        return ResultUtil.ok(user);
    }

    /**
     * 注册
     * @auth zhangxin
     *
     * @param record
     * @return
     */
    @PostMapping(value = "/register")
    @ResponseBody
    public ResultUtil register(@RequestBody TUserVo record) {
        try {
            String smsType = null == record.getSmsType() ? "WXREFISTERED" : record.getSmsType();
            String code = ObjectUtils.toString(redisUtil.get(smsType + record.getAccountNo()));
            List<TAccount> findphone = tAccountService.findphone(record.getAccountNo());
            List<TEndUserInfo> findIdCard = tEndUserInfoService.findIdCard(record.getIdcard());
            String phone = record.getAccountNo();
            RLock lock = redissonClient.getLock("Account" + phone);
            try {
                boolean tryLock = lock.tryLock(5, TimeUnit.SECONDS);
                if (tryLock) {
                    List<TSysUser> sysUsers = systemAPI.selectByPhone(phone);
                    record.setIfPasswordSecurity(true);
                    if (StringUtils.isBlank(record.getAccountNo())) {
                        return ResultUtil.error("请输入手机号");
                    } else if (findphone.size() > 0 || sysUsers.size() > 0) {
                        return ResultUtil.error("该手机号已经注册，请更换");
                    } else if (StringUtils.isBlank(record.getCode())) {
                        return ResultUtil.error("请输入验证码");
                    } else if (!record.getCode().equals(code)) {
                        return ResultUtil.error("验证码填写错误");
                    } else if (StringUtils.isBlank(code)) {
                        return ResultUtil.error("请重新发送验证码");
                    } else if (findIdCard.size() > 0) {
                        for(TEndUserInfo tEndUserInfo : findIdCard){
                            if(tEndUserInfo.getUserLogisticsRole().equals("CTYPECAPTAIN")){
                                return ResultUtil.error("该身份证号已经注册车队长身份，请更换。");
                            }else if(tEndUserInfo.getUserLogisticsRole().equals("CTYPEDRVIVER")){
                                return ResultUtil.error("该身份证号已经注册司机身份，请更换。");
                            }
                        }
                        return ResultUtil.error("身份证号已经注册，请更换");
                    } else if (smsType.equals("WXREFISTERED")){
                        record.setDatafrom("DRIWX");
                        if (null == record.getPassword() || StringUtils.isEmpty(record.getPassword())){
                            return ResultUtil.error("请输入密码");
                        } else {
                            // 检验密码格式
                            if (!StringUtils.validatePassword(record.getPassword())) {
                                return ResultUtil.error(CodeEnum.PASSWORD_FORMAT_ERROR.getCode());
                            }
                            record.setPassword(DigestUtils.md5Hex(record.getPassword()));
                        }
                    } else if (smsType.equals("APPREGISTERED")) {
                        record.setIfPasswordSecurity(false);
                        record.setDatafrom("COMAPP");
                        record.setPassword(DigestUtils.md5Hex("123456"));
                    }
                    TVerificationCodeLog tVerificationCodeLog=new TVerificationCodeLog();
                    tVerificationCodeLog.setReceivePhoneno(record.getAccountNo());
                    tVerificationCodeLog.setVerificationCode(code);
                    tVerificationCodeLogService.updateIfUsed(tVerificationCodeLog);
                    return tAccountService.saveAccount(record);
                } else {
                    return ResultUtil.error("注册失败, 稍后再试");
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("注册失败, {}", ThrowableUtil.getStackTrace(e));
            } finally {
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        } catch (Exception e) {
            log.error("注册失败, {}", ThrowableUtil.getStackTrace(e));
            return ResultUtil.error("注册失败");
        }
        return ResultUtil.error("注册失败");
    }

    /**
     * 申请CA证书
     * @auth zhangxin
     *
     * @return REsultUtil
     */
    @PostMapping("/applyCert")
    public ResultUtil applyCert(@RequestParam(value = "idCardNum") String idCardNum, @RequestParam(value = "name") String name, @RequestParam(value = "mobilePhone") String mobilePhone) {
        try{
            Integer enduserinfo = CurrentUser.getEndUserId();
            TEndUserInfo tEndUserInfo = tEndUserInfoService.findById(enduserinfo);
            ContractApplyCertReq data = new ContractApplyCertReq();
            data.setType("1");//类型 1：个人 、2：企业
            data.setCardType("0");// 证件类型 0：身份证 1：军官证 ,2：护照、 3：驾驶证、4：工商登记证、5：税务登记证、6：组织机构代码、7：其他证件，8：统一社会信用代码
            data.setIdCardNum(idCardNum); // 证件号码
            data.setName(name);//企业或者个人真实名称
            data.setMobilePhone(mobilePhone);//企业或者个人联系手机号
            ContractApplyCertResp cert = contractAPI.applyCert(data);
            if (cert.getCode().equals("0")) {
                tEndUserInfo.setIssuer(cert.getIssuer());
                tEndUserInfo.setSerialNumber(cert.getSerialNumber());
                tEndUserInfo.setBeginTime(DateUtils.parseDate(cert.getCertNotBefore()));
                tEndUserInfo.setEndTime(DateUtils.parseDate(cert.getCertNotAfter()));
            } else {
                return ResultUtil.error(cert.getMessage());
            }
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), "");
        }catch (Exception e){
            log.error("申请CA证书失败",e);
            return ResultUtil.error("dw-063:申请CA证书失败");
        }
    }

    /**
     * 更换手机号
     * @auth zhangxin
     *
     * @param account
     * @return
     */
    @PostMapping("/changePhone")
    public ResultUtil changePhone(@RequestBody TUserVo account) {
        try{
            String oldcode = ObjectUtils.toString(redisUtil.get("CHANGEPHONEOLD" + account.getAccountNo()));
            String o = account.getOldCode();
            List<TAccount> findphone = tAccountService.findphone(account.getAccountNo());
            if (StringUtils.isBlank(account.getAccountNo())) {
                return ResultUtil.error("请输入手机号码");
            } else if (findphone == null) {
                return ResultUtil.error("该手机号未注册");
            } else if (StringUtils.isEmpty(oldcode)) {
                return ResultUtil.error("请重新发送验证码");
            } else if (!account.getOldCode().equals(oldcode)) {
                return ResultUtil.error("验证码错误");
            } else {
                TVerificationCodeLog tVerificationCodeLog=new TVerificationCodeLog();
                tVerificationCodeLog.setReceivePhoneno(account.getAccountNo());
                tVerificationCodeLog.setVerificationCode(oldcode);
                tVerificationCodeLogService.updateIfUsed(tVerificationCodeLog);
                //tAccountService.updateIfUsed(account.getAccountNo(), oldcode);
//            sendMessageByChangeOld(account.getCode());
            }
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), "修改成功！");
        }catch (Exception e){
            log.error("更换手机号失败",e);
            return ResultUtil.error("dw-064:更换手机号失败");
        }
    }

    /**
     * 更换手机号（下一步）
     * @auth zhangxin
     *
     * @param account
     * @return
     */
    @PostMapping("/changePhoneNext")
    public ResultUtil changePhoneNext(@RequestBody TUserVo account) {
        String newcode = ObjectUtils.toString(redisUtil.get("CHANGEPHONENEW" + account.getNewPhone()));
        List<TAccount> findphone = tAccountService.findphone(account.getNewPhone());
        List<TAccount> findphone1 = tAccountService.findphone(account.getAccountNo());
        List<TSysUser> sysUsers = systemAPI.selectByPhone(account.getAccountNo());
        List<TEndUserInfo> tEndUserInfos =tEndUserInfoService.findphone(account.getAccountNo());

        if (StringUtils.isBlank(account.getNewPhone())) {
            return ResultUtil.error("请输入新手机号码");
        } else if (findphone.size() > 0) {
            return ResultUtil.error("该手机号码已注册,请更换手机号");
        } else if (StringUtils.isEmpty(account.getNewCode())) {
            return ResultUtil.error("请输入验证码");
        } else if (StringUtils.isEmpty(newcode)) {
            return ResultUtil.error("请重新发送验证码");
        } else if (!account.getNewCode().equals(newcode)) {
            return ResultUtil.error("验证码错误");
        } else {
            TVerificationCodeLog tVerificationCodeLog=new TVerificationCodeLog();
            tVerificationCodeLog.setReceivePhoneno(account.getAccountNo());
            tVerificationCodeLog.setVerificationCode(newcode);
            tVerificationCodeLogService.updateIfUsed(tVerificationCodeLog);
            //tAccountService.updateIfUsed(account.getNewPhone(),newcode);
//            sendMessageByChangenNew(account.getNewCode());
        }
        TAccount tAccount = findphone1.get(0);
        TSysUser tSysUser=sysUsers.get(0);
        TEndUserInfo tEndUserInfo=tEndUserInfos.get(0);
        List<TEnduserAccount> tEnduserAccounts=tAccountService.selectByEndUserInfoIdAndAccountId(tEndUserInfo.getId(),tAccount.getId());
        TEnduserAccount tEnduserAccount=tEnduserAccounts.get(0);
        return tAccountService.updateAccountNo(tAccount,tSysUser,tEndUserInfo,tEnduserAccount,account.getNewPhone());

    }

    /**
     * 重置密码
     * @auth zhangxin
     *
     * @param account
     * @return
     */
    @PostMapping("/changePassword")
    public ResultUtil changePassword(@RequestBody TUserVo account) {
        try{
            UserInfoVO userInfo = systemAPI.userGetUserInfo(account.getAccountNo());
            // 账号被锁，稍后再试
            if (null != userInfo.getAccountStatus() && DictEnum.ACCOUNTLOCK.code.equals(userInfo.getAccountStatus())) {
                // 账号锁住
                return ResultUtil.error("验证码输错3次以上账号已锁定");
            }
            String oldcode = ObjectUtils.toString(redisUtil.get("CHANGEPWD" + account.getAccountNo()));
            List<TAccount> findphone = tAccountService.findphone(account.getAccountNo());
            if (StringUtils.isEmpty(account.getAccountNo())) {
                return ResultUtil.error("请输入手机号码");
            } else if (findphone.size() == 0) {
                return ResultUtil.error("该手机号尚未注册");
            } else if (StringUtils.isEmpty(account.getCode())) {
                return ResultUtil.error("请输入验证码");
            } else if (StringUtils.isEmpty(oldcode)) {
                return ResultUtil.error("请重新发送验证码");
            } else if (!account.getCode().equals(oldcode)) {
                // 记录密码错误次数
                systemAPI.recordPassowordErrorNew(account.getAccountNo());
                return ResultUtil.error("验证码错误");
            } else {
                String newPassword = account.getPassword();
                if (!StringUtils.validatePassword(newPassword)) {
                    return ResultUtil.error("请输入8位或8位以上，不超过16位，包含数字、字母（大小写不限）、特殊字符");
                    //return ResultUtil.error("请输入8位或8位以上，不超过16位，包含数字、字母（大小写不限）");
                } else {
                    TVerificationCodeLog tVerificationCodeLog=new TVerificationCodeLog();
                    tVerificationCodeLog.setReceivePhoneno(account.getAccountNo());
                    tVerificationCodeLog.setVerificationCode(oldcode);
                    tVerificationCodeLogService.updateIfUsed(tVerificationCodeLog);
                    //tAccountService.updateIfUsed(account.getAccountNo(), oldcode);
                    //sendMessageByFindPassword(account.getCode());
                    if (findphone != null) {
                        TAccount taccount = findphone.get(0);
                        int userid = taccount.getUserId();
                        String password = DigestUtils.md5Hex(account.getPassword());
                        tAccountService.updatePassword(userid, password);
                        return new ResultUtil(CodeEnum.SUCCESS.getCode(), "登录密码重置成功");
                    }
                }

            }
            return ResultUtil.error("密码重置失败");
        }catch (Exception e){
            log.error("密码重置失败",e);
            return ResultUtil.error("dw-065:密码重置失败");
        }
    }

    /**
     * 未登录重置密码
     * @auth zhangxin
     *
     * @param account
     * @return
     */
    @PostMapping("/noLoginChangePassword")
    public ResultUtil noLoginChangePassword(@RequestBody TUserVo account) {
        try{
            UserInfoVO userInfo = systemAPI.userGetUserInfo(account.getAccountNo());
            // 账号被锁，稍后再试
            if (null != userInfo.getAccountStatus() && DictEnum.ACCOUNTLOCK.code.equals(userInfo.getAccountStatus())) {
                // 账号锁住
                return ResultUtil.error("验证码输错3次以上账号已锁定");
            }
            String oldcode = ObjectUtils.toString(redisUtil.get("CHANGEPWD" + account.getAccountNo()));
            List<TAccount> findphone = tAccountService.findphone(account.getAccountNo());
            if (StringUtils.isEmpty(account.getAccountNo())) {
                return ResultUtil.error("请输入手机号码");
            } else if (findphone.size() == 0) {
                return ResultUtil.error("该手机号尚未注册");
            } else if (StringUtils.isEmpty(account.getCode())) {
                return ResultUtil.error("请输入验证码");
            } else if (StringUtils.isEmpty(oldcode)) {
                return ResultUtil.error("请重新发送验证码");
            } else if (!account.getCode().equals(oldcode)) {
                // 记录密码错误次数
                systemAPI.recordPassowordErrorNew(account.getAccountNo());
                return ResultUtil.error("验证码错误");
            } else {
                String newPassword = account.getPassword();
                if (!StringUtils.validatePassword(newPassword)) {
                    return ResultUtil.error("请输入8位或8位以上，不超过16位，包含数字、字母（大小写不限）、特殊字符");
                    //return ResultUtil.error("请输入8位或8位以上，不超过16位，包含数字、字母（大小写不限）");
                } else {
                    TVerificationCodeLog tVerificationCodeLog=new TVerificationCodeLog();
                    tVerificationCodeLog.setReceivePhoneno(account.getAccountNo());
                    tVerificationCodeLog.setVerificationCode(oldcode);
                    tVerificationCodeLogService.updateIfUsed(tVerificationCodeLog);
                    //tAccountService.updateIfUsed(account.getAccountNo(), oldcode);
                    //sendMessageByFindPassword(account.getCode());
                    if (findphone != null) {
                        TAccount taccount = findphone.get(0);
                        int userid = taccount.getUserId();
                        String password = DigestUtils.md5Hex(account.getPassword());
                        tAccountService.updatePassword(userid, password);
                        return new ResultUtil(CodeEnum.SUCCESS.getCode(), "登录密码重置成功");
                    }
                }
            }
            return ResultUtil.error("密码重置失败");
        }catch (Exception e){
            log.error("密码重置失败",e);
            return ResultUtil.error("dw-065:密码重置失败");
        }
    }

    /**
     * 发送验证码接口(注册)
     * @auth zhangxin
     *
     * @param phone
     * @return
     */
    @PostMapping(value = "/sendMessageByRegister")
    @ResponseBody
    public ResultUtil sendMessageByRegister(@RequestParam(value = "phone") String phone, @RequestParam(value = "smsType", required = false) String smsType) {
        try {
            List<TAccount> findphone = tAccountService.findphone(phone);
            if (findphone.size() > 0) {
                return ResultUtil.error("手机号已经注册，请更换");
            }
            SmsReq req = new SmsReq();
            req.setMobiles(phone);
            //添加短信类型参数，默认微信端不需要传此参数，APP需要传v
            if (StringUtils.isNotEmpty(smsType)){
                req.setType(smsType);
            } else {
                req.setType("WXREFISTERED");
            }
            String phoneCodeKey = "REGISTER_PHONE_CODE" + phone;
            String phoneCode = ObjectUtils.toString(redisUtil.get(phoneCodeKey));
            if (StringUtils.isNotBlank(phoneCode)) {
                return ResultUtil.error("60秒内已经发送过验证码，请稍后再试");
            } else {
                redisUtil.set(phoneCodeKey, 1, 60);
            }
            String key = "REFISTERED" + phone;
            String oldcode = ObjectUtils.toString(redisUtil.get(key));
            if (StringUtils.isBlank(oldcode)) {
                redisUtil.set(key, 1, getSecondByEndToday());
            } else {
                Integer count = Integer.valueOf(oldcode);
                if (count >= 3) {
                    return ResultUtil.error("您今天已经超过3次，请明天再试");
                }
                count++;
                long expire = redisUtil.getExpire(key);
                redisUtil.set(key, count, expire);
            }
            SmsResp resp =  smsApi.sendSmsType(req);
        } catch (IOException e) {
            log.error("验证码发送失败", e);
            ResultUtil.error("dw-066:验证码发送失败");
        }
        return ResultUtil.ok("发送成功");
    }

    /**
     * 发送验证码接口(注册)
     * @param vo
     * @return
     */
    @PostMapping(value = "/sendMessageCodeByRegister")
    @ResponseBody
    public ResultUtil sendMessageCodeByRegister(@RequestBody RegisterCodeVO vo) {
        try {
            /*if (null == vo.getTimestamp() || null == vo.getSign() || StringUtils.isBlank(vo.getSign())) {
                return ResultUtil.error("签名不合法");
            }
            if (!signatureUtil.verifySignature(vo.getTimestamp(), vo.getSign())) {
                return ResultUtil.error("签名不合法");
            }*/
            if (null == vo.getPhone() || StringUtils.isBlank(vo.getPhone()) || null == vo.getSmsType() || StringUtils.isBlank(vo.getSmsType())) {
                return ResultUtil.ok();
            }
            String lockKey = "SMS_CODE_" + vo.getPhone();
            RLock lock = redissonClient.getLock(lockKey);
            try {
                boolean tryLock = lock.tryLock();
                if (tryLock) {
                    List<TAccount> findPhone = tAccountService.findphone(vo.getPhone());
                    if (!findPhone.isEmpty()) {
                        return ResultUtil.error("手机号已经注册，请更换");
                    }
                    //添加短信类型参数，默认微信端不需要传此参数，APP需要传v
                    String phoneCodeKey = "REGISTER_PHONE_CODE" + vo.getPhone();
                    String phoneCode = ObjectUtils.toString(redisUtil.get(phoneCodeKey));
                    if (StringUtils.isNotBlank(phoneCode)) {
                        return ResultUtil.error("60秒内已经发送过验证码，请稍后再试");
                    } else {
                        redisUtil.set(phoneCodeKey, 1, 60);
                    }
                    SmsReq req = new SmsReq();
                    req.setMobiles(vo.getPhone());
                    req.setType(vo.getSmsType());

                    String key = "REGISTER_PHONE" + vo.getPhone();
                    String oldCode = ObjectUtils.toString(redisUtil.get(key));
                    if (StringUtils.isBlank(oldCode)) {
                        redisUtil.set(key, 1, getSecondByEndToday());
                    } else {
                        int count = Integer.parseInt(oldCode);
                        if (count >= 3) {
                            return ResultUtil.error("您今天已经超过3次，请明天再试");
                        }
                        count++;
                        long expire = redisUtil.getExpire(key);
                        redisUtil.set(key, count, expire);
                    }
                    smsApi.sendSmsType(req);
                } else {
                    return ResultUtil.error("请稍后再试");
                }
            } catch (Exception e) {
                log.error("操作失败", e);
                return ResultUtil.error("操作失败");
            } finally {
                if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                    lock.unlock();
                }
            }

        } catch (Exception e) {
            log.error("验证码发送失败", e);
            ResultUtil.error("dw-066:验证码发送失败");
        }
        return ResultUtil.ok("发送成功");
    }

    /**
     * 发送验证码接口(登录)
     * @auth zhangxin
     *
     * @param phone
     * @return
     */
    @PostMapping(value = "/sendMessageByLogin")
    @ResponseBody
    public ResultUtil sendMessageByLogin(@RequestParam(value = "phone") String phone) {
        try {
            String lockKey = "SMS_CODE_" + phone;
            RLock lock = redissonClient.getLock(lockKey);
            try {
                boolean tryLock = lock.tryLock();
                if (tryLock) {
                    List<TAccount> findphone = tAccountService.findphone(phone);
                    List<TEndUserInfo> info = tEndUserInfoService.selectByAccountNo(phone);
                    //转账人账号不得登录平台
                    if(!info.isEmpty()
                            && null != info.get(0)
                            && null != info.get(0).getUserLogisticsRole()
                            && !info.get(0).getUserLogisticsRole().contains("CTYPEDRVIVER")
                            && !info.get(0).getUserLogisticsRole().contains("CTYPECAPTAIN")
                            && !info.get(0).getUserLogisticsRole().contains("CTYPEBOSS")){
                        return ResultUtil.error("当前身份不能登录此平台！");
                    }
                    if (findphone.size() > 1) {
                        return ResultUtil.error("账号异常");
                    }
                    if (findphone.size() == 0) {
                        return ResultUtil.error("账号填写错误");
                    }
                    TAccount tAccount = findphone.get(0);
                    if (null == tAccount.getUsertype() || StringUtils.isBlank(tAccount.getUsertype()) || DictEnum.CA.code.equals(tAccount.getUsertype())) {
                        return ResultUtil.error("当前身份不能登录此平台！");
                    }

                    TAccountUserInfoDTO userInfo = tAccountService.selectUserInfoByUserId(findphone.get(0).getUserId());
                    if(null != userInfo) {
                        Date date = null == userInfo.getPasswordResetTime() ? null : userInfo.getPasswordResetTime();
                        if (null == date) {
                            return ResultUtil.error("当前密码已过期，请重置密码");
                        }
                        Calendar instance = Calendar.getInstance();
                        instance.setTime(date);
                        instance.add(Calendar.MONTH, 3);
                        Calendar now = Calendar.getInstance();
                        now.setTime(new Date());
                        if (instance.compareTo(now) < 0) {
                            return ResultUtil.error("当前密码已过期，请重置密码");
                        }
                    }
                    String phoneCodeKey = "LOGIN_PHONE_CODE" + phone;
                    String phoneCode = ObjectUtils.toString(redisUtil.get(phoneCodeKey));
                    if (StringUtils.isNotBlank(phoneCode)) {
                        return ResultUtil.error("60秒内已经发送过验证码，请稍后再试");
                    } else {
                        redisUtil.set(phoneCodeKey, 1, 60);
                    }
                    SmsReq req = new SmsReq();
                    req.setMobiles(phone);
                    req.setType("LOGIN");

                    String key = "SMSLOGIN" + phone;
                    String oldcode = ObjectUtils.toString(redisUtil.get(key));
                    if (StringUtils.isBlank(oldcode)) {
                        redisUtil.set(key, 1, getSecondByEndToday());
                    } else {
                        Integer count = Integer.valueOf(oldcode);
                        count++;
                        long expire = redisUtil.getExpire(key);
                        redisUtil.set(key, count, expire);
                        if(count == 8){
                            SmsResp resp =  smsApi.sendSmsType(req);
                            return ResultUtil.error("验证码发送成功，今日登录验证码发送次数剩余2次");
                        }else if (count == 9){
                            SmsResp resp =  smsApi.sendSmsType(req);
                            return ResultUtil.error("验证码发送成功，今日登录验证码发送次数剩余1次");
                        }else if(count == 10){
                            SmsResp resp =  smsApi.sendSmsType(req);
                            return ResultUtil.error("验证码发送成功，今日登录验证码发送次数已用尽");
                        }else if(count > 10){
                            return ResultUtil.error("今日登录验证码发送次数已用尽");
                        }
                    }
                    SmsResp resp =  smsApi.sendSmsType(req);
                } else {
                    return ResultUtil.error("请稍后再试");
                }
            } catch (Exception e) {
                log.error("操作失败", e);
                return ResultUtil.error("操作失败");
            } finally {
                if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                    lock.unlock();
                }
            }

        } catch (Exception e) {
            log.error("验证码发送失败", e);
            ResultUtil.error("dw-067:验证码发送失败");
        }
        return ResultUtil.ok("发送成功");
    }

    /**
     * 发送验证码接口(找回密码)
     * @auth zhangxin
     *
     * @param phone
     * @return
     */
    @PostMapping(value = "/sendMessageByFindPassword")
    @ResponseBody
    public ResultUtil sendMessageByFindPassword(@RequestParam(value = "phone") String phone, String smsType) {
        try {
            String lockKey = "SMS_CODE_" + phone;
            RLock lock = redissonClient.getLock(lockKey);
            try {
                boolean tryLock = lock.tryLock();
                if (tryLock) {
                    // 判断是否在账号表存在数据
                    TAccountVo accountVo = new TAccountVo();
                    accountVo.setAccountNo(phone);
                    ResultUtil resultUtil = tAccountService.selectAccountByPhone(accountVo);
                    String userLogisticsRole = CurrentUser.getUserLogisticsRole();
                    if (null != resultUtil && null != resultUtil.getCode() && resultUtil.getCode().equals("success")) {
                        if (null != resultUtil.getData()) {
                            TAccount data = (TAccount) resultUtil.getData();
                            String usertype = data.getUsertype();
                            if (null == smsType || StringUtils.isEmpty(smsType)) {
                                if (!usertype.equals("CD")) {
                                    return ResultUtil.error("此账号不能在此客户端进行修改");
                                }
                            } else if (smsType.equals("app")) {
                                if (StringUtils.isNotEmpty(usertype)){
                                    if (!usertype.equals("BD")) {
                                        if (usertype.equals("CD")) {
                                            if (null != userLogisticsRole) {
                                                if (!userLogisticsRole.equals(DictEnum.CTYPEMANAGER.code)) {
                                                    return ResultUtil.error("此账号不能在此客户端进行修改");
                                                }
                                            } else {
                                                return ResultUtil.error("此账号不能在此客户端进行修改");
                                            }
                                            return ResultUtil.error("此账号不能在此客户端进行修改");
                                        }
                                    }
                                }
                            }
                        } else {
                            return ResultUtil.error("账号填写错误");
                        }
                    } else {
                        return ResultUtil.error("账号填写错误");
                    }

                    String phoneCodeKey = "LOGIN_REST_SEND_CHANGEPWD_CODE" + phone;
                    String phoneCode = ObjectUtils.toString(redisUtil.get(phoneCodeKey));
                    if (StringUtils.isNotBlank(phoneCode)) {
                        return ResultUtil.error("60秒内已经发送过验证码，请稍后再试");
                    } else {
                        redisUtil.set(phoneCodeKey, 1, 60);
                    }
//                    String key = "YDDCHANGEPWD" + phone;
//                    String oldcode = ObjectUtils.toString(redisUtil.get(key));
//                    if (StringUtils.isBlank(oldcode)) {
//                        redisUtil.set(key, 1, getSecondByEndToday());
//                    } else {
//                        Integer count = Integer.valueOf(oldcode);
//                        if (count >= 5) {
//                            return ResultUtil.error("您今天已经超过5次，请明天再试");
//                        }
//                        count++;
//                        long expire = redisUtil.getExpire(key);
//                        redisUtil.set(key, count, expire);
//                    }

                    SmsReq req = new SmsReq();
                    req.setMobiles(phone);
                    req.setType("CHANGEPWD");
                    SmsResp resp =  smsApi.sendSmsType(req);
                } else {
                    return ResultUtil.error("请稍后再试");
                }
            } catch (Exception e) {
                log.error("操作失败", e);
                return ResultUtil.error("操作失败");
            } finally {
                if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                    lock.unlock();
                }
            }

        } catch (Exception e) {
            log.error("验证码发送失败", e);
            ResultUtil.error("dw-068:验证码发送失败");
        }
        return ResultUtil.ok("发送成功");
    }

    /**
     * 未登录发送验证码接口(找回密码)
     * @auth zhangxin
     *
     * @param phone
     * @return
     */
    @PostMapping(value = "/noLoginSendMessageEditPassword")
    @ResponseBody
    public ResultUtil sendMessageByeditPassword(@RequestParam(value = "phone") String phone, String smsType) {
        try {
            String lockKey = "SMS_CODE_" + phone;
            RLock lock = redissonClient.getLock(lockKey);
            try {
                boolean tryLock = lock.tryLock();
                if (tryLock) {
                    // 判断是否在账号表存在数据
                    TAccountVo accountVo = new TAccountVo();
                    accountVo.setAccountNo(phone);
                    ResultUtil resultUtil = tAccountService.selectAccountByPhone(accountVo);
                    List<String> uerLogistRole = tEndUserInfoService.judgeEndUserByPhone(phone);

                    if (null != resultUtil && null != resultUtil.getCode() && resultUtil.getCode().equals("success") && null != resultUtil.getData()) {
                        if (null == smsType || StringUtils.isEmpty(smsType)) {
                            if (null != uerLogistRole && uerLogistRole.size() > 0) {
                                if (uerLogistRole.toString().contains(DictEnum.CTYPEAGENTPERSON.code) || uerLogistRole.toString().contains(DictEnum.CTYPEMANAGER.code) ||
                                        uerLogistRole.toString().contains(DictEnum.CTYPEAGENTCOMPANY.code) || uerLogistRole.toString().contains(DictEnum.CTYPETRANSPORT.code) ||
                                        uerLogistRole.toString().contains(DictEnum.CTYPETRANSFEROR.code)) {
                                    return ResultUtil.error("此账号不能在此客户端进行修改");
                                }
                            } else {
                                if (null != resultUtil.getData()) {
                                    TAccount data = (TAccount) resultUtil.getData();
                                    if (null == data.getUsertype() || !DictEnum.CD.code.equals(data.getUsertype())) {
                                        return ResultUtil.error("账号异常，请联系客服");

                                    }
                                }
                            }
                        } else if (smsType.equals("app")) {
                            if (null != resultUtil.getData()) {
                                TAccount data = (TAccount) resultUtil.getData();
                                if (null != data.getUsertype()){

                                } else {
                                    return ResultUtil.error("此账号不能在此客户端进行修改");
                                }
                            }
                        }
                        String phoneCodeKey = "REST_SEND_CHANGEPWD_CODE" + phone;
                        String phoneCode = ObjectUtils.toString(redisUtil.get(phoneCodeKey));
                        if (StringUtils.isNotBlank(phoneCode)) {
                            return ResultUtil.error("60秒内已经发送过验证码，请稍后再试");
                        } else {
                            redisUtil.set(phoneCodeKey, 1, 60);
                        }

//                        String phoneKey = "REST_SEND_CHANGEPWD" + phone;
//                        String oldCode = ObjectUtils.toString(redisUtil.get(phoneKey));
//                        if (StringUtils.isBlank(oldCode)) {
//                            redisUtil.set(phoneKey, 1, getSecondByEndToday());
//                        } else {
//                            int count = Integer.parseInt(oldCode);
//                            if (count >= 5) {
//                                return ResultUtil.error("您今天已经超过5次，请明天再试");
//                            }
//                            count++;
//                            long expire = redisUtil.getExpire(phoneKey);
//                            redisUtil.set(phoneKey, count, expire);
//                        }
                    } else {
                        return ResultUtil.error("账号填写错误");
                    }
                    SmsReq req = new SmsReq();
                    req.setMobiles(phone);
                    req.setType("CHANGEPWD");
                    smsApi.sendSmsType(req);
                } else {
                    return ResultUtil.error("请稍后再试");
                }
            } catch (Exception e) {
                log.error("操作失败", e);
                return ResultUtil.error("操作失败");
            } finally {
                if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                    lock.unlock();
                }
            }

        } catch (Exception e) {
            log.error("发送失败", e);
            ResultUtil.error("发送失败");
        }
        return ResultUtil.ok("发送成功");
    }


    public static long getSecondByEndToday() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR,1);
        calendar.set(Calendar.HOUR_OF_DAY,0);
        calendar.set(Calendar.SECOND,0);
        calendar.set(Calendar.MINUTE,0);
        calendar.set(Calendar.MILLISECOND,0);
        return ((calendar.getTimeInMillis()-System.currentTimeMillis()) / 1000) ;
    }
    /**
     * 发送验证码接口(修改手机号旧的)
     * @auth zhangxin
     *
     * @param phone
     * @return
     */
    @PostMapping(value = "/sendMessageByChangeOld")
    @ResponseBody
    public ResultUtil sendMessageByChangeOld(@RequestParam(value = "phone") String phone) {
        String accountId=CurrentUser.getUserAccountId().toString();
        try {
            SmsReq req = new SmsReq();
            req.setMobiles(phone);
            req.setType("CHANGEPHONEOLD");
            req.setAccountId(accountId);
            SmsResp resp =  smsApi.sendSmsType(req);
        } catch (IOException e) {
            log.error("验证码发送失败", e);
            ResultUtil.error("dw-069:验证码发送失败");
        }
        return ResultUtil.ok("发送成功");
    }

    /**
     * 发送验证码接口(修改手机号新的)
     * @auth zhangxin
     *
     * @param phone
     * @return
     */
    @PostMapping(value = "/sendMessageByChangenNew")
    @ResponseBody
    public ResultUtil sendMessageByChangenNew(@RequestParam(value = "phone") String phone) {
        String accountId=CurrentUser.getUserAccountId().toString();
        try {
            SmsReq req = new SmsReq();
            req.setMobiles(phone);
            req.setType("CHANGEPHONENEW");
            req.setAccountId(accountId);
            SmsResp resp =  smsApi.sendSmsType(req);
        } catch (IOException e) {
            log.error("验证码发送失败", e);
            ResultUtil.error("dw-070:验证码发送失败");
        }
        return ResultUtil.ok("发送成功");
    }

    /**
     * 发送验证码接口(添加银行卡)
     * @auth zhangxin
     *
     * @param phone
     * @return
     */
    @PostMapping(value = "/sendMessageByAddCard")
    public ResultUtil sendMessageByAddCard(@RequestParam(value = "phone") String phone,String realName,String bankName) {

        try {
            String accountId= null;
            try {
                accountId = CurrentUser.getUserAccountId().toString();
            } catch (ClassCastException e) {
                accountId = null;
            }
            String phoneCodeKey = "ADD_BANK_CODE" + phone;
            String phoneCode = ObjectUtils.toString(redisUtil.get(phoneCodeKey));
            if (StringUtils.isNotBlank(phoneCode)) {
                return ResultUtil.error("60秒内已经发送过验证码，请稍后再试");
            } else {
                redisUtil.set(phoneCodeKey, 1, 60);
            }
            SmsReq req = new SmsReq();
            req.setMobiles(phone);
            if(realName.equals(bankName)){
                req.setType("ADDBRCARD");
            }else{
                req.setType("ADDCARD");
            }
            req.setAccountId(accountId);
            SmsResp resp =  smsApi.sendSmsType(req);
        } catch (IOException e) {
            log.error("验证码发送失败", e);
            ResultUtil.error("dw-071:验证码发送失败");
        }
        return ResultUtil.ok("发送成功");
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/6 15:36
     *  @Description: 发送验证码接口(添加银行卡)
     */
    @PostMapping(value = "/sendMessageByAddHxyhCard")
    public ResultUtil sendMessageByAddHxyhCard(@RequestParam(value = "realPhone") String realPhone,
                                               String realName,String acctName) {
        try {
            String accountId= null;
            try {
                accountId = CurrentUser.getUserAccountId().toString();
            } catch (ClassCastException e) {
                accountId = null;
            }
            String lockKey = "SMS_CODE_" + realPhone;
            RLock lock = redissonClient.getLock(lockKey);
            try {
                boolean tryLock = lock.tryLock();
                if (tryLock) {
                    String phoneCodeKey = "ADD_BANK_CODE" + realPhone;
                    String phoneCode = ObjectUtils.toString(redisUtil.get(phoneCodeKey));
                    if (StringUtils.isNotBlank(phoneCode)) {
                        return ResultUtil.error("60秒内已经发送过验证码，请稍后再试");
                    } else {
                        redisUtil.set(phoneCodeKey, 1, 60);
                    }
                    TZtAccountOpenInfo tZtAccountOpenInfo = tZtAccountOpenInfoService.selectByAccountId(Integer.valueOf(accountId), DictEnum.CD.code);
                    SmsReq req = new SmsReq();
                    req.setMobiles(realPhone);
                    if(realName.equals(acctName)){
                        req.setType("ADDBRCARD");
                    }else if(tZtAccountOpenInfo.getOpenRealName().equals(acctName)){
                        req.setType("ADDBRCARD");
                    }else{
                        return  ResultUtil.error("不允许绑定非本人银行卡");
                    }
                    req.setAccountId(accountId);
                    SmsResp resp =  smsApi.sendSmsType(req);
                    return ResultUtil.ok();
                } else {
                    return ResultUtil.error("请稍后再试");
                }
            } catch (Exception e) {
                log.error("操作失败", e);
                return ResultUtil.error("操作失败");
            } finally {
                if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                    lock.unlock();
                }
            }

        } catch (Exception e) {
            log.error("验证码发送失败", e);
            ResultUtil.error("dw-071:验证码发送失败");
        }
        return ResultUtil.ok("发送成功");
    }

    //添加本人银行卡
    @PostMapping(value = "/sendMessageByAddOneselfCard")
    public ResultUtil sendMessageByAddOneselfCard(@RequestParam(value = "phone") String phone) {

        try {
            String accountId= null;
            try {
                accountId = CurrentUser.getUserAccountId().toString();
            } catch (ClassCastException e) {
                accountId = null;
            }
            SmsReq req = new SmsReq();
            req.setMobiles(phone);
            req.setType("ADDONESECARD");
            req.setAccountId(accountId);
            SmsResp resp =  smsApi.sendSmsType(req);
        } catch (IOException e) {
            log.error("验证码发送失败", e);
            ResultUtil.error("dw-071:验证码发送失败");
        }
        return ResultUtil.ok("发送成功");
    }

    /**changePhoneNext
     * 发送验证码接口( 删除银行卡)
     * @auth zhangxin
     *
     * @param phone
     * @return
     */
    @PostMapping(value = "/sendMessageByDelCard")
    @ResponseBody
    public ResultUtil sendMessageByDelCard(@RequestParam(value = "phone") String phone) {
        try {
            String accountId= null;
            try {
                accountId = CurrentUser.getUserAccountId().toString();
            } catch (ClassCastException e) {
                accountId = null;
            }
            List<TAccount> findphone = tAccountService.findphone(phone);
            if (findphone.size() == 0) {
                return ResultUtil.error("该手机号尚未注册");
            }
            SmsReq req = new SmsReq();
            req.setMobiles(phone);
            req.setType("DELCARD");
            req.setAccountId(accountId);
            SmsResp resp =  smsApi.sendSmsType(req);
        } catch (IOException e) {
            log.error("验证码发送失败", e);
            ResultUtil.error("dw-072:验证码发送失败");
        }
        return ResultUtil.ok("发送成功");
    }

    @PostMapping(value = "/sendMessageByOpenRole")
    @ResponseBody
    public ResultUtil sendMessageByOpenRole(@RequestParam(value = "phone") String phone) {
        try {
            String accountId= null;
            try {
                accountId = CurrentUser.getUserAccountId().toString();
            } catch (ClassCastException e) {
                accountId = null;
            }
            List<TAccount> findphone = tAccountService.findphone(phone);
            if (findphone.size() == 0) {
                return ResultUtil.error("该手机号尚未注册");
            }
            SmsReq req = new SmsReq();
            req.setMobiles(phone);
            req.setType("OPENROLE");
            req.setAccountId(accountId);
            SmsResp resp =  smsApi.sendSmsType(req);
        } catch (IOException e) {
            log.error("验证码发送失败!", e);
            ResultUtil.error("验证码发送失败！");
        }
        return ResultUtil.ok("发送成功");
    }
    /**
     * 发送验证码接口( 提现)
     */
    @PostMapping(value = "/sendMessageByTiXian")
    @ResponseBody
    public ResultUtil sendMessageByTiXian(@RequestParam(value = "phone") String phone, @RequestParam(value = "orderBusinessCode", required = false) String orderBusinessCode) {
        String accountId=CurrentUser.getUserAccountId().toString();
        try {
            String lockKey = "SMS_CODE_" + phone;
            RLock lock = redissonClient.getLock(lockKey);
            try {
                boolean tryLock = lock.tryLock();
                if (tryLock) {
                    List<TAccount> findphone = tAccountService.findphone(phone);
                    if (findphone.isEmpty()) {
                        return ResultUtil.error("该手机号尚未注册");
                    }
                    try {
                        if (null != CurrentUser.getEndUserId()) {
                            TEndUserInfo tEndUserInfo = tEndUserInfoService.selectById(CurrentUser.getEndUserId());
                            if (null != tEndUserInfo && null != tEndUserInfo.getCreateTime()) {
                                Date createTime = tEndUserInfo.getCreateTime();
                                Date date = DateUtils.plusHours(24, createTime);
                                if (date.getTime() > new Date().getTime()) {
                                    Calendar calendar = Calendar.getInstance();
                                    calendar.setTime(date);
                                    int month = calendar.get(Calendar.MONTH) + 1;
                                    return ResultUtil.error("注册24小时后才可发起提现，请于" + month + "月"
                                            + calendar.get(Calendar.DAY_OF_MONTH)
                                            + "日" + calendar.get(Calendar.HOUR_OF_DAY) + "：" + calendar.get(Calendar.MINUTE) + "后再进行操作");
                                }
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("查询用户信息失败, {}", ThrowableUtil.getStackTrace(e));
                    }

                    String phoneCodeKey = "TIXIAN_CODE" + phone;
                    String phoneCode = ObjectUtils.toString(redisUtil.get(phoneCodeKey));
                    if (StringUtils.isNotBlank(phoneCode)) {
                        return ResultUtil.error("60秒内已经发送过验证码，请稍后再试");
                    } else {
                        redisUtil.set(phoneCodeKey, 1, 60);
                    }

                    SmsReq req = new SmsReq();
                    req.setMobiles(phone);
                    req.setType("TIXIAN");
                    req.setAccountId(accountId);
                    if (null != orderBusinessCode && StringUtils.isNotEmpty(orderBusinessCode)) {
                        req.setOrderBusinessCode(orderBusinessCode);
                    }
                    SmsResp resp = smsApi.sendSmsType(req);
                } else {
                    return ResultUtil.error("请稍后再试");
                }
            } catch (Exception e) {
                log.error("操作失败", e);
                return ResultUtil.error("操作失败");
            } finally {
                if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                    lock.unlock();
                }
            }

        } catch (Exception e) {
            log.error("验证码发送失败, {}", ThrowableUtil.getStackTrace(e));
            ResultUtil.error("验证码发送失败");
        }
        return ResultUtil.ok("发送成功");
    }





    /**
     * @Author: cyp
     * @Description:  经纪人提现验证码
     * @Date: 2020/3/12
     * @return
     **/
     @PostMapping(value = "/sendMessageByAgentTiXian")
     @ResponseBody
    public ResultUtil sendMessageByAgentTiXian(@RequestParam(value = "phone") String phone) {
         SysParam sysParam = sysParamAPI.getParamByKey("MANAGERTX");
         if (null != sysParam && null != sysParam.getParamValue() && "1".equals(sysParam.getParamValue())) {
             return ResultUtil.error("暂不支持提现");
         }
        String accountId=CurrentUser.getUserAccountId().toString();
        try {
            List<TAccount> findphone = tAccountService.findphone(phone);
            if (findphone.size() == 0) {
                return ResultUtil.error("该手机号尚未注册");
            }
            SmsReq req = new SmsReq();
            req.setMobiles(phone);
            req.setType("AGENTTIXIAN");
            req.setAccountId(accountId);
            SmsResp resp =  smsApi.sendSmsType(req);
        } catch (IOException e) {
            log.error("dw-073:验证码发送失败", e);
            ResultUtil.error("dw-073:验证码发送失败");
        }
        return ResultUtil.ok("发送成功");
    }

    @PostMapping(value = "/sendMessageByCarrierTiXian")
    @ResponseBody
    public ResultUtil sendMessageByCarrierTiXian(@RequestParam(value = "phone") String phone) {
        //String accountId=CurrentUser.getUserAccountId().toString();
        try {
            //List<TAccount> findphone = tAccountService.findphone(phone);
            List<TSysUser> findphone=  systemAPI.selectByPhone(phone);
            if (findphone.size() == 0) {
                return ResultUtil.error("该手机号尚未注册");
            }
            SmsReq req = new SmsReq();
            req.setMobiles(phone);
            req.setType("CARRIERTIXIAN");
            req.setAccountId(findphone.get(0).getId().toString());
            SmsResp resp =  smsApi.sendSmsType(req);
        } catch (IOException e) {
            log.error("dw-073:验证码发送失败", e);
            ResultUtil.error("dw-073:验证码发送失败");
        }
        return ResultUtil.ok("发送成功");
    }

    @PostMapping(value = "/sendMessageByPfTiXian")
    @ResponseBody
    public ResultUtil sendMessageByPfTiXian(@RequestParam(value = "phone") String phone) {
        //String accountId=CurrentUser.getUserAccountId().toString();
        try {
            //List<TAccount> findphone = tAccountService.findphone(phone);
            List<TSysUser> findphone=  systemAPI.selectByPhone(phone);
            if (findphone.size() == 0) {
                return ResultUtil.error("该手机号尚未注册");
            }
            SmsReq req = new SmsReq();
            req.setMobiles(phone);
            req.setType("PFTIXIAN");
            req.setAccountId(findphone.get(0).getId().toString());
            SmsResp resp =  smsApi.sendSmsType(req);
        } catch (IOException e) {
            log.error("dw-073:验证码发送失败", e);
            ResultUtil.error("dw-073:验证码发送失败");
        }
        return ResultUtil.ok("发送成功");
    }
    @PostMapping(value = "/sendMessageByCompanyTiXian")
    @ResponseBody
    public ResultUtil sendMessageByCompanyTiXian(@RequestParam(value = "phone") String phone) {
        String accountId=CurrentUser.getUserAccountId().toString();
        try {
            List<TAccount> findphone = tAccountService.findphone(phone);
            if (findphone.size() == 0) {
                return ResultUtil.error("该手机号尚未注册");
            }
            SmsReq req = new SmsReq();
            req.setMobiles(phone);
            req.setType("COMPANYTIXIAN");
            req.setAccountId(accountId);
            SmsResp resp =  smsApi.sendSmsType(req);
        } catch (IOException e) {
            log.error("dw-073:验证码发送失败", e);
            ResultUtil.error("dw-073:验证码发送失败");
        }
        return ResultUtil.ok("发送成功");
    }

    /**
     * @Author: dingweibo
     * @Description:  紧急助手中运单审核发送验证码
     * @Date: 2021/4/23
     * @return
     **/
    @PostMapping(value = "/sendMessageByYdsh")
    @ResponseBody
    public ResultUtil sendMessageByYdsh(@RequestParam(value = "phone") String phone) {
        try {
            SmsReq req = new SmsReq();
            req.setMobiles(phone);
            req.setType("YDSH");
            SmsResp resp =  smsApi.sendSmsType(req);
        } catch (IOException e) {
            log.error("dw-073:验证码发送失败", e);
            ResultUtil.error("dw-073:验证码发送失败");
        }
        return ResultUtil.ok("发送成功");
    }


    /**
     *  @author: dingweibo
     *  @Date: 2021/8/24 17:19
     *  @Description:  删除平台服务费配置发送验证码接口
     */
    @PostMapping(value = "/sendMessageByFwfpz")
    @ResponseBody
    public ResultUtil sendMessageByFwfpz(@RequestParam(value = "phone") String phone) {
        try {
            SmsReq req = new SmsReq();
            req.setMobiles(phone);
            req.setType("FWFPZ");
            SmsResp resp =  smsApi.sendSmsType(req);
        } catch (IOException e) {
            log.error("dw-073:验证码发送失败", e);
            ResultUtil.error("dw-073:验证码发送失败");
        }
        return ResultUtil.ok("发送成功");
    }

    /**
     * 完善个人资料页面
     * @auth zhangxin
     *
     * @return
     */
    @PostMapping(value = "/perfectDataPage")
    @ResponseBody
    public ResultUtil perfectDataPage() {
        try{
            Integer enuserid = CurrentUser.getEndUserId();
            ResultUtil tEndUserInfo = tEndUserInfoService.findByUserId(enuserid);
            return tEndUserInfo;
        }catch (Exception e){
            log.error("dw-074:查询完善个人资料页面失败",e);
            return ResultUtil.error("dw-074:查询完善个人资料页面失败");
        }
    }

    //    /**
//     * 完善个人资料
//     * @auth zhangxin
//     *
//     * @param record
//     * @return
//     */
//    @PostMapping(value = "/perfectData")
//    @ResponseBody
//    public ResultUtil perfectData(@RequestBody TUserVo record) {
//        Integer enuserid = CurrentUser.getEndUserId();
//        TEndUserInfo tEndUserInfo = tEndUserInfoService.findById(enuserid);
//        tEndUserInfo.setIdcard(record.getIdcard());
//        tEndUserInfo.setRealName(record.getUsername());
//        tEndUserInfo.setIdcardPhoto1(record.getIdcardPhoto1());
//        tEndUserInfo.setIdcardPhoto2(record.getIdcardPhoto2());
//        if (record.getItemCode().equals("CTYPEDRVIVER")) {
//            tEndUserInfo.setDrivingLicencesPhoto1(record.getDrivingLicencesPhoto1());
//            tEndUserInfo.setDrivingLicencesPhoto2(record.getDrivingLicencesPhoto2());
//            tEndUserInfo.setCertificatePhoto1(record.getCertificatePhoto1());
//            tEndUserInfo.setCertificatePhoto2(record.getCertificatePhoto2());
//        }
//        tEndUserInfoService.updataData(tEndUserInfo);
//        return new ResultUtil(CodeEnum.SUCCESS.getCode(), "已提交审核！");
//    }
    @Log(value = "完善个人资料修改")
    @PostMapping(value = "/perfectData")
    @ResponseBody
    public ResultUtil perfectData(@RequestBody TUserVo resources) {
        RLock lock = null;
        RLock phoneLock = null;
        try {
            if (resources == null) {
                throw new BadRequestException("不能为空");
            }
            Integer endUserId = CurrentUser.getEndUserId();
            TEndUserInfo tEndUserInfo = tEndUserInfoService.selectById(endUserId);
            phoneLock = redissonClient.getLock(DictEnum.CTYPEDRVIVER.code + tEndUserInfo.getPhone());
            lock = redissonClient.getLock(String.valueOf(endUserId));
            boolean tryLock = lock.tryLock(5, TimeUnit.SECONDS);
            boolean tryPhoneLock = phoneLock.tryLock(5, TimeUnit.SECONDS);
            if (tryLock && tryPhoneLock) {
                return wxLoginService.perfectData(resources);
            } else {
                return ResultUtil.error("当前用户正在处理中，请稍后再试！");
            }
        } catch (Exception e) {
            log.error("完善个人资料修改失败", e);
            return ResultUtil.error("dw-075:完善个人资料修改失败");
        } finally {
            if (null != lock && lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
            if (null != phoneLock && phoneLock.isHeldByCurrentThread() && phoneLock.isLocked()) {
                phoneLock.unlock();
            }
        }

    }
    /**
     * 提现中的运单列表
     * @auth zhangxin
     *
     * @param
     * @return REsultUtil
     */
    @PostMapping("/updateIfUsed")
    public int updateIfUsed(@RequestParam(value = "phone") String phone,@RequestParam(value = "code") String code) {
        return tAccountService.updateIfUsed(phone, code);
    }


    /**
    * @Description 更新openId
    * <AUTHOR>
    * @Date   2019/7/16 19:26
    * @param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/updateOpenId")
    public ResultUtil updateOpenId(@RequestBody TAccountVo record){
        Integer accountId = CurrentUser.getUserAccountId();
        record.setId(accountId);
        int i = tAccountService.updateOpenId(record);
        return ResultUtil.ok();
    }

    /**
     * 身份证是否已注册
     * @param Idcard
     * @return
     */
    @PostMapping("/IdcardBeOverdue")
    public ResultUtil IdcardBeOverdue(@RequestParam(value = "Idcard") String Idcard){
        List<TEndUserInfo> findIdCard = tEndUserInfoService.findIdCard(Idcard);
        if (null !=findIdCard && !"".equals(findIdCard) && findIdCard.size() > 0) {
            return ResultUtil.error("您的身份证号已注册，请直接登录，如忘记账号，请联系客服解决。");
        }
        return ResultUtil.ok();
    }

    /**
     * 是否确认用户协议
     * @return
     */
    @PostMapping("/ifAgreement")
    public ResultUtil ifAgreement(@RequestParam(value = "phone")String phone){
        try{
             ResultUtil resultUtil = tEndUserInfoService.selectIfAgreement(phone);
             return  resultUtil;
        }catch (Exception e){
            log.error("用户协议操作失败", e);
            return ResultUtil.error("dw-076：用户协议操作失败！");
        }
    }

    /**
     *更改用户协议状态
     * @return
     */
    @PostMapping("/updateIfAgreement")
    public ResultUtil updateIfAgreement(@RequestParam(value = "phone")String phone){
        try{
            ResultUtil resultUtil = tEndUserInfoService.updateIfAgreement(phone);
            return  resultUtil;
        }catch (Exception e){
            log.error("确认用户协议操作失败", e);
            return ResultUtil.error("dw-076：确认用户协议操作失败！");
        }
    }
    @PostMapping("/getTokenObtain")
    public ChinaDataBaoObtainResp getTokenObtain(@RequestBody RxdbVo vo){
        ChinaDataBaoObtainResp resp = new ChinaDataBaoObtainResp();
        try{
            ChinaDataBao chinaDataBao = new ChinaDataBao();
            resp = chinaDataBao.tokenObtain();
            redisUtil.set("TokenObtain"+resp.getData(),vo.getPhone());
        }catch (Exception e){
            log.error("获取数据宝活体检测token失败", e);
            resp.setCode("error");
            resp.setMsg("获取失败");
        }
        return  resp;
    }
    @PostMapping("/communication/callback")
    public void communicationCallback(HttpServletRequest request){
        try{
            CommunicationCallbacklReq callbacklReq = new CommunicationCallbacklReq();
            callbacklReq.setCode(request.getParameter("code"));
            callbacklReq.setMessage(request.getParameter("message"));
            callbacklReq.setBackScore(request.getParameter("backScore"));
            callbacklReq.setBackResult(request.getParameter("backResult"));
            callbacklReq.setBackNo(request.getParameter("backNo"));
            callbacklReq.setSerialNumber(request.getParameter("serialNumber"));
            callbacklReq.setSourceIp(request.getParameter("sourceIp"));
            callbacklReq.setToken(request.getParameter("token"));
            callbacklReq.setBackImg(request.getParameter("backImg"));
            callbacklReq.setBackVideo(request.getParameter("backVideo"));
            wxLoginService.communicationPersonal(callbacklReq);
        }catch (Exception e){
            log.error("获取数据宝活体检测回调失败", e);
        }
    }
    //app端查询人相对比结果   对比成功后调用提现接口
    @PostMapping("/communicationPersonal")
    public ResultUtil communicationPersonal(@RequestBody HxOrderTxVO txVO){
        ResultUtil resultUtil = new ResultUtil();
        try{
            Object obj = redisUtil.get("rxdb"+txVO.getPhone());
            if(null!=obj && !"".equals(obj)){
                resultUtil = (ResultUtil) redisUtil.get("rxdb"+txVO.getPhone());
                if("10000".equals(resultUtil.getCode())){
                    txVO.setIfSmsCode(false);
                    resultUtil = wxOrderControllerAPI.hxTx(txVO);
                }
                redisUtil.del("rxdb"+txVO.getPhone());
            }else{
                resultUtil.setCode("error");
                resultUtil.setMsg("提现失败：获取人脸失败");
            }
        }catch (Exception e){
            log.error("获取人像对比结果失败", e);
            resultUtil.setCode("error");
            resultUtil.setMsg("获取人像对比结果失败");
        }
        return  resultUtil;
    }
}

