package com.lz.controller.wx;

import com.lz.common.config.RedisUtil;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.model.BankCardReq;
import com.lz.common.model.BankCardResp;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.IntegrationBankCard;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.model.TEndUserInfo;
import com.lz.service.TAccountService;
import com.lz.service.TEndCarInfoService;
import com.lz.service.TEndUserInfoService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("/personalCenter")
public class WxPersonalCenterController {
    @Autowired
    private TAccountService tAccountService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private TEndCarInfoService endCarInfoService;
    @Autowired
    private TEndCarInfoService tEndCarInfoService;

    @Autowired
    private TEndUserInfoService endUserInfoService;
    @Autowired
    private RedissonClient redissonClient;

    /**
     * 获取个人中心数据
     * @auth zhangxin
     *
     * @param
     * @return REsultUtil
     */
    @PostMapping("/getData")
    public ResultUtil getData() {
        try{
            Integer endUserId = CurrentUser.getEndUserId();
            Integer accountid = CurrentUser.getUserAccountId();
            String lockKey = "GET_DATA_" + endUserId;
            RLock lock = redissonClient.getLock(lockKey);
            try {
                boolean tryLock = lock.tryLock();
                if(tryLock){
                    return tAccountService.getData(accountid, endUserId);
                }else{
                    return ResultUtil.error("请稍后再试");
                }
            }catch (Exception e){
                log.error("操作失败", e);
                return ResultUtil.error("操作失败");
            }finally {
                if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                    lock.unlock();
                }
            }
        }catch (Exception e){
            log.error("获取个人中心失败",e);
            return ResultUtil.error("dw-076:获取个人中心失败");
        }
    }

    /**
     * 银行卡3、4要素综合验证
     * @auth zhangxin
     *
     * @return REsultUtil
     */
    @PostMapping("/checkBank")
    public ResultUtil checkBank(@RequestBody BankCardReq bankCardReq) {
        try{
            IntegrationBankCard ibc = new IntegrationBankCard();
            BankCardResp bankCardRespa = ibc.checkBankCard(bankCardReq);
            if ( StringUtils.isNotBlank(bankCardRespa.getResult())
                    && BankCardResp.RESULT_UNANIMOUS_CODE.equals(bankCardRespa.getResult())) {
                return ResultUtil.ok();
            } else {
                return ResultUtil.error(bankCardRespa.getDesc());
            }
        }catch (Exception e){
            log.error("银行卡综合验证失败",e);
            return ResultUtil.error("dw-077:银行卡综合验证失败");
        }
    }


    /**
    * @Description 查询当前登录用户的角色
    * <AUTHOR>
    * @Date   2019/7/9 23:06
    * @Param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/selectEnduserRoleForLogin")
    public ResultUtil selectEnduserRoleForLogin(){
        ResultUtil resultUtil = new ResultUtil();
        String userAccountNo = CurrentUser.getUserAccountNo();
        String cphone = "CPHONE" + userAccountNo;
        if (null!=redisUtil.get(cphone) && !"".equals(redisUtil.get(cphone))){
            resultUtil.setCode(DictEnum.SUCCESS.code);
            resultUtil.setData(CurrentUser.getUserLogisticsRole());
            return resultUtil;
        }
        List<TEndUserInfo> tEndUserInfoList = endUserInfoService.selectByAccountNo(CurrentUser.getUserAccountNo());
        if(tEndUserInfoList.size()==1){
            if(null == tEndUserInfoList.get(0).getUserLogisticsRole()){
                return ResultUtil.ok();
            }
            List<Map> array = new ArrayList<>();
            String[] split = tEndUserInfoList.get(0).getUserLogisticsRole().split(",");
            if (split.length == 1){
                String redisKey = "CROLE" + tEndUserInfoList.get(0).getId();
                redisUtil.set(redisKey, split[0]);
                redisUtil.set("C"+CurrentUser.getUserAccountId(), tEndUserInfoList.get(0).getId());

                String userAccount = CurrentUser.getUserAccountNo();
                String cp = "CPHONE" + userAccount;
                redisUtil.set(cp, split[0]);

                resultUtil.setCode(DictEnum.SUCCESS.code);
                resultUtil.setData(split[0]);
                return resultUtil;
            }else if(split.length>1){
                for (String role: split){
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("endUserId",tEndUserInfoList.get(0).getId());
                    if (role.equals(DictEnum.CTYPEDRVIVER.code)){
                        map.put("text", "司机");
                        map.put("type",role);
                    }
                    if (role.equals(DictEnum.CTYPEBOSS.code)){
                        map.put("text", "车主");
                        map.put("type",role);
                    }
                    if (role.equals(DictEnum.CTYPECAPTAIN.code)){
                        map.put("text", "车队长");
                        map.put("type",role);
                    }
                    array.add(map);
                }
                resultUtil.setCode(DictEnum.ERROR.code);
                resultUtil.setData(array);
                return resultUtil;
            }
        }else if(tEndUserInfoList.size()>1){
            List<Map> array = new ArrayList<>();
            for(TEndUserInfo tEndUserInfo:tEndUserInfoList){
                String[] split = tEndUserInfo.getUserLogisticsRole().split(",");
                for(String userRole:split){
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("endUserId",tEndUserInfo.getId());
                    if (userRole.equals(DictEnum.CTYPEDRVIVER.code)){
                        map.put("text", "司机");
                        map.put("type",userRole);
                    }
                    if (userRole.equals(DictEnum.CTYPEBOSS.code)){
                        map.put("text", "车主");
                        map.put("type",userRole);
                    }
                    if (userRole.equals(DictEnum.CTYPECAPTAIN.code)){
                        map.put("text", "车队长");
                        map.put("type",userRole);
                    }
                    array.add(map);
                }
            }

            resultUtil.setCode(DictEnum.ERROR.code);
            resultUtil.setData(array);
            return resultUtil;
        }
        /*String logisticsRole = CurrentUser.getUserLogisticsRole();
        String[] split = logisticsRole.split(",");
        if (split.length == 1){
            ResultUtil resultUtil = ResultUtil.ok();
            resultUtil.setData(split[0]);
            return resultUtil;
        }
        Integer endUserId = CurrentUser.getEndUserId();
        TEndUserInfo endUserInfo = endUserInfoService.selectById(endUserId);
        String userLogisticsRole = endUserInfo.getUserLogisticsRole();
        String[] userRole = userLogisticsRole.split(",");

        if (userRole.length >1){
            resultUtil.setCode("error");
            HashMap<String, String> map = new HashMap<>();
            for (String role: userRole){
                if (role.equals(DictEnum.CTYPEDRVIVER.code)){
                    map.put(role, "司机");
                }
                if (role.equals(DictEnum.CTYPEBOSS.code)){
                    map.put(role, "车主");
                }
            }
            resultUtil.setData(map);
            return resultUtil;
        }*/
        return ResultUtil.ok();
    }


    /**
    * @Description 根据用户选择的角色，设置当前登录用户的enduserId
    * <AUTHOR>
    * @Date   2019/7/9 23:09
    * @Param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/setCurrentEnduserRole")
    public ResultUtil setCurrentEnduserId(@RequestParam(value = "userLogisticsRole") String userLogisticsRole,
                                          @RequestParam(value = "endUserId") Integer endUserId){
        if (StringUtils.isEmpty(userLogisticsRole)){
            return ResultUtil.error("请选择角色");
        }
        String redisKey = "CROLE" + endUserId;
        redisUtil.set(redisKey, userLogisticsRole);
        redisUtil.set("C"+CurrentUser.getUserAccountId(), endUserId);
        String userAccountNo = CurrentUser.getUserAccountNo();
        String cphone = "CPHONE" + userAccountNo;
        redisUtil.set(cphone, userLogisticsRole);
        return ResultUtil.ok();
    }



    /**
    * @Description 微信端退出
    * <AUTHOR>
    * @Date   2019/7/10 9:24
    * @Param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/logout")
    public ResultUtil logout(HttpServletRequest request){
        //如果用户存在多种角色，清除Redis中设置的当前用户存在de 角色
        Integer endUserId = CurrentUser.getEndUserId();
        redisUtil.del("CROLE" + endUserId);
        redisUtil.del("C"+CurrentUser.getUserAccountId());
        redisUtil.del("CPHONE" + CurrentUser.getUserAccountNo());
        try {
            String currentUsername = CurrentUser.getCurrentUsername();
            redisUtil.del(currentUsername);
            String tokenValue = request.getHeader("authorization");
            log.info(tokenValue);
            if (tokenValue.contains("%20")) {
                tokenValue = tokenValue.replace("%20", " ");
            }
            String[] tokenWithPrefix = tokenValue.split(" ");
            redisUtil.del("auth:" + tokenWithPrefix[1]);
            redisUtil.del("access:" + tokenWithPrefix[1]);
            redisUtil.del("access_to_refresh:" + tokenWithPrefix[1]);
            MessageDigest digest;
            Map<String, String> values = new LinkedHashMap<String, String>();
            values.put("username", CurrentUser.getCurrentUsername());
            values.put("client_id", "client");
            values.put("scope", "success");
            String key = tokenWithPrefix[1];
            digest = MessageDigest.getInstance("MD5");
            byte[] bytes = digest.digest(values.toString().getBytes("UTF-8"));
            key = String.format("%032x", new BigInteger(1, bytes));
            log.info(key);
            redisUtil.del("auth_to_access:" + key);
        } catch (Exception e) {
            log.error("清除token失败", e);
        }
        return ResultUtil.ok();
    }



}

