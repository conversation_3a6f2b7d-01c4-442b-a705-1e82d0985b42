package com.lz.controller.wx;

import com.lz.api.TrajectoryRecentAPI;
import com.lz.common.annotation.validated.AddGroup;
import com.lz.common.annotation.validated.UpdateGroup;
import com.lz.common.config.RedisUtil;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.model.BankCardReq;
import com.lz.common.model.BankCardResp;
import com.lz.common.model.CodeEnum;
import com.lz.common.redisson.RedissLockUtil;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.IntegrationBankCard;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.dao.TBankCardMapper;
import com.lz.dao.TEnduserAccountMapper;
import com.lz.dao.TJdBankCardMapper;
import com.lz.model.*;
import com.lz.model.trajectory.req.TrajectoryRouterPathReq;
import com.lz.model.trajectory.resp.VQueryLicenseResp;
import com.lz.model.trajectory.resp.recent.VQueryLicenseV2Resp;
import com.lz.service.*;
import com.lz.system.api.SysParamAPI;
import com.lz.system.api.SystemAPI;
import com.lz.system.model.SysParam;
import com.lz.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/wxBank")
public class WxBankController {
    @Autowired
    private TAccountService tAccountService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private TBankCardService tBankCardService;
    @Autowired
    private TEndCarInfoService tEndCarInfoService;
    @Autowired
    private TEndUserCarRelService tEndUserCarRelService;
    @Autowired
    private TEndUserInfoService tEndUserInfoService;

    @Resource
    private TrajectoryRecentAPI trajectoryRecentAPI;

    @Resource
    private TBankCardMapper tBankCardMapper;
    @Autowired
    private TVerificationCodeLogService tVerificationCodeLogService;

    @Autowired
    private SysParamAPI sysParamAPI;

    @Autowired
    private SystemAPI systemAPI;

    @Autowired
    private TBankCardService bankCardService;

    @Autowired
    private TJDBankCardService tjdBankCardService;

    @Resource
    private TJdBankCardMapper tJdBankCardMapper;

    @Resource
    private TEnduserAccountMapper tEnduserAccountMapper;

    @Autowired
    private TZtBankUserService ztBbankUserService;
    @Autowired
    private  TZtAccountOpenInfoService tZtAccountOpenInfoService;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 添加银行卡
     * @auth zhangxin
     *
     * @param tBankCard
     * @return REsultUtil
     */
    @Deprecated
    @PostMapping("/addCard")
    public ResultUtil addCard(@RequestBody TBankCardVo tBankCard) {
        try{
            Integer accouontid =null;
            if(tBankCard.getAccountId()!=null){
                accouontid =tBankCard.getAccountId();
            }else{
                accouontid = CurrentUser.getUserAccountId();
            }
            TAccount t =  tAccountService.selectByPrimaryKey(accouontid);
            String code = "";
            if(t.getNickname().equals(tBankCard.getCardOwner())){
                code = ObjectUtils.toString(redisUtil.get("ADDBRCARD" + tBankCard.getPhone()));
            }else{
                code = ObjectUtils.toString(redisUtil.get("ADDCARD" + tBankCard.getPhone()));
            }
            List<TAccount> findphone = tAccountService.findphone(tBankCard.getPhone());
            List<TBankCard> b = tAccountService.getBankCardListByCardNo(tBankCard.getCardNo(), accouontid);
            List<TBankCard> c = tEndCarInfoService.getBankCardByAccountId(accouontid);
            BankCardReq bankCardReq = new BankCardReq();
            bankCardReq.setBankCard(tBankCard.getCardNo());
            bankCardReq.setIdCard(tBankCard.getCardOwnerIdcard());
            bankCardReq.setName(tBankCard.getCardOwner());
            if (StringUtils.isEmpty(tBankCard.getPhone())) {
                return ResultUtil.error("请输入手机号码");
            } else if (findphone.size() == 0) {
                return ResultUtil.error("该手机号未注册");
            } else if (StringUtils.isEmpty(tBankCard.getCode())) {
                return ResultUtil.error("请输入验证码");
            } else if (StringUtils.isEmpty(code)) {
                return ResultUtil.error("请重新发送验证码");
            } else if (!tBankCard.getCode().equals(code)) {
                return ResultUtil.error("验证码错误");
            } else if (b == null || b.size() == 0) {
                TVerificationCodeLog tVerificationCodeLog=new TVerificationCodeLog();
                tVerificationCodeLog.setReceivePhoneno(tBankCard.getPhone());
                tVerificationCodeLog.setVerificationCode(code);
                tVerificationCodeLogService.updateIfUsed(tVerificationCodeLog);

                SysParam sysParam = sysParamAPI.getParamByKey("IFKBIN");
                //是否开启 银行卡三要素校验 0否 1是
                if("1".equals(sysParam.getParamValue())){
                    IntegrationBankCard ibc = new IntegrationBankCard();
                    BankCardResp bankCardRespa = ibc.checkBankCard(bankCardReq);
                    if ( StringUtils.isNotBlank(bankCardRespa.getResult())
                            && BankCardResp.RESULT_UNANIMOUS_CODE.equals(bankCardRespa.getResult())) {
                        TBankCard tBankCard1 = new TBankCard();
                        tBankCard1.setCardNo(tBankCard.getCardNo());
                        tBankCard1.setCardOwnerPhone(tBankCard.getPhone());
                        tBankCard1.setCardOwner(tBankCard.getCardOwner());
                        tBankCard1.setCardOwnerIdcard(tBankCard.getCardOwnerIdcard());
                        tBankCard1.setAccountId(findphone.get(0).getId());
                        tBankCard1.setCreateTime(new Date());
                        //tBankCard1.setCreateUser(t.getNickname());
                        tBankCard1.setEnable(false);
                        // tBankCard1.setBankName(bankCardRespa.getBankName());
                        if (c.size() == 0) {//第一张银行卡是默认的
                            tBankCard1.setIfDefault(1);
                        } else if (c.size()!=0) {//不是第一次添加，判断选择的是不是默认的
                            if (tBankCard.getIfDefault() != null && tBankCard.getIfDefault() == 1) {
                                int i = tBankCardMapper.updateByAccountId(accouontid);
                            }
                            tBankCard1.setIfDefault(tBankCard.getIfDefault());
                        }
                        tEndUserInfoService.insertCard(tBankCard1);
                        Map<String, Object> map = new HashMap<>();
                        map.put("bankName", tBankCard1.getBankName());//姓名
                        map.put("id",tBankCard1.getId());
                        return new ResultUtil(CodeEnum.SUCCESS.getCode(), map, "银行卡添加成功");
                    } else {
                        return ResultUtil.error(bankCardRespa.getDesc());
                    }
                }else{
                    TBankCard tBankCard1 = new TBankCard();
                    tBankCard1.setCardNo(tBankCard.getCardNo());
                    tBankCard1.setCardOwnerPhone(tBankCard.getPhone());
                    tBankCard1.setCardOwner(tBankCard.getCardOwner());
                    tBankCard1.setCardOwnerIdcard(tBankCard.getCardOwnerIdcard());
                    tBankCard1.setAccountId(findphone.get(0).getId());
                    tBankCard1.setCreateTime(new Date());
                    tBankCard1.setEnable(false);
                    if (c.size() == 0) {//第一张银行卡是默认的
                        tBankCard1.setIfDefault(1);
                    } else if (c.size()!=0) {//不是第一次添加，判断选择的是不是默认的
                        if (tBankCard.getIfDefault() != null && tBankCard.getIfDefault() == 1) {
                            int i = tBankCardMapper.updateByAccountId(accouontid);
                        }
                        tBankCard1.setIfDefault(tBankCard.getIfDefault());
                    }
                    tEndUserInfoService.insertCard(tBankCard1);
                    Map<String, Object> map = new HashMap<>();
                    map.put("id",tBankCard1.getId());
                    return new ResultUtil(CodeEnum.SUCCESS.getCode(), map, "银行卡添加成功");
                }
            } else {
                return ResultUtil.error("该银行卡已被添加，请更换新卡");
            }
        }catch (Exception e){
            log.error("银行卡添加失败",e);
            return ResultUtil.error("dw-052:银行卡添加失败");
        }
    }

    /**
    * @description 添加银行卡
    * <AUTHOR>
    * @date 2021/4/12 09:40
    * @param
    * @return
    */
    @PostMapping("/addCard/v2")
    public ResultUtil addCardV2(@Validated({AddGroup.class}) @RequestBody PerfectCardInfoVO vo) {
        try{
            Integer accouontid =null;
            if(vo.getAccountId()!=null){
                accouontid =vo.getAccountId();
            }else{
                accouontid = CurrentUser.getUserAccountId();
            }
            vo.setAccountId(accouontid);
            TAccount t =  tAccountService.selectByPrimaryKey(accouontid);
            String code = "";
            String codeKey = "";
            if (t.getNickname().equals(vo.getCardOwner())){
                // 添加本人银行卡
                vo.setSelfCard(true);
                code = ObjectUtils.toString(redisUtil.get("ADDBRCARD" + vo.getPhone()));
                codeKey = "ADDBRCARD" + vo.getPhone();
            }else {
                vo.setSelfCard(false);
                code = ObjectUtils.toString(redisUtil.get("ADDCARD" + vo.getPhone()));
                codeKey = "ADDCARD" + vo.getPhone();
            }
            List<TAccount> findphone = tAccountService.findphone(vo.getPhone());
            List<TBankCard> b = tAccountService.getBankCardListByCardNo(vo.getCardNo(), accouontid);

            if (StringUtils.isEmpty(vo.getPhone())) {
                return ResultUtil.error("请输入手机号码");
            } else if (findphone.size() == 0) {
                return ResultUtil.error("该手机号未注册");
            } else if (StringUtils.isEmpty(vo.getCode())) {
                return ResultUtil.error("请输入验证码");
            } else if (StringUtils.isEmpty(code)) {
                return ResultUtil.error("请重新发送验证码");
            } else if (!vo.getCode().equals(code)) {
                return ResultUtil.error("验证码错误");
            } else if (b == null || b.size() == 0) {

                SysParam sysParam = sysParamAPI.getParamByKey("IFKBIN");
                //是否开启 银行卡三要素校验 0否 1是
                if("1".equals(sysParam.getParamValue())){
                    IntegrationBankCard ibc = new IntegrationBankCard();
                    BankCardReq bankCardReq = new BankCardReq();
                    bankCardReq.setBankCard(vo.getCardNo());
                    bankCardReq.setIdCard(vo.getCardOwnerIdcard());
                    bankCardReq.setName(vo.getCardOwner());
                    BankCardResp bankCardRespa = ibc.checkBankCard(bankCardReq);
                    if ( StringUtils.isBlank(bankCardRespa.getResult())
                            || !BankCardResp.RESULT_UNANIMOUS_CODE.equals(bankCardRespa.getResult())) {
                        return ResultUtil.error(bankCardRespa.getDesc());
                    }
                }

                ResultUtil resultUtil = tEndUserInfoService.addCard(vo);

                TVerificationCodeLog tVerificationCodeLog=new TVerificationCodeLog();
                tVerificationCodeLog.setReceivePhoneno(vo.getPhone());
                tVerificationCodeLog.setVerificationCode(code);
                tVerificationCodeLogService.updateIfUsed(tVerificationCodeLog);
                redisUtil.del(codeKey);

                return resultUtil;
            } else {
                return ResultUtil.error("该银行卡已被添加，请更换新卡");
            }
        }catch (Exception e){
            log.error("银行卡添加失败",e);
            return ResultUtil.error("银行卡添加失败:"+e.getMessage());
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/6 15:23
     *  @Description: 添加银行卡  华夏银行
     */
    @PostMapping("/addHxyhBankCard")
    public ResultUtil addHxyhBankCard(@Validated({AddGroup.class}) @RequestBody TZtBankCardVo vo) {
        Integer accouontId =null;
        if(vo.getAccountId()!=null){
            accouontId =vo.getAccountId();
        }else{
            accouontId = CurrentUser.getUserAccountId();
        }
        String lockKey = "hxAddBank"+accouontId;
        RLock lock = redissonClient.getLock(lockKey);
        boolean tryLock = lock.tryLock();
        try{
            if (tryLock) {
                vo.setAccountId(accouontId);
                TAccount t =  tAccountService.selectByPrimaryKey(accouontId);
                TZtAccountOpenInfo tZtAccountOpenInfo = tZtAccountOpenInfoService.selectByAccountId(accouontId, DictEnum.CD.code);
                String code = "";
                String codeKey = "";
                if (t.getNickname().equals(vo.getAcctName())){
                    // 添加本人银行卡
                    vo.setIsOneself(true);
                    code = ObjectUtils.toString(redisUtil.get("ADDBRCARD" + vo.getRealPhone()));
                    codeKey = "ADDBRCARD" + vo.getRealPhone();
                }else if(tZtAccountOpenInfo.getOpenRealName().equals(vo.getAcctName())){
                    // 添加本人银行卡
                    vo.setIsOneself(false);
                    code = ObjectUtils.toString(redisUtil.get("ADDBRCARD" + vo.getRealPhone()));
                    codeKey = "ADDBRCARD" + vo.getRealPhone();
                }else {
                    return ResultUtil.error("不允许绑定与开户人不一致的银行卡");
                }
                List<TAccount> findphone = tAccountService.findphone(vo.getRealPhone());
                List<TZtBankCard> b = tAccountService.getTZtBankCardListByCardNo(vo.getAcctNo(), accouontId);
                if (StringUtils.isEmpty(vo.getRealPhone())) {
                    return ResultUtil.error("请输入手机号码");
                } /*else if (findphone.size() == 0) {
                return ResultUtil.error("该手机号未注册");
            }*/ else if (StringUtils.isEmpty(vo.getCode())) {
                    return ResultUtil.error("请输入验证码");
                } else if (StringUtils.isEmpty(code)) {
                    return ResultUtil.error("请重新发送验证码");
                } else if (!vo.getCode().equals(code)) {
                    return ResultUtil.error("验证码错误");
                } else if (b == null || b.size() == 0) {

                    SysParam sysParam = sysParamAPI.getParamByKey("IFKBIN");
                    //是否开启 银行卡三要素校验 0否 1是
                    if("1".equals(sysParam.getParamValue())){
                        IntegrationBankCard ibc = new IntegrationBankCard();
                        BankCardReq bankCardReq = new BankCardReq();
                        bankCardReq.setBankCard(vo.getAcctNo());
                        bankCardReq.setIdCard(vo.getAcctCard());
                        bankCardReq.setName(vo.getAcctName());
                        BankCardResp bankCardRespa = ibc.checkBankCard(bankCardReq);
                        if ( StringUtils.isBlank(bankCardRespa.getResult())
                                || !BankCardResp.RESULT_UNANIMOUS_CODE.equals(bankCardRespa.getResult())) {
                            return ResultUtil.error(bankCardRespa.getDesc());
                        }
                    }
                    ResultUtil resultUtil = tEndUserInfoService.addHxyhBankCard(vo);
                    TVerificationCodeLog tVerificationCodeLog=new TVerificationCodeLog();
                    tVerificationCodeLog.setReceivePhoneno(vo.getRealPhone());
                    tVerificationCodeLog.setVerificationCode(code);
                    tVerificationCodeLogService.updateIfUsed(tVerificationCodeLog);
                    redisUtil.del(codeKey);
                    return resultUtil;
                } else {
                    return ResultUtil.error("该银行卡已被添加，请更换新卡");
                }
            }else{
                return ResultUtil.error("银行卡正在添加中");
            }
        }catch (Exception e){
            log.error("银行卡添加失败",e);
            return ResultUtil.error("银行卡添加失败:"+e.getMessage());
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    //绑定本人银行卡
    @RequestMapping("/addOpenRoleCard")
    public ResultUtil addOpenRoleCard(@RequestBody TBankCard tBankCard){

        try{
            String code = ObjectUtils.toString(redisUtil.get("ADDONESECARD" + tBankCard.getPhone()));
            String codeKey = "ADDONESECARD" + tBankCard.getPhone();

            if(null == tBankCard.getAccountId()){
                tBankCard.setAccountId(CurrentUser.getUserAccountId());
            }

            List<TAccount> findphone = tAccountService.findphone(tBankCard.getPhone());
            List<TBankCard> b = tAccountService.getBankCardListByCardNo(tBankCard.getCardNo(), tBankCard.getAccountId());

            if (StringUtils.isEmpty(tBankCard.getPhone())) {
                return ResultUtil.error("请输入手机号码");
            } else if (findphone.size() == 0) {
                return ResultUtil.error("该手机号未注册");
            } else if (StringUtils.isEmpty(tBankCard.getCode())) {
                return ResultUtil.error("请输入验证码");
            } else if (StringUtils.isEmpty(code)) {
                return ResultUtil.error("请重新发送验证码");
            } else if (!tBankCard.getCode().equals(code)) {
                return ResultUtil.error("验证码错误");
            } else if (b == null || b.size() == 0) {
                SysParam sysParam = sysParamAPI.getParamByKey("IFKBIN");
                //是否开启 银行卡三要素校验 0否 1是
                if("1".equals(sysParam.getParamValue())){
                    IntegrationBankCard ibc = new IntegrationBankCard();
                    BankCardReq bankCardReq = new BankCardReq();
                    bankCardReq.setBankCard(tBankCard.getCardNo());
                    bankCardReq.setIdCard(tBankCard.getCardOwnerIdcard());
                    bankCardReq.setName(tBankCard.getCardOwner());
                    BankCardResp bankCardRespa = ibc.checkBankCard(bankCardReq);
                    if ( StringUtils.isBlank(bankCardRespa.getResult())
                            || !BankCardResp.RESULT_UNANIMOUS_CODE.equals(bankCardRespa.getResult())) {
                        return ResultUtil.error(bankCardRespa.getDesc());
                    }
                }
                ResultUtil resultUtil = tEndUserInfoService.addOpenRoleCard(tBankCard);

                TVerificationCodeLog tVerificationCodeLog=new TVerificationCodeLog();
                tVerificationCodeLog.setReceivePhoneno(tBankCard.getPhone());
                tVerificationCodeLog.setVerificationCode(code);
                tVerificationCodeLogService.updateIfUsed(tVerificationCodeLog);
                redisUtil.del(codeKey);

                return resultUtil;
            }else {
                return ResultUtil.error("该银行卡已被添加，请更换新卡");
            }
        }catch (Exception e){
        log.error("京东开户银行卡添加失败!",e);
        return ResultUtil.error("京东开户银行卡添加失败!");
        }
    }

    /**
     * 删除银行卡
     * @auth zhangxin
     *
     * @param id
     * @return REsultUtil
     */
    @PostMapping("/delCard")
    public ResultUtil delCard(@RequestParam(value = "id") Integer id, @RequestParam(value = "code") String code,@RequestParam(value = "phone") String phone) {
        try{
            TBankCard tBankCard = tBankCardMapper.selectByPrimaryKey(id);
            String passCode = ObjectUtils.toString(redisUtil.get("DELCARD" + phone));
            List<TAccount> findphone = tAccountService.findphone(phone);
            if (StringUtils.isEmpty(phone)) {
                return ResultUtil.error("请输入手机号码");
            } else if (findphone.size() == 0) {
                return ResultUtil.error("该手机号未注册");
            } else if (StringUtils.isEmpty(code)) {
                return ResultUtil.error("请输入验证码");
            } else if (StringUtils.isEmpty(passCode)) {
                return ResultUtil.error("请重新发送验证码");
            } else if (!code.equals(passCode)) {
                return ResultUtil.error("验证码错误");
            } else {
                //jdBankCardVos有值就是京东绑过的卡先走京东解绑再删卡
                //deleteStatus为0证明删除的是非京东绑卡的银行卡
                int deleteStatus = 0;

                //查询开户表数据
                Integer endUserId = CurrentUser.getEndUserId();

//                List<TJdBankCardVo> jdBankCardVos = new ArrayList<>();
                List<TJdBankCardVo> jdBankCardVos = tJdBankCardMapper.openRoleUserJdBankList(endUserId);

//                //司机/车队长-》司机开户本卡京东绑卡了-车队长未开户，进行本卡的删除操作那么
//                // 如果jdBankCardVos列表为空去查一下本卡是否是司机进行京东绑卡了，如果是同样也删除
//                if(jdBankCardVos.size() == 0){
//                     Integer accountId = CurrentUser.getUserAccountId();
//
//                     TEnduserAccountExample tEnduserAccountExample = new TEnduserAccountExample();
//                     TEnduserAccountExample.Criteria cr1 = tEnduserAccountExample.createCriteria();
//                     cr1.andAccountIdEqualTo(accountId);
//                     cr1.andEnableEqualTo(false);
//                    List<TEnduserAccount> tEnduserAccountList = tEnduserAccountMapper.selectByExample(tEnduserAccountExample);
//                    for (TEnduserAccount tEnduserAccount : tEnduserAccountList){
//                        if(!tEnduserAccount.getEnduserId().equals(endUserId)){
//                            jdBankCardVos = tJdBankCardMapper.openRoleUserJdBankList(tEnduserAccount.getEnduserId());
//                        }
//                    }
//                }

                for(TJdBankCardVo tJdBankCard : jdBankCardVos){
                    if(tJdBankCard.getBankCardId().equals(tBankCard.getId())){
                        if(!tJdBankCard.getDataEnable()){
                            return ResultUtil.error("当前银行卡正在进行京东绑定处理中，不能删除！");
                        }
                        if(jdBankCardVos.size() == 1){
                            return ResultUtil.error("当前京东银行卡只有一张，不能删除！");
                        }
                        deleteStatus++;
                        //京东银行卡解绑

                        TJdBankCardVo tJdBankCardVo = new TJdBankCardVo();
                        tJdBankCardVo.setOpenRoleId(tJdBankCard.getOpenRoleId());
                        tJdBankCardVo.setPartnerAccId(tJdBankCard.getPartnerAccId());
                        tJdBankCardVo.setCardId(tJdBankCard.getCardId());
                        tJdBankCardVo.setJdBankCardId(tJdBankCard.getJdBankCardId());
                        ResultUtil jdResultUtil = tjdBankCardService.unbindOpenRoleUserBindingBank(tJdBankCardVo);


                        if(null != jdResultUtil && null != jdResultUtil.getCode() && jdResultUtil.getCode().equals("success")){

                            //如果京东解绑是处理中那么删卡操作就放到回调里
                            if(null != jdResultUtil.getData() && String.valueOf(jdResultUtil.getData()).equals("PROCESS")){
                                log.info(tBankCard.getId()+":银行卡解绑处理中!");
                                return new ResultUtil(CodeEnum.SUCCESS.getCode(), "银行卡解绑处理中!");
                            }else {
                                tBankCard.setEnable(true);
                                tBankCardMapper.updateByPrimaryKeySelective(tBankCard);
                                //如果删除的是默认银行卡，就设置一个银行卡为默认
                                if (tBankCard.getIfDefault().equals(1)) {
                                    tBankCardMapper.setSomeOneIsDefault(tBankCard.getAccountId());
                                }
                                //tAccountService.updateIfUsed(phone, passCode);
                                TVerificationCodeLog tVerificationCodeLog=new TVerificationCodeLog();
                                tVerificationCodeLog.setReceivePhoneno(phone);
                                tVerificationCodeLog.setVerificationCode(passCode);
                                tVerificationCodeLogService.updateIfUsed(tVerificationCodeLog);
                            }
                        }else {
                            return jdResultUtil;
                        }
                    }
                }

                if(deleteStatus == 0){
                    tBankCard.setEnable(true);
                    tBankCardMapper.updateByPrimaryKeySelective(tBankCard);
                    //如果删除的是默认银行卡，就设置一个银行卡为默认
                    if (tBankCard.getIfDefault().equals(1)) {
                        tBankCardMapper.setSomeOneIsDefault(tBankCard.getAccountId());
                    }
                    //tAccountService.updateIfUsed(phone, passCode);
                    TVerificationCodeLog tVerificationCodeLog=new TVerificationCodeLog();
                    tVerificationCodeLog.setReceivePhoneno(phone);
                    tVerificationCodeLog.setVerificationCode(passCode);
                    tVerificationCodeLogService.updateIfUsed(tVerificationCodeLog);
                }

            }
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), "银行卡解绑成功");
        }catch (Exception e){
            log.info("银行卡解绑失败",e);
            return ResultUtil.error("dw-053:银行卡解绑失败");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/9 9:38
     *  @Description: 司机端删除华夏银行卡
     */
    @PostMapping("/delHxyhCard")
    public ResultUtil delHxyhCard(@RequestParam(value = "id") Integer id, @RequestParam(value = "code") String code,
                                  @RequestParam(value = "phone") String phone,String accountId) {
        try{
            Integer accountIdInt = null;
            String passCode = ObjectUtils.toString(redisUtil.get("DELCARD" + phone));
            List<TAccount> findphone = tAccountService.findphone(phone);
            if (StringUtils.isEmpty(phone)) {
                return ResultUtil.error("请输入手机号码");
            } else if (findphone.size() == 0) {
                return ResultUtil.error("该手机号未注册");
            } else if (StringUtils.isEmpty(code)) {
                return ResultUtil.error("请输入验证码");
            } else if (StringUtils.isEmpty(passCode)) {
                return ResultUtil.error("请重新发送验证码");
            } else if (!code.equals(passCode)) {
                return ResultUtil.error("验证码错误");
            } else {
                if(null == accountId){
                    accountIdInt = CurrentUser.getUserAccountId();
                }else{
                    accountIdInt = Integer.parseInt(accountId);
                }
                ResultUtil resultUtil = ztBbankUserService.delHxyhCard(id,accountIdInt);
                TVerificationCodeLog tVerificationCodeLog=new TVerificationCodeLog();
                tVerificationCodeLog.setReceivePhoneno(phone);
                tVerificationCodeLog.setVerificationCode(passCode);
                tVerificationCodeLogService.updateIfUsed(tVerificationCodeLog);
                return resultUtil;
            }
        }catch (Exception e){
            log.info("银行卡解绑失败",e);
            return ResultUtil.error("银行卡解绑失败");
        }
    }
    /**  京东已下线
     * 银行卡列表
     * @auth zhangxin
     *
     * @param
     * @return REsultUtil
     */
    @PostMapping("/getBankCardList")
    public ResultUtil getBankCardList() {
        try{
            Integer accountid = CurrentUser.getUserAccountId();
            ResultUtil getData = tEndCarInfoService.getBankCardList(accountid);
            return getData;
        }catch (Exception e){
            log.error("银行卡列表查询失败",e);
            return ResultUtil.error("dw-054:银行卡列表查询失败");
        }
    }
    /**
     *  @author: dingweibo
     *  @Date: 2022/11/6 11:26
     *  @Description: 司机端华夏银行卡列表查询
     */
    @PostMapping("/hxyhGetBankCardList")
    public ResultUtil hxyhGetBankCardList() {
        try{
            Integer accountId = CurrentUser.getUserAccountId();
            ResultUtil getData = ztBbankUserService.hxyhGetBankCardList(accountId);
            return getData;
        }catch (Exception e){
            log.error("华夏银行卡列表查询失败",e);
            return ResultUtil.error("华夏银行卡列表查询失败");
        }
    }


    /**
     * 司机端银行卡详情
     * @auth zhangxin
     * @param
     * @return REsultUtil
     */
    @PostMapping("/getBankCardDetail")
    public ResultUtil getCarDetail(@RequestParam(value = "id") Integer id) {
        ResultUtil getData = tEndUserInfoService.findByCardId(id);
        return getData;
    }

    /**
     * 司机端银行卡详情
     * @auth zhangxin
     * @param
     * @return REsultUtil
     */
    @PostMapping("/getHxyhBankCardDetail")
    public ResultUtil getHxyhBankCardDetail(@RequestParam(value = "id") Integer id) {
        Integer accountId = CurrentUser.getUserAccountId();
        TZtBankCardVo tZtBankCardVo = ztBbankUserService.getHxyhBankCardDetail(id,accountId);
        return ResultUtil.ok(tZtBankCardVo);
    }
    /**
     * 解绑车辆
     * @auth  zhangxin
     * @param carId
     * @return
     * zhangxin
     */
    @PostMapping("/jiebang")
    public ResultUtil jiebang(@RequestParam(value = "carId") Integer carId) {
        Integer enduserinfoid = CurrentUser.getEndUserId();
        List<TEndUserCarRel> tEndUserCarRel = tEndUserCarRelService.selecByCarId(carId, enduserinfoid);
        TEndUserCarRel t = tEndUserCarRel.get(0);
        t.setDataConcelFrom("DRIWX");
        t.setDataConcelTime(new Date());
        tEndUserCarRelService.updateById(t);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), "解绑成功");
    }

    /**
     * 车辆已是否入网
     * @auth zhangxin
     *
     * @return REsultUtil
     */
    @PostMapping("/isRuWang")
    public ResultUtil isRuWang(@RequestParam(value = "vehicleNumber") String vehicleNumber) {
        List<TEndCarInfo> tEndCarInfoList = tEndCarInfoService.selectByCarNum(vehicleNumber);
        ResultUtil resultUtil = trajectoryRecentAPI.checkTruckExistV2(vehicleNumber+"_"+tEndCarInfoList.get(0).getLicensePlateColor());
        if (resultUtil.getMsg().equals("no")) {
            return ResultUtil.error("该车辆未入网");
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), "");
    }

    /**
     * 车辆识别代码
     * @auth zhangxin
     *
     * @return REsultUtil
     */
    @PostMapping("/saveCLSBDM")
    public ResultUtil saveCLSBDM(@RequestParam(value = "vehicleNumber") String vehicleNumber, @RequestParam(value = "vehicleIdentificationCode") String vehicleIdentificationCode) {
        List<TEndCarInfo> list = tEndCarInfoService.selectByCarNum(vehicleNumber);
        TEndCarInfo tEndCarInfo = list.get(0);
        tEndCarInfo.setVehicleClassificationCode(vehicleIdentificationCode+"");
        tEndCarInfoService.update(tEndCarInfo);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), "");
    }

    /**
     * 车辆行驶证信息查询
     * @auth zhangxin
     *
     * @return REsultUtil
     */
    @PostMapping("/vQueryLicense")
    public ResultUtil vQueryLicense(@RequestParam(value = "vehicleNumber") String vehicleNumber) {
        List<TEndCarInfo> list = tEndCarInfoService.selectByCarNum(vehicleNumber);
        TEndCarInfo tEndCarInfo = list.get(0);
        TrajectoryRouterPathReq req = new  TrajectoryRouterPathReq();
        req.setVclN(vehicleNumber);
        req.setVco(tEndCarInfo.getLicensePlateColor());
        VQueryLicenseV2Resp cert =trajectoryRecentAPI.vQueryLicenseV2(req);
        if("1001".equals(cert.getStatus())){
            tEndCarInfo.setBrandModel(cert.getVbrndCdNm() + cert.getPrdCdNm());
            tEndCarInfo.setVehicleIdentificationCode(cert.getVin());
            tEndCarInfo.setApprovedMotorFullQuality(40000d);
          /*  if(cert.getVclTon()!=null&&!"".equals(cert.getVclTon())){
                tEndCarInfo.setFullVehicleQuality(Double.parseDouble(cert.getVclTon()));
            }*/
            if(cert.getLdTn()!=null&&!"".equals(cert.getLdTn())) {
                tEndCarInfo.setApprovedBareQuality(Double.parseDouble(cert.getLdTn()));
            }
            /*if(cert.getVclTon()!=null&&!"".equals(cert.getVclTon())) {
                tEndCarInfo.setApprovedMotorFullQuality(Double.parseDouble(cert.getVclTon()));
            }*/
            tEndCarInfo.setOutlineSize(cert.getVclLng());// 外 长
            //tEndCarInfo.setParam1(cert.getVclWdt());// 外 宽
            tEndCarInfo.setParam2(cert.getVclHgt());// 外 高
            tEndCarInfo.setParam3(cert.getBoxLng());//内 长
            tEndCarInfo.setParam4(cert.getBoxWdt());//内 宽
            tEndCarInfo.setParam5(cert.getBoxHgt());//内 高
            tEndCarInfoService.updateByPrimaryKey(tEndCarInfo);
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), "");
    }

    /**
     * 银行卡3、4要素综合验证
     * @auth zhangxin
     *
     * @return REsultUtil
     */
    @PostMapping("/checkBank")
    public ResultUtil checkBank(@RequestBody BankCardReq bankCardReq) {
        IntegrationBankCard ibc = new IntegrationBankCard();
        BankCardResp bankCardRespa = ibc.checkBankCard(bankCardReq);
        if ( StringUtils.isNotBlank(bankCardRespa.getResult())
                && BankCardResp.RESULT_UNANIMOUS_CODE.equals(bankCardRespa.getResult())) {
            return ResultUtil.ok();
        } else {
            return ResultUtil.error(bankCardRespa.getDesc());
        }
    }

    /**
     * 设置默认银行卡
     * @auth zhangxin
     *
     * @return REsultUtil
     */
    @PostMapping("/setDefault")
    public int setDefault(@RequestParam(value = "id") Integer id) {
        Integer accountid = CurrentUser.getUserAccountId();
        int i = tBankCardMapper.updateByAccountId(accountid);
        TBankCard tBankCard = tBankCardMapper.selectByPrimaryKey(id);
        tBankCard.setIfDefault(1);
        return tBankCardMapper.updateByPrimaryKeySelective(tBankCard);
    }
    /**
     * 设置默认银行卡
     * @auth zhangxin
     *
     * @return REsultUtil
     */
    @PostMapping("/setHxyhDefault")
    public int setHxyhDefault(@RequestParam(value = "id") Integer id) {
        Integer accountId = CurrentUser.getUserAccountId();
        return ztBbankUserService.setHxyhDefault(id,accountId);
    }

    /**
     * 查询默认银行卡
     * @auth zhangxin
     *
     * @return REsultUtil
     */
    @PostMapping("/idDefault")
    public Object idDefault() {
        Integer accountid = CurrentUser.getUserAccountId();
        return tBankCardService.selectIdDefault(accountid);
    }

    /**
     * 根据主键id查询银行卡
     * @auth zhangxin
     *
     * @return REsultUtil
     */
    @PostMapping("/selectById")
    public TBankCard selectById(@RequestParam(value = "id") Integer id) {
        return tEndCarInfoService.selectById(id);
    }

    /**
     * 根据accountid 更新默认银行卡
     * @auth zhangxin
     *
     * @return REsultUtil
     */
    @PostMapping("/updateByAccountId")
    public Integer updateByAccountId() {
        Integer accountid = CurrentUser.getUserAccountId();
        return tEndCarInfoService.updateByAccountId(accountid);
    }

    /**
     * 根据bankid 更新默认银行卡
     * @auth zhangxin
     *
     * @return REsultUtil
     */
    @PostMapping("/setIsDefault")
    public Integer setIsDefault(@RequestParam(value = "id") Integer id) {
        return tEndCarInfoService.setIsDefault(id);
    }

    /**
     * @description 完善银行卡信息详情
     * <AUTHOR>
     * @date 2021/4/9 16:50
     * @param
     * @return
     */
    @PostMapping("/perfectCardInfo")
    public ResultUtil perfectCardInfo(@Validated @RequestBody TransferorInfoQueryVO vo) {
        return tEndUserInfoService.perfectCardInfo(vo);
    }

    /**
    * @description 完善银行卡信息
    * <AUTHOR>
    * @date 2021/4/8 09:17
    * @param
    * @return
    */
    @PostMapping("/perfectCard")
    public ResultUtil perfectCard(@Validated({UpdateGroup.class}) @RequestBody PerfectCardInfoVO vo) {
        String lockKey = "";
        boolean tryLock = false;
        try {
            lockKey = "CARD" + vo.getPhone();
            tryLock = RedissLockUtil.tryLock(lockKey, 60, 45);
            if (tryLock) {
                TBankCard tBankCard = bankCardService.selectById(vo.getCardId());
                if (null == tBankCard || StringUtils.isBlank(tBankCard.getCardNo())) {
                    return ResultUtil.error("银行卡不存在");
                }
                if (!tBankCard.getCardNo().equals(vo.getCardNo())) {
                    return ResultUtil.error("银行卡号不符");
                }
                Integer accountId = null;
                if (null != vo.getAccountId()) {
                    accountId = vo.getAccountId();
                } else {
                    accountId = CurrentUser.getUserAccountId();
                    vo.setAccountId(CurrentUser.getUserAccountId());
                }

                TAccount account =  tAccountService.selectByPrimaryKey(accountId);
                String code = "";
                String codeKey = "";
                if(account.getNickname().equals(vo.getCardOwner())){
                    vo.setSelfCard(true);
                    code = ObjectUtils.toString(redisUtil.get("ADDBRCARD" + vo.getPhone()));
                    codeKey = "ADDBRCARD" + vo.getPhone();
                }else{
                    vo.setSelfCard(false);
                    code = ObjectUtils.toString(redisUtil.get("ADDCARD" + vo.getPhone()));
                    codeKey = "ADDCARD" + vo.getPhone();
                }
                List<TAccount> findphone = tAccountService.findphone(vo.getPhone());
                if (findphone.size() == 0) {
                    return ResultUtil.error("该手机号未注册");
                }
                if (StringUtils.isEmpty(code)) {
                    return ResultUtil.error("请重新发送验证码");
                }
                if (!vo.getCode().equals(code)) {
                    return ResultUtil.error("验证码错误");
                }

                ResultUtil resultUtil = tEndUserInfoService.perfectCard(vo);

                TVerificationCodeLog tVerificationCodeLog=new TVerificationCodeLog();
                tVerificationCodeLog.setReceivePhoneno(vo.getPhone());
                tVerificationCodeLog.setVerificationCode(code);
                tVerificationCodeLogService.updateIfUsed(tVerificationCodeLog);
                redisUtil.del(codeKey);

                return resultUtil;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("操作失败, {}", e);
            return ResultUtil.error("操作失败");
        } finally {
            if (StringUtils.isNotBlank(lockKey) && tryLock) {
                RedissLockUtil.unlock(lockKey);
            }
        }

        return ResultUtil.ok();
    }

}

