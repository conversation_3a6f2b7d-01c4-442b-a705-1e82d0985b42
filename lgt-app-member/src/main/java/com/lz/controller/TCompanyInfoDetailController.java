package com.lz.controller;

import com.lz.model.TCompanyInfoDetail;
import com.lz.service.TCompanyInfoDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/companyInfoDetail")
public class TCompanyInfoDetailController {

    @Autowired
    private TCompanyInfoDetailService companyInfoDetailService;

    @GetMapping("/getCompanyInfoDetail")
    public TCompanyInfoDetail selectByCompanyId(@RequestParam("companyId") Integer companyId) {
        return companyInfoDetailService.selectByCompanyId(companyId);
    }

    @PostMapping("/updateByPrimaryKeySelective")
    public int updateByPrimaryKeySelective(@RequestBody TCompanyInfoDetail companyInfoDetail) {
        return companyInfoDetailService.updateByPrimaryKeySelective(companyInfoDetail);
    }

}
