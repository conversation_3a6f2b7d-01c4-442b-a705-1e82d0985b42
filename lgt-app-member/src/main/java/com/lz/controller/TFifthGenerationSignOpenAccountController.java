package com.lz.controller;


import cn.agile.anysign.sdk.api.param.ReqSealParam;
import com.lz.common.util.ResultUtil;
import com.lz.model.TCarrierInfo;
import com.lz.model.TEndUserInfo;
import com.lz.model.TFifthGenerationSignOpenAccount;
import com.lz.model.ht5Gq.resp.ContractCreatUrlResp;
import com.lz.service.TFifthGenerationSignOpenAccountService;
import com.lz.vo.TCarrierInfoVo;
import com.lz.vo.TEndUserInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/tFifthGenerationSignOpenAccount")
public class TFifthGenerationSignOpenAccountController {

    @Autowired
    private TFifthGenerationSignOpenAccountService tFifthGenerationSignOpenAccountService;


    /**
     *  @author: dingweibo
     *  @Date: 2022/4/11 9:31
     *  @Description: 承运方注册电子印章
     */
    @PostMapping(value = "/selectByUserIdAndType")
    @ResponseBody
    public TFifthGenerationSignOpenAccount selectByUserIdAndType(@RequestParam(value = "userId") Integer userId,
                                                                 @RequestParam(value = "userType") String userType){
        return tFifthGenerationSignOpenAccountService.selectByUserIdAndType(userId,userType);
    }
    /**
     *  @author: dingweibo
     *  @Date: 2022/4/11 9:31
     *  @Description: 承运方注册电子印章
     */
    @PostMapping(value = "/registerSign")
    @ResponseBody
    public ResultUtil registerSign(@RequestBody TCarrierInfo record){
        try{
            return tFifthGenerationSignOpenAccountService.registerSign(record);
        }catch (Exception e){
            log.error("承运方注册电子印章失败！",e);
            return ResultUtil.error("承运方注册电子印章失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/4/11 9:31
     *  @Description: 承运方修改电子印章资料同时去注册
     */
    @PostMapping(value = "/updateRegisterSign")
    @ResponseBody
    public ResultUtil updateRegisterSign(@RequestBody TCarrierInfo record){
        try{
            return tFifthGenerationSignOpenAccountService.updateRegisterSign(record);
        }catch (Exception e){
            log.error("承运方补充资料手动提交失败！",e);
            return ResultUtil.error("承运方补充资料手动提交失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/4/11 9:31
     *  @Description: 承运方添加印章
     */
    @PostMapping(value = "/createSeal")
    @ResponseBody
    public ResultUtil createSeal(@RequestBody TCarrierInfoVo record){
        try{
            return tFifthGenerationSignOpenAccountService.createSeal(record);
        }catch (Exception e){
            log.error("承运方添加印章失败！",e);
            return ResultUtil.error("承运方添加印章失败！");
        }
    }
    /**
     *  @author: dingweibo
     *  @Date: 2022/4/12 10:32
     *  @Description: 司机注册电子印章
     */
    @PostMapping(value = "/diverRegisterSign")
    @ResponseBody
    public ResultUtil diverRegisterSign(@RequestBody TEndUserInfo record){
        try{
            return tFifthGenerationSignOpenAccountService.diverRegisterSign(record);
        }catch (Exception e){
            log.error("司机注册电子印章失败！",e);
            return ResultUtil.error("司机方注册电子印章失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/4/12 10:39
     *  @Description: 司机修改电子印章资料同时去注册
     */
    @PostMapping(value = "/updateDiverRegisterSign")
    @ResponseBody
    public ResultUtil updateDiverRegisterSign(@RequestBody TEndUserInfo record){
        try{
            return tFifthGenerationSignOpenAccountService.updateDiverRegisterSign(record);
        }catch (Exception e){
            log.error("司机补充资料手动提交失败！",e);
            return ResultUtil.error("司机补充资料手动提交失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/4/11 9:31
     *  @Description: 承运方添加印章
     */
    @PostMapping(value = "/diverCreateSeal")
    @ResponseBody
    public ResultUtil diverCreateSeal(@RequestBody TEndUserInfoVO record){
        try{
            return tFifthGenerationSignOpenAccountService.diverCreateSeal(record);
        }catch (Exception e){
            log.error("司机添加印章失败！",e);
            return ResultUtil.error("司机添加印章失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/4/11 9:31
     *  @Description: 创建手写签名
     */
    @PostMapping(value = "/createUrl")
    @ResponseBody
    public ContractCreatUrlResp createUrl(@RequestBody ReqSealParam record){
        try{
            return tFifthGenerationSignOpenAccountService.createUrl(record);
        }catch (Exception e){
            log.error("创建手写签名失败！",e);
            return null;
        }
    }
}
