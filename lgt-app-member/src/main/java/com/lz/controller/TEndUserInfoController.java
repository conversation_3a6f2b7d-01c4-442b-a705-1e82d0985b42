package com.lz.controller;

import com.alibaba.fastjson.JSON;
import com.lz.aop.log.Log;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.model.BankCardReq;
import com.lz.common.model.BankCardResp;
import com.lz.common.model.datareport.hbdr.driverreport.DriverReportDTO;
import com.lz.common.util.*;
import com.lz.dto.JudgeEndUserDTO;
import com.lz.dto.TEndUserDTO;
import com.lz.dto.TEndUserInfoDto;
import com.lz.model.*;
import com.lz.service.TEndUserAuditInfoService;
import com.lz.service.TEndUserInfoService;
import com.lz.service.TEnduserStatusService;
import com.lz.service.TNetSignOpenAccountService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @date 2019-04-10
*/
@Slf4j
@RestController
@RequestMapping("api")
public class TEndUserInfoController {

    @Autowired
    private TEndUserInfoService tEndUserInfoService;


    @Autowired
    private TEnduserStatusService enduserStatusService;

    @Autowired
    private SysParamAPI sysParamAPI;

    @Autowired
    private TEndUserAuditInfoService endUserAuditInfoService;

    @Resource
    private TNetSignOpenAccountService netSignOpenAccountService;

    private static final String ENTITY_NAME = "tEndUserInfo";

    /**
     * @Description: Feign接口：根据手机号判断，用户是否已申请合同电子签名
     * @Author: Yan
     * @Date: 2019/8/13/013 11:22
     * @Param: phone
     * @Return: boolean
     */
    @PostMapping("/judgeUserIfOnlineSign")
    public JudgeEndUserDTO judgeUserIfOnlineSign(@RequestParam(value = "phone") String phone,@RequestParam(value = "endUserId") Integer endUserId) {
        return tEndUserInfoService.judgeEndUserIfOnlineSign(phone,endUserId);
    }

    /**
     * @Description: 修改C端用户是否已申请合同电子签名
     * @Author: Yan
     * @Date: 2019/8/13/013 15:17
     * @Param: endUserInfo
     * @Return:
     */
    @PostMapping("/updateEndUserIfOnlineSign")
    public ResultUtil updateEndUserIfOnlineSign(@RequestBody TEndUserInfo endUserInfo) {
        return  tEndUserInfoService.updateEndUserIfOnlineSign(endUserInfo);
    }


    /**
     * 运单打包： 获取用户所有银行卡
     * Yan
     * @param param
     * @return
     */
    @PostMapping("/getUserBankCard")
    public ResultUtil getUserCard(@RequestBody TEndUserInfoSearchVO param) {
        return tEndUserInfoService.getUserBankCards(param.getEnduserId());
    }

    /**
     * 判断C端用户是否有银行卡
     * true没有   false有
     * Yan
     * @param id
     * @return
     */
    @PostMapping("/judgeUserCard")
    public ResultUtil judgeUserCard(@RequestParam("id") Integer id) {
        return tEndUserInfoService.judgeUserCard(id);
    }

    /*
     * @Description 司机列表
     * @param page
     * @param size
     * @param order
     * @param username
     * @param phone
     * @param carNumber 车牌号
     * @param status  状态1：审核中；2：审核通过；3：审核不通过
     * @return com.lz.common.util.ResultUtil
     * <AUTHOR>
     * @Date 2019/4/17 14:07
     **/
    @PostMapping(value = "/tEndUserInfo/selectByPage")
//    @PreAuthorize("hasAnyRole('ADMIN')")
    public ResultUtil selectByPage(@RequestBody TEndUserInfoSearchVO param){
        try {
            if(param!=null&&param.getTime()!=null){
                param.setStartTime(param.getTime()[0]);
                param.setEndTime(param.getTime()[1]);
            }
            if(param!=null&&param.getUpdateTimeArray()!=null){
                param.setUpdateStartTime(param.getUpdateTimeArray()[0]);
                param.setUpdateEndTime(param.getUpdateTimeArray()[1]);
            }
            ResultUtil resultUtil = tEndUserInfoService.selectByPage(param);
            return resultUtil;
        } catch (Exception e) {
            log.error("获取司机列表失败！",e);
            return ResultUtil.error("dw-016:获取司机列表失败！");
        }
    }

    @PostMapping(value = "/tEndUserInfo/selectById")
//    @PreAuthorize("hasAnyRole('ADMIN')")
    public ResultUtil getTEndUserInfo(@RequestParam(value = "id") Integer id){
        try {
            return ResultUtil.ok(tEndUserInfoService.findById(id));
        }catch (Exception e){
            log.error("获取司机失败",e);
            return ResultUtil.error("dw-017:获取司机失败！");
        }

    }

    @Log("修改TEndUserInfo")
    @PostMapping(value = "/tEndUserInfo")
//    @PreAuthorize("hasAnyRole('ADMIN')")
    public ResultUtil update(@RequestBody EndUserInfoUpdate resources){
        try {
            if (null == resources.getId()) {
                return ResultUtil.error("请选择一条数据");
            }
            if (null != resources.getUpdateOrAudit() && resources.getUpdateOrAudit() == 1) {
                if (null == resources.getIdcardStatus() || StringUtils.isBlank(resources.getIdcardStatus())) {
                    return ResultUtil.error("身份证审核状态改不可为空");
                }
                TEndUserInfo tEndUserInfo = tEndUserInfoService.selectByPrimaryKey(resources.getId());
                if (tEndUserInfo.getUserLogisticsRole().contains(DictEnum.CTYPEDRVIVER.code)) {
                    if (null == resources.getDrivingLicencesStatus() || StringUtils.isBlank(resources.getDrivingLicencesStatus())) {
                        return ResultUtil.error("驶驶行审核状态不可叫空");
                    }
                    if (null == resources.getCertificateStatus() || StringUtils.isBlank(resources.getCertificateStatus())) {
                        return ResultUtil.error("从业资格证审核状态不可为空");
                    }
                }
            }
            return tEndUserInfoService.update(resources);
        }catch (Exception e){
            log.error("修改司机失败, {}", ThrowableUtil.getStackTrace(e));
            return ResultUtil.error("dw-018:修改司机失败！");
        }

    }

    @Log("删除TEndUserInfo")
    @PostMapping(value = "/tEndUserInfo/deleteEndUserInfo")
//    @PreAuthorize("hasAnyRole('ADMIN')")
    public ResultUtil delete(@RequestBody TEndUserInfoDto record){
        try {
            return tEndUserInfoService.deleteByUserID(record);
        }catch (Exception e){
            log.error("删除失败",e);
            return ResultUtil.error("dw-019:删除失败！");
        }
    }

    @Log("审批TEndUserInfo")
    @PostMapping(value = "/tEndUserInfo/approval")
//    @PreAuthorize("hasAnyRole('ADMIN')")
    public ResultUtil approval(@RequestBody ApprovalVO param){
        try {
            tEndUserInfoService.approval(param);
            return ResultUtil.ok();
        } catch (Exception e) {
            log.error("审批司机失败",e);
            return ResultUtil.error("dw-020:审批司机失败！");
        }
    }
    /**
     * 上报监管平台
     * 上报司机信息
     */
    @PostMapping("/driver/uploaded")
    public ResultUtil uploadedSupervisionPlatform(@RequestBody DriverReportDTO dto){
        try{
            return tEndUserInfoService.driverBasicInformation(dto);
        }catch (Exception e){
            log.error("上报监管平台",e);
            if (StringUtils.checkChineseCharacter(e.getMessage())){
                return ResultUtil.error(e.getMessage());
            }
            return ResultUtil.error("上报监管平台失败!");
        }
    }
    /**
     * 终端司机用户
     * @auth zhangjiji
     * @return
     */
    @PostMapping(value = "/tEndUserInfo/selectEndDriverUser")
    public ResultUtil selectEndDriverUser(@RequestBody TEndUserInfoSearchVO record) {
        record.setUserLogisticsRole("CTYPEDRVIVER");
        ResultUtil tEndUserInfos = tEndUserInfoService.selectEndUser(record);
        return tEndUserInfos;
    }

    /**
     * 终端业务部 edit by zhangjiji 20200208
     * @auth zhangjiji
     * @return
     */
    @PostMapping(value = "/tEndUserInfo/selectEndManagerUser")
    public ResultUtil selectEndManagerUser(@RequestBody TEndUserInfoSearchVO record) {
        ResultUtil tEndUserInfos = tEndUserInfoService.selectMamage(record);
        return tEndUserInfos;
    }

    /**
     * 终端经纪人
     * @auth zhangjiji
     * @return
     */
    @PostMapping(value = "/tEndUserInfo/selectAgentUser")
    public ResultUtil selectAgentUser(@RequestBody TEndUserInfoSearchVO record) {
        ResultUtil tEndUserInfos = tEndUserInfoService.selectAgent(record);
        return tEndUserInfos;
    }


    /**
     * author dingweibo
     * C端车老板列表
     */
    @PostMapping(value = "/tEndUserInfo/selectEndCarOwnerByPage")
    public ResultUtil selectEndCarOwnerByPage(@RequestBody TEndUserInfoVO tEndUserInfoVO){
        try{
            return tEndUserInfoService.selectEndCarOwnerByPage(tEndUserInfoVO);
        }catch (Exception e){
            log.error("车主列表查询失败！",e);
            return ResultUtil.error("dw-021:车主列表查询失败！");
        }
    }


    /**
     * 根据id 查询信息
     * @param id
     *  @auth dingweibo
     */
    @PostMapping(value = "/tEndUserInfo/selectEndCarOwnerById")
    public ResultUtil selectEndCarOwnerById(@RequestParam Integer id){
        try{
            return new ResultUtil("",tEndUserInfoService.selectEndCarOwnerById(id));
        }catch (Exception e){
            log.error("车主编辑回显失败！",e);
            return ResultUtil.error("dw-022:车主编辑回显失败！");
        }
    }

    /**
     * 车老板信息修改
     * @auth dingweibo
     * @param tEndUserInfoVO
     * @return
     */
    @PostMapping(value = "/tEndUserInfo/updateEndCarOwner")
    public ResultUtil updateEndCarOwner(@RequestBody TEndUserInfoVO tEndUserInfoVO){
        try{
            return tEndUserInfoService.updateEndCarOwner(tEndUserInfoVO);
        }catch (Exception e){
            log.error("车主修改失败！",e);
            return  ResultUtil.error("dw-023:车主修改失败！");
        }
    }

    /**
     * 车老板信息新增
     * @auth dingweibo
     * @param tEndUserInfoVO
     * @return
     */
    @PostMapping(value = "/tEndUserInfo/addEndCarOwner")
    public ResultUtil saveEndCarOwner(@RequestBody TEndUserInfoVO tEndUserInfoVO){
        try{
            tEndUserInfoService.addEndCarOwner(tEndUserInfoVO);
            return ResultUtil.ok();
        }catch (Exception e){
            log.error("车主新增失败！",e);
            return  ResultUtil.error("dw-024:车主新增失败！");
        }
    }
    /**
     * 车老板银行卡新增
     * @auth dingwiebo
     * @param tBankCardVo
     * @return
     */
    @PostMapping(value = "/tEndUserInfo/addEndCarOwnerBank")
    public ResultUtil saveEndCarOwnerBank(@RequestBody TBankCardVo tBankCardVo){
        try{
            return tEndUserInfoService.addEndCarOwnerBank(tBankCardVo);
        }catch (Exception e){
            log.error("车主银行卡新增失败！",e);
            return  ResultUtil.error("dw-025:车主银行卡新增失败！");
        }
    }



    /**
     *  @author: sanbin
     *  @Date: 2019/6/28 14:31
     *  @Description: 分页查询
     */
    @PostMapping(value = "/tEndUserInfo/selectDriverByPage")
    public ResultUtil selectDriverByPage(@RequestBody DriverListPageVO param){
        try {
            ResultUtil resultUtil = tEndUserInfoService.selectDriverByPage(param);
            return resultUtil;
        } catch (Exception e) {
            log.error("获取终端钱包列表失败！",e);
            return ResultUtil.error("dw-026:获取终端钱包列表失败！");
        }
    }


    /**
    * @Description 终端钱包详情
    * <AUTHOR>
    * @Date   2019/7/20 16:38
    * @Param
    * @Return
    * @Exception
    *
    */
    @PostMapping(value = "/tEndUserInfo/selectDriverInfo")
    public ResultUtil selectDriverInfo(@RequestParam("enduserId")Integer enduserId){
        try {
            return ResultUtil.ok(tEndUserInfoService.selectDriverInfo(enduserId));
        } catch (Exception e) {
            log.error("获取终端用户钱包失败！",e);
            return ResultUtil.error("dw-027:获取终端用户钱包失败！");
        }
    }


    /**
     *  @author: zhangxin
     *  @Date: 2019/6/28 14:31
     *  @Description: 根据主键查用户信息
     */
    @PostMapping(value = "/tEndUserInfo/selectByPrimaryKey")
//    @PreAuthorize("hasAnyRole('ADMIN')")
    public TEndUserInfo selectByPrimaryKey(@RequestParam(value = "id") Integer id){
        return tEndUserInfoService.selectByPrimaryKey(id);

    }

    /**
     * 查询司机货源
     * @auth zhangjiji
     * @param record
     * @return
     */
    @PostMapping("/selectEnduserForScanSource")
    public ResultUtil selectEnduserForScanSource(@RequestBody TEndUserInfoSearchVO record){
        ResultUtil resultUtil = tEndUserInfoService.selectEnduserForScanSource(record);
        return resultUtil;
    }

    /**
     * @Description 司机扫码查询司机状态
     * <AUTHOR>
     * @Date   2019/6/13 18:00
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/selectEnduserStatusForScan")
    public ResultUtil selectEnduserStatusForScan(@RequestBody String code){
        String userLogisticsRole = CurrentUser.getUserLogisticsRole();
        if (StringUtils.isNotEmpty(CurrentUser.getUserLogisticsRole())){
            if (userLogisticsRole.equals(DictEnum.CTYPEBOSS.code)){
                return ResultUtil.error("车老板不能抢单");
            } else if (userLogisticsRole.equals(DictEnum.CTYPEDRVIVER.code)){
                TEndUserInfo endUserInfo = new TEndUserInfo();
                Integer endUserId = CurrentUser.getEndUserId();
                endUserInfo.setId(endUserId);
                ResultUtil resultUtil = tEndUserInfoService.selectEnduserStatusForScan(endUserInfo);
                return resultUtil;
            }  else {
                return ResultUtil.error("当前登录人所属角色不能抢单");

            }
        } else {
            if (null != CurrentUser.getEndUserId()) {
                TEndUserInfo endUserInfo = tEndUserInfoService.selectById(CurrentUser.getEndUserId());
                if (null == endUserInfo) {
                    return ResultUtil.error("未获取到当前登录人的角色");
                }
                if (null == endUserInfo.getUserLogisticsRole() || StringUtils.isBlank(endUserInfo.getUserLogisticsRole())) {
                    // 未选择认证角色
                    return ResultUtil.error("请先选择认证角色");
                }
            }
            return ResultUtil.error("未获取到当前登录人的角色");
        }
    }

    /**
     * 收单查询司机状态
     * @auth zhangjiji
     * @param record
     * @return
     */
    @PostMapping("/selectEnduserStatus")
    public ResultUtil selectEnduserStatus(@RequestBody TEndUserStatus record){
        ResultUtil resultUtil = enduserStatusService.selectEnduserStatus(record);
        return resultUtil;
    }

    /**
     *  @author: zhangjiji
     *  @Date: 2019/6/28 14:32
     *  @Description: 查询车辆用户状态
     */
    @PostMapping("/selectEnduserCarStatus")
    public ResultUtil selectEnduserCarStatus(@RequestBody TEnduserCarStatus record){
        try {
            ResultUtil resultUtil = tEndUserInfoService.selectEnduserCarStatus(record);
            return resultUtil;
        } catch (Exception e){
           log.error("ZJJ-051:查询车辆司机信息失败!" ,e);
           return ResultUtil.error("ZJJ-051:查询车辆司机信息失败!");
        }

    }


    /**
     *  @author: dingweibo
     *  @Date: 2019/6/24 13:44
     *  @Description: 查询车老板列表
     */
    @PostMapping("/findCarOwnerList")
    public ResultUtil findCarOwnerList(){
        return tEndUserInfoService.findCarOwnerList();
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/1/5 10:50
     *  @Description: 查询车队长列表
     */
    @PostMapping("/findCaptainList")
    public ResultUtil findCaptainList(){
        return tEndUserInfoService.findCaptainList();
    }

    /**
     * @Description 车主身份回显
     * <AUTHOR>
     * @Date   2019/7/9 14:16
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/selectCarOwnerShip")
    public ResultUtil selectCarOwnerShip(@RequestBody TEndUserInfoVO vo){
        try{
            return tEndUserInfoService.selectCarOwnerShip(vo);
        }catch (Exception e){
            log.error("添加车主身份失败！",e);
            return ResultUtil.error("添加车主身份失败！");
        }
    }


    /**
     * @Description 司机身份回显
     * <AUTHOR>
     * @Date   2019/7/9 14:16
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/selectDriverIdentity")
    public ResultUtil selectDriverIdentity(@RequestBody TEndUserInfoVO vo){
        try{
            return tEndUserInfoService.selectDriverIdentity(vo);
        }catch (Exception e){
            log.error("添加司机身份失败！",e);
            return ResultUtil.error("添加司机身份失败！");
        }
    }

    /**
    * @Description 添加车主身份
    * <AUTHOR>
    * @Date   2019/7/9 14:16
    * @Param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/saveCarOwnerShip")
    public ResultUtil saveCarOwnerShip(@RequestBody TEndUserInfoVO vo){
        try{
            return tEndUserInfoService.saveCarOwnerShip(vo);
        }catch (Exception e){
            log.error("车主身份保存失败！",e);
            return ResultUtil.error("车主身份保存失败！");
        }
    }


    /**
    * @Description 添加司机身份
    * <AUTHOR>
    * @Date   2019/7/9 14:16
    * @Param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/saveDriverIdentity")
    public ResultUtil saveDriverIdentity(@RequestBody TEndUserInfoVO vo){
        try{
            return tEndUserInfoService.saveDriverIdentity(vo);
        }catch (Exception e){
            log.error("司机身份保存失败！",e);
            return ResultUtil.error("司机身份保存失败！");
        }
    }

    /**
    * @Description 根据承运方id和enduserid查询钱包id
    * <AUTHOR>
    * @Date   2019/7/10 17:28
    * @Param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/selectByWalltId")
    public ResultUtil selectByWalltId(@RequestParam(value = "endUserId") Integer endUserId,@RequestParam(value = "carrierId") Integer carrierId, @RequestParam("purseCategory") String purseCategory){
        return tEndUserInfoService.selectByWalltId(endUserId,carrierId, purseCategory);
    }


    /**
    * @Description 根据enduserId获取到默认银行卡
    * <AUTHOR>
    * @Date   2019/7/11 10:36
    * @Param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/selectByEndUserIdAndBankInfo")
    public ResultUtil selectByEndUserIdAndBankInfo(@RequestParam(value = "endUserId") Integer endUserId){

        return tEndUserInfoService.selectByEndUserIdAndBankInfo(endUserId);
    }


    /**
    * @Description 根据司机idList查询司机集合
    * <AUTHOR>
    * @Date   2019/8/10 9:03
    * @Param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/selectByEndUserIdListFeign")
    public List<TEndUserInfo> selectByEndUserIdListFeign(@RequestParam String endUserIdList){

        return tEndUserInfoService.selectByEndUserIdListFeign(endUserIdList);
    }


    /**
    * @Description 车主所有权查询
    * <AUTHOR>
    * @Date   2019/10/15 10:16
    * @param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/carOwnerAudit")
    public ResultUtil carOwnerAudit(@RequestBody TEndUserCarRelVo record){
        try {
            ResultUtil resultUtil = tEndUserInfoService.carOwnerAudit(record);
            return resultUtil;
        } catch (Exception e){
            log.error("车主所有权查询失败", e);
            return ResultUtil.error("车主所有权查询失败（错误编码：M0001）");
        }

    }


    /**
    * @Description 修改系统用户，修改C端信息
    * <AUTHOR>
    * @Date   2019/8/18 12:16
    * @param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/updateEnduserInfo")
    public ResultUtil updateEnduserInfo(@RequestBody TEndUserInfoVO record){
        try {
            tEndUserInfoService.updateEnduserInfo(record);
            return ResultUtil.ok();
        } catch (Exception e){
            log.error("修改C端信息失败", e);
            return ResultUtil.error();
        }
    }

    /**
     * 司机管理excel导出
     * @param param
     * <AUTHOR>
     * @return
     */
    @PostMapping("/tEndUserInfo/export")
    @ResponseBody
    public ResultUtil export(@RequestBody TEndUserInfoSearchVO param){
        try {

            if(param!=null&&param.getTime()!=null){
                param.setStartTime(param.getTime()[0]);
                param.setEndTime(param.getTime()[1]);
            }
            return  tEndUserInfoService.export(param);
        } catch (Exception e) {
            log.error(e.getMessage());
            return ResultUtil.error("dw-039:导出失败！");
        }
    }

    /*
     * <AUTHOR>
     * @Description 查询车辆司机审核状态
     * @Date 2019/11/14 16:12
     * @Param
     * @return
    **/
    @PostMapping("/tEndUserInfo/selectUserAndCarAuditStatus")
    public ResultUtil selectUserAndCarAuditStatus(@RequestBody TEndUserInfoSearchVO record) {
        ResultUtil resultUtil = tEndUserInfoService.selectUserAndCarAuditStatus(record);
        return resultUtil;
    }

    /*
     * <AUTHOR>
     * @Description 运单检查查询经纪人
     * @Date 2020/4/15 14:10
     * @Param
     * @return
    **/
    @PostMapping("/tEndUserInfo/orderExamineSelectAgent")
    public ResultUtil orderExamineSelectAgent(@RequestBody TAgentSearchVo record) {
        return tEndUserInfoService.orderExamineSelectAgent(record);
    }

    /*
     * <AUTHOR>
     * @Description 修改司机上链信息
     * @Date 2020/5/14 11:17
     * @Param
     * @return
    **/
    @PostMapping("/updateEnduserUploadedInfo")
    public int updateEnduserUploadedInfo(@RequestBody TEndUserInfo record) {
        return tEndUserInfoService.updateEnduserUploadedInfo(record);
    }

    /**
     * 司机添加车队长身份资料回显
     * @param vo
     * @return
     */
    @PostMapping("/tEndUserInfo/Captain")
    public ResultUtil selectDriverIfCaptain(@RequestBody TEndUserInfo vo){
        try{
            ResultUtil resultUtil = tEndUserInfoService.selectDriverIfCaptain(vo);
            return resultUtil;
        }catch (Exception e){
            log.error("添加车队长身份失败！",e);
            return ResultUtil.error("添加车队长身份失败！");
        }
    }

    /**
     * 司机添加住址信息
     * @param vo
     * @return
     */
    @PostMapping("/tEndUserInfo/Address")
    public ResultUtil addAddress(@RequestBody TEndUserInfo vo){
        try{
            ResultUtil resultUtil = tEndUserInfoService.addAddress(vo);
            return resultUtil;
        }catch (Exception e){
            log.error("补充身份证信息失败！",e);
            return ResultUtil.error("补充身份证信息失败！");
        }
    }

    /**
     * 自然人用户
     * excel批量添加住址信息
     * @return
     */
    @PostMapping("/tEndUserInfo/ExcelBatchAddAddress")
    @ResponseBody
    public ResultUtil ExcelBatchAddAddress(@RequestBody List<TOcrOpenRole> excelBatchAddAddress){
        List exceList = new ArrayList();
        try{
            for(TOcrOpenRole data : excelBatchAddAddress){
                TOcrOpenRole tOcrOpenRole = new TOcrOpenRole();
                String errorInfo ="";
                Boolean ifSuccess = true;
                if(null != data.getIdcard() && null != data.getIdentityRole()){
                    List<TEndUserInfo> tEndUserInfoList = tEndUserInfoService.selectIdcardUserLogisticsRole(data.getIdcard(),data.getIdentityRole());
                    if(tEndUserInfoList.size() > 1 || tEndUserInfoList.size() == 0){
                        errorInfo = errorInfo + data.getIdcard() + data.getIdentityRole() + tEndUserInfoList.size() +"查出多条或者零条数据!";
                    }else {
                        ResultUtil resultUtil = tEndUserInfoService.excelBatchAddAddress(tEndUserInfoList.get(0));

                        errorInfo = errorInfo + data.getIdcard() + data.getIdentityRole() + resultUtil.getMsg();
                        ifSuccess = false;
                        if(null != tEndUserInfoList.get(0).getId()){
                            tOcrOpenRole.setEndUserId(tEndUserInfoList.get(0).getId());
                        }
                    }
                    exceList.add(errorInfo);
                }else {
                    if(null != data.getName()){
                        errorInfo = errorInfo + data.getName();
                    }
                    if(null != data.getPhone()){
                        errorInfo = errorInfo + data.getPhone();
                    }
                    if(null != data.getIdcard()){
                        errorInfo = errorInfo + data.getIdcard();
                    }
                    errorInfo = errorInfo + "信息错误请检查信息!";
                    exceList.add(errorInfo);
                }
                if(null != data.getIdcard()){
                    tOcrOpenRole.setIdcard(data.getIdcard());
                }
                if(null != data.getIdentityRole()){
                    tOcrOpenRole.setIdentityRole(data.getIdentityRole());
                }
                if(null != data.getName()){
                    tOcrOpenRole.setName(data.getName());
                }
                if(null != data.getPhone()){
                    tOcrOpenRole.setPhone(data.getPhone());
                }
                tOcrOpenRole.setProcessingType("ocr");
                tOcrOpenRole.setProcessingData(errorInfo);
                tOcrOpenRole.setIfSuccess(ifSuccess);
                tOcrOpenRole.setCreateTime(new Date());
                tOcrOpenRole.setUpdateTime(new Date());
                tEndUserInfoService.insertOcrOpenRoleInfo(tOcrOpenRole);

                Thread.sleep(5000);
            }
        }catch (Exception e){
            log.error("excel批量补充身份证信息失败！",e);
        }
        log.info("excel批量ocr最终返回数据:"+ JSON.toJSONString(exceList));
        return ResultUtil.ok();
    }

    /**
     * 司机添加车队长身份
     * @param tEndUserInfo
     * @return
     */
    @PostMapping(value ="/tEndUserInfo/insertCaptain")
    public ResultUtil insertCaptain(@RequestBody TEndUserInfo tEndUserInfo){
        try {
            ResultUtil resultUtil = tEndUserInfoService.insertCaptain(tEndUserInfo);
            return  resultUtil;
        }catch (Exception e){
            log.error("司机添加车队长身份失败!",e);
            return ResultUtil.error("司机添加车队长身份失败!");
        }
    }

    /**
     * 车队长添加司机身份回显
     * @param vo
     * @return
     */
    @PostMapping("/tEndUserInfo/selectCaptainIdentity")
    public ResultUtil selectCaptainIfDriver(@RequestBody TEndUserInfoVO vo){
        try{
            ResultUtil resultUtil = tEndUserInfoService.selectCaptainIfDriver(vo);
            return resultUtil;
        }catch (Exception e){
            log.error("添加司机身份失败！",e);
            return ResultUtil.error("添加司机身份失败！");
        }
    }

    /**
     * 车队长添加司机身份
     * @param vo
     * @return
     */
    @PostMapping(value ="/tEndUserInfo/insertDirver")
    public ResultUtil insertDirver(@RequestBody TEndUserInfoVO vo){
        try {
            ResultUtil resultUtil = tEndUserInfoService.insertDirver(vo);
            return  resultUtil;
        }catch (Exception e){
            log.error("车队长添加司机身份失败!",e);
            return ResultUtil.error("车队长添加司机身份失败!");
        }
    }

    /**
     * 车队长页面列表
     * @param tEndUserInfoVO
     * @return
     */
    @PostMapping(value ="/tEndUserInfo/selectCaptainList")
    public ResultUtil selectCaptainPage(@RequestBody TEndUserInfoVO tEndUserInfoVO){
        try {
            if(tEndUserInfoVO!=null&&tEndUserInfoVO.getUpdateTimeArray()!=null){
                tEndUserInfoVO.setUpdateStartTime(tEndUserInfoVO.getUpdateTimeArray()[0]);
                tEndUserInfoVO.setUpdateEndTime(tEndUserInfoVO.getUpdateTimeArray()[1]);
            }
            ResultUtil resultUtil = tEndUserInfoService.selectCaptainPage(tEndUserInfoVO);
            return resultUtil;
        }catch (Exception e){
            log.error("车队长页面查询失败!",e);
            return ResultUtil.error("车队长页面查询失败!");
        }
    }

    /**
     * 车队长详情回显
     * @param id
     * @return
     */
    @PostMapping(value = "/tEndUserInfo/selectCaptainById")
    public ResultUtil selectCaptainById(@RequestParam(value = "id") Integer id){
        try {
            return new ResultUtil("",tEndUserInfoService.selectCaptainOwnerById(id));
        }catch (Exception e){
            log.error("获取司机失败",e);
            return ResultUtil.error("dw-017:获取司机失败！");
        }

    }

    /**
     * 车队长修改
     * @param vo
     * @return
     */
    @PostMapping(value ="/tEndUserInfo/updateCaptainPage")
    public ResultUtil updateCaptainPage(@RequestBody EndUserInfoUpdate vo){
        try {
            ResultUtil resultUtil = tEndUserInfoService.updateCaptainPage(vo);
            return resultUtil;
        }catch (Exception e){
            log.error("修改车队长信息失败!, {}", ThrowableUtil.getStackTrace(e));
            return ResultUtil.error("修改车队长信息失败!");
        }
    }

    /**
     * 删除车队长
     * @param vo
     * @return
     */
    @PostMapping(value ="/tEndUserInfo/deleteCaptain")
    public ResultUtil deleteCaptain(@RequestBody TEndUserInfoDto vo){
        try {
            return tEndUserInfoService.deleteCaptain(vo);
        }catch (Exception e){
            log.error("删除失败",e);
            return ResultUtil.error("dw-019:删除失败！");
        }
    }

    /**
     * 查询车队长信息列表
     * @param parameter
     * @return
     */
    @PostMapping(value ="/tEndUserInfo/selectCaptainNameList")
    public ResultUtil selectCaptainNameList(@RequestParam("parameter")String parameter){
        try {
            return tEndUserInfoService.selectCaptainNameList(parameter);
        }catch (Exception e){
            log.error("车队长信息查询失败",e);
            return ResultUtil.error("dw-019:车队长信息查询失败！");
        }
    }


    /**
     *  @author: dingweibo
     *  @Date: 2020/12/11 10:31
     *  @Description: postman 处理司机与车队长数据
     */
    @PostMapping("/updateEndUserLogisticsRole")
    public int updateEndUserLogisticsRole(@RequestBody TEndUserInfo record) {
        return tEndUserInfoService.updateEndUserLogisticsRole(record);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2020/7/29 11:36
     *  @Description: 车队长列表查询
     */
    @PostMapping("/tEndUserInfo/selectByCaptainListPage")
    public ResultUtil selectByCaptainListPage(@RequestBody TEndUserInfoVO vo){
        try{
            return tEndUserInfoService.selectByCaptainListPage(vo);
        }catch (Exception e){
            log.error("车队长列表查询失败",e);
            return ResultUtil.error("车队长列表查询失败");
        }
    }

    @PostMapping("/tEndUserInfo/selectByCaptainList")
    public ResultUtil selectByCaptainList(@RequestBody TEndUserInfoVO vo){
        try{
            return tEndUserInfoService.selectByCaptainList(vo);
        }catch (Exception e){
            log.error("车队长列表查询失败",e);
            return ResultUtil.error("车队长列表查询失败");
        }
    }

    /**
     * 根据id 查询信息
     * @param id
     *  @auth dingweibo
     */
    @PostMapping(value = "/tEndUserInfo/selectEndCaptainById")
    public ResultUtil selectEndCaptainById(@RequestParam Integer id){
        try{
            return ResultUtil.ok(tEndUserInfoService.selectEndCaptainById(id));
        }catch (Exception e){
            log.error("车队长编辑回显失败！",e);
            return ResultUtil.error("dw-022:车队长编辑回显失败！");
        }
    }
    /**
     *  @author: dingweibo
     *  @Date: 2021/2/25 8:54
     *  @Description: 申请司机子账号工具
     */
    @PostMapping("/insterDriverZzh")
    public int insterDriverZzh(@RequestBody TEndUserInfoSqZzhVO record) {
        return tEndUserInfoService.insterDriverZzh(record);
    }

    /**
    * @description 转账人列表查询
    * <AUTHOR>
    * @date 2021/4/8 21:09
    * @param
    * @return
    */
    @PostMapping("/selectTransferorPage")
    public ResultUtil selectTransferorPage(@RequestBody TEndUserInfoSearchVO vo) {
        return tEndUserInfoService.selectTransferorPage(vo);
    }

    /**
     * @description 转账人信息详情
     * <AUTHOR>
     * @date 2021/4/8 21:09
     * @param
     * @return
     */
    @PostMapping("/transferorInfo")
    public ResultUtil transferorInfo(@RequestParam("enduserId") Integer enduserId) {
        return tEndUserInfoService.selectTransferorInfo(enduserId);
    }

    /**
     * @description 转账人信息修改
     * <AUTHOR>
     * @date 2021/4/8 21:09
     * @param
     * @return
     */
    @PostMapping("/updateTransferorInfo")
    public ResultUtil updateTransferorInfo(@Validated @RequestBody TransferorInfoVO vo) {
        SysParam sysParam = sysParamAPI.getParamByKey("IFKBIN");
        //是否开启 银行卡三要素校验 0否 1是
        if("1".equals(sysParam.getParamValue())){
            IntegrationBankCard ibc = new IntegrationBankCard();
            BankCardReq bankCardReq = new BankCardReq();
            bankCardReq.setBankCard(vo.getCardNo());
            bankCardReq.setIdCard(vo.getIdcard());
            bankCardReq.setName(vo.getRealName());
            BankCardResp bankCardRespa = ibc.checkBankCard(bankCardReq);
            if ( StringUtils.isBlank(bankCardRespa.getResult())
                    || !BankCardResp.RESULT_UNANIMOUS_CODE.equals(bankCardRespa.getResult())) {
                return ResultUtil.error(bankCardRespa.getDesc());
            }
        }
        return tEndUserInfoService.updateTransferorInfo(vo);
    }

    //司机/车队长/经纪人开户 - 身份信息更新
    @PostMapping("/updateUserOpenRoleInfo")
    public ResultUtil updateUserOpenRoleInfo(@RequestBody TEndUserInfo tEndUserInfo){
        try {
            Integer enuserid = CurrentUser.getEndUserId();
            tEndUserInfo.setId(enuserid);
            return tEndUserInfoService.updateUserOpenRoleInfo(tEndUserInfo);
        }catch (Exception e){
            log.error("更新用户信息失败！",e);
            return  ResultUtil.error("更新用户信息失败!");
        }
    }

    //司机开户 - 用户信息是否完整和过期
    @PostMapping("/userInfoIfComplete")
    public ResultUtil userInfoIfComplete(){
        try{
            Integer enuserid = CurrentUser.getEndUserId();
            return tEndUserInfoService.userInfoIfComplete(enuserid);
        }catch (Exception e){
            log.error("检验用户信息接口查询失败！",e);
            return  ResultUtil.error("检验用户信息接口查询失败!");
        }
    }

    @PostMapping("/transferorIdentityChange")
    public ResultUtil transferorIdentityChange(@RequestParam(value = "id") Integer id,@RequestParam(value = "userLogisticsRole") String userLogisticsRole){
        return tEndUserInfoService.transferorIdentityChange(id,userLogisticsRole);
    }

    /**
    * @description 查询持卡人角色
    * <AUTHOR>
    * @date 2021/11/23 13:35
    */
    @PostMapping("/queryCardOwnerUserLogisticsRole")
    public String queryCardOwnerUserLogisticsRole(@RequestParam("cardOwnerPhone") String cardOwnerPhone, @RequestParam("cardOwnerIdcacrd") String cardOwnerIdcacrd) {
        return tEndUserInfoService.queryCardOwnerUserLogisticsRole(cardOwnerPhone, cardOwnerIdcacrd);
    }

    /**
    * @description 查询C端开户状态
    * <AUTHOR>
    * @date 2021/12/13 11:13
    */
    @PostMapping("/queryEnduserOpenRoleStatus")
    public TEndUserOpenRole queryEnduserOpenRoleStatus(@RequestParam("enduserId") Integer enduserId) {
        return tEndUserInfoService.queryEnduserOpenRoleStatus(enduserId);
    }

    /**
    * @description 查询持卡人是否本人开户
    * <AUTHOR>
    * @date 2021/12/9 11:48
    */
    @PostMapping("/queryCardOwnerWhetherSelfOpenRole")
    public String queryCardOwnerWhetherSelfOpenRole(@RequestParam("bankCardId") Integer bankCardId) {
        return tEndUserInfoService.queryCardOwnerWhetherSelfOpenRole(bankCardId);
    }

    /**
     * @description 查询司机名下的所有车辆
     * <AUTHOR>
     * @date 2023/04/11 11:13
     */
    @PostMapping("/queryEnduserAllCar")
    public List<TEndCarInfo>  queryEnduserAllCar(@RequestParam("enduserId") Integer enduserId) {
       return tEndUserInfoService.queryEnduserAllCar(enduserId);
    }
    /**
     * 判断手机号是否存在t_end_user_info表中
     */
    @PostMapping("/judgeEndUserByPhone")
    public List<String> judgeEndUserByPhone(@RequestParam("phone") String phone) {
        return tEndUserInfoService.judgeEndUserByPhone(phone);
    }

    /**
     * 获取当前登录用户信息
     * @return
     */
    @GetMapping("/getEndUserInfo")
    public ResultUtil getEndUserInfo() {
        try {
            if (null == CurrentUser.getEndUserId()) {
                return ResultUtil.ok();
            }
            TEndUserInfo tEndUserInfo = tEndUserInfoService.selectByPrimaryKey(CurrentUser.getEndUserId());
            TEndUserDTO tEndUserDTO = new TEndUserDTO();
            BeanCopyUtil.copyPropertiesIgnoreNull(tEndUserInfo, tEndUserDTO);
            if (null == tEndUserInfo) {
                return ResultUtil.ok();
            }
            TEndUserAuditInfo endUserAuditInfo = endUserAuditInfoService.selectByEndUserId(tEndUserInfo.getId());
            if(null == endUserAuditInfo){
                tEndUserDTO.setEndUserDateType("old");
                tEndUserDTO.setIdcardStatus(tEndUserInfo.getAuditStatus());
            }else{
                tEndUserDTO.setEndUserDateType("new");
                //身份证
                tEndUserDTO.setIdcardStatus(endUserAuditInfo.getIdcardStatus());
                tEndUserDTO.setIdcardOpinion(endUserAuditInfo.getIdcardOpinion());
                if (DictEnum.NOTPASSNODE.code.equals(endUserAuditInfo.getIdcardStatus())) {
                    tEndUserDTO.setIdcardStatusStr("审核不通过");
                }
                if (DictEnum.PAPERNEEDUPDATE.code.equals(endUserAuditInfo.getIdcardStatus())) {
                    tEndUserDTO.setIdcardStatusStr("资料需更新");
                }
                if (DictEnum.PASSNODE.code.equals(endUserAuditInfo.getIdcardStatus())) {
                    tEndUserDTO.setIdcardStatusStr("审核通过");
                    // 判断身份证是否过期
                    Date validUntil = tEndUserInfo.getIdcardValidUntil();
                    if (tEndUserInfo.getIdcardValidBeginning().getTime() > tEndUserInfo.getIdcardValidUntil().getTime()) {
                        validUntil = tEndUserInfo.getIdcardValidBeginning();
                    }
                    Integer checkValidDate = DateUtils.checkValidDate(validUntil);
                    if (checkValidDate == 0) {
                        tEndUserDTO.setIdcardOpinion("身份证已过期");
                    } else if (checkValidDate == 2) {
                        tEndUserDTO.setIdcardOpinion("身份证即将过期");
                    }
                }
                if (DictEnum.MIDNODE.code.equals(endUserAuditInfo.getIdcardStatus())) {
                    tEndUserDTO.setIdcardStatusStr("审核中");
                }

                //从业资格证
                tEndUserDTO.setCertificateStatus(endUserAuditInfo.getCertificateStatus());
                tEndUserDTO.setCertificateOpinion(endUserAuditInfo.getCertificateOpinion());
                if (DictEnum.NOTPASSNODE.code.equals(endUserAuditInfo.getCertificateStatus())) {
                    tEndUserDTO.setCertificateStatusStr("审核不通过");
                }
                if (DictEnum.PAPERNEEDUPDATE.code.equals(endUserAuditInfo.getCertificateStatus())) {
                    tEndUserDTO.setCertificateStatusStr("资料需更新");
                }
                if (DictEnum.PASSNODE.code.equals(endUserAuditInfo.getCertificateStatus())) {
                    tEndUserDTO.setCertificateStatusStr("审核通过");
                    // 判断从业资格证是否过期
                    Integer checkValidDate = DateUtils.checkValidDate(tEndUserInfo.getCertificateValidUntil());
                    if (checkValidDate == 0) {
                        tEndUserDTO.setCertificateOpinion("从业资格证已过期");
                    } else if (checkValidDate == 2) {
                        tEndUserDTO.setCertificateOpinion("从业资格证即将过期");
                    }
                }
                if (DictEnum.MIDNODE.code.equals(endUserAuditInfo.getCertificateStatus())) {
                    tEndUserDTO.setCertificateStatusStr("审核中");
                }

                //驾驶证
                tEndUserDTO.setDrivingLicencesStatus(endUserAuditInfo.getDrivingLicencesStatus());
                tEndUserDTO.setDrivingLicencesOpinion(endUserAuditInfo.getDrivingLicencesOpinion());
                if (DictEnum.NOTPASSNODE.code.equals(endUserAuditInfo.getDrivingLicencesStatus())) {
                    tEndUserDTO.setDrivingLicencesStatusStr("审核不通过");
                }
                if (DictEnum.PAPERNEEDUPDATE.code.equals(endUserAuditInfo.getDrivingLicencesStatus())) {
                    tEndUserDTO.setDrivingLicencesStatusStr("资料需更新");
                }
                if (DictEnum.PASSNODE.code.equals(endUserAuditInfo.getDrivingLicencesStatus())) {
                    tEndUserDTO.setDrivingLicencesStatusStr("审核通过");
                    // 判断从业资格证是否过期
                    Integer checkValidDate = DateUtils.checkValidDate(tEndUserInfo.getDrivingLicencesValidUntil());
                    if (checkValidDate == 0) {
                        tEndUserDTO.setDrivingLicencesOpinion("驾驶证已过期");
                    } else if (checkValidDate == 2) {
                        tEndUserDTO.setDrivingLicencesOpinion("驾驶证即将过期");
                    }
                }
                if (DictEnum.MIDNODE.code.equals(endUserAuditInfo.getDrivingLicencesStatus())) {
                    tEndUserDTO.setDrivingLicencesStatusStr("审核中");
                }
            }

            if((null == tEndUserInfo.getIdcardPhoto1() || "".equals(tEndUserInfo.getIdcardPhoto1())) &&
                    (null == tEndUserInfo.getIdcardPhoto2() || "".equals(tEndUserInfo.getIdcardPhoto2()))){
                tEndUserDTO.setIdcardIfNull(true);
            }else{
                tEndUserDTO.setIdcardIfNull(false);
            }

            if((null == tEndUserInfo.getDrivingLicencesPhoto1() || "".equals(tEndUserInfo.getDrivingLicencesPhoto1())) &&
                    (null == tEndUserInfo.getDrivingLicencesPhoto2() || "".equals(tEndUserInfo.getDrivingLicencesPhoto2()))){
                tEndUserDTO.setDrivingLicencesIfNull(true);
            }else{
                tEndUserDTO.setDrivingLicencesIfNull(false);
            }

            if((null == tEndUserInfo.getCertificatePhoto1() || "".equals(tEndUserInfo.getCertificatePhoto1())) &&
                    (null == tEndUserInfo.getCertificatePhoto2() || "".equals(tEndUserInfo.getCertificatePhoto2()))){
                tEndUserDTO.setCertificateIfNull(true);
            }else{
                tEndUserDTO.setCertificateIfNull(false);
            }

            if (DictEnum.NOTPASSNODE.code.equals(tEndUserInfo.getAuditStatus())) {
                tEndUserDTO.setAuditStatusStr("审核不通过");
            }
            if (DictEnum.PAPERNEEDUPDATE.code.equals(tEndUserInfo.getAuditStatus())) {
                tEndUserDTO.setAuditStatusStr("资料需更新");
            }
            if (DictEnum.PASSNODE.code.equals(tEndUserInfo.getAuditStatus())) {
                tEndUserDTO.setAuditStatusStr("审核通过");
            }
            if (DictEnum.MIDNODE.code.equals(tEndUserInfo.getAuditStatus())) {
                tEndUserDTO.setAuditStatusStr("审核中");
            }

            TNetSignOpenAccount tNetSignOpenAccount = netSignOpenAccountService.selectByUserIdAndUserType(tEndUserInfo.getId(), DictEnum.CD.code);
            if (null != tNetSignOpenAccount) {
                if (tNetSignOpenAccount.getAuthStatus() == 0) {
                    // 验证码未验证
                    tEndUserDTO.setEndDriverSignStatus("3");
                    if (null != tNetSignOpenAccount.getSerialNo()) {
                        tEndUserDTO.setEndDriverSignSerialNo(tNetSignOpenAccount.getSerialNo());
                    }
                } else if (tNetSignOpenAccount.getAuthStatus() == 1 && (null == tNetSignOpenAccount.getSealNo() || StringUtils.isBlank(tNetSignOpenAccount.getSealNo()))) {
                    // 实名认证，但未注册
                    tEndUserDTO.setEndDriverSignStatus("4");
                    tEndUserDTO.setEndDriverSignSerialNo(tNetSignOpenAccount.getSerialNo());
                } else if (null != tNetSignOpenAccount.getSealImage() && StringUtils.isNotBlank(tNetSignOpenAccount.getSealImage())) {
                    // 已注册，已上传印章
                    tEndUserDTO.setEndDriverSignStatus("0");
                    tEndUserDTO.setSealImage(tNetSignOpenAccount.getSealImage());
                } else {
                    // 已注册，未上传印章
                    tEndUserDTO.setEndDriverSignStatus("2");
                }
            } else {
                // 未注册
                tEndUserDTO.setEndDriverSignStatus("1");
            }
            return ResultUtil.ok(tEndUserDTO);
        } catch (Exception e) {
            log.error("获取当前登录用户信息失败",e);
            return ResultUtil.ok();
        }
    }

    @PostMapping("/driverIdentityVerificationStatus")
    public TEndUserInfoVO driverIdentityVerificationStatus(@RequestParam("phone") String phone) {
        return tEndUserInfoService.driverIdentityVerificationStatus(phone);
    }

    @PostMapping("/updateDriverPhone")
    public ResultUtil updateDriverPhone(@RequestParam("phone") String phone , @RequestParam("newphone") String newphone) {
        return tEndUserInfoService.updateDriverPhone(phone,newphone);
    }

    /*
    按用户ID查询用户证件的子状态
     */
    @PostMapping("/selectByEndUserId")
    public TEndUserAuditInfo selectByEndUserId(@RequestParam("endUserId") Integer endUserId) {
        return endUserAuditInfoService.selectByEndUserId(endUserId);
    }

    /**
     * 按用户名手机号身份证号查询用户ID
     */
    @PostMapping("/getDataByPhoneAndIdcard")
    public TEndUserInfo getDataByPhoneAndIdcard(@RequestParam("phone") String phone,
                                               @RequestParam("idcard") String idcard,
                                               @RequestParam("realName") String realName) {
        return tEndUserInfoService.getDataByPhoneAndIdcard(phone, idcard,realName);
    }

}