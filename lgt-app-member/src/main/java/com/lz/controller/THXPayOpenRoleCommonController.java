package com.lz.controller;

import com.lz.common.util.ResultUtil;
import com.lz.dto.OpenRoleWalletListDTO;
import com.lz.model.TZtAccountOpenInfo;
import com.lz.service.HXPayOpenRoleService;
import com.lz.service.JDPayOpenRoleService;
import com.lz.vo.SelectOpenRoleInfo;
import com.lz.vo.SelectOpenRoleVO;
import com.lz.vo.SelectOrderPackOpenRoleVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 *  @author: dingweibo
 *  @Date: 2022/11/23 14:58
 *  @Description: 华夏支付 通用查询类
 */
@Slf4j
@RestController
@RequestMapping("/hx/pay/openrole")
public class THXPayOpenRoleCommonController {

    @Autowired
    private HXPayOpenRoleService openRoleService;

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/23 14:58
     *  @Description: 查询开户信息
     */
    @PostMapping("/info")
    ResultUtil selectEnduserOpenRoleInfo(@RequestBody SelectOpenRoleInfo vo) {
        return openRoleService.selectEnduserOpenRoleInfo(vo);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/23 14:58
     *  @Description: 查询开户状态
     */
    @PostMapping("/status")
    public ResultUtil selectCarrierCompanyEnduserOpenRoleStatus(@RequestBody SelectOpenRoleVO vo) {
        return openRoleService.selectOpenRoleStatus(vo);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/23 14:58
     *  @Description: 查询钱包
     */
    @PostMapping("/wallet")
    public ResultUtil selectCarrierCompanyEnduserOpenRoleWallet(@RequestBody SelectOpenRoleVO vo) {
        return openRoleService.selectOpenRoleWallet(vo);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/23 14:59
     *  @Description: 运单打包，查询开户状态
     */
    @PostMapping("/pack/status")
    public ResultUtil selectOrderPackCarrierCompanyEnduserOpenRoleStatus(@RequestBody SelectOrderPackOpenRoleVO vo) {
        return openRoleService.selectOrderPackCarrierCompanyEnduserOpenRoleStatus(vo);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/23 14:59
     *  @Description: 运单打包，查询用户钱包
     */
    @PostMapping("/pack/wallet")
    public ResultUtil<OpenRoleWalletListDTO> selectOrderPackCarrierCompanyEnduserOPenRoleWallet(@RequestBody SelectOrderPackOpenRoleVO vo) {
        return openRoleService.selectOrderPackCarrierCompanyEnduserOPenRoleWallet(vo);
    }

    @PostMapping("/selectByAccountId")
    public TZtAccountOpenInfo selectByAccountId(@RequestParam("accountId") Integer accountId){
        return openRoleService.selectByAccountId(accountId);
    }
}
