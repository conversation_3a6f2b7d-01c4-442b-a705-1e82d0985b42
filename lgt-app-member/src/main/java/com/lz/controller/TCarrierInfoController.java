package com.lz.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lz.api.FastdfsAPI;
import com.lz.api.OssAPI;
import com.lz.common.model.CodeEnum;
import com.lz.common.util.ResultUtil;
import com.lz.dto.CarrieOpenInfoDetailsDTO;
import com.lz.dto.CarrierInfoDTO;
import com.lz.dto.CarrierOpenInfoDTO;
import com.lz.dto.TCarrierInfoDTO;
import com.lz.model.TCarrierInfo;
import com.lz.model.TCompanyInfo;
import com.lz.service.TCarrierInfoService;
import com.lz.sms.model.SmsReq;
import com.lz.sms.model.SmsResp;
import com.lz.sms.service.SmsClientService;
import com.lz.tpu.api.PaymentAPI;
import com.lz.vo.CarrieOpenInfoDetailsVO;
import com.lz.vo.CarrierOpenInfoListVO;
import com.lz.vo.TCarrierInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * auth dingweibo
 * date 2019-04-10
 * 承运方
 */
@Controller
@RequestMapping("/carrier")
@Slf4j
public class TCarrierInfoController {

    @Autowired
    private TCarrierInfoService tCarrierInfoService;

    @Autowired
    private FastdfsAPI fastdfsAPI;
    @Autowired
    private OssAPI ossAPI;

    @Autowired
    private PaymentAPI paymentAPI;
    @Autowired
    private SmsClientService smsClientService;

    @GetMapping(value = "/test")
    @ResponseBody
    public SmsResp test(HttpServletRequest request, HttpServletResponse response){
        /*UserRequest ur = new UserRequest();
        ur.setKeyStoreName("testKeyStore");//承运方表中第三方接口处理地址
        ur.setPartner_id("************");//业务平台签约编号
        ur.setUid("");//承运方与企业关联表Uid
        ur.setMember_name("aa");//会员名称。用户昵称(平台个人会员登录名)
        ur.setReal_name("张三");//真实姓名
        ur.setCertificate_type("IDCARD");//证件类型（见附录）。目前只支持身份证。 IDCARD
        ur.setCertificate_no("210213198602165207");//作为会员实名认证通过后的证件号*/

       /* PaymentRequest paymentRequest= new PaymentRequest();
        paymentRequest.setUid("11263768716768665602019050914427");//承运方与企业关联表Uid
        paymentRequest.setKeyStoreName("testKeyStore");//承运方表中第三方接口处理地址
        paymentRequest.setPartner_id("************");//业务平台签约编号
        paymentRequest.setNotify_url("http://124.239.144.66:2019/notfiy/payNotfiy");
        paymentRequest.setThirdPartyInterfaceConsumerCode("MYBANK00076");
        paymentRequest.setOuter_trade_no(IdWorkerUtil.getInstance().nextId()); //业务编号
        paymentRequest.setBuyer_id("1231ewrwdfe");//买家在业务平台的 ID（UID）。
        paymentRequest.setSubject("aa");//商品的标题
        paymentRequest.setPrice("2");//商品单价
        paymentRequest.setQuantity("2");//商品的数量
        paymentRequest.setTotal_amount("4");//交易金额=（商品单价×商品数量）。卖家实际扣款
        paymentRequest.setSeller_id("02196aurss2e");//卖家在业务平台的用户 ID（UID）
        paymentRequest.setUidDd("");//收取调度费的会员id
        paymentRequest.setDispatchingFee(new BigDecimal("0"));//调度费*/


       // ResultUtil resultUtil = paymentAPI.instantPay(paymentRequest);
        SmsResp resp = new SmsResp();
        try {
            SmsReq req = new SmsReq();
            req.setMobiles("***********");
            req.setType("TIXIAN");
            req.setAccountId("222222");
            req.setOrderBusinessCode("L20195585221122111");
            req.setUserName("王果儿");
            resp =  smsClientService.sendSmsType(req);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resp;
    }
    @PostMapping(value = "/selectByPage")
    @ResponseBody
    public ResultUtil selectByPage(@RequestBody TCarrierInfo record){
        try{

            return tCarrierInfoService.findList(record);
        }catch (Exception e){
            log.error("dw-001:承运方列表查询失败！",e);
            return ResultUtil.error("dw-001:承运方列表查询失败！");
        }
    }
    /**
     * 查询承运方与B端、C的关系：
     * <AUTHOR>
     * @param
     * @return
     */
    //@Log("获取承运方")
    @PostMapping(value = "/selectById")
    @ResponseBody
    public ResultUtil getById(@RequestParam("id") String id){

        return new ResultUtil(CodeEnum.SUCCESS.getCode(),tCarrierInfoService.getById(id));
    }

    @PostMapping(value = "/selectCarrierInfoByid")
    @ResponseBody
    public TCarrierInfoVo selectCarrierInfoByid(@RequestParam("id") String id){
        return tCarrierInfoService.getById(id);
    }

    @PostMapping(value = "/uploadCarrierStatusById")
    @ResponseBody
    public ResultUtil uploadCarrierStatusById(@RequestParam("id") Integer id, @RequestParam("carrierName") String carrierName){
        return tCarrierInfoService.uploadCarrierStatusById(id,carrierName);
    }


    /**
     *获取承运方开户列表信息
     * @return
     */
    @PostMapping(value = "/selectCarrieOpenInfoList")
    @ResponseBody
    public ResultUtil selectCarrieOpenInfoList(@RequestBody CarrierOpenInfoListVO carrierOpenInfoListVO){
        Page<Object> objectPage = PageHelper.startPage(carrierOpenInfoListVO.getPage(), carrierOpenInfoListVO.getSize());
        List<CarrierOpenInfoDTO> carrierOpenInfoDTOS = tCarrierInfoService.selectCarrieOpenInfoList(carrierOpenInfoListVO);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),objectPage,objectPage.getTotal());
    }

    /**
     *获取承运方开户详情
     * @return
     */
    @PostMapping(value = "/applyCarrieOpenInfo")
    @ResponseBody
    public ResultUtil applyCarrieOpenInfo(@RequestBody CarrieOpenInfoDetailsVO carrieOpenInfoDetailsVO){
        CarrieOpenInfoDetailsDTO applyCarrieOpenInfo = tCarrierInfoService.applyCarrieOpenInfo(carrieOpenInfoDetailsVO);
        return ResultUtil.ok(applyCarrieOpenInfo);
    }
    /**
     * 查询承运方与B端、C的关系：
     * <AUTHOR>
     * @param
     * @return
     */
    @PostMapping(value = "/selectCarrierById")
    @ResponseBody
    public TCarrierInfo selectCarrierById(@RequestParam("id") String id){

        return tCarrierInfoService.selectCarrierById(id);
    }

   // @Log("新增承运方")
    @PostMapping(value = "/save")
    @ResponseBody
    public ResultUtil save(@RequestBody TCarrierInfoVo record){
        try{
            return tCarrierInfoService.save(record);
        }catch(Exception e){
            log.error("dw-002:新增承运方失败！",e);
            return ResultUtil.error("dw-002:新增承运方失败！");
        }
    }

    //@Log("修改承运方")
    @PostMapping(value = "/update")
    @ResponseBody
    public ResultUtil update(@RequestBody TCarrierInfoVo record){
        try{
            return tCarrierInfoService.update(record);
        }catch (Exception e){
            log.error("dw-003:修改承运方失败！",e);
            return ResultUtil.error("dw-003:修改承运方失败！");
        }

    }

    //@Log("删除承运方")
    @PostMapping(value = "/delete")
    @ResponseBody
    public ResultUtil delete(@RequestBody String[] id){
        try {
            ResultUtil resultUtil = tCarrierInfoService.delete(id);
            return resultUtil;
        }catch (Exception e){
            log.error("dw-004:删除承运方失败！",e);
            return ResultUtil.error("dw-004:删除承运方失败！");
        }
    }


    /**
     * 图片上传
     * auth dingweibo
     * @param
     * @return
     */
    @PostMapping(value = "/upload",produces = {MediaType.APPLICATION_JSON_UTF8_VALUE},
            consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    public ResultUtil upload(MultipartFile file, HttpServletRequest request){
        try {
            //ResultUtil resultUtil = fastdfsAPI.uploadFileSample(file);
            //Map<Object,Object> map  = (Map<Object, Object>) resultUtil.getData();
            ResultUtil resultUtil =ossAPI.uploadFileSample(file);
            UserDetails userDetails = (UserDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            TCarrierInfo record = new TCarrierInfo();
            record.setBusinessLicensePhoto(resultUtil.getData().toString());
            return resultUtil;
        } catch (Exception e) {
            log.error("图片上传失败",e);
            return ResultUtil.error();
        }
    }

    /**
     * 承运方select
     * <AUTHOR>
     * @return
     */
    @PostMapping("/api/selectCarrier")
    @ResponseBody
    public ResultUtil selectCarrier() {
        List<CarrierInfoDTO> carrierInfoDTOS = tCarrierInfoService.selectCarrier();
        return ResultUtil.ok(carrierInfoDTOS);
    }


    /**
     * 根据承运方id查询企业select
     * <AUTHOR>
     * @return
     */
    @PostMapping("/api/selectByCarrierId")
    @ResponseBody
    public ResultUtil selectByCarrierId(@RequestParam(value = "carrierId") Integer carrierId) {
        List<TCompanyInfo> list = tCarrierInfoService.selectByCarrierId(carrierId);
        return ResultUtil.ok(list);
    }
    /**
     * 承运方合作的企业
     * <AUTHOR>
     * @return
     */
    @PostMapping(value = "/cooperationCompany")
    @ResponseBody
    public ResultUtil cooperationCompany(@RequestBody TCarrierInfoVo vo) {
        ResultUtil resultUtil = tCarrierInfoService.cooperationCompany(vo);
        return resultUtil;
    }


    /**
    * @Description 承运方钱包
    * <AUTHOR>
    * @Date   2019/9/29 9:58
    * @Param
    * @Return
    * @Exception
    *
    */
    @PostMapping(value = "/selectByCarrierWalle")
    @ResponseBody
    public ResultUtil selectByCarrierWalle(@RequestBody TCarrierInfoVo vo) {
        try {
            ResultUtil resultUtil = tCarrierInfoService.selectByCarrierWalle(vo);
            return resultUtil;
        }catch (Exception e){
            log.error("dw:查询承运方钱包失败！",e);
            return ResultUtil.error("dw:查询承运方钱包失败！");
        }
    }


    /** 承运方对公提现
     *  @author: dingweibo
     *  @Date: 2019/10/8 15:26
     *  @Description:
     */
    @PostMapping(value = "/payToCardPublic")
    @ResponseBody
    public ResultUtil payToCardPublic(@RequestBody TCarrierInfoVo vo) {
        try {
            ResultUtil resultUtil = tCarrierInfoService.payToCardPublic(vo);
            return resultUtil;
        }catch (Exception e){
            log.error("dw:查询承运方钱包失败！",e);
            return ResultUtil.error("dw:查询承运方钱包失败！");
        }
    }


    /**
    * @Description 查询承运方信息，包括uid
    * <AUTHOR>
    * @Date   2019/10/9 16:26
    * @param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/selectCarrierInfo")
    @ResponseBody
    public TCarrierInfoDTO selectCarrierInfo(@RequestParam Integer carrierId) {
        TCarrierInfoDTO tCarrierInfoDTO = tCarrierInfoService.selectCarrierInfo(carrierId);
        return tCarrierInfoDTO;
    }

    @PostMapping(value = "/getByCarrierList")
    @ResponseBody
    public ResultUtil getByCarrierList(@RequestBody TCarrierInfo record){
        try{
            return tCarrierInfoService.newFindList(record);
        }catch (Exception e){
            log.error("dw-001:承运方列表查询失败！",e);
            return ResultUtil.error("dw-001:承运方列表查询失败！");
        }
    }

    @PostMapping("/carrier/getDataByCarrierName")
    public TCarrierInfo getDataByCarrierName(@RequestParam("carrierName") String carrierName,
                                             @RequestParam("businessLicenseNo") String businessLicenseNo){
        return tCarrierInfoService.getDataByCarrierName(carrierName,  businessLicenseNo);
    }
}
