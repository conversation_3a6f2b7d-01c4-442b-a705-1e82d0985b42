package com.lz.controller;

import com.lz.common.model.jdPayment.request.bank.QueryVerifyAmtReq;
import com.lz.common.util.ResultUtil;
import com.lz.model.TJdWalletChangeLog;
import com.lz.service.TJdWalletService;
import com.lz.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 *  @author: dingweibo
 *  @Date: 2021/8/9 9:15
 *  @Description: 京东数科钱包
 */
@Slf4j
@RestController
@RequestMapping("/tJdWallet")
public class TJdWalletController {

    @Autowired
    private TJdWalletService tJdWalletService;

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/9 9:58
     *  @Description: 承运方或企业 查询状态
     */
    @PostMapping("/selectByStatus")
    public ResultUtil selectByStatus(@RequestBody TCarrierCompanyOpenRoleVo record){
        try {
            return tJdWalletService.selectByStatus(record);
        } catch (Exception e) {
            log.error("查询状态失败！",e);
            return ResultUtil.error("查询状态失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/9 9:58
     *  @Description: 承运方或企业绑卡
     */
    @PostMapping("/bindingBank")
    public ResultUtil bindingBank(@RequestBody TJdBankCardVo record){
        try {
            return tJdWalletService.bindingBank(record);
        } catch (Exception e) {
            log.error("对公账户信息完善失败！",e);
            return ResultUtil.error("对公账户信息完善失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/5/11 9:10
     *  @Description: 承运方或企业解绑银行卡
     */
    @PostMapping("/unbindBank")
    public ResultUtil unbindBank(@RequestBody TJdBankCardVo record){
        try {
            return tJdWalletService.unbindBank(record);
        } catch (Exception e) {
            log.error("解绑银行卡失败！",e);
            return ResultUtil.error("解绑银行卡失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/9 11:26
     *  @Description: 打款验证
     */
    @PostMapping("/vitual")
    public ResultUtil vitual(@RequestBody TJdBankCardVo record){
        try {
            return tJdWalletService.vitual(record);
        } catch (Exception e) {
            log.error("打款验证失败！",e);
            return ResultUtil.error("打款验证失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/5/11 10:03
     *  @Description: 对公账户查询回显（承运方与企业）
     */
    @PostMapping("/selectJdBankCardId")
    public TJdBankCardVo selectJdBankCardId(@RequestBody TJdBankCardVo record){
        try {
            return tJdWalletService.selectJdBankCardId(record);
        } catch (Exception e) {
            log.error("对公账户查询失败！",e);
            return null;
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/5/11 10:03
     *  @Description: 对公账户管理页面（承运方与企业）
     */
     @PostMapping("/jdBankCardList")
     public ResultUtil jdBankCardList(@RequestBody TJdBankCardVo record){
         try {
             return tJdWalletService.jdBankCardList(record);
         } catch (Exception e) {
             log.error("对公账户管理列表查询失败！",e);
             return ResultUtil.error("对公账户管理列表查询失败！");
         }
     }


    /**
     *  @author: dingweibo
     *  @Date: 2021/8/17 19:06
     *  @Description: 查询打款验证金额接口
     */
    @PostMapping("/queryVerifyAmount")
    public ResultUtil queryVerifyAmount(@RequestBody QueryVerifyAmtReq record){
        try {
            return tJdWalletService.queryVerifyAmount(record);
        } catch (Exception e) {
            log.error("查询打款验证金额失败！",e);
            return ResultUtil.error("查询打款验证金额失败！");
        }
    }


    /**
     *  @author: dingweibo
     *  @Date: 2021/8/9 15:11
     *  @Description: 承运方京东钱包列表查询
     */
    @PostMapping("/selectByCarrierWalletPage")
    public ResultUtil selectByCarrierWalletPage(@RequestBody TCarrierCompanyOpenRoleSearchVo record){
        try {
            return tJdWalletService.selectByCarrierWalletPage(record);
        } catch (Exception e) {
            log.error("承运方京东钱包列表查询失败！",e);
            return ResultUtil.error("承运方京东钱包列表查询失败！");
        }
    }


    /**
     *  @author: dingweibo
     *  @Date: 2021/8/9 15:11
     *  @Description: 承运方京东钱包流水查询
     */
    @PostMapping("/selectByCarrierWalletChangeLogPage")
    public ResultUtil selectByCarrierWalletChangeLogPage(@RequestBody TCarrierCompanyOpenRoleSearchVo record){
        try {
            return tJdWalletService.selectByCarrierWalletChangeLogPage(record);
        } catch (Exception e) {
            log.error("承运方京东钱包流水查询失败！",e);
            return ResultUtil.error("承运方京东钱包流水查询失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/6/13 14:15
     *  @Description: 承运方京东钱包流水导出
     */
    @PostMapping("/selectByCarrierWalletChangeLogExcel")
    public ResultUtil selectByCarrierWalletChangeLogExcel(@RequestBody TCarrierCompanyOpenRoleSearchVo record){
        try {
            return tJdWalletService.selectByCarrierWalletChangeLogExcel(record);
        } catch (Exception e) {
            log.error("承运方京东钱包流水导出失败！",e);
            return ResultUtil.error("承运方京东钱包流水导出失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/10 10:00
     *  @Description: 京东 pc运营管理中财务管理 中的企业钱包详情
     */
    @PostMapping("/pcCompanyCwDetail")
    public ResultUtil pcCompanyCwDetail(@RequestBody TCarrierCompanyOpenRoleSearchVo record) {
        try {
            return ResultUtil.ok(tJdWalletService.pcCompanyCwDetail(record.getCompanyId()));
        } catch (Exception e) {
            log.error("获取企业钱包详情失败",e);
            return ResultUtil.error("获取企业钱包详情失败");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/10 14:40
     *  @Description: 获取京东企业资金流水列表
     */
    @PostMapping("/companyCapitalFlow")
    public ResultUtil companyCapitalFlow(@RequestBody CompanyCapitalFlowVo companyCapitalFlowVo) {
        try {
            return tJdWalletService.companyCapitalFlow(companyCapitalFlowVo);
        } catch (Exception e) {
            log.error("获取京东企业资金流水失败！",e);
            return ResultUtil.error("获取京东企业资金流水失败！");
        }
    }
    /**
     *  @author: dingweibo
     *  @Date: 2022/3/11 9:21
     *  @Description: 获取京东企业资金流水导出
     */
    @PostMapping("/companyCapitalFlowExcel")
    public ResultUtil companyCapitalFlowExcel(@RequestBody CompanyCapitalFlowVo companyCapitalFlowVo) {
        try {
            return tJdWalletService.companyCapitalFlowExcel(companyCapitalFlowVo);
        } catch (Exception e) {
            log.error("导出京东企业资金流水失败！",e);
            return ResultUtil.error("导出京东企业资金流水失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/11 16:57
     *  @Description: 终端钱包流水
     */
    @PostMapping("/driverCapitalFlow")
    public ResultUtil driverCapitalFlow(@RequestBody CompanyCapitalFlowVo companyCapitalFlowVo) {
        try {
            return tJdWalletService.driverCapitalFlow(companyCapitalFlowVo);
        } catch (Exception e) {
            log.error("获取司机资金流水失败",e);
            return ResultUtil.error("获取司机资金流水失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/28 14:21
     *  @Description: 京东经纪人钱包
     */
    @PostMapping("/selectAgentWallet")
    public ResultUtil selectAgentWallet() {
        try {
            return tJdWalletService.selectAgentWallet();
        } catch (Exception e) {
            log.error("经纪钱包查询失败",e);
            return ResultUtil.error("经纪钱包查询失败！");
        }
    }
    /**
     *  @author: dingweibo
     *  @Date: 2021/8/28 14:21
     *  @Description: 京东经纪人钱包流水
     */
    @PostMapping("/selectAgentWalletList")
    public ResultUtil selectAgentWalletList(@RequestBody CompanyCapitalFlowVo record) {
        try {
            return tJdWalletService.selectAgentWalletList(record);
        } catch (Exception e) {
            log.error("经纪钱包查询失败",e);
            return ResultUtil.error("经纪钱包查询失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/9/6 11:36
     *  @Description: 平台钱包
     */
    @PostMapping("/selectPfWallet")
    public ResultUtil selectPfWallet() {
        try {
            return tJdWalletService.selectPfWallet();
        } catch (Exception e) {
            log.error("平台钱包查询失败",e);
            return ResultUtil.error("平台钱包查询失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/8/28 14:21
     *  @Description: 平台钱包流水
     */
    @PostMapping("/selectPfWalletList")
    public ResultUtil selectPfWalletList(@RequestBody CompanyCapitalFlowVo record) {
        try {
            return tJdWalletService.selectPfWalletList(record);
        } catch (Exception e) {
            log.error("平台钱包流水查询失败",e);
            return ResultUtil.error("平台钱包流水查询失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/6/13 14:27
     *  @Description: 平台钱包流水导出
     */
    @PostMapping("/selectPfWalletListExcel")
    public ResultUtil selectPfWalletListExcel(@RequestBody CompanyCapitalFlowVo record) {
        try {
            return tJdWalletService.selectPfWalletListExcel(record);
        } catch (Exception e) {
            log.error("平台钱包流水导出失败",e);
            return ResultUtil.error("平台钱包流水导出失败！");
        }
    }

}
