package com.lz.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.aliyun.oss.model.OSSObject;
import com.lz.common.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.*;

/**
 *  @author: dingweibo
 *  @Date: 2021/11/1 17:25
 *  @Description:
 */
@Slf4j
@Controller
@RequestMapping("/oss")
public class FileObjectController{

    @Autowired
    private OssClientUtil ossClientUtil;
    /**
     * 阿里云文件上传
     * @param
     *  file 文件流
     * @return
     * String 文件引用路径 如 String filePath = "http://aliyun.xxxx.xxxx/xxxx/xxxx/xxxx.jpg"
     * */
    @RequestMapping(value = "/upload/file/sample")
    @ResponseBody
    public ResultUtil uploadFileSample(@RequestParam("file")MultipartFile file) throws IOException {
        //获取文件名称
        String fileName = file.getOriginalFilename();
        //获得文件后缀
        String prefix=fileName.substring(fileName.lastIndexOf("."));
        //生成文件存储名称
        String fileSaveName = IdWorkerUtil.getInstance().nextId() + prefix;
        try{
            //新增阿里云文件上传
            log.info("文件名入参：",fileSaveName);
            log.info("文件入参：",file);
            ResultUtil resultUtil = ossClientUtil.uploadFile(fileSaveName,file.getInputStream());
            return resultUtil;
        }catch(Exception e){
            log.error("文件上传失败：",e);
            return ResultUtil.error("文件上传失败");
        }
    }

    /**
     * 阿里云文件上传
     * @param
     *  file 文件流
     * @return
     * String 文件引用路径 如 String filePath = "http://aliyun.xxxx.xxxx/xxxx/xxxx/xxxx.jpg"
     * */
    @RequestMapping(value = "/upload/file/sampleTwo")
    @ResponseBody
    public ResultUtil uploadFileSampleTwo(@RequestParam("file")MultipartFile file) throws IOException {
        Map<Object, Object> map = new HashMap<Object, Object>();
        // 检查文件类型
        if (!FileCheck.checkImage(file)&&
                !FileCheck.checkImage(file.getOriginalFilename())) {
            FileResponseData responseData = new FileResponseData(false);
            responseData.setCode(ErrorCode.FILE_TYPE_ERROR_IMAGE.CODE);
            responseData.setMessage(ErrorCode.FILE_TYPE_ERROR_IMAGE.MESSAGE);
            return new ResultUtil(CodeEnum.ERROR.getCode(), ErrorCode.FILE_TYPE_ERROR_IMAGE.MESSAGE);
        }
        //获取文件名称
        String fileName = file.getOriginalFilename();
        //获得文件后缀
        String prefix=fileName.substring(fileName.lastIndexOf("."));
        //生成文件存储名称
        String fileSaveName = IdWorkerUtil.getInstance().nextId() + prefix;
        try{
            //新增阿里云文件上传
            ResultUtil resultUtil = ossClientUtil.uploadFile(fileSaveName,file.getInputStream());
            map.put("filePath", resultUtil.getData());
            return ResultUtil.ok(map);
        }catch(Exception e){
            log.error("文件上传失败：",e);
            return ResultUtil.error("文件上传失败");
        }
    }

    @PostMapping("/uploadQRImageOss")
    @ResponseBody
    public String uploadQRImageOss(@RequestBody OssQrImageUploadVO in) throws Exception {
        try {
            log.info("开始生成二维码");
            File file = FileUtil.file(in.getServerPath() + in.getCode() + ".jpg");
            File qrFile = QrCodeUtil.generate(in.getDomainURL(),
                    300, 300, file);
            String fileName = file.getName();
            //获得文件后缀
            String prefix=fileName.substring(fileName.lastIndexOf("."));
            //生成文件存储名称
            String fileSaveName = IdWorkerUtil.getInstance().nextId() + prefix;
            //新增阿里云文件上传
            ResultUtil resultUtil = ossClientUtil.uploadFile(fileSaveName,new FileInputStream(qrFile));
            FileUtil.del(file);
            log.info("生成二维码结束:", resultUtil.getData());
            return resultUtil.getData().toString();
        } catch (Exception e) {
            log.error("生成二维码上传失败：",e);
            return "生成二维码上传失败";
        }
    }

    /**
     * 阿里云文件上传
     * @param
     * @return
     * */
    @RequestMapping(value = "/upload/filePath")
    @ResponseBody
    public ResultUtil filePath(@RequestParam(value="fileSaveName") String fileSaveName,@RequestParam(value="urlPath") String urlPath) {
        Map<Object, Object> map = new HashMap<Object, Object>();
        try{
            //新增阿里云文件上传
            InputStream inputStream = new FileInputStream(urlPath);
            ResultUtil resultUtil = ossClientUtil.uploadFile(fileSaveName,inputStream);
            map.put("filePath", resultUtil.getData());
            inputStream.close();
            return ResultUtil.ok(map);
        }catch(Exception e){
            log.error("文件上传失败：",e);
            return ResultUtil.error("文件上传失败");
        }
    }

    /**
     * 装货榜单图片下载
     * @param
     * @return
     * */
    @RequestMapping(value = "/downloadFile")
    @ResponseBody
    public ResultUtil downloadFile(@RequestParam("objectFileName") String objectFileName,
                                               @RequestParam("url") String url,
                                               @RequestParam("fileName") String fileName) {
        String imgURL="http://img.lugangtong56.com/";
        String replace = objectFileName.replace(imgURL, "");
        String[] split = replace.split("\\.");
        boolean fileExist = ossClientUtil.isFileExist(replace);
        if(fileExist){
            if(null != split[1]){
                ossClientUtil.downloadFile(replace,url,fileName+"."+split[1]);
            }
            return ResultUtil.ok();
        }else{
            return ResultUtil.error("下载图片："+replace+"失败");
        }
    }
}
