package com.lz.common;

import com.aliyun.oss.*;
import com.aliyun.oss.model.GetObjectRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;
import java.util.Date;

@Slf4j
@EnableConfigurationProperties
@Service
public class OssClientUtil {

    //阿里云API的外网域名
    @Value("${endpoint}")
    private String endpoint;
    //阿里云API的密钥Access Key ID
    @Value("${accessKeyId}")
    private  String accessKeyId;
    //阿里云API的密钥Access Key Secret
    @Value("${accessKeySecret}")
    private  String accessKeySecret;
    //阿里云API的bucket名称
    @Value("${bucketName}")
    private  String bucketName ;
    //图片访问域名
    @Value("${imgUrl}")
    private String imgUrl;

    public ResultUtil uploadFile(String savePathName, InputStream inputStream)
    {
        OSS ossClient = new OSSClientBuilder().build(endpoint,accessKeyId,accessKeySecret);
        try {
            ossClient.putObject(bucketName, savePathName, inputStream);
            log.info("文件上传返回地址：",imgUrl+"/"+savePathName);
            return ResultUtil.ok(imgUrl+"/"+savePathName);
        } catch (OSSException oe) {
            log.error("*************************************************OSS upload file error create_date " + new Date() + "*************************************");
            log.error("Caught an OSSException,which means your request made it to OSS,"
                    + "but was rejected with an error response for some reason.");
            log.error("Error Message: " + oe.getErrorCode());
            log.error("Error Code:    " + oe.getErrorCode());
            log.error("Request ID:   " + oe.getRequestId());
            log.error("Host ID:      " + oe.getHostId());
            log.error("*************************************************OSS upload file error*************************************",oe);
        } catch (ClientException ce) {
            log.error("Caught an ClientException,which means the client encountered "
                    + "a serIoUs internal problem while trying to communicate with OSS,"
                    + "such as not being able to access the network.");
            log.error("Error Message: " , ce);
            return ResultUtil.error("文件上传失败");
        } catch (Throwable e) {
            log.error("Error Throwable: ",e);
            return ResultUtil.error("文件上传失败");
        } finally {
            ossClient.shutdown();
        }
        return ResultUtil.ok();
    }

    /**
     * 通过文件名称（包括路径）下载文件
     *
     * @param objectName    相对于阿里云bucket存储空间的文件路径和文件名称
     * @param localFileName 下载的路径和文件名称
     */
    public ResultUtil downloadFile(String objectName,String url, String localFileName) {
        OSS ossClient = new OSSClientBuilder().build("oss-cn-zhangjiakou.aliyuncs.com",
                "LTAI4G1Q4jCfPKXQSLLmkK4E","******************************");
        try {
            // 下载OSS文件到本地文件。如果指定的本地文件存在会覆盖，不存在则新建。
            ossClient.getObject(new GetObjectRequest("fileupinfo", objectName), new File(url+"/"+localFileName));
            return ResultUtil.ok();
        } catch (OSSException oe) {
            log.error("*************************************************OSS upload file error create_date " + new Date() + "*************************************");
            log.error("Caught an OSSException,which means your request made it to OSS,"
                    + "but was rejected with an error response for some reason.");
            log.error("Error Message: " + oe.getErrorCode());
            log.error("Error Code:    " + oe.getErrorCode());
            log.error("Request ID:   " + oe.getRequestId());
            log.error("Host ID:      " + oe.getHostId());
            log.error("*************************************************OSS upload file error*************************************",oe);
        } catch (ClientException ce) {
            log.error("Caught an ClientException,which means the client encountered "
                    + "a serIoUs internal problem while trying to communicate with OSS,"
                    + "such as not being able to access the network.");
            log.error("Error Message: " , ce);
            return ResultUtil.error("文件下载失败");
        } catch (Throwable e) {
            log.error("Error Throwable: ",e);
            return ResultUtil.error("文件下载失败");
        } finally {
            ossClient.shutdown();
        }
        return ResultUtil.ok();
    }

    /**
     * 判断文件是否存在
     * @param objectName 文件名
     */
    public boolean isFileExist(String objectName) {
        OSS ossClient = new OSSClientBuilder().build("oss-cn-zhangjiakou.aliyuncs.com", "LTAI4G1Q4jCfPKXQSLLmkK4E", "******************************");
        boolean res = ossClient.doesObjectExist("fileupinfo", objectName);
        return res;
    }
}
