package com.lz.controller;

import me.chanjar.weixin.common.bean.menu.WxMenu;
import me.chanjar.weixin.common.bean.menu.WxMenuButton;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.menu.WxMpGetSelfMenuInfoResult;
import me.chanjar.weixin.mp.bean.menu.WxMpMenu;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.net.MalformedURLException;

import static me.chanjar.weixin.common.api.WxConsts.MenuButtonType;

/**
 * 菜单类
 * <AUTHOR>
 */
@RestController
@RequestMapping("/wx/menu/{appid}")
public class WxMenuController {
    private WxMpService wxService;

    @Value("${domainURL}")
    private String domainURL;

    @Value("${domainURLONE}")
    private String domainURLONE;

    @Autowired
    public WxMenuController(WxMpService wxService) {
        this.wxService = wxService;
    }

    /**
     * <pre>
     * 自定义菜单创建接口
     * 详情请见：https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421141013&token=&lang=zh_CN
     * 如果要创建个性化菜单，请设置matchrule属性
     * 详情请见：https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1455782296&token=&lang=zh_CN
     * </pre>
     *
     * @return 如果是个性化菜单，则返回menuid，否则返回null
     */
    @PostMapping("/create")
    public String menuCreate(@PathVariable String appid, @RequestBody WxMenu menu) throws WxErrorException {
        return this.wxService.switchover1(appid).getMenuService().menuCreate(menu);
    }

    @GetMapping("/create")
        public String menuCreateSample(@PathVariable String appid,String myCenterUrl,String walletUrl,String getMoney,String loadAddressUrl,String unloadAddressUrl) throws WxErrorException, MalformedURLException {


        /*  --------------------------------------------------
                “陆港通物流”公众号关闭1.5菜单后，菜单调整如下：
                1.注册教程----跳转原“2.0司机注册说明”
                2.司机端入口
                     ①装货签到----跳转原“2.0装货签到”
                     ②卸货签到----跳转原“2.0卸货签到”
                     ③个人中心----跳转原“2.0个人中心”
                3.企业端入口
                     ①企业端APP
                     ②APP下载
       ------------------------------------------------*/

        WxMenu menu = new WxMenu();

        WxMenuButton Button1 = new WxMenuButton();
        Button1.setType(MenuButtonType.VIEW);
        Button1.setName("陆港通帮你");
        WxMenuButton button11 = new WxMenuButton();
        button11.setType(MenuButtonType.VIEW);
        button11.setName("司机注册教程");
        button11.setUrl("https://c.eqxiu.com/s/BaTAchgl?eip=true");
        WxMenuButton button12=new WxMenuButton();
        button12.setType(MenuButtonType.VIEW);
        button12.setName("业务部助手");
        button12.setUrl(domainURL+"/lgt-app-2.0/#/loginBusiness");
        WxMenuButton button13=new WxMenuButton();
        button13.setType(MenuButtonType.VIEW);
        button13.setName("联系客服");
        button13.setUrl("https://work.weixin.qq.com/kfid/kfc9777cf21e4b154bf");
        Button1.getSubButtons().add(button11);
        Button1.getSubButtons().add(button12);
        Button1.getSubButtons().add(button13);

        WxMenuButton Button2 = new WxMenuButton();
        Button2.setName("司机端入口");
        WxMenuButton button21 = new WxMenuButton();
        button21.setType(MenuButtonType.VIEW);
        button21.setName("装货签到");
        button21.setUrl(domainURL+"/lgt-app-driver-weixin/#/loadsignin");
        WxMenuButton button22=new WxMenuButton();
        button22.setType(MenuButtonType.VIEW);
        button22.setName("卸货签到");
        button22.setUrl(domainURL+"/lgt-app-driver-weixin/#/unloadsignin");
        WxMenuButton button23=new WxMenuButton();
        button23.setType(MenuButtonType.VIEW);
        button23.setName("个人中心");
        button23.setUrl(domainURL+"/lgt-app-driver-weixin/#/my");
        WxMenuButton button24 = new WxMenuButton();
        button24.setName("司机端APP下载");
        button24.setType(MenuButtonType.VIEW);
        button24.setUrl(domainURL+"/driverAppQrCode");
        Button2.getSubButtons().add(button21);
        Button2.getSubButtons().add(button22);
        Button2.getSubButtons().add(button23);
        Button2.getSubButtons().add(button24);


        WxMenuButton Button3 = new WxMenuButton();
        Button3.setName("企业端入口");
        WxMenuButton button31 = new WxMenuButton();
        button31.setName("企业端H5");
        button31.setType(MenuButtonType.VIEW);
        button31.setUrl(domainURL+"/lgt-app-2.0/#/login");
        WxMenuButton button32 = new WxMenuButton();
        button32.setName("企业端APP下载");
        button32.setType(MenuButtonType.VIEW);
        button32.setUrl(domainURL+"/appQrCode");
        Button3.getSubButtons().add(button31);
        Button3.getSubButtons().add(button32);

        menu.getButtons().add(Button1);
        menu.getButtons().add(Button2);
        menu.getButtons().add(Button3);
        this.wxService.switchover(appid);
        return this.wxService.getMenuService().menuCreate(menu);
    }

    /**
     * <pre>
     * 自定义菜单创建接口
     * 详情请见： https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421141013&token=&lang=zh_CN
     * 如果要创建个性化菜单，请设置matchrule属性
     * 详情请见：https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1455782296&token=&lang=zh_CN
     * </pre>
     *
     * @return 如果是个性化菜单，则返回menuid，否则返回null
     */
    @PostMapping("/createByJson")
    public String menuCreate(@PathVariable String appid, @RequestBody String json) throws WxErrorException {
        return this.wxService.switchover1(appid).getMenuService().menuCreate(json);
    }

    /**
     * <pre>
     * 自定义菜单删除接口
     * 详情请见: https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421141015&token=&lang=zh_CN
     * </pre>
     */
    @GetMapping("/delete")
    public void menuDelete(@PathVariable String appid) throws WxErrorException {
        this.wxService.switchover1(appid).getMenuService().menuDelete();
    }

    /**
     * <pre>
     * 删除个性化菜单接口
     * 详情请见: https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1455782296&token=&lang=zh_CN
     * </pre>
     *
     * @param menuId 个性化菜单的menuid
     */
    @GetMapping("/delete/{menuId}")
    public void menuDelete(@PathVariable String appid, @PathVariable String menuId) throws WxErrorException {
        this.wxService.switchover1(appid).getMenuService().menuDelete(menuId);
    }

    /**
     * <pre>
     * 自定义菜单查询接口
     * 详情请见： https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421141014&token=&lang=zh_CN
     * </pre>
     */
    @GetMapping("/get")
    public WxMpMenu menuGet(@PathVariable String appid) throws WxErrorException {
        return this.wxService.switchover1(appid).getMenuService().menuGet();
    }

    /**
     * <pre>
     * 测试个性化菜单匹配结果
     * 详情请见: http://mp.weixin.qq.com/wiki/0/c48ccd12b69ae023159b4bfaa7c39c20.html
     * </pre>
     *
     * @param userid 可以是粉丝的OpenID，也可以是粉丝的微信号。
     */
    @GetMapping("/menuTryMatch/{userid}")
    public WxMenu menuTryMatch(@PathVariable String appid, @PathVariable String userid) throws WxErrorException {
        return this.wxService.switchover1(appid).getMenuService().menuTryMatch(userid);
    }

    /**
     * <pre>
     * 获取自定义菜单配置接口
     * 本接口将会提供公众号当前使用的自定义菜单的配置，如果公众号是通过API调用设置的菜单，则返回菜单的开发配置，而如果公众号是在公众平台官网通过网站功能发布菜单，则本接口返回运营者设置的菜单配置。
     * 请注意：
     * 1、第三方平台开发者可以通过本接口，在旗下公众号将业务授权给你后，立即通过本接口检测公众号的自定义菜单配置，并通过接口再次给公众号设置好自动回复规则，以提升公众号运营者的业务体验。
     * 2、本接口与自定义菜单查询接口的不同之处在于，本接口无论公众号的接口是如何设置的，都能查询到接口，而自定义菜单查询接口则仅能查询到使用API设置的菜单配置。
     * 3、认证/未认证的服务号/订阅号，以及接口测试号，均拥有该接口权限。
     * 4、从第三方平台的公众号登录授权机制上来说，该接口从属于消息与菜单权限集。
     * 5、本接口中返回的图片/语音/视频为临时素材（临时素材每次获取都不同，3天内有效，通过素材管理-获取临时素材接口来获取这些素材），本接口返回的图文消息为永久素材素材（通过素材管理-获取永久素材接口来获取这些素材）。
     *  接口调用请求说明:
     * http请求方式: GET（请使用https协议）
     * https://api.weixin.qq.com/cgi-bin/get_current_selfmenu_info?access_token=ACCESS_TOKEN
     * </pre>
     */
    @GetMapping("/getSelfMenuInfo")
    public WxMpGetSelfMenuInfoResult getSelfMenuInfo(@PathVariable String appid) throws WxErrorException {
        return this.wxService.switchover1(appid).getMenuService().getSelfMenuInfo();
    }
}
