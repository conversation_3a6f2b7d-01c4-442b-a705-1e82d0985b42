<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>lgt-app</artifactId>
		<groupId>com.lz</groupId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<groupId>com.lz</groupId>
	<artifactId>lgt-app-payment-api</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>lgt-app-payment-api</name>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<!-- set thymeleaf version -->
		<thymeleaf.version>3.0.0.RELEASE</thymeleaf.version>
		<thymeleaf-layout-dialect.version>2.0.0</thymeleaf-layout-dialect.version>
		<!--set java version-->
		<java.version>1.8</java.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>lgt-app-mq-api</artifactId>
			<version>0.0.1-SNAPSHOT</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>lgt-app-common</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcprov-jdk16</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-json</artifactId>
			<version>5.4.0</version>
		</dependency>
	</dependencies>



</project>
