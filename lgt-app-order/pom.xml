<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>lgt-app</artifactId>
		<groupId>com.lz</groupId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<packaging>jar</packaging>
	<url>http://maven.apache.org</url>
	<artifactId>lgt-app-order</artifactId>
	<dependencies>

		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>lgt-app-member-api</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>lgt-app-resource-api</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>lgt-app-order-api</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>lgt-app-logging</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>lgt-app-fastdfs-api</artifactId>
			<version>0.0.1-SNAPSHOT</version>
			<scope>compile</scope>
		</dependency>

		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>tx-trans-manager-service</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>lgt-app-system-api</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
        <dependency>
            <groupId>com.lz</groupId>
            <artifactId>lgt-app-tool-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>lgt-app-schedule-api</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
        <dependency>
            <groupId>com.lz</groupId>
            <artifactId>lgt-app-message</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>lgt-app-finance-api</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>fontbox</artifactId>
			<version>2.0.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
			<version>2.0.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox-tools</artifactId>
			<version>2.0.1</version>
		</dependency>

		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>lgt-app-es-api</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper</artifactId>
			<version>5.1.4</version>
		</dependency>
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
            <scope>compile</scope>
        </dependency>



		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>axiom-api</artifactId>
			<version>1.2.5</version>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>wonders-cuteinfo-client</artifactId>
			<version>3.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>axiom-dom</artifactId>
			<version>1.2.5</version>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>axiom-impl</artifactId>
			<version>1.2.5</version>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>axis2-adb</artifactId>
			<version>1.3</version>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>axis2-adb-codegen</artifactId>
			<version>1.3</version>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>axis2-clustering</artifactId>
			<version>1.3</version>
		</dependency>

		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>axis2-fastinfoset</artifactId>
			<version>1.3</version>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>axis2-java2wsdl</artifactId>
			<version>1.3</version>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>axis2-jaxbri</artifactId>
			<version>1.3</version>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>axis2-jaxws</artifactId>
			<version>1.3</version>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>axis2-jaxws-api</artifactId>
			<version>1.3</version>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>axis2-jibx</artifactId>
			<version>1.3</version>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>axis2-json</artifactId>
			<version>1.3</version>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>axis2-jws-api</artifactId>
			<version>1.3</version>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>axis2-kernel</artifactId>
			<version>1.3</version>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>axis2-metadata</artifactId>
			<version>1.3</version>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>axis2-mtompolicy</artifactId>
			<version>1.3</version>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>axis2-saaj</artifactId>
			<version>1.3</version>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>axis2-saaj-api</artifactId>
			<version>1.3</version>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>axis2-spring</artifactId>
			<version>1.3</version>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId >axis2-xmlbeans</artifactId>
			<version>1.3</version>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>wsdl4j</artifactId>
			<version>1.6.2</version>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>XmlSchema</artifactId>
			<version>1.3.2</version>
		</dependency>

		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>lgt-app-complaints-api</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>lgt-app-mq-api</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun.openservices</groupId>
			<artifactId>ons-client</artifactId>
			<version>1.8.7.4</version>
		</dependency>
        <dependency>
            <groupId>com.lz</groupId>
            <artifactId>lgt-app-oss-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.junit.vintage</groupId>
					<artifactId>junit-vintage-engine</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.lz</groupId>
			<artifactId>lz_sdk</artifactId>
			<version>0.0.3.6-beta2</version>
		</dependency>
    </dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<!-- mybatis generator 自动生成代码插件 -->
			<plugin>
				<groupId>org.mybatis.generator</groupId>
				<artifactId>mybatis-generator-maven-plugin</artifactId>
				<version>1.3.2</version>
				<configuration>
					<configurationFile>${basedir}/src/main/resources/generator/generatorConfig.xml</configurationFile>
					<overwrite>true</overwrite>
					<verbose>true</verbose>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<encoding>UTF-8</encoding>
					<!--<compilerArguments>
						<extdirs>${project.basedir}/src/main/resources/lib</extdirs>
					</compilerArguments>-->
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
