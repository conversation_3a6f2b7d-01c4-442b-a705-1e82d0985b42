package com.lz.controller;

import com.lz.common.util.ResultUtil;
import com.lz.model.TOrderContract;
import com.lz.model.VoucherReq;
import com.lz.service.TOrderContractService;
import com.lz.vo.TOrderContractUploadVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;


/**
 *  @author: dingweibo
 *  @Date: 2019/6/28 14:53
 *  @Description: 合同操作类
 */
@Controller
@RequestMapping("/tOrderContract")
@Slf4j
public class TOrderContractController {

    @Autowired
    private TOrderContractService tOrderContractService;

    
    /**
     *  @author: dingweibo
     *  @Date: 2019/6/28 14:54
     *  @Description: 根据运单code（非businessCode），查询合同
     */
    @RequestMapping("/selectByOrderBusinessCode")
    @ResponseBody
    public TOrderContract selectByOrderBusinessCode(@RequestParam(value = "code") String code){
        return tOrderContractService.selectByOrderBusinessCode(code);
    }
    /**
     *  @author: dingweibo
     *  @Date: 2019/6/28 14:54
     *  @Description: 根据运单code（非businessCode），查询合同
     */
    @RequestMapping("/selectByOrderCodeAnrType")
    @ResponseBody
    public TOrderContract selectByOrderCodeAnrType(@RequestParam(value = "code") String code,@RequestParam(value = "contractType") String contractType){
        return tOrderContractService.selectByOrderCodeAnrType(code,contractType);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2019/6/28 14:54
     *  @Description: 根据运单code（非businessCode），查询合同
     */
    @RequestMapping("/selectByOrderCodeAnrTypeAnrEnable")
    @ResponseBody
    public TOrderContract selectByOrderCodeAnrTypeAnrEnable(@RequestParam(value = "code") String code,@RequestParam(value = "contractType") String contractType,
                                                   @RequestParam(value = "enable") boolean enable){
        return tOrderContractService.selectByOrderCodeAnrTypeAnrEnable(code,contractType,enable);
    }

    //更新合同
    @RequestMapping("/updateHt")
    @ResponseBody
    public ResultUtil aupdateHt(@RequestBody TOrderContract record) {

        return new ResultUtil("修改成功",tOrderContractService.updateHt(record));
    }

    @RequestMapping("/updateByOrderCodeModel")
    @ResponseBody
    public ResultUtil updateByOrderCodeModel(@RequestBody TOrderContract record) {
        return ResultUtil.ok(tOrderContractService.updateByOrderCodeModel(record));
    }

    //增加合同
    @RequestMapping("/add")
    @ResponseBody
    public ResultUtil add(@RequestBody TOrderContract record) {

        return new ResultUtil("新增成功",tOrderContractService.add(record));
    }

    /*
     * <AUTHOR>
     * @Description 定时任务，生成收款凭证
     * @Date 2019/12/28 13:19
     * @Param
     * @return
    **/
    @RequestMapping("/createTxxy")
    @ResponseBody
    public ResultUtil createTxxy(@RequestBody VoucherReq record) {
        ResultUtil txxy = tOrderContractService.createTxxy(record);
        return txxy;
    }

    /**
     *  @author: dingweibo
     *  @Date: 2020/8/27 16:20
     *  @Description: 运单跟踪中三方货物合同上传
     *
     */
    @PostMapping("/threeGoodsContractUpload")
    @ResponseBody
    public ResultUtil threeGoodsContractUpload(@RequestBody TOrderContractUploadVo record){
        try{
            return tOrderContractService.threeGoodsContractUpload(record);
        }catch (Exception e){
            log.error("三方货物合同上传失败",e);
            return  ResultUtil.error("三方货物合同上传失败");
        }
    }

    @RequestMapping("/getContractPhotoByOrderCode")
    @ResponseBody
    TOrderContract getContractPhotoByOrderCode(@RequestParam("code") String code){
        return tOrderContractService.getContractPhotoByOrderCode(code);
    }

}
