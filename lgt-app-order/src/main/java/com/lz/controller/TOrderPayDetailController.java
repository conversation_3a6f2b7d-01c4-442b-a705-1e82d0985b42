package com.lz.controller;

import com.lz.common.util.ResultUtil;
import com.lz.dto.TOrderPayInfoDTO;
import com.lz.model.TOrderPayDetail;
import com.lz.service.TOrderPayDetailService;
import com.lz.vo.TOrderPayDetailVO;
import com.lz.vo.TxPcRecordQueryVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 *  @author: sangbin
 *  @Date: 2019/6/28 15:05
 *  @Description: 运单支付字表保存
 */
@Controller
@RequestMapping("/api/tOrderPayDetail")
public class TOrderPayDetailController {

    @Autowired
    private TOrderPayDetailService tOrderPayDetailService;

    @ResponseBody
    @PostMapping("/save")
    public ResultUtil asave(@RequestBody TOrderPayDetail orderPayDetail){
        tOrderPayDetailService.save(orderPayDetail);
        return ResultUtil.ok();
    }

    @ResponseBody
    @PostMapping("/selectByOrderPayInfoByDetailCode")
    public TOrderPayInfoDTO selectByOrderPayInfoByDetailCode(@RequestParam(value = "code") String code) {
        TOrderPayInfoDTO tOrderPayInfoDTO = tOrderPayDetailService.selectByOrderPayDetailKey(code);
        return tOrderPayInfoDTO;
    }

    @ResponseBody
    @PostMapping("/selectBankByCastChanges")
    public TOrderPayInfoDTO selectBankByCastChanges(@RequestParam(value = "code") String code) {
        TOrderPayInfoDTO tOrderPayInfoDTO=tOrderPayDetailService.selectBankByCastChanges(code);
        return tOrderPayInfoDTO;
    }
    @ResponseBody
    @PostMapping("/updateStatusByPayCode")
    public ResultUtil updateStatusByPayCode(@RequestBody TOrderPayDetailVO vo){
        tOrderPayDetailService.updateStatusByPayCode(vo);
        return ResultUtil.ok();
    }

    @ResponseBody
    @PostMapping("/selectByCode")
    public TOrderPayDetail asave(@RequestParam(value = "code") String code){
       return tOrderPayDetailService.selectByCode(code);
    }

    /**
     * @description PC京东提现记录
     * <AUTHOR>
     * @date 2021/11/26 15:40
     */
    @ResponseBody
    @PostMapping("/pc/txRecord")
    public ResultUtil pcTxRecord(@RequestBody TxPcRecordQueryVO vo) {
        return tOrderPayDetailService.selectTxPcRecord(vo);
    }

    /**
     * PC华夏提现记录
     */
    @ResponseBody
    @PostMapping("/pc/hxTxRecord")
    public ResultUtil hxTxRecord(@RequestBody TxPcRecordQueryVO vo) {
        return tOrderPayDetailService.selectHxTxPcRecord(vo);
    }

    //肥城资金上报修改上报状态
    @ResponseBody
    @PostMapping("/updateCapitalSendState")
    public ResultUtil updateCapitalSendState(@RequestParam Integer payDetailId) {
        return tOrderPayDetailService.updateCapitalSendState(payDetailId);
    }

    //安徽资金上报修改上报状态
    @ResponseBody
    @PostMapping("/updateAhCapitalSendState")
    public ResultUtil updateAhCapitalSendState(@RequestParam Integer payDetailId) {
        return tOrderPayDetailService.updateAhCapitalSendState(payDetailId);
    }

}
