package com.lz.controller.pay;

import cn.hutool.json.JSONUtil;
import com.lz.common.config.RedisUtil;
import com.lz.common.dbenum.BankNameEnum;
import com.lz.common.dbenum.BusinessCode;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.common.util.ThrowableUtil;
import com.lz.dto.OpenRoleStatusListDTO;
import com.lz.dto.OpenRoleWalletListDTO;
import com.lz.model.TOrderInfo;
import com.lz.payment.hxyh.HXPayOrderUtil;
import com.lz.service.TOrderContractService;
import com.lz.service.TOrderInfoService;
import com.lz.service.TOrderZtAccountOpenInfoService;
import com.lz.service.pay.OrderPayService;
import com.lz.util.PayUtil;
import com.lz.vo.SelectOpenRoleVO;
import com.lz.vo.pay.OrderBatchPay;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@RestController
@RequestMapping("/order")
public class OrderPayController {

    @Resource
    private TOrderInfoService orderInfoService;

    @Resource
    private OrderPayService orderPayService;

    @Resource
    private TOrderContractService orderContractService;

    @Resource
    private TOrderZtAccountOpenInfoService orderZtAccountOpenInfoService;

    @Autowired
    private PayUtil payUtil;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private HXPayOrderUtil hxPayOrderUtil;

    @Resource
    private RedisUtil redisUtil;

    /**
     * 批量支付
     *
     * @param
     * @return
     */
    @PostMapping("/batchPay")
    public ResultUtil<?> batchPay(@RequestBody OrderBatchPay pay, HttpServletRequest request) {
        try {
            if (null == pay.getList() || pay.getList().length == 0) {
                return ResultUtil.error("请选择运单");
            }
            if (pay.getList().length > 1000) {
                return ResultUtil.error("最多选择1000条运单");
            }
            Integer[] selectedOrderIds = pay.getList();
            Integer[] orderIds = orderPayService.commonPatternUnique(pay.getList());
            if (orderIds.length == 0) {
                return ResultUtil.error("当前不支持经纪人模式");
            }
            pay.setList(orderIds);
            Integer companyUnique = orderPayService.companyUnique(pay.getList());
            if (companyUnique != 1) {
                return ResultUtil.error("请选择同一企业运单");
            }
            Integer carrierUnique = orderPayService.carrierUnique(pay.getList());
            if (carrierUnique != 1) {
                return ResultUtil.error("请选择同一承运方运单");
            }
            TOrderInfo tOrderInfo = orderInfoService.selectByPrimaryKey(pay.getList()[0]);
            pay.setCompanyId(tOrderInfo.getCompanyId());
            pay.setCarrierId(tOrderInfo.getCarrierId());
            // 查询出款方、入款方会员编号
            SelectOpenRoleVO vo = new SelectOpenRoleVO();
            vo.setCarrierId(pay.getCarrierId());
            vo.setCompanyId(pay.getCompanyId());
            vo.setChannelId(BankNameEnum.HXBANK.key());
            ResultUtil openRoleStatus = hxPayOrderUtil.selectOpenRoleStatus(vo);
            if (DictEnum.ERROR.code.equals(openRoleStatus.getCode())) {
                return ResultUtil.error(openRoleStatus.getMsg());
            }
            String jsonStr = JSONUtil.toJsonStr(openRoleStatus.getData());
            OpenRoleStatusListDTO openRoleStatusListDTO = JSONUtil.toBean(jsonStr, OpenRoleStatusListDTO.class);
            pay.setOutPartnerAccId(openRoleStatusListDTO.getCompanyStatus().getPartnerAccId());
            pay.setInPartnerAccId(openRoleStatusListDTO.getCarrierStatus().getPartnerAccId());
            String key = openRoleStatusListDTO.getCompanyStatus().getPartnerAccId();
            RLock lock = redissonClient.getLock(key);
            if (lock.tryLock()) {
                try {
                    if (redisUtil.hasKey("BATCH_PAY:" + key)) {
                        return ResultUtil.error("企业正在支付，请稍后再试");
                    }
                    redisUtil.set("BATCH_PAY:" + key, key, 10);
                    orderIds = orderPayService.selectOrderIds(orderIds, pay.getCompanyId());
                    if (orderIds.length == 0) {
                        return ResultUtil.error("未查询到当前企业可支付运单");
                    }
                    pay.setList(orderIds);
                    ResultUtil checkPay = payUtil.checkPay(request, tOrderInfo.getCompanyId(), pay.getMacAddress(),
                            tOrderInfo.getOrderBusinessCode(), pay.getPayPassword(), pay.getSourceChannel());
                    if (checkPay != null) {
                        return checkPay;
                    }

                    // 查询款方、入款方钱包
                    SelectOpenRoleVO openRoleVO = new SelectOpenRoleVO();
                    openRoleVO.setCompanyId(pay.getCompanyId());
                    openRoleVO.setCarrierId(pay.getCarrierId());
                    openRoleVO.setChannelId(BankNameEnum.HXBANK.key());
                    ResultUtil openRoleWallet = hxPayOrderUtil.selectOpenRoleWallet(openRoleVO);
                    if (null == openRoleWallet || DictEnum.ERROR.code.equals(openRoleWallet.getCode())) {
                        return ResultUtil.error("支付失败，请联系运营平台予以解决。");
                    }
                    OpenRoleWalletListDTO walletListDTO = JSONUtil.toBean(JSONUtil.toJsonStr(openRoleWallet.getData()), OpenRoleWalletListDTO.class);
                    pay.setCompanyWalletId(walletListDTO.getCompanyWallet().getId());
                    pay.setCarrierWalletId(walletListDTO.getCarrierWallet().getId());
                    // 查询司机开户、钱包
                    List<Integer> payList = Arrays.asList(pay.getList());
                    List<Integer> endDriverOpenOrder = orderZtAccountOpenInfoService.selectEndDriverOpenOrder(payList);
                    if (endDriverOpenOrder.isEmpty()) {
                        return ResultUtil.error("司机未开户或未开通支付，请联系运营平台予以解决。");
                    }
                    List<Integer> endDriverWalletOrder = orderZtAccountOpenInfoService.selectEndDriverWalletOrder(endDriverOpenOrder);
                    if (endDriverWalletOrder.isEmpty()) {
                        return ResultUtil.error("司机未开户或未开通支付，请联系运营平台予以解决。");
                    }
                    // 合并司机已开户且钱包存在的运单
                    List<Integer> list = new ArrayList<>(endDriverWalletOrder);
                    // 查询车队长未开户或钱包不存在的运单
                    List<Integer> captionOpenOrder = orderZtAccountOpenInfoService.selectCaptionNotOpenOrder(list);
                    List<Integer> captionWalletOrder = orderZtAccountOpenInfoService.selectCaptionNoWalletOrder(list);

                    // 合并车队长未开户和钱包不存在的运单
                    List<Integer> unionList = Stream.concat(captionOpenOrder.stream(), captionWalletOrder.stream())
                            .distinct()
                            .collect(Collectors.toList());
                    // 从司机已开户且钱包存在的运单中移除车队长未开户和钱包不存在的运单
                    list.removeAll(unionList);
                    if (list.isEmpty()) {
                        return ResultUtil.error("司机\\车队长未开户或未开通支付，请联系运营平台予以解决。");
                    }
                    Integer[] array = list.toArray(new Integer[0]);
                    pay.setList(array);
                    orderPayService.batchPay(pay);

                    // 如果有未支付运单，返回未支付运单
                    List<Integer> selectOrderIdList = new ArrayList<>(Arrays.asList(selectedOrderIds));
                    selectOrderIdList.removeAll(new ArrayList<>(Arrays.asList(array)));
                    HashSet<Integer> integerHashSet = new HashSet<>(selectOrderIdList);
                    if (!integerHashSet.isEmpty()) {
                        List<String> selectOrderBusinessCodes = orderPayService.selectOrderBusinessCodes(integerHashSet.toArray(new Integer[0]));
                        return ResultUtil.error("未支付运单: " + StringUtils.join(selectOrderBusinessCodes, "、"));
                    }

                } catch (Exception e) {
                    log.error("批量支付失败, {}", ThrowableUtil.getStackTrace(e));
                    String message = e.getMessage();
                    if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                        message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再支付。";
                        return ResultUtil.error(message);
                    }
                    return ResultUtil.error("支付失败");
                } finally {
                    if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            } else {
                return ResultUtil.error("企业正在支付，请稍后再试");
            }

        } catch (Exception e) {
            log.error("批量支付失败, {}", ThrowableUtil.getStackTrace(e));
            return ResultUtil.error("支付失败");
        }

        return ResultUtil.ok();
    }

    // @PostMapping("/importBatchPay")
    public ResultUtil<?> importBatchPay(@RequestParam("file") MultipartFile file, OrderBatchPay pay, HttpServletRequest request) {
        try {
            if (null == file || file.isEmpty() || file.getSize() == 0) {
                return ResultUtil.error("请选择文件");
            }
            OrderBatchPay orderBatchPay = new OrderBatchPay();
            BeanUtils.copyProperties(pay, orderBatchPay);
            orderBatchPay.setPayPassword(null);
            log.info("支付参数: {}", JSONUtil.toJsonStr(orderBatchPay));

            Set<String> orderBusinessCodes = readExcelFile(file.getInputStream());
            if (orderBusinessCodes.isEmpty()) {
                return ResultUtil.error("请选择运单");
            }
            if (orderBusinessCodes.size() > 1000) {
                return ResultUtil.error("最多选择1000条运单");
            }
            Integer companyId = null;
            if (!CurrentUser.getUserCompanyIdInteger().isEmpty()) {
                companyId = CurrentUser.getUserCompanyIdInteger().get(0);
            }
            if (null == companyId) {
                return ResultUtil.error("当前用户无企业信息");
            }
            Integer[] orderIds = orderPayService.selectOrderIds(orderBusinessCodes, companyId);
            if (orderIds.length == 0) {
                return ResultUtil.error("未查询到当前企业可支付运单");
            }
            pay.setList(orderIds);
            orderIds = orderPayService.commonPatternUnique(pay.getList());
            if (orderIds.length == 0) {
                return ResultUtil.error("当前不支持经纪人模式");
            }
            pay.setList(orderIds);
            Integer companyUnique = orderPayService.companyUnique(pay.getList());
            if (companyUnique != 1) {
                return ResultUtil.error("请选择同一企业运单");
            }
            Integer carrierUnique = orderPayService.carrierUnique(pay.getList());
            if (carrierUnique != 1) {
                return ResultUtil.error("请选择同一承运方运单");
            }
            TOrderInfo tOrderInfo = orderInfoService.selectByPrimaryKey(pay.getList()[0]);
            pay.setCompanyId(tOrderInfo.getCompanyId());
            pay.setCarrierId(tOrderInfo.getCarrierId());

            ResultUtil checkPay = payUtil.checkPay(request, tOrderInfo.getCompanyId(), pay.getMacAddress(),
                    tOrderInfo.getOrderBusinessCode(), pay.getPayPassword(), pay.getSourceChannel());
            if (checkPay != null) {
                return checkPay;
            }

            // 查询出款方、入款方会员编号
            SelectOpenRoleVO vo = new SelectOpenRoleVO();
            vo.setCarrierId(pay.getCarrierId());
            vo.setCompanyId(pay.getCompanyId());
            vo.setChannelId(BankNameEnum.HXBANK.key());
            ResultUtil openRoleStatus = hxPayOrderUtil.selectOpenRoleStatus(vo);
            if (DictEnum.ERROR.code.equals(openRoleStatus.getCode())) {
                return ResultUtil.error(openRoleStatus.getMsg());
            }
            String jsonStr = JSONUtil.toJsonStr(openRoleStatus.getData());
            OpenRoleStatusListDTO openRoleStatusListDTO = JSONUtil.toBean(jsonStr, OpenRoleStatusListDTO.class);
            pay.setOutPartnerAccId(openRoleStatusListDTO.getCompanyStatus().getPartnerAccId());
            pay.setInPartnerAccId(openRoleStatusListDTO.getCarrierStatus().getPartnerAccId());

            // 查询款方、入款方钱包
            SelectOpenRoleVO openRoleVO = new SelectOpenRoleVO();
            openRoleVO.setCompanyId(pay.getCompanyId());
            openRoleVO.setCarrierId(pay.getCarrierId());
            openRoleVO.setChannelId(BankNameEnum.HXBANK.key());
            ResultUtil openRoleWallet = hxPayOrderUtil.selectOpenRoleWallet(openRoleVO);
            if (null == openRoleWallet || DictEnum.ERROR.code.equals(openRoleWallet.getCode())) {
                return ResultUtil.error("支付失败，请联系运营平台予以解决。");
            }
            OpenRoleWalletListDTO walletListDTO = JSONUtil.toBean(JSONUtil.toJsonStr(openRoleWallet.getData()), OpenRoleWalletListDTO.class);
            pay.setCompanyWalletId(walletListDTO.getCompanyWallet().getId());
            pay.setCarrierWalletId(walletListDTO.getCarrierWallet().getId());
            // 查询司机开户、钱包
            List<Integer> payList = Arrays.asList(pay.getList());
            List<Integer> endDriverOpenOrder = orderZtAccountOpenInfoService.selectEndDriverOpenOrder(payList);
            if (endDriverOpenOrder.isEmpty()) {
                return ResultUtil.error("司机未开户或未开通支付，请联系运营平台予以解决。");
            }
            List<Integer> endDriverWalletOrder = orderZtAccountOpenInfoService.selectEndDriverWalletOrder(endDriverOpenOrder);
            if (endDriverWalletOrder.isEmpty()) {
                return ResultUtil.error("司机未开户或未开通支付，请联系运营平台予以解决。");
            }
            // 合并司机已开户且钱包存在的运单
            List<Integer> list = new ArrayList<>(endDriverWalletOrder);
            // 查询车队长未开户或钱包不存在的运单
            List<Integer> captionOpenOrder = orderZtAccountOpenInfoService.selectCaptionNotOpenOrder(list);
            List<Integer> captionWalletOrder = orderZtAccountOpenInfoService.selectCaptionNoWalletOrder(list);

            // 合并车队长未开户和钱包不存在的运单
            List<Integer> unionList = Stream.concat(captionOpenOrder.stream(), captionWalletOrder.stream())
                    .distinct()
                    .collect(Collectors.toList());
            // 从司机已开户且钱包存在的运单中移除车队长未开户和钱包不存在的运单
            list.removeAll(unionList);
            if (list.isEmpty()) {
                return ResultUtil.error("司机\\车队长未开户或未开通支付，请联系运营平台予以解决。");
            }
            pay.setList(list.toArray(new Integer[0]));

            String key = openRoleStatusListDTO.getCompanyStatus().getPartnerAccId();
            RLock lock = redissonClient.getLock(key);
            if (lock.tryLock()) {
                try {
                    RBucket<Object> bucket = redissonClient.getBucket("BATCH_PAY:" + key);
                    bucket.set(key);
                    bucket.expire(60, TimeUnit.SECONDS);
                    orderPayService.batchPay(pay);
                } catch (Exception e) {
                    log.error("批量支付失败, {}", ThrowableUtil.getStackTrace(e));
                    String message = e.getMessage();
                    if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                        message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再支付。";
                        return ResultUtil.error(message);
                    }
                    return ResultUtil.error("支付失败");
                } finally {
                    if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            } else {
                return ResultUtil.error("企业正在支付，请稍后再试");
            }

        } catch (IOException io) {
            return ResultUtil.error("文件读取失败");
        } catch (Exception e) {
            log.error("批量支付失败, {}", ThrowableUtil.getStackTrace(e));
            return ResultUtil.error("支付失败");
        }

        return ResultUtil.ok();
    }

    @PostMapping("/importOrderBusinessCodesBatchPay")
    public ResultUtil<?> importOrderBusinessCodesBatchPay(@RequestBody OrderBatchPay pay, HttpServletRequest request) {
        try {
            if (pay.getOrderBusinessCodes().isEmpty()) {
                return ResultUtil.error("请选择运单");
            }
            if (pay.getOrderBusinessCodes().size() > 1000) {
                return ResultUtil.error("最多选择1000条运单");
            }
            if (CurrentUser.getUserCompanyIdInteger().isEmpty()) {
                return ResultUtil.error("当前用户无企业信息");
            }
            Set<String> orderBusinessCodes = new HashSet<>(pay.getOrderBusinessCodes());
            Integer[] payList1 = orderPayService.selectOrderIdsByOrderBusinessCode(pay.getOrderBusinessCodes());
            if (null == payList1 || payList1.length == 0) {
                return ResultUtil.error("未查询到运单");
            }
            Integer companyUnique = orderPayService.companyUnique(payList1);
            if (companyUnique != 1) {
                return ResultUtil.error("请选择同一企业运单");
            }

            Integer[] orderIds = null;
            for (Integer id : CurrentUser.getUserCompanyIdInteger()) {
                orderIds = orderPayService.selectOrderIds(orderBusinessCodes, id);
                if (orderIds.length > 0) {
                    break;
                }
            }

            if (null == orderIds || orderIds.length == 0) {
                return ResultUtil.error("未查询到当前企业可支付运单");
            }

            ArrayList<Integer> integers = new ArrayList<>(Arrays.asList(payList1));
            // 获取运单状态错误的运单
            integers.removeAll(new ArrayList<>(Arrays.asList(orderIds)));

            TOrderInfo tOrderInfo = orderInfoService.selectByPrimaryKey(orderIds[0]);
            pay.setCompanyId(tOrderInfo.getCompanyId());
            pay.setCarrierId(tOrderInfo.getCarrierId());
            // 查询出款方、入款方会员编号
            SelectOpenRoleVO vo = new SelectOpenRoleVO();
            vo.setCarrierId(pay.getCarrierId());
            vo.setCompanyId(pay.getCompanyId());
            vo.setChannelId(BankNameEnum.HXBANK.key());
            ResultUtil openRoleStatus = hxPayOrderUtil.selectOpenRoleStatus(vo);
            if (DictEnum.ERROR.code.equals(openRoleStatus.getCode())) {
                return ResultUtil.error(openRoleStatus.getMsg());
            }
            String jsonStr = JSONUtil.toJsonStr(openRoleStatus.getData());
            OpenRoleStatusListDTO openRoleStatusListDTO = JSONUtil.toBean(jsonStr, OpenRoleStatusListDTO.class);
            String key = openRoleStatusListDTO.getCompanyStatus().getPartnerAccId();
            RLock lock = redissonClient.getLock(key);
            if (lock.tryLock()) {
                try {
                    if (redisUtil.hasKey("BATCH_PAY:" + key)) {
                        return ResultUtil.error("企业正在支付，请稍后再试");
                    }
                    redisUtil.set("BATCH_PAY:" + key, key, 10);
                    pay.setOutPartnerAccId(openRoleStatusListDTO.getCompanyStatus().getPartnerAccId());
                    pay.setInPartnerAccId(openRoleStatusListDTO.getCarrierStatus().getPartnerAccId());

                    pay.setList(orderIds);
                    orderIds = orderPayService.commonPatternUnique(pay.getList());
                    if (orderIds.length == 0) {
                        return ResultUtil.error("当前不支持经纪人模式");
                    }
                    pay.setList(orderIds);
                    Integer carrierUnique = orderPayService.carrierUnique(pay.getList());
                    if (carrierUnique != 1) {
                        return ResultUtil.error("请选择同一承运方运单");
                    }

                    ResultUtil checkPay = payUtil.checkPay(request, tOrderInfo.getCompanyId(), pay.getMacAddress(),
                            tOrderInfo.getOrderBusinessCode(), pay.getPayPassword(), pay.getSourceChannel());
                    if (checkPay != null) {
                        return checkPay;
                    }

                    // 查询款方、入款方钱包
                    SelectOpenRoleVO openRoleVO = new SelectOpenRoleVO();
                    openRoleVO.setCompanyId(pay.getCompanyId());
                    openRoleVO.setCarrierId(pay.getCarrierId());
                    openRoleVO.setChannelId(BankNameEnum.HXBANK.key());
                    ResultUtil openRoleWallet = hxPayOrderUtil.selectOpenRoleWallet(openRoleVO);
                    if (null == openRoleWallet || DictEnum.ERROR.code.equals(openRoleWallet.getCode())) {
                        return ResultUtil.error("支付失败，请联系运营平台予以解决。");
                    }
                    OpenRoleWalletListDTO walletListDTO = JSONUtil.toBean(JSONUtil.toJsonStr(openRoleWallet.getData()), OpenRoleWalletListDTO.class);
                    pay.setCompanyWalletId(walletListDTO.getCompanyWallet().getId());
                    pay.setCarrierWalletId(walletListDTO.getCarrierWallet().getId());

                    // 查询司机开户、钱包
                    List<Integer> payList = Arrays.asList(pay.getList());
                    List<Integer> endDriverOpenOrder = orderZtAccountOpenInfoService.selectEndDriverOpenOrder(payList);
                    if (endDriverOpenOrder.isEmpty()) {
                        return ResultUtil.error("司机未开户或未开通支付，请联系运营平台予以解决。");
                    }
                    List<Integer> endDriverWalletOrder = orderZtAccountOpenInfoService.selectEndDriverWalletOrder(endDriverOpenOrder);
                    if (endDriverWalletOrder.isEmpty()) {
                        return ResultUtil.error("司机未开户或未开通支付，请联系运营平台予以解决。");
                    }
                    // 合并司机已开户且钱包存在的运单
                    List<Integer> list = new ArrayList<>(endDriverWalletOrder);
                    // 查询车队长未开户或钱包不存在的运单
                    List<Integer> captionOpenOrder = orderZtAccountOpenInfoService.selectCaptionNotOpenOrder(list);
                    List<Integer> captionWalletOrder = orderZtAccountOpenInfoService.selectCaptionNoWalletOrder(list);

                    // 合并车队长未开户和钱包不存在的运单
                    List<Integer> unionList = Stream.concat(captionOpenOrder.stream(), captionWalletOrder.stream())
                            .distinct()
                            .collect(Collectors.toList());
                    // 从司机已开户且钱包存在的运单中移除车队长未开户和钱包不存在的运单
                    list.removeAll(unionList);
                    if (list.isEmpty()) {
                        return ResultUtil.error("司机\\车队长未开户或未开通支付，请联系运营平台予以解决。");
                    }
                    Integer[] array = list.toArray(new Integer[0]);
                    pay.setList(array);
                    // 查询已签订合同运单
                    List<Integer> signOrderIds = orderContractService.selectSignContractByOrderId(Arrays.asList(array));
                    if (!signOrderIds.isEmpty()) {
                        orderPayService.batchPay(pay);
                    } else {
                        return ResultUtil.error("请选择可支付运单");
                    }

                    // 如果有未支付运单，返回未支付运单
                    List<String> orderBusinessCodeList = pay.getOrderBusinessCodes();
                    List<String> selectOrderBusinessCodes = orderPayService.selectOrderBusinessCodes(array);
                    orderBusinessCodeList.removeAll(selectOrderBusinessCodes);
                    // 运单状态错误的运单
                    if (!integers.isEmpty()) {
                        List<String> selectOrderBusinessCodes1 = orderPayService.selectOrderBusinessCodes(integers.toArray(new Integer[0]));
                        if (!selectOrderBusinessCodes1.isEmpty()) {
                            orderBusinessCodeList.addAll(selectOrderBusinessCodes1);
                        }
                    }
                    HashSet<String> stringHashSet = new HashSet<>(orderBusinessCodeList);
                    if (!stringHashSet.isEmpty()) {
                        return ResultUtil.error("未支付运单: " + String.join("、", stringHashSet));
                    }
                } catch (Exception e) {
                    log.error("批量支付失败", e);
                    log.error("批量支付失败, {}", ThrowableUtil.getStackTrace(e));
                    String message = e.getMessage();
                    if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                        message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再支付。";
                        return ResultUtil.error(message);
                    }
                    return ResultUtil.error("支付失败");
                } finally {
                    if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            } else {
                return ResultUtil.error("企业正在支付，请稍后再试");
            }
        } catch (Exception e) {
            log.error("批量支付失败", e);
            log.error("批量支付失败, {}", ThrowableUtil.getStackTrace(e));
            return ResultUtil.error("支付失败");
        }
        return ResultUtil.ok();
    }

    public Set<String> readExcelFile(InputStream inputStream) throws IOException {
        try {
            // 创建工作簿实例
            Workbook workbook = WorkbookFactory.create(inputStream);
            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            Set<String> set = new HashSet<>();
            // 遍历每一行
            for (Row row : sheet) {
                // 遍历每一个单元格
                for (Cell cell : row) {
                    // 根据单元格类型来处理数据
                    if (Objects.requireNonNull(cell.getCellTypeEnum()) == CellType.STRING) {
                        if (StringUtils.isNotBlank(cell.getStringCellValue())) {
                            set.add(cell.getStringCellValue());
                        }
                    }
                }
            }
            workbook.close();
            return set;
        } catch (Exception e) {
            throw new IOException();
        }
    }

}
