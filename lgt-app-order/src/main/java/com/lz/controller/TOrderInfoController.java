package com.lz.controller;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lz.api.CompanyProjectAPI;
import com.lz.api.EnduserCarRelAPI;
import com.lz.common.config.RedisUtil;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.CodeEnum;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.common.util.ThrowableUtil;
import com.lz.model.*;
import com.lz.schedule.api.TTaskAPI;
import com.lz.schedule.model.TTask;
import com.lz.schedule.model.TTaskHistory;
import com.lz.service.*;
import com.lz.sms.service.SmsClientService;
import com.lz.util.SendOrderUtil;
import com.lz.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

/**
 *  @author: yan
 *  @Date: 2019/6/28 14:55
 *  @Description: 运单主表类
 */
@Slf4j
@RestController
@RequestMapping("/tOrderInfo")
public class TOrderInfoController {

    @Autowired
    private TOrderInfoService tOrderInfoService;

    @Autowired
    private PcOrderManagerService pcOrderManagerService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private SmsClientService smsClientService;

    @Autowired
    private TOrderCastChangesService orderCastChangesService;

    @Autowired
    private TTaskAPI taskAPI;

    @Autowired
    private EnduserCarRelAPI endUserCar;

    @Autowired
    private SendOrderUtil sendOrderUtil;

    @Autowired
    private TOrderPayInfoService orderPayInfoService;

    @Autowired
    private CompanyProjectAPI companyProjectAPI;

    @Autowired
    private TOrderInfoSearchService tOrderInfoSearchService;

    @Autowired
    private RedissonClient redissonClient;

    @Resource
    private OrderAsyncService orderAsyncService;

    @Resource
    private TOrderBatchImportPushContractService orderBatchImportPushContractService;

    @Resource
    private TOrderInfoExportService orderInfoExportService;


    /**
     * PC端 查询运单
     * Yan
     * @return
     */
    @PostMapping("/selectByPage")
    public ResultUtil selcetOrderAll(@RequestBody AppOrderSearchVO search) {
        ResultUtil resultUtil = null;
        try {
            resultUtil = tOrderInfoSearchService.selectOrderByUserType(search);
        } catch(Exception e){
            log.error("运单查询失败{}", ThrowableUtil.getStackTrace(e));
            log.error("运单查询失败!查询参数：" + search.toString() + "错误信息：", e);
            return ResultUtil.error("运单查询失败!");
        }
        return  resultUtil;
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/6/7 10:32
     *  @Description: 运单跟踪合计
     */
    @PostMapping("/selectByYdgzSum")
    public ResultUtil selectByYdgzSum(@RequestBody AppOrderSearchVO search) {
        ResultUtil resultUtil = null;
        try {
            resultUtil = tOrderInfoSearchService.selectByYdgzSum(search);
        } catch(Exception e){
            log.error("运单合计查询失败", e);
            log.error("运单合计查询失败!查询参数：" + search.toString() + "错误信息：", e);
            return ResultUtil.error("运单合计查询失败!");
        }
        return  resultUtil;
    }



    /**
     * PC端查询运单审核
     * hwt
     * @return
     */
    @PostMapping("/selectOrderJUDGEByUserType")
    public ResultUtil selectOrderJUDGEByUserType(@RequestBody AppOrderSearchVO search) {
        ResultUtil resultUtil = null;
        try {
            resultUtil = tOrderInfoService.selectOrderJUDGEByUserType(search);
        } catch (RuntimeException e) {
            return ResultUtil.error(e.getMessage());
        }catch(Exception e){
            log.error("ZJJ-015:运单审核查询失败!查询参数：" + search.toString() + "错误信息：", e);
            return ResultUtil.error("ZJJ-015:运单审核查询失败!");
        }
        return  resultUtil;
    }

    /**
     * PC端查询运单检查
     * hwt
     * @return
     */
    @PostMapping("/selectOrderCheckByUserType")
    public ResultUtil selectOrderCheckByUserType(@RequestBody AppOrderSearchVO search) {
        ResultUtil resultUtil = null;
        try {
            resultUtil = tOrderInfoService.selectOrderCheckByUserType(search);
        } catch (RuntimeException e) {
            return ResultUtil.error(e.getMessage());
        }catch(Exception e){
            log.error("ZJJ-016:运单检查查询失败!查询参数：" + search.toString() + "错误信息：", e);
            return ResultUtil.error("ZJJ-016:运单检查查询失败!");
        }
        return  resultUtil;
    }

    /**
     * PC端查询跟踪运单
     * hwt
     * @return
     */
    @PostMapping("/selectOrderTrackByUserType")
    public ResultUtil selectOrderTrackByUserType(@RequestBody AppOrderSearchVO search) {
        ResultUtil resultUtil = null;
        try {
            resultUtil = tOrderInfoService.selectOrderTrackByUserType(search);
        } catch (RuntimeException e) {
            return ResultUtil.error(e.getMessage());
        }catch(Exception e){
            log.error("ZJJ-017:跟踪运单查询失败!查询参数：" + search.toString() + "错误信息：", e);
            return ResultUtil.error("ZJJ-017:跟踪运单查询失败!");
        }
        return  resultUtil;
    }

    /**
     * PC端 查询运单详情
     * Yan
     * @return
     */
    @PostMapping("/nodeOrderDetail")
    public ResultUtil selectnodeOrderDetail(@RequestBody AppOrderSearchVO search) {
        try {
            ResultUtil resultUtil = pcOrderManagerService.selectNodeOrderDetailed(search.getCode(), search.getPaymentPlatforms());
            return resultUtil;
        } catch (RuntimeException e) {
            return ResultUtil.error(e.getMessage());
        } catch (Exception e) {
            log.error("节点运单详情查询失败!查询参数：" + search.toString() + "错误信息：", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            }
            return ResultUtil.error("节点运单详情查询失败!");
        }
    }

    /**
     * PC端 查询运单详情
     * Yan
     * @return
     */
    @PostMapping("/orderDetail")
    public ResultUtil selectOrderDetail(@RequestBody AppOrderSearchVO search) {
        try {
            ResultUtil resultUtil = pcOrderManagerService.selectOrderDetailed(search.getCode());
            return resultUtil;
        } catch (Exception e) {
            log.error("运单详情查询失败!查询参数：{}", JSONUtil.toJsonStr(search.toString()));
            log.error("运单详情查询失败!, {}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            }
            return ResultUtil.error("运单详情查询失败!");
        }
    }

    /**
     * PC端 获取运单轨迹
     * Yan
     */
    @PostMapping("/orderTrajectory")
    public ResultUtil selectOrderTrajectory(@RequestBody AppOrderSearchVO search){
        try {
            return pcOrderManagerService.selectOrderTrajectory(search.getCode(), true);
        } catch (Exception e){
            log.error("ZJJ019:PC端获取运单轨迹失败!查询参数：" + search.toString() + "错误信息：", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            }
            return ResultUtil.error("ZJJ019:PC端获取运单轨迹失败!");
        }
    }

    /**
     * playwright查询运单轨迹
     * @param search
     * @return
     */
    @PostMapping("/pcd/trajectory")
    public ResultUtil trajectory(@RequestBody AppOrderSearchVO search){
        try {
            ResultUtil resultUtil = pcOrderManagerService.selectOrderDetailed(search.getCode());
            ResultUtil resultUtil2 = pcOrderManagerService.selectOrderTrajectory(search.getCode(), true);
            Map<String, Object> map = new HashMap<>();
            map.put("orderDetail", resultUtil.getData());
            map.put("trajectories", resultUtil2.getData());
            return ResultUtil.ok(map);
        } catch (Exception e){
            log.error("ZJJ019:PC端获取运单轨迹失败!查询参数：" + search.toString() + "错误信息：", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            }
            return ResultUtil.error("ZJJ019:PC端获取运单轨迹失败!");
        }
    }

    /**
     * app端 获取运单轨迹
     * Yan
     */
    @PostMapping("/appOrderTrajectory")
    public ResultUtil appOrderTrajectory(@RequestBody AppOrderSearchVO search) {
        try {
            return pcOrderManagerService.selectOrderTrajectory(search.getCode(), false);
        } catch (Exception e){
            log.error("YL-020:APP端获取运单轨迹失败!查询参数：" + search.toString() + "错误信息：", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            }
            return ResultUtil.error("YL-020:APP端获取运单轨迹失败!");
        }
    }

    /**
     * PC端 运营统计
     * hwt
     * @return
     */
    @PostMapping("/orderStatistics")
    public ResultUtil selectOrderStatistics(@RequestBody TOrderStatisticsVO search) {
        try{
            ResultUtil resultUtil = pcOrderManagerService.selectOrderStatistics(search);
            return resultUtil;
        }catch (RuntimeException e) {
            return ResultUtil.error(e.getMessage());
        } catch (Exception e) {
            log.error("ZJJ-023:运营统计查询失败!查询参数：" + search.toString() + "错误信息：", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            }
            return ResultUtil.error("ZJJ-023:运营统计查询失败!");
        }
    }

    /**
     * 运单过程管理-运单检查-上传磅单： 修改运单信息
     * Yan
     * @param detail
     * @return
     */
    @PostMapping("/updateOrderExamine")
    public ResultUtil updateOrderExamine(@RequestBody OrderDetailVO detail) {
        String orderKey = "";
        Boolean orderLock = false;
        try {
            orderKey = "Order" + detail.getCode();
            if (!redisUtil.hasKey(orderKey)) {
                orderLock = redisUtil.lock(orderKey);
            }
            if (orderLock) {
                TOrderInfo tOrderInfo = tOrderInfoService.selectOrderInfoByCode(detail.getCode());
                if (!detail.getUpdateEndAgent()) {
                    if (tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M10.code)
                            || tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M20.code)
                            || tOrderInfo.getOrderExecuteStatus().equals(DictEnum.O060.code)
                            || tOrderInfo.getOrderPayStatus().equals(DictEnum.P070.code)
                            || tOrderInfo.getOrderPayStatus().equals(DictEnum.M080.code)
                            || tOrderInfo.getOrderPayStatus().equals(DictEnum.M090.code)
                            || tOrderInfo.getOrderPayStatus().equals(DictEnum.M095.code)
                            || tOrderInfo.getOrderPayStatus().equals(DictEnum.P110.code)
                            || tOrderInfo.getOrderPayStatus().equals(DictEnum.M120.code)
                            || tOrderInfo.getOrderPayStatus().equals(DictEnum.M130.code)
                    ) {
                        return ResultUtil.error("运单状态错误，请核对后再进行操作");
                    }
                } else {
                    if (tOrderInfo.getOrderPayStatus().equals(DictEnum.P110.code)
                            || tOrderInfo.getOrderPayStatus().equals(DictEnum.M130.code)) {
                        return ResultUtil.error("运单状态错误，请核对后再进行操作");
                    }
                }
                return tOrderInfoService.updateExamine(detail);
            } else {
                return ResultUtil.error("该运单正在进行其他操作，请核对后再进行操作");
            }
        } catch (Exception e){
            log.error("ZJJ-024:运单检查失败!查询参数：" + detail.toString() + "错误信息：", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            }
            return ResultUtil.error("ZJJ-024:运单检查失败!");
        } finally {
            if (redisUtil.hasKey(orderKey) && orderLock) {
                redisUtil.del(orderKey);
            }
        }
    }


    /** 运单过程管理-运单检查-补车辆轨迹
    * @Description
    * <AUTHOR>
    * @Date   2020/1/2 9:46
    * @Param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/supplementCarTrajectory")
    public ResultUtil supplementCarTrajectory(@RequestBody TOrderInfoVO detail) {
        try {
            return tOrderInfoService.supplementCarTrajectory(detail);
        } catch (Exception e){
            log.error("运单检查失败补充运单轨迹失败", e);
            return ResultUtil.error("运单检查失败补充运单轨迹失败");
        }
    }

    /**
     * 运单过程管理-运单检查-上传磅单： 回显运单检查的信息
     * Yan
     * @param search
     * @return
     */
    @PostMapping("/orderExamine")
    public ResultUtil orderExamine(@RequestBody AppOrderSearchVO search) {
        try {
            return tOrderInfoService.orderExamineInfo(search.getCode());
        } catch (Exception e){
            log.error("ZJJ-025:运单检查查询失败!查询参数：" + search.toString() + "错误信息：", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            }
            return ResultUtil.error("ZJJ-025:运单检查查询失败!");
        }
    }

    /**
     * 运单管理-收单: 根据32CODE查询运单基础信息
     * Yan
     * @param search
     * @return
     */
    @PostMapping("/acceptOrder")
    public ResultUtil acceptOrder(@RequestBody AppOrderSearchVO search) {
        try {
            return pcOrderManagerService.acceptOrder(search.getCode());
        } catch (RuntimeException e) {
            return ResultUtil.error(e.getMessage());
        } catch (Exception e) {
            log.error("ZJJ-026:运单详情查询失败!参数：" + search.toString() + "错误信息：", e);
            return ResultUtil.error("ZJJ-026:运单详情查询失败!");
        }

    }
    /**
     * 运单管理-收单-重新收单: PC根据32CODE查询运单基础信息 第一次收单
     * Yan
     * @param search
     * @return
     */
    @PostMapping("/incomOrder")
    public ResultUtil incomOrder(@RequestBody AppOrderSearchVO search) {
        try {
            return pcOrderManagerService.pcIncomOrder(search.getCode());
        } catch (Exception e){
            log.error("ZJJ-027:运单详情查询失败!查询参数：" + search.toString() + "错误信息：", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            }
            return ResultUtil.error("ZJJ-027:运单详情查询失败!");
        }
    }

    /**
    * @Description 支付详情
    * <AUTHOR>
    * @Date   2019/6/12 14:44
    * @Param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/payOrderDetail")
    public ResultUtil payOrderDetail(@RequestBody AppOrderSearchVO search){
        try {
            ResultUtil resultUtil = pcOrderManagerService.payOrderDetail(search.getCode(), search.getPaymentPlatforms());
            return resultUtil;
        } catch (Exception e){
            log.error("ZJJ-028:支付详情查询失败!查询参数：" + search.toString() + "错误信息：", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            }
            return ResultUtil.error("ZJJ-028:支付详情查询失败!");
        }

    }

    /**
     * PC端：未签合同发送短信通知
     * Yan
     * @return
     */
    @PostMapping("/sendText")
    public ResultUtil noSignContractSendText(@RequestBody AppOrderSearchVO search) {
        try {
            return tOrderInfoService.noSignContractSendText(search);
        } catch (Exception e){
            log.error("YL-029:未签合同发送短信通知!查询参数：" + search.toString() + "错误信息：" , e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            }
            return ResultUtil.error("YL-029:未签合同发送短信通知!");
        }
    }

    /**
     * PC端：未签合同上传合同照
     * Yan
     * @param asv
     * @return
     */
    @PostMapping(value = "/uploadContract")
    public ResultUtil uploadContract(@RequestBody AppOrderSearchVO asv) {
        try {
            ResultUtil resultUtil = tOrderInfoService.noSignContractUpload(asv.getCode(), asv.getFiles(), asv.getClientType(), asv.getContractId());
            return resultUtil;
        } catch (Exception e){
            log.error("ZJJ-030:未签合同上传合同照!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("ZJJ-030:未签合同上传合同照!");
            }
        }
    }


    /**
     * PC端：查询未签合同的运单列表
     * Yan
     * @return
     */
    @PostMapping("/noSign")
    public ResultUtil selectNoSignOrder(@RequestBody PcOrderSearchVO search) {
        try {
            return tOrderInfoService.selectNoSignOrder(search);
        } catch (Exception e){
            log.error("ZJJ-031:查询未签合同的运单列表失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("ZJJ-031:查询未签合同的运单列表失败!");
            }
        }
    }


 /**
  *  @author: dingweibo
  *  @Date: 2019/6/28 14:56
  *  @Description: 运单导出excel
  */
    @PostMapping("/exportExcel")
    public ResultUtil exportExcel(AppOrderSearchVO search,HttpServletResponse response){
        ResultUtil resultUtil = new ResultUtil();
        try {
            // 防止中文乱码
            // 设置response参数，可以打开下载页面
            String[] headers =
                    { "运单号", "司机姓名", "司机手机号", "车牌号", "企业","承运方","企业联系人",
                            "企业联系电话","出发地","目的地","货物类型","运单状态","完成时间","原发重量","实收重量","运单支付状态"};
            String[] names = {"orderBusinessCode","realName","phone","vehicleNumber","companyName","carrierName","companyContacts",
                    "companyContactsPhone","fromName","endName","goodsName","pageShowCode","orderFinishTime","primaryWeight","dischargeWeight","orderPayStatus"};
            List<OrderInfoExcelVo> tOrderInfos = tOrderInfoService.selectExcelOrder(search);
            Map<String,Object> map = new HashMap<>();
            map.put("headers",headers);
            map.put("names",names);
            map.put("list",tOrderInfos);
            resultUtil.setData(map);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return  resultUtil;
    }

    /**
     * PC端 获取运单合同
     * Yan
     * @return
     */
    @PostMapping("/orderContract")
    public ResultUtil selectOrderContract(@RequestBody AppOrderSearchVO search) {
        try {
            return pcOrderManagerService.selectOrderContract(search.getCode());
        } catch (Exception e){
            log.error("获取运单合同失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("获取运单合同失败!");
            }
        }
    }

    /**
     * PC端 获取运单资金变动流程
     * Yan
     * @return
     */
    @PostMapping("/fundChanges")
    public ResultUtil selectOrderFundChanges(@RequestBody AppOrderSearchVO search) {
        try {
            List<TOrderPayInfo> tOrderPayInfos = orderPayInfoService.selectByOrderCode(search.getCode());
            if (!tOrderPayInfos.isEmpty()) {
                TOrderPayInfo tOrderPayInfo = tOrderPayInfos.get(0);
                if (null != tOrderPayInfo.getParam1() && ("1".equals(tOrderPayInfo.getParam1()) || "2".equals(tOrderPayInfo.getParam1()) || "3".equals(tOrderPayInfo.getParam1()))) {
                    return pcOrderManagerService.selectRoyaltyOrderFundChanges(search.getCode());
                }
            }
            return pcOrderManagerService.selectOrderFundChanges(search.getCode());
        } catch (Exception e){
            log.error("ZJJ-033:获取运单资金变动流程失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("ZJJ-033:获取运单资金变动流程失败!");
            }
        }
    }

    /**
     * PC端 获取运单审核列表
     * Yan
     * @return
     */
    @PostMapping("/auditList")
    public ResultUtil selectOrderAuditList(@RequestBody PcOrderSearchVO search) {
        try {
            return tOrderInfoService.selectOrderAuditList(search);
        } catch (Exception e){
            log.error("ZJJ-034:获取运单审核列表失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("ZJJ-034:获取运单审核列表失败!");
            }
        }
    }

    /**
     * PC端 获取运单审核
     * Yan
     * @return
     */
    @PostMapping("/auditLog")
    public ResultUtil orderAudit(@RequestBody TOrderAuditLog auditLog) {
        try {
            if (null != auditLog.getOrderBusinessCode()) {
                String key = "Order" + auditLog.getOrderBusinessCode();
                RLock lock = redissonClient.getLock(key);
                try {
                    boolean tryLock = lock.tryLock();
                    if (tryLock) {
                        if (null == auditLog.getOrderCode()) {
                            TOrderInfo tOrderInfo = tOrderInfoService.selectByOrderBusinessCode(auditLog.getOrderBusinessCode());
                            auditLog.setOrderCode(tOrderInfo.getCode());
                        }
                        return tOrderInfoService.orderAuditUpdate(auditLog);
                    } else {
                        return ResultUtil.error("当前运单正在审核中, 请稍后再试。");
                    }
                } catch (Exception e) {
                    log.error("获取运单审核失败, {}", ThrowableUtil.getStackTrace(e));
                    return ResultUtil.error("当前运单正在审核中, 请稍后再试。");
                } finally {
                    if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            } else {
                return tOrderInfoService.orderAuditUpdate(auditLog);
            }
        } catch (Exception e){
            log.error("获取运单审核失败, {}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("运单审核失败");
            }
        }
    }

    /**
     * @Description Fegin
     * @param code
     * @return java.lang.String
     * <AUTHOR>
     * @Date 2019/5/21 10:38
     *  return List<TOrderInfo> 的json字符串
     */
    @PostMapping("/selectByOrderCode")
    public String selectByOrderCode(@RequestParam(value = "code") String code){

        return JSON.toJSONString(tOrderInfoService.selectByOrderCode(code));
    }


    @PostMapping("/update")
    public ResultUtil update(@RequestBody TOrderInfo record) {

        return new ResultUtil("修改成功",tOrderInfoService.update(record));
    }

    @PostMapping("/edit")
    public ResultUtil edit(@RequestBody TOrderInfo record) {

        return new ResultUtil("修改成功",tOrderInfoService.update(record));
    }

    /**
     *  @author: dingweibo
     *  @Date: 2019/6/28 14:57
     *  @Description: 根据网商返回的支付code，查询运单 fegin
     */
    @RequestMapping("/selectByOuterTradeNo")
    public TOrderInfo selectByOuterTradeNo(@RequestParam(value="outerTradeNo") String outerTradeNo) {

        return tOrderInfoService.selectByOuterTradeNo(outerTradeNo);
    }

    @RequestMapping("/selectByOrderBusinessCode")
    public TOrderInfo selectByOrderBusinessCode(@RequestParam(value = "orderBusinessCode") String orderBusinessCode) {

        return tOrderInfoService.selectByOrderBusinessCode(orderBusinessCode);
    }
    /**
     *  @author: dingweibo
     *  @Date: 2019/6/28 14:58
     *  @Description: 根据网商流水号，查询承运方id
     */
    @RequestMapping("/selectByCarrierId")
    public String selectByCarrierId(@RequestParam(value="outerTradeNo") String outerTradeNo) {

        return tOrderInfoService.selectByCarrierId(outerTradeNo);
    }
    /**
     *  @author: dingweibo
     *  @Date: 2019/6/28 14:58
     *  @Description: 根据网商流水号，查询承运方公钥
     */
    @RequestMapping("/selectByThirdPartyInterfacePublicKey")
    public TOrderInfoVO selectByThirdPartyInterfacePublicKey(@RequestParam(value="outerTradeNo") String outerTradeNo) {

        return tOrderInfoService.selectByThirdPartyInterfacePublicKey(outerTradeNo);
    }


    /**
     *  @author: yan
     *  @Date: 2019/6/28 14:58
     *  @Description: 查询司机的运单个数
     */
    @PostMapping("/selectByEndDriverId")
    public Integer selectByEndDriverId(@RequestParam(value = "enduserinfoid") Integer enduserinfoid,@RequestParam(value = "status") String status
            , @RequestParam(value = "ctype") String ctype) {
        return tOrderInfoService.selectByEndDriverId(enduserinfoid,status, ctype);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2020/4/11 9:13 查询司机端待评价运单数
     *  @Description:
     */
    @PostMapping("/selectByNoAppraiseCount")
    public Integer selectByNoAppraiseCount(@RequestParam(value = "enduserinfoid") Integer enduserinfoid,@RequestParam(value = "status") String status
            , @RequestParam(value = "ctype") String ctype) {
        return tOrderInfoService.selectByNoAppraiseCount(enduserinfoid,status, ctype);
    }


    /**
     * PC端 查询可删除运单列表
     * <AUTHOR>
     * @return
     */
    @PostMapping("/selectOrderForDelete")
    public ResultUtil selectOrderForDelete(@RequestBody TOrderInfoVO orderInfo){
        //已建单
        orderInfo.setOrderExecuteStatus("M010");
        ResultUtil resultUtil = tOrderInfoService.selectOrder(orderInfo);
        return resultUtil;
    }

    /**
     * PC端 删除运单
     * @auth zhangjiji
     * @return
     */
    @PostMapping("/deleteOrder")
    public ResultUtil deleteOrder(@RequestBody TOrderInfoVO orderInfo){
        try {
            String verificationCode = orderInfo.getVerificationCode();
            String smsCode = String.valueOf(redisUtil.get("PCDELORDER" + orderInfo.getDeletePersonPhone()));
            ResultUtil resultUtil;
            if (verificationCode.equals(smsCode)){
                resultUtil = tOrderInfoService.deleteOrder(orderInfo);
            }else if ("null".equals(smsCode)){
                resultUtil = ResultUtil.error("验证码以失效");
            } else {
                resultUtil = ResultUtil.error("验证码错误");
            }
            return resultUtil;
        } catch (Exception e){
            log.error("ZJJ-036:运单删除失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("ZJJ-036:运单删除失败!");
            }
        }

    }

    /**
    * @Description 查询所有运单，根据订单编号
    * <AUTHOR>
    * @Date   2019/6/28 15:01
    * @Param  * @param null
    * @Return
    * @Exception
    *
    */
    @PostMapping("/selectByOrderCodeList")
    public List<TOrderInfo> selectByOrderCodeList(@RequestParam(value = "orderCodeStr") String orderCodeStr){
        return  tOrderInfoService.selectByOrderCodeList(orderCodeStr);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2019/6/28 15:02
     *  @Description:  财务系统使用，根据运单查询运单及企业承运方信息
     */
    @PostMapping("/selectByOrderCodeListName")
    public String selectByOrderCodeListName(@RequestParam(value = "orderCodeStr") String orderCodeStr){
        String  jsonStr = null;
        if(tOrderInfoService.selectByOrderCodeListName(orderCodeStr).size()>0){
            jsonStr = JSONObject.toJSONString(tOrderInfoService.selectByOrderCodeListName(orderCodeStr));
        }
        return  jsonStr;
    }

    /**
     *  @author: dingweibo
     *  @Date: 2019/6/28 15:02
     *  @Description:  财务系统使用，根据运单查询运单及企业承运方信息
     */
    @PostMapping("/selectByOrderCodeListNamePage")
    public ResultUtil selectByOrderCodeListNamePage(@RequestBody TOrderInfoVO tOrderInfoVO){
        return  tOrderInfoService.selectByOrderCodeListNamePage(tOrderInfoVO);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2019/6/28 15:02
     *  @Description:  财务系统使用，根据运单查询运单及企业承运方信息
     */
    @PostMapping("/selectByListName")
    public List<TOrderInfoVO>  selectByListName(@RequestBody TOrderInfoVO tOrderInfoVO){
        return  tOrderInfoService.selectByOrderCodeListName(tOrderInfoVO);
    }
    /**
    * @Description 根据运单编号查询运单详情 Fegin
    * <AUTHOR>
    * @Date   2019/6/13 19:03
    * @Param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/selectOrderInfoByOrderCode")
    public ResultUtil selectOrderInfoByOrderCode(@RequestBody TOrderInfoVO record){
        ResultUtil resultUtil = tOrderInfoService.selectOrderInfoByOrderCode(record);
        return resultUtil;
    }


    /**
    * @Description Fegin
    * @param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/selectOrderInfoByCode")
    public TOrderInfo selectOrderInfoByCode(String code){
        return  tOrderInfoService.selectOrderInfoByCode(code);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2019/6/18 14:44
     *  @Description: pc端运单提现
     *
     */
    @PostMapping("/withdraw")
    public ResultUtil withdraw(@RequestBody TOrderInfoVO tOrderInfoVO) {
        String driverWalletKey = "USER";
        String orderKey = "ORDER";
        Boolean driverWalletLock = false;
        Boolean orderLock = false;
        try {
            TOrderInfo orderInfo = tOrderInfoService.selectOrderInfoByCode(tOrderInfoVO.getCode());
            if (orderInfo.getUserConfirmPaymentAmount().compareTo(orderInfo.getUserConfirmServiceFee()) <= 0) {
                return ResultUtil.error("选中运单的服务费不能大于等于运费");
            }
            TEndCarInfoVO endCarInfoVO = new TEndCarInfoVO();
            endCarInfoVO.setUserId(orderInfo.getEndDriverId());
            HashSet<Integer> endCarId = new HashSet<>();
            endCarId.add(orderInfo.getVehicleId());
            endCarInfoVO.setEndCarId(endCarId);
            ResultUtil carAndDriverStatus = endUserCar.getCarAndDriverStatus(endCarInfoVO);
            if (carAndDriverStatus.getCode().equals("success")) {
                ArrayList<LinkedHashMap> data = (ArrayList) carAndDriverStatus.getData();
                if (data.size() > 0) {
                    StringBuilder msg = new StringBuilder();
                    for (LinkedHashMap datum : data) {
                        if ("driver".equals(datum.get("type"))) {
                            msg.append(datum.get("mark"));
                            msg.append(",");
                        } else {
                            msg.append(datum.get("mark"));
                            msg.append(",");
                        }
                    }
                    msg.append("未审核通过,暂不能提现。");
                    return ResultUtil.error(msg.toString());
                }
            }

            TOrderCastChanges tOrderCastChanges = orderCastChangesService.selectByNewOne(tOrderInfoVO.getCode());
            driverWalletKey = driverWalletKey + tOrderCastChanges.getEndDriverWalletId();
            orderKey = orderKey + tOrderInfoVO.getCode();

            if (!redisUtil.hasKey(driverWalletKey)) {
                driverWalletLock = redisUtil.lock(driverWalletKey);
            }
            if (!redisUtil.hasKey(orderKey)) {
                orderLock = redisUtil.lock(orderKey);
            }
            if (driverWalletLock && orderLock) {
                BigDecimal service = null == orderInfo.getUserConfirmServiceFee() ? BigDecimal.ZERO : orderInfo.getUserConfirmServiceFee();
                tOrderInfoVO.setUserConfirmPaymentAmount(orderInfo.getUserConfirmPaymentAmount().subtract(service));
                TCompanyProject tCompanyProject = companyProjectAPI.selectByCompanyProjectById(orderInfo.getCompanyProjectId());
                TBankCard bankCard = sendOrderUtil.checkCardholder(tOrderInfoVO.getBankId());
                //此项目是否限额
                if(tCompanyProject.getIfQuota()){
                    //判断 提现是否超额
                    sendOrderUtil.checkCardHolderWithdrawAmount(bankCard, tOrderInfoVO.getUserConfirmPaymentAmount());
                } else {
                    sendOrderUtil.checkCardHolderWithdrawAmount50Limit(bankCard, tOrderInfoVO.getUserConfirmPaymentAmount());
                }
                return tOrderInfoService.tixian(tOrderInfoVO);
            } else {
                return ResultUtil.error("当前运单正在处理中，请稍后再试");
            }
        } catch (Exception e) {
            log.error("ZJJ-048:运单提现失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("ZJJ-048:运单提现失败!");
            }
        } finally {
            if (driverWalletLock && redisUtil.hasKey(driverWalletKey)) {
                redisUtil.del(driverWalletKey);
            }
            if (orderLock && redisUtil.hasKey(orderKey)) {
                redisUtil.del(orderKey);
            }
        }
    }



    /**
    * @Description 根据员工查询订单
    * <AUTHOR>
    * @Date   2019/7/13 14:45
    * @Param  
    * @Return      
    * @Exception   
    * 
    */
    @PostMapping("/selectByAccountIdFeign")
    public List<TOrderInfo> selectByAccountIdFeign(@RequestParam(value = "accountId") Integer accountId){

        return tOrderInfoService.selectByAccountIdFeign(accountId);
    }

    /**
     * @Description 根据员工查询订单
     * <AUTHOR>
     * @Date   2019/7/13 14:45
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/selectByAccountIdAnrLineIdFeign")
    public List<TOrderInfo> selectByAccountIdAnrLineIdFeign(@RequestBody TOrderInfoVO record){

        return tOrderInfoService.selectByAccountIdAnrLineIdFeign(record);
    }

    /**
     * @Description 根据员工查询订单
     * <AUTHOR>
     * @Date   2019/7/13 14:45
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/selectByAccountIdAnrLineIdCountFeign")
    public ResultUtil selectByAccountIdAnrLineIdCountFeign(@RequestBody TOrderInfoVO record){
        ResultUtil resultUtil = new ResultUtil();
        resultUtil.setCount((long) tOrderInfoService.selectByAccountIdAnrLineIdCountFeign(record));
        return resultUtil;
    }
    @PostMapping("/selectByAccountIdAnrLineGoodsRelIdCountFeign")
    public ResultUtil selectByAccountIdAnrLineGoodsRelIdCountFeign(@RequestBody TOrderInfoVO record){
        ResultUtil resultUtil = new ResultUtil();
        resultUtil.setCount((long) tOrderInfoService.selectByAccountIdAnrLineGoodsRelIdCountFeign(record));
        return resultUtil;
    }

    /**
     * @Description 根据司机id查询订单
     * <AUTHOR>
     * @Date   2019/7/13 14:45
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/selectByEndDriverIdFeign")
    public List<TOrderInfo> selectByEndDriverIdFeign(@RequestParam(value = "endUserId") Integer endUserId){

        return tOrderInfoService.selectByEndDriverIdFeign(endUserId);
    }

    @PostMapping("/selectByEndDriverIdFeignHt")
    public Boolean selectByEndDriverIdFeignHt(@RequestParam(value = "endUserId") Integer endUserId){

        return tOrderInfoService.selectByEndDriverIdFeignHt(endUserId);
    }

    /**
     * @Description 根据司机id查询订单
     * <AUTHOR>
     * @Date   2019/7/13 14:45
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/selectByEndDriverIdAnrCarIdFeign")
    public List<TOrderInfo> selectByEndDriverIdAnrCarIdFeign(@RequestParam(value = "endUserId") Integer endUserId,@RequestParam(value = "endCarId") Integer endCarId){

        return tOrderInfoService.selectByEndDriverIdAnrCarIdFeign(endUserId,endCarId);
    }


    /**
     * @Description 根据车主id查询订单
     * <AUTHOR>
     * @Date   2019/7/13 14:45
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/selectByEndCarOwnerIdFeign")
    public List<TOrderInfo> selectByEndCarOwnerIdFeign(@RequestParam(value = "endUserId") Integer endUserId){

        return tOrderInfoService.selectByEndCarOwnerIdFeign(endUserId);
    }

    /**
     * 运单审核：修改运单信息（实发数量、实收数量、发货磅单、收货磅单）
     * hwt
     * @param detail
     * @return
     */
    @PostMapping("/updateOrderDetail")
    public ResultUtil updateOrderDetail(@RequestBody OrderDetailVO detail) {
        try {
            return tOrderInfoService.updateOrderDetail(detail);
        } catch (Exception e){
            log.error("ZJJ-037:修改运单信息失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("ZJJ-037:修改运单信息失败!");
            }
        }
    }

    /**
     * 根据线路货物关系Id查询运单
     * hwt
     * @param lineGoodsRelId
     * @return
     */
    @PostMapping("/selectByLineGoodsRelId")
    public List<TOrderInfo> selectByLineGoodsRelId(@RequestParam(value = "lineGoodsRelId") Integer lineGoodsRelId){
        return tOrderInfoService.selectByLineGoodsRelId(lineGoodsRelId);
    }

    /**
     * @Description 根据车辆id查询订单
     * <AUTHOR>
     * @Date   2019/7/24 19:45
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/selectByEndCarIdFeign")
    public List<TOrderInfo> selectByEndCarIdFeign(@RequestParam(value = "endCarId") Integer endCarId){

        return tOrderInfoService.selectByEndCarIdFeign(endCarId);
    }


    /**
     * @Description 根据车辆id查询订单
     * <AUTHOR>
     * @Date   2019/7/24 19:45
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/selectByEndCarIdAnrUserIdFeign")
    public List<TOrderInfo> selectByEndCarIdAnrUserIdFeign(@RequestParam(value = "endCarId") Integer endCarId,@RequestParam(value = "endUserId") Integer endUserId){

        return tOrderInfoService.selectByEndCarIdAnrUserIdFeign(endCarId,endUserId);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2019/7/25 17:19
     *  @Description: task任务失败的运单
     */
    @PostMapping("/selectTaskByOrderInfoPage")
    public ResultUtil selectTaskByOrderInfoPage(@RequestBody TOrderInfoVO record){
        try{
            return tOrderInfoService.selectTaskByOrderInfoPage(record);
        }catch (Exception e){
            log.error("dw-045: 运单异常记录查询失败！, {}", ThrowableUtil.getStackTrace(e));
            return ResultUtil.error("dw-045: 运单异常记录查询失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2019/7/30 8:58
     *  @Description: 失败运单重置任务
     */
    @PostMapping("/updateTaskById")
    public ResultUtil updateTaskById(@RequestParam Integer taskId){
        try{
            return tOrderInfoService.updateTaskById(taskId);
        }catch (Exception e){
            log.error(e.getMessage());
            return ResultUtil.error("dw-046:失败运单重置任务失败！");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2020/2/19 11:01
     *  @Description: 失败运单读取重置
     */
    @PostMapping("/updateTaskRequestParameter")
    public ResultUtil updateTaskRequestParameter(@RequestBody TOrderInfoVO tOrderInfoVO){
        try {
            return tOrderInfoService.updateTaskRequestParameter(tOrderInfoVO);
        }catch (Exception e){
            log.error(e.getMessage());
            e.printStackTrace();
            return ResultUtil.error("失败运单读取重置失败！");
        }
    }
    /**
    * @Description 运单检查excel导出
    * <AUTHOR>
    * @Date   2019/7/27 10:27
    * @Param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/ydjcExpro")
    public ResultUtil ydjcExpro(@RequestBody AppOrderSearchVO search){

        return tOrderInfoService.ydjcExpro(search);
    }

    /**
     * @Description 已支付实体运单excel导出
     * <AUTHOR>
     * @Date   2019/7/27 10:27
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/yzfxtydExpro")
    public ResultUtil yzfxtydExpro(@RequestBody AppOrderSearchVO search){

        return orderInfoExportService.yzfxtydExpro(search);
    }

    /**
     * PC端 运营统计导出
     * hwt
     * @return
     */
    @PostMapping("/orderStatisticsExcelExport")
    public ResultUtil orderStatisticsExcelExport(@RequestBody TOrderStatisticsVO search) {
        ResultUtil resultUtil = pcOrderManagerService.orderStatisticsExcelExport(search);
        return resultUtil;
    }


    /**
    * @Description 查询经纪人是否有未完成的运单: Fegin接口，勿修改勿使用
    * <AUTHOR>
    * @Date   2019/8/24 18:13
    * @param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/selectAgentManagerUnCompleteOrder")
    public ResultUtil selectAgentManagerUnCompleteOrder(@RequestBody List<HashMap<String, Integer>> record){
        ResultUtil resultUtil = tOrderInfoService.selectAgentManagerUnCompleteOrder(record);
        return resultUtil;
    }

    /**
     * @Description: 获取提现记录列表
     * @Author: Yan
     * @Date: 2019/9/18/018 8:41
     * @Param: search
     * @Return:
     */
    @PostMapping("/cashList")
    public ResultUtil getCashList(@RequestBody AppOrderSearchVO search) {
        return tOrderInfoService.getOrderCashList(search);
    }

    /*
     * <AUTHOR>
     * @Description 运单支付审核
     * @Date 2019/11/19 17:06
     * @Param
     * @return
    **/
    @PostMapping("/paymentAudit")
    public ResultUtil paymentAudit(@RequestBody TOrderInfoVO record) {
        try {
            if (null == record.getAuditStatus() || StringUtils.isEmpty(record.getAuditStatus())) {
                return ResultUtil.error("请选择审核状态");
            }
            if (null == record.getCodes() || record.getCodes().length == 0) {
                return ResultUtil.error("请选择运单");
            }
            ResultUtil resultUtil = tOrderInfoService.paymentAudit(record);
            return resultUtil;
        } catch (Exception e) {
            log.error("运单支付审核失败", e);
            return ResultUtil.error("运单支付审核失败");
        }
    }

    @PostMapping("/lineOrderExists")
    public ResultUtil judgeLineOrderExists(@RequestBody TOrderInfoVO record) {
        return tOrderInfoService.judgeLineExistsOrder(record.getLineId());
    }
    /** 咨询投诉调用 */
    @PostMapping("/feedbackInfo")
    public ResultUtil getOrderFeedbackInfo(@RequestParam("orderCode") String orderCode){
        return tOrderInfoService.getFeedbackOrderInfo(orderCode);
    }

    /*
     * <AUTHOR>
     * @Description 检验运单合格不合格
     * @Date 2019/12/26 17:36
     * @Param
     * @return
     **/
    @PostMapping("/orderUnqualifiedMark")
    public ResultUtil orderUnqualifiedMark(@RequestBody OrderDetailVO record) {
        Boolean orderLock = false;
        String orderKey = "Order";
        try {
            orderKey = orderKey + record.getCode();
            if (!redisUtil.hasKey(orderKey)) {
                orderLock = redisUtil.lock(orderKey);
            }
            if (orderLock) {
                return tOrderInfoService.orderUnqualifiedMark(record);
            } else {
                return ResultUtil.error("当前运单正在处理中，请稍后再试");
            }
        } catch (Exception e) {
            log.error("运单处理失败", e);
            return ResultUtil.error("运单处理失败");
        } finally {
            if (orderLock && redisUtil.hasKey(orderKey)) {
                redisUtil.del(orderKey);
            }
        }
    }


    /**
    * @Description 定时任务执行补运单轨迹
    * <AUTHOR>
    * @Date   2020/1/8 9:57
    * @Param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/carTrajectory")
    public void carTrajectory(@RequestBody TOrderInfoVO tOrderInfoVO){
        TTask task = taskAPI.selectById(Integer.parseInt(tOrderInfoVO.getTaskId()));
        ResultUtil resultUtil = tOrderInfoService.carTrajectory(tOrderInfoVO);
        try{
            if ("success".equals(resultUtil.getCode())) {
                //task表数据移到历史表
                TTaskHistory tTaskHistory = new TTaskHistory();
                tTaskHistory.setTaskHisId(IdWorkerUtil.getInstance().nextId());
                tTaskHistory.setTaskId(task.getTaskId());
                tTaskHistory.setTaskType(task.getTaskType());
                tTaskHistory.setTaskTypeNode(task.getTaskTypeNode());
                tTaskHistory.setBusinessType(task.getBusinessType());
                tTaskHistory.setSourceTablename(task.getSourceTablename());
                tTaskHistory.setSourcekeyFieldname(task.getSourcekeyFieldname());
                tTaskHistory.setSourceFieldname(task.getSourceFieldname());
                tTaskHistory.setSourceFieldvalue(task.getSourceFieldvalue());
                tTaskHistory.setRequestUrl(task.getRequestUrl());
                tTaskHistory.setRequestParameter(task.getRequestParameter());
                tTaskHistory.setRequestTimes(task.getRequestTimes());
                tTaskHistory.setRequestResult(task.getRequestResult());
                tTaskHistory.setErrorMessage(task.getErrorMessage());
                tTaskHistory.setRequestDate(task.getRequestDate());
                tTaskHistory.setDealTime(task.getDealTime());
                tTaskHistory.setIsSuccessed(task.getIsSuccessed());
                tTaskHistory.setToHisDate(task.getCreateTime());
                tTaskHistory.setToHisUserid(task.getCreateUser());
                tTaskHistory.setCreateTime(task.getCreateTime());
                tTaskHistory.setRemark(task.getRemark());
                taskAPI.addHis(tTaskHistory);
                taskAPI.deleteById(task.getId());
            } else {
                task.setRequestTimes(6);
                task.setParam1("zxwb");
                task.setErrorMessage(resultUtil.getMsg());
                taskAPI.update(task);
            }
        } catch (Exception e) {
            task.setRequestTimes(6);
            task.setParam1("zxwb");
            task.setErrorMessage(resultUtil.getMsg());
            taskAPI.update(task);
            log.error("定时任务执行补运单轨迹失败", e);
        }
    }

    /**
     * @Description: 企业PC增加[已提现实体运单]
     * @Author: Yan
     * @Date: 2020/1/2/002
     */
    @PostMapping("/extractOrder")
    public ResultUtil ExtractMoneyOrder(@RequestBody AppOrderSearchVO search) {
        return tOrderInfoService.getExtractMoneyOrder(search);
    }
    /**
     * @Description: 企业PC增加[已提现实体运单] 导出
     * @Author: liu
     * @Date: 2020/2/21
     */
    @PostMapping("/extractOrderEXCEL")
    public ResultUtil extractOrderEXCEL(@RequestBody AppOrderSearchVO search) {
        return tOrderInfoService.getExtractMoneyOrderEXCEL(search);
    }
    /**
     * 批量推送提醒微信补签协议
     */
    @PostMapping("/sendWxHtList")
    public ResultUtil sendWxHtList(@RequestBody List<TOrderInfoVO> records) {
        try{
            List<Map<String,Object>> mapList = new ArrayList<>();
            List<TOrderInfoVO> resultrecords = new ArrayList<>();
            for (TOrderInfoVO record : records){
                String key = "wxbq"+record.getCode();
                if (redisUtil.hasKey(key)){
                    Map<String ,Object> map = new HashMap<>();
                    map.put("orderCode",record.getOrderBusinessCode());
                    map.put("code","error");
                    map.put("msg","失败: 该运单1小时内已推送过补签协议，请稍后再推送");
                    mapList.add(map);
                }else {
                    redisUtil.set(key,"1",3600);
                    resultrecords.add(record);
                }
            }
            if (resultrecords.size()>0){
                List<Map<String,Object>> mapLists =  tOrderInfoService.sendWxHtList(resultrecords);
                if (mapLists.size()>0){
                    for (Map map : mapLists){
                        if ("批量推送微信补签通知失败".equals(map.get("msg"))){
                            if (records.size() > 0) {
                                for (TOrderInfoVO record : records) {
                                    String key = "wxbq"+record.getCode();
                                    redisUtil.del(key);
                                }
                            }
                            return ResultUtil.error("批量推送微信补签通知失败");
                        }else {
                            mapList.add(map);
                        }
                    }
                }

            }

            return new ResultUtil(CodeEnum.SUCCESS.getCode(),mapList);
        }catch (Exception e){
            log.error("批量推送微信补签通知失败",e);
            return ResultUtil.error("批量推送微信补签通知失败");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2020/11/12 9:56
     *  @Description: 运单合同跟进 上传文件批量补签合同
     */
    @PostMapping("/loadOrderHtList")
    @ResponseBody
    public ResultUtil loadOrderHtList(@RequestBody List<TOrderInfoVO> records){
        try{
            List<Map<String,Object>> mapList = new ArrayList<>();
            List<TOrderInfoVO> resultrecords = new ArrayList<>();
            for (TOrderInfoVO record : records){
                TOrderInfo tOrderInfo = tOrderInfoService.selectByOrderBusinessCode(record.getOrderBusinessCode());
                if(null!= tOrderInfo && !"".equals(tOrderInfo)){
                    record.setCode(tOrderInfo.getCode());
                    String key = "wxbq"+tOrderInfo.getCode();
                    if (redisUtil.hasKey(key)){
                        Map<String ,Object> map = new HashMap<>();
                        map.put("orderCode",record.getOrderBusinessCode());
                        map.put("code","error");
                        map.put("msg","失败: 该运单1小时内已推送过补签协议，请稍后再推送");
                        mapList.add(map);
                    }else {
                        redisUtil.set(key,"1",3600);
                        resultrecords.add(record);
                    }
                }
            }
            if (resultrecords.size()>0){
                List<Map<String,Object>> mapLists =  tOrderInfoService.sendWxHtListUpload(resultrecords);
                if (mapLists.size()>0){
                    for (Map map : mapLists){
                        if ("批量推送微信补签通知失败".equals(map.get("msg"))){
                            if (records.size() > 0) {
                                for (TOrderInfoVO record : records) {
                                    String key = "wxbq"+record.getCode();
                                    redisUtil.del(key);
                                }
                            }
                            return ResultUtil.error("批量推送微信补签通知失败");
                        }else {
                            mapList.add(map);
                        }
                    }
                }

            }
            return new ResultUtil(CodeEnum.SUCCESS.getCode(),mapList);
        }catch (Exception e){
            log.error("批量推送微信补签通知失败",e);
            return ResultUtil.error("批量推送微信补签通知失败");
        }
    }

    /**
     * 新版：运单合同跟进 上传文件批量补签合同
     */
    @PostMapping("/batchImportPushContract")
    @ResponseBody
    public ResultUtil batchImportPushContract(@RequestBody List<TOrderInfoVO> records) {
        try {
            if(records.size() > 0){
                HashSet<String> hashSet = new HashSet<>();
                for (TOrderInfoVO vo : records) {
                    hashSet.add(vo.getOrderBusinessCode());
                }
                orderAsyncService.batchImportPushContract(hashSet);
            }
            return ResultUtil.ok("请求成功");
        } catch (Exception e) {
            log.error("批量推送微信补签通知失败", e);
            return ResultUtil.error("批量推送微信补签通知失败");
        }
    }

    /**
     * 导入运单查询
     */
    @PostMapping("/selectImportListByPage")
    @ResponseBody
    public ResultUtil selectImportListByPage(@RequestBody TOrderBatchImportPushContractVO search) {
        ResultUtil resultUtil;
        try {
            resultUtil = orderBatchImportPushContractService.selectImportListByPage(search);
        } catch(Exception e){
            log.error("导入运单查询失败{}", ThrowableUtil.getStackTrace(e));
            log.error("导入运单查询失败!查询参数：" + search.toString() + "错误信息：", e);
            return ResultUtil.error("导入运单查询失败!");
        }
        return  resultUtil;
    }

    /**
     * 导入运单列表--导出接口
     * @param search
     * @return
     */
    @PostMapping("/importListExpro")
    public ResultUtil importListExpro(@RequestBody TOrderBatchImportPushContractVO search){

        return orderBatchImportPushContractService.importListExpro(search);
    }

    /**
     * 获取司机批量运单详情
     */
    @PostMapping("/getYdList")
    public ResultUtil getYdList(@RequestBody TOrderPushContract tOrderPushContract) {
        try{
            if (null!=tOrderPushContract&&null!=tOrderPushContract.getCode()){
                return tOrderInfoService.getYdbycode(tOrderPushContract.getCode());
            }else {
                return ResultUtil.error("获取司机批量运单详情失败");
            }

        }catch (Exception e){
            log.error("获取司机批量运单详情失败",e);
            return ResultUtil.error("获取司机批量运单详情失败");
        }
    }

    /**
     * 获取司机未签署运单列表
     */
    @PostMapping("/getYdListNew")
    public ResultUtil getYdListNew() {
        try{
            return tOrderInfoService.getYdbycodeNew();
        }catch (Exception e){
            log.error("获取司机批量运单详情失败",e);
            return ResultUtil.error("获取司机批量运单详情失败");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2020/3/3 9:02
     *  @Description: 提醒微信补签协议
     */
    @PostMapping("/sendWxHt")
    public ResultUtil sendWxHt(@RequestBody TOrderInfoVO record) {
        try{
            String key = "wxbq"+record.getCode();
            if (redisUtil.hasKey(key)){
                return ResultUtil.error("该运单1小时内已推送过补签协议，请稍后再推送");
            }else {
                redisUtil.set(key,"1",3600);
                return tOrderInfoService.sendWxHt(record);
            }

        }catch (Exception e){
            log.error("补签协议推送失败",e);
            return ResultUtil.error("补签协议推送失败");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2020/3/7 13:59
     *  @Description: 上传三方协议回显页面参数
     */
    @PostMapping("/selectByContract")
    public ResultUtil selectByContract(@RequestBody TOrderInfoVO record) {
        try{
            return tOrderInfoService.selectByContract(record);
        }catch (Exception e){
            log.error("获取信息失败",e);
            return ResultUtil.error("获取信息失败");
        }
    }


    /**
     * @Author: cyp
     * @Description: 统计企业数据 (每月发单量)
     * @Date: 2020/6/4
     * @return
     **/
    @PostMapping("/statisticalCompany")
    public ResultUtil statisticalCompany(@RequestBody TOrderInfoVO record){
        try {
            return tOrderInfoService.statisticalCompany(record);
        }catch (Exception e){
            log.error("查询统计异常",e);
            return ResultUtil.ok("查询统计异常");
        }
    }


    /**
     * PC端 节点支付查询运单列表
     * dingweibo
     * @return
     */
    @PostMapping("/selectNodeByPage")
    public ResultUtil selectNodeByPage(@RequestBody AppOrderSearchVO search) {
        ResultUtil resultUtil = null;
        try {
            resultUtil = tOrderInfoSearchService.selectNodeByPage(search);
        } catch(Exception e){
            log.error("节点支付运单查询失败", e);
            log.error("节点支付运单查询失败!查询参数：" + search.toString() + "错误信息：", e);
            return ResultUtil.error("节点支付运单查询失败!");
        }
        return  resultUtil;
    }

    /**
     *  @author: 张森森
     *  @Date:
     *  @Description: 节点运单导出excel
     */
    @PostMapping("/exportNodeExcel")
    public ResultUtil exportNodeExcel(@RequestBody AppOrderSearchVO search , HttpServletResponse response){
        ResultUtil resultUtil = new ResultUtil();
        try {
            // 防止中文乱码
            // 设置response参数，可以打开下载页面
            String[] headers =
                    { "运单编号", "司机姓名", "司机电话", "车牌号", "装货支付状态","卸货支付状态","收单支付状态",
                            "尾款支付状态","货物类型","起点","终点","预估运费（元）","预估调度费（元）","实际运费（元）",
                            "实际调度费（元）","装货支付金额（元）","装货支付时间","卸货支付金额（元）","卸货支付时间",
                            "收单支付金额（元）","收单支付时间","尾款支付金额（元）","调度费（元）","尾款支付时间",
                            "已支付运费（元）","剩余运费（元）","承运方名称","企业名称","装货运费提现时间","卸货运费提现时间",
                            "收单运费提现时间","尾款运费提现时间","经纪人服务费(元)"};
            String[] names = {"orderBusinessCode","realName","realPhone","vehicleNumber","zhPayStatusType","xhPayStatusType",
                    "sdPayStatusType", "wkPayStatusType","goodsName","fromName","endName","originalCarriageFee","originalDispatchFee",
                    "userConfirmPaymentAmount","dispatchFee","zhPaymentFee", "zhPayTime","xhPaymentFee","xhPayTime","sdPaymentFee",
                    "sdPayTime","wkPaymentFee","payDispatchFee","wkPayTime","yzfPaymentFee","surplusPaymentFee","carrierName","companyName",
                    "zhWithdrawTime","xhWithdrawTime","sdWithdrawTime","wkWithdrawTime","userConfirmServiceFee"};

            List<TOrderPayRuleVo> list = tOrderInfoSearchService.selectNodeBySearch(search);
            Map<String,Object> map = new HashMap<>();
            map.put("headers",headers);
            map.put("names",names);
            map.put("list",list);
            resultUtil.setData(map);
            resultUtil.setCode(CodeEnum.SUCCESS.getCode());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return  resultUtil;
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/2/18 15:47
     *  @Description: 快货运导入
     */
    @PostMapping("/khyExcelImport")
    public ResultUtil khyExcelImport(@RequestBody TOrderInfoUploadReq record){
        try {
            if(record.getOrderInfoList().size()>500){
                return ResultUtil.error("导入最大值为500条");
            }
            return tOrderInfoService.khyExcelImport(record);
        } catch(Exception e){
            log.error("快货运导入失败：", e);
            return ResultUtil.error("快货运导入失败!");
        }
    }
    /**
     *  @author: dingweibo
     *  @Date: 2022/2/18 15:47
     *  @Description: 山东肥城数据上报导入
     */
    @PostMapping("/sdfcExcelImport")
    public ResultUtil sdfcExcelImport(@RequestBody TOrderInfoUploadReq record){
        try {
            if(record.getOrderInfoList().size()>500){
                return ResultUtil.error("导入最大值为500条");
            }
            return tOrderInfoService.sdfcExcelImport(record);
        } catch(Exception e){
            log.error("数据上报导入失败：", e);
            return ResultUtil.error("数据上报导入失败!");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/2/18 15:47
     *  @Description: 山东肥城重置参数
     */
    @PostMapping("/parameterSdFcReset")
    public ResultUtil parameterSdFcReset(@RequestBody List<TTask> task){
        try {
            return tOrderInfoService.parameterSdFcReset(task);
        } catch(Exception e){
            log.error("山东肥城重置参数失败：", e);
            return ResultUtil.error("山东肥城重置参数失败!");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/2/18 15:47
     *  @Description: 快货运重置参数
     */
    @PostMapping("/parameterKhyReset")
    public ResultUtil parameterKhyReset(@RequestBody List<TTask> task){
        try {
            return tOrderInfoService.parameterKhyReset(task);
        } catch(Exception e){
            log.error("快货运重置参数失败：", e);
            return ResultUtil.error("快货运重置参数失败!");
        }
    }

    //肥城上报修改上报状态
    @PostMapping("/updateOrderSendState")
    public ResultUtil updateOrderSendState(@RequestParam Integer orderInfoId){
        try {
            return tOrderInfoService.updateOrderSendState(orderInfoId);
        } catch(Exception e){
            log.error("肥城上报修改上报状态失败：", e);
            return ResultUtil.error("肥城上报修改上报状态失败!");
        }
    }

    //安徽上报修改上报状态
    @PostMapping("/updateAhOrderSendState")
    public ResultUtil updateAhOrderSendState(@RequestParam Integer orderInfoId){
        try {
            return tOrderInfoService.updateAhOrderSendState(orderInfoId);
        } catch(Exception e){
            log.error("安徽上报修改上报状态失败：", e);
            return ResultUtil.error("安徽上报修改上报状态失败!");
        }
    }

    @PostMapping("/selectByEtcDate")
    public DataToEtcVO selectByEtcDate(@RequestParam("orderCode") String orderCode){
        return tOrderInfoService.selectByEtcDate(orderCode);
    }

    /**
     * 违规运单补车辆轨迹
     */
    @PostMapping("/wgydVehicleTrajectory")
    public ResultUtil wgydVehicleTrajectory(@RequestBody List<TOrderInfoVO> detail) {
        try {
            return tOrderInfoService.wgydVehicleTrajectory(detail);
        } catch (Exception e){
            log.error("违规运单补充运单轨迹失败", e);
            return ResultUtil.error("违规运单补充运单轨迹失败");
        }
    }
}
