package com.lz.controller;

import com.lz.api.TVerificationCodeLogAPI;
import com.lz.common.config.RedisUtil;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.model.TOrderAuditLog;
import com.lz.model.TOrderInfo;
import com.lz.model.TVerificationCodeLog;
import com.lz.model.trajectory.resp.TrajectoryVLastLocationResp;
import com.lz.model.trajectory.resp.recent.TrajectoryRecentResp;
import com.lz.model.trajectory.resp.recent.TransTimeManageVResp;
import com.lz.service.TOrderInfoService;
import com.lz.service.TtoolboxService;
import com.lz.tpu.web.reqeuest.PaymentRequest;
import com.lz.util.OrderUtil;
import com.lz.vo.TOrderShExcel;
import com.lz.vo.TtoolboxVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 *  @author: dingweibo
 *  @Date: 2019/8/2 15:09
 *  @Description:
 */
@Slf4j
@RestController
@RequestMapping("/toolbox")
public class TtoolboxController {
    @Autowired
    private TtoolboxService ttoolboxService;
    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private TOrderInfoService orderInfoService;

    @Autowired
    private TVerificationCodeLogAPI tVerificationCodeLogAPI;

    /**
     * @Description 工具箱
     * <AUTHOR>
     * @Date   2019/8/2 15:08
     * @Param
     * @Return
     * @Exception 针对运单重新生成合同
     *
     */
    @PostMapping("/renewalOfContract")
    public ResultUtil renewalOfContract(@RequestBody TtoolboxVo ttoolboxVo) {
        try{
            return ttoolboxService.renewalOfContract(ttoolboxVo);
        }catch (Exception e){
            log.error("针对运单重新生成合同失败！",e);
            return ResultUtil.error("dw-047:针对运单重新生成合同失败！");
        }
    }


    /**
    * @Description 针对运单重新生成收款凭证
    * <AUTHOR>
    * @Date   2019/8/5 8:50
    * @Param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/renewalOfTxxy")
    public ResultUtil renewalOfTxxy(@RequestBody TtoolboxVo ttoolboxVo){
        try{
            return ttoolboxService.renewalOfTxxy(ttoolboxVo);
        }catch (Exception e){
            log.error("针对运单重新生成收款凭证失败！",e);
            return ResultUtil.error("dw-048:针对运单重新生成收款凭证失败！");
        }
    }



    /**
     * @Description 解绑司机个人信息
     * <AUTHOR>
     * @Date   2019/8/5 8:50
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/driverUnwrap")
    public ResultUtil driverUnwrap(@RequestBody TtoolboxVo ttoolboxVo){
        try{
            return ttoolboxService.driverUnwrap(ttoolboxVo);
        }catch (Exception e){
            log.error("解绑司机个人信息失败！",e);
            return ResultUtil.error("dw-049:解绑司机个人信息失败！");
        }
    }


    /**
     * @Description 解绑企业信息
     * <AUTHOR>
     * @Date   2019/8/5 8:50
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/companyUnwrap")
    public ResultUtil companyUnwrap(@RequestBody TtoolboxVo ttoolboxVo){
        try{
            return ttoolboxService.companyUnwrap(ttoolboxVo);
        }catch (Exception e){
            log.error("解绑企业信息失败！",e);
            return ResultUtil.error("dw-050:解绑企业信息失败！");
        }
    }

    /**
     * @Description 修改司机手机号
     * <AUTHOR>
     * @Date   2019/8/5 8:50
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/driverChangeMobile")
    public ResultUtil driverChangeMobile(@RequestBody TtoolboxVo ttoolboxVo){
        try{
            return ttoolboxService.driverChangeMobile(ttoolboxVo);
        }catch (Exception e){
            log.error("修改司机手机号失败！",e);
            return ResultUtil.error("dw-051:修改司机手机号失败！");
        }
    }



    /**
    * @Description 查询车辆轨迹
    * <AUTHOR>
    * @Date   2019/9/12 15:01
    * @Param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/vHisTrack24")
    public TrajectoryRecentResp vHisTrack24(@RequestBody TtoolboxVo ttoolboxVo){
        try{
            return ttoolboxService.vHisTrack24(ttoolboxVo);
        }catch (Exception e){
            log.error("查询车辆轨迹失败！",e);
            TrajectoryRecentResp resp = new TrajectoryRecentResp();
            resp.setCode("error");
            resp.setMsg("查询车辆轨迹失败");
            return resp;
        }
    }

    /**
     * @Author: cyp
     * @Description: 资金回退
     * @Date: 2020/3/18
     * @return
     **/
    @PostMapping("/fundsFallback")
    public ResultUtil fundsFallback(@RequestBody TtoolboxVo ttoolboxVo){
        try {
            return ttoolboxService.fundsFallback(ttoolboxVo);
        }catch (Exception e){
            e.printStackTrace();
            return ResultUtil.error("资金回退操作失败！");

        }

    }


    /**
     * @Description 查询车辆状态
     * <AUTHOR>
     * @Date   2019/9/12 15:01
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/checkTruckExist")
    public ResultUtil checkTruckExist(@RequestBody TtoolboxVo ttoolboxVo){
        try{
            return ttoolboxService.checkTruckExist(ttoolboxVo);
        }catch (Exception e){
            return ResultUtil.error();
        }
    }


    /**
     * @Description 当前车辆位置
     * <AUTHOR>
     * @Date
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/vLastLocation")
    public TransTimeManageVResp vLastLocation(@RequestParam(value = "vln") String vln){
        try{
            return ttoolboxService.vLastLocation(vln);
        }catch (Exception e){
            log.error("查询车辆最新轨迹失败！",e);
            TransTimeManageVResp resp = new TransTimeManageVResp();
            resp.setCode("error");
            resp.setMsg("查询车辆最新轨迹失败");
            return resp;
        }
    }
    /**
     *  @author: dingweibo
     *  @Date: 2020/12/7 11:09
     *  @Description:
     *  网商余额查询
     */
    @PostMapping("/balanceAccount")
    public ResultUtil balanceAccount(@RequestBody PaymentRequest paymentRequest){
        try{
            return ttoolboxService.balanceAccount(paymentRequest);
        }catch (Exception e){
            log.error("网商余额查询！",e);
            ResultUtil resp = new ResultUtil();
            resp.setCode("error");
            resp.setMsg("网商余额查询失败");
            return resp;
        }
    }
    @GetMapping("/getIdWork")
    public String getIdWork(){
        return IdWorkerUtil.getInstance().nextId();
    }


    @PostMapping("/ImportExcel")
    public ResultUtil ImportExcel(@RequestBody TtoolboxVo ttoolboxVo){
        List<HashMap<String, Object>> resultUtils = new ArrayList<>();
        boolean flag = false;
        String code = ObjectUtils.toString(redisUtil.get("YDSH" + ttoolboxVo.getPhone()));
        if (StringUtils.isBlank(code)) {
            return ResultUtil.error("请输入验证码");
        } else if (StringUtils.isEmpty(code)) {
            return ResultUtil.error("请重新发送验证码");
        } else if (!ttoolboxVo.getVerificationCode().equals(code)) {
            return ResultUtil.error("验证码填写错误");
        } else {
            List<TOrderShExcel> tOrderShExcelList = ttoolboxVo.getOrderList();
            for(TOrderShExcel tOrderShExcel:tOrderShExcelList) {
                TOrderInfo tOrderInfo = orderInfoService.selectByOrderBusinessCode(tOrderShExcel.getOrderBusinessCode());
                if(null!=tOrderInfo && !"".equals(tOrderInfo)){
                    if(null!=tOrderInfo.getOrderExecuteStatus() && !"".equals(tOrderInfo.getOrderExecuteStatus())){
                        if ("M-20".equals(tOrderInfo.getOrderExecuteStatus()) ||
                                "M-10".equals(tOrderInfo.getOrderExecuteStatus()) ||
                                "M000".equals(tOrderInfo.getOrderExecuteStatus()) ||
                                "M010".equals(tOrderInfo.getOrderExecuteStatus()) ||
                                "M020".equals(tOrderInfo.getOrderExecuteStatus()) ||
                                "M030".equals(tOrderInfo.getOrderExecuteStatus())) {
                            flag = true;
                            HashMap<String, Object> result = new HashMap<>();
                            result.put("orderCode", tOrderInfo.getOrderBusinessCode());
                            result.put("code", "error");
                            result.put("msg", tOrderInfo.getOrderBusinessCode() + ":运单状态不符合要求");
                            resultUtils.add(result);
                            continue;
                        }
                    }else{
                        flag = true;
                        HashMap<String, Object> result = new HashMap<>();
                        result.put("orderCode", tOrderInfo.getOrderBusinessCode());
                        result.put("code", "error");
                        result.put("msg", tOrderInfo.getOrderBusinessCode() + ":运单状态不符合要求");
                        resultUtils.add(result);
                        continue;
                    }
                }else{
                    HashMap<String, Object> result = new HashMap<>();
                    result.put("orderCode", tOrderShExcel.getOrderBusinessCode());
                    result.put("code", "error");
                    result.put("msg", tOrderShExcel.getOrderBusinessCode() + ":当前运单号不正确");
                    resultUtils.add(result);
                    continue;
                }
            }
            if(flag){
                ResultUtil resultUtil = new ResultUtil();
                resultUtil.setCode("error");
                resultUtil.setData(resultUtils);
                return resultUtil;
            }else{
                TVerificationCodeLog tVerificationCodeLog=new TVerificationCodeLog();
                tVerificationCodeLog.setReceivePhoneno(ttoolboxVo.getPhone());
                tVerificationCodeLog.setVerificationCode(code);
                tVerificationCodeLogAPI.updateIfUsed(tVerificationCodeLog);
                return ttoolboxService.ImportExcel(ttoolboxVo);
            }
        }
    }


    @PostMapping("/ydhtqs")
    public ResultUtil ydhtqs(@RequestBody Map<String,Object> map){
        try{
            return ttoolboxService.ydhtqs(map);
        }catch (Exception e){
            log.error("5G签署合同失败！",e);
            ResultUtil resp = new ResultUtil();
            resp.setCode("error");
            resp.setMsg("5G签署合同失败");
            return resp;
        }
    }
}
