package com.lz.controller;

import cn.hutool.json.JSONUtil;
import com.lz.api.HXOpenRoleCommonAPI;
import com.lz.api.JDOpenRoleCommonAPI;
import com.lz.common.config.RedisUtil;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.dto.HXOpenRoleStatusListDTO;
import com.lz.dto.OpenRoleStatusListDTO;
import com.lz.dto.TAdvanceOrderTmpDto;
import com.lz.model.TOrderCastChanges;
import com.lz.model.TOrderInfo;
import com.lz.service.*;
import com.lz.service.hxyh.THxOrderInfoService;
import com.lz.util.SendOrderUtil;
import com.lz.vo.SelectOpenRoleVO;
import com.lz.vo.SendOrderVO;
import com.lz.vo.TAdvanceOrderTmpExcel;
import com.lz.vo.TOrderInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@RequestMapping("/tAdvanceOrderTmp")
@RestController
public class TAdvanceOrderTmpController {

    @Autowired
    private TAdvanceOrderTmpService tAdvanceOrderTmpService;

    @Autowired
    private TAdvanceOrderPayTempService advanceOrderPayTempService;

    @Autowired
    private TOrderInfoService orderInfoService;

    @Autowired
    private TOrderCastChangesService orderCastChangesService;

    @Autowired
    private TJDOrderInfoService jdOrderInfoService;

    @Autowired
    private THxOrderInfoService hxOrderInfoService;

    @Autowired
    private JDOpenRoleCommonAPI openRoleCommonAPI;

    @Autowired
    private HXOpenRoleCommonAPI hxOpenRoleCommonAPI;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private SendOrderUtil sendOrderUtil;

    /**
    * @Description 导入
    * <AUTHOR>
    * @Date   2020/2/24 14:07
    * @Param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/ImportExcel")
    public ResultUtil ImportExcel(@RequestBody List<TAdvanceOrderTmpExcel> list){
        try{
            List<HashMap<String, Object>> resultUtils = new ArrayList<>();
            TAdvanceOrderTmpExcel tAdvanceOrderTmpExcel;
            Integer companyWalletId = 0;
            if (null != list && !list.isEmpty()) {
                tAdvanceOrderTmpExcel = list.get(0);
                TOrderInfoVO tOrderInfoVO = orderInfoService.selectByOrderVOBusinessCode(tAdvanceOrderTmpExcel.getOrderBusinessCode());
                if (null != tOrderInfoVO) {
                    TOrderCastChanges tOrderCastChanges = orderCastChangesService.selectByNewOne(tOrderInfoVO.getCode());
                    companyWalletId = tOrderCastChanges.getCompanyWalletId();
                }
            }
            String walletKey = "Wallet" + companyWalletId;
            boolean walletLock = false;
            try {
                if (!redisUtil.hasKey(walletKey)) {
                    walletLock = redisUtil.set(walletKey, "lock");
                }
                if (walletLock) {
                    ResultUtil resultUtil = ResultUtil.ok();
                    for(TAdvanceOrderTmpExcel advanceOrderTmpExcel:list){
                        // 检测运单
                        HashMap<String, Object> resultCheck = sendOrderUtil.checkPrePayment(advanceOrderTmpExcel);
                        if (null == resultCheck) {
                            HashMap<String, Object> result = new HashMap<>();
                            try {
                                tAdvanceOrderTmpService.ImportExcel(advanceOrderTmpExcel);
                                result.put("orderCode", advanceOrderTmpExcel.getOrderBusinessCode());
                                result.put("code", "导入成功");
                                resultUtils.add(result);
                            } catch (Exception e) {
                                log.error("预付款运单导入失败!", e);
                                String message = e.getMessage();
                                resultUtil.setCode("error");
                                if (!StringUtils.checkChineseCharacter(message)) {
                                    message = "预付款运单导入失败";
                                }
                                result.put("code", "error");
                                result.put("msg", message);
                                resultUtils.add(result);
                            }
                        } else {
                            resultUtils.add(resultCheck);
                        }
                    }
                    if (resultUtils.size() > 0) {
                        resultUtil.setCode("success");
                        resultUtil.setData(resultUtils);
                        return resultUtil;
                    }
                    return ResultUtil.ok();
                } else {
                    return ResultUtil.error("企业正在操作中，请稍后再试");
                }
            } catch (Exception e) {
                log.error("预付款运单导入失败!", e);
                String message = e.getMessage();
                if (StringUtils.checkChineseCharacter(message)) {
                    return ResultUtil.error(message);
                } else {
                    return ResultUtil.error("预付款运单导入失败");
                }
            } finally {
                if (walletLock && redisUtil.hasKey(walletKey)) {
                    redisUtil.del(walletKey);
                }
            }
        }catch (Exception e){
            log.error("预付款运单导入失败:",e);
            return ResultUtil.error("预付款运单导入失败");
        }
    }

    /**
    * @description 预付款京东支付
    * <AUTHOR>
    * @date 2021/11/5 15:25
    */
    @PostMapping("/jd/pre/pay")
    public ResultUtil jdPrePay(@RequestBody List<TAdvanceOrderTmpExcel> list) {
        try {
            ResultUtil resultUtil = ResultUtil.ok();
            List<HashMap<String, Object>> resultUtils = new ArrayList<>();
            Map<Integer, Map<String, List<String>>> mapMap = new HashMap<>();
            if (null != list && !list.isEmpty()) {
                mapMap = jdOrderInfoService.selectAdvanceOrderGroupByCompanyId(list);
            }
            log.info("预付款运单信息, {}", JSONUtil.toJsonStr(mapMap));
            List<Integer> userCompanyIdInteger = CurrentUser.getUserCompanyIdInteger();
            if (userCompanyIdInteger.isEmpty()) {
                List<HashMap<String, Object>> results = new ArrayList<>();
                list.forEach(order -> {
                        HashMap<String, Object> map = new HashMap<>();
                    map.put("orderCode", order.getOrderBusinessCode());
                    map.put("code", "error");
                    map.put("msg", "ZJJ-240：支付失败！当前用户无支付权限。");
                    results.add(map);
                });
                return ResultUtil.ok(results);
            }
            Map<String, TAdvanceOrderTmpExcel> tmpExcelMap = new HashMap<>();
            list.stream().forEach((item) -> tmpExcelMap.put(item.getOrderBusinessCode(), item));
            for (Map.Entry<Integer, Map<String, List<String>>> entry : mapMap.entrySet()) {
                Integer companyId = entry.getKey();
                Map<String, List<String>> listMap = entry.getValue();
                List<String> orderBusinesCodes = new ArrayList<>();
                for (Map.Entry<String, List<String>> listEntry : listMap.entrySet()) {
                    // 获取当前企业下的所有运单
                    if (listEntry.getKey().equals("order_business_code")) {
                        orderBusinesCodes = listEntry.getValue();
                    }
                }
                if (!userCompanyIdInteger.contains(companyId)) {
                    List<HashMap<String, Object>> results = new ArrayList<>();
                    orderBusinesCodes.forEach(orderBusinesCode -> {
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("orderCode", orderBusinesCode);
                        map.put("code", "error");
                        map.put("msg", "ZJJ-240：支付失败！当前用户无支付权限。");
                        results.add(map);
                    });
                    resultUtils.addAll(results);
                    continue;
                }
                SelectOpenRoleVO vo = new SelectOpenRoleVO();
                vo.setCompanyId(companyId);
                ResultUtil openRoleStatus = openRoleCommonAPI.selectCarrierCompanyEnduserOpenRoleStatus(vo);
                OpenRoleStatusListDTO dto = JSONUtil.toBean(JSONUtil.parseObj(openRoleStatus.getData()), OpenRoleStatusListDTO.class);
                log.info(JSONUtil.toJsonStr(dto));
                if (null == dto.getCompanyStatus()
                        || null == dto.getCompanyStatus().getStatus()
                        || !dto.getCompanyStatus().getStatus()
                        || null == dto.getCompanyStatus().getPartnerAccId()
                        || StringUtils.isBlank(dto.getCompanyStatus().getPartnerAccId())) {
                    List<HashMap<String, Object>> results = new ArrayList<>();
                    orderBusinesCodes.forEach(orderBusinesCode -> {
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("orderCode", orderBusinesCode);
                        map.put("code", "error");
                        map.put("msg", "ZJJ-240：支付失败！企业未开通京东支付，请联系运营平台予以解决。");
                        results.add(map);
                    });
                    resultUtils.addAll(results);
                    continue;
                }
                if (null == dto.getCompanyStatus().getPaymentStatus() || !dto.getCompanyStatus().getPaymentStatus()) {
                    List<HashMap<String, Object>> results = new ArrayList<>();
                    orderBusinesCodes.forEach(orderBusinesCode -> {
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("orderCode", orderBusinesCode);
                        map.put("code", "error");
                        map.put("msg", "ZJJ-240：支付失败！企业未开通京东支付，请联系运营平台予以解决。");
                        results.add(map);
                    });
                    resultUtils.addAll(results);
                    continue;
                }
                String walletKey = dto.getCompanyStatus().getPartnerAccId();
                RLock lock = redissonClient.getLock(walletKey);
                try {
                    boolean walletLock = false;
                    printLockLog(lock, "分布式锁:lock:");
                    walletLock = lock.tryLock(5, TimeUnit.SECONDS);
                    printLockLog(lock, "分布式锁:tryLock:");
                    log.info("获取企业钱包锁，{}, {}", walletKey, walletLock);
                    if (walletLock) {
                        for (String orderBusinessCode : orderBusinesCodes) {
                            TAdvanceOrderTmpExcel advanceOrderTmpExcel = tmpExcelMap.get(orderBusinessCode);
                            // 检测运单
                            HashMap<String, Object> resultCheck = sendOrderUtil.checkPrePayment(advanceOrderTmpExcel);
                            if (null == resultCheck) {
                                HashMap<String, Object> result = new HashMap<>();
                                try {
                                    tAdvanceOrderTmpService.jdPrePayment(advanceOrderTmpExcel);
                                    result.put("orderCode", orderBusinessCode);
                                    result.put("code", "导入成功");
                                    resultUtils.add(result);
                                } catch (Exception e) {
                                    log.error("预付款运单导入失败!", e);
                                    String message = e.getMessage();
                                    resultUtil.setCode("error");
                                    if (!StringUtils.checkChineseCharacter(message)) {
                                        message = "预付款运单导入失败";
                                    }
                                    result.put("orderCode", orderBusinessCode);
                                    result.put("code", "error");
                                    result.put("msg", message);
                                    resultUtils.add(result);
                                }
                            } else {
                                resultUtils.add(resultCheck);
                            }
                        }
                    } else {
                        List<HashMap<String, Object>> results = new ArrayList<>();
                        orderBusinesCodes.forEach(orderBusinesCode -> {
                            HashMap<String, Object> map = new HashMap<>();
                            map.put("orderCode", orderBusinesCode);
                            map.put("code", "error");
                            map.put("msg", "ZJJ-240：支付失败！企业正在操作中，请稍后再试。");
                            results.add(map);
                        });
                        resultUtils.addAll(results);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("预付款运单导入失败!, {}", e);
                    HashMap<String, Object> result = new HashMap<>();
                    result.put("code", "error");
                    result.put("msg", "预付款运单导入失败");
                    resultUtils.add(result);
                } finally {
                    if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                        printLockLog(lock, "分布式锁:unlock:");
                        lock.unlock();
                        log.info("分布式锁解锁");
                    }
                }
            }
            if (resultUtils.size() > 0) {
                resultUtil.setCode("success");
                resultUtil.setData(resultUtils);
                log.info("287");
                return resultUtil;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("预付款运单导入失败!, {}", e);
            return ResultUtil.error("预付款运单导入失败");
        }
        log.info("294");
        return ResultUtil.ok();
    }

    /**
    * @description 京东预支付尾款支付
    * <AUTHOR>
    * @date 2021/11/8 14:43
    */
    @PostMapping("/jd/wk/pay")
    public ResultUtil jdWkPay(@RequestBody SendOrderVO record) {
        List<ResultUtil> resultUtils = new ArrayList<>();

        if (null != record.getCodes() && record.getCodes().length > 0) {
            List<Integer> userCompanyIdInteger = CurrentUser.getUserCompanyIdInteger();
            if (userCompanyIdInteger.isEmpty()) {
                List<String> orderBusinessCodes = tAdvanceOrderTmpService.selectOrderBusinessCode(Arrays.asList(record.getCodes()));
                List<ResultUtil> results = new ArrayList<>();
                orderBusinessCodes.forEach(orderBusinesCode -> results.add(ResultUtil.error("ZJJ-241：支付失败！当前用户无支付权限。", orderBusinesCode)));
                resultUtils.addAll(results);
                return ResultUtil.ok(resultUtils);
            }
            Map<Integer, Map<String, List<String>>> mapMap = jdOrderInfoService.selectOrderGroupByCompanyId(Arrays.asList(record.getCodes()));
            for (Map.Entry<Integer, Map<String, List<String>>> entry : mapMap.entrySet()) {
                Integer companyId = entry.getKey();
                Map<String, List<String>> listMap = entry.getValue();
                List<String> codes = new ArrayList<>();
                List<String> orderBusinesCodes = new ArrayList<>();
                for (Map.Entry<String, List<String>> listEntry : listMap.entrySet()) {
                    // 获取当前企业下的所有运单
                    if (listEntry.getKey().equals("codes")) {
                        codes = listEntry.getValue();
                    } else if (listEntry.getKey().equals("order_business_code")) {
                        orderBusinesCodes = listEntry.getValue();
                    }
                }
                // 判断是否使用京东支付
                String checkAdvancePaymentPlatforms = advanceOrderPayTempService.checkAdvancePaymentPlatforms(codes, DictEnum.JDPLATFORMS.code);
                if (StringUtils.isNotBlank(checkAdvancePaymentPlatforms)) {
                    List<ResultUtil> results = new ArrayList<>();
                    orderBusinesCodes.forEach(orderBusinesCode -> results.add(ResultUtil.error("ZJJ-241：支付失败！" + checkAdvancePaymentPlatforms, orderBusinesCode)));
                    resultUtils.addAll(results);
                    return ResultUtil.ok(resultUtils);
                }
                SelectOpenRoleVO vo = new SelectOpenRoleVO();
                vo.setCompanyId(companyId);
                ResultUtil openRoleStatus = openRoleCommonAPI.selectCarrierCompanyEnduserOpenRoleStatus(vo);
                OpenRoleStatusListDTO dto = JSONUtil.toBean(JSONUtil.parseObj(openRoleStatus.getData()), OpenRoleStatusListDTO.class);
                log.info(JSONUtil.toJsonStr(dto));
                if (null == dto.getCompanyStatus()
                        || null == dto.getCompanyStatus().getStatus()
                        || !dto.getCompanyStatus().getStatus()
                        || null == dto.getCompanyStatus().getPartnerAccId()
                        || StringUtils.isBlank(dto.getCompanyStatus().getPartnerAccId())) {
                    List<ResultUtil> results = new ArrayList<>();
                    orderBusinesCodes.forEach(orderBusinesCode -> results.add(ResultUtil.error("ZJJ-241：支付失败！企业未开通京东支付，请联系运营平台予以解决。", orderBusinesCode)));
                    resultUtils.addAll(results);
                    continue;
                }
                if (null == dto.getCompanyStatus().getPaymentStatus() || !dto.getCompanyStatus().getPaymentStatus()) {
                    List<ResultUtil> results = new ArrayList<>();
                    orderBusinesCodes.forEach(orderBusinesCode -> results.add(ResultUtil.error("ZJJ-241：支付失败！企业未打款验证，请联系运营平台予以解决。", orderBusinesCode)));
                    resultUtils.addAll(results);
                    continue;
                }
                String walletKey = dto.getCompanyStatus().getPartnerAccId();
                RLock lock = redissonClient.getLock(walletKey);
                try {
                    boolean walletLock = false;
                    printLockLog(lock, "分布式锁:lock:");
                    walletLock = lock.tryLock(5, TimeUnit.SECONDS);
                    printLockLog(lock, "分布式锁:tryLock:");
                    log.info("获取企业钱包锁，{}, {}", walletKey, walletLock);
                    if (walletLock) {
                        for (String orderCode : codes) {
                            TOrderInfo tOrderInfo = orderInfoService.selectOrderInfoByCode(orderCode);
                            try {
                                ResultUtil resultUtil = sendOrderUtil.checkWkPayment(tOrderInfo, record);
                                if (null == resultUtil) {
                                    TOrderInfoVO orderInfoVO = new TOrderInfoVO();
                                    orderInfoVO.setId(tOrderInfo.getId());
                                    orderInfoVO.setCode(tOrderInfo.getCode());
                                    if (null != record.getUserConfirmCarriagePayment()) {
                                        orderInfoVO.setNewUserConfirmCarriagePayment(record.getUserConfirmCarriagePayment());
                                    }
                                    tAdvanceOrderTmpService.jdYkfWkPayment(orderInfoVO);
                                    resultUtil = ResultUtil.ok();
                                    resultUtil.setData(tOrderInfo.getOrderBusinessCode());
                                    resultUtils.add(resultUtil);
                                } else {
                                    resultUtils.add(resultUtil);
                                }
                            } catch (Exception e) {
                                log.error("ZJJ-241:支付失败!", e);
                                String message = e.getMessage();
                                ResultUtil resultUtil = ResultUtil.error();
                                resultUtil.setData(tOrderInfo.getOrderBusinessCode());
                                if (StringUtils.checkChineseCharacter(message)) {
                                    resultUtil.setMsg(message);
                                } else {
                                    resultUtil.setMsg("ZJJ-241:支付失败!");
                                }
                                resultUtils.add(resultUtil);
                            }
                        }
                    } else {
                        List<ResultUtil> results = new ArrayList<>();
                        orderBusinesCodes.forEach(orderBusinesCode -> results.add(ResultUtil.error("企业正在操作中，请稍后再试。", orderBusinesCode)));
                        resultUtils.addAll(results);
                    }
                } catch (Exception e) {
                    log.error("ZJJ-241：支付失败！, {}", e);
                    String message = e.getMessage();
                    if (!StringUtils.checkChineseCharacter(message)) {
                        message = "ZJJ-241：支付失败！";
                    }
                    List<ResultUtil> results = new ArrayList<>();
                    final String msg = message;
                    orderBusinesCodes.forEach(orderBusinesCode -> results.add(ResultUtil.error(msg, orderBusinesCode)));
                    resultUtils.addAll(results);
                } finally {
                    if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                            printLockLog(lock, "分布式锁:unlock:");
                            lock.unlock();
                            log.info("分布式锁解锁");
                        }
                    }
                }
            }
        }
        return ResultUtil.ok(resultUtils);
    }

    private void printLockLog(RLock managerLock, String s) {
        log.info(s + managerLock.toString() + ",interrupted:" +
                Thread.currentThread().isInterrupted() + ",hold:" +
                managerLock.isHeldByCurrentThread() + ",threadId:" +
                Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2020/2/24 17:37
     *  @Description: 查询列表
     */
    @PostMapping("/selectByPage")
    public ResultUtil selectByPage(@RequestBody  TAdvanceOrderTmpDto record){
        try{
            return tAdvanceOrderTmpService.selectByPage(record);
        }catch (Exception e){
            log.error("预付款运单查询失败:",e);
            return ResultUtil.error("预付款运单查询失败");
        }
    }
    /**
     * @Author: cyp
     * @Description: 运单预支付导出
     * @Date: 2020/6/3
     * @return
     **/
    @PostMapping("/tAdvanceOrderTmpExport")
    public ResultUtil tAdvanceOrderTmpExport(@RequestBody  TAdvanceOrderTmpDto record){
        try{
            return tAdvanceOrderTmpService.tAdvanceOrderTmpExport(record);
        }catch (Exception e){
            log.error("预付款运单查询失败:",e);
            return ResultUtil.error("预付款运单查询失败");
        }
    }

    @PostMapping("/hx/pre/pay")
    public ResultUtil hxPrePay(@RequestBody List<TAdvanceOrderTmpExcel> list) {
        try {
            ResultUtil resultUtil = ResultUtil.ok();
            List<HashMap<String, Object>> resultUtils = new ArrayList<>();
            Map<Integer, Map<String, List<String>>> mapMap = new HashMap<>();
            if (null != list && !list.isEmpty()) {
                mapMap = hxOrderInfoService.selectAdvanceOrderGroupByCompanyId(list);
            }
            log.info("预付款运单信息, {}", JSONUtil.toJsonStr(mapMap));
            List<Integer> userCompanyIdInteger = CurrentUser.getUserCompanyIdInteger();
            if (userCompanyIdInteger.isEmpty()) {
                List<HashMap<String, Object>> results = new ArrayList<>();
                list.forEach(order -> {
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("orderCode", order.getOrderBusinessCode());
                    map.put("code", "error");
                    map.put("msg", "ZJJ-240：支付失败！当前用户无支付权限。");
                    results.add(map);
                });
                return ResultUtil.ok(results);
            }
            Map<String, TAdvanceOrderTmpExcel> tmpExcelMap = new HashMap<>();
            list.stream().forEach((item) -> tmpExcelMap.put(item.getOrderBusinessCode(), item));
            for (Map.Entry<Integer, Map<String, List<String>>> entry : mapMap.entrySet()) {
                Integer companyId = entry.getKey();
                Map<String, List<String>> listMap = entry.getValue();
                List<String> orderBusinesCodes = new ArrayList<>();
                for (Map.Entry<String, List<String>> listEntry : listMap.entrySet()) {
                    // 获取当前企业下的所有运单
                    if (listEntry.getKey().equals("order_business_code")) {
                        orderBusinesCodes = listEntry.getValue();
                    }
                }
                if (!userCompanyIdInteger.contains(companyId)) {
                    List<HashMap<String, Object>> results = new ArrayList<>();
                    orderBusinesCodes.forEach(orderBusinesCode -> {
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("orderCode", orderBusinesCode);
                        map.put("code", "error");
                        map.put("msg", "ZJJ-240：支付失败！当前用户无支付权限。");
                        results.add(map);
                    });
                    resultUtils.addAll(results);
                    continue;
                }
                SelectOpenRoleVO vo = new SelectOpenRoleVO();
                vo.setCompanyId(companyId);
                ResultUtil openRoleStatus = hxOpenRoleCommonAPI.selectCarrierCompanyEnduserOpenRoleStatus(vo);
                HXOpenRoleStatusListDTO dto = JSONUtil.toBean(JSONUtil.parseObj(openRoleStatus.getData()), HXOpenRoleStatusListDTO.class);
                log.info(JSONUtil.toJsonStr(dto));
                if (null == dto.getCompanyStatus()
                        || null == dto.getCompanyStatus().getStatus()
                        || !dto.getCompanyStatus().getStatus()
                        || null == dto.getCompanyStatus().getPartnerAccId()
                        || StringUtils.isBlank(dto.getCompanyStatus().getPartnerAccId())) {
                    List<HashMap<String, Object>> results = new ArrayList<>();
                    orderBusinesCodes.forEach(orderBusinesCode -> {
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("orderCode", orderBusinesCode);
                        map.put("code", "error");
                        map.put("msg", "ZJJ-240：支付失败！企业未开通华夏支付，请联系运营平台予以解决。");
                        results.add(map);
                    });
                    resultUtils.addAll(results);
                    continue;
                }
                String walletKey = dto.getCompanyStatus().getPartnerAccId();
                RLock lock = redissonClient.getLock(walletKey);
                try {
                    boolean walletLock = false;
                    printLockLog(lock, "分布式锁:lock:");
                    walletLock = lock.tryLock(5, TimeUnit.SECONDS);
                    printLockLog(lock, "分布式锁:tryLock:");
                    log.info("获取企业钱包锁，{}, {}", walletKey, walletLock);
                    if (walletLock) {
                        for (String orderBusinessCode : orderBusinesCodes) {
                            TAdvanceOrderTmpExcel advanceOrderTmpExcel = tmpExcelMap.get(orderBusinessCode);
                            // 检测运单
                            HashMap<String, Object> resultCheck = sendOrderUtil.checkPrePayment(advanceOrderTmpExcel);
                            if (null == resultCheck) {
                                HashMap<String, Object> result = new HashMap<>();
                                try {
                                    tAdvanceOrderTmpService.hxPrePayment(advanceOrderTmpExcel);
                                    result.put("orderCode", orderBusinessCode);
                                    result.put("code", "导入成功");
                                    resultUtils.add(result);
                                } catch (Exception e) {
                                    log.error("预付款运单导入失败!", e);
                                    String message = e.getMessage();
                                    resultUtil.setCode("error");
                                    if (!StringUtils.checkChineseCharacter(message)) {
                                        message = "预付款运单导入失败";
                                    }
                                    result.put("orderCode", orderBusinessCode);
                                    result.put("code", "error");
                                    result.put("msg", message);
                                    resultUtils.add(result);
                                }
                            } else {
                                resultUtils.add(resultCheck);
                            }
                        }
                    } else {
                        List<HashMap<String, Object>> results = new ArrayList<>();
                        orderBusinesCodes.forEach(orderBusinesCode -> {
                            HashMap<String, Object> map = new HashMap<>();
                            map.put("orderCode", orderBusinesCode);
                            map.put("code", "error");
                            map.put("msg", "ZJJ-240：支付失败！企业正在操作中，请稍后再试。");
                            results.add(map);
                        });
                        resultUtils.addAll(results);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("预付款运单导入失败!, {}", e);
                    HashMap<String, Object> result = new HashMap<>();
                    result.put("code", "error");
                    result.put("msg", "预付款运单导入失败");
                    resultUtils.add(result);
                } finally {
                    if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                        printLockLog(lock, "分布式锁:unlock:");
                        lock.unlock();
                        log.info("分布式锁解锁");
                    }
                }
            }
            if (resultUtils.size() > 0) {
                resultUtil.setCode("success");
                resultUtil.setData(resultUtils);
                log.info("287");
                return resultUtil;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("预付款运单导入失败!, {}", e);
            return ResultUtil.error("预付款运单导入失败");
        }
        log.info("294");
        return ResultUtil.ok();
    }

    /**
     * 华夏预支付尾款支付
     */
    @PostMapping("/hx/wk/pay")
    public ResultUtil hxWkPay(@RequestBody SendOrderVO record) {
        List<ResultUtil> resultUtils = new ArrayList<>();

        if (null != record.getCodes() && record.getCodes().length > 0) {
            List<Integer> userCompanyIdInteger = CurrentUser.getUserCompanyIdInteger();
            if (userCompanyIdInteger.isEmpty()) {
                List<String> orderBusinessCodes = tAdvanceOrderTmpService.selectOrderBusinessCode(Arrays.asList(record.getCodes()));
                List<ResultUtil> results = new ArrayList<>();
                orderBusinessCodes.forEach(orderBusinesCode -> results.add(ResultUtil.error("ZJJ-241：支付失败！当前用户无支付权限。", orderBusinesCode)));
                resultUtils.addAll(results);
                return ResultUtil.ok(resultUtils);
            }
            Map<Integer, Map<String, List<String>>> mapMap = hxOrderInfoService.selectOrderGroupByCompanyId(Arrays.asList(record.getCodes()));
            for (Map.Entry<Integer, Map<String, List<String>>> entry : mapMap.entrySet()) {
                Integer companyId = entry.getKey();
                Map<String, List<String>> listMap = entry.getValue();
                List<String> codes = new ArrayList<>();
                List<String> orderBusinesCodes = new ArrayList<>();
                for (Map.Entry<String, List<String>> listEntry : listMap.entrySet()) {
                    // 获取当前企业下的所有运单
                    if (listEntry.getKey().equals("codes")) {
                        codes = listEntry.getValue();
                    } else if (listEntry.getKey().equals("order_business_code")) {
                        orderBusinesCodes = listEntry.getValue();
                    }
                }
                // 判断是否使用华夏支付
                String checkAdvancePaymentPlatforms = advanceOrderPayTempService.checkAdvancePaymentPlatforms(codes, DictEnum.HXPLATFORMS.code);
                if (StringUtils.isNotBlank(checkAdvancePaymentPlatforms)) {
                    List<ResultUtil> results = new ArrayList<>();
                    orderBusinesCodes.forEach(orderBusinesCode -> results.add(ResultUtil.error("ZJJ-241：支付失败！" + checkAdvancePaymentPlatforms, orderBusinesCode)));
                    resultUtils.addAll(results);
                    return ResultUtil.ok(resultUtils);
                }
                SelectOpenRoleVO vo = new SelectOpenRoleVO();
                vo.setCompanyId(companyId);
                ResultUtil openRoleStatus = hxOpenRoleCommonAPI.selectCarrierCompanyEnduserOpenRoleStatus(vo);
                OpenRoleStatusListDTO dto = JSONUtil.toBean(JSONUtil.parseObj(openRoleStatus.getData()), OpenRoleStatusListDTO.class);
                log.info(JSONUtil.toJsonStr(dto));
                if (null == dto.getCompanyStatus()
                        || null == dto.getCompanyStatus().getStatus()
                        || !dto.getCompanyStatus().getStatus()
                        || null == dto.getCompanyStatus().getPartnerAccId()
                        || StringUtils.isBlank(dto.getCompanyStatus().getPartnerAccId())) {
                    List<ResultUtil> results = new ArrayList<>();
                    orderBusinesCodes.forEach(orderBusinesCode -> results.add(ResultUtil.error("ZJJ-241：支付失败！企业未开通京东支付，请联系运营平台予以解决。", orderBusinesCode)));
                    resultUtils.addAll(results);
                    continue;
                }
                String walletKey = dto.getCompanyStatus().getPartnerAccId();
                RLock lock = redissonClient.getLock(walletKey);
                try {
                    boolean walletLock = false;
                    printLockLog(lock, "分布式锁:lock:");
                    walletLock = lock.tryLock(5, TimeUnit.SECONDS);
                    printLockLog(lock, "分布式锁:tryLock:");
                    log.info("获取企业钱包锁，{}, {}", walletKey, walletLock);
                    if (walletLock) {
                        for (String orderCode : codes) {
                            TOrderInfo tOrderInfo = orderInfoService.selectOrderInfoByCode(orderCode);
                            try {
                                ResultUtil resultUtil = sendOrderUtil.checkWkPayment(tOrderInfo, record);
                                if (null == resultUtil) {
                                    TOrderInfoVO orderInfoVO = new TOrderInfoVO();
                                    orderInfoVO.setId(tOrderInfo.getId());
                                    orderInfoVO.setCode(tOrderInfo.getCode());
                                    if (null != record.getUserConfirmCarriagePayment()) {
                                        orderInfoVO.setNewUserConfirmCarriagePayment(record.getUserConfirmCarriagePayment());
                                    }
                                    tAdvanceOrderTmpService.hxYkfWkPayment(orderInfoVO);
                                    resultUtil = ResultUtil.ok();
                                    resultUtil.setData(tOrderInfo.getOrderBusinessCode());
                                    resultUtils.add(resultUtil);
                                } else {
                                    resultUtils.add(resultUtil);
                                }
                            } catch (Exception e) {
                                log.error("ZJJ-241:支付失败!", e);
                                String message = e.getMessage();
                                ResultUtil resultUtil = ResultUtil.error();
                                resultUtil.setData(tOrderInfo.getOrderBusinessCode());
                                if (StringUtils.checkChineseCharacter(message)) {
                                    resultUtil.setMsg(message);
                                } else {
                                    resultUtil.setMsg("ZJJ-241:支付失败!");
                                }
                                resultUtils.add(resultUtil);
                            }
                        }
                    } else {
                        List<ResultUtil> results = new ArrayList<>();
                        orderBusinesCodes.forEach(orderBusinesCode -> results.add(ResultUtil.error("企业正在操作中，请稍后再试。", orderBusinesCode)));
                        resultUtils.addAll(results);
                    }
                } catch (Exception e) {
                    log.error("ZJJ-241：支付失败！, {}", e);
                    String message = e.getMessage();
                    if (!StringUtils.checkChineseCharacter(message)) {
                        message = "ZJJ-241：支付失败！";
                    }
                    List<ResultUtil> results = new ArrayList<>();
                    final String msg = message;
                    orderBusinesCodes.forEach(orderBusinesCode -> results.add(ResultUtil.error(msg, orderBusinesCode)));
                    resultUtils.addAll(results);
                } finally {
                    if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                            printLockLog(lock, "分布式锁:unlock:");
                            lock.unlock();
                            log.info("分布式锁解锁");
                        }
                    }
                }
            }
        }
        return ResultUtil.ok(resultUtils);
    }

}
