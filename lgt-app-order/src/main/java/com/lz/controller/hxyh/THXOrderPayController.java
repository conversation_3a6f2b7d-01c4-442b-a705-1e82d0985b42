package com.lz.controller.hxyh;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.lz.api.HXOpenRoleCommonAPI;
import com.lz.api.MqAPI;
import com.lz.api.TZtAccountOpenInfoAPI;
import com.lz.common.dbenum.BusinessCode;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.HXTradeTypeEnum;
import com.lz.common.factory.HXMQMessageFactory;
import com.lz.common.model.MQMessage;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.common.util.ThrowableUtil;
import com.lz.dto.HXOpenRoleWalletListDTO;
import com.lz.dto.OpenRoleStatusListDTO;
import com.lz.model.*;
import com.lz.payment.hxyh.HXPayOrderUtil;
import com.lz.service.*;
import com.lz.service.hxyh.THXOrderPayService;
import com.lz.service.hxyh.THxOrderInfoService;
import com.lz.util.PayUtil;
import com.lz.vo.*;
import com.lz.vo.hxyh.HXOrderPayVO;
import com.lz.vo.hxyh.HXSinglePayVO;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 *  @author: dingweibo
 *  @Date: 2022/11/23 9:02
 *  @Description: 华夏银行支付
 */
@Slf4j
@RestController
@RequestMapping("/order/hx")
public class THXOrderPayController {

    private static final String P_ORDER = "P_ORDER";

    @Autowired
    private THxOrderInfoService hxOrderInfoService;

    @Autowired
    private TOrderInfoService orderInfoService;

    @Autowired
    private TOrderCastChangesService orderCastChangesService;

    @Autowired
    private THXOrderPayService hxOrderPayService;

    @Autowired
    private TOrderPackInfoService orderPackInfoService;

    @Autowired
    private TOrderPayInfoService orderPayInfoService;

    @Autowired
    private TOrderPayRuleService orderPayRuleService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private HXPayOrderUtil hxPayOrderUtil;

    @Autowired
    private MqAPI mqAPI;

    @Autowired
    private PayUtil payUtil;

    @Autowired
    private TZtAccountOpenInfoAPI tZtAccountOpenInfoAPI;

    @Autowired
    private HXOpenRoleCommonAPI hxOpenRoleCommonAPI;


    /**
     *  @author: dingweibo
     *  @Date: 2022/11/23 9:02
     *  @Description: 华夏银行实体运单单笔支付
     */
    @PostMapping("/pay")
    public ResultUtil pay(HttpServletRequest request, @Validated @RequestBody HXSinglePayVO payVO) {
        List<ResultUtil> resultUtils = new ArrayList<>();
        if (null == payVO.getCode() || StringUtils.isBlank(payVO.getCode())) {
            resultUtils.add(ResultUtil.error("请选择运单"));
            return ResultUtil.ok(resultUtils);
        }
        String walletKey = "";
        RLock lock = null;
        boolean walletLock = false;
        TOrderInfo orderInfo = orderInfoService.selectOrderInfoByCode(payVO.getCode());
        if (!payVO.getSourceChannel().equals(DictEnum.PAY_SOURCE_CHANNEL_PC.code)){
            payVO.setMacAddress(null);
        }
        ResultUtil checkPay = payUtil.checkPay(request,orderInfo.getCompanyId(),payVO.getMacAddress(),
                orderInfo.getOrderBusinessCode(),payVO.getPayPassword(),payVO.getSourceChannel());
        if (checkPay!=null){
            resultUtils.add(checkPay);
            return ResultUtil.ok(resultUtils);
        }
        try {
            TZtAccountOpenInfo tZtAccountOpenInfo = tZtAccountOpenInfoAPI.selectByCarrierIdAndCompanyIdAndEndUserIdByStatus(orderInfo.getCompanyId(),DictEnum.BD.code);
            log.info("华夏单笔支付前查询企业开户状态：{}",JSONUtil.toJsonStr(tZtAccountOpenInfo));
            if (tZtAccountOpenInfo.getStatus()!=1 || null == tZtAccountOpenInfo.getPartnerAccId()
                    || StringUtils.isBlank(tZtAccountOpenInfo.getPartnerAccId())) {
                ResultUtil resultUtil = ResultUtil.error("支付失败！企业未开通华夏，请联系运营平台予以解决。", orderInfo.getOrderBusinessCode());
                resultUtils.add(resultUtil);
                return ResultUtil.ok(resultUtils);
            }
            try {
                walletKey = tZtAccountOpenInfo.getPartnerAccId();
                lock = redissonClient.getLock(walletKey);
                printLockLog(lock, "分布式锁:getLock:");
                walletLock = lock.tryLock();
                printLockLog(lock, "分布式锁:tryLock:");
                log.info("获取企业钱包锁，{}, {}", walletKey, walletLock);
                if (walletLock) {
                    orderInfo = orderInfoService.selectOrderInfoByCode(payVO.getCode());
                    ResultUtil resultUtil = hxPayOrderUtil.checkOrderPay(orderInfo, payVO);
                    if (null == resultUtil) {
                        HXOrderPayVO hxOrderPayVO = new HXOrderPayVO();
                        hxOrderPayVO.setId(orderInfo.getId());
                        hxOrderPayVO.setCode(orderInfo.getCode());
                        hxOrderPayVO.setNewUserConfirmCarriagePayment(payVO.getUserConfirmCarriagePayment());
                        hxOrderPayVO.setServiceFee(payVO.getServiceFee());
                        hxOrderPayVO.setUserConfirmServiceFee(payVO.getUserConfirmServiceFee());
                        if (null != payVO.getBankId()) {
                            hxOrderPayVO.setBankId(payVO.getBankId());
                        } else if (null != orderInfo.getParam3()) {
                            hxOrderPayVO.setBankId(Integer.valueOf(orderInfo.getParam3()));
                        }
                        ResultUtil payResult = hxOrderPayService.pay(hxOrderPayVO);
                        payResult.setData(orderInfo.getOrderBusinessCode());
                        resultUtils.add(payResult);
                        return ResultUtil.ok(resultUtils);
                    } else {
                        resultUtil.setData(orderInfo.getOrderBusinessCode());
                        resultUtils.add(resultUtil);
                        return ResultUtil.ok(resultUtils);
                    }
                } else {
                    resultUtils.add(ResultUtil.error("企业正在支付，请稍后再试。", orderInfo.getOrderBusinessCode()));
                }
            } catch (Exception e) {
                log.error("支付失败！{}", e);
                String message = e.getMessage();
                if (StringUtils.checkChineseCharacter(message)) {
                    resultUtils.add(ResultUtil.error(message, orderInfo.getOrderBusinessCode()));
                    return ResultUtil.ok(resultUtils);
                } else {
                    resultUtils.add(ResultUtil.error("支付失败！"));
                    return ResultUtil.ok(resultUtils);
                }
            } finally {
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    printLockLog(lock, "分布式锁:finally:");
                    lock.unlock();
                    printLockLog(lock, "分布式锁:unlock:");
                }
            }
        } catch (Exception e) {
            log.error("支付失败！, {}", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                resultUtils.add(ResultUtil.error(message, orderInfo.getOrderBusinessCode()));
                return ResultUtil.ok(resultUtils);
            } else {
                resultUtils.add(ResultUtil.error("支付失败！"));
                return ResultUtil.ok(resultUtils);
            }
        }
        return ResultUtil.ok(resultUtils);
    }

    /**
     * @description 实体运单批量支付
     * <AUTHOR>
     * @date 2021/8/9 10:55
     */
    @PostMapping("/batch/pay")
    public ResultUtil batchPay(HttpServletRequest request, @RequestBody BatchPayVO payVO) {
        List<ResultUtil> resultUtils = new ArrayList<>();
        if (null == payVO.getCodes() || payVO.getCodes().length == 0) {
            resultUtils.add(ResultUtil.error("请选择运单"));
            return ResultUtil.ok(resultUtils);
        }
        Map<Integer, Map<String, List<String>>> mapMap = hxOrderInfoService.selectOrderGroupByCompanyId(Arrays.asList(payVO.getCodes()));

        if (mapMap.size()>1){
            for (Map.Entry<Integer, Map<String, List<String>>> entry : mapMap.entrySet()) {
                Map<String, List<String>> listMap = entry.getValue();
                List<String> orderBusinesCodes = new ArrayList<>();
                for (Map.Entry<String, List<String>> listEntry : listMap.entrySet()) {
                    if (listEntry.getKey().equals("order_business_code")) {
                        orderBusinesCodes = listEntry.getValue();
                    }
                }
                orderBusinesCodes.forEach(orderBusinesCode -> resultUtils.add(ResultUtil.error("请选择同一个企业的运单进行支付操作", orderBusinesCode)));
            }
            return ResultUtil.ok(resultUtils);
        }

        for (Map.Entry<Integer, Map<String, List<String>>> entry : mapMap.entrySet()) {
            Integer companyId = entry.getKey();
            Map<String, List<String>> listMap = entry.getValue();
            List<String> codes = new ArrayList<>();
            List<String> orderBusinesCodes = new ArrayList<>();
            for (Map.Entry<String, List<String>> listEntry : listMap.entrySet()) {
                // 获取当前企业下的所有运单
                if (listEntry.getKey().equals("codes")) {
                    codes = listEntry.getValue();
                } else if (listEntry.getKey().equals("order_business_code")) {
                    orderBusinesCodes = listEntry.getValue();
                }
            }
            if (mapMap.size()>1){
                orderBusinesCodes.forEach(orderBusinesCode -> resultUtils.add(ResultUtil.error("请选择同一个企业的运单进行支付操作", orderBusinesCode)));
                return ResultUtil.ok(resultUtils);
            }
            if (!payVO.getSourceChannel().equals(DictEnum.PAY_SOURCE_CHANNEL_PC.code)){
                payVO.setMacAddress(null);
            }
            ResultUtil checkPay = payUtil.checkPay(request, companyId, payVO.getMacAddress(),null,payVO.getPayPassword(),payVO.getSourceChannel());
            if (checkPay != null) {
                orderBusinesCodes.forEach(orderBusinesCode -> resultUtils.add(ResultUtil.error(checkPay.getMsg(), orderBusinesCode)));
                return ResultUtil.ok(resultUtils);
            }
            TZtAccountOpenInfo tZtAccountOpenInfo = tZtAccountOpenInfoAPI.selectByCarrierIdAndCompanyIdAndEndUserIdByStatus(companyId, DictEnum.BD.code);
            log.info("华夏单笔支付前查询企业开户状态：{}", JSONUtil.toJsonStr(tZtAccountOpenInfo));
            if (tZtAccountOpenInfo.getStatus() != 1 || null == tZtAccountOpenInfo.getPartnerAccId() || StringUtils.isBlank(tZtAccountOpenInfo.getPartnerAccId())) {
                List<ResultUtil> results = new ArrayList<>();
                orderBusinesCodes.forEach(orderBusinesCode -> results.add(ResultUtil.error("支付失败！企业未开通华夏支付，请联系运营平台予以解决。", orderBusinesCode)));
                resultUtils.addAll(results);
                continue;
            }
            String walletKey = tZtAccountOpenInfo.getPartnerAccId();
            RLock lock = redissonClient.getLock(walletKey);
            try {
                boolean walletLock = false;
                printLockLog(lock, "分布式锁:lock:");
                walletLock = lock.tryLock();
                printLockLog(lock, "分布式锁:tryLock:");
                log.info("获取企业钱包锁，{}, {}", walletKey, walletLock);
                if (walletLock) {
                    for (String orderCode : codes) {
                        TOrderInfo orderInfoByCoded = orderInfoService.selectOrderInfoByCode(orderCode);
                        try {
                            if (StringUtil.isNotEmpty(orderCode)) {
                                ResultUtil resultUtil;
                                resultUtil = hxPayOrderUtil.checkOrderPay(orderInfoByCoded, null);
                                if (null == resultUtil) {
                                    ResultUtil batchPay = hxOrderPayService.batchPay(orderInfoByCoded);
                                    batchPay.setData(orderInfoByCoded.getOrderBusinessCode());
                                    resultUtils.add(batchPay);
                                } else {
                                    resultUtil.setData(orderInfoByCoded.getOrderBusinessCode());
                                    resultUtils.add(resultUtil);
                                }
                            }
                        } catch (Exception e) {
                            log.error("支付失败！, {}", e);
                            String message = e.getMessage();
                            if (StringUtils.checkChineseCharacter(message)) {
                                resultUtils.add(ResultUtil.error(message, orderInfoByCoded.getOrderBusinessCode()));
                            } else {
                                resultUtils.add(ResultUtil.error("支付失败！", orderInfoByCoded.getOrderBusinessCode()));
                            }
                        }
                    }
                } else {
                    List<ResultUtil> results = new ArrayList<>();
                    orderBusinesCodes.forEach(orderBusinesCode -> results.add(ResultUtil.error("企业正在支付，请稍后再试。", orderBusinesCode)));
                    resultUtils.addAll(results);
                }
            } catch (Exception e) {
                log.error("支付失败！, {}", e);
                String message = e.getMessage();
                if (!StringUtils.checkChineseCharacter(message)) {
                    message = "支付失败！";
                }
                List<ResultUtil> results = new ArrayList<>();
                final String msg = message;
                orderBusinesCodes.forEach(orderBusinesCode -> results.add(ResultUtil.error(msg, orderBusinesCode)));
                resultUtils.addAll(results);
            } finally {
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    printLockLog(lock, "分布式锁:unlock:");
                    lock.unlock();
                    log.info("分布式锁解锁");
                }
            }
        }
        return ResultUtil.ok(resultUtils);
    }

    /**
    * @description 节点支付
    * <AUTHOR>
    * @date 2021/8/12 18:48
    */
    @PostMapping("/node/pay")
    public ResultUtil nodePay(HttpServletRequest request, @RequestBody NodePayVO payVO) {
        if (null == payVO.getCode() || StringUtils.isBlank(payVO.getCode())) {
            return ResultUtil.error("请选择运单");
        }
        TOrderInfo orderInfo = orderInfoService.selectOrderInfoByCode(payVO.getCode());
        if (null == orderInfo) {
            return ResultUtil.error("当前运单不存在");
        }
        if (!payVO.getSourceChannel().equals(DictEnum.PAY_SOURCE_CHANNEL_PC.code)){
            payVO.setMacAddress(null);
        }
        ResultUtil checkPay = payUtil.checkPay(request,orderInfo.getCompanyId(),payVO.getMacAddress(),orderInfo.getOrderBusinessCode(),
                payVO.getPayPassword(),payVO.getSourceChannel());
        if (checkPay!=null){
            return ResultUtil.error(checkPay.getMsg());
        }
        if (DictEnum.PACK.code.equals(orderInfo.getPackStatus())) {
            return ResultUtil.error("打包原始运单不可在此支付");
        }
        if(DictEnum.WQD.code.equals(orderInfo.getContractStatus())){
            return ResultUtil.error("当前运单未签订合同，不允许支付");
        }
        String walletKey;
        boolean walletLock = false;
        TZtAccountOpenInfo tZtAccountOpenInfo = tZtAccountOpenInfoAPI.selectByCarrierIdAndCompanyIdAndEndUserIdByStatus(orderInfo.getCompanyId(), DictEnum.BD.code);
        log.info("华夏单笔支付前查询企业开户状态：{}",JSONUtil.toJsonStr(tZtAccountOpenInfo));
        if (tZtAccountOpenInfo.getStatus()!=1 || null == tZtAccountOpenInfo.getPartnerAccId()
                || StringUtils.isBlank(tZtAccountOpenInfo.getPartnerAccId())) {
            return ResultUtil.error("支付失败！企业未开通华夏，请联系运营平台予以解决。", orderInfo.getOrderBusinessCode());
        }
        // 查询已支付节点支付使用的支付平台
        TOrderPayRule orderPayRule = orderPayRuleService.selectPaidNode(orderInfo.getCode());
        if (null != orderPayRule && null != orderPayRule.getParam1() && !DictEnum.HXPLATFORMS.code.equals(orderPayRule.getParam1())) {
            return ResultUtil.error("支付失败！当前运单不支持华夏平台进行支付。", orderInfo.getOrderBusinessCode());
        }
        walletKey = tZtAccountOpenInfo.getPartnerAccId();
        RLock lock = redissonClient.getLock(walletKey);
        try {
            printLockLog(lock, "分布式锁:lock:");
            walletLock = lock.tryLock();
            printLockLog(lock, "分布式锁:tryLock:");
            log.info("获取企业钱包锁，{}, {}", walletKey, walletLock);
            if (walletLock) {
                orderInfo = orderInfoService.selectOrderInfoByCode(payVO.getCode());
                ResultUtil resultUtil = hxPayOrderUtil.checkNodePay(orderInfo, payVO);
                if (null == resultUtil) {
                    resultUtil = hxOrderPayService.nodePay(payVO);
                }
                resultUtil.setData(orderInfo.getOrderBusinessCode());
                return resultUtil;
            } else {
                return ResultUtil.error("企业正在支付，请稍后再试。", orderInfo.getOrderBusinessCode());
            }
        } catch (Exception e) {
            log.error("支付失败！{}", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message, orderInfo.getOrderBusinessCode());
            } else {
                return ResultUtil.error("支付失败！", orderInfo.getOrderBusinessCode());
            }
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                printLockLog(lock, "分布式锁:unlock:");
                lock.unlock();
                log.info("分布式锁解锁");
            }
        }
    }

    /**
     * @description 打包运单支付
     * <AUTHOR>
     * @date 2021/8/9 10:55
     */
    @PostMapping("/pack/pay")
    public ResultUtil packPay(HttpServletRequest request, @RequestBody PackPayVO payVO) {
        List<ResultUtil> resultUtils = new ArrayList<>();
        if (null == payVO.getCodes() || payVO.getCodes().isEmpty()) {
            resultUtils.add(ResultUtil.error("请选择运单"));
            return ResultUtil.ok(resultUtils);
        }
        String code = payVO.getCodes().get(0);
        // 查询打包主表
        TOrderPackInfo tOrderPackInfo = orderPackInfoService.selectByCode(code);
        if (!payVO.getSourceChannel().equals(DictEnum.PAY_SOURCE_CHANNEL_PC.code)){
            payVO.setMacAddress(null);
        }
        ResultUtil checkPay = payUtil.checkPay(request,tOrderPackInfo.getCompanyId(),payVO.getMacAddress(),
                tOrderPackInfo.getVirtualOrderNo(),payVO.getPayPassword(),payVO.getSourceChannel());
        if (checkPay!=null){
            resultUtils.add(checkPay);
            return ResultUtil.ok(resultUtils);
        }
        // 查询企业会员编号
        TZtAccountOpenInfo tZtAccountOpenInfo = tZtAccountOpenInfoAPI.selectByCarrierIdAndCompanyIdAndEndUserIdByStatus(tOrderPackInfo.getCompanyId(), DictEnum.BD.code);
        log.info("华夏单笔支付前查询企业开户状态：{}",JSONUtil.toJsonStr(tZtAccountOpenInfo));
        if (tZtAccountOpenInfo.getStatus()!=1 || null == tZtAccountOpenInfo.getPartnerAccId()
                || StringUtils.isBlank(tZtAccountOpenInfo.getPartnerAccId())) {
            ResultUtil resultUtil = ResultUtil.error("支付失败！企业未开通华夏，请联系运营平台予以解决。", tOrderPackInfo.getVirtualOrderNo());
            resultUtils.add(resultUtil);
            return ResultUtil.ok(resultUtils);
        }
        // 查询企业钱包余额
        try {
            SelectOpenRoleVO vo = new SelectOpenRoleVO();
            vo.setCompanyId(tOrderPackInfo.getCompanyId());
            ResultUtil openRoleWallet = hxOpenRoleCommonAPI.selectCarrierCompanyEnduserOpenRoleWallet(vo);
            Object data = openRoleWallet.getData();
            HXOpenRoleWalletListDTO walletListDTO = JSONUtil.toBean(JSONUtil.toJsonStr(data), HXOpenRoleWalletListDTO.class);
            TZtWallet companyWallet = walletListDTO.getCompanyWallet();
            if (companyWallet.getAccountBalance().compareTo(tOrderPackInfo.getAppointmentPaymentCash().add(tOrderPackInfo.getRecountDispatchFee())) < 0) {
                resultUtils.add(ResultUtil.error("支付失败！当前货源企业钱包余额不足，请联系企业管理员/会计充值后再支付。", tOrderPackInfo.getVirtualOrderNo()));
                return ResultUtil.ok(resultUtils);
            }
        } catch (Exception e) {
            log.error("支付失败! 打包支付，查询企业钱包失败！, {}", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                resultUtils.add(ResultUtil.error(message));
            } else {
                resultUtils.add(ResultUtil.error("支付失败!", tOrderPackInfo.getVirtualOrderNo()));
            }
            return ResultUtil.ok(resultUtils);
        }
        String walletKey = tZtAccountOpenInfo.getPartnerAccId();
        boolean walletLock = false;
        RLock lock = redissonClient.getLock(walletKey);
        try {
            printLockLog(lock, "分布式锁:getLock:");
            walletLock = lock.tryLock();
            printLockLog(lock, "分布式锁:tryLock:");
            if (walletLock) {
                tOrderPackInfo = orderPackInfoService.selectByCode(code);
                if (tOrderPackInfo.getPackStatus().equals(DictEnum.PACKED.code)
                        || tOrderPackInfo.getPackStatus().equals(DictEnum.PACKORDERPAIDERROR.code)
                        || tOrderPackInfo.getPackStatus().equals(DictEnum.PACKRECALL.code)) {
                    ResultUtil checkPackPay = hxPayOrderUtil.checkPackPay(tOrderPackInfo);
                    if (null != checkPackPay) {
                        resultUtils.add(checkPackPay);
                        return ResultUtil.ok(resultUtils);
                    }

                    ResultUtil resultUtil = hxOrderPayService.packPay(code);
                    // 发送MQ消息
                    TOrderCastChanges castChanges = orderCastChangesService.selectOrderCastChangeOneByPackCode(tOrderPackInfo.getCode());
                    List<String> orderCodes = orderPackInfoService.getOrderCodesByPackId(tOrderPackInfo.getId());
                    MQMessage message = HXMQMessageFactory.getMessage(HXTradeTypeEnum.DBRZ.code, castChanges.getCapitalTransferType(), castChanges.getCapitalTransferPattern());
                    message.setKey(tOrderPackInfo.getCode());
                    message.setBody(String.join(",", orderCodes));
                    ResultUtil mqResult = mqAPI.sendMessage(message);
                    log.info("发送MQ消息结果，{}", JSONUtil.toJsonStr(mqResult));
                    if (DictEnum.ERROR.code.equals(mqResult.getCode())) {
                        throw new RuntimeException("打包支付失败！");
                    }
                    resultUtil.setData(tOrderPackInfo.getVirtualOrderNo());
                    resultUtils.add(resultUtil);
                } else {
                    resultUtils.add(ResultUtil.error("运单状态错误，不可支付", tOrderPackInfo.getVirtualOrderNo()));
                }
            } else {
                resultUtils.add(ResultUtil.error("企业正在支付，请稍后再试。", tOrderPackInfo.getVirtualOrderNo()));
            }
        } catch (Exception e) {
            log.error("支付失败！{}", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                resultUtils.add(ResultUtil.error(message, tOrderPackInfo.getVirtualOrderNo()));
            } else {
                resultUtils.add(ResultUtil.error("支付失败！", tOrderPackInfo.getVirtualOrderNo()));
            }
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                printLockLog(lock, "分布式锁:if:");
                lock.unlock();
                log.info("分布式锁解锁");
            }
        }
        return ResultUtil.ok(resultUtils);
    }

    /**
    * @description 单笔退款
    * <AUTHOR>
    * @date 2021/8/24 19:31
    */
    @PostMapping("/refund")
    public ResultUtil refund(@RequestBody RefundPayVO vo) {
        if (null == vo || null == vo.getCodes() || vo.getCodes().length == 0) {
            return ResultUtil.error("运单不能为空");
        }
        String[] codes = vo.getCodes();
        List<ResultUtil> resultUtils = new ArrayList<>();
        try {
            for (String orderCode : codes) {
                ResultUtil resultUtil = ResultUtil.ok();
                TOrderInfo orderInfo = orderInfoService.selectOrderInfoByCode(orderCode);
                try {
                    String orderBusinessCode = orderInfo.getOrderBusinessCode();
                    if (orderInfo.getPackStatus().equals("1")) {
                        resultUtil.setData(orderBusinessCode);
                        resultUtil.setMsg("打包运单不可在此召回");
                        resultUtil.setCode("error");
                        resultUtils.add(resultUtil);
                        continue;
                    }
                    TOrderPayInfo orderPayInfo = new TOrderPayInfo();
                    orderPayInfo.setOrderCode(orderCode);
                    orderPayInfo.setOrderPayStatus(DictEnum.M090.code);
                    orderPayInfo = orderPayInfoService.selectPayInfoBySelective(orderPayInfo);
                    if (!DictEnum.HXPLATFORMS.code.equals(orderPayInfo.getPaymentPlatforms())) {
                        resultUtil.setData(orderBusinessCode);
                        resultUtil.setMsg("当前运单不支持华夏召回，请重新选择。");
                        resultUtil.setCode("error");
                        resultUtils.add(resultUtil);
                        continue;
                    }
                    // 获取同步处理中的运单
                    try {
                        Set<Object> objects = redissonClient.getSet(P_ORDER).readAll();
                        if (null != objects && !objects.isEmpty() && objects.contains(orderCode)) {
                            resultUtil.setData(orderInfo.getOrderBusinessCode());
                            resultUtil.setMsg("当前运单正在处理中，请稍候再试。");
                            resultUtil.setCode("error");
                            resultUtils.add(resultUtil);
                            continue;
                        }
                        // 将当前运单添加到同步处理运单中
                        redissonClient.getSet(P_ORDER).add(orderCode);
                    } catch (Exception e) {
                        log.error("获取P_ORDER异常：{}", ThrowableUtil.getStackTrace(e));
                    }
                    // 检查运单是否已开票
                    String checkInvoiceOrder = hxPayOrderUtil.checkInvoiceOrder(Collections.singletonList(orderInfo.getCode()));
                    if (null != checkInvoiceOrder) {
                        resultUtil.setData(orderBusinessCode);
                        resultUtil.setMsg(checkInvoiceOrder);
                        resultUtil.setCode("error");
                        resultUtils.add(resultUtil);
                        // 将当前运单添加到同步处理运单中
                        redissonClient.getSet(P_ORDER).remove(orderCode);
                        continue;
                    }
                    boolean lock = false;
                    boolean driverWalletLock = true;
                    boolean captainWalletLock = true;
                    boolean managerWalletLock = true;
                    TOrderCastChanges tOrderCastChanges = orderCastChangesService.selectByNewOne(orderCode);
                    SelectOpenRoleVO openRoleVO = new SelectOpenRoleVO();
                    if (DictEnum.MANAGERPATTERN.code.equals(tOrderCastChanges.getCapitalTransferPattern())
                            && DictEnum.PAYTODRIVER.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                        openRoleVO.setDriverId(orderInfo.getEndDriverId());
                        openRoleVO.setManagerId(orderInfo.getAgentId());
                        ResultUtil openRoleStatus = hxOpenRoleCommonAPI.selectCarrierCompanyEnduserOpenRoleStatus(openRoleVO);
                        OpenRoleStatusListDTO dto = JSONUtil.toBean(JSONUtil.parseObj(openRoleStatus.getData()), OpenRoleStatusListDTO.class);
                        String driverAccId = dto.getDriverStatus().getPartnerAccId();
                        String managerAccId = dto.getManagerStatus().getPartnerAccId();
                        RLock driverLock = redissonClient.getLock(driverAccId);
                        RLock managerLock = redissonClient.getLock(managerAccId);
                        printLockLog(driverLock, "分布式锁:lock:");
                        printLockLog(managerLock, "分布式锁:lock:");
                        driverWalletLock = driverLock.tryLock(5, TimeUnit.SECONDS);
                        managerWalletLock = managerLock.tryLock(5, TimeUnit.SECONDS);
                        printLockLog(driverLock, "分布式锁:tryLock:");
                        printLockLog(managerLock, "分布式锁:tryLock:");
                        lock = driverWalletLock && managerWalletLock && true;
                        if (lock) {
                            try {
                                TOrderInfo order = orderInfoService.selectOrderInfoByCode(orderCode);
                                if ((order.getOrderExecuteStatus().equals(DictEnum.M100.code) && order.getOrderPayStatus().equals(DictEnum.M090.code))
                                        || order.getOrderPayStatus().equals(DictEnum.M120.code)) {
                                    hxOrderPayService.refund(orderCode, vo.getStateRemark());
                                    resultUtil.setMsg(DictEnum.ORDERBACKUCCESS.code);
                                    resultUtil.setData(orderBusinessCode);
                                } else {
                                    resultUtil.setData(orderBusinessCode);
                                    resultUtil.setMsg("运单状态错误，无法召回");
                                    resultUtil.setCode("error");
                                }
                                resultUtils.add(resultUtil);
                            } catch (Exception e) {
                                log.error("运单召回失败!", e);
                                resultUtil.setData(orderBusinessCode);
                                resultUtil.setCode("error");
                                String message = e.getMessage();
                                if (message.contains(BusinessCode.PWALLETNSUFFICIENT.code) || (message.contains(BusinessCode.CWALLETNSUFFICIENT.code))){
                                    resultUtil.setMsg("用户余额不足");
                                } else if (StringUtils.checkChineseCharacter(message)) {
                                    resultUtil.setMsg(message);
                                } else {
                                    log.error("运单召回失败!", e);
                                    resultUtil.setMsg("运单召回失败!");
                                }
                                resultUtils.add(resultUtil);
                            } finally {
                                try {
                                    // 将当前运单从同步处理运单中移除
                                    redissonClient.getSet(P_ORDER).remove(orderCode);

                                } catch (Exception e) {
                                    log.error("重置P_ORDER异常：{}", ThrowableUtil.getStackTrace(e));
                                }
                                if (driverLock.isLocked() && driverLock.isHeldByCurrentThread()) {
                                    printLockLog(driverLock, "分布式锁:unlock:");
                                    driverLock.unlock();
                                    log.info("分布式锁解锁");
                                }
                                if (managerLock.isLocked() && managerLock.isHeldByCurrentThread()) {
                                    printLockLog(managerLock, "分布式锁:unlock:");
                                    managerLock.unlock();
                                    log.info("分布式锁解锁");
                                }
                            }
                        } else {
                            resultUtil = ResultUtil.error("企业正在操作中，请稍后再试");
                            resultUtil.setData(orderBusinessCode);
                            resultUtils.add(resultUtil);
                        }
                    } else if (DictEnum.MANAGERPATTERN.code.equals(tOrderCastChanges.getCapitalTransferPattern())
                            && DictEnum.PAYTOCAPTAIN.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                        openRoleVO.setCaptainId(orderInfo.getEndCarOwnerId());
                        openRoleVO.setManagerId(orderInfo.getAgentId());
                        ResultUtil openRoleStatus = hxOpenRoleCommonAPI.selectCarrierCompanyEnduserOpenRoleStatus(openRoleVO);
                        OpenRoleStatusListDTO dto = JSONUtil.toBean(JSONUtil.parseObj(openRoleStatus.getData()), OpenRoleStatusListDTO.class);
                        String captainAccId = dto.getCaptionStatus().getPartnerAccId();
                        String managerAccId = dto.getManagerStatus().getPartnerAccId();
                        RLock captainLock = redissonClient.getLock(captainAccId);
                        RLock managerLock = redissonClient.getLock(managerAccId);
                        printLockLog(captainLock, "分布式锁:lock:");
                        printLockLog(managerLock, "分布式锁:lock:");
                        captainWalletLock = captainLock.tryLock(5, TimeUnit.SECONDS);
                        managerWalletLock = managerLock.tryLock(5, TimeUnit.SECONDS);
                        printLockLog(captainLock, "分布式锁:tryLock:");
                        printLockLog(managerLock, "分布式锁:tryLock:");
                        lock = captainWalletLock && managerWalletLock && true;
                        if (lock) {
                            try {
                                TOrderInfo order = orderInfoService.selectOrderInfoByCode(orderCode);
                                if ((order.getOrderExecuteStatus().equals(DictEnum.M100.code) && order.getOrderPayStatus().equals(DictEnum.M090.code))
                                        || order.getOrderPayStatus().equals(DictEnum.M120.code)) {
                                    hxOrderPayService.refund(orderCode, vo.getStateRemark());
                                    resultUtil.setMsg(DictEnum.ORDERBACKUCCESS.code);
                                    resultUtil.setData(orderBusinessCode);
                                } else {
                                    resultUtil.setData(orderBusinessCode);
                                    resultUtil.setMsg("运单状态错误，无法召回");
                                    resultUtil.setCode("error");
                                }
                                resultUtils.add(resultUtil);
                            } catch (Exception e) {
                                log.error("运单召回失败!", e);
                                resultUtil.setData(orderBusinessCode);
                                resultUtil.setCode("error");
                                String message = e.getMessage();
                                if (message.contains(BusinessCode.PWALLETNSUFFICIENT.code) || (message.contains(BusinessCode.CWALLETNSUFFICIENT.code))){
                                    resultUtil.setMsg("用户余额不足");
                                } else if (StringUtils.checkChineseCharacter(message)) {
                                    resultUtil.setMsg(message);
                                } else {
                                    log.error("运单召回失败!", e);
                                    resultUtil.setMsg("运单召回失败!");
                                }
                                resultUtils.add(resultUtil);
                            } finally {
                                try {
                                    // 将当前运单从同步处理运单中移除
                                    redissonClient.getSet(P_ORDER).remove(orderCode);
                                } catch (Exception e) {
                                    log.error("重置P_ORDER异常：{}", ThrowableUtil.getStackTrace(e));
                                }
                                if (captainLock.isLocked() && captainLock.isHeldByCurrentThread()) {
                                    printLockLog(captainLock, "分布式锁:unlock:");
                                    captainLock.unlock();
                                    log.info("分布式锁解锁");
                                }
                                if (managerLock.isLocked() && managerLock.isHeldByCurrentThread()) {
                                    printLockLog(managerLock, "分布式锁:unlock:");
                                    managerLock.unlock();
                                    log.info("分布式锁解锁");
                                }
                            }
                        } else {
                            resultUtil = ResultUtil.error("企业正在操作中，请稍后再试");
                            resultUtil.setData(orderBusinessCode);
                            resultUtils.add(resultUtil);
                        }
                    } else if (DictEnum.COMMONPATTERN.code.equals(tOrderCastChanges.getCapitalTransferPattern())
                            && DictEnum.PAYTODRIVER.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                        openRoleVO.setDriverId(orderInfo.getEndDriverId());
                        ResultUtil openRoleStatus = hxOpenRoleCommonAPI.selectCarrierCompanyEnduserOpenRoleStatus(openRoleVO);
                        OpenRoleStatusListDTO dto = JSONUtil.toBean(JSONUtil.parseObj(openRoleStatus.getData()), OpenRoleStatusListDTO.class);
                        String driverAccId = dto.getDriverStatus().getPartnerAccId();
                        RLock driverLock = redissonClient.getLock(driverAccId);
                        printLockLog(driverLock, "分布式锁:tryLock:");
                        driverWalletLock = driverLock.tryLock(5, TimeUnit.SECONDS);
                        printLockLog(driverLock, "分布式锁:tryLock:");
                        lock = driverWalletLock;
                        if (lock) {
                            try {
                                TOrderInfo order = orderInfoService.selectOrderInfoByCode(orderCode);
                                if ((order.getOrderExecuteStatus().equals(DictEnum.M100.code) && order.getOrderPayStatus().equals(DictEnum.M090.code))
                                        || order.getOrderPayStatus().equals(DictEnum.M120.code)) {
                                    hxOrderPayService.refund(orderCode, vo.getStateRemark());
                                    resultUtil.setMsg(DictEnum.ORDERBACKUCCESS.code);
                                    resultUtil.setData(orderBusinessCode);
                                } else {
                                    resultUtil.setData(orderBusinessCode);
                                    resultUtil.setMsg("运单状态错误，无法召回");
                                    resultUtil.setCode("error");
                                }
                                resultUtils.add(resultUtil);
                            } catch (Exception e) {
                                log.error("运单召回失败!", e);
                                resultUtil.setData(orderBusinessCode);
                                resultUtil.setCode("error");
                                String message = e.getMessage();
                                if (message.contains(BusinessCode.PWALLETNSUFFICIENT.code) || (message.contains(BusinessCode.CWALLETNSUFFICIENT.code))){
                                    resultUtil.setMsg("用户余额不足");
                                } else if (StringUtils.checkChineseCharacter(message)) {
                                    resultUtil.setMsg(message);
                                } else {
                                    log.error("运单召回失败!", e);
                                    resultUtil.setMsg("运单召回失败!");
                                }
                                resultUtils.add(resultUtil);
                            } finally {
                                try {
                                    redissonClient.getSet(P_ORDER).remove(orderCode);
                                } catch (Exception e) {
                                    log.error("重置P_ORDER异常：{}", ThrowableUtil.getStackTrace(e));
                                }
                                if (driverLock.isLocked() && driverLock.isHeldByCurrentThread()) {
                                    printLockLog(driverLock, "分布式锁:unlock:");
                                    driverLock.unlock();
                                    log.info("分布式锁解锁");
                                }
                            }
                        } else {
                            resultUtil = ResultUtil.error("企业正在操作中，请稍后再试");
                            resultUtil.setData(orderBusinessCode);
                            resultUtils.add(resultUtil);
                        }
                    } else if (DictEnum.COMMONPATTERN.code.equals(tOrderCastChanges.getCapitalTransferPattern())
                            && DictEnum.PAYTOCAPTAIN.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                        openRoleVO.setCaptainId(orderInfo.getEndCarOwnerId());
                        ResultUtil openRoleStatus = hxOpenRoleCommonAPI.selectCarrierCompanyEnduserOpenRoleStatus(openRoleVO);
                        OpenRoleStatusListDTO dto = JSONUtil.toBean(JSONUtil.parseObj(openRoleStatus.getData()), OpenRoleStatusListDTO.class);
                        String captainAcccId = dto.getCaptionStatus().getPartnerAccId();
                        RLock captainLcok = redissonClient.getLock(captainAcccId);
                        printLockLog(captainLcok, "分布式锁:tryLock:");
                        captainWalletLock = captainLcok.tryLock(5, TimeUnit.SECONDS);
                        printLockLog(captainLcok, "分布式锁:tryLock:");
                        lock = captainWalletLock;
                        if (lock) {
                            try {
                                TOrderInfo order = orderInfoService.selectOrderInfoByCode(orderCode);
                                if ((order.getOrderExecuteStatus().equals(DictEnum.M100.code) && order.getOrderPayStatus().equals(DictEnum.M090.code))
                                        || order.getOrderPayStatus().equals(DictEnum.M120.code)) {
                                    hxOrderPayService.refund(orderCode, vo.getStateRemark());
                                    resultUtil.setMsg(DictEnum.ORDERBACKUCCESS.code);
                                    resultUtil.setData(orderBusinessCode);
                                } else {
                                    resultUtil.setData(orderBusinessCode);
                                    resultUtil.setMsg("运单状态错误，无法召回");
                                    resultUtil.setCode("error");
                                }
                                resultUtils.add(resultUtil);
                            } catch (Exception e) {
                                log.error("运单召回失败!, {}", ThrowableUtil.getStackTrace(e));
                                resultUtil.setData(orderBusinessCode);
                                resultUtil.setCode("error");
                                String message = e.getMessage();
                                if (message.contains(BusinessCode.PWALLETNSUFFICIENT.code) || (message.contains(BusinessCode.CWALLETNSUFFICIENT.code))){
                                    resultUtil.setMsg("用户余额不足");
                                } else if (StringUtils.checkChineseCharacter(message)) {
                                    resultUtil.setMsg(message);
                                } else {
                                    log.error("运单召回失败!, {}", ThrowableUtil.getStackTrace(e));
                                    resultUtil.setMsg("运单召回失败!");
                                }
                                resultUtils.add(resultUtil);
                            } finally {
                                try {
                                    // 将当前运单从同步处理运单中移除
                                    redissonClient.getSet(P_ORDER).remove(orderCode);
                                } catch (Exception e) {
                                    log.error("重置P_ORDER异常：{}", ThrowableUtil.getStackTrace(e));
                                }
                                if (captainLcok.isLocked() && captainLcok.isHeldByCurrentThread()) {
                                    printLockLog(captainLcok, "分布式锁:unlock:");
                                    captainLcok.unlock();
                                    log.info("分布式锁解锁");
                                }
                            }
                        } else {
                            resultUtil = ResultUtil.error("企业正在操作中，请稍后再试");
                            resultUtil.setData(orderBusinessCode);
                            resultUtils.add(resultUtil);
                        }
                    }
                } catch (Exception e) {
                    log.error("运单召回失败!, {}", ThrowableUtil.getStackTrace(e));
                    resultUtil = ResultUtil.error();
                    resultUtil.setData(orderInfo.getOrderBusinessCode());
                    String message = e.getMessage();
                    if (StringUtils.checkChineseCharacter(message)) {
                        resultUtil.setMsg(message);
                    } else {
                        log.error("运单召回失败!, {}", ThrowableUtil.getStackTrace(e));
                        resultUtil.setMsg("运单召回失败!");
                    }
                    resultUtils.add(resultUtil);
                }
            }
        } catch (Exception e) {
            ResultUtil resultUtil = ResultUtil.error();
            log.error("运单召回失败!, {}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                resultUtil.setMsg(message);
                return resultUtil;
            } else {
                resultUtil.setMsg("运单召回失败!");
            }
            return resultUtil;
        }
        return ResultUtil.ok(resultUtils);
    }

    private void printLockLog(RLock managerLock, String s) {
        log.info(s + managerLock.toString() + ",interrupted:" +
                Thread.currentThread().isInterrupted() + ",hold:" +
                managerLock.isHeldByCurrentThread() + ",threadId:" +
                Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
    }

    /**
    * @description 打包退款
    * <AUTHOR>
    * @date 2021/8/24 19:31
    */
    @PostMapping("/packRefund")
    public ResultUtil packRefund(@RequestBody PackRefundPayVO vo) {
        if (null == vo.getCodes() || vo.getCodes().isEmpty()) {
            return ResultUtil.error("运单不能为空");
        }
        String packCode = vo.getCodes().get(0);
        TOrderPackInfo tOrderPackInfo = orderPackInfoService.selectPackInfoByCode(packCode);
        if (null == tOrderPackInfo) {
            return ResultUtil.error("运单不存在");
        }
        List<ResultUtil> resultUtils = new ArrayList<>(1);
        if (!DictEnum.HXPLATFORMS.code.equals(tOrderPackInfo.getPaymentPlatforms())) {
            resultUtils.add(ResultUtil.error("当前运单不支持华夏召回，请重新选择。", tOrderPackInfo.getVirtualOrderNo()));
            return ResultUtil.ok(resultUtils);
        }
        List<String> orderCodesByPackId = orderPackInfoService.getOrderCodesByPackId(tOrderPackInfo.getId());

        // 获取同步处理中的运单
        ;
        try {
            RSet<Object> objectRSet = redissonClient.getSet(P_ORDER);
            Set<Object> objects = objectRSet.readAll();

            if (null != objects && !objects.isEmpty() && CollUtil.containsAny(objects, orderCodesByPackId)) {
                resultUtils.add(ResultUtil.error("当前运单正在处理中，请稍候再试。", tOrderPackInfo.getVirtualOrderNo()));
                return ResultUtil.ok(resultUtils);
            }
            // 将当前运单添加到同步处理运单中
            orderCodesByPackId.forEach((code) -> redissonClient.getSet(P_ORDER).add(code));
        } catch (Exception e) {
            log.error("获取P_ORDER异常：{}", ThrowableUtil.getStackTrace(e));
        }

        // 检查运单是否已开票
        String checkInvoiceOrder = hxPayOrderUtil.checkInvoiceOrder(orderCodesByPackId);
        if (null != checkInvoiceOrder) {
            resultUtils.add(ResultUtil.error(checkInvoiceOrder, tOrderPackInfo.getVirtualOrderNo()));
            // 将当前运单添加到同步处理运单中
            RSet<Object> objectRSet = redissonClient.getSet(P_ORDER);
            orderCodesByPackId.forEach(objectRSet::remove);
            return ResultUtil.ok(resultUtils);
        }
        TOrderInfo orderInfo = orderInfoService.selectOrderInfoOneByPackCode(packCode);
        TOrderCastChanges tOrderCastChanges = orderCastChangesService.selectOrderCastChangeOneByPackCode(packCode);
        boolean lock = false;
        try {
            SelectOpenRoleVO openRoleVO = new SelectOpenRoleVO();
            if (DictEnum.MANAGERPATTERN.code.equals(tOrderCastChanges.getCapitalTransferPattern())) {
                openRoleVO.setManagerId(orderInfo.getAgentId());
            }
            if (DictEnum.PAYTODRIVER.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                openRoleVO.setDriverId(orderInfo.getEndDriverId());
            }
            if (DictEnum.PAYTOCAPTAIN.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                openRoleVO.setCaptainId(orderInfo.getEndCarOwnerId());
            }
            ResultUtil openRoleStatus = hxOpenRoleCommonAPI.selectCarrierCompanyEnduserOpenRoleStatus(openRoleVO);
            OpenRoleStatusListDTO dto = JSONUtil.toBean(JSONUtil.parseObj(openRoleStatus.getData()), OpenRoleStatusListDTO.class);
            String driverAccId = null != openRoleVO.getDriverId() ? dto.getDriverStatus().getPartnerAccId() : null;
            String captainAccId = null != openRoleVO.getCaptainId() ? dto.getCaptionStatus().getPartnerAccId() : null;
            String managerAccId = null != openRoleVO.getManagerId() ? dto.getManagerStatus().getPartnerAccId() : null;
            RLock driverLock = null;
            RLock captainLock = null;
            RLock managerLock = null;
            boolean driverWalletLock = true;
            boolean captainWalletLock = true;
            boolean managerWalletLock = true;
            if (StringUtils.isNotBlank(driverAccId)) {
                driverLock = redissonClient.getLock(driverAccId);
                driverWalletLock = driverLock.tryLock();
            }
            if (StringUtils.isNotBlank(captainAccId)) {
                captainLock = redissonClient.getLock(captainAccId);
                captainWalletLock = captainLock.tryLock();
            }
            if (StringUtils.isNotBlank(managerAccId)) {
                managerLock = redissonClient.getLock(managerAccId);
                managerWalletLock = managerLock.tryLock();
            }
            lock = driverWalletLock && captainWalletLock && managerWalletLock;
            try {
                if (lock) {
                    tOrderPackInfo = orderPackInfoService.selectByCode(packCode);
                    if (tOrderPackInfo.getPackStatus().equals(DictEnum.PACKPAID.code)
                            || tOrderPackInfo.getPackStatus().equals(DictEnum.PACKEWITHDRAWERROR.code)) {
                        ResultUtil resultUtil = hxOrderPayService.packRefund(packCode, vo.getStateRemark());
                        resultUtil.setData(tOrderPackInfo.getVirtualOrderNo());
                        resultUtils.add(resultUtil);
                        return ResultUtil.ok(resultUtils);
                    } else {
                        resultUtils.add(ResultUtil.error("运单状态错误，不可召回", tOrderPackInfo.getVirtualOrderNo()));
                        return ResultUtil.ok(resultUtils);
                    }
                } else {
                    resultUtils.add(ResultUtil.error("企业正在操作中，请稍后再试", tOrderPackInfo.getVirtualOrderNo()));
                    return ResultUtil.ok(resultUtils);
                }
            } catch (Exception e) {
                log.error("运单召回失败！{}", ThrowableUtil.getStackTrace(e));
                String message = e.getMessage();
                if (StringUtils.checkChineseCharacter(message)) {
                    resultUtils.add(ResultUtil.error(message, tOrderPackInfo.getVirtualOrderNo()));
                }
                resultUtils.add(ResultUtil.error("运单召回失败！"));
                return ResultUtil.ok(resultUtils);
            } finally {
                try {
                    // 将当前运单从同步处理运单中移除
                    RSet<Object> objectRSet = redissonClient.getSet(P_ORDER);
                    orderCodesByPackId.forEach(objectRSet::remove);
                } catch (Exception e) {
                    log.error("重置P_ORDER异常：{}", ThrowableUtil.getStackTrace(e));
                }
                if (null != driverLock && driverLock.isLocked() && driverLock.isHeldByCurrentThread()) {
                    driverLock.unlock();
                }
                if (null != captainLock && captainLock.isLocked() && captainLock.isHeldByCurrentThread()) {
                    captainLock.unlock();
                }
                if (null != managerLock && managerLock.isLocked() && managerLock.isHeldByCurrentThread()) {
                    managerLock.unlock();
                }
            }
        } catch (Exception e) {
            log.error("运单召回失败！{}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                resultUtils.add(ResultUtil.error(message, tOrderPackInfo.getVirtualOrderNo()));
            }
            resultUtils.add(ResultUtil.error("运单召回失败！", tOrderPackInfo.getVirtualOrderNo()));
            return ResultUtil.ok(resultUtils);
        }
    }

}
