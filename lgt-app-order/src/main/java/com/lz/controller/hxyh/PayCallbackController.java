package com.lz.controller.hxyh;

import cn.hutool.json.JSONUtil;
import com.lz.common.config.RedisUtil;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.HXTradeTypeEnum;
import com.lz.common.model.hxPayment.response.CustomerBalancePayRes;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.common.util.ThrowableUtil;
import com.lz.dto.OrderInfoDTO;
import com.lz.model.TOrderPayDetail;
import com.lz.payment.TradeType;
import com.lz.service.hxyh.THXOrderPayDetailService;
import com.lz.service.hxyh.THxOrderInfoService;
import com.lz.service.pay.CallbackService;
import com.lz.vo.CallbackOrderInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sdk.model.message.AsyncNotifyVirtualBalancePayMessageBody;

import javax.annotation.Resource;

/**
 *  @author: dingweibo
 *  @Date: 2022/11/25 10:16
 *  @Description: 华夏支付回调
 */
@Slf4j
@RestController
@RequestMapping("/pay/callback/entry")
public class PayCallbackController {

    private static final String ORDER_CALLBACK_BIZ_CODE_EXIST = "ORDER_CALLBACK_BIZ_CODE_EXIST";

    @Autowired
    private THxOrderInfoService hxOrderInfoService;

    @Autowired
    private THXOrderPayDetailService hxOrderPayDetailService;

    @Autowired
    private CallbackService callbackService;

    @Resource
    private RedisUtil redisUtil;

    /**
    * @description 余额支付回调
    * <AUTHOR>
    * @date 2021/8/16 08:55
    */
    @PostMapping("/balance/pay/notice")
    public ResultUtil balancePayNotice(@RequestBody AsyncNotifyVirtualBalancePayMessageBody messageBody) {
        try {
            log.info("华夏余额支付回调信息, {}", JSONUtil.toJsonStr(messageBody));
            // 根据payDetailCode查询交易类型
            TOrderPayDetail tOrderPayDetail = hxOrderPayDetailService.selectByCode(messageBody.getBizOrderNo());
            if (DictEnum.TRADE_FINISHED.code.equals(tOrderPayDetail.getTradeStatus()) || redisUtil.hasKey(ORDER_CALLBACK_BIZ_CODE_EXIST + messageBody.getBizOrderNo())) {
                log.info("华夏余额支付回调已处理");
            } else {
                log.info("华夏余额支付回调，支付字子表信息, {}", JSONUtil.toJsonStr(tOrderPayDetail));
                if (TradeType.RZ.code.equals(tOrderPayDetail.getOperateState()) && HXTradeTypeEnum.HX_COMBALANCE_PAY.code.equals(tOrderPayDetail.getTradeType())) {
                    // 企业余额支付回调
                    callbackService.companyBalancePayNotice(messageBody);
                    redisUtil.set(ORDER_CALLBACK_BIZ_CODE_EXIST + messageBody.getBizOrderNo(), 1, 60 * 60 * 24);
                } else {
                    callbackService.singleBalancePayNotice(messageBody);
                    redisUtil.set(ORDER_CALLBACK_BIZ_CODE_EXIST + messageBody.getBizOrderNo(), 1, 60 * 60 * 24);
                }
            }
            return ResultUtil.ok();
        } catch (Exception e) {
            log.error("余额支付处理回调失败, {}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("余额支付处理回调失败");
        }
    }

    /**
    * @description 华夏支付申请失败处理
    * <AUTHOR>
    * @date 2021/12/7 11:33
    */
    @PostMapping("/pay/fail")
    ResultUtil payFail(@RequestBody CustomerBalancePayRes response) {
        try {
            log.info("华夏支付申请失败处理, {}", JSONUtil.toJsonStr(response));
            TOrderPayDetail tOrderPayDetail = hxOrderPayDetailService.selectByCode(response.getBizOrderNo());
            if (DictEnum.TRADE_FINISHED.code.equals(tOrderPayDetail.getTradeStatus()) || redisUtil.hasKey(ORDER_CALLBACK_BIZ_CODE_EXIST + response.getBizOrderNo())) {
                log.info("华夏支付申请失败处理已处理");
                return ResultUtil.ok();
            }
            callbackService.payFail(response);
            redisUtil.set(ORDER_CALLBACK_BIZ_CODE_EXIST + response.getBizOrderNo(), 1, 60 * 60 * 24);
        } catch (Exception e) {
            log.error("华夏支付申请失败处理异常, {}", ThrowableUtil.getStackTrace(e));
        }
        return ResultUtil.ok();
    }

    @PostMapping("/pay/detail")
    public ResultUtil getCallbackOrderInfoByPayDetailCode(@RequestBody CallbackOrderInfoVO vo) {
        TOrderPayDetail orderPayDetail = hxOrderPayDetailService.selectByCode(vo.getCode());
        if (null != orderPayDetail && HXTradeTypeEnum.HX_COMBALANCE_PAY.code.equals(orderPayDetail.getTradeType())) {
            OrderInfoDTO orderInfoDTO = new OrderInfoDTO();
            orderInfoDTO.setTradeType(orderPayDetail.getTradeType());
            return ResultUtil.ok(orderInfoDTO);
        } else {
            return ResultUtil.ok(hxOrderInfoService.selectByCode(vo.getCode()));
        }
    }

}
