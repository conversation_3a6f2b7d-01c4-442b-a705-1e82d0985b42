package com.lz.controller.hxyh;

import cn.hutool.json.JSONUtil;
import com.lz.common.config.RedisUtil;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.model.hxPayment.response.CustomerBalancePayRes;
import com.lz.common.model.hxPayment.response.CustomerWithdrawRes;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.common.util.ThrowableUtil;
import com.lz.dto.THxAdvanceOrderPayTempInfoDTO;
import com.lz.dto.THxOrderPayInfoDTO;
import com.lz.model.TAdvanceOrderTmp;
import com.lz.model.TOrderPayDetail;
import com.lz.payment.TradeType;
import com.lz.service.TAdvanceOrderPayTempService;
import com.lz.service.TAdvanceOrderTmpService;
import com.lz.service.hxyh.HXCallbackService;
import com.lz.service.hxyh.THXOrderPayDetailService;
import com.lz.service.hxyh.THxOrderInfoService;
import com.lz.vo.CallbackOrderInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sdk.model.message.AsyncNotifyVirtualBalancePayMessageBody;
import sdk.model.message.AsyncNotifyVirtualMemberWithdrawMessageBody;

import javax.annotation.Resource;

/**
 *  @author: dingweibo
 *  @Date: 2022/11/25 10:16
 *  @Description: 华夏支付回调
 */
@Slf4j
@RestController
@RequestMapping("/hx/callback/entry")
public class HXCallbackController {
    
    private static final String ORDER_CALLBACK_BIZ_CODE_EXIST = "ORDER_CALLBACK_BIZ_CODE_EXIST";

    @Autowired
    private THxOrderInfoService hxOrderInfoService;

    @Autowired
    private THXOrderPayDetailService hxOrderPayDetailService;

    @Autowired
    private TAdvanceOrderTmpService advanceOrderTmpService;

    @Autowired
    private TAdvanceOrderPayTempService advanceOrderPayTempService;

    @Autowired
    private HXCallbackService hxCallbackService;
    
    @Resource
    private RedisUtil redisUtil;

    /**
    * @description 余额支付回调
    * <AUTHOR>
    * @date 2021/8/16 08:55
    */
    @PostMapping("/balance/pay/notice")
    public ResultUtil balancePayNotice(@RequestBody AsyncNotifyVirtualBalancePayMessageBody messageBody) {
        try {
            log.info("华夏余额支付回调信息, {}", JSONUtil.toJsonStr(messageBody));
            // 根据payDetailCode查询交易类型
            TOrderPayDetail tOrderPayDetail = hxOrderPayDetailService.selectByCode(messageBody.getBizOrderNo());
            if (DictEnum.TRADE_FINISHED.code.equals(tOrderPayDetail.getTradeStatus()) || redisUtil.hasKey(ORDER_CALLBACK_BIZ_CODE_EXIST + messageBody.getBizOrderNo())) {
                log.info("华夏提现回调通知已处理： {}", JSONUtil.toJsonStr(messageBody));
                return ResultUtil.ok();
            } else {
                log.info("华夏余额支付回调，支付字子表信息, {}", JSONUtil.toJsonStr(tOrderPayDetail));
                if (TradeType.RZ.code.equals(tOrderPayDetail.getOperateState())) {
                    // 单笔支付
                    hxCallbackService.singleBalancePayNotice(messageBody);
                    redisUtil.set(ORDER_CALLBACK_BIZ_CODE_EXIST + messageBody.getBizOrderNo(), 1, 60 * 60 * 24);
                }
                if(TradeType.RZNODE.code.equals(tOrderPayDetail.getOperateState())) {
                    // 节点支付
                    hxCallbackService.nodeBalancePayNotice(messageBody);
                    redisUtil.set(ORDER_CALLBACK_BIZ_CODE_EXIST + messageBody.getBizOrderNo(), 1, 60 * 60 * 24);
                }
            }
            return ResultUtil.ok();
        } catch (Exception e) {
            log.error("余额支付处理回调失败, {}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("余额支付处理回调失败");
        }
    }

    /**
    * @description 提现回调
    * <AUTHOR>
    * @date 2021/9/17 15:15
    */
    @PostMapping("/withdraw/tx/notice")
    public ResultUtil txNotice(@RequestBody AsyncNotifyVirtualMemberWithdrawMessageBody messageBody) {
        try {
            log.info("华夏提现回调通知信息, {}", JSONUtil.toJsonStr(messageBody));
            TOrderPayDetail tOrderPayDetail = hxOrderPayDetailService.selectByCode(messageBody.getBizOrderNo());
            if (DictEnum.TRADE_FINISHED.code.equals(tOrderPayDetail.getTradeStatus()) || redisUtil.hasKey(ORDER_CALLBACK_BIZ_CODE_EXIST + messageBody.getBizOrderNo())) {
                log.info("华夏提现回调通知已处理： {}", JSONUtil.toJsonStr(messageBody));
            } else {
                hxCallbackService.txNotice(messageBody);
                redisUtil.set(ORDER_CALLBACK_BIZ_CODE_EXIST + messageBody.getBizOrderNo(), 1, 60 * 60 * 24);
            }
            return ResultUtil.ok();
        } catch (Exception e) {
            log.error("华夏提现回调通知处理失败, {}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("华夏提现回调通知处理失败");
        }
    }

    /**
     * @description 交易退款结果通知消息
     * <AUTHOR>
     * @date 2021/8/16 09:02
     */
    @PostMapping("/refund/notice")
    public ResultUtil refundNotice(@RequestBody AsyncNotifyVirtualBalancePayMessageBody messageBody) {
        try {
            log.info("华夏交易退款通知回调信息, {}", JSONUtil.toJsonStr(messageBody));
            TOrderPayDetail tOrderPayDetail = hxOrderPayDetailService.selectByCode(messageBody.getBizOrderNo());
            if (DictEnum.TRADE_FINISHED.code.equals(tOrderPayDetail.getTradeStatus()) || redisUtil.hasKey(ORDER_CALLBACK_BIZ_CODE_EXIST + messageBody.getBizOrderNo())) {
                log.info("华夏交易退款通知回调已处理： {}", JSONUtil.toJsonStr(messageBody));
            } else {
                hxCallbackService.refundNotice(messageBody);
                redisUtil.set(ORDER_CALLBACK_BIZ_CODE_EXIST + messageBody.getBizOrderNo(), 1, 60 * 60 * 24);
            }
            return ResultUtil.ok();
        } catch (Exception e) {
            log.error("华夏交易退款通知回调失败，{}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("华夏交易退款通知回调失败");
        }
    }

    /**
     * @description 打包交易退款结果通知消息
     * <AUTHOR>
     * @date 2021/10/11 11:02
     */
    @PostMapping("/pack/refund/notice")
    public ResultUtil packRefundNotice(@RequestBody AsyncNotifyVirtualBalancePayMessageBody messageBody) {
        try {
            log.info("华夏打包交易退款通知回调信息, {}", JSONUtil.toJsonStr(messageBody));
            TOrderPayDetail tOrderPayDetail = hxOrderPayDetailService.selectByCode(messageBody.getBizOrderNo());
            if (DictEnum.TRADE_FINISHED.code.equals(tOrderPayDetail.getTradeStatus()) || redisUtil.hasKey(ORDER_CALLBACK_BIZ_CODE_EXIST + messageBody.getBizOrderNo())) {
                log.info("华夏打包交易退款通知回调已处理： {}", JSONUtil.toJsonStr(messageBody));
            } else {
                hxCallbackService.packRefundNotice(messageBody);
                redisUtil.set(ORDER_CALLBACK_BIZ_CODE_EXIST + messageBody.getBizOrderNo(), 1, 60 * 60 * 24);
            }
            return ResultUtil.ok();
        } catch (Exception e) {
            log.error("华夏打包交易退款通知回调失败，{}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("华夏打包交易退款通知回调失败");
        }
    }

    /**
     * @description 华夏预付款余额支付通知
     * <AUTHOR>
     * @date 2021/11/8 13:38
     */
    @PostMapping("/pre/pay/notice")
    ResultUtil prePayNotice(@RequestBody AsyncNotifyVirtualBalancePayMessageBody messageBody) {
        try {
            log.info("华夏预付款余额支付回调信息, {}", JSONUtil.toJsonStr(messageBody));
            // 根据payDetailCode查询交易类型
            THxAdvanceOrderPayTempInfoDTO dto = advanceOrderPayTempService.selectHxAdvanceOrderPayTempInfo(messageBody.getBizOrderNo());
            if (null != dto) {
                TAdvanceOrderTmp tAdvanceOrderTmp = advanceOrderTmpService.selectAdvanceOrderTempByOrderCode(dto.getOrderCode());
                if (null != tAdvanceOrderTmp) {
                    if (DictEnum.TRADE_FINISHED.code.equals(dto.getTradeStatus()) || redisUtil.hasKey(ORDER_CALLBACK_BIZ_CODE_EXIST + messageBody.getBizOrderNo())) {
                        log.info("华夏预付款余额支付回调已处理： {}", JSONUtil.toJsonStr(messageBody));
                    } else {
                        log.info("华夏预付款余额支付回调，支付字子表信息, {}", JSONUtil.toJsonStr(dto));
                        // 预付款回调
                        hxCallbackService.prePayNotice(messageBody, dto);
                        redisUtil.set(ORDER_CALLBACK_BIZ_CODE_EXIST + messageBody.getBizOrderNo(), 1, 60 * 60 * 24);
                    }
                }
            } else {
                TOrderPayDetail tOrderPayDetail = hxOrderPayDetailService.selectByCode(messageBody.getBizOrderNo());
                if (null == tOrderPayDetail) {
                    throw new RuntimeException("华夏预付款支付回调错误-未找到预支付原始运单：" + messageBody.getBizOrderNo());
                }
                THxOrderPayInfoDTO orderPayInfoDTO = hxOrderPayDetailService.selectByOrderPayDetailKey(messageBody.getBizOrderNo());
                if (null != orderPayInfoDTO) {
                    if (DictEnum.TRADE_FINISHED.code.equals(orderPayInfoDTO.getTradeStatus()) || redisUtil.hasKey(ORDER_CALLBACK_BIZ_CODE_EXIST + messageBody.getBizOrderNo())) {
                        log.info("华夏预付款余额支付回调已处理： {}", JSONUtil.toJsonStr(messageBody));
                    } else {
                        // 尾款回调
                        hxCallbackService.wkPayNotice(messageBody, orderPayInfoDTO);
                        redisUtil.set(ORDER_CALLBACK_BIZ_CODE_EXIST + messageBody.getBizOrderNo(), 1, 60 * 60 * 24);
                    }
                }
            }
            return ResultUtil.ok();
        } catch (Exception e) {
            log.error("华夏预付款余额支付回调失败, {}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("华夏预付款余额支付回调失败");
        }
    }

    /**
    * @description 华夏支付申请失败处理
    * <AUTHOR>
    * @date 2021/12/7 11:33
    */
    @PostMapping("/pay/fail")
    ResultUtil payFail(@RequestBody CustomerBalancePayRes response) {
        try {
            TOrderPayDetail tOrderPayDetail = hxOrderPayDetailService.selectByCode(response.getBizOrderNo());
            if (DictEnum.TRADE_FINISHED.code.equals(tOrderPayDetail.getTradeStatus()) || redisUtil.hasKey(ORDER_CALLBACK_BIZ_CODE_EXIST + response.getBizOrderNo())) {
                log.info("华夏支付申请失败处理异常，已处理：{}", JSONUtil.toJsonStr(response));
                return ResultUtil.ok();
            }
            hxCallbackService.payFail(response);
            redisUtil.set(ORDER_CALLBACK_BIZ_CODE_EXIST + response.getBizOrderNo(), 1, 60 * 60 * 24);
        } catch (Exception e) {
            log.error("华夏支付申请失败处理异常，异常信息：", e);
        }
        return ResultUtil.ok();
    }

    /**
    * @description 华夏提现申请失败处理
    * <AUTHOR>
    * @date 2021/11/16 10:41
    */
    @PostMapping("/tx/fail")
    ResultUtil txFail(@RequestBody CustomerWithdrawRes response) {
        try {
            TOrderPayDetail tOrderPayDetail = hxOrderPayDetailService.selectByCode(response.getBizOrderNo());
            if (DictEnum.TRADE_FINISHED.code.equals(tOrderPayDetail.getTradeStatus()) || redisUtil.hasKey(ORDER_CALLBACK_BIZ_CODE_EXIST + response.getBizOrderNo())) {
                log.info("华夏提现申请失败处理异常，已处理：{}", JSONUtil.toJsonStr(response));
                return ResultUtil.ok();
            }
            hxCallbackService.txFail(response);
            redisUtil.set(ORDER_CALLBACK_BIZ_CODE_EXIST + response.getBizOrderNo(), 1, 60 * 60 * 24);
        } catch (Exception e) {
            log.error("华夏提现申请失败处理异常，异常信息：", e);
        }
        return ResultUtil.ok();
    }

    /**
     * @description 补交账户服务费回调
     * <AUTHOR>
     * @date 2021/12/3 17:05
     */
    @PostMapping("/account/servicefee/callback")
    ResultUtil accountServiceFeeCallback(@RequestBody AsyncNotifyVirtualBalancePayMessageBody messageBody) {
        try {
            log.info("华夏补交账户服务费回调信息, {}", JSONUtil.toJsonStr(messageBody));
            TOrderPayDetail tOrderPayDetail = hxOrderPayDetailService.selectByCode(messageBody.getBizOrderNo());
            if (DictEnum.TRADE_FINISHED.code.equals(tOrderPayDetail.getTradeStatus()) || redisUtil.hasKey(ORDER_CALLBACK_BIZ_CODE_EXIST + messageBody.getBizOrderNo())) {
                log.info("华夏补交账户服务费回调已处理： {}", JSONUtil.toJsonStr(messageBody));
                return ResultUtil.ok();
            } else {
                hxCallbackService.accountServiceFeeCallback(messageBody);
                redisUtil.set(ORDER_CALLBACK_BIZ_CODE_EXIST + messageBody.getBizOrderNo(), 1, 60 * 60 * 24);
                return ResultUtil.ok();
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("华夏补交账户服务费回调失败，{}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("华夏补交账户服务费回调失败");
        }
    }

    /**
     * 查询支付子表
     * @param
     * @return
     */
    @PostMapping("/queryOrderPayDetail")
    public TOrderPayDetail queryOrderPayDetail(@RequestBody TOrderPayDetail orderPayDetail) {
        return hxOrderPayDetailService.selectByCode(orderPayDetail.getCode());
    }

    /**
     * 承运方余额支付(企业充值)回调
     * @param messageBody
     * @return
     */
    @PostMapping("/carrier/balance/company/charge/callback")
    public ResultUtil carrierBalanceCompanyChargeCallback(@RequestBody AsyncNotifyVirtualBalancePayMessageBody messageBody) {
        try {
            log.info("承运方余额支付(企业充值)回调信息, {}", JSONUtil.toJsonStr(messageBody));
            // 根据payDetailCode查询交易类型
            TOrderPayDetail tOrderPayDetail = hxOrderPayDetailService.selectByCode(messageBody.getBizOrderNo());
            if (DictEnum.TRADE_FINISHED.code.equals(tOrderPayDetail.getTradeStatus()) || redisUtil.hasKey(ORDER_CALLBACK_BIZ_CODE_EXIST + messageBody.getBizOrderNo())) {
                log.info("承运方余额支付(企业充值)回调已处理： {}", JSONUtil.toJsonStr(messageBody));
                return ResultUtil.ok();
            } else {
                log.info("承运方余额支付(企业充值)回调，支付字子表信息, {}", JSONUtil.toJsonStr(tOrderPayDetail));
                hxCallbackService.carrierBalanceCompanyChargeCallback(messageBody);
                redisUtil.set(ORDER_CALLBACK_BIZ_CODE_EXIST + messageBody.getBizOrderNo(), 1, 60 * 60 * 24);
                return ResultUtil.ok();
            }
        } catch (Exception e) {
            log.error("承运方余额支付(企业充值)回调失败, {}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("承运方余额支付(企业充值)回调失败");
        }
    }

    @PostMapping("/orderinfo")
    public ResultUtil getCallbackOrderInfoByPayDetailCode(@RequestBody CallbackOrderInfoVO vo) {
        return ResultUtil.ok(hxOrderInfoService.selectByCode(vo.getCode()));
    }

}
