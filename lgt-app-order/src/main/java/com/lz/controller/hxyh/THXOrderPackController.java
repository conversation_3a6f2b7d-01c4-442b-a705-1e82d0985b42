package com.lz.controller.hxyh;

import com.lz.common.dbenum.DictEnum;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.service.hxyh.THXOrderPackSerivce;
import com.lz.util.HXPayOrderPackJudgeFilter;
import com.lz.vo.TJDOrderPackDetailVO;
import com.lz.vo.TJDOrderPackVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/hx/order/pack")
public class THXOrderPackController {

    @Autowired
    private THXOrderPackSerivce hxOrderPackSerivce;

    /**
     * 打包
     */
    @PostMapping("/orderPack")
    public ResultUtil orderPack(@RequestBody TJDOrderPackVO vo) {
        try {
/*            if (null == vo.getBankCardId()) {
                return ResultUtil.error("请选择银行卡");
            }*/
            if (vo.getCountErase() != null && vo.getCountErase().compareTo(BigDecimal.valueOf(0D)) < 0) {
                return ResultUtil.error("选中运单累计总运费扣款金额不能小于0");
            }
            if (null != vo.getAppointmentPaymentCash()) {
                if (vo.getAppointmentPaymentCash().compareTo(BigDecimal.valueOf(0D)) < 0 || vo.getAppointmentPaymentCash().compareTo(BigDecimal.valueOf(0D)) == 0) {
                    return ResultUtil.error("与车老板约定应支付总运费现金额不能小于等于0");
                }
                BigDecimal countFreight = vo.getCountFreight();
                BigDecimal add_10 = countFreight.add(countFreight.multiply(new BigDecimal(DictEnum.FREIGHT_DIFFERENCES_LIMIT.code)));
                    if (vo.getAppointmentPaymentCash().compareTo(add_10) > 0) {
                    return ResultUtil.error("确认应付运费与规则应付运费差额不可超过10%。");
                }
            }
            if (null != vo.getAppointmentPaymentOther()) {
                if (vo.getAppointmentPaymentOther().compareTo(BigDecimal.valueOf(0D)) < 0) {
                    return ResultUtil.error("与车老板约定应支付总运费其他实物价值不能小于0");
                }
            }
            if (null != vo.getTotalSelectedOrdersServiceFee()) {
                if (vo.getAppointmentPaymentCash().compareTo(vo.getTotalSelectedOrdersServiceFee()) <= 0) {
                    return ResultUtil.error("选中运单的运费不能等于小于服务费");
                }
            }
            log.info("判断运单是否符合打包规则");
            Map<String, Object> judge = HXPayOrderPackJudgeFilter.judge(vo.getCodes());
            if ((boolean) judge.get("bool")) {
                TJDOrderPackDetailVO packDetailVO = new TJDOrderPackDetailVO();
                BeanUtils.copyProperties(vo, packDetailVO);
                packDetailVO.setOwner(judge.get("owner"));
                packDetailVO.setEndUserIds(judge.get("endUserId"));
                packDetailVO.setCompanyId((Integer) judge.get("company"));
                packDetailVO.setCarrierId((Integer) judge.get("carrier"));


                return hxOrderPackSerivce.orderPack(packDetailVO);
            } else {
                return ResultUtil.error(judge.get("msg").toString());
            }

        } catch (Exception e) {
            log.error("运单打包失败！, {}", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("运单打包失败！");
        }
    }

}
