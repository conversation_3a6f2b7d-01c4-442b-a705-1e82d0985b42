package com.lz.controller;

import com.alibaba.fastjson.JSONObject;
import com.lz.api.CompanyProjectAPI;
import com.lz.api.EnduserCarRelAPI;
import com.lz.api.WalletService;
import com.lz.common.config.RedisUtil;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.dto.WalletDTO;
import com.lz.model.*;
import com.lz.schedule.model.TTask;
import com.lz.service.*;
import com.lz.service.hxyh.THXOrderPackSerivce;
import com.lz.util.OrderStateJudgeFilter;
import com.lz.util.SendOrderUtil;
import com.lz.vo.CashOutVO;
import com.lz.vo.OrderPackVO;
import com.lz.vo.PcOrderSearchVO;
import com.lz.vo.TEndCarInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("/api/tOrderPackInfo")
public class TOrderPackInfoController {

    @Autowired
    private TOrderPackInfoService tOrderPackInfoService;

    @Autowired
    private TJDOrderPackSerivce jdOrderPackSerivce;

    @Autowired
    private THXOrderPackSerivce hxOrderPackSerivce;

    @Autowired
    private WxOrderInfoService wxOrderInfoService;


    @Autowired
    private TOrderCastChangesService orderCastChangesService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private TOrderInfoService orderInfoService;

    @Autowired
    private EnduserCarRelAPI endUserCar;

    @Autowired
    private WalletService walletService;

    @Autowired
    private SendOrderUtil sendOrderUtil;

    @Autowired
    private CompanyProjectAPI companyProjectAPI;

    /**
     *  @author: sangbin
     *  @Date: 2019/6/28 15:04
     *  @Description: 根据打包运单编号code，查询打包运单
     */
    @PostMapping("/selectByCode")
    public TOrderPackInfo selectByCode(@RequestParam("code") String code){
        TOrderPackInfo tOrderPackInfo = tOrderPackInfoService.selectByCode(code);
        return tOrderPackInfo;
    }

    @PostMapping("/selectById")
    public TOrderPackInfo selectById(@RequestParam("id") Integer id){
        TOrderPackInfo tOrderPackInfo = tOrderPackInfoService.selectById(id);
        return tOrderPackInfo;
    }

    @PostMapping("/selectPackInfoByOrderBusinessCodePayTime")
    TOrderPackInfo selectPackInfoByOrderBusinessCodePayTime(@RequestParam("orderBusinessCode") String orderBusinessCode , @RequestParam("tradeNo") String tradeNo) {
        TOrderPackInfo tOrderPackInfo = tOrderPackInfoService.selectPackInfoByOrderBusinessCodePayTime(orderBusinessCode, tradeNo);
        return tOrderPackInfo;
    }
    /**
     * 运单过程管理-运单付款-生成批量支付记录
     * 回显打包信息
     * Yan
     * @param
     * @return
     */
    @PostMapping("/ordersPackInfo")
    public ResultUtil getPackOrdersInfo(@RequestBody OrderPackVO packVO) {
        try {
            if (DictEnum.JDPLATFORMS_CODE.code.equals(packVO.getPaymentPlatforms())) {
                return jdOrderPackSerivce.ordersPackInfo(packVO);
            } else if (DictEnum.HXPLATFORMS_CODE.code.equals(packVO.getPaymentPlatforms())) {
                return hxOrderPackSerivce.ordersPackInfo(packVO);
            } else {
                return ResultUtil.error("请选择支付平台");
            }
        } catch (Exception e){
            log.error("ZJJ-038:生成批量支付记录失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("ZJJ-038:生成批量支付记录失败!");
            }
        }
    }

    /**
     * 运单过程管理-运单付款-生成批量支付记录
     * 运单打包
     * Yan
     * @return
     */
    @PostMapping("/orderPack")
    public ResultUtil orderPack(@Validated @RequestBody OrderPackVO pack) {
        log.info("开始进行打包");
        String projectKey = "Project";
        Boolean projectLock = false;
        try {
            if (null == pack.getBankCardId()){
                return ResultUtil.error("请选择银行卡");
            }
            if (null != pack.getCountErase() && pack.getCountErase().compareTo(BigDecimal.valueOf(0D)) == -1){
                return ResultUtil.error("选中运单累计总运费扣款金额不能小于0");
            }
            if (null != pack.getAppointmentPaymentCash()){
                System.out.println(pack.getAppointmentPaymentCash().compareTo(BigDecimal.valueOf(0D)));
                if (pack.getAppointmentPaymentCash().compareTo(BigDecimal.valueOf(0D)) == -1 || pack.getAppointmentPaymentCash().compareTo(BigDecimal.valueOf(0D)) == 0){
                    return ResultUtil.error("与车老板约定应支付总运费现金额不能小于等于0");
                }
            }
            if (null != pack.getAppointmentPaymentOther()){
                if (pack.getAppointmentPaymentOther().compareTo(BigDecimal.valueOf(0D)) == -1){
                    return ResultUtil.error("与车老板约定应支付总运费其他实物价值不能小于0");
                }
            }
            if (null != pack.getTotalSelectedOrdersServiceFee()) {
                if (pack.getAppointmentPaymentCash().compareTo(pack.getTotalSelectedOrdersServiceFee()) <= 0) {
                    return ResultUtil.error("选中运单的运费不能等于小于服务费");
                }
            }
            try {
                // 判断运单是否符合打包规则
                log.info("判断运单是否符合打包规则");
                Map<String, Object> judge = OrderStateJudgeFilter.judge(pack.getCodes());
                if ((boolean) judge.get("bool")) {
                    // 查询提现是否超额
                    queryTXWhetherOverLimit(pack, judge);
                    Object projectId = judge.get("projectId");
                    projectKey = projectKey + projectId;
                    if (!redisUtil.hasKey(projectKey)) {
                        projectLock = redisUtil.set(projectKey, "lock");
                    }
                    if (projectLock) {
                        log.info("进入service");
                        pack.setOwner(judge.get("owner"));
                        pack.setEndUserIds(judge.get("endUserId"));
                        pack.setCompany((Integer)judge.get("company"));
                        pack.setCarrier((Integer)judge.get("carrier"));
                        tOrderPackInfoService.orderPack(pack);
                        log.info("退出service");
                    } else {
                        return ResultUtil.error("企业正在操作中，请稍后再试");
                    }

                } else {
                    return ResultUtil.error(judge.get("msg").toString());
                }
            } catch (Exception e) {
                log.error("ZJJ-039:运单打包失败!", e);
                String message = e.getMessage();
                if (StringUtils.checkChineseCharacter(message)){
                    return ResultUtil.error(message);
                } else {
                    return ResultUtil.error("ZJJ-039:运单打包失败!");
                }
            } finally {
                if (projectLock && redisUtil.hasKey(projectKey)) {
                    redisUtil.del(projectKey);
                    log.info("释放企业项目锁, {}", projectKey);
                }

            }
            log.info("打包完成");

            return ResultUtil.ok();
        } catch (Exception e){
            log.error("ZJJ-039:运单打包失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("ZJJ-039:运单打包失败!");
            }
        }
    }

    private void queryTXWhetherOverLimit(OrderPackVO pack, Map<String, Object> judge) {
        TBankCard bankCard = sendOrderUtil.checkCardholder(pack.getBankCardId());
        BigDecimal serviceFee = null == pack.getTotalSelectedOrdersServiceFee() ? BigDecimal.ZERO : pack.getTotalSelectedOrdersServiceFee();
        BigDecimal amount = pack.getAppointmentPaymentCash().subtract(serviceFee);
        Object companyProjectId = judge.get("projectId");
        TCompanyProject tCompanyProject = companyProjectAPI.selectByCompanyProjectById(Integer.parseInt(companyProjectId.toString()));
        //此项目是否限额
        if(tCompanyProject.getIfQuota()){
            // 检测提现是否超额
            log.info("查询提现是否超额");
            sendOrderUtil.checkCardHolderWithdrawAmount(bankCard, amount);
            log.info("查询提现是否超额结束");
        } else {
            sendOrderUtil.checkCardHolderWithdrawAmount50Limit(bankCard, amount);
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2021/1/18 14:49
     *  @Description: 运单打包task处理
     */
    @PostMapping("/orderDbTask")
    public ResultUtil orderDbTask(@Validated @RequestBody TTask tTask) {
        try{
            return  tOrderPackInfoService.orderDbTask(tTask);
        }catch (Exception e){
            log.error("运单打包Task处理失败!", e);
            return ResultUtil.error("运单打包Task处理失败!");
        }
    }


    public static void main(String[] args) {
        System.out.println(BigDecimal.valueOf(0.1D).compareTo(BigDecimal.ZERO));
    }

    /**
     * 运单打包付款列表
     * 已经打好包的运单 未支付的
     * Yan
     * @param search
     * @return
     */
    @PostMapping("/finishedPack")
    public ResultUtil getOrderFinishedPack(@RequestBody PcOrderSearchVO search) {
        try {
            return tOrderPackInfoService.getOrderFinishedPack(search);
        } catch (RuntimeException e) {
            return ResultUtil.error(e.getMessage());
        } catch (Exception e){
            log.error("ZJJ-040:未支付打包运单列表查询失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("ZJJ-040:未支付打包运单列表查询失败!");
            }
        }
    }

    /**
     * 运单打包付款列表
     * 已经打好包的运单 已支付的
     * Yan
     * @param search
     * @return
     */
    @PostMapping("/finishedPackOk")
    public ResultUtil getOrderFinishedPackOk(@RequestBody PcOrderSearchVO search) {
        try {
            return tOrderPackInfoService.getOrderFinishedPackOk(search);
        } catch (RuntimeException e) {
            log.error("已支付打包运单列表查询失败",e);
            return ResultUtil.error(e.getMessage());
        } catch (Exception e){
            log.error("ZJJ-099:已支付打包运单列表查询失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("ZJJ-099:已支付打包运单列表查询失败!");
            }
        }
    }

    /**
     * 运单打包付款列表
     * 已经打好包的运单 已提现
     * Yan
     * @param search
     * @return
     */
    @PostMapping("/finishedPackTxOk")
    public ResultUtil finishedPackTxOk(@RequestBody PcOrderSearchVO search) {
        try {
            return tOrderPackInfoService.getOrderFinishedPackTxOk(search);
        } catch (RuntimeException e) {
            return ResultUtil.error(e.getMessage());
        } catch (Exception e){
            log.error("ZJJ-099:已支付打包运单列表查询失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("ZJJ-099:已支付打包运单列表查询失败!");
            }
        }
    }

    /**
     * 运单打包付款列表
     * 已经打好包的运单 导出
     * Yan
     * @param search
     * @return
     */
    @PostMapping("/finishedPackExproExcel")
    public ResultUtil finishedPackExproExcel(@RequestBody PcOrderSearchVO search) {
        try {
            return tOrderPackInfoService.finishedPackExproExcel(search);
        } catch (RuntimeException e) {
            return ResultUtil.error(e.getMessage());
        } catch (Exception e){
            log.error("DW-130:打包运单列表导出失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("DW-130:打包运单列表导出失败!");
            }
        }
    }


    /**
     * 运单拆包
     * Yan
     * @param search
     * @return
     */
    @PostMapping("/dismantledPack")
    public ResultUtil orderDismantledPack(@RequestBody PcOrderSearchVO search) {
        try {
            return tOrderPackInfoService.orderDismantledPack(search.getCode());
        } catch (Exception e){
            log.error("ZJJ-041:运单拆包失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("ZJJ-041:运单拆包失败!");
            }
        }
    }

    /**
     * 根据打包主表code获取
     * 打包运单的详细信息
     * Yan
     * @param search 打包主表code
     * @return
     */
    @PostMapping("/packDetail")
    public ResultUtil packDetail(@RequestBody PcOrderSearchVO search) {
        try {
            return tOrderPackInfoService.orderPackDetailInfo(search);
        } catch (Exception e){
            log.error("ZJJ-042:获取打包原始运单失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("ZJJ-042:获取打包原始运单失败!");
            }
        }
    }

    /**
     * 移除已打包的运单
     * Yan
     * @return
     */
    public ResultUtil removeOrder(@RequestBody PcOrderSearchVO search) {
        return tOrderPackInfoService.removeOrder(search.getCodes());
    }

    /**
     * @Description C用户提现列表
     * <AUTHOR>
     * @Date   2019/7/10 21:23
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/managerWithdrawPage")
    public ResultUtil managerWithdrawPage(@RequestBody PcOrderSearchVO record){
        try {
            String userLogisticsRole = CurrentUser.getUserLogisticsRole();
            if (!userLogisticsRole.equals(DictEnum.CTYPEMANAGER.code)
                    && !userLogisticsRole.contains((DictEnum.CTYPEBOSS.code))
                    && !userLogisticsRole.contains((DictEnum.CTYPECAPTAIN.code))){
                return ResultUtil.error("当前用户不是业务部或车主或车队长");
            }
            ResultUtil resultUtil = tOrderPackInfoService.managerWithdrawPage(record);
            return resultUtil;
        } catch (Exception e){
            log.error("ZJJ-043:获取提现列表失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("ZJJ-043:获取提现列表失败!");
            }
        }
    }


    /**
    * @Description 经纪人打包提现
    * <AUTHOR>
    * @Date   2019/7/11 10:51
    * @Param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/managerWithdraw")
    public ResultUtil managerWithdraw(@RequestBody PcOrderSearchVO record ) {
        String driverWalletKey = "";
        String orderKey = "";
        Boolean driverWalletLock = false;
        Boolean orderLock = false;
        Integer walletId = 0;
        try {
            // 获取所选车辆id
            HashSet<Integer> carIds = new HashSet<>();
            if (null != record.getCode() && StringUtils.isNotEmpty(record.getCode())) {
                TOrderPackInfo packInfo = tOrderPackInfoService.selectByCode(record.getCode());
                if (packInfo.getAppointmentPaymentCash().compareTo(packInfo.getTotalSelectedOrdersServiceFee()) <= 0) {
                    return ResultUtil.error("选中运单的服务费不能大于等于运费");
                }
                TOrderInfo orderInfo = orderInfoService.selectOrderInfoByCode(record.getCode());
                TOrderCastChanges tOrderCastChanges = orderCastChangesService.selectByNewOne(record.getCode());
                if (null == tOrderCastChanges) {
                    // 根据打包主表code查询原始运单的资金变动
                    tOrderCastChanges = orderCastChangesService.selectOrderCastChangeOneByPackCode(record.getCode());
                }
                if (null == orderInfo) {
                    // 根据打包主表code查询原始运单
                    orderInfo = orderInfoService.selectOrderInfoByCodePack(record.getCode());
                }
                List<Integer> carIdByOrderPackCode = orderInfoService.getCarIdByOrderPackCode(record.getCode());
                carIds.addAll(carIdByOrderPackCode);
                TEndCarInfoVO endCarInfoVO = new TEndCarInfoVO();
                if (tOrderCastChanges.getCapitalTransferType().equals(DictEnum.PAYTODRIVER.code)
                    || tOrderCastChanges.getCapitalTransferType().equals(DictEnum.FIRSTBROKERDRIVER.code)) {
                    List<TOrderInfo> orderInfoByPackCode = tOrderPackInfoService.getOrderInfoByPackCode(record.getCode());
                    if (null != orderInfoByPackCode && !orderInfoByPackCode.isEmpty()) {
                        TOrderInfo tOrderInfo = orderInfoByPackCode.get(0);
                        endCarInfoVO.setUserId(tOrderInfo.getEndDriverId());
                    }
                    walletId = tOrderCastChanges.getEndDriverWalletId();
                } else if (tOrderCastChanges.getCapitalTransferType().equals(DictEnum.PAYTOBELONGER.code)
                        || tOrderCastChanges.getCapitalTransferType().equals(DictEnum.FIRSTBROKERBELONGER.code)) {
                    List<TOrderInfo> orderInfoByPackCode = tOrderPackInfoService.getOrderInfoByPackCode(record.getCode());
                    if (null != orderInfoByPackCode && !orderInfoByPackCode.isEmpty()) {
                        HashSet<Integer> enduserId = new HashSet<>();
                        orderInfoByPackCode.stream().forEach((t) ->{
                            if (null != t.getEndDriverId()) {
                                enduserId.add(t.getEndDriverId());
                            }
                        });
                        if (!enduserId.isEmpty()) {
                            endCarInfoVO.setEnduserId(enduserId);
                        }
                    }
                    // 如果新版支付，且支付到车主，查询车主钱包
                    if (null != tOrderCastChanges.getCapitalTransferPattern()
                        && StringUtils.isNotBlank(tOrderCastChanges.getCapitalTransferPattern())) {
                        TOrderInfo tOrderInfo = orderInfoByPackCode.get(0);
                        WalletDTO walletDTO = walletService.selectWalletByEnduserCompanyId(tOrderInfo.getCarrierId(),
                                tOrderInfo.getEndCarOwnerId(),
                                DictEnum.CD.code, DictEnum.CCARBOSS.code);
                        walletId = walletDTO.getWalletId();
                    }
                }

                endCarInfoVO.setEndCarId(carIds);
                ResultUtil carAndDriverStatus = endUserCar.getCarAndDriverStatus(endCarInfoVO);
                if (carAndDriverStatus.getCode().equals("success")) {
                    ArrayList<LinkedHashMap> data = (ArrayList) carAndDriverStatus.getData();
                    if (data.size() > 0) {
                        StringBuilder msg = new StringBuilder();
                        for (LinkedHashMap datum : data) {
                            if ("driver".equals(datum.get("type"))) {
                                msg.append(datum.get("mark"));
                                msg.append(",");
                            } else {
                                msg.append(datum.get("mark"));
                                msg.append(",");
                            }
                        }
                        msg.append("未审核通过。");
                        return ResultUtil.error(msg.toString());
                    }
                }
                driverWalletKey = "USER" + walletId;
                orderKey = "ORDER" + record.getCode();
                if (!redisUtil.hasKey(driverWalletKey)) {
                    driverWalletLock = redisUtil.lock(driverWalletKey);
                }
                if (!redisUtil.hasKey(orderKey)) {
                    orderLock = redisUtil.lock(orderKey);
                }
                if (driverWalletLock && orderLock) {
                    packInfo = tOrderPackInfoService.selectByCode(record.getCode());
                    if (packInfo.getPackStatus().equals(DictEnum.PACKPAID.code) || packInfo.getPackStatus().equals(DictEnum.PACKEWITHDRAWERROR.code)){
                        if (null != packInfo.getBankCardId()){
                            Integer bankCardId = packInfo.getBankCardId();
                            CashOutVO cashOutVO = new CashOutVO();
                            cashOutVO.setCode(record.getCode());
                            cashOutVO.setType(2);
                            cashOutVO.setIfDelayed(false);
                            cashOutVO.setBankId(bankCardId);
                            String txStr = JSONObject.toJSONString(cashOutVO);
                            log.info("提现信息：", txStr);
                            List<CashOutVO> list = new ArrayList<>();
                            list.add(cashOutVO);

                            BigDecimal amount = packInfo.getAppointmentPaymentCash()
                                    .subtract(null == packInfo.getTotalSelectedOrdersServiceFee() ? BigDecimal.valueOf(0) : packInfo.getTotalSelectedOrdersServiceFee());
                            TBankCard bankCard = sendOrderUtil.checkCardholder(list.get(0).getBankId());

                            TCompanyProject tCompanyProject = companyProjectAPI.selectByCompanyProjectById(orderInfo.getCompanyProjectId());
                            //此项目是否限额
                            if(tCompanyProject.getIfQuota()){
                                // 判断持卡人提现是否超额
                                sendOrderUtil.checkCardHolderWithdrawAmount(bankCard, amount);
                            } else {
                                sendOrderUtil.checkCardHolderWithdrawAmount50Limit(bankCard, amount);
                            }
                            ResultUtil aftertixian = wxOrderInfoService.aftertixian(list, bankCardId, packInfo.getAppointmentPaymentCash(), DictEnum.COMPC.code);
                            return aftertixian;
                        } else {
                            return ResultUtil.error(packInfo.getVirtualOrderNo() + ": 未找到银行卡");
                        }
                    } else {
                        return ResultUtil.error(packInfo.getVirtualOrderNo() + ": 运单状态不可提现");
                    }
                } else {
                    return ResultUtil.error("当前运单正在处理中，请稍后再试");
                }
            } else {
                return ResultUtil.error("请选择一条运单");
            }
        } catch (Exception e){
            log.error("ZJJ-044:打包提现失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("ZJJ-044:打包提现失败!");
            }
        } finally {
            if (driverWalletLock && redisUtil.hasKey(driverWalletKey)) {
                redisUtil.del(driverWalletKey);
            }
            if (orderLock && redisUtil.hasKey(orderKey)) {
                redisUtil.del(orderKey);
            }
        }
    }

    @PostMapping("/selectJdPackInfoByOrderBusinessCode")
    public TOrderPackInfo selectJdPackInfoByOrderBusinessCode(@RequestParam("orderBusinessCode") String orderBusinessCode) {
        return tOrderPackInfoService.selectJdPackInfoByOrderBusinessCode(orderBusinessCode);
    }

    @PostMapping("/selectByVirtualOrderNo")
    public TOrderPackInfo selectByVirtualOrderNo(@RequestParam("virtualOrderNo") String virtualOrderNo) {
        return tOrderPackInfoService.selectByVirtualOrderNo(virtualOrderNo);
    }

}
