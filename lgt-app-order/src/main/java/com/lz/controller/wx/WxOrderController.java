package com.lz.controller.wx;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lz.api.*;
import com.lz.common.config.HXPropertiesConfig;
import com.lz.common.config.JDPropertiesConfig;
import com.lz.common.config.RedisUtil;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.JdEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.hxPayment.request.query.CustomerQueryBalanceReq;
import com.lz.common.model.jdPayment.util.CloudPayFormatUtil;
import com.lz.common.redisson.RedissLockUtil;
import com.lz.common.util.*;
import com.lz.dto.OpenRoleStatusListDTO;
import com.lz.dto.TZtBankCardDTO;
import com.lz.enums.InsuranceMethodsEnum;
import com.lz.hxpay.CloudPaymentAPI;
import com.lz.jdpay.CloudPayAPI;
import com.lz.model.*;
import com.lz.payment.JDPayOrderUtil;
import com.lz.payment.hxyh.HXPayOrderUtil;
import com.lz.service.*;
import com.lz.service.hxyh.THXOrderPayService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.util.SendOrderUtil;
import com.lz.vo.*;
import commonSdk.responseModel.CustomerQueryBalanceResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2019/5/22 - 19:00
 **/
@Slf4j
@RestController
@RequestMapping("/wx/order")
public class WxOrderController {

    public static final int WAIT_5_MINUTE = 5 * 5 * 60;
    public static final int HOUR_24 = 24 * 60 * 60 * 1000;
    public static final String CAR = "CAR";
    public static final String USER = "USER";
    public static final String GOODS_SOURCE = "GOODS_SOURCE";

    @Autowired
    private WxOrderInfoService wxOrderInfoService;
    @Autowired
    private TEndSUserInfoAPI tEndSUserInfoAPI;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private TVerificationCodeLogAPI tVerificationCodeLogAPI;
    @Autowired
    private TOrderCastChangesService orderCastChangesService;
    @Autowired
    private TOrderInfoService orderInfoService;
    @Autowired
    private TOrderPackInfoService orderPackInfoService;

    @Autowired
    private EnduserCarRelAPI endUserCar;

    @Autowired
    private TAdvanceOrderTmpService advanceOrderTmpService;

    @Autowired
    private SendOrderUtil sendOrderUtil;

    @Autowired
    private CompanyProjectAPI companyProjectAPI;
    @Autowired
    private TOrderPayRuleService tOrderPayRuleService;

    @Autowired
    private TOrderPackDetailService tOrderPackDetailService;

    @Autowired
    private TJDOrderPayInfoService jdOrderPayInfoService;


    @Autowired
    private TJDOrderPayService jdOrderPayService;

    @Autowired
    private TOrderPayDetailService orderPayDetailService;

    @Autowired
    private JDOpenRoleCommonAPI openRoleCommonAPI;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private JDPayOrderUtil jdPayOrderUtil;

    @Autowired
    private SysParamAPI sysParamAPI;

    @Autowired
    private CloudPayAPI cloudPayAPI;

    @Autowired
    private JDPropertiesConfig jdPropertiesConfig;

    @Autowired
    private TWithdrawBehalfApplicationService withdrawBehalfApplicationService;

    @Autowired
    private TOrderGoodsSourceInfoService orderGoodsSourceInfoService;

    @Autowired
    private HXOpenRoleCommonAPI hxOpenRoleCommonAPI;
    @Autowired
    private HXPayOrderUtil hxPayOrderUtil;
    @Autowired
    private THXOrderPayService hxOrderPayService;
    @Autowired
    private CloudPaymentAPI cloudPaymentAPI;
    @Autowired
    private HXPropertiesConfig hxPropertiesConfig;

    @Resource
    private TEndSUserInfoAPI endUserInfoAPI;

    @Resource
    private TEndCarInfoAPI endCarInfoAPI;

    @Resource
    private TCarInsuranceService carInsuranceService;

    /**
     * WX司机查询运单
     * Yan
     *
     * @param search
     * @return
     */
    @PostMapping("/wxDriverOrder")
    public ResultUtil wxDriverSelect(@RequestBody AppOrderSearchVO search) {
        try{
            ResultUtil resultUtil = wxOrderInfoService.wxDriverOrder(search);
            return resultUtil;
        }catch (Exception e){
            log.error("查询运单列表失败",e);
            return ResultUtil.error("查询运单列表失败");
        }
    }


    /**
     * WX司机查询货源大厅运单
     *
     * <AUTHOR>
     * @param search
     * @return
     */
    @PostMapping("/wxDriverResourceHallOrder")
    public ResultUtil wxDriverResourceHallOrder(@RequestBody AppOrderSearchVO search) {
        try{
           return wxOrderInfoService.wxDriverResourceHallOrder(search);
        }catch (Exception e){
            log.error("查询运单列表失败",e);
            return ResultUtil.error("查询运单列表失败");
        }
    }

    /**
     * 抢单、装货、签单
     * <AUTHOR>
     * @return
     */
    @PostMapping("/grabOrder")
    public ResultUtil saveGrabOrder(@RequestBody SendOrderVO record) {
        try {
            if(null == record.getLoadingCarPhoto() || "".equals(record.getLoadingCarPhoto())){
                return ResultUtil.error("请上传装车照片");
            }
            CarDriverRelVO carDriverRelVO = record.getCarDriverRelVO();
            //当运费单价单位为元/箱时，TOrderInfoWeight才有可能不为空
            if(null != record.getCarriagePriceUnit() &&
                    record.getCarriagePriceUnit().equals(DictEnum.CARRIAGE_PRICE_UNIT_BOX.code)){
                TOrderInfoWeight torderInfoweight = record.getOrderInfoWeight();
                if(null != torderInfoweight){
                    //对装货重量、毛重做判断，如果不为空，则判断不能小于等于0
                    //装货磅单重量1
                    if(null != torderInfoweight.getDeliverWeightNotesWeight1()){
                        if(torderInfoweight.getDeliverWeightNotesWeight1().compareTo(BigDecimal.ZERO) <= 0){
                            return ResultUtil.error("保存失败，装货磅单重量不可小于等于零");
                        }else if(torderInfoweight.getDeliverWeightNotesWeight1().compareTo(new BigDecimal("75")) >= 0){
                            return ResultUtil.error("装货磅单重量需小于75，请重新填写");
                        }
                    }else{
                        return ResultUtil.error("请填写装货磅单重量");
                    }
                    //发单重量（原发重量）1
                    if(null != torderInfoweight.getPrimaryWeight1()){
                        if(torderInfoweight.getPrimaryWeight1().compareTo(BigDecimal.ZERO) <= 0){
                            return ResultUtil.error("保存失败，原发重量不可小于等于零");
                        }else if(torderInfoweight.getPrimaryWeight1().compareTo(new BigDecimal("75")) >= 0){
                            return ResultUtil.error("原发重量需小于75，请重新填写");
                        }
                    }
                    //毛重1
                    if(null != torderInfoweight.getGrossWeight1()){
                        if(torderInfoweight.getGrossWeight1().compareTo(BigDecimal.ZERO) < 0){
                            return ResultUtil.error("保存失败，毛重不可小于零");
                        }
                    }
                    if(torderInfoweight.getBoxNum() == 2){
                        //装货磅单重量2
                        if(null != torderInfoweight.getDeliverWeightNotesWeight2()){
                            if(torderInfoweight.getDeliverWeightNotesWeight2().compareTo(BigDecimal.ZERO) <= 0){
                                return ResultUtil.error("保存失败，装货重量不可小于等于零");
                            }else if(torderInfoweight.getDeliverWeightNotesWeight2().compareTo(new BigDecimal("75")) >= 0){
                                return ResultUtil.error("装货磅单重量需小于75，请重新填写");
                            }
                            BigDecimal add = torderInfoweight.getDeliverWeightNotesWeight1().add(torderInfoweight.getDeliverWeightNotesWeight2());
                            if(add.compareTo(new BigDecimal("75")) >= 0){
                                return ResultUtil.error("总装货磅单重量需小于75，请重新填写");
                            }
                        }else{
                            return ResultUtil.error("请填写装货重量");
                        }
                        //发单重量（原发重量）2
                        if(null != torderInfoweight.getPrimaryWeight2()){
                            if(torderInfoweight.getPrimaryWeight2().compareTo(BigDecimal.ZERO) <= 0){
                                return ResultUtil.error("保存失败，原发重量不可小于等于零");
                            }else if(torderInfoweight.getPrimaryWeight2().compareTo(new BigDecimal("75")) >= 0){
                                return ResultUtil.error("原发重量需小于75，请重新填写");
                            }
                            if(null != torderInfoweight.getPrimaryWeight1()){
                                BigDecimal add = torderInfoweight.getPrimaryWeight1().add(torderInfoweight.getPrimaryWeight2());
                                if(add.compareTo(new BigDecimal("75")) >= 0){
                                    return ResultUtil.error("总原发重量需小于75，请重新填写");
                                }
                            }
                        }
                        //毛重2
                        if(null != torderInfoweight.getGrossWeight2()){
                            if(torderInfoweight.getGrossWeight2().compareTo(BigDecimal.ZERO) < 0){
                                return ResultUtil.error("保存失败，毛重不可小于零");
                            }
                        }
                    }
                }
            }else{
                if(null != record.getPrimaryWeight()){
                    if(record.getPrimaryWeight() <= 0){
                        return ResultUtil.error("保存失败，装货重量不可小于等于零");
                    }else if(record.getPrimaryWeight() >= 75){
                        return ResultUtil.error("装货重量需小于75，请重新填写");
                    }
                }else{
                    return ResultUtil.error("请填写装货重量");
                }
            }
            if(null != carDriverRelVO && null != carDriverRelVO.getEndcarId()){
                TEndCarInfo tEndCarInfo = endCarInfoAPI.selectByPrimaryKey(carDriverRelVO.getEndcarId());
                if(null != tEndCarInfo.getVehicleNumber()){
                    TCarInsuranceVO carInsuranceVO = new TCarInsuranceVO();
                    carInsuranceVO.setVehicleNumber(tEndCarInfo.getVehicleNumber());
                    TCarInsuranceVO vo = carInsuranceService.selectByVehicleNumber(carInsuranceVO);
                    if(null != vo && null != vo.getAuditStatus() && DictEnum.PASSNODE.code.equals(vo.getAuditStatus())){
                        Date date = new Date();
                        Date insuranceDateEnd = vo.getInsuranceDateEnd();
                        if(null != insuranceDateEnd && date.getTime() > insuranceDateEnd.getTime()){
                            return ResultUtil.error("车辆{" + tEndCarInfo.getVehicleNumber() + "}的保险有效期已过期");
                        }
                    }
                }
            }
            //如果投保方式不是“不投保”，则判断司机信息和车辆信息
            if(null != record.getInsuranceMethod() && !InsuranceMethodsEnum.NOTINSURED.getKey().equals(record.getInsuranceMethod())){
                if((null != record.getAutoselect() && record.getAutoselect()) ||
                        (record.getInsuranceMethod().equals(InsuranceMethodsEnum.MUSTBEINSURED.getKey()))){
                    //按ID分别查询司机和车辆的信息
                    if(null != carDriverRelVO){
                        if(null != carDriverRelVO.getEndDriverId()){
                            TEndUserInfo tEndUserInfo = endUserInfoAPI.selectByPrimaryKey(carDriverRelVO.getEndDriverId());
                            // 司机身份证审核状态必须为通过，否则弹窗提示：由于您已选择购买货运险，应保险公司要求请先完善身份证资料并审核通过后再进行操作
                            if(null != tEndUserInfo){
                                if(null != tEndUserInfo.getAuditStatus() && !DictEnum.PASSNODE.code.equals(tEndUserInfo.getAuditStatus())){
                                    return ResultUtil.error("由于您已选择购买货运险，应保险公司要求请先完善身份证资料并审核通过后再进行操作");
                                }
                            }
                        }
                        if(null != carDriverRelVO.getEndcarId()){
                            TEndCarInfo tEndCarInfo = endCarInfoAPI.selectByPrimaryKey(carDriverRelVO.getEndcarId());
                            // 车辆审核状态必须为通过，否则弹窗提示：由于您已选择购买货运险，应保险公司要求请先完善车辆冀A12345资料并审核通过后再进行操作
                            if(null != tEndCarInfo){
                                if(null != tEndCarInfo.getAuditStatus() && !DictEnum.PASSNODE.code.equals(tEndCarInfo.getAuditStatus())){
                                    return ResultUtil.error("由于您已选择购买货运险，应保险公司要求请先完善车辆"+tEndCarInfo.getVehicleNumber()+"资料并审核通过后再进行操作");
                                }
                            }
                        }
                    }
                }
            }

            //司机电子印章
            /*TFifthGenerationSignOpenAccount tFifthGenerationSignOpenAccount = tFifthGenerationSignOpenAccountApi.selectByUserIdAndType(carDriverRelVO.getEnduserId(), DictEnum.CD.code);
            if (null != tFifthGenerationSignOpenAccount && !"".equals(tFifthGenerationSignOpenAccount)) {
                if (null == tFifthGenerationSignOpenAccount.getSealImage() || "".equals(tFifthGenerationSignOpenAccount.getSealImage())) {
                    return ResultUtil.error("当前司机没有添加印章不可以扫码抢单签到");
                }
            }*/
            String carKey = CAR + carDriverRelVO.getEndcarId();
            String userKey = USER + carDriverRelVO.getEnduserId();
            String randomKey = "random"+record.getRandom();
            boolean carLock = RedissLockUtil.tryLock(carKey, WAIT_5_MINUTE, 50);
            boolean userLock = RedissLockUtil.tryLock(userKey, WAIT_5_MINUTE, 50);

            boolean randomLock = RedissLockUtil.tryLock(randomKey, WAIT_5_MINUTE, 50);
            try {
                if (carLock && userLock && randomLock) {
                    // 查询司机车辆发单数超过1单时，司机车辆审核状态
                    String checkDriverCarAuditStatusByTimes = sendOrderUtil.checkDriverCarAuditStatusByTimes(carDriverRelVO, true);
                    if (null != checkDriverCarAuditStatusByTimes) {
                        ResultUtil resultUtil = ResultUtil.error();
                        resultUtil.setMsg(checkDriverCarAuditStatusByTimes);
                        if (checkDriverCarAuditStatusByTimes.contains("实名认证")) {
                            resultUtil.setData(DictEnum.DRIVER.code);
                        } else {
                            resultUtil.setData(DictEnum.CAR.code);
                        }
                        return resultUtil;
                    }
                    ResultUtil resultUtil = wxOrderInfoService.grabOrder(record);
                    return resultUtil;
                }
            } catch (Exception e){
                log.error("ZJJ-010:抢单失败!, {}", ThrowableUtil.getStackTrace(e));
                String message = e.getMessage();
                if (StringUtils.checkChineseCharacter(message)){
                    return ResultUtil.error(message);
                } else {
                    throw new RuntimeException("ZJJ-010:抢单失败!");
                }
            } finally {
                if (carLock) {
                    RedissLockUtil.unlock(carKey);
                }
                if (userLock) {
                    RedissLockUtil.unlock(userKey);
                }
                if (randomLock) {
                    RedissLockUtil.unlock(randomKey);
                }
            }
        } catch (Exception e) {
            log.error("ZJJ-010:抢单失败!, {}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("ZJJ-010:抢单失败!");
            }
        }

        return ResultUtil.ok();
    }

    /**
     * 货源大厅抢单
     * @param record
     * @return
     */
    @PostMapping("/grabHallOrder")
    public ResultUtil grabHallOrder(@Valid @RequestBody SendOrderVO record) {
        try {
            CarDriverRelVO carDriverRelVO = record.getCarDriverRelVO();
            if (null == carDriverRelVO.getEndcarId()) {
                return ResultUtil.error("车辆为空");
            }
            if (null == carDriverRelVO.getVehicleNumber() || StringUtils.isBlank(carDriverRelVO.getVehicleNumber())) {
                return ResultUtil.error("车辆号为空");
            }
            if (null == carDriverRelVO.getEnduserId()) {
                record.getCarDriverRelVO().setEnduserId(CurrentUser.getEndUserId());
            }
            String carKey = CAR + carDriverRelVO.getEndcarId();
            String userKey = USER + carDriverRelVO.getEnduserId();
            boolean carLock = RedissLockUtil.tryLock(carKey, WAIT_5_MINUTE, 50);
            boolean userLock = RedissLockUtil.tryLock(userKey, WAIT_5_MINUTE, 50);

            TGoodsSourceInfo tGoodsSourceInfo = orderGoodsSourceInfoService.selectByPrimaryKey(record.getGoodsSourceInfoId());
            String resourceKey = GOODS_SOURCE + tGoodsSourceInfo.getId();
            boolean sourceLock = RedissLockUtil.tryLock(resourceKey, WAIT_5_MINUTE, 50);

            try {
                if (carLock && userLock && sourceLock) {
                    if (null!=tGoodsSourceInfo.getGoodsBanTime()){ //下架时间为null则为长期有效
                        if (tGoodsSourceInfo.getStatus().equals(DictEnum.OPENSOURCE.code)) {
                            Date goodsBanTime = tGoodsSourceInfo.getGoodsBanTime();
                            // 补充时分秒
                            goodsBanTime = DateUtils.getDateEnd(goodsBanTime);
                            if (goodsBanTime.getTime() < new Date().getTime()) {
                                // 如果货源已过下架时间，但未下架，将货源置为关闭
                                TGoodsSourceInfo goodsSourceInfo = new TGoodsSourceInfo();
                                goodsSourceInfo.setId(tGoodsSourceInfo.getId());
                                goodsSourceInfo.setStatus(DictEnum.CLOSESOURCE.code);
                                orderGoodsSourceInfoService.updateByPrimaryKeySelective(goodsSourceInfo);
                                return ResultUtil.error("货源已下架");
                            }
                            // 判断预计装货时间是否大于预计下架时间
                            if (record.getEstimateLoadTime().getTime() > goodsBanTime.getTime()) {
                                return ResultUtil.error("预计装货时间不可晚于下架时间，无法完成抢单");
                            }
                        } else {
                            return ResultUtil.error("货源已下架");
                        }
                    }
                    if (null == record.getEstimateLoadTime()) {
                        return ResultUtil.error("预计装货时间为空");
                    }
                    // 判断预计装货时间是否大于24小时
                    if (record.getEstimateLoadTime().getTime() - new Date().getTime() > HOUR_24) {
                        return ResultUtil.error("预计装货时间大于24小时，无法完成抢单");
                    }
                    if (record.getEstimatedLoadingWeight() > tGoodsSourceInfo.getEstimateGoodsWeight()) {
                        return ResultUtil.error("货源库存量不足");
                    }
                    return wxOrderInfoService.grabHallOrder(record);
                } else {
                    return ResultUtil.error("当前车辆或司机正在抢单中，请稍后再试");
                }
            } catch (Exception e){
                log.error("ZJJ-030:抢单失败!, {}", ThrowableUtil.getStackTrace(e));
                String message = e.getMessage();
                if (StringUtils.checkChineseCharacter(message)){
                    return ResultUtil.error(message);
                } else {
                    throw new RuntimeException("抢单失败!");
                }
            } finally {
                if (carLock) {
                    RedissLockUtil.unlock(carKey);
                }
                if (userLock) {
                    RedissLockUtil.unlock(userKey);
                }
                if (sourceLock) {
                    RedissLockUtil.unlock(resourceKey);
                }
            }
        } catch (Exception e) {
            log.error("ZJJ-030:抢单失败!, {}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("抢单失败!");
            }
        }
    }

    /**
     * 取消运单
     * <AUTHOR>
     * @param orderInfoVO
     * @return
     */
    @PostMapping("/cancleOrder")
    public ResultUtil cancleOrder(@RequestBody TOrderInfoVO orderInfoVO) {
        try {
            // 查询是否预付款已支付、已提现
            TAdvanceOrderTmp tAdvanceOrderTmp = advanceOrderTmpService.selectAdvanceOrderTempByOrderCode(orderInfoVO.getCode());
            if (null != tAdvanceOrderTmp) {
                if (DictEnum.P070.code.equals(tAdvanceOrderTmp.getOrderPayStatus())) {
                    return ResultUtil.error("预付款运单正在支付中，不能删除！");
                }
                if (DictEnum.M090.code.equals(tAdvanceOrderTmp.getOrderPayStatus())) {
                    return ResultUtil.error("预付款运单已支付，不能删除！");
                }
                if (DictEnum.M130.code.equals(tAdvanceOrderTmp.getOrderPayStatus())) {
                    return ResultUtil.error("预付款运单已提现，不能删除！");
                }
            }
            wxOrderInfoService.cancleOrder(orderInfoVO);
        } catch (Exception e) {
            log.error("ZJJ-049:运单删除失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("ZJJ-049:运单删除失败!");
            }
        }
        return ResultUtil.ok();
    }

    /**
     * 提现页面数据
     * <AUTHOR>
     *
     * @param
     * @return REsultUtil
     * zhangxin
     */
    @PostMapping("/tixianPage")
    public ResultUtil tixianPage() {
        Integer endUserId = CurrentUser.getEndUserId();
        ResultUtil getData = wxOrderInfoService.selectTiXianPage(endUserId);
        return getData;
    }
    /**
     * 打包运单列表
     * <AUTHOR>
     *
     * @param
     * @return REsultUtil
     */
    @PostMapping("/PackOrderPage")
    public ResultUtil PackOrderPage(String code) {
        ResultUtil getData = wxOrderInfoService.selectPackOrderPage(code);
        return getData;
    }

    /**
     * 京东提现页面
     * @return
     */
    @PostMapping("/jdTixianPage")
    public ResultUtil jdTixianPage() {
        try {
            ResultUtil resultUtil = wxOrderInfoService.jdTixianPage();
            return resultUtil;
        } catch (Exception e) {
            log.error("获取京东钱包失败, {}", e);
        }
        return ResultUtil.ok();
    }

    /**
     * 华夏提现页面
     * @return
     */
    @PostMapping("/hxTixianPage")
    public ResultUtil hxTixianPage() {
        try {
            ResultUtil resultUtil = wxOrderInfoService.hxTixianPage();
            return resultUtil;
        } catch (Exception e) {
            log.error("获取华夏钱包失败, {}", e);
        }
        return ResultUtil.ok();
    }

    /**
     * 提现
     * <AUTHOR>
     * id  orderId
     * bankId 银行卡id
     * @return REsultUtil
     * zhangxin
     */
//    @Log("微信端自助提现")
    @PostMapping("/tixian")
    public ResultUtil tixian(@RequestBody TOrderInfoVO tOrderInfoVO) {
        try {
            Integer endUserId = CurrentUser.getEndUserId();
            if (null != endUserId) {
                // 获取所选车辆id
                HashSet<Integer> carIds = new HashSet<>();
                List<CashOutVO> list = JSONArray.parseArray(tOrderInfoVO.getTxStr(), CashOutVO.class);
                for (CashOutVO cashOutVO : list) {
                    if (cashOutVO.getType().equals(1) || cashOutVO.getType().equals(3) || cashOutVO.getType().equals(4)) {
                        Integer carId = orderInfoService.getCarId(cashOutVO.getCode());
                        carIds.add(carId);
                    } else {
                        List<Integer> carIdByOrderPackCode = orderInfoService.getCarIdByOrderPackCode(cashOutVO.getCode());
                        carIds.addAll(carIdByOrderPackCode);
                    }
                }

                TEndCarInfoVO endCarInfoVO = new TEndCarInfoVO();
                endCarInfoVO.setUserId(endUserId);
                endCarInfoVO.setEndCarId(carIds);
                ResultUtil carAndDriverStatus = endUserCar.getCarAndDriverStatus(endCarInfoVO);
                if (carAndDriverStatus.getCode().equals("success")) {
                    ArrayList<LinkedHashMap> data = (ArrayList) carAndDriverStatus.getData();
                    if (data.size() > 0) {
                        StringBuilder msg = new StringBuilder();
                        for (LinkedHashMap datum : data) {
                            if ("driver".equals(datum.get("type"))) {
                                msg.append(datum.get("mark"));
                                msg.append("。");
                            } else {
                                msg.append(datum.get("mark"));
                                msg.append(",");
                            }
                        }
                        msg.append("未审核通过。");
                        return ResultUtil.error(msg.toString());
                    }
                }
            }
            String oldcode = ObjectUtils.toString(redisUtil.get("TIXIAN" + tOrderInfoVO.getPhone()));
            if (StringUtils.isEmpty(tOrderInfoVO.getCode())) {
                return ResultUtil.error("请输入验证码");
            } else if (StringUtils.isEmpty(oldcode)) {
                return ResultUtil.error("请重新发送验证码");
            } else if (!tOrderInfoVO.getCode().equals(oldcode)) {
                return ResultUtil.error("验证码错误");
            } else if (StringUtils.isBlank(tOrderInfoVO.getBankId()+"")) {
                return ResultUtil.error("请选择一张银行卡");
            } else {
                TVerificationCodeLog tVerificationCodeLog=new TVerificationCodeLog();
                tVerificationCodeLog.setReceivePhoneno(tOrderInfoVO.getPhone());
                tVerificationCodeLog.setVerificationCode(oldcode);
                tVerificationCodeLogAPI.updateIfUsed(tVerificationCodeLog);
                String txStr = tOrderInfoVO.getTxStr();
                log.info("提现信息："+txStr);
                List<CashOutVO> list = null;
                try {
                    list = JSONArray.parseArray(txStr,CashOutVO.class);
                } catch (Exception e) {
                    log.error("获取提现参数错误！",e);
                    throw new RuntimeException("获取提现参数错误！");
                }
                if(list == null || list.size() <= 0){
                    throw new RuntimeException("提现单号为空，请重新提交！");
                }

                // 判断运单是否都是网商支付, 查询支付主表
                List<String> codes = new ArrayList<>(list.size());
                list.forEach(item -> codes.add(item.getCode()));
                Boolean aBoolean = jdOrderPayInfoService.selectWSPayPlatformsUnique(list);
                if (!aBoolean) {
                    return ResultUtil.error("请选择网商支付运单进行提现");
                }

                ResultUtil resultUtil = ResultUtil.ok();
                StringBuffer stringBuffer = new StringBuffer();
                int successResult = 0;
                int errorResult = 0;
                for (CashOutVO cashOutVO : list) {
                    String orderBusinessCode = "";
                    BigDecimal amount = BigDecimal.valueOf(0);
                    TOrderCastChanges tOrderCastChanges = new TOrderCastChanges();
                    if (cashOutVO.getType().equals(1)) {//实体运单提现
                        TOrderInfo tOrderInfo = orderInfoService.selectOrderInfoByCode(cashOutVO.getCode());
                        amount = tOrderInfo.getUserConfirmPaymentAmount().subtract(tOrderInfo.getUserConfirmServiceFee());
                        orderBusinessCode = tOrderInfo.getOrderBusinessCode();
                        tOrderCastChanges = orderCastChangesService.selectByNewOne(cashOutVO.getCode());
                    } else if (cashOutVO.getType().equals(2)) {//打包运单提现
                        TOrderPackInfo tOrderPackInfo = orderPackInfoService.selectByCode(cashOutVO.getCode());
                        amount = tOrderPackInfo.getAppointmentPaymentCash().subtract(tOrderPackInfo.getTotalSelectedOrdersServiceFee());
                        orderBusinessCode = tOrderPackDetailService.selectByPackCode(tOrderPackInfo.getCode()).getOrderBusinessCode();
                        tOrderCastChanges = orderCastChangesService.selectOrderCastChangeOneByPackCode(cashOutVO.getCode());
                    } else if (cashOutVO.getType().equals(3)) {//预支付提现
                        tOrderCastChanges = orderCastChangesService.selectByNewOne(cashOutVO.getCode());
                        TAdvanceOrderTmp advanceOrderTmp = advanceOrderTmpService.selectAdvanceOrderTempByOrderCode(cashOutVO.getCode());
                        cashOutVO.setAdvanceCode(advanceOrderTmp.getCode());
                        amount = advanceOrderTmp.getAdvanceFee();
                        orderBusinessCode = advanceOrderTmp.getOrderBusinessCode();
                        cashOutVO.setAdvanceFee(amount);
                    } else if (cashOutVO.getType().equals(4)) {//节点支付运单提现
                        TOrderPayRule re = new TOrderPayRule();
                        re.setOrderCode(cashOutVO.getCode());
                        re.setPayNodeType(cashOutVO.getPayNodeType());
                        TOrderPayRule tOrderPayRule = tOrderPayRuleService.selectByOrderCodeAndType(re);
                        if(null!=tOrderPayRule.getWithdrawStatus()){
                            if(tOrderPayRule.getWithdrawStatus().equals(DictEnum.PACKEXTRACTPROCESSED.code)
                                    || tOrderPayRule.getWithdrawStatus().equals(DictEnum.PACKWITHDRAW.code)){
                                return  ResultUtil.error("选中运单中包含提现中或已提现运单，请刷新");
                            }
                        }
                        TOrderInfo tOrderInfo = orderInfoService.selectOrderInfoByCode(cashOutVO.getCode());
                        tOrderCastChanges = orderCastChangesService.selectByNewOne(cashOutVO.getCode());
                        orderBusinessCode = tOrderInfo.getOrderBusinessCode();
                        amount = tOrderPayRule.getPaymentFee();
                        cashOutVO.setNodeFee(amount);

                    }
                    String driverWalletKey = "USER" + tOrderCastChanges.getEndDriverWalletId();
                    String orderKey = "ORDER" + cashOutVO.getCode();
                    Boolean driverWalletLock = false;
                    Boolean orderLock = false;
                    if (!redisUtil.hasKey(driverWalletKey)) {
                        driverWalletLock = redisUtil.lock(driverWalletKey);
                    }
                    if (!redisUtil.hasKey(orderKey)) {
                        orderLock = redisUtil.lock(orderKey);
                    }
                    try {
                        if (driverWalletLock && orderLock) {
                            if (null != cashOutVO.getType() && cashOutVO.getType().equals(3)) {
                                // 预付款提现， 查询运单状态
                                TAdvanceOrderTmp tAdvanceOrderTmp = advanceOrderTmpService.selectAdvanceOrderTempByOrderCode(cashOutVO.getCode());
                                if (null != tAdvanceOrderTmp) {
                                    if (null != tAdvanceOrderTmp.getOrderPayStatus()) {
                                        if (!DictEnum.M090.code.equals(tAdvanceOrderTmp.getOrderPayStatus())
                                                && !DictEnum.M120.code.equals(tAdvanceOrderTmp.getOrderPayStatus())) {
                                            // return ResultUtil.ok("尾号为" + tAdvanceOrderTmp.getOrderBusinessCode().substring(tAdvanceOrderTmp.getOrderBusinessCode().length() - 8) + "的运单，运单状态错误，不可提现");
                                            continue;
                                        }
                                    }
                                } else {
                                    // return ResultUtil.ok("尾号为" + tAdvanceOrderTmp.getOrderBusinessCode().substring(tAdvanceOrderTmp.getOrderBusinessCode().length() - 8) + "的运单未找到，不可提现");
                                    continue;
                                }
                            }
                            TOrderInfo orderInfo = orderInfoService.selectByOrderBusinessCode(orderBusinessCode);
                            TCompanyProject tCompanyProject = companyProjectAPI.selectByCompanyProjectById(orderInfo.getCompanyProjectId());
                            TBankCard bankCard = sendOrderUtil.checkCardholder(cashOutVO.getBankId());
                            //此项目是否限额
                            if(tCompanyProject.getIfQuota()){
                                //判断 提现是否超额
                                sendOrderUtil.checkCardHolderWithdrawAmount(bankCard, amount);
                            } else {
                                sendOrderUtil.checkCardHolderWithdrawAmount50Limit(bankCard, amount);
                            }
                            // 判断是否预付款提现
                            if (null != cashOutVO.getType() && cashOutVO.getType().equals(3)) {
                                cashOutVO.setAdvancePayment(true);
                            }
                            wxOrderInfoService.aftertixianWeixin(cashOutVO, DictEnum.DRIWX.code);
                            successResult++;
                        }
                    } catch (Exception e) {
                        errorResult++;
                        log.error("ZJJ-048:运单提现失败!", e);
                        String message = e.getMessage();
                        if (stringBuffer.toString().contains("根据金融及税务政策要求")) {
                            resultUtil.setCode("error");
                            resultUtil.setData("EXCEED");
                            resultUtil.setMsg(message);
                            return resultUtil;
                        }
                        if (StringUtils.checkChineseCharacter(message)){
                            stringBuffer.append(message);
                        } else {
                            stringBuffer.append("运单" + orderBusinessCode + "提现失败");
                        }
                    } finally {
                        if (driverWalletLock && redisUtil.hasKey(driverWalletKey)) {
                            redisUtil.del(driverWalletKey);
                        }
                        if (orderLock && redisUtil.hasKey(orderKey)) {
                            redisUtil.del(orderKey);
                        }
                    }
                }
                if (stringBuffer.length() > 0) {
                    resultUtil = ResultUtil.error(stringBuffer.toString());
                    if (stringBuffer.toString().contains("根据金融及税务政策要求")) {
                        resultUtil.setData("EXCEED");
                    } else {
                        resultUtil.setMsg("提现申请结果，成功：" + successResult + ", 失败：" + errorResult + ".");
                    }
                }
                return resultUtil;
            }
        } catch (Exception e) {
            log.error("ZJJ-048:运单提现失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            }
            return ResultUtil.error("ZJJ-048:运单提现失败!");
        }
    }


    /**
     * 提现中的运单列表
     * <AUTHOR>
     *
     * @param
     * @return REsultUtil
     */
    @PostMapping("/tixianList")
    public ResultUtil tixianList() {
        Integer endUserId = CurrentUser.getEndUserId();
        ResultUtil getData = wxOrderInfoService.getTiXianList(endUserId);
        return getData;
    }

    /**
     * 京东提现
     * @return
     */
    @PostMapping("/jd/tx")
    public ResultUtil jdTx(@RequestBody JDOrderTxVO txVO) {
        try {
            Integer endUserId = CurrentUser.getEndUserId();
            String userLogisticsRole = CurrentUser.getUserLogisticsRole();
            RLock lock;
            Boolean walletLock = false;
            // 获取会员编号
            String partnerAccId = getEnduserPartnerAccId(endUserId, userLogisticsRole);
            lock = redissonClient.getLock(partnerAccId);
            log.info("分布式锁:getLock:" + lock.toString() + ",interrupted:" + Thread.currentThread().isInterrupted() + ",hold:" +
                    lock.isHeldByCurrentThread() + ",threadId:" + Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
            walletLock = lock.tryLock(5, TimeUnit.SECONDS);
            log.info("分布式锁:tryLock:" + lock.toString() + ",interrupted:" + Thread.currentThread().isInterrupted() + ",hold:" +
                    lock.isHeldByCurrentThread() + ",threadId:" + Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
            try {
                if (walletLock) {
                    // 检查司机审核状态
                    TEndUserInfo tEndUserInfo = tEndSUserInfoAPI.selectByPrimaryKey(endUserId);
                    if (null == tEndUserInfo.getAuditStatus() || !tEndUserInfo.getAuditStatus().equals(DictEnum.PASSNODE.code)) {
                        if (DictEnum.CTYPEDRVIVER.code.equals(userLogisticsRole)) {
                            return ResultUtil.error("司机未通过审核");
                        } else if (DictEnum.CTYPECAPTAIN.code.equals(userLogisticsRole)) {
                            return ResultUtil.error("车队长未通过审核");
                        }
                    }
                    // 检查提现验证码
                    ResultUtil smsCode = checkSmsCode(txVO);
                    if (smsCode != null) {
                        return smsCode;
                    }
                    Integer bankCardId = txVO.getBankCardId();
                    // 判断C端是否开户
                    TEndUserOpenRole endUserOpenRole = tEndSUserInfoAPI.queryEnduserOpenRoleStatus(endUserId);
                    if (null == endUserOpenRole || null == endUserOpenRole.getOpenStatus() || !JdEnum.OPENSUCCESS.code.equals(endUserOpenRole.getOpenStatus())) {
                        if (DictEnum.CTYPEDRVIVER.code.equals(userLogisticsRole)) {
                            return ResultUtil.error("司机未开通京东支付，请联系运营平台予以解决。");
                        } else if (DictEnum.CTYPEDRVIVER.code.equals(userLogisticsRole)) {
                            return ResultUtil.error("车队长未开通京东支付，请联系运营平台予以解决。");
                        }
                    }

                    // 查询持卡人开户类型
                    String openAccountType = tEndSUserInfoAPI.queryCardOwnerWhetherSelfOpenRole(bankCardId);
                    if (null == openAccountType || StringUtils.isBlank(openAccountType)) {
                        return ResultUtil.error("银行卡未绑定京东账户，请联系运营平台予以解决。");
                    }
                    // 检查银行卡开户状态
                    TJdBankCardInfo cardInfo = jdPayOrderUtil.checkBankCardOpenStatus(openAccountType, endUserId, bankCardId, userLogisticsRole);
                    txVO.setSelfCard(cardInfo.getSelfCard());
                    txVO.setBankCardId(cardInfo.getBankCardId());
                    txVO.setOpenAccountType(openAccountType);
                    // 京东运单提现
                    try {
                        // 获取提现服务费
                        log.info("提现信息, {}, 提现到{}", JSONUtil.toJsonStr(txVO), openAccountType);
                        // 查询是否存在提现超额申请
                        List<TWithdrawBehalfApplication> withdrawBehalfApplicationList = withdrawBehalfApplicationService.selectByEndUserId(endUserId);
                        if (null != withdrawBehalfApplicationList && !withdrawBehalfApplicationList.isEmpty()) {
                            return ResultUtil.error("您的提现申请正在处理中，请耐心等待，勿重复操作。");
                        }
                        // 1.提现大于1万
                        // Boolean bigThanOneMillion = txVO.getTotalFee().compareTo(new BigDecimal("10000")) >= 0;
                        // 2.提现大于超额金额
                        SysParam quotaAmount = sysParamAPI.getParamByKey(DictEnum.QUOTA.code);
                        Boolean bigThanSysParam = null != quotaAmount
                                && null != quotaAmount.getParamValue()
                                && txVO.getTotalFee().compareTo(new BigDecimal(quotaAmount.getParamValue())) > 0;
                        // 3.提现超额限制状态为"是"
                        SysParam quotaStatus = sysParamAPI.getParamByKey(DictEnum.QUOTASTATUS.code);
                        Boolean quotaStatusResult = null != quotaStatus && null != quotaStatus.getParamValue() && "1".equals(quotaStatus.getParamValue());
                        // 创建提现超额申请
                        if (quotaStatusResult || bigThanSysParam) {
                            txVO.setPartnerAccId(endUserOpenRole.getPartnerAccId());
                            // 查询C端京东账户余额
                            com.lz.common.model.jdPayment.request.query.CustomerQueryBalanceReq balanceReq = new com.lz.common.model.jdPayment.request.query.CustomerQueryBalanceReq();
                            balanceReq.setRequestId(IdWorkerUtil.getInstance().nextId());
                            balanceReq.setRequestTime(DateUtils.getRequestTime());
                            balanceReq.setMerchantCode(jdPropertiesConfig.getMerchantCode());
                            balanceReq.setPartnerId(jdPropertiesConfig.getPartnerId());
                            balanceReq.setPartnerAccId(endUserOpenRole.getPartnerAccId());
                            ResultUtil cloudPay = cloudPayAPI.cloudPay(balanceReq);
                            CustomerQueryBalanceResponse response = CloudPayFormatUtil.ObjToBean(cloudPay, CustomerQueryBalanceResponse.class);
                            if(JdEnum.JDSUCCESSCODE.code.equals(response.getResponseCode()) && null != response.getSubAccountMoney()) {
                                BigDecimal availableBalance = OrderMoneyUtil.changeF2Y(response.getSubAccountMoney());
                                if (availableBalance.compareTo(txVO.getTotalFee()) < 0) {
                                    return ResultUtil.error("余额不足");
                                }
                            } else {
                                return ResultUtil.error("余额查询失败");
                            }
                            jdOrderPayService.txQuota(txVO);
                            return ResultUtil.error("您的提现申请已提交，预计于次日到账，请耐心等待。");
                        }
                        BigDecimal txServiceFee = jdPayOrderUtil.getTxServiceFee(txVO.getTotalFee(), openAccountType);
                        if (null != txServiceFee && txServiceFee.compareTo(BigDecimal.ZERO) > 0) {
                            if (txServiceFee.compareTo(txVO.getTotalFee()) >= 0) {
                                return ResultUtil.error("ZJJ-601：提现失败！提现服务费不能大于等于提现金额，请联系运营平台予以解决。");
                            }
                        }
                        // 设置提现服务费
                        txVO.setTxServiceFee(txServiceFee);
                        // 本人银行卡，发起提现申请
                        if (txVO.getSelfCard()) {
                            return jdOrderPayService.tx(txVO);
                        } else {
                            // 非本人银行卡，发起转账申请
                            return jdOrderPayService.txTransferApply(txVO);
                        }
                    } catch (Exception e) {
                        log.error("ZJJ-600:提现失败!", e);
                        String message = e.getMessage();
                        if (StringUtils.checkChineseCharacter(message)){
                            return ResultUtil.error(message);
                        } else {
                            return ResultUtil.error("ZJJ-600:提现失败!");
                        }
                    }
                } else {
                    throw new RuntimeException("当前用户正在操作中，请稍后再试。");
                }
            } catch (Exception e) {
                log.error("ZJJ-600:提现失败!", e);
                String message = e.getMessage();
                if (StringUtils.checkChineseCharacter(message)){
                    return ResultUtil.error(message);
                }
                return ResultUtil.error("ZJJ-600:提现失败!");
            } finally {
                log.info("分布式锁:finally:" + lock.toString() + ",interrupted:" + Thread.currentThread().isInterrupted() + ",hold:" +
                        lock.isHeldByCurrentThread() + ",threadId:" + Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    log.info("分布式锁:unlock:" + lock.toString() + ",interrupted:" + Thread.currentThread().isInterrupted() + ",hold:" +
                            lock.isHeldByCurrentThread() + ",threadId:" + Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                    lock.unlock();
                }
            }
        } catch (Exception e) {
            log.error("ZJJ-600:提现失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            }
            return ResultUtil.error("ZJJ-600:提现失败!");
        }
    }


    /**
     * 华夏提现
     * @return
     */
    @PostMapping("/hx/tx")
    public ResultUtil hxTx(@RequestBody HxOrderTxVO txVO) {
        try {
            if( null == txVO.getIfRxdb() || "".equals(txVO.getIfRxdb()) || !txVO.getIfRxdb()){
                return ResultUtil.error("请更新版本人脸识别提现");
            }
            Integer endUserId = CurrentUser.getEndUserId();
            Integer accountId = CurrentUser.getUserAccountId();
            String userLogisticsRole = CurrentUser.getUserLogisticsRole();
            RLock lock;
            Boolean walletLock = false;
            // 获取会员编号
            TZtAccountOpenInfo tZtAccountOpenInfo = hxOpenRoleCommonAPI.selectByAccountId(accountId);
            // 判断C端是否开户
            if (null == tZtAccountOpenInfo || null == tZtAccountOpenInfo.getStatus() || tZtAccountOpenInfo.getStatus()!=1) {
                if (DictEnum.CTYPEDRVIVER.code.equals(userLogisticsRole)) {
                    return ResultUtil.error("司机未开通华夏支付，请联系运营平台予以解决。");
                } else if (DictEnum.CTYPECAPTAIN.code.equals(userLogisticsRole)) {
                    return ResultUtil.error("车队长未开通华夏支付，请联系运营平台予以解决。");
                }
            }
            String partnerAccId = tZtAccountOpenInfo.getPartnerAccId();
            lock = redissonClient.getLock(partnerAccId);
            log.info("分布式锁:getLock:" + lock.toString() + ",interrupted:" + Thread.currentThread().isInterrupted() + ",hold:" +
                    lock.isHeldByCurrentThread() + ",threadId:" + Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
            walletLock = lock.tryLock();
            log.info("分布式锁:tryLock:" + lock.toString() + ",interrupted:" + Thread.currentThread().isInterrupted() + ",hold:" +
                    lock.isHeldByCurrentThread() + ",threadId:" + Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
            try {
                if (walletLock) {
                    Integer bankCardId = txVO.getBankCardId();
                    //判断京东余额是否充足
                    CustomerQueryBalanceReq req = new CustomerQueryBalanceReq();
                    req.setPartnerId(hxPropertiesConfig.getPartnerId());
                    req.setRequestId(IdWorkerUtil.getInstance().nextId());
                    req.setRequestTime(DateUtils.getRequestTime());
                    req.setChannelId(hxPropertiesConfig.getChannelId());
                    req.setPartnerAccId(partnerAccId);
                    req.setAccountType(1);
                    log.info("查询华夏余额入参：{}", JSONObject.toJSONString(req));
                    ResultUtil resultUtil1 = cloudPaymentAPI.execute(req);
                    commonSdk.responseModel.CustomerQueryBalanceResponse response = CloudPayFormatUtil.ObjToBean(resultUtil1, commonSdk.responseModel.CustomerQueryBalanceResponse.class);
                    log.info("查询华夏余额回参：{}", JSONObject.toJSONString(response));
                    if(JdEnum.JDSUCCESSCODE.code.equals(response.getResponseCode())){
                        if(txVO.getTotalFee().doubleValue()>response.getSubAccountMoney().doubleValue()){
                            return ResultUtil.error("华夏实际余额不足,提现失败");
                        }
                    }else{
                        return ResultUtil.error("查询华夏余额失败");
                    }
                    // 检查银行卡开户状态
                    TZtBankCardDTO cardInfo = hxPayOrderUtil.checkBankCardOpenStatus(endUserId, bankCardId, userLogisticsRole);
                    txVO.setBankCardId(cardInfo.getBankCardId());
                    // 京东运单提现
                    try {
                        log.info("提现信息, {}, 提现到{}", JSONUtil.toJsonStr(txVO));
                        SysParam sysParam = sysParamAPI.getParamByKey("HXTXAMOUNT");
                        if (null != sysParam && txVO.getTotalFee().longValue()>Long.parseLong(sysParam.getParamValue())) {
                            return ResultUtil.error("提现金额不能大于"+sysParam.getParamValue());
                        }
                        // 本人银行卡，发起提现申请
                        return hxOrderPayService.tx(txVO);
                    } catch (Exception e) {
                        log.error("ZJJ-600:提现失败!", e);
                        String message = e.getMessage();
                        if (StringUtils.checkChineseCharacter(message)){
                            return ResultUtil.error(message);
                        } else {
                            return ResultUtil.error("ZJJ-600:提现失败!");
                        }
                    }
                } else {
                    throw new RuntimeException("当前用户正在操作中，请稍后再试。");
                }
            } catch (Exception e) {
                log.error("ZJJ-600:提现失败!", e);
                String message = e.getMessage();
                if (StringUtils.checkChineseCharacter(message)){
                    return ResultUtil.error(message);
                }
                return ResultUtil.error("ZJJ-600:提现失败!");
            } finally {
                log.info("分布式锁:finally:" + lock.toString() + ",interrupted:" + Thread.currentThread().isInterrupted() + ",hold:" +
                        lock.isHeldByCurrentThread() + ",threadId:" + Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    log.info("分布式锁:unlock:" + lock.toString() + ",interrupted:" + Thread.currentThread().isInterrupted() + ",hold:" +
                            lock.isHeldByCurrentThread() + ",threadId:" + Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                    lock.unlock();
                }
            }
        } catch (Exception e) {
            log.error("ZJJ-600:提现失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            }
            return ResultUtil.error("ZJJ-600:提现失败!");
        }
    }

    @PostMapping("/hx/checkWithdrawal")
    public ResultUtil checkWithdrawal(@RequestBody HxOrderTxVO txVO) {
        Integer endUserId = CurrentUser.getEndUserId();
        String userLogisticsRole = CurrentUser.getUserLogisticsRole();

        TEndUserInfo tEndUserInfo = tEndSUserInfoAPI.selectByPrimaryKey(endUserId);
        // 检查司机审核状态
        if (null == tEndUserInfo.getAuditStatus() || !tEndUserInfo.getAuditStatus().equals(DictEnum.PASSNODE.code)) {
            if (DictEnum.CTYPEDRVIVER.code.equals(userLogisticsRole)) {
                return ResultUtil.error("司机未通过审核");
            } else if (DictEnum.CTYPECAPTAIN.code.equals(userLogisticsRole)) {
                return ResultUtil.error("车队长未通过审核");
            }
        }
        // 检查提现验证码  (App端 拆出来单独验证)
        String oldcode = ObjectUtils.toString(redisUtil.get(DictEnum.TIXIAN.code + txVO.getPhone()));
        if (StringUtils.isEmpty(txVO.getCode())) {
            return ResultUtil.error("请输入验证码");
        } else if (StringUtils.isEmpty(oldcode)) {
            return ResultUtil.error("请重新发送验证码");
        } else if (!txVO.getCode().equals(oldcode)) {
            return ResultUtil.error("验证码错误");
        }
        return ResultUtil.ok();
    }


    /**
     * 获取C端会员编号
     * @param endUserId
     * @param userLogisticsRole
     * @return
     */
    private String getEnduserPartnerAccId(Integer endUserId, String userLogisticsRole) {
        String partnerAccId = "";
        if (DictEnum.CTYPEDRVIVER.code.equals(userLogisticsRole)) {
            SelectOpenRoleVO vo = new SelectOpenRoleVO();
            vo.setDriverId(endUserId);
            ResultUtil openRoleStatus = openRoleCommonAPI.selectCarrierCompanyEnduserOpenRoleStatus(vo);
            OpenRoleStatusListDTO dto = JSONUtil.toBean(JSONUtil.parseObj(openRoleStatus.getData()), OpenRoleStatusListDTO.class);
            if (null == dto || null == dto.getDriverStatus()
                    || null == dto.getDriverStatus().getStatus() || !dto.getDriverStatus().getStatus()
                    || null == dto.getDriverStatus().getPartnerAccId()) {
                throw new RuntimeException("司机未开通京东支付，请联系运营平台予以解决。");
            }
            partnerAccId = dto.getDriverStatus().getPartnerAccId();
        }
        if (DictEnum.CTYPECAPTAIN.code.equals(userLogisticsRole)) {
            SelectOpenRoleVO vo = new SelectOpenRoleVO();
            vo.setCaptainId(endUserId);
            ResultUtil openRoleStatus = openRoleCommonAPI.selectCarrierCompanyEnduserOpenRoleStatus(vo);
            OpenRoleStatusListDTO dto = JSONUtil.toBean(JSONUtil.parseObj(openRoleStatus.getData()), OpenRoleStatusListDTO.class);
            if (null == dto || null == dto.getCaptionStatus()
                    || null == dto.getCaptionStatus().getStatus() || !dto.getCaptionStatus().getStatus()
                    || null == dto.getCaptionStatus().getPartnerAccId()) {
                throw new RuntimeException("车队长未开通京东支付，请联系运营平台予以解决。");
            }
            partnerAccId = dto.getCaptionStatus().getPartnerAccId();
        }
        return partnerAccId;
    }

    private ResultUtil checkSmsCode(JDOrderTxVO txVO) {
        String oldcode = ObjectUtils.toString(redisUtil.get(DictEnum.TIXIAN.code + txVO.getPhone()));
        if (StringUtils.isEmpty(txVO.getCode())) {
            return ResultUtil.error("请输入验证码");
        } else if (StringUtils.isEmpty(oldcode)) {
            return ResultUtil.error("请重新发送验证码");
        } else if (!txVO.getCode().equals(oldcode)) {
            return ResultUtil.error("验证码错误");
        } else {
            TVerificationCodeLog tVerificationCodeLog=new TVerificationCodeLog();
            tVerificationCodeLog.setReceivePhoneno(txVO.getPhone());
            tVerificationCodeLog.setVerificationCode(oldcode);
            tVerificationCodeLogAPI.updateIfUsed(tVerificationCodeLog);
        }
        return null;
    }

    public ResultUtil hxcheckSmsCode(@RequestBody HxOrderTxVO txVO) {
        String oldcode = ObjectUtils.toString(redisUtil.get(DictEnum.TIXIAN.code + txVO.getPhone()));
        if (StringUtils.isEmpty(txVO.getCode())) {
            return ResultUtil.error("请输入验证码");
        } else if (StringUtils.isEmpty(oldcode)) {
            return ResultUtil.error("请重新发送验证码");
        } else if (!txVO.getCode().equals(oldcode)) {
            return ResultUtil.error("验证码错误");
        }
        return null;
    }

    //司机提现校验验证码接口
    @PostMapping("/hx/tx/hxcheckSmsCode")
    public ResultUtil hxcheckSmsCodeNew(@RequestBody HxOrderTxVO txVO) {
        String oldcode = ObjectUtils.toString(redisUtil.get(DictEnum.TIXIAN.code + txVO.getPhone()));
        if (StringUtils.isEmpty(txVO.getCode())) {
            return ResultUtil.error("请输入验证码");
        } else if (StringUtils.isEmpty(oldcode)) {
            return ResultUtil.error("请重新发送验证码");
        } else if (!txVO.getCode().equals(oldcode)) {
            return ResultUtil.error("验证码错误");
        }
        return ResultUtil.ok();
    }

    /**
    * @description C端京东提现记录查询
    * <AUTHOR>
    * @date 2021/11/26 09:27
    */
    @PostMapping("/txRecord")
    public ResultUtil txRecord(@RequestBody TxRecordQueryVO vo) {
        return orderPayDetailService.selectTxRecord(vo);
    }


    /**
     * 货源大厅抢单、装货、签单
     * <AUTHOR>
     * @return
     */
    @PostMapping("/grabResourceHallOrder")
    public ResultUtil grabResourceHallOrder(@RequestBody SendOrderVO record) {
        try {
            CarDriverRelVO carDriverRelVO = record.getCarDriverRelVO();

            String carKey = "CAR" + carDriverRelVO.getEndcarId();
            String userKey = "USER" + carDriverRelVO.getEnduserId();
            String randomKey = "random"+record.getRandom();
            boolean carLock = RedissLockUtil.tryLock(carKey, 5 * 5 * 60, 50);
            boolean userLock = RedissLockUtil.tryLock(userKey, 5 * 5 * 60, 50);

            boolean randomLock = RedissLockUtil.tryLock(randomKey, 5 * 5 * 60, 50);
            try {
                if (carLock && userLock && randomLock) {
                    ResultUtil resultUtil = wxOrderInfoService.grabOrder(record);
                    return resultUtil;
                }
            } catch (Exception e){
                log.error("ZJJ-010:抢单失败!", e);
                String message = e.getMessage();
                if (StringUtils.checkChineseCharacter(message)){
                    return ResultUtil.error(message);
                } else {
                    throw new RuntimeException("ZJJ-010:抢单失败!");
                }
            } finally {
                if (carLock) {
                    RedissLockUtil.unlock(carKey);
                }
                if (userLock) {
                    RedissLockUtil.unlock(userKey);
                }
                if (randomLock) {
                    RedissLockUtil.unlock(randomKey);
                }
            }
        } catch (Exception e) {
            log.error("ZJJ-010:抢单失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                return ResultUtil.error(message);
            } else {
                return ResultUtil.error("ZJJ-010:抢单失败!");
            }
        }

        return ResultUtil.ok();
    }

    @PostMapping("/countCarriageFee")
    public ResultUtil countCarriageFee(@RequestBody SendOrderVO record) {
        return wxOrderInfoService.countCarriageFee(record);
    }

    /**
     * 业务部运单查询
     * @param search
     * @return
     */
    @PostMapping("/business")
    public ResultUtil businessOrder(@RequestBody AppOrderSearchVO search) {
        try {
            ResultUtil resultUtil = wxOrderInfoService.businessOrder(search);
            return resultUtil;
        } catch (Exception e) {
            log.error("查询运单列表失败", e);
            return ResultUtil.error("查询运单列表失败");
        }
    }



}

