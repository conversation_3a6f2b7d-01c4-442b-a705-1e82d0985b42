package com.lz.controller;

import com.lz.model.*;
import com.lz.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 *  @Description: 导入运单结果类
 */
@Slf4j
@RestController
@RequestMapping("/importPushContract")
public class TOrderBatchImportPushContractController {
    @Resource
    private TOrderBatchImportPushContractService contractService;

    @PostMapping("/updateByPrimaryKeySelective")
    public void updateByPrimaryKeySelective(@RequestBody TOrderBatchImportPushContract record) {
        contractService.updateByPrimaryKeySelective(record);
    }
}
