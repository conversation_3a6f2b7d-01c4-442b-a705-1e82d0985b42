package com.lz.controller.app;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lz.api.*;
import com.lz.common.config.RedisUtil;
import com.lz.common.dbenum.BusinessCode;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.encryption.Encrypt;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.CodeEnum;
import com.lz.common.redisson.RedissLockUtil;
import com.lz.common.util.*;
import com.lz.common.util.gaode.AddressLocationUtil;
import com.lz.dto.*;
import com.lz.model.*;
import com.lz.payment.Payment;
import com.lz.payment.WalletUtil;
import com.lz.service.*;
import com.lz.sms.model.SmsReq;
import com.lz.sms.model.SmsResp;
import com.lz.sms.service.SmsClientService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SmsMessage;
import com.lz.system.model.SysParam;
import com.lz.util.BigDecimalUtils;
import com.lz.util.OrderDeductionUtil;
import com.lz.util.SendOrderUtil;
import com.lz.vo.*;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * App运单管理
 */
@Slf4j
@RestController
@RequestMapping("/app/order")
public class AppOrderController {

    public static final String GOODS_SOURCE = "GOODS_SOURCE";

    @Autowired
    AppOrderService appOrderService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private SmsClientService smsClientService;

    @Autowired
    private TOrderInfoService orderInfoService;

    @Autowired
    private TOrderCastChangesService orderCastChangesService;

    @Autowired
    private TOrderPayInfoService orderPayInfoService;

    @Autowired
    private AppCommonAPI appCommonAPI;

    @Autowired
    private TOrderPackInfoService orderPackInfoService;

    @Autowired
    private CarrierService carrierService;

    @Autowired
    private CompanyProjectAPI companyProjectAPI;

    @Autowired
    private GoodsSourceAPI goodsSourceAPI;

    @Autowired
    private ProjectCarrierAPI projectCarrierAPI;
    @Autowired
    private TVerificationCodeLogAPI tVerificationCodeLogAPI;

    @Autowired
    private TBankCardAPI bankCardAPI;

    @Autowired
    private TOrderStateService orderStateService;

    @Autowired
    private WalletUtil walletUtil;

    @Autowired
    private EnduserCarRelAPI endUserCar;

    @Autowired
    private SendOrderUtil sendOrderUtil;

    @Autowired
    private TAdvanceOrderTmpService advanceOrderTmpService;

    @Autowired
    private LinkGoodsRelAPI linkGoodsRelAPI;

    @Autowired
    private TOrderPayRuleService tOrderPayRuleService;

    @Autowired
    private TOrderGoodsSourceInfoService orderGoodsSourceInfoService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private TEndSUserInfoAPI tEndSUserInfoAPI;

    @Autowired
    private TEndCarInfoAPI tEndCarInfoAPI;

    @Resource
    private TOrderInfoDetailService orderInfoDetailService;

    @Resource
    private SysParamAPI sysParamAPI;

    @Resource
    private TOrderInsuranceService orderInsuranceService;

    @Resource
    private TCarInsuranceService carInsuranceService;

    /**
     * APP运单列表
     * Yan
     *
     * @param orderVO
     * @return
     */
    @PostMapping("/selectByPage")
    public ResultUtil selectOrderAll(@RequestBody AppOrderSearchVO orderVO) {
        try {
            return appOrderService.appSelectOrderList(orderVO);
        } catch (RuntimeException e) {
            return ResultUtil.error(e.getMessage());
        } catch (Exception e) {
            log.error("YL-060:运单列表查询失败，参数：" + orderVO.toString() + "错误信息" + e);
            return ResultUtil.error("YL-060:运单列表查询失败");
        }
    }

    /**
     * App 运单详情查询
     * Yan
     *
     * @return
     */
    @PostMapping("/detailed")
    public ResultUtil waybillDetailedInfo(@RequestBody AppOrderSearchVO search) {
        try {
            ResultUtil resultUtil = appOrderService.appSelectOrderDetails(search.getCode(), search.getPaymentPlatforms());
            return resultUtil;
        } catch (RuntimeException e) {
            return ResultUtil.error(e.getMessage());
        } catch (Exception e) {
            log.error("YL-061:运单详情查询失败，参数：" + search.toString() + "错误信息:", e);
            return ResultUtil.error("YL-061:运单详情查询失败");
        }
    }

    /**
     * APP 运单详情查询运单的结算规则  在快照表, 没有收单的查原始的
     * 这个是快照的规则
     * Yan
     *
     * @param search
     * @return
     */
    @PostMapping("/snapshotCarriageRule")
    public ResultUtil getOrderSnapshotCarriageRule(@RequestBody AppOrderSearchVO search) {
        try {
            return appOrderService.appSelectOrderCarriageRule(search.getCode());
        } catch (Exception e) {
            log.error("YL-063:运单结算规则查询失败" + e);
            return ResultUtil.error("YL-063:运单结算规则查询失败");
        }
    }

    /**
     * 发单
     *
     * @return
     * <AUTHOR>
     */
    @PostMapping(value = "/sendOrder")
    public ResultUtil saveSendOrder(@Validated @RequestBody SendOrderVO sendBillVO) {
        String vehicleNumber = "";
        //当运费单价单位为元/箱时，TOrderInfoWeight才有可能不为空
        if(null != sendBillVO.getCarriagePriceUnit() &&
                sendBillVO.getCarriagePriceUnit().equals(DictEnum.CARRIAGE_PRICE_UNIT_BOX.code)){
            TOrderInfoWeight torderInfoweight = sendBillVO.getOrderInfoWeight();
            if(null != torderInfoweight){
                //对装货重量、毛重做判断，如果不为空，则判断不能小于等于0
                //装货磅单重量1
                if(null != torderInfoweight.getDeliverWeightNotesWeight1()){
                    if(torderInfoweight.getDeliverWeightNotesWeight1().compareTo(BigDecimal.ZERO) < 0){
                        return ResultUtil.error("保存失败，装货磅单重量不可小于零");
                    }else if(torderInfoweight.getDeliverWeightNotesWeight1().compareTo(new BigDecimal("75")) >= 0){
                        return ResultUtil.error("装货磅单重量需小于75，请重新填写");
                    }
                }
                //发单重量（原发重量）1
                if(null != torderInfoweight.getPrimaryWeight1()){
                    if(torderInfoweight.getPrimaryWeight1().compareTo(BigDecimal.ZERO) <= 0){
                        return ResultUtil.error("保存失败，原发重量不可小于等于零");
                    }else if(torderInfoweight.getPrimaryWeight1().compareTo(new BigDecimal("75")) >= 0){
                        return ResultUtil.error("原发重量需小于75，请重新填写");
                    }
                }
                //毛重1
                if(null != torderInfoweight.getGrossWeight1()){
                    if(torderInfoweight.getGrossWeight1().compareTo(BigDecimal.ZERO) < 0){
                        return ResultUtil.error("保存失败，毛重不可小于零");
                    }
                }
                if(torderInfoweight.getBoxNum() == 2){
                    //装货磅单重量2
                    if(null != torderInfoweight.getDeliverWeightNotesWeight2()){
                        if(torderInfoweight.getDeliverWeightNotesWeight2().compareTo(BigDecimal.ZERO) < 0){
                            return ResultUtil.error("保存失败，装货重量不可小于零");
                        }else if(torderInfoweight.getDeliverWeightNotesWeight2().compareTo(new BigDecimal("75")) >= 0){
                            return ResultUtil.error("装货磅单重量需小于75，请重新填写");
                        }
                        if(null != torderInfoweight.getDeliverWeightNotesWeight1()){
                            BigDecimal add = torderInfoweight.getDeliverWeightNotesWeight1().add(torderInfoweight.getDeliverWeightNotesWeight2());
                            if(add.compareTo(new BigDecimal("75")) >= 0){
                                return ResultUtil.error("总装货磅单重量需小于75，请重新填写");
                            }
                        }

                    }
                    //发单重量（原发重量）2
                    if(null != torderInfoweight.getPrimaryWeight2()){
                        if(torderInfoweight.getPrimaryWeight2().compareTo(BigDecimal.ZERO) <= 0){
                            return ResultUtil.error("保存失败，原发重量不可小于等于零");
                        }else if(torderInfoweight.getPrimaryWeight2().compareTo(new BigDecimal("75")) >= 0){
                            return ResultUtil.error("原发重量需小于75，请重新填写");
                        }
                        if(null != torderInfoweight.getPrimaryWeight1()){
                            BigDecimal add = torderInfoweight.getPrimaryWeight1().add(torderInfoweight.getPrimaryWeight2());
                            if(add.compareTo(new BigDecimal("75")) >= 0){
                                return ResultUtil.error("总原发重量需小于75，请重新填写");
                            }
                        }
                    }
                    //毛重2
                    if(null != torderInfoweight.getGrossWeight2()){
                        if(torderInfoweight.getGrossWeight2().compareTo(BigDecimal.ZERO) < 0){
                            return ResultUtil.error("保存失败，毛重不可小于零");
                        }
                    }
                }
            }
        }else{
            if(null != sendBillVO.getPrimaryWeight()){
                if(sendBillVO.getPrimaryWeight() <= 0){
                    return ResultUtil.error("保存失败，装货重量不可小于等于零");
                }else if(sendBillVO.getPrimaryWeight() >= 75){
                    return ResultUtil.error("装货重量需小于75，请重新填写");
                }
            }
        }
        try {
            ResultUtil error = ResultUtil.error();
            List<CarDriverRelVO> carDriverRelVOList = sendBillVO.getCarDriverRelVOList();
            List<CarDriverRelVO> carDriverRelVOS = checkCarAndUser(carDriverRelVOList);
            if (null == carDriverRelVOS || carDriverRelVOS.size() == 0) {
                return ResultUtil.error("请选择车辆司机");
            }
            CarDriverRelVO carDriverRelVO = carDriverRelVOS.get(0);
            error.setData(carDriverRelVO.getVehicleNumber());
            vehicleNumber = carDriverRelVO.getVehicleNumber();
            double carriageUnitPrice = null == sendBillVO.getCurrentCarriageUnitPrice() ? 0D : sendBillVO.getCurrentCarriageUnitPrice();
            double estimateGoodsWeight = null == sendBillVO.getEstimateGoodsWeight() ? 0D : sendBillVO.getEstimateGoodsWeight();
            if (carriageUnitPrice * estimateGoodsWeight == 0D) {
                error.setMsg("运费不合法，无法发单");
                return error;
            }
            if (estimateGoodsWeight > 99999.99) {
                error.setMsg("请输入1-99999");
                return error;
            }
            //当运费单价单位为元/吨时，判断运费单价
            if(DictEnum.CARRIAGE_PRICE_UNIT_DUN.code.equals(sendBillVO.getCarriagePriceUnit())){
                if (carriageUnitPrice>Double.parseDouble(DictEnum.PAY_PRICE_LIMIT.code)){
                    error.setMsg("运费单价不可超过"+ DictEnum.PAY_PRICE_LIMIT.code +"元/吨");
                    return error;
                }
            } else if (DictEnum.CARRIAGE_PRICE_UNIT_BOX.code.equals(sendBillVO.getCarriagePriceUnit())
                    || DictEnum.CARRIAGE_PRICE_UNIT_CAR.code.equals(sendBillVO.getCarriagePriceUnit())) {
                SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.PAY_BOX_CAR_MAX_PRICE.code);
                if (null == paramByKey || null == paramByKey.getParamValue() || StringUtils.isBlank(paramByKey.getParamValue())) {
                    error.setMsg("运费单价不可超过" + DictEnum.PAY_PRICE_LIMIT.code + "元");
                    return error;
                }
                if (carriageUnitPrice > Double.parseDouble(paramByKey.getParamValue())) {
                    error.setMsg("运费单价不可超过" + paramByKey.getParamValue() + "元");
                    return error;
                }
            }
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

            //检查车辆和司机信息是否完整，车辆司机信息是否重复 add zhangjiji 2019/6/27
            String errorCarUser = checkRepeatCarUser(carDriverRelVOS);
            if (StringUtils.isNotEmpty(errorCarUser)) {
                return ResultUtil.error(errorCarUser);
            }

            //承运方与企业合作的项目是否开启
            TProjectCarrierRel projectCarrierRel = new TProjectCarrierRel();
            projectCarrierRel.setCompanyId(sendBillVO.getCompanyId());
            projectCarrierRel.setProjectId(sendBillVO.getProjectId());
            projectCarrierRel.setIfEfficient(false);
            ResultUtil selectProjectCarrier = projectCarrierAPI.selectProjectCarrier(projectCarrierRel);
            if (null != selectProjectCarrier.getCode() && selectProjectCarrier.getCode().equals("success")) {
                ArrayList data = (ArrayList) selectProjectCarrier.getData();
                if (null != data) {
                    if (data.size() == 0) {
                        error.setMsg("请选择其他货源进行发单操作，或联系企业管理员重新开启该货源。");
                        ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                        exceptionSendOrder.setCount(1);
                        exceptionSendOrder.setIsLogic(true);
                        exceptionSendOrder.setMessage("请选择其他货源进行发单操作，或联系企业管理员重新开启该货源。");
                        HashMap<String, Object> result = new HashMap<>();
                        result.put("exceptionSendOrder", exceptionSendOrder);
                        error.setData(result);
                        return error;
                    }
                    if (data.size() > 1) {
                        log.error("ZJJ-001：发单失败，请联系运营平台予以解决。");
                        error.setMsg("ZJJ-001：发单失败，请联系运营平台予以解决。");
                        ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                        exceptionSendOrder.setCount(2);
                        exceptionSendOrder.setIsLogic(true);
                        exceptionSendOrder.setMessage("ZJJ-001：发单失败，请联系运营平台予以解决。");
                        HashMap<String, Object> result = new HashMap<>();
                        result.put("exceptionSendOrder", exceptionSendOrder);
                        error.setData(result);
                        return error;
                    }
                    LinkedHashMap projectCarrier = (LinkedHashMap) data.get(0);
                    Integer carrierId = (Integer) projectCarrier.get("carrierId");
                    sendBillVO.setCarrierId(carrierId);
                } else {
                    log.error("请选择其他货源进行发单操作，或联系企业管理员重新开启该货源。");
                    error.setMsg("请选择其他货源进行发单操作，或联系企业管理员重新开启该货源。");
                    ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                    exceptionSendOrder.setCount(1);
                    exceptionSendOrder.setIsLogic(true);
                    HashMap<String, Object> result = new HashMap<>();
                    result.put("exceptionSendOrder", exceptionSendOrder);
                    error.setData(result);
                    return error;
                }
            }

            Integer goodsSourceInfoId = sendBillVO.getGoodsSourceInfoId();
            //查询运单资源
            ResultUtil goodsSourceAndLineInfo = goodsSourceAPI.selectGoodsSourceAndLineInfo(goodsSourceInfoId);
            if (null != goodsSourceAndLineInfo) {
                if (null != goodsSourceAndLineInfo.getCode() && StringUtils.isNotEmpty(goodsSourceAndLineInfo.getCode())
                        && "error".equals(goodsSourceAndLineInfo.getCode())) {
                    String msg = null != goodsSourceAndLineInfo.getMsg() ? goodsSourceAndLineInfo.getMsg() : "获取货源信息失败";
                    error.setMsg(msg);
                    HashMap<String, Object> result = new HashMap<>();
                    ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                    exceptionSendOrder.setCount(1);
                    exceptionSendOrder.setIsLogic(false);
                    result.put("exceptionSendOrder", exceptionSendOrder);
                    error.setData(result);
                    return error;
                }
            }
            LinkedHashMap goodsSourceAndLineInfoData = (LinkedHashMap) goodsSourceAndLineInfo.getData();
            LinkedHashMap source = (LinkedHashMap) goodsSourceAndLineInfoData.get("source");
            CompanySourceDTO companySourceDTO = objectMapper.convertValue(source, CompanySourceDTO.class);
            log.info("货源信息：{}", JSON.toJSONString(companySourceDTO));
            LinkedHashMap receivePrincipal = (LinkedHashMap) goodsSourceAndLineInfoData.get("receivePrincipal");
            LineInfoPrincipalDTO receiver = objectMapper.convertValue(receivePrincipal, LineInfoPrincipalDTO.class);

            if (companySourceDTO.getIfSendOrderStatus()) {
                //车辆司机信息
                TEndUserInfo tEndUserInfo = tEndSUserInfoAPI.selectByPrimaryKey(carDriverRelVO.getEnduserId());
                if (!DictEnum.PASSNODE.code.equals(tEndUserInfo.getAuditStatus())) {
                    return ResultUtil.error("请司机{" + tEndUserInfo.getRealName() + "}先完成实名认证后再发单");
                }
                TEndCarInfo tEndCarInfo = tEndCarInfoAPI.selectByPrimaryKey(carDriverRelVO.getEndcarId());
                if (!DictEnum.PASSNODE.code.equals(tEndCarInfo.getAuditStatus())) {
                    return ResultUtil.error("请司机先完善车辆{" + tEndCarInfo.getVehicleNumber() + "}的资料并审核通过后再发单");
                }
                if(null != tEndCarInfo.getVehicleNumber()){
                    TCarInsuranceVO carInsuranceVO = new TCarInsuranceVO();
                    carInsuranceVO.setVehicleNumber(tEndCarInfo.getVehicleNumber());
                    TCarInsuranceVO vo = carInsuranceService.selectByVehicleNumber(carInsuranceVO);
                    if(null != vo && null != vo.getAuditStatus() && DictEnum.PASSNODE.code.equals(vo.getAuditStatus())){
                        Date date = new Date();
                        Date insuranceDateEnd = vo.getInsuranceDateEnd();
                        if( null != insuranceDateEnd && date.getTime() > insuranceDateEnd.getTime()){
                            return ResultUtil.error("车辆{" + tEndCarInfo.getVehicleNumber() + "}的保险有效期已过期");
                        }
                    }
                }

                if(null!=carDriverRelVO.getCaptainId() && !"".equals(carDriverRelVO.getCaptainId())){
                    TEndUserInfo tEndUserInfo2 = tEndSUserInfoAPI.selectByPrimaryKey(carDriverRelVO.getCaptainId());
                    if(!DictEnum.PASSNODE.code.equals(tEndUserInfo2.getAuditStatus())){
                        return ResultUtil.error("请车队长先完成实名认证后再扫码抢单");
                    }
                }
            }

            String lineType = companySourceDTO.getLineType();
            LinkedHashMap deliverPrincipal = (LinkedHashMap) goodsSourceAndLineInfoData.get("deliverPrincipal");
            LineInfoPrincipalDTO deliver = objectMapper.convertValue(deliverPrincipal, LineInfoPrincipalDTO.class);
            if (null != deliver) {
                if (null == deliver.getPrincipalAccountId() || null == deliver.getPrincipalName() || null == deliver.getPrincipalPhone()) {
                    error.setMsg("当前货源无发单员，请先联系企业管理员或运营平台进行维护。");
                    ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                    exceptionSendOrder.setCount(1);
                    exceptionSendOrder.setIsLogic(false);
                    error.setData(exceptionSendOrder);
                    return error;
                }
            }
            if (null != receiver) {
                if (null == receiver.getPrincipalAccountId() || null == receiver.getPrincipalName() || null == receiver.getPrincipalPhone()) {
                    error.setMsg("当前货源无收单员，请先联系企业管理员或运营平台进行维护。");
                    ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                    exceptionSendOrder.setCount(1);
                    exceptionSendOrder.setIsLogic(false);
                    error.setData(exceptionSendOrder);
                    return error;
                }

            }
            // 查询承运方信息
            ResultUtil carrierResult = carrierService.selectById(String.valueOf(sendBillVO.getCarrierId()));
            LinkedHashMap carrier = (LinkedHashMap) carrierResult.getData();
            if (null != carrier.get("platformDid")) {
                companySourceDTO.setPlatformDid(String.valueOf(carrier.get("platformDid")));
            }

            //TODO 生成运单详情 TOrderInfo
            if (null != sendBillVO.getTemplateSend() && sendBillVO.getTemplateSend()) {
                sendBillVO.setOrderCreateType(DictEnum.TEMPLATESEND.code);
            } else {
                sendBillVO.setOrderCreateType(DictEnum.HANDSEND.code);
            }
            sendBillVO.setOrderExecuteStatus(DictEnum.M020.code);
            TOrderInfo orderInfo = createOrderInfo(companySourceDTO, receiver, sendBillVO);
            //资金转移方式
            String capitalTransferType = sendBillVO.getCapitalTransferType();
            ResultUtil resultUtil = ResultUtil.ok();
            HashMap<String, Object> result = new HashMap<>();

            String carKey = "CAR" + carDriverRelVO.getEndcarId();
            String userKey = "USER" + carDriverRelVO.getEndDriverId();
            boolean carLock = RedissLockUtil.tryLock(carKey, 5 * 5 * 60, 20);
            boolean userLock = RedissLockUtil.tryLock(userKey, 5 * 5 * 60, 20);

            try {
                IdWorkerUtil instance = IdWorkerUtil.getInstance();
                if (carLock && userLock) {
                    // 查询司机车辆发单数超过1单时，司机车辆审核状态
                    String checkDriverCarAuditStatusByTimes = sendOrderUtil.checkDriverCarAuditStatusByTimes(carDriverRelVO, false);
                    if (null != checkDriverCarAuditStatusByTimes) {
                        resultUtil.setMsg(checkDriverCarAuditStatusByTimes);
                        if (checkDriverCarAuditStatusByTimes.contains("实名认证")) {
                            resultUtil.setData(DictEnum.DRIVER.code);
                        } else {
                            resultUtil.setData(DictEnum.CAR.code);
                        }
                        resultUtil.setCode(CodeEnum.ERROR.getCode());
                    } else {
                        resultUtil = appOrderService.saveSendOrder(instance.nextId(), sendBillVO, carDriverRelVO, orderInfo, lineType, capitalTransferType, carrier, companySourceDTO);
                    }
                }
                result.put("vehicleNumber", carDriverRelVO.getVehicleNumber());
                resultUtil.setData(result);
            } catch (Exception e) {
                log.error("发单失败, {}", ThrowableUtil.getStackTrace(e));
                resultUtil.setCode("error");
                result.put("vehicleNumber", carDriverRelVO.getVehicleNumber());
                String message = e.getMessage();
                if (null != message && StringUtils.checkChineseCharacter(message)) {
                    ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                    exceptionSendOrder.setCount(1);
                    exceptionSendOrder.setIsLogic(false);
                    result.put("exceptionSendOrder", exceptionSendOrder);
                    resultUtil.setMsg(message);
                } else {
                    ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                    exceptionSendOrder.setCount(2);
                    exceptionSendOrder.setIsLogic(true);
                    result.put("exceptionSendOrder", exceptionSendOrder);
                    resultUtil.setMsg("ZJJ-001：发单失败!");
                }
                resultUtil.setData(result);
            } finally {
                if (carLock) {
                    RedissLockUtil.unlock(carKey);
                }
                if (userLock) {
                    RedissLockUtil.unlock(userKey);
                }
            }
            return resultUtil;
        } catch (Exception e) {
            log.error("ZJJ-001：发单失败!, {}", ThrowableUtil.getStackTrace(e));
            HashMap<String, Object> result = new HashMap<>();
            ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
            exceptionSendOrder.setCount(2);
            exceptionSendOrder.setIsLogic(true);
            result.put("exceptionSendOrder", exceptionSendOrder);
            result.put("vehicleNumber", vehicleNumber);
            ResultUtil error = ResultUtil.error();
            error.setData(result);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                error.setMsg(message);
                error.setData(result);
                return error;
            }
            return ResultUtil.error("ZJJ-001：发单失败!");
        }
    }

    /**
     * 批量导入收单
     *
     * @param receiveOrderList
     * @return
     * <AUTHOR>
     */
    @PostMapping(value = "/batchExcelReceiveOrder")
    public ResultUtil batchExcelReceiveOrder(@RequestBody List<BatchExcelReceiveOrder>  receiveOrderList) {
        List<Map> mapList = new ArrayList<>();
        try {
            Map map = new HashMap();
            if(receiveOrderList.size()>50){
                return ResultUtil.error("运单数量不超过50条");
            }
            if(receiveOrderList.size()>0){
                Integer companyId = null;
                for(BatchExcelReceiveOrder receiveOrder:receiveOrderList) {
                    log.info("批量接收运单，接收运单信息，{}", JSONUtil.toJsonStr(receiveOrder));
                    receiveOrder.setDeliverWeightNotesTime2(DateUtils.parseDateNew(receiveOrder.getDeliverWeightNotesTime()));
                    receiveOrder.setReceiveWeightNotesTime2(DateUtils.parseDateNew(receiveOrder.getReceiveWeightNotesTime()));
                    TOrderInfo tOrderInfo = orderInfoService.selectByRecieveOrder(receiveOrder);
                    log.info("批量接收运单，查询运单信息，{}", JSONUtil.toJsonStr(tOrderInfo));
                    if(null!=tOrderInfo && !"".equals(tOrderInfo)){
                        if(null == companyId || "".equals(companyId)){
                            companyId = tOrderInfo.getCompanyId();
                        }
                    }
                }
                if(null!=companyId && !"".equals(companyId)){
                    String companyKey = "PLDASD" + companyId;
                    RLock companyLock = redissonClient.getLock(companyKey);
                    try {
                        boolean tryCompanyLock = companyLock.tryLock();
                        if (tryCompanyLock) {
                            return appOrderService.batchExcelReceiveOrder(receiveOrderList);
                        } else {
                            return ResultUtil.error("企业正在操作中，请稍后再试");
                        }
                    }catch (Exception e){
                        log.error("批量导入收单失败! {}", e);
                        String message = e.getMessage();
                        if (StringUtils.checkChineseCharacter(message)) {
                            return ResultUtil.error(message);
                        }
                        return ResultUtil.error("批量导入收单败!");
                    }finally {
                        if (companyLock.isHeldByCurrentThread() && companyLock.isLocked()) {
                            companyLock.unlock();
                        }
                    }
                }else{
                    return ResultUtil.error("未匹配到运单，请检查表格");
                }
            }else{
                return ResultUtil.error("运单数量为0，请检查表格");
            }
        }catch (Exception e){
            log.error("批量导入收单失败! {}", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("批量导入收单败!");

        }
    }

    /**
     * 收单
     *
     * @param detailVO
     * @return
     * <AUTHOR>
     */
    @Encrypt
    @PostMapping(value = "/receiveOrder")
    public ResultUtil saveReceiveOrder(@Validated @RequestBody OrderDetailVO detailVO) {
        try {
            TOrderInfo orderInfo = orderInfoService.selectOrderInfoByCode(detailVO.getOrderCode());
            SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.LOADING_AND_DISCHARGE.code);
            if (null != paramByKey && paramByKey.getParamValue().equals("1")) {
                if (!DictEnum.M040.code.equals(orderInfo.getOrderExecuteStatus())) {
                    return ResultUtil.error("司机完成装、卸货签到后，才可进行收单操作！");
                }
            }
            if(null != detailVO.getCarriagePriceUnit() &&
                    detailVO.getCarriagePriceUnit().equals(DictEnum.CARRIAGE_PRICE_UNIT_BOX.code)){
                //对卸货重量做判断，如果不为空，则判断不能小于等于0
                //卸货磅单重量1
                if(null != detailVO.getReceiveWeightNotesWeight1()){
                    if(detailVO.getReceiveWeightNotesWeight1().compareTo(BigDecimal.ZERO) < 0){
                        return ResultUtil.error("保存失败，卸货重量不可小于零");
                    }else if(detailVO.getReceiveWeightNotesWeight1().compareTo(new BigDecimal("75")) >= 0){
                        return ResultUtil.error("卸货磅单重量需小于75，请重新填写");
                    }
                }
                //实收重量1
                if(null != detailVO.getDischargeWeight1()){
                    if(detailVO.getDischargeWeight1().compareTo(BigDecimal.ZERO) < 0){
                        return ResultUtil.error("保存失败，实收重量不可小于零");
                    }else if(detailVO.getDischargeWeight1().compareTo(new BigDecimal("75")) >= 0){
                        return ResultUtil.error("实收重量需小于75，请重新填写");
                    }
                }
                if(null != detailVO.getBoxNum() && detailVO.getBoxNum() == 2){
                    //卸货磅单重量2
                    if(null != detailVO.getReceiveWeightNotesWeight2()){
                        if(detailVO.getReceiveWeightNotesWeight2().compareTo(BigDecimal.ZERO) < 0){
                            return ResultUtil.error("保存失败，卸货重量不可小于零");
                        }else if(detailVO.getReceiveWeightNotesWeight2().compareTo(new BigDecimal("75")) >= 0){
                            return ResultUtil.error("卸货磅单重量需小于75，请重新填写");
                        }
                    }
                    if(null != detailVO.getReceiveWeightNotesWeight1() && null != detailVO.getReceiveWeightNotesWeight2()){
                        BigDecimal add = detailVO.getReceiveWeightNotesWeight1().add(detailVO.getReceiveWeightNotesWeight2());
                        if(add.compareTo(new BigDecimal("75")) >= 0){
                            return ResultUtil.error("总卸货磅单重量需小于75，请重新填写");
                        }
                    }
                    //实收重量2
                    if(null != detailVO.getDischargeWeight2()){
                        if(detailVO.getDischargeWeight2().compareTo(BigDecimal.ZERO) < 0){
                            return ResultUtil.error("保存失败，实收重量不可小于零");
                        }else if(detailVO.getDischargeWeight2().compareTo(new BigDecimal("75")) >= 0){
                            return ResultUtil.error("实收重量需小于75，请重新填写");
                        }
                    }
                    if(null != detailVO.getDischargeWeight1() && null != detailVO.getDischargeWeight2()){
                        BigDecimal add = detailVO.getDischargeWeight1().add(detailVO.getDischargeWeight2());
                        if(add.compareTo(new BigDecimal("75")) >= 0){
                            return ResultUtil.error("总实收重量需小于75，请重新填写");
                        }
                    }
                }
            }else{
                if(null != detailVO.getReceiveWeightNotesWeight()){
                    if(detailVO.getReceiveWeightNotesWeight() < 0){
                        return ResultUtil.error("保存失败，收货磅单重量不可小于零");
                    }else if(detailVO.getReceiveWeightNotesWeight() >= 75){
                        return ResultUtil.error("收货磅单重量需小于75，请重新填写");
                    }
                }
                if(null != detailVO.getRealDispathWeight()){
                    if(detailVO.getRealDispathWeight() < 0){
                        return ResultUtil.error("保存失败，实收重量不可小于零");
                    }else if(detailVO.getRealDispathWeight() >= 75){
                        return ResultUtil.error("实收重量需小于75，请重新填写");
                    }
                }
            }
            if (null != detailVO.getUserConfirmCarriagePayment() && detailVO.getUserConfirmCarriagePayment().compareTo(BigDecimal.ZERO) <= 0) {
                return ResultUtil.error("运费不合法，无法收单");
            }
            TOrderInsurance tOrderInsurance = orderInsuranceService.selectByOrderBusinessCode(orderInfo.getOrderBusinessCode());
            if (null != tOrderInsurance) {
                log.info("投保信息：{}", JSONUtil.toJsonStr(tOrderInsurance));
                if (OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance)) {
                    if (detailVO.getUserConfirmCarriagePayment().compareTo(tOrderInsurance.getInsuredAmount()) <= 0) {
                        return ResultUtil.error("运费不合法，无法收单。错误码：IIA");
                    }
                }
            }
            if(DictEnum.CARRIAGE_PRICE_UNIT_DUN.code.equals(detailVO.getCarriagePriceUnit())){
                BigDecimal carriagePayment = detailVO.getCarriagePayment();
                BigDecimal add_10 = carriagePayment.add(carriagePayment.multiply(new BigDecimal(DictEnum.FREIGHT_DIFFERENCES_LIMIT.code)));
                if (detailVO.getUserConfirmCarriagePayment().compareTo(add_10) > 0 ) {
                    return ResultUtil.error("确认应付运费与规则应付运费差额不可超过10%。");
                }
            }
            TOrderPayRule tOrderPayRule = new TOrderPayRule();
            tOrderPayRule.setOrderCode(detailVO.getOrderCode());
            List<String> payNodeTypes = Arrays.asList(DictEnum.ZHPAYNODE.code, DictEnum.XHPAYNODE.code);
            tOrderPayRule.setPayNodeTypes(payNodeTypes);
            List<TOrderPayRule> tOrderPayRules = tOrderPayRuleService.selectByOrderCodeAndTypes(tOrderPayRule);
            BigDecimal reduce = tOrderPayRules.stream().map(TOrderPayRule::getPaymentFee).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
            if (detailVO.getUserConfirmCarriagePayment().compareTo(reduce)<=0){
                return ResultUtil.error("收单失败！确认应付运费需大于已支付运费金额");
            }
            //查询t_order_info_detail，获取预计行驶时间
            TOrderInfoDetail orderInfoDetail = orderInfoDetailService.selectByOrderId(orderInfo.getId());
            //点击收单时，校验当前时间是否大于（建单时间+预计行驶时间），若小于，不允许收单，
            // 提示：未到达收单时间，请于xx月xx日 xx：xx（时分）后再进行收单操作。
            Double estimatedTravelTime = orderInfoDetail.getEstimatedTravelTime();//预计行驶时间
            if(null == estimatedTravelTime || estimatedTravelTime == 0d){
                //如果预计行驶时间是空的，则根据起点终点坐标得出预计行驶时间
                String fromCoordinates = orderInfo.getFromCoordinates();//起点坐标
                String endCoordinates = orderInfo.getEndCoordinates();//终点坐标
                if(null != fromCoordinates && null != endCoordinates){
                    Map<String, Double> map = AddressLocationUtil.getDistanceAndDuration(fromCoordinates, endCoordinates);
                    estimatedTravelTime = map.get("estimatedTravelTime");
                }
            }
            detailVO.setEstimatedTravelTime(estimatedTravelTime);
            TOrderCastChanges castChanges = orderCastChangesService.selectByNewOne(orderInfo.getCode());
            if (null != castChanges.getCapitalTransferPattern()) {
                detailVO.setCapitalTransferPattern(castChanges.getCapitalTransferPattern());
            }

            String capitalTransferPattern = null == castChanges.getCapitalTransferPattern() ? "" : castChanges.getCapitalTransferPattern();
            // 1. 判断车辆和司机是否审核通过：根据线路货物关系表中
            LineGoodsRelInfo lineGoodsRelInfo = linkGoodsRelAPI.selectLineGoodsRelInfo(new TGoodsSourceInfo().setLineGoodsRelId(orderInfo.getLineGoodsRelId()));
            if (null != lineGoodsRelInfo && null != lineGoodsRelInfo.getTransportIdentityCheck()
                    && !lineGoodsRelInfo.getTransportIdentityCheck()) {
                String checkUserCarStatus = sendOrderUtil.receiveOrderCheckUserCarStatus(orderInfo,
                        castChanges.getCapitalTransferType(), capitalTransferPattern);
                if (StringUtils.isNotBlank(checkUserCarStatus)) {
                    return ResultUtil.error(checkUserCarStatus);
                }
            }

            String orderKey = "Order" + detailVO.getOrderCode();

            // 货源大厅运单，添加货源锁
            String resourceKey = GOODS_SOURCE;
            RLock resourceLock = null;
            boolean tryResourceLOck = true;
            if (DictEnum.RESOURCEHALLSEND.code.equals(orderInfo.getOrderCreateType())) {
                TGoodsSourceInfo tGoodsSourceInfo = orderGoodsSourceInfoService.selectByByLineGoodsRelId(orderInfo.getLineGoodsRelId());
                resourceKey = resourceKey + tGoodsSourceInfo.getId();
                resourceLock = redissonClient.getLock(resourceKey);
                tryResourceLOck = resourceLock.tryLock();
            }

            RLock orderLock = redissonClient.getLock(orderKey);
            try {
                boolean tryOrderLock = orderLock.tryLock();
                if (tryOrderLock && tryResourceLOck) {
                    orderInfo = orderInfoService.selectOrderInfoByCode(detailVO.getOrderCode());
                    if (orderInfo.getPackStatus().equals("1")) {
                        ResultUtil resultUtil = ResultUtil.error();
                        resultUtil.setCode("error");
                        resultUtil.setData(orderInfo.getOrderBusinessCode());
                        resultUtil.setMsg("打包原始运单不可在此收单");
                        return resultUtil;
                    }
                    if (DictEnum.RESOURCEHALLSEND.code.equals(orderInfo.getOrderCreateType())) {
                        if (DictEnum.M040.code.equals(orderInfo.getOrderExecuteStatus())) {
                            return appOrderService.recieveOrder(detailVO);
                        } else {
                            return ResultUtil.error("请联系司机进行装、卸货签到后，再进行收单操作");
                        }
                    }
                    ResultUtil resultUtil = appOrderService.recieveOrder(detailVO);
                    return resultUtil;
                } else {
                    return ResultUtil.error("企业正在操作中，请稍后再试");
                }
            } catch (Exception e) {
                log.error("ZJJ-002:收单失败!, {}", ThrowableUtil.getStackTrace(e));
                String message = e.getMessage();
                if (StringUtils.checkChineseCharacter(message)) {
                    return ResultUtil.error(message);
                }
                return ResultUtil.error("ZJJ-002:收单失败!");
            } finally {
                if (orderLock.isHeldByCurrentThread() && orderLock.isLocked()) {
                    orderLock.unlock();
                }
                if (null != resourceLock && resourceLock.isLocked() && resourceLock.isHeldByCurrentThread()) {
                    resourceLock.unlock();
                }
            }
        } catch (Exception e) {
            log.error("ZJJ-002:收单失败! {}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("收单失败!");

        }
    }


    /**
     * 收单加密
     *
     * @param detailVO
     * @return
     * <AUTHOR>
     */
    @Encrypt
    @PostMapping(value = "/receiveOrderEncrypt")
    public ResultUtil saveReceiveOrderEncrypt(@Validated @RequestBody OrderDetailVO detailVO) {
        try {
            if (detailVO.getOrderCode() == null) {
                return ResultUtil.error("H5");
            } else {
                return ResultUtil.ok("H5");
            }
        } catch (Exception e) {
            log.error("ZJJ-002:收单失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("ZJJ-002:收单失败!");

        }
    }


    /**
     * 重新收单
     *
     * @param detailVO
     * @return
     * <AUTHOR>
     */
    @Encrypt
    @PostMapping(value = "/reReceiveOrder")
    public ResultUtil saveReReceiveOrder(@Validated @RequestBody OrderDetailVO detailVO) {
        try {
            if(DictEnum.CARRIAGE_PRICE_UNIT_DUN.code.equals(detailVO.getCarriagePriceUnit())){
                BigDecimal carriagePayment = detailVO.getCarriagePayment();
                BigDecimal add_10 = carriagePayment.add(carriagePayment.multiply(new BigDecimal(DictEnum.FREIGHT_DIFFERENCES_LIMIT.code)));
                if (detailVO.getUserConfirmCarriagePayment().compareTo(add_10) > 0) {
                    return ResultUtil.error("确认应付运费与规则应付运费差额不可超过10%。");
                }
            }
            TOrderPayRule tOrderPayRule = new TOrderPayRule();
            tOrderPayRule.setOrderCode(detailVO.getOrderCode());
            List<String> payNodeTypes = Arrays.asList(DictEnum.ZHPAYNODE.code, DictEnum.XHPAYNODE.code);
            tOrderPayRule.setPayNodeTypes(payNodeTypes);
            List<TOrderPayRule> tOrderPayRules = tOrderPayRuleService.selectByOrderCodeAndTypes(tOrderPayRule);
            BigDecimal reduce = tOrderPayRules.stream().map(TOrderPayRule::getPaymentFee).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
            if (detailVO.getUserConfirmCarriagePayment().compareTo(reduce)<=0){
                return ResultUtil.error("收单失败！确认应付运费需大于已支付运费金额");
            }
            TOrderInfo orderInfo = orderInfoService.selectOrderInfoByCode(detailVO.getOrderCode());
            TOrderCastChanges castChanges = orderCastChangesService.selectByNewOne(orderInfo.getCode());

            String capitalTransferPattern = null == castChanges.getCapitalTransferPattern() ? "" : castChanges.getCapitalTransferPattern();
            // 1. 判断车辆和司机是否审核通过：根据线路货物关系表中
            LineGoodsRelInfo lineGoodsRelInfo = linkGoodsRelAPI.selectLineGoodsRelInfo(new TGoodsSourceInfo().setLineGoodsRelId(orderInfo.getLineGoodsRelId()));
            if (null != lineGoodsRelInfo && null != lineGoodsRelInfo.getTransportIdentityCheck()
                    && !lineGoodsRelInfo.getTransportIdentityCheck()) {
                String checkUserCarStatus = sendOrderUtil.receiveOrderCheckUserCarStatus(orderInfo,
                        castChanges.getCapitalTransferType(), capitalTransferPattern);
                if (StringUtils.isNotBlank(checkUserCarStatus)) {
                    return ResultUtil.error(checkUserCarStatus);
                }
            }
            TOrderInsurance tOrderInsurance = orderInsuranceService.selectByOrderBusinessCode(orderInfo.getOrderBusinessCode());
            TOrderInfoDetail orderInfoDetail = orderInfoDetailService.selectByOrderId(orderInfo.getId());
            // 运费判断
            // 重新收单，退保临时改为不退保
            if (null != tOrderInsurance) {
                tOrderInsurance.setInsuranceCancellation(false);
            }
            ResultUtil carriageResult = OrderDeductionUtil.validateAmountAndReturnResult(detailVO.getUserConfirmCarriagePayment(), detailVO.getUserConfirmServiceFee(), castChanges.getCapitalTransferPattern(), tOrderInsurance, orderInfoDetail);
            if (!Objects.equals(carriageResult.getCode(), CodeEnum.SUCCESS.getCode())) {
                return carriageResult;
            }

            // 货源大厅运单，添加货源锁
            String resourceKey = GOODS_SOURCE;
            RLock resourceLock = null;
            boolean tryResourceLock = true;
            if (DictEnum.RESOURCEHALLSEND.code.equals(orderInfo.getOrderCreateType())) {
                TGoodsSourceInfo tGoodsSourceInfo = orderGoodsSourceInfoService.selectByByLineGoodsRelId(orderInfo.getLineGoodsRelId());
                resourceKey = resourceKey + tGoodsSourceInfo.getId();
                resourceLock = redissonClient.getLock(resourceKey);
                tryResourceLock = resourceLock.tryLock();
            }

            String orderKey = "Order" + detailVO.getOrderCode();

            RLock orderLock = redissonClient.getLock(orderKey);
            try {
                boolean tryOrderLock = orderLock.tryLock();
                if (tryOrderLock && tryResourceLock) {
                    orderInfo = orderInfoService.selectOrderInfoByCode(detailVO.getOrderCode());
                    if (orderInfo.getPackStatus().equals("1")) {
                        ResultUtil resultUtil = ResultUtil.error();
                        resultUtil.setCode("error");
                        resultUtil.setData(orderInfo.getOrderBusinessCode());
                        resultUtil.setMsg("打包原始运单不可在此收单");
                        return resultUtil;
                    }
                    ResultUtil resultUtil = appOrderService.reRecieveOrder(detailVO);
                    return resultUtil;
                } else {
                    return ResultUtil.error("企业正在操作中，请稍后再试");
                }
            } catch (Exception e) {
                log.error("ZJJ-003:重新收单!", e);
                String message = e.getMessage();
                if (StringUtils.checkChineseCharacter(message)) {
                    return ResultUtil.error(message);
                }
                return ResultUtil.error("ZJJ-003:重新收单!");
            } finally {
                if (orderLock.isLocked() && orderLock.isHeldByCurrentThread()) {
                    orderLock.unlock();
                }
                if (null != resourceLock && resourceLock.isLocked() && resourceLock.isHeldByCurrentThread()) {
                    resourceLock.unlock();
                }
            }
        } catch (Exception e) {
            log.error("ZJJ-003:重新收单!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("ZJJ-003:重新收单!");
        }
    }

    /**
     * APP支付
     *
     * @return
     * <AUTHOR>
     */
    @PostMapping(value = "/payOrder")
    public ResultUtil savePayOrder(@RequestBody SendOrderVO record) {
        try {
            ResultUtil resultUtil = ResultUtil.ok();
            String orderCode = record.getCode();
            if (StringUtils.isNotEmpty(orderCode)) {
                TOrderInfo orderInfoByCoded = orderInfoService.selectOrderInfoByCode(orderCode);
                TOrderCastChanges castChanges = orderCastChangesService.selectByNewOne(orderInfoByCoded.getCode());
                String orderKey = "Order" + orderCode;
                String walletKey = "Wallet" + castChanges.getCompanyWalletId();
                boolean orderLock = false;
                boolean walletLock = false;
                if (!redisUtil.hasKey(orderKey)) {
                    orderLock = redisUtil.set(orderKey, "lock");
                }
                if (!redisUtil.hasKey(walletKey)) {
                    walletLock = redisUtil.set(walletKey, "lock");
                }
                try {
                    if (!orderLock) {
                        return ResultUtil.error("该运单正在进行其他操作，请核对后在进行支付");
                    }
                    if (!walletLock) {
                        return ResultUtil.error("企业正在操作中，请稍后再试");
                    }
                    if (orderLock && walletLock) {
                        // 添加运单锁，防止工具并发，30s自动解锁，只锁支付
                        if (!redisUtil.hasKey("pay" + orderKey)) {
                            redisUtil.set("pay" + orderKey, "pay", 30);
                        } else {
                            return ResultUtil.error("该运单正在进行其他操作，请核对后在进行支付");
                        }

                        TOrderInfo orderInfoByCode = orderInfoService.selectOrderInfoByCode(orderCode);
                        resultUtil = sendOrderUtil.checkOrderPay(orderInfoByCoded, record);
                        if (null == resultUtil) {
                            TOrderInfoVO orderInfoVO = new TOrderInfoVO();
                            orderInfoVO.setId(orderInfoByCode.getId());
                            orderInfoVO.setCode(orderInfoByCode.getCode());
                            orderInfoVO.setNewUserConfirmCarriagePayment(record.getUserConfirmCarriagePayment());
                            orderInfoVO.setServiceFee(null != record.getServiceFee() && record.getServiceFee()
                                    .compareTo(BigDecimal.ZERO) > 0 ? record.getServiceFee() : BigDecimal.ZERO);
                            orderInfoVO.setUserConfirmServiceFee(null != record.getUserConfirmServiceFee() && record.getUserConfirmServiceFee()
                                    .compareTo(BigDecimal.ZERO) > 0 ? record.getUserConfirmServiceFee() : BigDecimal.ZERO);
                            if (null != record.getAdvancePayment() && record.getAdvancePayment().equals(3)) {
                                orderInfoVO.setAdvancePayment(true);
                            } else {
                                orderInfoVO.setAdvancePayment(false);
                            }
                            if (null != record.getBankId()) {
                                orderInfoVO.setBankId(record.getBankId());
                            }
                            resultUtil = appOrderService.payOrder(orderInfoVO);
                        } else {
                            ResultUtil error = ResultUtil.error();
                            error.setMsg(resultUtil.getMsg());
                            ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                            exceptionSendOrder.setCount(1);
                            exceptionSendOrder.setIsLogic(false);
                            HashMap<String, Object> result = new HashMap<>();
                            result.put("exceptionSendOrder", exceptionSendOrder);
                            error.setData(result);
                            return error;
                        }
                    } else {
                        return ResultUtil.error("企业正在操作中，请稍后再试");
                    }
                } catch (Exception e) {
                    log.error("错误码：ZJJZ001，请联系运营平台予以解决。", e);
                    String message = e.getMessage();
                    if (StringUtils.checkChineseCharacter(message)) {
                        ResultUtil error = ResultUtil.error();
                        error.setMsg(message);
                        ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                        exceptionSendOrder.setCount(1);
                        exceptionSendOrder.setIsLogic(false);
                        HashMap<String, Object> result = new HashMap<>();
                        result.put("exceptionSendOrder", exceptionSendOrder);
                        error.setData(result);
                        return error;
                    } else {
                        ResultUtil error = ResultUtil.error();
                        ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                        exceptionSendOrder.setCount(2);
                        exceptionSendOrder.setIsLogic(true);
                        HashMap<String, Object> result = new HashMap<>();
                        result.put("exceptionSendOrder", exceptionSendOrder);
                        error.setMsg("错误码：ZJJZ001，请联系运营平台予以解决。");
                        error.setData(result);
                        return error;
                    }
                } finally {
                    if (orderLock && redisUtil.hasKey(orderKey)) {
                        redisUtil.del(orderKey);
                    }
                    if (walletLock && redisUtil.hasKey(walletKey)) {
                        redisUtil.del(walletKey);
                    }
                }
            }
            return resultUtil;
        } catch (Exception e) {
            log.error("ZJJ004:APP支付失败", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("ZJJ004:APP支付失败");
        }
    }

    /**
     * PC单笔支付
     *
     * @return
     * @Date 2019/12/10 13:01
     * <AUTHOR> 临时添加，解决PC支付解密报错
     */
    @PostMapping(value = "/payOrderPC")
    public ResultUtil payOrderPC(@RequestBody SendOrderVO record) {
        try {
            ResultUtil resultUtil = ResultUtil.ok();
            String orderCode = record.getCode();
            if (StringUtils.isNotEmpty(orderCode)) {
                if (null != record.getUserConfirmCarriagePayment() && !(record.getUserConfirmCarriagePayment().compareTo(BigDecimal.ZERO) > 0)) {
                    return ResultUtil.error("确认应付运费应当大于0");
                }
                if (null != record.getOperateMethod() && DictEnum.PC.code.equals(record.getOperateMethod())) {
                    if (null != record.getWithdrawType() && DictEnum.AUTOMATION.code.equals(record.getWithdrawType())) {
                        if (null == record.getBankId()) {
                            return ResultUtil.error("当前提现方式是自动到卡，请选择银行卡");
                        }
                    }
                }
                TOrderInfo orderInfo = orderInfoService.selectOrderInfoByCode(orderCode);
                if (DictEnum.PACKPAY.code.equals(orderInfo.getPayMethod())) {
                    return ResultUtil.error("该运单为打包支付单，不能进行单笔支付");
                }
                TOrderCastChanges castChanges = orderCastChangesService.selectByNewOne(orderInfo.getCode());
                String orderKey = "Order" + orderCode;
                String walletKey = "Wallet" + castChanges.getCompanyWalletId();
                boolean orderLock = false;
                boolean walletLock = false;
                if (!redisUtil.hasKey(orderKey)) {
                    orderLock = redisUtil.set(orderKey, "lock");
                }
                if (!redisUtil.hasKey(walletKey)) {
                    walletLock = redisUtil.set(walletKey, "lock");
                }
                try {
                    if (!orderLock) {
                        return ResultUtil.error("该运单正在进行其他操作，请核对后在进行支付");
                    }
                    if (!walletLock) {
                        return ResultUtil.error("企业正在操作中，请稍后再试");
                    }
                    // 添加运单锁，防止工具并发，30s自动解锁，只锁支付
                    if (!redisUtil.hasKey("pay" + orderKey)) {
                        redisUtil.set("pay" + orderKey, "pay", 30);
                    } else {
                        return ResultUtil.error("该运单正在进行其他操作，请核对后在进行支付");
                    }
                    orderInfo = orderInfoService.selectOrderInfoByCode(orderCode);
                    resultUtil = sendOrderUtil.checkOrderPay(orderInfo, record);
                    if (null == resultUtil) {
                        TOrderInfoVO orderInfoVO = new TOrderInfoVO();
                        orderInfoVO.setId(orderInfo.getId());
                        orderInfoVO.setCode(orderInfo.getCode());
                        orderInfoVO.setNewUserConfirmCarriagePayment(record.getUserConfirmCarriagePayment());
                        orderInfoVO.setServiceFee(null != record.getServiceFee() && record.getServiceFee()
                                .compareTo(BigDecimal.ZERO) > 0 ? record.getServiceFee() : BigDecimal.ZERO);
                        orderInfoVO.setUserConfirmServiceFee(null != record.getUserConfirmServiceFee() && record.getUserConfirmServiceFee()
                                .compareTo(BigDecimal.ZERO) > 0 ? record.getUserConfirmServiceFee() : BigDecimal.ZERO);
                        if (null != record.getAdvancePayment() && record.getAdvancePayment().equals(3)) {
                            orderInfoVO.setAdvancePayment(true);
                        } else {
                            orderInfoVO.setAdvancePayment(false);
                        }
                        if (null != record.getBankId()) {
                            orderInfoVO.setBankId(record.getBankId());
                        }
                        resultUtil = appOrderService.payOrder(orderInfoVO);
                    } else {
                        return resultUtil;
                    }
                } catch (Exception e) {
                    log.error("错误码：ZJJZ001，请联系运营平台予以解决。", e);
                    String message = e.getMessage();
                    if (StringUtils.checkChineseCharacter(message)) {
                        ResultUtil error = ResultUtil.error();
                        error.setMsg(message);
                        ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                        if (message.equals(BusinessCode.COMPANYWALLETINSUFFICIENT.code)) {
                            exceptionSendOrder.setCount(1);
                            exceptionSendOrder.setIsLogic(false);
                        } else {
                            exceptionSendOrder.setCount(2);
                            exceptionSendOrder.setIsLogic(true);
                        }
                        HashMap<String, Object> result = new HashMap<>();
                        result.put("exceptionSendOrder", exceptionSendOrder);
                        error.setData(result);
                        return error;
                    } else {
                        ResultUtil error = ResultUtil.error();
                        ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                        exceptionSendOrder.setCount(2);
                        exceptionSendOrder.setIsLogic(true);
                        HashMap<String, Object> result = new HashMap<>();
                        result.put("exceptionSendOrder", exceptionSendOrder);
                        error.setMsg("错误码：ZJJZ001，请联系运营平台予以解决。");
                        error.setData(result);
                        return error;
                    }
                } finally {
                    if (orderLock && redisUtil.hasKey(orderKey)) {
                        redisUtil.del(orderKey);
                        log.info("释放运单锁, {}", orderKey);
                    }
                    if (walletLock && redisUtil.hasKey(walletKey)) {
                        redisUtil.del(walletKey);
                        log.info("释放企业钱包锁, {}", walletKey);
                    }
                }
            }
            resultUtil.setMsg(DictEnum.PAYSUCCESS.code);
            return resultUtil;
        } catch (Exception e) {
            log.error("ZJJ-004:APP支付失败", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("ZJJ-004:APP支付失败");
        }
    }

    /**
     * 批量PC支付
     *
     * @return
     * <AUTHOR>
     */
    @PostMapping(value = "/payPCOrder")
    public ResultUtil savePCPayOrder(@RequestBody SendOrderVO record) {
        if (null != record.getCodes() && record.getCodes().length > 0) {
            // 支付前检测运单信息
            /*List<ResultUtil> resultUtilList = sendOrderUtil.preCheckOrderPay(sendOrderVO.getCodes());
            if (!resultUtilList.isEmpty()) {
                ResultUtil resultUtil = ResultUtil.error();
                resultUtil.setData(resultUtilList);
                return resultUtil;
            }*/
            String[] codes = record.getCodes();
            String code = codes[0];
            TOrderInfo orderInfo = orderInfoService.selectOrderInfoByCode(code);
            TOrderCastChanges castChanges = orderCastChangesService.selectByNewOne(orderInfo.getCode());
            String walletKey = "Wallet" + castChanges.getCompanyWalletId();
            boolean walletLock = false;
            if (!redisUtil.hasKey(walletKey)) {
                walletLock = redisUtil.set(walletKey, "lock");
            }
            log.info("获取企业钱包锁，{}, {}", walletKey, walletLock);
            List<ResultUtil> resultUtils = new ArrayList<>();
            try {
                if (walletLock) {
                    for (String orderCode : codes) {
                        String orderBusinessCode = "";
                        try {
                            if (orderCode != null && StringUtil.isNotEmpty(orderCode)) {
                                ResultUtil resultUtil = ResultUtil.ok();
                                TOrderInfo orderInfoByCoded = orderInfoService.selectOrderInfoByCode(orderCode);
                                if (DictEnum.PACKPAY.code.equals(orderInfoByCoded.getPayMethod())) {
                                    return ResultUtil.error(orderInfoByCoded.getOrderBusinessCode() + ":该运单为打包支付单，不能进行单笔支付");
                                }
                                orderBusinessCode = orderInfoByCoded.getOrderBusinessCode();
                                resultUtil = sendOrderUtil.checkOrderPay(orderInfoByCoded, record);
                                if (null == resultUtil) {
                                    TOrderInfoVO orderInfoVO = new TOrderInfoVO();
                                    orderInfoVO.setId(orderInfoByCoded.getId());
                                    orderInfoVO.setCode(orderInfoByCoded.getCode());
                                    if (null != record.getAdvancePayment() && record.getAdvancePayment().equals(3)) {
                                        orderInfoVO.setAdvancePayment(true);
                                    } else {
                                        orderInfoVO.setAdvancePayment(false);
                                    }
                                    ResultUtil payResult = appOrderService.payOrder(orderInfoVO);
                                    payResult.setData(orderInfoByCoded.getOrderBusinessCode());
                                    resultUtils.add(payResult);
                                } else {
                                    resultUtils.add(resultUtil);
                                }
                            }
                        } catch (Exception e) {
                            log.error("ZJJ-005:PC支付失败!", e);
                            String message = e.getMessage();
                            ResultUtil resultUtil = ResultUtil.error();
                            resultUtil.setData(orderBusinessCode);
                            if (StringUtils.checkChineseCharacter(message)) {
                                resultUtil.setMsg(message);
                                resultUtils.add(resultUtil);
                            } else {
                                resultUtil.setMsg("ZJJ-005:PC支付失败!");
                            }
                            resultUtils.add(resultUtil);
                        }
                    }
                } else {
                    List<String> orderCodes;
                    if (null != record.getCodes() && record.getCodes().length > 0) {
                        orderCodes = Arrays.asList(record.getCodes());
                        List<String> orderBusinessCodeByCode = orderInfoService.selectOrderBusinessCodeByCode(orderCodes);
                        for (String orderBusiness : orderBusinessCodeByCode) {
                            ResultUtil resultUtil = ResultUtil.error("企业正在操作中，请稍后再试");
                            resultUtil.setData(orderBusiness);
                            resultUtils.add(resultUtil);
                        }
                    }
                    ResultUtil error = ResultUtil.error();
                    error.setData(resultUtils);
                    return error;
                }
            } catch (Exception e) {
                log.error("ZJJ-005:PC支付失败!", e);
                ResultUtil resultUtil = ResultUtil.error();
                String message = e.getMessage();
                if (StringUtils.checkChineseCharacter(message)) {
                    resultUtil.setMsg(message);
                    return resultUtil;
                } else {
                    resultUtil.setMsg("ZJJ-005:PC支付失败!");
                    return resultUtil;
                }
            } finally {
                /**
                 * ly
                 * 11月12日修改加解锁方式
                 */
                if (walletLock && redisUtil.hasKey(walletKey)) {
                    redisUtil.del(walletKey);
                    log.info("释放企业钱包锁, {}", walletKey);
                }
            }

            return ResultUtil.ok(resultUtils);
        } else {
            return ResultUtil.error();
        }
    }

    /**
     * @return
     * <AUTHOR>
     * @Description 1.0 分润支付
     * @Date 2020/6/8 11:06 上午
     * @Param
     **/
    @PostMapping(value = "/royaltyPayOrder")
    public ResultUtil royaltyPayOrder(@RequestBody SendOrderVO record) {
        if (null != record.getCodes() && record.getCodes().length > 0) {
            String[] codes = record.getCodes();
            String code = codes[0];
            TOrderInfo orderInfo = orderInfoService.selectOrderInfoByCode(code);
            TOrderCastChanges castChanges = orderCastChangesService.selectByNewOne(orderInfo.getCode());
            String walletKey = "Wallet" + castChanges.getCompanyWalletId();
            boolean walletLock = false;
            if (!redisUtil.hasKey(walletKey)) {
                walletLock = redisUtil.set(walletKey, "lock");
            }
            log.info("获取企业钱包锁，{}, {}", walletKey, walletLock);
            List<ResultUtil> resultUtils = new ArrayList<>();
            try {
                if (walletLock) {
                    for (String orderCode : codes) {
                        String orderBusinessCode = "";
                        try {
                            if (orderCode != null && StringUtil.isNotEmpty(orderCode)) {
                                ResultUtil resultUtil = ResultUtil.ok();
                                TOrderInfo orderInfoByCoded = orderInfoService.selectOrderInfoByCode(orderCode);
                                if (DictEnum.PACKPAY.code.equals(orderInfoByCoded.getPayMethod())) {
                                    return ResultUtil.error(orderInfoByCoded.getOrderBusinessCode() + ":该运单为打包支付单，不能进行单笔支付");
                                }
                                orderBusinessCode = orderInfoByCoded.getOrderBusinessCode();
                                resultUtil = sendOrderUtil.checkOrderPay(orderInfoByCoded, record);
                                if (null == resultUtil) {
                                    TOrderInfoVO orderInfoVO = new TOrderInfoVO();
                                    orderInfoVO.setId(orderInfoByCoded.getId());
                                    orderInfoVO.setCode(orderInfoByCoded.getCode());
                                    if (null != record.getCode() && !"".equals(record.getCode())) {//如果不为空时详情支付与app支付
                                        if (null != record.getUserConfirmCarriagePayment() && !"".equals(record.getUserConfirmCarriagePayment())) {
                                            orderInfoVO.setNewUserConfirmCarriagePayment(record.getUserConfirmCarriagePayment());
                                        }
                                        if (null != record.getServiceFee() && !"".equals(record.getServiceFee())) {
                                            orderInfoVO.setServiceFee(null != record.getServiceFee() && record.getServiceFee()
                                                    .compareTo(BigDecimal.ZERO) > 0 ? record.getServiceFee() : BigDecimal.ZERO);
                                        }
                                        if (null != record.getUserConfirmServiceFee() && !"".equals(record.getUserConfirmServiceFee())) {
                                            orderInfoVO.setUserConfirmServiceFee(null != record.getUserConfirmServiceFee() && record.getUserConfirmServiceFee()
                                                    .compareTo(BigDecimal.ZERO) > 0 ? record.getUserConfirmServiceFee() : BigDecimal.ZERO);
                                        }
                                    }
                                    if (null != record.getBankId()) {
                                        orderInfoVO.setBankId(record.getBankId());
                                    }
                                    if (null != record.getAdvancePayment() && record.getAdvancePayment().equals(3)) {
                                        orderInfoVO.setAdvancePayment(true);
                                    } else {
                                        orderInfoVO.setAdvancePayment(false);
                                    }
                                    // 查询资金转移模式，如果为空，则为旧单子，不使用分润模式
                                    castChanges = orderCastChangesService.selectByNewOne(orderCode);
                                    if (null != castChanges.getCapitalTransferPattern()
                                            && StringUtils.isNotBlank(castChanges.getCapitalTransferPattern())) {
                                        orderInfoVO.setRoyaltPayment(true);
                                    }
                                    // 判断是不是预支付运单, 预付款运单(尾款)不使用分润支付
                                    TAdvanceOrderTmp tAdvanceOrderTmp = advanceOrderTmpService.selectAdvanceOrderTempByOrderCode(orderCode);
                                    if (null != tAdvanceOrderTmp) {
                                        orderInfoVO.setRoyaltPayment(false);
                                    }
                                    ResultUtil payResult = appOrderService.payOrder(orderInfoVO);
                                    payResult.setData(orderInfoByCoded.getOrderBusinessCode());
                                    resultUtils.add(payResult);
                                } else {
                                    resultUtils.add(resultUtil);
                                }
                            }
                        } catch (Exception e) {
                            log.error("ZJJ-005:PC支付失败!", e);
                            String message = e.getMessage();
                            ResultUtil resultUtil = ResultUtil.error();
                            resultUtil.setData(orderBusinessCode);
                            if (StringUtils.checkChineseCharacter(message)) {
                                resultUtil.setMsg(message);
                            } else {
                                resultUtil.setMsg("ZJJ-005:PC支付失败!");
                            }
                            resultUtils.add(resultUtil);
                        }
                    }
                } else {
                    List<String> orderCodes;
                    if (null != record.getCodes() && record.getCodes().length > 0) {
                        orderCodes = Arrays.asList(record.getCodes());
                        List<String> orderBusinessCodeByCode = orderInfoService.selectOrderBusinessCodeByCode(orderCodes);
                        for (String orderBusiness : orderBusinessCodeByCode) {
                            ResultUtil resultUtil = ResultUtil.error("企业正在操作中，请稍后再试");
                            resultUtil.setData(orderBusiness);
                            resultUtils.add(resultUtil);
                        }
                    }
                    ResultUtil error = ResultUtil.error();
                    error.setData(resultUtils);
                    return error;
                }
            } catch (Exception e) {
                log.error("ZJJ-005:PC支付失败!", e);
                ResultUtil resultUtil = ResultUtil.error();
                String message = e.getMessage();
                if (StringUtils.checkChineseCharacter(message)) {
                    resultUtil.setMsg(message);
                    return resultUtil;
                } else {
                    resultUtil.setMsg("ZJJ-005:PC支付失败!");
                    return resultUtil;
                }
            } finally {
                /**
                 * ly
                 * 11月12日修改加解锁方式
                 */
                if (walletLock && redisUtil.hasKey(walletKey)) {
                    redisUtil.del(walletKey);
                    log.info("释放企业钱包锁, {}", walletKey);
                }
            }

            return ResultUtil.ok(resultUtils);
        } else {
            return ResultUtil.error();
        }
    }

    /*
     * <AUTHOR>
     * @Description 获取车辆司机审核状态
     * @Date 2020/1/19 13:56
     * @Param
     * @return
     **/
    private ResultUtil getUserCarAuditStatus(TOrderInfo orderInfo, String operateMethod) {
        ResultUtil resultUtil = ResultUtil.error();
        TEndCarInfoVO endCarInfoVO = new TEndCarInfoVO();
        endCarInfoVO.setUserId(orderInfo.getEndDriverId());
        HashSet<Integer> endCarId = new HashSet<>();
        endCarId.add(orderInfo.getVehicleId());
        endCarInfoVO.setEndCarId(endCarId);
        ResultUtil carAndDriverStatus = endUserCar.getCarAndDriverStatus(endCarInfoVO);
        if (carAndDriverStatus.getCode().equals("success")) {
            ArrayList<LinkedHashMap> data = (ArrayList) carAndDriverStatus.getData();
            if (data.size() > 0) {
                StringBuilder msg = new StringBuilder();
                for (LinkedHashMap datum : data) {
                    if ("driver".equals(datum.get("type"))) {
                        msg.append(datum.get("mark"));
                        msg.append(",");
                    } else {
                        msg.append(datum.get("mark"));
                        msg.append(",");
                    }
                }
                msg.append("未审核通过,暂不能支付。");
                ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                exceptionSendOrder.setCount(1);
                exceptionSendOrder.setIsLogic(false);
                exceptionSendOrder.setOrderBusinessCode(orderInfo.getOrderBusinessCode());
                HashMap<String, Object> result = new HashMap<>();
                result.put("exceptionSendOrder", exceptionSendOrder);
                if (operateMethod.equals("pay")) {
                    resultUtil.setData(result);
                } else if (operateMethod.equals("payPC")) {
                    resultUtil.setData(orderInfo.getOrderBusinessCode());
                }
                resultUtil.setMsg(msg.toString());
                return resultUtil;
            }
        }
        return null;
    }


    /**
     * @Description 运单打包支付
     * <AUTHOR>
     * @Date 2019/6/11 8:50
     * @Param
     * @Return
     * @Exception
     */
    @PostMapping("/packPay")
    public ResultUtil packPay(@RequestBody SendOrderVO sendOrderVO) {
        if (sendOrderVO != null && null != sendOrderVO.getCodes() && sendOrderVO.getCodes().length > 0) {
            String[] codes = sendOrderVO.getCodes();
            List<ResultUtil> resultUtils = new ArrayList<>();
            String code = codes[0];
            TOrderPayInfoDTO orderPayInfoDTO = orderPackInfoService.selectByPayInfoLimitOne(code);
            TOrderInfo orderInfo = orderInfoService.selectOrderInfoByCode(orderPayInfoDTO.getOrderCode());
            TWallet companyWallet = walletUtil.selectWalletByCompany(orderInfo.getCarrierId(), orderInfo.getCompanyId(), DictEnum.BD.code, DictEnum.BCOMPANY.code);
            /**
             * 2019.11.12 修改加解锁方式
             */
            String walletKey = "Wallet" + companyWallet.getId();
            boolean walletLock = false;
            if (!redisUtil.hasKey(walletKey)) {
                walletLock = redisUtil.set(walletKey, "lock");
            }
            try {
                if (walletLock) {

                    for (String orderCode : codes) {
                        ResultUtil resultUtil = new ResultUtil();
                        String virtualOrderNo = "";

                        TOrderPackInfo tOrderPackInfo = orderPackInfoService.selectByCode(orderCode);
                        if (null != tOrderPackInfo && null != tOrderPackInfo.getVirtualOrderNo()) {
                            virtualOrderNo = tOrderPackInfo.getVirtualOrderNo();
                            resultUtil.setData(virtualOrderNo);
                        } else {
                            resultUtil.setCode("error");
                            resultUtil.setData(virtualOrderNo);
                            resultUtil.setMsg("未找到打包运单");
                            resultUtils.add(resultUtil);
                            continue;
                        }
                        try {
                            if (tOrderPackInfo.getPackStatus().equals(DictEnum.PACKED.code)
                                    || tOrderPackInfo.getPackStatus().equals(DictEnum.PACKORDERPAIDERROR.code)
                                    || tOrderPackInfo.getPackStatus().equals(DictEnum.PACKRECALL.code)) {
                                // 判断提现方式，如果是自动卡到，判断提现是否超额
                                TOrderCastChanges tOrderCastChanges = orderCastChangesService.selectByNewOne(orderInfo.getCode());
                                // 判断车主权益证明
                                if (null != tOrderCastChanges.getCapitalTransferPattern() && DictEnum.PAYTOBELONGER.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                                    ResultUtil selectCarOwnerQY = appCommonAPI.selectCarOwnerQY(tOrderPackInfo.getEndUserId(), orderInfo.getVehicleId());
                                    if (DictEnum.ERROR.code.equals(selectCarOwnerQY.getCode())) {
                                        selectCarOwnerQY.setData(tOrderPackInfo.getVirtualOrderNo());
                                        selectCarOwnerQY.setMsg(selectCarOwnerQY.getMsg());
                                        resultUtils.add(selectCarOwnerQY);
                                        continue;
                                    }
                                }
                                if (DictEnum.AUTOMATION.code.equals(tOrderCastChanges.getWithdrawType())) {
                                    BigDecimal serviceFee = null == tOrderPackInfo.getTotalSelectedOrdersServiceFee() ? BigDecimal.ZERO : tOrderPackInfo.getTotalSelectedOrdersServiceFee();
                                    BigDecimal amount = tOrderPackInfo.getAppointmentPaymentCash().subtract(serviceFee);
                                    TBankCard bankCard = sendOrderUtil.checkCardholder(tOrderPackInfo.getBankCardId());

                                    TCompanyProject tCompanyProject = companyProjectAPI.selectByCompanyProjectById(orderInfo.getCompanyProjectId());
                                    //此项目是否限额
                                    if (tCompanyProject.getIfQuota()) {
                                        sendOrderUtil.checkCardHolderWithdrawAmount(bankCard, amount);
                                    } else {
                                        sendOrderUtil.checkCardHolderWithdrawAmount50Limit(bankCard, amount);
                                    }
                                }

                                if (null == tOrderCastChanges.getCapitalTransferPattern()
                                        || StringUtils.isBlank(tOrderCastChanges.getCapitalTransferPattern())) {
                                    log.info("旧打包支付");
                                    resultUtil = appOrderService.packPay(orderCode);
                                } else {
                                    log.info("新打包支付");
                                    resultUtil = appOrderService.newPackPay(orderCode);
                                }
                                resultUtil.setData(virtualOrderNo);
                                resultUtils.add(resultUtil);
                                // 记录提现金额
                                if (DictEnum.AUTOMATION.code.equals(tOrderCastChanges.getWithdrawType())) {
                                    sendOrderUtil.saveBankCardMonthlyAmount(tOrderPackInfo.getEndUserId(), tOrderPackInfo.getCode(), tOrderCastChanges.getCarriageFee().subtract(tOrderCastChanges.getServiceFee()));
                                }
                            } else {
                                resultUtil.setCode(DictEnum.ERROR.code);
                                resultUtil.setMsg("运单状态错误，不可支付");
                                resultUtils.add(resultUtil);
                            }
                        } catch (Exception e) {
                            log.error("ZJJ-006:打包支付失败!", e);
                            String message = e.getMessage();
                            resultUtil.setData(virtualOrderNo);
                            if (StringUtils.checkChineseCharacter(message)) {
                                resultUtil.setMsg(message);
                                resultUtils.add(resultUtil);
                            } else {
                                resultUtil.setMsg("ZJJ-006:打包支付失败!");
                                resultUtils.add(resultUtil);
                            }
                        }
                    }

                } else {
                    for (String orderCode : codes) {
                        ResultUtil resultUtil = new ResultUtil();
                        TOrderPackInfo tOrderPackInfo = orderPackInfoService.selectByCode(orderCode);
                        resultUtil.setMsg("企业正在支付，请稍后再试");
                        resultUtil.setData(tOrderPackInfo.getVirtualOrderNo());
                        resultUtils.add(resultUtil);
                    }
                    return ResultUtil.ok(resultUtils);
                }
            } catch (Exception e) {
                log.error("ZJJ-006:打包支付失败!", e);
                ResultUtil resultUtil = ResultUtil.error();
                String message = e.getMessage();
                if (StringUtils.checkChineseCharacter(message)) {
                    resultUtil.setMsg(message);
                    return resultUtil;
                } else {
                    resultUtil.setMsg("ZJJ-006:打包支付失败!");
                    return resultUtil;
                }
            } finally {
                if (walletLock && redisUtil.hasKey(walletKey)) {
                    redisUtil.del(walletKey);
                }
            }
            return ResultUtil.ok(resultUtils);
        }
        return ResultUtil.ok();
    }

    /*
     * <AUTHOR>
     * @Description 分润打包支付
     * @Date 2020/7/9 10:58
     * @Param
     * @return
     **/
    @PostMapping("/royaltyPackPay")
    public ResultUtil royaltyPackPay(@RequestBody SendOrderVO sendOrderVO) {
        if (sendOrderVO != null && null != sendOrderVO.getCodes() && sendOrderVO.getCodes().length > 0) {
            String[] codes = sendOrderVO.getCodes();
            List<ResultUtil> resultUtils = new ArrayList<>();
            String code = codes[0];
            TOrderPayInfoDTO orderPayInfoDTO = orderPackInfoService.selectByPayInfoLimitOne(code);
            if (!orderPayInfoDTO.getLsCalculation()) {
                return ResultUtil.error("打包运单正在计算系数，请稍后在进行支付");
            }
            TOrderInfo orderInfo = orderInfoService.selectOrderInfoByCode(orderPayInfoDTO.getOrderCode());
            TWallet companyWallet = walletUtil.selectWalletByCompany(orderInfo.getCarrierId(), orderInfo.getCompanyId(), DictEnum.BD.code, DictEnum.BCOMPANY.code);
            /**
             * 2019.11.12 修改加解锁方式
             */
            String walletKey = "Wallet" + companyWallet.getId();
            boolean walletLock = false;
            if (!redisUtil.hasKey(walletKey)) {
                walletLock = redisUtil.set(walletKey, "lock");
            }
            try {
                if (walletLock) {

                    for (String orderCode : codes) {
                        ResultUtil resultUtil = new ResultUtil();
                        String virtualOrderNo = "";

                        TOrderPackInfo tOrderPackInfo = orderPackInfoService.selectByCode(orderCode);
                        if (null != tOrderPackInfo && null != tOrderPackInfo.getVirtualOrderNo()) {
                            virtualOrderNo = tOrderPackInfo.getVirtualOrderNo();
                            resultUtil.setData(virtualOrderNo);
                        } else {
                            resultUtil.setCode("error");
                            resultUtil.setData(virtualOrderNo);
                            resultUtil.setMsg("未找到打包运单");
                            resultUtils.add(resultUtil);
                            continue;
                        }
                        try {
                            if (tOrderPackInfo.getPackStatus().equals(DictEnum.PACKED.code)
                                    || tOrderPackInfo.getPackStatus().equals(DictEnum.PACKORDERPAIDERROR.code)
                                    || tOrderPackInfo.getPackStatus().equals(DictEnum.PACKRECALL.code)) {
                                TOrderCastChanges tOrderCastChanges = orderCastChangesService.selectByNewOne(orderInfo.getCode());
                                //TODO 1. 判断车辆和司机是否审核通过：线路货物关系表中
                                TOrderInfo tOrderInfo = orderInfoService.selectOrderInfoOneByPackCode(tOrderPackInfo.getCode());
                                TGoodsSourceInfo tGoodsSourceInfo = new TGoodsSourceInfo().setLineGoodsRelId(tOrderInfo.getLineGoodsRelId());
                                LineGoodsRelInfo lineGoodsRelInfo = linkGoodsRelAPI.selectLineGoodsRelInfo(tGoodsSourceInfo);
                                if (null != lineGoodsRelInfo && null != lineGoodsRelInfo.getTransportIdentityCheck()
                                        && lineGoodsRelInfo.getTransportIdentityCheck()) {
                                    ResultUtil checkPackOrderUserCarStatus = sendOrderUtil.checkPackOrderUserCarStatus(tOrderPackInfo.getCode(),
                                            tOrderCastChanges.getCapitalTransferType(), tOrderCastChanges.getCapitalTransferPattern());
                                    if (null != checkPackOrderUserCarStatus) {
                                        checkPackOrderUserCarStatus.setData(tOrderPackInfo.getVirtualOrderNo());
                                        resultUtils.add(checkPackOrderUserCarStatus);
                                        return ResultUtil.ok(resultUtils);
                                    }
                                }
                                // 判断车主权益证明 车队长上线兼容老数据 车老板不做校验
                                /*if (null != tOrderCastChanges.getCapitalTransferPattern()
                                        && (DictEnum.PAYTOBELONGER.code.equals(tOrderCastChanges.getCapitalTransferType()))|| DictEnum.PAYTOCAPTAIN.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                                    ResultUtil selectCarOwnerQY = appCommonAPI.selectCarOwnerQY(tOrderPackInfo.getEndUserId(), orderInfo.getVehicleId());
                                    if (DictEnum.ERROR.code.equals(selectCarOwnerQY.getCode())) {
                                        selectCarOwnerQY.setData(tOrderPackInfo.getVirtualOrderNo());
                                        selectCarOwnerQY.setMsg(selectCarOwnerQY.getMsg());
                                        resultUtils.add(selectCarOwnerQY);
                                        continue;
                                    }
                                }*/

                                // 判断提现方式，如果是自动卡到，判断提现是否超额
                                if (DictEnum.AUTOMATION.code.equals(tOrderCastChanges.getWithdrawType())) {
                                    BigDecimal serviceFee = null == tOrderPackInfo.getTotalSelectedOrdersServiceFee() ? BigDecimal.ZERO : tOrderPackInfo.getTotalSelectedOrdersServiceFee();
                                    BigDecimal amount = tOrderPackInfo.getAppointmentPaymentCash().subtract(serviceFee);
                                    TBankCard bankCard = sendOrderUtil.checkCardholder(tOrderPackInfo.getBankCardId());

                                    TCompanyProject tCompanyProject = companyProjectAPI.selectByCompanyProjectById(orderInfo.getCompanyProjectId());
                                    //此项目是否限额
                                    if (tCompanyProject.getIfQuota()) {
                                        sendOrderUtil.checkCardHolderWithdrawAmount(bankCard, amount);
                                    } else {
                                        sendOrderUtil.checkCardHolderWithdrawAmount50Limit(bankCard, amount);
                                    }
                                }


                                if (null != tOrderCastChanges.getCapitalTransferPattern() && StringUtils.isNotBlank(tOrderCastChanges.getCapitalTransferPattern())) {
                                    log.info("打包分润支付");
                                    resultUtil = appOrderService.royaltyPackPay(orderCode);
                                } else {
                                    log.info("旧打包单支付");
                                    resultUtil = appOrderService.packPay(orderCode);
                                }
                                resultUtil.setData(virtualOrderNo);
                                resultUtils.add(resultUtil);
                            } else {
                                resultUtil.setCode(DictEnum.ERROR.code);
                                resultUtil.setMsg("运单状态错误，不可支付");
                                resultUtils.add(resultUtil);
                            }
                        } catch (Exception e) {
                            log.error("ZJJ-006:打包支付失败!", e);
                            String message = e.getMessage();
                            resultUtil.setData(virtualOrderNo);
                            if (StringUtils.checkChineseCharacter(message)) {
                                resultUtil.setMsg(message);
                                resultUtils.add(resultUtil);
                            } else {
                                resultUtil.setMsg("ZJJ-006:打包支付失败!");
                                resultUtils.add(resultUtil);
                            }
                        }
                    }

                } else {
                    for (String orderCode : codes) {
                        ResultUtil resultUtil = new ResultUtil();
                        TOrderPackInfo tOrderPackInfo = orderPackInfoService.selectByCode(orderCode);
                        resultUtil.setMsg("企业正在支付，请稍后再试");
                        resultUtil.setData(tOrderPackInfo.getVirtualOrderNo());
                        resultUtils.add(resultUtil);
                    }
                    return ResultUtil.ok(resultUtils);
                }
            } catch (Exception e) {
                log.error("ZJJ-006:打包支付失败!", e);
                ResultUtil resultUtil = ResultUtil.error();
                String message = e.getMessage();
                if (StringUtils.checkChineseCharacter(message)) {
                    resultUtil.setMsg(message);
                    return resultUtil;
                } else {
                    resultUtil.setMsg("ZJJ-006:打包支付失败!");
                    return resultUtil;
                }
            } finally {
                if (walletLock && redisUtil.hasKey(walletKey)) {
                    redisUtil.del(walletKey);
                }
            }
            return ResultUtil.ok(resultUtils);
        }
        return ResultUtil.ok();
    }

    /**
     * 节点支付
     *
     * @return
     * @Date 2020-11-21
     * <AUTHOR>
     */
    @PostMapping(value = "/nodePay")
    public ResultUtil nodePay(@RequestBody SendOrderVO record) {
        try {
            ResultUtil resultUtil = ResultUtil.ok();
            String orderCode = record.getCode();
            if (StringUtils.isNotEmpty(orderCode)) {
                if (null != record.getPaymentFee() && !(record.getPaymentFee().compareTo(BigDecimal.ZERO) > 0)) {
                    return ResultUtil.error("确认应付运费应当大于0");
                }
                TOrderInfo orderInfo = orderInfoService.selectOrderInfoByCode(orderCode);
                if (DictEnum.WQD.code.equals(orderInfo.getContractStatus())) {
                    return ResultUtil.error("当前运单未签订合同，不允许支付");
                }
                if (record.getPayNodeType().equals(DictEnum.ZHPAYNODE.code)) {
                    List<TOrderState> tOrderStateList = orderStateService.selectByOrderCodeAndStateNode(orderInfo.getCode(), DictEnum.M030.code);
                    if (null == tOrderStateList || "".equals(tOrderStateList) || tOrderStateList.size() < 1) {
                        return ResultUtil.error("装货地未签到，不允许装货支付");
                    }
                    TOrderPayRule tOrderPayRule = new TOrderPayRule();
                    tOrderPayRule.setOrderCode(orderInfo.getCode());
                    tOrderPayRule.setPayNodeType(record.getPayNodeType());
                    TOrderPayRule result = tOrderPayRuleService.selectByOrderCodeAndType(tOrderPayRule);
                    if (DictEnum.PACKEDHANDEL.code.equals(result.getPayStatus()) || DictEnum.PACKPAID.code.equals(result.getPayStatus())) {
                        return ResultUtil.error("当前运单已支付或支付处理中，不允许装货支付");
                    }
                } else if (record.getPayNodeType().equals(DictEnum.XHPAYNODE.code)) {
                    List<TOrderState> tOrderStateList = orderStateService.selectByOrderCodeAndStateNode(orderInfo.getCode(), DictEnum.M040.code);
                    if (null == tOrderStateList || "".equals(tOrderStateList) || tOrderStateList.size() < 1) {
                        return ResultUtil.error("卸货地未签到，不允许卸货支付");
                    }
                    TOrderPayRule tOrderPayRule = new TOrderPayRule();
                    tOrderPayRule.setOrderCode(orderInfo.getCode());
                    tOrderPayRule.setPayNodeType(record.getPayNodeType());
                    TOrderPayRule result = tOrderPayRuleService.selectByOrderCodeAndType(tOrderPayRule);
                    if (DictEnum.PACKEDHANDEL.code.equals(result.getPayStatus()) || DictEnum.PACKPAID.code.equals(result.getPayStatus())) {
                        return ResultUtil.error("当前运单已支付或支付处理中，不允许卸货支付");
                    }
                } else if (record.getPayNodeType().equals(DictEnum.SDPAYNODE.code)) {
                    List<TOrderState> tOrderStateList = orderStateService.selectByOrderCodeAndStateNode(orderInfo.getCode(), DictEnum.M050.code);
                    if (null == tOrderStateList || "".equals(tOrderStateList) || tOrderStateList.size() < 1) {
                        return ResultUtil.error("运单未收单，不允许收单支付");
                    }
                    TOrderPayRule tOrderPayRule = new TOrderPayRule();
                    tOrderPayRule.setOrderCode(orderInfo.getCode());
                    tOrderPayRule.setPayNodeType(record.getPayNodeType());
                    TOrderPayRule result = tOrderPayRuleService.selectByOrderCodeAndType(tOrderPayRule);
                    if (DictEnum.PACKEDHANDEL.code.equals(result.getPayStatus()) || DictEnum.PACKPAID.code.equals(result.getPayStatus())) {
                        return ResultUtil.error("当前运单已支付或支付处理中，不允许收单支付");
                    }
                } else if (record.getPayNodeType().equals(DictEnum.WKPAYNODE.code)) {
                    List<TOrderState> tOrderStateList = orderStateService.selectByOrderCodeAndStateNode(orderInfo.getCode(), DictEnum.M050.code);
                    if (null == tOrderStateList || "".equals(tOrderStateList) || tOrderStateList.size() < 1) {
                        return ResultUtil.error("运单未收单，不允许尾款支付");
                    }
                    TOrderPayRule tOrderPayRule = new TOrderPayRule();
                    tOrderPayRule.setOrderCode(orderInfo.getCode());
                    tOrderPayRule.setPayNodeType(record.getPayNodeType());
                    TOrderPayRule result = tOrderPayRuleService.selectByOrderCodeAndType(tOrderPayRule);
                    if (DictEnum.PACKEDHANDEL.code.equals(result.getPayStatus()) || DictEnum.PACKPAID.code.equals(result.getPayStatus())) {
                        return ResultUtil.error("当前运单已支付或支付处理中，不允许尾款支付");
                    }
                }
                TOrderPayRule re = new TOrderPayRule();
                re.setPayNodeType(record.getPayNodeType());
                re.setOrderCode(orderInfo.getCode());
                TOrderPayRule tOrderPayRule = tOrderPayRuleService.selectByOrderCodeAndType(re);
                if (null != tOrderPayRule && !"".equals(tOrderPayRule)) {
                    if (DictEnum.PACKPAID.code.equals(tOrderPayRule.getPayStatus()) || DictEnum.PACKEDHANDEL.code.equals(tOrderPayRule.getPayStatus())) {
                        return ResultUtil.error("当前运单，已支付或支付中请刷新");
                    }
                }
                TOrderCastChanges castChanges = orderCastChangesService.selectByNewOne(orderInfo.getCode());
                String orderKey = "Order" + orderCode;
                String walletKey = "Wallet" + castChanges.getCompanyWalletId();
                boolean orderLock = false;
                boolean walletLock = false;
                if (!redisUtil.hasKey(orderKey)) {
                    orderLock = redisUtil.set(orderKey, "lock");
                }
                if (!redisUtil.hasKey(walletKey)) {
                    walletLock = redisUtil.set(walletKey, "lock");
                }
                try {
                    if (!orderLock) {
                        return ResultUtil.error("该运单正在进行其他操作，请核对后在进行支付");
                    }
                    if (!walletLock) {
                        return ResultUtil.error("企业正在操作中，请稍后再试");
                    }
                    // 添加运单锁，防止工具并发，30s自动解锁，只锁支付
                    if (!redisUtil.hasKey("pay" + orderKey)) {
                        redisUtil.set("pay" + orderKey, "pay", 30);
                    } else {
                        return ResultUtil.error("该运单正在进行其他操作，请核对后在进行支付");
                    }
                    resultUtil = sendOrderUtil.checkNodePay(orderInfo, record);
                    if (null == resultUtil) {
                        resultUtil = appOrderService.nodePay(orderInfo, record);
                    } else {
                        return resultUtil;
                    }
                } catch (Exception e) {
                    log.error("错误码：ZJJZ001，请联系运营平台予以解决。", e);
                    String message = e.getMessage();
                    if (StringUtils.checkChineseCharacter(message)) {
                        ResultUtil error = ResultUtil.error();
                        error.setMsg(message);
                        ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                        if (message.equals(BusinessCode.COMPANYWALLETINSUFFICIENT.code)) {
                            exceptionSendOrder.setCount(1);
                            exceptionSendOrder.setIsLogic(false);
                        } else {
                            exceptionSendOrder.setCount(2);
                            exceptionSendOrder.setIsLogic(true);
                        }
                        HashMap<String, Object> result = new HashMap<>();
                        result.put("exceptionSendOrder", exceptionSendOrder);
                        error.setData(result);
                        return error;
                    } else {
                        ResultUtil error = ResultUtil.error();
                        ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                        exceptionSendOrder.setCount(2);
                        exceptionSendOrder.setIsLogic(true);
                        HashMap<String, Object> result = new HashMap<>();
                        result.put("exceptionSendOrder", exceptionSendOrder);
                        error.setMsg("错误码：ZJJZ001，请联系运营平台予以解决。");
                        error.setData(result);
                        return error;
                    }
                } finally {
                    if (orderLock && redisUtil.hasKey(orderKey)) {
                        redisUtil.del(orderKey);
                        log.info("释放运单锁, {}", orderKey);
                    }
                    if (walletLock && redisUtil.hasKey(walletKey)) {
                        redisUtil.del(walletKey);
                        log.info("释放企业钱包锁, {}", walletKey);
                    }
                }
            }
            resultUtil.setMsg(DictEnum.PAYSUCCESS.code);
            return resultUtil;
        } catch (Exception e) {
            log.error("ZJJ-004:APP支付失败", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("ZJJ-004:APP支付失败");
        }
    }

    /**
     * App 运单进度查询
     * Yan
     *
     * @param search
     * @return
     */
    @PostMapping("/process")
    public ResultUtil waybillProcess(@RequestBody AppOrderSearchVO search) {
        try {
            ResultUtil resultUtil = appOrderService.appSelectOrderProcess(search.getCode());
            return resultUtil;
        } catch (Exception e) {
            log.error("ZJJ-007:APP运单进度查询失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("ZJJ-007:APP运单进度查询失败!");
        }
    }

    /**
     * 删除运单：APP、运营、企业PC
     *
     * @return
     * <AUTHOR>
     */
    @PostMapping("/deleteOrder")
    public ResultUtil deleteOrder(@RequestBody TOrderInfoVO orderInfoVO) {
        try {
            ResultUtil resultUtil = ResultUtil.ok();
            TOrderInfo orderInfo = orderInfoService.selectOrderInfoByCode(orderInfoVO.getCode());
            // TWallet companyWallet = walletUtil.selectWalletByCompany(orderInfo.getCarrierId(), orderInfo.getCompanyId(), DictEnum.BD.code, DictEnum.BCOMPANY.code);
            String orderKey = "Order" + orderInfo.getOrderBusinessCode();
            // String walletKey = "Wallet";
            // walletKey = walletKey + companyWallet.getId();
            /*if (orderInfo.getFeeSettlementWay().equals("BILLPAY")) {
                walletKey = walletKey + orderInfo.getCompanyProjectId();
            } else {
                walletKey = walletKey + companyWallet.getId();
            }*/
            boolean orderLock = RedissLockUtil.tryLock(orderKey, 5 * 5 * 60, 20);
            // boolean walletLock = RedissLockUtil.tryLock(walletKey, 5 * 5 * 60, 20);
            try {
                if (orderLock) {
                    String verificationCode = orderInfoVO.getVerificationCode();
                    if (null != orderInfoVO.getSmsType()) {
                        if (StringUtils.isNotEmpty(orderInfoVO.getSmsType())) {
                            if (orderInfoVO.getSmsType().equals("APPDELORDER")) {
                                String smsCode = String.valueOf(redisUtil.get("APPDELORDER" + orderInfoVO.getDeletePersonPhone()));
                                if (verificationCode.equals(smsCode)) {
                                    // 查询是否预付款已支付、已提现
                                    TAdvanceOrderTmp tAdvanceOrderTmp = advanceOrderTmpService.selectAdvanceOrderTempByOrderCode(orderInfoVO.getCode());
                                    if (null != tAdvanceOrderTmp) {
                                        if (DictEnum.P070.code.equals(tAdvanceOrderTmp.getOrderPayStatus())) {
                                            return ResultUtil.error("预付款运单正在支付中，不能删除！");
                                        }
                                        if (DictEnum.M090.code.equals(tAdvanceOrderTmp.getOrderPayStatus())) {
                                            return ResultUtil.error("预付款运单已支付，不能删除！");
                                        }
                                        if (DictEnum.M130.code.equals(tAdvanceOrderTmp.getOrderPayStatus())) {
                                            return ResultUtil.error("预付款运单已提现，不能删除！");
                                        }
                                    }
                                    TVerificationCodeLog tVerificationCodeLog = new TVerificationCodeLog();
                                    tVerificationCodeLog.setReceivePhoneno(orderInfoVO.getDeletePersonPhone());
                                    tVerificationCodeLog.setVerificationCode(smsCode);
                                    tVerificationCodeLogAPI.updateIfUsed(tVerificationCodeLog);
                                    resultUtil = appOrderService.deleteOrder(orderInfoVO);
                                } else if ("null".equals(smsCode)) {
                                    resultUtil = ResultUtil.error("验证码已失效");
                                } else {
                                    resultUtil = ResultUtil.error("验证码错误");
                                }
                            }
                        }
                    } else {
                        String smsCode = String.valueOf(redisUtil.get("PCDELORDER" + orderInfoVO.getDeletePersonPhone()));
                        if (verificationCode.equals(smsCode)) {
                            TVerificationCodeLog tVerificationCodeLog = new TVerificationCodeLog();
                            tVerificationCodeLog.setReceivePhoneno(orderInfoVO.getDeletePersonPhone());
                            tVerificationCodeLog.setVerificationCode(smsCode);
                            tVerificationCodeLogAPI.updateIfUsed(tVerificationCodeLog);
                            resultUtil = appOrderService.deleteOrder(orderInfoVO);
                            redisUtil.del("PCDELORDER" + orderInfoVO.getDeletePersonPhone());
                        } else if ("null".equals(smsCode)) {
                            resultUtil = ResultUtil.error("验证码已失效");
                        } else {
                            resultUtil = ResultUtil.error("验证码错误");
                        }
                    }
                    return resultUtil;
                }

            } catch (Exception e) {
                log.error("ZJJ-008:删除运单失败!", e);
                String message = e.getMessage();
                if (StringUtils.checkChineseCharacter(message)) {
                    return ResultUtil.error(message);
                }
                return ResultUtil.error("ZJJ-008:删除运单失败!");
            } finally {
                if (orderLock) {
                    RedissLockUtil.unlock(orderKey);
                }
                /*if (walletLock) {
                    RedissLockUtil.unlock(walletKey);
                }*/
            }

        } catch (Exception e) {
            log.error("ZJJ-008:删除运单失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("ZJJ-008:删除运单失败!");
        }
        return ResultUtil.ok();
    }

    /**
     * @author: dingweibo
     * @Date: 2020/11/12 9:56
     * @Description: 运单检查批量删除运单
     */
    @PostMapping("/orderBatchDelete")
    @ResponseBody
    public ResultUtil orderBatchDelete(@RequestBody TOrderInfoUploadReq record) {
        try {
            ResultUtil resultUtil = ResultUtil.ok();
            String verificationCode = record.getVerificationCode();
            String smsCode = String.valueOf(redisUtil.get("PCDELORDER" + record.getDeletePersonPhone()));
            if (verificationCode.equals(smsCode)) {
                TVerificationCodeLog tVerificationCodeLog = new TVerificationCodeLog();
                tVerificationCodeLog.setReceivePhoneno(record.getDeletePersonPhone());
                tVerificationCodeLog.setVerificationCode(smsCode);
                tVerificationCodeLogAPI.updateIfUsed(tVerificationCodeLog);

                boolean udFlag = true;
                List<HashMap<String, Object>> resultUtilsUd = new ArrayList<>();
                for (TOrderInfoUploadVo tOrderInfoUploadVo : record.getOrderInfoList()) {
                    String orderBusinessCode = tOrderInfoUploadVo.getOrderBusinessCode().replace(" ", "");
                    HashMap<String, Object> result = new HashMap<>();
                    result.put("orderCode", orderBusinessCode);
                    TOrderInfo tOrderInfo = orderInfoService.selectOrderBusinessCode(orderBusinessCode);
                    if (null == tOrderInfo || "".equals(tOrderInfo)) {
                        udFlag = false;
                        log.info("删单失败！请确认运单号是否正确");
                        result.put("code", "error");
                        result.put("msg", "删单失败！请确认运单号是否正确");
                        resultUtilsUd.add(result);
                        break;
                    } else if (
                            !(
                                    tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M010.code)
                                            || tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M020.code)
                                            || tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M030.code)
                                            || tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M040.code)
                                            || tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M060.code)
                                            || (
                                            tOrderInfo.getOrderExecuteStatus().equals(DictEnum.O060.code)
                                                    && (
                                                    null == tOrderInfo.getOrderPayStatus()
                                                            || StrUtil.isBlank(tOrderInfo.getOrderPayStatus())
                                                            || tOrderInfo.getOrderPayStatus().equals(DictEnum.M045.code)
                                                            || tOrderInfo.getOrderPayStatus().equals(DictEnum.M095.code)
                                            )
                                    )
                            )
                    ) {
                        udFlag = false;
                        log.info("删单失败！请确认运单状态");
                        result.put("code", "error");
                        result.put("msg", "删单失败！请确认运单状态");
                        resultUtilsUd.add(result);
                        break;
                    } else if (DictEnum.NODEPAYFIXED.code.equals(tOrderInfo.getPayMethod())//发单节点支付模式
                            || DictEnum.NODEPAYPROPORTION.code.equals(tOrderInfo.getPayMethod())) {
                        boolean flag = tOrderPayRuleService.isNodePay(tOrderInfo);
                        if (flag) {
                            result.put("code", "error");
                            result.put("msg", "删单失败！请确认运单状态");
                            resultUtilsUd.add(result);
                            udFlag = false;
                            break;
                            //return ResultUtil.error("当前运单已节点支付， 不可删除");
                        }
                    }
                    // 查询是否预付款已支付、已提现
                    TAdvanceOrderTmp tAdvanceOrderTmp = advanceOrderTmpService.selectAdvanceOrderTempByOrderCode(tOrderInfo.getCode());
                    if (null != tAdvanceOrderTmp) {
                        if (DictEnum.P070.code.equals(tAdvanceOrderTmp.getOrderPayStatus())
                                || DictEnum.M090.code.equals(tAdvanceOrderTmp.getOrderPayStatus())
                                || DictEnum.M130.code.equals(tAdvanceOrderTmp.getOrderPayStatus())
                                || (null == tOrderInfo.getOrderPayStatus() || StrUtil.isBlank(tOrderInfo.getOrderPayStatus())) || tOrderInfo.getOrderPayStatus().equals(DictEnum.M095.code)) {
                            result.put("code", "error");
                            result.put("msg", "删单失败！请确认预付款运单状态");
                            resultUtilsUd.add(result);
                            udFlag = false;
                            break;
                        }
                    }
                }
                //判断 预支付只支持普通模式且支付给实际运输人的运单导入
                if (!udFlag) {
                    resultUtil.setCode("error");
                    resultUtil.setMsg("删单失败！请确认运单状态和运单号是否正确");
                    resultUtil.setData(resultUtilsUd);
                    return resultUtil;
                }
                for (TOrderInfoUploadVo tOrderInfoUploadVo : record.getOrderInfoList()) {
                    TOrderInfo tOrderInfo = orderInfoService.selectOrderBusinessCode(tOrderInfoUploadVo.getOrderBusinessCode());
                    resultUtil = appOrderService.orderBatchDelete(tOrderInfo, record, tOrderInfoUploadVo);
                }
                redisUtil.del("PCDELORDER" + record.getDeletePersonPhone());
            } else if ("null".equals(smsCode)) {
                resultUtil = ResultUtil.error("验证码已失效");
            } else {
                resultUtil = ResultUtil.error("验证码错误");
            }
            return resultUtil;
        } catch (Exception e) {
            log.error("批量删除运单失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("批量删除运单失败!");
        }
    }

    /**
     * 发送删除运单验证码
     *
     * @return
     * @auth zhangjiji
     */
    @PostMapping("/sendDeleteOrderSms")
    public ResultUtil sendDeleteOrderSms(@RequestBody TOrderInfoVO orderInfoVO) throws Exception {
        try {
            ResultUtil resultUtil = ResultUtil.ok();
            resultUtil.setCode("success");
            if (null != orderInfoVO.getPhone() && StringUtils.isNotEmpty(orderInfoVO.getPhone())) {
                if (null != orderInfoVO.getCode() && StringUtils.isNotEmpty(orderInfoVO.getCode())) {
                    TOrderInfo orderInfo = orderInfoService.selectOrderInfoByCode(orderInfoVO.getCode());
                    if (null != orderInfo) {
                        if (orderInfo.getPackStatus().equals("1")) {
                            return ResultUtil.error("运单已打包， 不可删除");
                        }
                        else if (orderInfo.getOrderExecuteStatus().equals(DictEnum.M010.code)
                                || orderInfo.getOrderExecuteStatus().equals(DictEnum.M020.code)
                                || orderInfo.getOrderExecuteStatus().equals(DictEnum.M030.code)
                                || orderInfo.getOrderExecuteStatus().equals(DictEnum.M040.code)
                                || orderInfo.getOrderExecuteStatus().equals(DictEnum.M060.code)
                                || (orderInfo.getOrderExecuteStatus().equals(DictEnum.O060.code)
                                    && (null == orderInfo.getOrderPayStatus() || StrUtil.isBlank(orderInfo.getOrderPayStatus())
                                    || orderInfo.getOrderPayStatus().equals(DictEnum.M045.code)
                                    || orderInfo.getOrderPayStatus().equals(DictEnum.M095.code))
                                )
                        ) {
                            //发单节点支付模式
                            if (DictEnum.NODEPAYFIXED.code.equals(orderInfo.getPayMethod())
                                    || DictEnum.NODEPAYPROPORTION.code.equals(orderInfo.getPayMethod())) {
                                boolean flag = tOrderPayRuleService.isNodePay(orderInfo);
                                if (flag) {
                                    return ResultUtil.error("当前运单已节点支付， 不可删除");
                                }
                            }
                            // 查询是否预付款已支付、已提现
                            TAdvanceOrderTmp tAdvanceOrderTmp = advanceOrderTmpService.selectAdvanceOrderTempByOrderCode(orderInfoVO.getCode());
                            if (null != tAdvanceOrderTmp) {
                                if (DictEnum.P070.code.equals(tAdvanceOrderTmp.getOrderPayStatus())) {
                                    return ResultUtil.error("预付款运单正在支付中，不能删除！");
                                }
                                if (DictEnum.M090.code.equals(tAdvanceOrderTmp.getOrderPayStatus())) {
                                    return ResultUtil.error("预付款运单已支付，不能删除！");
                                }
                                if (DictEnum.M130.code.equals(tAdvanceOrderTmp.getOrderPayStatus())) {
                                    return ResultUtil.error("预付款运单已提现，不能删除！");
                                }
                            }
                            SmsMessage smsMessage = new SmsMessage();
                            try {
                                SmsReq req = new SmsReq();
                                req.setOrderBusinessCode(orderInfo.getOrderBusinessCode());
                                req.setMobiles(orderInfoVO.getPhone());
                                if (null == orderInfoVO.getSmsType() || StringUtils.isEmpty(orderInfoVO.getSmsType())) {
                                    req.setType("PCDELORDER");
                                } else {
                                    req.setType("APPDELORDER");
                                }
                                req.setOrderBusinessCode(orderInfo.getOrderBusinessCode());
                                //System.out.println("-----orderCode---------"+orderInfoVO.getOrderBusinessCode());
                                SmsResp resp = smsClientService.sendSmsType(req);
                            } catch (Exception e) {
                                log.info("-----orderCode---------类型：" + orderInfoVO.getSmsType() + "--" + orderInfoVO.getOrderBusinessCode());
                                e.printStackTrace();
                                resultUtil.setCode("error");
                                resultUtil.setMsg(e.getMessage());
                            } finally {
                                //记录验证码信息 sendSmsType方法统一处理
                              /*  if (null == orderInfoVO.getSmsType() || StringUtils.isEmpty(orderInfoVO.getSmsType())) {
                                    smsMessage.setSmType("PCDELORDER");
                                } else {
                                    smsMessage.setSmType("APPDELORDER");
                                }
                                smsMessage.setMemberId(orderInfoVO.getPhone());
                                smsMessage.setCreateTime(new Date());
                                systemAPI.saveMsg(smsMessage);*/
                            }
                        } else {
                            return ResultUtil.error("运单状态错误，不可删除");
                        }
                    } else {
                        return ResultUtil.error("运单未找到");
                    }
                } else {
                    return ResultUtil.error("运单未找到");
                }
            } else {
                return ResultUtil.error("手机号不存在");
            }
            return resultUtil;
        } catch (Exception e) {
            log.error("ZJJ-009:发送删除运单验证码失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("ZJJ-009:发送删除运单验证码失败!");
        }
    }


    /**
     * 发送批量删除运单验证码
     *
     * @return
     * @auth zhangjiji
     */
    @PostMapping("/sendBatchDeleteOrderSms")
    public ResultUtil sendBatchDeleteOrderSms(@RequestBody TOrderInfoVO orderInfoVO) throws Exception {
        try {
            ResultUtil resultUtil = ResultUtil.ok();
            resultUtil.setCode("success");
            if (null != orderInfoVO.getPhone() && StringUtils.isNotEmpty(orderInfoVO.getPhone())) {
                try {
                    SmsReq req = new SmsReq();
                    req.setMobiles(orderInfoVO.getPhone());
                    req.setType("PCDELORDER");
                    SmsResp resp = smsClientService.sendSmsType(req);
                } catch (Exception e) {
                    log.info("-----orderCode---------类型：" + orderInfoVO.getSmsType() + "--" + orderInfoVO.getOrderBusinessCode());
                    e.printStackTrace();
                    resultUtil.setCode("error");
                    resultUtil.setMsg(e.getMessage());
                } finally {
                    //记录验证码信息 sendSmsType方法统一处理
                  /*  if (null == orderInfoVO.getSmsType() || StringUtils.isEmpty(orderInfoVO.getSmsType())) {
                        smsMessage.setSmType("PCDELORDER");
                    } else {
                        smsMessage.setSmType("APPDELORDER");
                    }
                    smsMessage.setMemberId(orderInfoVO.getPhone());
                    smsMessage.setCreateTime(new Date());
                    systemAPI.saveMsg(smsMessage);*/
                }
            } else {
                return ResultUtil.error("手机号不存在");
            }
            return resultUtil;
        } catch (Exception e) {
            log.error("ZJJ-009:发送删除运单验证码失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("ZJJ-009:发送删除运单验证码失败!");
        }
    }


    /**
     * @Description 运单召回
     * <AUTHOR>
     * @Date 2019/6/5 20:44
     * @Param
     * @Return
     * @Exception
     */
    @PostMapping("/orderBack")
    public ResultUtil orderBack(@RequestBody SendOrderVO record) {

        if (null != record && null != record.getCodes() && record.getCodes().length > 0) {
            String[] codes = record.getCodes();
            List<ResultUtil> resultUtils = new ArrayList<>();
            String orderBusinessCode = "";
            String code = codes[0];
            TOrderCastChanges tOrderCastChanges = orderCastChangesService.selectByNewOne(code);
            String walletKey = "Wallet" + tOrderCastChanges.getCompanyWalletId();
            String driverWalletKey = "USER" + tOrderCastChanges.getEndDriverWalletId();
            String carrierWalletKey = "CARRIER" + tOrderCastChanges.getCarrierWalletId();
            Boolean driverWalletLock = false;
            Boolean carrierWalletLock = false;
            boolean walletLock = false;
            String carOwnerWalletKey = "";
            String agentWalletKey = "";
            Boolean carOwnerWalletLock = true;
            Boolean agentWalltLock = true;

            /**
             * 11月12日 修改加锁方式
             */
            if (!redisUtil.hasKey(walletKey) && !redisUtil.hasKey(driverWalletKey) && !redisUtil.hasKey(carrierWalletKey)) {
                walletLock = redisUtil.set(walletKey, "lock");
                driverWalletLock = redisUtil.set(driverWalletKey, "lock");
                carrierWalletLock = redisUtil.set(carrierWalletKey, "lock");
            }
            try {
                if (walletLock && driverWalletLock && carrierWalletLock) {
                    for (String orderCode : codes) {
                        ResultUtil resultUtil = ResultUtil.ok();
                        TOrderInfo orderInfo = orderInfoService.selectOrderInfoByCode(orderCode);
                        orderBusinessCode = orderInfo.getOrderBusinessCode();
                        if (orderInfo.getPackStatus().equals("1")) {
                            resultUtil.setData(orderBusinessCode);
                            resultUtil.setMsg("打包运单不可在此召回");
                            resultUtil.setCode("error");
                            resultUtils.add(resultUtil);
                            continue;
                        }
                        TOrderPayInfo orderPayInfo = orderPayInfoService.selectOrderPayInfoByOrderCode(orderCode);
                        if (DictEnum.JDPLATFORMS.code.equals(orderPayInfo.getPaymentPlatforms())) {
                            resultUtil.setData(orderBusinessCode);
                            resultUtil.setMsg("当前运单不支持网商召回，请重新选择。");
                            resultUtil.setCode("error");
                            resultUtils.add(resultUtil);
                            continue;
                        }
                        try {
                            TOrderInfo order = orderInfoService.selectOrderInfoByCode(orderCode);
                            if ((order.getOrderExecuteStatus().equals(DictEnum.M100.code) && order.getOrderPayStatus().equals(DictEnum.M090.code))
                                    || order.getOrderPayStatus().equals(DictEnum.M120.code)) {
                                record.setOrderCode(orderCode);
                                TOrderInfoVO orderInfoVO = new TOrderInfoVO();
                                orderInfoVO.setOrderCode(orderCode);
                                orderInfoVO.setStateRemark(record.getStateRemark());
                                resultUtil = appOrderService.orderBack(orderInfoVO);
                                resultUtil.setData(orderBusinessCode);
                                resultUtil.setMsg(DictEnum.ORDERBACKUCCESS.code);
                                resultUtils.add(resultUtil);
                                continue;
                            } else {
                                resultUtil.setData(orderBusinessCode);
                                resultUtil.setMsg("运单状态错误，无法召回");
                                resultUtil.setCode("error");
                                resultUtils.add(resultUtil);
                                continue;
                            }
                        } catch (Exception e) {
                            log.error("ZJJ-010:运单召回失败!", e);
                            resultUtil.setData(orderBusinessCode);
                            resultUtil.setCode("error");
                            String message = e.getMessage();
                            if (message.contains(BusinessCode.PWALLETNSUFFICIENT.code) || (message.contains(BusinessCode.CWALLETNSUFFICIENT.code))) {
                                resultUtil.setMsg("用户余额不足");
                            } else if (StringUtils.checkChineseCharacter(message)) {
                                resultUtil.setMsg(message);
                            } else {
                                log.error("ZJJ-010:运单召回失败!", e);
                                resultUtil.setMsg("ZJJ-010:运单召回失败!");
                            }
                            resultUtils.add(resultUtil);
                            continue;
                        }
                    }
                } else {
                    List<String> orderCodes;
                    if (null != record.getCodes() && record.getCodes().length > 0) {
                        orderCodes = Arrays.asList(record.getCodes());
                        List<String> orderBusinessCodeByCode = orderInfoService.selectOrderBusinessCodeByCode(orderCodes);
                        for (String orderBusiness : orderBusinessCodeByCode) {
                            ResultUtil resultUtil = ResultUtil.error("企业正在操作中，请稍后再试");
                            resultUtil.setData(orderBusiness);
                            resultUtils.add(resultUtil);
                        }
                    }
                    ResultUtil error = ResultUtil.error();
                    error.setData(resultUtils);
                    return error;
                }
            } catch (Exception e) {
                ResultUtil resultUtil = ResultUtil.error();
                log.error("ZJJ-010:运单召回失败!", e);
                String message = e.getMessage();
                if (StringUtils.checkChineseCharacter(message)) {
                    resultUtil.setMsg(message);
                    return resultUtil;
                } else {
                    resultUtil.setMsg("ZJJ-010:运单召回失败!");
                }
                return resultUtil;
            } finally {
                if (walletLock && redisUtil.hasKey(walletKey)) {
                    redisUtil.del(walletKey);
                }
                if (driverWalletLock && redisUtil.hasKey(driverWalletKey)) {
                    redisUtil.del(driverWalletKey);
                }
                if (carrierWalletLock && redisUtil.hasKey(carrierWalletKey)) {
                    redisUtil.del(carrierWalletKey);
                }
            }
            return ResultUtil.ok(resultUtils);
        } else {
            return ResultUtil.error("运单不能为空");
        }

    }

    /**
     * @Description 运单驳回
     * <AUTHOR>
     * @Date 2019/6/9 10:42
     * @Param
     * @Return
     * @Exception
     */
    @PostMapping("/rejectOrder")
    public ResultUtil rejectOrder(@RequestBody AppOrderSearchVO orderSearchVO) {
        try {
            if (null == orderSearchVO.getCode()) {
                return ResultUtil.error("运单不能为空");
            }
            TOrderInfo orderInfo = orderInfoService.selectOrderInfoByCode(orderSearchVO.getCode());
            if (null != orderInfo && null != orderInfo.getOrderBusinessCode()) {
                String orderKey = "Order" + orderInfo.getOrderBusinessCode();
                boolean orderLock = RedissLockUtil.tryLock(orderKey, 5 * 5 * 60, 20);
                try {
                    if (orderLock) {
                        orderInfo = orderInfoService.selectOrderInfoByCode(orderSearchVO.getCode());
                        if (orderInfo.getPackStatus().equals("1")) {
                            ResultUtil resultUtil = ResultUtil.error();
                            resultUtil.setCode("error");
                            resultUtil.setData(orderInfo.getOrderBusinessCode());
                            resultUtil.setMsg("打包原始运单不可在此驳回");
                            return resultUtil;
                        }
                        ResultUtil resultUtil = appOrderService.rejectOrder(orderSearchVO);
                        return resultUtil;
                    } else {
                        return ResultUtil.error("当前运单正在操作中，请稍后再试");
                    }
                } catch (Exception e) {
                    log.error("ZJJ-012:运单驳回失败", e);
                    String message = e.getMessage();
                    if (StringUtils.checkChineseCharacter(message)) {
                        return ResultUtil.error(message);
                    }
                    return ResultUtil.error("ZJJ-012:运单驳回失败!");
                } finally {
                    if (orderLock) {
                        RedissLockUtil.unlock(orderKey);
                    }
                }
            } else {
                return ResultUtil.error("ZJJ-012:运单驳回失败!");
            }
        } catch (Exception e) {
            log.error("ZJJ-012:运单驳回失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("ZJJ-012:运单驳回失败!");
        }
    }

    /**
     * @Description 查询车辆和司机是否审核通过
     * <AUTHOR>
     * @Date 2019/6/26 16:07
     * @Param
     * @Return
     * @Exception
     */
    public StringBuffer carAndUserAuditState(EnduserCarStatus enduserCarStatus) {
        StringBuffer stringBuffer = new StringBuffer();
        if (null != enduserCarStatus.getCarAuditStatus()) {
            if (enduserCarStatus.getCarAuditStatus().equals(DictEnum.MIDNODE.code) || enduserCarStatus.getCarAuditStatus().equals(DictEnum.PASSNODE.code)) {
            } else {
                stringBuffer.append(enduserCarStatus.getVehicleNumber() + ":" + enduserCarStatus.getCarAuditValue() + ",");
            }
        } else {
            stringBuffer.append(enduserCarStatus.getVehicleNumber() + ":" + "车辆审核状态错误" + ",");
        }
        if (null != enduserCarStatus.getUserAuditStatus()) {
            if (enduserCarStatus.getUserAuditStatus().equals(DictEnum.MIDNODE.code) || enduserCarStatus.getUserAuditStatus().equals(DictEnum.PASSNODE.code)) {
            } else {
                stringBuffer.append(enduserCarStatus.getRealName() + ":" + enduserCarStatus.getUserAuditValue() + ",");
            }
        } else {
            stringBuffer.append(enduserCarStatus.getRealName() + ":" + "司机审核状态错误" + ",");
        }
        return stringBuffer;
    }

    /**
     * @Description 打包运单召回
     * <AUTHOR>
     * @Date 2019/7/15 11:44
     * @Param
     * @Return
     * @Exception
     */
    @PostMapping("/orderBackDB")
    public ResultUtil orderBackDB(@RequestBody SendOrderVO record) {
        if (null != record && null != record.getCodes() && record.getCodes().length > 0) {
            TOrderCastChanges tOrderCastChanges = orderCastChangesService.selectByNewOne(record.getCodes()[0]);
            /**
             * ly
             * 11月12日修改加解锁方式
             */
            Integer companyWalletId = 0;
            if (null == tOrderCastChanges) {
                // 根据打包运单code查询与那时运单的资金变动
                tOrderCastChanges = orderCastChangesService.selectOrderCastChangeOneByPackCode(record.getCodes()[0]);
            }
            companyWalletId = tOrderCastChanges.getCompanyWalletId();
            String walletKey = "Wallet" + companyWalletId;
            boolean walletLock = false;
            if (!redisUtil.hasKey(walletKey)) {
                walletLock = redisUtil.set(walletKey, "lock");
            }
            List<ResultUtil> resultUtils = new ArrayList<>();
            try {
                if (walletLock) {
                    for (String orderCode : record.getCodes()) {
                        ResultUtil resultUtil = ResultUtil.ok();
                        TOrderPackInfo tOrderPackInfo = orderPackInfoService.selectByCode(orderCode);
                        String virtualOrderNo = tOrderPackInfo.getVirtualOrderNo();
                        try {
                            if (tOrderPackInfo.getPackStatus().equals(DictEnum.PACKPAID.code)
                                    || tOrderPackInfo.getPackStatus().equals(DictEnum.PACKEWITHDRAWERROR.code)) {
                                if (DictEnum.JDPLATFORMS.code.equals(tOrderPackInfo.getPaymentPlatforms())) {
                                    resultUtil.setMsg("当前运单不支持网商召回，请重新选择。");
                                } else {
                                    OrderPackVO orderPackVO = new OrderPackVO();
                                    orderPackVO.setOrderCode(orderCode);
                                    orderPackVO.setStateRemark(record.getStateRemark());
                                    resultUtil = appOrderService.orderBackDB(orderPackVO);
                                }
                            } else {
                                resultUtil.setMsg("运单状态错误，不可召回");
                            }
                            resultUtil.setData(virtualOrderNo);
                            resultUtil.setMsg(DictEnum.ORDERBACKUCCESS.code);
                            resultUtils.add(resultUtil);

                        } catch (Exception e) {
                            resultUtil.setData(virtualOrderNo);
                            resultUtil.setCode("error");
                            String message = e.getMessage();
                            if (message.contains(BusinessCode.PWALLETNSUFFICIENT.code) || (message.contains(BusinessCode.CWALLETNSUFFICIENT.code))) {
                                resultUtil.setMsg("用户余额不足");
                                resultUtils.add(resultUtil);
                            } else if (StringUtils.checkChineseCharacter(message)) {
                                resultUtil.setMsg(message);
                                resultUtils.add(resultUtil);
                            } else {
                                message = "ZJJ-013:打包运单召回失败";
                                resultUtil.setMsg(message);
                                resultUtils.add(resultUtil);
                            }
                        }
                    }
                } else {
                    List<String> orderCodes;
                    if (null != record.getCodes() && record.getCodes().length > 0) {
                        orderCodes = Arrays.asList(record.getCodes());
                        List<String> virtualOrderByCode = orderPackInfoService.selectVirtualOrderByCode(orderCodes);
                        for (String virtualOrderCode : virtualOrderByCode) {
                            ResultUtil resultUtil = ResultUtil.error("企业正在操作中，请稍后再试");
                            resultUtil.setData(virtualOrderCode);
                            resultUtils.add(resultUtil);
                        }
                    }
                    ResultUtil error = ResultUtil.error();
                    error.setData(resultUtils);
                    return error;
                }
                return ResultUtil.ok(resultUtils);

            } catch (Exception e) {
                ResultUtil resultUtil = ResultUtil.error();
                log.error("ZJJ-013:打包运单召回失败!", e);
                String message = e.getMessage();
                if (StringUtils.checkChineseCharacter(message)) {
                    resultUtil.setMsg(message);
                    return resultUtil;
                }
                message = "ZJJ-013:打包运单召回失败!";
                resultUtil.setMsg(message);
                return resultUtil;
            } finally {
                /**
                 * 11月12日 修改加解锁方式
                 */
                if (walletLock && redisUtil.hasKey(walletKey)) {
                    redisUtil.del(walletKey);
                }
            }
        }
        return ResultUtil.ok();
    }

    /**
     * @Description 检查车辆司机信息是否正确：如id不能为0
     * <AUTHOR>
     * @Date 2019/6/29 11:48
     * @Param
     * @Return
     * @Exception
     */
    public List<CarDriverRelVO> checkCarAndUser(List<CarDriverRelVO> carDriverRelVOList) {
        List<CarDriverRelVO> carDriverRelVOS = new ArrayList<>();
        for (CarDriverRelVO carDriverRelVO : carDriverRelVOList) {
            if (null != carDriverRelVO.getEndDriverId() && null != carDriverRelVO.getEndcarId()
                    && carDriverRelVO.getEndDriverId() != 0 && carDriverRelVO.getEndcarId() != 0) {
                carDriverRelVOS.add(carDriverRelVO);
            }
        }
        return carDriverRelVOS;
    }

    /**
     * @Description 发单检查是否有重复的车辆和司机
     * <AUTHOR>
     * @Date 2019/6/27 11:22
     * @Param
     * @Return
     * @Exception
     */
    public String checkRepeatCarUser(List<CarDriverRelVO> record) {
        List<Integer> cars = new ArrayList<>();
        List<Integer> users = new ArrayList<>();
        StringBuffer errorCarUser = new StringBuffer();

        for (CarDriverRelVO carDriverRelVO : record) {
            if (null == carDriverRelVO.getEndcarId()) {
                errorCarUser.append("车辆信息不全");
                return errorCarUser.toString();
            }
            if (null == carDriverRelVO.getEndDriverId()) {
                errorCarUser.append("司机信息不全");
                return errorCarUser.toString();
            }
            if (cars.contains(carDriverRelVO.getEndcarId())) {
                errorCarUser.append(carDriverRelVO.getVehicleNumber() + ":车辆已选择,");
            } else {
                cars.add(carDriverRelVO.getEndcarId());
            }
            if (users.contains(carDriverRelVO.getEndDriverId())) {
                errorCarUser.append(carDriverRelVO.getRealName() + "司机已选择,");
            } else {
                users.add(carDriverRelVO.getEndDriverId());
            }
        }
        //去除最后一个','
        if (errorCarUser.length() > 0) {
            boolean endsWith = errorCarUser.toString().endsWith(",");
            if (endsWith) {
                errorCarUser.deleteCharAt(errorCarUser.length() - 1);
            }
        }
        return errorCarUser.toString();
    }

    /**
     * 生成运单详情
     *
     * @param companySourceDTO 运单资源信息、线路信息
     * @param sendOrderVO      发单参数
     * @return
     * <AUTHOR>
     */
    private TOrderInfo createOrderInfo(CompanySourceDTO companySourceDTO, LineInfoPrincipalDTO
            receiver, SendOrderVO sendOrderVO) {
        TOrderInfo orderInfo = new TOrderInfo();
        //承运方
        orderInfo.setCarrierId(sendOrderVO.getCarrierId());
        //运单资源信息表业务ID
        orderInfo.setGoodSourceCode(companySourceDTO.getCode());
        orderInfo.setLineGoodsRelId(companySourceDTO.getLineGoodsRelId());
        orderInfo.setCompanyId(sendOrderVO.getCompanyId());
        orderInfo.setCompanyProjectId(companySourceDTO.getProjectId());
        //货物相关信息
        orderInfo.setGoodsId(companySourceDTO.getGoodsId());
        orderInfo.setGoodsName(companySourceDTO.getGoodsName());
        orderInfo.setBigKindCode(companySourceDTO.getBigKindCode());
        orderInfo.setGoodsUnit(companySourceDTO.getGoodsUnit());
        //orderInfo.setEstimateGoodsWeight(sendOrderVO.getEstimateGoodsWeight());
        orderInfo.setCurrentCarriageUnitPrice(new BigDecimal(String.valueOf(sendOrderVO.getCurrentCarriageUnitPrice())).setScale(2, RoundingMode.HALF_UP));
        orderInfo.setPrimaryWeight(sendOrderVO.getEstimateGoodsWeight());
        //运费单价单位为“元/吨”时，与原有页面逻辑一致
        if(null!=sendOrderVO.getCarriagePriceUnit() && DictEnum.CARRIAGE_PRICE_UNIT_CAR.code.equals(sendOrderVO.getCarriagePriceUnit())){
            orderInfo.setEstimateTotalFee(orderInfo.getCurrentCarriageUnitPrice());
        }else if(null!=sendOrderVO.getCarriagePriceUnit() && DictEnum.CARRIAGE_PRICE_UNIT_BOX.code.equals(sendOrderVO.getCarriagePriceUnit())){
            Integer boxNum = sendOrderVO.getOrderInfoWeight().getBoxNum();
            orderInfo.setEstimateTotalFee(BigDecimal.valueOf(boxNum).multiply(companySourceDTO.getCurrentCarriageUnitPrice()).setScale(2, RoundingMode.HALF_UP));
            orderInfo.setPrimaryWeight(BigDecimal.valueOf(sendOrderVO.getEstimateGoodsWeight()).multiply(BigDecimal.valueOf(boxNum)).doubleValue());
        }else{
            //运费总估计
            BigDecimal estimateTotalFee = BigDecimal.valueOf(companySourceDTO.getEstimateGoodsWeight()).multiply(companySourceDTO.getCurrentCarriageUnitPrice());
            orderInfo.setEstimateTotalFee(estimateTotalFee.setScale(2, RoundingMode.HALF_UP));
        }
        //线路相关信息
        orderInfo.setLineId(companySourceDTO.getLineId());
        orderInfo.setLineCode(companySourceDTO.getLineCode());
        orderInfo.setLineName(companySourceDTO.getLineName());
        orderInfo.setLineShortName(companySourceDTO.getLineShortName());
        orderInfo.setCityFromCode(companySourceDTO.getCityFromCode());
        orderInfo.setFromName(companySourceDTO.getFromName());
        orderInfo.setFromCoordinates(companySourceDTO.getFromCoordinates());
        orderInfo.setCityEndCode(companySourceDTO.getCityEndCode());
        orderInfo.setEndName(companySourceDTO.getEndName());
        orderInfo.setEndCoordinates(companySourceDTO.getEndCoordinates());
        orderInfo.setLineType(companySourceDTO.getLineType());

        //发单员id、发单联系人、手机号(线路负责人)

        orderInfo.setDeliverOrderUserId(CurrentUser.getUserAccountId());
        orderInfo.setDeliverGoodsContacter(CurrentUser.getUserNickname());
        orderInfo.setDeliverGoodsContacterPhone(CurrentUser.getUserAccountNo());
        //收单员id、收单联系人、手机号(线路负责人)
        orderInfo.setReceiveOrderUserId(receiver.getPrincipalAccountId());
        orderInfo.setReceiveGoodsContacter(receiver.getPrincipalName());
        orderInfo.setReceiveGoodsContacterPhone(receiver.getPrincipalPhone());

        //TODO 委托方、客户名称
        orderInfo.setCompanyClient(sendOrderVO.getCompanyClient());
        orderInfo.setCompanyEntrust(sendOrderVO.getCompanyEntrust());

        //委托发单员账号id -> 经纪人
        //费用结算模式 add by zhangjiji 2019/6/4
        orderInfo.setFeeSettlementWay(companySourceDTO.getPayMethod());
        //运单生成类型: 人工生成

        orderInfo.setOrderCreateType(sendOrderVO.getOrderCreateType());
        //货源执行状态： 节点 -> 已建单0
        orderInfo.setOrderExecuteStatus(sendOrderVO.getOrderExecuteStatus());
        orderInfo.setContractStatus(DictEnum.WQD.code);
        //打包状态0
        orderInfo.setPackStatus("0");
        orderInfo.setRemark(sendOrderVO.getRemark());
        orderInfo.setDeliverOrderTime(new Date());
        orderInfo.setCreateUser(CurrentUser.getUserNickname());
        orderInfo.setUpdateUser(CurrentUser.getUserNickname());
        orderInfo.setPayMethod(companySourceDTO.getCarriagePayType());

        //从企业项目与承运方关系表获取调度费系数
        TProjectCarrierInfoVO vo = new TProjectCarrierInfoVO();
        vo.setCarrierId(orderInfo.getCarrierId());
        vo.setCompanyId(orderInfo.getCompanyId());
        vo.setProjectId(orderInfo.getCompanyProjectId());
        TProjectCarrierRel tProjectCarrierRel = projectCarrierAPI.selectDispatchFeeCoefficient(vo);
        if (null == tProjectCarrierRel) {
            log.error("错误码：ZYH015，未获取到调度费系数");
            throw new RuntimeException("错误码：ZYH015，请联系运营平台予以解决。");
        }

        BigDecimal dispatchFee = OrderMoneyUtil.getDispatchFee(tProjectCarrierRel.getDispatchFeeCoefficient(), orderInfo.getEstimateTotalFee());
        orderInfo.setDispatchFee(dispatchFee);
        orderInfo.setTotalFee(orderInfo.getEstimateTotalFee().add(dispatchFee));
        //如果是经纪人模式
        if (null != companySourceDTO.getCapitalTransferPattern()
                && DictEnum.MANAGERPATTERN.code.equals(companySourceDTO.getCapitalTransferPattern())) {
            if (null != companySourceDTO.getManagerId()) {
                orderInfo.setAgentId(companySourceDTO.getManagerId());
            }
        }
        return orderInfo;
    }

    /*
     * <AUTHOR>
     * @Description  计划发单
     * @Date 2019/10/28 15:18
     * @Param
     * @return
     **/
    @PostMapping("/planOrder")
    public ResultUtil planOrder(@RequestBody SendOrderVO sendBillVO) {
        String vehicleNumber = "";
        try {
            ResultUtil error = ResultUtil.error();
            List<CarDriverRelVO> carDriverRelVOList = sendBillVO.getCarDriverRelVOList();
            List<CarDriverRelVO> carDriverRelVOS = checkCarAndUser(carDriverRelVOList);
            if (null == carDriverRelVOS || carDriverRelVOS.size() == 0) {
                return ResultUtil.error("请选择车辆司机");
            }
            CarDriverRelVO carDriverRelVO = carDriverRelVOS.get(0);
            error.setData(carDriverRelVO.getVehicleNumber());
            vehicleNumber = carDriverRelVO.getVehicleNumber();
            double carriageUnitPrice = null == sendBillVO.getCurrentCarriageUnitPrice() ? 0D : sendBillVO.getCurrentCarriageUnitPrice();
            double estimateGoodsWeight = null == sendBillVO.getEstimateGoodsWeight() ? 0D : sendBillVO.getEstimateGoodsWeight();
            if (carriageUnitPrice * estimateGoodsWeight == 0D) {
                error.setMsg("运费不合法，无法发单");
                return error;
            }
            if (carriageUnitPrice > 99999.99) {
                error.setMsg("请输入1-99999");
                return error;
            }
            if (estimateGoodsWeight > 99999.99) {
                error.setMsg("请输入1-99999");
                return error;
            }
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

            //检查车辆和司机信息是否完整，车辆司机信息是否重复 add zhangjiji 2019/6/27
            String errorCarUser = checkRepeatCarUser(carDriverRelVOS);
            if (StringUtils.isNotEmpty(errorCarUser)) {
                return ResultUtil.error(errorCarUser);
            }

            //承运方与企业合作的项目是否开启
            TProjectCarrierRel projectCarrierRel = new TProjectCarrierRel();
            projectCarrierRel.setCompanyId(sendBillVO.getCompanyId());
            projectCarrierRel.setProjectId(sendBillVO.getProjectId());
            projectCarrierRel.setIfEfficient(false);
            ResultUtil selectProjectCarrier = projectCarrierAPI.selectProjectCarrier(projectCarrierRel);
            if (null != selectProjectCarrier.getCode() && selectProjectCarrier.getCode().equals("success")) {
                ArrayList data = (ArrayList) selectProjectCarrier.getData();
                if (null != data) {
                    if (data.size() == 0) {
                        error.setMsg("请选择其他货源进行发单操作，或联系企业管理员重新开启该货源。");
                        ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                        exceptionSendOrder.setCount(1);
                        exceptionSendOrder.setIsLogic(true);
                        exceptionSendOrder.setMessage("请选择其他货源进行发单操作，或联系企业管理员重新开启该货源。");
                        HashMap<String, Object> result = new HashMap<>();
                        result.put("exceptionSendOrder", exceptionSendOrder);
                        error.setData(result);
                        return error;
                    }
                    if (data.size() > 1) {
                        log.error("ZJJ-001：发单失败，请联系运营平台予以解决。");
                        error.setMsg("ZJJ-001：发单失败，请联系运营平台予以解决。");
                        ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                        exceptionSendOrder.setCount(2);
                        exceptionSendOrder.setIsLogic(true);
                        exceptionSendOrder.setMessage("ZJJ-001：发单失败，请联系运营平台予以解决。");
                        HashMap<String, Object> result = new HashMap<>();
                        result.put("exceptionSendOrder", exceptionSendOrder);
                        error.setData(result);
                        return error;
                    }
                    LinkedHashMap projectCarrier = (LinkedHashMap) data.get(0);
                    Integer carrierId = (Integer) projectCarrier.get("carrierId");
                    sendBillVO.setCarrierId(carrierId);
                } else {
                    log.error("请选择其他货源进行发单操作，或联系企业管理员重新开启该货源。");
                    error.setMsg("请选择其他货源进行发单操作，或联系企业管理员重新开启该货源。");
                    ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                    exceptionSendOrder.setCount(1);
                    exceptionSendOrder.setIsLogic(true);
                    HashMap<String, Object> result = new HashMap<>();
                    result.put("exceptionSendOrder", exceptionSendOrder);
                    error.setData(result);
                    return error;
                }
            }

            ResultUtil carrierResult = carrierService.selectById(String.valueOf(sendBillVO.getCarrierId()));
            LinkedHashMap carrier = (LinkedHashMap) carrierResult.getData();

            Integer goodsSourceInfoId = sendBillVO.getGoodsSourceInfoId();
            //查询运单资源
            ResultUtil goodsSourceAndLineInfo = goodsSourceAPI.selectGoodsSourceAndLineInfo(goodsSourceInfoId);
            if (null != goodsSourceAndLineInfo) {
                if (null != goodsSourceAndLineInfo.getCode() && StringUtils.isNotEmpty(goodsSourceAndLineInfo.getCode())
                        && "error".equals(goodsSourceAndLineInfo.getCode())) {
                    String msg = null != goodsSourceAndLineInfo.getMsg() ? goodsSourceAndLineInfo.getMsg() : "获取货源信息失败";
                    error.setMsg(msg);
                    HashMap<String, Object> result = new HashMap<>();
                    ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                    exceptionSendOrder.setCount(1);
                    exceptionSendOrder.setIsLogic(false);
                    result.put("exceptionSendOrder", exceptionSendOrder);
                    error.setData(result);
                    return error;
                }
            }
            LinkedHashMap goodsSourceAndLineInfoData = (LinkedHashMap) goodsSourceAndLineInfo.getData();
            LinkedHashMap source = (LinkedHashMap) goodsSourceAndLineInfoData.get("source");
            CompanySourceDTO companySourceDTO = objectMapper.convertValue(source, CompanySourceDTO.class);
            LinkedHashMap receivePrincipal = (LinkedHashMap) goodsSourceAndLineInfoData.get("receivePrincipal");
            LineInfoPrincipalDTO receiver = objectMapper.convertValue(receivePrincipal, LineInfoPrincipalDTO.class);

            String lineType = companySourceDTO.getLineType();
            if (null != receiver) {
                if (null == receiver.getPrincipalAccountId() || null == receiver.getPrincipalName() || null == receiver.getPrincipalPhone()) {
                    error.setMsg("当前货源无收单员，请先联系企业管理员或运营平台进行维护。");
                    ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                    exceptionSendOrder.setCount(1);
                    exceptionSendOrder.setIsLogic(false);
                    error.setData(exceptionSendOrder);
                    return error;
                }
            }

            //TODO 生成运单详情 TOrderInfo
            sendBillVO.setOrderCreateType(DictEnum.PLANSEND.code);
            sendBillVO.setOrderExecuteStatus(DictEnum.M010.code);

            //资金转移方式
            String capitalTransferType = sendBillVO.getCapitalTransferType();
            ResultUtil resultUtil = ResultUtil.ok();
            HashMap<String, Object> result = new HashMap<>();

            String carKey = "CAR" + carDriverRelVO.getEndcarId();
            String userKey = "USER" + carDriverRelVO.getEndDriverId();
            boolean carLock = RedissLockUtil.tryLock(carKey, 5 * 5 * 60, 20);
            boolean userLock = RedissLockUtil.tryLock(userKey, 5 * 5 * 60, 20);
            /*String projectKey = "Project" + sendBillVO.getProjectId();
            boolean projectLock = RedissLockUtil.tryLock(projectKey, 5 * 5 * 60, 20);*/
            try {
                IdWorkerUtil instance = IdWorkerUtil.getInstance();
                if (carLock && userLock) {
                    if (null != sendBillVO.getOperateType() && sendBillVO.getOperateType().equals("edit")) {
                        TOrderInfo orderInfo = orderInfoService.selectOrderInfoByCode(sendBillVO.getOrderCode());
                        resultUtil = appOrderService.editOrderInfo(companySourceDTO, receiver, orderInfo, carDriverRelVO,
                                sendBillVO, carrier);
                    } else {
                        TOrderInfo orderInfo = createOrderInfo(companySourceDTO, receiver, sendBillVO);
                        resultUtil = appOrderService.planOrder(instance.nextId(), sendBillVO, carDriverRelVO, orderInfo,
                                lineType, capitalTransferType, carrier, companySourceDTO);
                    }
                }
                result.put("vehicleNumber", carDriverRelVO.getVehicleNumber());
                resultUtil.setData(result);
            } catch (Exception e) {
                e.printStackTrace();
                resultUtil.setCode("error");
                result.put("vehicleNumber", carDriverRelVO.getVehicleNumber());
                String message = e.getMessage();
                if (null != message && StringUtils.checkChineseCharacter(message)) {
                    ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                    exceptionSendOrder.setCount(1);
                    exceptionSendOrder.setIsLogic(false);
                    result.put("exceptionSendOrder", exceptionSendOrder);
                    resultUtil.setMsg(message);
                } else {
                    ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                    exceptionSendOrder.setCount(2);
                    exceptionSendOrder.setIsLogic(true);
                    result.put("exceptionSendOrder", exceptionSendOrder);
                    resultUtil.setMsg("ZJJ-001：发单失败!");
                }
                resultUtil.setData(result);
            } finally {
                if (carLock) {
                    RedissLockUtil.unlock(carKey);
                }
                if (userLock) {
                    RedissLockUtil.unlock(userKey);
                }
                /*if (projectLock) {
                    RedissLockUtil.unlock(projectKey);
                }*/
            }
            return resultUtil;
        } catch (Exception e) {
            log.error("ZJJ-001：发单失败!", e);
            HashMap<String, Object> result = new HashMap<>();
            ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
            exceptionSendOrder.setCount(2);
            exceptionSendOrder.setIsLogic(true);
            result.put("exceptionSendOrder", exceptionSendOrder);
            result.put("vehicleNumber", vehicleNumber);
            ResultUtil error = ResultUtil.error();
            error.setData(result);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                error.setMsg(message);
                error.setData(result);
                return error;
            }
            return ResultUtil.error("ZJJ-001：发单失败!");
        }
    }

    /*
     * <AUTHOR>
     * @Description 立即发单
     * @Date 2019/10/28 15:19
     * @Param
     * @return
     **/
    @PostMapping("/sendPlanOrder")
    public ResultUtil sendPlanOrder(@RequestBody SendOrderVO sendOrderVO) {
        ResultUtil error = ResultUtil.error();
        HashMap<String, Object> result = new HashMap<>();
        TOrderInfo torderInfo = orderInfoService.selectOrderInfoByCode(sendOrderVO.getOrderCode());

        String carKey = "CAR" + torderInfo.getVehicleId();
        String userKey = "USER" + torderInfo.getEndDriverId();
        String orderKey = "Order" + torderInfo.getCode();
        boolean carLock = RedissLockUtil.tryLock(carKey, 5 * 5 * 60, 50);
        boolean userLock = RedissLockUtil.tryLock(userKey, 5 * 5 * 60, 50);
        boolean orderLock = RedissLockUtil.tryLock(orderKey, 5 * 5 * 50, 20);
        ResultUtil resultUtil = ResultUtil.ok();

        try {
            if (orderLock && carLock && userLock) {
                TOrderInfo orderInfo = orderInfoService.selectOrderInfoByCode(sendOrderVO.getOrderCode());
                if (null != torderInfo && null != torderInfo.getOrderExecuteStatus()) {
                    if (orderInfo.getOrderExecuteStatus().equals(DictEnum.M010.code)) {
                        TProjectCarrierRel projectCarrierRel = new TProjectCarrierRel();
                        projectCarrierRel.setCompanyId(orderInfo.getCompanyId());
                        projectCarrierRel.setProjectId(orderInfo.getCompanyProjectId());
                        projectCarrierRel.setIfEfficient(false);
                        ResultUtil selectProjectCarrier = projectCarrierAPI.selectProjectCarrier(projectCarrierRel);
                        if (null != selectProjectCarrier.getCode() && selectProjectCarrier.getCode().equals("success")) {
                            ArrayList data = (ArrayList) selectProjectCarrier.getData();
                            if (null != data) {
                                if (data.size() == 0) {
                                    error.setMsg("请选择其他货源进行发单操作，或联系企业管理员重新开启该货源。");
                                    ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                                    exceptionSendOrder.setCount(1);
                                    exceptionSendOrder.setIsLogic(true);
                                    exceptionSendOrder.setMessage("请选择其他货源进行发单操作，或联系企业管理员重新开启该货源。");
                                    result.put("exceptionSendOrder", exceptionSendOrder);
                                    error.setData(result);
                                    return error;
                                }
                                if (data.size() > 1) {
                                    log.error("ZJJ-001：发单失败，请联系运营平台予以解决。");
                                    error.setMsg("ZJJ-001：发单失败，请联系运营平台予以解决。");
                                    ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                                    exceptionSendOrder.setCount(2);
                                    exceptionSendOrder.setIsLogic(true);
                                    exceptionSendOrder.setMessage("ZJJ-001：发单失败，请联系运营平台予以解决。");
                                    result.put("exceptionSendOrder", exceptionSendOrder);
                                    error.setData(result);
                                    return error;
                                }
                                LinkedHashMap projectCarrier = (LinkedHashMap) data.get(0);
                                Integer carrierId = (Integer) projectCarrier.get("carrierId");
                                sendOrderVO.setCarrierId(carrierId);
                            } else {
                                log.error("请选择其他货源进行发单操作，或联系企业管理员重新开启该货源。");
                                error.setMsg("请选择其他货源进行发单操作，或联系企业管理员重新开启该货源。");
                                ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                                exceptionSendOrder.setCount(1);
                                exceptionSendOrder.setIsLogic(true);
                                result.put("exceptionSendOrder", exceptionSendOrder);
                                error.setData(result);
                                return error;
                            }
                        }

                        ObjectMapper objectMapper = new ObjectMapper();
                        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

                        //TODO
                        Integer goodsSourceInfoId = sendOrderVO.getGoodsSourceInfoId();
                        //查询运单资源
                        ResultUtil goodsSourceAndLineInfo = goodsSourceAPI.selectGoodsSourceAndLineInfo(goodsSourceInfoId);
                        if (null != goodsSourceAndLineInfo) {
                            if (null != goodsSourceAndLineInfo.getCode() && StringUtils.isNotEmpty(goodsSourceAndLineInfo.getCode())
                                    && "error".equals(goodsSourceAndLineInfo.getCode())) {
                                String msg = null != goodsSourceAndLineInfo.getMsg() ? goodsSourceAndLineInfo.getMsg() : "获取货源信息失败";
                                error.setMsg(msg);
                                ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                                exceptionSendOrder.setCount(1);
                                exceptionSendOrder.setIsLogic(false);
                                result.put("exceptionSendOrder", exceptionSendOrder);
                                error.setData(result);
                                return error;
                            }
                        }
                        LinkedHashMap goodsSourceAndLineInfoData = (LinkedHashMap) goodsSourceAndLineInfo.getData();
                        LinkedHashMap receivePrincipal = (LinkedHashMap) goodsSourceAndLineInfoData.get("receivePrincipal");
                        LineInfoPrincipalDTO receiver = objectMapper.convertValue(receivePrincipal, LineInfoPrincipalDTO.class);
                        LinkedHashMap source = (LinkedHashMap) goodsSourceAndLineInfoData.get("source");
                        CompanySourceDTO companySourceDTO = objectMapper.convertValue(source, CompanySourceDTO.class);

                        ResultUtil carrierResult = carrierService.selectById(String.valueOf(sendOrderVO.getCarrierId()));
                        LinkedHashMap carrier = (LinkedHashMap) carrierResult.getData();
                        if (null != carrier.get("platformDid")) {
                            companySourceDTO.setPlatformDid(String.valueOf(carrier.get("platformDid")));
                        }

                        if (null != receiver) {
                            if (null == receiver.getPrincipalAccountId() || null == receiver.getPrincipalName() || null == receiver.getPrincipalPhone()) {
                                error.setMsg("当前货源无收单员，请先联系企业管理员或运营平台进行维护。");
                                ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                                exceptionSendOrder.setCount(1);
                                exceptionSendOrder.setIsLogic(false);
                                error.setData(exceptionSendOrder);
                                return error;
                            }
                        }

                        result.put("vehicleNumber", sendOrderVO.getVehicleNumber());
                        // 查询司机车辆发单数超过1单时，司机车辆审核状态
                        CarDriverRelVO carDriverRelVO = sendOrderVO.getCarDriverRelVO();
                        String checkDriverCarAuditStatusByTimes = sendOrderUtil.checkDriverCarAuditStatusByTimes(carDriverRelVO, false);
                        if (null != checkDriverCarAuditStatusByTimes) {
                            resultUtil.setMsg(checkDriverCarAuditStatusByTimes);
                            if (checkDriverCarAuditStatusByTimes.contains("实名认证")) {
                                resultUtil.setData(DictEnum.DRIVER.code);
                            } else {
                                resultUtil.setData(DictEnum.CAR.code);
                            }
                            resultUtil.setCode(CodeEnum.ERROR.getCode());
                        } else {
                            resultUtil = appOrderService.sendPlanOrder(companySourceDTO, receiver, orderInfo, sendOrderVO, carrier);
                        }
                        return resultUtil;
                    } else {
                        return ResultUtil.error("运单状态错误，不可发单");
                    }
                }
            }
        } catch (Exception e) {
            resultUtil.setCode("error");
            String message = e.getMessage();
            if (null != message && StringUtils.checkChineseCharacter(message)) {
                ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                exceptionSendOrder.setCount(1);
                exceptionSendOrder.setIsLogic(false);
                result.put("exceptionSendOrder", exceptionSendOrder);
                resultUtil.setMsg(message);
            } else {
                ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                exceptionSendOrder.setCount(2);
                exceptionSendOrder.setIsLogic(true);
                result.put("exceptionSendOrder", exceptionSendOrder);
                resultUtil.setMsg("ZJJ-001：发单失败!");
            }
            resultUtil.setData(result);
            return resultUtil;
        } finally {
            if (carLock) {
                RedissLockUtil.unlock(carKey);
            }
            if (userLock) {
                RedissLockUtil.unlock(userKey);
            }
            if (orderLock) {
                RedissLockUtil.unlock(orderKey);
            }
        }

        result.put("vehicleNumber", sendOrderVO.getVehicleNumber());
        resultUtil.setData(result);
        return resultUtil;
    }

    /*
     * <AUTHOR>
     * @Description 创建发单模板
     * @Date 2019/10/28 15:19
     * @Param
     * @return
     **/
    @PostMapping("/createOrderTemplate")
    public ResultUtil createOrderTemplate(@RequestBody SendOrderVO sendOrderVO) {
        try {
            ResultUtil orderTemplate = appOrderService.createOrderTemplate(sendOrderVO);
            return orderTemplate;
        } catch (Exception e) {
            log.error("ZJJ-053：创建发单模板失败!", e);
            ResultUtil resultUtil = ResultUtil.error();
            String message = e.getMessage();
            resultUtil.setMsg("ZJJ-053：创建发单模板失败!");
            if (null != message && StringUtils.checkChineseCharacter(message)) {
                resultUtil.setMsg(message);
            }
            return resultUtil;
        }
    }

    /*
     * <AUTHOR>
     * @Description 删除运单模板
     * @Date 2019/10/28 15:21
     * @Param
     * @return
     **/
    @PostMapping("/deleteOrderTemplate")
    public ResultUtil deleteOrderTemplate(@RequestBody SendOrderVO sendOrderVO) {
        ResultUtil resultUtil = appOrderService.deleteOrderTemplate(sendOrderVO);
        return resultUtil;
    }


    /**
     * @Description
     * <AUTHOR>
     * @Date 2019/11/4 11:39
     * @Param code 运单表业务code
     * @Return
     * @Exception 运单详情
     */
    @PostMapping("/getOrderInfo")
    public ResultUtil getOrderInfo(@RequestBody TOrderInfo tOrderInfo) {
        ResultUtil resultUtil = new ResultUtil();
        try {
            resultUtil.setCode("success");
            resultUtil.setData(orderInfoService.getOrderInfo(tOrderInfo.getCode()));
            return resultUtil;
        } catch (Exception e) {
            log.error("获取计划发单详情失败！", e);
            resultUtil.setCode("error");
            return resultUtil;
        }
    }


    @RequestMapping("/advancePayment")
    public ResultUtil advancePayment() {
        TOrderInfo tOrderInfo = orderInfoService.selectOrderInfoByCode("12321739651838074882020022513226");
        Payment payment = new Payment();
        payment.advancePayment(tOrderInfo);
        return ResultUtil.ok();
    }

    /**
     * 发送取消运单验证码
     * smsType：RESOURCEHALLCANCEL
     *
     * @return
     * @auth liuxin
     */
    @PostMapping("/cancelResourceHallOrderSms")
    public ResultUtil sendCancelOrderSms(@RequestBody TOrderInfoVO orderInfoVO) {
        try {
            ResultUtil resultUtil = ResultUtil.ok();
            resultUtil.setCode("success");
            if (StringUtils.isNotBlank(orderInfoVO.getPhone())) {
                if (StringUtils.isNotBlank(orderInfoVO.getCode())) {
                    TOrderInfo orderInfo = orderInfoService.selectOrderInfoByCode(orderInfoVO.getCode());
                    if (null != orderInfo) {
                        if (orderInfo.getOrderExecuteStatus().equals(DictEnum.M011.code)) {
                            try {
                                SmsReq req = new SmsReq();
                                req.setOrderBusinessCode(orderInfo.getOrderBusinessCode());
                                req.setMobiles(orderInfoVO.getPhone());
                                if (null == orderInfoVO.getSmsType() || StringUtils.isEmpty(orderInfoVO.getSmsType())) {
                                    req.setType("PCDELORDER");
                                } else if ("RESOURCEHALLCANCEL".equals(orderInfoVO.getSmsType())) {
                                    req.setType(orderInfoVO.getSmsType());
                                }
                                req.setOrderBusinessCode(orderInfo.getOrderBusinessCode());
                                smsClientService.sendSmsType(req);
                            } catch (Exception e) {
                                log.info("-----orderCode---------类型：" + orderInfoVO.getSmsType() + "--" + orderInfoVO.getOrderBusinessCode());
                                resultUtil.setCode("error");
                                resultUtil.setMsg(e.getMessage());
                            }
                        } else {
                            return ResultUtil.error("运单状态错误，不可取消");
                        }
                    } else {
                        return ResultUtil.error("运单未找到");
                    }
                } else {
                    return ResultUtil.error("运单未找到");
                }
            } else {
                return ResultUtil.error("手机号不存在");
            }
            return resultUtil;
        } catch (Exception e) {
            log.error("发送取消运单验证码失败! {}", ThrowableUtil.getStackTrace(e));
            return ResultUtil.error("发送取消运单验证码失败!");
        }
    }

    /**
     * 取消运单
     *
     * @return
     * <AUTHOR>
     */
    @PostMapping("/cancelResourceHallOrder")
    public ResultUtil cancelResourceHallOrder(@RequestBody TOrderInfoVO orderInfoVO) {
        try {
            if (StringUtils.isBlank(orderInfoVO.getRemark())) {
                return ResultUtil.error("备注不能为空");
            }
            ResultUtil resultUtil = ResultUtil.ok();
            TOrderInfo orderInfo = orderInfoService.selectOrderInfoByCode(orderInfoVO.getCode());
            String orderKey = "Order" + orderInfo.getOrderBusinessCode();
            boolean orderLock = RedissLockUtil.tryLock(orderKey, 1, 20);
            TGoodsSourceInfo tGoodsSourceInfo = orderGoodsSourceInfoService.selectByByLineGoodsRelId(orderInfo.getLineGoodsRelId());
            String resourceKey = GOODS_SOURCE + tGoodsSourceInfo.getId();
            boolean resourceLock = RedissLockUtil.tryLock(resourceKey, 1, 20);
            try {
                if (orderLock && resourceLock) {
                    String verificationCode = orderInfoVO.getCancelHallVerificationCode();
                    if (StringUtils.isNotEmpty(orderInfoVO.getSmsType()) && orderInfoVO.getSmsType().equals("RESOURCEHALLCANCEL")) {
                        String smsCode = String.valueOf(redisUtil.get("RESOURCEHALLCANCEL" + orderInfoVO.getCancelHallPersonPhone()));
                        if (verificationCode.equals(smsCode)) {
                            TVerificationCodeLog tVerificationCodeLog = new TVerificationCodeLog();
                            tVerificationCodeLog.setReceivePhoneno(orderInfoVO.getDeletePersonPhone());
                            tVerificationCodeLog.setVerificationCode(smsCode);
                            tVerificationCodeLogAPI.updateIfUsed(tVerificationCodeLog);
                            resultUtil = appOrderService.cancelResourceHallOrder(orderInfoVO);
                        } else if ("null".equals(smsCode)) {
                            resultUtil = ResultUtil.error("验证码已失效");
                        } else {
                            resultUtil = ResultUtil.error("验证码错误");
                        }
                    }
                    return resultUtil;
                }

            } catch (Exception e) {
                log.error("取消运单失败!, {}", ThrowableUtil.getStackTrace(e));
                String message = e.getMessage();
                if (StringUtils.checkChineseCharacter(message)) {
                    return ResultUtil.error(message);
                }
                return ResultUtil.error("取消运单失败!");
            } finally {
                if (orderLock) {
                    RedissLockUtil.unlock(orderKey);
                }
                if (resourceLock) {
                    RedissLockUtil.unlock(resourceKey);
                }
            }

        } catch (Exception e) {
            log.error("取消运单失败!, {}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("取消运单失败!");
        }
        return ResultUtil.ok();
    }

}
