package com.lz.mq.handler;

import cn.hutool.json.JSONUtil;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.order.ConsumeOrderContext;
import com.aliyun.openservices.ons.api.order.MessageOrderListener;
import com.aliyun.openservices.ons.api.order.OrderAction;
import com.lz.common.constants.MqMessageTag;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.util.StringUtils;
import com.lz.request.ThirdPartyOrderPayRequest;
import com.lz.response.ThirdPartyOrderFileResponse;
import com.lz.schedule.TTaskVO;
import com.lz.schedule.model.TTask;
import com.lz.service.TOrderTaskServie;
import com.lz.service.thirdparty.ThirdPartyOrderPayService;
import com.lz.vo.ThirdPayVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class ThirdPartyOrderListener implements MessageOrderListener {

    @Resource
    private ThirdPartyOrderPayService thirdPartyOrderPayService;

    @Resource
    private TOrderTaskServie orderTaskServie;


    @Override
    public OrderAction consume(Message message, ConsumeOrderContext consumeContext) {
        try {
            log.info("第三方平台运单, {}", JSONUtil.toJsonStr(message));
            String tag = message.getTag();
            String body = new String(message.getBody());
            log.info("第三方平台运单, tag: {}, body: {}", tag, body);
            if (MqMessageTag.THIRD_PARTY_ORDER_PAY.equals(tag)) {
                ThirdPartyOrderPayRequest request = JSONUtil.toBean(body, ThirdPartyOrderPayRequest.class);
                log.info("第三方平台运单支付, {}", JSONUtil.toJsonStr(request));
                if (null != request.getRequestNo() && StringUtils.isNotBlank(request.getRequestNo())) {
                    TTaskVO taskVO = new TTaskVO();
                    taskVO.setTaskId(request.getRequestNo());
                    taskVO.setTaskType("ORDER");
                    taskVO.setBusinessType("THIRD_PARTY");
                    taskVO.setTaskTypeNode(Collections.singletonList("PAY"));
                    taskVO.setRequestTimes(1);
                    taskVO.setLimit(5);
                    List<TTask> tTasks = orderTaskServie.selectTaskList(taskVO);
                    for (TTask task : tTasks) {
                        ThirdPayVO thirdPayVO = JSONUtil.toBean(task.getRequestParameter(), ThirdPayVO.class);
                        if (null != thirdPayVO.getOrderId() && null != thirdPayVO.getOrderBusinessCode() && null != thirdPayVO.getNotifyUrl()
                                && StringUtils.isNotBlank(thirdPayVO.getOrderBusinessCode()) && StringUtils.isNotBlank(thirdPayVO.getNotifyUrl())) {
                            try {
                                thirdPartyOrderPayService.pay(task);
                            } catch (Exception e) {
                                log.error("第三方平台运单支付失败", e);
                                orderTaskServie.updateTask(task, false, e.getMessage());
                                task.setErrorMessage(e.getMessage());
                                orderTaskServie.insertErrorTask(task, DictEnum.HXRECEIPT.code, "5005");
                            }
                        } else {
                            log.error("第三方平台运单, 消息参数错误, {}", JSONUtil.toJsonStr(message));
                        }
                    }
                } else {
                    log.error("第三方平台运单, 消息参数错误, {}", JSONUtil.toJsonStr(message));
                }

            } else if (MqMessageTag.THIRD_PARTY_ORDER_RECEIPT.equals(tag)) {
                ThirdPartyOrderFileResponse fileResponse = JSONUtil.toBean(body, ThirdPartyOrderFileResponse.class);
                log.info("第三方平台运单电子回单, {}", JSONUtil.toJsonStr(fileResponse));
                thirdPartyOrderPayService.pushFile(fileResponse);
            } else if (MqMessageTag.THIRD_PARTY_ORDER_SKPZ.equals(tag)) {
                ThirdPartyOrderFileResponse fileResponse = JSONUtil.toBean(body, ThirdPartyOrderFileResponse.class);
                log.info("第三方平台运单收款凭证, {}", JSONUtil.toJsonStr(fileResponse));
                thirdPartyOrderPayService.pushFile(fileResponse);
            } else {
                log.error("第三方平台运单, 消息类型错误, {}", JSONUtil.toJsonStr(message));
            }
        } catch (Exception e) {
            log.error("第三方平台运单处理异常：", e);
        }



        return OrderAction.Success;
    }

}
