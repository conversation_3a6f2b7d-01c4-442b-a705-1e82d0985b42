package com.lz.mq.handler;

import cn.hutool.json.JSONUtil;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.lz.api.MqAPI;
import com.lz.common.config.RedisUtil;
import com.lz.common.constants.MqTagConstants;
import com.lz.common.constants.MqTopicConstants;
import com.lz.common.model.MQDelayMessage;
import com.lz.common.util.ThrowableUtil;
import com.lz.dao.TOrderPayRequestDetailMapper;
import com.lz.dto.TOrderPayRequestDTO;
import com.lz.dto.TOrderPayRequestDetailDTO;
import com.lz.vo.pay.PayMessageBody;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 信发支付请求处理
 */
@Slf4j
@Service
public class PayRequestListener implements MessageListener {

    private static final String PAY_REQUEST_KEY_OF_ORDER_ID = "PAY_REQUEST_KEY_OF_ORDER_ID";

    @Resource
    private TOrderPayRequestDetailMapper orderPayRequestDetailMapper;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private MqAPI mqAPI;

    @Resource
    private RedisUtil redisUtil;

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        log.info("发送承运方支付司机请求, {}", new String(message.getBody()));
        String tag = message.getTag();
        if (MqTagConstants.PAY_REQUEST.equals(tag)) {
            String body = new String(message.getBody());
            TOrderPayRequestDTO messageBody = JSONUtil.toBean(body, TOrderPayRequestDTO.class);
            String key = MqTopicConstants.PAY_REQUEST + messageBody.getPayRequestId();
            RLock lock = redissonClient.getLock(key);
            try {
                if (lock.tryLock()) {
                    List<TOrderPayRequestDetailDTO> details = orderPayRequestDetailMapper.selectByRequestId(messageBody.getPayRequestId());
                    if (!details.isEmpty()) {
                        for (TOrderPayRequestDetailDTO detail : details) {
                            if (redisUtil.hasKey(PAY_REQUEST_KEY_OF_ORDER_ID + detail.getOrderId())) {
                                log.info("当前运单支付开始处理, 请求已发送, {}", body);
                                continue;
                            }
                            MQDelayMessage mqDelayMessage = new MQDelayMessage();
                            mqDelayMessage.setTopic(MqTagConstants.SEND_PAY_REQUEST);
                            mqDelayMessage.setTag(MqTagConstants.SEND_PAY_REQUEST);
                            mqDelayMessage.setKey(String.valueOf(messageBody.getPayRequestId()));
                            PayMessageBody payMessageBody = new PayMessageBody();
                            payMessageBody.setOrderId(detail.getOrderId());
                            payMessageBody.setOrderPayRequestDetailId(detail.getId());
                            payMessageBody.setOperatorId(detail.getOperatorId());
                            payMessageBody.setCreateUser(detail.getCreateUser());
                            mqDelayMessage.setBody(payMessageBody);
                            mqDelayMessage.setStartDeliverTime(10L);
                            mqAPI.sendDelayMessage(mqDelayMessage);
                            redisUtil.set(PAY_REQUEST_KEY_OF_ORDER_ID + detail.getOrderId(), 1, 60 * 60 * 24);
                        }
                        if (details.size() > 1) {
                            // 继续发送
                            MQDelayMessage mqMessage = new MQDelayMessage();
                            mqMessage.setTopic(message.getTopic());
                            mqMessage.setTag(message.getTag());
                            mqMessage.setKey(message.getKey());
                            mqMessage.setBody(messageBody);
                            mqMessage.setStartDeliverTime(5 * 400);
                            mqAPI.sendDelayMessage(mqMessage);
                        } else {
                           log.info("requestId: {} 支付请求已处理完成。", messageBody.getPayRequestId());
                        }
                    } else {
                        log.info("支付请求列表为空, {}", messageBody.getPayRequestId());
                    }
                } else {
                    log.info("requestId: {} 支付请求未获取到锁, 重新发送。", messageBody.getPayRequestId());
                    // 继续发送
                    MQDelayMessage mqMessage = new MQDelayMessage();
                    mqMessage.setTopic(message.getTopic());
                    mqMessage.setTag(message.getTag());
                    mqMessage.setKey(message.getKey());
                    mqMessage.setBody(messageBody);
                    mqMessage.setStartDeliverTime(5 * 400);
                    mqAPI.sendDelayMessage(mqMessage);
                }
            } catch (Exception e) {
                log.error("requestId: {} 支付处理失败, {}", messageBody.getPayRequestId(), ThrowableUtil.getStackTrace(e));
            } finally {
                if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                    lock.unlock();
                }
            }
        }
        return Action.CommitMessage;
    }

}
