package com.lz.mq.handler;

import cn.hutool.json.JSONUtil;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.lz.api.MqAPI;
import com.lz.common.config.RedisUtil;
import com.lz.common.constants.MqTagConstants;
import com.lz.common.dbenum.BankNameEnum;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.model.MQDelayMessage;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.ThrowableUtil;
import com.lz.dao.TOrderInfoDetailMapper;
import com.lz.dao.TOrderInfoMapper;
import com.lz.dao.TOrderZtAccountOpenInfoMapper;
import com.lz.dto.OpenRoleStatusListDTO;
import com.lz.dto.OpenRoleWalletListDTO;
import com.lz.model.TOrderInfo;
import com.lz.model.TOrderInfoDetail;
import com.lz.model.TZtAccountOpenInfo;
import com.lz.payment.PaymentUtil;
import com.lz.service.pay.PayService;
import com.lz.vo.SelectOpenRoleVO;
import com.lz.vo.pay.PayMessageBody;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * 信发支付请求处理
 */
@Slf4j
@Service
public class SendPayRequestListener implements MessageListener {

    private static final String PAY_REQUEST_KEY = "PAY_REQUEST_KEY";

    @Resource
    private TOrderInfoMapper orderInfoMapper;

    @Resource
    private TOrderZtAccountOpenInfoMapper orderZtAccountOpenInfoMapper;

    @Resource
    private TOrderInfoDetailMapper orderInfoDetailMapper;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private MqAPI mqAPI;

    @Resource
    private PayService payService;

    @Resource
    private PaymentUtil paymentUtil;

    @Resource
    private RedisUtil redisUtil;

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String body = new String(message.getBody());
        PayMessageBody messageBody = JSONUtil.toBean(body, PayMessageBody.class);
        log.info("支付开始处理, {}", body);
        log.info("支付开始处理, 运单id: {}", messageBody.getOrderId());
        String tag = message.getTag();
        if (MqTagConstants.SEND_PAY_REQUEST.equals(tag)) {
            TOrderInfo tOrderInfo = orderInfoMapper.selectByPrimaryKey(messageBody.getOrderId());
            if (redisUtil.hasKey(PAY_REQUEST_KEY + tOrderInfo.getId())) {
                log.info("支付开始处理, 请求已存在, {}", body);
                return Action.CommitMessage;
            }
            TOrderInfoDetail tOrderInfoDetail = orderInfoDetailMapper.selectByOrderId(messageBody.getOrderId());
            boolean carrierWalletLock = true;
            RLock carrierLock = null;
            if (null != tOrderInfoDetail && tOrderInfoDetail.getIllegalCheck() && tOrderInfoDetail.getIllegalOrder()
                    && tOrderInfo.getUserConfirmPaymentAmount().compareTo(tOrderInfoDetail.getDeduction()) > 0
                    && tOrderInfoDetail.getDeduction().compareTo(BigDecimal.ZERO) > 0) {
                 TZtAccountOpenInfo tZtAccountOpenInfo = orderZtAccountOpenInfoMapper.selectCarrierOpenInfo(tOrderInfo.getCarrierId());
                carrierLock = redissonClient.getLock(tZtAccountOpenInfo.getPartnerAccId());
                carrierWalletLock = carrierLock.tryLock();
            }
           
            String key = "SEND" +tOrderInfo.getCode();
            RLock lock = redissonClient.getLock(key);

            try {
                if (lock.tryLock() && carrierWalletLock) {
                    if (DictEnum.P070.code.equals(tOrderInfo.getOrderPayStatus()) || DictEnum.M090.code.equals(tOrderInfo.getOrderPayStatus())) {
                        log.info("运单支付状态, {}, 不再处理", tOrderInfo.getOrderPayStatus());
                    }
                    // 查询出款方、入款方会员编号
                    SelectOpenRoleVO vo = new SelectOpenRoleVO();
                    vo.setCompanyId(tOrderInfo.getCompanyId());
                    vo.setCarrierId(tOrderInfo.getCarrierId());
                    vo.setDriverId(tOrderInfo.getEndDriverId());
                    vo.setChannelId(BankNameEnum.HXBANK.key());
                    ResultUtil openRoleStatus = paymentUtil.selectOpenRoleStatus(vo);
                    String jsonStr = JSONUtil.toJsonStr(openRoleStatus.getData());
                    OpenRoleStatusListDTO openRoleStatusListDTO = JSONUtil.toBean(jsonStr, OpenRoleStatusListDTO.class);
                    // 查询钱包
                    // 设置查询参数
                    SelectOpenRoleVO openRoleVO = new SelectOpenRoleVO();
                    openRoleVO.setCompanyId(tOrderInfo.getCompanyId());
                    openRoleVO.setCarrierId(tOrderInfo.getCarrierId());
                    openRoleVO.setDriverId(tOrderInfo.getEndDriverId());
                    openRoleVO.setChannelId(BankNameEnum.HXBANK.key());
                    ResultUtil openRoleWallet = paymentUtil.selectOpenRoleWallet(openRoleVO);
                    OpenRoleWalletListDTO walletListDTO = JSONUtil.toBean(JSONUtil.toJsonStr(openRoleWallet.getData()), OpenRoleWalletListDTO.class);
                    payService.carrierPay(messageBody, walletListDTO, openRoleStatusListDTO);
                    redisUtil.set(PAY_REQUEST_KEY + tOrderInfo.getId(), 1, 60 * 60 * 24);
                } else {
                    log.error("未获取到锁, 重新发送, {}", body);
                    // 重新发送
                    MQDelayMessage mqMessage = new MQDelayMessage();
                    mqMessage.setTopic(message.getTopic());
                    mqMessage.setTag(message.getTag());
                    mqMessage.setKey(message.getKey());
                    mqMessage.setBody(JSONUtil.toBean(body, PayMessageBody.class));
                    mqMessage.setStartDeliverTime(3 * 1000);
                    mqAPI.sendDelayMessage(mqMessage);
                }
            } catch (Exception e) {
                log.error("支付处理失败, 运单code: {}, {}", tOrderInfo.getCode(), ThrowableUtil.getStackTrace(e));
            } finally {
                if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                    lock.unlock();
                }
                if (null != carrierLock && carrierLock.isHeldByCurrentThread() && carrierLock.isLocked()) {
                    carrierLock.unlock();
                }
            }
        } else {
            log.error("交易类型不存在, tag: {}", tag);
        }
        log.info("支付开始处理结束, 运单id: {}", messageBody.getOrderId());
        return Action.CommitMessage;
    }

}
