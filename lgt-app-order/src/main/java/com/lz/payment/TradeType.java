package com.lz.payment;

public enum TradeType {
    /** 冻结 */
    DJ("DJ"),
    /** 入账 */
    R<PERSON>("RZ"),
    /** 召回 */
    ZH("ZH"),
    /** 打包召回 */
    DBZH("DBZH"),
    /** 提现 */
    TX("TX"),
    /** 发起 */
    FQ("FQ"),
    ZF("ZF"),
    /** 提现 */
    DBTX("DBTX"),
    /** 退票 */
    TP("TP"),
    /** 发起节点支付 */
    RZNODE("RZNODE"),
    /** 发单 */
    Invoice("Invoice"),
    /** 收单 */
    CollectionOrder("CollectionOrder"),
    /** 收单 */
    ReCollectionOrder("CollectionOrder"),
    /** 支付 */
    PayMent("PayMent"),
    /** 划账 */
    Remit("Remit"),
    /** 打包划账 */
    DBRemit("DBRemit"),
    /** 打包支付 */
    DBPayMent("DBPayMent"),
    /** 召回 */
    Recall("Recall"),
    /** 召回 */
    DBRecall("DBRecall"),
    /** 提现 */
    withdrawal("withdrawal"),
    /** 运费 */
    YFRZ("YFRZ"),
    /** 车主运费 */
    OYFRZ("OYFRZ"),
    /** 调度费 */
    DDFRZ("DDFRZ"),
    /** 服务费 */
    FWFRZ("FWFRZ"),
    /** 预付款支付 */
    AdvancePayment("AdvancePayment"),
    /** 预付款运费 */
    YFYF("YFYF"),
    /** 预付款调度费 */
    YFDDF("YFDDF"),
    /** 运费召回 */
    YFZH("YFZH"),
    /** 车主运费召回 */
    OYFZH("OYFZH"),
    /** 服务费召回 */
    FWFZH("FWFZH"),
    /** (承运方）调度费召回 */
    DDFZH("DDFZH"),
    /** tradeTypeNode*/
    YFKRZ("YFKRZ"),

    /** 预付款提现 */
    YFKTX("YFKTX"),

    /** 钱包流水 */
    /** (C端）运费划账 */
    YFHZ("YFHZ"),
    /** (C端）服务费划账 */
    FWFHZ("FWFHZ"),
    /** （企业）运费召回 */
    BYUNFEIZH("BYUNFEIZH"),
    /** （企业）调度费召回 */
    BDIAODUFEIZH("BDIAODUFEIZH"),
    /** （承运方）调度费召回 */
    PYUNFEIZH("PYUNFEIZH"),
    /** (企业)总费用召回*/
    BPAYZH("BPAYZH"),
    /** （承运方）总费用召回 */
    PPAYZH("PPAYZH"),
    /** （承运方）司机运费召回收入+ */
    PCYUNFEIZHSR("PCYUNFEIZHSR"),
    /**（承运方）车队长运费召回 + */
    PCPYUNFEIZHSR("PCPYUNFEIZHSR"),
    /**（承运方）经纪人召服务费回 + */
    PCMYUNFEIZHSR("PCMYUNFEIZHSR"),
    /**（C端）运费召回 - */
    CYUNFEIZH("CYUNFEIZH"),
    /**（C端）运费召回 + */
    CCYUNFEIZH("CCYUNFEIZH"),
    /** 车主运费召回 - */
    COYUNFEIZH("COYUNFEIZH"),
    /** 车队长运费召回 - */
    CPYUNFEIZH("CPYUNFEIZH"),
    /** 服务费召回 - */
    CMFUWUFEIZH("CMFUWUFEIZH"),
    /** 服务费召回 + */
    CCFUWUFEIZH("CCFUWUFEIZH"),
    /** (企业)总费用支出 */
    BPAY("BPAY"),
    /** (承运方)总费用收入*/
    PSHOURU("PSHOURU"),
    /** (承运方)运费支出*/
    PPAY("PPAY"),
    /** (承运方)违规扣款收入*/
    PDEDUCTIONSHOURU("PDEDUCTIONSHOURU"),
    /**(C端)运费支出 - */
    CPAY("CPAY"),
    /** (C端)运费收入 + */
    CYUNFEISHOURU("CYUNFEISHOURU"),
    /** (C端)违规扣款支出 - */
    CDEDUCTION("CDEDUCTION"),
    /** (C端)服务费支出 -  */
    CMFEEAPY("CMFEEAPY"),
    /** (C端)服务费收入 + */
    MFFESHOURU("MFFESHOURU"),

    /** (C端)保险支付 */
    CINSURANCE_PAY("CINSURANCE_PAY"),
    /** (承运方)保险收入 */
    PINSURANCESHOURU("PINSURANCESHOURU"),

    /** C端提现 */
    CTIXIAN("CTIXIAN"),
    //装货支付
    ZHPAYNODE("ZHPAYNODE"),
    //卸货支付节点
    XHPAYNODE("XHPAYNODE"),
    //收单支付节点
    SDPAYNODE("SDPAYNODE"),
    //尾款支付节点
    WKPAYNODE("WKPAYNODE")
    ;

    public String code;
    TradeType(String code) {
        this.code = code;
    }
}
