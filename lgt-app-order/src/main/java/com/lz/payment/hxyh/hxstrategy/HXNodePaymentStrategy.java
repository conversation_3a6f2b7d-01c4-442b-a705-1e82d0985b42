package com.lz.payment.hxyh.hxstrategy;

import cn.hutool.json.JSONUtil;
import com.lz.api.MqAPI;
import com.lz.common.config.HXPropertiesConfig;
import com.lz.common.dbenum.BusinessCode;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.HXTradeTypeEnum;
import com.lz.common.factory.HXMQMessageFactory;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.MQMessage;
import com.lz.common.model.hxPayment.request.pay.CustomerBalancePayReq;
import com.lz.common.util.*;
import com.lz.dao.TOrderCastChangesMapper;
import com.lz.dao.TOrderInfoMapper;
import com.lz.dao.TOrderPayRuleMapper;
import com.lz.dto.OpenRoleStatusListDTO;
import com.lz.dto.OpenRoleWalletListDTO;
import com.lz.model.TOrderCastChanges;
import com.lz.model.TOrderInfo;
import com.lz.model.TOrderPayInfo;
import com.lz.model.TOrderPayRule;
import com.lz.payment.TradeType;
import com.lz.payment.hxyh.HXPayOrderUtil;
import com.lz.payment.hxyh.HXPaymentUtil;
import com.lz.payment.hxyh.HXWalletUtil;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.vo.HxWalletVO;
import com.lz.vo.NodePayVO;
import com.lz.vo.SelectOpenRoleVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Service
public class HXNodePaymentStrategy extends HPaymentStrategy {

    @Autowired
    private TOrderInfoMapper orderInfoMapper;

    @Autowired
    private TOrderPayRuleMapper tOrderPayRuleMapper;

    @Autowired
    private TOrderCastChangesMapper tOrderCastChangesMapper;

    @Autowired
    private HXPaymentUtil hxPaymentUtil;

    @Autowired
    private HXWalletUtil hxWalletUtil;

    @Autowired
    private HXPayOrderUtil hxPayOrderUtil;

    @Autowired
    private HXPropertiesConfig hxPropertiesConfig;

    @Autowired
    private MqAPI mqAPI;

    @Autowired
    private SysParamAPI sysParamAPI;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil toNodePay(NodePayVO nodePayVO) {
        try {
            TOrderInfo tOrderInfo = orderInfoMapper.selectOrderByCode(nodePayVO.getCode());
            //当前节点改为入账处理中
            TOrderPayRule tOrderPayRule = tOrderPayRuleMapper.selectPayRuleByOrderCodeAndType(nodePayVO.getCode(), nodePayVO.getPayNodeType());
            tOrderPayRule.setPayStatus(DictEnum.PACKEDHANDEL.code);
            tOrderPayRule.setPayTime(new Date());
            tOrderPayRule.setPaymentFee(nodePayVO.getPaymentFee());
            tOrderPayRule.setRuleServiceFee(nodePayVO.getServiceFee());
            tOrderPayRule.setPayServiceFee(BigDecimal.ZERO);
            tOrderPayRule.setRulePaymentFee(nodePayVO.getRulePaymentFee());
            // 查询已支付运费
            BigDecimal sumFee = tOrderPayRuleMapper.selectSumFee(tOrderInfo.getCode());
            // 更新已支付运费
            BigDecimal nowSumFee = sumFee.add(nodePayVO.getPaymentFee());
            // 计算剩余运费
            if (null != tOrderInfo.getUserConfirmPaymentAmount() && tOrderInfo.getUserConfirmPaymentAmount().compareTo(BigDecimal.ZERO) > 0) {
                tOrderPayRule.setSurplusPaymentFee(tOrderInfo.getUserConfirmPaymentAmount().subtract(nowSumFee));
            } else {
                tOrderPayRule.setSurplusPaymentFee(tOrderInfo.getEstimateTotalFee().subtract(nowSumFee));
            }

            TOrderCastChanges cc = tOrderCastChangesMapper.selectByNewOne(tOrderInfo.getCode());
            String tradeValue = "";
            if (TradeType.WKPAYNODE.code.equals(nodePayVO.getPayNodeType())) {
                tradeValue = TradeType.RZ.code;
            } else {
                tradeValue = TradeType.DJ + nodePayVO.getPayNodeType();
            }
            TOrderCastChanges tOrderCastChanges = tOrderCastChangesMapper
                    .selectByTradeType(tOrderInfo.getCode(), tradeValue, nodePayVO.getPayNodeType());
            TOrderCastChanges castChanges = new TOrderCastChanges();
            BeanUtils.copyProperties(cc, castChanges);
            castChanges.setId(null);
            castChanges.setCode(IdWorkerUtil.getInstance().nextId());
            castChanges.setUserOper(nodePayVO.getPayNodeType());
            castChanges.setTradeType(tradeValue);
            castChanges.setCarriageFee(nodePayVO.getPaymentFee());
            castChanges.setTotalFee(castChanges.getCarriageFee());
            castChanges.setCreateUser(CurrentUser.getUserNickname());
            castChanges.setUpdateUser(CurrentUser.getUserNickname());
            castChanges.setDispatchFee(BigDecimal.ZERO);
            castChanges.setServiceFee(BigDecimal.ZERO);
            castChanges.setCreateTime(new Date());
            castChanges.setUpdateTime(new Date());
            castChanges.setDataEnable(false);

            //尾款支付，重新计算运费、调度费
            if (TradeType.WKPAYNODE.code.equals(nodePayVO.getPayNodeType())) {
                //更新支付规则表数据
                tOrderPayRule.setPayServiceFee(nodePayVO.getUserConfirmServiceFee());
                //计算调度费
                BigDecimal disFee = OrderMoneyUtil.getDispatchFee(cc.getCurrentDispatchRate(), nowSumFee);
                tOrderPayRule.setSurplusPaymentFee(BigDecimal.ZERO);
                tOrderPayRule.setPayDispatchFee(disFee);
                castChanges.setServiceFee(nodePayVO.getUserConfirmServiceFee());
                castChanges.setDispatchFee(disFee);
                BigDecimal total = castChanges.getDispatchFee().add(castChanges.getCarriageFee());
                castChanges.setTotalFee(total);
                // 更新运单
                TOrderInfo orderInfo = new TOrderInfo();
                orderInfo.setId(tOrderInfo.getId());
                orderInfo.setOrderPayStatus(DictEnum.P070.code);
                orderInfo.setDispatchFee(disFee);
                orderInfo.setUserConfirmServiceFee(nodePayVO.getUserConfirmServiceFee());
                orderInfo.setUserConfirmPaymentAmount(nowSumFee);
                orderInfo.setTotalFee(nowSumFee.add(disFee));
                orderInfoMapper.updateByPrimaryKeySelective(orderInfo);
                // 修改了运费, 解冻旧费用 - 冻结新费用
                /*if (DictEnum.BILLPAY.code.equals(tOrderInfo.getFeeSettlementWay())) {
                    // 账期付：修改账期可用额度
                    if (tOrderInfo.getTotalFee().compareTo(orderInfo.getTotalFee()) != 0) {
                        CompanyProjectByOrderVO vo = new CompanyProjectByOrderVO();
                        vo.setOldTotalPrice(tOrderInfo.getTotalFee());
                        vo.setTotalPrice(orderInfo.getTotalFee());
                        vo.setCompanyProjectId(tOrderInfo.getCompanyProjectId());
                        log.info(JSONUtil.toJsonStr(vo));
                        companyProjectUtil.unfreezeAndFreeze(vo);
                    }
                } else {
                    // 现金付：修改企业钱包冻结中和余额
                    JDWalletVO vo = new JDWalletVO();
                    vo.setOldTotalPrice(tOrderInfo.getTotalFee());
                    vo.setTotalFee(orderInfo.getTotalFee());
                    vo.setCompanyWalletId(castChanges.getCompanyWalletId());
                    jdWalletUtil.unfreezeAndFreezeWallet(vo);
                }*/

            }
            tOrderPayRuleMapper.updateByPrimaryKeySelective(tOrderPayRule);

            if (null == tOrderCastChanges) {
                tOrderCastChangesMapper.insertSelective(castChanges);
            } else {
                castChanges.setId(tOrderCastChanges.getId());
                tOrderCastChangesMapper.updateByPrimaryKeySelective(castChanges);
            }

            // 支付修改企业钱包,将总费用挪到入账中
            // 查询华夏钱包
            SelectOpenRoleVO openRoleVO = new SelectOpenRoleVO();
            openRoleVO.setCarrierId(tOrderInfo.getCarrierId());
            openRoleVO.setCompanyId(tOrderInfo.getCompanyId());
            openRoleVO.setDriverId(tOrderInfo.getEndDriverId());
            // 设置查询参数
            if (DictEnum.WKPAYNODE.code.equals(nodePayVO.getPayNodeType())) {
                if (DictEnum.PAYTOCAPTAIN.code.equals(cc.getCapitalTransferType())) {
                    openRoleVO.setCaptainId(tOrderInfo.getEndCarOwnerId());
                }
                if (DictEnum.MANAGERPATTERN.code.equals(cc.getCapitalTransferPattern())) {
                    openRoleVO.setManagerId(tOrderInfo.getAgentId());
                    openRoleVO.setCaptinanTransferPattern(cc.getCapitalTransferPattern());
                }
            }
            log.info(JSONUtil.toJsonStr(openRoleVO));
            // 查询钱包
            ResultUtil openRoleWallet = hxPayOrderUtil.selectOpenRoleWallet(openRoleVO);
            if (null == openRoleWallet || DictEnum.ERROR.code.equals(openRoleWallet.getCode())) {
                throw new RuntimeException("支付失败，请联系运营平台予以解决。");
            }
            OpenRoleWalletListDTO walletListDTO = JSONUtil.toBean(JSONUtil.toJsonStr(openRoleWallet.getData()), OpenRoleWalletListDTO.class);
            log.info("钱包数据, {}", JSONUtil.toJsonStr(walletListDTO));
            HxWalletVO walletVO = new HxWalletVO();
            walletVO.setCompanyWalletId(walletListDTO.getCompanyWallet().getId());
            BigDecimal totalFee = BigDecimal.ZERO;
            if (TradeType.WKPAYNODE.code.equals(nodePayVO.getPayNodeType())) {
                totalFee = castChanges.getTotalFee();
            } else {
                totalFee = castChanges.getCarriageFee();
            }
            walletVO.setFeeSettlementWay(tOrderInfo.getFeeSettlementWay());
            hxWalletUtil.modifyWallet(walletListDTO.getCompanyWallet().getId(), totalFee, tOrderInfo.getFeeSettlementWay());
            // 设置资金变动钱包为华夏钱包
            castChanges.setId(castChanges.getId());
            castChanges.setCompanyWalletId(walletListDTO.getCompanyWallet().getId());
            castChanges.setCarrierWalletId(walletListDTO.getCarrierWallet().getId());
            castChanges.setEndDriverWalletId(walletListDTO.getDriverWallet().getId());
            tOrderCastChangesMapper.updateByPrimaryKeySelective(castChanges);
            // 添加支付主表
            tOrderInfo.setTotalFee(walletVO.getTotalFee());
            TOrderPayInfo orderPayInfo = hxPaymentUtil.createOrderPayInfo(tOrderInfo);
            // 添加支付子表
            String orderPayDetailCode = hxPaymentUtil.createTxOrderPayDetail(orderPayInfo.getCode(), castChanges.getCode(),
                    HXTradeTypeEnum.HX_COMBALANCE_PAY.code, TradeType.RZNODE.code);
            // 查询出款方、入款方会员编号
            SelectOpenRoleVO vo = new SelectOpenRoleVO();
            vo.setCompanyId(tOrderInfo.getCompanyId());
            vo.setCarrierId(tOrderInfo.getCarrierId());
            if (DictEnum.WKPAYNODE.code.equals(nodePayVO.getPayNodeType())) {
                vo.setDriverId(tOrderInfo.getEndDriverId());
            }
            ResultUtil openRoleStatus = hxPayOrderUtil.selectOpenRoleStatus(vo);
            if (DictEnum.ERROR.code.equals(openRoleStatus.getCode())) {
                throw new RuntimeException(openRoleStatus.getMsg());
            }
            String jsonStr = JSONUtil.toJsonStr(openRoleStatus.getData());
            OpenRoleStatusListDTO openRoleStatusListDTO = JSONUtil.toBean(jsonStr, OpenRoleStatusListDTO.class);
            // 构造支付请求参数
            CustomerBalancePayReq req = new CustomerBalancePayReq();
            req.setPartnerId(hxPropertiesConfig.getPartnerId());
            req.setRequestId(IdWorkerUtil.getInstance().nextId());
            req.setRequestTime(DateUtils.getRequestTime());
            req.setChannelId(hxPropertiesConfig.getChannelId());
            req.setBizOrderNo(orderPayDetailCode);
            req.setOutPartnerAccId(openRoleStatusListDTO.getCompanyStatus().getPartnerAccId());
            req.setInPartnerAccId(openRoleStatusListDTO.getCarrierStatus().getPartnerAccId());
            req.setOrderAmount(castChanges.getTotalFee());
            Double weight = BigDecimal.valueOf(tOrderInfo.getEstimateGoodsWeight()).compareTo(BigDecimal.ZERO) > 0 ? tOrderInfo.getEstimateGoodsWeight() : tOrderInfo.getPrimaryWeight();
            req.setTradeAbstract("运费");
            SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.HXPAYNOTIFYURL.code);
            req.setNotifyUrl(paramByKey.getParamValue());

            MQMessage mqMessage = HXMQMessageFactory.getMessage(HXTradeTypeEnum.HX_COMBALANCE_PAY.code,
                    castChanges.getCapitalTransferType(), castChanges.getCapitalTransferPattern());
            mqMessage.setKey(tOrderInfo.getCode());
            mqMessage.setBody(req);
            ResultUtil resultUtil = mqAPI.sendMessage(mqMessage);
            log.info("发送MQ消息结果，{}", JSONUtil.toJsonStr(resultUtil));
            if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
                throw new RuntimeException("ZJJ-210:支付失败！");
            }
        } catch (Exception e) {
            log.error("支付失败！", e);
            String message = e.getMessage();
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                throw new RuntimeException(BusinessCode.PROJECTWALLETINSUFFICIENT.code);
            }
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                throw new RuntimeException(BusinessCode.WALLETAMOUNTINSUFFICIENT.code);
            }
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            }
            throw new RuntimeException("ZJJ-210:支付失败！");
        }
        return ResultUtil.ok();
    }

}
