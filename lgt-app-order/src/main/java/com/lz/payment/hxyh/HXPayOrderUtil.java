package com.lz.payment.hxyh;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lz.api.*;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.JdEnum;
import com.lz.common.model.CodeEnum;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.dao.*;
import com.lz.dto.*;
import com.lz.model.*;
import com.lz.service.TOrderCastChangesService;
import com.lz.service.TOrderStateService;
import com.lz.service.hxyh.THxPayBankCardService;
import com.lz.util.OrderDeductionUtil;
import com.lz.util.SendOrderUtil;
import com.lz.vo.*;
import com.lz.vo.hxyh.HXSinglePayVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Component
public class HXPayOrderUtil {

    @Autowired
    private TOrderInfoMapper orderInfoMapper;

    @Autowired
    private TOrderPayRuleMapper orderPayRuleMapper;

    @Resource
    private TOrderPackInfoMapper orderPackInfoMapper;

    @Resource
    private TOrderInvoiceInfoMapper orderInvoiceInfoMapper;

    @Resource
    private TOrderInfoDetailMapper orderInfoDetailMapper;

    @Resource
    private TOrderPayRequestDetailMapper orderPayRequestDetailMapper;

    @Autowired
    private TOrderStateService orderStateService;

    @Autowired
    private TOrderCastChangesService orderCastChangesService;

    @Autowired
    private THxPayBankCardService hxPayBankCardService;

    @Autowired
    private LinkGoodsRelAPI linkGoodsRelAPI;

    @Autowired
    private AppCommonAPI appCommonAPI;

    @Autowired
    private HXOpenRoleCommonAPI openRoleAPI;

    @Autowired
    private TServicefeeInfoAPI servicefeeInfoAPI;

    @Autowired
    private TZtBankUserAPI tZtBankUserAPI;

    @Autowired
    private SendOrderUtil sendOrderUtil;

    @Resource
    private TOrderInsuranceMapper orderInsuranceMapper;


    /**
     *  @author: dingweibo
     *  @Date: 2022/11/23 14:16
     *  @Description: 支付检查运单数据是否正确
     */
    public ResultUtil checkOrderPay(TOrderInfo orderInfoByCoded, HXSinglePayVO singlePayVO) {
        ResultUtil resultUtil = ResultUtil.error();
        resultUtil.setCode("error");
        resultUtil.setData(orderInfoByCoded.getOrderBusinessCode());
        if (null != orderInfoByCoded) {
            if (null != orderInfoByCoded.getPackStatus()) {
                if (orderInfoByCoded.getPackStatus().equals(DictEnum.PACK.code)) {
                    resultUtil.setMsg("打包原始运单不可在此支付");
                    return resultUtil;
                } else {
                    try {
                        if (orderInfoByCoded.getOrderExecuteStatus().equals(DictEnum.O060.code)
                                && (StringUtils.isEmpty(orderInfoByCoded.getOrderPayStatus())
                                || orderInfoByCoded.getOrderPayStatus().equals(DictEnum.M045.code)
                                || orderInfoByCoded.getOrderPayStatus().equals(DictEnum.M065.code)
                                || orderInfoByCoded.getOrderPayStatus().equals(DictEnum.M080.code)
                                || orderInfoByCoded.getOrderPayStatus().equals(DictEnum.M095.code))) {
                            TOrderPayRequestDetail orderPayRequestDetail = orderPayRequestDetailMapper.selectNewestByOrderId(orderInfoByCoded.getId());
                            if (null != orderPayRequestDetail && orderPayRequestDetail.getStatus() != 2) {
                                resultUtil.setMsg("运单状态错误， 不可支付。");
                                return resultUtil;
                            }
                            BigDecimal rulePaymentAmount = orderInfoByCoded.getRulePaymentAmount();
                            BigDecimal add_10 = rulePaymentAmount.add(rulePaymentAmount.multiply(new BigDecimal(DictEnum.FREIGHT_DIFFERENCES_LIMIT.code)));
                            if (singlePayVO!=null){
                                if (singlePayVO.getUserConfirmCarriagePayment().compareTo(add_10) > 0) {
                                    return ResultUtil.error("确认应付运费与规则应付运费差额不可超过10%。");
                                }
                            }else{
                                if (orderInfoByCoded.getUserConfirmPaymentAmount().compareTo(add_10) > 0 ) {
                                    return ResultUtil.error("确认应付运费与规则应付运费差额不可超过10%。");
                                }
                            }
                            TOrderState orderState = orderStateService.selectNewOrderState(orderInfoByCoded.getCode());
                            TOrderCastChanges tOrderCastChanges = orderCastChangesService.selectByNewOne(orderInfoByCoded.getCode());
                            BigDecimal txAmount = orderInfoByCoded.getUserConfirmPaymentAmount().subtract(orderInfoByCoded.getUserConfirmServiceFee());
                            TOrderInfoDetail tOrderInfoDetail = orderInfoDetailMapper.selectByOrderId(orderInfoByCoded.getId());
                            TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(orderInfoByCoded.getOrderBusinessCode());
                            if (null != singlePayVO) {
                                // 单笔支付
                                if (null != tOrderCastChanges.getCapitalTransferPattern()) {
                                   if (tOrderCastChanges.getCapitalTransferPattern().equals(DictEnum.MANAGERPATTERN.code)) {
                                        if (singlePayVO.getUserConfirmServiceFee().compareTo(BigDecimal.ZERO) <= 0) {
                                            return ResultUtil.error("经纪人服务费等于0，无法进行支付，请联系客服协商解决");
                                        }
                                        if (singlePayVO.getUserConfirmCarriagePayment().compareTo(singlePayVO.getUserConfirmServiceFee()) <= 0) {
                                            return ResultUtil.error("经纪人服务费大于或等于司机的总运费，无法进行支付，请联系客服协商解决");
                                        }
                                   }
                                }
                                // 运费判断
                                ResultUtil carriageResult = OrderDeductionUtil
                                        .validateAmountAndReturnResult(singlePayVO.getUserConfirmCarriagePayment(), singlePayVO.getUserConfirmServiceFee(),
                                                tOrderCastChanges.getCapitalTransferPattern(), tOrderInsurance, tOrderInfoDetail);
                                if (null != carriageResult && !carriageResult.getCode().equals(CodeEnum.SUCCESS.getCode())) {
                                    return carriageResult;
                                }
                            } else {
                                // 批量支付
                                if (null != tOrderCastChanges.getCapitalTransferPattern()
                                        && tOrderCastChanges.getCapitalTransferPattern().equals(DictEnum.MANAGERPATTERN.code)) {
                                    if (orderInfoByCoded.getUserConfirmServiceFee().compareTo(BigDecimal.ZERO) <= 0) {
                                        return ResultUtil.error("经纪人服务费等于0，无法进行支付，请联系客服协商解决");
                                    }
                                    if (orderInfoByCoded.getUserConfirmPaymentAmount().compareTo(orderInfoByCoded.getUserConfirmServiceFee()) <= 0) {
                                        return ResultUtil.error("经纪人服务费大于或等于司机的总运费，无法进行支付，请联系客服协商解决");
                                    }
                                }
                                // 运费判断
                                ResultUtil carriageResult = OrderDeductionUtil
                                        .validateAmountAndReturnResult(orderInfoByCoded.getUserConfirmPaymentAmount(), orderInfoByCoded.getUserConfirmServiceFee(),
                                                tOrderCastChanges.getCapitalTransferPattern(), tOrderInsurance, tOrderInfoDetail);
                                if (null != carriageResult && !carriageResult.getCode().equals(CodeEnum.SUCCESS.getCode())) {
                                    return carriageResult;
                                }
                            }

                            String capitalTransferType = tOrderCastChanges.getCapitalTransferType();
                            String capitalTransferPattern = null == tOrderCastChanges.getCapitalTransferPattern() ? "" : tOrderCastChanges.getCapitalTransferPattern();
                            // 支付流程，且审核未通过
                            ResultUtil auditResult = payBeforeAudit(orderInfoByCoded.getOrderPayStatus(), orderState.getStateNodeValue());
                            if (auditResult != null) {
                                return auditResult;
                            }
                            // 设置HX数据查询类
                            SelectOpenRoleVO selectOpenRoleVO = setOpenRoleVO(orderInfoByCoded, capitalTransferType, capitalTransferPattern);
                            // 查询HX开户状态
                            ResultUtil openRoleStatus = selectOpenRoleStatus(selectOpenRoleVO);
                            if (DictEnum.ERROR.code.equals(openRoleStatus.getCode())) {
                                throw new RuntimeException(openRoleStatus.getMsg());
                            }
                            // 查询HX钱包
                            ResultUtil openRoleWallet = selectOpenRoleWallet(selectOpenRoleVO);
                            if (null != openRoleWallet && DictEnum.ERROR.code.equals(openRoleWallet.getCode())) {
                                return openRoleWallet;
                            }
                            // 1. 判断车辆和司机是否审核通过：根据线路货物关系表中
                            LineGoodsRelInfo lineGoodsRelInfo = linkGoodsRelAPI.selectLineGoodsRelInfo(new TGoodsSourceInfo().setLineGoodsRelId(orderInfoByCoded.getLineGoodsRelId()));
                            if (null != lineGoodsRelInfo && null != lineGoodsRelInfo.getTransportIdentityCheck()
                                    && lineGoodsRelInfo.getTransportIdentityCheck()) {
                                String checkUserCarStatus = sendOrderUtil.checkUserCarStatus(orderInfoByCoded,
                                        tOrderCastChanges.getCapitalTransferType(), capitalTransferPattern);
                                if (StringUtils.isNotBlank(checkUserCarStatus)) {
                                    resultUtil.setMsg(checkUserCarStatus);
                                    return resultUtil;
                                }
                            }
                            // 2。判断合同
                            String contractStatus = orderInfoByCoded.getContractStatus();
                            if (contractStatus.equals(DictEnum.WQD.code)) {
                                resultUtil.setMsg("运单未签署电子合同，请联系司机签署线下合同。");
                                return resultUtil;
                            }
                            // 3. 查询车主权益证明(资金转移方式到车主) 车队长上线后为了处理老数据 车老板不进行判断
                            // 4. 获取C端ID
                            Integer enduserId = null;
                            ResultUtil result = getEnduserIdByCapitalTransferType(tOrderCastChanges.getCapitalTransferType(), orderInfoByCoded);
                            if (DictEnum.SUCCESS.code.equals(result.getCode())) {
                                if (null != result.getData()) {
                                    enduserId = (Integer) result.getData();
                                }
                            } else {
                                result.setData(orderInfoByCoded.getOrderBusinessCode());
                                return result;
                            }

                            String withdrawType = tOrderCastChanges.getWithdrawType();
                            //提现方式：自动到卡
                            if (withdrawType.equals(DictEnum.AUTOMATION.code)) {
                                // 查询车辆司机审核状态
                                String checkUserCarStatus = checkUserCarStatus(orderInfoByCoded,
                                        tOrderCastChanges.getCapitalTransferType(), capitalTransferPattern);
                                if (StringUtils.isNotBlank(checkUserCarStatus)) {
                                    resultUtil.setMsg(checkUserCarStatus);
                                    return resultUtil;
                                }
                                // 查询C端是否绑定银行卡
                                if (enduserId == null) {
                                    resultUtil.setMsg("未找到C端用户");
                                    return resultUtil;
                                } else {
                                    Integer bankCardId = null;
                                    TZtBankCard bankCard = new TZtBankCard();
                                    if (null == singlePayVO || null == singlePayVO.getBankId()) {
                                        // 查询默认银行卡
                                        bankCard = selectDefaultBankCard(enduserId);
                                        if (null == bankCard) {
                                            StringBuffer error = new StringBuffer();
                                            String orderBusinessCodeSplit = orderInfoByCoded.getOrderBusinessCode()
                                                    .substring(orderInfoByCoded.getOrderBusinessCode().length() - 8);
                                            error.append("尾号为" + orderBusinessCodeSplit);
                                            if (capitalTransferType.equals(DictEnum.PAYTODRIVER.code)
                                                    || capitalTransferType.equals(DictEnum.FIRSTBROKERDRIVER.code)) {
                                                error.append("的运单，未找到司机默认银行卡，请联系企业管理员或运营平台维护后再支付。");
                                            }
                                            if (capitalTransferType.equals(DictEnum.PAYTOBELONGER.code)
                                                    || capitalTransferType.equals(DictEnum.FIRSTBROKERBELONGER.code)) {
                                                error.append("的运单，未找到车老板默认银行卡，请联系企业管理员或运营平台维护后再支付。");
                                            }
                                            if (capitalTransferType.equals(DictEnum.PAYTOCAPTAIN.code)) {
                                                error.append("的运单，未找到车队长默认银行卡，请联系企业管理员或运营平台维护后再支付。");
                                            }
                                            resultUtil.setMsg(error.toString());
                                            return resultUtil;
                                        }
                                        bankCardId = bankCard.getId();
                                    } else {
                                        // 检测银行卡信息是否完整
                                        bankCard = tZtBankUserAPI.selectById(singlePayVO.getBankId());
                                        String error = checkCardholder(bankCard);
                                        if (StringUtils.isNotBlank(error)) {
                                            return ResultUtil.error(error, orderInfoByCoded.getOrderBusinessCode());
                                        }
                                        bankCardId = singlePayVO.getBankId();
                                    }

                                    //持卡人开户类型
                                    String openAccountType = DictEnum.MYACCOUNT.code;
                                    orderInfoByCoded.setParam3(String.valueOf(bankCardId));
                                    log.info("提现信息, {}, 提现到{}", JSONUtil.toJsonStr(singlePayVO), openAccountType);
                                    BigDecimal txServiceFee = getTxServiceFee(txAmount, openAccountType);
                                    if (null != txServiceFee && txServiceFee.compareTo(BigDecimal.ZERO) > 0) {
                                        if (txServiceFee.compareTo(txAmount) >= 0) {
                                            resultUtil.setMsg("提现服务费不能大于等于提现金额，请联系运营平台予以解决。");
                                            return resultUtil;
                                        }
                                    }
                                }
                            }
                            if (contractStatus.equals(DictEnum.YZHUD.code)
                                    || contractStatus.equals(DictEnum.YZID.code)
                                    || contractStatus.equals(DictEnum.HBXXHT.code)
                                    || contractStatus.equals(DictEnum.XXHTSC.code)
                                    || contractStatus.equals(DictEnum.APPHTSC.code)) {
                                return null;
                            } else {
                                resultUtil.setMsg("运单未签署电子合同，请联系司机签署线下合同。");
                                return resultUtil;
                            }
                        } else {
                            resultUtil.setMsg("运单状态错误， 不可支付。");
                            return resultUtil;
                        }
                    } catch (Exception e) {
                        log.error("支付失败!", e);
                        String message = e.getMessage();
                        if (StringUtils.checkChineseCharacter(message)) {
                            resultUtil.setMsg(message);
                        } else {
                            resultUtil.setMsg("支付失败!");
                        }
                        return resultUtil;
                    }
                }
            } else {
                return resultUtil;
            }
        }
        return null;
    }

    public ResultUtil checkNodePay(TOrderInfo orderInfo, NodePayVO payVO) {
        ResultUtil resultUtil = ResultUtil.error();
        BigDecimal rulePaymentFee = payVO.getRulePaymentFee();
        BigDecimal add_10 = rulePaymentFee.add(rulePaymentFee.multiply(new BigDecimal(DictEnum.FREIGHT_DIFFERENCES_LIMIT.code)));
        if (payVO.getPaymentFee().compareTo(add_10) > 0 ) {
            return ResultUtil.error("确认应付运费与规则应付运费差额不可超过10%。");
        }
        TOrderCastChanges tOrderCastChanges = orderCastChangesService.selectByNewOne(orderInfo.getCode());
        String capitalTransferPattern = null == tOrderCastChanges.getCapitalTransferPattern() ? "" : tOrderCastChanges.getCapitalTransferPattern();
        if (null == tOrderCastChanges.getCapitalTransferPattern() || StringUtils.isBlank(tOrderCastChanges.getCapitalTransferPattern())) {
            throw new RuntimeException("支付失败！");
        }
        try {
            if (payVO.getPayNodeType().equals(DictEnum.ZHPAYNODE.code)) {
                List<TOrderState> tOrderStateList = orderStateService.selectByOrderCodeAndStateNode(orderInfo.getCode(), DictEnum.M030.code);
                if (null == tOrderStateList || "".equals(tOrderStateList) || tOrderStateList.size() < 1) {
                    return ResultUtil.error("装货地未签到，不允许装货支付");
                }
                TOrderPayRule result = orderPayRuleMapper.selectPayRuleByOrderCodeAndType(orderInfo.getCode(), payVO.getPayNodeType());
                if (DictEnum.PACKEDHANDEL.code.equals(result.getPayStatus()) || DictEnum.PACKPAID.code.equals(result.getPayStatus())) {
                    return ResultUtil.error("当前运单已支付或支付处理中，不允许装货支付");
                }
            } else if (payVO.getPayNodeType().equals(DictEnum.XHPAYNODE.code)) {
                List<TOrderState> tOrderStateList = orderStateService.selectByOrderCodeAndStateNode(orderInfo.getCode(), DictEnum.M040.code);
                if (null == tOrderStateList || "".equals(tOrderStateList) || tOrderStateList.size() < 1) {
                    return ResultUtil.error("卸货地未签到，不允许卸货支付");
                }
                TOrderPayRule result = orderPayRuleMapper.selectPayRuleByOrderCodeAndType(orderInfo.getCode(), payVO.getPayNodeType());
                if (DictEnum.PACKEDHANDEL.code.equals(result.getPayStatus()) || DictEnum.PACKPAID.code.equals(result.getPayStatus())) {
                    return ResultUtil.error("当前运单已支付或支付处理中，不允许卸货支付");
                }
            } else if (payVO.getPayNodeType().equals(DictEnum.SDPAYNODE.code)) {
                List<TOrderState> tOrderStateList = orderStateService.selectByOrderCodeAndStateNode(orderInfo.getCode(), DictEnum.M050.code);
                if (null == tOrderStateList || "".equals(tOrderStateList) || tOrderStateList.size() < 1) {
                    return ResultUtil.error("运单未收单，不允许收单支付");
                }
                TOrderPayRule result = orderPayRuleMapper.selectPayRuleByOrderCodeAndType(orderInfo.getCode(), payVO.getPayNodeType());
                if (DictEnum.PACKEDHANDEL.code.equals(result.getPayStatus()) || DictEnum.PACKPAID.code.equals(result.getPayStatus())) {
                    return ResultUtil.error("当前运单已支付或支付处理中，不允许收单支付");
                }

                BigDecimal paymentFee = payVO.getPaymentFee();
                //收单支付金额 小于 确认应付运费-实际已支付运费
                BigDecimal userConfirmCarriagePayment = tOrderCastChanges.getCarriageFee();
                BigDecimal sumFee = orderPayRuleMapper.selectSumFee(orderInfo.getCode());
                BigDecimal subtract = userConfirmCarriagePayment.subtract(sumFee);
                if (paymentFee.compareTo(subtract) >= 0) {
                    return ResultUtil.error("支付失败！收单支付金额需小于"+subtract+"元");
                }

            } else if (payVO.getPayNodeType().equals(DictEnum.WKPAYNODE.code)) {
                List<TOrderState> tOrderStateList = orderStateService.selectByOrderCodeAndStateNode(orderInfo.getCode(), DictEnum.M050.code);
                if (null == tOrderStateList || "".equals(tOrderStateList) || tOrderStateList.size() < 1) {
                    return ResultUtil.error("运单未收单，不允许尾款支付");
                }
                TOrderPayRule result = orderPayRuleMapper.selectPayRuleByOrderCodeAndType(orderInfo.getCode(), payVO.getPayNodeType());
                if (DictEnum.PACKEDHANDEL.code.equals(result.getPayStatus()) || DictEnum.PACKPAID.code.equals(result.getPayStatus())) {
                    return ResultUtil.error("当前运单已支付或支付处理中，不允许尾款支付");
                }
                // 运费判断
                TOrderInfoDetail tOrderInfoDetail = orderInfoDetailMapper.selectByOrderId(orderInfo.getId());
                TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(orderInfo.getOrderBusinessCode());
                ResultUtil carriageResult = OrderDeductionUtil.validateAmountAndReturnResult(payVO.getPaymentFee(), payVO.getUserConfirmServiceFee(), tOrderCastChanges.getCapitalTransferPattern(), tOrderInsurance, tOrderInfoDetail);
                if (!Objects.equals(carriageResult.getCode(), CodeEnum.SUCCESS.getCode())) {
                    return carriageResult;
                }
                if (DictEnum.MANAGERPATTERN.code.equals(tOrderCastChanges.getCapitalTransferPattern())) {
                    if (payVO.getUserConfirmServiceFee().compareTo(BigDecimal.ZERO) <= 0) {
                        return ResultUtil.error("该运单经纪人服务费不合规，请修改经纪人服务费再支付");
                    }
                }
            }
            TOrderPayRule tOrderPayRule = orderPayRuleMapper.selectPayRuleByOrderCodeAndType(orderInfo.getCode(), payVO.getPayNodeType());
            if (null != tOrderPayRule && !"".equals(tOrderPayRule)) {
                if (DictEnum.PACKPAID.code.equals(tOrderPayRule.getPayStatus()) || DictEnum.PACKEDHANDEL.code.equals(tOrderPayRule.getPayStatus())) {
                    return ResultUtil.error("当前运单，已支付或支付中请刷新");
                }
            }
            // 判断车辆和司机是否审核通过：根据线路货物关系表中
            LineGoodsRelInfo lineGoodsRelInfo = linkGoodsRelAPI.selectLineGoodsRelInfo(new TGoodsSourceInfo().setLineGoodsRelId(orderInfo.getLineGoodsRelId()));
            if (null != lineGoodsRelInfo && null != lineGoodsRelInfo.getTransportIdentityCheck()
                    && lineGoodsRelInfo.getTransportIdentityCheck()) {
                String checkUserCarStatus = checkUserCarStatus(orderInfo,
                        tOrderCastChanges.getCapitalTransferType(), capitalTransferPattern);
                if (StringUtils.isNotBlank(checkUserCarStatus)) {
                    resultUtil.setCode("error");
                    resultUtil.setData(orderInfo.getOrderBusinessCode());
                    resultUtil.setMsg(checkUserCarStatus);
                    return resultUtil;
                }
            }
            // 获取最新资金变动表，检查运单C端用户id
            ResultUtil enduserIdByCapitalTransferType = getEnduserIdByCapitalTransferType(tOrderCastChanges.getCapitalTransferType(), orderInfo);
            if (!DictEnum.SUCCESS.code.equals(enduserIdByCapitalTransferType.getCode())) {
                enduserIdByCapitalTransferType.setData(orderInfo.getOrderBusinessCode());
                return enduserIdByCapitalTransferType;
            }
            // 设置开户查询参数
            SelectOpenRoleVO openRoleVO = new SelectOpenRoleVO();
            openRoleVO.setCarrierId(orderInfo.getCarrierId());
            openRoleVO.setCompanyId(orderInfo.getCompanyId());
            openRoleVO.setDriverId(orderInfo.getEndDriverId());
            if (DictEnum.MANAGERPATTERN.code.equals(tOrderCastChanges.getCapitalTransferPattern())) {
                openRoleVO.setManagerId(orderInfo.getAgentId());
            }
            openRoleVO.setCapitalTransferType(tOrderCastChanges.getCapitalTransferType());
            openRoleVO.setCaptinanTransferPattern(tOrderCastChanges.getCapitalTransferPattern());
            // 查询用户开户状态
            ResultUtil openRoleStatus = selectOpenRoleStatus(openRoleVO);
            if (DictEnum.ERROR.code.equals(openRoleStatus.getCode())) {
                throw new RuntimeException(openRoleStatus.getMsg());
            }

            // 查询钱包
            selectOpenRoleWallet(openRoleVO);

        } catch (Exception e) {
            log.error("节点支付失败!", e);
            resultUtil.setCode("error");
            resultUtil.setData(orderInfo.getOrderBusinessCode());
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                resultUtil.setMsg(message);
            } else {
                resultUtil.setMsg("节点支付失败!");
            }
            return resultUtil;
        }
        return null;
    }

    public ResultUtil checkPackPay(TOrderPackInfo packInfo) {
        //TODO 1. 判断车辆和司机是否审核通过：线路货物关系表中
        TOrderInfo tOrderInfo = orderInfoMapper.selectOrderInfoOneByPackCode(packInfo.getCode());
        TOrderCastChanges tOrderCastChanges = orderCastChangesService.selectByNewOne(tOrderInfo.getCode());
        TGoodsSourceInfo tGoodsSourceInfo = new TGoodsSourceInfo().setLineGoodsRelId(tOrderInfo.getLineGoodsRelId());
        LineGoodsRelInfo lineGoodsRelInfo = linkGoodsRelAPI.selectLineGoodsRelInfo(tGoodsSourceInfo);
        if (null != lineGoodsRelInfo && null != lineGoodsRelInfo.getTransportIdentityCheck()
                && lineGoodsRelInfo.getTransportIdentityCheck()) {
            ResultUtil checkPackOrderUserCarStatus = checkPackOrderUserCarStatus(packInfo.getCode(),
                    tOrderCastChanges.getCapitalTransferType(), tOrderCastChanges.getCapitalTransferPattern());
            if (null != checkPackOrderUserCarStatus) {
                checkPackOrderUserCarStatus.setData(packInfo.getVirtualOrderNo());
                return checkPackOrderUserCarStatus;
            }
        }

        if (null == tOrderCastChanges.getCapitalTransferPattern() || StringUtils.isBlank(tOrderCastChanges.getCapitalTransferPattern())) {
            throw new RuntimeException("支付失败！");
        }
        if (DictEnum.MANAGERPATTERN.code.equals(tOrderCastChanges.getCapitalTransferPattern())) {
            if (packInfo.getTotalSelectedOrdersServiceFee().compareTo(BigDecimal.ZERO) <= 0) {
                return ResultUtil.error("该运单经纪人服务费不合规，请修改经纪人服务费再支付", packInfo.getVirtualOrderNo());
            }
        }

        // 查询开户状态
        String openStatus = queryEndUserOpenStatus(packInfo, tOrderInfo, tOrderCastChanges);
        if (openStatus != null && StringUtils.isNotBlank(openStatus)) {
            return ResultUtil.error(openStatus, packInfo.getVirtualOrderNo());
        }
        // 自动到卡，查询提现服务费
        if (DictEnum.AUTOMATION.code.equals(tOrderCastChanges.getWithdrawType())) {
            BigDecimal txAmount = packInfo.getAppointmentPaymentCash().subtract(packInfo.getTotalSelectedOrdersServiceFee());
            String openAccountType = DictEnum.MYACCOUNT.code;
            // 查询银行卡开户状态
            try {
                String userLogisticsRole = "";
                if (DictEnum.PAYTODRIVER.code.equals(tOrderCastChanges.getCapitalTransferPattern())) {
                    userLogisticsRole = DictEnum.CTYPEDRVIVER.code;
                } else if (DictEnum.PAYTOCAPTAIN.code.equals(tOrderCastChanges.getCapitalTransferPattern())) {
                    userLogisticsRole = DictEnum.CTYPECAPTAIN.code;
                }
                checkBankCardOpenStatus(packInfo.getEndUserId(), packInfo.getBankCardId(), userLogisticsRole);
            } catch (Exception e) {
                e.printStackTrace();
                log.info("查询持卡人开户状态失败, {}" ,e);
                if (StringUtils.checkChineseCharacter(e.getMessage())) {

                } else {
                    return ResultUtil.error("查询持卡人开户状态失败，请联系运营平台予以解决。", packInfo.getVirtualOrderNo());
                }
            }
            log.info("提现信息, {},", JSONUtil.toJsonStr(packInfo));
            BigDecimal txServiceFee = getTxServiceFee(txAmount, openAccountType);
            if (txServiceFee.compareTo(txAmount) >= 0) {
                return ResultUtil.error("提现服务费不能大于等于提现金额，请联系运营平台予以解决。", packInfo.getVirtualOrderNo());
            }
        }

        return null;
    }

    private String queryEndUserOpenStatus(TOrderPackInfo packInfo, TOrderInfo tOrderInfo, TOrderCastChanges tOrderCastChanges) {
        if (DictEnum.COMMONPATTERN.code.equals(tOrderCastChanges.getCapitalTransferPattern())) {
            if (DictEnum.PAYTODRIVER.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                SelectOrderPackOpenRoleVO vo = new SelectOrderPackOpenRoleVO();
                // 司机
                vo.setDriverId(tOrderInfo.getEndDriverId());
                ResultUtil resultUtil = openRoleAPI.selectOrderPackCarrierCompanyEnduserOpenRoleStatus(vo);
                if (null != resultUtil.getCode() && DictEnum.SUCCESS.code.equals(resultUtil.getCode())) {
                    if (null != resultUtil.getData()) {
                        OpenRoleStatusListDTO dto = JSONUtil.toBean(JSONUtil.toJsonStr(resultUtil.getData()), OpenRoleStatusListDTO.class);
                        if (null == dto.getDriverStatus() || !dto.getDriverStatus().getStatus()) {
                            return "司机" + dto.getDriverStatus().getRealName() + "未开户，请联系运营平台予以解决!";
                        }
                    }
                }
            }

            if (DictEnum.PAYTOCAPTAIN.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                // 查询所有司机
                List<Integer> driverList = orderPackInfoMapper.getEndDriverIdByPackCode(packInfo.getCode());
                SelectOrderPackOpenRoleVO vo = new SelectOrderPackOpenRoleVO();
                // 所有司机
                vo.setDriverList(driverList);
                // 车队长
                vo.setCaptainId(tOrderInfo.getEndCarOwnerId());
                ResultUtil resultUtil = openRoleAPI.selectOrderPackCarrierCompanyEnduserOpenRoleStatus(vo);
                if (null != resultUtil.getCode() && DictEnum.SUCCESS.code.equals(resultUtil.getCode())) {
                    if (null != resultUtil.getData()) {
                        OpenRoleStatusListDTO dto = JSONUtil.toBean(JSONUtil.toJsonStr(resultUtil.getData()), OpenRoleStatusListDTO.class);
                        StringBuffer sb = new StringBuffer();
                        if (null == dto.getDriverList()) {
                            return "司机：" + dto.getDriverStatus().getRealName() + "未开户";
                        } else {
                            StringBuffer drvierError = new StringBuffer();
                            dto.getDriverList().forEach((driver) -> {
                                if (null == driver.getOpenStatus() || driver.getStatus() != 1) {
                                    drvierError.append(driver.getRealName()).append(",");
                                }
                            });
                            if (drvierError.length() > 0) {
                                sb.append("司机：").append(drvierError).append("未开户。\n");
                            }
                        }
                        if (null == dto.getCaptionStatus() || !dto.getCaptionStatus().getStatus()) {
                            sb.append("车队长：").append(dto.getCaptionStatus().getRealName()).append("未开户。");
                        }
                        if (sb.length() > 0) {
                            return sb.toString();
                        }
                    }
                }
            }
        } else if (DictEnum.MANAGERPATTERN.code.equals(tOrderCastChanges.getCapitalTransferPattern())) {
            if (DictEnum.PAYTODRIVER.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                SelectOrderPackOpenRoleVO vo = new SelectOrderPackOpenRoleVO();
                // 司机
                vo.setDriverId(tOrderInfo.getEndDriverId());
                // 经纪人
                vo.setManagerId(tOrderInfo.getAgentId());
                ResultUtil resultUtil = openRoleAPI.selectOrderPackCarrierCompanyEnduserOpenRoleStatus(vo);
                if (null != resultUtil.getCode() && DictEnum.SUCCESS.code.equals(resultUtil.getCode())) {
                    if (null != resultUtil.getData()) {
                        StringBuffer sb = new StringBuffer();
                        OpenRoleStatusListDTO dto = JSONUtil.toBean(JSONUtil.toJsonStr(resultUtil.getData()), OpenRoleStatusListDTO.class);
                        if (null == dto.getDriverStatus() || !dto.getDriverStatus().getStatus()) {
                            sb.append("司机:").append(dto.getDriverStatus().getRealName()).append("未开户。\n");
                        }
                        if (null == dto.getManagerStatus() || !dto.getManagerStatus().getStatus()) {
                            sb.append("经纪人:").append(dto.getManagerStatus().getRealName()).append("未开户。");
                        }
                        if (sb.length() > 0) {
                            return sb.toString();
                        }
                    }
                }
            }

            if (DictEnum.PAYTOCAPTAIN.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                // 查询所有司机
                List<Integer> driverList = orderPackInfoMapper.getEndDriverIdByPackCode(packInfo.getCode());
                SelectOrderPackOpenRoleVO vo = new SelectOrderPackOpenRoleVO();
                // 司机
                vo.setDriverList(driverList);
                // 车队长
                vo.setCaptainId(tOrderInfo.getEndCarOwnerId());
                // 经纪人
                vo.setManagerId(tOrderInfo.getAgentId());
                ResultUtil resultUtil = openRoleAPI.selectOrderPackCarrierCompanyEnduserOpenRoleStatus(vo);
                if (null != resultUtil.getCode() && DictEnum.SUCCESS.code.equals(resultUtil.getCode())) {
                    if (null != resultUtil.getData()) {
                        StringBuffer sb = new StringBuffer();
                        OpenRoleStatusListDTO dto = JSONUtil.toBean(JSONUtil.toJsonStr(resultUtil.getData()), OpenRoleStatusListDTO.class);
                        StringBuffer drvierError = new StringBuffer();
                        dto.getDriverList().forEach((driver) -> {
                            if (null == driver.getOpenStatus() || driver.getStatus() != 1) {
                                drvierError.append(driver.getRealName()).append(",");
                            }
                        });
                        if (drvierError.length() > 0) {
                            sb.append("司机：").append(drvierError).append("未开户。\n");
                        }
                        if (null == dto.getCaptionStatus() || !dto.getCaptionStatus().getStatus()) {
                            sb.append("车队长：").append(dto.getCaptionStatus().getRealName()).append("未开户。\n");
                        }
                        if (null == dto.getManagerStatus() || !dto.getManagerStatus().getStatus()) {
                            sb.append("经纪人").append(dto.getManagerStatus().getRealName()).append("未开户。");
                        }
                        if (sb.length() > 0) {
                            return sb.toString();
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * 支付前需要审核
     *
     * @param orderPayStatus
     * @param orderState
     * @return
     */
    ResultUtil payBeforeAudit(String orderPayStatus, String orderState) {
        if (orderPayStatus.equals(DictEnum.M065.code) && !DictEnum.S0651.code.equals(orderState)) {
            if (DictEnum.S0650.code.equals(orderState)) {
                return ResultUtil.error("当前运单支付前需要审核，审核员未审核，暂不能支付");
            } else {
                return ResultUtil.error("当前运单审核未通过，暂不能支付");
            }
        }
        return null;
    }

    /**
     * @param
     * @return
     * @description 设置华夏查询类
     * <AUTHOR>
     * @date 2021/8/9 10:22
     */
    private SelectOpenRoleVO setOpenRoleVO(TOrderInfo orderInfoByCoded, String capitalTransferType, String captinanTransferPattern) {
        SelectOpenRoleVO selectOpenRoleVO = new SelectOpenRoleVO();
        selectOpenRoleVO.setCarrierId(orderInfoByCoded.getCarrierId());
        selectOpenRoleVO.setCompanyId(orderInfoByCoded.getCompanyId());
        selectOpenRoleVO.setDriverId(orderInfoByCoded.getEndDriverId());
        if (DictEnum.PAYTOCAPTAIN.code.equals(capitalTransferType)) {
            selectOpenRoleVO.setCaptainId(orderInfoByCoded.getEndCarOwnerId());
        }
        if (DictEnum.MANAGERPATTERN.code.equals(captinanTransferPattern)) {
            selectOpenRoleVO.setManagerId(orderInfoByCoded.getAgentId());
        }
        selectOpenRoleVO.setCapitalTransferType(capitalTransferType);
        selectOpenRoleVO.setCaptinanTransferPattern(captinanTransferPattern);
        return selectOpenRoleVO;
    }

    /**
     * @param
     * @return
     * @description 查询华夏开户状态
     * <AUTHOR>
     * @date 2021/8/9 10:22
     */
    public ResultUtil selectOpenRoleStatus(SelectOpenRoleVO selectOpenRoleVO) {
        ResultUtil openRoleStatus = openRoleAPI.selectCarrierCompanyEnduserOpenRoleStatus(selectOpenRoleVO);
        if (null != openRoleStatus.getCode() && DictEnum.SUCCESS.code.equals(openRoleStatus.getCode())) {
            if (null != openRoleStatus.getData()) {
                String jsonStr = JSONUtil.toJsonStr(openRoleStatus.getData());
                HXOpenRoleStatusListDTO dto = JSONUtil.toBean(jsonStr, HXOpenRoleStatusListDTO.class);
                log.info("开户状态, {}", JSONUtil.toJsonStr(dto));
                if (null != selectOpenRoleVO.getCarrierId()) {
                    if (null == dto.getCarrierStatus()
                            || null == dto.getCarrierStatus().getStatus()
                            || !dto.getCarrierStatus().getStatus()
                            || null == dto.getCarrierStatus().getPartnerAccId()
                            || StringUtils.isBlank(dto.getCarrierStatus().getPartnerAccId())) {
                        return ResultUtil.error("支付失败！承运方未开通华夏，请联系运营平台予以解决。");
                    }
                }
                if (null != selectOpenRoleVO.getCompanyId()) {
                    if (null == dto.getCompanyStatus()
                            || null == dto.getCompanyStatus().getStatus()
                            || !dto.getCompanyStatus().getStatus()
                            || null == dto.getCompanyStatus().getPartnerAccId()
                            || StringUtils.isBlank(dto.getCompanyStatus().getPartnerAccId())) {
                        return ResultUtil.error("支付失败！企业未开通华夏，请联系运营平台予以解决。");
                    }
                }
                if (null != selectOpenRoleVO.getDriverId()
                        && (null == dto.getDriverStatus()
                        || null == dto.getDriverStatus().getStatus()
                        || !dto.getDriverStatus().getStatus())) {
                    log.info("支付失败，请联系运营平台予以解决。司机未开户");
                    return ResultUtil.error("支付失败！司机未开通华夏支付，请联系运营平台予以解决。");
                }
                if (DictEnum.PAYTOCAPTAIN.code.equals(selectOpenRoleVO.getCapitalTransferType())) {
                    if (null != selectOpenRoleVO.getCaptainId()
                            && (null == dto.getCaptionStatus()
                            || null == dto.getCaptionStatus().getStatus()
                            || !dto.getCaptionStatus().getStatus())) {
                        log.info("支付失败，请联系运营平台予以解决。车队长未开户");
                        return ResultUtil.error("支付失败！车队长未开通华夏支付，请联系运营平台予以解决。");
                    }
                }
                if (DictEnum.MANAGERPATTERN.code.equals(selectOpenRoleVO.getCaptinanTransferPattern())) {
                    if (null != selectOpenRoleVO.getManagerId()
                            && (null == dto.getManagerStatus()
                            || null == dto.getManagerStatus().getStatus()
                            || !dto.getManagerStatus().getStatus())) {
                        log.info("支付失败，请联系运营平台予以解决。经纪人未开户");
                        return ResultUtil.error("支付失败！经纪人未开通华夏支付，请联系运营平台予以解决。");
                    }
                }
                return openRoleStatus;
            }
        }
        return ResultUtil.error("支付失败！请联系运营平台予以解决。");
    }

    /**
     * @param
     * @return
     * @description 查询华夏钱包
     * <AUTHOR>
     * @date 2021/8/9 9:10
     */
    public ResultUtil selectOpenRoleWallet(SelectOpenRoleVO selectOpenRoleVO) {
        ResultUtil openRoleWallet = openRoleAPI.selectCarrierCompanyEnduserOpenRoleWallet(selectOpenRoleVO);
        if (null != openRoleWallet.getCode() && DictEnum.SUCCESS.code.equals(openRoleWallet.getCode())) {
            if (null != openRoleWallet.getData()) {
                String jsonStr = JSONUtil.toJsonStr(openRoleWallet.getData());
                HXOpenRoleWalletListDTO openRoleWalletListDTO = JSONUtil.toBean(jsonStr, HXOpenRoleWalletListDTO.class);
                if (null == openRoleWalletListDTO.getCarrierWallet()) {
                    log.error("支付失败，请联系运营平台予以解决。未找到承运方钱包");
                    return ResultUtil.error("支付失败，请联系运营平台予以解决。");
                }
                if (null == openRoleWalletListDTO.getCompanyWallet()) {
                    log.error("支付失败，请联系运营平台予以解决。未找到企业钱包");
                    return ResultUtil.error("支付失败，请联系运营平台予以解决。");
                }
                if (null != selectOpenRoleVO.getDriverId()
                        && null == openRoleWalletListDTO.getDriverWallet()) {
                    log.error("支付失败，请联系运营平台予以解决。未找到司机钱包");
                    return ResultUtil.error("支付失败，请联系运营平台予以解决。");
                }
                if (DictEnum.PAYTOCAPTAIN.code.equals(selectOpenRoleVO.getCapitalTransferType())) {
                    if (null == openRoleWalletListDTO.getCaptionWallet()) {
                        log.error("支付失败，请联系运营平台予以解决。未找到车队长钱包");
                        return ResultUtil.error("支付失败，请联系运营平台予以解决。");
                    }
                }
                if (DictEnum.MANAGERPATTERN.code.equals(selectOpenRoleVO.getCaptinanTransferPattern())) {
                    if (null == openRoleWalletListDTO.getManagerWallet()) {
                        log.error("支付失败，请联系运营平台予以解决。未找到经纪人钱包");
                        return ResultUtil.error("支付失败，请联系运营平台予以解决。");
                    }
                }
            }
            return openRoleWallet;
        } else {
            return ResultUtil.error("支付失败，请联系运营平台予以解决。");
        }
    }

    /**
     * @return 12:00 下午
     * <AUTHOR>
     * @Description 检测司机车辆认证状态
     * @Date 2020/3/3 12:00 下午
     * @Param
     **/
    private String checkUserCarStatus(TOrderInfo orderInfo, String capitalTransferType, String capitalTransferPattern) {
        CarDriverRelVO carDriverRelVO = new CarDriverRelVO();
        carDriverRelVO.setEndcarId(orderInfo.getVehicleId());
        carDriverRelVO.setEndDriverId(orderInfo.getEndDriverId());
        if (DictEnum.PAYTOBELONGER.code.equals(capitalTransferType)) {
            if (null != orderInfo.getEndCarOwnerId()) {
                carDriverRelVO.setEndCarOwnerId(orderInfo.getEndCarOwnerId());
            }
        }
        if (DictEnum.PAYTOCAPTAIN.code.equals(capitalTransferType)) {
            if (null != orderInfo.getEndCarOwnerId()) {
                carDriverRelVO.setEndCarOwnerId(orderInfo.getEndCarOwnerId());
            }
        }
        if (DictEnum.MANAGERPATTERN.code.equals(capitalTransferPattern)) {
            if (null != orderInfo.getAgentId()) {
                carDriverRelVO.setAgentId(orderInfo.getAgentId());
            }
        }
        ResultUtil endcarAndUserAuditStatus = appCommonAPI.selectEndcarAndUserAuditStatus(carDriverRelVO);
        LinkedHashMap data = (LinkedHashMap) endcarAndUserAuditStatus.getData();
        ObjectMapper objectMapper = new ObjectMapper();
        EnduserCarStatus enduserCarStatus = objectMapper.convertValue(data, EnduserCarStatus.class);
        StringBuffer message = new StringBuffer();
        String msgPre = "支付请求失败: ";
        if (null != enduserCarStatus.getCarAuditStatus()) {
            if (!enduserCarStatus.getCarAuditStatus().equals(DictEnum.PASSNODE.code)) {
                message.append(enduserCarStatus.getVehicleNumber() + ":" + "认证未通过" + ".");
            }
        } else {
            message.append(enduserCarStatus.getVehicleNumber() + ":" + "认证未通过" + ".");
        }
        if (null != enduserCarStatus.getUserAuditStatus()) {
            if (!enduserCarStatus.getUserAuditStatus().equals(DictEnum.PASSNODE.code)) {
                if (null == enduserCarStatus.getRealName() || StringUtils.isBlank(enduserCarStatus.getRealName())) {
                    enduserCarStatus.setRealName("司机");
                }
                message.append(enduserCarStatus.getRealName() + ":" + "认证未通过" + ".");
            }
        } else {
            message.append(enduserCarStatus.getRealName() + ":" + "认证未通过" + ".");
        }
        if (DictEnum.PAYTOBELONGER.code.equals(capitalTransferType)) {
            if (null != enduserCarStatus.getEndCarOwnerAuditStatus()) {
                if (!DictEnum.PASSNODE.code.equals(enduserCarStatus.getEndCarOwnerAuditStatus())) {
                    message.append(enduserCarStatus.getEndCarOwnerName() + ":" + "认证未通过" + ".");
                }
            }
        }
        if (DictEnum.PAYTOCAPTAIN.code.equals(capitalTransferType)) {
            if (null != enduserCarStatus.getEndCarOwnerAuditStatus()) {
                if (!DictEnum.PASSNODE.code.equals(enduserCarStatus.getEndCarOwnerAuditStatus())) {
                    if (null == enduserCarStatus.getEndCarOwnerName() || StringUtils.isBlank(enduserCarStatus.getEndCarOwnerName())) {
                        enduserCarStatus.setEndCarOwnerName("车队长");
                    }
                    message.append(enduserCarStatus.getEndCarOwnerName() + ":" + "认证未通过" + ".");
                }
            }
        }
        if (DictEnum.MANAGERPATTERN.code.equals(capitalTransferPattern)) {
            if (null != enduserCarStatus.getAgentAuditStatus()) {
                if (!DictEnum.PASSNODE.code.equals(enduserCarStatus.getAgentAuditStatus())) {
                    message.append(enduserCarStatus.getAgentName() + ":" + "认证未通过" + ".");
                }
            }
        }
        if (message.length() > 0) {
            return msgPre + message.toString();
        }
        return null;
    }

    /**
     * @return 9:49 上午
     * <AUTHOR>
     * @Description 检查运单C端用户id
     * @Date 2020/2/18 9:49 上午
     * @Param
     **/
    private ResultUtil getEnduserIdByCapitalTransferType(String capitalTransferType, TOrderInfo orderInfo) {
        Integer enduserId;
        String orderBusinessCodeSplit = "尾号为" + orderInfo.getOrderBusinessCode().substring(orderInfo.getOrderBusinessCode().length() - 8);
        if (capitalTransferType.equals(DictEnum.PAYTODRIVER.code)) {
            enduserId = orderInfo.getEndDriverId();
        } else if (capitalTransferType.equals(DictEnum.FIRSTBROKERDRIVER.code)) {
            if (null == orderInfo.getEndAgentId()) {
                return ResultUtil.error(orderBusinessCodeSplit + "的运单， 缺少业务部数据,请在运单检查中选择业务部",
                        orderInfo.getOrderBusinessCode());
            }
            enduserId = orderInfo.getEndDriverId();
        } else if (capitalTransferType.equals(DictEnum.PAYTOBELONGER.code)) {
            if (null == orderInfo.getEndCarOwnerId()) {
                return ResultUtil.error(orderBusinessCodeSplit + "的运单， 缺少车老板数据,请在运单检查中选择车老板",
                        orderInfo.getOrderBusinessCode());
            }
            enduserId = orderInfo.getEndCarOwnerId();
        } else if (capitalTransferType.equals(DictEnum.PAYTOCAPTAIN.code)) {
            if (null == orderInfo.getEndCarOwnerId()) {
                return ResultUtil.error(orderBusinessCodeSplit + "的运单， 缺少车队长数据,请在运单检查中选择车队长",
                        orderInfo.getOrderBusinessCode());
            }
            enduserId = orderInfo.getEndCarOwnerId();
        } else if (capitalTransferType.equals(DictEnum.FIRSTBROKERBELONGER.code)) {
            if (null == orderInfo.getEndCarOwnerId()) {
                ResultUtil error = ResultUtil.error(orderBusinessCodeSplit + "的运单， 缺少车老板数据,请在运单检查中选择车老板",
                        orderInfo.getOrderBusinessCode());
                return error;
            }
            if (null == orderInfo.getEndAgentId()) {
                ResultUtil error = ResultUtil.error(orderBusinessCodeSplit + "的运单， 缺少业务部数据,请在运单检查中选择业务部",
                        orderInfo.getOrderBusinessCode());
                return error;
            }
            enduserId = orderInfo.getEndCarOwnerId();
        } else {
            return ResultUtil.error(orderBusinessCodeSplit + "的运单， 资金转移方式未找到",
                    orderInfo.getOrderBusinessCode());
        }
        return ResultUtil.ok(enduserId);
    }

    /**
     * @return
     * <AUTHOR>
     * @Description 查询C端默认银行卡
     * @Date 2020/8/3 上午11:51
     * @Param
     **/
    public TZtBankCard selectDefaultBankCard(Integer enduserId) {
        try {
            TZtBankCard tZtBankCard = tZtBankUserAPI.selectBankByEndUserId(enduserId);
            if (null == tZtBankCard.getAcctNo()
                    || null == tZtBankCard.getAcctName()
                    || null == tZtBankCard.getOccBankPhone()
                    || StringUtils.isBlank(tZtBankCard.getAcctNo())
                    || StringUtils.isBlank(tZtBankCard.getAcctName())
                    || StringUtils.isBlank(tZtBankCard.getOccBankPhone())) {
                throw new RuntimeException("持卡人信息不完整，请先完善信息");
            }
            if (null == tZtBankCard.getAcctCard() || StringUtils.isBlank(tZtBankCard.getAcctCard())) {
                throw new RuntimeException("持卡人未绑定身份证，请先绑定身份证");
            }
            return tZtBankCard;
        } catch (Exception e) {
            log.info("查询银行卡失败, {}", e);
            e.printStackTrace();
            if (null != e.getMessage() && StringUtils.checkChineseCharacter(e.getMessage())) {
                throw new RuntimeException(e.getMessage());
            }
            throw new RuntimeException("查询银行卡失败");
        }
    }

    /**
     * @param
     * @return
     * @description 检测银行卡
     * <AUTHOR>
     * @date 21/6/2021 09:39
     */
    public String checkCardholder(TZtBankCard tZtBankCard) {
        String error = "";
        try {
            if (null != tZtBankCard) {
                if (null == tZtBankCard.getAcctNo() || null == tZtBankCard.getAcctName()
                        || StringUtils.isBlank(tZtBankCard.getAcctNo()) || StringUtils.isBlank(tZtBankCard.getAcctName())) {
                    error = "持卡人信息不完整，请先完善信息";
                }
                if (null == tZtBankCard.getAcctCard() || StringUtils.isBlank(tZtBankCard.getAcctCard())) {
                    error = "持卡人未绑定身份证，请先绑定身份证";
                }
            } else {
                error = "未找到银行卡";
            }
        } catch (Exception e) {
            log.info("检测持卡人失败, {}", e);
            e.printStackTrace();
            if (null != e.getMessage() && StringUtils.checkChineseCharacter(e.getMessage())) {
                error = e.getMessage();
            } else {
                error = "申请失败";
            }
        }
        return error;
    }

    /**
     * @return
     * <AUTHOR>
     * @Description 判断打包运单司机、车主、车辆审核状态
     * @Date 2020/9/1 4:53 下午
     * @Param
     **/
    public ResultUtil checkPackOrderUserCarStatus(String code, String capitalTransferType, String capitalTransferPattern) {
        List<OrderPackDTO> orderPackDTOS = orderInfoMapper.selectDriverOnwerCarStatusByPackCode(code);
        HashSet<OrderPackDTO> driverHashSet = new HashSet<>();
        HashSet<OrderPackDTO> ownerHashSet = new HashSet<>();
        HashSet<OrderPackDTO> carHashSet = new HashSet<>();
        for (OrderPackDTO dto : orderPackDTOS) {
            if (!DictEnum.PASSNODE.code.equals(dto.getDriverStatus())) {
                OrderPackDTO orderPackDTO = new OrderPackDTO();
                orderPackDTO.setEndDriverId(dto.getEndDriverId());
                orderPackDTO.setRealName(dto.getRealName());
                orderPackDTO.setPhone(dto.getPhone());
                driverHashSet.add(orderPackDTO);
            }
            if (!DictEnum.PASSNODE.code.equals(dto.getCarStatus())) {
                OrderPackDTO carDTO = new OrderPackDTO();
                carDTO.setVehicleNumber(dto.getVehicleNumber());
                carHashSet.add(carDTO);
            }
            if (DictEnum.PAYTOBELONGER.code.equals(capitalTransferType)) {
                if (!DictEnum.PASSNODE.code.equals(dto.getCarOwnerStatus())) {
                    OrderPackDTO ownerDTO = new OrderPackDTO();
                    ownerDTO.setEndCarOwnerId(dto.getEndCarOwnerId());
                    ownerDTO.setCarOwnerName(dto.getCarOwnerName());
                    ownerDTO.setCarOwnerPhone(dto.getCarOwnerPhone());
                    ownerHashSet.add(ownerDTO);
                }
            }
        }
        StringBuffer driverMsg = new StringBuffer();
        StringBuffer carMsg = new StringBuffer();
        StringBuffer ownerMsg = new StringBuffer();
        if (!driverHashSet.isEmpty()) {
            for (OrderPackDTO dto : driverHashSet) {
                if (driverMsg.length() != 0) {
                    driverMsg.append("、");
                }
                driverMsg.append(dto.getRealName()).append(dto.getPhone());
            }
        }
        if (!carHashSet.isEmpty()) {
            for (OrderPackDTO dto : carHashSet) {
                if (carMsg.length() != 0) {
                    carMsg.append("、");
                }
                carMsg.append(dto.getVehicleNumber());
            }
        }
        if (!ownerHashSet.isEmpty()) {
            for (OrderPackDTO dto : ownerHashSet) {
                if (ownerMsg.length() != 0) {
                    ownerMsg.append("、");
                }
                ownerMsg.append(dto.getCarOwnerName()).append(dto.getCarOwnerPhone());
            }
        }
        HashMap<String, String> msg = new HashMap<>();
        if (driverMsg.length() > 0) {
            msg.put("driver", driverMsg.append("认证不通过").toString());
        }
        if (carMsg.length() > 0) {
            msg.put("car", carMsg.append("认证不通过").toString());
        }
        if (ownerMsg.length() > 0) {
            msg.put("owner", ownerMsg.append("认证不通过").toString());
        }
        if (!msg.isEmpty()) {
            msg.put("status", "true");
            return ResultUtil.error(JSONUtil.toJsonStr(msg));
        }

        return null;
    }

    public Map<Integer, TEndUserOpenRole> queryEnduserPartnerAccId(List<Integer> enduserIds) {
        // 查询C端开户信息
        SelectOpenRoleInfo openRoleInfo = new SelectOpenRoleInfo();
        openRoleInfo.setEnduserIds(enduserIds);
        ResultUtil openRoleStatus = openRoleAPI.selectEnduserOpenRoleInfo(openRoleInfo);
        OpenRoleInfoListDTO openRoleStatusListDTO = JSONUtil.toBean(JSONUtil.parseObj(openRoleStatus.getData()), OpenRoleInfoListDTO.class);
        Map<Integer, TEndUserOpenRole> map = new HashMap<>();
        for (TEndUserOpenRole tEndUserOpenRole : openRoleStatusListDTO.getOpenRoleInfoDTOList()) {
            map.put(tEndUserOpenRole.getEndUserId(), tEndUserOpenRole);
        }
        return map;
    }

    /**
    * @description 获取提现手续费
    * <AUTHOR>
    * @date 2021/11/23 11:24
    */
    public BigDecimal getTxServiceFee(BigDecimal amount, String openAccountType) {
        try {
            TServiceFeeQueryVO vo = new TServiceFeeQueryVO();
            vo.setAmount(amount);
            vo.setOpenAccountType(openAccountType);
            ResultUtil resultUtil = servicefeeInfoAPI.selectEnduserTxFee(vo);
            Object data = resultUtil.getData();
            return new BigDecimal(data.toString());
        } catch (Exception e) {
            log.error("查询自然人提现手续费失败, {}", e);
        }
        return BigDecimal.ZERO;
    }


    /**
    * @description 查询银行卡开户状态
    * <AUTHOR>
    * @date 2021/12/13 12:29
    */
    public TZtBankCardDTO checkBankCardOpenStatus(Integer endUserId, Integer bankCardId, String userLogisticsRole) {
        // 查询银行卡是否绑定华夏账户
        TZtBankCardDTO ztBankCardDTO = hxPayBankCardService.selectOpenRoleBankCardInfo(bankCardId);
        if (null == ztBankCardDTO || null == ztBankCardDTO.getBindStatus()) {
            throw new RuntimeException("银行卡未绑定华夏账户，请联系运营平台予以解决。");
        }
        ztBankCardDTO = hxPayBankCardService
                .selectByEnduserIdAndBankCardId(endUserId, bankCardId);
        if (null == ztBankCardDTO || null == ztBankCardDTO.getBindStatus()) {
            throw new RuntimeException("银行卡未绑定华夏账户，请联系运营平台予以解决。");
        } else {
            if (null != ztBankCardDTO.getBindStatus() && !JdEnum.BIND.code.equals(ztBankCardDTO.getBindStatus())) {
                throw new RuntimeException("银行卡绑定失败，请联系运营平台予以解决。");
            }
        }
        return ztBankCardDTO;
    }

    /**
     * 查询运单是否已经开票
     * @return
     */
    public String checkInvoiceOrder(List<String> orderCodes) {
        if (null == orderCodes || orderCodes.isEmpty()) {
            return null;
        }
        // 查询是否取消开票
        Long orderInvoice = orderInvoiceInfoMapper.selectOrderCancelInvoice(orderCodes);
        if (null == orderInvoice || orderInvoice == 0) {
            // 查询是否开票
            orderInvoice = orderInvoiceInfoMapper.selectOrderInvoice(orderCodes);
            if (null == orderInvoice || orderInvoice == 0) {
                // 查询是否对账
                Long aLong = orderInvoiceInfoMapper.selectOrderInvoiceCheck(orderCodes);
                if (null == aLong || aLong == 0) {
                    return null;
                } else {
                    return "召回失败，对账中或对账完成的运单不可召回。";
                }
            } else {
                return "召回失败，已申请或已开票的运单不可召回。";
            }
        } else {
            return "召回失败，取消开票的运单不可召回。";
        }

    }

}
