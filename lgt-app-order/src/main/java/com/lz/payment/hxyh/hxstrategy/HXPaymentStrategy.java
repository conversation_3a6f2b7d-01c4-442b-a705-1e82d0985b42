package com.lz.payment.hxyh.hxstrategy;

import cn.hutool.json.JSONUtil;
import com.lz.api.MqAPI;
import com.lz.common.config.HXPropertiesConfig;
import com.lz.common.dbenum.BusinessCode;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.HXTradeTypeEnum;
import com.lz.common.factory.HXMQMessageFactory;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.MQMessage;
import com.lz.common.util.*;
import com.lz.dao.TOrderCastChangesMapper;
import com.lz.dao.TOrderInfoMapper;
import com.lz.dto.OpenRoleStatusListDTO;
import com.lz.dto.OpenRoleWalletListDTO;
import com.lz.model.TOrderCastChanges;
import com.lz.model.TOrderInfo;
import com.lz.model.TOrderPayInfo;
import com.lz.payment.TradeType;
import com.lz.payment.hxyh.HXPayOrderUtil;
import com.lz.payment.hxyh.HXPaymentUtil;
import com.lz.payment.hxyh.HXWalletUtil;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.vo.SelectOpenRoleVO;
import commonSdk.requestModel.CustomerBalancePayRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 *  @author: dingweibo
 *  @Date: 2022/11/24 10:25
 *  @Description: 京东支付实现
 */
@Slf4j
@Service
public class HXPaymentStrategy extends HPaymentStrategy {

    @Resource
    private TOrderInfoMapper tOrderInfoMapper;

    @Resource
    private TOrderCastChangesMapper tOrderCastChangesMapper;

    @Resource
    private HXWalletUtil hxWalletUtil;

    @Resource
    private HXPaymentUtil hxPaymentUtil;

    @Resource
    private HXPayOrderUtil hxPayOrderUtil;

    @Resource
    private HXPropertiesConfig hxPropertiesConfig;

    @Resource
    private MqAPI mqAPI;

    @Resource
    private SysParamAPI sysParamAPI;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil toPay(TOrderInfo tOrderInfo) {
        try {
            TOrderCastChanges cc = tOrderCastChangesMapper.selectByNewOne(tOrderInfo.getCode());

            // 设置查询参数
            SelectOpenRoleVO openRoleVO = new SelectOpenRoleVO();
            openRoleVO.setCarrierId(tOrderInfo.getCarrierId());
            openRoleVO.setCompanyId(tOrderInfo.getCompanyId());
            openRoleVO.setDriverId(tOrderInfo.getEndDriverId());
            if (DictEnum.PAYTOCAPTAIN.code.equals(cc.getCapitalTransferType())) {
                openRoleVO.setCaptainId(tOrderInfo.getEndCarOwnerId());
            }
            if (DictEnum.MANAGERPATTERN.code.equals(cc.getCapitalTransferPattern())) {
                openRoleVO.setManagerId(tOrderInfo.getAgentId());
            }
            // 查询钱包
            ResultUtil openRoleWallet = hxPayOrderUtil.selectOpenRoleWallet(openRoleVO);
            if (null == openRoleWallet || DictEnum.ERROR.code.equals(openRoleWallet.getCode())) {
                throw new RuntimeException("支付失败，请联系运营平台予以解决。");
            }
            OpenRoleWalletListDTO walletListDTO = JSONUtil.toBean(JSONUtil.toJsonStr(openRoleWallet.getData()), OpenRoleWalletListDTO.class);

            TOrderCastChanges castChanges = new TOrderCastChanges();
            BeanUtils.copyProperties(cc, castChanges);
            castChanges.setId(null);
            castChanges.setCode(IdWorkerUtil.getInstance().nextId());
            castChanges.setUserOper(TradeType.PayMent.code);
            castChanges.setTradeType(TradeType.RZ.code);
            // 修改京东钱包id
            castChanges.setCarrierWalletId(walletListDTO.getCarrierWallet().getId());
            castChanges.setCompanyWalletId(walletListDTO.getCompanyWallet().getId());
            castChanges.setEndDriverWalletId(walletListDTO.getDriverWallet().getId());
            castChanges.setCreateUser(CurrentUser.getUserNickname());
            castChanges.setUpdateUser(CurrentUser.getUserNickname());
            castChanges.setCreateTime(new Date());
            castChanges.setUpdateTime(new Date());
            if (tOrderInfo.getPackStatus().equals(DictEnum.SINGLE.code)) {
                //单笔支付：如果会计支付时重新修改了运费
                if (castChanges.getCarriageFee().compareTo(tOrderInfo.getUserConfirmPaymentAmount()) != 0) {
                    // 重新计算调度费，并修改运单主表；重新冻结企业钱包。
                    reCalculateDispatchFee(tOrderInfo, castChanges);
                }
            }

            castChanges.setServiceFee(tOrderInfo.getUserConfirmServiceFee());
            // 添加最新资金变动
            tOrderCastChangesMapper.insertSelective(castChanges);
            cc.setDataEnable(false);
            // 将上一条资金变动置为无效
            tOrderCastChangesMapper.updateByPrimaryKeySelective(cc);

            //修改企业钱包,将总费用挪到入账中
            hxWalletUtil.modifyWallet(castChanges.getCompanyWalletId(), castChanges.getTotalFee(), tOrderInfo.getFeeSettlementWay());
            // 添加支付主表
            TOrderPayInfo orderPayInfo = hxPaymentUtil.createOrderPayInfo(tOrderInfo);
            // 插入支付字表
            String orderPayDetailCode = hxPaymentUtil.createTxOrderPayDetail(orderPayInfo.getCode(),
                    castChanges.getCode(), HXTradeTypeEnum.HX_COMBALANCE_PAY.code, TradeType.RZ.code);

            // TODO 查询出款方、入款方会员编号
            SelectOpenRoleVO vo = new SelectOpenRoleVO();
            vo.setCarrierId(tOrderInfo.getCarrierId());
            vo.setCompanyId(tOrderInfo.getCompanyId());
            ResultUtil openRoleStatus = hxPayOrderUtil.selectOpenRoleStatus(vo);
            if (DictEnum.ERROR.code.equals(openRoleStatus.getCode())) {
                throw new RuntimeException(openRoleStatus.getMsg());
            }
            String jsonStr = JSONUtil.toJsonStr(openRoleStatus.getData());
            OpenRoleStatusListDTO openRoleStatusListDTO = JSONUtil.toBean(jsonStr, OpenRoleStatusListDTO.class);
            // TODO 构造支付请求参数
            CustomerBalancePayRequest req = new CustomerBalancePayRequest();
            req.setPartnerId(hxPropertiesConfig.getPartnerId());
            req.setRequestId(IdWorkerUtil.getInstance().nextId());
            req.setRequestTime(DateUtils.getRequestTime());
            req.setChannelId(hxPropertiesConfig.getChannelId());
            req.setBizOrderNo(orderPayDetailCode);
            req.setOutPartnerAccId(openRoleStatusListDTO.getCompanyStatus().getPartnerAccId());
            req.setInPartnerAccId(openRoleStatusListDTO.getCarrierStatus().getPartnerAccId());
            req.setOrderAmount(tOrderInfo.getTotalFee());
            req.setTradeAbstract("运费");
            // 查询回调地址
            SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.HXPAYNOTIFYURL.code);
            req.setNotifyUrl(paramByKey.getParamValue());

            MQMessage mqMessage = HXMQMessageFactory.getMessage(HXTradeTypeEnum.HX_COMBALANCE_PAY.code,
                    castChanges.getCapitalTransferType(), castChanges.getCapitalTransferPattern());
            mqMessage.setKey(tOrderInfo.getCode());
            mqMessage.setBody(req);
            ResultUtil resultUtil = mqAPI.sendMessage(mqMessage);
            log.info("发送MQ消息结果，{}", JSONUtil.toJsonStr(resultUtil));
            if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
                throw new RuntimeException("ZJJ-210:支付失败！");
            }
        } catch (Exception e) {
            log.error("ZJJ-210:支付失败！,{}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                throw new RuntimeException(BusinessCode.PROJECTWALLETINSUFFICIENT.code);
            }
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                throw new RuntimeException(BusinessCode.WALLETAMOUNTINSUFFICIENT.code);
            }
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            }
            throw new RuntimeException("ZJJ-210:支付失败！");
        }

        return ResultUtil.ok();
    }

    /**
    * @description 重新计算调度费
    * <AUTHOR>
    * @date 2021/8/12 08:47
    * @param
    * @return
    */
    private void reCalculateDispatchFee(TOrderInfo tOrderInfo, TOrderCastChanges castChanges) {
        //重新计算调度费
        BigDecimal disFee = OrderMoneyUtil.getDispatchFee(castChanges.getCurrentDispatchRate(), tOrderInfo.getUserConfirmPaymentAmount());
        castChanges.setDispatchFee(disFee);
        castChanges.setCarriageFee(tOrderInfo.getUserConfirmPaymentAmount());
        BigDecimal total = castChanges.getDispatchFee().add(castChanges.getCarriageFee());
        castChanges.setTotalFee(total);
        tOrderInfo.setTotalFee(total);
        tOrderInfo.setDispatchFee(disFee);
        tOrderInfoMapper.updateByPrimaryKeySelective(tOrderInfo);
    }

}
