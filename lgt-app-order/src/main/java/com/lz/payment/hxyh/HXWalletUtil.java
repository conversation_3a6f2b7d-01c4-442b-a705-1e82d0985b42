package com.lz.payment.hxyh;

import cn.hutool.json.JSONUtil;
import com.lz.common.dbenum.BusinessCode;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.HXTradeTypeEnum;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.StringUtils;
import com.lz.common.util.ThrowableUtil;
import com.lz.dao.*;
import com.lz.dto.THxOrderPayInfoDTO;
import com.lz.dto.TOrderPayRequestDTO;
import com.lz.dto.TTxOrderPayDetailDTO;
import com.lz.model.TOrderInsurance;
import com.lz.model.TOrderPayRule;
import com.lz.model.TZtWallet;
import com.lz.util.OrderDeductionUtil;
import com.lz.vo.RecallWalletVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import sdk.model.message.AsyncNotifyVirtualBalancePayMessageBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 华夏钱包工具类
 */
@Slf4j
@Component
public class HXWalletUtil {
    @Resource
    private TZtOrderWalletMapper walletMapper;
    @Resource
    private THXPayWalletMapper hxPayWalletMapper;
    @Resource
    private THXOrderPayDetailMapper orderPayDetailMapper;
    @Resource
    private TOrderPayRuleMapper orderPayRuleMapper;
    @Resource
    private TOrderInsuranceMapper orderInsuranceMapper;
    @Resource
    private TOrderPackInfoMapper orderPackInfoMapper;
    @Resource
    private TOrderPayRequestMapper orderPayRequestMapper;

    public TZtWallet selectByPartnerAccId(String partnerAccId) {
        return walletMapper.selectByPartnerAccId(partnerAccId);
    }

    public void updateByPrimaryKey(TZtWallet wallet) {
        walletMapper.updateByPrimaryKeySelective(wallet);
    }

    public void carrierBalanceCompanyChargeCallbackModifyWallet(AsyncNotifyVirtualBalancePayMessageBody messageBody, THxOrderPayInfoDTO dto, Boolean isSuccessed) {
        if (isSuccessed) {
            TZtWallet outWallet = walletMapper.selectByPartnerAccId(messageBody.getOutPartnerAccId());
            log.info("华夏支付 - 承运方余额支付(企业充值)回调承运方钱包, {}", JSONUtil.toJsonStr(outWallet));
            BigDecimal entryAmount = outWallet.getEntryAmount().subtract(dto.getOrderTotalPayment());
            outWallet.setEntryAmount(entryAmount);
            outWallet.setUpdateTime(new Date());
            walletMapper.updateByPrimaryKeySelective(outWallet);
            TZtWallet inWallet = walletMapper.selectByPartnerAccId(messageBody.getInPartnerAccId());
            log.info("华夏支付 - 承运方余额支付(企业充值)回调企业钱包, {}", JSONUtil.toJsonStr(inWallet));
            BigDecimal accountBalance = inWallet.getAccountBalance().add(dto.getOrderTotalPayment());
            BigDecimal accountInc = inWallet.getAccountInc().add(dto.getOrderTotalPayment());
            inWallet.setAccountBalance(accountBalance);
            inWallet.setAccountInc(accountInc);
            inWallet.setUpdateTime(new Date());
            walletMapper.updateByPrimaryKeySelective(inWallet);
        } else {
            TZtWallet outWallet = walletMapper.selectByPartnerAccId(messageBody.getOutPartnerAccId());
            log.info("华夏支付 - 承运方余额支付(企业充值)回调失败, 承运方钱包, {}", JSONUtil.toJsonStr(outWallet));
            BigDecimal entryAmount = outWallet.getEntryAmount().subtract(dto.getOrderTotalPayment());
            BigDecimal accountBalance = outWallet.getAccountBalance().add(dto.getOrderTotalPayment());
            outWallet.setEntryAmount(entryAmount);
            outWallet.setAccountBalance(accountBalance);
            outWallet.setUpdateTime(new Date());
            walletMapper.updateByPrimaryKeySelective(outWallet);
        }

    }

    /**
    * @description 根据partnerAccId查询钱包
    * <AUTHOR>
    * @date 2021/9/13 09:03
    */
    public TZtWallet selectByCarrierCompanyPartnerAccId(String partnerAccId, String dataSource) {
        return hxPayWalletMapper.selectByCarrierCompanyPartnerAccId(partnerAccId, dataSource);
    }

    public TZtWallet selectById(Integer id) {
        return hxPayWalletMapper.selectByPrimaryKey(id);
    }

    public TZtWallet selectByEnduserId(Integer enduserId) {
        return hxPayWalletMapper.selectByEndUserId(enduserId);
    }

    public TZtWallet selectByEnduserPartnerAccId(String partnerAccId) {
        return hxPayWalletMapper.selectByEnduserPartnerAccId(partnerAccId);
    }

    public void modifyWallet(Integer companyWalletId, BigDecimal totalFee, String feeSettlementWay) {
        // 查询企业钱包
        TZtWallet tZtWallet = hxPayWalletMapper.selectByPrimaryKey(companyWalletId);
        log.info("修改企业钱包, {}", JSONUtil.toJsonStr(tZtWallet));
        // 判断企业余额是否充足
        if (tZtWallet.getAccountBalance().compareTo(totalFee) < 0) {
            throw new RuntimeException(BusinessCode.WALLETAMOUNTINSUFFICIENT.code);
        }
        TZtWallet companyWallet = new TZtWallet();
        companyWallet.setId(tZtWallet.getId());
        BigDecimal newAccountBalance = tZtWallet.getAccountBalance().subtract(totalFee);
        companyWallet.setAccountBalance(newAccountBalance);
        // 入账中添加总运费
        BigDecimal newEntryAmount = tZtWallet.getEntryAmount().add(totalFee);
        companyWallet.setEntryAmount(newEntryAmount);
        companyWallet.setUpdateTime(new Date());
        companyWallet.setUpdateUser(CurrentUser.getUserNickname());
        hxPayWalletMapper.updateByPrimaryKeySelective(companyWallet);
    }

    public void callbackModifyWallet(THxOrderPayInfoDTO dto) {
        // 企业余额支付
        if (HXTradeTypeEnum.HX_COMBALANCE_PAY.code.equals(dto.getTradeType())) {
            log.info("企业余额支付回调，修改钱包");
            // 修改企业钱包
            TZtWallet companyWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCompanyWalletId());
            log.info("企业钱包, {}", JSONUtil.toJsonStr(companyWallet));
            // 入账中 - 总运费
            BigDecimal entryAmount = companyWallet.getEntryAmount().subtract(dto.getTotalFee());
            // 支出 + 总运费
            BigDecimal accountExp = companyWallet.getAccountExp().add(dto.getTotalFee());
            companyWallet.setAccountExp(accountExp);
            companyWallet.setEntryAmount(entryAmount);
            companyWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(companyWallet);
            // 发起承运方余额支付 -> 修改承运方钱包
            // 入账中 = 入账中 + 运费
            // 余额 = 余额 + 调度费
            TZtWallet carrierWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCarrierWalletId());
            log.info("承运方钱包, {}", JSONUtil.toJsonStr(carrierWallet));
            TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(dto.getOrderBusinessCode());
            BigDecimal insuredAmount = BigDecimal.ZERO;
            if (null != tOrderInsurance && OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance)) {
                insuredAmount = dto.getInsuredAmount();
            }
            BigDecimal accountBalance = carrierWallet.getAccountBalance().add(dto.getDispatchFee()).add(dto.getIllegalDeduction()).add(insuredAmount);
            entryAmount = carrierWallet.getEntryAmount().add(dto.getCarriageFee()).subtract(dto.getIllegalDeduction()).subtract(insuredAmount);
            BigDecimal accountInc = carrierWallet.getAccountInc().add(dto.getDispatchFee()).add(dto.getIllegalDeduction()).add(insuredAmount);
            carrierWallet.setAccountBalance(accountBalance);
            carrierWallet.setEntryAmount(entryAmount);
            carrierWallet.setAccountInc(accountInc);
            carrierWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(carrierWallet);

        } else if (HXTradeTypeEnum.HX_CABALANCE_PAY.code.equals(dto.getTradeType())) {
            log.info("承运方余额支付回调，修改钱包");
            //修改承运方钱包 入账中 = 入账中 - 运费
            TZtWallet carrierWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCarrierWalletId());
            log.info("承运方钱包, {}", JSONUtil.toJsonStr(carrierWallet));
            BigDecimal carriageFee = dto.getCarriageFee();
            // 查询是否支付保险
            TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(dto.getOrderBusinessCode());
            if (OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance)) {
                // 司机余额扣除保险金额
                carriageFee = carriageFee.subtract(dto.getInsuredAmount());
            }
            BigDecimal entryAmount = carrierWallet.getEntryAmount().subtract(carriageFee.subtract(dto.getIllegalDeduction()));
            carrierWallet.setEntryAmount(entryAmount);
            hxPayWalletMapper.updateByPrimaryKeySelective(carrierWallet);
            // 修改司机钱包
            TZtWallet driverWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getEndDriverWalletId());

            log.info("司机钱包, {}", JSONUtil.toJsonStr(driverWallet));
            if (DictEnum.COMMONPATTERN.code.equals(dto.getCapitalTransferPattern())
                    && DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())) {
                // 余额 = 余额 + 运费
                // 收入 = 收入 + 运费
                BigDecimal accountBalance = driverWallet.getAccountBalance().add(carriageFee.subtract(dto.getIllegalDeduction()));
                driverWallet.setAccountBalance(accountBalance);
                BigDecimal accountInc = driverWallet.getAccountInc().add(carriageFee).subtract(dto.getIllegalDeduction());
                driverWallet.setAccountInc(accountInc);
                driverWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(driverWallet);
            } else if (DictEnum.COMMONPATTERN.code.equals(dto.getCapitalTransferPattern())
                    && DictEnum.PAYTOCAPTAIN.code.equals(dto.getCapitalTransferType())){
                if (dto.getEndDriverWalletId().equals(dto.getCaptainWalletId())) {
                    // 余额 = 余额 + 运费
                    // 收入 = 收入 + 运费
                    BigDecimal accountBalance = driverWallet.getAccountBalance().add(carriageFee.subtract(dto.getIllegalDeduction()));
                    driverWallet.setAccountBalance(accountBalance);
                    BigDecimal accountInc = driverWallet.getAccountInc().add(carriageFee.subtract(dto.getIllegalDeduction()));
                    driverWallet.setAccountInc(accountInc);
                    driverWallet.setUpdateTime(new Date());
                    hxPayWalletMapper.updateByPrimaryKeySelective(driverWallet);
                } else {
                    // 修改司机钱包
                    // 入账中 = 入账中 + 运费
                    entryAmount = driverWallet.getEntryAmount().add(carriageFee.subtract(dto.getIllegalDeduction()));
                    driverWallet.setEntryAmount(entryAmount);
                    driverWallet.setUpdateTime(new Date());
                    hxPayWalletMapper.updateByPrimaryKeySelective(driverWallet);
                }
            } else if (DictEnum.MANAGERPATTERN.code.equals(dto.getCapitalTransferPattern())
                    && DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())){
                // 修改司机钱包
                // 入账中 = 入账中 + 运费
                // 余额 = 余额 + （运费 - 服务费)
                // 收入 = 收入 + （运费 - 服务费)
                BigDecimal accountBalance = driverWallet.getAccountBalance().add(carriageFee.subtract(dto.getIllegalDeduction()).subtract(dto.getServiceFee()));
                entryAmount = driverWallet.getEntryAmount().add(dto.getServiceFee());
                BigDecimal accountInc = driverWallet.getAccountInc().add(carriageFee.subtract(dto.getIllegalDeduction()).subtract(dto.getServiceFee()));
                driverWallet.setAccountBalance(accountBalance);
                driverWallet.setEntryAmount(entryAmount);
                driverWallet.setAccountInc(accountInc);
                driverWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(driverWallet);
            } else if (DictEnum.MANAGERPATTERN.code.equals(dto.getCapitalTransferPattern())
                    && DictEnum.PAYTOCAPTAIN.code.equals(dto.getCapitalTransferType())){
                if (dto.getEndDriverWalletId().equals(dto.getCaptainWalletId())) {
                    // 修改司机钱包
                    // 入账中 = 入账中 + 服务费
                    // 余额 = 余额 + （运费 - 服务费)
                    // 收入 = 收入 + （运费 - 服务费)
                    BigDecimal accountBalance = driverWallet.getAccountBalance().add(carriageFee.subtract(dto.getIllegalDeduction()).subtract(dto.getServiceFee()));
                    BigDecimal accountInc = driverWallet.getAccountInc().add(carriageFee.subtract(dto.getIllegalDeduction()).subtract(dto.getServiceFee()));
                    entryAmount = driverWallet.getEntryAmount().add(dto.getServiceFee());
                    driverWallet.setAccountBalance(accountBalance);
                    driverWallet.setAccountInc(accountInc);
                    driverWallet.setEntryAmount(entryAmount);
                    driverWallet.setUpdateTime(new Date());
                    hxPayWalletMapper.updateByPrimaryKeySelective(driverWallet);
                } else {
                    // 修改司机钱包
                    // 入账中 = 入账中 + 运费
                    entryAmount = driverWallet.getEntryAmount().add(carriageFee).subtract(dto.getIllegalDeduction());
                    driverWallet.setEntryAmount(entryAmount);
                    driverWallet.setUpdateTime(new Date());
                    hxPayWalletMapper.updateByPrimaryKeySelective(driverWallet);
                }
            }
        } else if (HXTradeTypeEnum.HX_CDBALANCE_PAY.code.equals(dto.getTradeType())) {
            log.info("司机余额支付回调，修改钱包");
            if (DictEnum.COMMONPATTERN.code.equals(dto.getCapitalTransferPattern())
                    && DictEnum.PAYTOCAPTAIN.code.equals(dto.getCapitalTransferType())) {
                // 普通模式 支付到车队长
                // 修改司机钱包 入账中 = 入账中 - 运费
                TZtWallet driverWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getEndDriverWalletId());
                log.info("司机钱包, {}", JSONUtil.toJsonStr(driverWallet));
                BigDecimal carriageFee = dto.getCarriageFee();
                // 查询是否支付保险
                TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(dto.getOrderBusinessCode());
                if (OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance)) {
                    // 司机余额扣除保险金额
                    carriageFee = carriageFee.subtract(dto.getInsuredAmount());
                }
                BigDecimal entryAmount = driverWallet.getEntryAmount().subtract(carriageFee.subtract(dto.getIllegalDeduction()));
                driverWallet.setEntryAmount(entryAmount);
                driverWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(driverWallet);
                // 修改车队长钱包余额 = 余额 + 运费；收入 = 收入 + 运费
                TZtWallet captainWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCaptainWalletId());
                log.info("车队长钱包, {}", JSONUtil.toJsonStr(captainWallet));
                BigDecimal accountBalance = captainWallet.getAccountBalance().add(carriageFee).subtract(dto.getIllegalDeduction());
                captainWallet.setAccountBalance(accountBalance);
                BigDecimal accountInc = captainWallet.getAccountInc().add(carriageFee).subtract(dto.getIllegalDeduction());
                captainWallet.setAccountInc(accountInc);
                captainWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(captainWallet);
            }
            if (DictEnum.MANAGERPATTERN.code.equals(dto.getCapitalTransferPattern())
                    && DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())) {
                // 经纪人模式 支付到司机
                // 修改司机钱包 入账中 = 入账中 - 运费
                TZtWallet driverWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getEndDriverWalletId());
                log.info("司机钱包, {}", JSONUtil.toJsonStr(driverWallet));
                BigDecimal entryAmount = driverWallet.getEntryAmount().subtract(dto.getServiceFee());
                driverWallet.setEntryAmount(entryAmount);
                driverWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(driverWallet);
                // 修改经纪人钱包 余额 = 余额 + 服务费；收入 = 收入 + 服务费
                TZtWallet agentWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getAgentWalletId());
                log.info("经纪人钱包, {}", JSONUtil.toJsonStr(agentWallet));
                BigDecimal accountBalance = agentWallet.getAccountBalance().add(dto.getServiceFee());
                agentWallet.setAccountBalance(accountBalance);
                BigDecimal accountInc = agentWallet.getAccountInc().add(dto.getServiceFee());
                agentWallet.setAccountInc(accountInc);
                agentWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(agentWallet);
            }
        } else if (HXTradeTypeEnum.HX_CCBALANCE_PAY.code.equals(dto.getTradeType())) {
            // 经纪人模式 司机支付车队长
            TZtWallet driverWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getEndDriverWalletId());
            log.info("司机钱包, {}", JSONUtil.toJsonStr(driverWallet));
            BigDecimal carriageFee = dto.getCarriageFee();
            // 查询是否支付保险
            TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(dto.getOrderBusinessCode());
            if (OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance)) {
                // 司机余额扣除保险金额
                carriageFee = carriageFee.subtract(dto.getInsuredAmount());
            }
            BigDecimal entryAmount = driverWallet.getEntryAmount().subtract(carriageFee.subtract(dto.getIllegalDeduction()).subtract(dto.getServiceFee()));
            driverWallet.setEntryAmount(entryAmount);
            driverWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(driverWallet);
            TZtWallet captainWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCaptainWalletId());
            log.info("车队长钱包, {}", JSONUtil.toJsonStr(captainWallet));
            BigDecimal accountBalance = captainWallet.getAccountBalance().add(carriageFee.subtract(dto.getIllegalDeduction()).subtract(dto.getServiceFee()));
            captainWallet.setAccountBalance(accountBalance);
            BigDecimal accountInc = captainWallet.getAccountInc().add(carriageFee.subtract(dto.getIllegalDeduction()).subtract(dto.getServiceFee()));
            captainWallet.setAccountInc(accountInc);
            captainWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(captainWallet);
        } else if (HXTradeTypeEnum.HX_CMBALANCE_PAY.code.equals(dto.getTradeType())) {
            TZtWallet driverWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getEndDriverWalletId());
            log.info("司机钱包, {}", JSONUtil.toJsonStr(driverWallet));
            BigDecimal entryAmount = driverWallet.getEntryAmount().subtract(dto.getServiceFee());
            driverWallet.setEntryAmount(entryAmount);
            driverWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(driverWallet);
            TZtWallet agentWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getAgentWalletId());
            log.info("经纪人钱包, {}", JSONUtil.toJsonStr(agentWallet));
            BigDecimal accountBalance = agentWallet.getAccountBalance().add(dto.getServiceFee());
            agentWallet.setAccountBalance(accountBalance);
            BigDecimal accountInc = agentWallet.getAccountInc().add(dto.getServiceFee());
            agentWallet.setAccountInc(accountInc);
            agentWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(agentWallet);
        } else if (HXTradeTypeEnum.HX_INSURANCE.code.equals(dto.getTradeType())) {
            TZtWallet driverWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getEndDriverWalletId());
            log.info("司机钱包, {}", JSONUtil.toJsonStr(driverWallet));
            BigDecimal entryAmount = driverWallet.getEntryAmount().subtract(dto.getInsuredAmount());
            driverWallet.setEntryAmount(entryAmount);
            driverWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(driverWallet);
            TZtWallet carrierWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCarrierWalletId());
            log.info("承运方钱包, {}", JSONUtil.toJsonStr(carrierWallet));
            BigDecimal accountAmount = carrierWallet.getAccountBalance().add(dto.getInsuredAmount());
            BigDecimal accountInc = carrierWallet.getAccountInc().add(dto.getInsuredAmount());
            carrierWallet.setAccountBalance(accountAmount);
            carrierWallet.setAccountInc(accountInc);
            hxPayWalletMapper.updateByPrimaryKeySelective(carrierWallet);
        }
    }

    /**
    * @description 支付回调失败，修改钱包
    * <AUTHOR>
    * @date 2021/8/26 20:33
    */
    public void failCallbackModifyWallet(THxOrderPayInfoDTO dto) {
        // 企业余额支付回调失败
        if (HXTradeTypeEnum.HX_COMBALANCE_PAY.code.equals(dto.getTradeType())) {
            // 修改企业钱包
            TZtWallet companyWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCompanyWalletId());
            log.info("企业余额支付回调失败, {}", companyWallet);
            // 入账中 - 总运费
            BigDecimal entryAmount = companyWallet.getEntryAmount().subtract(dto.getTotalFee());
            // 余额 + 总运费
            BigDecimal accountBalance = companyWallet.getAccountBalance().add(dto.getTotalFee());
            companyWallet.setAccountBalance(accountBalance);
            companyWallet.setEntryAmount(entryAmount);
            companyWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(companyWallet);
        } else if (HXTradeTypeEnum.HX_CABALANCE_PAY.code.equals(dto.getTradeType())) {
            // 承运方余额支付回调失败
            TZtWallet carrierWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCarrierWalletId());
            log.info("承运方余额支付回调失败, {}", carrierWallet);
            BigDecimal carriageFee = dto.getCarriageFee();
            TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(dto.getOrderBusinessCode());
            if (OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance)) {
                carriageFee = carriageFee.subtract(dto.getInsuredAmount());
            }
            BigDecimal entryAmount = carrierWallet.getEntryAmount().subtract(carriageFee.subtract(dto.getIllegalDeduction()));
            BigDecimal accountBalance = carrierWallet.getAccountBalance().add(carriageFee.subtract(dto.getIllegalDeduction()));
            carrierWallet.setEntryAmount(entryAmount);
            carrierWallet.setAccountBalance(accountBalance);
            carrierWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(carrierWallet);
        } else if (HXTradeTypeEnum.HX_CDBALANCE_PAY.code.equals(dto.getTradeType())) {
            // 司机余额支付回调失败
            if (DictEnum.COMMONPATTERN.code.equals(dto.getCapitalTransferPattern())
                    && DictEnum.PAYTOCAPTAIN.code.equals(dto.getCapitalTransferType())) {
                // 普通模式 支付到车队长
                TZtWallet driverWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getEndDriverWalletId());
                log.info("普通模式 司机支付到车队长, {}", driverWallet);
                BigDecimal carriageFee = dto.getCarriageFee();
                // 查询是否支付保险
                TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(dto.getOrderBusinessCode());
                if (OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance)) {
                    // 司机余额扣除保险金额
                    carriageFee = carriageFee.subtract(dto.getInsuredAmount());
                }
                BigDecimal entryAmount = driverWallet.getEntryAmount().subtract(carriageFee.subtract(dto.getIllegalDeduction()));
                BigDecimal accountBalance = driverWallet.getAccountBalance().add(carriageFee.subtract(dto.getIllegalDeduction()));
                driverWallet.setEntryAmount(entryAmount);
                driverWallet.setAccountBalance(accountBalance);
                driverWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(driverWallet);
            }
            if (DictEnum.MANAGERPATTERN.code.equals(dto.getCapitalTransferPattern())
                    && DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())) {
                // 经纪人 支付到司机
                TZtWallet driverWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getEndDriverWalletId());
                log.info("经纪人司机模式， 司机支付到经纪人回调失败, {}", driverWallet);
                BigDecimal entryAmount = driverWallet.getEntryAmount().subtract(dto.getServiceFee());
                BigDecimal accountBalance = driverWallet.getAccountBalance().add(dto.getServiceFee());
                driverWallet.setEntryAmount(entryAmount);
                driverWallet.setAccountBalance(accountBalance);

                driverWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(driverWallet);
            }
        } else if (HXTradeTypeEnum.HX_CMBALANCE_PAY.code.equals(dto.getTradeType())) {
            // 经纪人车队长模式
            // 司机支付到经纪人
            // 经纪人 支付到司机
            TZtWallet driverWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getEndDriverWalletId());
            log.info("经纪人车队长模式 支付到经纪人回调失败, {}", driverWallet);
            BigDecimal entryAmount = driverWallet.getEntryAmount().subtract(dto.getServiceFee());
            BigDecimal accountBalance = driverWallet.getAccountBalance().add(dto.getServiceFee());
            driverWallet.setEntryAmount(entryAmount);
            driverWallet.setAccountBalance(accountBalance);

            driverWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(driverWallet);
        }
    }

    public void companyPayCallbackModifyWallet(TOrderPayRequestDTO dto) {
        log.info("企业余额支付回调，修改钱包");
        // 修改企业钱包
        TZtWallet companyWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCompanyWalletId());
        log.info("企业钱包, {}", JSONUtil.toJsonStr(companyWallet));
        // 入账中 - 总运费
        BigDecimal entryAmount = companyWallet.getEntryAmount().subtract(dto.getOrderTotalPayment());
        // 支出 + 总运费
        BigDecimal accountExp = companyWallet.getAccountExp().add(dto.getOrderTotalPayment());
        companyWallet.setAccountExp(accountExp);
        companyWallet.setEntryAmount(entryAmount);
        companyWallet.setUpdateTime(new Date());
        hxPayWalletMapper.updateByPrimaryKeySelective(companyWallet);
        // 发起承运方余额支付 -> 修改承运方钱包
        // 入账中 = 入账中 + 运费
        // 余额 = 余额 + 调度费
        TZtWallet carrierWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCarrierWalletId());
        log.info("承运方钱包, {}", JSONUtil.toJsonStr(carrierWallet));
        BigDecimal accountBalance = carrierWallet.getAccountBalance().add(dto.getOrderActualPayment());
        BigDecimal accountInc = carrierWallet.getAccountInc().add(dto.getOrderActualPayment());
        entryAmount = carrierWallet.getEntryAmount().add(dto.getOrderTotalPayment().subtract(dto.getOrderActualPayment()));

        // 查询保费
        BigDecimal insuranceAmount = orderPayRequestMapper.selectInsuranceAmountById(dto.getPayRequestId());
        if (null != insuranceAmount) {
            accountInc = accountInc.add(insuranceAmount);
        }
        carrierWallet.setAccountBalance(accountBalance);
        carrierWallet.setAccountInc(accountInc);
        carrierWallet.setEntryAmount(entryAmount);
        carrierWallet.setUpdateTime(new Date());
        hxPayWalletMapper.updateByPrimaryKeySelective(carrierWallet);
    }

    public void companyPayFailModifyWallet(TOrderPayRequestDTO dto) {
        log.info("企业余额支付回调，修改钱包");
        // 修改企业钱包
        TZtWallet companyWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCompanyWalletId());
        log.info("企业余额支付回调失败, {}", companyWallet);
        // 入账中 - 总运费
        BigDecimal entryAmount = companyWallet.getEntryAmount().subtract(dto.getOrderTotalPayment());
        // 余额 + 总运费
        BigDecimal accountBalance = companyWallet.getAccountBalance().add(dto.getOrderTotalPayment());
        companyWallet.setAccountBalance(accountBalance);
        companyWallet.setEntryAmount(entryAmount);
        companyWallet.setUpdateTime(new Date());
        hxPayWalletMapper.updateByPrimaryKeySelective(companyWallet);
    }

    /**
    * @description 节点支付回调，修改钱包
    * <AUTHOR>
    * @date 2021/9/7 08:58
    */
    public void nodeCallbackModifyWallet(THxOrderPayInfoDTO dto) {
        // 企业余额支付
        if (HXTradeTypeEnum.HX_COMBALANCE_PAY.code.equals(dto.getTradeType())) {
            log.info("企业余额支付回调，修改钱包");
            // 修改企业钱包
            TZtWallet companyWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCompanyWalletId());
            log.info("企业钱包, {}", JSONUtil.toJsonStr(companyWallet));
            // 入账中 - 总运费
            BigDecimal entryAmount = companyWallet.getEntryAmount().subtract(dto.getTotalFee());
            // 支出 + 总运费
            BigDecimal accountExp = companyWallet.getAccountExp().add(dto.getTotalFee());
            companyWallet.setAccountExp(accountExp);
            companyWallet.setEntryAmount(entryAmount);
            companyWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(companyWallet);
            // 发起承运方余额支付 -> 修改承运方钱包
            // 入账中 = 入账中 + 运费
            // 余额 = 余额 + 调度费
            TZtWallet carrierWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCarrierWalletId());
            log.info("承运方钱包, {}", JSONUtil.toJsonStr(carrierWallet));
            BigDecimal accountBalance = carrierWallet.getAccountBalance().add(dto.getDispatchFee());
            entryAmount = carrierWallet.getEntryAmount().add(dto.getCarriageFee());
            BigDecimal accountInc = carrierWallet.getAccountInc().add(dto.getDispatchFee());

            if (DictEnum.WKPAYNODE.code.equals(dto.getUserOper())) {
                BigDecimal insuredAmount = BigDecimal.ZERO;
                if (null != dto.getInsure() && 0 != dto.getInsure() && !dto.getInsuranceCancellation() && dto.getInsuredAmount().compareTo(BigDecimal.ZERO) > 0) {
                    insuredAmount = dto.getInsuredAmount();
                }
                accountBalance = accountBalance.add(dto.getIllegalDeduction()).add(insuredAmount);
                entryAmount = entryAmount.subtract(dto.getIllegalDeduction()).subtract(insuredAmount);
                accountInc = accountInc.add(dto.getIllegalDeduction()).add(insuredAmount);
            }
            carrierWallet.setAccountBalance(accountBalance);
            carrierWallet.setEntryAmount(entryAmount);
            carrierWallet.setAccountInc(accountInc);
            carrierWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(carrierWallet);
        } else if (HXTradeTypeEnum.HX_CABALANCE_PAY.code.equals(dto.getTradeType())) {
            log.info("承运方余额支付回调，修改钱包");
            //修改承运方钱包 入账中 = 入账中 - 运费
            TZtWallet carrierWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCarrierWalletId());
            log.info("承运方钱包, {}", JSONUtil.toJsonStr(carrierWallet));
            BigDecimal carrierFee = dto.getCarriageFee();
            BigDecimal entryAmount = carrierWallet.getEntryAmount();
            BigDecimal insuredAmount = BigDecimal.ZERO;
            if (DictEnum.WKPAYNODE.code.equals(dto.getUserOper())) {
                if (null != dto.getInsure() && 0 != dto.getInsure() && !dto.getInsuranceCancellation() && dto.getInsuredAmount().compareTo(BigDecimal.ZERO) > 0) {
                    insuredAmount = dto.getInsuredAmount();
                }
                carrierFee = carrierFee.subtract(dto.getIllegalDeduction()).subtract(insuredAmount);
            }
            entryAmount = entryAmount.subtract(carrierFee);
            carrierWallet.setEntryAmount(entryAmount);
            carrierWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(carrierWallet);
            // 修改司机钱包
            TZtWallet driverWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getEndDriverWalletId());
            log.info("司机钱包, {}", JSONUtil.toJsonStr(driverWallet));
            BigDecimal accountBalance = driverWallet.getAccountBalance();
            BigDecimal accountInc = driverWallet.getAccountInc();
            if (DictEnum.COMMONPATTERN.code.equals(dto.getCapitalTransferPattern())
                    && DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())) {
                // 余额 = 余额 + 运费
                // 收入 = 收入 + 运费
                accountBalance = accountBalance.add(dto.getCarriageFee());
                accountInc = driverWallet.getAccountInc().add(dto.getCarriageFee());
                if (DictEnum.WKPAYNODE.code.equals(dto.getUserOper())) {
                    accountBalance = accountBalance.subtract(dto.getIllegalDeduction()).subtract(insuredAmount);
                    accountInc = accountInc.subtract(dto.getIllegalDeduction()).subtract(insuredAmount);
                }
                driverWallet.setAccountBalance(accountBalance);
                driverWallet.setAccountInc(accountInc);
                driverWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(driverWallet);
            } else if (DictEnum.MANAGERPATTERN.code.equals(dto.getCapitalTransferPattern())
                    && DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())){
                // 修改司机钱包
                // 余额 = 余额 + （运费 - 服务费)
                // 收入 = 收入 + （运费 - 服务费)
                accountBalance = accountBalance.add(dto.getCarriageFee().subtract(dto.getServiceFee()));
                accountInc = accountInc.add(dto.getCarriageFee().subtract(dto.getServiceFee()));
                // 如果是尾款支付，入账中 = 入账中 + 运费
                if (DictEnum.WKPAYNODE.code.equals(dto.getUserOper())) {
                    accountBalance = accountBalance.subtract(dto.getIllegalDeduction()).subtract(insuredAmount);
                    accountInc = accountInc.subtract(dto.getIllegalDeduction()).subtract(insuredAmount);
                }
                driverWallet.setAccountBalance(accountBalance);
                driverWallet.setAccountInc(accountInc);
                driverWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(driverWallet);
            }
        } else if (HXTradeTypeEnum.HX_CDBALANCE_PAY.code.equals(dto.getTradeType())) {
            log.info("司机余额支付回调，修改钱包");
            if (DictEnum.MANAGERPATTERN.code.equals(dto.getCapitalTransferPattern())
                    && DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())) {
                // 经纪人模式 支付到司机
                // 修改司机钱包 入账中 = 入账中 - 服务费
                TZtWallet driverWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getEndDriverWalletId());
                log.info("司机钱包, {}", JSONUtil.toJsonStr(driverWallet));
                BigDecimal entryAmount = driverWallet.getEntryAmount().subtract(dto.getServiceFee());
                driverWallet.setEntryAmount(entryAmount);
                driverWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(driverWallet);
                // 修改经纪人钱包 余额 = 余额 + 服务费；收入 = 收入 + 服务费
                TZtWallet agentWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getAgentWalletId());
                log.info("经纪人钱包, {}", JSONUtil.toJsonStr(agentWallet));
                BigDecimal accountBalance = agentWallet.getAccountBalance().add(dto.getServiceFee());
                agentWallet.setAccountBalance(accountBalance);
                BigDecimal accountInc = agentWallet.getAccountInc().add(dto.getServiceFee());
                agentWallet.setAccountInc(accountInc);
                agentWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(agentWallet);
            }
        }
    }

    /**
     * 节点支付失败，修改钱包
     * @param dto
     */
    public void failNodePayModifyWallet(THxOrderPayInfoDTO dto) {
        // 企业余额支付
        if (HXTradeTypeEnum.HX_COMBALANCE_PAY.code.equals(dto.getTradeType())) {
            log.info("企业余额支付失败回调，修改钱包");
            // 修改企业钱包
            TZtWallet companyWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCompanyWalletId());
            log.info("企业钱包, {}", JSONUtil.toJsonStr(companyWallet));
            // 入账中 - 总运费
            BigDecimal entryAmount = companyWallet.getEntryAmount().subtract(dto.getTotalFee());
            // 余额 + 总运费
            BigDecimal accountBalance = companyWallet.getAccountBalance().add(dto.getTotalFee());
            companyWallet.setAccountBalance(accountBalance);
            companyWallet.setEntryAmount(entryAmount);
            companyWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(companyWallet);
        } else if (HXTradeTypeEnum.HX_CABALANCE_PAY.code.equals(dto.getTradeType())) {
            log.info("承运方余额支付失败回调，修改钱包");
            //修改承运方钱包 入账中 = 入账中 - 运费
            // 余额 + 运费
            TZtWallet carrierWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCarrierWalletId());
            log.info("承运方钱包, {}", JSONUtil.toJsonStr(carrierWallet));
            BigDecimal carriageFee = dto.getCarriageFee();
            BigDecimal accountbalance = carrierWallet.getAccountBalance().add(carriageFee);
            carrierWallet.setAccountBalance(accountbalance);
            // 判断是否第一次支付
            TOrderPayRule tOrderPayRule = orderPayRuleMapper.selectPaidNode(dto.getOrderCode());
            if (null == tOrderPayRule) {
                // 第一次支付，查询是否支付保险
                TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(dto.getOrderBusinessCode());
                if (OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance)) {
                    // 司机余额扣除保险金额
                    carriageFee = carriageFee.subtract(dto.getInsuredAmount());
                }
            }
            if (DictEnum.WKPAYNODE.code.equals(dto.getUserOper())) {
                carriageFee = carriageFee.subtract(dto.getIllegalDeduction());
            }
            BigDecimal entryAmount = carrierWallet.getEntryAmount().subtract(carriageFee);
            carrierWallet.setEntryAmount(entryAmount);
            carrierWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(carrierWallet);
        } else if (HXTradeTypeEnum.HX_CDBALANCE_PAY.code.equals(dto.getTradeType())) {
            log.info("司机余额支付失败回调，修改钱包");
            if (DictEnum.MANAGERPATTERN.code.equals(dto.getCapitalTransferPattern())
                    && DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())) {
                // 经纪人模式 支付到司机
                // 修改司机钱包 入账中 = 入账中 - 服务费
                // 余额 + 服务费
                TZtWallet driverWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getEndDriverWalletId());
                log.info("司机钱包, {}", JSONUtil.toJsonStr(driverWallet));
                BigDecimal entryAmount = driverWallet.getEntryAmount().subtract(dto.getServiceFee());
                BigDecimal accountbalance = driverWallet.getAccountBalance().add(dto.getServiceFee());
                driverWallet.setEntryAmount(entryAmount);
                driverWallet.setAccountBalance(accountbalance);
                driverWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(driverWallet);
            }
        }
    }

    /**
     * @description 退款修改钱包
     * <AUTHOR>
     * @date 2021/8/25 16:58
     */
    public int refundModifyWallet(RecallWalletVO walletVO) {
        try {
            if (walletVO.getDispatchFee().compareTo(BigDecimal.ZERO) > 0) {
                TZtWallet carrierWallet = hxPayWalletMapper.selectByPrimaryKey(walletVO.getCarrierWalletId());
                if (null == carrierWallet) {
                    log.error("华夏召回，承运方未找到钱包");
                    throw new RuntimeException("ZJJ-902:运单召回失败!");
                }
                if (walletVO.getDispatchFee().compareTo(carrierWallet.getAccountBalance()) > 0) {
                    throw new RuntimeException("ZJJ-902:运单召回失败！承运方钱包余额不足！");
                }
            }
            // 普通司机
            if (DictEnum.COMMONPATTERN.code.equals(walletVO.getCapitalTransferPattern())
                && DictEnum.PAYTODRIVER.code.equals(walletVO.getCapitalTransferType())) {
                TZtWallet tZtWallet = hxPayWalletMapper.selectByPrimaryKey(walletVO.getDriverWalletId());
                if (null == tZtWallet) {
                    log.error("华夏召回，未找到钱包");
                    throw new RuntimeException("ZJJ-902:运单召回失败!");
                }
                if (walletVO.getCarriageFee().compareTo(tZtWallet.getAccountBalance()) > 0) {
                    throw new RuntimeException("ZJJ-902:运单召回失败！司机钱包余额不足！");
                }
                log.info("华夏召回, 司机钱包数据，{}, {}", tZtWallet.getPurseCategory(), JSONUtil.toJsonStr(tZtWallet));
                BigDecimal entryAmount = tZtWallet.getEntryAmount().add(walletVO.getCarriageFee());
                BigDecimal accountBalance = tZtWallet.getAccountBalance().subtract(walletVO.getCarriageFee());
                tZtWallet.setEntryAmount(entryAmount);
                tZtWallet.setAccountBalance(accountBalance);
                tZtWallet.setUpdateTime(new Date());
                tZtWallet.setUpdateUser(CurrentUser.getUserNickname());
                hxPayWalletMapper.updateByPrimaryKeySelective(tZtWallet);
                return 1;
            }
            // 普通车队长
            if (DictEnum.COMMONPATTERN.code.equals(walletVO.getCapitalTransferPattern())
                    && DictEnum.PAYTOCAPTAIN.code.equals(walletVO.getCapitalTransferType())) {
                TZtWallet tZtWallet = hxPayWalletMapper.selectByEndUserId(walletVO.getCaptainId());
                if (null == tZtWallet) {
                    log.error("华夏召回，未找到钱包");
                    throw new RuntimeException("ZJJ-902:运单召回失败!");
                }
                if (walletVO.getDriverWalletId().equals(tZtWallet.getId())) {
                    log.info("华夏召回, 司机钱包数据，{}, {}", tZtWallet.getPurseCategory(), JSONUtil.toJsonStr(tZtWallet));
                    if (walletVO.getCarriageFee().compareTo(tZtWallet.getAccountBalance()) > 0) {
                        throw new RuntimeException("ZJJ-902:运单召回失败！司机钱包余额不足！");
                    }
                } else {
                    log.info("华夏召回, 车队长钱包数据，{}, {}", tZtWallet.getPurseCategory(), JSONUtil.toJsonStr(tZtWallet));
                    if (walletVO.getCarriageFee().compareTo(tZtWallet.getAccountBalance()) > 0) {
                        throw new RuntimeException("ZJJ-902:运单召回失败！车队长钱包余额不足！");
                    }
                }
                BigDecimal entryAmount = tZtWallet.getEntryAmount().add(walletVO.getCarriageFee());
                BigDecimal accountBalance = tZtWallet.getAccountBalance().subtract(walletVO.getCarriageFee());
                tZtWallet.setEntryAmount(entryAmount);
                tZtWallet.setAccountBalance(accountBalance);
                tZtWallet.setUpdateTime(new Date());
                tZtWallet.setUpdateUser(CurrentUser.getUserNickname());
                hxPayWalletMapper.updateByPrimaryKeySelective(tZtWallet);
                return 1;
            }
            // 经纪人模式
            if (DictEnum.MANAGERPATTERN.code.equals(walletVO.getCapitalTransferPattern())) {
                if (DictEnum.PAYTODRIVER.code.equals(walletVO.getCapitalTransferType())) {
                    // 修改司机钱包
                    // 入账中 + 运费；余额 - 运费
                    TZtWallet driverWallet = hxPayWalletMapper.selectByPrimaryKey(walletVO.getDriverWalletId());
                    if (null == driverWallet) {
                        log.error("华夏召回，未找到钱包");
                        throw new RuntimeException("ZJJ-902:运单召回失败!");
                    }
                    if (driverWallet.getAccountBalance().compareTo(walletVO.getCarriageFee().subtract(walletVO.getServiceFee())) < 0) {
                        throw new RuntimeException("ZJJ-902:运单召回失败！司机钱包余额不足！");
                    }
                    log.info("华夏召回, 司机钱包数据，{}, {}", driverWallet.getPurseCategory(), JSONUtil.toJsonStr(driverWallet));
                    BigDecimal entryAmount = driverWallet.getEntryAmount().add(walletVO.getCarriageFee().subtract(walletVO.getServiceFee()));
                    BigDecimal accountBalance = driverWallet.getAccountBalance().subtract(walletVO.getCarriageFee().subtract(walletVO.getServiceFee()));
                    driverWallet.setEntryAmount(entryAmount);
                    driverWallet.setAccountBalance(accountBalance);
                    driverWallet.setUpdateTime(new Date());
                    driverWallet.setUpdateUser(CurrentUser.getUserNickname());
                    hxPayWalletMapper.updateByPrimaryKeySelective(driverWallet);
                } else  if (DictEnum.PAYTOCAPTAIN.code.equals(walletVO.getCapitalTransferType())) {
                    // 修改车队长钱包
                    // 入账中 + （运费 - 服务费）
                    // 余额 - （运费 - 服务费）
                    TZtWallet tZtWallet = hxPayWalletMapper.selectByEndUserId(walletVO.getCaptainId());
                    if (null == tZtWallet) {
                        log.error("华夏召回，未找到钱包");
                        throw new RuntimeException("ZJJ-902:运单召回失败!");
                    }
                    if (walletVO.getDriverWalletId().equals(tZtWallet.getId())) {
                        log.info("华夏召回, 司机钱包数据，{}, {}", tZtWallet.getPurseCategory(), JSONUtil.toJsonStr(tZtWallet));
                        if (walletVO.getCarriageFee().compareTo(tZtWallet.getAccountBalance()) > 0) {
                            throw new RuntimeException("ZJJ-902:运单召回失败！司机钱包余额不足！");
                        }
                    } else {
                        log.info("华夏召回, 车队长钱包数据，{}, {}", tZtWallet.getPurseCategory(), JSONUtil.toJsonStr(tZtWallet));
                        if (walletVO.getCarriageFee().compareTo(tZtWallet.getAccountBalance()) > 0) {
                            throw new RuntimeException("ZJJ-902:运单召回失败！车队长钱包余额不足！");
                        }
                    }
                    BigDecimal entryAmont = tZtWallet.getEntryAmount().add(walletVO.getCarriageFee().subtract(walletVO.getServiceFee()));
                    BigDecimal accountBalance = tZtWallet.getAccountBalance().subtract(walletVO.getCarriageFee().subtract(walletVO.getServiceFee()));
                    tZtWallet.setEntryAmount(entryAmont);
                    tZtWallet.setAccountBalance(accountBalance);
                    tZtWallet.setUpdateTime(new Date());
                    tZtWallet.setUpdateUser(CurrentUser.getUserNickname());
                    hxPayWalletMapper.updateByPrimaryKeySelective(tZtWallet);
                }
                // 修改经纪人钱包
                // 入账中 + 服务费
                // 余额 - 服务费）
                TZtWallet managerWallet = hxPayWalletMapper.selectByEndUserId(walletVO.getManagerId());
                if (null == managerWallet) {
                    log.error("华夏召回，未找到钱包");
                    throw new RuntimeException("ZJJ-902:运单召回失败!");
                }
                log.info("华夏召回, 经纪人钱包数据，{}, {}", managerWallet.getPurseCategory(), JSONUtil.toJsonStr(managerWallet));
                if (managerWallet.getAccountBalance().compareTo(walletVO.getServiceFee()) < 0) {
                    throw new RuntimeException("ZJJ-902:运单召回失败！经纪人钱包余额不足！");
                }
                BigDecimal entryAmount = managerWallet.getEntryAmount().add(walletVO.getServiceFee());
                BigDecimal accountBalance = managerWallet.getAccountBalance().subtract(walletVO.getServiceFee());
                managerWallet.setEntryAmount(entryAmount);
                managerWallet.setAccountBalance(accountBalance);
                managerWallet.setUpdateTime(new Date());
                managerWallet.setUpdateUser(CurrentUser.getUserNickname());
                hxPayWalletMapper.updateByPrimaryKeySelective(managerWallet);
            }
        } catch (Exception e) {
            log.error("ZJJ-902:运单召回失败! {}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("ZJJ-902:运单召回失败!");
            }
        }
        return 1;
    }

    /**
    * @description 退款回调修改钱包
    * <AUTHOR>
    * @date 2021/8/25 20:11
    */
    public int refundCallbackModifyWallet(THxOrderPayInfoDTO dto) {
        try {
            // 经纪人余额支付回调
            if (HXTradeTypeEnum.HX_CMREFUNDBALANCE_PAY.code.equals(dto.getTradeType())) {
                // 修改经纪人钱包
                TZtWallet managerWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getAgentWalletId());
                log.info("华夏召回回调, 经纪人钱包数据，{}, {}", managerWallet.getPurseCategory(), JSONUtil.toJsonStr(managerWallet));
                BigDecimal entryAmount = managerWallet.getEntryAmount().subtract(dto.getServiceFee());
                BigDecimal accountInc = managerWallet.getAccountInc().subtract(dto.getServiceFee());
                managerWallet.setEntryAmount(entryAmount);
                managerWallet.setAccountInc(accountInc);
                managerWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(managerWallet);
                // 修改承运方钱包
                TZtWallet carrierWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCarrierWalletId());
                log.info("华夏召回回调, 承运方钱包数据 {}", JSONUtil.toJsonStr(carrierWallet));
                BigDecimal accountBalance = carrierWallet.getAccountBalance().add(dto.getServiceFee());
                carrierWallet.setAccountBalance(accountBalance);
                carrierWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(carrierWallet);
            }
            // 车队长余额支付回调
            if (HXTradeTypeEnum.HX_CCREFUNDBALANCE_PAY.code.equals(dto.getTradeType())) {
                // 修改车队长钱包
                TZtWallet captainWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCaptainWalletId());
                log.info("华夏召回回调, 车队长钱包数据，{}, {}", captainWallet.getPurseCategory(), JSONUtil.toJsonStr(captainWallet));
                BigDecimal carriageFee = dto.getCarriageFee();
                if (!DictEnum.PACK.code.equals(dto.getPackStatus())) {
                    // 查询是否支付保险
                    TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(dto.getOrderBusinessCode());
                    if (OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance)) {
                        // 司机余额扣除保险金额
                        carriageFee = carriageFee.subtract(dto.getInsuredAmount());
                    }
                    carriageFee = carriageFee.subtract(dto.getIllegalDeduction());
                } else {
                    // 查询打包单支付保费金额
                    BigDecimal insuranceAmount = orderPackInfoMapper.calculateInsuranceAmount(dto.getPackCode());
                    carriageFee = carriageFee.subtract(insuranceAmount);
                }
                carriageFee = carriageFee.subtract(dto.getServiceFee());
                BigDecimal entryAmount = captainWallet.getEntryAmount().subtract(carriageFee);
                BigDecimal accountInc = captainWallet.getAccountInc().subtract(carriageFee);
                captainWallet.setEntryAmount(entryAmount);
                captainWallet.setAccountInc(accountInc);
                captainWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(captainWallet);
                // 修改承运方钱包
                TZtWallet carrierWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCarrierWalletId());
                log.info("华夏召回回调, 承运方钱包数据 {}", JSONUtil.toJsonStr(carrierWallet));
                BigDecimal accountBalance = carrierWallet.getAccountBalance().add(carriageFee);
                carrierWallet.setAccountBalance(accountBalance);
                carrierWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(carrierWallet);
            }
            // 司机余额支付回调
            if (HXTradeTypeEnum.HX_CDREFUNDBALANCE_PAY.code.equals(dto.getTradeType())) {
                TZtWallet driverWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getEndDriverWalletId());
                log.info("华夏召回回调, 司机钱包数据, {}", JSONUtil.toJsonStr(driverWallet));
                BigDecimal carriageFee = dto.getCarriageFee();
                if (!DictEnum.PACK.code.equals(dto.getPackStatus())) {
                    /// 查询是否支付保险
                    TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(dto.getOrderBusinessCode());
                    if (OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance)) {
                        // 司机余额扣除保险金额
                        carriageFee = carriageFee.subtract(dto.getInsuredAmount());
                    }
                    carriageFee = carriageFee.subtract(dto.getIllegalDeduction());
                } else {
                    // 查询打包单支付保费金额
                    BigDecimal insuranceAmount = orderPackInfoMapper.calculateInsuranceAmount(dto.getPackCode());
                    carriageFee = carriageFee.subtract(insuranceAmount);
                }
                carriageFee = carriageFee.subtract(dto.getServiceFee());
                BigDecimal entryAmount = driverWallet.getEntryAmount().subtract(carriageFee);
                BigDecimal accountInc = driverWallet.getAccountInc().subtract(carriageFee);
                driverWallet.setEntryAmount(entryAmount);
                driverWallet.setAccountInc(accountInc);
                driverWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(driverWallet);
                // 修改承运方钱包
                TZtWallet carrierWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCarrierWalletId());
                log.info("华夏召回回调, 承运方钱包数据 {}", JSONUtil.toJsonStr(carrierWallet));
                BigDecimal accountBalance = carrierWallet.getAccountBalance().add(carriageFee);
                carrierWallet.setAccountBalance(accountBalance);
                carrierWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(carrierWallet);
            }
            // 承运方余额支付回调
            if (HXTradeTypeEnum.HX_CAREFUNDBALANCE_PAY.code.equals(dto.getTradeType())) {
                // 修改承运方钱包
                TZtWallet carrierWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCarrierWalletId());
                log.info("华夏召回回调, 承运方钱包数据 {}", JSONUtil.toJsonStr(carrierWallet));
                BigDecimal entryAmount = carrierWallet.getEntryAmount().subtract(dto.getTotalFee());
                BigDecimal accountInc = carrierWallet.getAccountInc().subtract(dto.getDispatchFee());
                if (!DictEnum.PACK.code.equals(dto.getPackStatus())) {
                    accountInc = accountInc.subtract(dto.getIllegalDeduction());
                    if (null != dto.getInsuranceId() && 0 != dto.getInsure() && !dto.getInsuranceCancellation() && dto.getInsuredAmount().compareTo(BigDecimal.ZERO) > 0) {
                        accountInc = accountInc.subtract(dto.getInsuredAmount());
                    }
                } else {
                    BigDecimal insuranceAmount = orderPackInfoMapper.calculateInsuranceAmount(dto.getPackCode());
                    accountInc = accountInc.subtract(insuranceAmount);
                }
                carrierWallet.setEntryAmount(entryAmount);
                carrierWallet.setAccountInc(accountInc);
                carrierWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(carrierWallet);
                // 修改企业钱包
                TZtWallet companyWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCompanyWalletId());
                log.info("华夏召回回调, 企业钱包数据, {}", JSONUtil.toJsonStr(companyWallet));
                BigDecimal accoutBalance = companyWallet.getAccountBalance().add(dto.getTotalFee());
                BigDecimal accountExp = companyWallet.getAccountExp().subtract(dto.getTotalFee());
                companyWallet.setAccountBalance(accoutBalance);
                companyWallet.setAccountExp(accountExp);
                companyWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(companyWallet);
                return 1;
            }
        } catch (Exception e) {
            log.error("ZJJ-500:运单召回失败！{}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("ZJJ-500:运单召回失败！");
            }
        }
        return 1;
    }

    /**
    * @description 退款回调失败，修改钱包
    * <AUTHOR>
    * @date 2021/8/27 11:13
    */
    public int failRefundCallbackModifyWallet(THxOrderPayInfoDTO dto) {
        // 交易退款回调
        if (HXTradeTypeEnum.HX_CMREFUNDBALANCE_PAY.code.equals(dto.getUserOper())) {
            // 修改经纪人钱包
            // 修改经纪人钱包
            TZtWallet agentWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getAgentWalletId());
            BigDecimal entryAmount = agentWallet.getEntryAmount().subtract(dto.getServiceFee());
            BigDecimal accountBalance = agentWallet.getAccountBalance().add(dto.getServiceFee());
            BigDecimal accountInc = agentWallet.getAccountInc().add(dto.getServiceFee());
            agentWallet.setEntryAmount(entryAmount);
            agentWallet.setAccountBalance(accountBalance);
            agentWallet.setAccountInc(accountInc);
            agentWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(agentWallet);
            return 1;
        }
        if (HXTradeTypeEnum.HX_CCREFUNDBALANCE_PAY.code.equals(dto.getUserOper())) {
            // 修改车队长钱包
            TZtWallet captainWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCaptainWalletId());
            BigDecimal carriageAmount = dto.getCarriageFee();
            if (!DictEnum.PACK.code.equals(dto.getPackStatus())) {
                // 查询是否支付保险
                TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(dto.getOrderBusinessCode());
                if (OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance)) {
                    // 司机余额扣除保险金额
                    carriageAmount = carriageAmount.subtract(dto.getInsuredAmount());
                }
            }
            carriageAmount = carriageAmount.subtract(dto.getServiceFee());
            BigDecimal entryAmount = captainWallet.getEntryAmount().subtract(carriageAmount);
            BigDecimal accountBalance = captainWallet.getAccountBalance().add(carriageAmount);
            BigDecimal accountInc = captainWallet.getAccountInc().add(carriageAmount);
            captainWallet.setEntryAmount(entryAmount);
            captainWallet.setAccountBalance(accountBalance);
            captainWallet.setAccountInc(accountInc);
            captainWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(captainWallet);
            return 1;
        }
        if (HXTradeTypeEnum.HX_CDREFUNDBALANCE_PAY.code.equals(dto.getUserOper())) {
            // 修改司机钱包
            TZtWallet driverWallet = hxPayWalletMapper.selectByEndUserId(dto.getEndDriverWalletId());
            BigDecimal carriageAmount = dto.getCarriageFee();
            if (!DictEnum.PACK.code.equals(dto.getPackStatus())) {
                // 查询是否支付保险
                TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(dto.getOrderBusinessCode());
                if (OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance)) {
                    // 司机余额扣除保险金额
                    carriageAmount = carriageAmount.subtract(dto.getInsuredAmount());
                }
            }
            carriageAmount = carriageAmount.subtract(dto.getServiceFee());
            BigDecimal entryAmount = driverWallet.getEntryAmount().subtract(carriageAmount);
            BigDecimal accountAmount = driverWallet.getAccountBalance().add(carriageAmount);
            BigDecimal accountInc = driverWallet.getAccountInc().add(carriageAmount);
            driverWallet.setEntryAmount(entryAmount);
            driverWallet.setAccountBalance(accountAmount);
            driverWallet.setAccountInc(accountInc);
            driverWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(driverWallet);
            return 1;
        }
        if (HXTradeTypeEnum.HX_CAREFUNDBALANCE_PAY.code.equals(dto.getUserOper())) {
            // 修改承运方钱包
            TZtWallet carrierWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getCarrierWalletId());
            BigDecimal entryAmount = carrierWallet.getEntryAmount().subtract(dto.getTotalFee());
            BigDecimal accountBalance = carrierWallet.getAccountBalance().add(dto.getTotalFee());
            BigDecimal accountInc = carrierWallet.getAccountInc().add(dto.getTotalFee());
            carrierWallet.setEntryAmount(entryAmount);
            carrierWallet.setAccountBalance(accountBalance);
            carrierWallet.setAccountInc(accountInc);
            carrierWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(carrierWallet);
            return 1;
        }
        return 1;
    }

    /**
     * 提现，修改钱包
     * @param walletId
     * @param txAmount
     */
    public void txApplyModifyWallet(Integer walletId, BigDecimal txAmount) {
        TZtWallet tZtWallet = hxPayWalletMapper.selectByPrimaryKey(walletId);
        log.info("提现，C端钱包信息: {}", JSONUtil.toJsonStr(tZtWallet));
        if (txAmount.compareTo(tZtWallet.getAccountBalance()) > 0) {
            throw new RuntimeException("余额不足，提现失败");
        }
        // 本人银行卡，余额 - 提现金额，提现中 + 提现剩余金额
        BigDecimal accountBalance = tZtWallet.getAccountBalance().subtract(txAmount);
        BigDecimal withdrawAmount = tZtWallet.getWithdrawAmount().add(txAmount);
        tZtWallet.setWithdrawAmount(withdrawAmount);
        tZtWallet.setAccountBalance(accountBalance);
        tZtWallet.setUpdateTime(new Date());
        hxPayWalletMapper.updateByPrimaryKeySelective(tZtWallet);
    }

    /**
     * 提现，修改钱包
     * @param walletId
     * @param txAmount
     */
    public void txModifyWallet(Integer walletId, BigDecimal txAmount) {
        TZtWallet tZtWallet = hxPayWalletMapper.selectByPrimaryKey(walletId);
        log.info("提现，C端钱包信息: {}", JSONUtil.toJsonStr(tZtWallet));
        if (txAmount.compareTo(tZtWallet.getAccountBalance()) > 0) {
            throw new RuntimeException("余额不足，提现失败");
        }
        BigDecimal accountBalance = tZtWallet.getAccountBalance().subtract(txAmount);
        BigDecimal withdrawAmount = tZtWallet.getWithdrawAmount().add(txAmount);
        tZtWallet.setAccountBalance(accountBalance);
        tZtWallet.setWithdrawAmount(withdrawAmount);
        tZtWallet.setUpdateTime(new Date());
        hxPayWalletMapper.updateByPrimaryKeySelective(tZtWallet);
    }

    public void txServiceFeeModifyWallet(Integer walletId, BigDecimal txServiceFee, BigDecimal txAmount, Boolean isSelfCard) {
        TZtWallet tZtWallet = hxPayWalletMapper.selectByPrimaryKey(walletId);
        log.info("支付提现服务费，C端钱包信息: {}", JSONUtil.toJsonStr(tZtWallet));
        if (txAmount.compareTo(tZtWallet.getAccountBalance()) > 0) {
            throw new RuntimeException("余额不足，提现失败");
        }
        BigDecimal accountBalance = BigDecimal.ZERO;
        BigDecimal entryAmount = BigDecimal.ZERO;
        if (isSelfCard) {
            // 本人银行卡，余额 - 提现金额，入账中 + 服务费，提现中 + 提现剩余金额
            accountBalance = tZtWallet.getAccountBalance().subtract(txAmount);
            entryAmount = tZtWallet.getEntryAmount().add(txServiceFee);
            BigDecimal withdrawAmount = tZtWallet.getWithdrawAmount().add(txAmount.subtract(txServiceFee));
            tZtWallet.setWithdrawAmount(withdrawAmount);
        } else {
            // 非本人银行卡，余额 - 提现金额，入账中 + 提现金额
            accountBalance = tZtWallet.getAccountBalance().subtract(txAmount);
            entryAmount = tZtWallet.getEntryAmount().add(txAmount);
        }
        tZtWallet.setAccountBalance(accountBalance);
        tZtWallet.setEntryAmount(entryAmount);
        tZtWallet.setUpdateTime(new Date());
        hxPayWalletMapper.updateByPrimaryKeySelective(tZtWallet);
    }

    /**
    * @description 发起提现服务费支付请求，修改钱包
    * <AUTHOR>
    * @date 2021/11/24 14:22
    */
    public void txServiceFeeApplyModifyWallet(Integer walletId, BigDecimal txServiceFee) {
        TZtWallet tZtWallet = hxPayWalletMapper.selectByPrimaryKey(walletId);
        log.info("支付提现服务费，C端钱包信息: {}", JSONUtil.toJsonStr(tZtWallet));
        if (txServiceFee.compareTo(tZtWallet.getAccountBalance()) > 0) {
            throw new RuntimeException("余额不足，提现失败");
        }
        BigDecimal accountBalance = tZtWallet.getAccountBalance().subtract(txServiceFee);
        BigDecimal entryAmount = tZtWallet.getEntryAmount().add(txServiceFee);
        tZtWallet.setAccountBalance(accountBalance);
        tZtWallet.setEntryAmount(entryAmount);
        tZtWallet.setUpdateTime(new Date());
        hxPayWalletMapper.updateByPrimaryKeySelective(tZtWallet);
    }

    /**
     * 发起提现转账，修改钱包
     * @param walletId
     * @param txAmount
     */
    public void txTransferModifyWallet(Integer walletId, BigDecimal txAmount) {
        TZtWallet tZtWallet = hxPayWalletMapper.selectByPrimaryKey(walletId);
        log.info("支付提现转账，C端钱包信息: {}", JSONUtil.toJsonStr(tZtWallet));
        if (txAmount.compareTo(tZtWallet.getAccountBalance()) > 0) {
            throw new RuntimeException("余额不足，提现失败");
        }
        BigDecimal accountBalance = tZtWallet.getAccountBalance().subtract(txAmount);
        BigDecimal entryAmount = tZtWallet.getEntryAmount().add(txAmount);
        tZtWallet.setAccountBalance(accountBalance);
        tZtWallet.setEntryAmount(entryAmount);
        tZtWallet.setUpdateTime(new Date());
        hxPayWalletMapper.updateByPrimaryKeySelective(tZtWallet);
    }

    /**
     * 提现转账回调，发起提现，修改钱包
     * @param walletId
     * @param txAmount
     */
    public void txTransferApplyTxModifyWallet(Integer walletId, BigDecimal txAmount) {
        TZtWallet tZtWallet = hxPayWalletMapper.selectByPrimaryKey(walletId);
        log.info("提现转账回调，发起提现，C端钱包信息: {}", JSONUtil.toJsonStr(tZtWallet));
        BigDecimal accountBalance = tZtWallet.getAccountBalance().subtract(txAmount);
        BigDecimal withdrawAmount = tZtWallet.getWithdrawAmount().add(txAmount);
        tZtWallet.setAccountBalance(accountBalance);
        tZtWallet.setWithdrawAmount(withdrawAmount);
        tZtWallet.setUpdateTime(new Date());
        hxPayWalletMapper.updateByPrimaryKeySelective(tZtWallet);
    }


    /**
     * @description 提现服务费回调，修改钱包
     * <AUTHOR>
     * @date 2021/9/10 15:43
     */
    public void txServiceFeeNoticeModifyWallet(String inPartnerAccId, TTxOrderPayDetailDTO dto, Boolean isSuccessed) {
        TZtWallet tZtWallet = hxPayWalletMapper.selectByPrimaryKey(dto.getWalletId());
        log.info("提现服务费回调，C端钱包信息: {}", JSONUtil.toJsonStr(tZtWallet));
        if (isSuccessed) {
            // 修改C端钱钱包；入账中 - 服务费
            BigDecimal entryAmount = tZtWallet.getEntryAmount().subtract(dto.getOrderPrepayAmount());
            tZtWallet.setEntryAmount(entryAmount);
            tZtWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(tZtWallet);
            // 修改平台钱包
            TZtWallet plathformsWallet = hxPayWalletMapper.selectByCarrierCompanyPartnerAccId(inPartnerAccId, DictEnum.PF.code);
            log.info("提现服务费回调，平台钱包, {}", JSONUtil.toJsonStr(plathformsWallet));
            BigDecimal accountBalance = plathformsWallet.getAccountBalance().add(dto.getOrderPrepayAmount());
            BigDecimal accountInc = plathformsWallet.getAccountInc().add(dto.getOrderPrepayAmount());
            plathformsWallet.setAccountBalance(accountBalance);
            plathformsWallet.setAccountInc(accountInc);
            plathformsWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(plathformsWallet);
        } else {
            // 失败
            BigDecimal accountBalance = tZtWallet.getAccountBalance().add(dto.getOrderPrepayAmount());
            BigDecimal entryAmount = tZtWallet.getEntryAmount().subtract(dto.getOrderPrepayAmount());
            tZtWallet.setAccountBalance(accountBalance);
            tZtWallet.setEntryAmount(entryAmount);
            tZtWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(tZtWallet);
        }
    }

    /**
    * @description 提现转账回调，修改钱包
    * <AUTHOR>
    * @date 2021/11/24 14:25
    */
    public void txTransferNoticeModifyWallet(String outPartnerAccId, String inPartnerAccId, BigDecimal totalAmount, Boolean isSuccessed) {
        if (isSuccessed) {
            TZtWallet outWallet = hxPayWalletMapper.selectByEnduserPartnerAccId(outPartnerAccId);
            log.info("提现转账回调，出款方钱包, {}", JSONUtil.toJsonStr(outWallet));
            BigDecimal entryAmount = outWallet.getEntryAmount().subtract(totalAmount);
            outWallet.setEntryAmount(entryAmount);
            outWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(outWallet);
            TZtWallet inWallet = hxPayWalletMapper.selectByEnduserPartnerAccId(inPartnerAccId);
            log.info("提现转账回调，入款方钱包, {}", JSONUtil.toJsonStr(inWallet));
            BigDecimal accountBalance = inWallet.getAccountBalance().add(totalAmount);
            inWallet.setAccountBalance(accountBalance);
            inWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(inWallet);
        } else {
            // 失败
            TZtWallet outWallet = hxPayWalletMapper.selectByEnduserPartnerAccId(outPartnerAccId);
            log.info("提现转账回调，出款方钱包, {}", JSONUtil.toJsonStr(outWallet));
            BigDecimal accountBalance = outWallet.getAccountBalance().add(totalAmount);
            BigDecimal entryAmount = outWallet.getEntryAmount().subtract(totalAmount);
            outWallet.setAccountBalance(accountBalance);
            outWallet.setEntryAmount(entryAmount);
            outWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(outWallet);
        }
    }

    /**
    * @description 提现回调，修改钱包
    * <AUTHOR>
    * @date 2021/8/30 15:03
    */
    public void txNoticeModifyWallet(Integer walletId, BigDecimal amount, Boolean isSuccessed) {
        TZtWallet tZtWallet = hxPayWalletMapper.selectByPrimaryKey(walletId);
        log.info("提现回调，C端钱包信息: {}", JSONUtil.toJsonStr(tZtWallet));
        if (isSuccessed) {
            BigDecimal withdrawAmount = tZtWallet.getWithdrawAmount().subtract(amount);
            tZtWallet.setWithdrawAmount(withdrawAmount);
            tZtWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(tZtWallet);
        } else {
            // 提现失败
            BigDecimal withdrawAmount = tZtWallet.getWithdrawAmount().subtract(amount);
            BigDecimal accountBalance = tZtWallet.getAccountBalance().add(amount);
            tZtWallet.setWithdrawAmount(withdrawAmount);
            tZtWallet.setAccountBalance(accountBalance);
            tZtWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(tZtWallet);
        }
    }

    /**
    * @description 提现回调，退票，修改钱包
    * <AUTHOR>
    * @date 2021/12/27 15:14
    */
    public void txNoticePayReturnModifyWallet(Integer walletId, BigDecimal amount) {
        TZtWallet tZtWallet = hxPayWalletMapper.selectByPrimaryKey(walletId);
        log.info("提现回调，退票，C端钱包信息: {}", JSONUtil.toJsonStr(tZtWallet));
        BigDecimal accountBalance = tZtWallet.getAccountBalance().add(amount);
        tZtWallet.setAccountBalance(accountBalance);
        tZtWallet.setUpdateTime(new Date());
        hxPayWalletMapper.updateByPrimaryKeySelective(tZtWallet);
    }

    /**
    * @description 华夏预付款回调，修改钱包
    * <AUTHOR>
    * @date 2021/11/8 13:58
    */
    public void prePayCallbackModifyWallet(String outPartnerAccId, String inPartnerAccId, String tradeType, BigDecimal amount, Boolean isSuccessed) {
        if (isSuccessed) {
            if (HXTradeTypeEnum.HX_COMBALANCE_PREPAY.code.equals(tradeType) || HXTradeTypeEnum.HX_COMBALANCE_WKPAY.code.equals(tradeType)) {
                TZtWallet outZtWallet = selectByCarrierCompanyPartnerAccId(outPartnerAccId, DictEnum.BD.code);
                log.info("华夏预付款回调, 出款方钱包, {}", JSONUtil.toJsonStr(outZtWallet));
                // 企业预付款回调
                BigDecimal entryAmount = outZtWallet.getEntryAmount().subtract(amount);
                BigDecimal accountExp = outZtWallet.getAccountExp().add(amount);
                outZtWallet.setEntryAmount(entryAmount);
                outZtWallet.setAccountExp(accountExp);
                outZtWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(outZtWallet);
                TZtWallet inJdWallet = selectByCarrierCompanyPartnerAccId(inPartnerAccId, DictEnum.CA.code);
                log.info("华夏预付款回调, 入款方钱包, {}", JSONUtil.toJsonStr(outZtWallet));
                BigDecimal accountBalance = inJdWallet.getAccountBalance().add(amount);
                BigDecimal accountInc = inJdWallet.getAccountInc().add(amount);
                inJdWallet.setAccountBalance(accountBalance);
                inJdWallet.setAccountInc(accountInc);
                inJdWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(inJdWallet);
            } else if (HXTradeTypeEnum.HX_CABALANCE_PREPAY.code.equals(tradeType) || HXTradeTypeEnum.HX_CABALANCE_WKEPAY.code.equals(tradeType)) {
                // 承运方预付款回调
                TZtWallet outZtWallet = selectByCarrierCompanyPartnerAccId(outPartnerAccId, DictEnum.CA.code);
                log.info("华夏预付款回调, 出款方钱包, {}", JSONUtil.toJsonStr(outZtWallet));
                BigDecimal entryAmount = outZtWallet.getEntryAmount().subtract(amount);
                outZtWallet.setEntryAmount(entryAmount);
                BigDecimal accountInc = outZtWallet.getAccountInc().subtract(amount);
                outZtWallet.setAccountInc(accountInc);
                outZtWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(outZtWallet);
                TZtWallet inJdWallet = selectByEnduserPartnerAccId(inPartnerAccId);
                log.info("华夏预付款回调, 入款方钱包, {}", JSONUtil.toJsonStr(outZtWallet));
                BigDecimal accountBalance = inJdWallet.getAccountBalance().add(amount);
                accountInc = inJdWallet.getAccountInc().add(amount);
                inJdWallet.setAccountBalance(accountBalance);
                inJdWallet.setAccountInc(accountInc);
                inJdWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(inJdWallet);
            }
        } else {
            String dataSource = "";
            if (HXTradeTypeEnum.HX_COMBALANCE_PREPAY.code.equals(tradeType) || HXTradeTypeEnum.HX_COMBALANCE_WKPAY.code.equals(tradeType)) {
                dataSource = DictEnum.BD.code;
            } else if (HXTradeTypeEnum.HX_CABALANCE_PREPAY.code.equals(tradeType) || HXTradeTypeEnum.HX_CABALANCE_WKEPAY.code.equals(tradeType)) {
                dataSource = DictEnum.CA.code;
            }
            TZtWallet outZtWallet = selectByCarrierCompanyPartnerAccId(outPartnerAccId, dataSource);
            log.info("华夏预付款回调失败, 出款方钱包, {}", JSONUtil.toJsonStr(outZtWallet));
            BigDecimal entryAmount = outZtWallet.getEntryAmount().subtract(amount);
            BigDecimal accountBalance = outZtWallet.getAccountBalance().add(amount);
            if (HXTradeTypeEnum.HX_COMBALANCE_PAY.code.equals(tradeType) || HXTradeTypeEnum.HX_COMBALANCE_WKPAY.code.equals(tradeType)) {
                BigDecimal accountExp = outZtWallet.getAccountExp().subtract(amount);
                outZtWallet.setAccountExp(accountExp);
            }
            outZtWallet.setEntryAmount(entryAmount);
            outZtWallet.setAccountBalance(accountBalance);
            outZtWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(outZtWallet);
        }
    }

    /**
    * @description 华夏预付款承运方支付司机，修改钱包
    * <AUTHOR>
    * @date 2021/11/10 08:37
    */
    public void preCAPayModifyWallet(Integer walletId, BigDecimal amount) {
        TZtWallet tZtWallet = hxPayWalletMapper.selectByPrimaryKey(walletId);
        log.info("预付款承运方支付司机, 承运方钱包, {}", JSONUtil.toJsonStr(tZtWallet));
        BigDecimal entryAmount = tZtWallet.getEntryAmount().add(amount);
        BigDecimal accountBalance = tZtWallet.getAccountBalance().subtract(amount);
        tZtWallet.setEntryAmount(entryAmount);
        tZtWallet.setAccountBalance(accountBalance);
        tZtWallet.setUpdateTime(new Date());
        hxPayWalletMapper.updateByPrimaryKeySelective(tZtWallet);
    }

    /**
    * @description 发起账户服务费支付请求，修改钱包
    * <AUTHOR>
    * @date 2021/12/3 20:48
    */
    public void accountServicePayApplyModifyWallet(Integer walletId, BigDecimal accountServiceFee) {
        TZtWallet tZtWallet = hxPayWalletMapper.selectByPrimaryKey(walletId);
        if (null != tZtWallet) {
            log.info("发起账户服务费支付请求，修改钱包, 钱包类型, {}, {}", tZtWallet.getPurseCategory(), JSONUtil.toJsonStr(tZtWallet));
            if (tZtWallet.getAccountBalance().compareTo(accountServiceFee) >= 0) {
                BigDecimal accountBalance = tZtWallet.getAccountBalance().subtract(accountServiceFee);
                BigDecimal entryAmount = tZtWallet.getEntryAmount().add(accountServiceFee);
                tZtWallet.setAccountBalance(accountBalance);
                tZtWallet.setEntryAmount(entryAmount);
                tZtWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(tZtWallet);
            } else {
                throw new RuntimeException("发起账户服务费支付请求，修改钱包, 钱包余额不足");
            }
        } else {
            log.error("发起账户服务费支付请求，修改钱包, 未找到钱包");
        }
    }

    /**
    * @description 发起账户服务费支付回调，修改钱包
    * <AUTHOR>
    * @date 2021/12/3 21:27
    */
    public void accountServicePayCallbackModifyWallet(TZtWallet outWallet, TZtWallet inWallet, BigDecimal accountServiceFee, Boolean isSuccessed) {
        if (isSuccessed) {
            if (null != outWallet) {
                log.info("发起账户服务费支付回调，修改出款方钱包, 钱包类型,{}, {}", outWallet.getPurseCategory(), JSONUtil.toJsonStr(outWallet));
                BigDecimal entryAmount = outWallet.getEntryAmount().subtract(accountServiceFee);
                outWallet.setEntryAmount(entryAmount);
                outWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(outWallet);
            } else {
                log.error("发起账户服务费支付回调，修改钱包, 未找到出款方钱包");
            }
            if (null != inWallet) {
                log.info("发起账户服务费支付回调，修改入款方钱包, 钱包类型, {}, {}", inWallet.getPurseCategory(), JSONUtil.toJsonStr(inWallet));
                BigDecimal accountBalance = inWallet.getAccountBalance().add(accountServiceFee);
                inWallet.setAccountBalance(accountBalance);
                inWallet.setUpdateTime(new Date());
                hxPayWalletMapper.updateByPrimaryKeySelective(inWallet);
            } else {
                log.error("发起账户服务费支付回调，修改钱包, 未找到入款方钱包");
            }
        } else {
            // 失败，只修改出款方钱包
            BigDecimal accountBalance = outWallet.getAccountBalance().add(accountServiceFee);
            BigDecimal entryAmount = outWallet.getEntryAmount().subtract(accountServiceFee);
            outWallet.setAccountBalance(accountBalance);
            outWallet.setEntryAmount(entryAmount);
            outWallet.setUpdateTime(new Date());
            hxPayWalletMapper.updateByPrimaryKeySelective(outWallet);
        }
    }

    /**
     * 提现超额，修改钱包，记录提现中状态
     * @param walletId
     * @param txAmount
     */
    public void txQuotaModifyWallet(Integer walletId, BigDecimal txAmount) {
        TZtWallet tZtWallet = hxPayWalletMapper.selectByPrimaryKey(walletId);
        log.info("提现，C端钱包信息: {}", JSONUtil.toJsonStr(tZtWallet));
        if (txAmount.compareTo(tZtWallet.getAccountBalance()) > 0) {
            throw new RuntimeException("余额不足，提现失败");
        }
        BigDecimal accountBalance = tZtWallet.getAccountBalance().subtract(txAmount);
        BigDecimal withdrawAmount = tZtWallet.getWithdrawAmount().add(txAmount);
        tZtWallet.setAccountBalance(accountBalance);
        tZtWallet.setWithdrawAmount(withdrawAmount);
        tZtWallet.setParam1("1");
        tZtWallet.setUpdateTime(new Date());
        hxPayWalletMapper.updateByPrimaryKeySelective(tZtWallet);
    }

}
