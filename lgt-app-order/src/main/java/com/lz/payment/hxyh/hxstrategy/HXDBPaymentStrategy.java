package com.lz.payment.hxyh.hxstrategy;

import cn.hutool.json.JSONUtil;
import com.lz.common.config.HXPropertiesConfig;
import com.lz.common.dbenum.BusinessCode;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.HXTradeTypeEnum;
import com.lz.common.factory.HXMQMessageFactory;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.MQMessage;
import com.lz.common.model.hxPayment.request.pay.CustomerBalancePayReq;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.DateUtils;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.dao.TMqMessagePackPayDetailMapper;
import com.lz.dao.TOrderCastChangesMapper;
import com.lz.dto.OpenRoleStatusListDTO;
import com.lz.dto.OpenRoleWalletListDTO;
import com.lz.model.TMqMessagePackPayDetail;
import com.lz.model.TOrderCastChanges;
import com.lz.model.TOrderInfo;
import com.lz.model.TOrderPayInfo;
import com.lz.payment.TradeType;
import com.lz.payment.hxyh.HXPayOrderUtil;
import com.lz.payment.hxyh.HXPaymentUtil;
import com.lz.payment.hxyh.HXWalletUtil;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.vo.SelectOpenRoleVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Service
public class HXDBPaymentStrategy extends HPaymentStrategy {

    @Autowired
    private TOrderCastChangesMapper tOrderCastChangesMapper;

    @Resource
    private TMqMessagePackPayDetailMapper mqMessagePackPayDetailMapper;

    @Autowired
    private HXPayOrderUtil hxPayOrderUtil;

    @Autowired
    private HXPaymentUtil hxPaymentUtil;

    @Autowired
    private HXWalletUtil hxWalletUtil;

    @Autowired
    private HXPropertiesConfig hxPropertiesConfig;

    @Autowired
    private SysParamAPI sysParamAPI;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil toPay(TOrderInfo tOrderInfo) {
        try {
            TOrderCastChanges cc = tOrderCastChangesMapper.selectByNewOne(tOrderInfo.getCode());

            // 设置查询参数
            SelectOpenRoleVO openRoleVO = new SelectOpenRoleVO();
            openRoleVO.setCarrierId(tOrderInfo.getCarrierId());
            openRoleVO.setCompanyId(tOrderInfo.getCompanyId());
            openRoleVO.setDriverId(tOrderInfo.getEndDriverId());
            if (DictEnum.PAYTOCAPTAIN.code.equals(cc.getCapitalTransferType())) {
                openRoleVO.setCaptainId(tOrderInfo.getEndCarOwnerId());
            }
            if (DictEnum.MANAGERPATTERN.code.equals(cc.getCapitalTransferPattern())) {
                openRoleVO.setManagerId(tOrderInfo.getAgentId());
            }
            // 查询钱包
            ResultUtil openRoleWallet = hxPayOrderUtil.selectOpenRoleWallet(openRoleVO);
            if (null == openRoleWallet || DictEnum.ERROR.code.equals(openRoleWallet.getCode())) {
                throw new RuntimeException("支付失败，请联系运营平台予以解决。");
            }
            OpenRoleWalletListDTO walletListDTO = JSONUtil.toBean(JSONUtil.toJsonStr(openRoleWallet.getData()), OpenRoleWalletListDTO.class);

            TOrderCastChanges castChanges = new TOrderCastChanges();
            BeanUtils.copyProperties(cc, castChanges);
            castChanges.setId(null);
            castChanges.setCode(IdWorkerUtil.getInstance().nextId());
            castChanges.setUserOper(TradeType.PayMent.code);
            castChanges.setTradeType(TradeType.RZ.code);
            // 修改京东钱包id
            castChanges.setCarrierWalletId(walletListDTO.getCarrierWallet().getId());
            castChanges.setCompanyWalletId(walletListDTO.getCompanyWallet().getId());
            castChanges.setEndDriverWalletId(walletListDTO.getDriverWallet().getId());
            castChanges.setCreateUser(CurrentUser.getUserNickname());
            castChanges.setUpdateUser(CurrentUser.getUserNickname());
            castChanges.setCreateTime(new Date());
            castChanges.setUpdateTime(new Date());
            // 判断是打包还是实体运单，取出用户确认的费用
            // 如果是打包单，取打包后的运费和调度费
            castChanges.setCarriageFee(tOrderInfo.getSharePaymentAmount());
            castChanges.setDispatchFee(tOrderInfo.getShareDispatchFee());
            castChanges.setTotalFee(castChanges.getCarriageFee().add(castChanges.getDispatchFee()));
            // 临时设置运单主表总费用为资金变动的总费用，不更新运单主表
            tOrderInfo.setTotalFee(castChanges.getTotalFee());
            castChanges.setServiceFee(tOrderInfo.getUserConfirmServiceFee());
            // 添加最新资金变动
            tOrderCastChangesMapper.insertSelective(castChanges);
            cc.setDataEnable(false);
            // 将上一条资金变动置为无效
            tOrderCastChangesMapper.updateByPrimaryKeySelective(cc);
            //修改企业钱包,将总费用挪到入账中
            hxWalletUtil.modifyWallet(castChanges.getCompanyWalletId(), castChanges.getTotalFee(), tOrderInfo.getFeeSettlementWay());
            // 添加支付主表
            TOrderPayInfo orderPayInfo = hxPaymentUtil.createOrderPayInfo(tOrderInfo);
            String orderPayDetailCode = hxPaymentUtil.createTxOrderPayDetail(orderPayInfo.getCode(), castChanges.getCode(),
                    HXTradeTypeEnum.HX_COMBALANCE_PAY.code, TradeType.RZ.code);
            // 查询出款方、入款方会员编号
            SelectOpenRoleVO vo = new SelectOpenRoleVO();
            vo.setCarrierId(tOrderInfo.getCarrierId());
            vo.setCompanyId(tOrderInfo.getCompanyId());
            ResultUtil openRoleStatus = hxPayOrderUtil.selectOpenRoleStatus(vo);
            if (DictEnum.ERROR.code.equals(openRoleStatus.getCode())) {
                throw new RuntimeException(openRoleStatus.getMsg());
            }
            String jsonStr = JSONUtil.toJsonStr(openRoleStatus.getData());
            OpenRoleStatusListDTO openRoleStatusListDTO = JSONUtil.toBean(jsonStr, OpenRoleStatusListDTO.class);
            // TODO 构造京东支付请求参数
            CustomerBalancePayReq req = new CustomerBalancePayReq();
            req.setPartnerId(hxPropertiesConfig.getPartnerId());
            req.setRequestId(IdWorkerUtil.getInstance().nextId());
            req.setRequestTime(DateUtils.getRequestTime());
            req.setChannelId(hxPropertiesConfig.getChannelId());
            req.setBizOrderNo(orderPayDetailCode);
            req.setOutPartnerAccId(openRoleStatusListDTO.getCompanyStatus().getPartnerAccId());
            req.setInPartnerAccId(openRoleStatusListDTO.getCarrierStatus().getPartnerAccId());
            req.setOrderAmount(castChanges.getTotalFee());
            req.setTradeAbstract(String.join(tOrderInfo.getSettledWeight().toPlainString(), tOrderInfo.getGoodsName(), DictEnum.DUN.code));
            SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.HXPAYNOTIFYURL.code);
            req.setNotifyUrl(paramByKey.getParamValue());

            MQMessage mqMessage = HXMQMessageFactory.getMessage(HXTradeTypeEnum.HX_COMBALANCE_PAY.code,
                    castChanges.getCapitalTransferType(), castChanges.getCapitalTransferPattern());
            mqMessage.setKey(tOrderInfo.getCode());
            mqMessage.setBody(req);
            TMqMessagePackPayDetail messagePackPayDetail = new TMqMessagePackPayDetail();
            messagePackPayDetail.setTopic(mqMessage.getTopic());
            messagePackPayDetail.setTag(mqMessage.getTag());
            messagePackPayDetail.setMessageKey(tOrderInfo.getCode());
            messagePackPayDetail.setBody(JSONUtil.toJsonStr(req));
            messagePackPayDetail.setOrderCode(tOrderInfo.getCode());
            messagePackPayDetail.setStatus(false);
            mqMessagePackPayDetailMapper.insertSelective(messagePackPayDetail);
            /*ResultUtil resultUtil = mqAPI.sendMessage(mqMessage);
            log.info("发送MQ消息结果，{}", JSONUtil.toJsonStr(resultUtil));
            if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
                throw new RuntimeException("ZJJ-210:支付失败！");
            }*/
        } catch (Exception e) {
            log.error("ZJJ-210:支付失败！", e);
            String message = e.getMessage();
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                throw new RuntimeException(BusinessCode.PROJECTWALLETINSUFFICIENT.code);
            }
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                throw new RuntimeException(BusinessCode.WALLETAMOUNTINSUFFICIENT.code);
            }
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            }
            throw new RuntimeException("ZJJ-210:支付失败！");
        }
        return ResultUtil.ok();
    }

}
