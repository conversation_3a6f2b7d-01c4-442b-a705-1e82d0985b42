package com.lz.payment.hxyh.hxstrategy;

import cn.hutool.json.JSONUtil;
import com.lz.api.HXOpenRoleCommonAPI;
import com.lz.api.MqAPI;
import com.lz.common.config.HXPropertiesConfig;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.HXTradeTypeEnum;
import com.lz.common.factory.HXMQMessageFactory;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.MQMessage;
import com.lz.common.model.hxPayment.request.pay.CustomerBalancePayReq;
import com.lz.common.util.*;
import com.lz.dao.*;
import com.lz.dto.OpenRoleStatusListDTO;
import com.lz.model.TOrderCastChanges;
import com.lz.model.TOrderInfo;
import com.lz.model.TOrderPackInfo;
import com.lz.model.TOrderPayInfo;
import com.lz.payment.TradeType;
import com.lz.payment.hxyh.HXPaymentUtil;
import com.lz.payment.hxyh.HXWalletUtil;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.vo.RecallWalletVO;
import com.lz.vo.SelectOpenRoleVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
  * 打包支付退款
**/
@Slf4j
@Service
public class HXPackRefundStrategy extends HPaymentStrategy {

    @Resource
    private TOrderCastChangesMapper orderCastChangesMapper;

    @Resource
    private TOrderPayInfoMapper orderPayInfoMapper;

    @Resource
    private TOrderPackDetailMapper orderPackDetailMapper;

    @Resource
    private HXWalletUtil hxWalletUtil;

    @Resource
    private HXPaymentUtil hxPaymentUtil;

    @Resource
    private HXPropertiesConfig hxPropertiesConfig;

    @Resource
    private MqAPI mqAPI;

    @Resource
    private SysParamAPI sysParamAPI;

    @Autowired
    private HXOpenRoleCommonAPI hxOpenRoleCommonAPI;

    @Resource
    private TOrderPackInfoMapper orderPackInfoMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil toPackRefund(TOrderPackInfo orderPackInfo) {
        try {
            TOrderInfo orderInfo = orderPackDetailMapper.selectOrderInfoByPackCode(orderPackInfo.getCode());
            TOrderPayInfo orderPayInfo = new TOrderPayInfo();
            orderPayInfo.setOrderCode(orderInfo.getCode());
            orderPayInfo.setOrderPayStatus(DictEnum.M090.code);
            orderPayInfo = orderPayInfoMapper.selectPayInfoBySelective(orderPayInfo);
            if (null == orderPayInfo) {
                orderPayInfo = orderPayInfoMapper.selectOnePayInfoByOrderCode(orderInfo.getCode());
                orderPayInfo.setId(null);
                orderPayInfo.setCode(IdWorkerUtil.getInstance().nextId());
                orderPayInfo.setOrderCode(orderPackInfo.getCode());
                orderPayInfo.setOrderActualPayment(orderPackInfo.getAppointmentPaymentCash());
                orderPayInfo.setOrderTotalPayment(orderPackInfo.getAppointmentPaymentCash());
                orderPayInfo.setOrderPayStatus(DictEnum.P070.code);
                orderPayInfo.setCreateUser(CurrentUser.getUserNickname());
                orderPayInfo.setCreateTime(new Date());
                orderPayInfo.setUpdateUser(orderPayInfo.getCreateUser());
                orderPayInfo.setUpdateTime(orderPayInfo.getUpdateTime());
                orderPayInfoMapper.insertSelective(orderPayInfo);
            } else {
                orderPayInfo.setOrderPayStatus(DictEnum.P070.code);//运单支付状态  //支付中
                orderPayInfo.setOrderPrepayAmount(orderPackInfo.getAppointmentPaymentCash());
                orderPayInfo.setOrderTotalPayment(orderPackInfo.getAppointmentPaymentCash());
                orderPayInfoMapper.updateByPrimaryKeySelective(orderPayInfo);
            }

            TOrderCastChanges cc = orderCastChangesMapper.selectByNewOne(orderPackInfo.getCode());
            if (null != cc) {
                cc.setDataEnable(false);
                orderCastChangesMapper.updateByPrimaryKeySelective(cc);
            }
            cc = orderCastChangesMapper.selectOrderCastChangeOneByPackCode(orderPackInfo.getCode());
            cc.setOrderCode(orderPackInfo.getCode());
            cc.setCarriageFee(orderPackInfo.getAppointmentPaymentCash());
            cc.setDispatchFee(orderPackInfo.getRecountDispatchFee());
            cc.setServiceFee(orderPackInfo.getTotalSelectedOrdersServiceFee());
            cc.setTotalFee(orderPackInfo.getAppointmentPaymentCash());
            TOrderCastChanges castChanges = new TOrderCastChanges();
            BeanUtils.copyProperties(cc, castChanges);
            castChanges.setId(null);
            castChanges.setCode(IdWorkerUtil.getInstance().nextId());
            castChanges.setUserOper(TradeType.Recall.code);
            castChanges.setTradeType(TradeType.RZ.code);
            castChanges.setCreateUser(CurrentUser.getUserNickname());
            castChanges.setUpdateUser(CurrentUser.getUserNickname());
            castChanges.setCreateTime(new Date());
            castChanges.setUpdateTime(new Date());
            orderCastChangesMapper.insertSelective(castChanges);

            // 查询出款方、入款方会员编号
            SelectOpenRoleVO vo = new SelectOpenRoleVO();
            vo.setDriverId(orderInfo.getEndDriverId());
            vo.setCarrierId(orderInfo.getCarrierId());
            // 经纪人 - 车队长 = 分账退款
            if (DictEnum.MANAGERPATTERN.code.equals(castChanges.getCapitalTransferPattern())) {
                // 经纪人余额支付
                vo.setManagerId(orderInfo.getAgentId());
            }
            if (DictEnum.PAYTOCAPTAIN.code.equals(castChanges.getCapitalTransferType())) {
                // 车队长余额支付
                vo.setCaptainId(orderInfo.getEndCarOwnerId());
            }
            ResultUtil openRoleStatus = hxOpenRoleCommonAPI.selectCarrierCompanyEnduserOpenRoleStatus(vo);
            if (DictEnum.ERROR.code.equals(openRoleStatus.getCode())) {
                throw new RuntimeException(openRoleStatus.getMsg());
            }
            String jsonStr = JSONUtil.toJsonStr(openRoleStatus.getData());
            OpenRoleStatusListDTO openRoleStatusListDTO = JSONUtil.toBean(jsonStr, OpenRoleStatusListDTO.class);

            BigDecimal carriageFee = castChanges.getCarriageFee();
            // 保险费
            BigDecimal insuranceAmount = orderPackInfoMapper.calculateInsuranceAmount(orderPackInfo.getCode());
            if (null != insuranceAmount) {
                carriageFee = carriageFee.subtract(insuranceAmount);
            }
            // 修改C端钱包,将余额拿到入账中
            RecallWalletVO walletVO = new RecallWalletVO();
            walletVO.setDriverWalletId(castChanges.getEndDriverWalletId());
            walletVO.setCarrierWalletId(castChanges.getCarrierWalletId());
            if (DictEnum.PAYTOCAPTAIN.code.equals(castChanges.getCapitalTransferType())) {
                walletVO.setCaptainId(orderInfo.getEndCarOwnerId());
            }
            if (DictEnum.MANAGERPATTERN.code.equals(castChanges.getCapitalTransferPattern())) {
                walletVO.setManagerId(orderInfo.getAgentId());
            }
            walletVO.setCapitalTransferType(castChanges.getCapitalTransferType());
            walletVO.setCapitalTransferPattern(castChanges.getCapitalTransferPattern());
            walletVO.setCarriageFee(carriageFee);
            walletVO.setDispatchFee(castChanges.getDispatchFee());
            walletVO.setServiceFee(castChanges.getServiceFee());
            walletVO.setTotalFee(castChanges.getTotalFee());
            hxWalletUtil.refundModifyWallet(walletVO);

            // 查询回调地址
            SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.HXPAYNOTIFYURL.code);

            if (DictEnum.COMMONPATTERN.code.equals(castChanges.getCapitalTransferPattern())) {
                String outPartnerAccId = "";
                String tradeType = "";
                if (DictEnum.PAYTODRIVER.code.equals(castChanges.getCapitalTransferType())) {
                    outPartnerAccId = openRoleStatusListDTO.getDriverStatus().getPartnerAccId();
                    tradeType = HXTradeTypeEnum.HX_CDREFUNDBALANCE_PAY.code;
                }
                if (DictEnum.PAYTOCAPTAIN.code.equals(castChanges.getCapitalTransferType())) {
                    // 判断司机和车队长是否同一人
                    if (openRoleStatusListDTO.getDriverStatus().getPartnerAccId().equals(openRoleStatusListDTO.getCaptionStatus().getPartnerAccId())) {
                        tradeType = HXTradeTypeEnum.HX_CDREFUNDBALANCE_PAY.code;
                        outPartnerAccId = openRoleStatusListDTO.getDriverStatus().getPartnerAccId();
                    } else {
                        outPartnerAccId = openRoleStatusListDTO.getCaptionStatus().getPartnerAccId();
                        tradeType = HXTradeTypeEnum.HX_CCREFUNDBALANCE_PAY.code;
                    }
                }
                // 插入支付字表
                String orderPayDetailCode = hxPaymentUtil.createTxOrderPayDetail(orderPayInfo.getCode(),
                        castChanges.getCode(), tradeType, TradeType.DBZH.code);
                CustomerBalancePayReq req = getCustomerBalancePayReq("运费",
                        carriageFee,
                        outPartnerAccId,
                        openRoleStatusListDTO.getCarrierStatus().getPartnerAccId(),
                        paramByKey.getParamValue(),
                        orderPayDetailCode);
                MQMessage mqMessage = HXMQMessageFactory.getMessage(HXTradeTypeEnum.HX_REFUND.code, castChanges.getCapitalTransferType(), castChanges.getCapitalTransferPattern());
                mqMessage.setKey(orderInfo.getCode());
                mqMessage.setBody(req);
                ResultUtil resultUtil = mqAPI.sendMessage(mqMessage);
                log.info("发送MQ消息结果，{}", JSONUtil.toJsonStr(resultUtil));
                if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
                    throw new RuntimeException("ZJJ-302:运单召回失败！");
                }
            }
            if (DictEnum.MANAGERPATTERN.code.equals(castChanges.getCapitalTransferPattern())) {
                String remark = IdWorkerUtil.getInstance().nextId();
                if (DictEnum.PAYTODRIVER.code.equals(castChanges.getCapitalTransferType())) {
                    // 插入支付字表
                    String orderPayDetailCode = hxPaymentUtil.createOrderPayDetail(orderPayInfo.getCode(),
                            castChanges.getCode(), HXTradeTypeEnum.HX_CDREFUNDBALANCE_PAY.code, TradeType.DBZH.code, remark);
                    CustomerBalancePayReq req = getCustomerBalancePayReq("运费",
                            carriageFee.subtract(castChanges.getServiceFee()),
                            openRoleStatusListDTO.getDriverStatus().getPartnerAccId(),
                            openRoleStatusListDTO.getCarrierStatus().getPartnerAccId(),
                            paramByKey.getParamValue(),
                            orderPayDetailCode);
                    MQMessage mqMessage = HXMQMessageFactory.getMessage(HXTradeTypeEnum.HX_REFUND.code,
                            castChanges.getCapitalTransferType(),
                            castChanges.getCapitalTransferPattern());
                    mqMessage.setKey(orderInfo.getCode());
                    mqMessage.setBody(req);
                    ResultUtil resultUtil = mqAPI.sendMessage(mqMessage);
                    log.info("发送MQ消息结果，{}", JSONUtil.toJsonStr(resultUtil));
                    if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
                        throw new RuntimeException("ZJJ-302:运单召回失败！");
                    }
                }
                if (DictEnum.PAYTOCAPTAIN.code.equals(castChanges.getCapitalTransferType())) {
                    // 插入支付字表
                    String tradeType = HXTradeTypeEnum.HX_CCREFUNDBALANCE_PAY.code;
                    String outPartnerAccId = openRoleStatusListDTO.getCaptionStatus().getPartnerAccId();
                    // 判断司机和车队长是否同一人
                    if (openRoleStatusListDTO.getDriverStatus().getPartnerAccId().equals(openRoleStatusListDTO.getCaptionStatus().getPartnerAccId())) {
                        tradeType = HXTradeTypeEnum.HX_CDREFUNDBALANCE_PAY.code;
                        outPartnerAccId = openRoleStatusListDTO.getDriverStatus().getPartnerAccId();
                    }
                    String orderPayDetailCode = hxPaymentUtil.createOrderPayDetail(orderPayInfo.getCode(),
                            castChanges.getCode(), tradeType, TradeType.DBZH.code, remark);
                    CustomerBalancePayReq req = getCustomerBalancePayReq("运费",
                            carriageFee.subtract(castChanges.getServiceFee()),
                            outPartnerAccId,
                            openRoleStatusListDTO.getCarrierStatus().getPartnerAccId(),
                            paramByKey.getParamValue(),
                            orderPayDetailCode);
                    MQMessage mqMessage = HXMQMessageFactory.getMessage(HXTradeTypeEnum.HX_REFUND.code, castChanges.getCapitalTransferType(), castChanges.getCapitalTransferPattern());
                    mqMessage.setKey(orderInfo.getCode());
                    mqMessage.setBody(req);
                    ResultUtil resultUtil = mqAPI.sendMessage(mqMessage);
                    log.info("发送MQ消息结果，{}", JSONUtil.toJsonStr(resultUtil));
                    if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
                        throw new RuntimeException("ZJJ-302:运单召回失败！");
                    }
                }
                // 插入支付字表
                String orderPayDetailCode = hxPaymentUtil.createOrderPayDetail(orderPayInfo.getCode(),
                        castChanges.getCode(), HXTradeTypeEnum.HX_CMREFUNDBALANCE_PAY.code, TradeType.DBZH.code, remark);
                CustomerBalancePayReq req = getCustomerBalancePayReq("运费",
                        castChanges.getServiceFee(),
                        openRoleStatusListDTO.getManagerStatus().getPartnerAccId(),
                        openRoleStatusListDTO.getCarrierStatus().getPartnerAccId(),
                        paramByKey.getParamValue(),
                        orderPayDetailCode);
                MQMessage mqMessage = HXMQMessageFactory.getMessage(HXTradeTypeEnum.HX_REFUND.code, castChanges.getCapitalTransferType(), castChanges.getCapitalTransferPattern());
                mqMessage.setKey(orderInfo.getCode());
                mqMessage.setBody(req);
                ResultUtil resultUtil = mqAPI.sendMessage(mqMessage);
                log.info("发送MQ消息结果，{}", JSONUtil.toJsonStr(resultUtil));
                if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
                    throw new RuntimeException("ZJJ-302:运单召回失败！");
                }
            }
        } catch (Exception e) {
            log.error("ZJJ-302:运单召回失败！{}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("ZJJ-302:运单召回失败！");
            }
        }
        return ResultUtil.ok();
    }

    private CustomerBalancePayReq getCustomerBalancePayReq(String tradeAbstract,
                                                           BigDecimal orderAmount,
                                                           String outPartnerAccId,
                                                           String inPartnerAccId,
                                                           String notifyUrl,
                                                           String orderPayDetailCode) {
        CustomerBalancePayReq req = new CustomerBalancePayReq();
        req.setPartnerId(hxPropertiesConfig.getPartnerId());
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestId(DateUtils.getRequestTime());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setChannelId(hxPropertiesConfig.getChannelId());
        req.setBizOrderNo(orderPayDetailCode);
        req.setOutPartnerAccId(outPartnerAccId);
        req.setInPartnerAccId(inPartnerAccId);
        req.setOrderAmount(orderAmount);
        req.setTradeAbstract(tradeAbstract);
        req.setNotifyUrl(notifyUrl);
        return req;
    }

}
