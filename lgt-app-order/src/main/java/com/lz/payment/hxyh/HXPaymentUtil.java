package com.lz.payment.hxyh;

import cn.hutool.json.JSONUtil;
import com.lz.api.AccountService;
import com.lz.api.MqAPI;
import com.lz.common.config.HXPropertiesConfig;
import com.lz.common.constants.HXMqMessageTag;
import com.lz.common.constants.HXMqMessageTopic;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.HXTradeTypeEnum;
import com.lz.common.dbenum.JdEnum;
import com.lz.common.factory.HXMQMessageFactory;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.MQMessage;
import com.lz.common.model.hxPayment.request.pay.CustomerBalancePayReq;
import com.lz.common.model.hxPayment.request.query.CustomerReceiptReq;
import com.lz.common.util.*;
import com.lz.dao.*;
import com.lz.dto.*;
import com.lz.hxpay.CloudPaymentAPI;
import com.lz.model.*;
import com.lz.payment.CompanyProjectUtil;
import com.lz.payment.TradeType;
import com.lz.schedule.model.TTask;
import com.lz.sms.model.SmsReq;
import com.lz.sms.service.SmsClientService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.util.OrderDeductionUtil;
import com.lz.util.WxMsgSendUtil;
import com.lz.vo.AccountServiceFailedRecordVO;
import com.lz.vo.CompanyProjectByOrderVO;
import com.lz.vo.HxAccountServiceFeeRequest;
import com.lz.vo.SearchAccountVO;
import commonSdk.responseModel.CustomerReceiptResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import sdk.model.message.AsyncNotifyVirtualBalancePayMessageBody;
import sdk.model.message.AsyncNotifyVirtualMemberWithdrawMessageBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 *  @author: dingweibo
 *  @Date: 2022/11/24 13:55
 *  @Description: 华夏支付工具
 */
@Slf4j
@Component
public class HXPaymentUtil {

    @Autowired
    private TOrderCastCalcSnapshotMapper orderCastCalcSnapshotMapper;

    @Resource
    private THXOrderInfoMapper hxOrderInfoMapper;

    @Resource
    private THXOrderPayInfoMapper hxOrderPayInfoMapper;

    @Resource
    private THXOrderPayDetailMapper hxOrderPayDetailMapper;

    @Resource
    private THXOrderWalletChangeLogMapper hxOrderWalletChangeLogMapper;

    @Autowired
    private TOrderPackInfoMapper orderPackInfoMapper;

    @Autowired
    private TOrderStateMapper orderStateMapper;

    @Autowired
    private TOrderPayRuleMapper orderPayRuleMapper;
    @Autowired
    private THxPayBankCardMapper hxPayBankCardMapper;

    @Resource
    private TOrderTaskMapper orderTaskMapper;

    @Resource
    private TAdvanceOrderTmpMapper advanceOrderTmpMapper;

    @Resource
    private TAdvanceOrderPayTmpMapper advanceOrderPayTmpMapper;

    @Resource
    private TOrderServicefeeRecordMapper orderServicefeeRecordMapper;

    @Resource
    private TOrderPayRequestMapper orderPayRequestMappe;

    @Resource
    private TOrderInfoDetailMapper orderInfoDetailMapper;

    @Autowired
    private HXWalletUtil hxWalletUtil;

    @Autowired
    private HXPropertiesConfig hxPropertiesConfig;

    @Autowired
    private SysParamAPI sysParamAPI;

    @Autowired
    private CompanyProjectUtil companyProjectUtil;

    @Autowired
    private MqAPI mqAPI;

    @Autowired
    private CloudPaymentAPI cloudPaymentAPI;

    @Autowired
    private AccountService accountService;

    @Autowired
    private SmsClientService smsAPI;

    @Resource
    private TOrderInsuranceMapper orderInsuranceMapper;

    /**
    * @description 恢复企业项目可用额度
    * <AUTHOR>
    * @date 2021/8/24 11:09
    */
    public void unfreezeCompanyProject(Integer companyProjectId, BigDecimal totalFee) {
        CompanyProjectByOrderVO cp = new CompanyProjectByOrderVO();
        cp.setCompanyProjectId(companyProjectId);
        cp.setTotalPrice(totalFee);
        companyProjectUtil.unfreeze(cp);
    }

    /**
    * @description 召回重新冻结账单额度
    * <AUTHOR>
    * @date 2021/8/26 16:16
    */
    public void freezeCompanyProject(Integer companyProjectId, BigDecimal totalFee) {
        CompanyProjectByOrderVO cp = new CompanyProjectByOrderVO();
        cp.setCompanyProjectId(companyProjectId);
        cp.setTotalPrice(totalFee);
        companyProjectUtil.updateQuota(cp);
    }

    /**
     * @description 添加支付主表
     * <AUTHOR>
     * @date 2021/8/12 08:41
     * @param
     * @return
     */
    public TOrderPayInfo createOrderPayInfo(TOrderInfo tOrderInfo) {
        TOrderPayInfo orderPay = hxOrderPayInfoMapper.selectOnePayInfoByOrderCode(tOrderInfo.getCode());
        if (orderPay == null) {
            orderPay = new TOrderPayInfo();
            orderPay.setCode(IdWorkerUtil.getInstance().nextId());
            orderPay.setOrderCode(tOrderInfo.getCode());
            if (tOrderInfo.getPackStatus().equals(DictEnum.SINGLE.code)) {
                orderPay.setPayMethod(DictEnum.SINGLEPAY.code);
            } else {
                orderPay.setPayMethod(DictEnum.PACKPAY.code);
            }
            orderPay.setCarrierId(tOrderInfo.getCarrierId());
            orderPay.setFeeSettlementWay(tOrderInfo.getFeeSettlementWay());
            orderPay.setPaymentPlatforms(DictEnum.HXPLATFORMS.code);

            TOrderCastCalcSnapshot tOrderCastCalcSnapshot = orderCastCalcSnapshotMapper.selectSnapsByOrderCode(tOrderInfo.getCode());
            if (null != tOrderCastCalcSnapshot) {
                orderPay.setLineGoodsCarriageRuleId(tOrderCastCalcSnapshot.getLineGoodsCarriageRuleId());
            }
            orderPay.setOrderPayStatus(DictEnum.P070.code);
            orderPay.setOrderPrepayAmount(tOrderInfo.getTotalFee());
            orderPay.setOrderTotalPayment(tOrderInfo.getTotalFee());
            // 华夏支付
            orderPay.setParam1(DictEnum.HXPAY.code);
            hxOrderPayInfoMapper.insertSelective(orderPay);
        } else {
            orderPay.setPaymentPlatforms(DictEnum.HXPLATFORMS.code);
            orderPay.setPayMethod(DictEnum.SINGLEPAY.code);
            orderPay.setOrderPayStatus(DictEnum.P070.code);
            orderPay.setOrderPrepayAmount(tOrderInfo.getTotalFee());
            orderPay.setOrderActualPayment(tOrderInfo.getTotalFee());
            orderPay.setOrderTotalPayment(tOrderInfo.getTotalFee());
            orderPay.setParam1(DictEnum.HXPAY.code);
            hxOrderPayInfoMapper.updateByPrimaryKeySelective(orderPay);
        }

        return orderPay;
    }

    public String hxcreateOrderPayInfo(BigDecimal orderTotalPayment, String orderPayStatus) {
        TOrderPayInfo orderPayInfo = new TOrderPayInfo();
        orderPayInfo.setCode(IdWorkerUtil.getInstance().nextId());
        orderPayInfo.setOrderPrepayAmount(orderTotalPayment);
        orderPayInfo.setOrderActualPayment(orderTotalPayment);
        orderPayInfo.setOrderTotalPayment(orderTotalPayment);
        orderPayInfo.setOrderPayStatus(orderPayStatus);
        orderPayInfo.setPaymentPlatforms(DictEnum.HXPLATFORMS.code);
        hxOrderPayInfoMapper.insertSelective(orderPayInfo);
        return orderPayInfo.getCode();
    }

    public String createOrderPayInfo(BigDecimal orderTotalPayment, String orderPayStatus) {
        TOrderPayInfo orderPayInfo = new TOrderPayInfo();
        orderPayInfo.setCode(IdWorkerUtil.getInstance().nextId());
        orderPayInfo.setOrderPrepayAmount(orderTotalPayment.subtract(orderTotalPayment));
        orderPayInfo.setOrderActualPayment(orderTotalPayment);
        orderPayInfo.setOrderTotalPayment(orderTotalPayment);
        orderPayInfo.setOrderPayStatus(orderPayStatus);
        orderPayInfo.setPaymentPlatforms(DictEnum.HXPLATFORMS.code);
        hxOrderPayInfoMapper.insertSelective(orderPayInfo);
        return orderPayInfo.getCode();
    }

    public String createOrderPayDetail(String orderPayCode, String orderCastChangeCode, String tradeType, String OperateState, String remark) {
        TOrderPayDetail tOrderPayDetail = new TOrderPayDetail();
        tOrderPayDetail.setCode(IdWorkerUtil.getInstance().nextId());
        tOrderPayDetail.setOrderPayCode(orderPayCode);
        if (null != orderCastChangeCode) {
            tOrderPayDetail.setOrderCastChangeCode(orderCastChangeCode);
        }
        tOrderPayDetail.setTradeType(tradeType);
        tOrderPayDetail.setOperateState(OperateState);
        tOrderPayDetail.setOperateTime(new Date());
        tOrderPayDetail.setRemark(remark);
        hxOrderPayDetailMapper.insertSelective(tOrderPayDetail);
        return tOrderPayDetail.getCode();
    }

    public String createTxOrderPayDetail(String orderPayCode, String orderCastChangeCode, String tradeType, String OperateState) {
        TOrderPayDetail tOrderPayDetail = new TOrderPayDetail();
        tOrderPayDetail.setCode(IdWorkerUtil.getInstance().nextId());
        tOrderPayDetail.setOrderPayCode(orderPayCode);
        if (null != orderCastChangeCode) {
            tOrderPayDetail.setOrderCastChangeCode(orderCastChangeCode);
        }
        tOrderPayDetail.setTradeType(tradeType);
        tOrderPayDetail.setOperateState(OperateState);
        tOrderPayDetail.setOperateTime(new Date());
        hxOrderPayDetailMapper.insertSelective(tOrderPayDetail);
        return tOrderPayDetail.getCode();
    }

    public String createOrderPayDetail(String orderPayCode, String orderCastChangeCode, String tradeType, String OperateState, Integer walletId) {
        TOrderPayDetail tOrderPayDetail = new TOrderPayDetail();
        tOrderPayDetail.setCode(IdWorkerUtil.getInstance().nextId());
        tOrderPayDetail.setOrderPayCode(orderPayCode);
        if (null != orderCastChangeCode) {
            tOrderPayDetail.setOrderCastChangeCode(orderCastChangeCode);
        }
        tOrderPayDetail.setTradeType(tradeType);
        tOrderPayDetail.setOperateState(OperateState);
        tOrderPayDetail.setOperateTime(new Date());
        tOrderPayDetail.setParam1(String.valueOf(walletId));
        hxOrderPayDetailMapper.insertSelective(tOrderPayDetail);
        return tOrderPayDetail.getCode();
    }

    public String createTxOrderPayDetail(String orderPayCode, String tradeType, Integer walletId, Integer enduserId, Date createTime, TZtBankCardDTO cardDTO) {
        TOrderPayDetail tOrderPayDetail = new TOrderPayDetail();
        tOrderPayDetail.setCode(IdWorkerUtil.getInstance().nextId());
        tOrderPayDetail.setOrderPayCode(orderPayCode);
        tOrderPayDetail.setTradeType(tradeType);
        tOrderPayDetail.setOperateState(TradeType.RZ.code);
        tOrderPayDetail.setBankCardId(cardDTO.getBankCardId());
        tOrderPayDetail.setBankNo(cardDTO.getCardNo());
        tOrderPayDetail.setCardHolder(cardDTO.getCardOwner());
        tOrderPayDetail.setOperateTime(createTime);
        tOrderPayDetail.setRemark(String.valueOf(createTime.getTime()));
        tOrderPayDetail.setParam1(String.valueOf(walletId));
        tOrderPayDetail.setParam2(String.valueOf(enduserId));
        tOrderPayDetail.setParam3("1");
        hxOrderPayDetailMapper.insertSelective(tOrderPayDetail);
        return tOrderPayDetail.getCode();
    }

    /**
     * 司机支付保险费
     * @param orderPayCode
     * @param orderCastChangeCode
     * @return
     */
    public String createInsurancePayDetail(String orderPayCode, String orderCastChangeCode) {
        TOrderPayDetail tOrderPayDetail = new TOrderPayDetail();
        tOrderPayDetail.setCode(IdWorkerUtil.getInstance().nextId());
        tOrderPayDetail.setOrderPayCode(orderPayCode);
        if (null != orderCastChangeCode) {
            tOrderPayDetail.setOrderCastChangeCode(orderCastChangeCode);
        }
        tOrderPayDetail.setTradeType(HXTradeTypeEnum.HX_INSURANCE.code);
        tOrderPayDetail.setOperateState(DictEnum.RZ.code);
        tOrderPayDetail.setOperateTime(new Date());
        hxOrderPayDetailMapper.insertSelective(tOrderPayDetail);
        return tOrderPayDetail.getCode();
    }

    /**
    * @description 创建预付款支付临时表
    * <AUTHOR>
    * @date 2021/11/10 10:39
    */
    public String createAdvanceOrderPayTmp(THxAdvanceOrderPayTempInfoDTO dto, String tradeType) {
        TAdvanceOrderPayTmp advanceOrderPayTmp = new TAdvanceOrderPayTmp();
        advanceOrderPayTmp.setCode(IdWorkerUtil.getInstance().nextId());
        advanceOrderPayTmp.setAdvanceCode(dto.getCode());
        advanceOrderPayTmp.setTradeType(tradeType);
        advanceOrderPayTmp.setOperateState(TradeType.RZ.code);
        advanceOrderPayTmp.setOperaterId(dto.getOperaterId());
        advanceOrderPayTmp.setOperateTime(new Date());
        advanceOrderPayTmp.setPaymentPlatforms(DictEnum.HXPLATFORMS.code);
        advanceOrderPayTmp.setCreateUser(dto.getCreateUser());
        advanceOrderPayTmp.setUpdateUser(dto.getCreateUser());
        advanceOrderPayTmpMapper.insertSelective(advanceOrderPayTmp);
        return advanceOrderPayTmp.getCode();
    }

    /**
    * @description 记录钱包流水
    * <AUTHOR>
    * @date 2021/8/18 09:23
    * @param
    * @return
    */
    public Integer createWalletLog(AsyncNotifyVirtualBalancePayMessageBody messageBody,
                                   Integer walletId, Integer traderWalletId, BigDecimal amount,
                                   String purseCategory, String orderBusinessCode,
                                   String tradeNo, String tradeType, Date tradeTime) {
        TZtWalletChangeLog walletChangeLog = new TZtWalletChangeLog();
        walletChangeLog.setWalletId(walletId);
        walletChangeLog.setAmount(amount);
        walletChangeLog.setOuterTradeNo(messageBody.getBankOrderNo());
        walletChangeLog.setOrderBusinessCode(orderBusinessCode);
        walletChangeLog.setWalletType(purseCategory);
        walletChangeLog.setTradeNo(tradeNo);
        walletChangeLog.setTradeType(tradeType);
        walletChangeLog.setTradeTime(tradeTime);
        walletChangeLog.setTraderWalletId(traderWalletId);
        walletChangeLog.setParam1("0");
        if (HXTradeTypeEnum.HX_CTXTRANSFERZHICHU.code.equals(tradeType) || HXTradeTypeEnum.HX_CTXTRANSFERSHOURU.code.equals(tradeType)) {
            walletChangeLog.setCreateTime(DateUtils.addSeconds(new Date(), 4));
            walletChangeLog.setUpdateTime(walletChangeLog.getCreateTime());
        }
        hxOrderWalletChangeLogMapper.insertSelective(walletChangeLog);
        return walletChangeLog.getId();
    }

    public Integer createWalletLog(AsyncNotifyVirtualBalancePayMessageBody messageBody,
                                   Integer walletId, Integer traderWalletId, BigDecimal amount,
                                   String purseCategory, String orderBusinessCode,
                                   String tradeNo, String tradeType, Date tradeTime, String packStatus) {
        TZtWalletChangeLog walletChangeLog = new TZtWalletChangeLog();
        walletChangeLog.setWalletId(walletId);
        walletChangeLog.setAmount(amount);
        walletChangeLog.setOuterTradeNo(messageBody.getBankOrderNo());
        walletChangeLog.setOrderBusinessCode(orderBusinessCode);
        walletChangeLog.setWalletType(purseCategory);
        walletChangeLog.setTradeNo(tradeNo);
        walletChangeLog.setTradeType(tradeType);
        walletChangeLog.setTradeTime(tradeTime);
        walletChangeLog.setTraderWalletId(traderWalletId);
        walletChangeLog.setGmtClose(new Date());
        walletChangeLog.setParam1(packStatus);
        if (TradeType.BPAY.code.equals(tradeType) || TradeType.PPAY.code.equals(tradeType) || TradeType.CPAY.code.equals(tradeType) || TradeType.CMFEEAPY.code.equals(tradeType)) {
            walletChangeLog.setCarryOverAmount(messageBody.getCarryOverAmount());
        }
        if (HXTradeTypeEnum.HX_CTXTRANSFERZHICHU.code.equals(tradeType) || HXTradeTypeEnum.HX_CTXTRANSFERSHOURU.code.equals(tradeType)) {
            walletChangeLog.setCreateTime(DateUtils.addSeconds(new Date(), 4));
            walletChangeLog.setUpdateTime(walletChangeLog.getCreateTime());
        }
        hxOrderWalletChangeLogMapper.insertSelective(walletChangeLog);
        return walletChangeLog.getId();
    }

    public Integer createWalletLog(String bankOrderNo, String bankNo,
                                              Integer walletId, BigDecimal amount,
                                              String purseCategory,
                                              String tradeNo, String tradeType, Date tradeTime, Date createTime) {
        TZtWalletChangeLog walletChangeLog = new TZtWalletChangeLog();
        walletChangeLog.setWalletId(walletId);
        walletChangeLog.setAmount(amount);
        walletChangeLog.setBankNo(bankNo);
        walletChangeLog.setOuterTradeNo(bankOrderNo);
        walletChangeLog.setWalletType(purseCategory);
        walletChangeLog.setTradeNo(tradeNo);
        walletChangeLog.setTradeType(tradeType);
        walletChangeLog.setTradeTime(tradeTime);
        walletChangeLog.setParam1("1");
        walletChangeLog.setCreateTime(createTime);
        walletChangeLog.setUpdateTime(walletChangeLog.getCreateTime());
        hxOrderWalletChangeLogMapper.insertSelective(walletChangeLog);
        return walletChangeLog.getId();
    }

    /**
    * @description version-2 提现回调插入钱包流水
    * <AUTHOR>
    * @date 2021/9/17 19:25
    */
    public Integer createTXWalletLog(AsyncNotifyVirtualMemberWithdrawMessageBody messageBody, TTxOrderPayDetailDTO dto,
                                     TZtWallet tZtWallet, String tradeType) {
        TZtWalletChangeLog walletChangeLog = new TZtWalletChangeLog();
        walletChangeLog.setWalletId(tZtWallet.getId());
        walletChangeLog.setWalletType(tZtWallet.getPurseCategory());
        walletChangeLog.setTradeNo(messageBody.getBizOrderNo());
        walletChangeLog.setTradeType(tradeType);
        walletChangeLog.setAmount(dto.getOrderTotalPayment());
        walletChangeLog.setTradeTime(dto.getCreateTime());
        walletChangeLog.setOuterTradeNo(messageBody.getBankOrderNo());
        walletChangeLog.setBankCardId(dto.getBankCardId());
        walletChangeLog.setCardNo(dto.getBankNo());
        walletChangeLog.setCardHolder(dto.getCardHolder());
        walletChangeLog.setGmtClose(new Date());
        walletChangeLog.setCreateTime(DateUtils.addSeconds(new Date(), 4));
        walletChangeLog.setUpdateTime(walletChangeLog.getCreateTime());
        hxOrderWalletChangeLogMapper.insertSelective(walletChangeLog);
        return walletChangeLog.getId();
    }

    /**
     * @description 退款记录钱包流水
     * <AUTHOR>
     * @date 2021/8/18 09:23
     * @param
     * @return
     */
    private void insertRefundCallbackWalletLog(AsyncNotifyVirtualBalancePayMessageBody messageBody, Integer walletId, Integer traderWalletId,
                                               BigDecimal amount, String purseCategory, String orderBusinessCode,
                                               String tradeNo, String tradeType, Date tradeTime, String packStatus) {
        TZtWalletChangeLog walletChangeLog = new TZtWalletChangeLog();
        walletChangeLog.setWalletId(walletId);
        walletChangeLog.setAmount(amount);
        walletChangeLog.setOuterTradeNo(messageBody.getBankOrderNo());
        walletChangeLog.setOrderBusinessCode(orderBusinessCode);
        walletChangeLog.setWalletType(purseCategory);
        walletChangeLog.setTradeNo(tradeNo);
        walletChangeLog.setTradeType(tradeType);
        walletChangeLog.setTradeTime(tradeTime);
        walletChangeLog.setTraderWalletId(traderWalletId);
        walletChangeLog.setParam1(packStatus);
        if (TradeType.CYUNFEIZH.code.equals(tradeType) || TradeType.CPYUNFEIZH.code.equals(tradeType) || TradeType.CMFUWUFEIZH.code.equals(tradeType) || TradeType.BPAYZH.code.equals(tradeType)) {
            walletChangeLog.setCarryOverAmount(messageBody.getCarryOverAmount());
        }
        hxOrderWalletChangeLogMapper.insertSelective(walletChangeLog);
    }

    /**
     * @description 记录钱包流水
     * @param messageBody 回调信息
     * @param dto 支付详情
     */
    public void insertWalletLog(AsyncNotifyVirtualBalancePayMessageBody messageBody, THxOrderPayInfoDTO dto) {
        // 企业余额支付回调
        if (HXTradeTypeEnum.HX_COMBALANCE_PAY.code.equals(dto.getTradeType())) {
            // 企业总运费支出
            Integer walletLogId = createWalletLog(messageBody, dto.getCompanyWalletId(), dto.getCarrierWalletId(), dto.getTotalFee(),
                    DictEnum.BCOMPANY.code, dto.getOrderBusinessCode(), dto.getOrderPayDetailCode(),
                    TradeType.BPAY.code, dto.getOperateTime(), dto.getPackStatus());
            // 保存电子回单
            saveReceipt(dto.getCompanyAccId(), dto.getOrderPayDetailCode(), DictEnum.SALE.code, walletLogId);
            // 承运方总运费收入
            createWalletLog(messageBody, dto.getCarrierWalletId(), dto.getCompanyWalletId(), dto.getTotalFee(),
                    DictEnum.PCARRIER.code, dto.getOrderBusinessCode(), dto.getOrderPayDetailCode(),
                    TradeType.PSHOURU.code, dto.getOperateTime(), dto.getPackStatus());
        }
        // 承运方余额支付回调
        if (HXTradeTypeEnum.HX_CABALANCE_PAY.code.equals(dto.getTradeType())) {
            BigDecimal carriageFee = dto.getCarriageFee();
            TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(dto.getOrderBusinessCode());
            if (null != tOrderInsurance && OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance)) {
                carriageFee = carriageFee.subtract(dto.getInsuredAmount());
            }
            // 承运方运费支出
            Integer walletLogId = createWalletLog(messageBody, dto.getCarrierWalletId(), dto.getEndDriverWalletId(), carriageFee.subtract(dto.getIllegalDeduction()),
                    DictEnum.PCARRIER.code, dto.getOrderBusinessCode(), dto.getOrderPayDetailCode(),
                    TradeType.PPAY.code, dto.getOperateTime(), dto.getPackStatus());
            if (null != dto.getIllegalOrder() && dto.getIllegalOrder() && null != dto.getIllegalDeduction() && dto.getIllegalDeduction().compareTo(BigDecimal.ZERO) > 0) {
                createWalletLog(messageBody, dto.getCarrierWalletId(), dto.getEndDriverWalletId(), dto.getIllegalDeduction(),
                        DictEnum.PCARRIER.code, dto.getOrderBusinessCode(), dto.getOrderPayDetailCode(),
                        TradeType.PDEDUCTIONSHOURU.code, dto.getOperateTime(), dto.getPackStatus());
            }
            // 保存电子回单
            saveReceipt(dto.getCarrierAccId(), dto.getOrderPayDetailCode(), DictEnum.SALE.code, walletLogId);
            // 司机运费收入
            createWalletLog(messageBody, dto.getEndDriverWalletId(), dto.getCarrierWalletId(), carriageFee.subtract(dto.getIllegalDeduction()),
                    DictEnum.CDRIVER.code, dto.getOrderBusinessCode(), dto.getOrderPayDetailCode(),
                    TradeType.CYUNFEISHOURU.code, dto.getOperateTime(), dto.getPackStatus());
        }
        // 司机余额支付回调
        if (HXTradeTypeEnum.HX_CDBALANCE_PAY.code.equals(dto.getTradeType())) {
            Integer walletLogId = null;
            if (DictEnum.COMMONPATTERN.code.equals(dto.getCapitalTransferPattern())
                    && DictEnum.PAYTOCAPTAIN.code.equals(dto.getCapitalTransferType())) {
                // 司机运费支出
                BigDecimal carriageFee = dto.getCarriageFee();
                TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(dto.getOrderBusinessCode());
                if (null != tOrderInsurance && OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance)) {
                    carriageFee = carriageFee.subtract(dto.getInsuredAmount());
                }
                walletLogId = createWalletLog(messageBody, dto.getEndDriverWalletId(), dto.getCaptainWalletId(), carriageFee.subtract(dto.getIllegalDeduction()),
                        DictEnum.CDRIVER.code, dto.getOrderBusinessCode(), dto.getOrderPayDetailCode(),
                        TradeType.CPAY.code, dto.getOperateTime(), dto.getPackStatus());
                // 车队长运费收入
                createWalletLog(messageBody, dto.getCaptainWalletId(), dto.getEndDriverWalletId(), carriageFee.subtract(dto.getIllegalDeduction()),
                        DictEnum.CCAPTAIN.code, dto.getOrderBusinessCode(), dto.getOrderPayDetailCode(),
                        TradeType.CYUNFEISHOURU.code, dto.getOperateTime(), dto.getPackStatus());
            }
            if (DictEnum.MANAGERPATTERN.code.equals(dto.getCapitalTransferPattern())
                    && DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())
                    && dto.getServiceFee().compareTo(BigDecimal.ZERO) > 0) {
                // 司机服务费支出
                walletLogId = createWalletLog(messageBody, dto.getEndDriverWalletId(), dto.getAgentWalletId(), dto.getServiceFee(),
                        DictEnum.CDRIVER.code, dto.getOrderBusinessCode(), dto.getOrderPayDetailCode(),
                        TradeType.CMFEEAPY.code, dto.getOperateTime(), dto.getPackStatus());
                // 经纪人服务费收入
                createWalletLog(messageBody, dto.getAgentWalletId(), dto.getEndDriverWalletId(), dto.getServiceFee(),
                        DictEnum.CMANAGER.code, dto.getOrderBusinessCode(), dto.getOrderPayDetailCode(),
                        TradeType.MFFESHOURU.code, dto.getOperateTime(), dto.getPackStatus());
            }
            // 保存电子回单
            if (null != walletLogId) {
                saveReceipt(dto.getDriverAccId(), dto.getOrderPayDetailCode(), DictEnum.SALE.code, walletLogId);
            }
        }
        if (HXTradeTypeEnum.HX_CCBALANCE_PAY.code.equals(dto.getTradeType())) {
            // 司机运费支出
            BigDecimal carriageFee = dto.getCarriageFee();
            TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(dto.getOrderBusinessCode());
            if (null != tOrderInsurance && OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance)) {
                carriageFee = carriageFee.subtract(dto.getInsuredAmount());
            }
            Integer walletLogId = createWalletLog(messageBody, dto.getEndDriverWalletId(), dto.getCaptainWalletId(), carriageFee.subtract(dto.getIllegalDeduction()).subtract(dto.getServiceFee()),
                    DictEnum.CDRIVER.code, dto.getOrderBusinessCode(), dto.getOrderPayDetailCode(),
                    TradeType.CPAY.code, dto.getOperateTime(), dto.getPackStatus());
            // 车队长运费收入
            createWalletLog(messageBody, dto.getCaptainWalletId(), dto.getEndDriverWalletId(), carriageFee.subtract(dto.getIllegalDeduction()).subtract(dto.getServiceFee()),
                    DictEnum.CCAPTAIN.code, dto.getOrderBusinessCode(), dto.getOrderPayDetailCode(),
                    TradeType.CYUNFEISHOURU.code, dto.getOperateTime(), dto.getPackStatus());
            // 保存电子回单
            if (null != walletLogId) {
                saveReceipt(dto.getDriverAccId(), dto.getOrderPayDetailCode(), DictEnum.SALE.code, walletLogId);
            }
        }
        if (HXTradeTypeEnum.HX_CMBALANCE_PAY.code.equals(dto.getTradeType())) {
            // 司机服务费支出
            Integer walletLogId = createWalletLog(messageBody, dto.getEndDriverWalletId(), dto.getAgentWalletId(), dto.getServiceFee(),
                    DictEnum.CDRIVER.code, dto.getOrderBusinessCode(), dto.getOrderPayDetailCode(),
                    TradeType.CMFEEAPY.code, dto.getOperateTime(), dto.getPackStatus());
            // 经纪人服务费收入
            createWalletLog(messageBody, dto.getAgentWalletId(), dto.getEndDriverWalletId(), dto.getServiceFee(),
                    DictEnum.CMANAGER.code, dto.getOrderBusinessCode(), dto.getOrderPayDetailCode(),
                    TradeType.MFFESHOURU.code, dto.getOperateTime(), dto.getPackStatus());
            // 保存电子回单
            if (null != walletLogId) {
                saveReceipt(dto.getDriverAccId(), dto.getOrderPayDetailCode(), DictEnum.SALE.code, walletLogId);
            }
        }

    }

    public void companyCallbackInsertWalletLog(AsyncNotifyVirtualBalancePayMessageBody messageBody, TOrderPayRequestDTO dto) {
        // 企业总运费支出
        Integer walletLogId = createWalletLog(messageBody, dto.getCompanyWalletId(), dto.getCarrierWalletId(), dto.getOrderTotalPayment(),
                DictEnum.BCOMPANY.code, dto.getOrderPayCode(), dto.getOrderPayDetailCode(),
                TradeType.BPAY.code, dto.getOperateTime(), "0");
        // 保存电子回单
        saveReceipt(dto.getCompanyAccId(), dto.getOrderPayDetailCode(), DictEnum.SALE.code, walletLogId);
        // 承运方总运费收入
        createWalletLog(messageBody, dto.getCarrierWalletId(), dto.getCompanyWalletId(), dto.getOrderTotalPayment(),
                DictEnum.PCARRIER.code, dto.getOrderPayCode(), dto.getOrderPayDetailCode(),
                TradeType.PSHOURU.code, dto.getOperateTime(), "0");
    }

    /**
    * @description 节点支付，记录钱包流水
    * <AUTHOR>
    * @date 2021/9/7 09:03
    */
    public void nodeInsertWalletLog(AsyncNotifyVirtualBalancePayMessageBody messageBody, THxOrderPayInfoDTO dto) {
        // 企业余额支付回调
        if (HXTradeTypeEnum.HX_COMBALANCE_PAY.code.equals(dto.getTradeType())) {
            // 企业总运费支出
            Integer walletLogId = createWalletLog(messageBody, dto.getCompanyWalletId(), dto.getCarrierWalletId(), dto.getTotalFee(),
                    DictEnum.BCOMPANY.code, dto.getOrderBusinessCode(), dto.getOrderPayDetailCode(),
                    TradeType.BPAY.code, dto.getOperateTime());
            // 保存电子回单
            saveReceipt(dto.getCompanyAccId(), dto.getOrderPayDetailCode(), HXTradeTypeEnum.SALE.code, walletLogId);
            // 承运方总运费收入
            createWalletLog(messageBody, dto.getCarrierWalletId(), dto.getCompanyWalletId(), dto.getTotalFee(),
                    DictEnum.PCARRIER.code, dto.getOrderBusinessCode(), dto.getOrderPayDetailCode(),
                    TradeType.PSHOURU.code, dto.getOperateTime());
        }
        // 承运方余额支付回调
        if (HXTradeTypeEnum.HX_CABALANCE_PAY.code.equals(dto.getTradeType())) {
            BigDecimal carriageFee = dto.getCarriageFee();
            if (TradeType.WKPAYNODE.code.equals(dto.getUserOper())) {
                if (null != dto.getInsure() && 0 != dto.getInsure() && !dto.getInsuranceCancellation() && dto.getInsuredAmount().compareTo(BigDecimal.ZERO) > 0) {
                    carriageFee = carriageFee.subtract(dto.getInsuredAmount());
                }
            }
            // 承运方运费支出
            Integer walletLogId = createWalletLog(messageBody, dto.getCarrierWalletId(), dto.getEndDriverWalletId(), carriageFee,
                    DictEnum.PCARRIER.code, dto.getOrderBusinessCode(), dto.getOrderPayDetailCode(),
                    TradeType.PPAY.code, dto.getOperateTime());
            // 保存电子回单
            saveReceipt(dto.getCarrierAccId(), dto.getOrderPayDetailCode(), HXTradeTypeEnum.SALE.code, walletLogId);
            // 司机运费收入
            createWalletLog(messageBody, dto.getEndDriverWalletId(), dto.getCarrierWalletId(), carriageFee,
                    DictEnum.CDRIVER.code, dto.getOrderBusinessCode(), dto.getOrderPayDetailCode(),
                    TradeType.CYUNFEISHOURU.code, dto.getOperateTime());
        }
        // 司机余额支付回调
        if (HXTradeTypeEnum.HX_CDBALANCE_PAY.code.equals(dto.getTradeType())) {
            Integer walletLogId = null;
            if (DictEnum.MANAGERPATTERN.code.equals(dto.getCapitalTransferPattern())
                    && DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())
                    && dto.getServiceFee().compareTo(BigDecimal.ZERO) > 0) {
                // 司机服务费支出
                walletLogId = createWalletLog(messageBody, dto.getEndDriverWalletId(), dto.getAgentWalletId(), dto.getServiceFee(),
                        DictEnum.CDRIVER.code, dto.getOrderBusinessCode(), dto.getOrderPayDetailCode(),
                        TradeType.CMFEEAPY.code, dto.getOperateTime());
                // 经纪人服务费收入
                createWalletLog(messageBody, dto.getAgentWalletId(), dto.getEndDriverWalletId(), dto.getServiceFee(),
                        DictEnum.CMANAGER.code, dto.getOrderBusinessCode(), dto.getOrderPayDetailCode(),
                        TradeType.MFFESHOURU.code, dto.getOperateTime());
            }
            // 保存电子回单
            if (null != walletLogId) {
                saveReceipt(dto.getDriverAccId(), dto.getOrderPayDetailCode(), HXTradeTypeEnum.SALE.code, walletLogId);
            }
        }
    }

    /**
     * @description 退款回调记录钱包流水
     * @param dto 回调信息
     */
    public void refundCallbackInsertWalletLog(AsyncNotifyVirtualBalancePayMessageBody messageBody, THxOrderPayInfoDTO dto, String packStatus) {
        String orderBusinessCode = DictEnum.SINGLE.code.equals(packStatus) ? dto.getOrderBusinessCode() : dto.getVirtualOrderNo();
        // 司机余额支付退款回调
        if (HXTradeTypeEnum.HX_CDREFUNDBALANCE_PAY.code.equals(dto.getTradeType())) {
            // 司机运费召回
            BigDecimal carriageFee = dto.getCarriageFee();
            if (!DictEnum.PACK.code.equals(packStatus)) {
                BigDecimal insuredAmount = BigDecimal.ZERO;
                if (null != dto.getInsure() && 0 != dto.getInsure() && !dto.getInsuranceCancellation() && dto.getInsuredAmount().compareTo(BigDecimal.ZERO) > 0) {
                    insuredAmount = dto.getInsuredAmount();
                }
                carriageFee = carriageFee.subtract(insuredAmount).subtract(dto.getIllegalDeduction());
            } else {
                // 查询打包单保费
                BigDecimal insuranceAmount = orderPackInfoMapper.calculateInsuranceAmount(dto.getPackCode());
                carriageFee = carriageFee.subtract(insuranceAmount);
            }
            insertRefundCallbackWalletLog(messageBody, dto.getEndDriverWalletId(), dto.getCarrierWalletId(), carriageFee.subtract(dto.getServiceFee()),
                    DictEnum.CDRIVER.code, orderBusinessCode, dto.getOrderPayDetailCode(),
                    TradeType.CYUNFEIZH.code, dto.getOperateTime(), packStatus);
            // 承运方司机运费召回收入
            insertRefundCallbackWalletLog(messageBody, dto.getCarrierWalletId(), dto.getEndDriverWalletId(), carriageFee.subtract(dto.getServiceFee()),
                    DictEnum.PCARRIER.code, orderBusinessCode, dto.getOrderPayDetailCode(),
                    TradeType.PCYUNFEIZHSR.code, dto.getOperateTime(), packStatus);
        }
        // 车队长余额支付退款回调
        if (HXTradeTypeEnum.HX_CCREFUNDBALANCE_PAY.code.equals(dto.getTradeType())) {
            // 车队长运费召回
            BigDecimal carriageFee = dto.getCarriageFee();
            if (!DictEnum.PACK.code.equals(packStatus)) {
                BigDecimal insuredAmount = BigDecimal.ZERO;
                if (null != dto.getInsure() && 0 != dto.getInsure() && !dto.getInsuranceCancellation() && dto.getInsuredAmount().compareTo(BigDecimal.ZERO) > 0) {
                    insuredAmount = dto.getInsuredAmount();
                }
                carriageFee = carriageFee.subtract(insuredAmount).subtract(dto.getIllegalDeduction());
            } else {
                // 查询打包单保费
                BigDecimal insuranceAmount = orderPackInfoMapper.calculateInsuranceAmount(dto.getPackCode());
                carriageFee = carriageFee.subtract(insuranceAmount);
            }
            insertRefundCallbackWalletLog(messageBody, dto.getCaptainWalletId(), dto.getCarrierWalletId(), carriageFee.subtract(dto.getServiceFee()),
                    DictEnum.CCAPTAIN.code, orderBusinessCode, dto.getOrderPayDetailCode(),
                    TradeType.CPYUNFEIZH.code, dto.getOperateTime(), packStatus);
            // 承运方车队长运费召回收入
            insertRefundCallbackWalletLog(messageBody, dto.getCarrierWalletId(), dto.getCaptainWalletId(), carriageFee.subtract(dto.getServiceFee()),
                    DictEnum.PCARRIER.code, orderBusinessCode, dto.getOrderPayDetailCode(),
                    TradeType.PCPYUNFEIZHSR.code, dto.getOperateTime(), packStatus);
        }
        // 经纪人余额支付退款回调
        if (HXTradeTypeEnum.HX_CMREFUNDBALANCE_PAY.code.equals(dto.getTradeType())) {
            // 经纪人服务费召回
            insertRefundCallbackWalletLog(messageBody, dto.getAgentWalletId(), dto.getCarrierWalletId(), dto.getServiceFee(),
                    DictEnum.CMANAGER.code, orderBusinessCode, dto.getOrderPayDetailCode(),
                    TradeType.CMFUWUFEIZH.code, dto.getOperateTime(), packStatus);
            // 承运方经纪人服务费召回收入
            insertRefundCallbackWalletLog(messageBody, dto.getCarrierWalletId(), dto.getAgentWalletId(), dto.getServiceFee(),
                    DictEnum.PCARRIER.code, orderBusinessCode, dto.getOrderPayDetailCode(),
                    TradeType.PCMYUNFEIZHSR.code, dto.getOperateTime(), packStatus);
        }
        // 承运方余额支付退款回调
        if (HXTradeTypeEnum.HX_CAREFUNDBALANCE_PAY.code.equals(dto.getTradeType())) {
            // 企业总运费召回
            BigDecimal totalFee = dto.getTotalFee();
            if (!DictEnum.PACK.code.equals(packStatus)) {
                totalFee = totalFee.add(dto.getIllegalDeduction());
            }
            insertRefundCallbackWalletLog(messageBody, dto.getCompanyWalletId(), dto.getCarrierWalletId(), totalFee,
                    DictEnum.BCOMPANY.code, orderBusinessCode, dto.getOrderPayDetailCode(),
                    TradeType.BPAYZH.code, dto.getOperateTime(), packStatus);
            // 承运方总运费召回
            insertRefundCallbackWalletLog(messageBody, dto.getCarrierWalletId(), dto.getCompanyWalletId(), totalFee,
                    DictEnum.PCARRIER.code, orderBusinessCode, dto.getOrderPayDetailCode(),
                    TradeType.PPAYZH.code, dto.getOperateTime(), packStatus);
        }
    }

    /**
     * @description 发起余额支付请求
     * @param tradeType 交易类型
     * @param operateState
     * @param dto 支付详情
     * @param orderPayDetailCode 支付子表code
     */
    public void requestBalance(String tradeType, String operateState, THxOrderPayInfoDTO dto, String orderPayDetailCode) {
        try {
            CustomerBalancePayReq req = new CustomerBalancePayReq();
            req.setPartnerId(hxPropertiesConfig.getPartnerId());
            req.setRequestId(IdWorkerUtil.getInstance().nextId());
            req.setRequestTime(DateUtils.getRequestTime());
            req.setChannelId(hxPropertiesConfig.getChannelId());
            req.setOrderAmount(dto.getCarriageFee());
            // 入账
            if (TradeType.RZ.code.equals(operateState)) {
                if (HXTradeTypeEnum.HX_CABALANCE_PAY.code.equals(tradeType)) {
                    req.setOutPartnerAccId(dto.getCarrierAccId());
                    req.setInPartnerAccId(dto.getDriverAccId());
                    BigDecimal orderAmount = dto.getCarriageFee();
                    if (dto.getIllegalOrder() && null != dto.getIllegalDeduction()) {
                        if (dto.getIllegalDeduction().compareTo(BigDecimal.ZERO) > 0
                                && dto.getCarriageFee().subtract(dto.getIllegalDeduction()).compareTo(BigDecimal.ZERO) > 0
                        && dto.getCarriageFee().subtract(dto.getIllegalDeduction()).subtract(dto.getServiceFee()).compareTo(BigDecimal.ZERO) > 0) {
                            orderAmount = dto.getCarriageFee().subtract(dto.getIllegalDeduction());
                        }
                    }
                    if (null != dto.getInsure() && dto.getInsure() > 0  && !dto.getInsuranceCancellation() && dto.getInsuredAmount().compareTo(BigDecimal.ZERO) > 0) {
                        orderAmount = orderAmount.subtract(dto.getInsuredAmount());
                    }
                    req.setOrderAmount(orderAmount);
                } else if (HXTradeTypeEnum.HX_CDBALANCE_PAY.code.equals(tradeType)) {
                    req.setOutPartnerAccId(dto.getDriverAccId());
                    if (DictEnum.COMMONPATTERN.code.equals(dto.getCapitalTransferPattern())) {
                        req.setInPartnerAccId(dto.getCaptainAccId());
                        if (null != dto.getInsure() && 0 != dto.getInsure() && !dto.getInsuranceCancellation() && dto.getInsuredAmount().compareTo(BigDecimal.ZERO) > 0) {
                            req.setOrderAmount(dto.getCarriageFee().subtract(dto.getIllegalDeduction()).subtract(dto.getInsuredAmount()));
                        } else {
                            req.setOrderAmount(dto.getCarriageFee().subtract(dto.getIllegalDeduction()));
                        }
                    } else if (DictEnum.MANAGERPATTERN.code.equals(dto.getCapitalTransferPattern())) {
                        req.setInPartnerAccId(dto.getAgentAccId());
                        req.setOrderAmount(dto.getServiceFee());
                    }
                } else if (HXTradeTypeEnum.HX_CCBALANCE_PAY.code.equals(tradeType)) {
                    req.setOutPartnerAccId(dto.getDriverAccId());
                    req.setInPartnerAccId(dto.getCaptainAccId());
                    if (null != dto.getInsure() && 0 != dto.getInsure() && !dto.getInsuranceCancellation() && dto.getInsuredAmount().compareTo(BigDecimal.ZERO) > 0) {
                        req.setOrderAmount(dto.getCarriageFee().subtract(dto.getIllegalDeduction()).subtract(dto.getInsuredAmount()).subtract(dto.getServiceFee()));
                    } else {
                        req.setOrderAmount(dto.getCarriageFee().subtract(dto.getIllegalDeduction()).subtract(dto.getServiceFee()));
                    }
                } else if (HXTradeTypeEnum.HX_CMBALANCE_PAY.code.equals(tradeType)) {
                    req.setOutPartnerAccId(dto.getDriverAccId());
                    req.setInPartnerAccId(dto.getAgentAccId());
                    req.setOrderAmount(dto.getServiceFee());
                }
            } else if (TradeType.RZNODE.code.equals(operateState)) {
                if (HXTradeTypeEnum.HX_CABALANCE_PAY.code.equals(tradeType)) {
                    req.setOutPartnerAccId(dto.getCarrierAccId());
                    req.setInPartnerAccId(dto.getDriverAccId());
                    // 节点尾款支付
                    if (DictEnum.WKPAYNODE.code.equals(dto.getUserOper())) {
                        if (null != dto.getInsure() && 0 != dto.getInsure() && !dto.getInsuranceCancellation() && dto.getInsuredAmount().compareTo(BigDecimal.ZERO) > 0) {
                            req.setOrderAmount(dto.getCarriageFee().subtract(dto.getIllegalDeduction()).subtract(dto.getInsuredAmount()));
                        } else {
                            req.setOrderAmount(dto.getCarriageFee().subtract(dto.getIllegalDeduction()));
                        }
                    }
                } else if (HXTradeTypeEnum.HX_CDBALANCE_PAY.code.equals(tradeType)) {
                    req.setOutPartnerAccId(dto.getDriverAccId());
                    req.setInPartnerAccId(dto.getAgentAccId());
                    req.setOrderAmount(dto.getServiceFee());
                }
            } else if (TradeType.ZH.code.equals(operateState)) {
                if (HXTradeTypeEnum.HX_CAREFUNDBALANCE_PAY.code.equals(tradeType)) {
                    tradeType = HXTradeTypeEnum.HX_REFUND.code;
                    req.setOutPartnerAccId(dto.getCarrierAccId());
                    req.setInPartnerAccId(dto.getCompanyAccId());
                    req.setOrderAmount(dto.getTotalFee());
                }
            } else if (TradeType.DBZH.code.equals(operateState)) {
                tradeType = HXTradeTypeEnum.HX_REFUND.code;
                req.setOutPartnerAccId(dto.getCarrierAccId());
                req.setInPartnerAccId(dto.getCompanyAccId());
                req.setOrderAmount(dto.getTotalFee());
            }
            if (HXTradeTypeEnum.HX_INSURANCE.code.equals(tradeType)) {
                // 司机支付保险费
                req.setOutPartnerAccId(dto.getDriverAccId());
                req.setInPartnerAccId(dto.getCarrierAccId());
                req.setOrderAmount(dto.getInsuredAmount());
            }
            req.setBizOrderNo(orderPayDetailCode);
            Double weight;
            if (null != dto.getSettledWeight()) {
                weight = new Double(String.valueOf(dto.getSettledWeight()));
            } else {
                weight = BigDecimal.valueOf(dto.getEstimateGoodsWeight()).compareTo(BigDecimal.ZERO) > 0 ? dto.getEstimateGoodsWeight() : dto.getPrimaryWeight();
            }
            req.setTradeAbstract("运费");
            if (HXTradeTypeEnum.HX_INSURANCE.code.equals(tradeType)) {
                req.setTradeAbstract("代收货运险保费");
            }
            SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.HXPAYNOTIFYURL.code);
            req.setNotifyUrl(paramByKey.getParamValue());
            MQMessage mqMessage = HXMQMessageFactory.getMessage(tradeType, dto.getCapitalTransferType(), dto.getCapitalTransferPattern());
            mqMessage.setKey(orderPayDetailCode);
            mqMessage.setBody(req);
            ResultUtil resultUtil = mqAPI.sendMessage(mqMessage);
            log.info("发送余额支付MQ消息结果，{}", JSONUtil.toJsonStr(resultUtil));
            if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
                throw new RuntimeException("ZJJ-210:支付失败！");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("余额支付异常，{}", ThrowableUtil.getStackTrace(e));
            throw new RuntimeException("ZJJ-210:支付失败！");
        }
    }

    /**
    * @description 发送预付款支付请求
    * <AUTHOR>
    * @date 2021/11/9 16:21
    */
    public void requestPrePay(String orderPayDetailCode, String outPartnerAccId, String inPartnerAccId, BigDecimal amount, TOrderInfo orderInfo) {
        CustomerBalancePayReq req = new CustomerBalancePayReq();
        req.setPartnerId(hxPropertiesConfig.getPartnerId());
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setChannelId(hxPropertiesConfig.getChannelId());
        req.setOrderAmount(amount);
        req.setOutPartnerAccId(outPartnerAccId);
        req.setInPartnerAccId(inPartnerAccId);
        req.setBizOrderNo(orderPayDetailCode);
        Double weight;
        if (null != orderInfo.getSettledWeight()) {
            weight = new Double(String.valueOf(orderInfo.getSettledWeight()));
        } else {
            weight = BigDecimal.valueOf(orderInfo.getEstimateGoodsWeight()).compareTo(BigDecimal.ZERO) > 0 ? orderInfo.getEstimateGoodsWeight() : orderInfo.getPrimaryWeight();
        }
        req.setTradeAbstract("运费");
        SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.HXPAYNOTIFYURL.code);
        req.setNotifyUrl(paramByKey.getParamValue());
        MQMessage mqMessage = new MQMessage();
        mqMessage.setTopic(HXMqMessageTopic.HX_PREPAYMENT);
        mqMessage.setTag(HXMqMessageTag.HX_PREPAY);
        mqMessage.setKey(orderPayDetailCode);
        mqMessage.setBody(req);
        ResultUtil resultUtil = mqAPI.sendMessage(mqMessage);
        log.info("发送余额支付MQ消息结果，{}", JSONUtil.toJsonStr(resultUtil));
        if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
            throw new RuntimeException("ZJJ-410:支付失败！");
        }
    }

    /**
     * 修改运单主表状态
     * @param id 运单主表ID
     * @param orderPayStatus 运单主表支付状态
     */
    public void updateOrderInfo(Integer id, String orderExecuteStatus, String orderPayStatus, Date returnTime) {
        // 修改运单主表状态
        TOrderInfo orderInfo = new TOrderInfo();
        orderInfo.setId(id);
        if (null != orderExecuteStatus) {
            orderInfo.setOrderExecuteStatus(orderExecuteStatus);
        }
        orderInfo.setOrderPayStatus(orderPayStatus);
        if (DictEnum.M090.code.equals(orderPayStatus)) {
            orderInfo.setOrderFinishTime(returnTime);
        }
        orderInfo.setUpdateTime(returnTime);
        hxOrderInfoMapper.updateByPrimaryKeySelective(orderInfo);
        // 删除支付请求
        // orderPayRequestMapper.deleteByOrderId(id);
    }

    public void packRefundCallbackUpdateOrderInfoByPackId(List<String> orderCodes, String orderExecuteStatus, String orderPayStatus, Date returnTime) {
        hxOrderInfoMapper.batchUpdateOrderExecutePayStatus(orderCodes, orderExecuteStatus, orderPayStatus, returnTime);
    }

    public void failCallbackUpdateOrderInfoByPackId(List<String> orderCodes, String orderExecuteStatus, String orderPayStatus, Date returnTime) {
        hxOrderInfoMapper.batchUpdateOrderExecutePayStatus(orderCodes, orderExecuteStatus, orderPayStatus, returnTime);
    }

    /**
    * @description 节点支付回调完成，修改运单主表
    * <AUTHOR>
    * @date 2021/8/27 10:11
    */
    public void nodeCallbackUpdateOrderInfo(THxOrderPayInfoDTO dto, Date returnTime) {
        TOrderInfo orderInfo = hxOrderInfoMapper.selectByPrimaryKey(dto.getOrderId());
        BigDecimal selectSumFee = orderPayRuleMapper.selectSumFee(orderInfo.getCode());
        TOrderInfo forUpdate = new TOrderInfo();
        forUpdate.setId(dto.getOrderId());
        // 修改用户确认运费
        forUpdate.setUserConfirmPaymentAmount(selectSumFee);
        // 重新计算调度费
        BigDecimal disFee = OrderMoneyUtil.getDispatchFee(dto.getCurrentDispatchRate(), selectSumFee);
        forUpdate.setDispatchFee(disFee);
        // 修改总费用
        forUpdate.setTotalFee(selectSumFee.add(disFee));
        forUpdate.setOrderExecuteStatus(DictEnum.M100.code);
        forUpdate.setOrderPayStatus(DictEnum.M090.code);
        forUpdate.setOrderFinishTime(returnTime);
        forUpdate.setUpdateTime(returnTime);
        hxOrderInfoMapper.updateByPrimaryKeySelective(forUpdate);
    }

    public void updateOrderPackInfo(String packCode, String packStatus, Date returnTime) {
        orderPackInfoMapper.updatePackStatusByCode(packCode, packStatus, returnTime);
    }

    /**
    * @description 退款回调修改运单主表状态
    * <AUTHOR>
    * @date 2021/8/26 16:39
    */
    public void refundUpdateOrderInfo(Integer id, String orderExecuteStatus, String orderPayStatus, Date returnTime) {
        hxOrderInfoMapper.refundUpdateOrderInfo(id, orderExecuteStatus, orderPayStatus, returnTime);
    }

    /**
     * 修改支付主表
     * @param id 支付主表ID
     * @param orderPayStatus 支付状态
     */
    public void updateOrderPayInfo(Integer id, String orderPayStatus, Date returnTime) {
        TOrderPayInfo payInfo = new TOrderPayInfo();
        payInfo.setId(id);
        payInfo.setOrderPayStatus(orderPayStatus);
        payInfo.setUpdateTime(returnTime);
        hxOrderPayInfoMapper.updateByPrimaryKeySelective(payInfo);
    }

    /**
     * 召回修改支付主表
     * @param id
     * @param orderPayStatus
     * @param returnTime
     */
    public void refundRecallbackUpdateOrderPayInfo(Integer id, String orderPayStatus, Date returnTime) {
        hxOrderPayInfoMapper.refundRecallbackUpdateOrderPayInfo(id, orderPayStatus, returnTime);
    }

    /**
     * 修改打包运单原始运单的支付状态
     * @param packId
     * @param orderPayStatus
     * @param returnTime
     */
    public void updatePackOrderOrgiOrderPayInfo(Integer packId, String orderPayStatus, Date returnTime) {
        hxOrderPayInfoMapper.updatePackOrderOrgiOrderPayInfo(packId, orderPayStatus, returnTime);
    }

    /**
     * 退款回调修改打包单原始运单支付主表
     * @param packId
     * @param orderPayStatus
     * @param returnTime
     */
    public void refundRecallbackUpdatePackOrderOrgiOrderPayInfo(Integer packId, String orderPayStatus, Date returnTime) {
        hxOrderPayInfoMapper.refundRecallbackUpdatePackOrderOrgiOrderPayInfo(packId, orderPayStatus, returnTime);
    }

    /**
     * 修改支付子表
     * @param id 支付子表ID
     * @param tradeStatus 交易状态
     */
    public void updateOrderPayDetail(Integer id, String tradeStatus, Date returnTime) {
        TOrderPayDetail payDetail = new TOrderPayDetail();
        payDetail.setId(id);
        payDetail.setReturnTime(returnTime);
        payDetail.setTradeStatus(tradeStatus);
        hxOrderPayDetailMapper.updateByPrimaryKeySelective(payDetail);
    }

    public void updateOrderPayDetailEnableByOrderCode(String orderCode) {
        hxOrderPayDetailMapper.updateOrderPayDetailEnableByOrderCode(orderCode);
    }

    public void updateOrderPayDetailEnableByOrderPackCode(String orderPackCode) {
        hxOrderPayDetailMapper.updateOrderPayDetailEnableByOrderPackCode(orderPackCode);
    }

    /**
    * @description 修改预付款运单状态
    * <AUTHOR>
    * @date 2021/11/9 15:48
    */
    public void updateAdvanceOrderTmp(Integer advanceId, String orderPayStatus, Date returnTime) {
        TAdvanceOrderTmp advanceOrderTmp = new TAdvanceOrderTmp();
        advanceOrderTmp.setId(advanceId);
        advanceOrderTmp.setOrderPayStatus(orderPayStatus);
        advanceOrderTmp.setUpdateTime(returnTime);
        advanceOrderTmp.setOperaterTime(returnTime);
        advanceOrderTmpMapper.updateByPrimaryKeySelective(advanceOrderTmp);
    }

    /**
    * @description 修改预付款运单支付临时表状态
    * <AUTHOR>
    * @date 2021/11/9 15:49
    */
    public void updateAdvanceOrderPayTmp(Integer payId, String status, Date returnTime, String bankOrderNo) {
        TAdvanceOrderPayTmp advanceOrderPayTmp = new TAdvanceOrderPayTmp();
        advanceOrderPayTmp.setId(payId);
        advanceOrderPayTmp.setTradeStatus(status);
        advanceOrderPayTmp.setInnerTradeNo(bankOrderNo);
        advanceOrderPayTmp.setReturnTime(returnTime);
        advanceOrderPayTmpMapper.updateByPrimaryKeySelective(advanceOrderPayTmp);
    }

    /**
     * @description 插入运单执行状态
     * <AUTHOR>
     * @date 2021/8/20 15:31
     * @param orderCode 运单code
     * @param stateNodeValue 执行状态
     * @param returnTime 回调时间
     */
    public void insertOrderState(String orderCode, String stateNodeValue, Date returnTime) {
        List<String> states = new ArrayList<>(2);
        states.add(DictEnum.SP0701.code);
        states.add(DictEnum.SP0702.code);
        TOrderState orderState = orderStateMapper.selectOrderStateByOrderCode(states, orderCode);
        //插入订单状态子表
        TOrderState tOrderState = new TOrderState();
        tOrderState.setCode(IdWorkerUtil.getInstance().nextId());
        tOrderState.setOrderCode(orderCode);
        if (null != orderState.getOperatorId()) {
            tOrderState.setOperatorId(orderState.getOperatorId());
        }
        tOrderState.setOperateTime(returnTime);
        tOrderState.setStateNodeValue(stateNodeValue);
        tOrderState.setIfExpire(false);
        tOrderState.setEnable(false);
        tOrderState.setCreateUser(orderState.getUpdateUser());
        tOrderState.setCreateTime(returnTime);
        if (null != orderState.getRemark()) {
            tOrderState.setRemark(orderState.getRemark());
        }
        tOrderState.setUpdateUser(orderState.getUpdateUser());
        tOrderState.setUpdateTime(returnTime);
        orderStateMapper.insert(tOrderState);
    }

    public void batchInsertOrderState(List<String> orderCodes, String stateNodeValue, Date returnTime) {
        if (null != orderCodes && orderCodes.size() >= 1) {
            TOrderState tOrderState = orderStateMapper.selectNewOrderState(orderCodes.get(0));
            ArrayList<TOrderState> orderStates = new ArrayList<>();
            for (String code : orderCodes) {
                //添加运单执行状态子表
                TOrderState orderState = new TOrderState();
                orderState.setCode(IdWorkerUtil.getInstance().nextId());
                orderState.setOrderCode(code);
                orderState.setStateNodeValue(stateNodeValue);
                orderState.setOperateMethod(null != tOrderState.getOperateMethod() ? tOrderState.getOperateMethod() : "");
                orderState.setOperateTime(returnTime);
                orderState.setIfExpire(false);
                orderState.setCreateUser(tOrderState.getCreateUser());
                orderState.setCreateTime(returnTime);
                orderState.setUpdateUser(tOrderState.getCreateUser());
                orderState.setUpdateTime(returnTime);
                orderState.setEnable(false);
                orderStates.add(orderState);
            }
            orderStateMapper.batchInsert(orderStates);
        }
    }

    /**
    * @description 节点支付：插入运单执行状态
    * <AUTHOR>
    * @date 2021/8/24 13:45
    */
    public void insertNodeOrderState(String orderCode, String nodeValue, Date returnTime) {
        List<String> states = new ArrayList<>(6);
        states.add(DictEnum.SP0706.code);
        states.add(DictEnum.SP0707.code);
        states.add(DictEnum.SP0708.code);
        states.add(DictEnum.SP0709.code);
        states.add(DictEnum.SP0710.code);
        states.add(DictEnum.SP0711.code);
        TOrderState orderState = orderStateMapper.selectOrderStateByOrderCode(states, orderCode);
        //插入订单状态子表
        TOrderState tOrderState = new TOrderState();
        tOrderState.setCode(IdWorkerUtil.getInstance().nextId());
        tOrderState.setOrderCode(orderCode);
        tOrderState.setOperatorId(orderState.getOperatorId());
        tOrderState.setOperateTime(returnTime);
        tOrderState.setStateNodeValue(nodeValue);
        tOrderState.setIfExpire(false);
        tOrderState.setEnable(false);
        tOrderState.setCreateUser(orderState.getUpdateUser());
        tOrderState.setCreateTime(returnTime);
        if (null != orderState.getRemark()) {
            tOrderState.setRemark(orderState.getRemark());
        }
        tOrderState.setUpdateUser(orderState.getUpdateUser());
        tOrderState.setUpdateTime(returnTime);
        orderStateMapper.insert(tOrderState);
    }

    /**
     * 修改失败支付子表
     * @param id 支付子表ID
     * @param errorMsg 错误信息
     * @param errorCode 错误码
     */
    public void updateFailOrderPayDetail(Integer id, String errorMsg, String errorCode, Date returnTime) {
        TOrderPayDetail payDetail = new TOrderPayDetail();
        payDetail.setId(id);
        payDetail.setReturnTime(returnTime);
        payDetail.setTradeStatus(DictEnum.TRADE_FAILED.code);
        payDetail.setErrorMsg(errorMsg);
        payDetail.setErrorCode(errorCode);
        hxOrderPayDetailMapper.updateByPrimaryKeySelective(payDetail);
    }

    /**
     * 判断打包单所有回调是否完成
     * @param dto
     * @param tOrderPackInfo
     * @return
     */
    public boolean packCallbackFininshed(THxOrderPayInfoDTO dto, TOrderPackInfo tOrderPackInfo) {
        List<TOrderPayDetail> payDetailList = hxOrderPayDetailMapper.selectOrderPayDetailByPackId(tOrderPackInfo.getId());
        int size = 1;
        // 召回
        if (TradeType.ZH.code.equals(dto.getOperateState())) {
            size = 2;
        } else {
            // 入账
            if (DictEnum.MANAGERPATTERN.code.equals(dto.getCapitalTransferPattern())) {
                if (DictEnum.PAYTOCAPTAIN.code.equals(dto.getCapitalTransferType())) {
                    if (dto.getEndDriverWalletId().equals(dto.getCaptainWalletId())) {
                        size = 3;
                    } else {
                        size = 4;
                    }
                }
                if (DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())) {
                    size = 3;
                }
            } else if (DictEnum.COMMONPATTERN.code.equals(dto.getCapitalTransferPattern())) {
                if (DictEnum.PAYTOCAPTAIN.code.equals(dto.getCapitalTransferType())) {
                    if (dto.getEndDriverWalletId().equals(dto.getCaptainWalletId())) {
                        size = 2;
                    } else {
                        size = 3;
                    }
                }
                if (DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())) {
                    size = 2;
                }
            }
        }

        log.info("打包单数量【{}】 * 支付次数【{}】 = , {}", tOrderPackInfo.getTotalSelectedOrders(), size, tOrderPackInfo.getTotalSelectedOrders() * size);
        log.info("打包单数量, 【{}】", payDetailList.size());
        if (tOrderPackInfo.getTotalSelectedOrders() * size == payDetailList.size()) {
            return true;
        }
        return false;
    }

    /**
     * @description 修改节点支付规则状态
     * <AUTHOR>
     * @date 2021/8/23 10:22
     */
    public void updateOrderPayRule(String orderCode, String payNodeType, String payStatus, Date returnTime) {
        TOrderPayRule tOrderPayRule = orderPayRuleMapper.selectByOrderCodeAndPayNodeType(orderCode, payNodeType);
        tOrderPayRule.setPayStatus(payStatus);
        tOrderPayRule.setUpdateTime(returnTime);
        orderPayRuleMapper.updateByPrimaryKeySelective(tOrderPayRule);
    }

    /**
    * @description 节点支付回调失败，修改支付规则表
    * <AUTHOR>
    * @date 2021/8/27 09:49
    */
    public void failCallbackUpdateOrderPayRule(String orderCode, String payNodeType, BigDecimal surplusPaymentFee, Date returnTime) {
        TOrderPayRule tOrderPayRule = orderPayRuleMapper.selectByOrderCodeAndPayNodeType(orderCode, payNodeType);
        tOrderPayRule.setSurplusPaymentFee(surplusPaymentFee);
        tOrderPayRule.setPayStatus(DictEnum.PACKORDERPAIDERROR.code);
        tOrderPayRule.setUpdateTime(returnTime);
        orderPayRuleMapper.updateByPrimaryKeySelective(tOrderPayRule);
    }

    /**
     * @description 修改下一支付节点规则数据
     * <AUTHOR>
     * @date 2021/8/24 16:02
     */
    public void updateNextNodePay(THxOrderPayInfoDTO dto, String nextNodePayType) {
        BigDecimal selectSumFee = orderPayRuleMapper.selectSumFee(dto.getOrderCode());
        log.info("节点支付，已支付金额，{}", selectSumFee);
        if (StringUtils.isNotBlank(nextNodePayType)) {
            // 获取下一支付节点规则，设置剩余运费，修改支付状态
            TOrderPayRule nextOrderPayRule = orderPayRuleMapper.selectByOrderCodeAndPayNodeType(dto.getOrderCode(), nextNodePayType);
            if (null != nextOrderPayRule) {
                TOrderInfo orderInfo = hxOrderInfoMapper.selectOrderByCode(dto.getOrderCode());
                nextOrderPayRule.setPayStatus(DictEnum.PACKUNPAID.code);
                if (null != orderInfo.getUserConfirmPaymentAmount() && orderInfo.getUserConfirmPaymentAmount().compareTo(BigDecimal.ZERO) > 0) {
                    nextOrderPayRule.setSurplusPaymentFee(orderInfo.getUserConfirmPaymentAmount().subtract(selectSumFee));
                } else {
                    nextOrderPayRule.setSurplusPaymentFee(orderInfo.getEstimateTotalFee().subtract(selectSumFee));
                }
                orderPayRuleMapper.updateByPrimaryKeySelective(nextOrderPayRule);
            }
        }
    }

    /**
    * @description 节点支付 - 尾款支付回调处理
    * <AUTHOR>
    * @date 2021/8/24 16:04
    */
    public void handleNodePayWKCallback(THxOrderPayInfoDTO dto, Date returnTime) {
        // 修改运单主表
        nodeCallbackUpdateOrderInfo(dto, returnTime);
        // 修改支付主表
        updateOrderPayInfo(dto.getOrderPayId(), DictEnum.M090.code, returnTime);
        // 插入运单执行状态
        insertNodeOrderState(dto.getOrderCode(), DictEnum.S1002.code,
                DateUtils.addSeconds(returnTime, 1));
        // 账期付
        if (DictEnum.BILLPAY.code.equals(dto.getFeeSettlementWay())) {
            // 恢复账期额度
            unfreezeCompanyProject(dto.getCompanyProjectId(), dto.getTotalFee());
        }
    }

    /**
    * @description 获取运单执行状态，下一节点支付类型
    * <AUTHOR>
    * @date 2021/8/24 16:37
    */
    public String getNextNodeState(String orderCode, String userOper) {
        List<String> allNode = orderPayRuleMapper.selectAllNode(orderCode);
        String nextNodePayType = "";
        for (int i = 0; i < allNode.size(); i++) {
            if (userOper.equals(allNode.get(i))) {
                String nextUserOper = allNode.get(i + 1);
                if (DictEnum.XHPAYNODE.code.equals(nextUserOper)) {
                    nextNodePayType = DictEnum.XHPAYNODE.code;
                } else if (DictEnum.SDPAYNODE.code.equals(nextUserOper)) {
                    nextNodePayType = DictEnum.SDPAYNODE.code;
                } else if (DictEnum.WKPAYNODE.code.equals(nextUserOper)) {
                    nextNodePayType = DictEnum.WKPAYNODE.code;
                }
            }
        }
        return nextNodePayType;
    }

    /**
     * 节点支付回调，获取运单执行状态
     * @param userOper
     * @return
     */
    public String getNodeOrderState(String userOper) {
        String orderState = DictEnum.S0902.code;
        if (DictEnum.ZHPAYNODE.code.equals(userOper)) {
            orderState = DictEnum.S0904.code;
        } else if (DictEnum.XHPAYNODE.code.equals(userOper)) {
            orderState = DictEnum.S0905.code;
        } else if (DictEnum.SDPAYNODE.code.equals(userOper)) {
            orderState = DictEnum.S0906.code;
        }
        return orderState;
    }

    /**
    * @description 保存电子回单
    * <AUTHOR>
    * @date 2021/8/27 14:47
    */
    public void saveReceipt(String partnerAccId, String bizOrderNo, String tradeType, Integer walletLogId) {
        try {
            // 两种方式，选择其一
            // 直接发起请求获取电子回单
            //doReceiptApply(partnerAccId, bizOrderNo, tradeType, walletLogId);
            // 将申请电子回单请求写入定时任务
            saveReceiptApplyTask(partnerAccId, bizOrderNo, tradeType, walletLogId);
        } catch (Exception e) {
            log.error("华夏电子回单请求写入任务表失败, {}, {}, {}, {}", partnerAccId, bizOrderNo, tradeType, walletLogId);
        }

    }

    /**
     * 直接发起请求获取电子回单
     * @param partnerAccId
     * @param bizOrderNo
     * @param tradeType
     * @param walletLogId
     */
    private void doReceiptApply(String partnerAccId, String bizOrderNo, String tradeType, Integer walletLogId) {
        try {
            CustomerReceiptResponse receipt = doGetReceipt(partnerAccId, bizOrderNo, tradeType);
            TZtWalletChangeLog log = new TZtWalletChangeLog();
            log.setId(walletLogId);
            //log.setFileUrl(null != receipt.getFileUrl() ? receipt.getFileUrl() : "");
            //log.setReceiptNo(null != receipt.getReceiptNo() ? receipt.getReceiptNo() : "");
            hxOrderWalletChangeLogMapper.updateByPrimaryKeySelective(log);
        } catch (Exception e) {
            TZtWalletChangeLog log = new TZtWalletChangeLog();
            log.setId(walletLogId);
            log.setParam2(JSONUtil.toJsonStr(e.getMessage()));
            hxOrderWalletChangeLogMapper.updateByPrimaryKeySelective(log);
        }
    }

    /**
     * @description 获取电子回单
     * <AUTHOR>
     * @date 2021/8/27 14:22
     */
    public CustomerReceiptResponse doGetReceipt(String partnerAccId, String bizOrderNo, String tradeType) {
        CustomerReceiptReq req = new CustomerReceiptReq();
        req.setPartnerId(hxPropertiesConfig.getPartnerId());
        req.setChannelId(hxPropertiesConfig.getChannelId());
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setPartnerAccId(partnerAccId);
        req.setBizOrderNo(bizOrderNo);
        req.setTradeType(tradeType);
        log.info("获取电子回单信息, {}", JSONUtil.toJsonStr(req));
        ResultUtil resultUtil = cloudPaymentAPI.execute(req);
        CustomerReceiptResponse response = JSONUtil.toBean(JSONUtil.parseObj(resultUtil.getData()), CustomerReceiptResponse.class);
        if (JdEnum.REQUEST_SUCCESS.code.equals(response.getResponseCode())) {
            return response;
        } else {
            throw new RuntimeException(JSONUtil.toJsonStr(response));
        }
    }

    /**
     * 华夏电子回单请求写入任务表
     * @param partnerAccId
     * @param bizOrderNo
     * @param tradeType
     * @param walletLogId
     */
    public void saveReceiptApplyTask(String partnerAccId, String bizOrderNo, String tradeType, Integer walletLogId) {
        CustomerReceiptReq req = new CustomerReceiptReq();
        req.setPartnerId(hxPropertiesConfig.getPartnerId());
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setChannelId(hxPropertiesConfig.getChannelId());
        req.setPartnerAccId(partnerAccId);
        req.setBizOrderNo(bizOrderNo);
        req.setTradeType(tradeType);
        TTask tTask = new TTask();
        tTask.setTaskId(IdWorkerUtil.getInstance().nextId());
        tTask.setTaskType(DictEnum.HXRECEIPT.code);
        tTask.setTaskTypeNode(TradeType.FQ.code);
        tTask.setBusinessType(TradeType.ZF.code);
        tTask.setSourceTablename("t_zt_wallet_log");
        tTask.setSourcekeyFieldname("id");
        tTask.setSourceFieldname("id");
        tTask.setSourceFieldvalue(bizOrderNo);
        tTask.setRequestParameter(JSONUtil.toJsonStr(req));
        // 记录钱包流水ID
        tTask.setParam1(String.valueOf(walletLogId));
        tTask.setRequestTimes(0);
        tTask.setCreateTime(new Date());
        tTask.setRequestDate(new Date());
        tTask.setIsSuccessed(false);
        tTask.setEnable(false);
        orderTaskMapper.insert(tTask);
    }

    /**
    * @description 发起结算通知
    * <AUTHOR>
    * @date 2021/8/27 16:12
    */
    public void sendRZMessage(TOrderInfo tOrderInfo, THxOrderPayInfoDTO dto) {
        SysParam paramByKey = sysParamAPI.getParamByKey("WXTransition");
        if (null != paramByKey) {
            if (null != paramByKey.getParamValue() && paramByKey.getParamValue().equals("1")) {
                // 发送短信结算通知
                sendSMSJSMessage(tOrderInfo);
            } else {
                Integer endUserId = 0;
                if (dto.getCapitalTransferType().equals(DictEnum.PAYTODRIVER.code)) {
                    endUserId = tOrderInfo.getEndDriverId();
                } else if (dto.getCapitalTransferType().equals(DictEnum.PAYTOCAPTAIN.code)) {
                    endUserId = tOrderInfo.getEndCarOwnerId();
                }
                // 发送微信结算通知
                SendWXJSMessage(endUserId, tOrderInfo);
            }
        }
    }

    /**
    * @description 发送短信结算通知
    * <AUTHOR>
    * @date 2021/8/27 16:10
    */
    private void sendSMSJSMessage(TOrderInfo tOrderInfo) {
        if (null != tOrderInfo.getParam3() && StringUtils.isNotEmpty(tOrderInfo.getParam3())) {

            String bankId = tOrderInfo.getParam3();
            TZtBankCardDTO ztBankCardDTO = hxPayBankCardMapper.selectOpenRoleBankCardInfo(Integer.valueOf(bankId));
            if (null != ztBankCardDTO && null != ztBankCardDTO.getCardOwnerPhone()) {
                try {
                    SmsReq smsReq = new SmsReq();
                    smsReq.setMobiles(ztBankCardDTO.getCardOwnerPhone());
                    smsReq.setOrderBusinessCode(tOrderInfo.getOrderBusinessCode());
                    smsReq.setType("JIESUAN");
                    smsAPI.sendSmsType(smsReq);
                } catch (Exception e) {
                    log.error("入账回调发送短信失败", e);
                }
            } else {
                log.error("手机号为空");
            }
        }
    }

    /**
    * @description 发送微信结算通知
    * <AUTHOR>
    * @date 2021/8/27 16:10
    */
    private void SendWXJSMessage(Integer endUserId, TOrderInfo orderInfo) {
        //发送微信通知
        try {
            log.info("发起微信通知");
            // 查询C端openId
            ResultUtil resultUtil = accountService.queryOpenIdByEnduserId(endUserId);
            TAccountDTO tAccountDTO = JSONUtil.toBean(JSONUtil.parseObj(resultUtil.getData()), TAccountDTO.class);
            SysParam sysParam = sysParamAPI.getParamByKey("WXMessageUrl");
            if (sysParam != null) {
                BigDecimal deduction = BigDecimal.ZERO;
                TOrderInfoDetail tOrderInfoDetail = orderInfoDetailMapper.selectByOrderId(orderInfo.getId());
                if (null != tOrderInfoDetail && tOrderInfoDetail.getIllegalOrder() && null != tOrderInfoDetail.getDeduction() && tOrderInfoDetail.getDeduction().compareTo(BigDecimal.ZERO) > 0) {
                    deduction = tOrderInfoDetail.getDeduction();
                }
                // 保险费
                TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(orderInfo.getOrderBusinessCode());
                if (OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance)) {
                    deduction = deduction.add(tOrderInsurance.getInsuredAmount());
                }
                BigDecimal amount = orderInfo.getUserConfirmPaymentAmount().subtract(orderInfo.getUserConfirmServiceFee()).subtract(deduction);
                WxMsgSendUtil.sendMsgJS(orderInfo.getOrderBusinessCode(), orderInfo.getDeliverOrderTime(),
                        amount, tAccountDTO.getOpenId(),
                        sysParam.getParamValue(), tAccountDTO.getRealName());
                log.info("发起微信通知完成");
            }
        } catch (Exception e) {
            log.error("发起微信通知失败", e);
        }
    }

    /**
      * @Description 发送实体运单召回通知
      * <AUTHOR>
      * @Date 2021/10/11 14:00
      *
    **/
    public void sendZHMessage(TOrderInfo orderInfo,  THxOrderPayInfoDTO dto) {
        Integer endUserId = 0;
        if (dto.getCapitalTransferType().equals(DictEnum.PAYTODRIVER.code)) {
            endUserId = orderInfo.getEndDriverId();
        } else if (dto.getCapitalTransferType().equals(DictEnum.PAYTOCAPTAIN.code)) {
            endUserId = orderInfo.getEndCarOwnerId();
        }
        //发送微信通知
        SearchAccountVO accountVO = new SearchAccountVO();
        ArrayList<Integer> enduserIds = new ArrayList<>();
        enduserIds.add(endUserId);
        accountVO.setEnduserIds(enduserIds);
        try{
            log.info("发送实体运单召回微信通知");
            ResultUtil resultUtil = accountService.selectOpenIdByEnduserId(accountVO);
            if (null != resultUtil){
                ArrayList data = (ArrayList) resultUtil.getData();
                LinkedHashMap accountDTO = (LinkedHashMap) data.get(0);
                Object openId = accountDTO.get("openId");
                Object realName = accountDTO.get("realName");
                if (openId !=null && StringUtils.isNotEmpty(openId.toString())){
                    SysParam sysParam=sysParamAPI.getParamByKey("WXMessageUrl");
                    if(sysParam != null){
                        BigDecimal amount = orderInfo.getUserConfirmPaymentAmount().subtract(orderInfo.getUserConfirmServiceFee());
                        WxMsgSendUtil.sendMsgZH(orderInfo.getOrderBusinessCode(), orderInfo.getDeliverOrderTime(),
                                amount, openId.toString(), sysParam.getParamValue(), String.valueOf(realName));
                        log.info("发送实体运单召回微信通知完成");
                    }
                }
            }
        } catch (Exception e){
            log.error("发送实体运单召回微信通知失败", e);
        }
    }

    /**
     * @Description 发送打包运单召回通知
     * <AUTHOR>
     * @Date 2021/10/11 14:00
     *
     **/
    public void sendPackZHMessage(TOrderPackInfo orderPackInfo) {
        //发送微信通知
        SearchAccountVO accountVO = new SearchAccountVO();
        ArrayList<Integer> enduserIds = new ArrayList<>();
        enduserIds.add(orderPackInfo.getEndUserId());
        accountVO.setEnduserIds(enduserIds);
        try {
            log.info("发起打包运单召回微信通知");
            ResultUtil resultUtil = accountService.selectOpenIdByEnduserId(accountVO);
            if (null != resultUtil) {
                ArrayList data = (ArrayList) resultUtil.getData();
                LinkedHashMap accountDTO = (LinkedHashMap) data.get(0);
                Object openId = accountDTO.get("openId");
                Object realName = accountDTO.get("realName");
                if (openId != null && StringUtils.isNotEmpty(openId.toString())) {
                    SysParam sysParam = sysParamAPI.getParamByKey("WXMessageUrl");
                    if (sysParam != null) {
                        BigDecimal amount = orderPackInfo.getAppointmentPaymentCash().subtract(orderPackInfo.getTotalSelectedOrdersServiceFee());
                        // 保险费
                        BigDecimal insuranceAmount = orderPackInfoMapper.calculateInsuranceAmount(orderPackInfo.getCode());
                        if (null != insuranceAmount) {
                            amount = amount.subtract(insuranceAmount);
                        }
                        WxMsgSendUtil.sendMsgZH(orderPackInfo.getVirtualOrderNo(), new Date(Long.valueOf(orderPackInfo.getParam1())),
                                amount, openId.toString(), sysParam.getParamValue(), String.valueOf(realName));
                        log.info("发起打包运单召回微信通知完成");
                    }
                }
            }
        } catch (Exception e) {
            log.error("发送打包运单召回微信通知失败", e);
        }
    }

    public void sendDBRZMessage(TOrderPackInfo orderPackInfo) {
        SysParam paramByKey = sysParamAPI.getParamByKey("WXTransition");
        if (null != paramByKey) {
            if (null != paramByKey.getParamValue() && paramByKey.getParamValue().equals("1")) {
                // 发送短信结算通知
                sendDBSMSJSMEssage(orderPackInfo);
            } else {
                // 发送微信结算通知
                sendDBWXJSMessage(orderPackInfo);
            }
        }
    }

    private void sendDBSMSJSMEssage(TOrderPackInfo orderPackInfo) {
        TZtBankCardDTO tZtBankCardDTO = hxPayBankCardMapper.selectByEnduserIdAndBankCardId(orderPackInfo.getEndUserId(), orderPackInfo.getBankCardId());
        if (null != tZtBankCardDTO && null != tZtBankCardDTO.getCardOwner() && null != tZtBankCardDTO.getCardOwnerPhone()) {
            try {
                SmsReq smsReq = new SmsReq();
                smsReq.setMobiles(tZtBankCardDTO.getCardOwnerPhone());
                smsReq.setOrderBusinessCode(orderPackInfo.getVirtualOrderNo());
                smsReq.setType("JIESUAN");
                smsAPI.sendSmsType(smsReq);
            } catch (Exception e) {
                log.error("打包运单入账回调发送短信失败", e);
            }
        }
    }

    private void sendDBWXJSMessage(TOrderPackInfo orderPackInfo) {
        //发送打包运单结算微信通知
        try {
            log.info("发送打包运单结算微信通知");
            // 查询C端openId
            ResultUtil resultUtil = accountService.queryOpenIdByEnduserId(orderPackInfo.getEndUserId());
            TAccountDTO tAccountDTO = JSONUtil.toBean(JSONUtil.parseObj(resultUtil.getData()), TAccountDTO.class);
            SysParam sysParam = sysParamAPI.getParamByKey("WXMessageUrl");
            if (sysParam != null) {
                BigDecimal amount = orderPackInfo.getAppointmentPaymentCash().subtract(orderPackInfo.getTotalSelectedOrdersServiceFee());
                // 保险费
                BigDecimal insuranceAmount = orderPackInfoMapper.calculateInsuranceAmount(orderPackInfo.getCode());
                if (null != insuranceAmount) {
                    amount = amount.subtract(insuranceAmount);
                }
                WxMsgSendUtil.sendMsgJS(orderPackInfo.getVirtualOrderNo(), orderPackInfo.getCreateTime(),
                        amount, tAccountDTO.getOpenId(), sysParam.getParamValue(), tAccountDTO.getRealName());
                log.info("发送打包运单结算微信通知完成");
            }
        } catch (Exception e) {
            log.error("发送打包运单结算微信通知", e);
        }
    }

    /**
     * @description 发送微信提现通知
     * <AUTHOR>
     * @date 2021/8/27 16:10
     */
    public void sendWXTXMessage(AsyncNotifyVirtualMemberWithdrawMessageBody messageBody,
                                 Integer endUserId, TTxOrderPayDetailDTO dto, Boolean isSucceed) {
        //发送微信通知
        try {
            log.info("发起微信通知");
            // 查询C端openId
            ResultUtil resultUtil = accountService.queryOpenIdByEnduserId(endUserId);
            TAccountDTO tAccountDTO = JSONUtil.toBean(JSONUtil.parseObj(resultUtil.getData()), TAccountDTO.class);
            SysParam sysParam = sysParamAPI.getParamByKey("WXMessageUrl");
            if (sysParam != null) {
                BigDecimal amount = dto.getOrderTotalPayment();
                if (isSucceed) {
                    WxMsgSendUtil.sendMsgJDTx(messageBody.getBankOrderNo(), dto.getBankNo(), dto.getCreateTime(), amount,
                            tAccountDTO.getOpenId(), tAccountDTO.getRealName(), sysParam.getParamValue());
                } else {
                    WxMsgSendUtil.sendMsgJDTxError(messageBody.getBankOrderNo(), dto.getCreateTime(), amount,
                            tAccountDTO.getOpenId(), tAccountDTO.getRealName(), sysParam.getParamValue(),
                            messageBody.getResponseDesc());
                }
                log.info("发起微信通知完成");
            }
        } catch (Exception e) {
            log.error("发起微信通知失败", e);
        }
    }

    /**
    * @description 华夏预支付钱包流水
    * <AUTHOR>
    * @date 2021/11/8 17:28
    */
    public Integer insertPrePayWalletLog(AsyncNotifyVirtualBalancePayMessageBody messageBody, THxAdvanceOrderPayTempInfoDTO dto) {
        BigDecimal totalFee = dto.getCarriageFee().add(dto.getDispatchFee());
        if (HXTradeTypeEnum.HX_COMBALANCE_PREPAY.code.equals(dto.getTradeType())) {
            // 企业预支付
            TZtWallet outJdWallet = hxWalletUtil.selectByCarrierCompanyPartnerAccId(messageBody.getOutPartnerAccId(), DictEnum.BD.code);
            TZtWallet inJdWallet = hxWalletUtil.selectByCarrierCompanyPartnerAccId(messageBody.getInPartnerAccId(), DictEnum.CA.code);
            Integer walletLogId = createWalletLog(messageBody, outJdWallet.getId(), inJdWallet.getId(), totalFee, outJdWallet.getPurseCategory(), dto.getOrderBusinessCode(), messageBody.getBizOrderNo(), HXTradeTypeEnum.HX_CYKFJDZHICHU.code, dto.getCreateTime());
            createWalletLog(messageBody, inJdWallet.getId(), outJdWallet.getId(), totalFee, inJdWallet.getPurseCategory(), dto.getOrderBusinessCode(), messageBody.getBizOrderNo(), HXTradeTypeEnum.HX_CAYFKJDSHOURU.code, dto.getCreateTime());
            return walletLogId;
        } else if (HXTradeTypeEnum.HX_CABALANCE_PREPAY.code.equals(dto.getTradeType())) {
            // 承运方预支付
            TZtWallet outJdWallet = hxWalletUtil.selectByCarrierCompanyPartnerAccId(messageBody.getOutPartnerAccId(), DictEnum.CA.code);
            TZtWallet inJdWallet = hxWalletUtil.selectByEnduserPartnerAccId(messageBody.getInPartnerAccId());
            Integer walletLogId = createWalletLog(messageBody, outJdWallet.getId(), inJdWallet.getId(), dto.getCarriageFee(), outJdWallet.getPurseCategory(), dto.getOrderBusinessCode(), messageBody.getBizOrderNo(), HXTradeTypeEnum.HX_CAYFKJDZHICHU.code, dto.getCreateTime());
            createWalletLog(messageBody, inJdWallet.getId(), outJdWallet.getId(), dto.getCarriageFee(), inJdWallet.getPurseCategory(), dto.getOrderBusinessCode(), messageBody.getBizOrderNo(), HXTradeTypeEnum.HX_CDYFKJDSHOURU.code, dto.getCreateTime());
            return walletLogId;
        }
        return null;
    }

    /**
    * @description 华夏预支付尾款流水
    * <AUTHOR>
    * @date 2021/11/10 08:59
    */
    public Integer insertWkPayWalletLog(AsyncNotifyVirtualBalancePayMessageBody messageBody, THxOrderPayInfoDTO dto) {
        BigDecimal totalFee = dto.getCarriageFee().add(dto.getDispatchFee());
        if (HXTradeTypeEnum.HX_COMBALANCE_WKPAY.code.equals(dto.getTradeType())) {
            // 企业尾款支付
            TZtWallet outJdWallet = hxWalletUtil.selectByCarrierCompanyPartnerAccId(messageBody.getOutPartnerAccId(), DictEnum.BD.code);
            TZtWallet inJdWallet = hxWalletUtil.selectByCarrierCompanyPartnerAccId(messageBody.getInPartnerAccId(), DictEnum.CA.code);
            Integer walletLogId = createWalletLog(messageBody, outJdWallet.getId(), inJdWallet.getId(), totalFee, outJdWallet.getPurseCategory(), dto.getOrderBusinessCode(), messageBody.getBizOrderNo(), HXTradeTypeEnum.HX_CYKFJDZHICHU.code, dto.getCreateTime());
            createWalletLog(messageBody, inJdWallet.getId(), outJdWallet.getId(), totalFee, inJdWallet.getPurseCategory(), dto.getOrderBusinessCode(), messageBody.getBizOrderNo(), HXTradeTypeEnum.HX_CAYFKJDSHOURU.code, dto.getCreateTime());
            return walletLogId;
        } else if (HXTradeTypeEnum.HX_CABALANCE_WKEPAY.code.equals(dto.getTradeType())) {
            // 承运方尾款支付
            TZtWallet outJdWallet = hxWalletUtil.selectByCarrierCompanyPartnerAccId(messageBody.getOutPartnerAccId(), DictEnum.CA.code);
            TZtWallet inJdWallet = hxWalletUtil.selectByEnduserPartnerAccId(messageBody.getInPartnerAccId());
            Integer walletLogId = createWalletLog(messageBody, outJdWallet.getId(), inJdWallet.getId(), dto.getCarriageFee(), outJdWallet.getPurseCategory(), dto.getOrderBusinessCode(), messageBody.getBizOrderNo(), HXTradeTypeEnum.HX_CAYFKJDZHICHU.code, dto.getCreateTime());
            createWalletLog(messageBody, inJdWallet.getId(), outJdWallet.getId(), dto.getCarriageFee(), inJdWallet.getPurseCategory(), dto.getOrderBusinessCode(), messageBody.getBizOrderNo(), HXTradeTypeEnum.HX_CDYFKJDSHOURU.code, dto.getCreateTime());
            return walletLogId;
        }
        return null;
    }

    /**
    * @description 查询是否有收取账户服务费失败记录，如果有则返回需要收取的金额
    * <AUTHOR>
    * @date 2021/12/3 10:18
    */
    public AccountServiceFailedRecordVO queryAccountServiceFee(Integer openRoleId, String openType, String serviceFeeType) {
        AccountServiceFailedRecordVO vo = new AccountServiceFailedRecordVO();
        try {
            List<Integer> recordIds = new ArrayList<>();
            vo.setRecordIds(recordIds);
            vo.setAccountServiceFee(BigDecimal.ZERO);
            List<TServicefeeRecord> records = orderServicefeeRecordMapper.selectEnduserAccountServiceFeeFailedRecords(openRoleId, openType, serviceFeeType);
            if (null != records && !records.isEmpty()) {
                AtomicReference<BigDecimal> accountServiceFee = new AtomicReference<>(BigDecimal.ZERO);
                AtomicReference<String> accountServiceFeeType = new AtomicReference<>();
                records.forEach((record) -> {
                    if (null != record.getServicefeeAmount() && record.getServicefeeAmount().compareTo(BigDecimal.ZERO) > 0) {
                        accountServiceFee.set(accountServiceFee.get().add(record.getServicefeeAmount()));
                        accountServiceFeeType.set(record.getParam1());
                        recordIds.add(record.getId());
                    }
                });
                vo.setAccountServiceFee(accountServiceFee.get());
                vo.setAccountServiceFeeType(accountServiceFeeType.get());
                vo.setRecordIds(recordIds);
                return vo;
            }
        } catch (Exception e) {
            log.error("查询账户服务费收取失败, {}", JSONUtil.toJsonStr(e));
        }
        return vo;
    }

    /**
     * @description 查询入款方是否存在缴纳账户服务费失败的记录，如果存在，则发起补交账户服务费申请
     * <AUTHOR>
     * @date 2021/12/4 09:30
     */
    public BigDecimal checkAccountServiceFeeFaildRecord(THxOrderPayInfoDTO dto) {
        Integer openRoleId = null;
        String outPartenrAccId = null;
        Integer walletId = null;
        BigDecimal accountServiceFee = BigDecimal.ZERO;
        if (DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())) {
            openRoleId = dto.getDriverOpenRoleId();
            outPartenrAccId = dto.getDriverAccId();
            walletId = dto.getEndDriverWalletId();
        } else if (DictEnum.PAYTOCAPTAIN.code.equals(dto.getCapitalTransferType())) {
            openRoleId = dto.getCaptainOpenRoleId();
            outPartenrAccId = dto.getCaptainAccId();
            walletId = dto.getCaptainWalletId();
        }
        BigDecimal carriageFee = dto.getCarriageFee().subtract(dto.getServiceFee());
        AccountServiceFailedRecordVO accountServiceFailedRecordVO = queryAccountServiceFee(openRoleId, DictEnum.CD.code, HXTradeTypeEnum.HX_ACCOUNTSERVICE.code);
        if (!accountServiceFailedRecordVO.getRecordIds().isEmpty() && carriageFee.compareTo(accountServiceFailedRecordVO.getAccountServiceFee()) >= 0) {
            // 发起账户服务费支付
            sendAccountServiceFeeApply(outPartenrAccId, walletId, accountServiceFailedRecordVO);
            accountServiceFee = accountServiceFailedRecordVO.getAccountServiceFee();
        }
        return accountServiceFee;
    }

    /**
     * @description 发起账户服务费支付
     * <AUTHOR>
     * @date 2021/12/4 22:46
     */
    public void sendAccountServiceFeeApply(String partnerAccId, Integer walletId, AccountServiceFailedRecordVO accountServiceFailedRecordVO) {
        // 账户服务费
        BigDecimal accountServiceFee = accountServiceFailedRecordVO.getAccountServiceFee();
        // 补交账户服务费
        // 查询平台会员编号
        SysParam platformAccId = sysParamAPI.getParamByKey(DictEnum.HXPLATFORMACCID.code);
        SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.HXACCOUNTSERVICEFEEPAY.code);
        // 创建支付主表
        String orderPayInfoCode = createOrderPayInfo(accountServiceFailedRecordVO.getAccountServiceFee(), DictEnum.P070.code);
        // 创建支付子表
        String orderPayDetailCode = createOrderPayDetail(orderPayInfoCode, null, HXTradeTypeEnum.HX_ACCACCOUNTSERVICEREOUNTSERVICE.code, TradeType.RZ.code, walletId);
        // 修改钱包
        hxWalletUtil.accountServicePayApplyModifyWallet(walletId, accountServiceFailedRecordVO.getAccountServiceFee());

        HxAccountServiceFeeRequest req = new HxAccountServiceFeeRequest();
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setBizOrderNo(orderPayDetailCode);
        req.setPartnerId(hxPropertiesConfig.getPartnerId());
        req.setChannelId(hxPropertiesConfig.getChannelId());
        req.setOutPartnerAccId(partnerAccId);
        req.setInPartnerAccId(platformAccId.getParamValue());
        req.setOrderAmount(accountServiceFee);
        req.setTradeAbstract(DictEnum.ACCOUNTSERVICE.code);
        req.setNotifyUrl(paramByKey.getParamValue());
        req.setRecordIds(accountServiceFailedRecordVO.getRecordIds());
        req.setAccountServiceFeeType(accountServiceFailedRecordVO.getAccountServiceFeeType());

        MQMessage mqMessage = new MQMessage();
        mqMessage.setTopic(HXMqMessageTopic.HX_ACCOUNTSERVICEFEE);
        mqMessage.setTag(HXMqMessageTag.HX_ACCOUNTSERVICEFEERETROACTIVE);
        mqMessage.setBody(req);
        mqMessage.setKey(orderPayDetailCode);
        ResultUtil resultUtil = mqAPI.sendMessage(mqMessage);
        log.info("发送补交账户服务费MQ消息结果，{}", JSONUtil.toJsonStr(resultUtil));
        if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
            log.info("发送补交账户服务费MQ消息失败，{}", JSONUtil.toJsonStr(resultUtil));
            throw new RuntimeException("发送补交账户服务费MQ消息失败");
        } else {
            // 修改账户服务费失败记录，为处理中，防止再次收取。
            orderServicefeeRecordMapper.batchUpdateRecordStatus(accountServiceFailedRecordVO.getRecordIds(), -1);
        }
    }

    /**
     * @description 记录收取账户服务费钱包流水
     * <AUTHOR>
     * @date 2021/12/4 09:21
     */
    public void insertAccountServiceFeeWalletLog(TZtWallet outWallet, TZtWallet inWallet, String serviceFeeType, THxOrderPayInfoDTO dto, AsyncNotifyVirtualBalancePayMessageBody messageBody) {
        String serviceFeeTypeZC = "";
        String serviceFeeTypeSR = "";
        if (DictEnum.MYACCOUNT.code.equals(serviceFeeType)) {
            serviceFeeTypeZC = HXTradeTypeEnum.HX_MYACCOUNT_ACCOUNTSERVICE_ZC.code;
            serviceFeeTypeSR = HXTradeTypeEnum.HX_MYACCOUNT_ACCOUNTSERVICE_SR.code;
        }
        // 记录出款方钱包流水
        createWalletLog(messageBody, outWallet.getId(), inWallet.getId(), dto.getOrderTotalPayment(), outWallet.getPurseCategory(), null, messageBody.getBankOrderNo(), serviceFeeTypeZC, dto.getCreateTime());
        // 记录入款方钱包流水
        createWalletLog(messageBody, inWallet.getId(), outWallet.getId(), dto.getOrderTotalPayment(), inWallet.getPurseCategory(), null, messageBody.getBankOrderNo(), serviceFeeTypeSR, dto.getCreateTime());
    }

    public Integer insertCarrierBalanceCompanyChargeWalletLog(AsyncNotifyVirtualBalancePayMessageBody messageBody, THxOrderPayInfoDTO dto) {
        TZtWallet outWallet = hxWalletUtil.selectByCarrierCompanyPartnerAccId(messageBody.getOutPartnerAccId(), DictEnum.CA.code);
        TZtWallet inWallet = hxWalletUtil.selectByCarrierCompanyPartnerAccId(messageBody.getInPartnerAccId(), DictEnum.BD.code);
        // 记录出款方钱包流水
        Integer walletLogId = createWalletLog(messageBody, outWallet.getId(), inWallet.getId(), dto.getOrderTotalPayment(), outWallet.getPurseCategory(), null, messageBody.getBizOrderNo(), HXTradeTypeEnum.HX_CARRIERBALANCECOMPANYCHARGE_ZC.code, dto.getCreateTime());
        // 记录入款方钱包流水
        createWalletLog(messageBody, inWallet.getId(), outWallet.getId(), dto.getOrderTotalPayment(), inWallet.getPurseCategory(), null, messageBody.getBizOrderNo(), DictEnum.BCHONGZHI.code, dto.getCreateTime());
        return walletLogId;
    }

    /**
     * @description 更新保险状态
     * @param dto
     */
    public void updateInsuranceStatus(THxOrderPayInfoDTO dto, Integer payStatus) {
        TOrderInsurance orderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(dto.getOrderBusinessCode());
        log.info("更新保险状态：{}", JSONUtil.toJsonStr(orderInsurance));
        TOrderInsurance tOrderInsurance = new TOrderInsurance();
        tOrderInsurance.setId(orderInsurance.getId());
        tOrderInsurance.setPayStatus(payStatus);
        orderInsuranceMapper.updateByPrimaryKeySelective(tOrderInsurance);
    }

}
