package com.lz.payment.hxyh.hxstrategy;

import cn.hutool.json.JSONUtil;
import com.lz.api.MqAPI;
import com.lz.common.config.HXPropertiesConfig;
import com.lz.common.constants.HXMqMessageTag;
import com.lz.common.constants.HXMqMessageTopic;
import com.lz.common.dbenum.BusinessCode;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.HXTradeTypeEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.MQMessage;
import com.lz.common.model.hxPayment.request.pay.CustomerBalancePayReq;
import com.lz.common.util.*;
import com.lz.dao.*;
import com.lz.dto.OpenRoleStatusListDTO;
import com.lz.dto.OpenRoleWalletListDTO;
import com.lz.model.*;
import com.lz.payment.TradeType;
import com.lz.payment.hxyh.HXPayOrderUtil;
import com.lz.payment.hxyh.HXPaymentUtil;
import com.lz.payment.hxyh.HXWalletUtil;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.vo.SelectOpenRoleVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 华夏预支付支付实现
 */
@Slf4j
@Service
public class HXPrePaymentStrategy extends HPaymentStrategy {

    @Resource
    private TOrderInfoMapper orderInfoMapper;

    @Resource
    private TOrderCastChangesMapper tOrderCastChangesMapper;

    @Resource
    private TOrderCastCalcSnapshotMapper orderCastCalcSnapshotMapper;

    @Resource
    private TAdvanceOrderTmpMapper advanceOrderTmpMapper;

    @Resource
    private TAdvanceOrderPayTmpMapper advanceOrderPayTmpMapper;

    @Resource
    private TOrderPayInfoMapper orderPayInfoMapper;

    @Resource
    private HXWalletUtil hxWalletUtil;

    @Resource
    private HXPaymentUtil hxPaymentUtil;

    @Resource
    private HXPayOrderUtil hxPayOrderUtil;

    @Resource
    private HXPropertiesConfig hxPropertiesConfig;

    @Resource
    private MqAPI mqAPI;

    @Resource
    private SysParamAPI sysParamAPI;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil toPrePayment(TOrderInfo tOrderInfo, String type) {
        try {
            if (HXTradeTypeEnum.HX_COMBALANCE_PREPAY.code.equals(type)) {
                // 预支付
                prePay(tOrderInfo);
            } else if (HXTradeTypeEnum.HX_COMBALANCE_WKPAY.code.equals(type)) {
                // 尾款支付
                wkPay(tOrderInfo);
            }
        } catch (Exception e) {
            log.error("ZJJ-241:支付失败！", e);
            String message = e.getMessage();
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                throw new RuntimeException(BusinessCode.PROJECTWALLETINSUFFICIENT.code);
            }
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                throw new RuntimeException(BusinessCode.WALLETAMOUNTINSUFFICIENT.code);
            }
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            }
            throw new RuntimeException("ZJJ-241:支付失败！");
        }

        return ResultUtil.ok();
    }

    private void prePay(TOrderInfo tOrderInfo) {
        TOrderCastChanges castChanges = tOrderCastChangesMapper.selectByNewOne(tOrderInfo.getCode());
        TAdvanceOrderTmp advanceOrderTmp = advanceOrderTmpMapper.selectAdvanceOrderTempByOrderCode(tOrderInfo.getCode());
        // 计算调度费
        BigDecimal dispatchFee = OrderMoneyUtil.getDispatchFee(castChanges.getCurrentDispatchRate(), advanceOrderTmp.getAdvanceFee());
        TAdvanceOrderTmp tAdvanceOrderTmp = new TAdvanceOrderTmp();
        tAdvanceOrderTmp.setId(advanceOrderTmp.getId());
        tAdvanceOrderTmp.setAdvanceDispatchFee(dispatchFee);
        tAdvanceOrderTmp.setOrderPayStatus(DictEnum.P070.code);
        tAdvanceOrderTmp.setUpdateTime(new Date());
        advanceOrderTmpMapper.updateByPrimaryKeySelective(tAdvanceOrderTmp);

        TAdvanceOrderPayTmp advanceOrderPayTmp = new TAdvanceOrderPayTmp();
        advanceOrderPayTmp.setCode(IdWorkerUtil.getInstance().nextId());
        advanceOrderPayTmp.setAdvanceCode(advanceOrderTmp.getCode());
        advanceOrderPayTmp.setTradeType(HXTradeTypeEnum.HX_COMBALANCE_PREPAY.code);
        advanceOrderPayTmp.setOperateState(TradeType.RZ.code);
        advanceOrderPayTmp.setOperaterId(CurrentUser.getCurrentUserID().toString());
        advanceOrderPayTmp.setOperateTime(new Date());
        advanceOrderPayTmp.setPaymentPlatforms(DictEnum.HXPLATFORMS.code);
        advanceOrderPayTmp.setCreateUser(CurrentUser.getUserNickname());
        advanceOrderPayTmp.setUpdateUser(CurrentUser.getUserNickname());
        advanceOrderPayTmpMapper.insertSelective(advanceOrderPayTmp);


        // 设置查询参数
        SelectOpenRoleVO openRoleVO = new SelectOpenRoleVO();
        openRoleVO.setCarrierId(tOrderInfo.getCarrierId());
        openRoleVO.setCompanyId(tOrderInfo.getCompanyId());
        openRoleVO.setDriverId(tOrderInfo.getEndDriverId());

        // 查询出款方、入款方会员编号
        ResultUtil openRoleStatus = hxPayOrderUtil.selectOpenRoleStatus(openRoleVO);
        if (DictEnum.ERROR.code.equals(openRoleStatus.getCode())) {
            throw new RuntimeException(openRoleStatus.getMsg());
        }
        String jsonStr = JSONUtil.toJsonStr(openRoleStatus.getData());
        OpenRoleStatusListDTO openRoleStatusListDTO = JSONUtil.toBean(jsonStr, OpenRoleStatusListDTO.class);

        // 查询钱包
        ResultUtil openRoleWallet = hxPayOrderUtil.selectOpenRoleWallet(openRoleVO);
        if (null == openRoleWallet || DictEnum.ERROR.code.equals(openRoleWallet.getCode())) {
            throw new RuntimeException("支付失败，请联系运营平台予以解决。");
        }
        OpenRoleWalletListDTO walletListDTO = JSONUtil.toBean(JSONUtil.toJsonStr(openRoleWallet.getData()), OpenRoleWalletListDTO.class);

        BigDecimal totalFee = advanceOrderTmp.getAdvanceFee().add(dispatchFee);
        //修改企业钱包,将总费用挪到入账中
        hxWalletUtil.modifyWallet(walletListDTO.getCompanyWallet().getId(), totalFee, tOrderInfo.getFeeSettlementWay());

        // 构造支付请求参数
        CustomerBalancePayReq req = new CustomerBalancePayReq();
        req.setPartnerId(hxPropertiesConfig.getPartnerId());
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setChannelId(hxPropertiesConfig.getChannelId());
        req.setBizOrderNo(advanceOrderPayTmp.getCode());
        req.setOutPartnerAccId(openRoleStatusListDTO.getCompanyStatus().getPartnerAccId());
        req.setInPartnerAccId(openRoleStatusListDTO.getCarrierStatus().getPartnerAccId());
        req.setOrderAmount(totalFee);
        req.setTradeAbstract("预付款运费");
        // 查询回调地址
        SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.HXPAYNOTIFYURL.code);
        req.setNotifyUrl(paramByKey.getParamValue());
        MQMessage mqMessage = new MQMessage();
        mqMessage.setTopic(HXMqMessageTopic.HX_PREPAYMENT);
        mqMessage.setTag(HXMqMessageTag.HX_PREPAY);
        mqMessage.setKey(tOrderInfo.getCode());
        mqMessage.setBody(req);
        ResultUtil resultUtil = mqAPI.sendMessage(mqMessage);
        log.info("发送MQ消息结果，{}", JSONUtil.toJsonStr(resultUtil));
        if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
            throw new RuntimeException("ZJJ-241:支付失败！");
        }
    }

    private void wkPay(TOrderInfo tOrderInfo) {
        // 设置查询参数
        SelectOpenRoleVO openRoleVO = new SelectOpenRoleVO();
        openRoleVO.setCarrierId(tOrderInfo.getCarrierId());
        openRoleVO.setCompanyId(tOrderInfo.getCompanyId());
        openRoleVO.setDriverId(tOrderInfo.getEndDriverId());

        // 查询出款方、入款方会员编号
        ResultUtil openRoleStatus = hxPayOrderUtil.selectOpenRoleStatus(openRoleVO);
        if (DictEnum.ERROR.code.equals(openRoleStatus.getCode())) {
            throw new RuntimeException(openRoleStatus.getMsg());
        }
        String jsonStr = JSONUtil.toJsonStr(openRoleStatus.getData());
        OpenRoleStatusListDTO openRoleStatusListDTO = JSONUtil.toBean(jsonStr, OpenRoleStatusListDTO.class);
        // 查询钱包
        ResultUtil openRoleWallet = hxPayOrderUtil.selectOpenRoleWallet(openRoleVO);
        if (null == openRoleWallet || DictEnum.ERROR.code.equals(openRoleWallet.getCode())) {
            throw new RuntimeException("支付失败，请联系运营平台予以解决。");
        }
        OpenRoleWalletListDTO walletListDTO = JSONUtil.toBean(JSONUtil.toJsonStr(openRoleWallet.getData()), OpenRoleWalletListDTO.class);

        TOrderCastChanges cc = tOrderCastChangesMapper.selectByNewOne(tOrderInfo.getCode());
        TOrderCastChanges castChanges = new TOrderCastChanges();
        BeanUtils.copyProperties(cc, castChanges);
        castChanges.setId(null);
        castChanges.setCode(IdWorkerUtil.getInstance().nextId());
        castChanges.setCompanyWalletId(walletListDTO.getCompanyWallet().getId());
        castChanges.setCarrierWalletId(walletListDTO.getCarrierWallet().getId());
        castChanges.setEndDriverWalletId(walletListDTO.getDriverWallet().getId());
        castChanges.setUserOper(TradeType.PayMent.code);
        castChanges.setTradeType(TradeType.RZ.code);
        castChanges.setCreateUser(CurrentUser.getUserNickname());
        castChanges.setUpdateUser(CurrentUser.getUserNickname());
        castChanges.setCreateTime(new Date());
        castChanges.setUpdateTime(new Date());

        // TODO 查询预付款运单主表，判断当前操作是否是支付尾款
        BigDecimal carriageFee;
        BigDecimal dispatchFee;
        TAdvanceOrderTmp tAdvanceOrderTmp = advanceOrderTmpMapper.selectAdvanceOrderTempByOrderCode(tOrderInfo.getCode());
        // 支付尾款，计算剩余运费和调度费
        carriageFee = tOrderInfo.getUserConfirmPaymentAmount().subtract(tAdvanceOrderTmp.getAdvanceFee());
        if (carriageFee.compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("运费不合法无法完成支付");
        }
        dispatchFee = tOrderInfo.getDispatchFee().subtract(tAdvanceOrderTmp.getAdvanceDispatchFee());
        castChanges.setCarriageFee(carriageFee);
        castChanges.setDispatchFee(dispatchFee);
        castChanges.setTotalFee(carriageFee.add(dispatchFee));

        // 修改运单主表用户确认费用和调度费
        tOrderInfo.setUserConfirmPaymentAmount(carriageFee);
        tOrderInfo.setDispatchFee(dispatchFee);
        tOrderInfo.setUpdateTime(new Date());
        tOrderInfo.setUpdateUser(CurrentUser.getUserNickname());
        orderInfoMapper.updateByPrimaryKeySelective(tOrderInfo);

        tOrderCastChangesMapper.insertSelective(castChanges);
        cc.setDataEnable(false);
        tOrderCastChangesMapper.updateByPrimaryKeySelective(cc);

        // 修改钱包
        hxWalletUtil.modifyWallet(walletListDTO.getCompanyWallet().getId(), castChanges.getTotalFee(), tOrderInfo.getFeeSettlementWay());

        TOrderPayInfo orderPay = orderPayInfoMapper.selectOnePayInfoByOrderCode(tOrderInfo.getCode());
        if (orderPay == null) {
            orderPay = new TOrderPayInfo();
            orderPay.setCode(IdWorkerUtil.getInstance().nextId());
            orderPay.setOrderCode(tOrderInfo.getCode());
            orderPay.setPayMethod(DictEnum.SINGLEPAY.code);
            orderPay.setCarrierId(tOrderInfo.getCarrierId());
            orderPay.setFeeSettlementWay(tOrderInfo.getFeeSettlementWay());

            TOrderCastCalcSnapshot tOrderCastCalcSnapshot = orderCastCalcSnapshotMapper.selectSnapsByOrderCode(tOrderInfo.getCode());
            if (null != tOrderCastCalcSnapshot) {
                orderPay.setLineGoodsCarriageRuleId(tOrderCastCalcSnapshot.getLineGoodsCarriageRuleId());
            }
            orderPay.setOrderPayStatus(DictEnum.P070.code);
            orderPay.setOrderPrepayAmount(tOrderInfo.getEstimateTotalFee());
            orderPay.setOrderActualPayment(carriageFee.add(dispatchFee));
            orderPay.setOrderTotalPayment(orderPay.getOrderActualPayment());
            orderPay.setParam1(DictEnum.HXPAY.code);
            orderPay.setPaymentPlatforms(DictEnum.HXPLATFORMS.code);
            orderPayInfoMapper.insertSelective(orderPay);
        } else {
            orderPay.setPayMethod(DictEnum.SINGLEPAY.code);
            orderPay.setOrderPayStatus(DictEnum.P070.code);
            orderPay.setOrderPrepayAmount(tOrderInfo.getTotalFee());
            orderPay.setOrderActualPayment(carriageFee.add(dispatchFee));
            orderPay.setOrderTotalPayment(orderPay.getOrderActualPayment());
            orderPay.setParam1(DictEnum.HXPAY.code);
            orderPayInfoMapper.updateByPrimaryKeySelective(orderPay);
        }

        // 创建支付子表
        String orderPayDetailCode = hxPaymentUtil.createOrderPayDetail(orderPay.getCode(), castChanges.getCode(), HXTradeTypeEnum.HX_COMBALANCE_WKPAY.code, TradeType.RZ.code, "");

        // 构造请求参数
        CustomerBalancePayReq req = new CustomerBalancePayReq();
        req.setPartnerId(hxPropertiesConfig.getPartnerId());
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setChannelId(hxPropertiesConfig.getChannelId());
        req.setBizOrderNo(orderPayDetailCode);
        req.setOutPartnerAccId(openRoleStatusListDTO.getCompanyStatus().getPartnerAccId());
        req.setInPartnerAccId(openRoleStatusListDTO.getCarrierStatus().getPartnerAccId());
        req.setOrderAmount(castChanges.getTotalFee());
        req.setTradeAbstract("预付款尾款运费");
        // 查询回调地址
        SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.HXPAYNOTIFYURL.code);
        req.setNotifyUrl(paramByKey.getParamValue());
        MQMessage mqMessage = new MQMessage();
        mqMessage.setTopic(HXMqMessageTopic.HX_PREPAYMENT);
        mqMessage.setTag(HXMqMessageTag.HX_PREPAY);
        mqMessage.setKey(tOrderInfo.getCode());
        mqMessage.setBody(req);
        ResultUtil resultUtil = mqAPI.sendMessage(mqMessage);
        log.info("发送MQ消息结果，{}", JSONUtil.toJsonStr(resultUtil));
        if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
            throw new RuntimeException("ZJJ-241:支付失败！");
        }
    }

}
