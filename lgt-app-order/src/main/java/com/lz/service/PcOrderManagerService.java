package com.lz.service;

import com.lz.common.util.ResultUtil;
import com.lz.vo.TOrderStatisticsVO;

/**
 * PC 运单管理
 * Yan
 */
public interface PcOrderManagerService {

    /**
     * PC端 运单管理: 查询运单详情
     * Yan
     * @param code
     * @return
     */
    ResultUtil selectOrderDetailed(String code);

    /**
     * PC端 运单管理: 查询运单详情
     * Yan
     * @param code
     * @return
     */
    ResultUtil selectNodeOrderDetailed(String code, String paymentPlatforms);

    /**
     * PC端 运单管理: 运单轨迹
     * Yan
     * @param code
     * @return
     */
    ResultUtil selectOrderTrajectory(String code, boolean isPC);

    /**
     * PC端 运单详情： 查询运单合同
     * Yan
     * @param code
     * @return
     */
    ResultUtil selectOrderContract(String code);

    /**
     * PC端 运单管理： 查询运单资金变动
     * @param code
     * @return
     */
    ResultUtil selectOrderFundChanges(String code);

    /**
     * 运单管理-收单-重新收单: APP根据32CODE查询运单基础信息
     * Yan
     * @param code
     * @return
     */
    ResultUtil acceptOrder(String code) throws RuntimeException ;
    /**
     * 运单管理-收单-重新收单: PC根据32CODE查询运单基础信息
     * Yan
     * @param code
     * @return
     */
    ResultUtil pcIncomOrder(String code);


    /**
    * @Description 支付运单 -> 运单详情
    * <AUTHOR>
    * @Date   2019/6/3 10:13
    * @Param
    * @Return
    * @Exception
    *
    */
    ResultUtil payOrderDetail(String code, String paymentPlatforms);

    /**
     * PC端 运营统计
     * hwt
     * @return
     */
    ResultUtil selectOrderStatistics(TOrderStatisticsVO search);

    /**
     * PC端 运营统计excel导出
     * hwt
     * @return
     */
    ResultUtil orderStatisticsExcelExport(TOrderStatisticsVO search);

    /*
     * <AUTHOR>
     * @Description 分润支付查询资金流动
     * @Date 2020/7/10 15:33
     * @Param
     * @return
    **/
    ResultUtil selectRoyaltyOrderFundChanges(String code);

}
