package com.lz.service;

import com.lz.common.util.ResultUtil;
import com.lz.dto.TAdvanceOrderTmpDto;
import com.lz.vo.AppOrderSearchVO;
import com.lz.vo.TOrderPayRuleVo;

import java.util.List;

/**
 *  @author: dingweibo
 *  @Date: 2020/3/24 9:06
 *  @Description: 运单查询
 */
public interface TOrderInfoSearchService {

    /**
    * @Description 运单跟踪查询
    * <AUTHOR>
    * @Date   2020/3/24 9:07
    * @Param
    * @Return
    * @Exception
    *
    */
    ResultUtil selectByYdgzPage(AppOrderSearchVO search) throws RuntimeException;

    ResultUtil selectByYdgzSum(AppOrderSearchVO search) throws RuntimeException;


    /**
    * @Description 运单检查查询
    * <AUTHOR>
    * @Date   2020/3/25 10:51
    * @Param
    * @Return
    * @Exception
    *
    */
    ResultUtil selectByYdjcPage(AppOrderSearchVO search) throws RuntimeException;



    /**
    * @Description 已支付实体运单
    * <AUTHOR>
    * @Date   2020/3/26 19:13
    * @Param
    * @Return
    * @Exception
    *
    */

    ResultUtil selectByYzfstydPage(AppOrderSearchVO search) throws RuntimeException;


    /**
     *  @author: dingweibo
     *  @Date: 2020/8/31 14:39
     *  @Description: 运单审核
     */
    ResultUtil selectByYdshPage(AppOrderSearchVO search) throws RuntimeException;

    ResultUtil selectByWgydPage(AppOrderSearchVO search) throws RuntimeException;

    /**
     *  @author: dingweibo
     *  @Date: 2020/11/16 9:12
     *  @Description: 节点支付查询列表
     */
    ResultUtil selectNodeByPage(AppOrderSearchVO search);


    List<TOrderPayRuleVo> selectNodeBySearch(AppOrderSearchVO search);

    ResultUtil getOrderAllPrimaryWeight(AppOrderSearchVO search);

    ResultUtil yfkTotalData(TAdvanceOrderTmpDto search);

    ResultUtil selectOrderByUserType(AppOrderSearchVO search) throws RuntimeException;

}

