package com.lz.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.codingapi.txlcn.tc.annotation.LcnTransaction;
import com.lowagie.text.pdf.PdfReader;
import com.lz.api.*;
import com.lz.common.dbenum.DictEnum;
import com.lz.api.*;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.datareport.dataexchengezj.ResultData;
import com.lz.common.util.*;
import com.lz.dao.*;
import com.lz.dto.TOrderPayInfoDTO;
import com.lz.example.TOrderContractExample;
import com.lz.model.*;
import com.lz.model.contract.req.ContractApplyCertReq;
import com.lz.model.contract.resp.ContractApplyCertResp;
import com.lz.model.contract.resp.ContractDownloadContResp;
import com.lz.model.trajectory.req.TrajectoryRouterPathReq;
import com.lz.model.trajectory.resp.TrajectoryVLastLocationResp;
import com.lz.model.trajectory.resp.VHisTrackResp;
import com.lz.model.trajectory.resp.recent.RouterPathResp;
import com.lz.model.trajectory.resp.recent.TrajectoryRecentResp;
import com.lz.model.trajectory.resp.recent.TransTimeManageVResp;
import com.lz.schedule.api.TTaskAPI;
import com.lz.schedule.model.TTask;
import com.lz.service.TOrderInfoService;
import com.lz.service.TtoolboxService;
import com.lz.tpu.api.PaymentAPI;
import com.lz.tpu.web.reqeuest.PaymentRequest;
import com.lz.util.reportData.util.StringUtil;
import com.lz.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.util.*;


/** 工具箱
 *  @author: dingweibo
 *  @Date: 2019/8/2 15:31
 *  @Description:
 */
@Service
@Transactional
@Slf4j
public class TtoolboxServiceImpl implements TtoolboxService {

    @Resource
    private TOrderInfoMapper tOrderInfoMapper;

    @Resource
    private TOrderContractMapper tOrderContractMapper;
    @Resource
    private ContractAPI contractAPI;
    @Resource
    private TrajectoryRecentAPI trajectoryRecentAPI;

    @Resource
    private TEndSUserInfoAPI tEndSUserInfoAPI;

    @Resource
    private CarrierService carrierService;

    @Resource
    private TTaskAPI tTaskAPI;

    @Resource
    private TCarrierEnduserCompanyRelAPI tCarrierEnduserCompanyRelAPI;

    @Resource
    private TOrderStateMapper tOrderStateMapper;

    @Resource
    private TOrderCastChangesMapper tOrderCastChangesMapper;

    @Resource
    private TWalletMapper tWalletMapper;

    @Resource
    private TWalletChangeLogMapper tWalletChangeLogMapper;

    @Resource
    private PaymentAPI paymentAPI;

    @Resource
    private TOrderPayDetailMapper tOrderPayDetailMapper;

    @Resource
    private TOrderPackInfoMapper tOrderPackInfoMapper;

    @Resource
    private FastdfsAPI fastdfsAPI;

    @Autowired
    private OssAPI ossAPI;

    @Resource
    private ProjectCarrierAPI projectCarrierAPI;

    @Value("${htPath}")
    private String htPath;

    @Autowired
    private TOrderInfoService orderInfoService;

    @Resource
    private TOrderAuditLogMapper orderAuditLogMapper;
    @Autowired
    private TEndCarInfoAPI tEndCarInfoAPI;

    /**
     *  @author: dingweibo
     *  @Date: 2019/8/2 15:32
     *  @Description: 针对运单重新生成合同
     */
    @Override
    public ResultUtil renewalOfContract(TtoolboxVo ttoolboxVo) {
        try {
            TOrderInfo tOrderInfo = tOrderInfoMapper.selectByOrderBusinessCode(ttoolboxVo.getOrderBusinessCode());
            String orderCode = tOrderInfo.getCode();
            TOrderContract tOrderContract = tOrderContractMapper.selectByOrderCode(orderCode);
            if(tOrderContract!=null&&!"".equals(tOrderContract)){
                if(DictEnum.REPAIRHT.code.equals(tOrderContract.getContractType())){
                    return ResultUtil.error("此运单已签署补签协议不允许重新生成合同！");
                }
            }
            TTask task = new TTask();
            task.setTaskType(DictEnum.CYHT.code+"new");//承运合同
            task.setBusinessType("HT");//HT合同 ZF 支付
            task.setSourceFieldvalue(orderCode);
            task.setRequestTimes(6);
            List<TTask> tTaskList = tTaskAPI.selectByCyht(task);
            if(tTaskList.size()>0){
                return ResultUtil.error("此运单正在异步执行生成合同，请稍后查看！");
            }
            TOrderInfoVO tOrderInfoVO = tOrderInfoMapper.selectByOrdeCode(orderCode);
            //司机信息 姓名电话
            ResultUtil reEnd = tEndSUserInfoAPI.selectById(tOrderInfoVO.getEndDriverId());
            //承运方信息
            ResultUtil reCarr = carrierService.selectById(tOrderInfoVO.getCarrierId().toString());
            TCarrierInfo tCarrierInfo =  JSON.parseObject(JSONObject.toJSON(reCarr.getData()).toString(),TCarrierInfo.class);
            //经纪人信息
            TEndUserInfo agentUserInfo = new   TEndUserInfo();
            if (null != tOrderInfoVO.getAgentId() && !"".equals(tOrderInfoVO.getAgentId())) {
                ResultUtil agentEnd = tEndSUserInfoAPI.selectById(tOrderInfoVO.getAgentId());
                agentUserInfo = JSON.parseObject(JSONObject.toJSON(agentEnd.getData()).toString(), TEndUserInfo.class);
            }

            //车队长信息
            TEndUserInfo CaptainUserInfo = new   TEndUserInfo();
            if (null!=tOrderInfoVO.getEndCarOwnerId() && !"".equals(tOrderInfoVO.getEndCarOwnerId())){
                ResultUtil CaptainEnd = tEndSUserInfoAPI.selectById(tOrderInfoVO.getEndCarOwnerId());
                CaptainUserInfo = JSON.parseObject(JSONObject.toJSON(CaptainEnd.getData()).toString(), TEndUserInfo.class);
            }
            //组织参数 生成补签协议
            Map<String, Object> map = new HashMap<>();
            map.put("orderCode",tOrderInfoVO.getCode());
            map.put("orderBusinessCode",tOrderInfoVO.getOrderBusinessCode());
            map.put("carrierId",tOrderInfoVO.getCarrierId());//承运方id
            map.put("AId",tOrderInfoVO.getEndDriverId());//司机id
            map.put("QName", tOrderInfoVO.getCarrierName());//承运方名称
            map.put("CCDBusinessNo", tOrderInfoVO.getBusinessLicenseNo());//营业执照号
            map.put("Qphone",tCarrierInfo.getCompanyContactsPhone());//企业联系手机号
            map.put("AName", tOrderInfoVO.getRealName());//司机名称
            map.put("Aphone", tOrderInfoVO.getRealPhone());//司机手机号
            map.put("ACarNo", tOrderInfoVO.getVehicleNumber());//车牌号
            map.put("ACardNo", tOrderInfoVO.getIdcard());//身份证号
            map.put("SGType", tOrderInfoVO.getGoodsName());//货物类型
            if(0.0==tOrderInfoVO.getEstimateGoodsWeight()||tOrderInfoVO.getEstimateGoodsWeight().equals(0.0)){
                map.put("SGFactCount", tOrderInfoVO.getPrimaryWeight());//数量
            }else{
                map.put("SGFactCount", tOrderInfoVO.getEstimateGoodsWeight());//数量
            }
            map.put("mobilePhone", "***********"); //平台注册手机号  固定的
            map.put("CDName", tOrderInfoVO.getCompanyName());//发货方
            map.put("SGLoadingAddress", tOrderInfoVO.getFromName());//装货地点
            map.put("SGUnLoadingName", tOrderInfoVO.getDeliverGoodsContacter());//装货联系人
            map.put("SGUnLoadingPhone", tOrderInfoVO.getDeliverGoodsContacterPhone());//装货联系电话
            map.put("SGUnLoadingAddress", tOrderInfoVO.getEndName());//收货地址
            map.put("SGLoadingName", tOrderInfoVO.getReceiveGoodsContacter());//收货联系人
            map.put("SGLoadingPhone", tOrderInfoVO.getReceiveGoodsContacterPhone());//收货联系人电话
            map.put("SGLimitTime", "30");//要求到货期限
            map.put("SGFreightTotal", tOrderInfoVO.getEstimateTotalFee());//总运费
            map.put("type", "1");//类型 1：个人 、2：企业
            map.put("cardType", "0");// 证件类型 0：身份证 1：军官证 ,2：护照、 3：驾驶证、4：工商登记证、5：税务登记证、6：组织机构代码、7：其他证件，8：统一社会信用代码
            map.put("Qphone",tCarrierInfo.getCompanyContactsPhone());//企业联系手机号
            //经纪人不为空时 为三方协议  为空时为双方协议
            TTask tTask = new TTask();
            if (null != tOrderInfoVO.getAgentId() && !"".equals(tOrderInfoVO.getAgentId())) {
                map.put("BName", agentUserInfo.getRealName());// 经纪人姓名
                map.put("Bphone", agentUserInfo.getPhone());// 经纪人平台账号
                map.put("BId", agentUserInfo.getId());// 经纪人id
                map.put("BCardNo", agentUserInfo.getIdcard());//经纪人身份证号
                map.put("SGServiceFee", tOrderInfoVO.getServiceFee());//服务费
                tTask.setTaskTypeNode(DictEnum.THREESIDES.code);//三方协议

                if (null!=tOrderInfoVO.getCapitalTransferType() && DictEnum.PAYTOCAPTAIN.code.equals(tOrderInfoVO.getCapitalTransferType())){
                    if (null!=tOrderInfoVO.getEndCarOwnerId() && !"".equals(tOrderInfoVO.getEndCarOwnerId())){
                        map.put("DName", CaptainUserInfo.getRealName());// 车队长姓名
                        map.put("Dphone", CaptainUserInfo.getPhone());// 车队长平台账号
                        map.put("DId", CaptainUserInfo.getId());// 车队长id
                        map.put("DCardNo", CaptainUserInfo.getIdcard());//车队长身份证号
                        tTask.setTaskTypeNode(DictEnum.FOURSIDES.code);//四方协议
                    }
                }
            }else{
                if (null!=tOrderInfoVO.getCapitalTransferType() && DictEnum.PAYTOCAPTAIN.code.equals(tOrderInfoVO.getCapitalTransferType())){
                    if (null!=tOrderInfoVO.getEndCarOwnerId() && !"".equals(tOrderInfoVO.getEndCarOwnerId())){
                        map.put("DName", CaptainUserInfo.getRealName());// 车队长姓名
                        map.put("Dphone", CaptainUserInfo.getPhone());// 车队长平台账号
                        map.put("DId", CaptainUserInfo.getId());// 车队长id
                        map.put("DCardNo", CaptainUserInfo.getIdcard());//车队长身份证号
                        tTask.setTaskTypeNode(DictEnum.CAPTAINTHREESIDES.code);//普通模式三方协议
                    }
                }else{
                    tTask.setTaskTypeNode(DictEnum.BOTHSIDES.code);//双方协议
                }
            }
            String requestParameter = JSONObject.toJSONString(map);
            tTask.setRequestParameter(requestParameter);
            tTask.setTaskId(IdWorkerUtil.getInstance().nextId());
            tTask.setTaskType(DictEnum.CYHT.code+"new");//承运合同
            tTask.setBusinessType("HT");//HT合同 ZF 支付
            tTask.setSourceTablename("T_ORDER_INFO");
            tTask.setSourceFieldvalue(orderCode);
            tTask.setSourceFieldname("code");
            tTask.setRequestTimes(0);
            tTask.setCreateTime(new Date());
            tTask.setRequestDate(new Date());
            tTask.setIsSuccessed(false);
            tTask.setEnable(false);
            //添加task表
            tTaskAPI.add(tTask);
            return ResultUtil.ok();
        }catch (Exception e){
            log.error("重新生成合同：",e);
            return ResultUtil.error("重新生成合同失败，运单号:"+ttoolboxVo.getOrderBusinessCode());
        }
    }



    /**
     * @Description 针对运单重新生成收款凭证
     * <AUTHOR>
     * @Date   2019/8/5 8:50
     * @Param
     * @Return
     * @Exception
     *
     */
    @Override
    public ResultUtil renewalOfTxxy(TtoolboxVo ttoolboxVo) {
        try {
            ResultUtil resultUtil = new ResultUtil();
            String orderBusinessCode = ttoolboxVo.getOrderBusinessCode();
            TOrderInfoVO tOrderInfoVO = tOrderInfoMapper.selectByOrderBusinessCode(orderBusinessCode);
            TOrderCastChanges tOrderCastChanges = tOrderCastChangesMapper.selectByNewOne(tOrderInfoVO.getCode());
            if(tOrderCastChanges.getCapitalTransferPattern()!=null && !"".equals(tOrderCastChanges.getCapitalTransferPattern())){
                resultUtil = renewalOfTxxyNew(ttoolboxVo,tOrderCastChanges);
            }else{
                resultUtil = renewalOfTxxyOld(ttoolboxVo);
            }
            return  resultUtil;
        }catch (Exception e){
            log.error("生成收款凭证失败！",e);
            return ResultUtil.error("生成收款凭证失败！");
        }
    }
    /**
     * @Description 针对运单重新生成收款凭证 新模式
     * <AUTHOR>
     * @Date   2019/8/5 8:50
     * @Param
     * @Return
     * @Exception
     *
     */
    private ResultUtil renewalOfTxxyNew(TtoolboxVo ttoolboxVo,TOrderCastChanges tOrderCastChanges) {
        try {
            String orderBusinessCode = ttoolboxVo.getOrderBusinessCode();
            TOrderInfoVO tOrderInfoVO = tOrderInfoMapper.selectByOrderBusinessCode(orderBusinessCode);
            String orderCode= tOrderInfoVO.getCode();
            if(!DictEnum.M100.code.equals(tOrderInfoVO.getOrderExecuteStatus())&&!"M090".equals(tOrderInfoVO.getOrderPayStatus())){
                return ResultUtil.error("运单未完成不允许生成收款凭证");
            }

            TTask task = new TTask();
            task.setTaskType(DictEnum.SKPZ.code);//提现协议
            task.setBusinessType("TX");//提现
            task.setSourceFieldvalue(orderCode);
            task.setRequestTimes(6);
            List<TTask> tTaskList = tTaskAPI.selectByCyht(task);
            if(tTaskList.size()>0){
                return ResultUtil.error("此运单正在异步执行生成收款凭证，请稍后查看！");
            }
            //经纪人id不为空时生成经纪人收款凭证
            if(tOrderInfoVO.getAgentId()!=null&&!"".equals(tOrderInfoVO.getAgentId())){
                TOrderContractExample example = new TOrderContractExample();
                TOrderContractExample.Criteria  c = example.createCriteria();
                example.setOrderByClause("create_time desc");
                c.andOrderCodeEqualTo(orderCode);
                c.andEnableEqualTo(true);
                c.andContractTypeEqualTo(DictEnum.AGENTTXXY.code);
                List<TOrderContract> list = tOrderContractMapper.selectByExample(example);
                TOrderContract tOrderContract = null;
                if (list.size()>0){
                    tOrderContract = list.get(0);
                }
                //备注
                String remark = "";
                if(tOrderContract!=null &&!"".equals(tOrderContract)){
                    remark = "凭证编号为"+tOrderContract.getContractCode()+"的合同已作废";
                }
                //经纪人信息
                ResultUtil agentEnd = tEndSUserInfoAPI.selectById(tOrderInfoVO.getAgentId());
                TEndUserInfo agentUserInfo = JSON.parseObject(JSONObject.toJSON(agentEnd.getData()).toString(), TEndUserInfo.class);
                //经纪人网商子账号信息
                TCarrierEnduserCompanyRel agentCarrierRel = tCarrierEnduserCompanyRelAPI.selectByEndUser(tOrderInfoVO.getCarrierId(), tOrderInfoVO.getAgentId());
                Map<String, Object> map = new HashMap<>();
                map.put("carrierId", tOrderInfoVO.getCarrierId());//承运方id
                map.put("AId", tOrderInfoVO.getEndDriverId());//司机id
                map.put("ACardNo", tOrderInfoVO.getIdcard());// 司机证件号码
                map.put("AName", tOrderInfoVO.getRealName());//司机真实名称
                map.put("Aphone", tOrderInfoVO.getRealPhone());//司机联系手机号
                map.put("orderCode", tOrderInfoVO.getCode());//运单code
                map.put("orderBusinessCode", tOrderInfoVO.getOrderBusinessCode());//运单号
                map.put("mobilePhone", "***********"); //平台注册手机号  固定的
                map.put("agentAmount", tOrderInfoVO.getUserConfirmServiceFee());//服务费
                map.put("remark",remark);//备注
                if ("0".equals(tOrderInfoVO.getPackStatus())) {//0 未打包运单去用户确认应付运费     1 打包运单 去打包后运费
                    map.put("driverAmount", tOrderInfoVO.getUserConfirmPaymentAmount());
                } else {
                    map.put("driverAmount", tOrderInfoVO.getSharePaymentAmount());
                }
                TTask tTask = new TTask();
                map.put("BCardNo", agentUserInfo.getIdcard());//经纪人证件号码
                map.put("BName", agentUserInfo.getRealName());//经纪人名称
                map.put("Bphone", agentUserInfo.getPhone());//经纪人联系手机号
                map.put("BId", agentUserInfo.getId());// 经纪人id
                map.put("agentApplyType", "服务费("+tOrderInfoVO.getOrderBusinessCode()+")");
                map.put("agentRecCardNO", agentCarrierRel.getThridParySubAccount());
                tTask.setTaskTypeNode(DictEnum.AGENTTXXY.code);//经纪人收款凭证
                String requestParameter = JSONObject.toJSONString(map);
                tTask.setRequestParameter(requestParameter);
                tTask.setTaskId(IdWorkerUtil.getInstance().nextId());
                tTask.setTaskType(DictEnum.SKPZ.code);//提现协议
                tTask.setBusinessType("TX");//提现
                tTask.setSourceTablename("T_ORDER_INFO");
                tTask.setSourceFieldvalue(orderCode);
                tTask.setSourceFieldname("code");
                tTask.setRequestTimes(0);
                tTask.setCreateTime(new Date());
                tTask.setRequestDate(new Date());
                tTask.setIsSuccessed(false);
                tTask.setEnable(false);
                tTaskAPI.add(tTask);
            }

            //司机收款凭证
            TOrderContractExample example = new TOrderContractExample();
            TOrderContractExample.Criteria  c = example.createCriteria();
            example.setOrderByClause("create_time desc");
            c.andOrderCodeEqualTo(orderCode);
            c.andEnableEqualTo(true);
            List<TOrderContract> list = tOrderContractMapper.selectByExample(example);
            TOrderContract tOrderContract = null;
            if (list.size()>0){
                tOrderContract = list.get(0);
            }
            //备注
            String remark = "";
            //承运方信息
            ResultUtil reCarr = carrierService.selectById(tOrderInfoVO.getCarrierId().toString());
            TCarrierInfo tCarrierInfo = JSON.parseObject(JSONObject.toJSON(reCarr.getData()).toString(), TCarrierInfo.class);
            TCarrierEnduserCompanyRel driverCarrierRel = new TCarrierEnduserCompanyRel();
            if(tOrderContract!=null &&!"".equals(tOrderContract)){
                remark = "凭证编号为"+tOrderContract.getContractCode()+"的合同已作废";
            }
            TTask tTask = new TTask();
            Map<String, Object> map = new HashMap<>();
            map.put("carrierId", tOrderInfoVO.getCarrierId());//承运方id
            if(DictEnum.PAYTOCAPTAIN.code.equals(tOrderCastChanges.getCapitalTransferType())){
                c.andContractTypeEqualTo(DictEnum.CAPTAINTXXY.code);
                //车队长网商子账号信息
                driverCarrierRel = tCarrierEnduserCompanyRelAPI.selectByEndUser(tOrderInfoVO.getCarrierId(), tOrderInfoVO.getEndCarOwnerId());
                map.put("DId", tOrderInfoVO.getEndCarOwnerId());//车队长id
                map.put("DCardNo", tOrderInfoVO.getCaptainIdcard());// 车队长证件号码
                map.put("DName", tOrderInfoVO.getCaptainName());//车队长真实名称
                map.put("Dphone", tOrderInfoVO.getCaptainPhone());//车队长联系手机号
                tTask.setTaskTypeNode(DictEnum.CAPTAINTXXY.code);//司机收款凭证
            }else{
                c.andContractTypeEqualTo(DictEnum.DRIVERTXXY.code);
                //司机网商子账号信息
                driverCarrierRel = tCarrierEnduserCompanyRelAPI.selectByEndUser(tOrderInfoVO.getCarrierId(), tOrderInfoVO.getEndDriverId());
                map.put("AId", tOrderInfoVO.getEndDriverId());//司机id
                map.put("ACardNo", tOrderInfoVO.getIdcard());// 司机证件号码
                map.put("AName", tOrderInfoVO.getRealName());//司机真实名称
                map.put("Aphone", tOrderInfoVO.getRealPhone());//司机联系手机号
                tTask.setTaskTypeNode(DictEnum.DRIVERTXXY.code);//司机收款凭证
            }
            map.put("orderCode", tOrderInfoVO.getCode());//运单code
            map.put("orderBusinessCode", tOrderInfoVO.getOrderBusinessCode());//运单号
            map.put("mobilePhone", "***********"); //平台注册手机号  固定的
            map.put("agentAmount", tOrderInfoVO.getUserConfirmServiceFee());//服务费
            map.put("remark",remark);//备注
            if ("0".equals(tOrderInfoVO.getPackStatus())) {//0 未打包运单去用户确认应付运费     1 打包运单 去打包后运费
                map.put("driverAmount", tOrderInfoVO.getUserConfirmPaymentAmount());
            } else {
                map.put("driverAmount", tOrderInfoVO.getSharePaymentAmount());
            }
            map.put("CCDBusinessNo", tCarrierInfo.getBusinessLicenseNo());// 承运方证件号码
            map.put("QName", tCarrierInfo.getCarrierName());//承运方名称
            map.put("Qphone", tCarrierInfo.getCompanyContactsPhone());//承运方联系手机号
            map.put("driverApplyType", "运费("+tOrderInfoVO.getOrderBusinessCode()+")");
            map.put("driverRecCardNO", driverCarrierRel.getThridParySubAccount());
            String requestParameter = JSONObject.toJSONString(map);
            tTask.setRequestParameter(requestParameter);
            tTask.setTaskId(IdWorkerUtil.getInstance().nextId());
            tTask.setTaskType(DictEnum.SKPZ.code);//提现协议
            tTask.setBusinessType("TX");//提现
            tTask.setSourceTablename("T_ORDER_INFO");
            tTask.setSourceFieldvalue(orderCode);
            tTask.setSourceFieldname("code");
            tTask.setRequestTimes(0);
            tTask.setCreateTime(new Date());
            tTask.setRequestDate(new Date());
            tTask.setIsSuccessed(false);
            tTask.setEnable(false);
            tTaskAPI.add(tTask);
            return ResultUtil.ok();
        }catch (Exception e){
            log.error("生成收款凭证失败！",e);
            return ResultUtil.error("生成收款凭证失败！");
        }
    }

    /**
     * @Description 针对运单重新生成收款凭证 旧模式
     * <AUTHOR>
     * @Date   2019/8/5 8:50
     * @Param
     * @Return
     * @Exception
     *
     */
    private ResultUtil renewalOfTxxyOld(TtoolboxVo ttoolboxVo) {
        try {
            String orderBusinessCode = ttoolboxVo.getOrderBusinessCode();
            TOrderInfoVO tOrderInfoVO = tOrderInfoMapper.selectByOrderBusinessCode(orderBusinessCode);
            VoucherReq req = new VoucherReq();
            //单笔参数
            if("0".equals(tOrderInfoVO.getPackStatus())){
                req.setMoney(tOrderInfoVO.getUserConfirmPaymentAmount());//金额
                TOrderPayInfoDTO tOrderPayInfoDTO = tOrderPayDetailMapper.selectByBankno(tOrderInfoVO.getCode());
                req.setRecUser(tOrderPayInfoDTO.getCardHolder());//收款人
                req.setRecCardNO(tOrderPayInfoDTO.getBankNo());//收款账号
            }else{//打包参数
                req.setMoney(tOrderInfoVO.getUserConfirmPaymentAmount());//金额
                TOrderPayInfoDTO tOrderPayInfoDTO = tOrderPackInfoMapper.selectByBankno(tOrderInfoVO.getCode());
                req.setRecUser(tOrderPayInfoDTO.getCardHolder());//收款人
                req.setRecCardNO(tOrderPayInfoDTO.getBankNo());//收款账号
            }
            log.info("生成收款凭证入参："+JSONObject.toJSONString(tOrderInfoVO));
            TOrderContract tOrderContract = new TOrderContract();
            tOrderContract.setContractCode(IdWorkerUtil.getInstance().nextId());
            tOrderContract.setCreateTime(new Date());//生成时间

            req.setDriverName(tOrderInfoVO.getRealName());//司机姓名
            req.setApplyType(tOrderInfoVO.getOrderBusinessCode());//支出项 运单号
            req.setBusinessLicenseNo(tOrderInfoVO.getBusinessLicenseNo());//营业执照号
            req.setCarrierId(tOrderInfoVO.getCarrierId());//承运方id
            req.setEndUserId(tOrderInfoVO.getEndDriverId());//司机id
            req.setIdCardNum(tOrderInfoVO.getIdcard());//身份证号
            req.setMobilePhone(tOrderInfoVO.getRealPhone());
            req.setOrderCode(tOrderInfoVO.getCode());//运单业务id
            req.setPayUser(tOrderInfoVO.getCarrierName());//付款方 承运方
            req.setQName(tOrderInfoVO.getCarrierName());//企业名称
            req.setQphone(tOrderInfoVO.getCompanyContactsPhone());//企业联系手机号
            req.setType("运费");
            ResultUtil resultUtil = contractAPI.createVoucher(req);
            //易云章返回合同编号
            String contractNum = JSONObject.parseObject(JSONObject.parseObject(resultUtil.getData().toString()).get("data").toString()).get("contractNum").toString();

            //企业签署合同
            Map<Object, Object> mapo = new HashMap<>();
            mapo.put("type", "2");//类型 1：个人 、2：企业
            mapo.put("cardType", "8");// 证件类型 0：身份证 1：军官证 ,2：护照、 3：驾驶证、4：工商登记证、5：税务登记证、6：组织机构代码、7：其他证件，8：统一社会信用代码
            mapo.put("idCardNum", req.getBusinessLicenseNo());// 证件号码
            mapo.put("name", req.getQName());//企业或者个人真实名称
            mapo.put("mobilePhone", req.getQphone());//企业联系手机号

            tOrderContract.setFirstPartSignTime(new Date());
            //个人调用易云章 根据签署合同
            Map<Object, Object> mapp = new HashMap<>();
            mapp.put("type", "1");//类型 1：个人 、2：企业
            mapp.put("cardType", "0");// 证件类型 0：身份证 1：军官证 ,2：护照、 3：驾驶证、4：工商登记证、5：税务登记证、6：组织机构代码、7：其他证件，8：统一社会信用代码
            mapp.put("idCardNum", req.getIdCardNum());// 证件号码
            mapp.put("name", req.getDriverName());//企业或者个人真实名称
            mapp.put("mobilePhone", req.getMobilePhone());//企业或者个人联系手机号

            tOrderContract.setSecondPartSignTime(new Date());
            tOrderContract.setSignFinishTime(new Date());
            log.info("签署合同开始！");
            //组织参数
            ContractApplyCertResp ru22 = contractAPI.autoSign(mapo, contractNum, 1);
            ContractApplyCertResp ru = contractAPI.autoSign(mapp, contractNum, 0);

            //成功
            if ("0".equals(ru.getCode()) && "0".equals(ru22.getCode())) {
                log.info("签署合同成功！");
                //合同下载
                tOrderContract.setContractDownloadTime(new Date());//下载时间
                ContractDownloadContResp r = contractAPI.downloadCont(contractNum, req.getQphone());
                log.info("收款凭证下载成功！");
                InputStream inputStream = new ByteArrayInputStream(r.getBytes());

                BufferedOutputStream bos = null;
                FileOutputStream fos = null;
                File file = null;
                log.info("写入pdf文件开始！" + htPath + contractNum + ".pdf");
                try {
                    File dir = new File(htPath + contractNum + ".pdf");
                    if (!dir.exists() && dir.isDirectory()) {//判断文件目录是否存在
                        dir.mkdirs();
                    }
                    fos = new FileOutputStream(dir);
                    bos = new BufferedOutputStream(fos);
                    bos.write(r.getBytes());
                    log.info("写入pdf文件成功！" + htPath + contractNum + ".pdf");
                } catch (Exception e) {
                    log.info("生成pdf失败！", e);
                    throw new RuntimeException("签署合同失败！");
                } finally {
                    if (bos != null) {
                        try {
                            bos.close();
                        } catch (IOException e1) {
                            e1.printStackTrace();
                            throw new RuntimeException("签署合同失败！");
                        }
                    }
                    if (fos != null) {
                        try {
                            fos.close();
                        } catch (IOException e1) {
                            throw new RuntimeException("签署合同失败！");
                        }
                    }
                }
                PDDocument pdDocument = null;
                try {
                    log.info("写入图片文件开始！" + htPath + contractNum + ".pdf");
                    File file2 = new File(htPath + contractNum + ".pdf");
                    pdDocument = PDDocument.load(file2);
                    PDFRenderer renderer = new PDFRenderer(pdDocument);
                    /* dpi越大转换后越清晰，相对转换速度越慢 */
                    PdfReader reader = new PdfReader(htPath + contractNum + ".pdf");
                    int pages = reader.getNumberOfPages();
                    for (int i = 0; i < 1; i++) {
                        String pngFilePath = htPath + contractNum + ".png";
                        BufferedImage image = renderer.renderImageWithDPI(i, 300);
                        ImageIO.write(image, "png", new File(pngFilePath));
                    }
                    log.info("写入图片文件成功！");
                } catch (IOException e) {
                    log.error("写入图片文件失败！",e);
                    throw new RuntimeException("签署合同失败！");
                }finally {
                    pdDocument.close();
                }
                MultipartFile multipartFile = null;
                MultipartFile multipartFileImg = null;
                try {
                    multipartFile = new MockMultipartFile("file", contractNum + ".pdf", "pdf", inputStream);
                } catch (IOException e) {
                    e.printStackTrace();
                    throw new RuntimeException("签署合同失败！");
                }
                //生成图片
                try {
                    File file3 = new File(htPath + contractNum + ".png");
                    InputStream In = new FileInputStream(file3);
                    multipartFileImg = new MockMultipartFile("file", contractNum + ".png", "png", In);
                } catch (IOException e) {
                    log.info("生成图片失败！", e);
                    throw new RuntimeException("签署合同失败！");
                }
                //文件上传
                //ResultUtil fastResult = fastdfsAPI.uploadFileSample(multipartFile);
                ResultUtil fastResult = ossAPI.uploadFileSample(multipartFile);
                //图片上传
                //ResultUtil fastResult2 = fastdfsAPI.uploadFileSample(multipartFileImg);
                ResultUtil fastResult2 = ossAPI.uploadFileSample(multipartFileImg);

                /*Map<Object, Object> fastResultData = (Map<Object, Object>) fastResult.getData();
                Map<Object, Object> fastResultData2 = (Map<Object, Object>) fastResult2.getData();*/
                //合同下载路径
                String contractFilePath = fastResult.getData().toString();
                //合同下载路径
                String contractFilePath2 = fastResult2.getData().toString();
                tOrderContract.setContractType("TXXY");//提现协议
                tOrderContract.setContractForm("SKPZ");
                tOrderContract.setFirstPart(req.getPayUser());
                tOrderContract.setFirstPartPlatformId(req.getCarrierId());
                tOrderContract.setSecondPart(req.getRecUser());
                tOrderContract.setSecondPartPlatformId(req.getEndUserId());
                tOrderContract.setOrderCode(req.getOrderCode());
                tOrderContract.setContractFilePath(contractFilePath);
                tOrderContract.setParam1(contractNum + ".pdf");
                tOrderContract.setParam2(contractFilePath2);//合同图片路径
                tOrderContract.setParam3(contractNum + ".jpg");
                tOrderContractMapper.insertSelective(tOrderContract);
            } else {
                log.info("签署合同失败！");
                log.error("司机签署失败原因：" + ru.getMessage() + "|| 企业签署失败原因：" + ru22.getMessage());
                return ResultUtil.error("司机签署失败原因：" + ru.getMessage() + "|| 企业签署失败原因：" + ru22.getMessage());
            }
        }catch (Exception e){
            log.error("生成收款凭证失败！",e);
            return ResultUtil.error("生成收款凭证失败！");
        }
        return ResultUtil.ok();
    }



    /**
     * @Description 解绑司机个人信息
     * <AUTHOR>
     * @Date   2019/8/5 8:50
     * @Param
     * @Return
     * @Exception
     *
     */
    @Override
    public ResultUtil driverUnwrap(TtoolboxVo ttoolboxVo) {

        try{
            ContractApplyCertReq req = new ContractApplyCertReq();
            req.setType("1");//类型 1：个人 、2：企业
            req.setCardType("0");// 证件类型 0：身份证 1：军官证 ,2：护照、 3：驾驶证、4：工商登记证、5：税务登记证、6：组织机构代码、7：其他证件，8：统一社会信用代码
            req.setIdCardNum(ttoolboxVo.getIdCard());// 证件号码
            req.setName(ttoolboxVo.getName());//企业或者个人真实名称
            req.setMobilePhone(ttoolboxVo.getPhone());//企业或者个人联系手机号
            log.info("解绑司机个人信息入参："+JSONObject.toJSONString(req));
            ContractApplyCertResp resp = contractAPI.unwrap(req);
            log.info("返回参数："+resp);
            if("0".equals(resp.getCode())){
                return ResultUtil.ok("解绑成功！");
            }else{
                return ResultUtil.error("解绑失败，失败原因:"+resp.getMessage());
            }
        }catch (Exception e){
            log.error("解绑司机个人信息失败！",e);
            return ResultUtil.error("解绑司机个人信息失败！");
        }
    }

    /**
     * @Description 解绑企业信息
     * <AUTHOR>
     * @Date   2019/8/5 8:50
     * @Param
     * @Return
     * @Exception
     *
     */
    @Override
    public ResultUtil companyUnwrap(TtoolboxVo ttoolboxVo) {
        try{

            ContractApplyCertReq req = new ContractApplyCertReq();
            req.setType("2");//类型 1：个人 、2：企业
            req.setCardType("8");// 证件类型 0：身份证 1：军官证 ,2：护照、 3：驾驶证、4：工商登记证、5：税务登记证、6：组织机构代码、7：其他证件，8：统一社会信用代码
            req.setIdCardNum(ttoolboxVo.getBusinessLicenseNo());// 证件号码
            req.setName(ttoolboxVo.getName());//企业或者个人真实名称
            req.setMobilePhone(ttoolboxVo.getPhone());//企业或者个人联系手机号
            ContractApplyCertResp resp = contractAPI.unwrap(req);
            log.info("返回参数："+resp);
            if("0".equals(resp.getCode())){
                return ResultUtil.ok("解绑成功！");
            }else{
                return ResultUtil.error("解绑失败，失败原因:"+resp.getMessage());
            }
        }catch (Exception e){
            log.error("解绑企业信息失败！",e);
            return ResultUtil.error("解绑企业信息失败！");
        }
    }

    /**
     * @Description 修改司机手机号
     * <AUTHOR>
     * @Date   2019/8/5 8:50
     * @Param
     * @Return
     * @Exception
     *
     */
    @Override
    public ResultUtil driverChangeMobile(TtoolboxVo ttoolboxVo) {
        try{
            ContractApplyCertReq req = new ContractApplyCertReq();
            req.setType("1");//类型 1：个人 、2：企业
            req.setCardType("0");// 证件类型 0：身份证 1：军官证 ,2：护照、 3：驾驶证、4：工商登记证、5：税务登记证、 6：组织机构代码、7：其他证件，8：统一社会信用代码
            req.setIdCardNum(ttoolboxVo.getIdCard());// 证件号码
            req.setName(ttoolboxVo.getName());//企业或者个人真实名称
            req.setMobilePhone(ttoolboxVo.getOldPhone());//企业或者个人联系手机号 旧手机号
            req.setNewMobilePhone(ttoolboxVo.getPhone());//新手机号
            log.info("更换手机号入参："+JSONObject.toJSONString(req));
            ContractApplyCertResp resp = contractAPI.changeMobile(req);
            log.info("返回参数："+resp);
            if("0".equals(resp.getCode())){
                return ResultUtil.ok("修改司机手机号成功！");
            }else{
                return ResultUtil.error("修改司机手机号失败，失败原因:"+resp.getMessage());
            }
        }catch (Exception e){
            log.error("修改司机手机号失败",e);
            return ResultUtil.error("修改司机手机号失败！");
        }
    }

    /**
     * @Description 查询车辆轨迹
     * <AUTHOR>
     * @Date   2019/9/12 15:01
     * @Param
     * @Return
     * @Exception
     *
     */
    @Override
    public TrajectoryRecentResp vHisTrack24(TtoolboxVo ttoolboxVo) {
        TrajectoryRecentResp resp = new TrajectoryRecentResp();
        try{
            String qryBtm = "";
            String qryEtm = "";
            if(ttoolboxVo.getDateArray()!=null &&ttoolboxVo.getDateArray().length>0){
                qryBtm = DateUtils.formatDateTime(ttoolboxVo.getDateArray()[0]);
                qryEtm = DateUtils.formatDateTime(ttoolboxVo.getDateArray()[1]);
            }else{
                resp.setCode("error");
                resp.setMsg("请选择查询的时间！");
                return resp;
            }
            TrajectoryRouterPathReq req = new TrajectoryRouterPathReq();
            req.setVclN(ttoolboxVo.getVehicleNumber());
            req.setVco(ttoolboxVo.getLicensePlateColor());
            req.setQryBtm(qryBtm);
            req.setQryEtm(qryEtm);
            resp = trajectoryRecentAPI.routerPath(req);
            if("1006".equals(resp.getStatus())){
                resp.setCode("success");
                resp.setMsg("当前时间段车辆无轨迹");
            }else if("1001".equals(resp.getStatus())){
                List<RouterPathResp> list  =  resp.getData();
               for(RouterPathResp r:list){
                   if(r.getLat()!=null &&!"".equals(r.getLat())){
                        r.setLat(String.valueOf(Double.parseDouble(r.getLat())/600000));
                   }
                   if(r.getLon()!=null &&!"".equals(r.getLon())){
                       r.setLon(String.valueOf(Double.parseDouble(r.getLon())/600000));
                   }

               }
            }else{
                resp.setStatus("0");
                resp.setCode("success");
                resp.setMsg("车辆无轨迹");
            }
            return resp;
        }catch (Exception e){
            log.error("查询车辆轨迹失败！",e);
            resp.setCode("error");
            resp.setMsg("查询车辆轨迹失败");
            return resp;
        }
    }

    @Override
    @LcnTransaction
    @Transactional
    public ResultUtil fundsFallback(TtoolboxVo ttoolboxVo) {

        /*
        *   1、根据运单号判断运单状态，提现完成的才能做资金回退
            2、根据运单查找资金转移方式
            3、根据资金转移方式查询出实际到账的子账号，去网商查询子账号是否已经资金回退成功
            4、将运单支付状态修改为支付完成，运单子状态表将提现记录数据置为无效
            5、修改资金变动表将提现记录修改为无效，将支付记录修改为有效
            6、修改支付子表，将提现记录数据置为无效
            7、修改钱包表增加余额，并将钱包流水表的提现流水置为无效
       * */
       if (StringUtil.isEmpty(ttoolboxVo.getOrderBusinessCode())){
           return ResultUtil.error("请输入需要操作的运单号");
       }
       /*支付状态改成支付完成*/
       TOrderInfoVO tOrderInfoDB = tOrderInfoMapper.selectByOrderBusinessCode(ttoolboxVo.getOrderBusinessCode());
       if (tOrderInfoDB==null){
           return ResultUtil.error("当前运单号不存在");
       }else if (!"M130".equals(tOrderInfoDB.getOrderPayStatus())){
           return ResultUtil.error("当前运单状态不是提现完成");
       }


       /*运单资金变动表*/
       /*todo 判断 enable  资金转移方式为tx   确定data_enable值 */
       List<TOrderCastChanges> tOrderCastChangesDB = tOrderCastChangesMapper.selectByOrderCode(tOrderInfoDB.getCode());
       if (tOrderCastChangesDB==null){
           return ResultUtil.error("当前运单无资金转移方式为TX的数据");
       }else if (tOrderCastChangesDB.size()!=2){
           return ResultUtil.error("当前运单无资金回退记录");
       }

       //资金转移方式 查询子账号
       String capitalTransferType="";
       //资金转移方式对应钱包
       Integer endUserId=null;
       Integer walletId=null;
       for (TOrderCastChanges tOrderCastChanges : tOrderCastChangesDB) {
           if (tOrderCastChanges.getDataEnable()){
               capitalTransferType=tOrderCastChanges.getCapitalTransferType();
               if("PAYTODRIVER".equals(capitalTransferType)){
                   //直接支付到实际运输人  end_driver_id
                    endUserId=tOrderInfoDB.getEndDriverId();
                   walletId=tOrderCastChangesDB.get(0).getEndDriverWalletId();
               }else if ("PAYTOBELONGER".equals(capitalTransferType)){
                   //支付到车辆所有人
                    endUserId=tOrderInfoDB.getEndCarOwnerId();
                    walletId=tOrderCastChangesDB.get(0).getEndDriverWalletId();
               }else if ("FIRSTBROKERDRIVER".equals(capitalTransferType)){
                   //支付给经纪人，再由经纪人支付给实际运输人 //todo 需要确定取值
                   return ResultUtil.error("不支持经纪人模式资金回退");
               }else if ("FIRSTBROKERBELONGER".equals(capitalTransferType)){
                   //支付给经纪人，再由经纪人支付给车辆所有人 //todo 需要确定取值
                   return ResultUtil.error("不支持经纪人模式资金回退");
                }
           }
       }

       if (tOrderInfoDB.getCarrierId()==null){
           return ResultUtil.error("当前运单承运方信息不全,无法查询网商余额");
       }else if (tOrderInfoDB.getCompanyId()==null){
           return ResultUtil.error("当前运单公司信息不全,无法查询网商余额");
       }
       TCarrierEnduserCompanyRelVo tCarrierEnduserCompanyRelVo = new TCarrierEnduserCompanyRelVo();
       tCarrierEnduserCompanyRelVo.setCarrierId(tOrderInfoDB.getCarrierId());
       tCarrierEnduserCompanyRelVo.setEnduserCompanyId(endUserId);
       ResultUtil result = tCarrierEnduserCompanyRelAPI.selectCarrierRel(tCarrierEnduserCompanyRelVo);
       TCarrierEnduserCompanyRel tCarrierEnduserCompanyRelDB=null;
       if ("success".equals(result.getCode()) && result.getData()!=null){
           LinkedHashMap data = (LinkedHashMap) result.getData();
           String s = JSONObject.toJSONString(data);
           tCarrierEnduserCompanyRelDB = JSONObject.parseObject(s, TCarrierEnduserCompanyRel.class);
/*
           tCarrierEnduserCompanyRelDB = (TCarrierEnduserCompanyRel) result.getData();
*/
           if (tCarrierEnduserCompanyRelDB==null){
               return ResultUtil.error("该运单承运方与企业关系表不存在!");
           }
       }else {
           return result;
       }

       //承运方信息
       TCarrierInfo tCarrierInfo = carrierService.selectCarrierById(tOrderInfoDB.getCarrierId().toString());
       if (tCarrierInfo==null){
           return ResultUtil.error("查询不到当前承运方信息");
       }
       //查询网商当前账户余额
       PaymentRequest paymentRequest = new PaymentRequest();
       paymentRequest.setPartner_id(tOrderInfoDB.getCarrierId().toString());//承运方ID
       paymentRequest.setKeyStoreName(tCarrierInfo.getThirdPartyInterfaceManageAddress());//密钥
       paymentRequest.setUid(tCarrierEnduserCompanyRelDB.getUid());//承运方与企业关系表UID
       ResultUtil resultUtil = paymentAPI.balanceAccount(paymentRequest);
       boolean flag=false;
       Map WSMap=null;
       if (resultUtil!=null){
           if (resultUtil.getData()!=null){
               String WSData = (String) resultUtil.getData();
               Map map = JSONObject.parseObject(WSData, Map.class);
               if (map!=null){
                   WSMap=map;
                   String is_success = (String) map.get("is_success");
                   if ("T".equals(is_success)){
                       flag=true;
                       log.info("网商查询成功");
                   }else {
                       return resultUtil;
                   }
               }
           }else {
               return resultUtil;
           }
       }else {
           return resultUtil;
       }
       /*
            资金回退资金转移方式为tx,data_enable为1的置0 enable置1
            资金回退资金转移方式为tx,data_enable为0的置1 enable置0
        */
        for (TOrderCastChanges tOrderCastChanges : tOrderCastChangesDB) {
            if (tOrderCastChanges.getDataEnable()){
                tOrderCastChanges.setDataEnable(false);
                tOrderCastChanges.setEnable(true);
                tOrderCastChanges.setCode(tOrderCastChanges.getCode()+"1");
                tOrderCastChangesMapper.updateByPrimaryKeySelective(tOrderCastChanges);
            }else{
                tOrderCastChanges.setDataEnable(true);
                tOrderCastChanges.setEnable(false);
                tOrderCastChangesMapper.updateByPrimaryKeySelective(tOrderCastChanges);
            }
        }

        /*
        *  将运单支付状态修改为支付完成
        * */
        TOrderInfoVO tOrderInfo = new TOrderInfoVO();
        tOrderInfo.setId(tOrderInfoDB.getId());
        tOrderInfo.setOrderPayStatus("M090");
        tOrderInfoMapper.updateByPrimaryKeySelective(tOrderInfo);


        /*运单执行状态子表*/
        ArrayList<String> arrayList = new ArrayList<>();
        arrayList.add("S1301");
        TOrderState tOrderStateDB = tOrderStateMapper.selectOrderStateByOrderCode(arrayList, tOrderInfoDB.getCode());
//        List<TOrderState> tOrderStateDB =tOrderStateMapper.selectByOrderCodeAndS1301(tOrderInfoDB.());
        /*
        if (tOrderStateDB==null || tOrderStateDB.size()==0){
            return ResultUtil.error("当前运单查询不到运单执行状态表");
        }else if (tOrderStateDB.size()>1){
            return ResultUtil.error("异常：当前运单执行状态表TX数据为多条");
        }*/
        if (tOrderStateDB==null){
            return ResultUtil.error("当前运单支付状态表，找不到状态为S130为已提现的记录");
        }
        TOrderState tOrderState = new TOrderState();
        tOrderState.setId(tOrderStateDB.getId());
        tOrderInfo.setEnable(true);
        tOrderInfo.setCode(tOrderStateDB.getCode()+"1");
        tOrderStateMapper.updateByPrimaryKeySelective(tOrderState);


        /*虚拟钱包表,钱包变动*/
       TWallet tWalletDB=tWalletMapper.selectByPrimaryKey(walletId);
       if (tWalletDB==null){
           return ResultUtil.error("当前运单查询不到钱包记录");
       }


       //查询当前钱包 提现记录
       List<TWalletChangeLog> tWalletChangeLogDBList=tWalletChangeLogMapper.selectByWalletId(tWalletDB.getId());

       if (tWalletChangeLogDBList==null){
           return ResultUtil.error("当前运单找不到，资金变动记录提现记录");
       }else if (tWalletChangeLogDBList.size()>1){
           return ResultUtil.error("资金回退暂不支持，多比提现");
       }
       TWalletChangeLog tWalletChangeLogDB=tWalletChangeLogDBList.get(0);

       List<Map> account_list = (List<Map>) WSMap.get("account_list");
       if (account_list!=null){
           Map map = account_list.get(0);
           Double available__balance = (Double) map.get("available_balance");
           Double balance = (Double) map.get("balance");
           // todo 运费减去服务费
           if (tWalletDB.getAccountBalance().compareTo(new BigDecimal(balance))!=-1){
               return ResultUtil.error("网商余额等于或小于钱包余额");
           }

       }else {
          return resultUtil;
       }

       /*修改资金变动表将提现记录修改为无效，将支付记录修改为有效
       /*资金变动表 提现金额*/
       //并将钱包流水表的提现流水置为无效
       BigDecimal tWalletChangeLogAccount = tWalletChangeLogDB.getAmount();
       TWalletChangeLog tWalletChangeLog = new TWalletChangeLog();
       tWalletChangeLog.setId(tWalletChangeLogDB.getId());
       tWalletChangeLog.setEnable(true);
       tWalletChangeLogMapper.updateByPrimaryKeySelective(tWalletChangeLog);

       //钱包根据资金流水,恢复资金
       TWallet tWallet = new TWallet();
       tWallet.setId(tWalletDB.getId());
       tWallet.setAccountBalance(tWalletDB.getAccountBalance().add(tWalletChangeLogDB.getAmount()));
       tWalletMapper.updateByPrimaryKeySelective(tWallet);

       return ResultUtil.ok("资金回退,数据重置成功");
     }

    /**
     *  @author: dingweibo
     *  @Date: 2020/7/20 10:27
     *  @Description: 查询车辆状态
     */
    @Override
    public ResultUtil checkTruckExist(TtoolboxVo ttoolboxVo) {
        try{
            if(ttoolboxVo.getVehicleNumber()==null || "".equals(ttoolboxVo.getVehicleNumber())){
                return ResultUtil.error("请输入车牌号");
            }
            TEndCarInfo tEndCarInfo = tEndCarInfoAPI.selectByVehicleNumberFeign(ttoolboxVo.getVehicleNumber());
            ResultUtil resultUtil = trajectoryRecentAPI.checkTruckExistV2(ttoolboxVo.getVehicleNumber()+"_"+tEndCarInfo.getLicensePlateColor());
            if (resultUtil.getMsg().equals("no")) {
                return ResultUtil.ok("未入网");
            }else{
                return ResultUtil.ok("已入网");
            }
        }catch (Exception e){
            log.error("查询该车辆入网失败！",e);
            return  ResultUtil.error("查询该车辆入网失败");
        }
    }

    @Override
    public TransTimeManageVResp vLastLocation(String vln) {
        TransTimeManageVResp resp = new TransTimeManageVResp();
        try{
            TEndCarInfo tEndCarInfo = tEndCarInfoAPI.selectByVehicleNumberFeign(vln);
            resp = trajectoryRecentAPI.transTimeManageV3One(vln+"_"+tEndCarInfo.getLicensePlateColor(),24);
            if("1006".equals(resp.getStatus())){
                resp.setCode("success");
                resp.setMsg("当前车辆无轨迹");
            }else if("1001".equals(resp.getStatus())){
                if(resp.getLat()!=null &&!"".equals(resp.getLat())){
                    resp.setLat(String.valueOf(Double.parseDouble(resp.getLat())/600000));
                }
                if(resp.getLon()!=null &&!"".equals(resp.getLon())){
                    resp.setLon(String.valueOf(Double.parseDouble(resp.getLon())/600000));
                }
                resp.setCode("success");
                resp.setMsg("成功");
            }else{
                resp.setStatus("0");
                resp.setCode("success");
                resp.setMsg("车辆无轨迹");
            }
            return resp;
        }catch (Exception e){
            log.error("查询车辆最新轨迹失败！",e);
            resp.setCode("error");
            resp.setMsg("查询车辆最新轨迹失败");
            return resp;
        }
    }

    @Override
    public ResultUtil balanceAccount(PaymentRequest paymentRequest) {
        return paymentAPI.balanceAccount(paymentRequest);
    }

    @Override
    public ResultUtil ImportExcel(TtoolboxVo ttoolboxVo){
        for(TOrderShExcel tOrderSh : ttoolboxVo.getOrderList()){
            TOrderAuditLog audit = new TOrderAuditLog();
            TOrderInfo orderInfo = orderInfoService.selectByOrderBusinessCode(tOrderSh.getOrderBusinessCode());
            List<TOrderAuditLog> exist = orderAuditLogMapper.orderJudgeAuditExist(orderInfo.getCode());
            Integer sysUserId = CurrentUser.getCurrentUserID();
            audit.setAuditor(String.valueOf(sysUserId));
            audit.setAuditTime(new Date());
            audit.setOrderCode(orderInfo.getCode());
            audit.setOrderBusinessCode(orderInfo.getOrderBusinessCode());
            audit.setAuditRemark(tOrderSh.getAuditOpinion());
            audit.setAuditStatus(ttoolboxVo.getAuditStatus());
            audit.setUpdateTime(new Date());
            if (exist.size() == 0) {
                String code = IdWorkerUtil.getInstance().nextId();
                audit.setCode(code);
                orderAuditLogMapper.insertSelective(audit);
            } else {
                orderAuditLogMapper.updateByOrderCodeSelective(audit);
            }
        }
        return ResultUtil.ok("导入成功");
    }

    @Override
    public ResultUtil ydhtqs(Map<String, Object> map) throws Exception {

        String host = "https://esign.hl.chinamobile.com/anysign/api/";
        String path = "/api-openapi/openApi/auth/getToken";
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        Map<String, Object> querys = new HashMap<>();
        querys.put("appKey","a00ee92959764ddc8e62c9cb55bee948");
        HttpResponse response = HttpUtils.doGet(host, path,"GET" , headers, querys);
        log.info("回参Str：{}", response);
        //获取response的body
        String json = HttpUtils.bytesToString(HttpUtils.getData(response.getEntity()));
        log.info("回参Str：{}", json);
        ResultData jso =  JSON.parseObject(json, ResultData.class);
        log.info("回参：{}", JSON.toJSONString(jso));

        return ResultUtil.ok(jso);
    }

    private ResultUtil createUser(String token,Map<String,String> map) throws Exception {

        String host = "https://esign.hl.chinamobile.com/anysign/api/";
        String path = "/api-openapi/openApi/user/createUser";
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        Map<String, Object> querys = new HashMap<>();

        String req = "";
        log.info("etc运单结束入参：{}", JSON.toJSONString(req));
        HttpResponse response = HttpUtils.doPost(host, path, headers, querys, map);

        //获取response的body
        String json = HttpUtils.bytesToString(HttpUtils.getData(response.getEntity()));
        ResultData jso =  JSON.parseObject(json, ResultData.class);
        log.info("etc运单结束回参：{}", JSON.toJSONString(jso));
        return null;
    }

}
