package com.lz.service.impl.hxyh;

import cn.hutool.json.JSONUtil;
import com.lz.api.MqAPI;
import com.lz.common.config.HXPropertiesConfig;
import com.lz.common.constants.HXMqMessageTag;
import com.lz.common.constants.HXMqMessageTopic;
import com.lz.common.dbenum.BusinessCode;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.HXTradeTypeEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.MQMessage;
import com.lz.common.util.*;
import com.lz.dao.*;
import com.lz.dto.TZtBankCardDTO;
import com.lz.enums.HXPaymentEnums;
import com.lz.model.*;
import com.lz.payment.content.HXPayStrategyContent;
import com.lz.payment.content.PayStrategyContent;
import com.lz.payment.hxyh.HXPayOrderUtil;
import com.lz.payment.hxyh.HXPaymentUtil;
import com.lz.payment.hxyh.HXWalletUtil;
import com.lz.service.hxyh.THXOrderPayService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.vo.HxOrderTxVO;
import com.lz.vo.NodePayVO;
import com.lz.vo.hxyh.HXOrderPayVO;
import commonSdk.requestModel.CustomerWithdrawRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 京东运单支付
 * @date 2021/8/12 15:27
 */
@Slf4j
@Service
public class THXOrderPayServiceImpl implements THXOrderPayService {

    @Resource
    private THXOrderInfoMapper hxOrderInfoMapper;

    @Autowired
    private TOrderCastChangesMapper orderCastChangesMapper;

    @Autowired
    private TOrderCastCalcSnapshotMapper orderCastCalcSnapshotMapper;

    @Autowired
    private TOrderStateMapper orderStateMapper;

    @Autowired
    private TOrderPackInfoMapper orderPackInfoMapper;

    @Resource
    private THxPayBankCardMapper hxPayBankCardMapper;

    @Resource
    private TOrderPayRuleMapper orderPayRuleMapper;

    @Autowired
    private HXPaymentUtil hxPaymentUtil;

    @Autowired
    private HXWalletUtil hxWalletUtil;

    @Autowired
    private HXPayStrategyContent hxPayStrategyContent;

    @Autowired
    HXPropertiesConfig hxPropertiesConfig;

    @Autowired
    private SysParamAPI sysParamAPI;

    @Autowired
    private MqAPI mqAPI;



    /**
     *  @author: dingweibo
     *  @Date: 2022/11/24 10:01
     *  @Description: 单笔支付
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil pay(HXOrderPayVO payVO) {
        TOrderInfo orderInfo = hxOrderInfoMapper.selectByPrimaryKey(payVO.getId());

        //TODO 修改运单主表状态
        // 判断是否手动修改运费
        whetherChangeUserConfirmCarriagePayment(payVO);
        TOrderInfo forUpdateOrder = new TOrderInfo();
        forUpdateOrder.setId(payVO.getId());
        //运单状态：支付处理中
        forUpdateOrder.setOrderPayStatus(DictEnum.P070.code);
        orderInfo.setOrderPayStatus(DictEnum.P070.code);
        if (StringUtils.isNotEmpty(payVO.getRemark())) {
            forUpdateOrder.setUserConfirmPaymentAmount(payVO.getNewUserConfirmCarriagePayment());
            forUpdateOrder.setParam2(payVO.getRemark());
            orderInfo.setUserConfirmPaymentAmount(payVO.getNewUserConfirmCarriagePayment());
            orderInfo.setParam2(payVO.getRemark());
        }
        if (null != payVO.getServiceFee()) {
            orderInfo.setServiceFee(payVO.getServiceFee());
            forUpdateOrder.setServiceFee(payVO.getServiceFee());
        }
        if (null != payVO.getUserConfirmServiceFee()) {
            orderInfo.setUserConfirmServiceFee(payVO.getUserConfirmServiceFee());
            forUpdateOrder.setUserConfirmServiceFee(payVO.getUserConfirmServiceFee());
        }
        //如果自动到卡，设置选择的银行卡
        TOrderCastChanges tOrderCastChanges = orderCastChangesMapper.selectByNewOne(orderInfo.getCode());
        if (DictEnum.AUTOMATION.code.equals(tOrderCastChanges.getWithdrawType())) {
            orderInfo.setParam3(String.valueOf(payVO.getBankId()));
            forUpdateOrder.setParam3(String.valueOf(payVO.getBankId()));
        }
        hxOrderInfoMapper.updateByPrimaryKeySelective(forUpdateOrder);
        // 添加支付处理中子状态
        insertOrderState(orderInfo.getCode(), DictEnum.SP0701.code, "", "");

        try {
            hxPayStrategyContent.toPay(HXPaymentEnums.HX_PAY, orderInfo);
        } catch (Exception e) {
            log.error("ZJJ-201:支付失败!", e);
            String message = e.getMessage();
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再支付。";
            }
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再支付";
            }
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            }
            throw new RuntimeException("ZJJ-201:支付失败!");
        }
        return ResultUtil.ok();
    }

    /**
     * @description 批量支付
     * <AUTHOR>
     * @date 2021/8/13 11:33
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil batchPay(TOrderInfo orderInfo) {
        updateOrderInfo(orderInfo.getId(), DictEnum.P070.code);
        // 添加运单执行状态：支付处理中P070
        insertOrderState(orderInfo.getCode(), DictEnum.SP0701.code, "", "");

        //如果自动到卡，设置选择的银行卡
        TOrderCastChanges tOrderCastChanges = orderCastChangesMapper.selectByNewOne(orderInfo.getCode());
        if (DictEnum.AUTOMATION.code.equals(tOrderCastChanges.getWithdrawType())) {
            TOrderInfo orderInfoForUpdate = new TOrderInfo();
            orderInfoForUpdate.setId(orderInfo.getId());
            orderInfoForUpdate.setParam3(orderInfo.getParam3());
            hxOrderInfoMapper.updateByPrimaryKeySelective(orderInfoForUpdate);
        }
        try {
            hxPayStrategyContent.toPay(HXPaymentEnums.HX_PAY, orderInfo);
        } catch (Exception e) {
            log.error("ZJJ-202:支付失败!", e);
            String message = e.getMessage();
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再支付。";
            }
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再支付";
            }
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            }
            throw new RuntimeException("ZJJ-202:支付失败!");
        }


        return ResultUtil.ok();
    }

    /**
     * @description 节点支付
     * <AUTHOR>
     * @date 2021/8/13 11:33
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil nodePay(NodePayVO payVO) {
        // 新增运单执行状态子表
        //节点：会计、收单员已点击支付
        // 根据当前登录人的线路角色，会计、收单员
        String stateNodeValue;
        if (payVO.getPayNodeType().equals(DictEnum.ZHPAYNODE.code)) {
            stateNodeValue = DictEnum.SP0706.code;
        } else if (payVO.getPayNodeType().equals(DictEnum.XHPAYNODE.code)) {
            stateNodeValue = DictEnum.SP0708.code;
        } else if (payVO.getPayNodeType().equals(DictEnum.SDPAYNODE.code)) {
            stateNodeValue = DictEnum.SP0710.code;
        } else {
            stateNodeValue = DictEnum.SP0701.code;
        }
        insertOrderState(payVO.getCode(), stateNodeValue, "", "");

        try {
            //TODO 支付
            hxPayStrategyContent.toNodePay(HXPaymentEnums.HX_NODEPAY, payVO);
            // 修改节点支付规则 param1 为京东支付平台
            orderPayRuleMapper.updatePaymentPlatformsByOrderCode(DictEnum.HXPLATFORMS.code, payVO.getCode());
        } catch (Exception e) {
            log.error("ZJJ-231:支付失败!", e);
            String message = e.getMessage();
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再支付。";
            }
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再支付";
            }
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            }
            throw new RuntimeException("ZJJ-231:支付失败!");
        }
        return ResultUtil.ok();
    }

    /**
     * @description 打包支付
     * <AUTHOR>
     * @date 2021/8/13 11:34
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil packPay(String packCode) {
        try {
            TOrderPackInfo orderPackInfo = orderPackInfoMapper.selectOrderPackByCode(packCode);
            if (null != orderPackInfo) {
                List<String> orderCodes = orderPackInfoMapper.selectOrderCodeByPackCode(orderPackInfo.getCode());
                //修改打包主表状态: 处理中
                TOrderPackInfo packInfo = new TOrderPackInfo();
                packInfo.setId(orderPackInfo.getId());
                packInfo.setPackStatus(DictEnum.PACKEDHANDEL.code);
                // 记录支付时间
                packInfo.setParam1(String.valueOf(System.currentTimeMillis()));
                orderPackInfoMapper.updateByPrimaryKeySelective(packInfo);
                //修改运单主表支付状态：支付处理中
                hxOrderInfoMapper.batchUpdateOrderPayStatus(orderCodes, DictEnum.P070.code);
                //添加运单执行状态子表
                batchInsertOrderState(orderCodes, DictEnum.SP0701.code, "");
                for (String orderCode : orderCodes) {
                    try {
                        TOrderInfo tOrderInfo = hxOrderInfoMapper.selectOrderByCode(orderCode);
                        hxPayStrategyContent.toPay(HXPaymentEnums.HX_DBPAY, tOrderInfo);
                    } catch (Exception e) {
                        log.error("ZJJ-006:打包支付失败!", e);
                        String message = e.getMessage();
                        if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                            message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再支付。";
                            throw new RuntimeException(message);
                        }
                        if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                            message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再支付";
                            throw new RuntimeException(message);
                        }
                        throw new RuntimeException("ZJJ-006:打包支付失败!");
                    }
                }
            } else {
                log.error("未找到打包运单");
            }
        } catch (Exception e) {
            log.error("ZJJ-006:打包支付失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            }
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再支付。";
                throw new RuntimeException(message);
            }
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再支付";
                throw new RuntimeException(message);
            }
            throw new RuntimeException("ZJJ-006:打包支付失败!");
        }

        return ResultUtil.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil refund(String orderCode, String remark) {
        try {
            TOrderInfo orderInfo = hxOrderInfoMapper.selectOrderByCode(orderCode);
            // 运单完成且支付完成
            if ((orderInfo.getOrderExecuteStatus().equals(DictEnum.M100.code) && orderInfo.getOrderPayStatus().equals(DictEnum.M090.code))
                    || orderInfo.getOrderPayStatus().equals(DictEnum.M120.code)) {
                // 修改运单主表状态：P070 入账处理中；
                updateOrderInfo(orderInfo.getId(), DictEnum.P070.code);
                // 添加运单执行状态子表：
                String usertype = CurrentUser.getUsertype();
                String orderStateNode = "";
                if (StringUtils.isNotEmpty(usertype)) {
                    if (DictEnum.BD.code.equals(usertype)) {
                        //会计点击召回
                        orderStateNode = DictEnum.SP0704.code;
                    }
                } else {
                    //运营点击召回
                    orderStateNode = DictEnum.SP0705.code;
                }
                insertOrderState(orderCode, orderStateNode, remark,  "");
                hxPayStrategyContent.toRefund(HXPaymentEnums.HX_REFUND, orderInfo);
                return ResultUtil.ok();
            }
        } catch (Exception e) {
            log.error("ZJJ-220:支付失败！京东单笔运单召回失败, {}", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("ZJJ-220:支付失败！");
            }
        }
        return ResultUtil.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil packRefund(String packCode, String remark) {
        try {
            List<String> orderCodes = orderPackInfoMapper.selectOrderCodeByPackCode(packCode);
            for (String orderCode : orderCodes) {
                TOrderInfo orderInfo = hxOrderInfoMapper.selectOrderByCode(orderCode);
                // 运单完成且支付完成
                if ((orderInfo.getOrderExecuteStatus().equals(DictEnum.M100.code) && orderInfo.getOrderPayStatus().equals(DictEnum.M090.code))
                        || orderInfo.getOrderPayStatus().equals(DictEnum.M120.code)) {
                    // 修改运单主表状态：P070 入账处理中；
                    updateOrderInfo(orderInfo.getId(), DictEnum.P070.code);
                    // 添加运单执行状态子表：
                    String usertype = CurrentUser.getUsertype();
                    String orderStateNode = "";
                    if (StringUtils.isNotEmpty(usertype)) {
                        if (DictEnum.BD.code.equals(usertype)) {
                            //会计点击召回
                            orderStateNode = DictEnum.SP0704.code;
                        }
                    } else {
                        //运营点击召回
                        orderStateNode = DictEnum.SP0705.code;
                    }
                    insertOrderState(orderCode, orderStateNode, remark,  "");
                }
            }
            // 修改打包主表状态
            orderPackInfoMapper.updatePayStatusByOrderCode(packCode, DictEnum.PACKRECALLPROCESSED.code);
            TOrderPackInfo tOrderPackInfo = orderPackInfoMapper.selectOrderPackByCode(packCode);
            hxPayStrategyContent.toPackRefund(HXPaymentEnums.HX_PACKREFUND, tOrderPackInfo);
            return ResultUtil.ok();
        } catch (Exception e) {
            log.error("ZJJ-301:运单召回失败！{}", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("ZJJ-301:运单召回失败！");
            }
        }
    }

    /**
     * 修改运单主表
     * @param orderId
     * @param orderPayStatus
     */
    private void updateOrderInfo(Integer orderId, String orderPayStatus) {
        TOrderInfo orderInfo = new TOrderInfo();
        orderInfo.setId(orderId);
        orderInfo.setOrderPayStatus(orderPayStatus);
        orderInfo.setUpdateUser(CurrentUser.getUserNickname());
        orderInfo.setUpdateTime(new Date());
        hxOrderInfoMapper.updateByPrimaryKeySelective(orderInfo);
    }

    /**
     * @param orderCode      运单code
     * @param orderStateNode 子状态
     * @param operateMethod
     * @description 新增运单执行状态子表
     * <AUTHOR>
     * @date 2022/11/24 10:02
     */
    private void insertOrderState(String orderCode, String orderStateNode, String remark, String operateMethod) {
        TOrderState orderState = new TOrderState();
        orderState.setCode(IdWorkerUtil.getInstance().nextId());
        orderState.setOrderCode(orderCode);
        orderState.setStateNodeValue(orderStateNode);
        orderState.setOperateTime(new Date());
        //TODO 操作方式
        orderState.setOperateMethod(operateMethod);
        orderState.setOperatorId(CurrentUser.getCurrentUserID());
        orderState.setEnable(false);
        orderState.setIfExpire(false);
        orderState.setRemark(remark);
        orderState.setCreateUser(CurrentUser.getUserNickname());
        orderState.setUpdateUser(CurrentUser.getUserNickname());
        orderStateMapper.insertSelective(orderState);
    }

    /**
     * @description 批量新增运单执行状态子表
     * <AUTHOR>
     * @date 2021/8/12 15:26
     */
    private void batchInsertOrderState(List<String> orderCodes, String orderStateNode, String operateMethod) {
        if (null != orderCodes && !orderCodes.isEmpty()) {
            orderCodes.forEach((code) -> insertOrderState(code, orderStateNode, "",  operateMethod));
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/24 10:01
     *  @Description: 判断是否手动修改运费
     */
    private void whetherChangeUserConfirmCarriagePayment(HXOrderPayVO payVO) {
        TOrderCastCalcSnapshot orderCastCalcSnapshot = orderCastCalcSnapshotMapper.selectSnapsByOrderCode(payVO.getCode());
        String remark = "";
        if (null != payVO.getNewUserConfirmCarriagePayment() && null != orderCastCalcSnapshot.getUserConfirmCarriagePayment()) {
            if (payVO.getNewUserConfirmCarriagePayment().compareTo(orderCastCalcSnapshot.getUserConfirmCarriagePayment()) != 0) {
                remark = "用户" + CurrentUser.getUserNickname()
                        + "将运费由" + orderCastCalcSnapshot.getUserConfirmCarriagePayment()
                        + "改为" + payVO.getNewUserConfirmCarriagePayment();
                orderCastCalcSnapshot.setUserConfirmCarriagePayment(payVO.getNewUserConfirmCarriagePayment());
                orderCastCalcSnapshot.setParam2(remark);
                orderCastCalcSnapshotMapper.updateByPrimaryKeySelective(orderCastCalcSnapshot);
                payVO.setRemark(remark);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil tx(HxOrderTxVO hxOrderTxVO) {
        try {
            // 查询C端绑卡华夏开户绑卡信息
            TZtBankCardDTO ztBankCardDTO = hxPayBankCardMapper.selectOpenRoleBankCardInfo(hxOrderTxVO.getBankCardId());

            // 修改钱包
            TZtWallet ztWallet = hxWalletUtil.selectByEnduserId(CurrentUser.getEndUserId());
            if (null == ztWallet) {
                throw new RuntimeException("ZJJ-600:提现失败！京东钱包不存在，请联系运营平台予以解决。");
            }
            hxWalletUtil.txApplyModifyWallet(ztWallet.getId(), hxOrderTxVO.getTotalFee());

            String orderPayInfoCode = hxPaymentUtil.hxcreateOrderPayInfo(hxOrderTxVO.getTotalFee(), DictEnum.P110.code);
            String orderPayDetailCode = hxPaymentUtil.createTxOrderPayDetail(orderPayInfoCode, HXTradeTypeEnum.TX.code,
                    ztWallet.getId(), CurrentUser.getEndUserId(), new Date(), ztBankCardDTO);
            // 发起提现请求
            CustomerWithdrawRequest req = new CustomerWithdrawRequest();
            req.setRequestId(IdWorkerUtil.getInstance().nextId());
            req.setRequestTime(DateUtils.getRequestTime());
            req.setPartnerId(hxPropertiesConfig.getPartnerId());
            req.setChannelId(hxPropertiesConfig.getChannelId());
            req.setBizOrderNo(orderPayDetailCode);
            req.setCardId(ztBankCardDTO.getBankNo());
            req.setPartnerAccId(ztBankCardDTO.getPartnerAccId());
            req.setOrderAmount(hxOrderTxVO.getTotalFee());
            if (null != hxOrderTxVO.getTradeAbstract()) {
                req.setTradeAbstract(hxOrderTxVO.getTradeAbstract());
            } else {
                req.setTradeAbstract("提现");
            }
            SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.HXWITHDRAWURL.code);
            req.setNotifyUrl(paramByKey.getParamValue());
            MQMessage message = new MQMessage();
            message.setTopic(HXMqMessageTopic.HX_WITHDRAW);
            message.setTag(HXMqMessageTag.HX_WITHDRAWAPPLY);
            message.setKey(orderPayDetailCode);
            message.setBody(req);
            log.info("发送提现申请MQ消息, {}", JSONUtil.toJsonStr(message));
            ResultUtil resultUtil = mqAPI.sendMessage(message);
            log.info("发送提现申请MQ消息结果，{}", JSONUtil.toJsonStr(resultUtil));
            if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
                throw new RuntimeException("ZJJ-600:提现失败！");
            }
        } catch (Exception e) {
            log.error("ZJJ-600:提现失败！", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            }
            throw new RuntimeException("ZJJ-600:提现失败！");
        }
        return ResultUtil.ok();
    }
}
