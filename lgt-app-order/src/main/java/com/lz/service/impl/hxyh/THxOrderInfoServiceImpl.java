package com.lz.service.impl.hxyh;

import com.lz.dao.TAdvanceOrderPayTmpMapper;
import com.lz.dao.THXOrderInfoMapper;
import com.lz.dao.THXOrderPayDetailMapper;
import com.lz.dto.*;
import com.lz.model.TOrderPayDetail;
import com.lz.service.hxyh.THxOrderInfoService;
import com.lz.vo.TAdvanceOrderTmpExcel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class THxOrderInfoServiceImpl implements THxOrderInfoService {

    @Resource
    private THXOrderInfoMapper hxOrderInfoMapper;

    @Resource
    private THXOrderPayDetailMapper hxOrderPayDetailMapper;

    @Resource
    private TAdvanceOrderPayTmpMapper advanceOrderPayTmpMapper;
    @Override
    public Map<Integer, Map<String, List<String>>> selectOrderGroupByCompanyId(List<String> orderCodes) {
        List<OrderGroupDTO> orderGroupDTOS = hxOrderInfoMapper.selectOrderGroupByCompanyId(orderCodes);
        Map<Integer, Map<String, List<String>>> mapMap = new HashMap<>();
        orderGroupDTOS.forEach(item -> {
            Map<String, List<String>> listMap = new HashMap<>();
            Integer company_id = item.getCompanyId();
            String codes = item.getCodes();
            String order_business_code = item.getOrderBuisnessCode();
            listMap.put("codes", Arrays.asList(codes.split(",")));
            listMap.put("order_business_code", Arrays.asList(order_business_code.split(",")));
            mapMap.put(company_id, listMap);
        });
        return mapMap;
    }

    @Override
    public OrderInfoDTO selectByCode(String code) {
        // 实体回调
        OrderInfoDTO orderInfoDTO = hxOrderInfoMapper.selectOrderInfoByPayDetailCode(code);
        if (null != orderInfoDTO) {
            return orderInfoDTO;
        }
        // 打包回调
        orderInfoDTO = hxOrderInfoMapper.selectOrderPackInfoByPayDetailCode(code);
        if (null != orderInfoDTO) {
            return orderInfoDTO;
        }
        // 提现转账回调
        orderInfoDTO = hxOrderPayDetailMapper.selectTxServiceTransferDetailByCode(code);
        if (null != orderInfoDTO) {
            return orderInfoDTO;
        }
        // 预付款回调
        THxAdvanceOrderPayTempInfoDTO advanceOrderPayTempInfoDTO = advanceOrderPayTmpMapper.selectHxAdvanceOrderPayTemp(code);
        if (null != advanceOrderPayTempInfoDTO) {
            orderInfoDTO = new OrderInfoDTO();
            orderInfoDTO.setTradeType(advanceOrderPayTempInfoDTO.getTradeType());
            orderInfoDTO.setOperateState(advanceOrderPayTempInfoDTO.getOperateState());
            return orderInfoDTO;
        }
        // 查询支付子表
        // || 1.补交账户服务费交易
        TOrderPayDetail tOrderPayDetail = hxOrderPayDetailMapper.selectByCode(code);
        if (null != tOrderPayDetail) {
            orderInfoDTO = new OrderInfoDTO();
            orderInfoDTO.setCode(code);
            orderInfoDTO.setOperateState(tOrderPayDetail.getOperateState());
            orderInfoDTO.setTradeType(tOrderPayDetail.getTradeType());
            return orderInfoDTO;
        }

        return null;
    }

    @Override
    public Map<Integer, Map<String, List<String>>>  selectAdvanceOrderGroupByCompanyId(List<TAdvanceOrderTmpExcel> list) {
        List<OrderGroupDTO> orderGroupDTOS = hxOrderInfoMapper.selectAdvanceOrderGroupByCompanyId(list);
        Map<Integer, Map<String, List<String>>> mapMap = new HashMap<>();
        orderGroupDTOS.forEach(item -> {
            Map<String, List<String>> listMap = new HashMap<>();
            Integer company_id = item.getCompanyId();
            String codes = item.getCodes();
            String order_business_code = item.getOrderBuisnessCode();
            listMap.put("codes", Arrays.asList(codes.split(",")));
            listMap.put("order_business_code", Arrays.asList(order_business_code.split(",")));
            mapMap.put(company_id, listMap);
        });
        return mapMap;
    }

}
