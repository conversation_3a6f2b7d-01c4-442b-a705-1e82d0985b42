package com.lz.service.impl;

import com.lz.dao.TOrderPackDetailMapper;
import com.lz.model.TOrderInfo;
import com.lz.service.TOrderPackDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
public class TOrderPackDetailServiceImpl implements TOrderPackDetailService {

    @Autowired
    private TOrderPackDetailMapper orderPackDetailMapper;

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public int updateEnableByPackCode(String code) {
        return orderPackDetailMapper.updateEnableByPackCode(code);
    }

    @Override
    public TOrderInfo selectByPackCode(String packCode) {
        List<TOrderInfo> list = orderPackDetailMapper.selectByPackCode(packCode);
        return list.get(0);
    }
}
