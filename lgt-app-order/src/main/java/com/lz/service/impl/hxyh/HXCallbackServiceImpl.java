package com.lz.service.impl.hxyh;

import cn.hutool.json.JSONUtil;
import com.lz.api.MqAPI;
import com.lz.common.config.HXPropertiesConfig;
import com.lz.common.config.RedisUtil;
import com.lz.common.constants.HXMqMessageTag;
import com.lz.common.constants.HXMqMessageTopic;
import com.lz.common.constants.MqMessageTag;
import com.lz.common.constants.MqMessageTopic;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.HXTradeTypeEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.MQMessage;
import com.lz.common.model.hxPayment.request.pay.CustomerBalancePayReq;
import com.lz.common.model.hxPayment.response.CustomerBalancePayRes;
import com.lz.common.model.hxPayment.response.CustomerWithdrawRes;
import com.lz.common.util.DateUtils;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.common.util.ThrowableUtil;
import com.lz.dao.*;
import com.lz.dto.THxAdvanceOrderPayTempInfoDTO;
import com.lz.dto.THxOrderPayInfoDTO;
import com.lz.dto.TTxOrderPayDetailDTO;
import com.lz.dto.TZtBankCardDTO;
import com.lz.model.*;
import com.lz.payment.TradeType;
import com.lz.payment.hxyh.HXPayOrderUtil;
import com.lz.payment.hxyh.HXPaymentUtil;
import com.lz.payment.hxyh.HXWalletUtil;
import com.lz.service.TOrderAnHuiReportService;
import com.lz.service.TOrderContractService;
import com.lz.service.TOrderKhyReportService;
import com.lz.service.hxyh.HXCallbackService;
import com.lz.service.hxyh.THxPayBankCardService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.vo.AccountServiceFailedRecordVO;
import commonSdk.requestModel.CustomerWithdrawRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdk.model.message.AsyncNotifyVirtualBalancePayMessageBody;
import sdk.model.message.AsyncNotifyVirtualMemberWithdrawMessageBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 华夏支付回调服务类
 */
@Slf4j
@Service
public class HXCallbackServiceImpl implements HXCallbackService {

    private static final String PAY_REQUEST_KEY = "PAY_REQUEST_KEY";

    private static final String PAY_REQUEST_KEY_OF_ORDER_ID = "PAY_REQUEST_KEY_OF_ORDER_ID";

    @Resource
    private TOrderInfoMapper orderInfoMapper;

    @Autowired
    private TOrderPackInfoMapper orderPackInfoMapper;

    @Resource
    private TOrderPayInfoMapper orderPayInfoMapper;

    @Resource
    private THXOrderPayDetailMapper hxOrderPayDetailMapper;

    @Resource
    private TOrderPayRuleMapper orderPayRuleMapper;

    @Resource
    private TOrderMqMessageMapper orderMqMessageMapper;

    @Resource
    private TOrderServicefeeRecordMapper orderServicefeeRecordMapper;

    @Autowired
    private THxPayBankCardService hxPayBankCardService;

    @Autowired
    private TOrderContractService orderContractService;

    @Autowired
    private TOrderKhyReportService khyReportService;

    @Resource
    private HXPaymentUtil hxPaymentUtil;

    @Resource
    private HXWalletUtil hxWalletUtil;

    @Autowired
    private HXPayOrderUtil hxPayOrderUtil;

    @Autowired
    private SysParamAPI sysParamAPI;

    @Autowired
    private MqAPI mqAPI;

    @Autowired
    private HXPropertiesConfig hxPropertiesConfig;

    @Resource
    private THxPayBankCardMapper hxPayBankCardMapper;

    @Autowired
    private TOrderAnHuiReportService tOrderAnHuiReportService;

    @Resource
    private RedisUtil redisUtil;

    /**
     * 余额支付回调
     *
     * @description
     * <AUTHOR>
     * @date 2021/8/23 09:40
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil singleBalancePayNotice(AsyncNotifyVirtualBalancePayMessageBody messageBody) {
        Date returnTime = new Date();
        THxOrderPayInfoDTO dto = hxOrderPayDetailMapper.selectByOrderPayDetailKey(messageBody.getBizOrderNo());
        log.info("华夏余额支付回调，支付子表dto信息, {}", JSONUtil.toJsonStr(dto));
        if (DictEnum.PAY_SUCC.code.equals(messageBody.getOrderStatus())) {
            // 修改支付子表，添加回调结果、时间
            hxPaymentUtil.updateOrderPayDetail(dto.getOrderPayDetailId(), DictEnum.TRADE_FINISHED.code, returnTime);
            // 修改钱包
            hxWalletUtil.callbackModifyWallet(dto);
            // 记录钱包流水
            hxPaymentUtil.insertWalletLog(messageBody, dto);
            // 企业余额支付回调
            if (HXTradeTypeEnum.HX_COMBALANCE_PAY.code.equals(dto.getTradeType())) {
                return COMBalancePayNotice(dto);
            }
            // 承运方余额支付回调
            if (HXTradeTypeEnum.HX_CABALANCE_PAY.code.equals(dto.getTradeType())) {
                return CABalancePayNotice(dto, returnTime);
            }
            // 司机余额支付回调
            if (HXTradeTypeEnum.HX_CDBALANCE_PAY.code.equals(dto.getTradeType())) {
                callbackSuccess(dto, returnTime);
            }
            // 经纪人模式，司机支付到车队长
            if (HXTradeTypeEnum.HX_CCBALANCE_PAY.code.equals(dto.getTradeType())) {
                return CCBalancePayNotice(dto);
            }
            // 经纪人车队长模式， 司机支付到经纪人
            if (HXTradeTypeEnum.HX_CMBALANCE_PAY.code.equals(dto.getTradeType())) {
                return callbackSuccess(dto, returnTime);
            }
            // 保费回调
            if (HXTradeTypeEnum.HX_INSURANCE.code.equals(dto.getTradeType())) {
                // 记录支付成功
                hxPaymentUtil.updateInsuranceStatus(dto, 1);
            }
        } else {
            // 支付失败
            // 不是支付保险
            if (!HXTradeTypeEnum.HX_INSURANCE.code.equals(dto.getTradeType())) {
                hxPaymentUtil.updateOrderInfo(dto.getOrderId(), DictEnum.O060.code, DictEnum.M080.code, returnTime);
                hxPaymentUtil.insertOrderState(dto.getOrderCode(), DictEnum.S0800.code, returnTime);
                hxPaymentUtil.updateOrderPayInfo(dto.getOrderPayId(), DictEnum.M080.code, returnTime);
            } else {
                // 支付保险失败，修改保险状态
                hxPaymentUtil.updateInsuranceStatus(dto, 2);
            }
            // 修改支付子表
            hxPaymentUtil.updateFailOrderPayDetail(dto.getOrderPayDetailId(), messageBody.getResponseDesc(), messageBody.getResponseCode(), returnTime);
            // 恢复钱包
            hxWalletUtil.failCallbackModifyWallet(dto);
        }
        return ResultUtil.ok();
    }

    /**
     * @description 企业余额支付回调
     * <AUTHOR>
     * @date 2021/8/16 20:15
     */
    public ResultUtil COMBalancePayNotice(THxOrderPayInfoDTO dto) {
        // 发起承运方余额支付请求
        // 创建支付子表
        String orderPayDetailCode = hxPaymentUtil.createTxOrderPayDetail(dto.getPayCode(),
                dto.getOrderCastChangeCode(), HXTradeTypeEnum.HX_CABALANCE_PAY.code, TradeType.RZ.code);
        // 创建承运方余额支付请求消息，写入MQ
        hxPaymentUtil.requestBalance(HXTradeTypeEnum.HX_CABALANCE_PAY.code, TradeType.RZ.code, dto, orderPayDetailCode);
        return ResultUtil.ok();
    }

    /**
     * @description 承运方余额支付回调
     * <AUTHOR>
     * @date 2021/8/20 10:05
     */
    public ResultUtil CABalancePayNotice(THxOrderPayInfoDTO dto, Date returnTime) {
        // 普通模式 -> 司机
        if (DictEnum.COMMONPATTERN.code.equals(dto.getCapitalTransferPattern())
                && DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())) {
            callbackSuccess(dto, returnTime);
            return ResultUtil.ok();
        }
        // 普通模式 -> 车队长 = 发起余额支付
        // 经纪人模式 -> 司机 = 发起余额支付
        boolean commonCaptain = DictEnum.COMMONPATTERN.code.equals(dto.getCapitalTransferPattern())
                && DictEnum.PAYTOCAPTAIN.code.equals(dto.getCapitalTransferType());
        boolean managerDriver = DictEnum.MANAGERPATTERN.code.equals(dto.getCapitalTransferPattern())
                && DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType());
        if (commonCaptain) {
            // 判断司机和车队长是否同一人
            if (dto.getEndDriverWalletId().equals(dto.getCaptainWalletId())) {
                callbackSuccess(dto, returnTime);
                return ResultUtil.ok();
            } else {
                // 创建支付子表
                String orderPayDetailCode = hxPaymentUtil.createTxOrderPayDetail(dto.getPayCode(),
                        dto.getOrderCastChangeCode(), HXTradeTypeEnum.HX_CDBALANCE_PAY.code, TradeType.RZ.code);
                // 创建司机余额支付请求消息，写入MQ
                hxPaymentUtil.requestBalance(HXTradeTypeEnum.HX_CDBALANCE_PAY.code, TradeType.RZ.code, dto, orderPayDetailCode);
            }
        }
        if (managerDriver) {
            // 创建支付子表
            String orderPayDetailCode = hxPaymentUtil.createTxOrderPayDetail(dto.getPayCode(),
                    dto.getOrderCastChangeCode(), HXTradeTypeEnum.HX_CDBALANCE_PAY.code, TradeType.RZ.code);
            // 创建司机余额支付请求消息，写入MQ
            hxPaymentUtil.requestBalance(HXTradeTypeEnum.HX_CDBALANCE_PAY.code, TradeType.RZ.code, dto, orderPayDetailCode);
        }
        boolean managerCaption = DictEnum.MANAGERPATTERN.code.equals(dto.getCapitalTransferPattern())
                && DictEnum.PAYTOCAPTAIN.code.equals(dto.getCapitalTransferType());
        if (managerCaption) {
            if (dto.getEndDriverWalletId().equals(dto.getCaptainWalletId())) {
                // 判断司机和车队长是同一人
                // 司机支付到经纪人
                // 创建支付子表
                String orderPayDetailCode = hxPaymentUtil.createTxOrderPayDetail(dto.getPayCode(),
                        dto.getOrderCastChangeCode(), HXTradeTypeEnum.HX_CMBALANCE_PAY.code, TradeType.RZ.code);
                // 创建司机余额支付到经纪人
                hxPaymentUtil.requestBalance(HXTradeTypeEnum.HX_CMBALANCE_PAY.code, TradeType.RZ.code, dto, orderPayDetailCode);
            } else {
                // 判断司机和车队长不是同一人
                // 司机支付到车队长
                // 创建支付子表
                String orderPayDetailCode = hxPaymentUtil.createTxOrderPayDetail(dto.getPayCode(),
                        dto.getOrderCastChangeCode(), HXTradeTypeEnum.HX_CCBALANCE_PAY.code, TradeType.RZ.code);
                // 创建司机余额支付到车队长
                hxPaymentUtil.requestBalance(HXTradeTypeEnum.HX_CCBALANCE_PAY.code, TradeType.RZ.code, dto, orderPayDetailCode);
            }

        }
        return ResultUtil.ok();
    }

    private ResultUtil callbackSuccess(THxOrderPayInfoDTO dto, Date returnTime) {
        // 修改运单主表
        hxPaymentUtil.updateOrderInfo(dto.getOrderId(), DictEnum.M100.code, DictEnum.M090.code, returnTime);
        // 修改支付主表
        hxPaymentUtil.updateOrderPayInfo(dto.getOrderPayId(), DictEnum.M090.code, returnTime);
        hxPaymentUtil.insertOrderState(dto.getOrderCode(), DictEnum.S0902.code, returnTime);
        hxPaymentUtil.insertOrderState(dto.getOrderCode(), DictEnum.S1002.code,
                DateUtils.addSeconds(returnTime, 1));
        // 单笔支付
        if (DictEnum.SINGLE.code.equals(dto.getPackStatus())) {
            singleOrderCallback(dto);
        }
        // 打包支付
        if (DictEnum.PACK.code.equals(dto.getPackStatus())) {
            packOrderCallback(dto, returnTime);
        }
        log.info("创建司机收款凭证");
        orderContractService.createHXAnySignSkpz(dto.getOrderCode(), DictEnum.ANYSIGNDRIVERSKPZ.code);

        // 添加快货运运单、支付上报任务
//        try {
//            khyReportService.orderUpload(dto.getOrderId());
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("快货运上报失败, {}", ThrowableUtil.getStackTrace(e));
//        }
//
//        if (DictEnum.SINGLE.code.equals(dto.getPackStatus())) {
//            try {
//                tOrderAnHuiReportService.dataInfoUpload(dto.getOrderId());
//            } catch (Exception e) {
//                e.printStackTrace();
//                log.error("安徽上报失败, {}", e);
//            }
//        }

        return ResultUtil.ok();
    }

    /**
     * 经纪人车队长模式
     * 司机支付车队长回调
     * 发起司机支付到经纪人
     * @param dto
     * @return
     */
    public ResultUtil CCBalancePayNotice(THxOrderPayInfoDTO dto) {
        // 司机支付到经纪人
        // 创建支付子表
        String orderPayDetailCode = hxPaymentUtil.createTxOrderPayDetail(dto.getPayCode(),
                dto.getOrderCastChangeCode(), HXTradeTypeEnum.HX_CMBALANCE_PAY.code, TradeType.RZ.code);
        // 创建司机余额支付到经纪人
        hxPaymentUtil.requestBalance(HXTradeTypeEnum.HX_CMBALANCE_PAY.code, TradeType.RZ.code, dto, orderPayDetailCode);
        return ResultUtil.ok();
    }

    private void singleOrderCallback(THxOrderPayInfoDTO dto) {
        TOrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(dto.getOrderId());
        // 发送结算通知
        hxPaymentUtil.sendRZMessage(orderInfo, dto);
        // 提现金额
        BigDecimal txAmount = dto.getCarriageFee().subtract(dto.getIllegalDeduction());
        if (DictEnum.AUTOMATION.code.equals(dto.getWithdrawType())) {
            try {
                // 创建提现申请
                Integer enduserId = 0;
                String userLogisticsRole = "";
                if (DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())) {
                    enduserId = orderInfo.getEndDriverId();
                    userLogisticsRole = DictEnum.CTYPEDRVIVER.code;
                } else if (DictEnum.PAYTOCAPTAIN.code.equals(dto.getCapitalTransferType())) {
                    enduserId = orderInfo.getEndCarOwnerId();
                    userLogisticsRole = DictEnum.CTYPECAPTAIN.code;
                }
                // 判断是否本人银行卡
                Integer bankCardId = Integer.valueOf(orderInfo.getParam3());
                String openAccountType = DictEnum.MYACCOUNT.code;
                TZtBankCardDTO bankCardDTO = hxPayOrderUtil.checkBankCardOpenStatus(enduserId, bankCardId, userLogisticsRole);
                log.info("提现信息, {}", JSONUtil.toJsonStr(dto));
                BigDecimal txServiceFee = hxPayOrderUtil.getTxServiceFee(txAmount, openAccountType);
                if (txServiceFee.compareTo(txAmount) >= 0) {
                    log.error("无法提现");
                } else if (txServiceFee.compareTo(txAmount) < 0 && txServiceFee.compareTo(BigDecimal.ZERO) >= 0) {
                    TZtWallet tZtWallet = hxWalletUtil.selectByEnduserId(enduserId);
                    // 发起提现申请
                    hxWalletUtil.txApplyModifyWallet(tZtWallet.getId(), txAmount);
                    txApply(null, txAmount, enduserId, bankCardDTO.getBankCardId(), new Date(), tZtWallet, true);
                }
            } catch (Exception e) {
                log.error("自动到卡，发起提现失败, {}", ThrowableUtil.getStackTrace(e));
            }
        }

    }

    private void packOrderCallback(THxOrderPayInfoDTO dto, Date returnTime) {
        TOrderPackInfo tOrderPackInfo = orderPackInfoMapper.selectPackInfoByOneOrderCode(dto.getOrderCode());
        // 处理打包运单回调, 判断所有原始运单回调是否完成，修改打包主表支付状态
        boolean packCallbackFininshed = hxPaymentUtil.packCallbackFininshed(dto, tOrderPackInfo);
        if (packCallbackFininshed) {
            // 修改打包主表状态
            tOrderPackInfo.setPackStatus(DictEnum.PACKPAID.code);
            tOrderPackInfo.setUpdateTime(returnTime);
            orderPackInfoMapper.updateByPrimaryKeySelective(tOrderPackInfo);
            // 发送结算通知
            hxPaymentUtil.sendDBRZMessage(tOrderPackInfo);

            // 提现金额
            BigDecimal txAmount = tOrderPackInfo.getAppointmentPaymentCash();
            if (DictEnum.AUTOMATION.code.equals(dto.getWithdrawType())) {
                try {
                    // 创建提现申请
                    Integer enduserId = tOrderPackInfo.getEndUserId();
                    // 判断是否本人银行卡
                    Integer bankCardId = tOrderPackInfo.getBankCardId();
                    // 获取开户类型
                    String openAccountType = DictEnum.MYACCOUNT.code;
                    String userLogisticsRole = "";
                    if (DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())) {
                        userLogisticsRole = DictEnum.CTYPEDRVIVER.code;
                    } else if (DictEnum.PAYTOCAPTAIN.code.equals(dto.getCapitalTransferType())) {
                        userLogisticsRole = DictEnum.CTYPECAPTAIN.code;
                    }
                    TZtBankCardDTO bankCardDTO = hxPayOrderUtil.checkBankCardOpenStatus(enduserId, bankCardId, userLogisticsRole);
                    log.info("提现信息, {}, 提现到{}", JSONUtil.toJsonStr(dto), openAccountType);
                    BigDecimal txServiceFee = hxPayOrderUtil.getTxServiceFee(txAmount, openAccountType);
                    if (txServiceFee.compareTo(txAmount) >= 0) {
                        log.error("无法提现");
                    } else if (txServiceFee.compareTo(txAmount) < 0 && txServiceFee.compareTo(BigDecimal.ZERO) >= 0) {
                        TZtWallet tztWallet = hxWalletUtil.selectByEnduserId(enduserId);
                        // 发起提现申请
                        hxWalletUtil.txApplyModifyWallet(tztWallet.getId(), txAmount);
                        txApply(null, txAmount, enduserId, bankCardDTO.getBankCardId(), new Date(), tztWallet, true);
                    }
                } catch (Exception e) {
                    log.error("自动到卡，发起提现失败, {}", ThrowableUtil.getStackTrace(e));
                }
            }
        }
    }

    /**
     * @description 节点支付回调
     * <AUTHOR>
     * @date 2021/8/20 15:24
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil nodeBalancePayNotice(AsyncNotifyVirtualBalancePayMessageBody messageBody) {
        Date returnTime = new Date();
        TOrderPayDetail tOrderPayDetail = hxOrderPayDetailMapper.selectByCode(messageBody.getBizOrderNo());
        THxOrderPayInfoDTO dto = hxOrderPayDetailMapper.selectByOrderPayDetailKey(messageBody.getBizOrderNo());
        log.info("华夏节点回调，支付信息, {}", JSONUtil.toJsonStr(dto));
        if (DictEnum.PAY_SUCC.code.equals(messageBody.getOrderStatus())) {
            // 修改支付子表
            hxPaymentUtil.updateOrderPayDetail(tOrderPayDetail.getId(), DictEnum.TRADE_FINISHED.code, returnTime);
            // 修改钱包
            hxWalletUtil.nodeCallbackModifyWallet(dto);
            // 记录钱包流水
            hxPaymentUtil.nodeInsertWalletLog(messageBody, dto);
            // 企业余额支付回调
            if (HXTradeTypeEnum.HX_COMBALANCE_PAY.code.equals(dto.getTradeType())) {
                NodeCOMbalancePayNotice(dto, returnTime);
            }
            // 承运方余额支付回调
            if (HXTradeTypeEnum.HX_CABALANCE_PAY.code.equals(dto.getTradeType())) {
                NodeCAbalancePayNotice(dto, returnTime);
            }
            // 司机余额支付回调
            if (HXTradeTypeEnum.HX_CDBALANCE_PAY.code.equals(dto.getTradeType())) {
                NodeCDbalancePayNotice(dto, returnTime);
            }

            // 经纪人车队长模式，司机支付到经纪人
            if (HXTradeTypeEnum.HX_CMBALANCE_PAY.code.equals(dto.getTradeType())) {
                NodeCMBalancePayNotice(dto, returnTime);
            }

            // 尾款支付回调，查询是否存在收取账户服务费失败的记录，如果存在，则发起补交账户服务费
            if (TradeType.WKPAYNODE.code.equals(dto.getUserOper())) {
                try {
                    log.info("查询是否存在收取账户服务费失败的记录");
                    hxPaymentUtil.checkAccountServiceFeeFaildRecord(dto);
                } catch (Exception e) {
                    log.error("查询是否存在收取账户服务费失败的记录失败, {}", ThrowableUtil.getStackTrace(e));
                }
            }

        } else {
            // 支付失败
            hxPaymentUtil.updateFailOrderPayDetail(dto.getOrderPayDetailId(), messageBody.getResponseDesc(), messageBody.getResponseCode(), returnTime);
            // 不是支付保险
            if (!HXTradeTypeEnum.HX_INSURANCE.code.equals(dto.getTradeType())) {
                BigDecimal amount = dto.getCarriageFee();
                // 修改支付规则
                // 尾款支付
                if (DictEnum.WKPAYNODE.code.equals(dto.getUserOper())) {
                    TOrderInfo orderInfo = orderInfoMapper.selectOrderByCode(dto.getOrderCode());
                    BigDecimal selectSumFee = orderPayRuleMapper.selectSumFee(dto.getOrderCode());
                    amount = orderInfo.getEstimateTotalFee().subtract(selectSumFee);
                    hxPaymentUtil.updateOrderInfo(dto.getOrderId(), DictEnum.O060.code, DictEnum.M080.code, returnTime);
                    hxPaymentUtil.insertOrderState(dto.getOrderCode(), DictEnum.S0800.code, returnTime);
                }
                hxPaymentUtil.failCallbackUpdateOrderPayRule(dto.getOrderCode(), dto.getUserOper(), amount, returnTime);
                // 修改钱包
                hxWalletUtil.failNodePayModifyWallet(dto);
            }
        }

        return ResultUtil.ok();
    }

    /**
     * @description 节点支付回调 - 企业余额支付回调
     * <AUTHOR>
     * @date 2021/8/23 10:33
     */
    private ResultUtil NodeCOMbalancePayNotice(THxOrderPayInfoDTO dto, Date returnTime) {
        // 创建支付子表
        String orderPayDetailCode = hxPaymentUtil.createTxOrderPayDetail(dto.getPayCode(),
                dto.getOrderCastChangeCode(), HXTradeTypeEnum.HX_CABALANCE_PAY.code, TradeType.RZNODE.code);
        // 创建承运方余额支付请求消息，写入MQ
        hxPaymentUtil.requestBalance(HXTradeTypeEnum.HX_CABALANCE_PAY.code, TradeType.RZNODE.code, dto, orderPayDetailCode);
        return ResultUtil.ok();
    }

    /**
     * @description 节点支付回调 - 承运方余额支付回调
     * <AUTHOR>
     * @date 2021/8/23 10:33
     */
    private ResultUtil NodeCAbalancePayNotice(THxOrderPayInfoDTO dto, Date returnTime) {
        // 普通模式 -> 司机
        if (DictEnum.COMMONPATTERN.code.equals(dto.getCapitalTransferPattern())
                && DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())) {
            // 修改节点支付规则表支付状态
            hxPaymentUtil.updateOrderPayRule(dto.getOrderCode(), dto.getUserOper(), DictEnum.PACKPAID.code, returnTime);
            // 获取运单执行状态
            String nodeOrderState = hxPaymentUtil.getNodeOrderState(dto.getUserOper());
            // 插入运单执行状态, 支付完成
            hxPaymentUtil.insertNodeOrderState(dto.getOrderCode(), nodeOrderState, returnTime);
            // 如果是尾款支付
            if (DictEnum.WKPAYNODE.code.equals(dto.getUserOper())) {
                // 处理回调
                hxPaymentUtil.handleNodePayWKCallback(dto, returnTime);
                // 创建收款凭证
                orderContractService.createHXAnySignSkpz(dto.getOrderCode(), DictEnum.ANYSIGNDRIVERSKPZ.code);
            } else {
                // 修改下一支付节点规则数据，准备下一节点支付
                String nextNodeType = hxPaymentUtil.getNextNodeState(dto.getOrderCode(), dto.getUserOper());
                log.info("下一节点, {}", nextNodeType);
                hxPaymentUtil.updateNextNodePay(dto, nextNodeType);
            }
        }
        // 经纪人模式 -> 司机
        if (DictEnum.MANAGERPATTERN.code.equals(dto.getCapitalTransferPattern())
                && DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())) {
            // 尾款支付，发起余额支付： 司机 -> 经纪人
            if (DictEnum.WKPAYNODE.code.equals(dto.getUserOper())) {
                /// 创建支付子表
                String orderPayDetailCode = hxPaymentUtil.createTxOrderPayDetail(dto.getPayCode(),
                        dto.getOrderCastChangeCode(), HXTradeTypeEnum.HX_CDBALANCE_PAY.code, TradeType.RZNODE.code);
                // 创建司机余额支付请求消息，写入MQ
                hxPaymentUtil.requestBalance(HXTradeTypeEnum.HX_CDBALANCE_PAY.code, TradeType.RZNODE.code, dto, orderPayDetailCode);
            } else {
                // 修改节点支付规则表支付状态
                hxPaymentUtil.updateOrderPayRule(dto.getOrderCode(), dto.getUserOper(), DictEnum.PACKPAID.code, returnTime);
                // 获取运单执行状态和下一节点支付类型
                String nextNodeType = hxPaymentUtil.getNextNodeState(dto.getOrderCode(), dto.getUserOper());
                String nodeOrderState = hxPaymentUtil.getNodeOrderState(dto.getUserOper());
                log.info("下一节点, {}", nextNodeType);
                // 插入运单执行状态
                hxPaymentUtil.insertNodeOrderState(dto.getOrderCode(), nodeOrderState, returnTime);
                // 修改下一支付节点规则数据，准备下一节点支付
                hxPaymentUtil.updateNextNodePay(dto, nextNodeType);
            }
        }

        return ResultUtil.ok();
    }

    /**
     * @description 节点支付回调 - 司机余额支付回调
     * <AUTHOR>
     * @date 2021/8/23 10:33
     */
    private ResultUtil NodeCDbalancePayNotice(THxOrderPayInfoDTO dto, Date returnTime) {
        // 修改节点支付规则表支付状态
        hxPaymentUtil.updateOrderPayRule(dto.getOrderCode(), dto.getUserOper(), DictEnum.PACKPAID.code, returnTime);
        // 获取运单执行状态
        String nodeOrderState = hxPaymentUtil.getNodeOrderState(dto.getUserOper());
        // 插入运单执行状态
        hxPaymentUtil.insertNodeOrderState(dto.getOrderCode(), nodeOrderState, returnTime);
        // 处理回调
        hxPaymentUtil.handleNodePayWKCallback(dto, returnTime);
        // 如果不是尾款支付
        if (!DictEnum.WKPAYNODE.code.equals(dto.getUserOper())) {
            // 修改下一支付节点规则数据
            String nextNodeType = hxPaymentUtil.getNextNodeState(dto.getOrderCode(), dto.getUserOper());
            log.info("下一节点, {}", nextNodeType);
            hxPaymentUtil.updateNextNodePay(dto, nextNodeType);
            return ResultUtil.ok();
        }
        // 创建收款凭证
        orderContractService.createHXAnySignSkpz(dto.getOrderCode(), DictEnum.ANYSIGNDRIVERSKPZ.code);

        return ResultUtil.ok();
    }

    /**
     * 节点支付 经纪人车队长模式
     * 司机支付到经纪人
     * @param dto
     * @param returnTime
     * @return
     */
    private ResultUtil NodeCMBalancePayNotice(THxOrderPayInfoDTO dto, Date returnTime) {
        // 修改节点支付规则表支付状态
        hxPaymentUtil.updateOrderPayRule(dto.getOrderCode(), dto.getUserOper(), DictEnum.PACKPAID.code, returnTime);
        // 获取运单执行状态
        String nodeOrderState = hxPaymentUtil.getNodeOrderState(dto.getUserOper());
        // 插入运单执行状态
        hxPaymentUtil.insertNodeOrderState(dto.getOrderCode(), nodeOrderState, returnTime);
        // 处理回调
        hxPaymentUtil.handleNodePayWKCallback(dto, returnTime);
        // 创建收款凭证
        orderContractService.createHXAnySignSkpz(dto.getOrderCode(), DictEnum.ANYSIGNDRIVERSKPZ.code);
        return ResultUtil.ok();
    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil txNotice(AsyncNotifyVirtualMemberWithdrawMessageBody messageBody) {
        Date returnTime = new Date();
        TTxOrderPayDetailDTO dto = hxOrderPayDetailMapper.selectTxOrderPayDetail(messageBody.getBizOrderNo());
        log.info("提现回调，支付信息, {}", JSONUtil.toJsonStr(dto));
        if (DictEnum.PAY_SUCC.code.equals(messageBody.getOrderStatus())) {
            if(null == dto.getTradeStatus() || DictEnum.TRADE_FAILED.code.equals(dto.getTradeStatus())){
                // 修改支付子表
                hxPaymentUtil.updateOrderPayDetail(dto.getId(), DictEnum.TRADE_FINISHED.code, returnTime);

                // 修改钱包
                hxWalletUtil.txNoticeModifyWallet(dto.getWalletId(), dto.getOrderTotalPayment(), true);
                // 记录C端钱包流水
                TZtWallet tZtWallet = hxWalletUtil.selectById(dto.getWalletId());
                Integer txWalletLogId = hxPaymentUtil.createTXWalletLog(messageBody, dto, tZtWallet, TradeType.CTIXIAN.code);
                // 修改支付主表
                hxPaymentUtil.updateOrderPayInfo(dto.getOrderPayId(), DictEnum.M130.code, returnTime);
                try {
                    // 保存电子回单
                    hxPaymentUtil.saveReceipt(messageBody.getPartnerAccId(), messageBody.getBizOrderNo(), DictEnum.WIDR.code, txWalletLogId);
                } catch (Exception e) {
                    log.error("保存提现电子回单失败, 错误信息, {}", e);
                }
                hxPaymentUtil.sendWXTXMessage(messageBody, dto.getEnduserId(), dto, true);
            }
        } else if (DictEnum.PAY_RETURN.code.equals(messageBody.getOrderStatus())) {
            // 退票
            hxPaymentUtil.updateFailOrderPayDetail(dto.getId(), messageBody.getResponseDesc(), messageBody.getResponseCode(), returnTime);
            hxPaymentUtil.sendWXTXMessage(messageBody, dto.getEnduserId(), dto, false);
            // 修改钱包, 只恢复余额
            hxWalletUtil.txNoticePayReturnModifyWallet(dto.getWalletId(), dto.getOrderTotalPayment());
            hxPaymentUtil.updateOrderPayInfo(dto.getOrderPayId(), DictEnum.M120.code, returnTime);
            // 添加提现退汇流水
            TZtWallet tztWallet = hxWalletUtil.selectById(dto.getWalletId());
            hxPaymentUtil.createWalletLog(messageBody.getBankOrderNo(), dto.getBankNo(), dto.getWalletId(), dto.getOrderTotalPayment(), tztWallet.getPurseCategory(),
                    dto.getOrderPayDetailCode(), HXTradeTypeEnum.HX_CTXPAYRETURN.code, dto.getCreateTime(), returnTime);
        } else {
            // 提现失败
            hxPaymentUtil.updateFailOrderPayDetail(dto.getId(), messageBody.getResponseDesc(), messageBody.getResponseCode(), returnTime);
            hxWalletUtil.txNoticeModifyWallet(dto.getWalletId(), dto.getOrderTotalPayment(), false);
            hxPaymentUtil.sendWXTXMessage(messageBody, dto.getEnduserId(), dto, false);
            hxPaymentUtil.updateOrderPayInfo(dto.getOrderPayId(), DictEnum.M120.code, returnTime);
        }
        return ResultUtil.ok();
    }

    /**
     * 发起提现申请
     * @param txAmount
     * @param enduserId
     * @param bankCardId
     * @param tztWallet
     */
    private void txApply(String orderPayCode, BigDecimal txAmount,  Integer enduserId, Integer bankCardId, Date txTime, TZtWallet tztWallet, Boolean selfCard) {
        // 查询C端绑卡华夏开户绑卡信息
        TZtBankCardDTO ztBankCardDTO = hxPayBankCardService.selectOpenRoleBankCardInfo(bankCardId);

        if (null == orderPayCode || StringUtils.isBlank(orderPayCode)) {
            orderPayCode = hxPaymentUtil.createOrderPayInfo(txAmount, DictEnum.P110.code);
        } else {
            TOrderPayInfo orderPayInfo = orderPayInfoMapper.selectByCode(orderPayCode);
            if (txAmount.compareTo(orderPayInfo.getOrderActualPayment()) != 0) {
                TOrderPayInfo forUpdate = new TOrderPayInfo();
                forUpdate.setId(orderPayInfo.getId());
                forUpdate.setOrderActualPayment(txAmount);
                forUpdate.setRemark(String.format("修改提现金额：由%s改为%s", orderPayInfo.getOrderActualPayment(), txAmount));
                orderPayInfoMapper.updateByPrimaryKeySelective(forUpdate);
            }
        }
        String orderPayDetailCode = hxPaymentUtil.createTxOrderPayDetail(orderPayCode, DictEnum.TX.code,
                tztWallet.getId(), enduserId, txTime, ztBankCardDTO);

        // 发起提现请求
        CustomerWithdrawRequest req = new CustomerWithdrawRequest();
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setPartnerId(hxPropertiesConfig.getPartnerId());
        //req.setMerchantCode(hxPropertiesConfig.getMerchantCode());
        req.setBizOrderNo(orderPayDetailCode);
        req.setCardId(ztBankCardDTO.getBankNo());
        req.setPartnerAccId(ztBankCardDTO.getPartnerAccId());
        req.setOrderAmount(txAmount);
        SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.HXWITHDRAWURL.code);
        req.setNotifyUrl(paramByKey.getParamValue());
        MQMessage message = new MQMessage();
        message.setTopic(HXMqMessageTopic.HX_WITHDRAW);
        message.setTag(HXMqMessageTag.HX_WITHDRAWAPPLY);
        message.setKey(orderPayDetailCode);
        message.setBody(req);
        log.info("发送提现申请MQ消息, {}", JSONUtil.toJsonStr(message));
        ResultUtil resultUtil = mqAPI.sendMessage(message);
        log.info("发送提现申请MQ消息结果，{}", JSONUtil.toJsonStr(resultUtil));
        if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
            throw new RuntimeException("ZJJ-600:提现失败！");
        }
    }

    /**
     * 发起提现服务费申请
     */
    private void txServiceFeeApply(BigDecimal txServiceFee, String outPartnerAccId, TTxOrderPayDetailDTO dto) {
        TZtBankCardDTO ztBankCardDTO = new TZtBankCardDTO();
        ztBankCardDTO.setCardNo(dto.getBankNo());
        ztBankCardDTO.setBankCardId(dto.getBankCardId());
        ztBankCardDTO.setCardOwner(dto.getCardHolder());

        // 创建提现服务费支付子表
        String orderPayDetailCode = hxPaymentUtil.createTxOrderPayDetail(dto.getOrderPayCode(), HXTradeTypeEnum.HX_TXSERVICEFEE.code,
                dto.getWalletId(), dto.getEnduserId(), new Date(), ztBankCardDTO);

        // 发起提现服务费支付请求
        CustomerBalancePayReq req = new CustomerBalancePayReq();
        req.setBizOrderNo(orderPayDetailCode);
        req.setRequestId(IdWorkerUtil.getInstance().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setPartnerId(hxPropertiesConfig.getPartnerId());
        //req.setMerchantCode(hxPropertiesConfig.getMerchantCode());
        req.setOutPartnerAccId(outPartnerAccId);
        // 查询平台会员编号
        SysParam platformAccId = sysParamAPI.getParamByKey(DictEnum.HXPLATFORMACCID.code);
        req.setInPartnerAccId(platformAccId.getParamValue());
        req.setOrderAmount(txServiceFee);
        req.setTradeAbstract("提现服务费");
        SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.HXPAYNOTIFYURL.code);
        req.setNotifyUrl(paramByKey.getParamValue());
        MQMessage message = new MQMessage();
        message.setTopic(MqMessageTopic.WITHDRAW);
        message.setTag(MqMessageTag.TXSERVICEFEE);
        message.setKey(orderPayDetailCode);
        message.setBody(req);
        ResultUtil resultUtil = mqAPI.sendMessage(message);
        log.info("发送提现服务费申请MQ消息, {}", JSONUtil.toJsonStr(message));
        log.info("发送提现服务费申请MQ消息结果，{}", JSONUtil.toJsonStr(resultUtil));
        if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
            throw new RuntimeException("ZJJ-210:提现失败！");
        }
    }

    /**
     * @description 交易退款回调
     * <AUTHOR>
     * @date 2021/8/25 19:23
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil refundNotice(AsyncNotifyVirtualBalancePayMessageBody messageBody) {
        Date returnTime = new Date();
        THxOrderPayInfoDTO dto = hxOrderPayDetailMapper.selectByOrderPayDetailKey(messageBody.getBizOrderNo());
        log.info("华夏退款回调，支付信息, {}", JSONUtil.toJsonStr(dto));
        if (DictEnum.PAY_SUCC.code.equals(messageBody.getOrderStatus())) {
            // 修改支付子表状态
            hxPaymentUtil.updateOrderPayDetail(dto.getOrderPayDetailId(), DictEnum.TRADE_FINISHED.code, returnTime);
            // 修改钱包
            hxWalletUtil.refundCallbackModifyWallet(dto);
            // 记录钱包流水
            hxPaymentUtil.refundCallbackInsertWalletLog(messageBody, dto, "0");
            // 经纪人余额支付回调
            if (HXTradeTypeEnum.HX_CMREFUNDBALANCE_PAY.code.equals(dto.getTradeType())) {
                CMRefundNotice(dto);
            }
            // 车队长余额支付回调
            if (HXTradeTypeEnum.HX_CCREFUNDBALANCE_PAY.code.equals(dto.getTradeType())) {
                CCRefundNotice(dto);
            }
            // 司机余额支付回调
            if (HXTradeTypeEnum.HX_CDREFUNDBALANCE_PAY.code.equals(dto.getTradeType())) {
                CDRefundNotice(dto);
            }
            // 承运方余额支付回调
            if (HXTradeTypeEnum.HX_CAREFUNDBALANCE_PAY.code.equals(dto.getTradeType())) {
                CARefundNotice(dto, returnTime);
            }
        } else {
            // 失败
            hxPaymentUtil.updateFailOrderPayDetail(dto.getOrderPayDetailId(), messageBody.getResponseDesc(), messageBody.getResponseCode(), returnTime);
            hxPaymentUtil.updateOrderInfo(dto.getOrderId(), DictEnum.O060.code, DictEnum.M080.code, returnTime);
            hxPaymentUtil.insertOrderState(dto.getOrderCode(), DictEnum.S0800.code, returnTime);
            hxPaymentUtil.updateOrderPayInfo(dto.getOrderPayId(), DictEnum.M080.code, returnTime);
            hxWalletUtil.failRefundCallbackModifyWallet(dto);
        }
        return ResultUtil.ok();
    }

    /**
     * @description 承运方交易退款回调
     * <AUTHOR>
     * @date 2021/8/26 15:48
     */
    private void CARefundNotice(THxOrderPayInfoDTO dto, Date returnTime) {
        // 修改支付主表
        hxPaymentUtil.refundRecallbackUpdateOrderPayInfo(dto.getOrderPayId(), DictEnum.M095.code, returnTime);

        // 账期付，重新冻结账期额度
        if (DictEnum.BILLPAY.code.equals(dto.getFeeSettlementWay())) {
            hxPaymentUtil.freezeCompanyProject(dto.getCompanyProjectId(), dto.getTotalFee());
        }

        // 单笔
        if (DictEnum.SINGLE.code.equals(dto.getPackStatus())) {
            TOrderInfo orderInfo = orderInfoMapper.selectOrderByCode(dto.getOrderCode());
            // 修改运单主表状态
            hxPaymentUtil.refundUpdateOrderInfo(dto.getOrderId(), DictEnum.O060.code, DictEnum.M095.code, returnTime);
            hxPaymentUtil.insertOrderState(dto.getOrderCode(), DictEnum.S0952.code, returnTime);
            hxPaymentUtil.insertOrderState(dto.getOrderCode(), DictEnum.S0953.code, DateUtils.addSeconds(returnTime, 1));
            // 修改支付子表入账为删除状态
            hxPaymentUtil.updateOrderPayDetailEnableByOrderCode(dto.getOrderCode());
            // 收款凭证作废
            orderContractService.deleteHxAnySignSkpz(dto.getOrderCode(), DictEnum.ANYSIGNDRIVERSKPZ.code);

            // 发送结算通知
            hxPaymentUtil.sendZHMessage(orderInfo, dto);
            redisUtil.del(PAY_REQUEST_KEY + orderInfo.getId());
            redisUtil.del(PAY_REQUEST_KEY_OF_ORDER_ID + orderInfo.getId());
        } else if (DictEnum.PACK.code.equals(dto.getPackStatus())) {
            TOrderPackInfo tOrderPackInfo = orderPackInfoMapper.selectPackInfoByOneOrderCode(dto.getOrderCode());
            // 修改打包主表状态
            tOrderPackInfo.setPackStatus(DictEnum.PACKRECALL.code);
            tOrderPackInfo.setUpdateTime(returnTime);
            orderPackInfoMapper.updateByPrimaryKeySelective(tOrderPackInfo);
            // 修改原始运单支付主表支付状态
            hxPaymentUtil.refundRecallbackUpdatePackOrderOrgiOrderPayInfo(dto.getPackId(), DictEnum.M095.code, returnTime);
            // 修改原始运单支付子表入账为删除状态
            hxPaymentUtil.updateOrderPayDetailEnableByOrderPackCode(dto.getPackCode());
            // 修改运单主表状态
            List<String> orderCodes = orderPackInfoMapper.selectOrderCodeByPackCode(dto.getPackCode());
            hxPaymentUtil.packRefundCallbackUpdateOrderInfoByPackId(orderCodes, DictEnum.O060.code, DictEnum.M095.code, returnTime);
            hxPaymentUtil.batchInsertOrderState(orderCodes, DictEnum.S0952.code, returnTime);
            hxPaymentUtil.batchInsertOrderState(orderCodes, DictEnum.S0953.code, DateUtils.addSeconds(returnTime, 1));
            // 收款凭证作废
            orderCodes.forEach((orderCode) -> orderContractService.deleteHxAnySignSkpz(orderCode, DictEnum.ANYSIGNDRIVERSKPZ.code));
            if (DictEnum.MANAGERPATTERN.code.equals(dto.getCapitalTransferPattern())) {
                orderCodes.forEach((orderCode) -> orderContractService.cancelContractTask(orderCode, dto.getAgentId(), DictEnum.AGENTTXXY.code));
            }
            // 发送结算通知
            hxPaymentUtil.sendPackZHMessage(tOrderPackInfo);
        }
    }

    /**
     * @description 司机交易退款回调
     * <AUTHOR>
     * @date 2021/8/26 15:48
     */
    private void CDRefundNotice(THxOrderPayInfoDTO dto) {
        if (DictEnum.COMMONPATTERN.code.equals(dto.getCapitalTransferPattern())) {
            // 发起承运方交易退款申请
            createCarrierRefundRequest(dto);
        } else if (DictEnum.MANAGERPATTERN.code.equals(dto.getCapitalTransferPattern())) {
            List<TOrderPayDetail> tOrderPayDetails = hxOrderPayDetailMapper.selectOrderPayDetailByRemark(dto.getRemark());
            if (tOrderPayDetails.size() == 2) {
                // 发起承运方交易退款申请
                createCarrierRefundRequest(dto);
            }
        }

    }

    /**
     * @description 车队长交易退款回调
     * <AUTHOR>
     * @date 2021/8/26 15:48
     */
    private void CCRefundNotice(THxOrderPayInfoDTO dto) {
        if (DictEnum.COMMONPATTERN.code.equals(dto.getCapitalTransferPattern())) {
            // 发起承运方交易退款申请
            createCarrierRefundRequest(dto);
        } else if (DictEnum.MANAGERPATTERN.code.equals(dto.getCapitalTransferPattern())) {
            List<TOrderPayDetail> tOrderPayDetails = hxOrderPayDetailMapper.selectOrderPayDetailByRemark(dto.getRemark());
            if (tOrderPayDetails.size() == 2) {
                // 发起承运方交易退款申请
                createCarrierRefundRequest(dto);
            }
        }

    }

    /**
     * @description 经纪人交易退款回调
     * <AUTHOR>
     * @date 2021/8/26 15:48
     */
    private void CMRefundNotice(THxOrderPayInfoDTO dto) {
        List<TOrderPayDetail> tOrderPayDetails = hxOrderPayDetailMapper.selectOrderPayDetailByRemark(dto.getRemark());
        if (tOrderPayDetails.size() == 2) {
            // 发起承运方交易退款申请
            createCarrierRefundRequest(dto);
        }

    }

    /**
    * @description 发起承运方交易退款申请
    * <AUTHOR>
    * @date 2021/10/12 11:29
    */
    private void createCarrierRefundRequest(THxOrderPayInfoDTO dto) {
        // 修改钱包
        TZtWallet carrierWallet = hxWalletUtil.selectById(dto.getCarrierWalletId());
        log.info("华夏退款，承运方发起退款申请，承运方钱包, {}", JSONUtil.toJsonStr(carrierWallet));
        BigDecimal entryAmount = carrierWallet.getEntryAmount().add(dto.getTotalFee());
        BigDecimal accountBalance = carrierWallet.getAccountBalance().subtract(dto.getTotalFee());
        carrierWallet.setEntryAmount(entryAmount);
        carrierWallet.setAccountBalance(accountBalance);
        hxWalletUtil.updateByPrimaryKey(carrierWallet);
        // 创建承运方交易退款子表
        String orderPayDetailCode = hxPaymentUtil.createTxOrderPayDetail(dto.getPayCode(), dto.getOrderCastChangeCode(),
                HXTradeTypeEnum.HX_CAREFUNDBALANCE_PAY.code, dto.getOperateState());
        // 发起承运方交易退款
        hxPaymentUtil.requestBalance(HXTradeTypeEnum.HX_CAREFUNDBALANCE_PAY.code, dto.getOperateState(), dto, orderPayDetailCode);
    }

    /**
      * <AUTHOR>
      * @Date 2021/10/11 11:04
      * @Description 打包退款回调
    **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil packRefundNotice(AsyncNotifyVirtualBalancePayMessageBody messageBody) {
        Date returnTime = new Date();
        THxOrderPayInfoDTO dto = hxOrderPayDetailMapper.selectOrderPackByOrderPayDetailKey(messageBody.getBizOrderNo());
        log.info("华夏打包退款回调，支付信息, {}", JSONUtil.toJsonStr(dto));
        if (DictEnum.PAY_SUCC.code.equals(messageBody.getOrderStatus())) {
            // 修改支付子表状态
            hxPaymentUtil.updateOrderPayDetail(dto.getOrderPayDetailId(), DictEnum.TRADE_FINISHED.code, returnTime);
            // 修改钱包
            hxWalletUtil.refundCallbackModifyWallet(dto);
            // 记录钱包流水
            hxPaymentUtil.refundCallbackInsertWalletLog(messageBody, dto, "1");
            // 经纪人余额支付回调
            if (HXTradeTypeEnum.HX_CMREFUNDBALANCE_PAY.code.equals(dto.getTradeType())) {
                CMRefundNotice(dto);
            }
            // 车队长余额支付回调
            if (HXTradeTypeEnum.HX_CCREFUNDBALANCE_PAY.code.equals(dto.getTradeType())) {
                CCRefundNotice(dto);
            }
            // 司机余额支付回调
            if (HXTradeTypeEnum.HX_CDREFUNDBALANCE_PAY.code.equals(dto.getTradeType())) {
                CDRefundNotice(dto);
            }
            // 承运方余额支付回调
            if (HXTradeTypeEnum.HX_CAREFUNDBALANCE_PAY.code.equals(dto.getTradeType())) {
                CARefundNotice(dto, returnTime);
            }
        } else {
            // 失败
            // 修改打包主表
            hxPaymentUtil.updateOrderPackInfo(dto.getPackCode(), DictEnum.PACKORDERPAIDERROR.code, returnTime);
            // 修改支付子表
            hxPaymentUtil.updateFailOrderPayDetail(dto.getOrderPayDetailId(), messageBody.getResponseDesc(), messageBody.getResponseCode(), returnTime);
            // 修改支付主表
            hxPaymentUtil.updateOrderPayInfo(dto.getOrderPayId(), DictEnum.M080.code, returnTime);
            // 修改原始运单主表
            List<String> orderCodes = orderPackInfoMapper.selectOrderCodeByPackCode(dto.getPackCode());
            hxPaymentUtil.failCallbackUpdateOrderInfoByPackId(orderCodes, DictEnum.O060.code, DictEnum.M080.code, returnTime);
            hxPaymentUtil.batchInsertOrderState(orderCodes, DictEnum.S0800.code, returnTime);
            // 修改原始运单支付主表
            hxPaymentUtil.updatePackOrderOrgiOrderPayInfo(dto.getPackId(), DictEnum.M080.code, returnTime);
            // 修改钱包
            hxWalletUtil.failRefundCallbackModifyWallet(dto);
        }
        return ResultUtil.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil payFail(CustomerBalancePayRes response) {
        try {
            // 修改支付子表状态
            TOrderPayDetail detail = new TOrderPayDetail();
            detail.setCode(response.getBizOrderNo());
            detail.setReturnTime(new Date());
            detail.setTradeStatus(DictEnum.TRADE_FAILED.code);
            detail.setErrorMsg(response.getResponseDesc());
            detail.setErrorCode(response.getResponseCode());
            hxOrderPayDetailMapper.updateByCode(detail);

            // 修改支付主表
            THxOrderPayInfoDTO hxOrderPayInfoDTO = hxOrderPayDetailMapper.selectByOrderPayDetailKey(response.getBizOrderNo());

            // 不是支付保险
            if (!HXTradeTypeEnum.HX_INSURANCE.code.equals(hxOrderPayInfoDTO.getTradeType())) {
                if (null != hxOrderPayInfoDTO.getOrderPayId()) {
                    hxPaymentUtil.updateOrderPayInfo(hxOrderPayInfoDTO.getOrderPayId(), DictEnum.M080.code, new Date());
                }
                // 修改支付主表
                if (null != hxOrderPayInfoDTO.getOrderId()) {
                    hxPaymentUtil.updateOrderInfo(hxOrderPayInfoDTO.getOrderId(), DictEnum.O060.code, DictEnum.M080.code, new Date());
                    hxPaymentUtil.insertOrderState(hxOrderPayInfoDTO.getOrderCode(), DictEnum.S0800.code, new Date());
                }
                // 修改钱包
                TZtWallet tZtWallet = hxWalletUtil.selectByCarrierCompanyPartnerAccId(response.getOutPartnerAccId(), DictEnum.BD.code);
                if (null == tZtWallet) {
                    tZtWallet = hxWalletUtil.selectByCarrierCompanyPartnerAccId(response.getOutPartnerAccId(), DictEnum.CA.code);
                    if (null == tZtWallet) {
                        tZtWallet = hxWalletUtil.selectByEnduserPartnerAccId(response.getOutPartnerAccId());
                    }
                }
                log.info("支付失败，修改出款方钱包, {}", JSONUtil.toJsonStr(tZtWallet));
                BigDecimal entryAmount = tZtWallet.getEntryAmount().subtract(response.getOrderAmount());
                BigDecimal accountBalance = tZtWallet.getAccountBalance().add(response.getOrderAmount());
                tZtWallet.setEntryAmount(entryAmount);
                tZtWallet.setAccountBalance(accountBalance);
                tZtWallet.setUpdateTime(new Date());
                hxWalletUtil.updateByPrimaryKey(tZtWallet);

                // 如果是账户服务费支付申请
                if (DictEnum.ACCOUNTSERVICERE.code.equals(detail.getTradeType())) {
                    // 修改记录状态为失败
                    // 查询补交账户服务费支付请求消息, 获取账户服务费收取失败记录
                    List<TMqMessage> mqMessages = orderMqMessageMapper.selectByMessageKey(response.getBizOrderNo(), HXMqMessageTopic.HX_ACCOUNTSERVICEFEE, HXMqMessageTag.HX_ACCOUNTSERVICEFEERETROACTIVE);

                    if (null != mqMessages && !mqMessages.isEmpty()) {
                        TMqMessage tMqMessage = mqMessages.get(mqMessages.size() - 1);
                        if (null != tMqMessage.getBody() && StringUtils.isNotBlank(tMqMessage.getBody())) {
                            AccountServiceFailedRecordVO accountServiceFailedRecordVO = JSONUtil.toBean(tMqMessage.getBody(), AccountServiceFailedRecordVO.class);
                            List<Integer> recordIds = accountServiceFailedRecordVO.getRecordIds();
                            orderServicefeeRecordMapper.batchUpdateRecordStatus(recordIds, 0);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("支付申请失败，处理失败, {}", ThrowableUtil.getStackTrace(e));
        }

        return ResultUtil.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil txFail(CustomerWithdrawRes response) {
        try {
            // 修改支付子表状态
            TOrderPayDetail detail = new TOrderPayDetail();
            detail.setCode(response.getBizOrderNo());
            detail.setReturnTime(new Date());
            detail.setTradeStatus(DictEnum.TRADE_FAILED.code);
            detail.setErrorMsg(response.getResponseDesc());
            detail.setErrorCode(response.getResponseCode());
            hxOrderPayDetailMapper.updateByCode(detail);
            // 修改支付主表
            TOrderPayInfo tOrderPayInfo = orderPayInfoMapper.selectByBizOrderNo(response.getBizOrderNo());
            hxPaymentUtil.updateOrderPayInfo(tOrderPayInfo.getId(), DictEnum.M120.code, new Date());
            // 修改钱包
            TZtWallet tZtWallet = hxWalletUtil.selectByEnduserPartnerAccId(response.getPartnerAccId());
            log.info("提现失败，修改出款方钱包, {}", JSONUtil.toJsonStr(tZtWallet));
            BigDecimal withdrawAmount = tZtWallet.getWithdrawAmount().subtract(response.getOrderAmount());
            BigDecimal accountBalance = tZtWallet.getAccountBalance().add(response.getOrderAmount());
            tZtWallet.setWithdrawAmount(withdrawAmount);
            tZtWallet.setAccountBalance(accountBalance);
            tZtWallet.setUpdateTime(new Date());
            hxWalletUtil.updateByPrimaryKey(tZtWallet);
        } catch (Exception e) {
            log.error("提现申请失败，处理失败, {}", ThrowableUtil.getStackTrace(e));
        }

        return ResultUtil.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil prePayNotice(AsyncNotifyVirtualBalancePayMessageBody messageBody, THxAdvanceOrderPayTempInfoDTO dto) {
        Date returnTime = new Date();
        BigDecimal amount = BigDecimal.ZERO;
        if (HXTradeTypeEnum.HX_COMBALANCE_PREPAY.code.equals(dto.getTradeType())) {
            amount = dto.getCarriageFee().add(dto.getDispatchFee());
        } else if (HXTradeTypeEnum.HX_CABALANCE_PREPAY.code.equals(dto.getTradeType())) {
            amount = dto.getCarriageFee();
        }
        if (DictEnum.PAY_SUCC.code.equals(messageBody.getOrderStatus())) {
            // 修改支付临时表
            hxPaymentUtil.updateAdvanceOrderPayTmp(dto.getPayId(), DictEnum.TRADE_FINISHED.code, returnTime, messageBody.getBankOrderNo());
            //修改钱包金额并记录钱包记录表
            hxWalletUtil.prePayCallbackModifyWallet(messageBody.getOutPartnerAccId(), messageBody.getInPartnerAccId(), dto.getTradeType(), amount, true);
            Integer walletLogId = hxPaymentUtil.insertPrePayWalletLog(messageBody, dto);
            // 创建电子回单
            hxPaymentUtil.saveReceipt(messageBody.getOutPartnerAccId(), messageBody.getBizOrderNo(), DictEnum.SALE.code, walletLogId);

            if (HXTradeTypeEnum.HX_COMBALANCE_PREPAY.code.equals(dto.getTradeType())) {
                // 企业支付回调
                TOrderInfo tOrderInfo = orderInfoMapper.selectOrderByCode(dto.getOrderCode());
                // 创建预付款支付临时表
                String advanceOrderPayTmpCode = hxPaymentUtil.createAdvanceOrderPayTmp(dto, HXTradeTypeEnum.HX_CABALANCE_PREPAY.code);
                // 修改承运方钱包， 余额 - 运费；入账中 + 运费
                TZtWallet tZtWallet = hxWalletUtil.selectByCarrierCompanyPartnerAccId(messageBody.getInPartnerAccId(), DictEnum.CA.code);
                hxWalletUtil.preCAPayModifyWallet(tZtWallet.getId(), dto.getCarriageFee());
                // 承运方发起余额支付
                hxPaymentUtil.requestPrePay(advanceOrderPayTmpCode, dto.getCarrierAccId(), dto.getDriverAccId(), dto.getCarriageFee(), tOrderInfo);
            } else if (HXTradeTypeEnum.HX_CABALANCE_PREPAY.code.equals(dto.getTradeType())) {
                // 承运方支付回调
                // 修改预付款主表
                hxPaymentUtil.updateAdvanceOrderTmp(dto.getAdvanceId(), DictEnum.M090.code, returnTime);
                // 账期付，恢复账期额度
                if (DictEnum.BILLPAY.code.equals(dto.getFeeSettlementWay())) {
                    hxPaymentUtil.unfreezeCompanyProject(dto.getCompanyProjectId(), amount);
                }
            }
        } else {
            // 失败只修改出款方钱包
            hxPaymentUtil.updateAdvanceOrderPayTmp(dto.getPayId(), DictEnum.TRADE_FAILED.code, returnTime, messageBody.getBankOrderNo());
            hxPaymentUtil.updateAdvanceOrderTmp(dto.getAdvanceId(), DictEnum.M080.code, returnTime);
            hxWalletUtil.prePayCallbackModifyWallet(messageBody.getOutPartnerAccId(), null, dto.getTradeType(), amount, false);
        }

        return ResultUtil.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil wkPayNotice(AsyncNotifyVirtualBalancePayMessageBody messageBody, THxOrderPayInfoDTO dto) {
        Date returnTime = new Date();
        BigDecimal amount = BigDecimal.ZERO;
        if (HXTradeTypeEnum.HX_COMBALANCE_WKPAY.code.equals(dto.getTradeType())) {
            amount = dto.getCarriageFee().add(dto.getDispatchFee());
        } else if (HXTradeTypeEnum.HX_CABALANCE_WKEPAY.code.equals(dto.getTradeType())) {
            amount = dto.getCarriageFee();
        }
        if (DictEnum.PAY_SUCC.code.equals(messageBody.getOrderStatus())) {
            // 修改支付临时表
            hxPaymentUtil.updateOrderPayDetail(dto.getOrderPayDetailId(), DictEnum.TRADE_FINISHED.code, returnTime);
            //修改钱包金额并记录钱包记录表
            hxWalletUtil.prePayCallbackModifyWallet(messageBody.getOutPartnerAccId(), messageBody.getInPartnerAccId(), dto.getTradeType(), amount, true);
            Integer walletLogId = hxPaymentUtil.insertWkPayWalletLog(messageBody, dto);
            // 创建电子回单
            hxPaymentUtil.saveReceipt(messageBody.getOutPartnerAccId(), messageBody.getBizOrderNo(), DictEnum.SALE.code, walletLogId);

            if (HXTradeTypeEnum.HX_COMBALANCE_WKPAY.code.equals(dto.getTradeType())) {
                TOrderInfo tOrderInfo = orderInfoMapper.selectOrderByCode(dto.getOrderCode());
                // 承运方发起尾款支付
                // 创建支付子表
                String advanceOrderPayTmpCode = hxPaymentUtil.createOrderPayDetail(dto.getPayCode(), dto.getOrderCastChangeCode(), HXTradeTypeEnum.HX_CABALANCE_WKEPAY.code, TradeType.RZ.code,"");
                // 修改钱包
                hxWalletUtil.preCAPayModifyWallet(dto.getCarrierWalletId(), dto.getCarriageFee());
                // 发起承运方余额支付
                hxPaymentUtil.requestPrePay(advanceOrderPayTmpCode, dto.getCarrierAccId(), dto.getDriverAccId(), dto.getCarriageFee(), tOrderInfo);
            } else if (HXTradeTypeEnum.HX_CABALANCE_WKEPAY.code.equals(dto.getTradeType())) {
                // 承运方支付回调
                // 账期付，恢复账期额度
                if (DictEnum.BILLPAY.code.equals(dto.getFeeSettlementWay())) {
                    hxPaymentUtil.unfreezeCompanyProject(dto.getCompanyProjectId(), dto.getTotalFee());
                }
                // 修改支付子表
                hxPaymentUtil.updateOrderPayDetail(dto.getOrderPayDetailId(), DictEnum.TRADE_FINISHED.code, returnTime);
                // 修改运单主表
                hxPaymentUtil.updateOrderInfo(dto.getOrderId(), DictEnum.M100.code, DictEnum.M090.code, returnTime);
                // 修改支付主表
                hxPaymentUtil.updateOrderPayInfo(dto.getOrderPayId(), DictEnum.M090.code, returnTime);
                // 创建运单执行状态
                hxPaymentUtil.insertOrderState(dto.getOrderCode(), DictEnum.S0902.code, returnTime);
                hxPaymentUtil.insertOrderState(dto.getOrderCode(), DictEnum.S1002.code, DateUtils.addSeconds(returnTime, 1));
                // 创建收款凭证
                orderContractService.createHXAnySignSkpz(dto.getOrderCode(), DictEnum.ANYSIGNDRIVERSKPZ.code);
            }

            // 查询是否存在收取账户服务费失败的记录，如果存在，则发起补交账户服务费
            try {
                log.info("查询是否存在收取账户服务费失败的记录");
                hxPaymentUtil.checkAccountServiceFeeFaildRecord(dto);
            } catch (Exception e) {
                log.error("查询是否存在收取账户服务费失败的记录失败, {}", ThrowableUtil.getStackTrace(e));
            }

        } else {
            // 失败只修改出款方钱包
            hxPaymentUtil.updateFailOrderPayDetail(dto.getOrderPayDetailId(), messageBody.getResponseDesc(), messageBody.getResponseCode(), returnTime);
            hxPaymentUtil.updateOrderPayInfo(dto.getOrderPayId(), DictEnum.M080.code, returnTime);
            hxPaymentUtil.updateOrderInfo(dto.getOrderId(), DictEnum.O060.code, DictEnum.M080.code, returnTime);
            hxWalletUtil.prePayCallbackModifyWallet(messageBody.getOutPartnerAccId(), null, dto.getTradeType(), amount, false);
        }

        return ResultUtil.ok();
    }

    /**
    * @description 补交账户服务费回调
    * <AUTHOR>
    * @date 2021/12/4 09:29
    */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil accountServiceFeeCallback(AsyncNotifyVirtualBalancePayMessageBody messageBody) {
        Date returnTime = new Date();
        THxOrderPayInfoDTO dto = hxOrderPayDetailMapper.selectByOrderPayDetailKey(messageBody.getBizOrderNo());
        // 查询补交账户服务费支付请求消息, 获取账户服务费收取失败记录
        List<TMqMessage> mqMessages = orderMqMessageMapper.selectByMessageKey(messageBody.getBizOrderNo(), MqMessageTopic.ACCOUNTSERVICEFEE, MqMessageTag.ACCOUNTSERVICEFEERETROACTIVE);
        List<Integer> recordIds = null;
        String serviceFeeType = "";
        if (null != mqMessages && !mqMessages.isEmpty()) {
            TMqMessage tMqMessage = mqMessages.get(mqMessages.size() - 1);
            if (null != tMqMessage.getBody() && StringUtils.isNotBlank(tMqMessage.getBody())) {
                AccountServiceFailedRecordVO accountServiceFailedRecordVO = JSONUtil.toBean(tMqMessage.getBody(), AccountServiceFailedRecordVO.class);
                recordIds = accountServiceFailedRecordVO.getRecordIds();
                serviceFeeType = accountServiceFailedRecordVO.getAccountServiceFeeType();
            }
        }
        // 查询出款方钱包
        TZtWallet outWallet = hxWalletUtil.selectByEnduserPartnerAccId(messageBody.getOutPartnerAccId());
        if (DictEnum.PAY_SUCC.code.equals(messageBody.getOrderStatus())) {
            // 修改支付主表
            hxPaymentUtil.updateOrderPayInfo(dto.getOrderPayId(), DictEnum.M090.code, returnTime);
            // 修改支付子表
            hxPaymentUtil.updateOrderPayDetail(dto.getOrderPayDetailId(), DictEnum.TRADE_FINISHED.code, returnTime);
            // 查询入款方钱包
            TZtWallet inWallet = hxWalletUtil.selectByCarrierCompanyPartnerAccId(messageBody.getInPartnerAccId(), DictEnum.PF.code);
            // 修改钱包
            hxWalletUtil.accountServicePayCallbackModifyWallet(outWallet, inWallet, dto.getOrderTotalPayment(), true);
            // 记录钱包流水
            hxPaymentUtil.insertAccountServiceFeeWalletLog(outWallet, inWallet, serviceFeeType, dto, messageBody);
            // 修改账户服务费记录状态
            orderServicefeeRecordMapper.batchUpdateRecordStatus(recordIds, 1);
        } else {
            // 失败
            hxPaymentUtil.updateOrderPayInfo(dto.getOrderPayId(), DictEnum.M080.code, returnTime);
            hxPaymentUtil.updateFailOrderPayDetail(dto.getOrderPayId(), messageBody.getResponseCode(), messageBody.getResponseCode(), returnTime);
            // 修改钱包
            hxWalletUtil.accountServicePayCallbackModifyWallet(outWallet, null, dto.getOrderTotalPayment(), false);
            // 修改账户服务费记录状态
            orderServicefeeRecordMapper.batchUpdateRecordStatus(recordIds, 0);
        }

        return ResultUtil.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil carrierBalanceCompanyChargeCallback(AsyncNotifyVirtualBalancePayMessageBody messageBody) {
        Date returnTime = new Date();
        THxOrderPayInfoDTO dto = hxOrderPayDetailMapper.selectByOrderPayDetailKey(messageBody.getBizOrderNo());
        log.info("承运方余额支付(企业充值)回调，支付子表dto信息, {}", JSONUtil.toJsonStr(dto));
        if (DictEnum.PAY_SUCC.code.equals(messageBody.getOrderStatus())) {
            hxPaymentUtil.updateOrderPayInfo(dto.getOrderPayId(), DictEnum.M090.code, returnTime);
            // 修改支付子表，添加回调结果、时间
            hxPaymentUtil.updateOrderPayDetail(dto.getOrderPayDetailId(), DictEnum.TRADE_FINISHED.code, returnTime);
            // 修改钱包
            hxWalletUtil.carrierBalanceCompanyChargeCallbackModifyWallet(messageBody, dto, true);
            // 记录钱包流水
            Integer walletLogId = hxPaymentUtil.insertCarrierBalanceCompanyChargeWalletLog(messageBody, dto);
            // 电子回单
            hxPaymentUtil.saveReceipt(messageBody.getOutPartnerAccId(), messageBody.getBizOrderNo(), DictEnum.SALE.code, walletLogId);
        } else {
            // 支付失败
            hxPaymentUtil.updateOrderPayInfo(dto.getOrderPayId(), DictEnum.M080.code, returnTime);
            hxPaymentUtil.updateFailOrderPayDetail(dto.getOrderPayDetailId(), messageBody.getResponseDesc(), messageBody.getResponseCode(), returnTime);
            // 恢复钱包
            hxWalletUtil.carrierBalanceCompanyChargeCallbackModifyWallet(messageBody, dto, false);
        }
        return ResultUtil.ok();
    }

}
