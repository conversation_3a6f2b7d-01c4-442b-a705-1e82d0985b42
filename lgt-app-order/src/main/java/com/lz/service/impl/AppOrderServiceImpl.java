package com.lz.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.codingapi.txlcn.tc.annotation.LcnTransaction;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lz.api.*;
import com.lz.common.config.RedisUtil;
import com.lz.common.constants.MqMessageTag;
import com.lz.common.constants.MqMessageTopic;
import com.lz.common.dbenum.BusinessCode;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.CodeEnum;
import com.lz.common.model.MQMessage;
import com.lz.common.util.StringUtils;
import com.lz.common.util.ThrowableUtil;
import com.lz.common.util.*;
import com.lz.common.util.gaode.AddressLocationUtil;
import com.lz.controller.CollectionOrderController;
import com.lz.dao.*;
import com.lz.dao.mongodb.TrajectoryMongoDao;
import com.lz.dto.*;
import com.lz.enums.InsuranceMethodsEnum;
import com.lz.model.*;
import com.lz.model.mongodb.TrajectoryMongo;
import com.lz.payment.Payment;
import com.lz.schedule.api.TTaskAPI;
import com.lz.schedule.model.TTask;
import com.lz.service.*;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.util.*;
import com.lz.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 企业端 APP 发单、收单
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AppOrderServiceImpl implements AppOrderService {

    /**
     * 发单
     */
    private static final String SEND = "SEND";

    /**
     * 收单
     */
    private static final String RECIEVE = "RECIEVE";

    /**
     * 证件类型 - 身份证
     */
    private static final String CERTIFICATE_TYPE = "ID_CARD";

    /**
     * 系统运单号，开头
     */
    private static final String BIZHEADER = "DB";

    @Resource
    private TOrderInfoMapper orderInfoMapper;

    @Resource
    TAppOrderInfoMapper appOrderInfoMapper;

    @Resource
    private TOrderStateMapper orderStateMapper;

    @Resource
    private TOrderCastCalcSnapshotMapper orderCastCalcSnapshotMapper;

    @Resource
    private TGoodsSourceVehicleDriverInfoMapper goodsSourceVehicleDriverInfoMapper;

    @Autowired
    private EnduserCarRelAPI enduserCarRelAPI;

    @Resource
    private TOrderVehicleTrajectoryMapper orderVehicleTrajectoryMapper;

    @Resource
    private TrajectoryMongoDao trajectoryMongoDao;

    @Autowired
    private GoodsSourceAPI goodsSourceAPI;

    @Autowired
    private AppCommonAPI appCommonAPI;

    @Autowired
    private TOrderInfoService tOrderInfoService;

    @Autowired
    private TOrderPackInfoMapper orderPackInfoMapper;

    @Autowired
    TOrderPackDetailMapper orderPackDetailMapper;

    @Resource
    private TOrderDeleteLogMapper orderDeleteLogMapper;

    @Autowired
    private TEndSUserInfoAPI endSUserInfoAPI;

    @Autowired
    private TOrderContractMapper orderContractMapper;
    @Autowired
    private TOrderCastChangesMapper orderCastChangesMapper;
    @Autowired
    private TOrderAbnormalMapper orderAbnormalMapper;

    @Autowired
    private ProjectCarrierAPI projectCarrierAPI;
    @Autowired
    private ComplaintsAPI complaintsAPI;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private SendOrderUtil sendOrderUtil;

    @Autowired
    private LinkGoodsRelAPI lineGoodsAPI;

    @Autowired
    private TOrderPayInfoMapper tOrderPayInfoMapper;

    @Autowired
    private SysParamAPI sysParamAPI;

    @Autowired
    private AccountService accountService;
    @Autowired
    private TEndSUserInfoAPI tEndSUserInfoAPI;

    @Value("${domainURL}")
    private String domainURL;

    @Autowired
    private DigitlFlowUtil digitlFlowUtil;

    @Autowired
    private TEndCarInfoAPI endCarInfoAPI;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private TOrderPayRuleMapper tOrderPayRuleMapper;

    @Autowired
    private TOrderPayRuleService tOrderPayRuleService;

    @Autowired
    private MqAPI mqAPI;

    @Autowired
    private TAdvanceOrderTmpService advanceOrderTmpService;

    @Resource
    private TOrderPayDetailMapper orderPayDetailMapper;

    @Autowired
    private TTaskAPI tTaskAPI;

    @Autowired
    private TOrderAnHuiReportService tOrderAnHuiReportService;

    @Resource
    private TOrderGoodsSourceInfoMapper orderGoodsSourceInfoMapper;

    @Resource
    private TOrderInfoDetailMapper orderInfoDetailMapper;

    @Resource
    private TOrderDriverCarStatisticsMapper orderDriverCarStatisticsMapper;

    @Autowired
    private TXinFaOrderInfoMapper xinFaOrderInfoMapper;

    @Resource
    private TOrderInfoWeightMapper orderInfoWeightMapper;

    @Autowired
    private CollectionOrderController collectionOrder;

    @Resource
    private TOrderLineGoodsUserRelMemberMapper tOrderLineGoodsUserRelMemberMapper;

    @Resource
    private TOrderInsuranceMapper orderInsuranceMapper;

    @Resource
    private TCarInsuranceMapper carInsuranceMapper;

    /**
     * 发单
     *
     * @param sendBillVO
     * @return
     * <AUTHOR>
     */
    //@LcnTransaction
    @Transactional
    @Override
    public ResultUtil saveSendOrder(String code, SendOrderVO sendBillVO, CarDriverRelVO carDriverRelVO, TOrderInfo orderInfo, String lineType,
                                    String capitalTransferType, LinkedHashMap carrier, CompanySourceDTO companySourceDTO) {
        String carKey = carDriverRelVO.getEndcarId() + "";
        String userKey = carDriverRelVO.getEndDriverId() + "";
        System.out.println(carKey + " " + userKey);
        String business = "";
        try {
            //查询车辆司机状态
            List<CarDriverRelVO> carDriverRelVOS = new ArrayList<>();
            carDriverRelVOS.add(carDriverRelVO);
            List<EnduserCarStatus> enduserCarStatusList = selectEnduserCarStatus(carDriverRelVOS);
            //如果是从模板加载，立即发单
            HashMap<String, Object> map;
            if (null != sendBillVO.getTemplateSend() && sendBillVO.getTemplateSend()) {
                map = checkUserCarStatusTemplateSend(carDriverRelVOS, enduserCarStatusList);
            } else {
                //普通发单
                map = checkUserCarStatus(carDriverRelVOS, enduserCarStatusList);
            }
            String unable = null != map.get("unable") ? map.get("unable").toString() : "";
            log.info("司机车辆认证状态, {}", unable);
            if (!unable.isEmpty()) {
                return ResultUtil.error(unable);
            }


            carDriverRelVO.setCode(orderInfo.getCode());
            //设置车辆司机的当前运单编号
            carDriverRelVO.setCurrentDriverAccountNo(carDriverRelVO.getPhone());
            //已接单
            carDriverRelVO.setCarStatus(DictEnum.ONROADRECIEVEORDER.code);
            //已接单
            carDriverRelVO.setUserStatus(DictEnum.NOTRECIEVEORDER.code);
            Integer endUserCarRelId = carDriverRelVO.getEnduserCarRelId();

            //设置运单主表业务id add by zhangjiji 2019/6/15 update by zhangjiji 2019/6/23
            orderInfo.setCode(code);

            //运单编号：线路编码 + 年月日时分秒毫秒
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            String date = sdf.format(new Date());
            String random = RandomUtil.randomString(3, 10);
            //设置运单主表运单编号
            orderInfo.setOrderBusinessCode(lineType + date + random);
            //创建运单主表
            orderInfo.setPayMethod(companySourceDTO.getCarriagePayType());
            orderInfo.setDeliverOrderTime(new Date());
            orderInfo.setVehicleId(carDriverRelVO.getEndcarId());
            orderInfo.setEndDriverId(carDriverRelVO.getEndDriverId());
            orderInfo.setEndAgentId(carDriverRelVO.getEndAgentId());
            orderInfo.setAgentId(null != companySourceDTO.getManagerId() ? companySourceDTO.getManagerId() : null);
            orderInfo.setEndUserCarRelId(endUserCarRelId);
            if (null != carDriverRelVO.getCaptainId()) {
                orderInfo.setEndCarOwnerId(carDriverRelVO.getCaptainId());
            } /*else if (null != carDriverRelVO.getEndCarOwnerId()) {
                orderInfo.setEndCarOwnerId(carDriverRelVO.getEndCarOwnerId());
            }*/
            orderInfo.setOperateMethod(sendBillVO.getOperateMethod());
            orderInfo.setBusinessAssist(companySourceDTO.getBusinessAssist());
            // 放入当前月索引 Yan
            orderInfo.setParam4(OrderUtil.indexTime());
            orderInfo.setEnable(false);
            //创建运单
            if (null != orderInfo.getEndAgentId() && orderInfo.getEndAgentId() == 0) {
                orderInfo.setEndAgentId(null);
            }
            //TODO 创建货源车辆司机子表
            TGoodsSourceVehicleDriverInfo goodsSourceVehicleDriverInfo = createGoodsSourceVehicleDriverInfo(orderInfo, carDriverRelVO, DictEnum.HANDSEND.code);

            orderInfo.setGoodsSourceVehicleDriverInfoCode(goodsSourceVehicleDriverInfo.getCode());
            if (null != companySourceDTO.getBlockchainPass() && companySourceDTO.getBlockchainPass()) {
                orderInfo.setVehicleGpsBdStatus(DictEnum.AUTOFETCHGPS.code);
            } else {
                orderInfo.setVehicleGpsBdStatus(DictEnum.NOFETCHGP.code);
            }
            orderInfoMapper.insertSelective(orderInfo);

            //当单位为箱时，记录运单ID、箱数
            if(null != sendBillVO.getCarriagePriceUnit() &&
                    DictEnum.CARRIAGE_PRICE_UNIT_BOX.code.equals(sendBillVO.getCarriagePriceUnit())){
                TOrderInfoWeight torderInfoweight = sendBillVO.getOrderInfoWeight();
                torderInfoweight.setOrderId(orderInfo.getId());
                if(torderInfoweight.getBoxNum()>0){
                    if(torderInfoweight.getBoxNum()==1){
                        torderInfoweight.setPrimaryWeight1(new BigDecimal(String.valueOf(orderInfo.getPrimaryWeight())));//原发重量1
                        torderInfoweight.setCarriageUnitPrice1(orderInfo.getCurrentCarriageUnitPrice());//运费单价1
                    }else{
                        torderInfoweight.setPrimaryWeight1(BigDecimal.valueOf(companySourceDTO.getEstimateGoodsWeight()));//原发重量1
                        torderInfoweight.setPrimaryWeight2(BigDecimal.valueOf(companySourceDTO.getEstimateGoodsWeight()));//原发重量2
                        torderInfoweight.setCarriageUnitPrice1(orderInfo.getCurrentCarriageUnitPrice());//运费单价1
                        torderInfoweight.setCarriageUnitPrice2(orderInfo.getCurrentCarriageUnitPrice());//运费单价2
                    }
                }
                orderInfoWeightMapper.insertSelective(torderInfoweight);
            }

            // 记录运单信息详情
            TOrderInfoDetail orderInfoDetail = new TOrderInfoDetail();
            orderInfoDetail.setOrderId(orderInfo.getId());
            //如果线路中的预计行驶时间不为空，直接赋值
            if(null != companySourceDTO.getEstimatedTravelTime()){
                orderInfoDetail.setEstimatedTravelTime(companySourceDTO.getEstimatedTravelTime());
            }else {
                //如果为空，则根据起终点的坐标计算
                Map<String, Double> stringDoubleMap = AddressLocationUtil.getDistanceAndDuration(companySourceDTO.getFromCoordinates(), companySourceDTO.getEndCoordinates());
                Double estimatedTravelTime = stringDoubleMap.get("estimatedTravelTime");
                orderInfoDetail.setEstimatedTravelTime(estimatedTravelTime);
            }
            // 记录司机、车辆审核状态
            EnduserCarStatus enduserCarStatus = enduserCarStatusList.get(0);
            if (null != enduserCarStatus.getUserAuditStatus()
                    && DictEnum.PASSNODE.code.equals(enduserCarStatus.getUserAuditStatus())) {
                orderInfoDetail.setDriverAuditStatus(DictEnum.PASSNODE.code);
            } else {
                orderInfoDetail.setDriverAuditStatus(DictEnum.NOTPASSNODE.code);
            }
            if (null != enduserCarStatus.getCarAuditStatus()
                    && DictEnum.PASSNODE.code.equals(enduserCarStatus.getCarAuditStatus())) {
                orderInfoDetail.setCarAuditStatus(DictEnum.PASSNODE.code);
            } else {
                orderInfoDetail.setCarAuditStatus(DictEnum.NOTPASSNODE.code);
            }
            if (null != sendBillVO.getCarriagePriceUnit()) {
                orderInfoDetail.setCarriagePriceUnit(sendBillVO.getCarriagePriceUnit());
            }
            //获取货源信息
            TGoodsSourceInfo tGoodsSourceInfo = new TGoodsSourceInfo();
            tGoodsSourceInfo.setLineGoodsRelId(orderInfo.getLineGoodsRelId());
            TGoodsSourceInfo info = goodsSourceAPI.selectGoodsSourceInfo(tGoodsSourceInfo);
            //当货源开启了“校验司机是否违规”设置项，
            // 在发单员发单后即将运单判定为违规（违规原因：未在装货地范围内签到）
            if(DictEnum.SINGLEPAY.code.equals(orderInfo.getPayMethod()) && info.getIllegal()){
                String orderReason = orderInfoDetail.getIllegalOrderReason();
                orderInfoDetail.setIllegalOrder(true);
                if(null == orderReason || "".equals(orderReason)){
                    orderInfoDetail.setIllegalOrderReason("未在装货地范围内签到");
                }else{
                    orderInfoDetail.setIllegalOrderReason(orderReason+",未在装货地范围内签到");
                }
                orderInfoDetail.setDeduction(info.getDeduction());//违规扣款金额
            }
            //发单时，获取货源“是否开启违规校验”的值保存到运单详情表
            orderInfoDetail.setIllegalCheck(info.getIllegal());
            orderInfoDetailMapper.insertSelective(orderInfoDetail);

            //资金转移方式, 创建承运方与C端的关系，申请子账号，虚拟钱包
            // 如果承运方无子账号，不创建C端与承运方的管子，申请子账号，钱包，待之后使用工具批量处理
            // 2022-03-31 网商停止，注释
            /*if (null != carrier.get("thridParySubAccount") && StringUtils.isNotBlank(carrier.get("thridParySubAccount").toString())) {
                sendOrderUtil.createCarrierAndEnduserRel(sendBillVO, carDriverRelVO, capitalTransferType, carrier, orderInfo.getCode(),companySourceDTO);
            }*/
            //TODO 承运方签署合同，经纪人签署合同
            // sendOrderUtil.signContract(orderInfo, carrier, carDriverRelVO, companySourceDTO, DictEnum.QSHT.code);

            //创建运单状态子表
            createOrderState(orderInfo.getCode(), orderInfo.getCreateUser(), DictEnum.S0201.code, sendBillVO);

            //TODO 新增运单车辆轨迹
            createOrderVehicleTrajectory(orderInfo.getCode(), orderInfo.getFromCoordinates(),
                    orderInfo.getFromName(), DictEnum.SENDORDERSIGN.code, carDriverRelVO, companySourceDTO);

            //TODO 发单
            Payment payment = new Payment();
            payment.Invoice(orderInfo);
            carDriverRelVO.setCurrentReceiptsNo(orderInfo.getOrderBusinessCode());

            //发单节点支付模式
            if(DictEnum.NODEPAYFIXED.code.equals(companySourceDTO.getCarriagePayType())
                    || DictEnum.NODEPAYPROPORTION.code.equals(companySourceDTO.getCarriagePayType())){
                // 创建节点支付规则
                sendOrderUtil.createOrderPayRule(orderInfo.getCode(), companySourceDTO);
            }

            // 保存司机、车辆运单统计
            sendOrderUtil.saveDriverAndCarOrderCount(carDriverRelVO);

            // 修改货源使用次数
            auditGoodsSourceUseNumber(sendBillVO.getGoodsSourceInfoId());

            // 发送微信通知
            try {
                sendDriverSourceWXMessage(orderInfo, companySourceDTO);
            } catch (Exception e) {
                log.info("发送微信通知失败");
                e.printStackTrace();
            }

            //TODO 更新C端用户和车辆的状态
            try {
                ResultUtil resultUtil = appCommonAPI.batchUpdateCarEnduserStatus(carDriverRelVO);
                if (null != resultUtil && null != resultUtil.getCode() && !resultUtil.getCode().equals("success")) {
                    throw new RuntimeException("修改车辆司机状态失败");
                }
            } catch (Exception e) {
                log.error("修改车辆司机状态失败", e);
                throw new RuntimeException("错误码：ZJJ001，请联系运营平台予以解决。");
            }

            tOrderInfoService.sendEtcData(orderInfo.getOrderBusinessCode());

            return ResultUtil.ok();
        }catch (Exception e){
            log.info("ZJJ-001:发单失败!", e);
            String message = e.getMessage();
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再发单。";
            }
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再发单";
            }
            if (StringUtils.isNotEmpty(message) && StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("ZJJ-001:发单失败!");
            }
        }
    }


    /**
     * <AUTHOR>
     * @Description 发送给司机接单微信通知
     * @Date 2020/4/30 3:41 下午
     * @Param
     * @return 3:41 下午
    **/
    private void sendDriverSourceWXMessage(TOrderInfo orderInfo, CompanySourceDTO companySourceDTO) {
        //司机信息 姓名电话
        ResultUtil reEnd = tEndSUserInfoAPI.selectById(orderInfo.getEndDriverId());
        TEndUserInfo tEndUserInfo = JSON.parseObject(JSONObject.toJSON(reEnd.getData()).toString(), TEndUserInfo.class);
        SearchAccountVO accountVO = new SearchAccountVO();
        ArrayList<Integer> enduserIds = new ArrayList<>();
        enduserIds.add(orderInfo.getEndDriverId());
        accountVO.setEnduserIds(enduserIds);
        String url = domainURL + "/lgt-app-driver-weixin/#/loadsignin?code=" + orderInfo.getCode();
        log.info("发起微信通知");
        ResultUtil resultUtil = accountService.selectOpenIdByEnduserId(accountVO);
        if (null != resultUtil) {
            ArrayList data = (ArrayList) resultUtil.getData();
            LinkedHashMap accountDTO = (LinkedHashMap) data.get(0);
            Object openId = accountDTO.get("openId");
            if (openId != null && StringUtils.isNotEmpty(openId.toString())) {
                SysParam sysParam = sysParamAPI.getParamByKey("WXMessageUrl");
                if (sysParam != null && null != sysParam.getParamValue()) {
                    if (StringUtils.isBlank(String.valueOf(openId))) {
                        log.info("司机openid为空");
                    } else {
                        SysParam sys = sysParamAPI.getParamByKey("WXTransition");
                        if (sys != null) {
                            if (!"1".equals(sys.getParamValue())) {
                                String frist = "尊敬的" + tEndUserInfo.getRealName() + "司机，您接到新的运单，请及时前往装货地进行装货签到。";
                                String remark = "点击“详情”即可进入“装货签到”页面。";
                                WxMsgSendUtil.sendDriverSource(frist, companySourceDTO.getGoodsName(),
                                        String.valueOf(companySourceDTO.getEstimateGoodsWeight()), orderInfo.getFromName() + "/" + orderInfo.getEndName(),
                                        orderInfo.getCreateTime(), companySourceDTO.getGoodsCreateTime(), openId.toString(), sysParam.getParamValue(), url, remark);
                            }
                        }
                    }
                }
            }
        }
    }


    // 发单成功修改货源使用次数
    public synchronized void auditGoodsSourceUseNumber(Integer sourceId) {
        goodsSourceAPI.auditSourceUseNumber(sourceId);
    }

    /**  批量导入收单
     *
     * @param receiveOrderList
     * @return
     */
    @Override
    @Transactional
    public ResultUtil batchExcelReceiveOrder(List<BatchExcelReceiveOrder> receiveOrderList) {
        List<Map> mapList = new ArrayList<>();
        try {
            for(BatchExcelReceiveOrder receiveOrder:receiveOrderList){
                receiveOrder.setDeliverWeightNotesTime2(DateUtils.parseDateNew(receiveOrder.getDeliverWeightNotesTime()));
                receiveOrder.setReceiveWeightNotesTime2(DateUtils.parseDateNew(receiveOrder.getReceiveWeightNotesTime()));
                TOrderInfo tOrderInfo = orderInfoMapper.selectByRecieveOrder(receiveOrder);
                Map map = new HashMap();
                if(tOrderInfo!=null){
                    //发货磅单重量
                    if(null != tOrderInfo.getDeliverWeightNotesWeight() && tOrderInfo.getDeliverWeightNotesWeight()>=75){
                        map.put("vehicleNumber",receiveOrder.getVehicleNumber());
                        map.put("msg","收单失败");
                        map.put("result","发货磅单重量需小于75，请重新填写，运单号："+tOrderInfo.getOrderBusinessCode());
                        mapList.add(map);
                        continue;
                    }
                    //卸货重量
                    if(null != receiveOrder.getReceiveWeightNotesWeight() &&
                            receiveOrder.getReceiveWeightNotesWeight()>=75){
                        map.put("vehicleNumber",receiveOrder.getVehicleNumber());
                        map.put("msg","收单失败");
                        map.put("result","卸货重量需小于75，请重新填写，运单号："+tOrderInfo.getOrderBusinessCode());
                        mapList.add(map);
                        continue;
                    }
                    Integer receiveInt = tOrderLineGoodsUserRelMemberMapper.receiveOrder(tOrderInfo.getLineGoodsRelId(),CurrentUser.getUserAccountId());
                    if(null == receiveInt || receiveInt<1){
                        map.put("vehicleNumber",receiveOrder.getVehicleNumber());
                        map.put("msg","收单失败");
                        map.put("result","当前收单员无权限收单此运单："+tOrderInfo.getOrderBusinessCode());
                        mapList.add(map);
                    }else{
                        TOrderCastChanges tOrderCastChanges = orderCastChangesMapper.selectByNewOne(tOrderInfo.getCode());
                        TOrderInfoDetail infoDetail = orderInfoDetailMapper.selectByOrderId(tOrderInfo.getId());//运单详情信息
                        //查询运单详情表，当“校验司机是否违规”开启，并且运单是单笔支付运单
                        if(null != tOrderInfo.getPayMethod() && DictEnum.SINGLEPAY.code.equals(tOrderInfo.getPayMethod()) &&
                                infoDetail.getIllegalCheck()){
                            //校验是否已卸货签到，若未卸货签到，将运单判定为违规（违规原因：未在卸货地范围内签到）
                            if(!DictEnum.M040.code.equals(tOrderInfo.getOrderExecuteStatus())){
                                infoDetail.setIllegalOrder(true);
                                if(null == infoDetail.getIllegalOrderReason() || "".equals(infoDetail.getIllegalOrderReason())){
                                    infoDetail.setIllegalOrderReason("未在卸货地范围内签到");
                                }else{
                                    infoDetail.setIllegalOrderReason(infoDetail.getIllegalOrderReason()+",未在卸货地范围内签到");
                                }
                            }
                            //校验发货磅单至卸货磅单时间段内的轨迹，若轨迹不全或获取不到轨迹，将运单判定为违规（违规原因：轨迹不完整）
                            //先判断是否关闭过北斗，如果关闭过北斗定位，则轨迹不完整
                            //如果没有关闭过北斗，或者没有扫描到的，则查询车辆轨迹，看是否存在“服务自动抓取（SERVERCATCH）”的数据
                            if(infoDetail.getCloseGps()){
                                infoDetail.setIllegalOrder(true);
                                if(null == infoDetail.getIllegalOrderReason() || "".equals(infoDetail.getIllegalOrderReason())){
                                    infoDetail.setIllegalOrderReason("轨迹不完整");
                                }else{
                                    infoDetail.setIllegalOrderReason(infoDetail.getIllegalOrderReason()+",轨迹不完整");
                                }
                            } else {
                                List<TrajectoryMongo> list = trajectoryMongoDao.getAllByOrderCode(tOrderInfo.getCode());//根据运单code查询车辆轨迹
                                int sum = 0;
                                if(list.size() > 0){
                                    //遍历集合，查询是否存在“服务自动抓取（SERVERCATCH）”的数据
                                    for (TrajectoryMongo trajectoryMongo : list) {
                                        if(DictEnum.SERVERCATCH.code.equals(trajectoryMongo.getTrajectoryReceiveMethod())){
                                            sum++;
                                        }
                                    }
                                    if(sum == 0){
                                        infoDetail.setIllegalOrder(true);
                                        if(null == infoDetail.getIllegalOrderReason() || "".equals(infoDetail.getIllegalOrderReason())){
                                            infoDetail.setIllegalOrderReason("轨迹不完整");
                                        }else{
                                            infoDetail.setIllegalOrderReason(infoDetail.getIllegalOrderReason()+",轨迹不完整");
                                        }
                                    }
                                }
                            }
                        }
                        //匹配到运单，但运单未卸货签到的，不进行收单操作
                        if(!DictEnum.M040.code.equals(tOrderInfo.getOrderExecuteStatus())){
                            map.put("vehicleNumber",receiveOrder.getVehicleNumber());
                            map.put("msg","收单失败");
                            map.put("result","未卸货签到"+tOrderInfo.getOrderBusinessCode());
                            mapList.add(map);
                            continue;
                        }
                        if (infoDetail.getIllegalCheck() && infoDetail.getIllegalOrder() && null != infoDetail.getDeduction() && infoDetail.getDeduction().compareTo(BigDecimal.ZERO) > 0) {
                            if (DictEnum.COMMONPATTERN.code.equals(tOrderCastChanges.getCapitalTransferPattern())) {
                                if (receiveOrder.getAmount().compareTo(infoDetail.getDeduction()) <= 0) {
                                    map.put("vehicleNumber",receiveOrder.getVehicleNumber());
                                    map.put("msg","收单失败");
                                    map.put("result","违规扣款金额大于运费金额，不允许收单"+tOrderInfo.getOrderBusinessCode());
                                    mapList.add(map);
                                    continue;
                                }
                            } else if (DictEnum.MANAGERPATTERN.code.equals(tOrderCastChanges.getCapitalTransferPattern())) {
                                BigDecimal serviceFee2 =  sendOrderUtil.calulateServiceFee(tOrderInfo.getCode(), receiveOrder.getAmount(), Double.parseDouble(tOrderInfo.getSettledWeight().toString()));
                                if (receiveOrder.getAmount().subtract(serviceFee2).compareTo(infoDetail.getDeduction()) <= 0) {
                                    map.put("vehicleNumber",receiveOrder.getVehicleNumber());
                                    map.put("msg","收单失败");
                                    map.put("result","违规扣款金额大于运费金额，不允许收单"+tOrderInfo.getOrderBusinessCode());
                                    mapList.add(map);
                                    continue;
                                }
                            }
                        }
                        if((null == tOrderInfo.getDeliverWeightNotesWeight() || "".equals(tOrderInfo.getDeliverWeightNotesWeight())) ||
                                (null == tOrderInfo.getDeliverWeightNotesPhoto() || "".equals(tOrderInfo.getDeliverWeightNotesPhoto())) ||
                                (null == tOrderInfo.getReceiveWeightNotesWeight() || "".equals(tOrderInfo.getReceiveWeightNotesWeight())) ||
                                (null == tOrderInfo.getReceiveWeightNotesPhoto() || "".equals(tOrderInfo.getReceiveWeightNotesPhoto()))){
                            //匹配到运单，但装卸货信息不完整的（装卸货磅单照片、装卸货重量任意缺少一项或多项），不进行收单操作
                            map.put("vehicleNumber",receiveOrder.getVehicleNumber());
                            map.put("msg","收单失败");
                            map.put("result","装卸货信息不完整"+tOrderInfo.getOrderBusinessCode());
                            mapList.add(map);
                            continue;
                        }

                        TGoodsSourceInfo goodsSourceInfo = xinFaOrderInfoMapper.selectByTGoodsSourceInfo(tOrderInfo.getLineGoodsRelId());
                        TOrderInfoWeight orderInfoWeight = orderInfoWeightMapper.selectByOrderId(tOrderInfo.getId());

                        //查询t_order_info_detail，获取预计行驶时间
                        TOrderInfoDetail orderInfoDetail = orderInfoDetailMapper.selectByOrderId(tOrderInfo.getId());

                        //点击收单时，校验当前时间是否大于（建单时间+预计行驶时间），若小于，不允许收单，
                        // 提示：未到达收单时间，请于xx月xx日 xx：xx（时分）后再进行收单操作。
                        Double estimatedTravelTime = orderInfoDetail.getEstimatedTravelTime();//预计行驶时间
                        if(null == estimatedTravelTime || estimatedTravelTime == 0d){
                            //如果预计行驶时间是空的，则根据起点终点坐标得出预计行驶时间
                            String fromCoordinates = tOrderInfo.getFromCoordinates();//起点坐标
                            String endCoordinates = tOrderInfo.getEndCoordinates();//终点坐标
                            if(null != fromCoordinates && null != endCoordinates){
                                Map<String, Double> map1 = AddressLocationUtil.getDistanceAndDuration(fromCoordinates, endCoordinates);
                                estimatedTravelTime = map1.get("estimatedTravelTime");
                            }
                        }
                        int v = (int)(estimatedTravelTime * 3600);//换算成秒
                        Date createTime = tOrderInfo.getCreateTime();//建单时间
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(createTime);
                        calendar.add(Calendar.SECOND, v);
                        Date newDate = calendar.getTime();//预计收单时间
                        Date date = new Date();
                        SimpleDateFormat format = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分");
                        String dateStr = format.format(newDate);

                        if(date.getTime()< newDate.getTime()){
                            map.put("vehicleNumber",receiveOrder.getVehicleNumber());
                            map.put("msg","收单失败");
                            map.put("result","未达到线路规定的行驶时间，请于"+dateStr+"后再进行审核操作。"+tOrderInfo.getOrderBusinessCode());
                            mapList.add(map);
                        }else{
                            if(receiveOrder.getReceiveWeightNotesWeight().compareTo(tOrderInfo.getReceiveWeightNotesWeight())!=0){
                                tOrderInfo.setReceiveWeightNotesWeight(receiveOrder.getReceiveWeightNotesWeight());
                            }
                            CollectOrderInfoDTO collectOrderInfoDTO = orderInfoMapper.pcGetCollectOrderBasisInfo(tOrderInfo.getCode());
                            CollectionOrderResp resp = new CollectionOrderResp();
                            //调用计算运费
                            CollectionOrderReq req = new CollectionOrderReq();
                            req.setSettledWeightType(collectOrderInfoDTO.getSettledWeightType());
                            req.setIfExceedAmount(collectOrderInfoDTO.getIfExceedAmount());
                            req.setIsEditSettledWeight(false);
                            req.setSettledWeight(Double.parseDouble(collectOrderInfoDTO.getSettledWeight().toString()));
                            req.setPrimaryWeight(tOrderInfo.getDeliverWeightNotesWeight());
                            req.setDischargeWeight(tOrderInfo.getReceiveWeightNotesWeight());
                            req.setCarriageUnitPrice(collectOrderInfoDTO.getCarriageUnitPrice());
                            req.setCode(collectOrderInfoDTO.getOrderCode());
                            req.setRuleName(collectOrderInfoDTO.getRuleName());
                            req.setGoodsCutWater(collectOrderInfoDTO.getGoodsCutWater());
                            req.setGoodsCutImpurities(collectOrderInfoDTO.getGoodsCutImpurities());
                            req.setGoodsUnitPrice(collectOrderInfoDTO.getGoodsUnitPrice());
                            req.setOtherCutFee1(collectOrderInfoDTO.getOtherCutFee1());
                            req.setOtherCutFee2(collectOrderInfoDTO.getOtherCutFee2());
                            req.setOtherCutFee3(collectOrderInfoDTO.getOtherCutFee3());
                            req.setOtherCutFee4(collectOrderInfoDTO.getOtherCutFee4());
                            req.setFixCutFee(collectOrderInfoDTO.getFixCutFee());
                            req.setCarriageZeroCutPaymentRule(collectOrderInfoDTO.getCarriageZeroCutPaymentRule());
                            req.setToleranceItemValue(collectOrderInfoDTO.getToleranceItemValue());
                            req.setToleranceItem(collectOrderInfoDTO.getToleranceItem());
                            req.setCutImpuritiesIsCalcvalue(collectOrderInfoDTO.getCutImpuritiesIsCalcvalue());
                            req.setCutWaterIsCalcvalue(collectOrderInfoDTO.getCutWaterIsCalcvalue());
                            if (null == req.getRuleName() || "null".equals(req.getRuleName())) {
                                Map<String, Object> map2 =  JSONUtil.toBean(JSONUtil.toJsonStr(collectionOrder.collection(req).getData()),Map.class);
                                resp.setSettledWeight(Double.parseDouble(String.valueOf(map2.get("settledWeight"))));
                                resp.setFreightPayable(new BigDecimal(String.valueOf(map2.get("freightPayable"))));
                                resp.setServiceFee(new BigDecimal(String.valueOf(map2.get("serviceFee"))));
                            }else{
                                resp = JSONUtil.toBean(JSONUtil.toJsonStr(collectionOrder.collection(req).getData()), CollectionOrderResp.class);
                            }


                            tOrderInfo.setPrimaryWeight(tOrderInfo.getDeliverWeightNotesWeight());
                            tOrderInfo.setSettledWeight(BigDecimal.valueOf(resp.getSettledWeight()));
                            tOrderInfo.setRulePaymentAmount(resp.getFreightPayable());

                            if(receiveOrder.getAmount().compareTo(resp.getFreightPayable())!=0){

                                tOrderInfo.setDischargeWeight(receiveOrder.getReceiveWeightNotesWeight());
                                tOrderInfo.setUserConfirmPaymentAmount(receiveOrder.getAmount());

                                map.put("vehicleNumber",receiveOrder.getVehicleNumber());
                                map.put("msg","收单成功");
                                map.put("result","规则应付运费与实付运费不一致"+tOrderInfo.getOrderBusinessCode());
                                mapList.add(map);
                            }else{
                                tOrderInfo.setDischargeWeight(tOrderInfo.getReceiveWeightNotesWeight());
                                tOrderInfo.setUserConfirmPaymentAmount(resp.getFreightPayable());
                                map.put("vehicleNumber",receiveOrder.getVehicleNumber());
                                map.put("msg","收单成功");
                                map.put("result",tOrderInfo.getOrderBusinessCode());
                                mapList.add(map);
                            }
                            //查询运单资源
                            ResultUtil goodsSourceAndLineInfo = goodsSourceAPI.selectGoodsSourceBySourceCode(tOrderInfo.getGoodSourceCode());
                            LinkedHashMap source = (LinkedHashMap) goodsSourceAndLineInfo.getData();
                            ObjectMapper objectMapper = new ObjectMapper();
                            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                            CompanySourceDTO companySourceDTO = objectMapper.convertValue(source, CompanySourceDTO.class);

                            BigDecimal serviceFee =  sendOrderUtil.calulateServiceFee(tOrderInfo.getCode(), tOrderInfo.getUserConfirmPaymentAmount(), Double.parseDouble(tOrderInfo.getSettledWeight().toString()));
                            if (null != companySourceDTO.getCapitalTransferPattern() && companySourceDTO.getCapitalTransferPattern().equals(DictEnum.MANAGERPATTERN.code)) {
                                if (null != serviceFee) {
                                    tOrderInfo.setServiceFee(serviceFee);
                                    tOrderInfo.setUserConfirmServiceFee(serviceFee);
                                }
                            }


                            Payment payment = new Payment();
                            payment.CollectionOrder(tOrderInfo, "");


                            TOrderLineGoodsCarriageRuleDetail record = xinFaOrderInfoMapper.selectByLineGoodsRelId(tOrderInfo.getLineGoodsRelId());
                            TOrderCastCalcSnapshot orderCastCalcSnapshot = new TOrderCastCalcSnapshot();

                            if(null != tOrderInfo.getPayMethod() && DictEnum.SINGLEPAY.code.equals(tOrderInfo.getPayMethod()) &&
                                    infoDetail.getIllegalCheck()){
                                //更新数据
                                orderInfoDetailMapper.updateByPrimaryKeySelective(infoDetail);
                            }

                            //将上一条置为无效:根据运单业务表id(code)
                            orderCastCalcSnapshotMapper.updateDataEnableByCode(tOrderInfo.getCode());
                            if(null!= record){
                                if (null != record.getRuleName() && StringUtils.isNotEmpty(record.getRuleName()) && !"null".equals(record.getRuleName())) {
                                    orderCastCalcSnapshot.setGoodsUnitPrice(companySourceDTO.getGoodsUnitPrice());
                                    orderCastCalcSnapshot.setLossCutExpression(record.getLossCutExpression());
                                    orderCastCalcSnapshot.setLossPayableExpression(record.getLossPayableExpression());
                                    orderCastCalcSnapshot.setRiseCutExpression(null != record.getRiseCutExpression() ? record.getRiseCutExpression() : "");
                                    orderCastCalcSnapshot.setRisePayableExpression(record.getRisePayableExpression());
                                    orderCastCalcSnapshot.setLineGoodsCarriageRuleId(record.getCarriageRuleId());
                                    orderCastCalcSnapshot.setCarriageZeroCutPayment(null == resp.getCarriageZeroCutPayment() ? new BigDecimal(0) : resp.getCarriageZeroCutPayment());
                                    orderCastCalcSnapshot.setCarriageZeroCutPaymentRule(record.getCarriageZeroCutPaymentRule());
                                    orderCastCalcSnapshot.setLineGoodsCarriageChangeId(collectOrderInfoDTO.getLineGoodsCarriageChangeId());
                                    orderCastCalcSnapshot.setFixCutFee(collectOrderInfoDTO.getFixCutFee());
                                    orderCastCalcSnapshot.setFixCutRemark(record.getCutFixRemark());
                                    orderCastCalcSnapshot.setOtherCutFee1(null == record.getOtherCutPayment1() ? new BigDecimal(0) : record.getOtherCutPayment1());
                                    orderCastCalcSnapshot.setOtherCutFee2(null == record.getOtherCutPayment2() ? new BigDecimal(0) : record.getOtherCutPayment2());
                                    orderCastCalcSnapshot.setOtherCutFee3(null == record.getOtherCutPayment3() ? new BigDecimal(0) : record.getOtherCutPayment3());
                                    orderCastCalcSnapshot.setOtherCutFee4(null == record.getOtherCutPayment4() ? new BigDecimal(0) : record.getOtherCutPayment4());
                                    orderCastCalcSnapshot.setOtherCutRemark1(null == record.getCutPaymentNote1() ? "" : record.getCutPaymentNote1());
                                    orderCastCalcSnapshot.setOtherCutRemark2(null == record.getCutPaymentNote2() ? "" : record.getCutPaymentNote2());
                                    orderCastCalcSnapshot.setOtherCutRemark3(null == record.getCutPaymentNote3() ? "" : record.getCutPaymentNote3());
                                    orderCastCalcSnapshot.setOtherCutRemark4(null == record.getCutPaymentNote4() ? "" : record.getCutPaymentNote4());
                                    orderCastCalcSnapshot.setGoodsCutImpurities(record.getGoodsCutImpurities());
                                    orderCastCalcSnapshot.setGoodsCutWater(record.getGoodsCutWater());
                                    orderCastCalcSnapshot.setRuleName(record.getRuleName());
                                    orderCastCalcSnapshot.setLoseOrRise(null == resp.getLossWeight() ? 0 : resp.getLossWeight());
                                    orderCastCalcSnapshot.setLoseOrRiseCut(null == resp.getLossDeduction() ? new BigDecimal(0) : resp.getLossDeduction());
                                    if (null != record.getToleranceItem() && StringUtils.isNotBlank(record.getToleranceItem())) {
                                        if (record.getToleranceItem().equals(DictEnum.ANDUNSHU.code)) {
                                            BigDecimal bigDecimal = record.getToleranceItemValue();
                                            bigDecimal = bigDecimal.setScale(4, BigDecimal.ROUND_HALF_UP);
                                            orderCastCalcSnapshot.setTolerantValueWeight(bigDecimal.doubleValue());
                                            orderCastCalcSnapshot.setTolerantValueCoefficient(null);
                                        }
                                        if (record.getToleranceItem().equals(DictEnum.ANXISHU.code)) {
                                            BigDecimal bigDecimal = record.getToleranceItemValue();
                                            bigDecimal = bigDecimal.setScale(4, BigDecimal.ROUND_HALF_UP);
                                            orderCastCalcSnapshot.setTolerantValueCoefficient(bigDecimal.doubleValue());
                                            orderCastCalcSnapshot.setTolerantValueWeight(null);
                                        }
                                    }
                                    orderCastCalcSnapshot.setCutImpuritiesIsCalcvalue(record.getCutImpuritiesIsCalcvalue());
                                    orderCastCalcSnapshot.setCutWaterIsCalcvalue(record.getCutWaterIsCalcvalue());
                                    orderCastCalcSnapshot.setDeficitWeight(null == resp.getDeficitWeight() ? 0 : resp.getDeficitWeight());
                                }
                            }else{

                            }
                            orderCastCalcSnapshot.setCarriagePayment(tOrderInfo.getRulePaymentAmount());
                            orderCastCalcSnapshot.setCarriageUnitPrice(tOrderInfo.getCurrentCarriageUnitPrice());
                            orderCastCalcSnapshot.setRealDeliverWeight(tOrderInfo.getPrimaryWeight());
                            orderCastCalcSnapshot.setRealDispathWeight(tOrderInfo.getDischargeWeight());
                            orderCastCalcSnapshot.setDeliverWeightNotesPhoto(tOrderInfo.getDeliverWeightNotesPhoto());
                            orderCastCalcSnapshot.setReceiveWeightNotesPhoto(tOrderInfo.getReceiveWeightNotesPhoto());
                            orderCastCalcSnapshot.setDeliverWeightNotesUploadTime(tOrderInfo.getDeliverWeightNotesTime());
                            orderCastCalcSnapshot.setReceiveWeightNotesUploadTime(tOrderInfo.getReceiveWeightNotesTime());
                            orderCastCalcSnapshot.setUserConfirmCarriagePayment(tOrderInfo.getUserConfirmPaymentAmount());
                            orderCastCalcSnapshot.setGoodsType(tOrderInfo.getGoodsName());
                            orderCastCalcSnapshot.setCode(IdWorkerUtil.getInstance().nextId());
                            orderCastCalcSnapshot.setDataEnable(true);
                            orderCastCalcSnapshot.setEnable(false);
                            orderCastCalcSnapshot.setOrderCode(tOrderInfo.getCode());
                            orderCastCalcSnapshot.setSettledWeight(tOrderInfo.getSettledWeight());
                            orderCastCalcSnapshot.setFixCutRemark(collectOrderInfoDTO.getFixCutRemark());
                            orderCastCalcSnapshotMapper.insertSelective(orderCastCalcSnapshot);

                            createState(tOrderInfo.getCode(), DictEnum.S0501.code, "");

                            CarDriverRelVO carDriverRelVO = new CarDriverRelVO();
                            carDriverRelVO.setBizCode(tOrderInfo.getOrderBusinessCode());
                            carDriverRelVO.setUserStatus("DISTRIBUTIONCHECKED");
                            carDriverRelVO.setCarStatus("AVAILABLECHECKED");
                            carDriverRelVO.setEndcarId(tOrderInfo.getVehicleId());
                            carDriverRelVO.setVehicleNumber(receiveOrder.getVehicleNumber());


                            //TODO 新增运单车辆轨迹
                            createOrderVehicleTrajectory(tOrderInfo.getCode(), tOrderInfo.getEndCoordinates(),
                                    tOrderInfo.getEndName(), DictEnum.RECEIVERORDERSIGN.code, carDriverRelVO, companySourceDTO);

                            //TODO 修改运单主表状态: 货运完成
                            //货运完成
                            tOrderInfo.setOrderExecuteStatus(DictEnum.O060.code);
                            // TODO 查询是否需要支付前审核
                            TLineGoodsRel tLineGoodsRel = lineGoodsAPI.selectById(tOrderInfo.getLineGoodsRelId());
                            if (null != tLineGoodsRel && null != tLineGoodsRel.getPaymentProcess()
                                    && tLineGoodsRel.getPaymentProcess() == 1) {
                                // 财务审核
                                tOrderInfo.setOrderPayStatus(DictEnum.M065.code);
                                //新增运单执行状态子表: 财务审核默认值
                                createState(tOrderInfo.getCode(), DictEnum.S0650.code, "");
                            }

                            if(null!=CurrentUser.getUserAccountId()){
                                tOrderInfo.setReceiveOrderUserId(CurrentUser.getUserAccountId());
                                tOrderInfo.setReceiveGoodsContacter(CurrentUser.getUserNickname());
                                tOrderInfo.setReceiveGoodsContacterPhone(CurrentUser.getUserAccountNo());
                            }
                            tOrderInfo.setReceiveOrderTime(new Date());
                            tOrderInfo.setUpdateTime(new Date());
                            if(null!=CurrentUser.getUserNickname()){
                                tOrderInfo.setUpdateUser(CurrentUser.getUserNickname());
                            }

                            if (!DictEnum.NOFETCHGP.code.equals(tOrderInfo.getVehicleGpsBdStatus())) {
                                tOrderInfo.setVehicleGpsBdStatus(DictEnum.NOFETCHGP.code);
                            }
                            orderInfoMapper.updateByPrimaryKey(tOrderInfo);
                            if (null != tOrderInfo.getOrderBusinessCode() && !"".equals(tOrderInfo.getOrderBusinessCode())) {
                                TCarInsurance tCarInsurance = carInsuranceMapper.selectByVehicleId(tOrderInfo.getVehicleId());
                                //获取保单信息
                                TOrderInsurance orderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(tOrderInfo.getOrderBusinessCode());
                                if(null == orderInsurance){
                                    orderInsurance = new TOrderInsurance();
                                }
                                //获取默认保费
                                SysParam insuredAmount = sysParamAPI.getParamByKey("PICC_FREIGHT_INSURANCE");
                                if(null != orderInsurance.getInsure() && orderInsurance.getInsure() != 1 &&
                                        null != orderInsurance.getInsuranceMethod() &&
                                        !orderInsurance.getInsuranceMethod().equals(InsuranceMethodsEnum.NOTINSURED.getKey())){
                                    if(null == tCarInsurance){
                                        if (null != companySourceDTO.getCapitalTransferType() &&
                                                DictEnum.PAYTOCAPTAIN.code.equals(companySourceDTO.getCapitalTransferType())) {
                                            SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.NOINSURANCE.code);
                                            if (null != paramByKey) {
                                                String[] split = paramByKey.getParamValue().split(",");
                                                if (split.length > 0) {
                                                    List<String> list = Arrays.asList(split);
                                                    if (null != tOrderInfo.getEndCarOwnerId()) {
                                                        if (list.contains(tOrderInfo.getEndCarOwnerId().toString())) {
                                                            if (null != orderInsurance.getInsure() && 2 == orderInsurance.getInsure()) {
                                                                orderInsurance.setInsure(0);
                                                                orderInsurance.setInsuredAmount(BigDecimal.ZERO);
                                                            }
                                                            TEndUserInfo tEndUserInfo = endSUserInfoAPI.selectByPrimaryKey(tOrderInfo.getEndCarOwnerId());
                                                            orderInsurance.setUninsuredCause("车队长" + tEndUserInfo.getRealName() + "不投保");
                                                        } else {
                                                            if(null != orderInsurance.getInsuranceMethod() &&
                                                                    !orderInsurance.getInsuranceMethod().equals(InsuranceMethodsEnum.NOTINSURED.getKey())){
                                                                //实收<=38,投保状态是其他2
                                                                if (tOrderInfo.getDischargeWeight() <= 38) {
                                                                    orderInsurance.setInsure(2);
                                                                    orderInsurance.setUninsuredCause("实收重量未超38吨，平台收取保费");
                                                                    orderInsurance.setInsuredAmount(new BigDecimal(insuredAmount.getParamValue()));
                                                                } else {
                                                                    //实收>38吨，投保状态是不投保0;
                                                                    orderInsurance.setInsure(0);
                                                                    orderInsurance.setUninsuredCause("实收重量大于38吨");
                                                                    orderInsurance.setInsuredAmount(BigDecimal.ZERO);
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        } /*else if (null != tOrderInfo.getGoodSourceCode()) {
                                        TGoodsSourceInfo goodsSourceInfo1 = new TGoodsSourceInfo();
                                        goodsSourceInfo1.setCode(tOrderInfo.getGoodSourceCode());
                                        TGoodsSourceInfo result = goodsSourceAPI.selectGoodsSourceInfo(goodsSourceInfo1);
                                        if (null != result) {
                                            if (null != result.getInsuranceMethod() && result.getInsuranceMethod().equals(InsuranceMethodsEnum.NOTINSURED.getKey())) {
                                                // 收单时，实收<=38吨，投保状态是其他2;
                                                if (tOrderInfo.getDischargeWeight() <= 38) {
                                                    orderInsurance.setInsure(2);
                                                    orderInsurance.setUninsuredCause("实收重量未超38吨，平台收取保费");
                                                    orderInsurance.setInsuredAmount(new BigDecimal(insuredAmount.getParamValue()));
                                                } else {
                                                    orderInsurance.setInsure(0);
                                                    orderInsurance.setInsuredAmount(BigDecimal.ZERO);
                                                    orderInsurance.setUninsuredCause("实收重量大于38吨");
                                                }
                                            }
                                        }
                                    } */else {
                                            if (null != tOrderInfo.getDischargeWeight() && tOrderInfo.getDischargeWeight() > 38) {
                                                if (2 == orderInsurance.getInsure()) {
                                                    orderInsurance.setInsure(0);
                                                    orderInsurance.setInsuredAmount(BigDecimal.ZERO);
                                                    orderInsurance.setUninsuredCause("装货重量大于38吨");
                                                }
                                            } else {
                                                if (0 == orderInsurance.getInsure() && null != orderInsurance.getUninsuredCause() && "装货重量大于38吨".equals(orderInsurance.getUninsuredCause())) {
                                                    orderInsurance.setInsure(2);
                                                    orderInsurance.setInsuredAmount(new BigDecimal(insuredAmount.getParamValue()));
                                                    orderInsurance.setUninsuredCause("实收重量未超38吨，平台收取保费");
                                                }
                                            }
                                        }
                                    }else{
                                        if(orderInsurance.getInsuranceMethod().equals(InsuranceMethodsEnum.MUSTBEINSURED.getKey())){
                                            orderInsurance.setInsure(0);
                                            orderInsurance.setInsuredAmount(BigDecimal.ZERO);
                                            orderInsurance.setUninsuredCause("车辆已投保");
                                        }
                                    }
                                }

                                if(null != orderInsurance.getId()){
                                    orderInsurance.setUpdateUser(CurrentUser.getUserNickname());
                                    orderInsurance.setUpdateTime(new Date());
                                    orderInsuranceMapper.updateByPrimaryKeySelective(orderInsurance);
                                }
                            }
                            //etc运单结束交互
                            tOrderInfoService.sendEtcData(tOrderInfo.getOrderBusinessCode());


                            // 放入mq删除轨迹中间表
                            MQMessage mqMessage = new MQMessage();
                            mqMessage.setTopic(MqMessageTopic.ORDERCARLOCATION);
                            mqMessage.setTag(MqMessageTag.DELETE);
                            mqMessage.setBody(tOrderInfo.getCode());
                            mqMessage.setKey(IdWorkerUtil.getInstance().nextId());
                            mqAPI.sendMessage(mqMessage);

                            //想易煤网发送信息
                            tOrderInfoService.sendOrderInfoToYimei(tOrderInfo.getOrderBusinessCode());

//                            //快货运数据上报 - 司机/车辆/客户/收款账户/托运方合同
//                            tOrderInfoService.sendOrderInfoToKhy(tOrderInfo.getCode());
//
//                            //安徽数据上报
//                            tOrderAnHuiReportService.basicInfo(tOrderInfo.getCode());

                        }

                    }

                }else{//未匹配到运单的
                    map.put("vehicleNumber",receiveOrder.getVehicleNumber());
                    map.put("msg","收单失败");
                    map.put("result","未匹配到运单");
                    mapList.add(map);
                }
            }
        }catch (Exception e){
            log.info("批量导入收单失败! {}", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("收单失败!");
            }
        }

        return ResultUtil.ok(mapList);
    }

    /**
     * 收单
     *
     * @param record
     * @return
     * <AUTHOR>
     */
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil recieveOrder(OrderDetailVO record) {
        try {
            TOrderInfo order = orderInfoMapper.selectOrderByCode(record.getOrderCode());
            TOrderCastChanges tOrderCastChanges = orderCastChangesMapper.selectByNewOne(order.getCode());
            TGoodsSourceInfo tGoodsSourceInfo = new TGoodsSourceInfo();
            tGoodsSourceInfo.setLineGoodsRelId(order.getLineGoodsRelId());
            TOrderInfoDetail infoDetail = orderInfoDetailMapper.selectByOrderId(order.getId());//运单详情信息
            // 获取运单的保修信息
            TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(order.getOrderBusinessCode());

            //查询运单详情表，当“校验司机是否违规”开启，并且运单是单笔支付运单
            if(null != order.getPayMethod() && DictEnum.SINGLEPAY.code.equals(order.getPayMethod()) &&
                    infoDetail.getIllegalCheck()){
                //校验是否已卸货签到，若未卸货签到，将运单判定为违规（违规原因：未在卸货地范围内签到）
                if(!DictEnum.M040.code.equals(order.getOrderExecuteStatus())){
                    infoDetail.setIllegalOrder(true);
                    if(null == infoDetail.getIllegalOrderReason() || "".equals(infoDetail.getIllegalOrderReason())){
                        infoDetail.setIllegalOrderReason("未在卸货地范围内签到");
                    }else{
                        infoDetail.setIllegalOrderReason(infoDetail.getIllegalOrderReason()+",未在卸货地范围内签到");
                    }
                }
                //校验发货磅单至卸货磅单时间段内的轨迹，若轨迹不全或获取不到轨迹，将运单判定为违规（违规原因：轨迹不完整）
                //先判断是否关闭过北斗，如果关闭过北斗定位，则轨迹不完整
                //如果没有关闭过北斗，或者没有扫描到的，则查询车辆轨迹，看是否存在“服务自动抓取（SERVERCATCH）”的数据
                if(infoDetail.getCloseGps()){
                    infoDetail.setIllegalOrder(true);
                    if(null == infoDetail.getIllegalOrderReason() || "".equals(infoDetail.getIllegalOrderReason())){
                        infoDetail.setIllegalOrderReason("轨迹不完整");
                    }else{
                        infoDetail.setIllegalOrderReason(infoDetail.getIllegalOrderReason()+",轨迹不完整");
                    }
                } else {
                    List<TrajectoryMongo> list = trajectoryMongoDao.getAllByOrderCode(record.getOrderCode());//根据运单code查询车辆轨迹
                    int sum = 0;
                    if(list.size() > 0){
                        //遍历集合，查询是否存在“服务自动抓取（SERVERCATCH）”的数据
                        for (TrajectoryMongo trajectoryMongo : list) {
                            if(DictEnum.SERVERCATCH.code.equals(trajectoryMongo.getTrajectoryReceiveMethod())){
                                sum++;
                            }
                        }
                        if(sum == 0){
                            infoDetail.setIllegalOrder(true);
                            if(null == infoDetail.getIllegalOrderReason() || "".equals(infoDetail.getIllegalOrderReason())){
                                infoDetail.setIllegalOrderReason("轨迹不完整");
                            }else{
                                infoDetail.setIllegalOrderReason(infoDetail.getIllegalOrderReason()+",轨迹不完整");
                            }
                        }
                    }
                }
            }
            // 运费判断
            ResultUtil resultUtil = OrderDeductionUtil.validateAmountAndReturnResult(record.getUserConfirmCarriagePayment(), record.getUserConfirmServiceFee(), tOrderCastChanges.getCapitalTransferPattern(), tOrderInsurance, infoDetail);
            if (!Objects.equals(resultUtil.getCode(), CodeEnum.SUCCESS.getCode())) {
                return resultUtil;
            }

            TOrderInfoWeight orderInfoWeight = orderInfoWeightMapper.selectByOrderId(order.getId());
            // 如果是货源大厅的运单，修改货源库存
//            boolean resourceIfHall = orderGoodsSourceInfoMapper.selectResourceIfHall(order.getLineGoodsRelId());
//            if (resourceIfHall) {
//                if (null != record.getRealDispathWeight()) {
//                    Double primaryWeight = order.getPrimaryWeight();
//                    if (null != order.getReceiveWeightNotesWeight()) {
//                        primaryWeight = order.getReceiveWeightNotesWeight();
//                    } else if (null != order.getDeliverWeightNotesWeight()) {
//                        primaryWeight = order.getDeliverWeightNotesWeight();
//                    }
//                    // 判断是否相等
//                    if (primaryWeight.compareTo(record.getRealDispathWeight()) != 0) {
//                        // 修改货源库存
//                        TGoodsSourceInfo goodsSourceInfo = orderGoodsSourceInfoMapper.selectByByLineGoodsRelId(order.getLineGoodsRelId());
//                        TGoodsSourceInfo goodsSourceInfoForUpdate = new TGoodsSourceInfo();
//                        goodsSourceInfoForUpdate.setId(goodsSourceInfo.getId());
//                        goodsSourceInfoForUpdate.setEstimateGoodsWeight(goodsSourceInfo.getEstimateGoodsWeight() + primaryWeight);
//                        goodsSourceInfoForUpdate.setEstimateGoodsWeight(goodsSourceInfoForUpdate.getEstimateGoodsWeight() - record.getRealDispathWeight());
//                        if (goodsSourceInfoForUpdate.getEstimateGoodsWeight().compareTo(0d) < 0) {
//                            return ResultUtil.error("货源库存不足");
//                        } else if (goodsSourceInfoForUpdate.getEstimateGoodsWeight().compareTo(0d) == 0) {
//                            goodsSourceInfoForUpdate.setStatus(DictEnum.CLOSESOURCE.code);
//                        }
//                        goodsSourceInfoForUpdate.setUpdateTime(new Date());
//                        orderGoodsSourceInfoMapper.updateEstimateGoodsWeight(goodsSourceInfoForUpdate);
//                    }
//                }
//            }
            Double estimatedTravelTime = record.getEstimatedTravelTime();
            int v = (int)(estimatedTravelTime * 3600);//换算成秒
            Date createTime = order.getCreateTime();//建单时间
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(createTime);
            calendar.add(Calendar.SECOND, v);
            Date newDate = calendar.getTime();//预计收单时间
            Date date = new Date();
            SimpleDateFormat format = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分");
            String dateStr = format.format(newDate);
            if(date.getTime()< newDate.getTime()){
                return ResultUtil.error("未到达收单时间，请于"+dateStr+"后再进行收单操作。");
            }
            if(null != order.getPayMethod() && !"".equals(order.getPayMethod())){
                if(order.getPayMethod().equals(DictEnum.NODEPAYPROPORTION.code)
                        || order.getPayMethod().equals(DictEnum.NODEPAYFIXED.code)){
                    if(!"M040".equals(order.getOrderExecuteStatus())){
                        return ResultUtil.error("此运单没有卸货地签到，不可收单");
                    }
                }
            }
            if(!"M010".equals(order.getOrderExecuteStatus()) && !"M020".equals(order.getOrderExecuteStatus())
                    && !"M030".equals(order.getOrderExecuteStatus()) && !"M040".equals(order.getOrderExecuteStatus())){
                return ResultUtil.error("此运单状态，不可收单");
            }
            if(null != order.getPayMethod() && DictEnum.SINGLEPAY.code.equals(order.getPayMethod()) &&
                    infoDetail.getIllegalCheck()){
                //更新数据
                orderInfoDetailMapper.updateByPrimaryKeySelective(infoDetail);
            }
            if (null != order.getOrderPayStatus() && order.getOrderPayStatus().equals(DictEnum.M045.code)){
                //TODO 将上一条置为无效:根据运单业务表id(code)
                orderCastCalcSnapshotMapper.updateDataEnableByCode(record.getOrderCode());
            }

            //TODO 新增运单快照主表
            TOrderCastCalcSnapshot orderCastCalcSnapshot = createOrderCastCalcSnapshot(record, order);
            orderCastCalcSnapshotMapper.insertSelective(orderCastCalcSnapshot);

            //TODO 判断司机是否卸货签到、装货签到：如果未签到，添加运单执行状态子表：货主确认卸货:S0403
            if (!order.getOrderExecuteStatus().equals(DictEnum.M030.code) && !order.getOrderExecuteStatus().equals(DictEnum.M040.code)){
                createState(order.getCode(), DictEnum.S0403.code, "");
            }

            //TODO 新增运单执行状态子表:收单员确认卸货
            createState(order.getCode(), DictEnum.S0501.code, "");

            CarDriverRelVO carDriverRelVO = new CarDriverRelVO();
            try {
                //TODO 更新C端用户和车辆的状态
                carDriverRelVO.setBizCode(order.getOrderBusinessCode());
                carDriverRelVO.setUserStatus("DISTRIBUTIONCHECKED");
                carDriverRelVO.setCarStatus("AVAILABLECHECKED");
                appCommonAPI.updateCarEnduserStatus(carDriverRelVO);
            } catch (Exception e){
                log.error("修改车辆司机状态失败", e);
                throw new RuntimeException("修改车辆司机状态失败");
            }

            carDriverRelVO.setEndcarId(order.getVehicleId());
            carDriverRelVO.setVehicleNumber(record.getVehicleNumber());

            //查询运单资源
            ResultUtil goodsSourceAndLineInfo = goodsSourceAPI.selectGoodsSourceBySourceCode(order.getGoodSourceCode());
            LinkedHashMap source = (LinkedHashMap) goodsSourceAndLineInfo.getData();
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            CompanySourceDTO companySourceDTO = objectMapper.convertValue(source, CompanySourceDTO.class);

            //TODO 新增运单车辆轨迹
            createOrderVehicleTrajectory(record.getOrderCode(), order.getEndCoordinates(),
                    order.getEndName(), DictEnum.RECEIVERORDERSIGN.code, carDriverRelVO, companySourceDTO);

            //update by xyz 2019.6.21 收单时将修改运单和账目操作同步
            try {
                //TODO 修改运单主表状态: 货运完成
                order.setId(order.getId());
                //货运完成
                order.setOrderExecuteStatus(DictEnum.O060.code);
                // TODO 查询是否需要支付前审核
                TOrderInfo tOrderInfo = orderInfoMapper.selectByPrimaryKey(order.getId());
                TLineGoodsRel tLineGoodsRel = lineGoodsAPI.selectById(tOrderInfo.getLineGoodsRelId());
                if (null != tLineGoodsRel && null != tLineGoodsRel.getPaymentProcess()
                        && tLineGoodsRel.getPaymentProcess() == 1) {
                    // 财务审核
                    order.setOrderPayStatus(DictEnum.M065.code);
                    //新增运单执行状态子表: 财务审核默认值
                    createState(order.getCode(), DictEnum.S0650.code, "");
                }

                if(DictEnum.CARRIAGE_PRICE_UNIT_BOX.code.equals(record.getCarriagePriceUnit())){
                    orderInfoWeight.setBoxNum(record.getBoxNum());
                    orderInfoWeight.setDeliverWeightNotesTime1(record.getDeliverWeightNotesTime1());
                    orderInfoWeight.setDeliverWeightNotesWeight1(record.getDeliverWeightNotesWeight1());
                    orderInfoWeight.setDeliverWeightNotesPhoto1(record.getDeliverWeightNotesPhoto1());
                    orderInfoWeight.setReceiveWeightNotesTime1(record.getReceiveWeightNotesTime1());
                    orderInfoWeight.setReceiveWeightNotesWeight1(record.getReceiveWeightNotesWeight1());
                    orderInfoWeight.setReceiveWeightNotesPhoto1(record.getReceiveWeightNotesPhoto1());
                    orderInfoWeight.setGrossWeight1(record.getGrossWeight1());
                    orderInfoWeight.setPrimaryWeight1(record.getPrimaryWeight1());
                    orderInfoWeight.setDischargeWeight1(record.getDischargeWeight1());
                    orderInfoWeight.setCarriageUnitPrice1(record.getCarriageUnitPrice1());
                    if(record.getBoxNum()<2){
                        order.setPrimaryWeight(Double.parseDouble((record.getPrimaryWeight1()).toString()));
                        order.setDischargeWeight(Double.parseDouble((record.getDischargeWeight1()).toString()));
                        order.setSettledWeight(record.getDischargeWeight1());
                        if(null!=record.getGrossWeight1()){
                            order.setGrossWeight(Double.parseDouble(record.getGrossWeight1().toString()));//毛重
                        }
                        if(null!=record.getDeliverWeightNotesTime1()){
                            order.setDeliverWeightNotesTime(record.getDeliverWeightNotesTime1());
                        }
                        if(null!=record.getReceiveWeightNotesTime1()){
                            order.setReceiveWeightNotesTime(record.getReceiveWeightNotesTime1());
                        }
                    }else{
                        orderInfoWeight.setDeliverWeightNotesTime2(record.getDeliverWeightNotesTime2());
                        orderInfoWeight.setDeliverWeightNotesWeight2(record.getDeliverWeightNotesWeight2());
                        orderInfoWeight.setDeliverWeightNotesPhoto2(record.getDeliverWeightNotesPhoto2());
                        orderInfoWeight.setReceiveWeightNotesTime2(record.getReceiveWeightNotesTime2());
                        orderInfoWeight.setReceiveWeightNotesWeight2(record.getReceiveWeightNotesWeight2());
                        orderInfoWeight.setReceiveWeightNotesPhoto2(record.getReceiveWeightNotesPhoto2());
                        orderInfoWeight.setGrossWeight2(record.getGrossWeight2());
                        orderInfoWeight.setPrimaryWeight2(record.getPrimaryWeight2());
                        orderInfoWeight.setDischargeWeight2(record.getDischargeWeight2());
                        orderInfoWeight.setCarriageUnitPrice2(record.getCarriageUnitPrice2());
                        order.setPrimaryWeight(Double.parseDouble((record.getPrimaryWeight1().add(record.getPrimaryWeight2())).toString()));
                        order.setDischargeWeight(Double.parseDouble((record.getDischargeWeight1().add(record.getDischargeWeight2())).toString()));
                        if(null!=record.getDeliverWeightNotesTime1()){
                            order.setDeliverWeightNotesTime(record.getDeliverWeightNotesTime1());
                        }else if(null!=record.getDeliverWeightNotesTime2()){
                            order.setDeliverWeightNotesTime(record.getDeliverWeightNotesTime2());
                        }
                        if(null!=record.getReceiveWeightNotesTime1()){
                            order.setReceiveWeightNotesTime(record.getReceiveWeightNotesTime1());
                        }else if(null!=record.getReceiveWeightNotesTime2()){
                            order.setReceiveWeightNotesTime(record.getReceiveWeightNotesTime2());
                        }

                        order.setSettledWeight(record.getDischargeWeight1().add(record.getDischargeWeight2()));
                        if(null!=record.getGrossWeight1() && null!=record.getGrossWeight2()){
                            order.setGrossWeight(Double.parseDouble((record.getGrossWeight1().add(record.getGrossWeight2())).toString()));//毛重
                        }
                    }

                    orderInfoWeightMapper.updateByPrimaryKeySelective(orderInfoWeight);


                }else{
                    order.setDeliverWeightNotesWeight(record.getRealDeliverWeight());
                    order.setReceiveWeightNotesWeight(record.getRealDispathWeight());
                    order.setPrimaryWeight(orderCastCalcSnapshot.getRealDeliverWeight());
                    order.setDischargeWeight(record.getRealDispathWeight());
                    order.setSettledWeight(record.getSettledWeight());
                    //当按车时，结算重量取卸货重量
                    if(DictEnum.CARRIAGE_PRICE_UNIT_CAR.code.equals(record.getCarriagePriceUnit())){
                        order.setSettledWeight(BigDecimal.valueOf(record.getRealDispathWeight()));
                    }
                    order.setGrossWeight(record.getGrossWeight());//毛重
                    order.setDeliverWeightNotesPhoto(orderCastCalcSnapshot.getDeliverWeightNotesPhoto());
                    order.setReceiveWeightNotesPhoto(orderCastCalcSnapshot.getReceiveWeightNotesPhoto());
                    order.setDeliverWeightNotesTime(orderCastCalcSnapshot.getDeliverWeightNotesUploadTime());
                    order.setReceiveWeightNotesTime(orderCastCalcSnapshot.getReceiveWeightNotesUploadTime());
                }
                //计算规则计算的应付运费
                order.setRulePaymentAmount(orderCastCalcSnapshot.getCarriagePayment());
                //添加用户确认的运费 add by zhangjiji 2019/6/13
                order.setUserConfirmPaymentAmount(orderCastCalcSnapshot.getUserConfirmCarriagePayment());

                if(null!=CurrentUser.getUserAccountId()){
                    order.setReceiveOrderUserId(CurrentUser.getUserAccountId());
                    order.setReceiveGoodsContacter(CurrentUser.getUserNickname());
                    order.setReceiveGoodsContacterPhone(CurrentUser.getUserAccountNo());
                }
                order.setReceiveOrderTime(new Date());
                order.setUpdateTime(new Date());
                if(null!=CurrentUser.getUserNickname()){
                    order.setUpdateUser(CurrentUser.getUserNickname());
                }
                if (null != record.getCapitalTransferPattern() && record.getCapitalTransferPattern().equals(DictEnum.MANAGERPATTERN.code)) {
                    if (null != record.getServiceFee()) {
                        order.setServiceFee(record.getServiceFee());
                    }
                    if (null != record.getUserConfirmServiceFee()) {
                        order.setUserConfirmServiceFee(record.getUserConfirmServiceFee());
                    }
                }
                // 保存业务部
                if (null != record.getCapitalTransferPattern() && StringUtils.isNotBlank(record.getCapitalTransferPattern())) {
                    if (null != record.getEndAgentId()) {
                        order.setEndAgentId(record.getEndAgentId());
                    } else {
                        order.setEndAgentId(null);
                    }
                }
                if (!DictEnum.NOFETCHGP.code.equals(order.getVehicleGpsBdStatus())) {
                    order.setVehicleGpsBdStatus(DictEnum.NOFETCHGP.code);
                }
                orderInfoMapper.updateByPrimaryKey(order);

                TOrderInfo info = orderInfoMapper.selectByPrimaryKey(order.getId());
                assert info != null;
                if(null != info.getOrderBusinessCode()){
                    TCarInsurance tCarInsurance = carInsuranceMapper.selectByVehicleId(info.getVehicleId());
                    TOrderInsurance orderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(info.getOrderBusinessCode());
                    if(null == orderInsurance){
                        orderInsurance = new TOrderInsurance();
                    }
                    if (null == tCarInsurance) {
                        //获取默认保费
                        SysParam insuredAmount = sysParamAPI.getParamByKey("PICC_FREIGHT_INSURANCE");
                        if (null != orderInsurance.getInsure() && orderInsurance.getInsure() != 1 && null != companySourceDTO.getCapitalTransferType() &&
                                DictEnum.PAYTOCAPTAIN.code.equals(companySourceDTO.getCapitalTransferType())) {
                            SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.NOINSURANCE.code);
                            if (null != paramByKey) {
                                String[] split = paramByKey.getParamValue().split(",");
                                if (split.length > 0) {
                                    List<String> list = Arrays.asList(split);
                                    if (null != info.getEndCarOwnerId()) {
                                        if (list.contains(info.getEndCarOwnerId().toString())) {
                                            TEndUserInfo tEndUserInfo = endSUserInfoAPI.selectByPrimaryKey(info.getEndCarOwnerId());
                                            orderInsurance.setUninsuredCause("车队长" + tEndUserInfo.getRealName() + "不投保");
                                            if (null != orderInsurance.getInsure() && 2 == orderInsurance.getInsure()) {
                                                orderInsurance.setInsure(0);
                                                orderInsurance.setInsuredAmount(BigDecimal.ZERO);
                                            }
                                            //收单时，更换成车队长B
                                        } else {
                                            if (null != orderInsurance.getInsuranceMethod() && !InsuranceMethodsEnum.NOTINSURED.getKey().equals(orderInsurance.getInsuranceMethod())) {
                                                //实收<=38,投保状态是其他2，保存保费
                                                if (info.getDischargeWeight() <= 38) {
                                                    orderInsurance.setInsure(2);
                                                    orderInsurance.setUninsuredCause("实收重量未超38吨，平台收取保费");
                                                    orderInsurance.setInsuredAmount(new BigDecimal(insuredAmount.getParamValue()));
                                                    //实收>38吨，投保状态是不投保0;
                                                } else {
                                                    orderInsurance.setInsure(0);
                                                    orderInsurance.setInsuredAmount(BigDecimal.ZERO);
                                                    orderInsurance.setUninsuredCause("实收重量大于38吨");
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        } else if (null != orderInsurance.getInsure() && 0 == orderInsurance.getInsure() && null != orderInsurance.getUninsuredCause() &&
                                "装货重量大于38吨".equals(orderInsurance.getUninsuredCause())) {
                            // >38吨不投保，收单时，
                            if (null != info.getDischargeWeight()) {
                                // 实收<=38吨，投保状态是其他2，保存保费
                                if (info.getDischargeWeight() <= 38) {
                                    orderInsurance.setInsure(2);
                                    orderInsurance.setInsuredAmount(new BigDecimal(insuredAmount.getParamValue()));
                                    orderInsurance.setUninsuredCause("实收重量未超38吨，平台收取保费");
                                }
                            }
                        } else if (null != orderInsurance.getInsuranceMethod() &&
                                InsuranceMethodsEnum.MUSTBEINSURED.getKey().equals(orderInsurance.getInsuranceMethod()) &&
                                null != orderInsurance.getInsure() && orderInsurance.getInsure() == 0) {
                            // 迁移后的货源必需投保
                            // 收单时，实收<=38吨，投保状态是其他2，保存保费
                            if (null != info.getDischargeWeight() && info.getDischargeWeight() <= 38) {
                                orderInsurance.setInsure(2);
                                orderInsurance.setUninsuredCause("实收重量未超38吨，平台收取保费");
                                orderInsurance.setInsuredAmount(new BigDecimal(insuredAmount.getParamValue()));
                            }else {
                                //实收>38吨，投保状态是不投保0;
                                orderInsurance.setInsure(0);
                                orderInsurance.setInsuredAmount(BigDecimal.ZERO);
                                orderInsurance.setUninsuredCause("实收重量大于38吨");
                            }
                        }
                    }else{
                        if(null != orderInsurance.getInsure() && 1 != orderInsurance.getInsure() &&
                                null != orderInsurance.getInsuranceMethod() &&
                                orderInsurance.getInsuranceMethod().equals(InsuranceMethodsEnum.MUSTBEINSURED.getKey())){
                            orderInsurance.setInsure(0);
                            orderInsurance.setInsuredAmount(BigDecimal.ZERO);
                            orderInsurance.setUninsuredCause("车辆已投保");
                        }
                    }
                    if(null != orderInsurance.getId()){
                        orderInsurance.setUpdateUser(CurrentUser.getUserNickname());
                        orderInsurance.setUpdateTime(new Date());
                        orderInsuranceMapper.updateByPrimaryKeySelective(orderInsurance);
                    }
                }
                //etc运单结束交互
                tOrderInfoService.sendEtcData(info.getOrderBusinessCode());
                Payment payment = new Payment();
                payment.CollectionOrder(info, "");

                //节点支付模式
                if (DictEnum.NODEPAYFIXED.code.equals(info.getPayMethod())
                        || DictEnum.NODEPAYPROPORTION.code.equals(info.getPayMethod())) {
                    TOrderInfo orderInfo = orderInfoMapper.selectOrderByCode(info.getCode());
                    tOrderPayRuleService.nodePayUpdate(orderInfo);

                    BigDecimal sumFee = tOrderPayRuleMapper.selectSumFee(tOrderInfo.getCode());
                    TOrderPayRule re = new TOrderPayRule();
                    re.setOrderCode(tOrderInfo.getCode());
                    re.setPayStatus(DictEnum.PACKUNPAID.code);
                    //re.setPayNodeType(DictEnum.SDPAYNODE.code);
                    TOrderPayRule tOrderPayRule1 = tOrderPayRuleMapper.selectByOrderCodeAndType(re);
                    if (null != sumFee && null != tOrderPayRule1) {
                        tOrderPayRule1.setSurplusPaymentFee(order.getUserConfirmPaymentAmount().subtract(sumFee));
                        tOrderPayRuleMapper.updateByPrimaryKeySelective(tOrderPayRule1);
                    }
                }
                // 放入mq删除轨迹中间表
                MQMessage mqMessage = new MQMessage();
                mqMessage.setTopic(MqMessageTopic.ORDERCARLOCATION);
                mqMessage.setTag(MqMessageTag.DELETE);
                mqMessage.setBody(info.getCode());
                mqMessage.setKey(IdWorkerUtil.getInstance().nextId());
                mqAPI.sendMessage(mqMessage);

                //想易煤网发送信息
                tOrderInfoService.sendOrderInfoToYimei(tOrderInfo.getOrderBusinessCode());

//                //快货运数据上报 - 司机/车辆/客户/收款账户/托运方合同
//                tOrderInfoService.sendOrderInfoToKhy(order.getCode());
//
//                //安徽数据上报
//                tOrderAnHuiReportService.basicInfo(order.getCode());

            } catch (Exception e) {
                log.info("ZJJ-002:收单失败!, {}", ThrowableUtil.getStackTrace(e));
                String message = e.getMessage();
                if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)){
                    message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再收单。";
                }
                if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)){
                    message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再收单";
                }
                if (StringUtils.checkChineseCharacter(message)){
                    throw new RuntimeException(message);
                } else {
                    throw new RuntimeException("ZJJ-002:收单失败!");
                }
            }
        }catch (Exception e){
            log.info("收单失败, {}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("收单失败!");
            }
        }
        return ResultUtil.ok();
    }

    /**
     * 磅房自动收单
     *
     * @param record
     * @return
     * <AUTHOR>
     */
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil recieveOrderAutomatic(OrderDetailVO record) {
        String orderKey = "RECIEVE" + record.getOrderCode();
        boolean orderLock = false;
        try {
            //TODO 查询运单状态
            if (!redisUtil.hasKey(orderKey)) {
                orderLock = redisUtil.set(orderKey, "lock");
            }
            if (orderLock){
                TOrderInfo order = orderInfoMapper.selectOrderByCode(record.getOrderCode());
                if(null != order.getPayMethod() && !"".equals(order.getPayMethod())){
                    if(order.getPayMethod().equals(DictEnum.NODEPAYPROPORTION.code)
                            || order.getPayMethod().equals(DictEnum.NODEPAYFIXED.code)){
                        if(!"M040".equals(order.getOrderExecuteStatus())){
                            return ResultUtil.error("此运单没有卸货地签到，不可收单");
                        }
                    }
                }
                if(!"M010".equals(order.getOrderExecuteStatus()) && !"M020".equals(order.getOrderExecuteStatus())
                        && !"M030".equals(order.getOrderExecuteStatus()) && !"M040".equals(order.getOrderExecuteStatus())){
                    return ResultUtil.error("此运单状态，不可收单");
                }
                //TODO 新增运单快照主表
                TOrderCastCalcSnapshot orderCastCalcSnapshot = createOrderCastCalcSnapshot(record, order);
                orderCastCalcSnapshotMapper.insertSelective(orderCastCalcSnapshot);

                //TODO 判断司机是否卸货签到、装货签到：如果未签到，添加运单执行状态子表：货主确认卸货:S0403
                if (!order.getOrderExecuteStatus().equals(DictEnum.M030.code) && !order.getOrderExecuteStatus().equals(DictEnum.M040.code)){
                    createState(order.getCode(), DictEnum.S0403.code, "");
                }

                //TODO 新增运单执行状态子表:收单员确认卸货
                createState(order.getCode(), DictEnum.S0501.code, "");

                CarDriverRelVO carDriverRelVO = new CarDriverRelVO();
                try {
                    //TODO 更新C端用户和车辆的状态
                    carDriverRelVO.setBizCode(order.getOrderBusinessCode());
                    carDriverRelVO.setUserStatus("DISTRIBUTIONCHECKED");
                    carDriverRelVO.setCarStatus("AVAILABLECHECKED");
                    appCommonAPI.updateCarEnduserStatus(carDriverRelVO);
                } catch (Exception e){
                    log.error("修改车辆司机状态失败", e);
                    throw new RuntimeException("修改车辆司机状态失败");
                }

                carDriverRelVO.setEndcarId(order.getVehicleId());
                carDriverRelVO.setVehicleNumber(record.getVehicleNumber());

                //查询运单资源
                ResultUtil goodsSourceAndLineInfo = goodsSourceAPI.selectGoodsSourceBySourceCode(order.getGoodSourceCode());
                LinkedHashMap source = (LinkedHashMap) goodsSourceAndLineInfo.getData();
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                CompanySourceDTO companySourceDTO = objectMapper.convertValue(source, CompanySourceDTO.class);

                //TODO 新增运单车辆轨迹
                createOrderVehicleTrajectory(record.getOrderCode(), order.getEndCoordinates(),
                        order.getEndName(), DictEnum.RECEIVERORDERSIGN.code, carDriverRelVO, companySourceDTO);

                //update by xyz 2019.6.21 收单时将修改运单和账目操作同步
                try {
                    //TODO 修改运单主表状态: 货运完成
                    order.setId(order.getId());
                    //货运完成
                    order.setOrderExecuteStatus(DictEnum.O060.code);
                    // TODO 查询是否需要支付前审核
                    TOrderInfo tOrderInfo = orderInfoMapper.selectByPrimaryKey(order.getId());
                    TLineGoodsRel tLineGoodsRel = lineGoodsAPI.selectById(tOrderInfo.getLineGoodsRelId());
                    if (null != tLineGoodsRel && null != tLineGoodsRel.getPaymentProcess()
                            && tLineGoodsRel.getPaymentProcess() == 1) {
                        // 财务审核
                        order.setOrderPayStatus(DictEnum.M065.code);
                        //新增运单执行状态子表: 财务审核默认值
                        createState(order.getCode(), DictEnum.S0650.code, "");
                    }
                    //计算规则计算的应付运费
                    order.setRulePaymentAmount(orderCastCalcSnapshot.getCarriagePayment());
                    //添加用户确认的运费 add by zhangjiji 2019/6/13
                    order.setUserConfirmPaymentAmount(orderCastCalcSnapshot.getUserConfirmCarriagePayment());
                    order.setPrimaryWeight(orderCastCalcSnapshot.getRealDeliverWeight());
                    order.setDischargeWeight(record.getRealDispathWeight());
                    order.setSettledWeight(record.getSettledWeight());
                    order.setDeliverWeightNotesPhoto(orderCastCalcSnapshot.getDeliverWeightNotesPhoto());
                    order.setReceiveWeightNotesPhoto(orderCastCalcSnapshot.getReceiveWeightNotesPhoto());
                    order.setDeliverWeightNotesTime(orderCastCalcSnapshot.getDeliverWeightNotesUploadTime());
                    order.setReceiveWeightNotesTime(orderCastCalcSnapshot.getReceiveWeightNotesUploadTime());
                    if(null!=CurrentUser.getUserAccountId()){
                        order.setReceiveOrderUserId(CurrentUser.getUserAccountId());
                    }
                    order.setReceiveOrderTime(new Date());
                    order.setUpdateTime(new Date());
                    if(null!=CurrentUser.getUserNickname()){
                        order.setUpdateUser(CurrentUser.getUserNickname());
                    }
                    if (null != record.getCapitalTransferPattern() && record.getCapitalTransferPattern().equals(DictEnum.MANAGERPATTERN.code)) {
                        if (null != record.getServiceFee()) {
                            order.setServiceFee(record.getServiceFee());
                        }
                        if (null != record.getUserConfirmServiceFee()) {
                            order.setUserConfirmServiceFee(record.getUserConfirmServiceFee());
                        }
                    }
                    // 保存业务部
                    if (null != record.getCapitalTransferPattern() && StringUtils.isNotBlank(record.getCapitalTransferPattern())) {
                        if (null != record.getEndAgentId()) {
                            order.setEndAgentId(record.getEndAgentId());
                        } else {
                            order.setEndAgentId(null);
                        }
                    }
                    if(!DictEnum.NOFETCHGP.code.equals(order.getVehicleGpsBdStatus())){
                        order.setVehicleGpsBdStatus(DictEnum.NOFETCHGP.code);
                    }
                    orderInfoMapper.updateByPrimaryKeySelective(order);

                    TOrderInfo info = orderInfoMapper.selectByPrimaryKey(order.getId());

                    //etc运单结束交互
                    tOrderInfoService.sendEtcData(info.getOrderBusinessCode());
                    Payment payment = new Payment();
                    payment.CollectionOrder(info, "");

                    //节点支付模式
                    if(DictEnum.NODEPAYFIXED.code.equals(info.getPayMethod())
                            || DictEnum.NODEPAYPROPORTION.code.equals(info.getPayMethod())){
                        TOrderInfo orderInfo= orderInfoMapper.selectOrderByCode(info.getCode());
                        tOrderPayRuleService.nodePayUpdate(orderInfo);

                        BigDecimal sumFee= tOrderPayRuleMapper.selectSumFee(tOrderInfo.getCode());
                        TOrderPayRule re = new TOrderPayRule();
                        re.setOrderCode(tOrderInfo.getCode());
                        re.setPayStatus(DictEnum.PACKUNPAID.code);
                        //re.setPayNodeType(DictEnum.SDPAYNODE.code);
                        TOrderPayRule tOrderPayRule1 = tOrderPayRuleMapper.selectByOrderCodeAndType(re);
                        if(null != sumFee && null != tOrderPayRule1){
                            tOrderPayRule1.setSurplusPaymentFee(order.getUserConfirmPaymentAmount().subtract(sumFee));
                            tOrderPayRuleMapper.updateByPrimaryKeySelective(tOrderPayRule1);
                        }
                    }
                    // 放入mq删除轨迹中间表
                    MQMessage mqMessage = new MQMessage();
                    mqMessage.setTopic(MqMessageTopic.ORDERCARLOCATION);
                    mqMessage.setTag(MqMessageTag.DELETE);
                    mqMessage.setBody(info.getCode());
                    mqMessage.setKey(IdWorkerUtil.getInstance().nextId());
                    mqAPI.sendMessage(mqMessage);
                    // 查询运单是否有起运上链记录，如果没有，手动创建起运记录
                    /*if (null != companySourceDTO.getBlockchainPass() && companySourceDTO.getBlockchainPass()) {
                        TDigitalLogistic query = new TDigitalLogistic();
                        query.setOrderCode(info.getCode());
                        query.setStateNode(DigitlFlowEnum.OrderStartShip.code);
                        TDigitalLogistic tDigitalLogistic = digitalLogisticMapper.selectDigitalLogisticByModel(query);
                        if (null == tDigitalLogistic) {
                            TDigitalLogistic digitalLogistic = new TDigitalLogistic();
                            digitalLogistic.setCode(IdWorkerUtil.getInstance().nextId());
                            digitalLogistic.setOrderCode(info.getCode());
                            digitalLogistic.setStateNode(DigitlFlowEnum.OrderStartShip.code);
                            digitalLogistic.setCreateUser("");
                            digitalLogistic.setEnable(false);
                            digitalLogistic.setTxCode(digitalLogistic.getCode());
                            digitalLogistic.setRemark("手动上链");
                            digitalLogisticMapper.insertSelective(digitalLogistic);
                        }
                    }

                    // 数字物流：创建运输完成
                    if (digitlFlowUtil.selectOrderStatus(order.getCode(), DigitlFlowEnum.CreateFlowOrder.code)) {
                        if (!digitlFlowUtil.selectOrderStatus(order.getCode(), DigitlFlowEnum.ClosedOrder.code)) {
                            TOrderCastChanges orderCastChanges = orderCastChangesMapper.selectByNewOne(info.getCode());
                            if (null != orderCastChanges.getCapitalTransferPattern() && StringUtils.isNotBlank(orderCastChanges.getCapitalTransferPattern())) {
                                TGoodsSourceVehicleDriverInfo tGoodsSourceVehicleDriverInfo = goodsSourceVehicleDriverInfoMapper.selectByOrderCode(order.getCode());
                                companySourceDTO.setCargoOrder(tGoodsSourceVehicleDriverInfo.getCode());
                                digitlFlowUtil.orderSave(order, companySourceDTO);
                            }

                            }
                    }*/
                    //想易煤网发送信息
                    tOrderInfoService.sendOrderInfoToYimei(tOrderInfo.getOrderBusinessCode());

                    //快货运数据上报 - 司机/车辆/客户/收款账户/托运方合同
                    //tOrderInfoService.sendOrderInfoToKhy(order.getCode());

                } catch (Exception e) {
                    log.info("ZJJ-002:收单失败!", e);
                    String message = e.getMessage();
                    if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)){
                        message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再收单。";
                    }
                    if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)){
                        message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再收单";
                    }
                    if (StringUtils.checkChineseCharacter(message)){
                        throw new RuntimeException(message);
                    } else {
                        throw new RuntimeException("ZJJ-002:收单失败!");
                    }
                }
            }
        }catch (Exception e){
            log.info("收单失败：", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("ZJJ-002:收单失败!");
            }
        } finally {
            if (orderLock && redisUtil.hasKey(orderKey)) {
                redisUtil.del(orderKey);
                log.info("释放运单锁, {}", orderKey);
            }
        }
        return ResultUtil.ok();
    }

    public static void main(String[] args) {
        Boolean f1 = true;
        Boolean f2 = true;
        System.out.println(!f1 || !f2);
    }

    /**
     * 重新收单
     *
     * @param record
     * @return
     * <AUTHOR>
     */
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil reRecieveOrder(OrderDetailVO record) {
        try {
            TOrderInfo orderInfo = orderInfoMapper.selectOrderByCode(record.getOrderCode());
            TOrderInfoWeight orderInfoWeight =  orderInfoWeightMapper.selectByOrderId(orderInfo.getId());
            //查询运单资源
            ResultUtil goodsSourceAndLineInfo = goodsSourceAPI.selectGoodsSourceBySourceCode(orderInfo.getGoodSourceCode());
            LinkedHashMap source = (LinkedHashMap) goodsSourceAndLineInfo.getData();
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            CompanySourceDTO companySourceDTO = objectMapper.convertValue(source, CompanySourceDTO.class);
            // 如果是货源大厅的运单，修改货源库存
          /*  boolean resourceIfHall = orderGoodsSourceInfoMapper.selectResourceIfHall(orderInfo.getLineGoodsRelId());
            if (resourceIfHall) {
                if (null != record.getSettledWeight()) {
                    // 判断是否相等
                    if (orderInfo.getSettledWeight().compareTo(record.getSettledWeight()) != 0) {
                        // 修改货源库存
                        TGoodsSourceInfo goodsSourceInfo = orderGoodsSourceInfoMapper.selectByByLineGoodsRelId(orderInfo.getLineGoodsRelId());
                        TGoodsSourceInfo goodsSourceInfoForUpdate = new TGoodsSourceInfo();
                        goodsSourceInfoForUpdate.setId(goodsSourceInfo.getId());
                        goodsSourceInfoForUpdate.setEstimateGoodsWeight(goodsSourceInfo.getEstimateGoodsWeight() + orderInfo.getSettledWeight().doubleValue());
                        goodsSourceInfoForUpdate.setEstimateGoodsWeight(goodsSourceInfoForUpdate.getEstimateGoodsWeight() - record.getSettledWeight().doubleValue());
                        if (goodsSourceInfoForUpdate.getEstimateGoodsWeight().compareTo(0d) < 0) {
                            return ResultUtil.error("货源库存不足");
                        } else if (goodsSourceInfoForUpdate.getEstimateGoodsWeight().compareTo(0d) == 0) {
                            goodsSourceInfoForUpdate.setStatus(DictEnum.CLOSESOURCE.code);
                        }
                        goodsSourceInfoForUpdate.setUpdateTime(new Date());
                        orderGoodsSourceInfoMapper.updateEstimateGoodsWeight(goodsSourceInfoForUpdate);
                    }
                }
            }*/

            TOrderPayRule re = new TOrderPayRule();
            re.setPayNodeType(DictEnum.SDPAYNODE.code);
            re.setOrderCode(orderInfo.getCode());
            TOrderPayRule tOrderPayRule = tOrderPayRuleMapper.selectByOrderCodeAndType(re);
            //收单支付后不允许重新收单
            if(null!=tOrderPayRule && !"".equals(tOrderPayRule)){
                if(DictEnum.PACKEDHANDEL.code.equals(tOrderPayRule.getPayStatus())
                        || DictEnum.PACKPAID.code.equals(tOrderPayRule.getPayStatus())){
                    return ResultUtil.error("收单支付后不允许重新收单!");
                }
            }
            Boolean orderState = (((orderInfo.getOrderExecuteStatus().equals(DictEnum.O060.code)
                    && (null == orderInfo.getOrderPayStatus() || StringUtils.isBlank(orderInfo.getOrderPayStatus())))
                    || (null != orderInfo.getOrderPayStatus() && (orderInfo.getOrderPayStatus().equals(DictEnum.M095.code))
                    || orderInfo.getOrderPayStatus().equals(DictEnum.M065.code) || orderInfo.getOrderPayStatus().equals(DictEnum.M045.code)))
                    || orderInfo.getOrderExecuteStatus().equals(DictEnum.M060.code));
            if (orderState){
                //TODO 将上一条置为无效:根据运单业务表id(code)
                orderCastCalcSnapshotMapper.updateDataEnableByCode(record.getOrderCode());

                //TODO 创建运单快照主表
                TOrderCastCalcSnapshot orderCastCalcSnapshot = createOrderCastCalcSnapshot(record, orderInfo);
                orderCastCalcSnapshotMapper.insertSelective(orderCastCalcSnapshot);
                try {
                    //计算规则计算的应付运费
                    orderInfo.setRulePaymentAmount(orderCastCalcSnapshot.getCarriagePayment());
                    //添加用户确认的运费 add by zhangjiji 2019/6/13
                    orderInfo.setDeliverWeightNotesWeight(record.getRealDeliverWeight());
                    orderInfo.setReceiveWeightNotesWeight(record.getRealDispathWeight());

                    orderInfo.setReceiveGoodsContacter(CurrentUser.getUserNickname());
                    orderInfo.setReceiveGoodsContacterPhone(CurrentUser.getUserAccountNo());


                    if(DictEnum.CARRIAGE_PRICE_UNIT_BOX.code.equals(record.getCarriagePriceUnit())){
                        orderInfoWeight.setBoxNum(record.getBoxNum());
                        orderInfoWeight.setDeliverWeightNotesTime1(record.getDeliverWeightNotesTime1());
                        orderInfoWeight.setDeliverWeightNotesWeight1(record.getDeliverWeightNotesWeight1());
                        orderInfoWeight.setDeliverWeightNotesPhoto1(record.getDeliverWeightNotesPhoto1());
                        orderInfoWeight.setReceiveWeightNotesTime1(record.getReceiveWeightNotesTime1());
                        orderInfoWeight.setReceiveWeightNotesWeight1(record.getReceiveWeightNotesWeight1());
                        orderInfoWeight.setReceiveWeightNotesPhoto1(record.getReceiveWeightNotesPhoto1());
                        orderInfoWeight.setGrossWeight1(record.getGrossWeight1());
                        orderInfoWeight.setPrimaryWeight1(record.getPrimaryWeight1());
                        orderInfoWeight.setDischargeWeight1(record.getDischargeWeight1());
                        orderInfoWeight.setCarriageUnitPrice1(record.getCarriageUnitPrice1());

                        if(record.getBoxNum()<2){
                            orderInfo.setPrimaryWeight(Double.parseDouble(record.getPrimaryWeight1().toString()));
                            orderInfo.setDischargeWeight(Double.parseDouble(record.getDischargeWeight1().toString()));
                            orderInfo.setSettledWeight(record.getDischargeWeight1());
                            if(null!=record.getGrossWeight1()){
                                orderInfo.setGrossWeight(Double.parseDouble(record.getGrossWeight1().toString()));//毛重
                            }
                            if(null!=record.getDeliverWeightNotesTime1()){
                                orderInfo.setDeliverWeightNotesTime(record.getDeliverWeightNotesTime1());
                            }
                            if(null!=record.getReceiveWeightNotesTime1()){
                                orderInfo.setReceiveWeightNotesTime(record.getReceiveWeightNotesTime1());
                            }
                        }else{
                            orderInfoWeight.setDeliverWeightNotesTime2(record.getDeliverWeightNotesTime2());
                            orderInfoWeight.setDeliverWeightNotesWeight2(record.getDeliverWeightNotesWeight2());
                            orderInfoWeight.setDeliverWeightNotesPhoto2(record.getDeliverWeightNotesPhoto2());
                            orderInfoWeight.setReceiveWeightNotesTime2(record.getReceiveWeightNotesTime2());
                            orderInfoWeight.setReceiveWeightNotesWeight2(record.getReceiveWeightNotesWeight2());
                            orderInfoWeight.setReceiveWeightNotesPhoto2(record.getReceiveWeightNotesPhoto2());
                            orderInfoWeight.setGrossWeight2(record.getGrossWeight2());
                            orderInfoWeight.setPrimaryWeight2(record.getPrimaryWeight2());
                            orderInfoWeight.setDischargeWeight2(record.getDischargeWeight2());
                            orderInfoWeight.setCarriageUnitPrice2(record.getCarriageUnitPrice2());
                            orderInfo.setPrimaryWeight(Double.parseDouble((record.getPrimaryWeight1().add(record.getPrimaryWeight2())).toString()));
                            orderInfo.setDischargeWeight(Double.parseDouble((record.getDischargeWeight1().add(record.getDischargeWeight2())).toString()));
                            orderInfo.setSettledWeight(record.getDischargeWeight1().add(record.getDischargeWeight2()));
                            if(null!=record.getGrossWeight1() && null!=record.getGrossWeight2()){
                                orderInfo.setGrossWeight(Double.parseDouble((record.getGrossWeight1().add(record.getGrossWeight2())).toString()));//毛重
                            }
                            if(null!=record.getDeliverWeightNotesTime1()){
                                orderInfo.setDeliverWeightNotesTime(record.getDeliverWeightNotesTime1());
                            }else if(null!=record.getDeliverWeightNotesTime2()){
                                orderInfo.setDeliverWeightNotesTime(record.getDeliverWeightNotesTime2());
                            }
                            if(null!=record.getReceiveWeightNotesTime1()){
                                orderInfo.setReceiveWeightNotesTime(record.getReceiveWeightNotesTime1());
                            }else if(null!=record.getReceiveWeightNotesTime2()){
                                orderInfo.setReceiveWeightNotesTime(record.getReceiveWeightNotesTime2());
                            }

                        }
                        orderInfoWeightMapper.updateByPrimaryKeySelective(orderInfoWeight);
                    }else{

                        orderInfo.setDischargeWeight(record.getRealDispathWeight());
                        orderInfo.setGrossWeight(record.getGrossWeight());
                        orderInfo.setSettledWeight(record.getSettledWeight());
                        //当按车时，结算重量取卸货重量
                        if(DictEnum.CARRIAGE_PRICE_UNIT_CAR.code.equals(record.getCarriagePriceUnit())){
                            orderInfo.setSettledWeight(BigDecimal.valueOf(record.getRealDispathWeight()));
                        }
                        orderInfo.setDeliverWeightNotesPhoto(orderCastCalcSnapshot.getDeliverWeightNotesPhoto());
                        orderInfo.setReceiveWeightNotesPhoto(orderCastCalcSnapshot.getReceiveWeightNotesPhoto());
                        orderInfo.setDeliverWeightNotesTime(orderCastCalcSnapshot.getDeliverWeightNotesUploadTime());
                        orderInfo.setReceiveWeightNotesTime(orderCastCalcSnapshot.getReceiveWeightNotesUploadTime());
                    }

                    orderInfo.setUserConfirmPaymentAmount(orderCastCalcSnapshot.getUserConfirmCarriagePayment());
                    orderInfo.setPrimaryWeight(orderCastCalcSnapshot.getRealDeliverWeight());

                    orderInfo.setReceiveOrderUserId(CurrentUser.getUserAccountId());
                    orderInfo.setReceiveOrderTime(new Date());
                    orderInfo.setUpdateTime(new Date());
                    orderInfo.setUpdateUser(CurrentUser.getUserNickname());
                    orderInfo.setOrderExecuteStatus(DictEnum.O060.code);
                    if (null != record.getPaymentProcess() && record.getPaymentProcess() == 1) {
                        orderInfo.setParam1("");
                        orderInfo.setOrderPayStatus(DictEnum.M065.code);
                    }
                    if (null != record.getCapitalTransferPattern() && StringUtils.isNotBlank(record.getCapitalTransferPattern())) {
                        if (null != record.getEndAgentId()) {
                            orderInfo.setEndAgentId(record.getEndAgentId());
                        } else {
                            orderInfo.setEndAgentId(null);
                        }
                    }
                    if (null != record.getServiceFee()) {
                        orderInfo.setServiceFee(record.getServiceFee());
                    }
                    if (null != record.getUserConfirmServiceFee()) {
                        orderInfo.setUserConfirmServiceFee(record.getUserConfirmServiceFee());
                    }
                    orderInfoMapper.updateByPrimaryKey(orderInfo);

                    //若后续重新收单时，实收重量修改为小于等于38吨，投保状态再变更为“是”
                    if(null != orderInfo.getOrderBusinessCode()){
                        TCarInsurance tCarInsurance = carInsuranceMapper.selectByVehicleId(orderInfo.getVehicleId());
                        TOrderInsurance orderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(orderInfo.getOrderBusinessCode());
                        if(null == orderInsurance){
                            orderInsurance = new TOrderInsurance();
                        }
                        if (null == tCarInsurance) {
                            //获取默认保费
                            SysParam insuredAmount = sysParamAPI.getParamByKey("PICC_FREIGHT_INSURANCE");
                            if (null != orderInsurance.getInsure() && orderInsurance.getInsure() != 1 && null != companySourceDTO.getCapitalTransferType() &&
                                    DictEnum.PAYTOCAPTAIN.code.equals(companySourceDTO.getCapitalTransferType())) {
                                SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.NOINSURANCE.code);
                                if (null != paramByKey) {
                                    String[] split = paramByKey.getParamValue().split(",");
                                    if (split.length > 0) {
                                        List<String> list = Arrays.asList(split);
                                        if (null != orderInfo.getEndCarOwnerId()) {
                                            if (list.contains(orderInfo.getEndCarOwnerId().toString())) {
                                                TEndUserInfo tEndUserInfo = endSUserInfoAPI.selectByPrimaryKey(orderInfo.getEndCarOwnerId());
                                                orderInsurance.setUninsuredCause("车队长" + tEndUserInfo.getRealName() + "不投保");
                                                if (null != orderInsurance.getInsure() && 2 == orderInsurance.getInsure()) {
                                                    orderInsurance.setInsure(0);
                                                    orderInsurance.setInsuredAmount(BigDecimal.ZERO);
                                                }
                                            } else {
                                                if (null != orderInsurance.getInsuranceMethod() && !InsuranceMethodsEnum.NOTINSURED.getKey().equals(orderInsurance.getInsuranceMethod())) {
                                                    //实收<=38,投保状态是其他2
                                                    if (orderInfo.getDischargeWeight() <= 38) {
                                                        orderInsurance.setInsure(2);
                                                        orderInsurance.setUninsuredCause("实收重量未超38吨，平台收取保费");
                                                        orderInsurance.setInsuredAmount(new BigDecimal(insuredAmount.getParamValue()));
                                                    } else {
                                                        //实收>38吨，投保状态是不投保0;
                                                        orderInsurance.setInsure(0);
                                                        orderInsurance.setInsuredAmount(BigDecimal.ZERO);
                                                        orderInsurance.setUninsuredCause("实收重量大于38吨");
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            } else if (null != orderInsurance.getInsuranceMethod() &&
                                    InsuranceMethodsEnum.MUSTBEINSURED.getKey().equals(orderInsurance.getInsuranceMethod()) &&
                                    null != orderInsurance.getInsure() && orderInsurance.getInsure() == 0) {
                                // 收单时，实收<=38吨，投保状态是其他2;
                                if (null != orderInfo.getDischargeWeight() && orderInfo.getDischargeWeight() <= 38) {
                                    orderInsurance.setInsure(2);
                                    orderInsurance.setUninsuredCause("实收重量未超38吨，平台收取保费");
                                    orderInsurance.setInsuredAmount(new BigDecimal(insuredAmount.getParamValue()));
                                }else {
                                    //实收>38吨，投保状态是不投保0;
                                    orderInsurance.setInsure(0);
                                    orderInsurance.setInsuredAmount(BigDecimal.ZERO);
                                    orderInsurance.setUninsuredCause("实收重量大于38吨");
                                }
                            } else {
                                if (null != orderInfo.getDischargeWeight() && orderInfo.getDischargeWeight() > 38) {
                                    if (null != orderInsurance.getInsure() && 2 == orderInsurance.getInsure()) {
                                        orderInsurance.setInsure(0);
                                        orderInsurance.setInsuredAmount(BigDecimal.ZERO);
                                        orderInsurance.setUninsuredCause("装货重量大于38吨");
                                    }
                                } else {
                                    if (null != orderInsurance.getInsure() && 1 == orderInsurance.getInsure()) {
                                        orderInsurance.setInsuranceCancellation(false);
                                        orderInsurance.setUninsuredCause(null);
                                    } else {
                                        if (null != orderInsurance.getInsure() && 0 == orderInsurance.getInsure() && null != orderInsurance.getUninsuredCause() &&
                                                "装货重量大于38吨".equals(orderInsurance.getUninsuredCause())) {
                                            orderInsurance.setInsure(2);
                                            orderInsurance.setInsuredAmount(new BigDecimal(insuredAmount.getParamValue()));
                                            orderInsurance.setUninsuredCause("实收重量未超38吨，平台收取保费");
                                        }
                                    }
                                }
                            }

                        } else {
                            if (null != orderInsurance.getInsure() && 1 != orderInsurance.getInsure() &&
                                    null != orderInsurance.getInsuranceMethod() &&
                                    orderInsurance.getInsuranceMethod().equals(InsuranceMethodsEnum.MUSTBEINSURED.getKey())) {
                                orderInsurance.setInsure(0);
                                orderInsurance.setInsuredAmount(BigDecimal.ZERO);
                                orderInsurance.setUninsuredCause("车辆已投保");
                            }
                        }
                        if(null != orderInsurance.getId()){
                            orderInsurance.setUpdateUser(CurrentUser.getUserNickname());
                            orderInsurance.setUpdateTime(new Date());
                            orderInsuranceMapper.updateByPrimaryKey(orderInsurance);
                        }
                    }
                    //创建运单执行状态子表
                    createState(orderInfo.getCode(), DictEnum.S0501.code, "");

                    //节点支付模式
                    if(DictEnum.NODEPAYFIXED.code.equals(orderInfo.getPayMethod())
                            || DictEnum.NODEPAYPROPORTION.code.equals(orderInfo.getPayMethod())){
                        TOrderInfo info= orderInfoMapper.selectOrderByCode(orderInfo.getCode());
                        tOrderPayRuleService.nodePayUpdate(info);

                        BigDecimal sumFee= tOrderPayRuleMapper.selectSumFee(orderInfo.getCode());
                        TOrderPayRule rer = new TOrderPayRule();
                        rer.setOrderCode(orderInfo.getCode());
                        rer.setPayStatus(DictEnum.PACKUNPAID.code);
                        //rer.setPayNodeType(DictEnum.SDPAYNODE.code);
                        TOrderPayRule tOrderPayRule1 = tOrderPayRuleMapper.selectByOrderCodeAndType(rer);
                        if(null!=sumFee && !"".equals(sumFee) && null!=tOrderPayRule1 &&!"".equals(tOrderPayRule1)){
                            tOrderPayRule1.setSurplusPaymentFee(orderInfo.getUserConfirmPaymentAmount().subtract(sumFee));
                            tOrderPayRuleMapper.updateByPrimaryKeySelective(tOrderPayRule1);
                        }
                    }
                    // TODO 查询是否需要支付前审核
                    if (null != record.getPaymentProcess() && record.getPaymentProcess() == 1) {
                        //新增运单执行状态子表: 财务审核默认值
                        createState(orderInfo.getCode(), DictEnum.S0650.code, "");
                    }

                    Payment payment = new Payment();
                    payment.CollectionOrder(orderInfo, "re");

                    //想易煤网发送信息
                    tOrderInfoService.sendOrderInfoToYimei(orderInfo.getOrderBusinessCode());

                } catch (Exception e) {
                    log.error("ZJJ-003：重新收单失败！, {}", ThrowableUtil.getStackTrace(e));
                    String message = e.getMessage();
                    if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)){
                        message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再收单。";
                    }
                    if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)){
                        message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再收单";
                    }
                    if (StringUtils.checkChineseCharacter(message)){
                        throw new RuntimeException(message);
                    }
                    throw new RuntimeException("ZJJ-047:收单失败");
                }
            }else {
                return ResultUtil.error("运单状态错误");
            }
        }catch (Exception e){
            log.error("ZJJ-003：重新收单失败！, {}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("ZJJ-003：重新收单失败！");
            }
        }
        return ResultUtil.ok();
    }

    /**
     * 支付
     *
     * @param
     * @return
     * <AUTHOR>
     */
    //@LcnTransaction
    @Transactional
    @Override
    public ResultUtil payOrder(TOrderInfoVO record) {
        TOrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(record.getId());
        TOrderCastCalcSnapshot orderCastCalcSnapshot = orderCastCalcSnapshotMapper.selectSnapsByOrderCode(record.getCode());
        String remark = "";
        if (null != record.getNewUserConfirmCarriagePayment() && null != orderCastCalcSnapshot.getUserConfirmCarriagePayment()) {
            if (record.getNewUserConfirmCarriagePayment().compareTo(orderCastCalcSnapshot.getUserConfirmCarriagePayment()) != 0) {
                remark = "用户" + CurrentUser.getUserNickname() + "将运费由" + orderCastCalcSnapshot.getUserConfirmCarriagePayment() + "改为" + record.getNewUserConfirmCarriagePayment();
                orderCastCalcSnapshot.setUserConfirmCarriagePayment(record.getNewUserConfirmCarriagePayment());
                orderCastCalcSnapshot.setParam2(remark);
                orderCastCalcSnapshotMapper.updateByPrimaryKeySelective(orderCastCalcSnapshot);
            }
        }

        //TODO 修改运单主表状态
        TOrderInfo forUpdateOrder = new TOrderInfo();
        forUpdateOrder.setId(record.getId());
        //运单状态：支付处理中
        forUpdateOrder.setOrderPayStatus(DictEnum.P070.code);
        orderInfo.setOrderPayStatus(DictEnum.P070.code);
        if (StringUtils.isNotEmpty(remark)) {
            forUpdateOrder.setUserConfirmPaymentAmount(record.getNewUserConfirmCarriagePayment());
            forUpdateOrder.setParam2(remark);
            orderInfo.setUserConfirmPaymentAmount(record.getNewUserConfirmCarriagePayment());
            orderInfo.setParam2(remark);
        }
        if (null != record.getBankId()) {
            //如果自动到卡，设置选择的银行卡
            orderInfo.setParam3(String.valueOf(record.getBankId()));
            forUpdateOrder.setParam3(String.valueOf(record.getBankId()));
        }
        if (null != record.getServiceFee()) {
            orderInfo.setServiceFee(record.getServiceFee());
            forUpdateOrder.setServiceFee(record.getServiceFee());
        }
        if (null != record.getUserConfirmServiceFee()) {
            orderInfo.setUserConfirmServiceFee(record.getUserConfirmServiceFee());
            forUpdateOrder.setUserConfirmServiceFee(record.getUserConfirmServiceFee());
        }

        TOrderCastChanges tOrderCastChanges = orderCastChangesMapper.selectByNewOne(orderInfo.getCode());
        if (DictEnum.AUTOMATION.code.equals(tOrderCastChanges.getWithdrawType()) && !record.getAdvancePayment()) {
            Integer enduserId = null;
            if (DictEnum.PAYTODRIVER.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                enduserId = orderInfo.getEndDriverId();
            } else if (DictEnum.PAYTOBELONGER.code.equals(tOrderCastChanges.getCapitalTransferType()) ||DictEnum.PAYTOCAPTAIN.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                enduserId = orderInfo.getEndCarOwnerId();
            }
            // 记录提现金额
            BigDecimal carriageFee = orderInfo.getUserConfirmPaymentAmount().subtract(orderInfo.getUserConfirmServiceFee());//tOrderCastChanges.getCarriageFee().subtract(tOrderCastChanges.getServiceFee());
            if (null != record.getBankId()) {
                sendOrderUtil.saveBankCardMonthlyAmountByBankId(record.getBankId(), orderInfo.getCode(), carriageFee);
            } else {
                TBankCard bankCard = sendOrderUtil.selectDefaultBankCard(enduserId);
                orderInfo.setParam3(String.valueOf(bankCard.getId()));
                forUpdateOrder.setParam3(String.valueOf(bankCard.getId()));
                sendOrderUtil.saveBankCardMonthlyAmountByBankId(bankCard.getId(), orderInfo.getCode(), carriageFee);
            }
        }
        orderInfoMapper.updateByPrimaryKeySelective(forUpdateOrder);

        //TODO 新增运单执行状态子表
        TOrderState orderState = new TOrderState();
        orderState.setCode(IdWorkerUtil.getInstance().nextId());
        orderState.setOrderCode(record.getCode());
        //节点：会计、收单员已点击支付
        //TODO 根据当前登录人的线路角色，会计、收单员
        orderState.setStateNodeValue(DictEnum.SP0701.code);
        orderState.setOperateTime(new Date());
        //TODO 操作方式
        orderState.setOperateMethod("");
        orderState.setOperatorId(CurrentUser.getCurrentUserID());
        orderState.setEnable(false);
        orderState.setIfExpire(false);
        orderState.setCreateUser(CurrentUser.getUserNickname());
        orderState.setUpdateUser(CurrentUser.getUserNickname());
        orderStateMapper.insertSelective(orderState);
        //TODO 支付
        Payment payment = new Payment();
        try {
            if (null != record.getRoyaltPayment() && record.getRoyaltPayment()) {
                payment.royaltyFinancePay(orderInfo);
            } else {
                if (record.getAdvancePayment()) {
                    payment.FinalPay(orderInfo);
                } else {
                    payment.FinancePay(orderInfo);
                }
            }

        } catch (Exception e) {
            log.error("ZJJ-005:PC支付失败!", e);
            String message = e.getMessage();
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再支付。";
            }
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再支付";
            }
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            }
            throw new RuntimeException("ZJJ-005:PC支付失败!");
        }
        return ResultUtil.ok();
    }


    /**
     * @Description 运单打包支付
     * <AUTHOR>
     * @Date 2019/6/11 8:52
     * @Param
     * @Return
     * @Exception
     */
    //@LcnTransaction
    @Transactional
    @Override
    public ResultUtil packPay(String code) {
        try {
            TOrderPackInfo orderPackInfo = orderPackInfoMapper.selectOrderPackByCode(code);
            if (null != orderPackInfo) {
                if (orderPackInfo.getPackStatus().equals(DictEnum.PACKED.code)
                        || orderPackInfo.getPackStatus().equals(DictEnum.PACKORDERPAIDERROR.code)
                        || orderPackInfo.getPackStatus().equals(DictEnum.PACKRECALL.code)) {
                    Payment payment = new Payment();
                    try {
                        payment.FinanceDBPay(orderPackInfo);
                        //修改打包主表状态: 处理中
                        TOrderPackInfo packInfo = new TOrderPackInfo();
                        packInfo.setId(orderPackInfo.getId());
                        packInfo.setPackStatus(DictEnum.PACKEDHANDEL.code);
                        orderPackInfoMapper.updateByPrimaryKeySelective(packInfo);
                        //修改运单主表支付状态：支付处理中
                        List<String> orderCodes = orderPackInfoMapper.selectOrderCodeByPackCode(orderPackInfo.getCode());
                        HashMap hashMap = new HashMap();
                        hashMap.put("orderPayStatus", DictEnum.P070.code);
                        hashMap.put("codes", orderCodes);
                        orderInfoMapper.batchUpdateOrderPayStatus(hashMap);
                        //添加运单执行状态子表
                        createOrderState(DictEnum.SP0701.code, CurrentUser.getUserNickname(), DictEnum.COMPC.code, "", orderCodes);
                    } catch (Exception e) {
                        log.error("ZJJ-006:打包支付失败!", e);
                        String message = e.getMessage();
                        if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                            message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再支付。";
                            throw new RuntimeException(message);
                        }
                        if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                            message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再支付";
                            throw new RuntimeException(message);
                        }
                        throw new RuntimeException("ZJJ-006:打包支付失败!");
                    }
                } else {
                    return ResultUtil.error("运单状态错误，无法支付");
                }
            } else {
                log.error("未找到打包运单");
            }
        } catch (Exception e) {
            log.error("ZJJ-006:打包支付失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            }
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再支付。";
                throw new RuntimeException(message);
            }
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再支付";
                throw new RuntimeException(message);
            }
            throw new RuntimeException("ZJJ-006:打包支付失败!");
        }

        return ResultUtil.ok();
    }


    /**
     * @author: dingweibo
     * @Date: 2020/11/20 16:02
     * @Description: 节点支付
     */
    //@LcnTransaction
    @Transactional
    @Override
    public ResultUtil nodePay(TOrderInfo orderInfo, SendOrderVO sendOrderVO) {
        //TODO 新增运单执行状态子表
        TOrderState orderState = new TOrderState();
        orderState.setCode(IdWorkerUtil.getInstance().nextId());
        orderState.setOrderCode(orderInfo.getCode());
        //节点：会计、收单员已点击支付
        //TODO 根据当前登录人的线路角色，会计、收单员
        if (sendOrderVO.getPayNodeType().equals(DictEnum.ZHPAYNODE.code)) {
            orderState.setStateNodeValue(DictEnum.SP0706.code);
        } else if (sendOrderVO.getPayNodeType().equals(DictEnum.XHPAYNODE.code)) {
            orderState.setStateNodeValue(DictEnum.SP0708.code);
        } else if (sendOrderVO.getPayNodeType().equals(DictEnum.SDPAYNODE.code)) {
            orderState.setStateNodeValue(DictEnum.SP0710.code);
        } else {
            orderState.setStateNodeValue(DictEnum.SP0701.code);
        }
        orderState.setOperateTime(new Date());
        //TODO 操作方式
        orderState.setOperateMethod("");
        orderState.setOperatorId(CurrentUser.getCurrentUserID());
        orderState.setEnable(false);
        orderState.setIfExpire(false);
        orderState.setCreateUser(CurrentUser.getUserNickname());
        orderState.setUpdateUser(CurrentUser.getUserNickname());
        orderStateMapper.insertSelective(orderState);

        //TODO 支付
        Payment payment = new Payment();
        try {
            payment.nodePay(orderInfo, sendOrderVO);
        } catch (Exception e) {
            log.error("节点支付失败!", e);
            String message = e.getMessage();
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再支付。";
            }
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再支付";
            }
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            }
            throw new RuntimeException("节点支付失败!");
        }
        return ResultUtil.ok();
    }

    /**
     * App 根据 业务ID查询运单详情
     * Yan
     *
     * @param code
     * @return
     */
    @Override
    public ResultUtil appSelectOrderDetails(String code, String paymentPlatforms) throws RuntimeException {
        String usertype = CurrentUser.getUsertype();
        String state = orderStateMapper.selectOrderState(code);
        state = state.replaceAll("-", "");
        int num = OrderStateEnum.valueOf(state).code;

        if (!OrderStateEnum.judge(num)) {
            log.error("运单号：" + code + "运单错误状态：" + state);
            throw new RuntimeException("运单状态错误");
        } else {
            Integer userAccountId = CurrentUser.getUserAccountId();
            OrderDetailDTO orderDetailed = new OrderDetailDTO();
            try {
                // 查询运单详情
                orderDetailed = orderInfoMapper.getOrderDetailInfo(code, userAccountId);
                //如果有order_info_weight表ID，则表明一定是集装箱运单
                if(null != orderDetailed.getOrderInfoWeightId()){
                    //根据order_info_weight表ID查询
                    TOrderInfoWeight orderInfoWeight = orderInfoWeightMapper.selectByPrimaryKey(Long.valueOf(orderDetailed.getOrderInfoWeightId()));
                    TOrderInfoWeightVO vo = new TOrderInfoWeightVO();
                    BeanUtils.copyProperties(orderInfoWeight,vo);
                    orderDetailed.setOrderInfoWeight(vo);
                }

                //查询节点运单金额
                TOrderNodeDetailVO tOrderNodeDetailVO = tOrderPayRuleMapper.selectNodeOrderPayDetailed(code);
                if (null != tOrderNodeDetailVO) {
                    BeanCopyUtil.copyPropertiesIgnoreNull(tOrderNodeDetailVO, orderDetailed);
                    orderDetailed.setZhPaymentFee(tOrderNodeDetailVO.getZhPaymentFee());
                    orderDetailed.setXhPaymentFee(tOrderNodeDetailVO.getXhPaymentFee());
                    orderDetailed.setSdPaymentFee(tOrderNodeDetailVO.getSdPaymentFee());
                    BigDecimal userConfirmCarriagePayment = orderDetailed.getUserConfirmCarriagePayment();
                    BigDecimal sumFee = tOrderPayRuleMapper.selectSumFee(code);
                    BigDecimal subtract = userConfirmCarriagePayment.subtract(sumFee);
                    orderDetailed.setWkPaymentFee(subtract);
                }
                String type = null;
                if (DictEnum.PAYTODRIVER.code.equals(orderDetailed.getCapitalTransferType())
                        || DictEnum.FIRSTBROKERDRIVER.code.equals(orderDetailed.getCapitalTransferType())) {
                    type = "end_driver_id";
                } else if (DictEnum.PAYTOBELONGER.code.equals(orderDetailed.getCapitalTransferType())
                        ||DictEnum.FIRSTBROKERBELONGER.code.equals(orderDetailed.getCapitalTransferType())
                        ||DictEnum.PAYTOCAPTAIN.code.equals(orderDetailed.getCapitalTransferType())) {
                    type  = "end_car_owner_id";
                }
                List<String> receiver = new ArrayList<>();
                if (DictEnum.JDPLATFORMS_CODE.code.equals(paymentPlatforms)) {
                    receiver = orderInfoMapper.selectOrderReceiverBankCard(code, type);
                } else if (DictEnum.HXPLATFORMS_CODE.code.equals(paymentPlatforms)) {
                    List<TBankInfoVo> hxBankList  = orderInfoMapper.selectOrderReceiverHxBankCard(code, type);
                    hxBankList.removeAll(Collections.singleton(null)); //移除所有的null元素
                    if(null !=hxBankList && hxBankList.size() > 0){
                        if(DictEnum.PAYTOCAPTAIN.code.equals(orderDetailed.getCapitalTransferType())){
                            orderDetailed.setReceiverName(orderDetailed.getOwner());
                        }else{
                            orderDetailed.setReceiverName(orderDetailed.getDriverUser());
                        }
                        orderDetailed.setReceiverName(orderDetailed.getDriverUser());

                        List<HashMap> cards = new ArrayList<>();
                        for(TBankInfoVo tBankInfoVo:hxBankList){
                            HashMap<String, String> card = new HashMap<>();
                            card.put("bankId", String.valueOf(tBankInfoVo.getBankId()));
                            card.put("cardHolder", tBankInfoVo.getCardHolder());
                            card.put("ifDefault", String.valueOf(tBankInfoVo.getIfDefault()));
                            cards.add(card);
                        }
                        orderDetailed.setReceiverBankCard(cards);
                    }
                }
                if (null !=receiver && receiver.size() > 0){
                    List<HashMap> cards = new ArrayList<>();
                    for (String cardInfo: receiver){
                        if(DictEnum.PAYTOCAPTAIN.code.equals(orderDetailed.getCapitalTransferType())){
                            orderDetailed.setReceiverName(orderDetailed.getOwner());
                        }else{
                            orderDetailed.setReceiverName(orderDetailed.getDriverUser());
                        }
                        if(StringUtils.isNotEmpty(cardInfo)){
                            HashMap<String, String> card = new HashMap<>();
                            String[] info = cardInfo.split(",");
                            card.put("bankId", info[0]);
                            card.put("cardHolder", info[1] + " " + info[2]);
                            card.put("ifDefault", info[3]);
                            cards.add(card);
                        }
                    }
                    orderDetailed.setReceiverBankCard(cards);
                }

            } catch (RuntimeException e) {
                log.error("Y2021--查询运单详情失败:错误运单号" + code + "错误信息：" , e);
                throw new RuntimeException("查询运单详情失败(错误编码：Y2021)");
            }
//            //计算 经纪人模式下，支付时检测运费与经纪人服务费
//            BigDecimal carriagePayment = orderDetailed.getCarriagePayment();
//            if (carriagePayment!=null && orderDetailed.getBusinessAssist()==1){
//                TLineGoodsManagerRel tLineGoodsManagerRel = endSUserInfoAPI.selectByLineGoodsrRelId(orderDetailed.getLineGoodsRelId());
//                String shareMethod = tLineGoodsManagerRel.getShareMethod();
//                BigDecimal shareValue = tLineGoodsManagerRel.getShareValue();
//                if (shareMethod!=null && shareValue!=null && "FIXEDLIMIT".equals(shareMethod)){
//                    /** 按照固定额度*/
//                    orderDetailed.setManagerPayment(shareValue);
//                }else if (shareMethod!=null && shareValue!=null&& "FIXEDPROPORTION".equals(shareMethod)){
//                    /** 按照固定比例*/
//                    BigDecimal bigDecimal=null;
//                }
//            }

            // 查询合同
            List<Map<String, Object>> contractlist = orderContractMapper.appSelectOrderContract(code);
            // 合同照片
            for (Map<String, Object> me : contractlist) {
                if (null != me && null != me.get("param2")) {
                    String photo = me.get("param2").toString();
                    String[] split = photo.split(",");
                    List<String> contract = new ArrayList<>();
                    for (String spl : split) {
                        if (StringUtils.isNotBlank(spl)) {
                            contract.add(spl);
                        }
                    }
                    orderDetailed.setContractlist(contract);
                }
            }
            // 查询装货，卸货
            OrderDetailDTO handlingOrder = null;
            if (num > OrderStateEnum.M010.code) {
                handlingOrder = orderInfoMapper.appHandlingOrder(code, num);
                orderDetailed.setDeliverWeightNotesPhoto(handlingOrder.getDeliverWeightNotesPhoto());
                orderDetailed.setPrimaryWeight(handlingOrder.getPrimaryWeight());
                orderDetailed.setDeliverWeightNotesTime(handlingOrder.getDeliverWeightNotesTime());
            }
            if (num >= OrderStateEnum.M040.code) {
                orderDetailed.setReceiveWeightNotesPhoto(handlingOrder.getReceiveWeightNotesPhoto());
                orderDetailed.setDischargeWeight(handlingOrder.getDischargeWeight());
                orderDetailed.setReceiveWeightNotesTime(handlingOrder.getReceiveWeightNotesTime());
            }
            //节点支付提现防止查询收单之前的运单照表
            List<String> resultstate = new ArrayList<>();
            resultstate.add(DictEnum.M20.code);
            resultstate.add(DictEnum.M10.code);
            resultstate.add(DictEnum.M000.code);
            resultstate.add(DictEnum.M010.code);
            resultstate.add(DictEnum.M020.code);
            resultstate.add(DictEnum.M030.code);
            resultstate.add(DictEnum.M040.code);
            if (num > OrderStateEnum.M040.code) {
                try {
                    // 已收单：查快运单照表
                    if (!resultstate.contains(orderDetailed.getOrderExecuteStatus())){
                        OrderDetailDTO orderSnapshot = orderCastCalcSnapshotMapper.appSelectSnapsByOrderCode(code);
                        if(DictEnum.CARRIAGE_PRICE_UNIT_DUN.code.equals(orderDetailed.getCarriagePriceUnit())){
                            String accountWeight = orderSnapshot.getSettleAccountsWeight();
                            BigDecimal bigDecimal = new BigDecimal(accountWeight);
                            BigDecimal multiply = orderSnapshot.getCarriageUnitPrice().multiply(bigDecimal);
                            BigDecimal bigDecimal1 = multiply.setScale(4, BigDecimal.ROUND_DOWN);
                            orderSnapshot.setSubtotal(bigDecimal1);
                        }
                        BeanCopyUtil.copyPropertiesIgnoreNull(orderSnapshot, orderDetailed);
                    }

                } catch (RuntimeException e) {
                    log.error("Y2022--查询运单快照失败:错误运单号" + code + "错误信息", e);
                    throw new RuntimeException("查询运单详情失败(错误编码：Y2022)");
                }
            }

            try{
                String payNodeType = orderInfoMapper.selectOrderPayRulePayNodeType(code);
                if (null != payNodeType && !"".equals(payNodeType)) {
                    orderDetailed.setPayNodeType(payNodeType);
                }
                OrderDetailDTO orderDetailDTO =orderInfoMapper.selectOrderPayRuleDetail(code);
                if(null != orderDetailDTO){
                    BeanCopyUtil.copyPropertiesIgnoreNull(orderDetailDTO, orderDetailed);
                }
            }catch (Exception e){
                e.getMessage();
                log.error("运单详情异常", e);
            }
//            //查看经济人信息
//            TEndUserInfo tEndUserInfo = endSUserInfoAPI.selectByManagerOrderCode(orderDetailed.getCode());
//            if (null != tEndUserInfo) {
//                    orderDetailed.setManagerName(tEndUserInfo.getRealName())
//                            .setManagerPhone(tEndUserInfo.getPhone())
//                            .setCertificateNo(tEndUserInfo.getCertificateNo())
//                            .setCertificateValidUntil(tEndUserInfo.getCertificateValidUntil())
//                            .setManagerCertificatePhoto(tEndUserInfo.getCertificatePhoto1());
//                }

            try {
                // 查询运单是否有反馈问题信息
                OrderFeedbackVO build = OrderFeedbackVO.builder().orderCode(code).build();
                ResultUtil resultUtil = complaintsAPI.orderIsExistsFeedback(build);
                if (resultUtil.getData().toString() != null && !"".equals(resultUtil.getData().toString()) && resultUtil.getData().toString().length() != 0) {
                    List<LinkedHashMap> data = (List<LinkedHashMap>) resultUtil.getData();
                    if (data.size() > 0 && data != null) {
                        for (LinkedHashMap datum : data) {
                            if (datum.get("dataProblemType") != null && !"".equals(datum.get("dataProblemType"))) {
                                if ("LOADING".equals(datum.get("dataProblemType"))) {
                                    orderDetailed.setLoading(true);
                                } else if ("DISCHARGE".equals(datum.get("dataProblemType")) || "LOADING_X".equals(datum.get("dataProblemType")) || "LOADINGANDDISCHARGE".equals(datum.get("dataProblemType"))) {
                                    orderDetailed.setUnloading(true);
                                } else if ("YFW_X".equals(datum.get("dataProblemType"))) {
                                    orderDetailed.setDriverUnloading(true);
                                } else {
                                    orderDetailed.setDriverCollect(true);
                                }
                            }
                        }
                    }
                }

                ResultUtil orderFeedbackInfo = tOrderInfoService.getFeedbackOrderInfo(code);
                if (orderFeedbackInfo.getData().toString() != null && !"".equals(orderFeedbackInfo.getData().toString()) && orderFeedbackInfo.getData().toString().length() != 0) {
                    Map<String, Object> stringObjectMap = EntityUtils.entityToMap(orderFeedbackInfo.getData());
                    build.setFeedbackUserEndType(usertype);
                    ResultUtil orderProblemDescription = complaintsAPI.getOrderProblemDescription(build);
                    if (orderProblemDescription.getData().toString() != null && !"".equals(orderProblemDescription.getData().toString()) && orderProblemDescription.getData().toString().length() != 0) {
                        List<Map> mapData = (List<Map>) orderProblemDescription.getData();
                        for (Map map : mapData) {
                            map.put("oi", stringObjectMap);
                        }
                        orderDetailed.setFeedbackMap(mapData);
                    }
                }
            } catch (Exception e) {
                log.error("咨询投诉查询异常", e);
            }
            return ResultUtil.ok(orderDetailed);
        }
    }

    /**
     * APP 运单详情查询运单的结算规则  已收单在快照表, 没有收单的查原始的
     * 这个是快照的规则
     * Yan
     *
     * @param code
     * @return
     */
    @Override
    public ResultUtil appSelectOrderCarriageRule(String code) {
        LineGoodsCarriageRuleDetailDTO detail = orderCastCalcSnapshotMapper.getOrderDetailCarriageRule(code);
        if (null == detail.getTolerantValueCoefficient() && null == detail.getTolerantValueWeight()) {
            detail.setToleranceItem("ZERO");
            detail.setToleranceItemValue(0.00);
        } else {
            if (null != detail.getTolerantValueCoefficient()) {
                detail.setToleranceItem("ANXISHU");
                detail.setToleranceItemValue(detail.getTolerantValueCoefficient());
            } else {
                detail.setToleranceItem("ANDUNSHU");
                detail.setToleranceItemValue(detail.getTolerantValueWeight());
            }
        }
        if (null != detail.getCompanyId()) {
            TCompanyInfoQuery companyInfoQuery = new TCompanyInfoQuery();
            companyInfoQuery.setCompanyId(detail.getCompanyId());
            ResultUtil resultUtil = companyService.selectCompanyInfoById(companyInfoQuery);
            if (null != resultUtil && null != resultUtil.getCode() && "success".equals(resultUtil.getCode())) {
                if (null != resultUtil.getData()) {
                    LinkedHashMap data = (LinkedHashMap) resultUtil.getData();
                    String companyContacts = null == data.get("companyContacts") ? "" : data.get("companyContacts").toString();
                    String companyContactsPhone = null == data.get("companyContactsPhone") ? "" : data.get("companyContactsPhone").toString();
                    detail.setCompanyContacts(companyContacts);
                    detail.setCompanyContactsPhone(companyContactsPhone);
                }
            }
        }
        return ResultUtil.ok(detail);
    }

    /**
     * App 根据 业务Id 查询运单进度
     * Yan
     *
     * @param code
     * @return
     */
    @Override
    public ResultUtil appSelectOrderProcess(String code) {
        List<AppOrderProcessDTO> states = orderStateMapper.appOrderProcess(code, false);
        List<AppOrderProcessDTO> trajectories =  orderVehicleTrajectoryMapper.appOrderProcess(code);
        Map<String, Object> map = new HashMap<>();
        map.put("process", states);
        map.put("trajectories", trajectories);
        return ResultUtil.ok(map);
    }

    /**
     * APP端 运单列表
     * Yan
     *
     * @return
     */
    @Override
    public ResultUtil appSelectOrderList(AppOrderSearchVO orderVO) throws RuntimeException {
        try {
            Map<String, Object> res = new HashMap<>();
            String userNickname = CurrentUser.getUserNickname();
            Integer accountId = CurrentUser.getUserAccountId();
            String usertype = CurrentUser.getUsertype();
            if (accountId == null) {
                return ResultUtil.error("账号信息错误");
            }

            // 判断筛选条件
            AppOrderSearchVO judge = SearchOrderFilter.judge(orderVO);


            OrderUtil.getUserType(judge);
            SysParam isscopeday = sysParamAPI.getParamByKey("ISSCOPEDAY");
            judge.setScoreDay(Integer.valueOf(isscopeday.getParamValue()));
            // 查询运单
            Page<Object> objects = PageHelper.startPage(orderVO.getPage(), orderVO.getSize());
            List<AppOrderListDTO> appOrderList = appOrderInfoMapper.appSelectOrder(judge);

            Future<BigDecimal> sumFuture = CompletableFuture.supplyAsync(() -> appOrderInfoMapper.appSelectOrderSum(judge));

            // 查询所有线路
            //CompletableFuture.supplyAsync(() -> lineGoodsAPI.selectLineGoodsRelByAccountId(accountId));
            Future<List<TLineGoodsRelDTO>> line = null;
            ResultUtil goodsSourceByCompanyId = null;
            //登录人是企业管理员时，按照企业ID查询线路
            if(CurrentUser.accountIsCompanyAdmin()){
                List<Integer> userCompanyId = CurrentUser.getUserCompanyIdInteger();
                TLineGoodsRel tLineGoodsRel = new TLineGoodsRel();
                tLineGoodsRel.setCompanyId(userCompanyId.get(0));
                goodsSourceByCompanyId = lineGoodsAPI.getDataByCompanyId(tLineGoodsRel);
            }else{
                line = CompletableFuture.supplyAsync(() -> lineGoodsAPI.selectLineGoodsRelByAccountId(accountId));
            }

            // 线路按钮去重
            Integer finalScoreDays = judge.getScoreDay()*1440;
            Future<List<AppOrderListDTO>> listFuture = CompletableFuture.supplyAsync(() -> {
                appOrderList.parallelStream().forEach((order) -> {

                    if (null!=order.getPayMethod()){
                        if ("NODEPAYPROPORTION".equals(order.getPayMethod()) || "NODEPAYFIXED".equals(order.getPayMethod()) ){
                            TOrderPayRule tOrderPayRule = tOrderPayRuleMapper.selectByCodeAndStatus(order.getCode());

                            List<AppOrderProcessDTO> appOrderProcess = orderStateMapper.appOrderProcess(order.getCode(), false);
                            List<String> resulttype = new ArrayList<>();
                            if(null!=appOrderProcess ){
                                appOrderProcess.forEach(appOrderProcessDTO -> resulttype.add(appOrderProcessDTO.getStateNodeValue()));
                                appOrderProcess.forEach(appOrderProcessDTO -> {
                                    if(resulttype.contains(DictEnum.M030.code) && null!= tOrderPayRule && StringUtils.isNotBlank(tOrderPayRule.getPayNodeType()) && "ZHPAYNODE".equals(tOrderPayRule.getPayNodeType())){
                                        order.setPayNodeType(tOrderPayRule.getPayNodeType());
                                    }
                                    if(resulttype.contains(DictEnum.M040.code) && null!= tOrderPayRule && StringUtils.isNotBlank(tOrderPayRule.getPayNodeType()) && "XHPAYNODE".equals(tOrderPayRule.getPayNodeType())){
                                        order.setPayNodeType(tOrderPayRule.getPayNodeType());
                                    }
                                    if(resulttype.contains(DictEnum.M050.code) && null!= tOrderPayRule && StringUtils.isNotBlank(tOrderPayRule.getPayNodeType()) && "SDPAYNODE".equals(tOrderPayRule.getPayNodeType())){
                                        order.setPayNodeType(tOrderPayRule.getPayNodeType());
                                    }
                                    if(resulttype.contains(DictEnum.M050.code) && null!= tOrderPayRule && StringUtils.isNotBlank(tOrderPayRule.getPayNodeType())  && !"SDPAYNODE".equals(tOrderPayRule.getPayNodeType()) && "WKPAYNODE".equals(tOrderPayRule.getPayNodeType())){
                                        order.setPayNodeType(tOrderPayRule.getPayNodeType());
                                    }
                                });
                            }
                            // 判断是否是第一次支付
                            order.setFirstPay(false);
                            List<TOrderPayRuleNodeTypeSortDTO> tOrderPayRules = tOrderPayRuleMapper.selectOrderPayRuleByOrderCode(order.getCode());
                            if (null != tOrderPayRules && !tOrderPayRules.isEmpty()) {
                                TOrderPayRuleNodeTypeSortDTO ruleNodeTypeSortDTO = tOrderPayRules.get(0);
                                if (null != ruleNodeTypeSortDTO.getPayNodeType()
                                        && null != ruleNodeTypeSortDTO.getPayStatus()
                                        && null != ruleNodeTypeSortDTO.getSort()) {
                                    if (DictEnum.PACKUNPAID.code.equals(ruleNodeTypeSortDTO.getPayStatus())) {
                                        order.setFirstPay(true);
                                    }
                                }
                            }
                        }
                    }
                    // 实时子状态,如果运单中支付状态还没有会写上，切他是支付状态就用它代替
                    if ("".equals(order.getOrderPayStatus())) {
                        if (OrderUtil.isOneContain(order.getChildStatus(), OrderUtil.getPayStatus())) {
                            order.setOrderPayStatus(order.getChildStatus());
                        }
                    }
                    // 查询运单的异常信息
                    List<Map<String, String>> errorTypes = orderAbnormalMapper.wxJudgeOrderIsError(order.getCode(), "App", userNickname);
                    if (!DictEnum.WQD.code.equals(order.getContractStatus())) {
                        List<Map<String, String>> filteredList = errorTypes.stream()
                                .filter(map -> !"HETONGYC".equals(map.get("abnormalType")))
                                .collect(Collectors.toList());
                        if (!errorTypes.isEmpty()) {
                            order.setAbnormalType(filteredList);
                        }
                    } else {
                        order.setErrorType(errorTypes);
                    }
                    if (order.getPackStatus().equals(1)) {
                        order.setButton(new HashSet<>());
                        order.setIsScore("4");
                    } else {
                        String buttons = order.getButtons();
                        if (StringUtils.isNotEmpty(buttons)) {
                            String[] split = order.getButtons().split(",");
                            Set<String> buttonSet = Arrays.stream(split).collect(Collectors.toSet());
                            String status = "";
                            String orderPayStatus = order.getOrderPayStatus();
                            if (StringUtils.isNotEmpty(orderPayStatus) && orderPayStatus.length() > 0) {
                                status = order.getOrderPayStatus();
                            } else {
                                status = order.getOrderExecuteStatus();
                            }
                            String[] code = WaybillButtonEnum.valueOf(status.replace("-", "")).getCode();
                            if("0".equals(order.getParam5())){
                                Iterator it = buttonSet.iterator();
                                while(it.hasNext()){
                                    Object o = it.next();
                                    if("CollOrder".equals(o)){
                                        it.remove();
                                    }
                                }
                            }
                            if (code.length != 0) {
                                boolean fla = true;
                                if(DictEnum.M130.code.equals(order.getOrderPayStatus())||DictEnum.M120.code.equals(order.getOrderPayStatus())
                                        ||DictEnum.P070.code.equals(order.getOrderPayStatus())||DictEnum.P110.code.equals(order.getOrderPayStatus())
                                        ||DictEnum.M090.code.equals(order.getOrderPayStatus())
                                        ||DictEnum.S0905.code.equals(order.getPayChildStatus())){
                                    if ("NODEPAYPROPORTION".equals(order.getPayMethod()) || "NODEPAYFIXED".equals(order.getPayMethod()) ){
                                        if(DictEnum.M050.code.equals(order.getOrderExecuteStatus()) || DictEnum.O060.code.equals(order.getOrderExecuteStatus())
                                                || DictEnum.M060.code.equals(order.getOrderExecuteStatus())|| DictEnum.M100.code.equals(order.getOrderExecuteStatus())){
                                            fla = false;
                                        }
                                    }else{
                                        fla = false;
                                    }
                                }
                                if(fla){
                                    if (DictEnum.M010.code.equals(status)) {
                                        Set<Integer> lineGoodsRuleIds = judge.getSendLines();
                                        Set<String> m010But = new HashSet<>();
                                        for (Integer id : lineGoodsRuleIds) {
                                            if (id.equals(order.getLineGoodsRelId())) {
                                                for (String bs : buttonSet) {
                                                    for (String ce : code) {
                                                        if (bs.equals(ce)) {
                                                            m010But.add(bs);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        order.setButton(m010But);
                                    } else {
                                        Set<String> bu = new HashSet<>();
                                        for (String be : code) {
                                            for (String bs : buttonSet) {
                                                if (DictEnum.O060.code.equals(be)) {
                                                    // 货运完成 并且 支付状态为空 或 支付状态为支付失败
                                                    if (null == order.getOrderPayStatus() || DictEnum.M080.code.equals(order.getOrderPayStatus())) {
                                                        bu.add(bs);
                                                    }
                                                } else if (be.equals(bs)) {
                                                    bu.add(bs);
                                                }
                                            }
                                        }
                                        if (DictEnum.S0651.code.equals(order.getPayChildStatus())) {
                                            if (buttonSet.contains("OrderPay")) {
                                                bu.add("OrderPay");
                                            }
                                        }
                                        order.setButton(bu);
                                    }
                                }
                            }
                        }
                    }
                    try {
                        ResultUtil orderFeedbackInfo = tOrderInfoService.getFeedbackOrderInfo(order.getCode());
                        if (orderFeedbackInfo.getData() != null && !"".equals(orderFeedbackInfo.getData())) {
                            Map<String, Object> stringObjectMap = EntityUtils.entityToMap(orderFeedbackInfo.getData());
                            OrderFeedbackVO orderFeedbackVO = new OrderFeedbackVO();
                            orderFeedbackVO.setOrderCode(order.getCode());
                            orderFeedbackVO.setFeedbackUserEndType(usertype);
                            ResultUtil orderProblemDescription = complaintsAPI.getOrderProblemDescription(orderFeedbackVO);
                            if (orderProblemDescription.getData() != null) {
                                List<Map> mapData = (List<Map>) orderProblemDescription.getData();
                                for (Map map : mapData) {
                                    map.put("oi", stringObjectMap);
                                }
                                order.setFeedbackMap(mapData);
                            }
                        }
                    } catch (Exception e){
                        log.info("咨询投诉相关异常, {}", ThrowableUtil.getStackTrace(e));
                    }
                    if (null==orderVO.getIsDPJ() || !orderVO.getIsDPJ() || orderVO.getPayState().length!=0 || orderVO.getWayBillState().length!=0) {
                        if (DictEnum.M090.code.equals(order.getOrderPayStatus())) {
                            if ("0".equals(order.getIsScore())) {
                                TOrderPayDetail tOrderPayDetail = orderPayDetailMapper.selectNewestOrderPayDetailByOrderCode(order.getCode());
                                if (null != tOrderPayDetail) {
                                    if (null!=tOrderPayDetail.getReturnTime()){
                                        LocalDateTime returnTime = tOrderPayDetail.getReturnTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                                        LocalDateTime nowTime = LocalDateTime.now();
                                        if (Duration.between(returnTime, nowTime).toMinutes() - finalScoreDays> 0) {
                                            //大于0 超过5天
                                            order.setIsScore("2");
                                        } else {
                                            //小于0 在5天内
                                            order.setIsScore("0");
                                        }
                                    }else {
                                        order.setIsScore("6");
                                    }
                                } else {
                                    order.setIsScore("3");//找不到提现记录
                                }
                            }
                        } else {
                            if (!"1".equals(order.getIsScore())) {
                                order.setIsScore("5");
                            }
                        }
                    }
                });
                return appOrderList;
            });

            // 查询总重
            BigDecimal sum = BigDecimal.ZERO;
            try {
                sum = sumFuture.get();
            } catch (Exception e) {
                e.printStackTrace();
            }

            try {
                List<AppOrderListDTO> appOrderListDTOS = listFuture.get();
                res.put("waybill", appOrderListDTOS);
            } catch (Exception e){
                log.error("运单列表获取失败：{}", ThrowableUtil.getStackTrace(e));
            }

            try {
                List<TLineGoodsRelDTO> lineGoodsRelDTOS = null;
                if(null != line){
                    lineGoodsRelDTOS = line.get();
                }else {
                    String data = JSON.toJSONString(goodsSourceByCompanyId.getData());
                    lineGoodsRelDTOS = JSON.parseArray(data, TLineGoodsRelDTO.class);
                }
                res.put("line", lineGoodsRelDTOS);
            } catch (Exception e) {
                log.error("获取员工货源失败：{}", ThrowableUtil.getStackTrace(e));
            }

            res.put("weight", OrderUtil.weight(sum));
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), res, objects.getTotal());
        } catch (RuntimeException e) {
            log.error("YL-058:运单查询出错查询参数：{}, 错误信息: {}", orderVO.toString() ,ThrowableUtil.getStackTrace(e));
            throw new RuntimeException("YL-058:查询运单失败");
        }
    }

    /**
     * @Description 运单召回
     * <AUTHOR>
     * @Date 2019/6/5 20:47
     * @Param
     * @Return
     * @Exception
     */
    //@LcnTransaction
    @Transactional
    @Override
    public ResultUtil orderBack(TOrderInfoVO record) {
        try {
            TOrderInfo orderInfo = orderInfoMapper.selectOrderByCode(record.getOrderCode());
            //运单完成且支付完成
            if ((orderInfo.getOrderExecuteStatus().equals(DictEnum.M100.code) && orderInfo.getOrderPayStatus().equals(DictEnum.M090.code))
                    || orderInfo.getOrderPayStatus().equals(DictEnum.M120.code)) {
                TOrderCastChanges tOrderCastChanges = orderCastChangesMapper.selectByNewOne(orderInfo.getCode());
                Payment payment = new Payment();
                TOrderPayInfo orderPayInfo = new TOrderPayInfo();
                orderPayInfo.setOrderCode(orderInfo.getCode());
                orderPayInfo.setOrderPayStatus(DictEnum.M090.code);
                orderPayInfo = tOrderPayInfoMapper.selectPayInfoBySelective(orderPayInfo);
                if (null == orderPayInfo.getParam1() || StringUtils.isBlank(orderPayInfo.getParam1())) {
                    // 如果资金转移模式不为空
                    // 经纪人模式和普通模式支付到车主：使用新召回
                    if (null != tOrderCastChanges.getCapitalTransferPattern()
                            && StringUtils.isNotBlank(tOrderCastChanges.getCapitalTransferPattern())) {
                        if (DictEnum.MANAGERPATTERN.code.equals(tOrderCastChanges.getCapitalTransferPattern())
                                && DictEnum.PAYTODRIVER.code.equals(tOrderCastChanges.getCapitalTransferType())
                                && (null == tOrderCastChanges.getServiceFee()
                                || tOrderCastChanges.getServiceFee().compareTo(BigDecimal.ZERO) == 0)) {
                            // 经纪人模式,支付到司机，无服务费，调用旧版召回
                            log.info("旧版召回");
                            payment.Recall(orderInfo);
                        } else {
                            log.info("新版召回");
                            payment.NewRecall(orderInfo);
                        }
                    } else {
                        // 无资金转移模式，调用旧版召回，司机运费召回、调度费
                        log.info("旧版召回");
                        payment.Recall(orderInfo);
                    }
                } else if ("1".equals(orderPayInfo.getParam1())) {
                    log.info("分润模式召回");
                    // 分润模式回调
                    payment.royaltyRecall(orderInfo);
                }

                //修改运单主表状态：P070 入账处理中；
                TOrderInfo orderForUpdate = new TOrderInfo();
                orderForUpdate.setId(orderInfo.getId());
                orderForUpdate.setOrderPayStatus(DictEnum.P070.code);
                orderInfoMapper.updateByPrimaryKeySelective(orderForUpdate);
                //TODO 添加运单执行状态子表：
                TOrderState orderState = new TOrderState();
                orderState.setCode(IdWorkerUtil.getInstance().nextId());
                orderState.setOrderCode(orderInfo.getCode());
                String usertype = CurrentUser.getUsertype();
                if (StringUtils.isNotEmpty(usertype)) {
                    if (DictEnum.BD.code.equals(usertype)) {
                        //会计点击召回
                        orderState.setStateNodeValue(DictEnum.SP0704.code);
                    }
                } else {
                    //运营点击召回
                    orderState.setStateNodeValue(DictEnum.SP0705.code);
                }
                orderState.setIfExpire(false);
                orderState.setEnable(false);
                orderState.setOperatorId(CurrentUser.getCurrentUserID());
                orderState.setOperateTime(new Date());
                if (null != record.getStateRemark()) {
                    orderState.setRemark(record.getStateRemark());
                }
                orderStateMapper.insertSelective(orderState);
                return ResultUtil.ok();
            }
        } catch (Exception e) {
            log.error("召回失败，错误码：ZJJ0041", e);
            String message = e.getMessage();
            if (message.contains(BusinessCode.PWALLETNSUFFICIENT.code) || message.contains(BusinessCode.CWALLETNSUFFICIENT.code)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("召回失败，错误码：ZJJ0041");
            }
        }
        return ResultUtil.ok();
    }


    /**
     * @param
     * @Description 打包召回
     * <AUTHOR>
     * @Date 2019/7/15 13:57
     * @Return
     * @Exception
     */
    //@LcnTransaction
    @Transactional
    @Override
    public ResultUtil orderBackDB(OrderPackVO record) {
        try {
            TOrderPackInfo packInfo = orderPackInfoMapper.selectOrderPackByCode(record.getOrderCode());
            if (packInfo.getPackStatus().equals(DictEnum.PACKPAID.code)
                    || packInfo.getPackStatus().equals(DictEnum.PACKEWITHDRAWERROR.code)) {
                Payment payment = new Payment();
                TOrderCastChanges tOrderCastChanges = orderCastChangesMapper.selectOrderCastChangeOneByPackCode(record.getOrderCode());
                ;
                if (null == tOrderCastChanges.getCapitalTransferPattern()
                        || StringUtils.isBlank(tOrderCastChanges.getCapitalTransferPattern())) {
                    payment.DBRecall(packInfo);
                } else {
                    List<TOrderInfo> tOrderInfos = orderInfoMapper.selectOrderInfoByPackCode(record.getOrderCode());
                    List<Integer> newOrderId = new ArrayList<>();
                    for (TOrderInfo orderInfo : tOrderInfos) {
                        TOrderPayInfo orderPayInfo = new TOrderPayInfo();
                        orderPayInfo.setOrderCode(orderInfo.getCode());
                        orderPayInfo.setOrderPayStatus(DictEnum.M090.code);
                        orderPayInfo = tOrderPayInfoMapper.selectPayInfoBySelective(orderPayInfo);
                        if (null == orderPayInfo.getParam1()) {
                            // 如果资金转移模式不为空
                            // 经纪人模式和普通模式支付到车主：使用新召回
                            if (null != tOrderCastChanges.getCapitalTransferPattern()
                                    && StringUtils.isNotBlank(tOrderCastChanges.getCapitalTransferPattern())) {
                                if (DictEnum.MANAGERPATTERN.code.equals(tOrderCastChanges.getCapitalTransferPattern())
                                        && DictEnum.PAYTODRIVER.code.equals(tOrderCastChanges.getCapitalTransferType())
                                        && (null == tOrderCastChanges.getServiceFee()
                                        || tOrderCastChanges.getServiceFee().compareTo(BigDecimal.ZERO) == 0)) {
                                    // 经纪人模式,支付到司机，无服务费，调用旧版召回
                                    log.info("旧版召回");
                                    payment.Recall(orderInfo);
                                } else {
                                    log.info("新版召回");
                                    payment.NewRecall(orderInfo);
                                    newOrderId.add(orderInfo.getId());
                                }
                            } else {
                                // 无资金转移模式，调用旧版召回，司机运费召回、调度费
                                log.info("旧版召回");
                                payment.Recall(orderInfo);
                            }
                        } else {
                            log.info("分润模式召回");
                            // 分润模式回调
                            payment.royaltyRecall(orderInfo);
                            newOrderId.add(orderInfo.getId());
                        }

                    }
                    if (!newOrderId.isEmpty()) {
                        // 修改原始运单支付状态为P070
                        orderInfoMapper.batchUpdateOrderPayStatusByOrderId(newOrderId, DictEnum.P070.code);
                    }
                }
                //修改打包主表状态
                orderPackInfoMapper.updatePayStatusByOrderCode(packInfo.getCode(), DictEnum.PACKRECALLPROCESSED.code);
                //添加运单执行状态子表
                List<String> orderCodes = orderPackInfoMapper.selectOrderCodeByPackCode(packInfo.getCode());
                String stateRemark = null != record.getStateRemark() ? record.getStateRemark() : "";
                String usertype = CurrentUser.getUsertype();
                String orderState = "";
                if (StringUtils.isNotEmpty(usertype)) {
                    if (DictEnum.BD.code.equals(usertype)) {
                        //会计点击召回
                        orderState = DictEnum.SP0704.code;
                    }
                } else {
                    //运营点击召回
                    orderState = DictEnum.SP0705.code;
                }
                createOrderState(orderState, CurrentUser.getUserNickname(), DictEnum.COMPC.code, stateRemark, orderCodes);
            } else {
                ResultUtil resultUtil = ResultUtil.error();
                resultUtil.setData(packInfo.getVirtualOrderNo());
                resultUtil.setMsg("运单状态错误，不可召回");
                return resultUtil;
            }
        } catch (RuntimeException e) {
            log.error("ZJJ-013:打包运单召回失败!", e);
            String message = e.getMessage();
            if (message.equals(BusinessCode.PWALLETNSUFFICIENT.code) || message.equals(BusinessCode.CWALLETNSUFFICIENT.code)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("ZJJ-013:打包运单召回失败!");
            }
        }
        return ResultUtil.ok();
    }

    /**
     * @Description 运单驳回
     * <AUTHOR>
     * @Date 2019/6/9 10:44
     * @Param
     * @Return
     * @Exception
     */
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil rejectOrder(AppOrderSearchVO record) {
        TOrderInfo orderInfo = orderInfoMapper.selectOrderByCode(record.getCode());
        if (orderInfo.getOrderExecuteStatus().equals(DictEnum.O060.code)
                && (null == orderInfo.getOrderPayStatus()
                || StringUtils.isEmpty(orderInfo.getOrderPayStatus())
                || DictEnum.M045.code.equals(orderInfo.getOrderPayStatus())
                || orderInfo.getOrderPayStatus().equals(DictEnum.M095.code)
                || orderInfo.getOrderPayStatus().equals(DictEnum.M065.code))) {
            //修改运单主表状态
            TOrderInfo order = new TOrderInfo();
            order.setId(orderInfo.getId());
            order.setOrderExecuteStatus(DictEnum.M060.code);
            order.setOrderPayStatus("");
            orderInfoMapper.updateByPrimaryKeySelective(order);
            //添加运单执行状态子表
            TOrderState orderState = new TOrderState();
            orderState.setCode(IdWorkerUtil.getInstance().nextId());
            orderState.setOrderCode(orderInfo.getCode());
            String usertype = CurrentUser.getUsertype();
            if (StringUtils.isNotEmpty(usertype)) {
                if (usertype.equals(DictEnum.CA.code)) {
                    //承运方驳回
                    orderState.setStateNodeValue(DictEnum.SM0602.code);
                } else if (usertype.equals(DictEnum.BD.code)) {
                    //货主企业驳回
                    orderState.setStateNodeValue(DictEnum.SM0601.code);
                }
            } else {
                //运营驳回
                orderState.setStateNodeValue(DictEnum.SM0603.code);
            }
            orderState.setIfExpire(false);
            orderState.setEnable(false);
            orderState.setOperatorId(CurrentUser.getCurrentUserID());
            orderState.setOperateTime(new Date());
            orderStateMapper.insertSelective(orderState);
            return ResultUtil.ok();
        } else {
            return ResultUtil.error("运单状态不符合驳回");
        }

    }


    /**
     * @Description 根据打包主表业务id查询运单主表信息
     * <AUTHOR>
     * @Date 2019/6/26 17:39
     * @Param
     * @Return
     * @Exception
     */
    @Override
    public List<TOrderInfo> selectOrderInfoByPackOrderCode(String code) {
        return orderInfoMapper.selectOrderInfoByPackCode(code);
    }

    /**
     * 创建车辆与司机关系
     *
     * @param carDriverRelVO 车辆司机信息
     * @return createInfo 创建信息
     * <AUTHOR>
     */
    private Integer createEnduserCarRel(CarDriverRelVO carDriverRelVO) {
        Integer endUserCarRelId = null;
        TEndUserCarRel endUserCarRel = new TEndUserCarRel();
        endUserCarRel.setEnduserId(carDriverRelVO.getEndDriverId());
        endUserCarRel.setEndcarId(carDriverRelVO.getEndcarId());
        endUserCarRel.setDataConfirmTime(new Date());
        //关系类型:车辆使用人（司机）
        endUserCarRel.setUserCarRelationType("CLSYRSJ");

        ResultUtil save = enduserCarRelAPI.save(endUserCarRel);
        Integer id = (Integer) save.getData();
        endUserCarRelId = id;
        return endUserCarRelId;
    }

    /**
     * 创建货源车辆司机子表
     *
     * @param orderInfo      运单信息表
     * @param carDriverRelVO 车辆与司机信息
     * <AUTHOR>
     */
    private TGoodsSourceVehicleDriverInfo createGoodsSourceVehicleDriverInfo(TOrderInfo orderInfo,
                                                                             CarDriverRelVO carDriverRelVO, String orderGenerateType) {
        TGoodsSourceVehicleDriverInfo goodsSourceVehicleDriverInfo = new TGoodsSourceVehicleDriverInfo();
        goodsSourceVehicleDriverInfo.setEndCarId(carDriverRelVO.getEndcarId());
        goodsSourceVehicleDriverInfo.setEndDriverId(carDriverRelVO.getEndDriverId());
        goodsSourceVehicleDriverInfo.setEndCarOwnerId(carDriverRelVO.getEndCarOwnerId());
        if (null != orderInfo.getEndCarOwnerId()) {
            goodsSourceVehicleDriverInfo.setEndUserCarRelId(orderInfo.getEndCarOwnerId());
        }
        //经纪人：由司机带入
        goodsSourceVehicleDriverInfo.setEndAgentId(carDriverRelVO.getEndAgentId());
        goodsSourceVehicleDriverInfo.setCode(IdWorkerUtil.getInstance().nextId());
        //运单资源信息表业务ID
        goodsSourceVehicleDriverInfo.setGoodsSourceCode(null != orderInfo.getGoodSourceCode() ? orderInfo.getGoodSourceCode() : "");
        //运单业务id
        goodsSourceVehicleDriverInfo.setOrderId(null != orderInfo.getCode() ? orderInfo.getCode() : "");
        //运单编号
        goodsSourceVehicleDriverInfo.setOrderBusinessCode(null != orderInfo.getOrderBusinessCode() ? orderInfo.getOrderBusinessCode() : "");
        goodsSourceVehicleDriverInfo.setCreateUser(null != orderInfo.getCreateUser() ? orderInfo.getCreateUser() : "");
        goodsSourceVehicleDriverInfo.setUpdateUser(null != orderInfo.getUpdateUser() ? orderInfo.getUpdateUser() : "");
        //货源执行状态:生成运单?
        goodsSourceVehicleDriverInfo.setGoodsExecuteStatus(DictEnum.CREATEORDER.code);
        //运单生成类型:人工发单?
        goodsSourceVehicleDriverInfo.setOrderGenerateType(orderGenerateType);
        goodsSourceVehicleDriverInfo.setEnable(false);

        goodsSourceVehicleDriverInfoMapper.insertSelective(goodsSourceVehicleDriverInfo);
        return goodsSourceVehicleDriverInfo;
    }

    /**
     * 创建运单子状态
     *
     * @param orderCode  运单主表业务id
     * @param sendBillVO
     * <AUTHOR>
     */
    private void createOrderState(String orderCode, String createUser, String stateNodeValue, SendOrderVO sendBillVO) {
        //TODO 新增运单执行状态子表 TOrderState
        TOrderState orderState = new TOrderState();
        orderState.setCode(IdWorkerUtil.getInstance().nextId());
        orderState.setOrderCode(orderCode);
        orderState.setStateNodeValue(stateNodeValue);
        orderState.setIfExpire(false);
        orderState.setOperatorId(CurrentUser.getCurrentUserID());
        orderState.setOperateMethod(sendBillVO.getOperateMethod());
        orderState.setOperateTime(new Date());
        orderState.setEnable(false);
        orderState.setCreateUser(createUser);
        orderState.setUpdateUser(createUser);
        //如果是计划发单，存储调度费
        if (DictEnum.S0101.code.equals(stateNodeValue)) {
            orderState.setParam1(sendBillVO.getDispatchFee().toString());
        }
        orderStateMapper.insertSelective(orderState);
    }

    /**
     * 创建运单车辆轨迹
     *
     * @param code           运单业务id
     * @param coordinate     线路信息
     * @param fromName       起始位置
     * @param carDriverRelVO 司机车辆信息
     */
    private void createOrderVehicleTrajectory(String code, String coordinate, String fromName,
                                              String trajectoryReceiveMethod, CarDriverRelVO carDriverRelVO,
                                              CompanySourceDTO companySourceDTO) {

        TrajectoryMongo trajectoryMongo = new TrajectoryMongo();
        trajectoryMongo.setId(IdWorkerUtil.getInstance().nextId());
        trajectoryMongo.setTrajectoryCode(IdWorkerUtil.getInstance().nextId());
        trajectoryMongo.setOrderCode(code);
        trajectoryMongo.setVehicleId(carDriverRelVO.getEndcarId());
        if (null == carDriverRelVO.getVehicleNumber()) {
            if (null != carDriverRelVO.getEndcarId()) {
                ResultUtil resultUtil = endCarInfoAPI.selectById(carDriverRelVO.getEndcarId());
                if (null != resultUtil && null != resultUtil.getCode() && resultUtil.getCode().equals("success")) {
                    if (null != resultUtil.getData()) {
                        LinkedHashMap result = (LinkedHashMap) resultUtil.getData();
                        if (null != result.get("vehicleNumber")) {
                            trajectoryMongo.setVehiclePlateNo(String.valueOf(result.get("vehicleNumber")));
                        }
                    }
                }
            }
        } else {
            trajectoryMongo.setVehiclePlateNo(carDriverRelVO.getVehicleNumber());
        }
        trajectoryMongo.setTrajectoryReceiveTime(new Date());
        trajectoryMongo.setTrajectoryReceiveMethod(trajectoryReceiveMethod);
        //地经、纬度
        String[] coordinates = coordinate.split(",");
        trajectoryMongo.setLongitude(coordinates[0]);
        trajectoryMongo.setLatitudes(coordinates[1]);

        trajectoryMongo.setVehicleGeographyPosition(fromName);
        trajectoryMongo.setVehicleStatus("0");
        trajectoryMongo.setVehicleCurrentSpeed("0");
        trajectoryMongo.setDataEnable(true);
        trajectoryMongo.setEnable(false);
        if (DictEnum.SENDORDERSIGN.code.equals(trajectoryReceiveMethod)) {
            trajectoryMongo.setProvince(companySourceDTO.getProviceFrom());
            trajectoryMongo.setCity(companySourceDTO.getCityFrom());
            trajectoryMongo.setCountry(companySourceDTO.getCountryFrom());
        } else if (DictEnum.RECEIVERORDERSIGN.code.equals(trajectoryReceiveMethod)) {
            trajectoryMongo.setProvince(companySourceDTO.getProviceEnd());
            trajectoryMongo.setCity(companySourceDTO.getCityEnd());
            trajectoryMongo.setCountry(companySourceDTO.getCountryEnd());
        }
        trajectoryMongoDao.insert(trajectoryMongo);
    }

    /**
     * APP 删单
     *
     * @param orderInfo
     * @return
     */
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil deleteOrder(TOrderInfoVO orderInfo) {
        try {
            TOrderInfo tOrderInfo = orderInfoMapper.selectOrderByCode(orderInfo.getCode());
            //发单节点支付模式
            if (DictEnum.NODEPAYFIXED.code.equals(tOrderInfo.getPayMethod())
                    || DictEnum.NODEPAYPROPORTION.code.equals(tOrderInfo.getPayMethod())) {
                boolean flag = tOrderPayRuleService.isNodePay(tOrderInfo);
                if (flag) {
                    return ResultUtil.error("当前运单已节点支付， 不可删除");
                }
            }
            if (tOrderInfo.getPackStatus().equals("1")) {
                return ResultUtil.error("运单已打包， 不可删除");
            }
            //已建单、已接单、已装货
            else if (tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M010.code)
                    || tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M020.code)
                    || tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M030.code)
                    || tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M040.code)
                    || tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M060.code)
                    || (tOrderInfo.getOrderExecuteStatus().equals(DictEnum.O060.code)
                        && (null == tOrderInfo.getOrderPayStatus() || StrUtil.isBlank(tOrderInfo.getOrderPayStatus()) || tOrderInfo.getOrderPayStatus().equals(DictEnum.M045.code) || tOrderInfo.getOrderPayStatus().equals(DictEnum.M095.code))
                        )
            ) {
                //1. 修改运单主表状态
                TOrderInfo info = new TOrderInfo();
                info.setId(tOrderInfo.getId());

                //已删单
                info.setOrderExecuteStatus("M-20");
                orderInfoMapper.updateByPrimaryKeySelective(info);
                //删除运单时，已投保运单的投保状态变更为“退保”
                if(null != tOrderInfo.getOrderBusinessCode()){
                    TOrderInsurance orderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(tOrderInfo.getOrderBusinessCode());
                    if (null != orderInsurance && 0 != orderInsurance.getInsure()) {
                        orderInsurance.setInsuranceCancellation(true);
                        orderInsurance.setUninsuredCause("删单");
                        orderInsurance.setUpdateUser(CurrentUser.getUserNickname());
                        orderInsurance.setUpdateTime(new Date());
                        orderInsuranceMapper.updateByPrimaryKeySelective(orderInsurance);
                    }
                }
                //2. 修改运单执行状态子表
                TOrderState orderState = new TOrderState();
                //发单员删单
                orderState.setCode(IdWorkerUtil.getInstance().nextId());
                orderState.setOrderCode(tOrderInfo.getCode());
                String usertype = CurrentUser.getUsertype();
                if (null != usertype) {
                    if (usertype.equals(DictEnum.BD.code)) {
                        //发单员删除
                        orderState.setOperateMethod("COMAPP");
                        orderState.setStateNodeValue("S-204");
                    } else if (usertype.equals(DictEnum.CA.code)) {
                        //承运方删除
                        orderState.setOperateMethod("CAPC");
                        orderState.setStateNodeValue("S-205");
                    } else {
                        orderState.setOperateMethod("OPPC");
                        orderState.setStateNodeValue("S-201");
                    }
                } else {
                    orderState.setOperateMethod("OPPC");
                    orderState.setStateNodeValue("S-201");
                }

                orderState.setIfExpire(false);
                orderState.setOperatorId(CurrentUser.getCurrentUserID());

                orderState.setOperateTime(new Date());
                orderState.setEnable(false);
                orderStateMapper.insertSelective(orderState);

                //运单删除记录
                TOrderDeleteLog orderDeleteLog = new TOrderDeleteLog();
                orderDeleteLog.setCode(IdWorkerUtil.getInstance().nextId());
                orderDeleteLog.setOrderCode(orderInfo.getCode());
                orderDeleteLog.setOrderBusinessCode(tOrderInfo.getOrderBusinessCode());
                orderDeleteLog.setDeletePerson(CurrentUser.getCurrentUsername());
                orderDeleteLog.setDeletePersonPhone(orderInfo.getDeletePersonPhone());
                orderDeleteLog.setVerificationCode(orderInfo.getVerificationCode());
                orderDeleteLog.setDeleteReason(orderInfo.getDeleteReason());
                orderDeleteLog.setDeleteTime(new Date());

                orderDeleteLogMapper.insertSelective(orderDeleteLog);
                //想易煤网发送信息
                tOrderInfoService.sendOrderInfoToYimei(tOrderInfo.getOrderBusinessCode());

                //恢复车辆司机状态
                CarDriverRelVO carDriverRelVO = new CarDriverRelVO();
                carDriverRelVO.setBizCode(tOrderInfo.getOrderBusinessCode());
                carDriverRelVO.setCarStatus("AVAILABLECANCLE");
                carDriverRelVO.setUserStatus("DISTRIBUTIONCANCLE");
                ResultUtil resultUtil = appCommonAPI.updateCarEnduserStatus(carDriverRelVO);
                if (resultUtil.getCode().equals("error")) {
                    throw new RuntimeException("修改车辆司机状态失败");
                }
                if (!tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M010.code)) {
                    //解冻
                    Payment payment = new Payment();
                    payment.deleteOrder(tOrderInfo);
                }

                //删单后删除车辆轨迹中间表数据
                MQMessage mqMessage = new MQMessage();
                mqMessage.setTopic(MqMessageTopic.ORDERCARLOCATION);
                mqMessage.setTag(MqMessageTag.DELETE);
                mqMessage.setBody(tOrderInfo.getCode());
                mqMessage.setKey(IdWorkerUtil.getInstance().nextId());
                mqAPI.sendMessage(mqMessage);

                //删单后删除车辆轨迹数据
                List<TrajectoryMongo> trajectoryMongoList = trajectoryMongoDao.getAllByOrderCode(tOrderInfo.getCode());
                for(TrajectoryMongo trajectoryMongo:trajectoryMongoList){
                    trajectoryMongoDao.delete(trajectoryMongo);
                }

                //发货到黄骅海通的货源数据跟对三分仓储对接
                SysParam sysParam = sysParamAPI.getParamByKey("HHCCKEY");
                if(sysParam.getParamValue().contains(tOrderInfo.getGoodSourceCode())){
                    TEndUserInfo tEndUserInfo = tEndSUserInfoAPI.selectByPrimaryKey(tOrderInfo.getEndDriverId());
                    TEndCarInfo tEndCarInfo = endCarInfoAPI.selectByPrimaryKey(tOrderInfo.getVehicleId());
                    addTaskHhcc(tOrderInfo,tEndUserInfo,tEndCarInfo,4,"ydDelete");
                }
            } else {
                return ResultUtil.error("运单状态错误， 不可删除");
            }
        } catch (Exception e) {
            log.error("批量删除运单失败, {}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再删除";
                throw new RuntimeException(message);
            }
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再删除";
                throw new RuntimeException(message);
            }
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            }
        }
        return ResultUtil.ok();
    }


    /**
     * 货源大厅-取消运单
     *
     * @param orderInfo
     * <AUTHOR>
     * @return
     */
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil cancelResourceHallOrder(TOrderInfoVO orderInfo) {
        try {
            TOrderInfo tOrderInfo = orderInfoMapper.selectOrderByCode(orderInfo.getCode());
            //已接单
            if (tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M011.code)) {
                //1. 修改运单主表状态
                TOrderInfo info = new TOrderInfo();
                info.setId(tOrderInfo.getId());
                //已取消
                info.setOrderExecuteStatus(DictEnum.M10.code);
                orderInfoMapper.updateByPrimaryKeySelective(info);
                //2. 修改运单执行状态子表
                TOrderState orderState = new TOrderState();
                orderState.setCode(IdWorkerUtil.getInstance().nextId());
                orderState.setOrderCode(tOrderInfo.getCode());
                String usertype = CurrentUser.getUsertype();
                //释放库存
                TGoodsSourceInfoDTO goodsSourceInfo = orderGoodsSourceInfoMapper.selectByLineGoodsRelId(tOrderInfo.getLineGoodsRelId());
                goodsSourceInfo.setEstimateGoodsWeight(goodsSourceInfo.getEstimateGoodsWeight() + tOrderInfo.getEstimateGoodsWeight());
                orderGoodsSourceInfoMapper.updateByPrimaryKeySelective(goodsSourceInfo);
                if (StringUtils.isNotBlank(usertype) && usertype.equals(DictEnum.CD.code)) {
                    //司机端 -》 司机取消运单
                    orderState.setStateNodeValue(DictEnum.S104.code);
                } else {
                    //企业app端 -》 企业收货人取消运单
                    //企业pc端 -》 管理员取消运单
                    orderState.setStateNodeValue(DictEnum.S101.code);
                    try{
                        log.info("发起微信通知");
                        //向公众号发送消息
                        SysParam sysParam = sysParamAPI.getParamByKey("WXMessageUrl");
                        ResultUtil result = accountService.queryOpenIdByEnduserId(tOrderInfo.getEndDriverId());
                        TCompanyInfoQuery infoQuery = new TCompanyInfoQuery().setCompanyId(tOrderInfo.getCompanyId());
                        ResultUtil returnData = companyService.selectCompanyInfoById(infoQuery);
                        TCompanyInfo companyInfo = JSON.parseObject(JSONObject.toJSON(returnData.getData()).toString(), TCompanyInfo.class);
                        TAccountDTO tAccountDTO = JSONUtil.toBean(JSONUtil.parseObj(result.getData()), TAccountDTO.class);
                        WxMsgSendUtil.sendMsgOrderCancel(companyInfo.getCompanyName(),goodsSourceInfo.getShippingAddress(),goodsSourceInfo.getUnloadingAddress(),
                                tAccountDTO.getOpenId(),orderInfo.getCancelHallPersonPhone(),
                                sysParam.getParamValue(),tOrderInfo.getGoodsName(),orderInfo.getRemark());
                        log.info("发起微信通知完成");
                    }catch (Exception e) {
                        log.error("发起微信通知失败, {}", ThrowableUtil.getStackTrace(e));
                    }
                }
                orderState.setOperateMethod(orderInfo.getOperateMethod());
                orderState.setIfExpire(false);
                orderState.setOperatorId(CurrentUser.getCurrentUserID());

                orderState.setOperateTime(new Date());
                orderState.setEnable(false);
                orderState.setUpdateTime(new Date());
                //取消原因
                orderState.setRemark(orderInfo.getRemark());
                orderState.setUpdateUser(CurrentUser.getUserNickname());
                orderState.setCreateUser(CurrentUser.getUserNickname());
                orderState.setCreateTime(new Date());
                orderStateMapper.insertSelective(orderState);

                //像易煤网发送信息
                tOrderInfoService.sendOrderInfoToYimei(tOrderInfo.getOrderBusinessCode());

                //恢复车辆司机状态
                CarDriverRelVO carDriverRelVO = new CarDriverRelVO();
                carDriverRelVO.setCode(tOrderInfo.getCode());
                carDriverRelVO.setBizCode(tOrderInfo.getOrderBusinessCode());
                carDriverRelVO.setCarStatus(DictEnum.AVAILABLECANCLE.code);
                carDriverRelVO.setUserStatus(DictEnum.DISTRIBUTIONCANCLE.code);
                ResultUtil resultUtil = appCommonAPI.updateCarEnduserStatus(carDriverRelVO);

                if (resultUtil.getCode().equals(DictEnum.ERROR.code)) {
                    throw new RuntimeException("修改车辆司机状态失败");
                }
            } else {
                return ResultUtil.error("运单状态错误， 不可取消");
            }
        } catch (Exception e) {
            log.error("取消货源大厅运单失败!, {}", ThrowableUtil.getStackTrace(e));
        }
        return ResultUtil.ok();
    }


    /**
     * 运单检查批量删单
     *
     * @param record
     * @return
     */
    @Override
    @LcnTransaction
    @Transactional
    public ResultUtil orderBatchDelete(TOrderInfo tOrderInfo, TOrderInfoUploadReq record,TOrderInfoUploadVo tOrderInfoUploadVo) {
        try {
            if (tOrderInfo.getPackStatus().equals("1")) {
                return ResultUtil.error("运单已打包， 不可删除");
            }
            //已建单、已接单、已装货
            else if (tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M010.code)
                    || tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M020.code)
                    || tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M030.code)
                    || tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M040.code)
                    || tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M060.code)
                    || (tOrderInfo.getOrderExecuteStatus().equals(DictEnum.O060.code)
                    && (null == tOrderInfo.getOrderPayStatus() || StrUtil.isBlank(tOrderInfo.getOrderPayStatus()) || tOrderInfo.getOrderPayStatus().equals(DictEnum.M045.code) || tOrderInfo.getOrderPayStatus().equals(DictEnum.M095.code))
                       )
            ){
                //1. 修改运单主表状态
                TOrderInfo info = new TOrderInfo();
                info.setId(tOrderInfo.getId());

                //已删单
                info.setOrderExecuteStatus("M-20");
                orderInfoMapper.updateByPrimaryKeySelective(info);
                //删除运单时，已投保运单的投保状态变更为“退保”
                TOrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(tOrderInfo.getId());
                if(null != orderInfo.getOrderBusinessCode()){
                    TOrderInsurance orderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(orderInfo.getOrderBusinessCode());
                    if (null != orderInsurance && 0 != orderInsurance.getInsure()) {
                        orderInsurance.setInsuranceCancellation(true);
                        orderInsurance.setUninsuredCause("删单");
                        orderInsurance.setUpdateUser(CurrentUser.getUserNickname());
                        orderInsurance.setUpdateTime(new Date());
                        orderInsuranceMapper.updateByPrimaryKeySelective(orderInsurance);
                    }
                }
                //2. 修改运单执行状态子表
                TOrderState orderState = new TOrderState();
                //发单员删单
                orderState.setCode(IdWorkerUtil.getInstance().nextId());
                orderState.setOrderCode(tOrderInfo.getCode());
                String usertype = CurrentUser.getUsertype();
                if (null != usertype) {
                    if (usertype.equals(DictEnum.BD.code)) {
                        //发单员删除
                        orderState.setOperateMethod("COMAPP");
                        orderState.setStateNodeValue("S-204");
                    }
                    else if (usertype.equals(DictEnum.CA.code)) {
                        //承运方删除
                        orderState.setOperateMethod("CAPC");
                        orderState.setStateNodeValue("S-205");
                    }else{
                        orderState.setOperateMethod("OPPC");
                        orderState.setStateNodeValue("S-201");
                    }
                } else {
                    orderState.setOperateMethod("OPPC");
                    orderState.setStateNodeValue("S-201");
                }
                orderState.setIfExpire(false);
                orderState.setOperatorId(CurrentUser.getCurrentUserID());
                orderState.setOperateTime(new Date());
                orderState.setEnable(false);
                orderStateMapper.insertSelective(orderState);

                //运单删除记录
                TOrderDeleteLog orderDeleteLog = new TOrderDeleteLog();
                orderDeleteLog.setCode(IdWorkerUtil.getInstance().nextId());
                orderDeleteLog.setOrderCode(tOrderInfo.getCode());
                orderDeleteLog.setOrderBusinessCode(tOrderInfo.getOrderBusinessCode());
                orderDeleteLog.setDeletePerson(CurrentUser.getCurrentUsername());
                orderDeleteLog.setDeletePersonPhone(record.getDeletePersonPhone());
                orderDeleteLog.setVerificationCode(record.getVerificationCode());
                orderDeleteLog.setDeleteReason(tOrderInfoUploadVo.getDeleteReason()+","+tOrderInfoUploadVo.getProposerName());
                orderDeleteLog.setDeleteTime(new Date());
                orderDeleteLogMapper.insertSelective(orderDeleteLog);

                //恢复车辆司机状态
                CarDriverRelVO carDriverRelVO = new CarDriverRelVO();
                carDriverRelVO.setBizCode(tOrderInfo.getOrderBusinessCode());
                carDriverRelVO.setCarStatus("AVAILABLECANCLE");
                carDriverRelVO.setUserStatus("DISTRIBUTIONCANCLE");
                ResultUtil resultUtil = appCommonAPI.updateCarEnduserStatus(carDriverRelVO);

                if (resultUtil.getCode().equals("error")){
                    throw new RuntimeException("修改车辆司机状态失败");
                }

                if (!tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M010.code)) {
                    //解冻
                    Payment payment = new Payment();
                    payment.deleteOrder(tOrderInfo);
                }
                //删单后删除车辆轨迹中间表数据
                MQMessage mqMessage = new MQMessage();
                mqMessage.setTopic(MqMessageTopic.ORDERCARLOCATION);
                mqMessage.setTag(MqMessageTag.DELETE);
                mqMessage.setBody(tOrderInfo.getCode());
                mqMessage.setKey(IdWorkerUtil.getInstance().nextId());
                mqAPI.sendMessage(mqMessage);

                //删单后删除车辆轨迹数据
                trajectoryMongoDao.deleteByOrderCode(tOrderInfo.getCode());

                //发货到黄骅海通的货源数据跟对三分仓储对接
                SysParam sysParam = sysParamAPI.getParamByKey("HHCCKEY");
                if(sysParam.getParamValue().contains(tOrderInfo.getGoodSourceCode())){
                    TEndUserInfo tEndUserInfo = tEndSUserInfoAPI.selectByPrimaryKey(tOrderInfo.getEndDriverId());
                    TEndCarInfo tEndCarInfo = endCarInfoAPI.selectByPrimaryKey(tOrderInfo.getVehicleId());
                    addTaskHhcc(tOrderInfo,tEndUserInfo,tEndCarInfo,4,"ydDelete");
                }
            }
        } catch (Exception e){
            log.error("批量删除运单失败",e);
            String message = e.getMessage();
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)){
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再删除";
                throw new RuntimeException(message);
            }
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)){
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再删除";
                throw new RuntimeException(message);
            }
            if (StringUtils.checkChineseCharacter(message)){
                throw new RuntimeException(message);
            }
        }
        return ResultUtil.ok();
    }

    /**
     * 创建运单执行状态子表
     *
     * @param orderCode
     * @param stateNodeValue
     */
    public void createState(String orderCode, String stateNodeValue, String operateMethod) {
        TOrderState orderState = new TOrderState();
        orderState.setCode(IdWorkerUtil.getInstance().nextId());
        orderState.setOrderCode(orderCode);
        //节点: 收单员收货
        orderState.setStateNodeValue(stateNodeValue);
        orderState.setOperatorId(CurrentUser.getCurrentUserID());
        Date date = new Date();

        if (DictEnum.S0650.code.equals(stateNodeValue) || DictEnum.S0651.code.equals(stateNodeValue)
                || DictEnum.S0652.code.equals(stateNodeValue)) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.SECOND, 1);
            date = calendar.getTime();
        }
        orderState.setOperateTime(date);
        orderState.setOperateMethod(operateMethod);
        orderState.setEnable(false);
        orderState.setIfExpire(false);
        orderStateMapper.insertSelective(orderState);
    }


    /**
     * @Description 发单查询车辆司机状态
     * <AUTHOR>
     * @Date 2019/6/2 11:58
     * @Param
     * @Return
     * @Exception
     */
    public List<EnduserCarStatus> selectEnduserCarStatus(List<CarDriverRelVO> carDriverRelVOS) {
        List<EnduserCarStatus> enduserCarStatusList = new ArrayList<>();
        for (CarDriverRelVO carDriverRelVO : carDriverRelVOS) {
            EnduserCarStatus status = new EnduserCarStatus();
            status.setEndcarId(carDriverRelVO.getEndcarId());
            status.setEnduserId(carDriverRelVO.getEndDriverId());
            enduserCarStatusList.add(status);
        }
        TEnduserCarStatus enduserCarStatus = new TEnduserCarStatus();
        enduserCarStatus.setEnduserCarStatuses(enduserCarStatusList);
        ResultUtil usercarStatus = endSUserInfoAPI.selectEnduserCarStatus(enduserCarStatus);
        LinkedHashMap data = (LinkedHashMap) usercarStatus.getData();
        ArrayList<LinkedHashMap> enduserCarStatuses = (ArrayList) data.get("enduserCarStatuses");
        ArrayList<EnduserCarStatus> list = new ArrayList<>();
        ObjectMapper objectMapper = new ObjectMapper();
        for (LinkedHashMap map : enduserCarStatuses) {
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            EnduserCarStatus status = objectMapper.convertValue(map, EnduserCarStatus.class);
            list.add(status);
        }

        return list;
    }


    /**
     * @Description 判断车辆司机状态
     * <AUTHOR>
     * @Date 2019/6/2 14:26
     * @Param
     * @Return 1: 可用司机车辆 2. 不可用司机车辆
     * @Exception
     */
    public HashMap<String, Object> checkUserCarStatus(List<CarDriverRelVO> carDriverRelVOS, List<EnduserCarStatus> enduserCarStatuses) {
        StringBuilder sb = new StringBuilder();
        List<CarDriverRelVO> carUser = new ArrayList<>();
        for (EnduserCarStatus enduserCarStatus : enduserCarStatuses) {
            log.info("身份证信息是否完善, {}", enduserCarStatus.getIdCardPerfect());
            if (null != enduserCarStatus.getIdCardPerfect() && !enduserCarStatus.getIdCardPerfect()) {
                String realName = null == enduserCarStatus.getDriverName() ? "" : enduserCarStatus.getDriverName();
                sb.append("请司机").append(realName).append("先上传身份证后再发单。");
                continue;
            }
            for (CarDriverRelVO carDriverRelVO : carDriverRelVOS) {
                if (enduserCarStatus.getEndcarId().equals(carDriverRelVO.getEndcarId())) {
                    if (!enduserCarStatus.getEndcarUseable()) {
                        sb.append(carDriverRelVO.getVehicleNumber()).append(":").append(enduserCarStatus.getEndcarStatus()).append("。");
                    }
                    if (!enduserCarStatus.getEnduserUseable()) {
                        sb.append(carDriverRelVO.getRealName()).append(":").append(enduserCarStatus.getEnduserStatus()).append("。");
                    }
                    if (enduserCarStatus.getEndcarUseable() && enduserCarStatus.getEnduserUseable()) {
                        carUser.add(carDriverRelVO);
                    }
                }
            }
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("unable", sb.toString());
        map.put("useable", carUser);
        return map;
    }

    /**
     * @Description 模板加载发单，判断车辆司机状态
     * <AUTHOR>
     * @Date 2019/11/13 10:26
     * @Param
     * @Return 1: 可用司机车辆 2. 不可用司机车辆
     * @Exception
     */
    public HashMap<String, Object> checkUserCarStatusTemplateSend(List<CarDriverRelVO> carDriverRelVOS, List<EnduserCarStatus> enduserCarStatuses) {
        List<String> errorStatus = new ArrayList<>();
        StringBuffer sb = new StringBuffer();
        List<CarDriverRelVO> carUser = new ArrayList<>();
        for (EnduserCarStatus enduserCarStatus : enduserCarStatuses) {
            for (CarDriverRelVO carDriverRelVO : carDriverRelVOS) {
                if (enduserCarStatus.getEndcarId().equals(carDriverRelVO.getEndcarId())
                        && enduserCarStatus.getEnduserId().equals(carDriverRelVO.getEndDriverId())) {
                    //车辆司机都是不可用
                    if (!enduserCarStatus.getEndcarUseable() && !enduserCarStatus.getEnduserUseable()) {
                        //车辆司机都是已计划
                        if (DictEnum.CARPLANORDER.code.equals(enduserCarStatus.getEndcarStatusCode())
                                && DictEnum.USERPLANORDER.code.equals(enduserCarStatus.getEnduserStatusCode())) {
                            sb.append("车辆" + enduserCarStatus.getVehicleNumber()
                                    + ", 司机" + enduserCarStatus.getRealName() + "存在执行中的运单，运单号为："
                                    + enduserCarStatus.getCurrentReceiptsNo() + "。");
                            continue;
                        }
                        //车辆或司机是已计划
                        if (DictEnum.CARPLANORDER.code.equals(enduserCarStatus.getEndcarStatusCode())) {
                            sb.append("车辆" + enduserCarStatus.getVehicleNumber()
                                    + "存在执行中的运单，运单号为："
                                    + enduserCarStatus.getCurrentReceiptsNo() + "。");
                        } else {
                            errorStatus.add(carDriverRelVO.getVehicleNumber() + ":" + enduserCarStatus.getEndcarStatus());
                            sb.append(carDriverRelVO.getVehicleNumber() + ":" + enduserCarStatus.getEndcarStatus() + "。");
                        }
                        if (DictEnum.USERPLANORDER.code.equals(enduserCarStatus.getEnduserStatusCode())) {
                            sb.append("司机" + enduserCarStatus.getCurrentDriver() + "存在执行中的运单，运单号为："
                                    + enduserCarStatus.getUserCurrentReceiptsNo() + "。");
                        } else {
                            errorStatus.add(carDriverRelVO.getRealName() + ":" + enduserCarStatus.getEnduserStatus());
                            sb.append(carDriverRelVO.getRealName() + ":" + enduserCarStatus.getEnduserStatus() + "。");
                        }
                        continue;
                    }
                    //车辆或司机是已计划
                    if (DictEnum.CARPLANORDER.code.equals(enduserCarStatus.getEndcarStatusCode())) {
                        sb.append("车辆" + enduserCarStatus.getVehicleNumber()
                                + ", 存在执行中的运单，运单号为："
                                + enduserCarStatus.getCurrentReceiptsNo() + "。");
                    } else if (!enduserCarStatus.getEndcarUseable()) {
                        errorStatus.add(carDriverRelVO.getVehicleNumber() + ":" + enduserCarStatus.getEndcarStatus());
                        sb.append(carDriverRelVO.getVehicleNumber() + ":" + enduserCarStatus.getEndcarStatus() + "。");
                    }
                    if (DictEnum.USERPLANORDER.code.equals(enduserCarStatus.getEnduserStatusCode())) {
                        sb.append("司机" + enduserCarStatus.getCurrentDriver() + "存在执行中的运单，运单号为："
                                + enduserCarStatus.getUserCurrentReceiptsNo() + "。");
                    } else if (!enduserCarStatus.getEnduserUseable()) {
                        errorStatus.add(carDriverRelVO.getRealName() + ":" + enduserCarStatus.getEnduserStatus());
                        sb.append(carDriverRelVO.getRealName() + ":" + enduserCarStatus.getEnduserStatus() + "。");
                    }
                    if (enduserCarStatus.getEndcarUseable() && enduserCarStatus.getEnduserUseable()) {
                        carUser.add(carDriverRelVO);
                    }
                }
            }
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("unable", sb.toString());
        map.put("useable", carUser);
        return map;
    }


    /**
     * @Description 创建运单快照表
     * <AUTHOR>
     * @Date 2019/6/8 10:45
     * @Param
     * @Return
     * @Exception
     */
    public TOrderCastCalcSnapshot createOrderCastCalcSnapshot(OrderDetailVO record, TOrderInfo order) {
        TOrderCastCalcSnapshot orderCastCalcSnapshot = new TOrderCastCalcSnapshot();
        if (null != record.getRuleName() && StringUtils.isNotEmpty(record.getRuleName()) && !"null".equals(record.getRuleName())) {
            orderCastCalcSnapshot.setGoodsUnitPrice(record.getGoodsUnitPrice());
            orderCastCalcSnapshot.setLossCutExpression(record.getLossCutExpression());
            orderCastCalcSnapshot.setLossPayableExpression(record.getLossPayableExpression());
            orderCastCalcSnapshot.setRiseCutExpression(null != record.getRiseCutExpression() ? record.getRiseCutExpression() : "");
            orderCastCalcSnapshot.setRisePayableExpression(record.getRisePayableExpression());
            orderCastCalcSnapshot.setLineGoodsCarriageRuleId(record.getLineGoodsCarriageRuleId());
            orderCastCalcSnapshot.setCarriageZeroCutPayment(record.getCarriageZeroCutPayment());
            orderCastCalcSnapshot.setCarriageZeroCutPaymentRule(record.getCarriageZeroCutPaymentRule());
            orderCastCalcSnapshot.setLineGoodsCarriageRuleId(record.getLineGoodsCarriageRuleId());
            orderCastCalcSnapshot.setLineGoodsCarriageChangeId(record.getLineGoodsCarriageChangeId());
            orderCastCalcSnapshot.setFixCutFee(record.getFixCutFee());
            orderCastCalcSnapshot.setFixCutRemark(record.getFixCutRemark());
            orderCastCalcSnapshot.setOtherCutFee1(null == record.getOtherCutFee1() ? new BigDecimal(0) : record.getOtherCutFee1());
            orderCastCalcSnapshot.setOtherCutFee2(null == record.getOtherCutFee2() ? new BigDecimal(0) : record.getOtherCutFee2());
            orderCastCalcSnapshot.setOtherCutFee3(null == record.getOtherCutFee3() ? new BigDecimal(0) : record.getOtherCutFee3());
            orderCastCalcSnapshot.setOtherCutFee4(null == record.getOtherCutFee4() ? new BigDecimal(0) : record.getOtherCutFee4());
            orderCastCalcSnapshot.setOtherCutRemark1(null == record.getOtherCutRemark1() ? "" : record.getOtherCutRemark1());
            orderCastCalcSnapshot.setOtherCutRemark2(null == record.getOtherCutRemark2() ? "" : record.getOtherCutRemark2());
            orderCastCalcSnapshot.setOtherCutRemark3(null == record.getOtherCutRemark3() ? "" : record.getOtherCutRemark3());
            orderCastCalcSnapshot.setOtherCutRemark4(null == record.getOtherCutRemark4() ? "" : record.getOtherCutRemark4());
            orderCastCalcSnapshot.setGoodsCutImpurities(record.getGoodsCutImpurities());
            orderCastCalcSnapshot.setGoodsCutWater(record.getGoodsCutWater());
            orderCastCalcSnapshot.setRuleName(record.getRuleName());
            orderCastCalcSnapshot.setLoseOrRise(record.getLoseOrRise());
            orderCastCalcSnapshot.setLoseOrRiseCut(record.getLoseOrRiseCut());
            if (record.getToleranceItem().equals(DictEnum.ANDUNSHU.code)) {
                BigDecimal bigDecimal = BigDecimal.valueOf(record.getToleranceItemValue());
                bigDecimal = bigDecimal.setScale(4, BigDecimal.ROUND_HALF_UP);
                orderCastCalcSnapshot.setTolerantValueWeight(bigDecimal.doubleValue());
                orderCastCalcSnapshot.setTolerantValueCoefficient(null);
            }
            if (record.getToleranceItem().equals(DictEnum.ANXISHU.code)) {
                BigDecimal bigDecimal = BigDecimal.valueOf(record.getToleranceItemValue());
                bigDecimal = bigDecimal.setScale(4, BigDecimal.ROUND_HALF_UP);
                orderCastCalcSnapshot.setTolerantValueCoefficient(bigDecimal.doubleValue());
                orderCastCalcSnapshot.setTolerantValueWeight(null);
            }
            orderCastCalcSnapshot.setCutImpuritiesIsCalcvalue(record.getCutImpuritiesIsCalcvalue());
            orderCastCalcSnapshot.setCutWaterIsCalcvalue(record.getCutWaterIsCalcvalue());
            // add by zhangjiji 2019/6/5
            orderCastCalcSnapshot.setDeficitWeight(record.getDeficitWeight());
        }

        if(DictEnum.CARRIAGE_PRICE_UNIT_BOX.code.equals(record.getCarriagePriceUnit())){
            if(record.getBoxNum()<2){
                orderCastCalcSnapshot.setCarriagePayment(record.getCarriagePayment());
                orderCastCalcSnapshot.setUserConfirmCarriagePayment(record.getUserConfirmCarriagePayment());

                orderCastCalcSnapshot.setCarriageUnitPrice(record.getCarriageUnitPrice1());
                orderCastCalcSnapshot.setRealDeliverWeight(Double.parseDouble(record.getPrimaryWeight1().toString()));
                orderCastCalcSnapshot.setRealDispathWeight(Double.parseDouble(record.getDischargeWeight1().toString()));
            }else{
                orderCastCalcSnapshot.setCarriagePayment(record.getCarriagePayment());
                orderCastCalcSnapshot.setUserConfirmCarriagePayment(record.getUserConfirmCarriagePayment());

                orderCastCalcSnapshot.setCarriageUnitPrice(record.getCarriageUnitPrice1());
                orderCastCalcSnapshot.setRealDeliverWeight(Double.parseDouble((record.getPrimaryWeight1().add(record.getPrimaryWeight2())).toString()));
                orderCastCalcSnapshot.setRealDispathWeight(Double.parseDouble((record.getDischargeWeight1().add(record.getDischargeWeight2())).toString()));
            }
            orderCastCalcSnapshot.setFixCutFee(record.getFixCutFee());
            orderCastCalcSnapshot.setFixCutRemark(record.getFixCutRemark());
        }else if(DictEnum.CARRIAGE_PRICE_UNIT_CAR.code.equals(record.getCarriagePriceUnit())){
            orderCastCalcSnapshot.setFixCutFee(record.getFixCutFee());
            orderCastCalcSnapshot.setFixCutRemark(record.getFixCutRemark());
            orderCastCalcSnapshot.setCarriagePayment(record.getCarriagePayment());
            orderCastCalcSnapshot.setUserConfirmCarriagePayment(record.getUserConfirmCarriagePayment());

            orderCastCalcSnapshot.setCarriageUnitPrice(record.getCarriageUnitPrice());
            orderCastCalcSnapshot.setRealDeliverWeight(record.getRealDeliverWeight());
            orderCastCalcSnapshot.setRealDispathWeight(record.getRealDispathWeight());
            orderCastCalcSnapshot.setDeliverWeightNotesPhoto(record.getDeliverWeightNotesPhoto());
            orderCastCalcSnapshot.setReceiveWeightNotesPhoto(record.getReceiveWeightNotesPhoto());
            orderCastCalcSnapshot.setDeliverWeightNotesUploadTime(record.getDeliverWeightNotesUploadTime());
            orderCastCalcSnapshot.setReceiveWeightNotesUploadTime(record.getReceiveWeightNotesUploadTime());
        }else{
            orderCastCalcSnapshot.setCarriagePayment(record.getCarriagePayment());
            orderCastCalcSnapshot.setUserConfirmCarriagePayment(record.getUserConfirmCarriagePayment());

            orderCastCalcSnapshot.setCarriageUnitPrice(record.getCarriageUnitPrice());
            orderCastCalcSnapshot.setRealDeliverWeight(record.getRealDeliverWeight());
            orderCastCalcSnapshot.setRealDispathWeight(record.getRealDispathWeight());
            orderCastCalcSnapshot.setDeliverWeightNotesPhoto(record.getDeliverWeightNotesPhoto());
            orderCastCalcSnapshot.setReceiveWeightNotesPhoto(record.getReceiveWeightNotesPhoto());
            orderCastCalcSnapshot.setDeliverWeightNotesUploadTime(record.getDeliverWeightNotesUploadTime());
            orderCastCalcSnapshot.setReceiveWeightNotesUploadTime(record.getReceiveWeightNotesUploadTime());
        }
        orderCastCalcSnapshot.setGoodsType(order.getGoodsName());
        orderCastCalcSnapshot.setCode(IdWorkerUtil.getInstance().nextId());
        orderCastCalcSnapshot.setDataEnable(true);
        orderCastCalcSnapshot.setEnable(false);
        orderCastCalcSnapshot.setOrderCode(order.getCode());
        orderCastCalcSnapshot.setSettledWeight(record.getSettledWeight());
        orderCastCalcSnapshot.setRemark(record.getRemark());

        return orderCastCalcSnapshot;
    }

    /**
     * @param codes 运单主表业务id 或打包主表业务id
     * @Description 支付：创建运单执行状态子表
     * <AUTHOR>
     * @Date 2019/7/12 11:46
     * @Param operateMethod 操作方式
     * @Return
     * @Exception
     */
    private void createOrderState(String stateNodeValue, String createUser, String operateMethod, String stateRemark, List<String> codes) {
        if (StringUtils.isEmpty(stateNodeValue)) {
            log.error("未找到当前用户角色");
        }
        //添加运单执行状态子表
        IdWorkerUtil idWorkerUtil = IdWorkerUtil.getInstance();
        if (null != codes && codes.size() >= 1) {
            ArrayList<TOrderState> orderStates = new ArrayList<>();
            for (String code : codes) {
                //添加运单执行状态子表
                TOrderState orderState = new TOrderState();
                orderState.setStateNodeValue(stateNodeValue);
                orderState.setOperatorId(CurrentUser.getCurrentUserID());
                orderState.setOperatorIp("");
                orderState.setOperateMethod(operateMethod);
                orderState.setOperateTime(new Date());
                orderState.setOperateGeographyPosition("");
                orderState.setIfExpire(false);
                //添加运单执行状态子表
                orderState.setCode(idWorkerUtil.nextId());
                orderState.setOrderCode(code);
                orderState.setCreateUser(createUser);
                orderState.setCreateTime(new Date());
                orderState.setUpdateUser(createUser);
                orderState.setUpdateTime(new Date());
                orderState.setRemark(stateRemark);
                orderState.setEnable(false);
                orderStates.add(orderState);
            }
            orderStateMapper.batchInsert(orderStates);
        }

    }

    /*
     * <AUTHOR>
     * @Description 计划发单
     * @Date 2019/10/28 15:18
     * @Param
     * @return
     **/
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil planOrder(String code, SendOrderVO sendBillVO, CarDriverRelVO carDriverRelVO, TOrderInfo orderInfo, String lineType,
                                String capitalTransferType, LinkedHashMap carrier, CompanySourceDTO companySourceDTO) {
        try {
            //查询车辆司机状态
            List<CarDriverRelVO> carDriverRelVOS = new ArrayList<>();
            carDriverRelVOS.add(carDriverRelVO);
            List<EnduserCarStatus> enduserCarStatusList = selectEnduserCarStatus(carDriverRelVOS);
            HashMap<String, Object> map = checkUserCarStatus(carDriverRelVOS, enduserCarStatusList);
            String unable = null != map.get("unable") ? map.get("unable").toString() : "";
            if (unable.length() > 0) {
                return ResultUtil.error(unable);
            }

            carDriverRelVO.setCode(orderInfo.getCode());
            //设置车辆司机的当前运单编号
            carDriverRelVO.setCurrentDriverAccountNo(carDriverRelVO.getPhone());
            //已接单
            carDriverRelVO.setCarStatus(DictEnum.CARPLANORDER.code);
            //已接单
            carDriverRelVO.setUserStatus(DictEnum.USERPLANORDER.code);
            Integer endUserCarRelId = carDriverRelVO.getEnduserCarRelId();
            carDriverRelVO.getUserCarRelationType();

            //设置运单主表业务id add by zhangjiji 2019/6/15 update by zhangjiji 2019/6/23
            orderInfo.setCode(code);

            //运单编号：线路编码 + 年月日时分秒毫秒
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            String date = sdf.format(new Date());
            String random = RandomUtil.randomString(3, 10);
            //设置运单主表运单编号
            orderInfo.setOrderBusinessCode(lineType + date + random);
            //创建运单主表
            orderInfo.setDeliverOrderTime(new Date());
            orderInfo.setVehicleId(carDriverRelVO.getEndcarId());
            orderInfo.setEndDriverId(carDriverRelVO.getEndDriverId());
            orderInfo.setEndAgentId(carDriverRelVO.getEndAgentId());
            orderInfo.setEndUserCarRelId(endUserCarRelId);
            orderInfo.setEndCarOwnerId(carDriverRelVO.getEnduserIdRel());
            orderInfo.setOperateMethod(sendBillVO.getOperateMethod());
            orderInfo.setBusinessAssist(companySourceDTO.getBusinessAssist());
            // 放入当前月索引 Yan
            orderInfo.setParam4(OrderUtil.indexTime());
            orderInfo.setEnable(false);
            //创建运单
            if (null != orderInfo.getEndAgentId() && orderInfo.getEndAgentId() == 0) {
                orderInfo.setEndAgentId(null);
            }

            //从企业项目与承运方关系表获取调度费系数
            TProjectCarrierInfoVO vo = new TProjectCarrierInfoVO();
            vo.setCarrierId(orderInfo.getCarrierId());
            vo.setCompanyId(orderInfo.getCompanyId());
            vo.setProjectId(orderInfo.getCompanyProjectId());
            TProjectCarrierRel tProjectCarrierRel = projectCarrierAPI.selectDispatchFeeCoefficient(vo);
            if (null == tProjectCarrierRel) {
                log.error("错误码：ZYH015，未获取到调度费系数");
                throw new RuntimeException("错误码：ZYH015，请联系运营平台予以解决。");
            }

            BigDecimal dispatchFee = OrderMoneyUtil.getDispatchFee(tProjectCarrierRel.getDispatchFeeCoefficient(), orderInfo.getEstimateTotalFee());
            //设置调度费
            sendBillVO.setDispatchFee(dispatchFee);
            orderInfo.setDispatchFee(BigDecimal.ZERO);
            orderInfoMapper.insertSelective(orderInfo);

            //创建运单状态子表
            createOrderState(orderInfo.getCode(), orderInfo.getCreateUser(), DictEnum.S0101.code, sendBillVO);

            //资金转移方式, 创建承运方与C端的关系，申请子账号，虚拟钱包
            // sendOrderUtil.createCarrierAndEnduserRel(sendBillVO, carDriverRelVO, sendBillVO.getCapitalTransferType(), carrier,orderInfo.getCode(), companySourceDTO);

            //TODO 更新C端用户和车辆的状态
            carDriverRelVO.setCurrentReceiptsNo(orderInfo.getOrderBusinessCode());

            try {
                ResultUtil resultUtil = appCommonAPI.batchUpdateCarEnduserStatus(carDriverRelVO);
                if(null != resultUtil && null != resultUtil.getCode() && "success".equals(resultUtil.getCode())){
                    ThirdPartyOrderVO thirdPartyOrderVO = new ThirdPartyOrderVO();
                    thirdPartyOrderVO.setOrderCode(orderInfo.getCode());
                    return ResultUtil.ok(thirdPartyOrderVO,resultUtil.getMsg());
                }
                if (null != resultUtil && null != resultUtil.getCode() && !resultUtil.getCode().equals("success")) {
                    throw new RuntimeException("修改车辆司机状态失败");
                }
            } catch (Exception e) {
                log.error("修改车辆司机状态失败", e);
                throw new RuntimeException("错误码：ZJJ001，请联系运营平台予以解决。");
            }

            return ResultUtil.ok();
        } catch (Exception e) {
            log.info("ZJJ-001:发单失败!", e);
            String message = e.getMessage();

            if (StringUtils.isNotEmpty(message) && StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("ZJJ-001:发单失败!");
            }
        }
    }

    /*
     * <AUTHOR>
     * @Description  立即发单
     * @Date 2019/10/28 15:18
     * @Param
     * @return
     **/
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil sendPlanOrder(CompanySourceDTO companySourceDTO, LineInfoPrincipalDTO receiver,
                                    TOrderInfo orderInfo, SendOrderVO sendOrderVO, LinkedHashMap carrier) {
        try {
            //编辑运单详情
            TOrderInfo tOrderInfo = editOrderInfoDetail(companySourceDTO, receiver, orderInfo, sendOrderVO);
            tOrderInfo.setId(orderInfo.getId());
            tOrderInfo.setOrderExecuteStatus(DictEnum.M020.code);
            tOrderInfo.setDeliverOrderTime(new Date());
            tOrderInfo.setOrderExecuteStatus(DictEnum.M020.code);
            tOrderInfo.setUpdateUser(CurrentUser.getUserNickname());
            tOrderInfo.setUpdateTime(new Date());
            if (null != companySourceDTO.getBusinessAssist()) {
                tOrderInfo.setBusinessAssist(companySourceDTO.getBusinessAssist());
            }

            //从企业项目与承运方关系表获取调度费系数
            TProjectCarrierInfoVO vo = new TProjectCarrierInfoVO();
            vo.setCarrierId(tOrderInfo.getCarrierId());
            vo.setCompanyId(tOrderInfo.getCompanyId());
            vo.setProjectId(tOrderInfo.getCompanyProjectId());
            TProjectCarrierRel tProjectCarrierRel = projectCarrierAPI.selectDispatchFeeCoefficient(vo);
            if (null == tProjectCarrierRel) {
                log.error("错误码：ZYH015，未获取到调度费系数");
                throw new RuntimeException("错误码：ZYH015，请联系运营平台予以解决。");
            }
            //修改运单执行状态子表，param1 调度费
            BigDecimal dispatchFee = OrderMoneyUtil.getDispatchFee(tProjectCarrierRel.getDispatchFeeCoefficient(), tOrderInfo.getEstimateTotalFee());
            List<String> orderStates = Arrays.asList(DictEnum.S0101.code);
            TOrderState orderState = orderStateMapper.selectOrderStateByOrderCode(orderStates, orderInfo.getCode());
            orderState.setParam1(dispatchFee.toString());
            orderStateMapper.updateByPrimaryKeySelective(orderState);

            CarDriverRelVO carDriverRelVO = new CarDriverRelVO();
            //TODO 新增运单车辆轨迹
            createOrderVehicleTrajectory(orderInfo.getCode(), orderInfo.getFromCoordinates(),
                    orderInfo.getFromName(), DictEnum.SENDORDERSIGN.code, carDriverRelVO, companySourceDTO);

            createState(sendOrderVO.getOrderCode(), DictEnum.S0201.code, sendOrderVO.getOperateMethod());

            //修改车辆司机状态
            TEndUserInfo tEndUserInfo = endSUserInfoAPI.selectByPrimaryKey(orderInfo.getEndDriverId());
            carDriverRelVO.setCurrentDriverAccountNo(tEndUserInfo.getPhone());
            carDriverRelVO.setRealName(sendOrderVO.getRealName());
            carDriverRelVO.setCurrentReceiptsNo(orderInfo.getOrderBusinessCode());
            carDriverRelVO.setEndcarId(orderInfo.getVehicleId());
            carDriverRelVO.setVehicleNumber(sendOrderVO.getVehicleNumber());
            carDriverRelVO.setEndDriverId(orderInfo.getEndDriverId());
            //已接单
            carDriverRelVO.setCarStatus(DictEnum.ONROADRECIEVEORDER.code);
            //已接单
            carDriverRelVO.setUserStatus(DictEnum.NOTRECIEVEORDER.code);

            //TODO 创建货源车辆司机子表
            TGoodsSourceVehicleDriverInfo goodsSourceVehicleDriverInfo = createGoodsSourceVehicleDriverInfo(orderInfo, carDriverRelVO, DictEnum.HANDSEND.code);
            orderInfo.setGoodsSourceVehicleDriverInfoCode(goodsSourceVehicleDriverInfo.getCode());

            if (null != companySourceDTO.getBlockchainPass() && companySourceDTO.getBlockchainPass()) {
                tOrderInfo.setVehicleGpsBdStatus(DictEnum.AUTOFETCHGPS.code);
            } else {
                tOrderInfo.setVehicleGpsBdStatus(DictEnum.NOFETCHGP.code);
            }

            orderInfoMapper.updateByPrimaryKey(tOrderInfo);

            // 保存司机、车辆运单统计
            sendOrderUtil.saveDriverAndCarOrderCount(carDriverRelVO);

            try {
                ResultUtil resultUtil = appCommonAPI.batchUpdateCarEnduserStatus(carDriverRelVO);
                if (null != resultUtil && null != resultUtil.getCode() && !resultUtil.getCode().equals("success")) {
                    throw new RuntimeException("修改车辆司机状态失败");
                }
            } catch (Exception e) {
                log.error("修改车辆司机状态失败", e);
                throw new RuntimeException("错误码：ZJJ001，请联系运营平台予以解决。");
            }

            Payment payment = new Payment();
            payment.Invoice(tOrderInfo);

            // 修改货源使用次数
            auditGoodsSourceUseNumber(sendOrderVO.getGoodsSourceInfoId());

            // 创建数字物流运单任务
            /*if (null != companySourceDTO.getCapitalTransferPattern() && StringUtils.isNotBlank(companySourceDTO.getCapitalTransferPattern())) {
                companySourceDTO.setVehicleNumber(carDriverRelVO.getVehicleNumber());
                companySourceDTO.setCode(goodsSourceVehicleDriverInfo.getCode());
                companySourceDTO.setCargoOrder(goodsSourceVehicleDriverInfo.getCode());
                digitlFlowUtil.createOrder(orderInfo, companySourceDTO);
            }*/

            return ResultUtil.ok();
        } catch (Exception e) {
            log.info("ZJJ-001:发单失败!", e);
            String message = e.getMessage();
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再发单。";
            }
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再发单";
            }
            if (StringUtils.isNotEmpty(message) && StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("ZJJ-001:发单失败!");
            }
        }

    }


    /*
     * <AUTHOR>
     * @Description  创建运单模板
     * @Date 2019/10/28 15:18
     * @Param
     * @return
     **/
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil createOrderTemplate(SendOrderVO record) {
        //创建运单资源信息表
        TGoodsSourceInfo goodsSourceInfo = new TGoodsSourceInfo();
        goodsSourceInfo.setPid(record.getGoodsSourceInfoId());
        goodsSourceInfo.setCompanyId(record.getCompanyId());
        goodsSourceInfo.setCode(IdWorkerUtil.getInstance().nextId());
        goodsSourceInfo.setTemplateName(record.getTemplateName());
        goodsSourceInfo.setTemplateCode(IdWorkerUtil.getInstance().nextId());
        goodsSourceInfo.setTemplateStatus(DictEnum.NEWCREATETEMPALTE.code);
        goodsSourceInfo.setOperateMethod(record.getOperateMethod());
        goodsSourceInfo.setPublisherId(CurrentUser.getCurrentUserID());
        goodsSourceInfo.setPublishTime(new Date());
        goodsSourceInfo.setPublishCompanyId(record.getCompanyId());
        ResultUtil goodsSourceInfoForTempalte = goodsSourceAPI.createGoodsSourceInfoForTempalte(goodsSourceInfo);
        if (null != goodsSourceInfoForTempalte && null != goodsSourceInfoForTempalte.getCode() && "error".equals(goodsSourceInfoForTempalte.getCode())) {
            return goodsSourceInfoForTempalte;
        }

        //创建货源车辆司机信息子表
        List<CarDriverRelVO> carDriverRelVOList = record.getCarDriverRelVOList();
        if (null != carDriverRelVOList && carDriverRelVOList.size() > 0) {
            TOrderInfo orderInfo = new TOrderInfo();
            orderInfo.setGoodSourceCode(goodsSourceInfo.getCode());
            for (CarDriverRelVO carDriverRelVO : carDriverRelVOList) {
                createGoodsSourceVehicleDriverInfo(orderInfo, carDriverRelVO, DictEnum.TEMPLATESEND.code);
            }
        }
        return ResultUtil.ok();
    }

    /*
     * <AUTHOR>
     * @Description 删除运单模板
     * @Date 2019/10/28 15:22
     * @Param
     * @return
     **/
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil deleteOrderTemplate(SendOrderVO record) {
        int i = goodsSourceVehicleDriverInfoMapper.deleteOrderTemplate(record.getGoodsSourceCode());
        TGoodsSourceInfo goodsSourceInfo = new TGoodsSourceInfo();
        goodsSourceInfo.setCode(record.getGoodsSourceCode());
        goodsSourceAPI.deleteGoodsSourceInfo(goodsSourceInfo);
        return ResultUtil.ok();
    }

    /*
     * <AUTHOR>
     * @Description 编辑运单
     * @Date 2019/11/5 9:51
     * @Param
     * @return
     **/
    @Transactional
    @Override
    public ResultUtil editOrderInfo(CompanySourceDTO companySourceDTO, LineInfoPrincipalDTO receiver,
                                    TOrderInfo orderInfo, CarDriverRelVO carDriverRelVO, SendOrderVO sendOrderVO, LinkedHashMap carrier) {
        //编辑运单详情
        TOrderInfo tOrderInfo = editOrderInfoDetail(companySourceDTO, receiver, orderInfo, sendOrderVO);

        //从企业项目与承运方关系表获取调度费系数
        TProjectCarrierInfoVO vo = new TProjectCarrierInfoVO();
        vo.setCarrierId(tOrderInfo.getCarrierId());
        vo.setCompanyId(tOrderInfo.getCompanyId());
        vo.setProjectId(tOrderInfo.getCompanyProjectId());
        TProjectCarrierRel tProjectCarrierRel = projectCarrierAPI.selectDispatchFeeCoefficient(vo);
        if (null == tProjectCarrierRel) {
            log.error("错误码：ZYH015，未获取到调度费系数");
            throw new RuntimeException("错误码：ZYH015，请联系运营平台予以解决。");
        }
        //修改运单执行状态子表，param1 调度费
        BigDecimal dispatchFee = OrderMoneyUtil.getDispatchFee(tProjectCarrierRel.getDispatchFeeCoefficient(), tOrderInfo.getEstimateTotalFee());
        List<String> orderStates = Arrays.asList(DictEnum.S0101.code);
        TOrderState orderState = orderStateMapper.selectOrderStateByOrderCode(orderStates, orderInfo.getCode());
        orderState.setParam1(dispatchFee.toString());
        orderStateMapper.updateByPrimaryKeySelective(orderState);
        //修改运单主表总费用
        tOrderInfo.setTotalFee(tOrderInfo.getEstimateTotalFee().add(dispatchFee));

        tOrderInfo.setRemark(sendOrderVO.getRemark());
        tOrderInfo.setUpdateUser(CurrentUser.getUserNickname());
        tOrderInfo.setUpdateTime(new Date());
        orderInfoMapper.updateByPrimaryKey(tOrderInfo);
        //承运方是否不一样
        //资金转移方式, 创建承运方与C端的关系，申请子账号，虚拟钱包
        /*if (!orderInfo.getCarrierId().equals(sendOrderVO.getCarrierId())) {
            sendOrderUtil.createCarrierAndEnduserRel(sendOrderVO, carDriverRelVO, sendOrderVO.getCapitalTransferType(), carrier,
                    orderInfo.getCode(), companySourceDTO);
        }*/

        return ResultUtil.ok();
    }

    /*
     * <AUTHOR>
     * @Description 编辑运单详情
     * @Date 2019/11/6 14:19
     * @Param
     * @return
     **/
    private TOrderInfo editOrderInfoDetail(CompanySourceDTO companySourceDTO, LineInfoPrincipalDTO receiver, TOrderInfo orderInfo, SendOrderVO sendOrderVO) {
        //运费单价、货源重量
        if (null != sendOrderVO.getCurrentCarriageUnitPrice()) {
            orderInfo.setCurrentCarriageUnitPrice(new BigDecimal(String.valueOf(sendOrderVO.getCurrentCarriageUnitPrice())));
        }
        if (null != sendOrderVO.getEstimateGoodsWeight()) {
            orderInfo.setPrimaryWeight(sendOrderVO.getEstimateGoodsWeight());
        }
        //运费总估计
        BigDecimal estimateTotalFee = BigDecimal.valueOf(sendOrderVO.getEstimateGoodsWeight()).multiply(BigDecimal.valueOf(sendOrderVO.getCurrentCarriageUnitPrice()));
        orderInfo.setEstimateTotalFee(estimateTotalFee.setScale(2, RoundingMode.HALF_UP));

        //承运方
        orderInfo.setCarrierId(sendOrderVO.getCarrierId());
        orderInfo.setFeeSettlementWay(companySourceDTO.getPayMethod());
        //货源信息
        if (!companySourceDTO.getCode().equals(sendOrderVO.getGoodsSourceCode())) {
            //运单资源信息表业务ID
            orderInfo.setGoodSourceCode(companySourceDTO.getCode());
            orderInfo.setLineGoodsRelId(companySourceDTO.getLineGoodsRelId());
            orderInfo.setCompanyId(companySourceDTO.getCompanyId());
            orderInfo.setCompanyProjectId(companySourceDTO.getProjectId());
            //货物相关信息
            orderInfo.setGoodsId(companySourceDTO.getGoodsId());
            orderInfo.setGoodsName(companySourceDTO.getGoodsName());
            orderInfo.setBigKindCode(companySourceDTO.getBigKindCode());
            orderInfo.setGoodsUnit(companySourceDTO.getGoodsUnit());

            //线路相关信息
            orderInfo.setLineId(companySourceDTO.getLineId());
            orderInfo.setLineCode(companySourceDTO.getLineCode());
            orderInfo.setLineName(companySourceDTO.getLineName());
            orderInfo.setLineShortName(companySourceDTO.getLineShortName());
            orderInfo.setCityFromCode(companySourceDTO.getCityFromCode());
            orderInfo.setFromName(companySourceDTO.getFromName());
            orderInfo.setFromCoordinates(companySourceDTO.getFromCoordinates());
            orderInfo.setCityEndCode(companySourceDTO.getCityEndCode());
            orderInfo.setEndName(companySourceDTO.getEndName());
            orderInfo.setEndCoordinates(companySourceDTO.getEndCoordinates());
            orderInfo.setLineType(companySourceDTO.getLineType());
            orderInfo.setBusinessAssist(null != companySourceDTO.getBusinessAssist()
                    ? companySourceDTO.getBusinessAssist() : null);
        }

        //发单员id、发单联系人、手机号(线路负责人)
        orderInfo.setDeliverOrderUserId(CurrentUser.getUserAccountId());
        orderInfo.setDeliverGoodsContacter(CurrentUser.getUserNickname());
        orderInfo.setDeliverGoodsContacterPhone(CurrentUser.getUserAccountNo());
        //收单员id、收单联系人、手机号(线路负责人)
        orderInfo.setReceiveOrderUserId(receiver.getPrincipalAccountId());
        orderInfo.setReceiveGoodsContacter(receiver.getPrincipalName());
        orderInfo.setReceiveGoodsContacterPhone(receiver.getPrincipalPhone());

        //TODO 委托方、客户名称
        orderInfo.setCompanyClient(sendOrderVO.getCompanyClient());
        orderInfo.setCompanyEntrust(sendOrderVO.getCompanyEntrust());

        //如果是经纪人模式
        if (null != companySourceDTO.getCapitalTransferPattern()
                && DictEnum.MANAGERPATTERN.code.equals(companySourceDTO.getCapitalTransferPattern())) {
            if (null == orderInfo.getAgentId()) {
                if (null != companySourceDTO.getManagerId()) {
                    orderInfo.setAgentId(companySourceDTO.getManagerId());
                }
            } else {
                if (null == companySourceDTO.getManagerId()) {
                    orderInfo.setAgentId(null);
                } else if (!orderInfo.getAgentId().equals(companySourceDTO.getManagerId())) {
                    orderInfo.setAgentId(companySourceDTO.getManagerId());
                }
            }
        } else {
            //不是经纪人模式
            orderInfo.setAgentId(null);
        }

        // 是否业务部辅助
        if (null != orderInfo.getBusinessAssist() && orderInfo.getBusinessAssist()) {
            List<CarDriverRelVO> carDriverRelVOList = sendOrderVO.getCarDriverRelVOList();
            if (null != carDriverRelVOList && null != carDriverRelVOList.get(0)) {
                CarDriverRelVO carDriverRelVO = carDriverRelVOList.get(0);
                if (null != carDriverRelVO.getEndAgentId()) {
                    orderInfo.setEndAgentId(carDriverRelVO.getEndAgentId());
                }
            }

        } else {
            orderInfo.setEndAgentId(null);
        }


        return orderInfo;
    }

    /**
     * <AUTHOR>
     * @Description 新版打包支付，原始运单支付
     * @Date 2020/3/2 8:54 上午
     * @Param
     * @return 8:54 上午
    **/
    @Transactional
    @Override
    public ResultUtil newPackPay(String code) {
        try {
            TOrderPackInfo orderPackInfo = orderPackInfoMapper.selectOrderPackByCode(code);
            if (null != orderPackInfo) {
                if (orderPackInfo.getPackStatus().equals(DictEnum.PACKED.code)
                        || orderPackInfo.getPackStatus().equals(DictEnum.PACKORDERPAIDERROR.code)
                        || orderPackInfo.getPackStatus().equals(DictEnum.PACKRECALL.code)) {
                    List<String> orderCodes = orderPackInfoMapper.selectOrderCodeByPackCode(orderPackInfo.getCode());
                    for (String orderCode : orderCodes) {
                        Payment payment = new Payment();
                        try {
                            TOrderInfo tOrderInfo = orderInfoMapper.selectOrderByCode(orderCode);
                            payment.FinancePay(tOrderInfo);
                        } catch (Exception e) {
                            log.error("ZJJ-006:打包支付失败!", e);
                            String message = e.getMessage();
                            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再支付。";
                                throw new RuntimeException(message);
                            }
                            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再支付";
                                throw new RuntimeException(message);
                            }
                            throw new RuntimeException("ZJJ-006:打包支付失败!");
                        }
                    }
                    //修改打包主表状态: 处理中
                    TOrderPackInfo packInfo = new TOrderPackInfo();
                    packInfo.setId(orderPackInfo.getId());
                    packInfo.setPackStatus(DictEnum.PACKEDHANDEL.code);
                    orderPackInfoMapper.updateByPrimaryKeySelective(packInfo);
                    //修改运单主表支付状态：支付处理中
                    HashMap hashMap = new HashMap();
                    hashMap.put("orderPayStatus", DictEnum.P070.code);
                    hashMap.put("codes", orderCodes);
                    orderInfoMapper.batchUpdateOrderPayStatus(hashMap);
                    //添加运单执行状态子表
                    createOrderState(DictEnum.SP0701.code, CurrentUser.getUserNickname(), DictEnum.COMPC.code,
                            "", orderCodes);
                } else {
                    return ResultUtil.error("运单状态错误，无法支付");
                }
            } else {
                log.error("未找到打包运单");
            }
        } catch (Exception e) {
            log.error("ZJJ-006:打包支付失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            }
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再支付。";
                throw new RuntimeException(message);
            }
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再支付";
                throw new RuntimeException(message);
            }
            throw new RuntimeException("ZJJ-006:打包支付失败!");
        }

        return ResultUtil.ok();
    }

    /*
     * <AUTHOR>
     * @Description 分润打包支付
     * @Date 2020/7/9 10:57
     * @Param
     * @return
     **/
    @Transactional
    @Override
    public ResultUtil royaltyPackPay(String code) {
        try {
            TOrderPackInfo orderPackInfo = orderPackInfoMapper.selectOrderPackByCode(code);
            if (null != orderPackInfo) {
                if (orderPackInfo.getPackStatus().equals(DictEnum.PACKED.code)
                        || orderPackInfo.getPackStatus().equals(DictEnum.PACKORDERPAIDERROR.code)
                        || orderPackInfo.getPackStatus().equals(DictEnum.PACKRECALL.code)) {
                    List<String> orderCodes = orderPackInfoMapper.selectOrderCodeByPackCode(orderPackInfo.getCode());
                    TOrderCastChanges tOrderCastChanges = null;
                    if (orderCodes.size() > 0) {
                        tOrderCastChanges = orderCastChangesMapper.selectByNewOne(orderCodes.get(0));
                    }
                    for (String orderCode : orderCodes) {
                        Payment payment = new Payment();
                        try {
                            TOrderInfo tOrderInfo = orderInfoMapper.selectOrderByCode(orderCode);
                            payment.royaltyFinancePay(tOrderInfo);
                        } catch (Exception e) {
                            log.error("ZJJ-006:打包支付失败!", e);
                            String message = e.getMessage();
                            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再支付。";
                                throw new RuntimeException(message);
                            }
                            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再支付";
                                throw new RuntimeException(message);
                            }
                            throw new RuntimeException("ZJJ-006:打包支付失败!");
                        }
                    }
                    //修改打包主表状态: 处理中
                    TOrderPackInfo packInfo = new TOrderPackInfo();
                    packInfo.setId(orderPackInfo.getId());
                    packInfo.setPackStatus(DictEnum.PACKEDHANDEL.code);
                    orderPackInfoMapper.updateByPrimaryKeySelective(packInfo);
                    //修改运单主表支付状态：支付处理中
                    HashMap hashMap = new HashMap();
                    hashMap.put("orderPayStatus", DictEnum.P070.code);
                    hashMap.put("codes", orderCodes);
                    orderInfoMapper.batchUpdateOrderPayStatus(hashMap);
                    //添加运单执行状态子表
                    createOrderState(DictEnum.SP0701.code, CurrentUser.getUserNickname(), DictEnum.COMPC.code,
                            "", orderCodes);
                    if (null != tOrderCastChanges) {
                        // 记录提现金额
                        if (DictEnum.AUTOMATION.code.equals(tOrderCastChanges.getWithdrawType())) {
                            sendOrderUtil.saveBankCardMonthlyAmountByBankId(orderPackInfo.getBankCardId(), orderPackInfo.getCode(), orderPackInfo.getAppointmentPaymentCash().subtract(orderPackInfo.getTotalSelectedOrdersServiceFee()));
                        }
                    }

                } else {
                    return ResultUtil.error("运单状态错误，无法支付");
                }
            } else {
                log.error("未找到打包运单");
            }
        } catch (Exception e) {
            log.error("ZJJ-006:打包支付失败!", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            }
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再支付。";
                throw new RuntimeException(message);
            }
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再支付";
                throw new RuntimeException(message);
            }
            throw new RuntimeException("ZJJ-006:打包支付失败!");
        }

        return ResultUtil.ok();
    }

    private ResultUtil addTaskHhcc(TOrderInfo orderInfo,TEndUserInfo tEndUserInfo,TEndCarInfo tEndCarInfo,Integer status,String type){
        TOrderHhccVo orderHhccVo = new TOrderHhccVo();
        orderHhccVo.setDriverName(tEndUserInfo.getRealName());
        orderHhccVo.setContact(tEndUserInfo.getPhone());
        orderHhccVo.setCoaType(orderInfo.getGoodsName());
        orderHhccVo.setPrimary(orderInfo.getPrimaryWeight());
        if(null == orderInfo.getDeliverWeightNotesWeight() || "".equals(orderInfo.getDeliverWeightNotesWeight())){
            orderHhccVo.setActualHair(0d);
        }else{
            orderHhccVo.setActualHair(orderInfo.getDeliverWeightNotesWeight());
        }
        if(null == orderInfo.getReceiveWeightNotesWeight() || "".equals(orderInfo.getReceiveWeightNotesWeight())){
            orderHhccVo.setNetReceipts(0d);
        }else{
            orderHhccVo.setNetReceipts(orderInfo.getReceiveWeightNotesWeight());
        }
        orderHhccVo.setDepartureDate(orderInfo.getDeliverOrderTime());
        orderHhccVo.setDays(0);
        orderHhccVo.setStatus(status);//状态（0 发单- 1 装货- 2 卸货- 3 收单- 4 已刪除）
        orderHhccVo.setOrderNumber(orderInfo.getOrderBusinessCode());
        orderHhccVo.setOrderPlate(tEndCarInfo.getVehicleNumber());

        TTask tTask = new TTask();
        String requestParameter = JSONObject.toJSONString(orderHhccVo);
        tTask.setRequestParameter(requestParameter);
        tTask.setTaskId(IdWorkerUtil.getInstance().nextId());
        tTask.setTaskType("hhcc");//三分仓储对接
        tTask.setBusinessType(type);//三分仓储对接
        tTask.setTaskTypeNode("hhcc");//三分仓储对接
        tTask.setSourceTablename("T_ORDER_INFO");
        tTask.setSourceFieldname("code");
        tTask.setSourceFieldvalue(orderHhccVo.getOrderNumber());
        tTask.setRequestTimes(0);
        tTask.setCreateTime(new Date());
        tTask.setRequestDate(new Date());
        tTask.setIsSuccessed(false);
        tTask.setEnable(false);
        ResultUtil resultUtil = tTaskAPI.select(tTask);
        if(resultUtil.getData()!=null){
            return ResultUtil.error("此运单正在三分仓储对接请稍后！");
        }else{
            tTaskAPI.add(tTask);
        }
        return  ResultUtil.ok();
    }
    /**
     * APP端 货源大厅运单列表
     * 司机端 只有司机 有权限
     * 企业端 只有收货人 有权限
     * <AUTHOR>
     * @return
     */
    @Override
    public ResultUtil selectHallOrderList(AppOrderSearchVO orderVO) throws RuntimeException {
        try {
            Map<String, Object> res = new HashMap<>();
            String userNickname = CurrentUser.getUserNickname();
            Integer accountId = CurrentUser.getUserAccountId();
            String usertype =  CurrentUser.getUsertype();
            if (accountId == null) {
                return ResultUtil.error("账号信息错误");
            }

            // 判断筛选条件
            AppOrderSearchVO judge = SearchOrderFilter.judge(orderVO);

            OrderUtil.getUserType(judge);
            SysParam isscopeday = sysParamAPI.getParamByKey("ISSCOPEDAY");
            judge.setScoreDay(Integer.valueOf(isscopeday.getParamValue()));
            // 查询运单
            Page<Object> objects = PageHelper.startPage(orderVO.getPage(), orderVO.getSize());
            List<AppOrderListDTO> appOrderList = orderInfoMapper.appSelectHallOrderList(judge);
            // 查询所有线路
            Future<List<TLineGoodsRelDTO>> line = null;
            ResultUtil goodsSourceByCompanyId = null;
            //登录人是企业管理员时，按照企业ID查询线路
            if(CurrentUser.accountIsCompanyAdmin()){
                List<Integer> userCompanyId = CurrentUser.getUserCompanyIdInteger();
                TLineGoodsRel tLineGoodsRel = new TLineGoodsRel();
                tLineGoodsRel.setCompanyId(userCompanyId.get(0));
                goodsSourceByCompanyId = lineGoodsAPI.getGoodsSourceByCompanyId(tLineGoodsRel);
            }else{
                line = CompletableFuture.supplyAsync(() -> lineGoodsAPI.selectResourceHallLineGoodsRelByAccountId(accountId));
            }
            Future<BigDecimal> sumFuture = CompletableFuture.supplyAsync(() -> orderInfoMapper.appSelectHallOrderSum(judge));

            // 线路按钮去重
            Integer finalScoreDays = judge.getScoreDay()*1440;
            Future<List<AppOrderListDTO>> listFuture = CompletableFuture.supplyAsync(() -> {
                for (AppOrderListDTO order : appOrderList) {
                    // 实时子状态,如果运单中支付状态还没有会写上，切他是支付状态就用它代替
                    if ("".equals(order.getOrderPayStatus())) {
                        if (OrderUtil.isOneContain(order.getChildStatus(), OrderUtil.getPayStatus())) {
                            order.setOrderPayStatus(order.getChildStatus());
                        }
                    }
                    // 查询运单的异常信息
                    List<Map<String, String>> errorTypes = orderAbnormalMapper.wxJudgeOrderIsError(order.getCode(), "App",userNickname);
                    if (CollectionUtils.isNotEmpty(errorTypes)) {
                        order.setErrorType(errorTypes);
                    }
                    if (order.getPackStatus().equals(1)){
                        order.setButton(new HashSet<>());
                        order.setIsScore("4");
                        continue;
                    }
                    String buttons = order.getButtons();
                    if (StringUtils.isNotEmpty(buttons)) {
                        String[] split = order.getButtons().split(",");
                        Set<String> buttonSet = Arrays.stream(split).collect(Collectors.toSet());
                        String status = "";
                        String orderPayStatus = order.getOrderPayStatus();
                        if (StringUtils.isNotEmpty(orderPayStatus) && orderPayStatus.length() > 0) {
                            status = order.getOrderPayStatus();
                        } else {
                            status = order.getOrderExecuteStatus();
                        }
                        if(status.contains("-")){
                            status = status.replace("-", "");
                        }
                        String[] code = WaybillButtonEnum.valueOf(status).getCode();
                        if("0".equals(order.getParam5())){
                            Iterator it = buttonSet.iterator();
                            while(it.hasNext()){
                                Object o = it.next();
                                if("CollOrder".equals(o)){
                                    it.remove();
                                }
                            }
                        }
                        if (code.length != 0) {
                            boolean fla = true;
                            if(DictEnum.M130.code.equals(order.getOrderPayStatus())||DictEnum.M120.code.equals(order.getOrderPayStatus())
                                    ||DictEnum.P070.code.equals(order.getOrderPayStatus())||DictEnum.P110.code.equals(order.getOrderPayStatus())
                                    ||DictEnum.M090.code.equals(order.getOrderPayStatus())
                                    ||DictEnum.S0905.code.equals(order.getPayChildStatus())){
                                if ("NODEPAYPROPORTION".equals(order.getPayMethod()) || "NODEPAYFIXED".equals(order.getPayMethod()) ){
                                    if(DictEnum.M050.code.equals(order.getOrderExecuteStatus()) || DictEnum.O060.code.equals(order.getOrderExecuteStatus())
                                            || DictEnum.M060.code.equals(order.getOrderExecuteStatus())|| DictEnum.M100.code.equals(order.getOrderExecuteStatus())){
                                        fla = false;
                                    }
                                }else{
                                    fla = false;
                                }
                            }
                            if(fla){
                                if (DictEnum.M010.code.equals(status)) {
                                    Set<Integer> lineGoodsRuleIds = judge.getSendLines();
                                    Set<String> m010But = new HashSet<>();
                                    for (Integer id : lineGoodsRuleIds) {
                                        if (id.equals(order.getLineGoodsRelId())) {
                                            for (String bs : buttonSet) {
                                                for (String ce : code) {
                                                    if (bs.equals(ce)) {
                                                        m010But.add(bs);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    order.setButton(m010But);
                                } else {
                                    Set<String> bu = new HashSet<>();
                                    for (String be : code) {
                                        for (String bs : buttonSet) {
                                            if (DictEnum.O060.code.equals(be)) {
                                                // 货运完成 并且 支付状态为空 或 支付状态为支付失败
                                                if (null == order.getOrderPayStatus() || DictEnum.M080.code.equals(order.getOrderPayStatus())) {
                                                    bu.add(bs);
                                                }
                                            } else if (be.equals(bs)) {
                                                bu.add(bs);
                                            }
                                        }
                                    }
                                    if (DictEnum.S0651.code.equals(order.getPayChildStatus())) {
                                        if (buttonSet.contains("OrderPay")) {
                                            bu.add("OrderPay");
                                        }
                                    }
                                    order.setButton(bu);
                                }
                            }
                        }
                    }
                    try {
                        ResultUtil orderFeedbackInfo = tOrderInfoService.getFeedbackOrderInfo(order.getCode());
                        if (orderFeedbackInfo.getData()  != null && !"".equals(orderFeedbackInfo.getData()) ) {
                            Map<String, Object> stringObjectMap = EntityUtils.entityToMap(orderFeedbackInfo.getData());
                            OrderFeedbackVO orderFeedbackVO = new OrderFeedbackVO();
                            orderFeedbackVO.setOrderCode(order.getCode());
                            orderFeedbackVO.setFeedbackUserEndType(usertype);
                            ResultUtil orderProblemDescription = complaintsAPI.getOrderProblemDescription(orderFeedbackVO);
                            if (orderProblemDescription.getData()!=null){
                                List<Map> mapData = (List<Map>) orderProblemDescription.getData();
                                for (Map map : mapData) {
                                    map.put("oi", stringObjectMap);
                                }
                                order.setFeedbackMap(mapData);
                            }
                        }
                    }catch (Exception e){
                        log.info("咨询投诉相关异常",e);
                    }
                    if (null==orderVO.getIsDPJ() || !orderVO.getIsDPJ() || orderVO.getPayState().length!=0 || orderVO.getWayBillState().length!=0) {
                        if (DictEnum.M130.code.equals(order.getOrderPayStatus())) {
                            if ("0".equals(order.getIsScore())) {
                                TOrderPayDetail tOrderPayDetail = orderPackInfoMapper.selectOrderDetailByOrdercode(order.getCode());
                                if (null != tOrderPayDetail) {
                                    if (null!=tOrderPayDetail.getReturnTime()){
                                        LocalDateTime returnTime = tOrderPayDetail.getReturnTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                                        LocalDateTime nowTime = LocalDateTime.now();
                                        if (Duration.between(returnTime, nowTime).toMinutes() - finalScoreDays> 0) {
                                            //大于0 超过5天
                                            order.setIsScore("2");
                                        } else {
                                            //小于0 在5天内
                                            order.setIsScore("0");
                                        }
                                    }else {
                                        order.setIsScore("6");
                                    }
                                } else {
                                    order.setIsScore("3");//找不到提现记录
                                }
                            }
                        } else {
                            if (!"1".equals(order.getIsScore())) {
                                order.setIsScore("5");
                            }
                        }
                    }
                }
                return appOrderList;
            });
            // 查询总重
            BigDecimal sum = BigDecimal.ZERO;
            try {
                sum = sumFuture.get();
            } catch (Exception e) {
                log.error("运单列表查询总数失败：{}", ThrowableUtil.getStackTrace(e));
            }
            try {
                List<AppOrderListDTO> appOrderListDTOS = listFuture.get();
                res.put("waybill", appOrderListDTOS);
            } catch (Exception e){
                log.error("运单列表获取失败：{}", e);
            }

            try {
                List<TLineGoodsRelDTO> lineGoodsRelDTOS = null;
                if(null != line){
                    lineGoodsRelDTOS = line.get();
                }else {
                    String data = JSON.toJSONString(goodsSourceByCompanyId.getData());
                    lineGoodsRelDTOS = JSON.parseArray(data, TLineGoodsRelDTO.class);
                }
                res.put("line", lineGoodsRelDTOS);
            } catch (Exception e) {
                log.error("获取员工货源失败：{}", e);
            }
            res.put("weight", OrderUtil.weight(sum));
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), res, objects.getTotal());
        } catch (RuntimeException e) {
            log.error("货源大厅运单查询出错查询参数：{}, 错误信息: {}", orderVO.toString() ,e);
            throw new RuntimeException("查询货源大厅运单失败");
        }
    }
}
