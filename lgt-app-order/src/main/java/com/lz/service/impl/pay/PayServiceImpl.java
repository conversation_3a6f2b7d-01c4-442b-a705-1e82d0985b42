package com.lz.service.impl.pay;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.lz.api.MqAPI;
import com.lz.common.config.HXPropertiesConfig;
import com.lz.common.constants.MqTagConstants;
import com.lz.common.constants.MqTopicConstants;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.HXTradeTypeEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.MQMessage;
import com.lz.common.util.DateUtils;
import com.lz.common.util.ResultUtil;
import com.lz.dao.*;
import com.lz.dto.OpenRoleStatusListDTO;
import com.lz.dto.OpenRoleWalletListDTO;
import com.lz.model.*;
import com.lz.payment.TradeType;
import com.lz.payment.PaymentUtil;
import com.lz.service.pay.PayService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.util.OrderDeductionUtil;
import com.lz.vo.pay.PayMessageBody;
import commonSdk.requestModel.CustomerBalancePayRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Service
public class PayServiceImpl implements PayService {

    @Resource
    private THXOrderInfoMapper hxOrderInfoMapper;

    @Resource
    private TOrderStateMapper orderStateMapper;

    @Resource
    private TOrderCastChangesMapper tOrderCastChangesMapper;

    @Resource
    private TOrderPayRequestDetailMapper orderPayRequestDetailMapper;

    @Resource
    private TOrderInfoDetailMapper orderInfoDetailMapper;

    @Resource
    private TOrderInsuranceMapper orderInsuranceMapper;

    @Resource
    private PaymentUtil paymentUtil;

    @Resource
    private HXPropertiesConfig hxPropertiesConfig;

    @Resource
    private MqAPI mqAPI;

    @Resource
    private SysParamAPI sysParamAPI;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil carrierPay(PayMessageBody messageBody, OpenRoleWalletListDTO walletListDTO, OpenRoleStatusListDTO openRoleStatusListDTO) {
        TOrderInfo tOrderInfo = hxOrderInfoMapper.selectByPrimaryKey(messageBody.getOrderId());
        if (DictEnum.P070.code.equals(tOrderInfo.getOrderPayStatus()) || DictEnum.M090.code.equals(tOrderInfo.getOrderPayStatus())) {
            log.info("运单支付状态, {}, 不再处理", tOrderInfo.getOrderPayStatus());
            return ResultUtil.ok();
        }
        TOrderCastChanges cc = tOrderCastChangesMapper.selectByNewOne(tOrderInfo.getCode());

        // 修改运单主表支付状态
        updateOrderInfo(messageBody.getOrderId(), messageBody.getCreateUser());
        // 添加运单执行状态：支付处理中P070
        insertOrderState(tOrderInfo.getCode(), messageBody.getOperatorId(), messageBody.getCreateUser());

        TOrderCastChanges castChanges = new TOrderCastChanges();
        BeanUtils.copyProperties(cc, castChanges);
        castChanges.setId(null);
        castChanges.setCode(IdWorkerUtil.getInstance(RandomUtil.randomInt(32)).nextId());
        castChanges.setUserOper(TradeType.PayMent.code);
        castChanges.setTradeType(TradeType.RZ.code);
        // 修改钱包id
        castChanges.setCarrierWalletId(walletListDTO.getCarrierWallet().getId());
        castChanges.setCompanyWalletId(walletListDTO.getCompanyWallet().getId());
        castChanges.setEndDriverWalletId(walletListDTO.getDriverWallet().getId());
        castChanges.setCreateUser(messageBody.getCreateUser());
        castChanges.setCreateTime(new Date());
        castChanges.setUpdateTime(new Date());
        castChanges.setServiceFee(tOrderInfo.getUserConfirmServiceFee());
        // 添加最新资金变动
        tOrderCastChangesMapper.insertSelective(castChanges);
        cc.setDataEnable(false);
        // 将上一条资金变动置为无效
        tOrderCastChangesMapper.updateByPrimaryKeySelective(cc);

        // 添加支付主表
        TOrderPayInfo orderPayInfo = paymentUtil.createOrderPayInfo(tOrderInfo, messageBody.getCreateUser());
        // 插入支付字表
        String orderPayDetailCode = paymentUtil.createOrderPayDetail(orderPayInfo.getCode(),
                castChanges.getCode(), HXTradeTypeEnum.HX_CABALANCE_PAY.code, TradeType.RZ.code, messageBody.getOperatorId(), messageBody.getCreateUser());

        // 构造支付请求参数
        CustomerBalancePayRequest req = new CustomerBalancePayRequest();
        req.setPartnerId(hxPropertiesConfig.getPartnerId());
        req.setRequestId(IdWorkerUtil.getInstance(RandomUtil.randomInt(32)).nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setChannelId(hxPropertiesConfig.getChannelId());
        req.setBizOrderNo(orderPayDetailCode);
        req.setOutPartnerAccId(openRoleStatusListDTO.getCarrierStatus().getPartnerAccId());
        req.setInPartnerAccId(openRoleStatusListDTO.getDriverStatus().getPartnerAccId());
        TOrderInfoDetail tOrderInfoDetail = orderInfoDetailMapper.selectByOrderId(tOrderInfo.getId());
        BigDecimal orderAmount = tOrderInfo.getUserConfirmPaymentAmount();
        BigDecimal deduction = BigDecimal.ZERO;
        if (null != tOrderInfoDetail && tOrderInfoDetail.getIllegalCheck() && tOrderInfoDetail.getIllegalOrder()
                && tOrderInfo.getUserConfirmPaymentAmount().compareTo(tOrderInfoDetail.getDeduction()) > 0
                && tOrderInfoDetail.getDeduction().compareTo(BigDecimal.ZERO) > 0) {
            //修改承运方钱包, 将入账中减违规扣款；余额加违规扣款
            deduction = tOrderInfoDetail.getDeduction();
            paymentUtil.modifyCarrierEntryAmount(castChanges.getCarrierWalletId(), tOrderInfoDetail.getDeduction(), messageBody.getCreateUser());
        }
        TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(tOrderInfo.getOrderBusinessCode());
        if (null != tOrderInsurance && OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance)) {
            deduction = deduction.add(tOrderInsurance.getInsuredAmount());
        }
        if (deduction.compareTo(BigDecimal.ZERO) > 0) {
            //修改承运方钱包, 将入账中减违规扣款；余额加违规扣款；保险扣款
            paymentUtil.modifyCarrierEntryAmount(castChanges.getCarrierWalletId(), deduction, messageBody.getCreateUser());
        }
        orderAmount = orderAmount.subtract(deduction);
        req.setOrderAmount(orderAmount);
        req.setTradeAbstract(String.join(tOrderInfo.getSettledWeight().toPlainString(), tOrderInfo.getGoodsName(), DictEnum.DUN.code));
        // 查询回调地址
        SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.PAYCALLBACKURL.code);
        req.setNotifyUrl(paramByKey.getParamValue());

        String topic = MqTopicConstants.CARRIER_PAY;
        String tag = MqTagConstants.CARRIER_DRIVER;
        MQMessage mqMessage = new MQMessage();
        mqMessage.setTopic(topic);
        mqMessage.setTag(tag);
        mqMessage.setKey(tOrderInfo.getCode());
        mqMessage.setBody(req);
        log.info("发送MQ消息内容, {}", JSONUtil.toJsonStr(mqMessage));
        ResultUtil resultUtil = mqAPI.sendMessage(mqMessage);
        log.info("发送MQ消息结果，{}", JSONUtil.toJsonStr(resultUtil));
        if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
            throw new RuntimeException("发送MQ消息失败");
        }
        TOrderPayRequestDetail orderPayRequestDetail = new TOrderPayRequestDetail();
        orderPayRequestDetail.setId(messageBody.getOrderPayRequestDetailId());
        orderPayRequestDetail.setOrderPayDetailCode(orderPayDetailCode);
        orderPayRequestDetail.setStatus(1);
        orderPayRequestDetail.setUpdateTime(new Date());
        orderPayRequestDetailMapper.updateByPrimaryKeySelective(orderPayRequestDetail);
        return ResultUtil.ok();
    }

    /**
     * 修改运单主表
     * @param orderId
     * @param createUser
     */
    private void updateOrderInfo(Integer orderId, String createUser) {
        TOrderInfo orderInfo = new TOrderInfo();
        orderInfo.setId(orderId);
        orderInfo.setOrderPayStatus(DictEnum.P070.code);
        orderInfo.setUpdateUser(createUser);
        orderInfo.setUpdateTime(new Date());
        hxOrderInfoMapper.updateByPrimaryKeySelective(orderInfo);
    }

    /**
     * @description 新增运单执行状态子表
     */
    private void insertOrderState(String code, Integer operatorId, String createUser) {
        TOrderState orderState = new TOrderState();
        orderState.setCode(IdWorkerUtil.getInstance(RandomUtil.randomInt(32)).nextId());
        orderState.setOrderCode(code);
        orderState.setStateNodeValue(DictEnum.SP0701.code);
        orderState.setOperateTime(new Date());
        //TODO 操作方式
        orderState.setOperatorId(operatorId);
        orderState.setEnable(false);
        orderState.setIfExpire(false);
        orderState.setCreateUser(createUser);
        orderStateMapper.insertSelective(orderState);
    }

}
