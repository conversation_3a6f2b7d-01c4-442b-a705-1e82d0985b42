package com.lz.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.model.CodeEnum;
import com.lz.common.model.UserRoleDTO;
import com.lz.common.util.*;
import com.lz.dao.*;
import com.lz.dto.*;
import com.lz.model.TOrderPayRule;
import com.lz.payment.TradeType;
import com.lz.service.TOrderInfoSearchService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.util.OrderUtil;
import com.lz.util.reportData.util.StringUtil;
import com.lz.vo.AppOrderSearchVO;
import com.lz.vo.TAdvanceOrderTmpVo;
import com.lz.vo.TOrderPayRuleVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;

@Slf4j
@Service
public class TOrderInfoSearchServiceImpl implements TOrderInfoSearchService {

    @Resource
    private TOrderInfoMapper tOrderInfoMapper;

    @Resource
    private TOrderInfoSearchMapper orderInfoSearchMapper;

    @Resource
    private TOrderInfoSearchWeightMapper orderInfoSearchWeightMapper;

    @Resource
    private TOrderPayRuleMapper tOrderPayRuleMapper;

    @Resource
    private TOrderStateMapper orderStateMapper;

    @Resource
    private TAdvanceOrderTmpMapper advanceOrderTmpMapper;

    @Resource
    private SysParamAPI sysParamAPI;

    /**
    * @Description 运单跟踪查询
    * <AUTHOR>
    * @Date   2020/3/24 9:08
    * @Param
    * @Return
    * @Exception
    *
    */
    @Override
    public ResultUtil selectByYdgzPage(AppOrderSearchVO search) throws RuntimeException {
        try{
            Map<String,Object> resp = new HashMap<>();
            Page<Object> obja = PageHelper.startPage(search.getPage(), search.getSize());
            if(!search.getOrderWeight()){
                List<TOrderInfoDTO> tOrderInfos = orderInfoSearchMapper.selectByYdgzPage(search);
                for (TOrderInfoDTO tOrderInfo : tOrderInfos) {
                    if(null != tOrderInfo.getCarriageFee() && tOrderInfo.getCarriageFee().compareTo(BigDecimal.ZERO) >= 0 &&
                            null != tOrderInfo.getInsuredAmount() && tOrderInfo.getInsuredAmount().compareTo(BigDecimal.ZERO) >= 0){
                        tOrderInfo.setCarriageFee(tOrderInfo.getCarriageFee().subtract(tOrderInfo.getInsuredAmount()).setScale(2, RoundingMode.HALF_UP));
                    }
                }
                resp.put("waybill", tOrderInfos);
            }
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), resp, obja.getTotal());
        }catch (Exception e){
            log.error("运单跟踪查询失败",e);
            throw new RuntimeException("运单跟踪查询失败");
        }
    }
    /**
     *  @author: dingweibo
     *  @Date: 2022/6/7 10:50
     *  @Description: 查询运单跟踪合计
     */
    @Override
    public ResultUtil selectByYdgzSum(AppOrderSearchVO search) {
        OrderUtil.getUserType(search);
        // 查询运单总重量
        if(search.getOrderWeight()){
            SelectedOrderPrimaryWeightDTO selectedOrderPrimaryWeightDTO = orderInfoSearchWeightMapper.selectByYdgzSum(search);
            if(null != selectedOrderPrimaryWeightDTO){
                if(selectedOrderPrimaryWeightDTO.getRulePaymentAmount().compareTo(BigDecimal.ZERO) >= 0 &&
                        selectedOrderPrimaryWeightDTO.getInsuredAmountSum().compareTo(BigDecimal.ZERO) >= 0){
                    selectedOrderPrimaryWeightDTO.setRulePaymentAmount(selectedOrderPrimaryWeightDTO.getRulePaymentAmount().
                            subtract(selectedOrderPrimaryWeightDTO.getInsuredAmountSum()).setScale(2, RoundingMode.HALF_UP));
                }
            }
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), selectedOrderPrimaryWeightDTO);
        }
        return ResultUtil.ok();
    }
    /**
    * @Description 运单检查
    * <AUTHOR>
    * @Date   2020/3/26 19:14
    * @Param
    * @Return
    * @Exception
    *
    */
    @Override
    public ResultUtil selectByYdjcPage(AppOrderSearchVO search) throws RuntimeException {
        try {
            Map<String, Object> resp = new HashMap<>();
            Page<Object> obja = PageHelper.startPage(search.getPage(), search.getSize());
            List<TOrderInfoDTO> tOrderInfos = orderInfoSearchMapper.selectByYdjcPage(search);
            resp.put("waybill", tOrderInfos);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), resp, obja.getTotal());
        } catch (Exception e) {
            log.error("运单检查查询失败", e);
            throw new RuntimeException("运单检查查询失败");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2020/3/26 19:14
     *  @Description: 已支付实体运单
     */
    @Override
    public ResultUtil selectByYzfstydPage(AppOrderSearchVO search) throws RuntimeException {
        try {
            Map<String, Object> resp = new HashMap<>();
            Page<Object> obja = PageHelper.startPage(search.getPage(), search.getSize());
            List<TOrderInfoDTO> tOrderInfos = orderInfoSearchMapper.selectOrderByUserType(search);
            if (CurrentUser.accountIsCompanyAdmin()) {
                for (TOrderInfoDTO tOrderInfo : tOrderInfos) {
                    tOrderInfo.setOrderPayPms(true);
                    tOrderInfo.setOrderReceiverPms(true);
                    tOrderInfo.setPayReviewPms(true);
                }
            }
            resp.put("waybill", tOrderInfos);
            // 查询运单总重量
            // Map<String, Object> orderAllPrimaryWeigh = tOrderInfoMapper.selectByYzfstydSum(search);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), resp, obja.getTotal());
        } catch (Exception e) {
            log.error("运单检查查询失败， {}", ThrowableUtil.getStackTrace(e));
            throw new RuntimeException("运单检查查询失败");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2020/8/31 15:48
     *  @Description: 运单审核
     */
    @Override
    public ResultUtil selectByYdshPage(AppOrderSearchVO search) throws RuntimeException {
        try{
            if(null != search.getTimeOfPayment() && search.getTimeOfPayment().length == 2){
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Instant instant = Instant.parse(search.getTimeOfPayment()[0]);
                ZonedDateTime zdt = instant.atZone(ZoneId.of("UTC"));
                Date from = Date.from(zdt.toInstant());
                search.setTimeOfPaymentStart(format.format(from));
                Instant instant1 = Instant.parse(search.getTimeOfPayment()[1]);
                ZonedDateTime zdt1 = instant1.atZone(ZoneId.of("UTC"));
                Date from1 = Date.from(zdt1.toInstant());
                search.setTimeOfPaymentEnd(format.format(from1));
            }
            Map<String, Object> resp = new HashMap<>();
            Page<Object> obja = PageHelper.startPage(search.getPage(), search.getSize());
            List<TOrderInfoDTO> tOrderInfos = orderInfoSearchMapper.selectByYdshPage(search);
            resp.put("waybill", tOrderInfos);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), resp, obja.getTotal());
        } catch (Exception e) {
            log.error("运单检查查询失败", e);
            throw new RuntimeException("运单检查查询失败");
        }
    }

    /**
     * 违规运单
     */
    @Override
    public ResultUtil selectByWgydPage(AppOrderSearchVO search) throws RuntimeException {
        try{
            Map<String, Object> resp = new HashMap<>();
            Page<Object> obja = PageHelper.startPage(search.getPage(), search.getSize());
            List<TOrderInfoDTO> tOrderInfos = orderInfoSearchMapper.selectByWgydPage(search);
            for (TOrderInfoDTO tOrderInfo : tOrderInfos) {
                //当结算金额不为0时，计算司机收款金额
                if(null != tOrderInfo.getCarriageFee() &&
                tOrderInfo.getCarriageFee().compareTo(BigDecimal.ZERO) > 0){
                    // 结算金额-违规扣款金额=司机收款金额
                    if(null != tOrderInfo.getDeduction()){
                        tOrderInfo.setDriverProceeds(tOrderInfo.getCarriageFee().subtract(tOrderInfo.getDeduction()));
                    }
                }
            }
            resp.put("waybill", tOrderInfos);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), resp, obja.getTotal());
        } catch (Exception e) {
            log.error("违规运单查询失败", e);
            throw new RuntimeException("违规运单查询失败");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2020/11/16 9:13
     *  @Description: 节点支付查询列表
     */
    @Override
    public ResultUtil selectNodeByPage(AppOrderSearchVO search) {
        try {
            Map<String, Object> resp = new HashMap<>();
            List<String> orderIndexCount = tOrderInfoMapper.getOrderIndexCount();
            search.setOrderIndex(orderIndexCount);
            OrderUtil.getUserType(search);
            Page<Object> obja = PageHelper.startPage(search.getPage(), search.getSize());
            List<TOrderPayRuleVo> tOrderPayRuleVoList = tOrderPayRuleMapper.selectNodeByPage(search);
            tOrderPayRuleVoList.forEach(tOrderPayRuleVo -> {
                tOrderPayRuleVo.setIsShowPayButton(false);
                List<AppOrderProcessDTO> appOrderProcess = orderStateMapper.appOrderProcess(tOrderPayRuleVo.getOrderCode(), true);
                CollectOrderInfoDTO collectOrderInfo = tOrderInfoMapper.selectPayOrderDetail(tOrderPayRuleVo.getOrderCode(), CurrentUser.getUserAccountId());
                BigDecimal userConfirmCarriagePayment = collectOrderInfo.getUserConfirmCarriagePayment();
                BigDecimal sumFee = tOrderPayRuleMapper.selectSumFee(tOrderPayRuleVo.getOrderCode());
                BigDecimal subtract = userConfirmCarriagePayment.subtract(sumFee);
                tOrderPayRuleVo.setWkRulePaymentFee(subtract);
                List<String> resulttype = new ArrayList<>();
                TOrderPayRule re = new TOrderPayRule();
                re.setOrderCode(tOrderPayRuleVo.getOrderCode());
                re.setPayNodeType(TradeType.WKPAYNODE.code);
                TOrderPayRule tOrderPayRule = tOrderPayRuleMapper.selectByOrderCodeAndType(re);
                appOrderProcess.forEach(appOrderProcessDTO -> {
                    resulttype.add(appOrderProcessDTO.getStateNodeValue());
                });
                appOrderProcess.forEach(appOrderProcessDTO -> {
                    if(resulttype.contains(DictEnum.M030.code) && ("PACKUNPAID".equals(tOrderPayRuleVo.getZhPayStatus()) || "PACKORDERPAIDERROR".equals(tOrderPayRuleVo.getZhPayStatus()))){
                        tOrderPayRuleVo.setIsShowPayButton(true);
                        tOrderPayRuleVo.setPayNodeType(DictEnum.ZHPAYNODE.code);
                    }
                    if(resulttype.contains(DictEnum.M040.code)  && ("PACKUNPAID".equals(tOrderPayRuleVo.getXhPayStatus()) || "PACKORDERPAIDERROR".equals(tOrderPayRuleVo.getXhPayStatus()))){
                        tOrderPayRuleVo.setIsShowPayButton(true);
                        tOrderPayRuleVo.setPayNodeType(DictEnum.XHPAYNODE.code);
                    }
                    if(resulttype.contains(DictEnum.M050.code)  && ("PACKUNPAID".equals(tOrderPayRuleVo.getSdPayStatus()) || "PACKORDERPAIDERROR".equals(tOrderPayRuleVo.getSdPayStatus()))){
                        tOrderPayRuleVo.setIsShowPayButton(true);
                        tOrderPayRuleVo.setPayNodeType(DictEnum.SDPAYNODE.code);
                    }
                    if (null!=tOrderPayRule && null!=tOrderPayRule.getRulePaymentFee() && !"0".equals(tOrderPayRule.getRulePaymentFee())){
                        if(resulttype.contains(DictEnum.M050.code) && ("PACKUNPAID".equals(tOrderPayRuleVo.getWkPayStatus()) || "PACKORDERPAIDERROR".equals(tOrderPayRuleVo.getWkPayStatus()))){
                            tOrderPayRuleVo.setIsShowPayButton(true);
                            tOrderPayRuleVo.setPayNodeType(DictEnum.WKPAYNODE.code);
                        }
                    }
                });

                // 判断是否是第一次支付
                tOrderPayRuleVo.setFirstPay(false);
                List<TOrderPayRuleNodeTypeSortDTO> tOrderPayRules = tOrderPayRuleMapper.selectOrderPayRuleByOrderCode(tOrderPayRuleVo.getOrderCode());
                if (null != tOrderPayRules && !tOrderPayRules.isEmpty()) {
                    TOrderPayRuleNodeTypeSortDTO ruleNodeTypeSortDTO = tOrderPayRules.get(0);
                    if (null != ruleNodeTypeSortDTO.getPayNodeType()
                            && null != ruleNodeTypeSortDTO.getPayStatus()
                            && null != ruleNodeTypeSortDTO.getSort()) {
                        if (DictEnum.PACKUNPAID.code.equals(ruleNodeTypeSortDTO.getPayStatus())) {
                            tOrderPayRuleVo.setFirstPay(true);
                        }
                    }
                }
            });

            resp.put("waybill", tOrderPayRuleVoList);
            // 查询运单运费
            SelectNodeOrderSumDTO selectNodeOrderSumDTO = tOrderPayRuleMapper.selectNodeSum(search);
            resp.put("weight", selectNodeOrderSumDTO);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), resp, obja.getTotal());
        } catch (Exception e) {
            log.error("节点支付运单查询失败", e);
            throw new RuntimeException("节点支付运单查询失败");
        }
    }
    @Override
    public List<TOrderPayRuleVo> selectNodeBySearch(AppOrderSearchVO search) {
        try {
            List<String> orderIndexCount = tOrderInfoMapper.getOrderIndexCount();
            search.setOrderIndex(orderIndexCount);
            OrderUtil.getUserType(search);
            List<TOrderPayRuleVo> tOrderPayRuleVoList = tOrderPayRuleMapper.selectNodeByPage(search);
            return tOrderPayRuleVoList;
        } catch (Exception e) {
            log.error("节点支付运单查询失败", e);
            throw new RuntimeException("节点支付运单查询失败");
        }
    }

    @Override
    public ResultUtil getOrderAllPrimaryWeight(AppOrderSearchVO search) {
        try {
            OrderUtil.getUserType(search);
            if ("orderUnqualified".equalsIgnoreCase(search.getState())) {
                search.setOrderUnqualified(true);
                search.setUnqualifiedOrder(true);
            }
            SelectedOrderPrimaryWeightDTO orderAllPrimaryWeight = orderInfoSearchWeightMapper.getOrderAllPrimaryWeight(search);
            return ResultUtil.ok(orderAllPrimaryWeight);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询运单合计失败, {}", ThrowableUtil.getStackTrace(e));
        }
        return ResultUtil.ok();
    }

    @Override
    public ResultUtil yfkTotalData(TAdvanceOrderTmpDto search) {
        try {
            if (null != search.getCreateTime()) {
                search.setStartTime(search.getCreateTime()[0]);
                search.setEndTime(search.getCreateTime()[1]);
            }
            if (null != search.getFhTime()) {
                search.setFhStartTime(search.getFhTime()[0]);
                search.setFhEndTime(search.getFhTime()[1]);
            }
            if (null != search.getShTime()) {
                search.setShStartTime(search.getShTime()[0]);
                search.setShEndTime(search.getShTime()[1]);
            }
            if (null != search.getFhbdTime()) {
                search.setFhbdStartTime(search.getFhbdTime()[0]);
                search.setFhbdEndTime(search.getFhbdTime()[1]);
            }
            if (null != search.getShbdTime()) {
                search.setShbdStartTime(search.getShbdTime()[0]);
                search.setShbdEndTime(search.getShbdTime()[1]);
            }
            if (null != search.getFinalTime()) {
                search.setFinalPayTime(search.getFinalTime()[0]);
                search.setFinalEndTime(search.getFinalTime()[1]);
            }

            if (null != search.getCompanyId()) {
                List<String> userCompanyId = new ArrayList<>();
                userCompanyId.add(String.valueOf(search.getCompanyId()));
                search.setCompanyIdList(userCompanyId);
            } else {
                List<String> userCompanyId = CurrentUser.getUserCompanyId();
                if (null != userCompanyId && !userCompanyId.isEmpty()) {
                    search.setCompanyIdList(userCompanyId);
                }
            }
            BigDecimal advanceFeeHj = new BigDecimal(0);
            BigDecimal advanceDispatchFeeHj = new BigDecimal(0);
            BigDecimal surplusFeeHj = new BigDecimal(0);
            BigDecimal surplusDispatchFeeHj = new BigDecimal(0);

            List<TAdvanceOrderTmpVo> listHj = advanceOrderTmpMapper.selectByPage(search);
            for (TAdvanceOrderTmpVo hj : listHj) {
                //实际运费 如果用户确认运费为空去规则运费
                if (hj.getCarriageFee() == null || hj.getCarriageFee().compareTo(BigDecimal.ZERO) == 0) {
                    hj.setCarriageFee(hj.getRulePaymentAmount());
                    //实际运费为0时， 实际调度费也为0
                    if (hj.getCarriageFee() == null || hj.getCarriageFee().compareTo(BigDecimal.ZERO) == 0) {
                        hj.setDispatchFee(BigDecimal.ZERO);
                    }
                }
                //剩余运费 调度费   实际为空时取预估
                if (!DictEnum.M100.code.equals(hj.getOrderExecuteStatus())) {
                    if (hj.getCarriageFee() != null && !(hj.getCarriageFee().compareTo(BigDecimal.ZERO) == 0)) {
                        hj.setSurplusFee(hj.getCarriageFee().subtract(hj.getAdvanceFee()));
                        hj.setSurplusDispatchFee(hj.getDispatchFee().subtract(hj.getAdvanceDispatchFee()));
                    } else {
                        if (hj.getEstimateTotalFee() != null && !(hj.getEstimateTotalFee().compareTo(BigDecimal.ZERO) == 0)) {
                            hj.setSurplusFee(hj.getEstimateTotalFee().subtract(hj.getAdvanceFee()));
                            hj.setSurplusDispatchFee(hj.getEstimateDispatchFee().subtract(hj.getAdvanceDispatchFee()));
                        } else {
                            hj.setSurplusFee(BigDecimal.ZERO);
                            hj.setSurplusDispatchFee(BigDecimal.ZERO);
                        }
                    }
                } else {
                    hj.setSurplusFee(BigDecimal.ZERO);
                    hj.setSurplusDispatchFee(BigDecimal.ZERO);
                }
                //如果剩余运费小于0 为0
                if (hj.getSurplusFee().compareTo(BigDecimal.ZERO) < 0) {
                    hj.setSurplusFee(BigDecimal.ZERO);
                }
                //如果剩余调度费小于0 为0
                if (hj.getSurplusDispatchFee().compareTo(BigDecimal.ZERO) < 0) {
                    hj.setSurplusDispatchFee(BigDecimal.ZERO);
                }
                if (hj.getAdvanceFee() != null) {
                    advanceFeeHj = advanceFeeHj.add(hj.getAdvanceFee());
                }
                if (hj.getAdvanceDispatchFee() != null) {
                    advanceDispatchFeeHj = advanceDispatchFeeHj.add(hj.getAdvanceDispatchFee());
                }
                if (hj.getSurplusFee() != null) {
                    surplusFeeHj = surplusFeeHj.add(hj.getSurplusFee());
                }
                if (hj.getSurplusDispatchFee() != null) {
                    surplusDispatchFeeHj = surplusDispatchFeeHj.add(hj.getSurplusDispatchFee());
                }
            }

            SelectAdvanceOrderSumDTO selectAdvanceOrderSumDTO = new SelectAdvanceOrderSumDTO();
            selectAdvanceOrderSumDTO.setAdvanceFeeHj(advanceFeeHj);
            selectAdvanceOrderSumDTO.setAdvanceDispatchFeeHj(advanceDispatchFeeHj);
            selectAdvanceOrderSumDTO.setSurplusFeeHj(surplusFeeHj);
            selectAdvanceOrderSumDTO.setSurplusDispatchFeeHj(surplusDispatchFeeHj);
            return ResultUtil.ok(selectAdvanceOrderSumDTO);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询预付款合计失败, {}", ThrowableUtil.getStackTrace(e));
        }
        return ResultUtil.ok();
    }

    @Override
    public ResultUtil selectOrderByUserType(AppOrderSearchVO search) throws RuntimeException {
        Map<String, Object> resp = new HashMap<>();
        try {
            OrderUtil.getUserType(search);
            // 如果查不出运单就不去查重量
            int orderSize = 0;
            if ("GZYD".equals(search.getState())) {
                // 运单跟踪
                OrderUtil.levelSort(search);
                return selectByYdgzPage(search);
            } else if ("WAYBILL".equals(search.getState())) {
                // 运单检查，实体运单检查、运单收单
                OrderUtil.levelSort(search);
                search.setOrderUnqualified(true);
                search.setUnqualifiedOrder(false);
                return selectByYdjcPage(search);
            } else if ("PAID".equals(search.getState())) {
                // 已支付实体运单
                OrderUtil.levelSort(search);
                return selectByYzfstydPage(search);
            } else if ("JUDGE".equals(search.getState())) {
                // 运单审核
                OrderUtil.levelSort(search);
                return selectByYdshPage(search);
            }else if ("WGYD".equals(search.getState())) {
                // 违规运单
                OrderUtil.levelSort(search);
                return selectByWgydPage(search);
            }  else {
                OrderUtil.levelSort(search);
                // 不合格运单、经纪人
                if ("orderUnqualified".equalsIgnoreCase(search.getState()) || "AGENT".equals(search.getState())) {
                    /*// 运单跟踪页面默认按角色查询审核通过的运单
                    if ("GZYD".equals(search.getState())) {
                        search.setAuditPass(audit());
                    }*/
                    if ("orderUnqualified".equalsIgnoreCase(search.getState())) {
                        search.setOrderUnqualified(true);
                        search.setUnqualifiedOrder(true);
                    }
                    //如果为经纪人 AGENT  获取当前登录经纪人id 查询当前运单
                    if ("AGENT".equals(search.getState())) {
                        search.setAuditPass(audit());
                        search.setAgentId(CurrentUser.getEndUserId());
                        search.setAgentWalletType(true);
                    }

                    Page<Object> obja = PageHelper.startPage(search.getPage(), search.getSize());
                    List<TOrderInfoDTO> tOrderInfos = orderInfoSearchMapper.getGzorderList(search);
                    resp.put("waybill", tOrderInfos);
                    return new ResultUtil(CodeEnum.SUCCESS.getCode(), resp, obja.getTotal());
                } else {
                    // 运单收单、实体运单支付、驳回运单记录
                    if ("YDSH".equals(search.getState())) {
                        SysParam ordereditpmsmark = sysParamAPI.getParamByKey("ORDEREDITPMSMARK");
                        ArrayList<String> companyIds = new ArrayList<>();
                        if (null != ordereditpmsmark && null != ordereditpmsmark.getParamValue()) {
                            String ordereditpmsmarkParamValue = ordereditpmsmark.getParamValue();
                            if (StringUtils.isNotBlank(ordereditpmsmarkParamValue)) {
                                String[] companys = ordereditpmsmarkParamValue.split(",");
                                companyIds.addAll(Arrays.asList(companys));
                            }
                        }
                        search.setOrderUnqualified(false);
                        if (!companyIds.isEmpty()) {
                            List<String> userCompanyId = CurrentUser.getUserCompanyId();
                            if (null != userCompanyId && !userCompanyId.isEmpty()) {
                                companyIds.forEach(id -> {
                                    if (userCompanyId.contains(id)) {
                                        search.setOrderUnqualified(true);
                                    }
                                });
                            }

                        }

                    }

                    Page<Object> objb = PageHelper.startPage(search.getPage(), search.getSize());
                    // 查询运单
                    if (!search.isSearch()) {
                        List<TOrderInfoDTO> tOrderInfos = orderInfoSearchMapper.selectOrderByUserType(search);
                        orderSize = tOrderInfos.size();

                        if (CurrentUser.accountIsCompanyAdmin()) {
                            for (TOrderInfoDTO tOrderInfo : tOrderInfos) {
                                tOrderInfo.setOrderPayPms(true);
                                tOrderInfo.setOrderReceiverPms(true);
                                tOrderInfo.setPayReviewPms(true);
                            }
                        } else {
                            // 运单支付页面判断哪些运单是可以支付的
                            if ("PAY".equals(search.getState()) && (null != search.getPayLine() || null != search.getPayReviewLine())) {
                                Set<Integer> payLine = search.getPayLine();
                                Set<Integer> payReviewLine = search.getPayReviewLine();
                                for (TOrderInfoDTO tOrderInfo : tOrderInfos) {
                                    for (Integer pl : payLine) {
                                        if (pl.equals(tOrderInfo.getLineGoodsRelId())) {
                                            tOrderInfo.setOrderPayPms(true);
                                        }
                                    }
                                    for (Integer rl : payReviewLine) {
                                        if (rl.equals(tOrderInfo.getLineGoodsRelId())) {
                                            tOrderInfo.setPayReviewPms(true);
                                        }
                                    }
                                }
                            } else if ("YDSH".equals(search.getState()) && null != search.getReceiverLine()) {
                                // 收单权限
                                Set<Integer> receiverLine = search.getReceiverLine();
                                for (TOrderInfoDTO tOrderInfo : tOrderInfos) {
                                    for (Integer rl : receiverLine) {
                                        if (rl.equals(tOrderInfo.getLineGoodsRelId())) {
                                            tOrderInfo.setOrderReceiverPms(true);
                                        }
                                    }
                                }
                            }
                        }
                        resp.put("waybill", tOrderInfos);
                    }

                    return new ResultUtil(CodeEnum.SUCCESS.getCode(), resp, objb.getTotal());
                }
            }
        } catch (RuntimeException e) {
            log.error("YL-1011:运单查询出错查询参数, {}", ThrowableUtil.getStackTrace(e));
            throw new RuntimeException("YL-1011:运单查询出错查询参数");
        }
    }

    public boolean audit() {
        SysParam orderauditpass = sysParamAPI.getParamByKey("ORDERAUDITPASS");
        List<UserRoleDTO> userRole = CurrentUser.getUserRole();
        for (UserRoleDTO userRoleDTO : userRole) {
            String roleCode = userRoleDTO.getRoleCode();
            if (StringUtil.isEmpty(roleCode) || CurrentUser.isSuperAdmin()) {
                continue;
            }
            if (!StringUtil.isEmpty(orderauditpass.getParamValue())) {
                String[] split = orderauditpass.getParamValue().split(",");
                for (String rc : split) {
                    if (roleCode.equals(rc)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

}
