package com.lz.service.impl;

import cn.hutool.json.JSONUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lz.api.TEndSUserInfoAPI;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.CodeEnum;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.common.util.gaode.AddressLocationUtil;
import com.lz.controller.CollectionOrderController;
import com.lz.dao.*;
import com.lz.dto.CollectOrderInfoDTO;
import com.lz.dto.TXinFaOrderInfoCaptainAuditDto;
import com.lz.dto.TXinFaOrderWeightDTO;
import com.lz.enums.InsuranceMethodsEnum;
import com.lz.model.*;
import com.lz.payment.Payment;
import com.lz.service.TXinFaOrderInfoService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.util.OrderDeductionUtil;
import com.lz.util.OrderUtil;
import com.lz.vo.AppOrderSearchVO;
import com.lz.vo.CollectionOrderReq;
import com.lz.vo.CollectionOrderResp;
import com.lz.vo.TXinFaOrderVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 *  dingweibo
 *  信发运单查询
 */
@Slf4j
@Service
public class TXinFaOrderInfoServiceImpl implements TXinFaOrderInfoService {

    @Autowired
    private TXinFaOrderInfoMapper xinFaOrderInfoMapper;

    @Autowired
    private TOrderInfoMapper orderInfoMapper;

    @Autowired
    private TOrderStateMapper orderStateMapper;

    @Autowired
    private TOrderCastCalcSnapshotMapper orderCastCalcSnapshotMapper;

    @Resource
    private TOrderInfoDetailMapper orderInfoDetailMapper;

    @Autowired
    private CollectionOrderController collectionOrder;
    @Resource
    private TOrderInfoWeightMapper orderInfoWeightMapper;

    @Resource
    private TOrderCastChangesMapper orderCastChangesMapper;

    @Resource
    private TOrderInsuranceMapper orderInsuranceMapper;

    @Autowired
    private SysParamAPI sysParamAPI;

    @Resource
    private TCarInsuranceMapper carInsuranceMapper;

    @Resource
    private TEndSUserInfoAPI endSUserInfoAPI;

    /**
     * PC端 车队长运单审核
     * @return
     */
    @Override
    public ResultUtil selectByCaptainAudit(AppOrderSearchVO search) throws RuntimeException {
        search.setCaptainId(CurrentUser.getEndUserId());
        Date[] fhTime = search.getFhTime();
        if (fhTime != null) {
            search.setFhsTime(fhTime[0]);
            search.setFheTime(fhTime[1]);
        }
        Date[] fhbdTime = search.getFhbdTime();
        if (fhbdTime != null) {
            search.setFhbdsTime(fhbdTime[0]);
            search.setFhbdeTime(fhbdTime[1]);
        }
        Date[] shbdTime = search.getShbdTime();
        if (shbdTime != null) {
            search.setShbdsTime(shbdTime[0]);
            search.setShbdeTime(shbdTime[1]);
        }
        if(null!= search.getAuditStatus() && !"".equals(search.getAuditStatus())){
            if(search.getAuditStatus().equals("NO")){
                search.setPayStateNode(DictEnum.S0450.code);
            }else if(search.getAuditStatus().equals("PASSNODE")){
                search.setPayStateNode(DictEnum.S0451.code);
            }else if(search.getAuditStatus().equals("NOTPASSNODE")){
                search.setPayStateNode(DictEnum.S0452.code);
            }
        }
        Page<Object> objb = PageHelper.startPage(search.getPage(), search.getSize());
        List<TXinFaOrderInfoCaptainAuditDto> captainAuditDtoList = xinFaOrderInfoMapper.selectByCaptainAudit(search);

        return new ResultUtil(CodeEnum.SUCCESS.getCode(), captainAuditDtoList, objb.getTotal());
    }

    /**
     *  车队长运单审核合计
     * @param search
     * @return
     */
    @Override
    public ResultUtil selectByCaptainAuditSum(AppOrderSearchVO search) {
        // 查询运单总重量
        if(search.getOrderWeight()){
            search.setCaptainId(CurrentUser.getEndUserId());
            Date[] fhTime = search.getFhTime();
            if (fhTime != null) {
                search.setFhsTime(fhTime[0]);
                search.setFheTime(fhTime[1]);
            }
            Date[] fhbdTime = search.getFhbdTime();
            if (fhbdTime != null) {
                search.setFhbdsTime(fhbdTime[0]);
                search.setFhbdeTime(fhbdTime[1]);
            }
            Date[] shbdTime = search.getShbdTime();
            if (shbdTime != null) {
                search.setShbdsTime(shbdTime[0]);
                search.setShbdeTime(shbdTime[1]);
            }
            if(null!= search.getAuditStatus() && !"".equals(search.getAuditStatus())){
                if(search.getAuditStatus().equals("NO")){
                    search.setPayStateNode(DictEnum.S0450.code);
                }else if(search.getAuditStatus().equals("PASSNODE")){
                    search.setPayStateNode(DictEnum.S0451.code);
                }else if(search.getAuditStatus().equals("NOTPASSNODE")){
                    search.setPayStateNode(DictEnum.S0452.code);
                }
            }
            TXinFaOrderWeightDTO xinFaOrderWeightDTO = xinFaOrderInfoMapper.selectByCaptainAuditSum(search);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), xinFaOrderWeightDTO);
        }
        return ResultUtil.ok();
    }


    /**
     * 车队长运单审核导出
     * @param search
     * @return
     */
    @Override
    public ResultUtil captainAuditExportExcel(AppOrderSearchVO search) {
        search.setCaptainId(CurrentUser.getEndUserId());
        Date[] fhTime = search.getFhTime();
        if (fhTime != null) {
            search.setFhsTime(fhTime[0]);
            search.setFheTime(fhTime[1]);
        }
        Date[] fhbdTime = search.getFhbdTime();
        if (fhbdTime != null) {
            search.setFhbdsTime(fhbdTime[0]);
            search.setFhbdeTime(fhbdTime[1]);
        }
        Date[] shbdTime = search.getShbdTime();
        if (shbdTime != null) {
            search.setShbdsTime(shbdTime[0]);
            search.setShbdeTime(shbdTime[1]);
        }
        if(null!= search.getAuditStatus() && !"".equals(search.getAuditStatus())){
            if(search.getAuditStatus().equals("NO")){
                search.setPayStateNode(DictEnum.S0450.code);
            }else if(search.getAuditStatus().equals("PASSNODE")){
                search.setPayStateNode(DictEnum.S0451.code);
            }else if(search.getAuditStatus().equals("NOTPASSNODE")){
                search.setPayStateNode(DictEnum.S0452.code);
            }
        }
        String[] headers =
                { "运单号", "车牌号","司机姓名", "装货重量（吨）","卸货重量（吨）","装货（磅单）时间","卸货（磅单）时间","运费单价","审核状态","运单状态","线路名称","货物类型"};
        String[] names = {"orderBusinessCode","vehicleNumber","realName","deliverWeightNotesWeight","receiveWeightNotesWeight",
                "deliverWeightNotesTime","receiveWeightNotesTime","carriageUnitPrice","orderPayStatus","orderExecuteStatus","lineName","goodsName"};
        List<TXinFaOrderInfoCaptainAuditDto> tOrderInfos = xinFaOrderInfoMapper.selectByCaptainAudit(search);
        Map<String,Object> map = new HashMap<>();
        map.put("headers",headers);
        map.put("names",names);
        map.put("list",tOrderInfos);
        return ResultUtil.ok(map);
    }

    /**
     *  车队长审核回显
     * @param search
     * @return
     */
    @Override
    public ResultUtil selectByCaptainAuditOrderId(AppOrderSearchVO search) {
        Map<String,Object> map = new HashMap();
        TXinFaOrderInfoCaptainAuditDto captainAuditDto = xinFaOrderInfoMapper.selectByCaptainAuditOrderId(search);
        if(DictEnum.CARRIAGE_PRICE_UNIT_BOX.code.equals(captainAuditDto.getCarriagePriceUnit())){
            captainAuditDto.setPrimaryWeight1(captainAuditDto.getDeliverWeightNotesWeight1());
            captainAuditDto.setDischargeWeight1(captainAuditDto.getReceiveWeightNotesWeight1());
            captainAuditDto.setPrimaryWeight2(captainAuditDto.getDeliverWeightNotesWeight2());
            captainAuditDto.setDischargeWeight2(captainAuditDto.getReceiveWeightNotesWeight2());
        }else{
            captainAuditDto.setPrimaryWeight(captainAuditDto.getDeliverWeightNotesWeight());
            captainAuditDto.setDischargeWeight(captainAuditDto.getReceiveWeightNotesWeight());
        }
        TOrderInfo tOrderInfo = orderInfoMapper.selectByPrimaryKey(captainAuditDto.getId());
        CollectOrderInfoDTO collectOrderInfoDTO = orderInfoMapper.pcGetCollectOrderBasisInfo(tOrderInfo.getCode());
        map.put("captainAuditDto",captainAuditDto);
        map.put("collectOrderInfoDTO",collectOrderInfoDTO);
        return ResultUtil.ok(map);
    }

    /**
     *  车队长审核
     * @param vo
     * @return
     */
    @Override
    public ResultUtil captainAuditOrderId(TXinFaOrderVo vo) {
        TOrderInfo tOrderInfo = orderInfoMapper.selectByPrimaryKey(vo.getId());
        TGoodsSourceInfo goodsSourceInfo = xinFaOrderInfoMapper.selectByTGoodsSourceInfo(tOrderInfo.getLineGoodsRelId());
        TOrderInfoWeight orderInfoWeight = orderInfoWeightMapper.selectByOrderId(tOrderInfo.getId());

        //查询t_order_info_detail，获取预计行驶时间
        TOrderInfoDetail orderInfoDetail = orderInfoDetailMapper.selectByOrderId(tOrderInfo.getId());

        //点击收单时，校验当前时间是否大于（建单时间+预计行驶时间），若小于，不允许收单，
        // 提示：未到达收单时间，请于xx月xx日 xx：xx（时分）后再进行收单操作。
        Double estimatedTravelTime = orderInfoDetail.getEstimatedTravelTime();//预计行驶时间
        if(null == estimatedTravelTime || estimatedTravelTime == 0d){
            //如果预计行驶时间是空的，则根据起点终点坐标得出预计行驶时间
            String fromCoordinates = tOrderInfo.getFromCoordinates();//起点坐标
            String endCoordinates = tOrderInfo.getEndCoordinates();//终点坐标
            if(null != fromCoordinates && null != endCoordinates){
                Map<String, Double> map = AddressLocationUtil.getDistanceAndDuration(fromCoordinates, endCoordinates);
                estimatedTravelTime = map.get("estimatedTravelTime");
            }
        }
        int v = (int)(estimatedTravelTime * 3600);//换算成秒
        Date createTime = tOrderInfo.getCreateTime();//建单时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(createTime);
        calendar.add(Calendar.SECOND, v);
        Date newDate = calendar.getTime();//预计收单时间
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分");
        String dateStr = format.format(newDate);
        if(vo.getIfUpdateStatus()){
            if(date.getTime()< newDate.getTime()){
                return ResultUtil.error("未达到线路规定的行驶时间，请于"+dateStr+"后再进行审核操作。");
            }
        }

        if(vo.getIfUpdateStatus()) {
            // 运费判断
            TOrderCastChanges tOrderCastChanges = orderCastChangesMapper.selectByNewOne(tOrderInfo.getCode());
            TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(tOrderInfo.getOrderBusinessCode());
            ResultUtil resultUtil = OrderDeductionUtil
                    .validateAmountAndReturnResult(vo.getFreightPayable(), tOrderInfo.getUserConfirmServiceFee(), tOrderCastChanges.getCapitalTransferPattern(), tOrderInsurance, orderInfoDetail);
            if (!CodeEnum.SUCCESS.getCode().equals(resultUtil.getCode())) {
                return resultUtil;
            }
        }

        if(DictEnum.CARRIAGE_PRICE_UNIT_BOX.code.equals(goodsSourceInfo.getCarriagePriceUnit())){
            orderInfoWeight.setBoxNum(vo.getBoxNum());
            orderInfoWeight.setDeliverWeightNotesTime1(vo.getDeliverWeightNotesTime1());
            orderInfoWeight.setDeliverWeightNotesWeight1(vo.getPrimaryWeight1());
            orderInfoWeight.setDeliverWeightNotesPhoto1(vo.getDeliverWeightNotesPhoto1());
            orderInfoWeight.setReceiveWeightNotesTime1(vo.getReceiveWeightNotesTime1());
            orderInfoWeight.setReceiveWeightNotesWeight1(vo.getDischargeWeight1());
            orderInfoWeight.setReceiveWeightNotesPhoto1(vo.getReceiveWeightNotesPhoto1());
            if(null!=vo.getGrossWeight1()){
                orderInfoWeight.setGrossWeight1(vo.getGrossWeight1());
            }
            orderInfoWeight.setPrimaryWeight1(vo.getPrimaryWeight1());
            orderInfoWeight.setDischargeWeight1(vo.getDischargeWeight1());
            orderInfoWeight.setCarriageUnitPrice1(vo.getCarriageUnitPrice1());
            if(vo.getBoxNum()<2){
                if(null!=vo.getGrossWeight1()){
                    tOrderInfo.setGrossWeight(Double.parseDouble(vo.getGrossWeight1().toString()));//毛重
                }
                tOrderInfo.setDeliverWeightNotesWeight(Double.parseDouble(vo.getPrimaryWeight1().toString()));
                tOrderInfo.setReceiveWeightNotesWeight(Double.parseDouble(vo.getDischargeWeight1().toString()));
                tOrderInfo.setSettledWeight(vo.getDischargeWeight1());
                if(null!=vo.getDeliverWeightNotesTime1()){
                    tOrderInfo.setDeliverWeightNotesTime(vo.getDeliverWeightNotesTime1());
                }
                if(null!=vo.getReceiveWeightNotesTime1()){
                    tOrderInfo.setReceiveWeightNotesTime(vo.getReceiveWeightNotesTime1());
                }
            }else{
                orderInfoWeight.setDeliverWeightNotesTime2(vo.getDeliverWeightNotesTime2());
                orderInfoWeight.setDeliverWeightNotesWeight2(vo.getPrimaryWeight2());
                orderInfoWeight.setDeliverWeightNotesPhoto2(vo.getDeliverWeightNotesPhoto2());
                orderInfoWeight.setReceiveWeightNotesTime2(vo.getReceiveWeightNotesTime2());
                orderInfoWeight.setReceiveWeightNotesWeight2(vo.getDischargeWeight2());
                orderInfoWeight.setReceiveWeightNotesPhoto2(vo.getReceiveWeightNotesPhoto2());
                if(null!=vo.getGrossWeight2()){
                    orderInfoWeight.setGrossWeight2(vo.getGrossWeight2());
                }
                orderInfoWeight.setPrimaryWeight2(vo.getPrimaryWeight2());
                orderInfoWeight.setDischargeWeight2(vo.getDischargeWeight2());
                orderInfoWeight.setCarriageUnitPrice2(vo.getCarriageUnitPrice2());

                if(null!=vo.getGrossWeight1() && null!=vo.getGrossWeight2()){
                    tOrderInfo.setGrossWeight(Double.parseDouble((vo.getGrossWeight1().add(vo.getGrossWeight2())).toString()));//毛重
                }
                tOrderInfo.setDeliverWeightNotesWeight(Double.parseDouble((vo.getPrimaryWeight1().add(vo.getPrimaryWeight2())).toString()));
                tOrderInfo.setReceiveWeightNotesWeight(Double.parseDouble((vo.getDischargeWeight1().add(vo.getDischargeWeight2())).toString()));
                tOrderInfo.setSettledWeight(vo.getDischargeWeight1().add(vo.getDischargeWeight2()));

                if(null!=vo.getDeliverWeightNotesTime1()){
                    tOrderInfo.setDeliverWeightNotesTime(vo.getDeliverWeightNotesTime1());
                }else if(null!=vo.getDeliverWeightNotesTime2()){
                    tOrderInfo.setDeliverWeightNotesTime(vo.getDeliverWeightNotesTime2());
                }
                if(null!=vo.getReceiveWeightNotesTime1()){
                    tOrderInfo.setReceiveWeightNotesTime(vo.getReceiveWeightNotesTime1());
                }else if(null!=vo.getReceiveWeightNotesTime2()){
                    tOrderInfo.setReceiveWeightNotesTime(vo.getReceiveWeightNotesTime2());
                }
            }
            orderInfoWeightMapper.updateByPrimaryKeySelective(orderInfoWeight);
            tOrderInfo.setRulePaymentAmount(vo.getFreightPayable());
            tOrderInfo.setUserConfirmPaymentAmount(vo.getFreightPayable());

        }else{

            if(null!=vo.getGrossWeight()){
                tOrderInfo.setGrossWeight(vo.getGrossWeight());//毛重
            }
            tOrderInfo.setPrimaryWeight(vo.getPrimaryWeight());
            tOrderInfo.setDischargeWeight(vo.getDischargeWeight());
            tOrderInfo.setSettledWeight(vo.getSettledWeight());
            tOrderInfo.setDeliverWeightNotesWeight(vo.getPrimaryWeight());
            tOrderInfo.setReceiveWeightNotesWeight(vo.getDischargeWeight());
            tOrderInfo.setDeliverWeightNotesPhoto(vo.getDeliverWeightNotesPhoto());
            tOrderInfo.setReceiveWeightNotesPhoto(vo.getReceiveWeightNotesPhoto());
            tOrderInfo.setDeliverWeightNotesTime(vo.getDeliverWeightNotesTime());
            tOrderInfo.setReceiveWeightNotesTime(vo.getReceiveWeightNotesTime());
            /** 元/车 当按车时，结算重量取卸货重量*/
            if(DictEnum.CARRIAGE_PRICE_UNIT_CAR.code.equals(goodsSourceInfo.getCarriagePriceUnit())){
                tOrderInfo.setRulePaymentAmount(vo.getFreightPayable());
                tOrderInfo.setUserConfirmPaymentAmount(vo.getFreightPayable());
                tOrderInfo.setSettledWeight(BigDecimal.valueOf(vo.getDischargeWeight()));
            }else{
                tOrderInfo.setRulePaymentAmount(vo.getFreightPayable());
                tOrderInfo.setUserConfirmPaymentAmount(vo.getFreightPayable());
            }

        }

        if(vo.getIfUpdateStatus()){
            tOrderInfo.setOrderPayStatus(DictEnum.M045.code);
        }
        orderInfoMapper.updateByPrimaryKeySelective(tOrderInfo);

        if(vo.getIfUpdateStatus()){
            Payment payment = new Payment();
            payment.CollectionOrder(tOrderInfo, "");

            CollectOrderInfoDTO collectOrderInfoDTO = orderInfoMapper.pcGetCollectOrderBasisInfo(tOrderInfo.getCode());

            TOrderLineGoodsCarriageRuleDetail record = xinFaOrderInfoMapper.selectByLineGoodsRelId(tOrderInfo.getLineGoodsRelId());
            TOrderCastCalcSnapshot orderCastCalcSnapshot = new TOrderCastCalcSnapshot();
            //将上一条置为无效:根据运单业务表id(code)
            orderCastCalcSnapshotMapper.updateDataEnableByCode(tOrderInfo.getCode());
            if(null!= record){
                if (null != record.getRuleName() && StringUtils.isNotEmpty(record.getRuleName()) && !"null".equals(record.getRuleName())) {
                    orderCastCalcSnapshot.setGoodsUnitPrice(collectOrderInfoDTO.getGoodsUnitPrice());
                    orderCastCalcSnapshot.setLossCutExpression(record.getLossCutExpression());
                    orderCastCalcSnapshot.setLossPayableExpression(record.getLossPayableExpression());
                    orderCastCalcSnapshot.setRiseCutExpression(null != record.getRiseCutExpression() ? record.getRiseCutExpression() : "");
                    orderCastCalcSnapshot.setRisePayableExpression(record.getRisePayableExpression());
                    orderCastCalcSnapshot.setLineGoodsCarriageRuleId(record.getCarriageRuleId());
                    orderCastCalcSnapshot.setCarriageZeroCutPayment(null == vo.getCarriageZeroCutPayment() ? new BigDecimal(0) : vo.getCarriageZeroCutPayment());
                    orderCastCalcSnapshot.setCarriageZeroCutPaymentRule(record.getCarriageZeroCutPaymentRule());
                    orderCastCalcSnapshot.setLineGoodsCarriageChangeId(collectOrderInfoDTO.getLineGoodsCarriageChangeId());
                    orderCastCalcSnapshot.setFixCutFee(collectOrderInfoDTO.getFixCutFee());
                    orderCastCalcSnapshot.setFixCutRemark(record.getCutFixRemark());
                    orderCastCalcSnapshot.setOtherCutFee1(null == record.getOtherCutPayment1() ? new BigDecimal(0) : record.getOtherCutPayment1());
                    orderCastCalcSnapshot.setOtherCutFee2(null == record.getOtherCutPayment2() ? new BigDecimal(0) : record.getOtherCutPayment2());
                    orderCastCalcSnapshot.setOtherCutFee3(null == record.getOtherCutPayment3() ? new BigDecimal(0) : record.getOtherCutPayment3());
                    orderCastCalcSnapshot.setOtherCutFee4(null == record.getOtherCutPayment4() ? new BigDecimal(0) : record.getOtherCutPayment4());
                    orderCastCalcSnapshot.setOtherCutRemark1(null == record.getCutPaymentNote1() ? "" : record.getCutPaymentNote1());
                    orderCastCalcSnapshot.setOtherCutRemark2(null == record.getCutPaymentNote2() ? "" : record.getCutPaymentNote2());
                    orderCastCalcSnapshot.setOtherCutRemark3(null == record.getCutPaymentNote3() ? "" : record.getCutPaymentNote3());
                    orderCastCalcSnapshot.setOtherCutRemark4(null == record.getCutPaymentNote4() ? "" : record.getCutPaymentNote4());
                    orderCastCalcSnapshot.setGoodsCutImpurities(record.getGoodsCutImpurities());
                    orderCastCalcSnapshot.setGoodsCutWater(record.getGoodsCutWater());
                    orderCastCalcSnapshot.setRuleName(record.getRuleName());
                    orderCastCalcSnapshot.setLoseOrRise(null == vo.getDeficitWeight() ? 0 : vo.getDeficitWeight());
                    orderCastCalcSnapshot.setLoseOrRiseCut(null == vo.getLossDeduction() ? new BigDecimal(0) : vo.getLossDeduction());
                    if (null != record.getToleranceItem() && StringUtils.isNotBlank(record.getToleranceItem())) {
                        if (record.getToleranceItem().equals(DictEnum.ANDUNSHU.code)) {
                            BigDecimal bigDecimal = record.getToleranceItemValue();
                            bigDecimal = bigDecimal.setScale(4, BigDecimal.ROUND_HALF_UP);
                            orderCastCalcSnapshot.setTolerantValueWeight(bigDecimal.doubleValue());
                            orderCastCalcSnapshot.setTolerantValueCoefficient(null);
                        }
                        if (record.getToleranceItem().equals(DictEnum.ANXISHU.code)) {
                            BigDecimal bigDecimal = record.getToleranceItemValue();
                            bigDecimal = bigDecimal.setScale(4, BigDecimal.ROUND_HALF_UP);
                            orderCastCalcSnapshot.setTolerantValueCoefficient(bigDecimal.doubleValue());
                            orderCastCalcSnapshot.setTolerantValueWeight(null);
                        }
                    }
                    orderCastCalcSnapshot.setCutImpuritiesIsCalcvalue(record.getCutImpuritiesIsCalcvalue());
                    orderCastCalcSnapshot.setCutWaterIsCalcvalue(record.getCutWaterIsCalcvalue());
                    orderCastCalcSnapshot.setDeficitWeight(null == vo.getDeficitWeight() ? 0 : vo.getDeficitWeight());
                }
            }
            orderCastCalcSnapshot.setCarriagePayment(tOrderInfo.getRulePaymentAmount());
            orderCastCalcSnapshot.setCarriageUnitPrice(tOrderInfo.getCurrentCarriageUnitPrice());
            orderCastCalcSnapshot.setRealDeliverWeight(tOrderInfo.getPrimaryWeight());
            orderCastCalcSnapshot.setRealDispathWeight(tOrderInfo.getDischargeWeight());
            orderCastCalcSnapshot.setDeliverWeightNotesPhoto(tOrderInfo.getDeliverWeightNotesPhoto());
            orderCastCalcSnapshot.setReceiveWeightNotesPhoto(tOrderInfo.getReceiveWeightNotesPhoto());
            orderCastCalcSnapshot.setDeliverWeightNotesUploadTime(tOrderInfo.getDeliverWeightNotesTime());
            orderCastCalcSnapshot.setReceiveWeightNotesUploadTime(tOrderInfo.getReceiveWeightNotesTime());
            orderCastCalcSnapshot.setUserConfirmCarriagePayment(tOrderInfo.getUserConfirmPaymentAmount());
            orderCastCalcSnapshot.setGoodsType(tOrderInfo.getGoodsName());
            orderCastCalcSnapshot.setCode(IdWorkerUtil.getInstance().nextId());
            orderCastCalcSnapshot.setDataEnable(true);
            orderCastCalcSnapshot.setEnable(false);
            orderCastCalcSnapshot.setOrderCode(tOrderInfo.getCode());
            orderCastCalcSnapshot.setSettledWeight(tOrderInfo.getSettledWeight());
            if(!DictEnum.CARRIAGE_PRICE_UNIT_DUN.code.equals(goodsSourceInfo.getCarriagePriceUnit())){
                orderCastCalcSnapshot.setFixCutFee(goodsSourceInfo.getFixCutFee());
            }else{
                orderCastCalcSnapshot.setFixCutFee(collectOrderInfoDTO.getFixCutFee());
            }
            orderCastCalcSnapshot.setFixCutRemark(collectOrderInfoDTO.getFixCutRemark());
            orderCastCalcSnapshotMapper.insertSelective(orderCastCalcSnapshot);

            TOrderState orderState = new TOrderState();
            orderState.setCode(IdWorkerUtil.getInstance().nextId());
            orderState.setOrderCode(tOrderInfo.getCode());
            orderState.setOperatorId(tOrderInfo.getEndCarOwnerId());
            orderState.setOperateMethod("PC车队长审核");
            orderState.setOperateTime(new Date());
            orderState.setStateNodeValue(DictEnum.S0451.code);
            orderState.setIfExpire(false);
            orderStateMapper.insertSelective(orderState);
        }
        return ResultUtil.ok();
    }


    /**
     *  批量车队长审核
     * @param vo
     * @return
     */
    @Override
    public ResultUtil captainAuditOrderIdArray(TXinFaOrderVo vo) {
        List<Map<String,Object>> arrayList = new ArrayList<>();
        for(Integer orderId:vo.getIdArray()){
            Map<String,Object> result = new HashMap<>();

            TOrderInfo tOrderInfo = orderInfoMapper.selectByPrimaryKey(orderId);

            TGoodsSourceInfo goodsSourceInfo = xinFaOrderInfoMapper.selectByTGoodsSourceInfo(tOrderInfo.getLineGoodsRelId());
            TOrderInfoWeight orderInfoWeight = orderInfoWeightMapper.selectByOrderId(tOrderInfo.getId());

            //查询t_order_info_detail，获取预计行驶时间
            TOrderInfoDetail orderInfoDetail = orderInfoDetailMapper.selectByOrderId(tOrderInfo.getId());
            //点击收单时，校验当前时间是否大于（建单时间+预计行驶时间），若小于，不允许收单，
            // 提示：未到达收单时间，请于xx月xx日 xx：xx（时分）后再进行收单操作。
            Double estimatedTravelTime = orderInfoDetail.getEstimatedTravelTime();//预计行驶时间
            if(null == estimatedTravelTime || estimatedTravelTime == 0d){
                //如果预计行驶时间是空的，则根据起点终点坐标得出预计行驶时间
                String fromCoordinates = tOrderInfo.getFromCoordinates();//起点坐标
                String endCoordinates = tOrderInfo.getEndCoordinates();//终点坐标
                if(null != fromCoordinates && null != endCoordinates){
                    Map<String, Double> map = AddressLocationUtil.getDistanceAndDuration(fromCoordinates, endCoordinates);
                    estimatedTravelTime = map.get("estimatedTravelTime");
                }
            }
            int v = (int)(estimatedTravelTime * 3600);//换算成秒
            Date createTime = tOrderInfo.getCreateTime();//建单时间
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(createTime);
            calendar.add(Calendar.SECOND, v);
            Date newDate = calendar.getTime();//预计收单时间
            Date date = new Date();
            SimpleDateFormat format = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分");
            String dateStr = format.format(newDate);
            if(date.getTime()< newDate.getTime()){
                result.put("msg","运单号:"+tOrderInfo.getOrderBusinessCode()+"未达到线路规定的行驶时间，此运单暂时不可审核,请于"+dateStr+"后再进行审核操作。");
            }else{
                boolean falg = true;
                if(DictEnum.CARRIAGE_PRICE_UNIT_BOX.code.equals(goodsSourceInfo.getCarriagePriceUnit())){
                    //箱数为1时判断
                    if(orderInfoWeight.getBoxNum() == 1){
                        if(null == orderInfoWeight.getDeliverWeightNotesTime1() || null == orderInfoWeight.getReceiveWeightNotesTime1() ||
                                null == orderInfoWeight.getDeliverWeightNotesWeight1() || null == orderInfoWeight.getReceiveWeightNotesWeight1() ||
                                null == orderInfoWeight.getDeliverWeightNotesPhoto1() || "".equals(orderInfoWeight.getDeliverWeightNotesPhoto1())||
                                null == orderInfoWeight.getReceiveWeightNotesPhoto1() || "".equals(orderInfoWeight.getReceiveWeightNotesPhoto1())){
                            falg = false;
                        }
                    }else if(orderInfoWeight.getBoxNum() == 2){
                        //箱数为2时判断
                        if(null == orderInfoWeight.getDeliverWeightNotesTime1() || null == orderInfoWeight.getDeliverWeightNotesTime2() ||
                                null == orderInfoWeight.getReceiveWeightNotesTime1() || null == orderInfoWeight.getReceiveWeightNotesTime2() ||
                                null == orderInfoWeight.getDeliverWeightNotesWeight1() || null == orderInfoWeight.getDeliverWeightNotesWeight2() ||
                                null == orderInfoWeight.getReceiveWeightNotesWeight1() || null == orderInfoWeight.getReceiveWeightNotesWeight2() ||
                                null == orderInfoWeight.getDeliverWeightNotesPhoto1() || "".equals(orderInfoWeight.getDeliverWeightNotesPhoto1())||
                                null == orderInfoWeight.getDeliverWeightNotesPhoto2() || "".equals(orderInfoWeight.getDeliverWeightNotesPhoto2())||
                                null == orderInfoWeight.getReceiveWeightNotesPhoto1() || "".equals(orderInfoWeight.getReceiveWeightNotesPhoto1())||
                                null == orderInfoWeight.getReceiveWeightNotesPhoto2() || "".equals(orderInfoWeight.getReceiveWeightNotesPhoto2())){
                            falg = false;
                        }
                    }
                }else{
                    if(null == tOrderInfo.getDeliverWeightNotesTime() || null == tOrderInfo.getReceiveWeightNotesTime() ||
                            null == tOrderInfo.getDeliverWeightNotesWeight() || null == tOrderInfo.getReceiveWeightNotesWeight() ||
                            null == tOrderInfo.getDeliverWeightNotesPhoto() || "".equals(tOrderInfo.getDeliverWeightNotesPhoto()) ||
                            null == tOrderInfo.getReceiveWeightNotesPhoto() || "".equals(tOrderInfo.getReceiveWeightNotesPhoto())){
                        falg = false;
                    }
                }
                if(null != tOrderInfo.getDeliverWeightNotesWeight() && tOrderInfo.getDeliverWeightNotesWeight() >= 75){
                    result.put("msg","运单号:"+tOrderInfo.getOrderBusinessCode()+"装货重量需小于75吨，请重新填写，此运单暂时不可审核");
                    arrayList.add(result);
                    continue;
                }
                if(null != tOrderInfo.getReceiveWeightNotesWeight() && tOrderInfo.getReceiveWeightNotesWeight() >= 75){
                    result.put("msg","运单号:"+tOrderInfo.getOrderBusinessCode()+"卸货重量需小于75吨，请重新填写，此运单暂时不可审核");
                    arrayList.add(result);
                    continue;
                }
                if(falg){
                    AppOrderSearchVO search = new  AppOrderSearchVO();
                    search.setOrderId(Long.parseLong(orderId.toString()));
                    CollectOrderInfoDTO collectOrderInfoDTO = orderInfoMapper.pcGetCollectOrderBasisInfo(tOrderInfo.getCode());
                    CollectionOrderResp resp = new CollectionOrderResp();
                    if(!DictEnum.CARRIAGE_PRICE_UNIT_DUN.code.equals(goodsSourceInfo.getCarriagePriceUnit())){
                        CollectionOrderReq req = new CollectionOrderReq();
                        req.setCarriageUnitPrice1(collectOrderInfoDTO.getCarriageUnitPrice());
                        req.setBoxNum(collectOrderInfoDTO.getBoxNum());
                        req.setCarriagePriceUnit(goodsSourceInfo.getCarriagePriceUnit());
                        req.setFixCutFee(goodsSourceInfo.getFixCutFee());
                        resp = JSONUtil.toBean(JSONUtil.toJsonStr(collectionOrder.collection2(req).getData()), CollectionOrderResp.class);
                        tOrderInfo.setRulePaymentAmount(resp.getFreightPayable());
                        tOrderInfo.setUserConfirmPaymentAmount(resp.getFreightPayable());
                        //当按车/箱时，结算重量取卸货重量
                        tOrderInfo.setSettledWeight(BigDecimal.valueOf(tOrderInfo.getReceiveWeightNotesWeight()));
                    }else{
                        //调用计算运费
                        CollectionOrderReq req = new CollectionOrderReq();
                        req.setSettledWeightType(collectOrderInfoDTO.getSettledWeightType());
                        req.setIfExceedAmount(collectOrderInfoDTO.getIfExceedAmount());
                        req.setIsEditSettledWeight(false);
                        req.setSettledWeight(Double.parseDouble(collectOrderInfoDTO.getSettledWeight().toString()));
                        req.setPrimaryWeight(tOrderInfo.getDeliverWeightNotesWeight());
                        req.setDischargeWeight(tOrderInfo.getReceiveWeightNotesWeight());
                        req.setCarriageUnitPrice(collectOrderInfoDTO.getCarriageUnitPrice());
                        req.setCode(collectOrderInfoDTO.getOrderCode());
                        req.setRuleName(collectOrderInfoDTO.getRuleName());
                        req.setGoodsCutWater(collectOrderInfoDTO.getGoodsCutWater());
                        req.setGoodsCutImpurities(collectOrderInfoDTO.getGoodsCutImpurities());
                        req.setGoodsUnitPrice(collectOrderInfoDTO.getGoodsUnitPrice());
                        req.setOtherCutFee1(collectOrderInfoDTO.getOtherCutFee1());
                        req.setOtherCutFee2(collectOrderInfoDTO.getOtherCutFee2());
                        req.setOtherCutFee3(collectOrderInfoDTO.getOtherCutFee3());
                        req.setOtherCutFee4(collectOrderInfoDTO.getOtherCutFee4());
                        req.setFixCutFee(collectOrderInfoDTO.getFixCutFee());
                        req.setCarriageZeroCutPaymentRule(collectOrderInfoDTO.getCarriageZeroCutPaymentRule());
                        req.setToleranceItemValue(collectOrderInfoDTO.getToleranceItemValue());
                        req.setToleranceItem(collectOrderInfoDTO.getToleranceItem());
                        req.setCutImpuritiesIsCalcvalue(collectOrderInfoDTO.getCutImpuritiesIsCalcvalue());
                        req.setCutWaterIsCalcvalue(collectOrderInfoDTO.getCutWaterIsCalcvalue());
                        if (null == req.getRuleName() || "null".equals(req.getRuleName())) {
                            Map<String, Object> map =  JSONUtil.toBean(JSONUtil.toJsonStr(collectionOrder.collection(req).getData()),Map.class);
                            resp.setSettledWeight(Double.parseDouble(String.valueOf(map.get("settledWeight"))));
                            resp.setFreightPayable(new BigDecimal(String.valueOf(map.get("freightPayable"))));
                            resp.setServiceFee(new BigDecimal(String.valueOf(map.get("serviceFee"))));
                        }else{
                            resp = JSONUtil.toBean(JSONUtil.toJsonStr(collectionOrder.collection(req).getData()), CollectionOrderResp.class);
                        }
                        if(null!=vo.getGrossWeight()){
                            tOrderInfo.setGrossWeight(vo.getGrossWeight());//毛重
                        }
                        tOrderInfo.setPrimaryWeight(tOrderInfo.getDeliverWeightNotesWeight());
                        tOrderInfo.setDischargeWeight(tOrderInfo.getReceiveWeightNotesWeight());
                        tOrderInfo.setSettledWeight(BigDecimal.valueOf(resp.getSettledWeight()));
                        tOrderInfo.setRulePaymentAmount(resp.getFreightPayable());
                        tOrderInfo.setUserConfirmPaymentAmount(resp.getFreightPayable());
                    }
                    if(Double.parseDouble(resp.getFreightPayable().toString())<=0){
                        result.put("msg","运单号:"+tOrderInfo.getOrderBusinessCode()+"审核不通过运费为0。");
                    }else{
                        // 运费判断
                        TOrderCastChanges tOrderCastChanges = orderCastChangesMapper.selectByNewOne(tOrderInfo.getCode());
                        TOrderInfoDetail tOrderInfoDetail = orderInfoDetailMapper.selectByOrderId(tOrderInfo.getId());
                        TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(tOrderInfo.getOrderBusinessCode());
                        ResultUtil resultUtil = OrderDeductionUtil.validateAmountAndReturnResult(tOrderInfo.getUserConfirmPaymentAmount(), tOrderInfo.getUserConfirmServiceFee(), tOrderCastChanges.getCapitalTransferPattern(), tOrderInsurance, tOrderInfoDetail);
                        if (!Objects.equals(resultUtil.getCode(), CodeEnum.SUCCESS.getCode())) {
                            result.put("msg", "运单号:"+tOrderInfo.getOrderBusinessCode()+resultUtil.getMsg());
                        } else {
                            tOrderInfo.setOrderPayStatus(DictEnum.M045.code);
                            orderInfoMapper.updateByPrimaryKeySelective(tOrderInfo);

                            Payment payment = new Payment();
                            payment.CollectionOrder(tOrderInfo, "");

                            TOrderLineGoodsCarriageRuleDetail record = xinFaOrderInfoMapper.selectByLineGoodsRelId(tOrderInfo.getLineGoodsRelId());
                            TOrderCastCalcSnapshot orderCastCalcSnapshot = new TOrderCastCalcSnapshot();
                            //将上一条置为无效:根据运单业务表id(code)
                            orderCastCalcSnapshotMapper.updateDataEnableByCode(tOrderInfo.getCode());
                            if(null!= record){
                                if (null != record.getRuleName() && StringUtils.isNotEmpty(record.getRuleName()) && !"null".equals(record.getRuleName())) {
                                    orderCastCalcSnapshot.setGoodsUnitPrice(collectOrderInfoDTO.getGoodsUnitPrice());
                                    orderCastCalcSnapshot.setLossCutExpression(record.getLossCutExpression());
                                    orderCastCalcSnapshot.setLossPayableExpression(record.getLossPayableExpression());
                                    orderCastCalcSnapshot.setRiseCutExpression(null != record.getRiseCutExpression() ? record.getRiseCutExpression() : "");
                                    orderCastCalcSnapshot.setRisePayableExpression(record.getRisePayableExpression());
                                    orderCastCalcSnapshot.setLineGoodsCarriageRuleId(record.getCarriageRuleId());
                                    orderCastCalcSnapshot.setCarriageZeroCutPayment(null == resp.getCarriageZeroCutPayment() ? new BigDecimal(0) : resp.getCarriageZeroCutPayment());
                                    orderCastCalcSnapshot.setCarriageZeroCutPaymentRule(record.getCarriageZeroCutPaymentRule());
                                    orderCastCalcSnapshot.setLineGoodsCarriageChangeId(collectOrderInfoDTO.getLineGoodsCarriageChangeId());
                                    orderCastCalcSnapshot.setFixCutFee(collectOrderInfoDTO.getFixCutFee());
                                    orderCastCalcSnapshot.setFixCutRemark(record.getCutFixRemark());
                                    orderCastCalcSnapshot.setOtherCutFee1(null == record.getOtherCutPayment1() ? new BigDecimal(0) : record.getOtherCutPayment1());
                                    orderCastCalcSnapshot.setOtherCutFee2(null == record.getOtherCutPayment2() ? new BigDecimal(0) : record.getOtherCutPayment2());
                                    orderCastCalcSnapshot.setOtherCutFee3(null == record.getOtherCutPayment3() ? new BigDecimal(0) : record.getOtherCutPayment3());
                                    orderCastCalcSnapshot.setOtherCutFee4(null == record.getOtherCutPayment4() ? new BigDecimal(0) : record.getOtherCutPayment4());
                                    orderCastCalcSnapshot.setOtherCutRemark1(null == record.getCutPaymentNote1() ? "" : record.getCutPaymentNote1());
                                    orderCastCalcSnapshot.setOtherCutRemark2(null == record.getCutPaymentNote2() ? "" : record.getCutPaymentNote2());
                                    orderCastCalcSnapshot.setOtherCutRemark3(null == record.getCutPaymentNote3() ? "" : record.getCutPaymentNote3());
                                    orderCastCalcSnapshot.setOtherCutRemark4(null == record.getCutPaymentNote4() ? "" : record.getCutPaymentNote4());
                                    orderCastCalcSnapshot.setGoodsCutImpurities(record.getGoodsCutImpurities());
                                    orderCastCalcSnapshot.setGoodsCutWater(record.getGoodsCutWater());
                                    orderCastCalcSnapshot.setRuleName(record.getRuleName());
                                    orderCastCalcSnapshot.setLoseOrRise(null == resp.getDeficitWeight() ? 0 : resp.getDeficitWeight());
                                    orderCastCalcSnapshot.setLoseOrRiseCut(null == resp.getLossDeduction() ? new BigDecimal(0) : resp.getLossDeduction());
                                    if (null != record.getToleranceItem() && StringUtils.isNotBlank(record.getToleranceItem())) {
                                        if (record.getToleranceItem().equals(DictEnum.ANDUNSHU.code)) {
                                            BigDecimal bigDecimal = record.getToleranceItemValue();
                                            bigDecimal = bigDecimal.setScale(4, BigDecimal.ROUND_HALF_UP);
                                            orderCastCalcSnapshot.setTolerantValueWeight(bigDecimal.doubleValue());
                                            orderCastCalcSnapshot.setTolerantValueCoefficient(null);
                                        }
                                        if (record.getToleranceItem().equals(DictEnum.ANXISHU.code)) {
                                            BigDecimal bigDecimal = record.getToleranceItemValue();
                                            bigDecimal = bigDecimal.setScale(4, BigDecimal.ROUND_HALF_UP);
                                            orderCastCalcSnapshot.setTolerantValueCoefficient(bigDecimal.doubleValue());
                                            orderCastCalcSnapshot.setTolerantValueWeight(null);
                                        }
                                    }
                                    orderCastCalcSnapshot.setCutImpuritiesIsCalcvalue(record.getCutImpuritiesIsCalcvalue());
                                    orderCastCalcSnapshot.setCutWaterIsCalcvalue(record.getCutWaterIsCalcvalue());
                                    orderCastCalcSnapshot.setDeficitWeight(null == resp.getDeficitWeight() ? 0 : resp.getDeficitWeight());
                                }
                            }
                            orderCastCalcSnapshot.setCarriagePayment(tOrderInfo.getRulePaymentAmount());
                            orderCastCalcSnapshot.setCarriageUnitPrice(tOrderInfo.getCurrentCarriageUnitPrice());
                            orderCastCalcSnapshot.setRealDeliverWeight(tOrderInfo.getPrimaryWeight());
                            orderCastCalcSnapshot.setRealDispathWeight(tOrderInfo.getDischargeWeight());
                            orderCastCalcSnapshot.setDeliverWeightNotesPhoto(tOrderInfo.getDeliverWeightNotesPhoto());
                            orderCastCalcSnapshot.setReceiveWeightNotesPhoto(tOrderInfo.getReceiveWeightNotesPhoto());
                            orderCastCalcSnapshot.setDeliverWeightNotesUploadTime(tOrderInfo.getDeliverWeightNotesTime());
                            orderCastCalcSnapshot.setReceiveWeightNotesUploadTime(tOrderInfo.getReceiveWeightNotesTime());
                            orderCastCalcSnapshot.setUserConfirmCarriagePayment(tOrderInfo.getUserConfirmPaymentAmount());
                            orderCastCalcSnapshot.setGoodsType(tOrderInfo.getGoodsName());
                            orderCastCalcSnapshot.setCode(IdWorkerUtil.getInstance().nextId());
                            orderCastCalcSnapshot.setDataEnable(true);
                            orderCastCalcSnapshot.setEnable(false);
                            orderCastCalcSnapshot.setOrderCode(tOrderInfo.getCode());
                            orderCastCalcSnapshot.setSettledWeight(tOrderInfo.getSettledWeight());
                            if(!DictEnum.CARRIAGE_PRICE_UNIT_DUN.code.equals(goodsSourceInfo.getCarriagePriceUnit())){
                                orderCastCalcSnapshot.setFixCutFee(goodsSourceInfo.getFixCutFee());
                            }else{
                                orderCastCalcSnapshot.setFixCutFee(collectOrderInfoDTO.getFixCutFee());
                            }
                            orderCastCalcSnapshot.setFixCutRemark(collectOrderInfoDTO.getFixCutRemark());
                            orderCastCalcSnapshotMapper.insertSelective(orderCastCalcSnapshot);

                            TOrderState orderState = new TOrderState();
                            orderState.setCode(IdWorkerUtil.getInstance().nextId());
                            orderState.setOrderCode(tOrderInfo.getCode());
                            orderState.setOperatorId(tOrderInfo.getEndCarOwnerId());
                            orderState.setOperateMethod("PC车队长审核");
                            orderState.setOperateTime(new Date());
                            orderState.setStateNodeValue(DictEnum.S0451.code);
                            orderState.setIfExpire(false);
                            orderStateMapper.insertSelective(orderState);
                            result.put("msg","运单号:"+tOrderInfo.getOrderBusinessCode()+"已审核通过。");
                        }
                    }
                }else{
                    result.put("msg","运单号:"+tOrderInfo.getOrderBusinessCode()+"装、卸货磅单信息不完整，此运单暂时不可审核。");
                }
            }
            arrayList.add(result);

        }
        if(arrayList.size()>0){
            return ResultUtil.ok(arrayList);
        }else{
            return ResultUtil.ok();
        }

    }


    /**
     *  运单收单
     * @param search
     * @return
     */
    @Override
    public ResultUtil selectByPutOrderPage(AppOrderSearchVO search) {
        search.setState("YDSH");
        OrderUtil.getUserType(search);
        if (CurrentUser.accountIsCompanyAdmin()) {
            search.setCompanyId(Integer.parseInt(CurrentUser.getUserCompanyId().get(0)));
        }else{
            List<Integer> lineGoodsRelIdList =  xinFaOrderInfoMapper.selectByAccountId(CurrentUser.getUserAccountId());
            search.setLineGoodsRelId(lineGoodsRelIdList);
        }
        Page<Object> objb = PageHelper.startPage(search.getPage(), search.getSize());
        List<TXinFaOrderInfoCaptainAuditDto> putOrderList = xinFaOrderInfoMapper.selectByPutOrderPage(search);

        if (CurrentUser.accountIsCompanyAdmin()) {
            for (TXinFaOrderInfoCaptainAuditDto tOrderInfo : putOrderList) {
                tOrderInfo.setOrderPayPms(true);
                tOrderInfo.setOrderReceiverPms(true);
                tOrderInfo.setPayReviewPms(true);
            }
        }else{
            // 收单权限
            Set<Integer> receiverLine = search.getReceiverLine();
            for (TXinFaOrderInfoCaptainAuditDto tOrderInfo : putOrderList) {
                for (Integer rl : receiverLine) {
                    if (rl.equals(tOrderInfo.getLineGoodsRelId())) {
                        tOrderInfo.setOrderReceiverPms(true);
                    }
                }
            }
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), putOrderList, objb.getTotal());
    }

    /**
     *  运单收单合计
     * @param search
     * @return
     */
    @Override
    public ResultUtil selectByPutOrderSum(AppOrderSearchVO search) {
        // 查询运单总重量
        if(search.getOrderWeight()){
            search.setState("YDSH");
            OrderUtil.getUserType(search);
            if (CurrentUser.accountIsCompanyAdmin()) {
                search.setCompanyId(Integer.parseInt(CurrentUser.getUserCompanyId().get(0)));
            }else{
                List<Integer> lineGoodsRelIdList =  xinFaOrderInfoMapper.selectByAccountId(CurrentUser.getUserAccountId());
                search.setLineGoodsRelId(lineGoodsRelIdList);
            }
            TXinFaOrderWeightDTO xinFaOrderWeightDTO = xinFaOrderInfoMapper.selectByPutOrderSum(search);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), xinFaOrderWeightDTO);
        }
        return ResultUtil.ok();
    }

    /**
     *  运单收单导出
     * @param search
     * @return
     */
    @Override
    public ResultUtil putOrderExportExcel(AppOrderSearchVO search) {
        search.setState("YDSH");
        OrderUtil.getUserType(search);
        if (CurrentUser.accountIsCompanyAdmin()) {
            search.setCompanyId(Integer.parseInt(CurrentUser.getUserCompanyId().get(0)));
        }else{
            List<Integer> lineGoodsRelIdList =  xinFaOrderInfoMapper.selectByAccountId(CurrentUser.getUserAccountId());
            search.setLineGoodsRelId(lineGoodsRelIdList);
        }
        String[] headers =
                { "运单号", "车牌号", "装货重量（吨）","卸货重量（吨）","装货（磅单）时间","卸货（磅单）时间","司机姓名","车队长姓名",
                        "结算重量（吨）","线路名称","货物类型","运单状态","收单员","确认应付运费","运费单价","收货时间","调度费"};
        String[] names = {"orderBusinessCode","vehicleNumber","deliverWeightNotesWeight","receiveWeightNotesWeight","deliverWeightNotesTime","receiveWeightNotesTime",
                "realName","captainName","settledWeight","lineName","goodsName","orderExecuteStatus","receiveGoodsContacter","userConfirmPaymentAmount",
                "carriageUnitPrice","receiveOrderTime","dispatchFee"};
        List<TXinFaOrderInfoCaptainAuditDto> tOrderInfos = xinFaOrderInfoMapper.selectByPutOrderPage(search);
        Map<String,Object> map = new HashMap<>();
        map.put("headers",headers);
        map.put("names",names);
        map.put("list",tOrderInfos);
        return ResultUtil.ok(map);
    }

    /**
     * 运单收单驳回车队长审核
     * @param search
     * @return
     */
    @Override
    public ResultUtil orderCaptainReject(AppOrderSearchVO search) {
        TOrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(Integer.parseInt(search.getOrderId().toString()));
        TOrderState orderState = new TOrderState();
        orderState.setCode(IdWorkerUtil.getInstance().nextId());
        orderState.setOrderCode(orderInfo.getCode());
        orderState.setOperatorId(orderInfo.getEndCarOwnerId());
        orderState.setOperateMethod("PC车队长审核");
        orderState.setOperateTime(new Date());
        orderState.setStateNodeValue(DictEnum.S0452.code);
        orderState.setIfExpire(false);
        orderStateMapper.insertSelective(orderState);
        return ResultUtil.ok();
    }

    /**
     *  批量收单
     * @param search
     * @return
     */
    @Override
    @Transactional
    public ResultUtil batchPutOrder(AppOrderSearchVO search) {
        for(Integer id:search.getIds()){
            TOrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(id);
            if(null != orderInfo){
                if(null != orderInfo.getDeliverWeightNotesWeight() &&
                        orderInfo.getDeliverWeightNotesWeight() >= 75){
                    return ResultUtil.error("运单号："+orderInfo.getOrderBusinessCode()+"装货重量需小于75吨，请重新填写");
                }
                if(null != orderInfo.getReceiveWeightNotesWeight() &&
                        orderInfo.getReceiveWeightNotesWeight() >= 75){
                    return ResultUtil.error("运单号："+orderInfo.getOrderBusinessCode()+"卸货重量需小于75吨，请重新填写");
                }
                orderInfo.setOrderExecuteStatus(DictEnum.O060.code);
                orderInfo.setReceiveOrderUserId(CurrentUser.getUserAccountId());
                orderInfo.setReceiveOrderTime(new Date());
                orderInfo.setReceiveGoodsContacter(CurrentUser.getUserNickname());
                orderInfo.setReceiveGoodsContacterPhone(CurrentUser.getUserAccountNo());
                orderInfoMapper.updateByPrimaryKeySelective(orderInfo);
                //获取保单信息
                if (null != orderInfo.getOrderBusinessCode() && !"".equals(orderInfo.getOrderBusinessCode())) {
                    TCarInsurance tCarInsurance = carInsuranceMapper.selectByVehicleId(orderInfo.getVehicleId());
                    if (null == tCarInsurance) {
                        //收单时，若实收重量大于38吨，已投保运单将投保状态变更为“退保”；
                        //收单时，若实收重量小于等于38吨，运单投保状态变为其他
                        TOrderInsurance orderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(orderInfo.getOrderBusinessCode());
                        //获取默认保费
                        SysParam insuredAmount = sysParamAPI.getParamByKey("PICC_FREIGHT_INSURANCE");
                        if(null != orderInsurance){
                            TOrderCastChanges tOrderCastChanges = orderCastChangesMapper.selectByNewOne(orderInfo.getCode());
                            if (null != orderInsurance.getInsure() && orderInsurance.getInsure() != 1 && null != tOrderCastChanges.getCapitalTransferType() &&
                                    DictEnum.PAYTOCAPTAIN.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                                SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.NOINSURANCE.code);
                                if(null != paramByKey){
                                    String[] split = paramByKey.getParamValue().split(",");
                                    if(split.length > 0){
                                        List<String> list = Arrays.asList(split);
                                        if (null != orderInfo.getEndCarOwnerId()) {
                                            if(list.contains(orderInfo.getEndCarOwnerId().toString())){
                                                TEndUserInfo tEndUserInfo = endSUserInfoAPI.selectByPrimaryKey(orderInfo.getEndCarOwnerId());
                                                orderInsurance.setUninsuredCause("车队长"+tEndUserInfo.getRealName()+"不投保");
                                                if(null != orderInsurance.getInsure() && 2 == orderInsurance.getInsure()){
                                                    orderInsurance.setInsure(0);
                                                    orderInsurance.setInsuredAmount(BigDecimal.ZERO);
                                                }
                                            }else{
                                                if (null != orderInsurance.getInsuranceMethod() && !InsuranceMethodsEnum.NOTINSURED.getKey().equals(orderInsurance.getInsuranceMethod())) {
                                                    //实收<=38,投保状态是其他2
                                                    if(orderInfo.getDischargeWeight() <= 38){
                                                        orderInsurance.setInsure(2);
                                                        orderInsurance.setInsuredAmount(new BigDecimal(insuredAmount.getParamValue()));
                                                    }else{
                                                        //实收>38吨，投保状态是不投保0;
                                                        orderInsurance.setInsure(0);
                                                        orderInsurance.setInsuredAmount(BigDecimal.ZERO);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }else if(null != orderInsurance.getInsuranceMethod() &&
                                    InsuranceMethodsEnum.MUSTBEINSURED.getKey().equals(orderInsurance.getInsuranceMethod()) &&
                                    null != orderInsurance.getInsure() && orderInsurance.getInsure() == 0){
                                // 收单时，实收<=38吨，投保状态是其他2;
                                if(null != orderInfo.getDischargeWeight() && orderInfo.getDischargeWeight() <= 38){
                                    orderInsurance.setInsure(2);
                                    orderInsurance.setInsuredAmount(new BigDecimal(insuredAmount.getParamValue()));
                                }
                            }else {
                                if(null != orderInfo.getDischargeWeight() && orderInfo.getDischargeWeight() > 38){
                                    if(null != orderInsurance.getInsure() && 2 == orderInsurance.getInsure()){
                                        orderInsurance.setInsure(0);
                                        orderInsurance.setInsuredAmount(BigDecimal.ZERO);
                                        orderInsurance.setUninsuredCause("装货重量大于38吨");
                                    }
                                }else{
                                    if(null != orderInsurance.getInsure() && 1 == orderInsurance.getInsure()){
                                        orderInsurance.setInsuranceCancellation(false);
                                        orderInsurance.setUninsuredCause(null);
                                    }else{
                                        if(null != orderInsurance.getInsure() && 0 == orderInsurance.getInsure() && null != orderInsurance.getUninsuredCause() &&
                                                "装货重量大于38吨".equals(orderInsurance.getUninsuredCause())){
                                            orderInsurance.setInsure(2);
                                            orderInsurance.setInsuredAmount(new BigDecimal(insuredAmount.getParamValue()));
                                            orderInsurance.setUninsuredCause("实收重量未超38吨，平台收取保费");
                                        }
                                    }
                                }
                            }
                            orderInsurance.setUpdateUser(CurrentUser.getUserNickname());
                            orderInsurance.setUpdateTime(new Date());
                            orderInsuranceMapper.updateByPrimaryKey(orderInsurance);
                        }
                    }

                }
                TOrderState orderState = new TOrderState();
                orderState.setCode(IdWorkerUtil.getInstance().nextId());
                orderState.setOrderCode(orderInfo.getCode());
                orderState.setOperatorId(CurrentUser.getUserAccountId());
                orderState.setOperateTime(new Date());
                orderState.setStateNodeValue(DictEnum.S0501.code);
                orderState.setIfExpire(false);
                orderStateMapper.insertSelective(orderState);
            }
        }
        return ResultUtil.ok();
    }

    //信发实体运单支付查询列表
    @Override
    public ResultUtil payOrderPage(AppOrderSearchVO search) {
        search.setState("PAY");
        OrderUtil.getUserType(search);
        if (CurrentUser.accountIsCompanyAdmin()) {
            search.setCompanyId(Integer.parseInt(CurrentUser.getUserCompanyId().get(0)));
        }else{
            List<Integer> lineGoodsRelIdList =  xinFaOrderInfoMapper.selectPayLineByAccountId(CurrentUser.getUserAccountId());
            search.setLineGoodsRelId(lineGoodsRelIdList);
        }
        Page<Object> objb = PageHelper.startPage(search.getPage(), search.getSize());
        List<TXinFaOrderInfoCaptainAuditDto> putOrderList = xinFaOrderInfoMapper.payOrderPage(search);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), putOrderList, objb.getTotal());
    }

    /**
     * 信发实体运单支付合计查询
     * @param search
     * @return
     */
    @Override
    public ResultUtil payOrderSum(AppOrderSearchVO search) {
        // 查询运单总重量
        if(search.getOrderWeight()){
            search.setState("PAY");
            OrderUtil.getUserType(search);
            if (CurrentUser.accountIsCompanyAdmin()) {
                search.setCompanyId(Integer.parseInt(CurrentUser.getUserCompanyId().get(0)));
            }else{
                List<Integer> lineGoodsRelIdList =  xinFaOrderInfoMapper.selectPayLineByAccountId(CurrentUser.getUserAccountId());
                search.setLineGoodsRelId(lineGoodsRelIdList);
            }
            TXinFaOrderWeightDTO xinFaOrderWeightDTO = xinFaOrderInfoMapper.payOrderSum(search);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), xinFaOrderWeightDTO);
        }
        return ResultUtil.ok();
    }

    /**
     * 信发实体运单支付导出
     * @param search
     * @return
     */
    @Override
    public ResultUtil payOrderExportExcel(AppOrderSearchVO search) {
        String[] headers =
                { "运单号", "车牌号", "司机姓名","车队长姓名","确认结算运费（元）","确认结算调度费（元）","结算重量（吨）","资金状态",
                        "线路名称","货物类型","承运方"};
        String[] names = {"orderBusinessCode","vehicleNumber","realName","captainName","userConfirmPaymentAmount","dispatchFee",
                "settledWeight","orderPayStatusValue","lineName","goodsName","carrierName"};
        search.setState("PAY");
        OrderUtil.getUserType(search);
        if (CurrentUser.accountIsCompanyAdmin()) {
            search.setCompanyId(Integer.parseInt(CurrentUser.getUserCompanyId().get(0)));
        }else{
            List<Integer> lineGoodsRelIdList =  xinFaOrderInfoMapper.selectPayLineByAccountId(CurrentUser.getUserAccountId());
            search.setLineGoodsRelId(lineGoodsRelIdList);
        }
        List<TXinFaOrderInfoCaptainAuditDto> tOrderInfos = xinFaOrderInfoMapper.payOrderPage(search);
        Map<String,Object> map = new HashMap<>();
        map.put("headers",headers);
        map.put("names",names);
        map.put("list",tOrderInfos);
        return ResultUtil.ok(map);
    }

    /**
     *  信发运单列表查询
     * @param search
     * @return
     */
    @Override
    public ResultUtil orderPage(AppOrderSearchVO search) {
        Date[] fhTime = search.getFhTime();
        if (fhTime != null) {
            search.setFhsTime(fhTime[0]);
            search.setFheTime(fhTime[1]);
        }
        Date[] fhbdTime = search.getFhbdTime();
        if (fhbdTime != null) {
            search.setFhbdsTime(fhbdTime[0]);
            search.setFhbdeTime(fhbdTime[1]);
        }
        Date[] shbdTime = search.getShbdTime();
        if (shbdTime != null) {
            search.setShbdsTime(shbdTime[0]);
            search.setShbdeTime(shbdTime[1]);
        }
        Date[] sh = search.getShTime();
        if (sh != null) {
            search.setShsTime(sh[0]);
            search.setSheTime(sh[1]);
        }
        Date[] fk = search.getFkTime();
        if (null != fk) {
            search.setFksTime(fk[0]);
            search.setFkeTime(fk[1]);
        }
        Date[] of = search.getOrderFinishTimeArry();
        if (null != of) {
            search.setFihsTime(of[0]);
            search.setFiheTime(of[1]);
        }
        if (!CurrentUser.getUserCompanyIdInteger().isEmpty()) {
            search.setCompanyId(CurrentUser.getUserCompanyIdInteger().get(0));
        } else {
            return ResultUtil.ok(new ArrayList<>(), 0L);
        }
        if (null != search.getState() && search.getState().equals("ST")) {
            List<Integer> lineGoodsRelIdList =  xinFaOrderInfoMapper.selectLineByAccountId(CurrentUser.getUserAccountId());
            search.setLineGoodsRelId(lineGoodsRelIdList);
        }
        Page<Object> objb = PageHelper.startPage(search.getPage(), search.getSize());
        List<TXinFaOrderInfoCaptainAuditDto> putOrderList = xinFaOrderInfoMapper.orderPage(search);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), putOrderList, objb.getTotal());
    }

    /**
     *  信发运单合计查询
     * @param search
     * @return
     */
    @Override
    public ResultUtil orderPageSum(AppOrderSearchVO search) {
        // 查询运单总重量
        if(search.getOrderWeight()){
            Date[] fhTime = search.getFhTime();
            if (fhTime != null) {
                search.setFhsTime(fhTime[0]);
                search.setFheTime(fhTime[1]);
            }
            Date[] fhbdTime = search.getFhbdTime();
            if (fhbdTime != null) {
                search.setFhbdsTime(fhbdTime[0]);
                search.setFhbdeTime(fhbdTime[1]);
            }
            Date[] shbdTime = search.getShbdTime();
            if (shbdTime != null) {
                search.setShbdsTime(shbdTime[0]);
                search.setShbdeTime(shbdTime[1]);
            }
            Date[] sh = search.getShTime();
            if (sh != null) {
                search.setShsTime(sh[0]);
                search.setSheTime(sh[1]);
            }
            Date[] fk = search.getFkTime();
            if (null != fk) {
                search.setFksTime(fk[0]);
                search.setFkeTime(fk[1]);
            }
            Date[] of = search.getOrderFinishTimeArry();
            if (null != of) {
                search.setFihsTime(of[0]);
                search.setFiheTime(of[1]);
            }
            if (!CurrentUser.getUserCompanyIdInteger().isEmpty()) {
                search.setCompanyId(CurrentUser.getUserCompanyIdInteger().get(0));
            } else {
                return ResultUtil.ok();
            }
            if (null != search.getState() && search.getState().equals("ST")) {
                List<Integer> lineGoodsRelIdList =  xinFaOrderInfoMapper.selectLineByAccountId(CurrentUser.getUserAccountId());
                search.setLineGoodsRelId(lineGoodsRelIdList);
            }
            TXinFaOrderWeightDTO xinFaOrderWeightDTO = xinFaOrderInfoMapper.orderPageSum(search);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), xinFaOrderWeightDTO);
        }
        return ResultUtil.ok();
    }

    /**
     * 信发运单列表导出
     * @param search
     * @return
     */
    @Override
    public ResultUtil orderPageExportExcel(AppOrderSearchVO search) {
        Date[] fhTime = search.getFhTime();
        if (fhTime != null) {
            search.setFhsTime(fhTime[0]);
            search.setFheTime(fhTime[1]);
        }
        Date[] fhbdTime = search.getFhbdTime();
        if (fhbdTime != null) {
            search.setFhbdsTime(fhbdTime[0]);
            search.setFhbdeTime(fhbdTime[1]);
        }
        Date[] shbdTime = search.getShbdTime();
        if (shbdTime != null) {
            search.setShbdsTime(shbdTime[0]);
            search.setShbdeTime(shbdTime[1]);
        }
        Date[] sh = search.getShTime();
        if (sh != null) {
            search.setShsTime(sh[0]);
            search.setSheTime(sh[1]);
        }
        Date[] fk = search.getFkTime();
        if (null != fk) {
            search.setFksTime(fk[0]);
            search.setFkeTime(fk[1]);
        }
        Date[] of = search.getOrderFinishTimeArry();
        if (null != of) {
            search.setFihsTime(of[0]);
            search.setFiheTime(of[1]);
        }
        if (!CurrentUser.getUserCompanyIdInteger().isEmpty()) {
            search.setCompanyId(CurrentUser.getUserCompanyIdInteger().get(0));
        } else {
            return ResultUtil.ok(new ArrayList<>());
        }
        if (null != search.getState() && search.getState().equals("ST")) {
            List<Integer> lineGoodsRelIdList =  xinFaOrderInfoMapper.selectLineByAccountId(CurrentUser.getUserAccountId());
            search.setLineGoodsRelId(lineGoodsRelIdList);
        }
        String[] headers =
                { "运单号", "车牌号", "司机姓名","车队长姓名","发货时间","装货重量（吨）","卸货重量（吨）","装货（磅单）时间","卸货（磅单）时间", "运费单价",
                        "结算重量（吨）","收单员","收单时间","确认结算运费（元）","确认结算调度费（元）","资金转移方式","资金状态","运单状态","线路名称","货物类型","承运方"};
        String[] names = {"orderBusinessCode","vehicleNumber","realName","captainName","deliverOrderTime","deliverWeightNotesWeight","receiveWeightNotesWeight","deliverWeightNotesTime","receiveWeightNotesTime", "carriageUnitPrice",
                "settledWeight","receiveGoodsContacter","receiveOrderTime","userConfirmPaymentAmount","dispatchFee","capitalTransferType","orderPayStatus","orderExecuteStatus","lineName","goodsName","carrierName"};
        List<TXinFaOrderInfoCaptainAuditDto> tOrderInfos = xinFaOrderInfoMapper.orderPage(search);
        Map<String,Object> map = new HashMap<>();
        map.put("headers",headers);
        map.put("names",names);
        map.put("list",tOrderInfos);
        return ResultUtil.ok(map);
    }

    @Override
    public ResultUtil receiveOrderUser() {
        return ResultUtil.ok(xinFaOrderInfoMapper.receiveOrderUser(CurrentUser.getUserCompanyIdInteger().get(0)));
    }

}
