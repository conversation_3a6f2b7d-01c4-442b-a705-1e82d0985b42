package com.lz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.codingapi.txlcn.tc.annotation.LcnTransaction;
import com.lz.api.*;
import com.lz.common.config.RedisUtil;
import com.lz.common.constants.MqMessageTag;
import com.lz.common.constants.MqMessageTopic;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.LicensePlateColorEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.CodeEnum;
import com.lz.common.model.MQMessage;
import com.lz.common.util.*;
import com.lz.common.util.gaode.AddressLocationUtil;
import com.lz.dao.*;
import com.lz.dao.mongodb.TrajectoryMongoDao;
import com.lz.dto.CompanySourceDTO;
import com.lz.dto.TEndUserDTO;
import com.lz.enums.InsuranceMethodsEnum;
import com.lz.enums.InsuredGoodsTypesEnum;
import com.lz.example.TOrderCarlocationExample;
import com.lz.example.TOrderInfoExample;
import com.lz.example.TOrderStateExample;
import com.lz.model.*;
import com.lz.model.mongodb.TrajectoryMongo;
import com.lz.model.trajectory.resp.recent.TransTimeManageVResp;
import com.lz.payment.Payment;
import com.lz.schedule.model.TTask;
import com.lz.service.*;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.util.DigitlFlowUtil;
import com.lz.util.SendOrderUtil;
import com.lz.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.awt.geom.Point2D;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * author dingweibo
 * 微信端装货签到接口
 */
@Service("tLoadingSignInService")
@Slf4j
public class TLoadingSignInServiceImpl  implements TLoadingSignInService {
    @Resource
    private AccountService accountService;

    @Resource
    private TOrderInfoMapper tOrderInfoMapper;

    @Resource
    private TOrderGoodsSourceInfoMapper orderGoodsSourceInfoMapper;

    @Autowired
    private TOrderInfoService tOrderInfoService;

    @Autowired
    private TOrderContractServiceImpl orderContractService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private TEndSUserInfoAPI tEndSUserInfoAPI;

    @Autowired
    private TEndCarInfoAPI tEndCarInfoAPI;

    @Autowired
    private CarrierService carrierService;

    @Autowired
    private TrajectoryRecentAPI trajectoryRecentAPI;

    @Autowired
    private LineService lineService;

    @Autowired
    private SysParamAPI sysParamAPI;

    @Autowired
    private TOrderVehicleTrajectoryMapper tOrderVehicleTrajectoryMapper;

    @Resource
    private TrajectoryMongoDao trajectoryMongoDao;

    @Autowired
    private TOrderStateMapper tOrderStateMapper;

    @Autowired
    private TOrderAbnormalMapper tOrderAbnormalMapper;

    @Autowired
    private AppCommonAPI appCommonAPI;

    @Autowired
    private LinkGoodsRelAPI linkGoodsRelAPI;

    @Resource
    private TOrderCarlocationMapper tOrderCarlocationMapper;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private DigitlFlowUtil digitlFlowUtil;

    @Autowired
    private TOrderCastChangesMapper orderCastChangesMapper;

    @Autowired
    private TBusinessAPI tBusinessAPI;

    @Autowired
    private SendOrderUtil sendOrderUtil;

    @Autowired
    private GoodsSourceAPI goodsSourceAPI;

    @Autowired
    private MqAPI mqAPI;

    @Autowired
    private TAdvanceOrderTmpService advanceOrderTmpService;

    @Autowired
    private TFifthGenerationSignOpenAccountApi tFifthGenerationSignOpenAccountApi;

    @Autowired
    private PoundRoomService poundRoomService;
    @Autowired
    private  GoodsSourceDisposeAPI goodsSourceDisposeAPI;

    @Autowired
    private TOrderAnHuiReportService tOrderAnHuiReportService;

    @Resource
    private TOrderInfoDetailMapper orderInfoDetailMapper;

    @Resource
    private TOrderDriverCarStatisticsMapper orderDriverCarStatisticsMapper;

    @Resource
    private TOrderInfoWeightMapper orderInfoWeightMapper;

    @Resource
    private NetSignOpenAccountAPI netSignOpenAccountAPI;

    @Autowired
    private TOrderInfoDetailMapper tOrderInfoDetailMapper;

    @Resource
    private TOrderInsuranceMapper orderInsuranceMapper;

    @Resource
    private TOrderTaskMapper orderTaskMapper;

    @Resource
    private TOrderTaskServie orderTaskServie;

    @Resource
    private TCarInsuranceMapper carInsuranceMapper;

    /**
     *  根据openid 查询用户是否登录，如果登录返回相应信息
     * @param orderCode
     * @return
     */
    @Override
    //@LcnTransaction(propagation = DTXPropagation.SUPPORTS)
    @Transactional
    public ResultUtil selectByOpenId(String orderCode) {
        ResultUtil resultUtil = new ResultUtil();
        Integer endUserId = CurrentUser.getEndUserId();
        //查询用户证件子状态
        TEndUserAuditInfo endUserAuditInfo = tEndSUserInfoAPI.selectByEndUserId(endUserId);
        TOrderInfoExample example = new  TOrderInfoExample();
        TOrderInfoExample.Criteria  c = example.createCriteria();
        if(orderCode==null||"".equals(orderCode)){
            if(endUserId!=null&&!"".equals(endUserId)){
                c.andEndDriverIdEqualTo(endUserId);
                c.andOrderExecuteStatusEqualTo(DictEnum.M020.code);
            }else{
                resultUtil.setCode("400");
                resultUtil.setMsg("当前司机没有登录，请登录！");
                return resultUtil;
            }

        }else{
            c.andCodeEqualTo(orderCode);
        }
        List<TOrderInfo> list = tOrderInfoMapper.selectByExample(example);
        TOrderInfo info =  new TOrderInfo();
        if (list.size() > 0) {
            info = list.get(0);
        }else if(list.size()<1&&(orderCode==null||"".equals(orderCode))){
            return new ResultUtil(CodeEnum.FAIL.getCode(),"当前无运单！");
        }else{
            resultUtil.setMsg("无此运单信息！");
            resultUtil.setCode("error");
            return  resultUtil;
        }
        if(!endUserId.equals(info.getEndDriverId())){
            resultUtil.setMsg("当前登录司机与运单司机不一致！");
            resultUtil.setCode("error");
            return  resultUtil;
        }else if(!DictEnum.M020.code.equals(info.getOrderExecuteStatus())&&!DictEnum.M011.code.equals(info.getOrderExecuteStatus())){//M020 已建单 M011已抢单
            resultUtil.setMsg("当前运单没有建单！");
            resultUtil.setCode("error");
            return  resultUtil;
        }else{
            if(list.size()>0){
                //企业信息
                TCompanyInfoQuery companyInfoQuery = new TCompanyInfoQuery().setCompanyId(info.getCompanyId());
                ResultUtil re = companyService.selectCompanyInfoById(companyInfoQuery);
                TCompanyInfo companyInfo = JSON.parseObject(JSONObject.toJSON(re.getData()).toString(),TCompanyInfo.class);

                //承运方信息
                ResultUtil reCarr = carrierService.selectById(info.getCarrierId().toString());
                TCarrierInfo tCarrierInfo =  JSON.parseObject(JSONObject.toJSON(reCarr.getData()).toString(),TCarrierInfo.class);

                //司机信息 姓名电话
                ResultUtil resuw = tEndSUserInfoAPI.selectById(info.getEndDriverId());
                TEndUserInfo teu =  JSON.parseObject(JSONObject.toJSON(resuw.getData()).toString(),TEndUserInfo.class);

                //车辆信息
                ResultUtil rr = tEndCarInfoAPI.selectById(info.getVehicleId());
                TEndCarInfo tc = JSON.parseObject(JSONObject.toJSON(rr.getData()).toString(),TEndCarInfo.class);

                TLineGoodsRel tLineGoodsRel  = tOrderInfoMapper.selectLineGoodsRelByOrderCode(info.getCode());
                TLineInfo tLineInfo = lineService.selectById(tLineGoodsRel.getLineId());
                TOrderInfoVO vo = new TOrderInfoVO();
                BeanUtils.copyProperties(info,vo);
                if(null != endUserAuditInfo &&
                        StringUtils.isNotBlank(endUserAuditInfo.getIdcardStatus())){
                    vo.setDriverStatus(endUserAuditInfo.getIdcardStatus());
                }else{
                    vo.setDriverStatus(teu.getAuditStatus());
                }
                //查询投保表信息
                if(null != info.getOrderBusinessCode()){
                    TOrderInsurance orderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(info.getOrderBusinessCode());
                    if(null != orderInsurance && null != orderInsurance.getInsure() && null != orderInsurance.getInsuranceCancellation()){
                        vo.setInsure(orderInsurance.getInsure());
                        vo.setInsuranceCancellation(orderInsurance.getInsuranceCancellation());
                    }
                }
                vo.setCarStatus(tc.getAuditStatus());
                vo.setFromName(tLineInfo.getFromName());
                vo.setEndName(tLineInfo.getEndName());
                // 货源是否签署合同
                ResultUtil selectGoodsSourceBySourceCode = goodsSourceAPI.selectGoodsSourceBySourceCode(info.getGoodSourceCode());
                CompanySourceDTO companySourceDTO = null;
                if (CodeEnum.SUCCESS.getCode().equals(selectGoodsSourceBySourceCode.getCode())) {
                    companySourceDTO = JSONUtil.toBean(JSONUtil.toJsonStr(selectGoodsSourceBySourceCode.getData()), CompanySourceDTO.class);
                    if (null == companySourceDTO || null == companySourceDTO.getSignContract() || !companySourceDTO.getSignContract()) {
                        vo.setSignContract(false);
                    } else {
                        vo.setSignContract(true);
                    }
                }
                TGoodsSourceInfo goodsSourceInfo = goodsSourceAPI.getDataByGoodsSourceCode(info.getGoodSourceCode());
                if(null != goodsSourceInfo){
                    //货源设置为“按趟投保（司机自主选择）“时，展示货运险信息，货运险后展示复选框
                    //货源设置为“按趟投保（必须投保）“时，展示货运险信息，货运险后不展示复选框
                    if(null != goodsSourceInfo.getInsuranceMethod() && StringUtils.isNotBlank(goodsSourceInfo.getInsuranceMethod())){
                        if(InsuranceMethodsEnum.INDEPENDENTCHOICE.getKey().equals(goodsSourceInfo.getInsuranceMethod())){
                            vo.setInsuranceMethod(InsuranceMethodsEnum.INDEPENDENTCHOICE.getKey());//按趟投保（司机自主选择）
                        }else if(InsuranceMethodsEnum.MUSTBEINSURED.getKey().equals(goodsSourceInfo.getInsuranceMethod())){
                            vo.setInsuranceMethod(InsuranceMethodsEnum.MUSTBEINSURED.getKey());//按趟投保（必须投保）
                        }else{
                            vo.setInsuranceMethod(InsuranceMethodsEnum.NOTINSURED.getKey());//不投保
                        }
                    }else{
                        vo.setInsuranceMethod(InsuranceMethodsEnum.NOTINSURED.getKey());//不投保
                    }
                    if(null != goodsSourceInfo.getInsuranceMethod() &&
                            !InsuranceMethodsEnum.NOTINSURED.getKey().equals(goodsSourceInfo.getInsuranceMethod())){
                        if(null != goodsSourceInfo.getInsuredGoodsType()){
                            vo.setInsuredGoodsType(goodsSourceInfo.getInsuredGoodsType());//投保货物类型
                            SysParam paramByKey = sysParamAPI.getParamByKey(goodsSourceInfo.getInsuredGoodsType());
                            if(null != paramByKey && StringUtils.isNotBlank(paramByKey.getParamValue())){
                                vo.setRate(new BigDecimal(paramByKey.getParamValue()));
                            }
                        }else{
                            //如果货物类型不存在，则默认给煤的费率
                            SysParam paramByKey = sysParamAPI.getParamByKey(InsuredGoodsTypesEnum.COAL.getKey());
                            if(null != paramByKey && StringUtils.isNotBlank(paramByKey.getParamValue())){
                                vo.setRate(new BigDecimal(paramByKey.getParamValue()));
                            }
                        }
                    }
                    if(null != goodsSourceInfo.getGoodsUnitPrice()){
                        vo.setGoodsUnitPrice(goodsSourceInfo.getGoodsUnitPrice());
                    }
                    SysParam paramByKey = sysParamAPI.getParamByKey("PICC_FREIGHT_INSURANCE");
                    if(null != paramByKey && StringUtils.isNotBlank(paramByKey.getParamValue())){
                        vo.setPiccFreightInsurance(new BigDecimal(paramByKey.getParamValue()));//人保货运险最低金额
                    }
                }
                //按车牌号查询车辆投保表，查询是否审核通过，如果通过，则前端不用显示运单投保信息
                TCarInsuranceVO carInsurance = carInsuranceMapper.selectByVehicleNumber(tc.getVehicleNumber());
                if(BeanUtil.isNotEmpty(carInsurance)){
                    if(StringUtils.isNotBlank(carInsurance.getAuditStatus()) &&
                            DictEnum.PASSNODE.code.equals(carInsurance.getAuditStatus())){
                        vo.setCarInsuranceStatus(DictEnum.PASSNODE.code);
                    }
                }
                //根据运单ID查询运单详细表，获取运费单价单位和固定扣款
                TOrderInfoDetail orderInfoDetail = orderInfoDetailMapper.selectByOrderId(info.getId());
                if(null == orderInfoDetail.getCarriagePriceUnit()){
                    vo.setCarriagePriceUnit("DUN");
                }else{
                    vo.setCarriagePriceUnit(orderInfoDetail.getCarriagePriceUnit());
                }
                //除去运费单位为吨时，获取固定扣款
                if(null != companySourceDTO &&
                        !DictEnum.CARRIAGE_PRICE_UNIT_DUN.code.equals(orderInfoDetail.getCarriagePriceUnit())){
                    vo.setFixCutFee(companySourceDTO.getFixCutFee());
                }
                if(null != orderInfoDetail.getCarriagePriceUnit() &&
                        DictEnum.CARRIAGE_PRICE_UNIT_BOX.code.equals(orderInfoDetail.getCarriagePriceUnit())){
                    TOrderInfoWeight tOrderInfoWeight = orderInfoWeightMapper.selectByOrderId(info.getId());
                    vo.setOrderInfoWeight(tOrderInfoWeight);
                }

                //车队长信息
                TEndUserInfo captain = new TEndUserInfo();
                TOrderCastChanges tOrderCastChanges = orderCastChangesMapper.selectByNewOne(info.getCode());
                if(DictEnum.PAYTOCAPTAIN.code.equals(tOrderCastChanges.getCapitalTransferType())){
                    if(null!=info.getEndCarOwnerId() && !"".equals(info.getEndCarOwnerId())){
                        ResultUtil resuCap = tEndSUserInfoAPI.selectById(info.getEndCarOwnerId());
                        captain =  JSON.parseObject(JSONObject.toJSON(resuCap.getData()).toString(),TEndUserInfo.class);
                        vo.setCaptainId(captain.getId());
                        vo.setCaptainName(captain.getRealName());
                        vo.setCaptainPhone(captain.getPhone());
                        vo.setCaptainIdcard(captain.getIdcard());
                    }
                }
                vo.setCapitalTransferType(tOrderCastChanges.getCapitalTransferType());
                //企业名称
                vo.setCompanyName(companyInfo.getCompanyName());
                //司机名称
                vo.setRealName(teu.getRealName());
                //司机身份证号
                vo.setIdcard(teu.getIdcard());
                //司机电话
                vo.setRealPhone(teu.getPhone());
                //承运方名称
                vo.setCarrierName(tCarrierInfo.getCarrierName());
                vo.setCarrierId(tCarrierInfo.getId());
                // 车牌号
                vo.setVehicleNumber(tc.getVehicleNumber());
                vo.setIsDisplay(tLineGoodsRel.getFreightPrice()?null:"1");
                SysParam paramByKey = sysParamAPI.getParamByKey("APPUOLOADEDATACARRIERID");
                vo.setUploadCarrierId(paramByKey.getParamValue());
                vo.setFromCoordinates(tLineInfo.getFromCoordinates());
                vo.setEndCoordinates(tLineInfo.getEndCoordinates());
                vo.setCityFromCode(tLineInfo.getCityFromCode());
                vo.setCityEndCode(tLineInfo.getCityEndCode());

                //司机电子印章
                TNetSignOpenAccount netSignOpenAccount = new TNetSignOpenAccount();
                netSignOpenAccount.setUserId(info.getEndDriverId());
                netSignOpenAccount.setUserType(DictEnum.CD.code);
                TNetSignOpenAccount tNetSignOpenAccount = netSignOpenAccountAPI.selectNetSignOpenAccount(netSignOpenAccount);
                if (null != tNetSignOpenAccount) {
                    if (tNetSignOpenAccount.getAuthStatus() == 0) {
                        // 验证码未验证
                        vo.setEndDriverSignStatus("3");
                        vo.setEndDriverSignSerialNo(tNetSignOpenAccount.getSerialNo());
                    } else if (tNetSignOpenAccount.getAuthStatus() == 1 && (null == tNetSignOpenAccount.getSealNo() || StringUtils.isBlank(tNetSignOpenAccount.getSealNo()))) {
                        // 实名认证，但未注册
                        vo.setEndDriverSignStatusStr("4");
                    } else if (null != tNetSignOpenAccount.getSealImage() && StringUtils.isNotBlank(tNetSignOpenAccount.getSealImage())) {
                        vo.setEndDriverSealImage(tNetSignOpenAccount.getSealImage());
                        vo.setEndDriverSignStatus("0");
                    } else {
                        vo.setEndDriverSignStatus("2");
                    }
                    vo.setEndDriverSignStatusStr("已注册");
                    vo.setSignUserId(tNetSignOpenAccount.getSealNo());
                } else {
                    vo.setEndDriverSignStatusStr("未注册");
                    vo.setEndDriverSignStatus("1");
                }
                ResultUtil resultEndUser = tEndSUserInfoAPI.getEndUserInfo();
                TEndUserDTO endUserDTO =  JSON.parseObject(JSONObject.toJSON(resultEndUser.getData()).toString(), TEndUserDTO.class);
                vo.setIdcardStatus(endUserDTO.getIdcardStatus());

                //业务部其他信息
                if(info.getEndAgentId()!= null && !"".equals(info.getEndAgentId())){
                    log.info("业务部id:"+info.getEndAgentId());
                    ResultUtil ru = tEndSUserInfoAPI.selectById(info.getEndAgentId());
                    TEndUserInfo tu = JSON.parseObject(JSONObject.toJSON(ru.getData()).toString(),TEndUserInfo.class);
                    log.info("业务部名称:"+tu.getRealName());
                    //用户组织名称
                    vo.setShortName(tu.getRealName());
                    vo.setShortPhone(tu.getPhone());
                }
                resultUtil.setCode("success");
                resultUtil.setData(vo);
            }
            return  resultUtil;
        }
    }


    /**
     * 装货地签到
     * @param orderInfoVO
     * @return
     */
    @Override
    @LcnTransaction
    @Transactional
    public ResultUtil loadingSingIn(TOrderInfoVO orderInfoVO) {
        TOrderInfo orderInfo = tOrderInfoMapper.selectByPrimaryKey(orderInfoVO.getId());
        try{
            log.info("-------------------------------------操作位置：{},{}",orderInfoVO.getOperateGeographyPosition(),JSONObject.toJSONString(orderInfoVO));
            // 如果是货源大厅的运单，修改货源库存
            boolean resourceIfHall = orderGoodsSourceInfoMapper.selectResourceIfHall(orderInfo.getLineGoodsRelId());
            if (resourceIfHall) {
                if (null != orderInfoVO.getDeliverWeightNotesWeight()&&0!=orderInfoVO.getDeliverWeightNotesWeight()) {
                    // 判断是否相等
                    if (orderInfo.getPrimaryWeight().compareTo(orderInfoVO.getDeliverWeightNotesWeight()) != 0) {
                        // 修改货源库存
                        TGoodsSourceInfo goodsSourceInfo = orderGoodsSourceInfoMapper.selectByByLineGoodsRelId(orderInfo.getLineGoodsRelId());
                        TGoodsSourceInfo goodsSourceInfoForUpdate = new TGoodsSourceInfo();
                        goodsSourceInfoForUpdate.setId(goodsSourceInfo.getId());
                        goodsSourceInfoForUpdate.setEstimateGoodsWeight(goodsSourceInfo.getEstimateGoodsWeight() + orderInfo.getPrimaryWeight());
                        goodsSourceInfoForUpdate.setEstimateGoodsWeight(goodsSourceInfoForUpdate.getEstimateGoodsWeight() - orderInfoVO.getDeliverWeightNotesWeight());
                        if (goodsSourceInfoForUpdate.getEstimateGoodsWeight().compareTo(0d) < 0) {
                            return ResultUtil.error("货源库存不足");
                        } else if (goodsSourceInfoForUpdate.getEstimateGoodsWeight().compareTo(0d) == 0) {
                            goodsSourceInfoForUpdate.setStatus(DictEnum.CLOSESOURCE.code);
                        }
                        goodsSourceInfoForUpdate.setUpdateTime(new Date());
                        orderGoodsSourceInfoMapper.updateEstimateGoodsWeight(goodsSourceInfoForUpdate);
                    }
                }
            }

            ResultUtil resultUtil = new ResultUtil();
            ResultUtil selectGoodsSourceBySourceCode = goodsSourceAPI.selectGoodsSourceBySourceCode(orderInfo.getGoodSourceCode());
            CompanySourceDTO companySourceDTO = null;
            if (CodeEnum.SUCCESS.getCode().equals(selectGoodsSourceBySourceCode.getCode())) {
                companySourceDTO = JSONUtil.toBean(JSONUtil.toJsonStr(selectGoodsSourceBySourceCode.getData()), CompanySourceDTO.class);
            }
            //TLineGoodsRel tLineGoodsRel = linkGoodsRelAPI.selectById(orderInfo.getLineGoodsRelId());
            TOrderCastChanges tOrderCastChanges = orderCastChangesMapper.selectByNewOne(orderInfo.getCode());
            if( DictEnum.CCAPTAIN.code.equals(orderInfoVO.getRoleType())){
                resultUtil.setCode("error");
                resultUtil.setMsg("车队长身份不可以签到");
                return resultUtil;
            }
            //是否签到 0签到  1取消
            if("0".equals(orderInfoVO.getIfSignIn())) {
                if(DictEnum.M20.code.equals(orderInfo.getOrderExecuteStatus())){
                    resultUtil.setCode("error");
                    resultUtil.setMsg("此运单已删单不允许签到！");
                    return resultUtil;
                }

                if(DictEnum.M10.code.equals(orderInfo.getOrderExecuteStatus())){
                    resultUtil.setCode("error");
                    resultUtil.setMsg("此运单已取消不允许签到！");
                    return resultUtil;
                }
                if(DictEnum.M030.code.equals(orderInfo.getOrderExecuteStatus())){
                    resultUtil.setCode("error");
                    resultUtil.setMsg("此运单已签到！");
                    return resultUtil;
                }
                if(!DictEnum.M000.code.equals(orderInfo.getOrderExecuteStatus())&&!DictEnum.M010.code.equals(orderInfo.getOrderExecuteStatus())
                        &&!DictEnum.M020.code.equals(orderInfo.getOrderExecuteStatus())&&!DictEnum.M011.code.equals(orderInfo.getOrderExecuteStatus())){
                    resultUtil.setCode("error");
                    resultUtil.setMsg("此运单不允许装货地签到！");
                    return resultUtil;
                }
                orderInfo.setDeliverWeightNotesPhoto(orderInfoVO.getDeliverWeightNotesPhoto());
                orderInfo.setDeliverWeightNotesWeight(orderInfoVO.getDeliverWeightNotesWeight());
                orderInfo.setDeliverWeightNotesTime(orderInfoVO.getDeliverWeightNotesTime());
                if(null!=orderInfoVO.getGrossWeight() && !"".equals(orderInfoVO.getGrossWeight())){
                    orderInfo.setGrossWeight(orderInfoVO.getGrossWeight());
                }
                orderInfo.setOrderExecuteStatus(DictEnum.M030.code);//运单状态 已装货
                if (null!= orderInfoVO.getUploadDatas() && "1".equals(orderInfoVO.getUploadDatas())){
                    orderInfo.setUploadData(true);
                }
                if(DictEnum.PAYTOCAPTAIN.code.equals(orderInfoVO.getCapitalTransferType())){
                    if(null == orderInfo.getEndCarOwnerId() || "".equals(orderInfo.getEndCarOwnerId())){
                        if(orderInfoVO.getCaptainId() ==null ||"".equals(orderInfoVO.getCaptainId())){
                            resultUtil.setCode("error");
                            resultUtil.setMsg("请选择车队长");
                            return resultUtil;
                        }else {
                            orderInfo.setEndCarOwnerId(orderInfoVO.getCaptainId());
                        }
                    }
                }


                //运费单价单位为“元/箱”时，将信息保存到t_order_info_weight表
                if(null != orderInfoVO.getCarriagePriceUnit() &&
                        DictEnum.CARRIAGE_PRICE_UNIT_BOX.code.equals(orderInfoVO.getCarriagePriceUnit())){
                    TOrderInfoWeight torderInfoweight = orderInfoVO.getOrderInfoWeight();
                    torderInfoweight.setOrderId(orderInfo.getId());
                    TOrderInfoWeight weight = orderInfoWeightMapper.selectByPrimaryKey(Long.valueOf(torderInfoweight.getId()));
                    if(null == torderInfoweight.getBoxNum()){
                        torderInfoweight.setBoxNum(weight.getBoxNum());
                    }
                    if(weight.getBoxNum()>0){
                        if(null != weight.getPrimaryWeight1() && null == torderInfoweight.getPrimaryWeight1()){
                            torderInfoweight.setPrimaryWeight1(weight.getPrimaryWeight1());//原发重量1
                        }
                        if(null != weight.getPrimaryWeight2() && null == torderInfoweight.getPrimaryWeight2()){
                            torderInfoweight.setPrimaryWeight2(weight.getPrimaryWeight2());//原发重量1
                        }
                        if(null != weight.getCarriageUnitPrice1() && null == torderInfoweight.getCarriageUnitPrice1()){
                            torderInfoweight.setCarriageUnitPrice1(weight.getCarriageUnitPrice1());//运费单价1
                        }
                        if(null != weight.getCarriageUnitPrice2() && null == torderInfoweight.getCarriageUnitPrice2()){
                            torderInfoweight.setCarriageUnitPrice2(weight.getCarriageUnitPrice2());//运费单价1
                        }
                        //如果是两箱时装货重量取两箱之和
                        if(null != torderInfoweight.getDeliverWeightNotesWeight1() && null != torderInfoweight.getDeliverWeightNotesWeight2()){
                            orderInfo.setDeliverWeightNotesWeight(torderInfoweight.getDeliverWeightNotesWeight1().add(torderInfoweight.getDeliverWeightNotesWeight2()).doubleValue());
                        } else if (null != torderInfoweight.getDeliverWeightNotesWeight1()) {
                            orderInfo.setDeliverWeightNotesWeight(torderInfoweight.getDeliverWeightNotesWeight1().doubleValue());
                        } else if (null != torderInfoweight.getDeliverWeightNotesWeight2()) {
                            orderInfo.setDeliverWeightNotesWeight(torderInfoweight.getDeliverWeightNotesWeight2().doubleValue());
                        }
                        //装货磅单时间，取第一箱的时间
                        if(null != torderInfoweight.getDeliverWeightNotesTime1() && null != torderInfoweight.getDeliverWeightNotesTime2()){
                            orderInfo.setDeliverWeightNotesTime(torderInfoweight.getDeliverWeightNotesTime1());
                        } else if(null != torderInfoweight.getDeliverWeightNotesTime1()){
                            orderInfo.setDeliverWeightNotesTime(torderInfoweight.getDeliverWeightNotesTime1());
                        } else if (null != torderInfoweight.getDeliverWeightNotesTime2()) {
                            orderInfo.setDeliverWeightNotesTime(torderInfoweight.getDeliverWeightNotesTime2());
                        }
                    }
                    orderInfoWeightMapper.updateByPrimaryKey(torderInfoweight);
                }

                //修改状态
                tOrderInfoMapper.updateByPrimaryKeySelective(orderInfo);
                //运单状态运行子表
                //新增状态表当前车辆状态
                TOrderState orderState = new TOrderState();
                orderState.setCode(IdWorkerUtil.getInstance().nextId());
                orderState.setOrderCode(orderInfo.getCode());
                orderState.setOperateTime(new Date());
                orderState.setOperateMethod("WX");
                orderState.setOperatorId(CurrentUser.getCurrentUserID());
                orderState.setOperateGeographyPosition(orderInfoVO.getOperateGeographyPosition());
                orderState.setStateNodeValue("S0302");//司机确认装货
                orderState.setIfExpire(false);
                orderState.setCreateUser(CurrentUser.getUserNickname());
                orderState.setCreateTime(new Date());
                orderState.setEnable(false);
                TOrderStateExample example = new TOrderStateExample();
                TOrderStateExample.Criteria cr = example.createCriteria();
                cr.andOrderCodeEqualTo(orderInfo.getCode());
                cr.andStateNodeValueEqualTo("S0302");
                List<TOrderState> tOrderStateList = tOrderStateMapper.selectByExample(example);
                if(tOrderStateList.size()<1){
                    tOrderStateMapper.insertSelective(orderState);
                }

                TOrderInfoDetail tOrderInfoDetail = tOrderInfoDetailMapper.selectByOrderId(orderInfo.getId());
                tOrderInfoDetail.setLoadingCarPhoto(orderInfoVO.getLoadingCarPhoto());
                tOrderInfoDetail.setLoadingCarPhotoInfo(orderInfoVO.getLoadingCarPhotoInfo());
                tOrderInfoDetailMapper.updateByPrimaryKeySelective(tOrderInfoDetail);

                TEndCarInfo tEndCarInfo = tEndCarInfoAPI.selectByPrimaryKey(orderInfo.getVehicleId());
                //根据车牌号获取车辆轨迹
                TransTimeManageVResp resultData = trajectoryRecentAPI.transTimeManageV3One(tEndCarInfo.getVehicleNumber()+"_"+tEndCarInfo.getLicensePlateColor(),1);
                //根据运单表信息拿到线路id查询线路信息
                TLineInfo tLineInfo = lineService.selectById(orderInfo.getLineId());
                //log.info("线路参数："+JSONObject.toJSONString(tLineInfo));
                String jd = tLineInfo.getFromCoordinates().split(",")[0];//经度
                String wd = tLineInfo.getFromCoordinates().split(",")[1];//纬度

                Double fencingIdentifiedDistance = (Double) tLineInfo.getFencingIdentifiedDistance();//电子围栏识别距离

                //电子围栏距离为空时 查询系统参数配置表
                if(fencingIdentifiedDistance==null || "".equals(fencingIdentifiedDistance)){
                    SysParam sysParam = sysParamAPI.getParamByKey("fencingIdentifiedDistance");
                    if(sysParam!=null &&!"".equals(sysParam)){
                        fencingIdentifiedDistance = Double.parseDouble(sysParam.getParamValue());
                    }
                }

                //根据两组经纬度计算距离 （km）
                Point2D pointDD = null;
                Point2D pointXD = null;
                if((resultData.getLon()!=null&& !"".equals(resultData.getLon())) &&(resultData.getLat())!=null&& !"".equals(resultData.getLat())){
                    pointDD = new Point2D.Double(Double.parseDouble(jd), Double.parseDouble(wd));

                    pointXD = new Point2D.Double(Double.parseDouble(String.valueOf(resultData.getLon()))/600000, Double.parseDouble(String.valueOf(resultData.getLat()))/600000);
                }
                Double distance = 0.0d;
                //距离
                if(pointDD!=null&&pointXD !=null){
                    distance = ElectronicFence.getDistance(pointDD, pointXD);
                }
                //车辆轨迹
                TrajectoryMongo trajectoryMongo = new TrajectoryMongo();
                trajectoryMongo.setId(IdWorkerUtil.getInstance().nextId());
                TOrderInfo tOrderInfoEdit = new TOrderInfo();
                tOrderInfoEdit.setId(orderInfo.getId());
                if (distance < fencingIdentifiedDistance) {
                    trajectoryMongo.setVehicleStatus("2");//车辆状态
                      /*  if(null!=orderInfo.getVehicleGpsBdStatus()&&!"".equals(orderInfo.getVehicleGpsBdStatus())){
                            if(orderInfo.getVehicleGpsBdStatus().equals(DictEnum.AUTOFETCHGPS.code)){
                                tOrderInfoEdit.setVehicleGpsBdStatus(DictEnum.NORMALFETCHGPS.code);
                            }else{
                                tOrderInfoEdit.setVehicleGpsBdStatus(DictEnum.NORMALFETCHGPS.code);
                            }
                        }else{
                            tOrderInfoEdit.setVehicleGpsBdStatus(DictEnum.NORMALFETCHGPS.code);
                        }*/
                    if (null!=companySourceDTO){
                        if (companySourceDTO.getQueryTrajectory()){
                            tOrderInfoEdit.setVehicleGpsBdStatus(DictEnum.NORMALFETCHGPS.code);
                        }
                    }

                } else {
                    trajectoryMongo.setVehicleStatus("1");//车辆状态
                    tOrderInfoEdit.setVehicleGpsBdStatus(DictEnum.NORMALFETCHGPS.code);
                }
                tOrderInfoMapper.updateByPrimaryKeySelective(tOrderInfoEdit);
                if("1001".equals(resultData.getStatus())) {
                    trajectoryMongo.setTrajectoryReceiveTime(DateUtils.parseDate(resultData.getUtc()));
                    trajectoryMongo.setLongitude(String.valueOf(Double.parseDouble(resultData.getLon())/600000));//经度
                    trajectoryMongo.setLatitudes(String.valueOf(Double.parseDouble(resultData.getLat())/600000));//纬度
                    trajectoryMongo.setVehicleGeographyPosition(resultData.getAdr());//车辆地理位置信息
                    trajectoryMongo.setVehicleCurrentSpeed(resultData.getSpd());//车辆当前速度
                    trajectoryMongo.setVehicleCurrentDirection(resultData.getDrc());//车辆行驶方向
                    trajectoryMongo.setProvince(resultData.getProvince());
                    trajectoryMongo.setCity(resultData.getCity());
                    trajectoryMongo.setCountry(resultData.getCountry());
                }
                trajectoryMongo.setVehicleId(orderInfo.getVehicleId());//车辆id
                trajectoryMongo.setOrderCode(orderInfo.getCode()); //运单id
                trajectoryMongo.setTrajectoryCode(IdWorkerUtil.getInstance().nextId());
                trajectoryMongo.setVehiclePlateNo(orderInfoVO.getVehicleNumber());
                trajectoryMongo.setTrajectoryReceiveMethod("DRIVERLOADSIGN");//装货地签到
                trajectoryMongo.setTrajectoryReceiveTime(new Date());
                if("0".equals(orderInfoVO.getCarStatus())){
                    TOrderAbnormal tOrderAbnormal = new TOrderAbnormal();
                    tOrderAbnormal.setCode(IdWorkerUtil.getInstance().nextId());
                    tOrderAbnormal.setOrderCode(orderInfo.getCode());
                    tOrderAbnormal.setAbnormalType("QIANDAOYC");//签到异常
                    tOrderAbnormal.setParam1("ZH");
                    tOrderAbnormal.setOperateMethod("WX");
                    tOrderAbnormal.setEnable(false);
                    tOrderAbnormal.setAbnormalDescription(orderInfoVO.getErrorMemo());//异常内容
                    tOrderAbnormal.setOperateGeographyPosition(orderInfoVO.getOperateGeographyPosition());//操作地理位置
                    tOrderAbnormalMapper.insertSelective(tOrderAbnormal);
                    trajectoryMongo.setDataEnable(false);//行数据可用性
                }else{
                    trajectoryMongo.setDataEnable(true);//行数据可用性
                }
                //插入运单轨迹表
                trajectoryMongoDao.insert(trajectoryMongo);

                //车辆轨迹中间表
                TOrderCarlocation tocl = new TOrderCarlocation();
                BeanUtils.copyProperties(trajectoryMongo,tocl);
                //插入车辆轨迹中间表
                TOrderCarlocationExample tOrderCarlocationExample = new TOrderCarlocationExample();
                TOrderCarlocationExample.Criteria cre = tOrderCarlocationExample.createCriteria();
                cre.andOrderCodeEqualTo(tocl.getOrderCode());
                cre.andEnableEqualTo(false);
                List<TOrderCarlocation> tOrderCarlocationList = tOrderCarlocationMapper.selectByExample(tOrderCarlocationExample);
                if(tOrderCarlocationList.size()<1){
                    MQMessage mqMessage = new MQMessage();
                    mqMessage.setTopic(MqMessageTopic.ORDERCARLOCATION);
                    mqMessage.setTag(MqMessageTag.INSERT);
                    mqMessage.setBody(tocl);
                    mqMessage.setKey(IdWorkerUtil.getInstance().nextId());
                    mqAPI.sendMessage(mqMessage);
                }else{
                    MQMessage mqMessage = new MQMessage();
                    mqMessage.setTopic(MqMessageTopic.ORDERCARLOCATION);
                    mqMessage.setTag(MqMessageTag.UPDATE);
                    mqMessage.setBody(tocl);
                    mqMessage.setKey(IdWorkerUtil.getInstance().nextId());
                    mqAPI.sendMessage(mqMessage);
                }

                //redisUtil.set("big-"+"GJ-"+tovt.getOrderCode()+"-"+tovt.getProvince()+"-"+tovt.getCity()+"-"+tovt.getCountry(),orderInfo.getCarrierId());
                resultUtil.setCode("success");
                resultUtil.setMsg("装货地签到成功！");
                //想易煤网发送信息
                tOrderInfoService.sendOrderInfoToYimei(orderInfo.getOrderBusinessCode());

                //etc备案
                tOrderInfoService.sendEtcData(orderInfo.getOrderBusinessCode());
                if(null == orderInfoVO.getInsuranceMethod()){
                    if (null != companySourceDTO && null != companySourceDTO.getInsuranceMethod()) {
                        orderInfoVO.setInsuranceMethod(companySourceDTO.getInsuranceMethod());
                    }
                }
                saveInsurance(orderInfo,orderInfoVO,companySourceDTO);
                // 创建随意签承运合同
                if (!DictEnum.YZID.code.equals(orderInfo.getContractStatus())) {
                    // 判断货源是否签署合同
                    if (null != companySourceDTO && (null == companySourceDTO.getSignContract() || companySourceDTO.getSignContract())) {
                        orderContractService.createAnySignCyht(orderInfo.getCode(), false);
                    }
                }

                //发货到黄骅海通的货源数据跟对三分仓储对接
                SysParam sysParam = sysParamAPI.getParamByKey("HHCCKEY");
                if(sysParam.getParamValue().contains(orderInfo.getGoodSourceCode())){
                    TEndUserInfo tEndUserInfo = tEndSUserInfoAPI.selectByPrimaryKey(orderInfo.getEndDriverId());
                    addTaskHhcc(orderInfo,tEndUserInfo,tEndCarInfo,1,"zhd");
                }
                //上传磅房运单信息 判断当前货源是否推送
                TGoodsSourceDispose tGoodsSourceDispose = goodsSourceDisposeAPI.selectByGoodsSourceCode(orderInfo.getGoodSourceCode());
                if(null != tGoodsSourceDispose && !"".equals(tGoodsSourceDispose)){
                    if(tGoodsSourceDispose.getIsWeigh()){
                        poundRoomService.bangfangOrderReportTask(orderInfo);
                    }
                }

                //新增状态表当前车辆状态 司机状态
                CarDriverRelVO carDriverRelVO = new CarDriverRelVO();
                carDriverRelVO.setCode(orderInfo.getCode());
                carDriverRelVO.setBizCode(orderInfo.getOrderBusinessCode());
                carDriverRelVO.setUserStatus(DictEnum.NOTCHECKED.code);
                carDriverRelVO.setCarStatus(DictEnum.ONROADCHECKED.code);
                appCommonAPI.updateCarEnduserStatus(carDriverRelVO);

            }else{
                if (orderInfo.getOrderExecuteStatus().equals(DictEnum.M010.code)
                        || orderInfo.getOrderExecuteStatus().equals(DictEnum.M020.code)
                        || orderInfo.getOrderExecuteStatus().equals(DictEnum.M030.code)) {
                    // 查询是否预付款已支付、已提现
                    TAdvanceOrderTmp tAdvanceOrderTmp = advanceOrderTmpService.selectAdvanceOrderTempByOrderCode(orderInfo.getCode());
                    if (null != tAdvanceOrderTmp) {
                        if (DictEnum.P070.code.equals(tAdvanceOrderTmp.getOrderPayStatus())) {
                            return ResultUtil.error("预付款运单正在支付中，不能取消！");
                        }
                        if (DictEnum.M090.code.equals(tAdvanceOrderTmp.getOrderPayStatus())) {
                            return ResultUtil.error("预付款运单已支付，不能取消！");
                        }
                        if (DictEnum.M130.code.equals(tAdvanceOrderTmp.getOrderPayStatus())) {
                            return ResultUtil.error("预付款运单已提现，不能取消！");
                        }
                    }
                    //取消解冻金额
                    Payment payment=new Payment();
                    payment.cancelOrder(orderInfo);

                    orderInfo.setOrderExecuteStatus(DictEnum.M10.code);//运单状态 已取消
                    //修改状态
                    tOrderInfoMapper.updateByPrimaryKeySelective(orderInfo);

                    //运单状态运行子表
                    //新增状态表当前车辆状态
                    TOrderState orderState = new TOrderState();
                    orderState.setCode(IdWorkerUtil.getInstance().nextId());
                    orderState.setOrderCode(orderInfo.getCode());
                    orderState.setOperateGeographyPosition(orderInfoVO.getOperateGeographyPosition());
                    orderState.setOperateTime(new Date());
                    orderState.setOperateMethod("WX");
                    orderState.setOperatorId(CurrentUser.getCurrentUserID());
                    orderState.setStateNodeValue("S-104");//司机已取消
                    orderState.setIfExpire(false);
                    orderState.setCreateUser(CurrentUser.getUserNickname());
                    orderState.setCreateTime(new Date());
                    orderState.setEnable(false);
                    TOrderStateExample example = new TOrderStateExample();
                    TOrderStateExample.Criteria cr = example.createCriteria();
                    cr.andOrderCodeEqualTo(orderInfo.getCode());
                    cr.andStateNodeValueEqualTo("S-104");
                    List<TOrderState> tOrderStateList = tOrderStateMapper.selectByExample(example);
                    if(tOrderStateList.size()<1){
                        tOrderStateMapper.insertSelective(orderState);
                    }

                    //新增状态表当前车辆状态 司机状态
                    CarDriverRelVO carDriverRelVO = new CarDriverRelVO();
                    carDriverRelVO.setCode(orderInfo.getCode());
                    carDriverRelVO.setBizCode(orderInfo.getOrderBusinessCode());
                    carDriverRelVO.setUserStatus(DictEnum.DISTRIBUTIONCANCLE.code);//已取消
                    carDriverRelVO.setCarStatus(DictEnum.AVAILABLECANCLE.code);//已取消
                    appCommonAPI.updateCarEnduserStatus(carDriverRelVO);

                    resultUtil.setCode("success");
                    resultUtil.setMsg("运单取消成功！");
                    //想易煤网发送信息
                    tOrderInfoService.sendOrderInfoToYimei(orderInfo.getOrderBusinessCode());

                    TOrderInfoDetail tOrderInfoDetail = tOrderInfoDetailMapper.selectByOrderId(orderInfo.getId());
                    tOrderInfoDetail.setLoadingCarPhoto(orderInfoVO.getLoadingCarPhoto());
                    tOrderInfoDetail.setLoadingCarPhotoInfo(orderInfoVO.getLoadingCarPhotoInfo());
                    tOrderInfoDetailMapper.updateByPrimaryKeySelective(tOrderInfoDetail);
                } else {
                    return ResultUtil.error("当前运单不可取消");
                }
            }
            return resultUtil;
        }catch (Exception e){
            log.error("装货地签到失败！", e);
            throw new RuntimeException("装货地签到失败！");
        }
    }

    private void saveInsurance(TOrderInfo orderInfo, TOrderInfoVO orderInfoVO, CompanySourceDTO companySourceDTO){
        TEndCarInfo tEndCarInfo = tEndCarInfoAPI.selectByPrimaryKey(orderInfo.getVehicleId());
        // 判断投保方式，不为空，则进入下个判断，
        // 如果自主选择状态为true或者投保方式为必需投保，
        // 则调用投保接口，生成投保信息。其余情况则不调用接口，只生成投保信息
        TOrderInsurance orderInsurance = new TOrderInsurance();
        List<String> errorList = new ArrayList<>();
        orderInsurance.setInsure(0);
        //20250510 如果是车队长模式，则获取不需要投保的数据，查询 t_sys_param
        boolean isCaptian = false;//是否是 车队长模式并且是不需要投保的
        if (null != companySourceDTO.getCapitalTransferType() && DictEnum.PAYTOCAPTAIN.code.equals(companySourceDTO.getCapitalTransferType())) {
            SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.NOINSURANCE.code);
            if(null != paramByKey){
                String[] split = paramByKey.getParamValue().split(",");
                if(split.length > 0){
                    List<String> list = Arrays.asList(split);
                    if (null != orderInfoVO.getCaptainId()) {
                        if(list.contains(orderInfoVO.getCaptainId().toString())){
                            isCaptian = true;
                            TEndUserInfo tEndUserInfo = tEndSUserInfoAPI.selectByPrimaryKey(orderInfoVO.getCaptainId());
                            errorList.add("车队长"+tEndUserInfo.getRealName()+"不投保");
                        }
                    }
                }
            }
        }
        if(null == orderInfoVO.getInsuranceMethod()){
            if (null != companySourceDTO.getInsuranceMethod()) {
                orderInfoVO.setInsuranceMethod(companySourceDTO.getInsuranceMethod());
            }else{
                orderInfoVO.setInsuranceMethod(InsuranceMethodsEnum.NOTINSURED.getKey());
            }
        }
        TCarInsuranceVO carInsurance = carInsuranceMapper.selectByVehicleNumber(tEndCarInfo.getVehicleNumber());
        boolean carInsuranceStatus = true;
        orderInsurance.setInsuranceMethod(orderInfoVO.getInsuranceMethod());
        if(!isCaptian){
            if(!InsuranceMethodsEnum.NOTINSURED.getKey().equals(orderInfoVO.getInsuranceMethod())){
                if(BeanUtil.isNotEmpty(carInsurance) && null != carInsurance.getAuditStatus() && DictEnum.PASSNODE.code.equals(carInsurance.getAuditStatus())){
                    errorList.add("车辆已投保");
                    carInsuranceStatus = false;
                }else if(null != orderInfoVO.getInsuranceMethod()){
                    if(orderInfoVO.getInsuranceMethod().equals(InsuranceMethodsEnum.INDEPENDENTCHOICE.getKey()) &&
                            !orderInfoVO.getAutoselect()){
                        errorList.add("司机选择不投保");
                    }
                }
                if(carInsuranceStatus){
                    if(orderInfoVO.getAutoselect() ||
                            InsuranceMethodsEnum.MUSTBEINSURED.getKey().equals(orderInfoVO.getInsuranceMethod())){
                        if(orderInfo.getDeliverWeightNotesWeight() <= 38){
                            TOrderInfo info = tOrderInfoMapper.selectByPrimaryKey(orderInfo.getId());
                            orderInsurance.setInsure(1);
                            //组装参数过程放到定时任务中
                            BeanUtils.copyProperties(info,orderInfoVO);
                            if(null != info.getLineId()){
                                TLineInfo line = lineService.selectById(info.getLineId());
                                orderInfoVO.setFromName(line.getProviceFrom()+line.getCityFrom()+line.getCountryFrom());
                                orderInfoVO.setEndName(line.getProviceEnd()+line.getCityEnd()+line.getCountryEnd());
                            }
                            if(null == orderInfoVO.getGoodsUnitPrice()){
                                if(null != companySourceDTO){
                                    if(null != companySourceDTO.getGoodsUnitPrice()){
                                        orderInfoVO.setGoodsUnitPrice(companySourceDTO.getGoodsUnitPrice());
                                    }
                                    if(null != companySourceDTO.getInsuredGoodsType()){
                                        orderInfoVO.setInsuredGoodsType(companySourceDTO.getInsuredGoodsType());
                                    }
                                }
                            }
                            //信息数据保存到任务表
                            TTask task = new TTask();
                            task.setTaskId(IdWorkerUtil.getInstance().nextId());
                            task.setTaskType(DictEnum.INSURANCE.code);
                            task.setTaskTypeNode(DictEnum.TOUBAO.code);
                            task.setBusinessType(DictEnum.TB.code);
                            task.setSourceTablename("T_ORDER_INFO");
                            task.setSourcekeyFieldname("id");
                            task.setSourceFieldname("order_business_code");
                            task.setRequestParameter(JSON.toJSONString(orderInfoVO));
                            task.setSourceFieldvalue(info.getOrderBusinessCode());
                            task.setIsSuccessed(false);
                            task.setRequestTimes(0);
                            task.setEnable(false);
                            orderTaskMapper.insert(task);
                        }else{
                            errorList.add("装货重量大于38吨");
                        }
                    }
                }
            }
        }
        orderInsurance.setOrderBusinessCode(orderInfo.getOrderBusinessCode());
        if(null == orderInfoVO.getRealName() && null == orderInfoVO.getRealPhone()){
            Integer endDriverId = orderInfo.getEndDriverId();
            TEndUserInfo tEndUserInfo = tEndSUserInfoAPI.selectByPrimaryKey(endDriverId);
            if(null != tEndUserInfo){
                orderInsurance.setDriverName(tEndUserInfo.getRealName());
                orderInsurance.setDriverPhone(tEndUserInfo.getPhone());
            }
        }else{
            orderInsurance.setDriverName(orderInfoVO.getRealName());
            orderInsurance.setDriverPhone(orderInfoVO.getRealPhone());
        }
        TLineInfo tLineInfo = lineService.selectById(orderInfo.getLineId());
        if(null == orderInfoVO.getGoodsName()){
            String sourceName = "["+orderInfo.getGoodsName()+"]"+tLineInfo.getLineName();
            orderInsurance.setGoodsName(sourceName);
        }else{
            String sourceName = "["+orderInfoVO.getGoodsName()+"]"+tLineInfo.getLineName();
            orderInsurance.setGoodsName(sourceName);
        }
        if(null ==orderInfoVO.getCompanyName()){
            if(null != orderInfo.getCompanyId()){
                TCompanyInfo tCompanyInfo = companyService.selectByCompanyIdAndAccountId(orderInfo.getCompanyId());
                if(null != tCompanyInfo){
                    orderInsurance.setCompanyName(tCompanyInfo.getCompanyName());
                }
            }
        }else{
            orderInsurance.setCompanyName(orderInfoVO.getCompanyName());
        }
        if(null != orderInfo.getCarrierId()){
            TCarrierInfo tCarrierInfo = carrierService.selectCarrierById(String.valueOf(orderInfo.getCarrierId()));
            if(null != tCarrierInfo){
                orderInsurance.setCarrierName(tCarrierInfo.getCarrierName());
            }
        }
        if(1 == orderInsurance.getInsure()){
            orderInsurance.setInsuredAmount(orderInfoVO.getInsuredAmount());
        }
        if(errorList.size() > 0){
            orderInsurance.setUninsuredCause(String.join(",", errorList));//未投保原因
        }
        orderInsurance.setCreateUser(CurrentUser.getCurrentUsername());
        orderInsurance.setCreateTime(new Date());
        orderInsurance.setUpdateUser(CurrentUser.getCurrentUsername());
        orderInsurance.setUpdateTime(new Date());
        orderInsurance.setEnable(false);
        orderInsurance.setInsuranceCancellation(false);
        if (null != companySourceDTO.getGoodsUnitPrice() && null != orderInfoVO.getDeliverWeightNotesWeight()) {
            orderInsurance.setCargoValue(companySourceDTO.getGoodsUnitPrice().multiply(
                    BigDecimal.valueOf(orderInfoVO.getDeliverWeightNotesWeight())).setScale(2, RoundingMode.HALF_UP));
        }
        //费率，这里保存的是对应的货物类型，费率需要去 t_sys_param 获取
        if (null != companySourceDTO.getInsuranceMethod() && !InsuranceMethodsEnum.NOTINSURED.getKey().equals(companySourceDTO.getInsuranceMethod())) {
            if (null != orderInfoVO.getInsuredGoodsType()) {
                SysParam paramByKey = sysParamAPI.getParamByKey(orderInfoVO.getInsuredGoodsType());
                if (null != paramByKey) {
                    orderInsurance.setRate(new BigDecimal(paramByKey.getParamValue()));
                }
            } else {
                if (null != orderInfo.getGoodSourceCode()) {
                    TGoodsSourceInfo dataByGoodsSourceCode = goodsSourceAPI.getDataByGoodsSourceCode(orderInfo.getGoodSourceCode());
                    SysParam paramByKey = sysParamAPI.getParamByKey(dataByGoodsSourceCode.getInsuredGoodsType());
                    if (null != paramByKey) {
                        orderInsurance.setRate(new BigDecimal(paramByKey.getParamValue()));
                    }
                }
            }
        }
        orderInsuranceMapper.insertSelective(orderInsurance);
    }

    /**
     * 卸货地参数
     * @param orderCode
     * @return
     */
    @Override
    @LcnTransaction
    @Transactional
    public ResultUtil selectDischarge(String orderCode) {
        ResultUtil resultUtil = new ResultUtil();
        try {
            Integer endUserId = CurrentUser.getEndUserId();
            TOrderInfoExample example = new TOrderInfoExample();
            TOrderInfoExample.Criteria c = example.createCriteria();
            if(orderCode==null||"".equals(orderCode)){
                if(endUserId!=null&&!"".equals(endUserId)){
                    c.andEndDriverIdEqualTo(endUserId);
                    c.andOrderExecuteStatusEqualTo(DictEnum.M030.code);
                }else{
                    resultUtil.setCode("400");
                    resultUtil.setMsg("当前司机没有登录，请登录！");
                    return resultUtil;
                }
            }else{
                c.andCodeEqualTo(orderCode);
            }
            List<TOrderInfo> list = tOrderInfoMapper.selectByExample(example);
            TOrderInfo info = new TOrderInfo();
            if (list.size() > 0) {
                info = list.get(0);
            }else if(list.size()<1&&(orderCode==null||"".equals(orderCode))){
                return new ResultUtil(CodeEnum.FAIL.getCode(),"当前无运单！");
            }else{
                resultUtil.setMsg("无此运单信息！");
                resultUtil.setCode("error");
                return  resultUtil;
            }
            if (!endUserId.equals(info.getEndDriverId()) && !endUserId.equals(info.getEndCarOwnerId())) {
                resultUtil.setMsg("当前登录司机与运单司机不一致！");
                resultUtil.setCode("error");
                return resultUtil;
            } /*else if (!DictEnum.M030.code.equals(info.getOrderExecuteStatus())) {//M030已装货
                resultUtil.setMsg("当前运单没有装货！");
                resultUtil.setCode("error");
                return resultUtil;
            }*/ else {
                if (list.size() > 0) {
                    //企业信息
                    TCompanyInfoQuery companyInfoQuery = new TCompanyInfoQuery().setCompanyId(info.getCompanyId());
                    ResultUtil re = companyService.selectCompanyInfoById(companyInfoQuery);
                    TCompanyInfo companyInfo = JSON.parseObject(JSONObject.toJSON(re.getData()).toString(), TCompanyInfo.class);

                    //司机信息 姓名电话
                    ResultUtil resuw = tEndSUserInfoAPI.selectById(info.getEndDriverId());
                    TEndUserInfo teu = JSON.parseObject(JSONObject.toJSON(resuw.getData()).toString(), TEndUserInfo.class);

                    //车辆信息
                    ResultUtil rr = tEndCarInfoAPI.selectById(info.getVehicleId());
                    TEndCarInfo tc = JSON.parseObject(JSONObject.toJSON(rr.getData()).toString(), TEndCarInfo.class);

/*
                    //项目信息
                    TCompanyProject tCompanyProject = companyProjectAPI.findById(info.getCompanyProjectId());
*/
//                    TLineGoodsRel tLineGoodsRel= StringUtils.isEmpty(orderCode) || "".equals(orderCode) ?
//                            tOrderInfoMapper.selectLineGoodsRelByOrderCode(info.getCode()) : tOrderInfoMapper.selectLineGoodsRelByOrderCode(orderCode);


                    TLineGoodsRel tLineGoodsRel=tOrderInfoMapper.selectLineGoodsRelByOrderCode(info.getCode());

                    TOrderInfoVO vo = new TOrderInfoVO();
                    BeanUtils.copyProperties(info, vo);
                    //查询投保表信息
                    if(null != info.getOrderBusinessCode()){
                        TOrderInsurance orderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(info.getOrderBusinessCode());
                        if(null != orderInsurance && null != orderInsurance.getInsure() && null != orderInsurance.getInsuranceCancellation()){
                            vo.setInsure(orderInsurance.getInsure());
                            vo.setInsuranceCancellation(orderInsurance.getInsuranceCancellation());
                        }
                    }
                    //企业名称
                    vo.setCompanyName(companyInfo.getCompanyName());
                    //司机名称
                    vo.setRealName(teu.getRealName());

                    //司机电话
                    vo.setRealPhone(teu.getPhone());

                    // 车牌号
                    vo.setVehicleNumber(tc.getVehicleNumber());

                    vo.setIsDisplay(tLineGoodsRel.getFreightPrice()?null:"1");

                    //依据运单ID查询运单详情表
                    TOrderInfoDetail detail = orderInfoDetailMapper.selectByOrderId(info.getId());
                    if(null != detail && null != detail.getCarriagePriceUnit()){
                        vo.setCarriagePriceUnit(detail.getCarriagePriceUnit());
                        //根据运单ID查询t_order_info_weight表
                        if(DictEnum.CARRIAGE_PRICE_UNIT_BOX.code.equals(detail.getCarriagePriceUnit())){
                            TOrderInfoWeight tOrderInfoWeight = orderInfoWeightMapper.selectByOrderId(info.getId());
                            vo.setOrderInfoWeight(tOrderInfoWeight);
                        }
                    }else{
                        vo.setCarriagePriceUnit("DUN");
                    }
                    vo.setLoadingCarPhoto(detail.getLoadingCarPhoto());
                    vo.setLoadingCarPhotoInfo(detail.getLoadingCarPhotoInfo());
                    //依据line_goods_rel_id查询货源表
                    TGoodsSourceInfo tGoodsSourceInfo = new TGoodsSourceInfo();
                    tGoodsSourceInfo.setLineGoodsRelId(info.getLineGoodsRelId());
                    TGoodsSourceInfo goodsSourceInfo = goodsSourceAPI.selectGoodsSourceInfo(tGoodsSourceInfo);
                    if(null != goodsSourceInfo){
                        vo.setFixCutFee(goodsSourceInfo.getFixCutFee());
                    }

                    //业务部其他信息
                    if (info.getEndAgentId() != null && !"".equals(info.getEndAgentId())) {
                        ResultUtil ru = tEndSUserInfoAPI.selectById(info.getEndAgentId());
                        if (ru.getData() != null && !"".equals(ru.getData())) {
                            TEndUserInfo tu = JSON.parseObject(JSONObject.toJSON(ru.getData()).toString(), TEndUserInfo.class);
                            //用户组织名称
                            vo.setShortName(tu.getRealName());
                            vo.setShortPhone(tu.getPhone());
                        }
                    }

                    //经纪人信息
                  /*  if(info.getAgentId()!= null && !"".equals(info.getAgentId())){
                        ResultUtil resuAgent = tEndSUserInfoAPI.selectById(info.getAgentId());
                        TEndUserInfo teuAgent =  JSON.parseObject(JSONObject.toJSON(resuAgent.getData()).toString(),TEndUserInfo.class);
                        if(teuAgent!=null &&!"".equals(teuAgent)){
                            vo.setAgentName(teuAgent.getRealName());
                            vo.setAgentPhone(teuAgent.getPhone());
                        }
                    }*/

                    //根据运单表信息拿到线路id查询线路信息
                    TLineInfo tLineInfo = lineService.selectById(info.getLineId());
                    //根据车牌号获取车辆轨迹
                    /*TrajectoryVLastLocationResp resultData = trajectoryAPI.vLastLocation(tc.getVehicleNumber(), 1);
                    if("0".equals(resultData.getStatus())){
                        resultUtil.setCode("erorr");
                        resultUtil.setMsg("轨迹获取失败！");
                        return resultUtil;
                    }
                    if ("1001".equals(resultData.getStatus())) {
                        //经度
                        String lon = resultData.getLon();
                        //纬度
                        String lat = resultData.getLat();

                        String jd = tLineInfo.getEndCoordinates().split(",")[0];//经度
                        String wd = tLineInfo.getEndCoordinates().split(",")[1];//纬度
                        Double fenceRecognitionDistance = (Double) tLineInfo.getFenceRecognitionDistance();//电子围栏识别距离
                        //根据两组经纬度计算距离 （km）
                        Point2D pointDD = new Point2D.Double(Double.parseDouble(jd), Double.parseDouble(wd));
                        Point2D pointXD = new Point2D.Double(Double.parseDouble(lon), Double.parseDouble(lat));
                        //距离
                        Double distance = ElectronicFence.getDistance(pointDD, pointXD);
                        //电子围栏距离为空时 查询系统参数配置表
                        if(fenceRecognitionDistance==null || "".equals(fenceRecognitionDistance)){
                            SysParam sysParam = sysParamAPI.getParamByKey("fenceRecognitionDistance");
                            if(sysParam!=null &&!"".equals(sysParam)){
                                fenceRecognitionDistance = Double.parseDouble(sysParam.getParamValue());
                            }
                        }
                        if (distance < fenceRecognitionDistance) {
                            vo.setCarStatus("1");//车辆状态正常
                        } else {
                            vo.setCarStatus("0");//车辆状态异常
                        }
                    } else {
                        vo.setCarStatus("0");//车辆状态异常
                    }*/
                    /*TOrderVehicleTrajectoryExample example1 = new TOrderVehicleTrajectoryExample();
                    TOrderVehicleTrajectoryExample.Criteria cr1 = example1.createCriteria();
                    cr1.andOrderCodeEqualTo(info.getCode());
                    cr1.andDataEnableEqualTo(true);
                    List<TOrderVehicleTrajectory> trajectoryList = tOrderVehicleTrajectoryMapper.selectByExample(example1);
                    List<Map<String, Object>> positionList = new ArrayList<>();
                    for (TOrderVehicleTrajectory trajectory : trajectoryList) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("jd", trajectory.getLongitude());
                        map.put("wd", trajectory.getLatitudes());
                        map.put("dataEnable",trajectory.getDataEnable());
                        map.put("trajectoryReceiveMethod",trajectory.getTrajectoryReceiveMethod());
                        positionList.add(map);
                    }
                    vo.setPositionList(positionList);*/
                    vo.setFromCoordinates(tLineInfo.getFromCoordinates());
                    vo.setEndCoordinates(tLineInfo.getEndCoordinates());
                    vo.setCityFromCode(tLineInfo.getCityFromCode());
                    vo.setCityEndCode(tLineInfo.getCityEndCode());
                    SysParam paramByKey = sysParamAPI.getParamByKey("APPUOLOADEDATACARRIERID");
                    vo.setUploadCarrierId(paramByKey.getParamValue());

                    TOrderInfoDetail tOrderInfoDetail = tOrderInfoDetailMapper.selectByOrderId(vo.getId());
                    vo.setLoadingCarPhoto(tOrderInfoDetail.getLoadingCarPhoto());
                    if(null != tOrderInfoDetail.getLoadingCarPhotoInfo() && StringUtils.isNotBlank(tOrderInfoDetail.getLoadingCarPhotoInfo())) {
                        vo.setLoadingCarPhotoInfo(tOrderInfoDetail.getLoadingCarPhotoInfo());
                    }
                    vo.setUnloadCarPhoto(tOrderInfoDetail.getUnloadCarPhoto());
                    if(null != tOrderInfoDetail.getUnloadCarPhotoInfo() && StringUtils.isNotBlank(tOrderInfoDetail.getUnloadCarPhotoInfo())){
                        vo.setUnloadCarPhotoInfo(tOrderInfoDetail.getUnloadCarPhotoInfo());
                    }

                    resultUtil.setCode("success");
                    resultUtil.setData(vo);
                }
                return resultUtil;
            }
        }catch (Exception e){
            log.error("参数查询失败！订单业务id为:"+ orderCode,e);
            resultUtil.setMsg("失败！");
            resultUtil.setCode("error");
            return resultUtil;
        }

    }



    /**
     * 卸货地签到
     * @param orderInfoVO
     * @return
     */
    @Override
    @LcnTransaction
    @Transactional
    public ResultUtil dischargeSingIn(TOrderInfoVO orderInfoVO) {
        TOrderInfo orderInfo = tOrderInfoMapper.selectByPrimaryKey(orderInfoVO.getId());
        TGoodsSourceInfo tGoodsSourceInfo = new TGoodsSourceInfo();
        tGoodsSourceInfo.setLineGoodsRelId(orderInfo.getLineGoodsRelId());
        TGoodsSourceInfo info = goodsSourceAPI.selectGoodsSourceInfo(tGoodsSourceInfo);
        TOrderInfoDetail infoDetail = orderInfoDetailMapper.selectByOrderId(orderInfo.getId());
        Double primaryWeight = orderInfo.getPrimaryWeight();
        Double deliverWeightNotesWeight = orderInfo.getDeliverWeightNotesWeight();
        TOrderInfoWeight torderInfoweight = orderInfoVO.getOrderInfoWeight();
        TEndCarInfo tEndCarInfo = tEndCarInfoAPI.selectByPrimaryKey(orderInfo.getVehicleId());
        //根据车牌号获取车辆轨迹
        TransTimeManageVResp resultData = trajectoryRecentAPI.transTimeManageV3One(tEndCarInfo.getVehicleNumber()+"_"+tEndCarInfo.getLicensePlateColor(),1);
        log.info("车牌号获取车辆轨迹，经度："+resultData.getLon()+"，纬度："+resultData.getLat());
        //根据运单表信息拿到线路id查询线路信息
        TLineInfo tLineInfo = lineService.selectById(orderInfo.getLineId());
        //log.info("线路参数："+JSONObject.toJSONString(tLineInfo));
        String jd = tLineInfo.getEndCoordinates().split(",")[0];//经度
        String wd = tLineInfo.getEndCoordinates().split(",")[1];//纬度
        log.info("终点经纬度，经度："+jd+"，纬度："+wd);
        Double fenceRecognitionDistance = (Double) tLineInfo.getFenceRecognitionDistance();//电子围栏识别距离

        //电子围栏距离为空时 查询系统参数配置表
        if(fenceRecognitionDistance==null || "".equals(fenceRecognitionDistance)){
            SysParam sysParam = sysParamAPI.getParamByKey("fenceRecognitionDistance");
            if(sysParam!=null &&!"".equals(sysParam)){
                fenceRecognitionDistance = Double.parseDouble(sysParam.getParamValue());
            }
        }

        //根据两组经纬度计算距离 （km）
        //终点
        Point2D pointDD = new Point2D.Double(Double.parseDouble(jd), Double.parseDouble(wd));
        Point2D pointXD = null;
        //车点
        if((resultData.getLon()!=null&& !"".equals(resultData.getLon())) &&
                (resultData.getLat())!=null&& !"".equals(resultData.getLat())){
            pointXD = new Point2D.Double(Double.parseDouble(String.valueOf(resultData.getLon()))/600000, Double.parseDouble(String.valueOf(resultData.getLat()))/600000);
        }
        //计算两点距离
        double distance = 0.0d;
        if(pointXD != null){
            distance = ElectronicFence.getDistance(pointDD, pointXD);
        }

        double v = BigDecimal.valueOf(distance).setScale(2, RoundingMode.HALF_UP).doubleValue();

        //人工发单运单，装、卸货签到时，无需校验手机位置是否在围栏内
        //当“装货签到/扫码接单时校验车辆是否在电子围栏内”为开启状态，
        //运单是单笔支付并且运单来源不是人工发单的时候，进行校验
        if(infoDetail.getIllegalCheck()){
            if(info.getIfElectronicFence() && null != orderInfo.getOrderCreateType() &&
                    DictEnum.SINGLEPAY.code.equals(orderInfo.getPayMethod()) &&
                    !DictEnum.HANDSEND.code.equals(orderInfo.getOrderCreateType())){
                String operateGeographyPosition = orderInfoVO.getOperateGeographyPosition();
                if(null != operateGeographyPosition){
                    String str = operateGeographyPosition.replaceAll("[, ]","");
                    if(str.length() > 0){
                        String[] s = operateGeographyPosition.split(",");
                        if(s.length == 2){
                            log.info("获取到的手机经纬度，经度："+s[0]+ "，纬度："+s[1]);
                            //经度
                            String carLon = operateGeographyPosition.split(",")[0];
                            //纬度
                            String carLat = operateGeographyPosition.split(",")[1];
                            //获取手机位置的点
                            Point2D phonePoint = new Point2D.Double(Double.parseDouble(carLon), Double.parseDouble(carLat));
                            //距离
                            distance = ElectronicFence.getDistance(pointDD, phonePoint);
                            v = BigDecimal.valueOf(distance).setScale(2, RoundingMode.HALF_UP).doubleValue();
                            log.info("车辆到装货终点的距离:"+v+"，终点电子围栏的距离"+fenceRecognitionDistance);
                            if (null != fenceRecognitionDistance && v > fenceRecognitionDistance){
                                return ResultUtil.error("您的当前位置不在卸货地范围内，无法签到");
                            }
                        }else{
                            return ResultUtil.error("请打开手机定位，否则无法卸货签到");
                        }
                    }else{
                        return ResultUtil.error("请打开手机定位，否则无法卸货签到");
                    }
                }else{
                    return ResultUtil.error("获取您的当前位置失败，无法签到");
                }
            }
        }
        try{
            log.info("-------------------------------------操作位置{},{}",orderInfoVO.getOperateGeographyPosition(),JSONObject.toJSONString(orderInfoVO));
            ResultUtil resultUtil = new ResultUtil();

            //签到 保存参数修改状态
            if("0".equals(orderInfoVO.getTypeSign())){

                //是否提示卸货地签到操作
                if(orderInfoVO.getIfWarn()){
                    //若点击卸货签到的时间与装货签到时间间隔30分钟以内，则弹窗提示“装、卸货签到操作频繁“。
                    TOrderInfoVO orderResult = tOrderInfoMapper.selectByCodeStatus(orderInfo.getCode());
                    if(null!=orderResult){
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(orderResult.getStateCreateTime());
                        SysParam sysParam = sysParamAPI.getParamByKey("QIANDAOTIME");
                        int dateMinute  = 30;
                        if(null!=sysParam && sysParam.getParamValue()!=null &&!"".equals(sysParam.getParamValue())){
                            dateMinute  = Integer.parseInt(sysParam.getParamValue());
                        }
                        cal.add(Calendar.MINUTE, dateMinute);
                        if (cal.getTime().after(new Date())) {
                            ResultUtil result = new ResultUtil();
                            BusinessVO businessVO = new BusinessVO();
                            businessVO.setCompanyId(orderResult.getCompanyId());
                            businessVO.setProjectId(orderResult.getCompanyProjectId());
                            List<TBusinessBasic> tBusinessBasicList = tBusinessAPI.selectByCompanyIdAndProjectId(businessVO);
                            String phone = "";
                            if(null!=tBusinessBasicList && !"".equals(tBusinessBasicList) && tBusinessBasicList.size()>0){
                                if(null!=tBusinessBasicList.get(0) &&!"".equals(tBusinessBasicList.get(0))){
                                    phone = tBusinessBasicList.get(0).getAccountNo();
                                }
                            }else{
                                BusinessVO businessVONew = new BusinessVO();
                                businessVONew.setCompanyId(orderResult.getCompanyId());
                                List<TBusinessBasic> tBusinessBasicListNew = tBusinessAPI.selectByCompanyIdAndProjectId(businessVONew);
                                if(null!=tBusinessBasicListNew &&!"".equals(tBusinessBasicListNew) && tBusinessBasicListNew.size()>0){
                                    if(null!=tBusinessBasicListNew.get(0) &&!"".equals(tBusinessBasicListNew.get(0))){
                                        phone = tBusinessBasicListNew.get(0).getAccountNo();
                                    }
                                }
                            }
                            result.setCode(CodeEnum.WARN.getCode());
                            result.setData(phone);
                            return result;
                        }
                    }
                }

                if("CTYPEBOSS".equals(orderInfoVO.getRoleType())){
                    resultUtil.setCode("error");
                    resultUtil.setMsg("车主身份不可以签到");
                    return resultUtil;
                }

                if(DictEnum.M20.code.equals(orderInfo.getOrderExecuteStatus())){
                    resultUtil.setCode("error");
                    resultUtil.setMsg("此运单已删单不允许签到！");
                    return resultUtil;
                }

                if(DictEnum.M10.code.equals(orderInfo.getOrderExecuteStatus())){
                    resultUtil.setCode("error");
                    resultUtil.setMsg("此运单已取消不允许签到！");
                    return resultUtil;
                }
                if(DictEnum.M040.code.equals(orderInfo.getOrderExecuteStatus())){
                    resultUtil.setCode("error");
                    resultUtil.setMsg("此运单已签到！");
                    return resultUtil;
                }

                if(!"M000".equals(orderInfo.getOrderExecuteStatus())&&!"M010".equals(orderInfo.getOrderExecuteStatus())
                        &&!"M020".equals(orderInfo.getOrderExecuteStatus())&&!"M030".equals(orderInfo.getOrderExecuteStatus())){
                    resultUtil.setCode("error");
                    resultUtil.setMsg("此运单不允许卸货地签到！");
                    return resultUtil;
                }
                if(null != orderInfo.getPayMethod() && !"".equals(orderInfo.getPayMethod())){
                    if(orderInfo.getPayMethod().equals(DictEnum.NODEPAYPROPORTION.code)
                            || orderInfo.getPayMethod().equals(DictEnum.NODEPAYFIXED.code)){
                        if(!"M030".equals(orderInfo.getOrderExecuteStatus())){
                            resultUtil.setCode("error");
                            resultUtil.setMsg("此运单没有装货地签到不允许卸货地签到！");
                            return resultUtil;
                        }
                    }
                }
                //查询t_order_info_detail，获取预计行驶时间
                TOrderInfoDetail orderInfoDetail1 =  orderInfoDetailMapper.selectByOrderId(orderInfo.getId());
                //点击收单时，校验当前时间是否大于（建单时间+预计行驶时间），若小于，不允许收单，
                // 提示：未到达收单时间，请于xx月xx日 xx：xx（时分）后再进行收单操作。
                Double estimatedTravelTime = orderInfoDetail1.getEstimatedTravelTime();//预计行驶时间
                if(null == estimatedTravelTime || estimatedTravelTime == 0d){
                    //如果预计行驶时间是空的，则根据起点终点坐标得出预计行驶时间
                    String fromCoordinates = orderInfo.getFromCoordinates();//起点坐标
                    String endCoordinates = orderInfo.getEndCoordinates();//终点坐标
                    if(null != fromCoordinates && null != endCoordinates){
                        Map<String, Double> map = AddressLocationUtil.getDistanceAndDuration(fromCoordinates, endCoordinates);
                        estimatedTravelTime = map.get("estimatedTravelTime");
                    }
                }
                Double estimatedTravelTime11 = estimatedTravelTime;
                int second = (int)(estimatedTravelTime11 * 3600);//换算成秒
                Date createTime = orderInfo.getCreateTime();//建单时间
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(createTime);
                calendar.add(Calendar.SECOND, second);
                Date newDate = calendar.getTime();//预计收单时间
                Date date = new Date();
                SimpleDateFormat format = new SimpleDateFormat("MM月dd日 HH:mm");
                String dateStr = format.format(newDate);
                if(date.getTime()< newDate.getTime()){
                    return ResultUtil.error("未到达线路规定的行驶时间，请于"+dateStr+"后再进行卸货签到操作。");
                }

                // 如果是货源大厅的运单，修改货源库存
                boolean resourceIfHall = orderGoodsSourceInfoMapper.selectResourceIfHall(orderInfo.getLineGoodsRelId());
                if (resourceIfHall) {
                    if (null != orderInfoVO.getDeliverWeightNotesWeight() && 0 != orderInfoVO.getDeliverWeightNotesWeight()) {
                        // 判断收货是否填写装货重量
                        if (null == deliverWeightNotesWeight || deliverWeightNotesWeight == 0) {
                            // 修改货源库存
                            TGoodsSourceInfo goodsSourceInfo = orderGoodsSourceInfoMapper.selectByByLineGoodsRelId(orderInfo.getLineGoodsRelId());
                            TGoodsSourceInfo goodsSourceInfoForUpdate = new TGoodsSourceInfo();
                            goodsSourceInfoForUpdate.setId(goodsSourceInfo.getId());
                            goodsSourceInfoForUpdate.setEstimateGoodsWeight(goodsSourceInfo.getEstimateGoodsWeight() + primaryWeight);
                            goodsSourceInfoForUpdate.setEstimateGoodsWeight(goodsSourceInfoForUpdate.getEstimateGoodsWeight() - orderInfoVO.getDeliverWeightNotesWeight());
                            if (goodsSourceInfoForUpdate.getEstimateGoodsWeight().compareTo(0d) < 0) {
                                return ResultUtil.error("货源库存不足");
                            } else if (goodsSourceInfoForUpdate.getEstimateGoodsWeight().compareTo(0d) == 0) {
                                goodsSourceInfoForUpdate.setStatus(DictEnum.CLOSESOURCE.code);
                            }
                            goodsSourceInfoForUpdate.setUpdateTime(new Date());
                            orderGoodsSourceInfoMapper.updateEstimateGoodsWeight(goodsSourceInfoForUpdate);
                        }
                    }
                }

                orderInfo.setDeliverWeightNotesPhoto(orderInfoVO.getDeliverWeightNotesPhoto());//装货磅单
                orderInfo.setDeliverWeightNotesWeight(orderInfoVO.getDeliverWeightNotesWeight());//原发重量
                orderInfo.setDeliverWeightNotesTime(orderInfoVO.getDeliverWeightNotesTime());//出场时间
                orderInfo.setReceiveWeightNotesPhoto(orderInfoVO.getReceiveWeightNotesPhoto());//卸货磅单
                orderInfo.setReceiveWeightNotesWeight(orderInfoVO.getReceiveWeightNotesWeight());//实收重量
                orderInfo.setReceiveWeightNotesTime(orderInfoVO.getReceiveWeightNotesTime());//卸货磅单时间
                if(null != torderInfoweight && null != orderInfoVO.getCarriagePriceUnit() &&
                DictEnum.CARRIAGE_PRICE_UNIT_BOX.code.equals(orderInfoVO.getCarriagePriceUnit())){
                    //如果是两箱时装货重量取两箱之和
                    if(null != torderInfoweight.getDeliverWeightNotesWeight1() && null != torderInfoweight.getDeliverWeightNotesWeight2()){
                        orderInfo.setDeliverWeightNotesWeight(torderInfoweight.getDeliverWeightNotesWeight1().add(torderInfoweight.getDeliverWeightNotesWeight2()).doubleValue());
                    } else if (null != torderInfoweight.getDeliverWeightNotesWeight1()) {
                        orderInfo.setDeliverWeightNotesWeight(torderInfoweight.getDeliverWeightNotesWeight1().doubleValue());
                    } else if (null != torderInfoweight.getDeliverWeightNotesWeight2()) {
                        orderInfo.setDeliverWeightNotesWeight(torderInfoweight.getDeliverWeightNotesWeight2().doubleValue());
                    }
                    //装货磅单时间，取第一箱的时间
                    if(null != torderInfoweight.getDeliverWeightNotesTime1() && null != torderInfoweight.getDeliverWeightNotesTime2()){
                        orderInfo.setDeliverWeightNotesTime(torderInfoweight.getDeliverWeightNotesTime1());
                    }else if(null != torderInfoweight.getDeliverWeightNotesTime1()){
                        orderInfo.setDeliverWeightNotesTime(torderInfoweight.getDeliverWeightNotesTime1());
                    } else if (null != torderInfoweight.getDeliverWeightNotesTime2()) {
                        orderInfo.setDeliverWeightNotesTime(torderInfoweight.getDeliverWeightNotesTime2());
                    }
                    //如果是两箱时卸货重量取两箱之和
                    if(null != torderInfoweight.getReceiveWeightNotesWeight1() && null != torderInfoweight.getReceiveWeightNotesWeight2()){
                        orderInfo.setReceiveWeightNotesWeight(torderInfoweight.getReceiveWeightNotesWeight1().add(torderInfoweight.getReceiveWeightNotesWeight2()).doubleValue());
                    } else if (null != torderInfoweight.getReceiveWeightNotesWeight1()) {
                        orderInfo.setReceiveWeightNotesWeight(torderInfoweight.getReceiveWeightNotesWeight1().doubleValue());
                    } else if (null != torderInfoweight.getReceiveWeightNotesWeight2()) {
                        orderInfo.setReceiveWeightNotesWeight(torderInfoweight.getReceiveWeightNotesWeight2().doubleValue());
                    }
                    //卸货磅单时间，取第一箱的时间
                    if(null != torderInfoweight.getReceiveWeightNotesTime1() && null != torderInfoweight.getReceiveWeightNotesTime2()){
                        orderInfo.setReceiveWeightNotesTime(torderInfoweight.getReceiveWeightNotesTime1());
                    } else if(null != torderInfoweight.getReceiveWeightNotesTime1()){
                        orderInfo.setReceiveWeightNotesTime(torderInfoweight.getReceiveWeightNotesTime1());
                    } else if (null != torderInfoweight.getReceiveWeightNotesTime2()) {
                        orderInfo.setReceiveWeightNotesTime(torderInfoweight.getReceiveWeightNotesTime2());
                    }
                }
                orderInfo.setOrderExecuteStatus(DictEnum.M040.code);
                orderInfo.setVehicleGpsBdStatus(DictEnum.NOFETCHGP.code);
                if(null!=orderInfoVO.getGrossWeight() && !"".equals(orderInfoVO.getGrossWeight())){
                    orderInfo.setGrossWeight(orderInfoVO.getGrossWeight());
                }

                TLineGoodsRel lineGoodsRel = orderGoodsSourceInfoMapper.selectByGoodsRelId(orderInfo.getLineGoodsRelId());
                if(lineGoodsRel.getIfCaptainAudit().equals(1)){
                    orderInfo.setOrderPayStatus(DictEnum.M045.code);
                }
                tOrderInfoMapper.updateByPrimaryKey(orderInfo);

                // 记录运单信息详情
                TOrderInfoDetail orderInfoDetail = new TOrderInfoDetail();
                orderInfoDetail.setOrderId(orderInfo.getId());
                // 查询司机、车辆审核状态
                EnduserCarStatus enduserCarStatus = sendOrderUtil.selectEnduserCarStatus(orderInfo.getEndDriverId(), orderInfo.getVehicleId());
                // 记录司机、车辆审核状态
                if (null != enduserCarStatus.getUserAuditStatus()
                        && DictEnum.PASSNODE.code.equals(enduserCarStatus.getUserAuditStatus())) {
                    orderInfoDetail.setDriverAuditStatus(DictEnum.PASSNODE.code);
                } else {
                    orderInfoDetail.setDriverAuditStatus(DictEnum.NOTPASSNODE.code);
                }
                if (null != enduserCarStatus.getCarAuditStatus()
                        && DictEnum.PASSNODE.code.equals(enduserCarStatus.getCarAuditStatus())) {
                    orderInfoDetail.setCarAuditStatus(DictEnum.PASSNODE.code);
                } else {
                    orderInfoDetail.setCarAuditStatus(DictEnum.NOTPASSNODE.code);
                }
                orderInfoDetail.setLoadingCarPhoto(orderInfoVO.getLoadingCarPhoto());
                orderInfoDetail.setLoadingCarPhotoInfo(orderInfoVO.getLoadingCarPhotoInfo());
                orderInfoDetail.setUnloadCarPhoto(orderInfoVO.getUnloadCarPhoto());
                orderInfoDetail.setUnloadCarPhotoInfo(orderInfoVO.getUnloadCarPhotoInfo());
                orderInfoDetailMapper.updateByOrderIdSelective(orderInfoDetail);

                //新增状态表当前车辆状态 司机状态

                CarDriverRelVO carDriverRelVO = new CarDriverRelVO();
                carDriverRelVO.setCode(orderInfo.getCode());
                carDriverRelVO.setBizCode(orderInfo.getOrderBusinessCode());
                carDriverRelVO.setUserStatus("DISTRIBUTIONCHECKED");
                carDriverRelVO.setCarStatus("AVAILABLECHECKED");
                appCommonAPI.updateCarEnduserStatus(carDriverRelVO);


                //运单状态运行子表
                //新增状态表当前车辆状态
                TOrderState orderState = new TOrderState();
                orderState.setCode(IdWorkerUtil.getInstance().nextId());
                orderState.setOrderCode(orderInfo.getCode());
                orderState.setOperateTime(new Date());
                orderState.setOperateMethod("WX");
                if(null!=CurrentUser.getCurrentUserID()){
                    orderState.setOperatorId(CurrentUser.getCurrentUserID());
                }
                orderState.setOperateGeographyPosition(orderInfoVO.getOperateGeographyPosition());
                orderState.setStateNodeValue("S0402");//司机确认卸货
                orderState.setIfExpire(false);
                if(null!=CurrentUser.getUserNickname()){
                    orderState.setCreateUser(CurrentUser.getUserNickname());
                }
                orderState.setCreateTime(new Date());
                orderState.setEnable(false);
                TOrderStateExample example = new TOrderStateExample();
                TOrderStateExample.Criteria cr = example.createCriteria();
                cr.andOrderCodeEqualTo(orderInfo.getCode());
                cr.andStateNodeValueEqualTo("S0402");
                List<TOrderState> tOrderStateList = tOrderStateMapper.selectByExample(example);
                if(tOrderStateList.size()<1){
                    tOrderStateMapper.insertSelective(orderState);
                }

                if(lineGoodsRel.getIfCaptainAudit().equals(1)){
                    TOrderState orderState2 = new TOrderState();
                    orderState2.setCode(IdWorkerUtil.getInstance().nextId());
                    orderState2.setOrderCode(orderInfo.getCode());
                    orderState2.setOperatorId(orderInfo.getEndCarOwnerId());
                    orderState2.setOperateMethod("PC车队长审核");
                    orderState2.setOperateTime(new Date());
                    orderState2.setStateNodeValue(DictEnum.S0450.code);
                    orderState2.setIfExpire(false);
                    tOrderStateMapper.insertSelective(orderState2);
                }
                //车辆轨迹
                TrajectoryMongo trajectoryMongo = new TrajectoryMongo();
                trajectoryMongo.setId(IdWorkerUtil.getInstance().nextId());
                if (null != fenceRecognitionDistance && v < fenceRecognitionDistance) {
                    trajectoryMongo.setVehicleStatus("2");//车辆状态
                } else {
                    trajectoryMongo.setVehicleStatus("1");//车辆状态
                }

                if("1001".equals(resultData.getStatus())) {
                    trajectoryMongo.setTrajectoryReceiveTime(DateUtils.parseDate(resultData.getUtc()));
                    trajectoryMongo.setLongitude(String.valueOf(Double.parseDouble(resultData.getLon())/600000));//经度
                    trajectoryMongo.setLatitudes(String.valueOf(Double.parseDouble(resultData.getLat())/600000));//纬度
                    trajectoryMongo.setVehicleGeographyPosition(resultData.getAdr());//车辆地理位置信息
                    trajectoryMongo.setVehicleCurrentSpeed(resultData.getSpd());//车辆当前速度
                    trajectoryMongo.setVehicleCurrentDirection(resultData.getDrc());//车辆行驶方向
                    trajectoryMongo.setProvince(resultData.getProvince());
                    trajectoryMongo.setCity(resultData.getCity());
                    trajectoryMongo.setCountry(resultData.getCountry());
                }
                trajectoryMongo.setVehicleId(orderInfo.getVehicleId());//车辆id
                trajectoryMongo.setOrderCode(orderInfo.getCode()); //运单id
                trajectoryMongo.setTrajectoryCode(IdWorkerUtil.getInstance().nextId());
                trajectoryMongo.setVehiclePlateNo(tEndCarInfo.getVehicleNumber());
                trajectoryMongo.setTrajectoryReceiveMethod("DRIVERUNLOADSIGN");//服务端自动抓取
                trajectoryMongo.setTrajectoryReceiveTime(new Date());
                if("0".equals(orderInfoVO.getCarStatus())){
                    TOrderAbnormal tOrderAbnormal = new TOrderAbnormal();
                    tOrderAbnormal.setCode(IdWorkerUtil.getInstance().nextId());
                    tOrderAbnormal.setOrderCode(orderInfo.getCode());
                    tOrderAbnormal.setAbnormalType("QIANDAOYC");//签到异常
                    tOrderAbnormal.setParam1("XH");
                    tOrderAbnormal.setOperateMethod("WX");
                    tOrderAbnormal.setEnable(false);
                    tOrderAbnormal.setAbnormalDescription(orderInfoVO.getErrorMemo());//异常内容
                    tOrderAbnormal.setOperateGeographyPosition(orderInfoVO.getOperateGeographyPosition());//操作地理位置
                    tOrderAbnormalMapper.insertSelective(tOrderAbnormal);
                    trajectoryMongo.setDataEnable(false);//行数据可用性
                }else{
                    trajectoryMongo.setDataEnable(true);//行数据可用性
                }
                //插入运单轨迹表
                trajectoryMongoDao.insert(trajectoryMongo);
                //车辆卸货地签到后删除车辆轨迹中间表数据
                MQMessage mqMessage = new MQMessage();
                mqMessage.setTopic(MqMessageTopic.ORDERCARLOCATION);
                mqMessage.setTag(MqMessageTag.DELETE);
                mqMessage.setBody(trajectoryMongo.getOrderCode());
                mqMessage.setKey(IdWorkerUtil.getInstance().nextId());
                mqAPI.sendMessage(mqMessage);

                //发货到黄骅海通的货源数据跟对三分仓储对接
                SysParam sysParam = sysParamAPI.getParamByKey("HHCCKEY");
                if(sysParam.getParamValue().contains(orderInfo.getGoodSourceCode())){
                    TEndUserInfo tEndUserInfo = tEndSUserInfoAPI.selectByPrimaryKey(orderInfo.getEndDriverId());
                    addTaskHhcc(orderInfo,tEndUserInfo,tEndCarInfo,2,"xhd");
                }

                //redisUtil.slurDel("big-GJ-"+tovt.getOrderCode()+"*");
                resultUtil.setMsg("卸货地签到成功！");
                resultUtil.setCode("success");
            }else{//只保存参数，不修改状态
                if(orderInfoVO.getDeliverWeightNotesPhoto()!=null&&!"".equals(orderInfoVO.getDeliverWeightNotesPhoto())){
                    orderInfo.setDeliverWeightNotesPhoto(orderInfoVO.getDeliverWeightNotesPhoto());//装货磅单
                }else{
                    orderInfo.setDeliverWeightNotesPhoto(null);
                }
                if(orderInfoVO.getReceiveWeightNotesPhoto()!=null&&!"".equals(orderInfoVO.getReceiveWeightNotesPhoto())){
                    orderInfo.setReceiveWeightNotesPhoto(orderInfoVO.getReceiveWeightNotesPhoto());//卸货磅单
                }else{
                    orderInfo.setReceiveWeightNotesPhoto(null);
                }
                if(orderInfoVO.getPrimaryWeight()!=null&&!"".equals(orderInfoVO.getPrimaryWeight())){
                    orderInfo.setPrimaryWeight(orderInfoVO.getPrimaryWeight());//原发重量
                }
                if(orderInfoVO.getDeliverWeightNotesWeight()!=null&&!"".equals(orderInfoVO.getDeliverWeightNotesWeight())) {
                    orderInfo.setDeliverWeightNotesWeight(orderInfoVO.getDeliverWeightNotesWeight());//装货磅单重量
                    // 如果是货源大厅的运单，修改货源库存
                    boolean resourceIfHall = orderGoodsSourceInfoMapper.selectResourceIfHall(orderInfo.getLineGoodsRelId());
                    if (resourceIfHall) {
                        if (null == deliverWeightNotesWeight || deliverWeightNotesWeight == 0) {
                            if (null!=orderInfoVO.getDeliverWeightNotesWeight()&&0!= orderInfoVO.getDeliverWeightNotesWeight()) {
                                // 修改货源库存
                                TGoodsSourceInfo goodsSourceInfo = orderGoodsSourceInfoMapper.selectByByLineGoodsRelId(orderInfo.getLineGoodsRelId());
                                TGoodsSourceInfo goodsSourceInfoForUpdate = new TGoodsSourceInfo();
                                goodsSourceInfoForUpdate.setId(goodsSourceInfo.getId());
                                goodsSourceInfoForUpdate.setEstimateGoodsWeight(goodsSourceInfo.getEstimateGoodsWeight() + primaryWeight);
                                goodsSourceInfoForUpdate.setEstimateGoodsWeight(goodsSourceInfoForUpdate.getEstimateGoodsWeight() - orderInfoVO.getDeliverWeightNotesWeight());
                                if (goodsSourceInfoForUpdate.getEstimateGoodsWeight().compareTo(0d) < 0) {
                                    return ResultUtil.error("货源库存不足");
                                } else if (goodsSourceInfoForUpdate.getEstimateGoodsWeight().compareTo(0d) == 0) {
                                    goodsSourceInfoForUpdate.setStatus(DictEnum.CLOSESOURCE.code);
                                }
                                goodsSourceInfoForUpdate.setUpdateTime(new Date());
                                orderGoodsSourceInfoMapper.updateEstimateGoodsWeight(goodsSourceInfoForUpdate);
                            }
                        }
                    }
                }
                if(orderInfoVO.getDeliverWeightNotesTime()!=null&&!"".equals(orderInfoVO.getDeliverWeightNotesTime())){
                    orderInfo.setDeliverWeightNotesTime(orderInfoVO.getDeliverWeightNotesTime());//出场时间
                }
                if(orderInfoVO.getReceiveWeightNotesWeight()!=null&&!"".equals(orderInfoVO.getReceiveWeightNotesWeight())){
                    orderInfo.setReceiveWeightNotesWeight(orderInfoVO.getReceiveWeightNotesWeight());//实收重量
                }
                if(orderInfoVO.getReceiveWeightNotesTime()!=null&&!"".equals(orderInfoVO.getReceiveWeightNotesTime())){
                    orderInfo.setReceiveWeightNotesTime(orderInfoVO.getReceiveWeightNotesTime());//卸货磅单时间
                }
                if(null!=orderInfoVO.getGrossWeight() && !"".equals(orderInfoVO.getGrossWeight())){
                    orderInfo.setGrossWeight(orderInfoVO.getGrossWeight());
                }
                if(null != torderInfoweight && null != orderInfoVO.getCarriagePriceUnit() &&
                        DictEnum.CARRIAGE_PRICE_UNIT_BOX.code.equals(orderInfoVO.getCarriagePriceUnit())){
                    //如果是两箱时装货重量取两箱之和
                    if(null != torderInfoweight.getDeliverWeightNotesWeight1() && null != torderInfoweight.getDeliverWeightNotesWeight2()){
                        orderInfo.setDeliverWeightNotesWeight(torderInfoweight.getDeliverWeightNotesWeight1().add(torderInfoweight.getDeliverWeightNotesWeight2()).doubleValue());
                    } else if (null != torderInfoweight.getDeliverWeightNotesWeight1()) {
                        orderInfo.setDeliverWeightNotesWeight(torderInfoweight.getDeliverWeightNotesWeight1().doubleValue());
                    } else if (null != torderInfoweight.getDeliverWeightNotesWeight2()) {
                        orderInfo.setDeliverWeightNotesWeight(torderInfoweight.getDeliverWeightNotesWeight2().doubleValue());
                    }
                    //装货磅单时间，取第一箱的时间
                    if(null != torderInfoweight.getDeliverWeightNotesTime1() && null != torderInfoweight.getDeliverWeightNotesTime2()){
                        orderInfo.setDeliverWeightNotesTime(torderInfoweight.getDeliverWeightNotesTime1());
                    } else if(null != torderInfoweight.getDeliverWeightNotesTime1()){
                        orderInfo.setDeliverWeightNotesTime(torderInfoweight.getDeliverWeightNotesTime1());
                    }  else if (null != torderInfoweight.getDeliverWeightNotesTime2()) {
                        orderInfo.setDeliverWeightNotesTime(torderInfoweight.getDeliverWeightNotesTime2());
                    }
                    //如果是两箱时卸货重量取两箱之和
                    if(null != torderInfoweight.getReceiveWeightNotesWeight1() && null != torderInfoweight.getReceiveWeightNotesWeight2()){
                        orderInfo.setReceiveWeightNotesWeight(torderInfoweight.getReceiveWeightNotesWeight1().add(torderInfoweight.getReceiveWeightNotesWeight2()).doubleValue());
                    } else if (null != torderInfoweight.getReceiveWeightNotesWeight1()) {
                        orderInfo.setReceiveWeightNotesWeight(torderInfoweight.getReceiveWeightNotesWeight1().doubleValue());
                    } else if (null != torderInfoweight.getReceiveWeightNotesWeight2()) {
                        orderInfo.setReceiveWeightNotesWeight(torderInfoweight.getReceiveWeightNotesWeight2().doubleValue());
                    }
                    //卸货磅单时间，取第一箱的时间
                    if(null != torderInfoweight.getReceiveWeightNotesTime1() && null != torderInfoweight.getReceiveWeightNotesTime2()){
                        orderInfo.setReceiveWeightNotesTime(torderInfoweight.getReceiveWeightNotesTime1());
                    }else if(null != torderInfoweight.getReceiveWeightNotesTime1()){
                        orderInfo.setReceiveWeightNotesTime(torderInfoweight.getReceiveWeightNotesTime1());
                    }else if (null != torderInfoweight.getReceiveWeightNotesTime2()) {
                        orderInfo.setReceiveWeightNotesTime(torderInfoweight.getReceiveWeightNotesTime2());
                    }
                }
                tOrderInfoMapper.updateByPrimaryKeySelectiveNew(orderInfo);

                resultUtil.setMsg("保存成功！");
                resultUtil.setCode("success");
            }
            // 记录运单信息详情
            TOrderInfoDetail orderInfoDetail = new TOrderInfoDetail();
            orderInfoDetail.setOrderId(orderInfo.getId());
            orderInfoDetail.setLoadingCarPhoto(orderInfoVO.getLoadingCarPhoto());
            orderInfoDetail.setLoadingCarPhotoInfo(orderInfoVO.getLoadingCarPhotoInfo());
            orderInfoDetail.setUnloadCarPhoto(orderInfoVO.getUnloadCarPhoto());
            orderInfoDetail.setUnloadCarPhotoInfo(orderInfoVO.getUnloadCarPhotoInfo());
            orderInfoDetailMapper.updateByOrderIdSelective(orderInfoDetail);
            //卸货签到时，如果运费单价单位为元/箱，则需要将数据保存到t_order_info_weight表中
            if(null != orderInfoVO.getCarriagePriceUnit() &&
                    DictEnum.CARRIAGE_PRICE_UNIT_BOX.code.equals(orderInfoVO.getCarriagePriceUnit())){
                if(null != torderInfoweight && null == torderInfoweight.getOrderId()){
                    torderInfoweight.setOrderId(orderInfo.getId());
                }
                TOrderInfoWeight weight = orderInfoWeightMapper.selectByPrimaryKey(Long.valueOf(torderInfoweight.getId()));
                if(null == torderInfoweight.getBoxNum()){
                    torderInfoweight.setBoxNum(weight.getBoxNum());
                }
                if(null != weight.getPrimaryWeight1() && null == torderInfoweight.getPrimaryWeight1()){
                    torderInfoweight.setPrimaryWeight1(weight.getPrimaryWeight1());//原发重量1
                }
                if(null != weight.getPrimaryWeight2() && null == torderInfoweight.getPrimaryWeight2()){
                    torderInfoweight.setPrimaryWeight2(weight.getPrimaryWeight2());//原发重量2
                }
                if(null != weight.getCarriageUnitPrice1() && null == torderInfoweight.getCarriageUnitPrice1()){
                    torderInfoweight.setCarriageUnitPrice1(weight.getCarriageUnitPrice1());//运费单价1
                }
                if(null != weight.getCarriageUnitPrice2() && null == torderInfoweight.getCarriageUnitPrice2()){
                    torderInfoweight.setCarriageUnitPrice2(weight.getCarriageUnitPrice2());//运费单价2
                }
                orderInfoWeightMapper.updateByPrimaryKey(torderInfoweight);
            }
            return resultUtil;
        }catch(Exception e){
            log.error("卸货地签到失败, {}", ThrowableUtil.getStackTrace(e));
            throw new RuntimeException("卸货地签到失败！");
        }
    }




    /**
     * 签到查询轨迹是否在签到地
     * @param orderInfoVO
     * @return
     */
    @Override
    public ResultUtil loadingQuery(TOrderInfoVO orderInfoVO) {
        ResultUtil resultUtil = new ResultUtil();
        TOrderInfo orderInfo = tOrderInfoMapper.selectByPrimaryKey(orderInfoVO.getId());
        //车辆信息
        TEndCarInfo tEndCarInfo = tEndCarInfoAPI.selectByPrimaryKey(orderInfo.getVehicleId());
        //根据车牌号获取车辆轨迹
        TransTimeManageVResp resultData = trajectoryRecentAPI.transTimeManageV3One(tEndCarInfo.getVehicleNumber()+"_"+tEndCarInfo.getLicensePlateColor(),1);
        if("1001".equals(resultData.getStatus())){
            //经度
            String lon = resultData.getLon();
            //纬度
            String lat = resultData.getLat();
            //根据运单表信息拿到线路id查询线路信息
            TLineInfo tLineInfo  = lineService.selectById(orderInfo.getLineId());
            String jd = tLineInfo.getEndCoordinates().split(",")[0];//经度
            String wd = tLineInfo.getEndCoordinates().split(",")[1];//纬度
            Double fenceRecognitionDistance = (Double) tLineInfo.getFenceRecognitionDistance();//电子围栏识别距离

            //根据两组经纬度计算距离 （km）
            Point2D pointDD = new Point2D.Double(Double.parseDouble(jd), Double.parseDouble(wd));
            Point2D pointXD = new Point2D.Double(Double.parseDouble(lon), Double.parseDouble(lat));
            //距离
            Double distance = ElectronicFence.getDistance(pointDD, pointXD);
            //车辆轨迹
            TrajectoryMongo trajectoryMongo = new TrajectoryMongo();
            trajectoryMongo.setId(IdWorkerUtil.getInstance().nextId());
            //插入运单轨迹表
            trajectoryMongo.setVehicleId(tEndCarInfo.getId());//车辆id
            trajectoryMongo.setOrderCode(orderInfo.getCode()); //运单id
            trajectoryMongo.setTrajectoryCode(IdWorkerUtil.getInstance().nextId());
            trajectoryMongo.setVehiclePlateNo(orderInfoVO.getVehicleNumber());
            trajectoryMongo.setTrajectoryReceiveTime(DateUtils.parseDate(resultData.getUtc()));
            trajectoryMongo.setTrajectoryReceiveMethod("1");//服务端自动抓取
            trajectoryMongo.setLongitude(resultData.getLon());//经度
            trajectoryMongo.setLatitudes(resultData.getLat());//纬度
            trajectoryMongo.setVehicleGeographyPosition(resultData.getAdr());//车辆地理位置信息
            trajectoryMongo.setVehicleCurrentSpeed(resultData.getSpd());//车辆当前速度
            trajectoryMongo.setVehicleCurrentDirection(resultData.getDrc());//车辆行驶方向
            if(distance<fenceRecognitionDistance){
                trajectoryMongo.setDataEnable(true);//行数据可用性
            }else{
                trajectoryMongo.setDataEnable(false);//行数据可用性
                trajectoryMongoDao.insert(trajectoryMongo);
                resultUtil.setCode("WDQDD");
                resultUtil.setMsg("车辆未在签到地！");
                return resultUtil;
            }
            trajectoryMongoDao.insert(trajectoryMongo);
        }
        return resultUtil;
    }

    /**
     * 查询车辆轨迹是否在卸货地
     * @param orderInfoVO
     * @return
     */
    @Override
    public ResultUtil dischargeQuery(TOrderInfoVO orderInfoVO) {
        ResultUtil resultUtil = new ResultUtil();
        TOrderInfo orderInfo = tOrderInfoMapper.selectByPrimaryKey(orderInfoVO.getId());
        //车辆信息
        TEndCarInfo tEndCarInfo = tEndCarInfoAPI.selectByPrimaryKey(orderInfo.getVehicleId());
        //根据车牌号获取车辆轨迹
        TransTimeManageVResp resultData = trajectoryRecentAPI.transTimeManageV3One(tEndCarInfo.getVehicleNumber()+"_"+tEndCarInfo.getLicensePlateColor(),1);
        if("1001".equals(resultData.getStatus())){
            //经度
            String lon = resultData.getLon();
            //纬度
            String lat = resultData.getLat();
            //根据运单表信息拿到线路id查询线路信息
            TLineInfo tLineInfo  = lineService.selectById(orderInfo.getLineId());
            String jd = tLineInfo.getEndCoordinates().split(",")[0];//经度
            String wd = tLineInfo.getEndCoordinates().split(",")[1];//纬度
            Double fenceRecognitionDistance = (Double) tLineInfo.getFenceRecognitionDistance();//电子围栏识别距离

            //根据两组经纬度计算距离 （km）
            Point2D pointDD = new Point2D.Double(Double.parseDouble(jd), Double.parseDouble(wd));
            Point2D pointXD = new Point2D.Double(Double.parseDouble(lon), Double.parseDouble(lat));
            //距离
            Double distance = ElectronicFence.getDistance(pointDD, pointXD);
            //车辆轨迹
            TrajectoryMongo trajectoryMongo = new TrajectoryMongo();
            trajectoryMongo.setId(IdWorkerUtil.getInstance().nextId());
            //插入运单轨迹表
            trajectoryMongo.setVehicleId(tEndCarInfo.getId());//车辆id
            trajectoryMongo.setOrderCode(orderInfo.getCode()); //运单id
            trajectoryMongo.setTrajectoryCode(IdWorkerUtil.getInstance().nextId());
            trajectoryMongo.setVehiclePlateNo(orderInfoVO.getVehicleNumber());
            trajectoryMongo.setTrajectoryReceiveTime(DateUtils.parseDate(resultData.getUtc()));
            trajectoryMongo.setTrajectoryReceiveMethod("2");//
            trajectoryMongo.setLongitude(resultData.getLon());//经度
            trajectoryMongo.setLatitudes(resultData.getLat());//纬度
            trajectoryMongo.setVehicleGeographyPosition(resultData.getAdr());//车辆地理位置信息
            trajectoryMongo.setVehicleCurrentSpeed(resultData.getSpd());//车辆当前速度
            trajectoryMongo.setVehicleCurrentDirection(resultData.getDrc());//车辆行驶方向
            if(distance<fenceRecognitionDistance){
                trajectoryMongo.setDataEnable(true);//行数据可用性
                //调用签到
                dischargeSingIn(orderInfoVO);
            }else{
                trajectoryMongo.setDataEnable(false);//行数据可用性
                trajectoryMongoDao.insert(trajectoryMongo);
                resultUtil.setCode("WDQDD");
                resultUtil.setMsg("车辆未在签到地！");
                return resultUtil;
            }
            trajectoryMongoDao.insert(trajectoryMongo);
        }
        return resultUtil;
    }

    @Override
    public ResultUtil transportAgreement(String orderCode){
        try{
            TransportAgreementVO transportAgreementVO = tOrderVehicleTrajectoryMapper.transportAgreement(orderCode);
            if(0.0000==transportAgreementVO.getEstimateGoodsWeight()||transportAgreementVO.getEstimateGoodsWeight().equals(0.0000)){
                transportAgreementVO.setEstimateGoodsWeight(transportAgreementVO.getPrimaryWeight());
            }
            transportAgreementVO.setLimitTime("30天");
            transportAgreementVO.setGoodsUnitPrice(null);//货值
            Date deliverWeightNotesTime = transportAgreementVO.getDeliverWeightNotesTime();//发货磅单时间
            if(null != deliverWeightNotesTime){
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(deliverWeightNotesTime);
                calendar.add(Calendar.DATE,3);
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                transportAgreementVO.setDepartureTime(format.format(deliverWeightNotesTime));//出发时间
                transportAgreementVO.setArrivalTime(format.format(calendar.getTime()));//到达时间--出发时间加3天
            }
            if(null != transportAgreementVO.getPlateColor()){
                if("1".equals(transportAgreementVO.getPlateColor())){
                    transportAgreementVO.setPlateColor(LicensePlateColorEnum.BLUE.value);
                } else if("2".equals(transportAgreementVO.getPlateColor())){
                    transportAgreementVO.setPlateColor(LicensePlateColorEnum.YELLOW.value);
                } else if("3".equals(transportAgreementVO.getPlateColor())){
                    transportAgreementVO.setPlateColor(LicensePlateColorEnum.KELLY.value);
                }
            }
            transportAgreementVO.setCountFreight(OrderMoneyUtil.transitionMoney(transportAgreementVO.getEstimateTotalFee().doubleValue()));//运费总额:大写
            transportAgreementVO.setTotalFreight(transportAgreementVO.getEstimateTotalFee());//运费总额:小写
            transportAgreementVO.setAdvancePayment(OrderMoneyUtil.transitionMoney(0D));//预付款：大写
            transportAgreementVO.setAdvance(BigDecimal.ZERO);//预付款：小写
            transportAgreementVO.setRemainingSum(OrderMoneyUtil.transitionMoney(0D));//余额：大写
            transportAgreementVO.setBalance(BigDecimal.ZERO);//余额：小写
            return ResultUtil.ok(transportAgreementVO);
        }catch (Exception e){
            log.error("货物运输协议加载失败",e);
            return ResultUtil.error("货物运输协议加载失败！");
        }
    }

    @Override
    public TOrderInfoWeight selectWeightById(Long aLong) {
        return orderInfoWeightMapper.selectByPrimaryKey(aLong);
    }

    //运费计算
    public BigDecimal calulateServiceFee(CompanySourceDTO companySourceDTO, BigDecimal userConfirmCarriagePayment, Double  settledWeight) {

        TOrderCastChanges tOrderCastChanges = new TOrderCastChanges();
        tOrderCastChanges.setShareMethod(companySourceDTO.getShareMethod());
        tOrderCastChanges.setShareValue(companySourceDTO.getShareValue());

        if (null != tOrderCastChanges.getShareMethod() && DictEnum.FIXEDPROPORTION.code.equals(tOrderCastChanges.getShareMethod())) {
            if (null != tOrderCastChanges.getShareValue() && tOrderCastChanges.getShareValue().compareTo(BigDecimal.ZERO) > 0) {
                if (null != userConfirmCarriagePayment
                        && userConfirmCarriagePayment.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal serviceFee = userConfirmCarriagePayment.setScale(2, RoundingMode.HALF_UP).multiply(tOrderCastChanges.getShareValue());
                    serviceFee = serviceFee.setScale(2, RoundingMode.HALF_UP);
                    return serviceFee;
                } else {
                    return BigDecimal.ZERO;
                }
            }
        } else if (null != tOrderCastChanges.getShareMethod() && DictEnum.SETTLEMENTWEIGHT.code.equals(tOrderCastChanges.getShareMethod())){
            if (null!=tOrderCastChanges.getShareValue() && tOrderCastChanges.getShareValue().compareTo(BigDecimal.ZERO )>0){
                if (null!=settledWeight && settledWeight>0){
                    return  BigDecimal.valueOf(settledWeight)
                            .multiply(tOrderCastChanges.getShareValue())
                            .setScale(2, RoundingMode.HALF_UP);
                }else {
                    return BigDecimal.ZERO;
                }
            }
        } else if (null != tOrderCastChanges.getShareMethod() && DictEnum.FIXEDLIMIT.code.equals(tOrderCastChanges.getShareMethod())) {
            if (null!=tOrderCastChanges.getShareValue() && tOrderCastChanges.getShareValue().compareTo(BigDecimal.ZERO )>0) {
                return tOrderCastChanges.getShareValue();
            }else {
                return BigDecimal.ZERO;
            }
        }
        return null;
    }

    private ResultUtil addTaskHhcc(TOrderInfo orderInfo,TEndUserInfo tEndUserInfo,TEndCarInfo tEndCarInfo,Integer status,String type){
        TOrderHhccVo orderHhccVo = new TOrderHhccVo();
        orderHhccVo.setDriverName(tEndUserInfo.getRealName());
        orderHhccVo.setContact(tEndUserInfo.getPhone());
        orderHhccVo.setCoaType(orderInfo.getGoodsName());
        orderHhccVo.setPrimary(orderInfo.getPrimaryWeight());
        if(null == orderInfo.getDeliverWeightNotesWeight() || "".equals(orderInfo.getDeliverWeightNotesWeight())){
            orderHhccVo.setActualHair(0d);
        }else{
            orderHhccVo.setActualHair(orderInfo.getDeliverWeightNotesWeight());
        }
        if(null == orderInfo.getReceiveWeightNotesWeight() || "".equals(orderInfo.getReceiveWeightNotesWeight())){
            orderHhccVo.setNetReceipts(0d);
        }else{
            orderHhccVo.setNetReceipts(orderInfo.getReceiveWeightNotesWeight());
        }
        orderHhccVo.setDepartureDate(orderInfo.getDeliverOrderTime());
        orderHhccVo.setDays(0);
        orderHhccVo.setStatus(status);//状态（0 发单- 1 装货- 2 卸货- 3 收单- 4 已刪除）
        orderHhccVo.setOrderNumber(orderInfo.getOrderBusinessCode());
        orderHhccVo.setOrderPlate(tEndCarInfo.getVehicleNumber());

        TTask tTask = new TTask();
        String requestParameter = JSONObject.toJSONString(orderHhccVo);
        tTask.setRequestParameter(requestParameter);
        tTask.setTaskId(IdWorkerUtil.getInstance().nextId());
        tTask.setTaskType("hhcc");//三分仓储对接
        tTask.setBusinessType(type);//三分仓储对接
        tTask.setTaskTypeNode("hhcc");//三分仓储对接
        tTask.setSourceTablename("T_ORDER_INFO");
        tTask.setSourceFieldname("code");
        tTask.setSourceFieldvalue(orderHhccVo.getOrderNumber());
        tTask.setRequestTimes(0);
        tTask.setCreateTime(new Date());
        tTask.setRequestDate(new Date());
        tTask.setIsSuccessed(false);
        tTask.setEnable(false);
        ResultUtil resultUtil = orderTaskServie.select(tTask);
        if(resultUtil.getData()!=null){
            return ResultUtil.error("此运单正在三分仓储对接请稍后！");
        }else{
            orderTaskMapper.insert(tTask);
        }
        return  ResultUtil.ok();
    }
   /* //公钥
    private final static String PUBLICKEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAi74w1e7Nu7516LqPwoYV/UM+45fJW6d1kECZ1fySS3Shv8SmHZJ2sPPFo0c/fBZIJ32AB0x3IitkOdecvs75oHLRkokC/RhKGwGx36iSr8mDqWeVke6bT/5+rVvZcukzdGsuyei1Eu0Rn1K387EfRhfphgGHUePrc27HIxOOeAyNKzC1rBe30j9fwuB3JT7YvGmTBmspNnyhXOMF1Jm8ar8xnUVRhMtN4GQ+15wXRzAM2GjpYOn6ipbEj2XSfKvg07j8hUbC93N7oLXWJkAgw5noT0tm6ELtfoyo8mUfO19o9UObi35Mvisq0ifnte3RuJ9c2aI+Lj+NgECLXWsm2QIDAQAB";

    public static void main(String[] args) throws Exception {
        TOrderHhccVo orderHhccVo = new TOrderHhccVo();
        orderHhccVo.setDriverName("胡肖锋");
        orderHhccVo.setContact("13620233801");
        orderHhccVo.setCoaType("肥煤");
        orderHhccVo.setPrimary(30d);
        orderHhccVo.setActualHair(30d);
        orderHhccVo.setNetReceipts(0d);
        orderHhccVo.setDepartureDate(new Date());
        orderHhccVo.setDays(0);
        orderHhccVo.setStatus(1);//状态（0 发单- 1 装货- 2 卸货- 3 收单- 4 已刪除）
        orderHhccVo.setOrderNumber("CT20230308164522520577");
        orderHhccVo.setOrderPlate("鲁H79A25");
        String json = RSAUtil.encrypt(JSONObject.toJSONString(orderHhccVo),PUBLICKEY);
        Map<String, String> params = new HashMap<>();
        params.put("jsonStr",json);
        JSONObject result = HttpClientUtil.sendPost("http://server.apsp.com.cn:82/api/tcpScales/tcpScales/featureLogistics/save",params);
        System.out.println(result.get("state"));
    }*/
}
