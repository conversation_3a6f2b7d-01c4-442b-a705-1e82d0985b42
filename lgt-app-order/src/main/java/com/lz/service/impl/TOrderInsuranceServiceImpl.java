package com.lz.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.model.CodeEnum;
import com.lz.common.util.ResultUtil;
import com.lz.dao.TOrderInsuranceMapper;
import com.lz.model.TOrderInsurance;
import com.lz.service.TOrderInsuranceService;
import com.lz.vo.TOrderInsuranceVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class TOrderInsuranceServiceImpl implements TOrderInsuranceService {
    @Autowired
    private TOrderInsuranceMapper orderInsuranceMapper;
    @Override
    public ResultUtil selectByPage(TOrderInsuranceVO search) {
        try{
            if(null != search.getTimeOfPayment() && search.getTimeOfPayment().length == 2){
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Instant instant = Instant.parse(search.getTimeOfPayment()[0]);
                ZonedDateTime zdt = instant.atZone(ZoneId.of("UTC"));
                Date from = Date.from(zdt.toInstant());
                search.setTimeOfPaymentStart(format.format(from));
                Instant instant1 = Instant.parse(search.getTimeOfPayment()[1]);
                ZonedDateTime zdt1 = instant1.atZone(ZoneId.of("UTC"));
                Date from1 = Date.from(zdt1.toInstant());
                search.setTimeOfPaymentEnd(format.format(from1));
            }
            if (null != search.getOrderExecuteStatus() && DictEnum.M050.code.equals(search.getOrderExecuteStatus())) {
                search.setOrderExecuteStatus(DictEnum.O060.code);
            }
            Date[] fhTime = search.getFhTime();
            if (fhTime != null) {
                search.setFhsTime(fhTime[0]);
                search.setFheTime(fhTime[1]);
            }
            Date[] fhbdTime = search.getFhbdTime();
            if (fhbdTime != null) {
                search.setFhbdsTime(fhbdTime[0]);
                search.setFhbdeTime(fhbdTime[1]);
            }
            Date[] shbdTime = search.getShbdTime();
            if (shbdTime != null) {
                search.setShbdsTime(shbdTime[0]);
                search.setShbdeTime(shbdTime[1]);
            }
            Date[] sh = search.getShTime();
            if (sh != null) {
                if (null != sh[0]) {
                    search.setShsTime(sh[0]);
                }
                if (null != sh[1]) {
                    search.setSheTime(sh[1]);
                }
            }
            //计算总金额
            List<TOrderInsuranceVO> list = orderInsuranceMapper.selectByPage(search);
            BigDecimal insuranceAmount = BigDecimal.ZERO;
            BigDecimal receivablePremium = BigDecimal.ZERO;
            for (TOrderInsuranceVO vo : list) {
                if(!vo.getInsuranceCancellation() && 0 != vo.getInsure() ){
                    if(null != vo.getOrderExecuteStatus() && DictEnum.M100.code.equals(vo.getOrderExecuteStatus())){
                        insuranceAmount = insuranceAmount.add(vo.getInsuredAmount());
                    }else{
                        receivablePremium = receivablePremium.add(vo.getInsuredAmount());
                    }
                }
            }
            try (Page<Object> page = PageHelper.startPage(search.getPage(), search.getSize())) {
                List<TOrderInsuranceVO> orderInsuranceList = orderInsuranceMapper.selectByPage(search);
                Map<String, Object> map = new HashMap<>();
                map.put("insuranceAmount", insuranceAmount);
                map.put("receivablePremium", receivablePremium);
                map.put("list", orderInsuranceList);
                return new ResultUtil(CodeEnum.SUCCESS.getCode(), map, page.getTotal());
            }catch (Exception e){
                log.error("selectByPage error",e);
            }
            return ResultUtil.error();
        }catch (Exception e){
            log.error("selectByPage error",e);
        }
        return null;
    }

    @Override
    public void updateByOrderBusinessCode(TOrderInsuranceVO search) {
        orderInsuranceMapper.updateByOrderBusinessCode(search);
    }

    @Override
    public TOrderInsurance selectByOrderBusinessCode(String orderBusinessCode) {
        try {
            return orderInsuranceMapper.selectByOrderBusinessCode(orderBusinessCode);
        }catch (Exception e){
            log.error("selectByOrderBusinessCode error",e);
        }
        return null;
    }

    @Override
    public void updateByPrimaryKeySelective(TOrderInsurance orderInsurance) {
        try {
            orderInsuranceMapper.updateByPrimaryKeySelective(orderInsurance);
        }catch (Exception e){
            log.error("updateByPrimaryKeySelective error",e);
        }
    }

    @Override
    public ResultUtil selectDataByEndDriverId(TOrderInsuranceVO search) {
        try(Page<Object> page = PageHelper.startPage(search.getPage(), search.getSize())) {
            List<TOrderInsuranceVO> list = orderInsuranceMapper.selectDataByEndDriverId(search.getEndDriverId());
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), list, page.getTotal());
        }catch (Exception e){
            log.error("selectDataByEndDriverId error",e);
        }
        return null;
    }

    @Override
    public ResultUtil orderInsuranceExport(TOrderInsuranceVO search) {
        try{
            Map<String, Object> map = new HashMap<>();
            if(null != search.getTimeOfPayment() && search.getTimeOfPayment().length == 2){
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Instant instant = Instant.parse(search.getTimeOfPayment()[0]);
                ZonedDateTime zdt = instant.atZone(ZoneId.of("UTC"));
                Date from = Date.from(zdt.toInstant());
                search.setTimeOfPaymentStart(format.format(from));
                Instant instant1 = Instant.parse(search.getTimeOfPayment()[1]);
                ZonedDateTime zdt1 = instant1.atZone(ZoneId.of("UTC"));
                Date from1 = Date.from(zdt1.toInstant());
                search.setTimeOfPaymentEnd(format.format(from1));
            }
            Date[] fhTime = search.getFhTime();
            if (fhTime != null) {
                search.setFhsTime(fhTime[0]);
                search.setFheTime(fhTime[1]);
            }
            Date[] fhbdTime = search.getFhbdTime();
            if (fhbdTime != null) {
                search.setFhbdsTime(fhbdTime[0]);
                search.setFhbdeTime(fhbdTime[1]);
            }
            Date[] shbdTime = search.getShbdTime();
            if (shbdTime != null) {
                search.setShbdsTime(shbdTime[0]);
                search.setShbdeTime(shbdTime[1]);
            }
            Date[] sh = search.getShTime();
            if (sh != null) {
                if (null != sh[0]) {
                    search.setShsTime(sh[0]);
                }
                if (null != sh[1]) {
                    search.setSheTime(sh[1]);
                }
            }
            List<TOrderInsuranceVO> list = orderInsuranceMapper.selectByPage(search);
            String[] headers =
                    {
                            "运单号", "是否已投保", "司机姓名", "司机手机号", "运单状态",
                            "车牌号", "车队长姓名", "车队长电话", "发货时间", "装货(磅单)时间",
                            "卸货(磅单)时间", "收货时间", "支付时间", "货源名称", "企业名称",
                            "承运方名称", "保险金额（元）","资金状态","未投保原因"
                    };
            String[] names =
                    {
                            "orderBusinessCode", "insureStr", "driverName", "driverPhone", "orderExecuteStatusValue",
                            "vehicleNumber", "captainName", "captainPhone", "deliverOrderTime", "deliverWeightNotesTime",
                            "receiveWeightNotesTime", "receiveOrderTime", "orderFinishTime", "goodsName", "companyName",
                            "carrierName", "insuredAmount","orderPayStatusValue", "uninsuredCause"
                    };
            map.put("headers", headers);
            map.put("names", names);
            map.put("list", list);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), map);
        }catch (Exception e){
            log.error("selectByPage error",e);
        }
        return null;
    }

}
