package com.lz.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lz.api.TEndSUserInfoAPI;
import com.lz.common.dbenum.BusinessCode;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.CodeEnum;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.OrderMoneyUtil;
import com.lz.common.util.RandomUtil;
import com.lz.common.util.ResultUtil;
import com.lz.dao.*;
import com.lz.dto.OrderPackDTO;
import com.lz.dto.OrderSundryFeeDTO;
import com.lz.dto.TOrderInfoDTO;
import com.lz.dto.TOrderPayInfoDTO;
import com.lz.example.TOrderPackInfoExample;
import com.lz.model.TOrderCastChanges;
import com.lz.model.TOrderInfo;
import com.lz.model.TOrderPackInfo;
import com.lz.model.TOrderPayDetail;
import com.lz.payment.Payment;
import com.lz.schedule.model.TTask;
import com.lz.service.TOrderInfoService;
import com.lz.service.TOrderPackInfoService;
import com.lz.util.DateTimeUtil;
import com.lz.util.OrderStateJudgeFilter;
import com.lz.util.OrderUtil;
import com.lz.util.StringUtils;
import com.lz.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Created by sangbin on 2019/4/23.
 */
@Slf4j
@Service
public class TOrderPackInfoServiceImpl implements TOrderPackInfoService {
    /** 系统运单号，开头 */
    private static final String BIZHEADER = "DB";

    @Autowired
    TOrderPackInfoMapper tOrderPackInfoMapper;
    @Autowired
    private TEndSUserInfoAPI tEndSUserInfoAPI;
    @Autowired
    private TOrderInfoMapper tOrderInfoMapper;
    @Resource
    private TOrderInfoMapper orderInfoMapper;
    @Autowired
    private TOrderPackInfoMapper orderPackInfoMapper;
    @Autowired
    TOrderPackDetailMapper orderPackDetailMapper;
    @Autowired
    private TOrderInfoService orderInfoService;
    @Autowired
    private TOrderCastChangesMapper orderCastChangesMapper;

    @Resource
    TOrderPayDetailMapper orderPayDetailMapper;

    @Resource
    private TOrderTaskMapper orderTaskMapper;


    @Override
    public TOrderPackInfo selectByCode(String code) {
        TOrderPackInfoExample ex = new TOrderPackInfoExample();
        TOrderPackInfoExample.Criteria c = ex.createCriteria();
        c.andCodeEqualTo(code);
        c.andEnableEqualTo(false);
        return tOrderPackInfoMapper.selectByExample(ex).get(0);
    }

    /**
     * 运单过程管理-运单付款-生成批量支付记录
     * 运单打包
     * Yan
     * @return
     */
    @Override
    public ResultUtil orderPackInfo(OrderPackVO packVO) {
        // 判断运单是否符合打包规则
        Map<String, Object> judge = OrderStateJudgeFilter.judge(packVO.getCodes());
        if ((boolean) judge.get("bool")) {
            String capitalTransfer = judge.get("capitalTransfer").toString();
            String endUserId = "";
            switch (capitalTransfer) {
                case "PAYTODRIVER":
                    endUserId = judge.get("driver").toString();
                    break;
                    //车队长/车辆所有人
                case"PAYTOCAPTAIN":
                case "PAYTOBELONGER":
                    if (null != judge.get("owner") && StringUtils.isNotEmpty(judge.get("owner").toString())){
                        endUserId = judge.get("owner").toString();
                    } else {
                        return ResultUtil.error("未找到车主");
                    }
                    break;
                case "FIRSTBROKERDRIVER":
                case "FIRSTBROKERBELONGER":
                    if (capitalTransfer.equals("FIRSTBROKERDRIVER")){
                        if (null != judge.get("driver") && StringUtils.isNotEmpty(judge.get("driver").toString())){
                            endUserId = judge.get("driver").toString();
                        } else {
                            return ResultUtil.error("未找到司机");
                        }

                    }
                    if (capitalTransfer.equals("FIRSTBROKERBELONGER")){
                        if (null != judge.get("owner") && StringUtils.isNotEmpty(judge.get("owner").toString())){
                            endUserId = judge.get("owner").toString();
                        } else {
                            return ResultUtil.error("未找到车主");
                        }
                    }
                    break;
                default:
                    return ResultUtil.error("资金方式CODE错误");
            }

            Map<String, Object> rest = new HashMap<>();

            TEndUserInfoSearchVO euis = new TEndUserInfoSearchVO();
            euis.setEnduserId(Integer.parseInt(endUserId));
            if (packVO.getPaymentPlatforms().equals(DictEnum.JDPLATFORMS.code)) {
                ResultUtil res = tEndSUserInfoAPI.getUserBankCard(euis);
                ArrayList userCard = (ArrayList)res.getData();
                rest.put("card", userCard);
            } else if (DictEnum.HXPLATFORMS.code.equals(packVO.getPaymentPlatforms())) {

            }

            Map<String, Object> sumDispatchCarriage = tOrderInfoMapper.getSumDispatchCarriage(packVO.getCodes());
            rest.put("capitalTransfer", capitalTransfer);
            rest.put("fee", sumDispatchCarriage);
            rest.put("capitalTransferPattern", judge.get("capitalTransferPattern"));
            return ResultUtil.ok(rest);
        } else {
            return ResultUtil.error(judge.get("msg").toString());
        }
    }

    /**
     * 运单打包付款列表
     * 已经打好包的运单  未支付的
     * Yan
     * @param search
     * @return
     */
    @Override
    public ResultUtil getOrderFinishedPack(PcOrderSearchVO search) throws RuntimeException {
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        Map<String,Object> result= new HashMap<>();
        if (null == userCompanyId) {
            throw new RuntimeException("该账号没有任何企业信息，请联系企业管理员");
        }
        List<Integer> companyId = new ArrayList<>();
        for (String uc : userCompanyId) {
            companyId.add(Integer.parseInt(uc));
        }
        search.setCompanyIds(companyId);
        if (search.getDriverOrAgent() != null && search.getDriverOrAgent() != "") {
            String driverOrAgent = search.getDriverOrAgent();
            search.setDriverOrAgent(driverOrAgent.trim());
            List<Integer> packId = tOrderPackInfoMapper.getOrderPackByDriverAgent(search);
            search.setPackId(packId);
        }
        Date[] packTimes = search.getPackTimes();
        if (null != packTimes) {
            if (null != packTimes[0]) {
                search.setStartTime(packTimes[0]);
            }
            if (null != packTimes[1]) {
                search.setEndTime(packTimes[1]);
            }
        }
        Date[] txTime = search.getTxTime();
        if (null != txTime) {
            if (null != txTime[0]) {
                search.setTxStartTime(txTime[0]);
            }
            if (null != txTime[1]) {
                search.setTxEndTime(txTime[1]);
            }
        }
        OrderUtil.levelSort(search);

        Long total = 0L;
        if (search.isOrderWeight()) {
            // 查询合计
            Map<String, Object> sum = tOrderPackInfoMapper.getOrderPackNoSUM(search);
            result.put("weight", sum);
        } else {
            // 查询列表
            Page<Object> objects = PageHelper.startPage(search.getPage(), search.getSize());
            List<OrderPackDTO> orderPackAll = tOrderPackInfoMapper.getOrderPackNo(search);
            total = objects.getTotal();
            result.put("waybill", orderPackAll);
        }

        return new ResultUtil(CodeEnum.SUCCESS.getCode(), result, total);
    }

    /**
     * 运单打包付款列表
     * 已经打好包的运单  已支付的
     * Yan
     * @param search
     * @return
     */
    @Override
    public ResultUtil getOrderFinishedPackOk(PcOrderSearchVO search) throws RuntimeException {
        String userType = CurrentUser.getUsertype();
        Map<String,Object> result =new HashMap<>();
        if(DictEnum.BD.code.equals(userType)){
            List<String> userCompanyId = CurrentUser.getUserCompanyId();
            if (null == userCompanyId) {
                throw new RuntimeException("该账号没有任何企业信息，请联系企业管理员");
            }
            List<Integer> companyId = new ArrayList<>();
            for (String uc : userCompanyId) {
                companyId.add(Integer.parseInt(uc));
            }
            search.setCompanyIds(companyId);
        }
        if (search.getDriverOrAgent() != null && search.getDriverOrAgent() != "") {
            String driverOrAgent = search.getDriverOrAgent();
            search.setDriverOrAgent(driverOrAgent.trim());
            List<Integer> packId = tOrderPackInfoMapper.getOrderPackByDriverAgent(search);
            search.setPackId(packId);
        }
        Date[] packTimes = search.getPackTimes();
        if (null != packTimes) {
            if (null != packTimes[0]) {
                search.setStartTime(packTimes[0]);
            }
            if (null != packTimes[1]) {
                search.setEndTime(packTimes[1]);
            }
        }


        Date[] fkTimes = search.getFkTime();
        if (null != fkTimes) {
            if (null != fkTimes[0]) {
                search.setFkStartTime(fkTimes[0]);
            }
            if (null != fkTimes[1]) {
                search.setFkEndTime(fkTimes[1]);
            }
        }


        Date[] txTimes = search.getTxTime();
        if (null != txTimes) {
            if (null != txTimes[0]) {
                search.setTxStartTime(txTimes[0]);
            }
            if (null != txTimes[1]) {
                search.setTxEndTime(txTimes[1]);
            }
        }

        Date[] dzTimes = search.getDzTime();
        if (null != dzTimes) {
            if (null != dzTimes[0]) {
                search.setDzStartTime(dzTimes[0]);
            }
            if (null != dzTimes[1]) {
                search.setDzEndTime(dzTimes[1]);
            }
        }

        OrderUtil.levelSort(search);

        Long total = 0L;
        if (search.isOrderWeight()) {
            // 查询合计
            Map<String, Object> sum = tOrderPackInfoMapper.getOrderPackOkSUM(search);
            result.put("weight", sum);
        } else {
            // 列表查询
            Page<Object> objects = PageHelper.startPage(search.getPage(), search.getSize());
            List<OrderPackDTO> orderPackAll = tOrderPackInfoMapper.getOrderPackOk(search);
            total = objects.getTotal();
            result.put("waybill", orderPackAll);
        }

        return new ResultUtil(CodeEnum.SUCCESS.getCode(), result, total);
    }


    /**
     * 运单打包付款列表
     * 已经打好包的运单  已提现
     * Yan
     * @param search
     * @return
     */
    @Override
    public ResultUtil getOrderFinishedPackTxOk(PcOrderSearchVO search) throws RuntimeException {
        Map<String,Object> result=new HashMap<>();
        String userType = CurrentUser.getUsertype();
        if(DictEnum.BD.code.equals(userType)){
            List<String> userCompanyId = CurrentUser.getUserCompanyId();
            if (null == userCompanyId) {
                throw new RuntimeException("该账号没有任何企业信息，请联系企业管理员");
            }
            List<Integer> companyId = new ArrayList<>();
            for (String uc : userCompanyId) {
                companyId.add(Integer.parseInt(uc));
            }
            search.setCompanyIds(companyId);
        }
        if (search.getDriverOrAgent() != null && search.getDriverOrAgent() != "") {
            String driverOrAgent = search.getDriverOrAgent();
            search.setDriverOrAgent(driverOrAgent.trim());
            List<Integer> packId = tOrderPackInfoMapper.getOrderPackByDriverAgent(search);
            search.setPackId(packId);
        }
        Date[] packTimes = search.getPackTimes();
        if (null != packTimes) {
            if (null != packTimes[0]) {
                search.setStartTime(packTimes[0]);
            }
            if (null != packTimes[1]) {
                search.setEndTime(packTimes[1]);
            }
        }


        Date[] fkTimes = search.getFkTime();
        if (null != fkTimes) {
            if (null != fkTimes[0]) {
                search.setFkStartTime(fkTimes[0]);
            }
            if (null != fkTimes[1]) {
                search.setFkEndTime(fkTimes[1]);
            }
        }


        Date[] txTimes = search.getTxTime();
        if (null != txTimes) {
            if (null != txTimes[0]) {
                search.setTxStartTime(txTimes[0]);
            }
            if (null != txTimes[1]) {
                search.setTxEndTime(txTimes[1]);
            }
        }

        Date[] dzTimes = search.getDzTime();
        if (null != dzTimes) {
            if (null != dzTimes[0]) {
                search.setDzStartTime(dzTimes[0]);
            }
            if (null != dzTimes[1]) {
                search.setDzEndTime(dzTimes[1]);
            }
        }
        String userLogisticsRoles = CurrentUser.getUserLogisticsRole();
        if (null != userLogisticsRoles) {
            if (userLogisticsRoles.contains(DictEnum.CTYPEMANAGER.code)) {
                search.setEndAgent(true);
                search.setEndAgentId(CurrentUser.getEndUserId());
            } else if (userLogisticsRoles.contains(DictEnum.CTYPEBOSS.code)) {
                search.setCarOwner(true);
                search.setCarOwnerId(CurrentUser.getEndUserId());
            }else if (userLogisticsRoles.contains(DictEnum.CTYPECAPTAIN.code)) {
                search.setCarCaptain(true);
                search.setCarCaptainId(CurrentUser.getEndUserId());
            }
        }
        Page<Object> objects =  PageHelper.startPage(search.getPage(), search.getSize());

        List<OrderPackDTO> orderPackAll = tOrderPackInfoMapper.getOrderPackTxOk(search);
        result.put("waybill",orderPackAll);
        if(search.isOrderWeight()){
           Map<String,Object> sum= tOrderPackInfoMapper.getOrderPackTxOkSUM(search);
           result.put("weight",sum);
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), result, objects.getTotal());
    }


    /**
     * 运单打包付款列表
     * 已经打好包的运单  已支付的
     * Yan
     * @param search
     * @return
     */
    @Override
    public ResultUtil finishedPackExproExcel(PcOrderSearchVO search) throws RuntimeException {
        String userType = CurrentUser.getUsertype();
        if(DictEnum.BD.code.equals(userType)){
            List<String> userCompanyId = CurrentUser.getUserCompanyId();
            if (null == userCompanyId) {
                throw new RuntimeException("该账号没有任何企业信息，请联系企业管理员");
            }
            List<Integer> companyId = new ArrayList<>();
            for (String uc : userCompanyId) {
                companyId.add(Integer.parseInt(uc));
            }
            search.setCompanyIds(companyId);
        }
        if (search.getDriverOrAgent() != null && search.getDriverOrAgent() != "") {
            String driverOrAgent = search.getDriverOrAgent();
            search.setDriverOrAgent(driverOrAgent.trim());
            List<Integer> packId = tOrderPackInfoMapper.getOrderPackByDriverAgent(search);
            search.setPackId(packId);
        }
        Date[] packTimes = search.getPackTimes();
        if (null != packTimes) {
            if (null != packTimes[0]) {
                search.setStartTime(packTimes[0]);
            }
            if (null != packTimes[1]) {
                search.setEndTime(packTimes[1]);
            }
        }


        Date[] fkTimes = search.getFkTime();
        if (null != fkTimes) {
            if (null != fkTimes[0]) {
                search.setFkStartTime(fkTimes[0]);
            }
            if (null != fkTimes[1]) {
                search.setFkEndTime(fkTimes[1]);
            }
        }


        Date[] txTimes = search.getTxTime();
        if (null != txTimes) {
            if (null != txTimes[0]) {
                search.setTxStartTime(txTimes[0]);
            }
            if (null != txTimes[1]) {
                search.setTxEndTime(txTimes[1]);
            }
        }

        Date[] dzTimes = search.getDzTime();
        if (null != dzTimes) {
            if (null != dzTimes[0]) {
                search.setDzStartTime(dzTimes[0]);
            }
            if (null != dzTimes[1]) {
                search.setDzEndTime(dzTimes[1]);
            }
        }
        List<OrderPackDTO> orderPackAll = new ArrayList<>();
        if (null != search.getLevelName() && null != search.getLevel()
                && StringUtils.isNotBlank(search.getLevelName())) {
            OrderUtil.levelSort(search);
        }
        String userLogisticsRoles = CurrentUser.getUserLogisticsRoles();
        if (null != userLogisticsRoles) {
            if (userLogisticsRoles.contains(DictEnum.CTYPEMANAGER.code)) {
                search.setEndAgent(true);
                search.setEndAgentId(CurrentUser.getEndUserId());
            } else if (userLogisticsRoles.contains(DictEnum.CTYPEBOSS.code)) {
                search.setCarOwner(true);
                search.setCarOwnerId(CurrentUser.getEndUserId());
            }else if (userLogisticsRoles.contains(DictEnum.CTYPECAPTAIN.code)) {
                search.setCarCaptain(true);
                search.setCarCaptainId(CurrentUser.getEndUserId());
            }
        }
        if("0".equals(search.getExproType())){//未支付打包运单
            orderPackAll = tOrderPackInfoMapper.getOrderPackNo(search);
        }else if("1".equals(search.getExproType())){//已支付打包运单
            orderPackAll = tOrderPackInfoMapper.getOrderPackOk(search);
        }else{
            orderPackAll = tOrderPackInfoMapper.getOrderPackTxOk(search);

        }
        Map<String,Object> map = new HashMap<>();
        String[] headers =
                {
                        "虚拟单号", "虚拟单据状态", "企业用户", "运输人", "联系方式", "累计单据数", "累计重量(吨)",
                        "累计总运费(元)", "累计总调度费(元)", "累计总运费扣款(元)", "确认线上应付运费(元)", "确认线下应付运费(元)",
                        "确认线上调度费(元)", "资金转移方式", "约定内容描述", "打包时间", "持卡人", "银行卡号", "提现时间","货物类型","支付平台"
                };
        String[] names =
                {
                        "virtualOrderNo","packStatusValue","checkAccountPerson","realName","phone","totalSelectedOrders",
                        "totalSelectedOrdersWeight","totalSelectedOrdersCarriageFee","totalSelectedOrdersDispatchFee","totalSelectedOrdersCarriageZeroCutFee",
                        "appointmentPaymentCash","appointmentPaymentOther","recountDispatchFee","capitalTransferType","contentDescribtion","createTimes",
                        "cardOwner","cardNo","yfkTxTime","goodsName","paymentPlatforms"
                };
        map.put("headers",headers);
        map.put("names",names);
        map.put("list",orderPackAll);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), map);
    }

    /**
     * 运单打包
     * Yan
     * @param pack
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil orderPack(OrderPackVO pack) {
        String code = IdWorkerUtil.getInstance().nextId();
        List<String> codes = pack.getCodes();
        // 打包子表
        CopyOnWriteArrayList<OrderPackDetailVO> listDetail = new CopyOnWriteArrayList<>();
        log.info("原始运单号，{}", JSONUtil.toJsonStr(codes));
        log.info("原始运单个数，{}", codes.size());
        for (String item : codes) {
            log.info(item);
            OrderPackDetailVO packDetail = new OrderPackDetailVO();
            try {
                Thread.sleep(1);
            } catch (Exception e) {
                e.printStackTrace();
            }
            String detailCode = IdWorkerUtil.getInstance().nextId();
            packDetail.setCode(detailCode);
            packDetail.setPackPayCode(code);
            packDetail.setOrderCode(item);
            packDetail.setRemark(pack.getRemark());
            packDetail.setCreateTime(new Date());
            packDetail.setCreateUser(CurrentUser.getUserNickname());
            listDetail.add(packDetail);
        }

        List<OrderSundryFeeDTO> orderSundryFee = new ArrayList<>();
        try {
            /*log.info("判断运单是否符合打包规则");
            // 判断运单是否符合打包规则
            Map<String, Object> judge = OrderStateJudgeFilter.judge(pack.getCodes());
            log.info("判断运单是否符合打包规则结束");*/
            String username = CurrentUser.getUserNickname();

            // 运单的各项费用
            orderSundryFee = orderInfoMapper.selectOrderSundryFee(pack.getCodes());
            Map<String, Object> map = countOrder(orderSundryFee);

            // 打包主表
            String bizCode = BIZHEADER + DateTimeUtil.getBizTime() + RandomUtil.randomString(3, 10);

            TOrderPackInfo orderPackInfo = new TOrderPackInfo();
            orderPackInfo.setCode(code);
            if (null != pack.getOwner()) {
                orderPackInfo.setEndUserId((Integer)pack.getOwner());
            }
            orderPackInfo.setCompanyId(pack.getCompany());
            orderPackInfo.setCarrierId(pack.getCarrier());
            if (null != pack.getEndUserIds()) {
                String userId = pack.getEndUserIds().toString();
                orderPackInfo.setEndUserId(Integer.valueOf(userId));
            }
            orderPackInfo.setPaymentPlatforms(DictEnum.WSPLATFORMS.code);
            orderPackInfo.setVirtualOrderNo(bizCode);
            orderPackInfo.setCheckAccountPerson(username);
            orderPackInfo.setTotalSelectedOrders(codes.size());
            orderPackInfo.setTotalSelectedOrdersCarriageZeroCutFee(pack.getCountErase());
            orderPackInfo.setTotalSelectedOrdersCarriageFee(pack.getCountFreight());
            orderPackInfo.setTotalSelectedOrdersDispatchFee(pack.getDispatch());
            orderPackInfo.setTotalSelectedOrdersServiceFee((BigDecimal) map.get("service"));
            orderPackInfo.setTotalSelectedOrdersOtherFee((BigDecimal) map.get("other"));
            orderPackInfo.setTotalSelectedOrdersFee((BigDecimal) map.get("total"));
            orderPackInfo.setTotalSelectedOrdersWeight((double) map.get("weight"));
            orderPackInfo.setAppointmentPaymentCash(pack.getAppointmentPaymentCash());
            orderPackInfo.setContentDescribtion(pack.getRemark());
            orderPackInfo.setAppointmentPaymentOther(pack.getAppointmentPaymentOther());
            orderPackInfo.setBankCardId(pack.getBankCardId());
            orderPackInfo.setPackStatus(DictEnum.PACKED.code);
            orderPackInfo.setEnable(false);


            //定时任务类型代码
           /* // 判断选择总运单的调度费是否等于用户输入的确认运费， 如果相等，重新计算的调度费 = 选择总运单的调度费
            BigDecimal currentDispatchRate = BigDecimal.ZERO;
            if (orderPackInfo.getTotalSelectedOrdersCarriageFee().compareTo(orderPackInfo.getAppointmentPaymentCash()) == 0) {
                orderPackInfo.setRecountDispatchFee(orderPackInfo.getTotalSelectedOrdersDispatchFee());

            } else {
                //计算调度费
                //调度费=运费/（1-调度费系数）-运费
                TOrderCastChanges castChanges = orderCastChangesMapper.selectByNewOne(codes.get(0));
                currentDispatchRate = castChanges.getCurrentDispatchRate();
                BigDecimal disFee= OrderMoneyUtil.getDispatchFee(castChanges.getCurrentDispatchRate(), orderPackInfo.getAppointmentPaymentCash());
                if(disFee == null || disFee.doubleValue() < 0){
                    return  ResultUtil.error("调度费为负数，无法完成支付！");
                }
                orderPackInfo.setRecountDispatchFee(disFee);
            }

            List<OrderSundryFeeDTO> finalOrderSundryFee = orderSundryFee;
            BigDecimal divide = BigDecimal.ONE;
            if (orderPackInfo.getTotalSelectedOrdersCarriageFee().compareTo(orderPackInfo.getAppointmentPaymentCash()) != 0) {
                //运单分摊比例: 协商的打包运费总价/运单汇总运费，取两位小数
                divide = orderPackInfo.getAppointmentPaymentCash().divide(orderPackInfo.getTotalSelectedOrdersCarriageFee(), 2, BigDecimal.ROUND_DOWN);
                //运单分摊比例: 协商的打包运费总价/运单汇总运费，取两位小数
                orderPackInfo.setShareCoefficient(divide);
            }
            //orderPackInfoMapper.insertSelective(orderPackInfo);
            if (divide.compareTo(BigDecimal.ONE) == 0) {//修改sql 后期优化成一个sql
                orderInfoMapper.updateOrginOrderCarriageFeeAndDispatchFee(finalOrderSundryFee);
                orderPackInfo.setLsCalculation(true);
            }else if(orderPackInfo.getShareCoefficient().compareTo(BigDecimal.ZERO) == 0){
                throw new RuntimeException("确认运费与选中总运费计算系数为0，不能打包!");
            }else{
                orderPackInfo.setLsCalculation(false);
                orderInfoMapper.updateByPackStatusBath(codes);
                TTask task = new TTask();
                task.setTaskId(IdWorkerUtil.getInstance().nextId());
                task.setTaskType("YDDB");//任务类型
                task.setTaskTypeNode("YDDB");//任务类型节点
                task.setBusinessType("DB");//业务类型
                task.setSourceTablename("t_order_pack_info");
                task.setSourcekeyFieldname("id");
                task.setSourceFieldname("code");
                task.setSourceFieldvalue(orderPackInfo.getCode());
                task.setRequestTimes(0);
                //存入参数
                Map<String,Object> mapReq = new HashMap<>();
                mapReq.put("divide",divide);
                mapReq.put("orderPackInfo",orderPackInfo);
                mapReq.put("currentDispatchRate",currentDispatchRate);
                task.setRequestParameter(JSONObject.toJSONString(mapReq));
                task.setRequestDate(new Date());
                task.setIsSuccessed(false);
                task.setEnable(false);
                task.setRemark(orderPackInfo.getCompanyId().toString());
                task.setCreateTime(new Date());
                orderTaskMapper.insert(task);

                orderPackInfoMapper.insertSelective(orderPackInfo);

                orderPackDetailMapper.insertOrderPackDetail(listDetail);

            }*/

            // 判断选择总运单的调度费是否等于用户输入的确认运费， 如果相等，重新计算的调度费 = 选择总运单的调度费
            BigDecimal currentDispatchRate = BigDecimal.ZERO;
            if (orderPackInfo.getTotalSelectedOrdersCarriageFee().compareTo(orderPackInfo.getAppointmentPaymentCash()) == 0) {
                orderPackInfo.setRecountDispatchFee(orderPackInfo.getTotalSelectedOrdersDispatchFee());
            } else {
                //计算调度费
                //调度费=运费/（1-调度费系数）-运费
                TOrderCastChanges castChanges = orderCastChangesMapper.selectByNewOne(codes.get(0));
                currentDispatchRate = castChanges.getCurrentDispatchRate();
                BigDecimal disFee= OrderMoneyUtil.getDispatchFee(castChanges.getCurrentDispatchRate(), orderPackInfo.getAppointmentPaymentCash());
                if(disFee == null || disFee.doubleValue() < 0){
                    return  ResultUtil.error("调度费为负数，无法完成支付！");
                }
                orderPackInfo.setRecountDispatchFee(disFee);
            }

            List<OrderSundryFeeDTO> finalOrderSundryFee = orderSundryFee;
            BigDecimal divide = BigDecimal.ONE;
            if (orderPackInfo.getTotalSelectedOrdersCarriageFee().compareTo(orderPackInfo.getAppointmentPaymentCash()) != 0) {
                //运单分摊比例: 协商的打包运费总价/运单汇总运费，取两位小数
                divide = orderPackInfo.getAppointmentPaymentCash().divide(orderPackInfo.getTotalSelectedOrdersCarriageFee(), 2, BigDecimal.ROUND_DOWN);
                //运单分摊比例: 协商的打包运费总价/运单汇总运费，取两位小数
                orderPackInfo.setShareCoefficient(divide);
            }
            orderPackInfo.setLsCalculation(true);
            orderPackInfoMapper.insertSelective(orderPackInfo);

            log.info("更新原始运单中的打包后的运费和调度费");
            BigDecimal finalDivide = divide;
            BigDecimal finalCurrentDispatchRate = currentDispatchRate;
            CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() ->{
                // 如果选中运单的运费 = 用户确认的运费
                if (finalDivide.compareTo(BigDecimal.ONE) == 0) {
                    orderInfoMapper.updateOrginOrderCarriageFeeAndDispatchFee(finalOrderSundryFee);
                } else {
                    // 根据分摊系数，重新计算原始运单的运费和调度费
                    updateOrderSharePaymentAndDispatch(pack, orderPackInfo, finalDivide, finalCurrentDispatchRate);
                }
                return true;
            });
            log.info("更新原始运单中的打包后的运费和调度费结束");

            log.info("插入打包子表");

            orderPackDetailMapper.insertOrderPackDetail(listDetail);
            log.info("插入打包子表结束");
            // 原始单据的钱解冻-在冻结
            Payment py = new Payment();
            py.DBFrozen(orderPackInfo);

            // 获取修改原始运单：打包后的运费和调度费 的结果
            if (null != future) {
                Boolean result = future.get();
                if (!result) {
                    throw new RuntimeException("ZJJ-039:运单打包失败!");
                }
            }

        } catch (Exception e) {
            log.error("ZJJ-039:运单打包失败! {}", e);
            // 恢复原始运单 打包后的运费和调度费 : 置为null
            if (!orderSundryFee.isEmpty()) {
                orderInfoService.recoverOrginOrderCarriageFeeAndDispatchFee(orderSundryFee);
            }
            log.error("ZJJ-039:运单打包失败!", e);
            String message = "";
            if (null != e.getMessage()) {
                message = e.getMessage();
            }
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)){
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再打包。";
            }
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)){
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再打包";
            }
            if (message.contains("SHAREPAYMENTTOOLOW")) {
                message = "打包后的运单运费不能小于等于0";
            }
            if (message.contains("SHAREDISPATCHFEE")) {
                message = "打包后的运单调度费不能小于0";
            }
            if (com.lz.common.util.StringUtils.checkChineseCharacter(message)){
                throw new RuntimeException(message);
            }
            throw new RuntimeException("ZJJ-039:运单打包失败!");
        }
        return ResultUtil.ok();
    }


    /**
     *  @author: dingweibo
     *  @Date: 2021/1/18 14:49
     *  @Description: 运单打包task处理
     */
    @Override
    public ResultUtil orderDbTask(TTask tTask){
        try{
            String requestParameter = tTask.getRequestParameter();
            Map map = JSONObject.parseObject(requestParameter,Map.class);
            TOrderPackInfo orderPackInfo = JSONObject.parseObject(map.get("orderPackInfo").toString(),TOrderPackInfo.class);
            BigDecimal currentDispatchRate =  new BigDecimal(map.get("currentDispatchRate").toString());
            BigDecimal divide =  new BigDecimal(map.get("divide").toString());
            List<String> codes = orderPackDetailMapper.orderPackDetailInfo(orderPackInfo.getCode());
            if (codes.size() == 1) {
                String orderCode = codes.get(0);
                // 如果分摊系数不为空
                if (null != orderPackInfo.getShareCoefficient()) {
                    //保存运单主表打包后的运费和调度费
                    BigDecimal sharePaymentAmount = orderPackInfo.getAppointmentPaymentCash();
                    BigDecimal shareDispatchFee = orderPackInfo.getRecountDispatchFee();
                    tOrderInfoMapper.updateOrderSahrePaymentAndDispatchFee(sharePaymentAmount, shareDispatchFee, orderCode);
                }
            } else {
                String lastCode = null;
                if (null != orderPackInfo.getShareCoefficient()) {
                    // 取出最后一个运单
                    lastCode = codes.remove(codes.size() - 1);
                    BigDecimal totalCarriageAmount = BigDecimal.ZERO;
                    BigDecimal totalDispatchFee = BigDecimal.ZERO;
                    List<TOrderInfoDTO> tOrderInfoDTOS = tOrderInfoMapper.selectOrderInfoByCodes(codes);
                    List<TOrderInfo> tOrderInfoList = new ArrayList<>();
                    for (TOrderInfoDTO orderInfoDTO : tOrderInfoDTOS) {

                        BigDecimal sharePaymentAmount = orderInfoDTO.getUserConfirmPaymentAmount().multiply(divide)
                                .setScale(2, BigDecimal.ROUND_HALF_UP);
                        if (sharePaymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
                            log.error("运单打包Task处理失败!运单code："+orderInfoDTO.getCode(), "sharePaymentAmount:SHAREPAYMENTTOOLOW，运单code："+orderInfoDTO.getCode());
                            return ResultUtil.error("运单打包Task处理失败!sharePaymentAmount:SHAREPAYMENTTOOLOW，运单code："+orderInfoDTO.getCode());
                        }
                        // 根据分摊后的运费，重新计算调度费
                        BigDecimal dispatchFee = OrderMoneyUtil.getDispatchFee(currentDispatchRate, sharePaymentAmount);
                        totalCarriageAmount = totalCarriageAmount.add(sharePaymentAmount);
                        totalDispatchFee = totalDispatchFee.add(dispatchFee);
                        TOrderInfo orderInfo = new TOrderInfo();
                        orderInfo.setId(orderInfoDTO.getId());
                        orderInfo.setSharePaymentAmount(sharePaymentAmount);
                        orderInfo.setShareDispatchFee(dispatchFee);
                        orderInfo.setPackStatus("1");
                        //orderInfo.setUpdateUser(CurrentUser.getUserNickname());
                        orderInfo.setUpdateTime(new Date());
                        tOrderInfoList.add(orderInfo);
                        //tOrderInfoMapper.updateByPrimaryKeySelective(orderInfo);
                    }
                    tOrderInfoMapper.updateByIdBath(tOrderInfoList);

                    //tOrderInfoMapper.batchUpdatesharePaymentAmountAndshareDispatchFee(tOrderInfoDTOS);
                    //计算最后一个运单的打包后的运费和调度费
                    BigDecimal lastAmount = orderPackInfo.getAppointmentPaymentCash().subtract(totalCarriageAmount);
                    BigDecimal lastDispatchFee = orderPackInfo.getRecountDispatchFee().subtract(totalDispatchFee);
                    if (lastAmount.compareTo(BigDecimal.ZERO) <= 0) {
                        log.error("运单打包Task处理失败!", "lastAmount:SHAREPAYMENTTOOLOW");
                        return ResultUtil.error("运单打包Task处理失败!lastAmount:SHAREPAYMENTTOOLOW");
                    }
                    if (lastDispatchFee.compareTo(BigDecimal.ZERO) < 0) {
                        log.error("运单打包Task处理失败!", "lastDispatchFee:SHAREDISPATCHFEE");
                        return ResultUtil.error("运单打包Task处理失败!lastDispatchFee:SHAREDISPATCHFEE");
                    }
                    tOrderInfoMapper.updateOrderSahrePaymentAndDispatchFee(lastAmount, lastDispatchFee, lastCode);
                }
            }
            orderPackInfoMapper.updatePackLsCalculationCode(orderPackInfo.getCode());
            log.info("插入打包子表结束");
            // 原始单据的钱解冻-在冻结
            Payment py = new Payment();
            py.DBFrozen(orderPackInfo);
            return ResultUtil.ok();
        }catch (Exception e){
            log.error("运单打包Task处理失败!", e);
            return ResultUtil.error("运单打包Task处理失败!"+e.getMessage());
        }

    }

    private void updateOrderSharePaymentAndDispatch(OrderPackVO pack, TOrderPackInfo orderPackInfo, BigDecimal divide,
                                                    BigDecimal currentDispatchRate) {
        List<String> codes = pack.getCodes();
        if (codes.size() == 1) {
            String orderCode = codes.get(0);
            // 如果分摊系数不为空
            if (null != orderPackInfo.getShareCoefficient()) {
                //保存运单主表打包后的运费和调度费
                BigDecimal sharePaymentAmount = orderPackInfo.getAppointmentPaymentCash();
                BigDecimal shareDispatchFee = orderPackInfo.getRecountDispatchFee();
                tOrderInfoMapper.updateOrderSahrePaymentAndDispatchFee(sharePaymentAmount, shareDispatchFee, orderCode);
            }
        } else {
            String lastCode = null;
            if (null != orderPackInfo.getShareCoefficient()) {
                // 取出最后一个运单
                lastCode = codes.remove(codes.size() - 1);
                BigDecimal totalCarriageAmount = BigDecimal.ZERO;
                BigDecimal totalDispatchFee = BigDecimal.ZERO;
                List<TOrderInfoDTO> tOrderInfoDTOS = tOrderInfoMapper.selectOrderInfoByCodes(codes);
                for (TOrderInfoDTO orderInfoDTO : tOrderInfoDTOS) {

                    BigDecimal sharePaymentAmount = orderInfoDTO.getUserConfirmPaymentAmount().multiply(divide)
                            .setScale(2, BigDecimal.ROUND_HALF_UP);
                    if (sharePaymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
                        throw new RuntimeException("SHAREPAYMENTTOOLOW");
                    }
                    // 根据分摊后的运费，重新计算调度费
                    BigDecimal dispatchFee = OrderMoneyUtil.getDispatchFee(currentDispatchRate, sharePaymentAmount);
                    totalCarriageAmount = totalCarriageAmount.add(sharePaymentAmount);
                    totalDispatchFee = totalDispatchFee.add(dispatchFee);
                    TOrderInfo orderInfo = new TOrderInfo();
                    orderInfo.setId(orderInfoDTO.getId());
                    orderInfo.setSharePaymentAmount(sharePaymentAmount);
                    orderInfo.setShareDispatchFee(dispatchFee);
                    orderInfo.setPackStatus("1");
                    orderInfo.setUpdateUser(CurrentUser.getUserNickname());
                    orderInfo.setUpdateTime(new Date());
                    tOrderInfoMapper.updateByPrimaryKeySelective(orderInfo);
                }
                //tOrderInfoMapper.batchUpdatesharePaymentAmountAndshareDispatchFee(tOrderInfoDTOS);
                //计算最后一个运单的打包后的运费和调度费
                BigDecimal lastAmount = orderPackInfo.getAppointmentPaymentCash().subtract(totalCarriageAmount);
                BigDecimal lastDispatchFee = orderPackInfo.getRecountDispatchFee().subtract(totalDispatchFee);
                if (lastAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    throw new RuntimeException("SHAREPAYMENTTOOLOW");
                }
                if (lastDispatchFee.compareTo(BigDecimal.ZERO) < 0) {
                    throw new RuntimeException("SHAREDISPATCHFEE");
                }
                tOrderInfoMapper.updateOrderSahrePaymentAndDispatchFee(lastAmount, lastDispatchFee, lastCode);
            }
        }
    }

    /**
     * @param
     * @Description 保存运单主表打包后的运费和调度费
     * <AUTHOR>
     * @Date 2019/7/15 10:07
     * @Return
     * @Exception
     */
    private Map<String, BigDecimal> saveSharePaymentAmountAndDispatch(BigDecimal divide, String code, BigDecimal currentDispatchRate) {
        TOrderInfo orderInfo = tOrderInfoMapper.selectOrderByCode(code);
        BigDecimal userConfirmPaymentAmount = orderInfo.getUserConfirmPaymentAmount();
        BigDecimal sharePaymentAmount = userConfirmPaymentAmount.multiply(divide).setScale(2, BigDecimal.ROUND_HALF_UP);
        // 根据分摊后的运费，重新计算调度费
        BigDecimal shareDispatchFee = OrderMoneyUtil.getDispatchFee(currentDispatchRate, sharePaymentAmount);
        tOrderInfoMapper.updateOrderSahrePaymentAndDispatchFee(sharePaymentAmount, shareDispatchFee, code);
        Map<String, BigDecimal> result = new HashMap<String, BigDecimal>(2);
        result.put("sharePaymentAmount", sharePaymentAmount);
        result.put("shareDispatchFee", shareDispatchFee);
        return result;
    }

    /**
     * 运单拆包
     * Yan
     * @param code 打包主表 Code
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil orderDismantledPack(String code) {
        // flase 已经支付
        Boolean aBoolean = orderPackInfoMapper.judgeOrderPackStatus(code);
        if (aBoolean) {
            // 逻辑删除打包主表
            orderPackInfoMapper.orderDismantledPackLogicDelete(code);
            // 在打包子表 查询 运单主表 Code
            List<String> orderCodeByPackPayCode = orderPackDetailMapper.getOrderCodeByPackPayCode(code);
            // 逻辑删除打包子表
            orderPackDetailMapper.orderDetailDismantledPackLogicDelete(code, true);
            // 修改运单主表打包状态
            OrderPackVO orderPackVO = new OrderPackVO()
                    .setCodes(orderCodeByPackPayCode)
                    .setEnable(false);
            orderInfoMapper.orderPackAfterAuditState(orderPackVO);


            TOrderPackInfo orderPackInfo = orderPackInfoMapper.selectOrderPackByCode(code);
            try {
                if(orderPackInfo.getLsCalculation()){
                    Payment py = new Payment();
                    py.DBThaw(orderPackInfo);
                }
            } catch (Exception e){
                log.error("ZJJ-041:运单拆包失败!", e);
                String message = e.getMessage();
                if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)){
                    message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再拆包。";
                }
                if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)){
                    message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再拆包";
                }
                if (com.lz.common.util.StringUtils.checkChineseCharacter(message)){
                    throw new RuntimeException(message);
                }
                throw new RuntimeException("ZJJ-041:运单拆包失败!");
            }
            return ResultUtil.ok();
        } else {
            return ResultUtil.error("当前运单，不能拆包");
        }
    }

    /**
     * 根据打包主表code获取
     * 打包运单的详细信息
     * Yan
     * @param search 打包主表code
     * @return
     */
    @Override
    public ResultUtil orderPackDetailInfo(PcOrderSearchVO search) {
        if (null != search.getTxTime()) {
            Date[] tx = search.getTxTime();
            if (null != tx) {
                search.setTxStartTime(tx[0]);
                search.setTxEndTime(tx[1]);
            }
        }
        List<String> codes = orderPackDetailMapper.orderPackDetailInfo(search.getCode());
        if (null != search.getBizCode()) {
            search.setOrderBusinessCode(search.getBizCode());
            search.setCodes(new ArrayList<>());
            /*TOrderInfoVO orderInfoVO = orderInfoMapper.selectByOrderBusinessCode(search.getOrderBusinessCode());
            if (null == orderInfoVO) {
                return ResultUtil.ok("");
            }
            if (!codes.contains(orderInfoVO.getCode())) {
                return ResultUtil.ok("");
            }*/
        } else {
            search.setCodes(codes);
        }

        List<TOrderInfoDTO> orderListByCode = orderInfoMapper.getOrderListByCode(search);
        return ResultUtil.ok(orderListByCode);
    }

    /**
     * 移除已打包的运单
     * @param code 运单主表的 Code
     * Yan
     * @return
     */
    @Override
    public ResultUtil removeOrder(List<String> code) {
        // 回复运单的打包状态为 ： 未打包
        OrderPackVO opv = new OrderPackVO();
        opv.setCodes(code);
        opv.setEnable(false);
        orderInfoMapper.orderPackAfterAuditState(opv);

        // 逻辑删除打包子表
        orderPackDetailMapper.removeOrderLogicDelete(code, true);
        return ResultUtil.ok("移除成功");
    }

    /**
     * 打包支付： 计算所有运单 各种费用总和
     * Yan
     */
    private Map<String, Object> countOrder(List<OrderSundryFeeDTO> orderSundryFee) throws RuntimeException {
        Map<String, Object> map = new HashMap<>();
        // 求的是预估货源重量总和
        double countWeight = 0;
        // 求的是服务费总和
        BigDecimal service = new BigDecimal("0");
        // 求的是其他费用总和
        BigDecimal other = new BigDecimal("0");
        // 求得是累计总费用
        BigDecimal total = new BigDecimal("0");

        for (OrderSundryFeeDTO sundry : orderSundryFee) {
            if (sundry.getDischargeWeight() != 0) {
                countWeight += sundry.getDischargeWeight();
            }
            if (sundry.getServiceFee() != null) {
                service = service.add(sundry.getServiceFee());
            }
            if (sundry.getOtherFee() != null) {
                other = other.add(sundry.getOtherFee());
            }
            if (sundry.getTotalFee() != null) {
                total = total.add(sundry.getTotalFee());
            }
        }
        map.put("total", total);
        map.put("other", other);
        map.put("service", service);
        map.put("weight", countWeight);
        return map;
    }
    @Override
    public TOrderPayInfoDTO selectByPayInfoLimitOne(String code){
        return tOrderPackInfoMapper.selectByPayInfoLimitOne(code);
    }

    @Override
    public int updateByPrimaryKey(TOrderPackInfo record){
        return tOrderPackInfoMapper.updateByPrimaryKey(record);
    }

    @Override
    public ResultUtil managerWithdrawPage(PcOrderSearchVO search) {
        try {
            Page<Object> objects =  PageHelper.startPage(search.getPage(), search.getSize());
            Date[] orderTimes = search.getOrderTime();
            if (null != orderTimes) {
                if (null != orderTimes[0]) {
                    search.setStartTime(orderTimes[0]);
                }
                if (null != orderTimes[1]) {
                    search.setEndTime(orderTimes[1]);
                }
            }
            Integer endUserId = CurrentUser.getEndUserId();
            search.setEndUserId(endUserId);
            String[] packStatus = new String[]{
                    DictEnum.PACKPAID.code,
                    DictEnum.PACKEWITHDRAWERROR.code,
                    DictEnum.PACKEXTRACTPROCESSED.code
            };
            if(null != search.getPackStatus())
            {
                packStatus = new String[]{search.getPackStatus()};
            }
            search.setPackStatusArray(packStatus);
            OrderUtil.levelSort(search);
            String userLogisticsRoles = CurrentUser.getUserLogisticsRole();
            if (null != userLogisticsRoles) {
                if (userLogisticsRoles.contains(DictEnum.CTYPEMANAGER.code)) {
                    search.setEndAgent(true);
                    search.setEndAgentId(CurrentUser.getEndUserId());
                } else if (userLogisticsRoles.contains(DictEnum.CTYPEBOSS.code)) {
                    search.setCarOwner(true);
                    search.setCarOwnerId(CurrentUser.getEndUserId());
                }else if (userLogisticsRoles.contains(DictEnum.CTYPECAPTAIN.code)) {
                    search.setCarCaptain(true);
                    search.setCarCaptainId(CurrentUser.getEndUserId());
                }
            }
            List<OrderPackDTO> orderPackDTOS = tOrderPackInfoMapper.managerWithdrawPage(search);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), orderPackDTOS, objects.getTotal());
        } catch (Exception e){
            log.error("经纪人提现列表查询错误", e);
        }
        return ResultUtil.ok();
    }

    @Override
    public TOrderPackInfo selectPackInfoByCode(String code) {
        TOrderPackInfo packInfo = tOrderPackInfoMapper.selectOrderPackByCode(code);
        return packInfo;
    }

    @Override
    public TOrderPackInfo selectPackInfoByPayDetailCode(String outerTradeNo) {
        TOrderPackInfo orderPackInfo = tOrderPackInfoMapper.selectPackInfoByPayDetailCode(outerTradeNo);
        return orderPackInfo;
    }

    @Override
    public List<String> selectVirtualOrderByCode(List<String> codes) {
        return tOrderPackInfoMapper.selectVirtualOrderByCode(codes);
    }

    @Override
    public List<TOrderInfo> getOrderInfoByPackCode(String code) {
        return tOrderPackInfoMapper.getOrderInfoByPackCode(code);
    }

    @Override
    public List<String> getOrderCodesByPackId(Integer id) {
        return tOrderPackInfoMapper.selectOrderCodeByPackId(id);
    }

    @Override
    public TOrderPackInfo selectJdPackInfoByOrderBusinessCode(String orderBusinessCode) {
        return tOrderPackInfoMapper.selectJdPackInfoByOrderBusinessCode(orderBusinessCode);
    }

    @Override
    public TOrderPackInfo selectById(Integer id) {
        return tOrderPackInfoMapper.selectByPrimaryKey(id);
    }

    @Override
    public TOrderPackInfo selectPackInfoByOrderBusinessCodePayTime(String orderBusinessCode, String tradeNo) {
        TOrderInfo tOrderInfo = tOrderInfoMapper.selectOrderBusinessCode(orderBusinessCode);
        if (null != tOrderInfo) {
            TOrderPayDetail tOrderPayDetail = orderPayDetailMapper.selectByCode(tradeNo);
            TOrderPackInfo tOrderPackInfo = tOrderPackInfoMapper.selectPackInfoByOrderBusinessCodePayTime(tOrderInfo.getCode(), tOrderPayDetail.getOperateTime());
            return tOrderPackInfo;
        }
        return null;
    }

    @Override
    public TOrderPackInfo selectByVirtualOrderNo(String virtualOrderNo) {
        return tOrderPackInfoMapper.selectByVirtualOrderNo(virtualOrderNo);
    }

}
