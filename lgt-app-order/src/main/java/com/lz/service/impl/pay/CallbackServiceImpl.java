package com.lz.service.impl.pay;

import cn.hutool.json.JSONUtil;
import com.lz.api.MqAPI;
import com.lz.common.config.HXPropertiesConfig;
import com.lz.common.constants.MqTagConstants;
import com.lz.common.constants.MqTopicConstants;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.HXTradeTypeEnum;
import com.lz.common.factory.PayMQMessageFactory;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.MQDelayMessage;
import com.lz.common.model.MQMessage;
import com.lz.common.model.hxPayment.request.pay.CustomerBalancePayReq;
import com.lz.common.model.hxPayment.response.CustomerBalancePayRes;
import com.lz.common.util.DateUtils;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.ThrowableUtil;
import com.lz.dao.THXOrderPayDetailMapper;
import com.lz.dao.TOrderInfoMapper;
import com.lz.dao.TOrderPayRequestMapper;
import com.lz.dto.THxOrderPayInfoDTO;
import com.lz.dao.TOrderPayRequestDetailMapper;
import com.lz.dto.TOrderPayRequestDTO;
import com.lz.dto.TOrderPayRequestDetailDTO;
import com.lz.model.*;
import com.lz.payment.TradeType;
import com.lz.payment.hxyh.HXPaymentUtil;
import com.lz.payment.hxyh.HXWalletUtil;
import com.lz.service.TOrderContractService;
import com.lz.service.pay.CallbackService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdk.model.message.AsyncNotifyVirtualBalancePayMessageBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 华夏支付回调服务类
 */
@Slf4j
@Service
public class CallbackServiceImpl implements CallbackService {

    @Resource
    private TOrderInfoMapper orderInfoMapper;

    @Resource
    private THXOrderPayDetailMapper hxOrderPayDetailMapper;

    @Resource
    private TOrderPayRequestMapper orderPayRequestMapper;

    @Resource
    private TOrderPayRequestDetailMapper orderPayRequestDetailMapper;

    @Autowired
    private TOrderContractService orderContractService;

    @Resource
    private HXPaymentUtil hxPaymentUtil;

    @Resource
    private HXWalletUtil hxWalletUtil;

    @Autowired
    private SysParamAPI sysParamAPI;

    @Autowired
    private HXPropertiesConfig hxPropertiesConfig;


    @Autowired
    private MqAPI mqAPI;

    /**
     * 企业余额支付回调
     * @param messageBody
     * @return
     */
    @Override
    public ResultUtil companyBalancePayNotice(AsyncNotifyVirtualBalancePayMessageBody messageBody) {
        Date returnTime = new Date();
        TOrderPayRequestDTO dto = hxOrderPayDetailMapper.selectOrderPayInfo(messageBody.getBizOrderNo());
        log.info("华夏余额支付回调，支付子表dto信息, {}", JSONUtil.toJsonStr(dto));
        if (DictEnum.PAY_SUCC.code.equals(messageBody.getOrderStatus())) {
            hxPaymentUtil.updateOrderPayInfo(dto.getOrderPayId(), DictEnum.M090.code, returnTime);
            // 修改支付子表，添加回调结果、时间
            hxPaymentUtil.updateOrderPayDetail(dto.getOrderPayDetailId(), DictEnum.TRADE_FINISHED.code, returnTime);
            // 修改钱包
            hxWalletUtil.companyPayCallbackModifyWallet(dto);
            // 记录钱包流水
            hxPaymentUtil.companyCallbackInsertWalletLog(messageBody, dto);
            // 触发承运方余额支付
            MQDelayMessage message = new MQDelayMessage();
            message.setTopic(MqTopicConstants.PAY_REQUEST);
            message.setTag(MqTagConstants.PAY_REQUEST);
            message.setKey(dto.getPayRequestCode());
            message.setBody(dto);
            message.setStartDeliverTime(100);
            mqAPI.sendDelayMessage(message);
        } else {
            hxPaymentUtil.updateOrderPayInfo(dto.getOrderPayId(), DictEnum.M080.code, returnTime);
            hxPaymentUtil.updateFailOrderPayDetail(dto.getOrderPayDetailId(), messageBody.getResponseDesc(), messageBody.getResponseCode(), returnTime);
            // 删除支付请求主表
            orderPayRequestMapper.deleteByPrimaryKey(Long.valueOf(dto.getPayRequestId()));
            // 删除支付请求详情表
            orderPayRequestDetailMapper.deleteByPayRequestId(Long.valueOf(dto.getPayRequestId()));
            // 恢复钱包
            hxWalletUtil.companyPayFailModifyWallet(dto);
        }
        return ResultUtil.ok();
    }

    /**
     * 余额支付回调
     *
     * @description
     * <AUTHOR>
     * @date 2021/8/23 09:40
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil singleBalancePayNotice(AsyncNotifyVirtualBalancePayMessageBody messageBody) {
        Date returnTime = new Date();
        THxOrderPayInfoDTO dto = hxOrderPayDetailMapper.selectByOrderPayDetailKey(messageBody.getBizOrderNo());
        log.info("华夏余额支付回调，支付子表dto信息, {}", JSONUtil.toJsonStr(dto));
        if (DictEnum.PAY_SUCC.code.equals(messageBody.getOrderStatus())) {
            // 修改支付子表，添加回调结果、时间
            hxPaymentUtil.updateOrderPayDetail(dto.getOrderPayDetailId(), DictEnum.TRADE_FINISHED.code, returnTime);
            // 修改钱包
            hxWalletUtil.callbackModifyWallet(dto);
            // 记录钱包流水
            hxPaymentUtil.insertWalletLog(messageBody, dto);

            // 承运方余额支付回调
            if (HXTradeTypeEnum.HX_CABALANCE_PAY.code.equals(dto.getTradeType())) {
                return CABalancePayNotice(dto, returnTime);
            }
            // 司机余额支付回调
            if (HXTradeTypeEnum.HX_CDBALANCE_PAY.code.equals(dto.getTradeType())) {
                return callbackSuccess(dto, returnTime);
            }

        } else {
            // 支付失败
            if (!HXTradeTypeEnum.HX_INSURANCE.code.equals(dto.getTradeType())) {
                hxPaymentUtil.updateOrderInfo(dto.getOrderId(), DictEnum.O060.code, DictEnum.M080.code, returnTime);
                hxPaymentUtil.insertOrderState(dto.getOrderCode(), DictEnum.S0800.code, returnTime);
                hxPaymentUtil.updateOrderPayInfo(dto.getOrderPayId(), DictEnum.M080.code, returnTime);
            } else {
                // 支付保险失败，修改保险状态
                hxPaymentUtil.updateInsuranceStatus(dto, 2);
            }
            hxPaymentUtil.updateFailOrderPayDetail(dto.getOrderPayDetailId(), messageBody.getResponseDesc(), messageBody.getResponseCode(), returnTime);
            // 恢复钱包
            hxWalletUtil.failCallbackModifyWallet(dto);
        }
        return ResultUtil.ok();
    }


    /**
     * @description 承运方余额支付回调
     * <AUTHOR>
     * @date 2021/8/20 10:05
     */
    public ResultUtil CABalancePayNotice(THxOrderPayInfoDTO dto, Date returnTime) {
        // 普通模式 -> 司机
        if (DictEnum.COMMONPATTERN.code.equals(dto.getCapitalTransferPattern())
                && DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())) {
            callbackSuccess(dto, returnTime);
            return ResultUtil.ok();
        }
        // 普通模式 -> 车队长 = 发起余额支付
        // 经纪人模式 -> 司机 = 发起余额支付
        boolean commonCaptain = DictEnum.COMMONPATTERN.code.equals(dto.getCapitalTransferPattern())
                && DictEnum.PAYTOCAPTAIN.code.equals(dto.getCapitalTransferType());
        if (commonCaptain) {
            // 判断司机和车队长是否同一人
            if (dto.getEndDriverWalletId().equals(dto.getCaptainWalletId())) {
                // 修改运单主表
                return callbackSuccess(dto, returnTime);
            } else {
                // 创建支付子表
                String orderPayDetailCode = hxPaymentUtil.createTxOrderPayDetail(dto.getPayCode(),
                        dto.getOrderCastChangeCode(), HXTradeTypeEnum.HX_CDBALANCE_PAY.code, TradeType.RZ.code);
                // 更新支付申请详细支付子表code
                TOrderPayRequestDetail orderPayRequestDetail = orderPayRequestDetailMapper.selectByOrderId(dto.getOrderId(), dto.getOrderPayDetailCode());
                TOrderPayRequestDetail tOrderPayRequestDetail = new TOrderPayRequestDetail();
                tOrderPayRequestDetail.setId(orderPayRequestDetail.getId());
                tOrderPayRequestDetail.setOrderPayDetailCode(orderPayDetailCode);
                orderPayRequestDetailMapper.updateByPrimaryKeySelective(tOrderPayRequestDetail);
                // 创建司机余额支付请求消息，写入MQ
                requestBalance(HXTradeTypeEnum.HX_CDBALANCE_PAY.code, TradeType.RZ.code, dto, orderPayDetailCode);
                return ResultUtil.ok();
            }
        }
        return ResultUtil.ok();
    }

    private ResultUtil callbackSuccess(THxOrderPayInfoDTO dto, Date returnTime) {
        // 修改运单主表
        hxPaymentUtil.updateOrderInfo(dto.getOrderId(), DictEnum.M100.code, DictEnum.M090.code, returnTime);
        // 修改支付主表
        hxPaymentUtil.updateOrderPayInfo(dto.getOrderPayId(), DictEnum.M090.code, returnTime);
        hxPaymentUtil.insertOrderState(dto.getOrderCode(), DictEnum.S0902.code, returnTime);
        hxPaymentUtil.insertOrderState(dto.getOrderCode(), DictEnum.S1002.code,
                DateUtils.addSeconds(returnTime, 1));
        // 修改支付请求
        orderPayRequestDetailMapper.updateStatusByOrderId(2, dto.getOrderId());
        TOrderPayRequestDetail tOrderPayRequestDetail = orderPayRequestDetailMapper.selectByOrderId(dto.getOrderId(), dto.getOrderPayDetailCode());
        boolean finishedPay = orderPayRequestDetailMapper.selectFinishedPay(tOrderPayRequestDetail.getRequestId());
        if (finishedPay) {
            TOrderPayRequest orderPayRequest = new TOrderPayRequest();
            orderPayRequest.setId(tOrderPayRequestDetail.getRequestId());
            orderPayRequest.setStatus(2);
            orderPayRequest.setUpdateTime(new Date());
            orderPayRequestMapper.updateByPrimaryKeySelective(orderPayRequest);
        }

        // 单笔支付
        if (DictEnum.SINGLE.code.equals(dto.getPackStatus())) {
            singleOrderCallback(dto);
        }

        log.info("创建司机收款凭证");
        orderContractService.createHXAnySignSkpz(dto.getOrderCode(), DictEnum.ANYSIGNDRIVERSKPZ.code);

        return ResultUtil.ok();
    }

    private void singleOrderCallback(THxOrderPayInfoDTO dto) {
        TOrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(dto.getOrderId());
        // 发送结算通知
        hxPaymentUtil.sendRZMessage(orderInfo, dto);
    }

    public void requestBalance(String tradeType, String operateState, THxOrderPayInfoDTO dto, String orderPayDetailCode) {
        try {
            CustomerBalancePayReq req = new CustomerBalancePayReq();
            req.setPartnerId(hxPropertiesConfig.getPartnerId());
            req.setRequestId(IdWorkerUtil.getInstance().nextId());
            req.setRequestTime(DateUtils.getRequestTime());
            req.setChannelId(hxPropertiesConfig.getChannelId());
            BigDecimal orderAmount = dto.getCarriageFee();
            if (null != dto.getInsuranceId() && 0 != dto.getInsuranceId() && !dto.getInsuranceCancellation() && dto.getInsuredAmount().compareTo(BigDecimal.ZERO) > 0) {
                orderAmount = orderAmount.subtract(dto.getInsuredAmount());
            }
            // 入账
            if (TradeType.RZ.code.equals(operateState)) {
                if (HXTradeTypeEnum.HX_CABALANCE_PAY.code.equals(tradeType)) {
                    req.setOutPartnerAccId(dto.getCarrierAccId());
                    req.setInPartnerAccId(dto.getDriverAccId());
                } else if (HXTradeTypeEnum.HX_CDBALANCE_PAY.code.equals(tradeType)) {
                    req.setOutPartnerAccId(dto.getDriverAccId());
                    req.setInPartnerAccId(dto.getCaptainAccId());
                    if (dto.getIllegalOrder() && null != dto.getIllegalDeduction()) {
                        if (dto.getIllegalDeduction().compareTo(BigDecimal.ZERO) > 0
                                && dto.getCarriageFee().subtract(dto.getIllegalDeduction()).compareTo(BigDecimal.ZERO) > 0
                                && dto.getCarriageFee().subtract(dto.getIllegalDeduction()).subtract(dto.getServiceFee()).compareTo(BigDecimal.ZERO) > 0) {
                            orderAmount = dto.getCarriageFee().subtract(dto.getIllegalDeduction());
                        }
                    }
                }
            }
            req.setOrderAmount(orderAmount);
            req.setBizOrderNo(orderPayDetailCode);
            BigDecimal weight;
            if (null != dto.getSettledWeight()) {
                weight = new BigDecimal(dto.getSettledWeight());
            } else {
                weight = BigDecimal.valueOf(BigDecimal.valueOf(dto.getEstimateGoodsWeight()).compareTo(BigDecimal.ZERO) > 0 ? dto.getEstimateGoodsWeight() : dto.getPrimaryWeight());
            }
            req.setTradeAbstract(String.join(weight.toString(), dto.getGoodsName(), DictEnum.DUN.code));
            SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.PAYCALLBACKURL.code);
            req.setNotifyUrl(paramByKey.getParamValue());
            MQMessage mqMessage = PayMQMessageFactory.getMessage(tradeType, dto.getCapitalTransferType(), dto.getCapitalTransferPattern());
            mqMessage.setKey(orderPayDetailCode);
            mqMessage.setBody(req);
            ResultUtil resultUtil = mqAPI.sendMessage(mqMessage);
            log.info("发送余额支付MQ消息结果，{}", JSONUtil.toJsonStr(resultUtil));
            if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
                throw new RuntimeException("支付失败！");
            }
        } catch (Exception e) {
            log.error("余额支付异常，{}", ThrowableUtil.getStackTrace(e));
            throw new RuntimeException("支付失败！");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil payFail(CustomerBalancePayRes response) {
        try {
            // 修改支付子表状态
            TOrderPayDetail detail = new TOrderPayDetail();
            detail.setCode(response.getBizOrderNo());
            detail.setReturnTime(new Date());
            detail.setTradeStatus(DictEnum.TRADE_FAILED.code);
            detail.setErrorMsg(response.getResponseDesc());
            detail.setErrorCode(response.getResponseCode());
            hxOrderPayDetailMapper.updateByCode(detail);

            TOrderPayRequestDTO orderPayInfoDTO = hxOrderPayDetailMapper.selectOrderPayInfo(response.getBizOrderNo());
            if (null != orderPayInfoDTO && HXTradeTypeEnum.HX_COMBALANCE_PAY.code.equals(orderPayInfoDTO.getTradeType())) {
                hxPaymentUtil.updateOrderPayInfo(orderPayInfoDTO.getOrderPayId(), DictEnum.M080.code, new Date());
                TOrderPayRequest orderPayRequest = orderPayRequestMapper.selectByCode(orderPayInfoDTO.getOrderCode());
                if (null != orderPayRequest) {
                    List<TOrderPayRequestDetailDTO> tOrderPayRequestDetailDTOS = orderPayRequestDetailMapper.selectByRequestId(orderPayRequest.getId());
                    List<Integer> ids = new ArrayList<>();
                    for (TOrderPayRequestDetailDTO tOrderPayRequestDetailDTO : tOrderPayRequestDetailDTOS) {
                        ids.add(tOrderPayRequestDetailDTO.getId());
                    }
                    if (!ids.isEmpty()) {
                        orderPayRequestDetailMapper.batchUpdateStatusById(ids);
                    }
                }
            } else  {
                // 修改支付主表
                THxOrderPayInfoDTO hxOrderPayInfoDTO = hxOrderPayDetailMapper.selectByOrderPayDetailKey(response.getBizOrderNo());
                if (null != hxOrderPayInfoDTO.getOrderPayId()) {
                    hxPaymentUtil.updateOrderPayInfo(hxOrderPayInfoDTO.getOrderPayId(), DictEnum.M080.code, new Date());
                }
                // 修改支付主表
                if (null != hxOrderPayInfoDTO.getOrderId()) {
                    hxPaymentUtil.updateOrderInfo(hxOrderPayInfoDTO.getOrderId(), DictEnum.O060.code, DictEnum.M080.code, new Date());
                    hxPaymentUtil.insertOrderState(hxOrderPayInfoDTO.getOrderCode(), DictEnum.S0800.code, new Date());
                }
                TOrderPayRequestDetail orderPayRequestDetail = orderPayRequestDetailMapper.selectNewestByOrderId(hxOrderPayInfoDTO.getOrderId());
                if (null != orderPayRequestDetail && orderPayRequestDetail.getStatus() != 2) {
                    orderPayRequestDetail.setStatus(2);
                    orderPayRequestDetail.setUpdateTime(new Date());
                    orderPayRequestDetailMapper.updateByPrimaryKeySelective(orderPayRequestDetail);
                }
            }

            // 修改钱包
            TZtWallet tZtWallet = hxWalletUtil.selectByCarrierCompanyPartnerAccId(response.getOutPartnerAccId(), DictEnum.BD.code);
            if (null == tZtWallet) {
                tZtWallet = hxWalletUtil.selectByCarrierCompanyPartnerAccId(response.getOutPartnerAccId(), DictEnum.CA.code);
                if (null == tZtWallet) {
                    tZtWallet = hxWalletUtil.selectByEnduserPartnerAccId(response.getOutPartnerAccId());
                }
            }
            log.info("支付失败，修改出款方钱包, {}", JSONUtil.toJsonStr(tZtWallet));
            BigDecimal entryAmount = tZtWallet.getEntryAmount().subtract(response.getOrderAmount());
            BigDecimal accountBalance = tZtWallet.getAccountBalance().add(response.getOrderAmount());
            tZtWallet.setEntryAmount(entryAmount);
            tZtWallet.setAccountBalance(accountBalance);
            tZtWallet.setUpdateTime(new Date());
            hxWalletUtil.updateByPrimaryKey(tZtWallet);

        } catch (Exception e) {
            log.error("支付申请失败，处理失败, {}", ThrowableUtil.getStackTrace(e));
        }

        return ResultUtil.ok();
    }

}
