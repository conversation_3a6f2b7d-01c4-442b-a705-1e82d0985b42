package com.lz.service.impl.hxyh;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.lz.api.TEndSUserInfoAPI;
import com.lz.api.TZtBankUserAPI;
import com.lz.common.constants.CapitalTransferConstants;
import com.lz.common.dbenum.BusinessCode;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.util.*;
import com.lz.dao.*;
import com.lz.dto.BandCardDTO;
import com.lz.dto.OrderSundryFeeDTO;
import com.lz.dto.TJDOrderPackFeeDTO;
import com.lz.dto.TOrderInfoDTO;
import com.lz.model.*;
import com.lz.service.TOrderInfoService;
import com.lz.service.hxyh.THXOrderPackSerivce;
import com.lz.util.DateTimeUtil;
import com.lz.util.HXPayOrderPackJudgeFilter;
import com.lz.util.OrderDeductionUtil;
import com.lz.vo.OrderPackDetailVO;
import com.lz.vo.OrderPackVO;
import com.lz.vo.TJDOrderPackDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;

@Slf4j
@Service
public class THXOrderPackSerivceImpl implements THXOrderPackSerivce {

    /**
     * 系统运单号，开头
     */
    private static final String BIZHEADER = "DB";
    private static final String BOOL = "bool";
    private static final String DRIVER = "driver";
    private static final String OWNER = "owner";
    private static final String SHAREPAYMENTTOOLOW = "SHAREPAYMENTTOOLOW";
    private static final String SHAREDISPATCHFEE = "SHAREDISPATCHFEE";
    private static final String INSURANCEPAYMENTTOOHIGH = "INSURANCEPAYMENTTOOHIGH";

    @Autowired
    private TOrderInfoMapper orderInfoMapper;

    @Autowired
    private TOrderCastChangesMapper orderCastChangesMapper;

    @Autowired
    private TOrderPackInfoMapper orderPackInfoMapper;

    @Autowired
    private TOrderPackDetailMapper orderPackDetailMapper;

    @Autowired
    private TOrderInfoService orderInfoService;

    @Autowired
    private TZtBankUserAPI ztBankUserAPI;
    @Autowired
    private TEndSUserInfoAPI tEndSUserInfoAPI;

    @Resource
    private TOrderInsuranceMapper orderInsuranceMapper;



    @Override
    public ResultUtil ordersPackInfo(OrderPackVO vo) {
        try {
            Map<String, Object> judge = HXPayOrderPackJudgeFilter.judge(vo.getCodes());
            if ((boolean) judge.get(BOOL)) {
                String capitalTransfer = judge.get("capitalTransfer").toString();
                String endUserId;
                StringBuilder ownerOfheWaybill = new StringBuilder();
                switch (capitalTransfer) {
                    case CapitalTransferConstants
                            .PAYTODRIVER:
                        endUserId = judge.get(DRIVER).toString();
                        break;
                    //车队长/车辆所有人
                    case CapitalTransferConstants.PAYTOCAPTAIN:
                        if (null != judge.get(OWNER) && StringUtils.isNotEmpty(judge.get(OWNER).toString())) {
                            endUserId = judge.get(OWNER).toString();
                        } else {
                            return ResultUtil.error("未找到车队长");
                        }
                        break;
                    case CapitalTransferConstants.PAYTOBELONGER:
                        if (null != judge.get(OWNER) && StringUtils.isNotEmpty(judge.get(OWNER).toString())) {
                            endUserId = judge.get(OWNER).toString();
                        } else {
                            return ResultUtil.error("未找到车主");
                        }
                        break;
                    default:
                        return ResultUtil.error("资金方式错误");
                }
                //查询所勾选运单所属车队长/司机
                TEndUserInfo tEndUserInfo = tEndSUserInfoAPI.selectByPrimaryKey(Integer.valueOf(endUserId));
                ownerOfheWaybill.append(tEndUserInfo.getRealName());
                ownerOfheWaybill.append(" ");
                ownerOfheWaybill.append(tEndUserInfo.getPhone());
                // 查询银行卡
                ResultUtil resultUtil = ztBankUserAPI.selectBankCards(Integer.valueOf(endUserId));
                JSONArray objects = JSONUtil.parseArray(JSONUtil.toJsonStr(resultUtil.getData()));
                List<BandCardDTO> userCard = JSONUtil.toList(objects, BandCardDTO.class);
                Map<String, Object> sumDispatchCarriage = orderInfoMapper.getSumDispatchCarriage(vo.getCodes());
                Map<String, Object> rest = new HashMap<>(0);
                rest.put("capitalTransfer", capitalTransfer);
                rest.put("card", userCard);
                rest.put("fee", sumDispatchCarriage);
                rest.put("ownerOfheWaybill", ownerOfheWaybill);
                rest.put("capitalTransferPattern", judge.get("capitalTransferPattern"));
                return ResultUtil.ok(rest);
            } else {
                return ResultUtil.error(judge.get("msg").toString());
            }
        } catch (Exception e) {
            log.error("ZJJ-400:运单打包失败! ", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                return ResultUtil.error(message);
            }
            return ResultUtil.error("运单打包失败! ");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil orderPack(TJDOrderPackDetailVO packDetailVO) {
        String code = IdWorkerUtil.getInstance().nextId();
        final List<String> orderCodes = packDetailVO.getCodes();
        log.info("原始运单号，{}", JSONUtil.toJsonStr(orderCodes));
        log.info("原始运单个数，{}", orderCodes.size());
        log.info("插入打包子表");
        // 打包子表
        CopyOnWriteArrayList<OrderPackDetailVO> listDetail = new CopyOnWriteArrayList<>();
        for (String item : orderCodes) {
            log.info(item);
            OrderPackDetailVO packDetail = new OrderPackDetailVO();
            String detailCode = IdWorkerUtil.getInstance().nextId();
            packDetail.setCode(detailCode);
            packDetail.setPackPayCode(code);
            packDetail.setOrderCode(item);
            packDetail.setRemark(packDetail.getRemark());
            packDetail.setCreateTime(new Date());
            packDetail.setCreateUser(CurrentUser.getUserNickname());
            listDetail.add(packDetail);
        }
        orderPackDetailMapper.insertOrderPackDetail(listDetail);
        log.info("插入打包子表结束");
        // 运单的各项费用
        List<OrderSundryFeeDTO> orderSundryFee = orderInfoMapper.selectOrderSundryFee(packDetailVO.getCodes());
        try {
            TJDOrderPackFeeDTO packFeeDTO = countOrder(orderSundryFee);
            // 获取运单的调度费系数
            TOrderCastChanges castChanges = orderCastChangesMapper.selectByNewOne(orderCodes.get(0));
            BigDecimal currentDispatchRate = castChanges.getCurrentDispatchRate();

            // 判断选择总运单的调度费是否等于用户输入的确认运费， 如果相等，重新计算的调度费 = 选择总运单的调度费
            BigDecimal recountDispatchFee;
            if (packDetailVO.getCountFreight().compareTo(packDetailVO.getAppointmentPaymentCash()) == 0) {
                recountDispatchFee = packDetailVO.getDispatch();
            } else {
                //计算调度费
                //调度费=运费/（1-调度费系数）-运费
                recountDispatchFee = recalculateDispatchFee(packDetailVO.getAppointmentPaymentCash(), currentDispatchRate);
            }
            packDetailVO.setRecountDispatchFee(recountDispatchFee);

            // 创建打包主表
            TOrderPackInfo orderPackInfo = createOrderPackInfo(code, packDetailVO, packFeeDTO, orderCodes.size());

            log.info("更新原始运单中的打包后的运费和调度费");
            BigDecimal finalDivide = packFeeDTO.getFinalDivide();
            BigDecimal finalCurrentDispatchRate = currentDispatchRate;
            List<OrderSundryFeeDTO> finalOrderSundryFee = orderSundryFee;
            CompletableFuture<Boolean> future = null;
            // 如果选中运单的运费 = 用户确认的运费
            if (finalDivide.compareTo(BigDecimal.ONE) == 0) {
                orderInfoMapper.updateOrginOrderCarriageFeeAndDispatchFee(finalOrderSundryFee);
            } else {
                // 根据分摊系数，重新计算原始运单的运费和调度费
                future = CompletableFuture.supplyAsync(() -> {
                    updateOrderSharePaymentAndDispatch(packDetailVO.getCodes(), orderPackInfo, finalDivide, finalCurrentDispatchRate);
                    return true;
                });
            }

            log.info("更新原始运单中的打包后的运费和调度费结束");

            // 获取修改原始运单：打包后的运费和调度费 的结果
            if (null != future) {
                Boolean result = future.get();
                if (!result) {
                    throw new RuntimeException("运单打包失败!");
                }
            }
        } catch (Exception e) {
            log.error("ZJJ-401:运单打包失败! ", e);
            // 恢复原始运单 打包后的运费和调度费 : 置为null
            if (!orderSundryFee.isEmpty()) {
                orderInfoService.recoverOrginOrderCarriageFeeAndDispatchFee(orderSundryFee);
            }
            log.error("ZJJ-401:运单打包失败!", e);
            String message = "";
            if (null != e.getMessage()) {
                message = e.getMessage();
            }
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再打包。";
            }
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再打包";
            }
            if (message.contains(SHAREPAYMENTTOOLOW)) {
                message = "打包后的运单运费不能小于等于0";
            }
            if (message.contains(SHAREDISPATCHFEE)) {
                message = "打包后的运单调度费不能小于0";
            }
            if (message.contains(INSURANCEPAYMENTTOOHIGH)) {
                message = "打包后的运单运费不合规。错误码：IIA";
            }
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            }
            throw new RuntimeException("运单打包失败!");
        }
        return ResultUtil.ok();
    }

    private TOrderPackInfo createOrderPackInfo(String code, TJDOrderPackDetailVO packDetailVO,
                                               TJDOrderPackFeeDTO packFeeDTO,
                                               int orderSize) {
        TOrderPackInfo orderPackInfo = new TOrderPackInfo();
        orderPackInfo.setCode(code);
        if (null != packDetailVO.getOwner()) {
            orderPackInfo.setEndUserId((Integer) packDetailVO.getOwner());
        }
        orderPackInfo.setCompanyId(packDetailVO.getCompanyId());
        orderPackInfo.setCarrierId(packDetailVO.getCarrierId());
        if (null != packDetailVO.getEndUserIds()) {
            String userId = packDetailVO.getEndUserIds().toString();
            orderPackInfo.setEndUserId(Integer.valueOf(userId));
        }

        // 打包主表
        String bizCode = BIZHEADER + DateTimeUtil.getBizTime() + RandomUtil.randomString(3, 10);
        orderPackInfo.setVirtualOrderNo(bizCode);
        orderPackInfo.setCheckAccountPerson(CurrentUser.getUserNickname());
        orderPackInfo.setTotalSelectedOrders(orderSize);
        orderPackInfo.setTotalSelectedOrdersCarriageZeroCutFee(packDetailVO.getCountErase());
        orderPackInfo.setTotalSelectedOrdersCarriageFee(packDetailVO.getCountFreight());
        orderPackInfo.setTotalSelectedOrdersDispatchFee(packDetailVO.getDispatch());
        orderPackInfo.setRecountDispatchFee(packDetailVO.getRecountDispatchFee());
        orderPackInfo.setTotalSelectedOrdersServiceFee(packFeeDTO.getSerivce());
        orderPackInfo.setTotalSelectedOrdersOtherFee(packFeeDTO.getOther());
        orderPackInfo.setTotalSelectedOrdersFee(packFeeDTO.getTotal());
        orderPackInfo.setTotalSelectedOrdersWeight(packFeeDTO.getWeight());
        orderPackInfo.setAppointmentPaymentCash(packDetailVO.getAppointmentPaymentCash());
        orderPackInfo.setContentDescribtion(packDetailVO.getRemark());
        orderPackInfo.setAppointmentPaymentOther(packDetailVO.getAppointmentPaymentOther());
        orderPackInfo.setShareCoefficient(BigDecimal.ZERO);
        orderPackInfo.setBankCardId(packDetailVO.getBankCardId());
        orderPackInfo.setPaymentPlatforms(DictEnum.HXPLATFORMS.code);
        orderPackInfo.setPackStatus(DictEnum.PACKED.code);
        orderPackInfo.setEnable(false);

        packFeeDTO.setFinalDivide(BigDecimal.ONE);
        if (orderPackInfo.getTotalSelectedOrdersCarriageFee().compareTo(orderPackInfo.getAppointmentPaymentCash()) != 0) {
            //运单分摊比例: 协商的打包运费总价/运单汇总运费，取两位小数
            BigDecimal divide = BigDecimal.ONE;
            if (orderPackInfo.getAppointmentPaymentCash().compareTo(orderPackInfo.getTotalSelectedOrdersServiceFee()) > 0) {
                divide = orderPackInfo.getAppointmentPaymentCash().divide(orderPackInfo.getTotalSelectedOrdersCarriageFee(), 15, RoundingMode.DOWN);
            } else {
                divide = orderPackInfo.getAppointmentPaymentCash().divide(orderPackInfo.getTotalSelectedOrdersCarriageFee(), 2, RoundingMode.DOWN);
            }
            orderPackInfo.setShareCoefficient(divide);
            packFeeDTO.setFinalDivide(divide);
        }
        orderPackInfo.setLsCalculation(true);
        orderPackInfoMapper.insertSelective(orderPackInfo);
        return orderPackInfo;
    }

    /**
     * 打包支付： 计算所有运单 各种费用总和
     * Yan
     */
    private TJDOrderPackFeeDTO countOrder(List<OrderSundryFeeDTO> orderSundryFee) throws RuntimeException {
        TJDOrderPackFeeDTO packFeeDTO = new TJDOrderPackFeeDTO();
        // 求的是预估货源重量总和
        double countWeight = 0;
        // 求的是服务费总和
        BigDecimal service = BigDecimal.ZERO;
        // 求的是其他费用总和
        BigDecimal other = BigDecimal.ZERO;
        // 求得是累计总费用
        BigDecimal total = BigDecimal.ZERO;

        for (OrderSundryFeeDTO sundry : orderSundryFee) {
            if (sundry.getDischargeWeight() != 0) {
                countWeight += sundry.getDischargeWeight();
            }
            if (sundry.getServiceFee() != null) {
                service = service.add(sundry.getServiceFee());
            }
            if (sundry.getOtherFee() != null) {
                other = other.add(sundry.getOtherFee());
            }
            if (sundry.getTotalFee() != null) {
                total = total.add(sundry.getTotalFee());
            }
        }
        packFeeDTO.setTotal(total);
        packFeeDTO.setOther(other);
        packFeeDTO.setSerivce(service);
        packFeeDTO.setWeight(countWeight);
        return packFeeDTO;
    }

    /**
     * 更新原始运单分摊比例和分摊后的运费
     *
     * @param codes
     * @param orderPackInfo
     * @param divide
     * @param currentDispatchRate
     */
    private void updateOrderSharePaymentAndDispatch(List<String> codes, TOrderPackInfo orderPackInfo, BigDecimal divide,
                                                    BigDecimal currentDispatchRate) {
        if (codes.size() == 1) {
            String orderCode = codes.get(0);
            // 如果分摊系数不为空
            if (null != orderPackInfo.getShareCoefficient()) {
                // 判断运费
                TOrderInfo tOrderInfo = orderInfoMapper.selectOrderByCode(orderCode);
                TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(tOrderInfo.getOrderBusinessCode());
                boolean insuranceApplicable = OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance);
                if (insuranceApplicable) {
                    if (orderPackInfo.getAppointmentPaymentCash().compareTo(tOrderInsurance.getInsuredAmount()) <= 0) {
                        // 保险费大于或等于司机的运费，无法进行打包，请先完善保险费设置
                        throw new RuntimeException(INSURANCEPAYMENTTOOHIGH);
                    }
                }
                //保存运单主表打包后的运费和调度费
                BigDecimal sharePaymentAmount = orderPackInfo.getAppointmentPaymentCash();
                BigDecimal shareDispatchFee = orderPackInfo.getRecountDispatchFee();
                orderInfoMapper.updateOrderSahrePaymentAndDispatchFee(sharePaymentAmount, shareDispatchFee, orderCode);
            }
        } else {
            String lastCode;
            if (null != orderPackInfo.getShareCoefficient()) {
                // 取出最后一个运单
                lastCode = codes.remove(codes.size() - 1);
                BigDecimal totalCarriageAmount = BigDecimal.ZERO;
                BigDecimal totalDispatchFee = BigDecimal.ZERO;
                List<TOrderInfoDTO> tOrderInfoDTOS = orderInfoMapper.selectOrderInfoByCodes(codes);
                for (TOrderInfoDTO orderInfoDTO : tOrderInfoDTOS) {
                    BigDecimal sharePaymentAmount = orderInfoDTO.getUserConfirmPaymentAmount().multiply(divide)
                            .setScale(2, RoundingMode.HALF_UP);
                    if (sharePaymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
                        throw new RuntimeException(SHAREPAYMENTTOOLOW);
                    }
                    // 判断运费
                    TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(orderInfoDTO.getOrderBusinessCode());
                    boolean insuranceApplicable = OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance);
                    if (insuranceApplicable) {
                        if (sharePaymentAmount.compareTo(tOrderInsurance.getInsuredAmount()) <= 0) {
                            // 保险费大于或等于司机的运费，无法进行打包，请先完善保险费设置
                            throw new RuntimeException(INSURANCEPAYMENTTOOHIGH);
                        }
                    }
                    // 根据分摊后的运费，重新计算调度费
                    BigDecimal dispatchFee = OrderMoneyUtil.getDispatchFee(currentDispatchRate, sharePaymentAmount);
                    totalCarriageAmount = totalCarriageAmount.add(sharePaymentAmount);
                    totalDispatchFee = totalDispatchFee.add(dispatchFee);
                    TOrderInfo orderInfo = new TOrderInfo();
                    orderInfo.setId(orderInfoDTO.getId());
                    orderInfo.setSharePaymentAmount(sharePaymentAmount);
                    orderInfo.setShareDispatchFee(dispatchFee);
                    orderInfo.setPackStatus(DictEnum.PACK.code);
                    orderInfo.setUpdateUser(CurrentUser.getUserNickname());
                    orderInfo.setUpdateTime(new Date());
                    orderInfoMapper.updateByPrimaryKeySelective(orderInfo);
                }
                //计算最后一个运单的打包后的运费和调度费
                BigDecimal lastAmount = orderPackInfo.getAppointmentPaymentCash().subtract(totalCarriageAmount);
                BigDecimal lastDispatchFee = orderPackInfo.getRecountDispatchFee().subtract(totalDispatchFee);
                if (lastAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    throw new RuntimeException(SHAREPAYMENTTOOLOW);
                }
                if (lastDispatchFee.compareTo(BigDecimal.ZERO) < 0) {
                    throw new RuntimeException(SHAREDISPATCHFEE);
                }
                // 判断运费
                TOrderInfo tOrderInfo = orderInfoMapper.selectOrderByCode(lastCode);
                TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(tOrderInfo.getOrderBusinessCode());
                boolean insuranceApplicable = OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance);
                if (insuranceApplicable) {
                    if (lastAmount.compareTo(tOrderInsurance.getInsuredAmount()) <= 0) {
                        // 保险费大于或等于司机的运费，无法进行打包，请先完善保险费设置
                        throw new RuntimeException(INSURANCEPAYMENTTOOHIGH);
                    }
                }
                orderInfoMapper.updateOrderSahrePaymentAndDispatchFee(lastAmount, lastDispatchFee, lastCode);
            }
        }
    }

    /**
     * 重新计算调度费
     *
     * @param appointmentPaymentCash
     */
    public BigDecimal recalculateDispatchFee(BigDecimal appointmentPaymentCash, BigDecimal currentDispatchRate) {
        BigDecimal disFee = OrderMoneyUtil.getDispatchFee(currentDispatchRate, appointmentPaymentCash);
        if (disFee == null || disFee.doubleValue() < 0) {
            throw new RuntimeException("调度费为负数，无法完成支付！");
        }
        return disFee;
    }

}
