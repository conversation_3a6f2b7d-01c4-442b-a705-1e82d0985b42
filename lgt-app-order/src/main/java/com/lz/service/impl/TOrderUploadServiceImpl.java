package com.lz.service.impl;

import com.lz.common.util.ResultUtil;
import com.lz.dao.TOrderUploadMapper;
import com.lz.example.TOrderUploadExample;
import com.lz.model.TOrderUpload;
import com.lz.service.TOrderUploadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TOrderUploadServiceImpl implements TOrderUploadService {

    @Autowired
    private TOrderUploadMapper tOrderUploadMapper;

    @Override
    public ResultUtil updateOrderSendState(String orderStage, String orderCode) {
        TOrderUpload tOrderUpload = new TOrderUpload();
        if(orderStage.equals("orderOne")){
           tOrderUpload.setReceiptUploadStatus(1);
        }else if(orderStage.equals("orderTwo")){
           tOrderUpload.setTradeUploadStatus(1);
        }else if(orderStage.equals("orderThree")){
           tOrderUpload.setPayUploadStatus(1);
        }else if(orderStage.equals("capital")){
            tOrderUpload.setFlowUploadStatus(1);
        }
        TOrderUploadExample orderStateExample = new TOrderUploadExample();
        orderStateExample.createCriteria().andOrderCodeEqualTo(orderCode);
        tOrderUploadMapper.updateByExampleSelective(tOrderUpload,orderStateExample);
        return ResultUtil.ok();
    }

    @Override
    public TOrderUpload selectInfoByOrderCode(String orderCode) {
        return tOrderUploadMapper.selectByOrderCode(orderCode);
    }
}
