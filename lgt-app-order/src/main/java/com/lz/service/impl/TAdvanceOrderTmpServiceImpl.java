package com.lz.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lz.api.CompanyService;
import com.lz.common.dbenum.BusinessCode;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.HXTradeTypeEnum;
import com.lz.common.dbenum.JDTradeTypeEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.CodeEnum;
import com.lz.common.util.*;
import com.lz.dao.*;
import com.lz.dto.TAdvanceOrderTmpDto;
import com.lz.enums.HXPaymentEnums;
import com.lz.enums.PaymentEnums;
import com.lz.example.TAdvanceOrderTmpExample;
import com.lz.model.*;
import com.lz.payment.Payment;
import com.lz.payment.content.HXPayStrategyContent;
import com.lz.payment.content.PayStrategyContent;
import com.lz.service.TAdvanceOrderTmpService;
import com.lz.vo.TAdvanceOrderTmpExcel;
import com.lz.vo.TAdvanceOrderTmpVo;
import com.lz.vo.TOrderInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;

@Slf4j
@Service
@Transactional
public class TAdvanceOrderTmpServiceImpl implements TAdvanceOrderTmpService {

    @Resource
    private TAdvanceOrderTmpMapper tAdvanceOrderTmpMapper;

    @Resource
    private TOrderInfoMapper tOrderInfoMapper;

    @Resource
    private TOrderCastChangesMapper tOrderCastChangesMapper;

    @Resource
    private TOrderCastCalcSnapshotMapper orderCastCalcSnapshotMapper;

    @Resource
    private TOrderStateMapper orderStateMapper;

    @Resource
    private CompanyService companyService;

    @Autowired
    private PayStrategyContent payStrategyContent;

    @Autowired
    private HXPayStrategyContent hxPayStrategyContent;

    @Transactional
    @Override
    public ResultUtil ImportExcel(TAdvanceOrderTmpExcel tmpExcel) {
        try {
            TOrderInfoVO tOrderInfoVO = tOrderInfoMapper.selectByOrderBusinessCode(tmpExcel.getOrderBusinessCode());
            TOrderInfo tOrderInfo = tOrderInfoMapper.selectOrderByCode(tOrderInfoVO.getCode());
            TOrderCastChanges tOrderCastChanges = tOrderCastChangesMapper.selectByNewOne(tOrderInfoVO.getCode());
            TAdvanceOrderTmp tAdvanceOrderTmp = new TAdvanceOrderTmp();
            tAdvanceOrderTmp.setCode(IdWorkerUtil.getInstance().nextId());
            tAdvanceOrderTmp.setOrderBusinessCode(tmpExcel.getOrderBusinessCode());
            tAdvanceOrderTmp.setOrderCode(tOrderInfoVO.getCode());
            tAdvanceOrderTmp.setAdvanceFee(tmpExcel.getAdvanceFee());
            if((tOrderCastChanges.getCurrentDispatchRate()!=null &&!"".equals(tOrderCastChanges.getCurrentDispatchRate()) &&
                    (tmpExcel.getAdvanceFee()!=null&&!"".equals(tmpExcel.getAdvanceFee())) )){
                tAdvanceOrderTmp.setAdvanceDispatchFee(OrderMoneyUtil.getDispatchFee(
                        tOrderCastChanges.getCurrentDispatchRate(),tmpExcel.getAdvanceFee()));
            }else{
                tAdvanceOrderTmp.setAdvanceDispatchFee(new BigDecimal(0));
            }
            tAdvanceOrderTmp.setEnable(false);
            tAdvanceOrderTmp.setOrderPayStatus(DictEnum.P070.code);//入账中
            tAdvanceOrderTmp.setOperaterId(CurrentUser.getCurrentUserID().toString());
            TAdvanceOrderTmpExample example = new TAdvanceOrderTmpExample();
            TAdvanceOrderTmpExample.Criteria cr = example.createCriteria();
            cr.andOrderBusinessCodeEqualTo(tAdvanceOrderTmp.getOrderBusinessCode());
            if(tAdvanceOrderTmpMapper.selectByExample(example).size()<1){
                tAdvanceOrderTmpMapper.insertSelective(tAdvanceOrderTmp);
                Payment payment = new Payment();
                payment.advancePayment(tOrderInfo);
            }else{
                // fg = false;
                //orderCodeError+=advanceOrderTmpExcel.getOrderBusinessCode()+",";
            }
        } catch (Exception e) {
            log.error("预付款运单导入失败!, {}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)){
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再支付。";
            }
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)){
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再支付";
            }
            if (StringUtils.checkChineseCharacter(message)){
                throw new RuntimeException(message);
            }
            throw new RuntimeException("预付款运单导入失败!");
        }

        return ResultUtil.ok();
    }

    /**
    * @description 京东预付款支付
    * <AUTHOR>
    * @date 2021/11/8 15:43
    */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil jdPrePayment(TAdvanceOrderTmpExcel tmpExcel) {
        try {
            TOrderInfo tOrderInfo = tOrderInfoMapper.selectOrderInfoByOrderBusinessCode(tmpExcel.getOrderBusinessCode());
            TOrderCastChanges tOrderCastChanges = tOrderCastChangesMapper.selectByNewOne(tOrderInfo.getCode());
            TAdvanceOrderTmp tAdvanceOrderTmp = new TAdvanceOrderTmp();
            tAdvanceOrderTmp.setCode(IdWorkerUtil.getInstance().nextId());
            tAdvanceOrderTmp.setOrderBusinessCode(tmpExcel.getOrderBusinessCode());
            tAdvanceOrderTmp.setOrderCode(tOrderInfo.getCode());
            tAdvanceOrderTmp.setAdvanceFee(tmpExcel.getAdvanceFee());
            if (tOrderCastChanges.getCurrentDispatchRate() != null && tmpExcel.getAdvanceFee() != null) {
                tAdvanceOrderTmp.setAdvanceDispatchFee(OrderMoneyUtil.getDispatchFee(
                        tOrderCastChanges.getCurrentDispatchRate(), tmpExcel.getAdvanceFee()));
            } else {
                tAdvanceOrderTmp.setAdvanceDispatchFee(new BigDecimal(0));
            }
            tAdvanceOrderTmp.setEnable(false);
            tAdvanceOrderTmp.setOrderPayStatus(DictEnum.P070.code);//入账中
            tAdvanceOrderTmp.setOperaterId(CurrentUser.getCurrentUserID().toString());
            TAdvanceOrderTmpExample example = new TAdvanceOrderTmpExample();
            TAdvanceOrderTmpExample.Criteria cr = example.createCriteria();
            cr.andOrderBusinessCodeEqualTo(tAdvanceOrderTmp.getOrderBusinessCode());
            if (tAdvanceOrderTmpMapper.selectByExample(example).size() < 1) {
                tAdvanceOrderTmpMapper.insertSelective(tAdvanceOrderTmp);
                payStrategyContent.toPrePayment(PaymentEnums.JD_PREPAY, tOrderInfo, JDTradeTypeEnum.COMBALANCE_PREPAY.code);
            }
        } catch (Exception e) {
            log.error("预付款运单导入失败!, {}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再支付。";
            }
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再支付";
            }
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            }
            throw new RuntimeException("预付款运单导入失败!");
        }

        return ResultUtil.ok();
    }

    /** 京东预付款尾款支付
    * @description
    * <AUTHOR>
    * @date 2021/11/8 15:42
    */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil jdYkfWkPayment(TOrderInfoVO record) {
        try {
            TOrderInfo orderInfo = tOrderInfoMapper.selectByPrimaryKey(record.getId());
            TOrderCastCalcSnapshot orderCastCalcSnapshot = orderCastCalcSnapshotMapper.selectSnapsByOrderCode(record.getCode());
            String remark = "";
            if (null != record.getNewUserConfirmCarriagePayment() && null != orderCastCalcSnapshot.getUserConfirmCarriagePayment()) {
                if (record.getNewUserConfirmCarriagePayment().compareTo(orderCastCalcSnapshot.getUserConfirmCarriagePayment()) != 0) {
                    remark = "用户" + CurrentUser.getUserNickname() + "将运费由" + orderCastCalcSnapshot.getUserConfirmCarriagePayment() + "改为" + record.getNewUserConfirmCarriagePayment();
                    orderCastCalcSnapshot.setUserConfirmCarriagePayment(record.getNewUserConfirmCarriagePayment());
                    orderCastCalcSnapshot.setParam2(remark);
                    orderCastCalcSnapshotMapper.updateByPrimaryKeySelective(orderCastCalcSnapshot);
                }
            }

            //TODO 修改运单主表状态
            TOrderInfo forUpdateOrder = new TOrderInfo();
            forUpdateOrder.setId(record.getId());
            //运单状态：支付处理中
            forUpdateOrder.setOrderPayStatus(DictEnum.P070.code);
            orderInfo.setOrderPayStatus(DictEnum.P070.code);
            if (StringUtils.isNotEmpty(remark)) {
                forUpdateOrder.setUserConfirmPaymentAmount(record.getNewUserConfirmCarriagePayment());
                forUpdateOrder.setParam2(remark);
                orderInfo.setUserConfirmPaymentAmount(record.getNewUserConfirmCarriagePayment());
                orderInfo.setParam2(remark);
            }
            tOrderInfoMapper.updateByPrimaryKeySelective(forUpdateOrder);

            //TODO 新增运单执行状态子表
            TOrderState orderState = new TOrderState();
            orderState.setCode(IdWorkerUtil.getInstance().nextId());
            orderState.setOrderCode(record.getCode());
            //节点：会计、收单员已点击支付
            //TODO 根据当前登录人的线路角色，会计、收单员
            orderState.setStateNodeValue(DictEnum.SP0701.code);
            orderState.setOperateTime(new Date());
            //TODO 操作方式
            orderState.setOperateMethod(DictEnum.PC.code);
            orderState.setOperatorId(CurrentUser.getCurrentUserID());
            orderState.setEnable(false);
            orderState.setIfExpire(false);
            orderState.setCreateUser(CurrentUser.getUserNickname());
            orderState.setUpdateUser(CurrentUser.getUserNickname());
            orderStateMapper.insertSelective(orderState);

            payStrategyContent.toPrePayment(PaymentEnums.JD_PREPAY, orderInfo, JDTradeTypeEnum.COMBALANCE_WKPAY.code);

        } catch (Exception e) {
            log.error("ZJJ-241:支付失败!, {}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)){
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再支付。";
            }
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)){
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再支付";
            }
            if (StringUtils.checkChineseCharacter(message)){
                throw new RuntimeException(message);
            }
            throw new RuntimeException("ZJJ-241:支付失败!");
        }

        return ResultUtil.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil hxPrePayment(TAdvanceOrderTmpExcel tmpExcel) {
        try {
            TOrderInfo tOrderInfo = tOrderInfoMapper.selectOrderInfoByOrderBusinessCode(tmpExcel.getOrderBusinessCode());
            TOrderCastChanges tOrderCastChanges = tOrderCastChangesMapper.selectByNewOne(tOrderInfo.getCode());
            TAdvanceOrderTmp tAdvanceOrderTmp = new TAdvanceOrderTmp();
            tAdvanceOrderTmp.setCode(IdWorkerUtil.getInstance().nextId());
            tAdvanceOrderTmp.setOrderBusinessCode(tmpExcel.getOrderBusinessCode());
            tAdvanceOrderTmp.setOrderCode(tOrderInfo.getCode());
            tAdvanceOrderTmp.setAdvanceFee(tmpExcel.getAdvanceFee());
            if (tOrderCastChanges.getCurrentDispatchRate() != null && tmpExcel.getAdvanceFee() != null) {
                tAdvanceOrderTmp.setAdvanceDispatchFee(OrderMoneyUtil.getDispatchFee(
                        tOrderCastChanges.getCurrentDispatchRate(), tmpExcel.getAdvanceFee()));
            } else {
                tAdvanceOrderTmp.setAdvanceDispatchFee(new BigDecimal(0));
            }
            tAdvanceOrderTmp.setEnable(false);
            tAdvanceOrderTmp.setOrderPayStatus(DictEnum.P070.code);//入账中
            tAdvanceOrderTmp.setOperaterId(CurrentUser.getCurrentUserID().toString());
            TAdvanceOrderTmpExample example = new TAdvanceOrderTmpExample();
            TAdvanceOrderTmpExample.Criteria cr = example.createCriteria();
            cr.andOrderBusinessCodeEqualTo(tAdvanceOrderTmp.getOrderBusinessCode());
            if (tAdvanceOrderTmpMapper.selectByExample(example).size() < 1) {
                tAdvanceOrderTmpMapper.insertSelective(tAdvanceOrderTmp);
                hxPayStrategyContent.toPrePayment(HXPaymentEnums.HX_PREPAY, tOrderInfo, HXTradeTypeEnum.HX_COMBALANCE_PREPAY.code);
            }
        } catch (Exception e) {
            log.error("预付款运单导入失败!, {}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再支付。";
            }
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再支付";
            }
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            }
            throw new RuntimeException("预付款运单导入失败!");
        }

        return ResultUtil.ok();
    }

    /** 京东预付款尾款支付
     * @description
     * <AUTHOR>
     * @date 2021/11/8 15:42
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil hxYkfWkPayment(TOrderInfoVO record) {
        try {
            TOrderInfo orderInfo = tOrderInfoMapper.selectByPrimaryKey(record.getId());
            TOrderCastCalcSnapshot orderCastCalcSnapshot = orderCastCalcSnapshotMapper.selectSnapsByOrderCode(record.getCode());
            String remark = "";
            if (null != record.getNewUserConfirmCarriagePayment() && null != orderCastCalcSnapshot.getUserConfirmCarriagePayment()) {
                if (record.getNewUserConfirmCarriagePayment().compareTo(orderCastCalcSnapshot.getUserConfirmCarriagePayment()) != 0) {
                    remark = "用户" + CurrentUser.getUserNickname() + "将运费由" + orderCastCalcSnapshot.getUserConfirmCarriagePayment() + "改为" + record.getNewUserConfirmCarriagePayment();
                    orderCastCalcSnapshot.setUserConfirmCarriagePayment(record.getNewUserConfirmCarriagePayment());
                    orderCastCalcSnapshot.setParam2(remark);
                    orderCastCalcSnapshotMapper.updateByPrimaryKeySelective(orderCastCalcSnapshot);
                }
            }

            //TODO 修改运单主表状态
            TOrderInfo forUpdateOrder = new TOrderInfo();
            forUpdateOrder.setId(record.getId());
            //运单状态：支付处理中
            forUpdateOrder.setOrderPayStatus(DictEnum.P070.code);
            orderInfo.setOrderPayStatus(DictEnum.P070.code);
            if (StringUtils.isNotEmpty(remark)) {
                forUpdateOrder.setUserConfirmPaymentAmount(record.getNewUserConfirmCarriagePayment());
                forUpdateOrder.setParam2(remark);
                orderInfo.setUserConfirmPaymentAmount(record.getNewUserConfirmCarriagePayment());
                orderInfo.setParam2(remark);
            }
            tOrderInfoMapper.updateByPrimaryKeySelective(forUpdateOrder);

            //TODO 新增运单执行状态子表
            TOrderState orderState = new TOrderState();
            orderState.setCode(IdWorkerUtil.getInstance().nextId());
            orderState.setOrderCode(record.getCode());
            //节点：会计、收单员已点击支付
            //TODO 根据当前登录人的线路角色，会计、收单员
            orderState.setStateNodeValue(DictEnum.SP0701.code);
            orderState.setOperateTime(new Date());
            //TODO 操作方式
            orderState.setOperateMethod(DictEnum.PC.code);
            orderState.setOperatorId(CurrentUser.getCurrentUserID());
            orderState.setEnable(false);
            orderState.setIfExpire(false);
            orderState.setCreateUser(CurrentUser.getUserNickname());
            orderState.setUpdateUser(CurrentUser.getUserNickname());
            orderStateMapper.insertSelective(orderState);

            hxPayStrategyContent.toPrePayment(HXPaymentEnums.HX_PREPAY, orderInfo, HXTradeTypeEnum.HX_COMBALANCE_WKPAY.code);

        } catch (Exception e) {
            log.error("ZJJ-241:支付失败!, {}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)){
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再支付。";
            }
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)){
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再支付";
            }
            if (StringUtils.checkChineseCharacter(message)){
                throw new RuntimeException(message);
            }
            throw new RuntimeException("ZJJ-241:支付失败!");
        }

        return ResultUtil.ok();
    }

    @Override
    public ResultUtil selectByPage(TAdvanceOrderTmpDto record) {
        if (null != record.getCreateTime()) {
            record.setStartTime(record.getCreateTime()[0]);
            record.setEndTime(record.getCreateTime()[1]);
        }
        if (null != record.getFhTime()) {
            record.setFhStartTime(record.getFhTime()[0]);
            record.setFhEndTime(record.getFhTime()[1]);
        }
        if (null != record.getShTime()) {
            record.setShStartTime(record.getShTime()[0]);
            record.setShEndTime(record.getShTime()[1]);
        }
        if (null != record.getFhbdTime()) {
            record.setFhbdStartTime(record.getFhbdTime()[0]);
            record.setFhbdEndTime(record.getFhbdTime()[1]);
        }
        if (null != record.getShbdTime()) {
            record.setShbdStartTime(record.getShbdTime()[0]);
            record.setShbdEndTime(record.getShbdTime()[1]);
        }
        if (null != record.getFinalTime()) {
            record.setFinalPayTime(record.getFinalTime()[0]);
            record.setFinalEndTime(record.getFinalTime()[1]);
        }

        if (null != record.getCompanyId()) {
            List<String> userCompanyId = new ArrayList<>();
            userCompanyId.add(record.getCompanyId() + "");
            record.setCompanyIdList(userCompanyId);
        } else {
            List<String> userCompanyId = CurrentUser.getUserCompanyId();
            if (null != userCompanyId && !userCompanyId.isEmpty()) {
                record.setCompanyIdList(userCompanyId);
            }
        }

        Page<Object> objectPage = PageHelper.startPage(record.getPage(), record.getSize());
        List<TAdvanceOrderTmpVo> list = tAdvanceOrderTmpMapper.selectByPage(record);
        for (TAdvanceOrderTmpVo vo : list) {
            TOrderCastChanges tOrderCastChanges = tOrderCastChangesMapper.selectByNewOne(vo.getOrderCode());
            if (null != vo.getCreateTime()) {
                vo.setCreateTimeStr(DateUtils.formatDateTime(vo.getCreateTime()));
            }
            //预估调度费
            if (vo.getEstimateTotalFee() != null) {
                vo.setEstimateDispatchFee(OrderMoneyUtil.getDispatchFee(tOrderCastChanges.getCurrentDispatchRate(), vo.getEstimateTotalFee()));
            }
            //实际运费 如果用户确认运费为空去规则运费
            if (vo.getCarriageFee() == null || vo.getCarriageFee().compareTo(BigDecimal.ZERO) == 0) {
                vo.setCarriageFee(vo.getRulePaymentAmount());
                //实际运费为0时， 实际调度费也为0
                if (vo.getCarriageFee() == null || vo.getCarriageFee().compareTo(BigDecimal.ZERO) == 0) {
                    vo.setDispatchFee(BigDecimal.ZERO);
                }
            }
            //剩余运费 调度费   实际为空时取预估
            if (!"M100".equals(vo.getOrderExecuteStatus())) {
                if (vo.getCarriageFee() != null && !(vo.getCarriageFee().compareTo(BigDecimal.ZERO) == 0)) {
                    vo.setSurplusFee(vo.getCarriageFee().subtract(vo.getAdvanceFee()));
                    vo.setSurplusDispatchFee(vo.getDispatchFee().subtract(vo.getAdvanceDispatchFee()));
                } else {
                    if (vo.getEstimateTotalFee() != null && !(vo.getEstimateTotalFee().compareTo(BigDecimal.ZERO) == 0)) {
                        vo.setSurplusFee(vo.getEstimateTotalFee().subtract(vo.getAdvanceFee()));
                        vo.setSurplusDispatchFee(vo.getEstimateDispatchFee().subtract(vo.getAdvanceDispatchFee()));
                    } else {
                        vo.setSurplusFee(BigDecimal.ZERO);
                        vo.setSurplusDispatchFee(BigDecimal.ZERO);
                    }
                }
            } else {
                vo.setSurplusFee(BigDecimal.ZERO);
                vo.setSurplusDispatchFee(BigDecimal.ZERO);
            }

            //如果剩余运费小于0 为0
            if (vo.getSurplusFee().compareTo(BigDecimal.ZERO) < 0) {
                vo.setSurplusFee(BigDecimal.ZERO);
            }
            //如果剩余调度费小于0 为0
            if (vo.getSurplusDispatchFee().compareTo(BigDecimal.ZERO) < 0) {
                vo.setSurplusDispatchFee(BigDecimal.ZERO);
            }
            //已支付运费
            if (!"P070".equals(vo.getAdvanceOrderPayStatus()) && !"M080".equals(vo.getAdvanceOrderPayStatus())) {
                vo.setPaidFee(vo.getAdvanceFee());
                if ("M100".equals(vo.getOrderExecuteStatus())) {
                    vo.setPaidFee(vo.getAdvanceFee().add(vo.getCarriageFee()));
                }
            } else {
                vo.setPaidFee(BigDecimal.ZERO);
            }
        }

        Map<String, Object> map = new HashMap<>();
        map.put("data", list);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), map, objectPage.getTotal());
    }

    /**
    * @description 查询预付款数据合计
    * <AUTHOR>
    * @date 2022/3/16 09:53
    */
    private Map<String, Object> yfkTotalData(TAdvanceOrderTmpDto record) {
        BigDecimal advanceFeeHj = new BigDecimal(0);
        BigDecimal advanceDispatchFeeHj = new BigDecimal(0);
        BigDecimal surplusFeeHj = new BigDecimal(0);
        BigDecimal surplusDispatchFeeHj = new BigDecimal(0);

        List<TAdvanceOrderTmpVo> listHj = tAdvanceOrderTmpMapper.selectByPage(record);
        for (TAdvanceOrderTmpVo hj : listHj) {
            //实际运费 如果用户确认运费为空去规则运费
            if (hj.getCarriageFee() == null || hj.getCarriageFee().compareTo(BigDecimal.ZERO) == 0) {
                hj.setCarriageFee(hj.getRulePaymentAmount());
                //实际运费为0时， 实际调度费也为0
                if (hj.getCarriageFee() == null || hj.getCarriageFee().compareTo(BigDecimal.ZERO) == 0) {
                    hj.setDispatchFee(BigDecimal.ZERO);
                }
            }
            //剩余运费 调度费   实际为空时取预估
            if (!"M100".equals(hj.getOrderExecuteStatus())) {
                if (hj.getCarriageFee() != null && !(hj.getCarriageFee().compareTo(BigDecimal.ZERO) == 0)) {
                    hj.setSurplusFee(hj.getCarriageFee().subtract(hj.getAdvanceFee()));
                    hj.setSurplusDispatchFee(hj.getDispatchFee().subtract(hj.getAdvanceDispatchFee()));
                } else {
                    if (hj.getEstimateTotalFee() != null && !(hj.getEstimateTotalFee().compareTo(BigDecimal.ZERO) == 0)) {
                        hj.setSurplusFee(hj.getEstimateTotalFee().subtract(hj.getAdvanceFee()));
                        hj.setSurplusDispatchFee(hj.getEstimateDispatchFee().subtract(hj.getAdvanceDispatchFee()));
                    } else {
                        hj.setSurplusFee(BigDecimal.ZERO);
                        hj.setSurplusDispatchFee(BigDecimal.ZERO);
                    }
                }
            } else {
                hj.setSurplusFee(BigDecimal.ZERO);
                hj.setSurplusDispatchFee(BigDecimal.ZERO);
            }
            //如果剩余运费小于0 为0
            if (hj.getSurplusFee().compareTo(BigDecimal.ZERO) < 0) {
                hj.setSurplusFee(BigDecimal.ZERO);
            }
            //如果剩余调度费小于0 为0
            if (hj.getSurplusDispatchFee().compareTo(BigDecimal.ZERO) < 0) {
                hj.setSurplusDispatchFee(BigDecimal.ZERO);
            }
            if (hj.getAdvanceFee() != null) {
                advanceFeeHj = advanceFeeHj.add(hj.getAdvanceFee());
            }
            if (hj.getAdvanceDispatchFee() != null) {
                advanceDispatchFeeHj = advanceDispatchFeeHj.add(hj.getAdvanceDispatchFee());
            }
            if (hj.getSurplusFee() != null) {
                surplusFeeHj = surplusFeeHj.add(hj.getSurplusFee());
            }
            if (hj.getSurplusDispatchFee() != null) {
                surplusDispatchFeeHj = surplusDispatchFeeHj.add(hj.getSurplusDispatchFee());
            }
        }
        Map<String, Object> map = new HashMap<>();
        map.put("advanceFeeHj", advanceFeeHj);
        map.put("advanceDispatchFeeHj", advanceDispatchFeeHj);
        map.put("surplusFeeHj", surplusFeeHj);
        map.put("surplusDispatchFeeHj", surplusDispatchFeeHj);
        return map;
    }

    @Override
    public TAdvanceOrderTmp selectAdvanceOrderTempByOrderCode(String orderCode) {
        return tAdvanceOrderTmpMapper.selectAdvanceOrderTempByOrderCode(orderCode);
    }

    @Override
    public TAdvanceOrderTmp selectAdvanceOrderTempByOrderBusinessCode(String orderBusinessCode) {
        return tAdvanceOrderTmpMapper.selectAdvanceOrderTempByOrderBusinessCode(orderBusinessCode);
    }

    /**
     * <AUTHOR>
     * @Description 查询预付款提现处理中和提现完成的
     * @Date 2020/5/9 3:24 下午
     * @Param
     * @return
    **/
    @Override
    public BigDecimal selectAdavanceFeeByBankNos(List<String> cardNos) {
        Date fromTime = new Date();
        Date endTime = new Date();
        try {
            fromTime = DateUtils.forDateStart();//获取当前月第一天开始时间
            endTime = DateUtils.forDateEnd();//获取当前月最后一天结束时间
        } catch (ParseException e) {
            log.error("获取日期错误！");
        }
        // 查询提现处理中和提现完成的
        BigDecimal totalAmount = tAdvanceOrderTmpMapper.selectAdavanceFeeByBankNos(cardNos, fromTime, endTime);
        log.info("查询预付款提现处理中和提现完成的 {}", totalAmount);
        return totalAmount;
    }

    @Override
    public ResultUtil tAdvanceOrderTmpExport( TAdvanceOrderTmpDto tAdvanceOrderTmpDto) {
        if(null!=tAdvanceOrderTmpDto.getCreateTime()&&!"".equals(tAdvanceOrderTmpDto.getCreateTime())){
            tAdvanceOrderTmpDto.setStartTime(tAdvanceOrderTmpDto.getCreateTime()[0]);
            tAdvanceOrderTmpDto.setEndTime(tAdvanceOrderTmpDto.getCreateTime()[1]);
        }
        if(null != tAdvanceOrderTmpDto.getFinalTime() && !"".equals(tAdvanceOrderTmpDto.getFinalTime())){
            tAdvanceOrderTmpDto.setFinalPayTime(tAdvanceOrderTmpDto.getFinalTime()[0]);
            tAdvanceOrderTmpDto.setFinalEndTime(tAdvanceOrderTmpDto.getFinalTime()[1]);
        }
        if(null != tAdvanceOrderTmpDto.getFhTime() && !"".equals(tAdvanceOrderTmpDto.getFhTime())){
            tAdvanceOrderTmpDto.setFhStartTime(tAdvanceOrderTmpDto.getFhTime()[0]);
            tAdvanceOrderTmpDto.setFhEndTime(tAdvanceOrderTmpDto.getFhTime()[1]);
        }
        if(null != tAdvanceOrderTmpDto.getShTime() && !"".equals(tAdvanceOrderTmpDto.getShTime())){
            tAdvanceOrderTmpDto.setShStartTime(tAdvanceOrderTmpDto.getShTime()[0]);
            tAdvanceOrderTmpDto.setShEndTime(tAdvanceOrderTmpDto.getShTime()[1]);
        }
        if(null != tAdvanceOrderTmpDto.getFhbdTime() && !"".equals(tAdvanceOrderTmpDto.getFhbdTime())){
            tAdvanceOrderTmpDto.setFhbdStartTime(tAdvanceOrderTmpDto.getFhbdTime()[0]);
            tAdvanceOrderTmpDto.setFhbdEndTime(tAdvanceOrderTmpDto.getFhbdTime()[1]);
        }
        if(null != tAdvanceOrderTmpDto.getShbdTime() && !"".equals(tAdvanceOrderTmpDto.getShbdTime())){
            tAdvanceOrderTmpDto.setShbdStartTime(tAdvanceOrderTmpDto.getShbdTime()[0]);
            tAdvanceOrderTmpDto.setShbdEndTime(tAdvanceOrderTmpDto.getShbdTime()[1]);
        }

        ResultUtil resultUtil = companyService.selectCompany();
        List<LinkedHashMap> companyInfoDTOS = (List<LinkedHashMap>)resultUtil.getData();
        List<String> userCompanyIds = new ArrayList<>();
        for (LinkedHashMap compangId :companyInfoDTOS){
            userCompanyIds.add(compangId.get("id").toString());
        }
        if (null!=tAdvanceOrderTmpDto && null!=tAdvanceOrderTmpDto.getCompanyId()){
            List<String> userCompanyId = new ArrayList<>();
            userCompanyId.add(tAdvanceOrderTmpDto.getCompanyId()+"");
            tAdvanceOrderTmpDto.setCompanyIdList(userCompanyId);
        }else{
            List<String> userCompanyId = CurrentUser.getUserCompanyId();
            Integer currentUserOrgID = CurrentUser.getCurrentUserOrgID();
            if ( CurrentUser.isSuperAdmin()){
                tAdvanceOrderTmpDto.setCompanyIdList(userCompanyIds);
            }else {
                if (null != currentUserOrgID && currentUserOrgID.equals(1)){
                    tAdvanceOrderTmpDto.setCompanyIdList(userCompanyIds);
                }else{
                    tAdvanceOrderTmpDto.setCompanyIdList(userCompanyId);
                }
            }

        }
        List<TAdvanceOrderTmpVo> list = tAdvanceOrderTmpMapper.selectByPage(tAdvanceOrderTmpDto);
        for(TAdvanceOrderTmpVo vo:list){
            if(null!=vo.getCreateTime() &&!"".equals(vo.getCreateTime())){
                vo.setCreateTimeStr(DateUtils.formatDateTime(vo.getCreateTime()));
            }
            //实际运费 如果用户确认运费为空去规则运费
            if(vo.getCarriageFee()==null||"".equals(vo.getCarriageFee())|| (vo.getCarriageFee().compareTo(BigDecimal.ZERO)==0)){
                vo.setCarriageFee(vo.getRulePaymentAmount());
                //实际运费为0时， 实际调度费也为0
                if(vo.getCarriageFee()==null||"".equals(vo.getCarriageFee())|| (vo.getCarriageFee().compareTo(BigDecimal.ZERO)==0)) {
                    vo.setDispatchFee(BigDecimal.ZERO);
                }
            }
            //剩余运费 调度费   实际为空时取预估
            if(!"M100".equals(vo.getOrderExecuteStatus())) {
                if(vo.getCarriageFee()!=null&& !"".equals(vo.getCarriageFee())&& !(vo.getCarriageFee().compareTo(BigDecimal.ZERO)==0)){
                    vo.setSurplusFee(vo.getCarriageFee().subtract(vo.getAdvanceFee()));
                    vo.setSurplusDispatchFee(vo.getDispatchFee().subtract(vo.getAdvanceDispatchFee()));
                }else{
                    if(vo.getEstimateTotalFee()!=null&& !"".equals(vo.getEstimateTotalFee())&& !(vo.getEstimateTotalFee().compareTo(BigDecimal.ZERO)==0)){
                        vo.setSurplusFee(vo.getEstimateTotalFee().subtract(vo.getAdvanceFee()));
                        vo.setSurplusDispatchFee(vo.getEstimateDispatchFee().subtract(vo.getAdvanceDispatchFee()));
                    }else{
                        vo.setSurplusFee(BigDecimal.ZERO);
                        vo.setSurplusDispatchFee(BigDecimal.ZERO);
                    }
                }
            }else{
                vo.setSurplusFee(BigDecimal.ZERO);
                vo.setSurplusDispatchFee(BigDecimal.ZERO);
            }

            //如果剩余运费小于0 为0
            if(vo.getSurplusFee().compareTo(BigDecimal.ZERO)<0){
                vo.setSurplusFee(BigDecimal.ZERO);
            }
            //如果剩余调度费小于0 为0
            if(vo.getSurplusDispatchFee().compareTo(BigDecimal.ZERO)<0){
                vo.setSurplusDispatchFee(BigDecimal.ZERO);
            }
            //已支付运费
            if(!"P070".equals(vo.getAdvanceOrderPayStatus())&&!"M080".equals(vo.getAdvanceOrderPayStatus())){
                vo.setPaidFee(vo.getAdvanceFee());
                if("M100".equals(vo.getOrderExecuteStatus())){
                    vo.setPaidFee(vo.getAdvanceFee().add(vo.getCarriageFee()));
                }
            }else{
                vo.setPaidFee(BigDecimal.ZERO);
            }
        }

        Map<String,Object> map = new HashMap<>();
        String[] headers ={
                "运单编号",
                "司机姓名",
                "司机电话",
                "业务部",
                "业务部电话",
                "运单状态",
                "预付款状态",
                "预付款提现时间",
                "车牌号",
                "货物类型",
                "起点",
                "终点",
                "原发重量",
                "实收重量",
                "预估运费(元)",
                "预估调度费(元)",
                "实际运费(元)",
                "实际调度费(元)",
                "预付运费(元)",
                "预付调度费(元)",
                "剩余运费(元)",
                "剩余调度费(元)",
                "已支付运费(元)",
                "承运方",
                "导入时间",
                "尾款付款时间",
                "车队长",
                "客户名称",
                "委托方名称",
                "货源"
        };

        String[] names ={
                "orderBusinessCode",
                "realName",
                "realPhone",
                "ywName",
                "ywPhone",
                "orderExecuteStatusStr",
                "orderPayStatusStr",
                "returnTime",
                "vehicleNumber",
                "goodsName",
                "fromName",
                "endName",
                "primaryWeight",
                "dischargeWeight",
                "estimateTotalFee",
                "estimateDispatchFee",
                "carriageFee",
                "dispatchFee",
                "advanceFee",
                "advanceDispatchFee",
                "surplusFee",
                "surplusDispatchFee",
                "paidFee",
                "carrierName",
                "createTimeStr",
                "finalPayTime",
                "captainName",
                "companyClient",
                "companyEntrust",
                "sourceName"
        };

        map.put("list",list);
        map.put("headers",headers);
        map.put("names",names);

        return ResultUtil.ok(map);

    }

    @Override
    public List<String> selectOrderBusinessCode(List<String> codes) {
        return tAdvanceOrderTmpMapper.selectOrderBusinessCode(codes);
    }

}
