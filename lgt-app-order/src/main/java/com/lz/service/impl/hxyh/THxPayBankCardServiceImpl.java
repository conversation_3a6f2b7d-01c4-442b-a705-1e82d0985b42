package com.lz.service.impl.hxyh;

import com.lz.dao.THxPayBankCardMapper;
import com.lz.dto.TZtBankCardDTO;
import com.lz.service.hxyh.THxPayBankCardService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class THxPayBankCardServiceImpl implements THxPayBankCardService {

    @Resource
    private THxPayBankCardMapper hxPayBankCardMapper;

    @Override
    public TZtBankCardDTO selectByEnduserIdAndBankCardId(Integer enduserId, Integer bankCardId) {
        return hxPayBankCardMapper.selectByEnduserIdAndBankCardId(enduserId, bankCardId);
    }

    @Override
    public TZtBankCardDTO selectOpenRoleBankCardInfo(Integer bankCardId) {
        return hxPayBankCardMapper.selectOpenRoleBankCardInfo(bankCardId);
    }

}
