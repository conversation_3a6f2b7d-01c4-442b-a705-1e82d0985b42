package com.lz.service.impl;

import cn.hutool.json.JSONUtil;
import com.codingapi.txlcn.tc.annotation.DTXPropagation;
import com.codingapi.txlcn.tc.annotation.LcnTransaction;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.JDTradeTypeEnum;
import com.lz.common.model.CodeEnum;
import com.lz.common.model.datareport.sddrFcfgs.Fcenum.FCFGSEnum;
import com.lz.common.util.*;
import com.lz.dao.TOrderInfoMapper;
import com.lz.dao.TOrderPayDetailMapper;
import com.lz.dto.TOrderPayInfoDTO;
import com.lz.dto.TxPcRecordDTO;
import com.lz.dto.TxRecordDTO;
import com.lz.model.TJdWallet;
import com.lz.model.TOrderPayDetail;
import com.lz.model.TZtWallet;
import com.lz.payment.JDWalletUtil;
import com.lz.payment.hxyh.HXWalletUtil;
import com.lz.service.TOrderPayDetailService;
import com.lz.vo.TOrderPayDetailVO;
import com.lz.vo.TxPcRecordQueryVO;
import com.lz.vo.TxRecordQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;

/**
 * Created by sangbin on 2019/4/23.
 */
@Slf4j
@Service
public class TOrderPayDetailServiceImpl implements TOrderPayDetailService {
    @Autowired
    private TOrderPayDetailMapper tOrderPayDetailMapper;

    @Autowired
    private TOrderInfoMapper orderInfoMapper;

    @Resource
    private JDWalletUtil jdWalletUtil;

    @Resource
    private HXWalletUtil hxWalletUtil;

    @Override
    @LcnTransaction(propagation = DTXPropagation.SUPPORTS)
    @Transactional
    public int save(TOrderPayDetail orderPayDetail) {
        return tOrderPayDetailMapper.insertSelective(orderPayDetail);
    }

    @Override
    public TOrderPayInfoDTO selectByOrderPayDetailKey(String code) {
        return tOrderPayDetailMapper.selectByOrderPayDetailKey(code);
    }

    @Override
    public BigDecimal selectSumCashByBankNo(List<String> cardNos) {
        Date fromTime = new Date();
        Date endTime = new Date();
        try {
            fromTime = DateUtils.forDateStart();//获取当前月第一天开始时间
            endTime = DateUtils.forDateEnd();//获取当前月最后一天结束时间
        } catch (ParseException e) {
            log.error("获取日期错误！");
        }
        // 查询提现处理中和提现完成的
        BigDecimal totalAmount = tOrderPayDetailMapper.selectSumCashByBankNo(cardNos, fromTime, endTime);
        log.info("查询提现处理中和提现完成的 {}", totalAmount);
        return totalAmount;
    }

    @Override
    public TOrderPayInfoDTO selectBankByCastChanges(String code) {
        return tOrderPayDetailMapper.selectBankByCastChanges(code);
    }

    @Override
    @LcnTransaction(propagation = DTXPropagation.SUPPORTS)
    @Transactional
    public int updateStatusByPayCode(TOrderPayDetailVO ov) {
        return tOrderPayDetailMapper.updateStatusByPayCode(ov);
    }

    @Override
    public TOrderPayDetail selectByCode(String code) {
        return tOrderPayDetailMapper.selectByCode(code);
    }

    @Override
    public ResultUtil selectTxRecord(TxRecordQueryVO vo) {
        try {
            List<Integer> walletIds = new ArrayList<>();
            /* 京东已下线
            TJdWallet tJdWallet = jdWalletUtil.selectByEnduserId(CurrentUser.getEndUserId());
            if (null != tJdWallet) {
                walletIds.add(tJdWallet.getId());
            }*/
            TZtWallet tZtWallet = hxWalletUtil.selectByEnduserId(CurrentUser.getEndUserId());
            if (null != tZtWallet) {
                walletIds.add(tZtWallet.getId());
            }
            if (walletIds.isEmpty()) {
                return ResultUtil.ok();
            }
            if (walletIds.size() == 1) {
                vo.setWalletId(walletIds.get(0));
            }
            if (walletIds.size() > 1) {
                vo.setWalletIds(walletIds);
            }
            vo.setEnduserId(CurrentUser.getEndUserId());
            Page<Object> objects = PageHelper.startPage(vo.getPage(), vo.getSize());
            List<TxRecordDTO> txRecordDTOS = tOrderPayDetailMapper.selectTxRecord(vo);
            for (TxRecordDTO txRecordDTO : txRecordDTOS) {
                if (null != txRecordDTO.getCardHolder()) {
                    txRecordDTO.setCardHolder(txRecordDTO.getCardHolder().split(",")[0]);
                }
                if (null != txRecordDTO.getBankNo()) {
                    String bankNo = txRecordDTO.getBankNo().split(",")[0];
                    txRecordDTO.setBankNo(StringUtils.getBankNo(bankNo));
                }
                // 提现失败, 退回金额
                if (DictEnum.M080.code.equals(txRecordDTO.getOrderPayStatus()) ||
                        DictEnum.M120.code.equals(txRecordDTO.getOrderPayStatus())) {
                    if (null != txRecordDTO.getTradeType() && StringUtils.isNotBlank(txRecordDTO.getTradeType())) {
                        String[] tradeStatus = txRecordDTO.getTradeStatus().split(",");
                        for (int i = 0; i < tradeStatus.length; i++) {
                            if (DictEnum.TRADE_FAILED.code.equals(tradeStatus[i])) {
                                String[] tradeTypes = txRecordDTO.getTradeType().split(",");
                                String tradeType = tradeTypes[i];
                                if (JDTradeTypeEnum.TXTRANSFER.code.equals(tradeType)) {
                                    txRecordDTO.setReturnAmount(txRecordDTO.getOrderTotalPayment());
                                } else if (JDTradeTypeEnum.TXSERVICEFEE.code.equals(tradeType)) {
                                    txRecordDTO.setReturnAmount(txRecordDTO.getOrderPrePayAmount());
                                } else if (JDTradeTypeEnum.TX.code.equals(tradeType)) {
                                    txRecordDTO.setReturnAmount(txRecordDTO.getOrderActualPayment());
                                }
                            }
                        }
                    }
                }
                if (DictEnum.P070.code.equals(txRecordDTO.getOrderPayStatus())) {
                    txRecordDTO.setOrderPayStatus(DictEnum.P110.code);
                }
                if (DictEnum.M080.code.equals(txRecordDTO.getOrderPayStatus())) {
                    txRecordDTO.setOrderPayStatus(DictEnum.P110.code);
                }
                if (DictEnum.M080.code.equals(txRecordDTO.getOrderPayStatus())
                        || DictEnum.M120.code.equals(txRecordDTO.getOrderPayStatus())
                        || DictEnum.M130.code.equals(txRecordDTO.getOrderPayStatus())) {
                    if (null != txRecordDTO.getReturnTime() && StringUtils.isNotBlank(txRecordDTO.getReturnTime())) {
                        String[] tradeStatus = txRecordDTO.getTradeStatus().split(",");
                        for (int i = 0; i < tradeStatus.length; i++) {
                            if (DictEnum.TRADE_FAILED.code.equals(tradeStatus[i])) {
                                String[] returnTimes = txRecordDTO.getReturnTime().split(",");
                                String returnTime = returnTimes[i];
                                txRecordDTO.setDate(DateUtils.parseDate(returnTime, DateUtils.parsePattern));
                            }
                        }
                        // 完成时间
                        if (DictEnum.M130.code.equals(txRecordDTO.getOrderPayStatus())) {
                            String[] returnTimes = txRecordDTO.getReturnTime().split(",");
                            List<Date> times = new ArrayList<>(returnTimes.length);
                            Arrays.stream(returnTimes).forEach((time) -> {
                                try {
                                    times.add(DateUtils.parseDate(time, DateUtils.parsePattern));
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }
                            });
                            if (!times.isEmpty()) {
                                times.sort(Comparator.comparing(Date::getTime).reversed());
                                Date date = times.get(0);
                                txRecordDTO.setDate(date);
                            }

                        }
                    }
                } else {
                    txRecordDTO.setDate(txRecordDTO.getCreateTime());
                }
            }
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), txRecordDTOS, objects.getTotal());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询提现记录失败, {}", ThrowableUtil.getStackTrace(e));
            return ResultUtil.error("查询失败");
        }
    }

    @Override
    public ResultUtil selectTxPcRecord(TxPcRecordQueryVO vo) {
        try {
            Page<Object> objects = PageHelper.startPage(vo.getPage(), vo.getSize());
            List<TxPcRecordDTO> txPcRecordDTOS = tOrderPayDetailMapper.selectTxPcRecord(vo);
            for (TxPcRecordDTO txRecordDTO : txPcRecordDTOS) {
                if (null != txRecordDTO.getCardHolder()) {
                    txRecordDTO.setCardHolder(txRecordDTO.getCardHolder().split(",")[0]);
                }
                if (null != txRecordDTO.getBankNo()) {
                    String bankNo = txRecordDTO.getBankNo().split(",")[0];
                    txRecordDTO.setBankNo(StringUtils.getBankNo(bankNo));
                }
                if (DictEnum.P070.code.equals(txRecordDTO.getOrderPayStatusCode())) {
                    txRecordDTO.setOrderPayStatusCode(DictEnum.P110.code);
                }
                if (DictEnum.M080.code.equals(txRecordDTO.getOrderPayStatusCode())) {
                    txRecordDTO.setOrderPayStatusCode(DictEnum.P110.code);
                }
                if (DictEnum.M080.code.equals(txRecordDTO.getOrderPayStatusCode())
                        ||DictEnum.M120.code.equals(txRecordDTO.getOrderPayStatusCode())
                        || DictEnum.M130.code.equals(txRecordDTO.getOrderPayStatusCode())) {
                    if (null != txRecordDTO.getReturnTimes() && StringUtils.isNotBlank(txRecordDTO.getReturnTimes())) {
                        String[] tradeStatus = txRecordDTO.getTradeStatus().split(",");
                        for (int i = 0; i < tradeStatus.length; i++) {
                            if (DictEnum.TRADE_FAILED.code.equals(tradeStatus[i])) {
                                String[] returnTimes = txRecordDTO.getReturnTimes().split(",");
                                String returnTime = returnTimes[i];
                                txRecordDTO.setReturnTime(DateUtils.parseDate(returnTime, DateUtils.parsePattern));
                            }
                        }
                        // 完成时间
                        if (DictEnum.M130.code.equals(txRecordDTO.getOrderPayStatusCode())) {
                            String[] returnTimes = txRecordDTO.getReturnTimes().split(",");
                            List<Date> times = new ArrayList<>(returnTimes.length);
                            Arrays.stream(returnTimes).forEach((time) -> {
                                try {
                                    times.add(DateUtils.parseDate(time, DateUtils.parsePattern));
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }
                            });
                            if (!times.isEmpty()) {
                                times.sort(Comparator.comparing(Date::getTime).reversed());
                                Date date = times.get(0);
                                txRecordDTO.setReturnTime(date);
                            }

                        }
                    }
                }
            }
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), txPcRecordDTOS, objects.getTotal());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询提现记录失败, {}", ThrowableUtil.getStackTrace(e));
            return ResultUtil.error("查询失败");
        }
    }

    @Override
    public ResultUtil selectHxTxPcRecord(TxPcRecordQueryVO vo) {
        try {
            Page<Object> objects = PageHelper.startPage(vo.getPage(), vo.getSize());
            List<TxPcRecordDTO> txPcRecordDTOS = tOrderPayDetailMapper.selectHxTxPcRecord(vo);
            for (TxPcRecordDTO txRecordDTO : txPcRecordDTOS) {
                if (null != txRecordDTO.getCardHolder()) {
                    txRecordDTO.setCardHolder(txRecordDTO.getCardHolder().split(",")[0]);
                }
                if (null != txRecordDTO.getBankNo()) {
                    String bankNo = txRecordDTO.getBankNo().split(",")[0];
                    txRecordDTO.setBankNo(StringUtils.getBankNo(bankNo));
                }
                if (DictEnum.P070.code.equals(txRecordDTO.getOrderPayStatusCode())) {
                    txRecordDTO.setOrderPayStatusCode(DictEnum.P110.code);
                }
                if (DictEnum.M080.code.equals(txRecordDTO.getOrderPayStatusCode())) {
                    txRecordDTO.setOrderPayStatusCode(DictEnum.P110.code);
                }
                if (DictEnum.M080.code.equals(txRecordDTO.getOrderPayStatusCode())
                        ||DictEnum.M120.code.equals(txRecordDTO.getOrderPayStatusCode())
                        || DictEnum.M130.code.equals(txRecordDTO.getOrderPayStatusCode())) {
                    if (null != txRecordDTO.getReturnTimes() && StringUtils.isNotBlank(txRecordDTO.getReturnTimes())) {
                        String[] tradeStatus = txRecordDTO.getTradeStatus().split(",");
                        for (int i = 0; i < tradeStatus.length; i++) {
                            if (DictEnum.TRADE_FAILED.code.equals(tradeStatus[i])) {
                                String[] returnTimes = txRecordDTO.getReturnTimes().split(",");
                                String returnTime = returnTimes[i];
                                txRecordDTO.setReturnTime(DateUtils.parseDate(returnTime, DateUtils.parsePattern));
                            }
                        }
                        // 完成时间
                        if (DictEnum.M130.code.equals(txRecordDTO.getOrderPayStatusCode())) {
                            String[] returnTimes = txRecordDTO.getReturnTimes().split(",");
                            List<Date> times = new ArrayList<>(returnTimes.length);
                            Arrays.stream(returnTimes).forEach((time) -> {
                                try {
                                    times.add(DateUtils.parseDate(time, DateUtils.parsePattern));
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }
                            });
                            if (!times.isEmpty()) {
                                times.sort(Comparator.comparing(Date::getTime).reversed());
                                Date date = times.get(0);
                                txRecordDTO.setReturnTime(date);
                            }

                        }
                    }
                }
            }
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), txPcRecordDTOS, objects.getTotal());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询提现记录失败, {}", ThrowableUtil.getStackTrace(e));
            return ResultUtil.error("查询失败");
        }
    }

    @Override
    public ResultUtil updateCapitalSendState(Integer payDetailId) {
        TOrderPayDetail tOrderPayDetail = new TOrderPayDetail();
        tOrderPayDetail.setId(payDetailId);
        tOrderPayDetail.setSduploadedStatus(FCFGSEnum.UPLOADED.code);
        tOrderPayDetailMapper.updateByPrimaryKeySelective(tOrderPayDetail);
        return ResultUtil.ok();
    }

    @Override
    public ResultUtil updateAhCapitalSendState(Integer payDetailId) {
        TOrderPayDetail tOrderPayDetail = new TOrderPayDetail();
        tOrderPayDetail.setId(payDetailId);
        tOrderPayDetail.setAhuploadedStatus(FCFGSEnum.UPLOADED.code);
        tOrderPayDetailMapper.updateByPrimaryKeySelective(tOrderPayDetail);
        return ResultUtil.ok();
    }

}
