package com.lz.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.codingapi.txlcn.tc.annotation.DTXPropagation;
import com.codingapi.txlcn.tc.annotation.LcnTransaction;
import com.lz.api.*;
import com.lz.common.config.RedisUtil;
import com.lz.common.constants.MqMessageTag;
import com.lz.common.constants.MqMessageTopic;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.MQMessage;
import com.lz.common.util.*;
import com.lz.dao.*;
import com.lz.dao.mongodb.TrajectoryMongoDao;
import com.lz.example.TOrderCarlocationExample;
import com.lz.example.TOrderStateExample;
import com.lz.model.*;
import com.lz.model.mongodb.TrajectoryMongo;
import com.lz.model.trajectory.resp.recent.TransTimeManageVResp;
import com.lz.service.GpsTaskService;
import com.lz.service.TOrderInfoService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.util.DigitlFlowUtil;
import com.lz.vo.CarDriverRelVO;
import com.lz.vo.TOrderInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.threads.ThreadPoolExecutor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.awt.geom.Point2D;
import java.util.*;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;

/**
 * auth dingweibo
 * 执行未卸货车辆更新轨迹
 */
@Service("gpsTaskService")
@Slf4j
public class GpsTaskServiceImpl implements GpsTaskService {

    @Autowired
    private TOrderInfoService tOrderInfoService;

    @Resource
    private TOrderInfoMapper tOrderInfoMapper;

    @Resource
    private TrajectoryRecentAPI trajectoryRecentAPI;

    @Resource
    private LineService lineService;

    @Autowired
    private RedisUtil redisUtil;

    @Resource
    private SysParamAPI sysParamAPI;

    @Autowired
    private DigitlFlowUtil digitlFlowUtil;

    @Resource
    private TOrderStateMapper tOrderStateMapper;

    @Autowired
    private AppCommonAPI appCommonAPI;

    @Autowired
    private MqAPI mqAPI;

    @Resource
    private TrajectoryMongoDao trajectoryMongoDao;
    @Resource
    private TOrderCarlocationMapper tOrderCarlocationMapper;

    @Resource
    private TOrderInfoDetailMapper orderInfoDetailMapper;

    /**
     * 执行未卸货车辆更新轨迹
     * @return
     */
    @Override
    @LcnTransaction(propagation = DTXPropagation.SUPPORTS)
    @Transactional
    public ResultUtil gpsTask() {
        //查询已装货状态北斗状态不等于已经过卸货地
        log.info("NORMALFETCHGPS，获取司机轨迹");
        Map<String,String> orderCodeMap = new HashMap<String,String>();//运单业务id
        Map<String,Integer> vehicleIdMap = new HashMap<String,Integer>();//车辆id
        TOrderInfo record = new TOrderInfo();
        //record.setOrderExecuteStatus(DictEnum.M030.toString());
        record.setVehicleGpsBdStatus(DictEnum.NORMALFETCHGPS.code);
        List<TOrderInfoVO> list = tOrderInfoMapper.selectByExecuteStateCodeNewOrGsp(record);
        String [] vclNs = new String[list.size()];//车牌号
        for(int i=0;i<list.size();i++){
            //根据车牌号存入运单id 和车辆id
            if(list.get(i).getVehicleNumber()!=null&&!"".equals(list.get(i).getVehicleNumber())){
                vclNs[i]=list.get(i).getVehicleNumber()+"_"+list.get(i).getLicensePlateColor();
                orderCodeMap.put(list.get(i).getVehicleNumber(),list.get(i).getCode());//运单业务id
                vehicleIdMap.put(list.get(i).getVehicleNumber(),list.get(i).getVehicleId());//车辆id
            }
        }
        SplitAryUtil splitAryUtil = new SplitAryUtil();
        int splitSize = 200;//分割的块大小
        Object[] subAry = splitAryUtil.splitAry(vclNs, splitSize);
        for(Object vclns:subAry){
            ThreadPoolExecutor executor = new ThreadPoolExecutor(5, 10, 10, TimeUnit.SECONDS,
                    new LinkedBlockingDeque<>(10));
            try {
                executor.execute(() -> {
                    if(list.size()>0){
                        // log.info("获取车辆轨迹开始车牌号："+JSONObject.toJSONString(vclNs));
                        //请求中交兴路接口查询车辆最新位置    vclNs车牌号
                        List<TransTimeManageVResp> transTimeManageVRespList = trajectoryRecentAPI.transTimeManageV3((String[]) vclns,1);
                        //log.info("获取的轨迹-------------"+transTimeManageVRespList);
                        for(TransTimeManageVResp timeManageVResp:transTimeManageVRespList){//多条json字符串
                            log.info("获取单条轨迹-------------"+timeManageVResp);
                            try {
                                //查询运单信息
                                TOrderInfoVO tOrderInfoVO = tOrderInfoMapper.selectByCodeAndIfSingin(orderCodeMap.get(timeManageVResp.getVno()));
                                if("1001".equals(timeManageVResp.getStatus())){
                                    //根据运单表信息拿到线路id查询线路信息
                                    TLineInfo tLineInfo = lineService.selectById(tOrderInfoVO.getLineId());
                                    //log.info("线路参数："+JSONObject.toJSONString(tLineInfo));
                                    String jd = tLineInfo.getEndCoordinates().split(",")[0];//经度
                                    String wd = tLineInfo.getEndCoordinates().split(",")[1];//纬度
                                    Double fenceRecognitionDistance = (Double) tLineInfo.getFenceRecognitionDistance();//电子围栏识别距离
                                    //电子围栏距离为空时 查询系统参数配置表
                                    if(fenceRecognitionDistance==null || "".equals(fenceRecognitionDistance)){
                                        SysParam sysParam = sysParamAPI.getParamByKey("fenceRecognitionDistance");
                                        if(sysParam!=null &&!"".equals(sysParam)){
                                            fenceRecognitionDistance = Double.parseDouble(sysParam.getParamValue());
                                        }
                                    }
                                    //根据两组经纬度计算距离 （km）
                                    Point2D pointDD = null;
                                    Point2D pointXD = null;
                                    if((timeManageVResp.getLon()!=null&& !"".equals(timeManageVResp.getLon())) &&(timeManageVResp.getLat()!=null&& !"".equals(timeManageVResp.getLat()))){
                                        pointDD = new Point2D.Double(Double.parseDouble(jd), Double.parseDouble(wd));

                                        pointXD = new Point2D.Double(Double.parseDouble(String.valueOf(timeManageVResp.getLon()))/600000, Double.parseDouble(String.valueOf(timeManageVResp.getLat()))/600000);
                                    }
                                    Double distance = 0.0d;
                                    //距离
                                    if(pointDD!=null&&pointXD !=null){
                                        distance = ElectronicFence.getDistance(pointDD, pointXD);
                                    }
                                    //车辆轨迹
                                    TrajectoryMongo transTimeManageV = new TrajectoryMongo();

                                    //车辆轨迹中间表
                                    TOrderCarlocation tocl = new TOrderCarlocation();
                                    if (distance < fenceRecognitionDistance) {
                                        TOrderInfo tOrderInfo = new TOrderInfo();
                                        tOrderInfo.setId(tOrderInfoVO.getId());
                                        tOrderInfo.setVehicleGpsBdStatus("YGXHD");
                                        if(tOrderInfoVO.getIfLoadSingin()){
                                            tOrderInfo.setOrderExecuteStatus(DictEnum.M040.code);
                                            //新增状态表当前车辆状态
                                            TOrderState orderState = new TOrderState();
                                            orderState.setCode(IdWorkerUtil.getInstance().nextId());
                                            orderState.setOrderCode(tOrderInfoVO.getCode());
                                            orderState.setOperateTime(new Date());
                                            orderState.setOperateMethod("WX");
                                            orderState.setOperatorId(CurrentUser.getCurrentUserID());
                                            orderState.setOperateGeographyPosition(tOrderInfoVO.getOperateGeographyPosition());
                                            orderState.setStateNodeValue("S0404");//自动定位确认
                                            orderState.setIfExpire(false);
                                            orderState.setCreateUser(CurrentUser.getUserNickname());
                                            orderState.setCreateTime(new Date());
                                            orderState.setEnable(false);
                                            TOrderStateExample example = new TOrderStateExample();
                                            TOrderStateExample.Criteria cr = example.createCriteria();
                                            cr.andOrderCodeEqualTo(tOrderInfoVO.getCode());
                                            List<String> stateNodeValueList = new ArrayList<>();
                                            stateNodeValueList.add("S0402");
                                            stateNodeValueList.add("S0404");
                                            cr.andStateNodeValueIn(stateNodeValueList);
                                            List<TOrderState> tOrderStateList = tOrderStateMapper.selectByExample(example);
                                            if(tOrderStateList.size()<1){
                                                tOrderStateMapper.insertSelective(orderState);
                                            }
                                            //新增状态表当前车辆状态 司机状态
                                            CarDriverRelVO carDriverRelVO = new CarDriverRelVO();
                                            carDriverRelVO.setCode(tOrderInfoVO.getCode());
                                            carDriverRelVO.setBizCode(tOrderInfoVO.getOrderBusinessCode());
                                            carDriverRelVO.setEndDriverId(tOrderInfoVO.getEndDriverId());
                                            carDriverRelVO.setEndcarId(tOrderInfoVO.getVehicleId());
                                            carDriverRelVO.setUserStatus("DISTRIBUTIONCHECKED");
                                            carDriverRelVO.setCarStatus("AVAILABLECHECKED");
                                            carDriverRelVO.setCurrentReceiptsNo(tOrderInfoVO.getOrderBusinessCode());
                                            carDriverRelVO.setCurrentDriver(tOrderInfoVO.getRealName());
                                            carDriverRelVO.setPhone(tOrderInfoVO.getRealPhone());
                                            carDriverRelVO.setCurrentDriverAccountNo(tOrderInfoVO.getCurrentDriverAccountNo());
                                            appCommonAPI.updateCarEnduserStatusGps(carDriverRelVO);

                                            //插入运单轨迹表
                                            transTimeManageV.setVehicleStatus("2");//车辆状态
                                            transTimeManageV.setId(IdWorkerUtil.getInstance().nextId());
                                            transTimeManageV.setTrajectoryCode(IdWorkerUtil.getInstance().nextId());
                                            transTimeManageV.setVehiclePlateNo(timeManageVResp.getVno());
                                            transTimeManageV.setOrderCode(orderCodeMap.get(timeManageVResp.getVno()));
                                            transTimeManageV.setVehicleId(vehicleIdMap.get(timeManageVResp.getVno()));
                                            transTimeManageV.setTrajectoryReceiveTime(DateUtils.parseDateTime(timeManageVResp.getUtc()));
                                            transTimeManageV.setTrajectoryReceiveMethod("DRIVERUNLOADSIGN");//服务端自动抓取
                                            if(timeManageVResp.getLon()!=null && !"".equals(timeManageVResp.getLon())){
                                                transTimeManageV.setLongitude(String.valueOf(Double.parseDouble(timeManageVResp.getLon())/600000));//经度
                                            }
                                            if(timeManageVResp.getLat()!=null && !"".equals(timeManageVResp.getLat())){
                                                transTimeManageV.setLatitudes(String.valueOf(Double.parseDouble(timeManageVResp.getLat())/600000));//纬度
                                            }
                                            transTimeManageV.setVehicleGeographyPosition(timeManageVResp.getAdr());//车辆地理位置信息
                                            transTimeManageV.setVehicleCurrentSpeed(timeManageVResp.getSpd());//车辆当前速度
                                            transTimeManageV.setVehicleCurrentDirection(timeManageVResp.getDrc());//车辆行驶方向
                                            transTimeManageV.setProvince(timeManageVResp.getProvince());//省
                                            transTimeManageV.setCity(timeManageVResp.getCity());//市
                                            transTimeManageV.setCountry(timeManageVResp.getCountry());//区县
                                            transTimeManageV.setCreateTime(new Date());
                                            transTimeManageV.setDataEnable(true);//行数据可用性
                                            transTimeManageV.setEnable(false);
                                            transTimeManageV.setTtmData(timeManageVResp);

                                            trajectoryMongoDao.insert(transTimeManageV);
                                        }
                                        tOrderInfoMapper.updateByPrimaryKeySelective(tOrderInfo);
                                    } else {
                                        transTimeManageV.setVehicleStatus("1");//车辆状态
                                    }
                                    //log.info("获取Redis-------------"+redisUtil.get(orderCodeMap.get(timeManageVResp.getVno()) + "2"));
                                    if (redisUtil.get(orderCodeMap.get(timeManageVResp.getVno()) + "2") != null && !"".equals(redisUtil.get(orderCodeMap.get(timeManageVResp.getVno()) + "2"))) {
                                        Object os = redisUtil.get(orderCodeMap.get(timeManageVResp.getVno()) + "2");//上一次的角度
                                        Object drc = timeManageVResp.getDrc();
                                        // 0 或 360：正北，
                                        // 大于 0 且小于 90：东北，
                                        // 等于 90：正东，
                                        // 大于 90 且小于 180：东南，
                                        // 等于 180：正南，
                                        // 大于 180 且小于 270：西南，
                                        // 等于 270：正西，
                                        // 大于 270 且小于等于 359：西北
                                        boolean flag = true;
                                        if((os !=null && !"".equals(os)) && (drc !=null && !"".equals(drc))){
                                            Double osd = Double.parseDouble(os.toString());
                                            Double drcd = Double.parseDouble(drc.toString());
                                            if((osd == 0 || osd == 360) && (drcd == 0 || drcd == 360)){
                                                flag=false;
                                            } else if((osd > 0 || osd <90) && (drcd > 0 || drcd< 90)){
                                                flag=false;
                                            }else if((osd == 90) && (drcd == 90)){
                                                flag=false;
                                            }else if((osd > 90 || osd <180) && (drcd > 90 || drcd< 180)){
                                                flag=false;
                                            }else if((osd  == 180) && (drcd == 180)){
                                                flag=false;
                                            }else if((osd > 180 || osd <270 ) && (drcd > 180 || drcd< 270)){
                                                flag=false;
                                            }else if((osd == 270) && (drcd == 270)){
                                                flag=false;
                                            }else if((osd > 270 || osd <359) && (drcd > 270 || drcd< 359)){
                                                flag=false;
                                            }
                                        }
                                        if (flag) {//车辆转弯 每次更新轨迹
                                            //插入运单轨迹表
                                            transTimeManageV.setId(IdWorkerUtil.getInstance().nextId());
                                            transTimeManageV.setTrajectoryCode(IdWorkerUtil.getInstance().nextId());
                                            transTimeManageV.setVehicleId(vehicleIdMap.get(timeManageVResp.getVno()));//车辆id
                                            transTimeManageV.setOrderCode(orderCodeMap.get(timeManageVResp.getVno())); //运单业务id
                                            transTimeManageV.setTrajectoryCode(IdWorkerUtil.getInstance().nextId());
                                            transTimeManageV.setVehiclePlateNo(timeManageVResp.getVno());
                                            transTimeManageV.setTrajectoryReceiveTime(DateUtils.parseDateTime(timeManageVResp.getUtc()));
                                            transTimeManageV.setTrajectoryReceiveMethod("SERVERCATCH");//服务端自动抓取
                                            if(timeManageVResp.getLon()!=null && !"".equals(timeManageVResp.getLon())){
                                                transTimeManageV.setLongitude(String.valueOf(Double.parseDouble(timeManageVResp.getLon())/600000));//经度
                                            }
                                            if(timeManageVResp.getLat()!=null && !"".equals(timeManageVResp.getLat())){
                                                transTimeManageV.setLatitudes(String.valueOf(Double.parseDouble(timeManageVResp.getLat())/600000));//纬度
                                            }
                                            transTimeManageV.setVehicleGeographyPosition(timeManageVResp.getAdr());//车辆地理位置信息
                                            transTimeManageV.setVehicleCurrentSpeed(timeManageVResp.getSpd());//车辆当前速度
                                            transTimeManageV.setVehicleCurrentDirection(timeManageVResp.getDrc());//车辆行驶方向
                                            transTimeManageV.setProvince(timeManageVResp.getProvince());//省
                                            transTimeManageV.setCity(timeManageVResp.getCity());//市
                                            transTimeManageV.setCountry(timeManageVResp.getCountry());//区县
                                            transTimeManageV.setCreateTime(new Date());
                                            transTimeManageV.setDataEnable(true);//行数据可用性
                                            transTimeManageV.setEnable(false);
                                            transTimeManageV.setTtmData(timeManageVResp);

                                            //更新车辆轨迹中间表数据
                                            BeanUtils.copyProperties(transTimeManageV,tocl);
                                            if(redisUtil.get(orderCodeMap.get(timeManageVResp.getVno()) + "jwd")!=null && !"".equals(redisUtil.get(orderCodeMap.get(timeManageVResp.getVno()) + "jwd"))){
                                                String jwd = (String) redisUtil.get(orderCodeMap.get(timeManageVResp.getVno()) + "jwd");//经纬度
                                                String[] jwdArray =  jwd.split(",");
                                                if(!timeManageVResp.getLon().equals(jwdArray[0])&&!timeManageVResp.getLat().equals(jwdArray[1])){
                                                    TOrderCarlocationExample tOrderCarlocationExample = new TOrderCarlocationExample();
                                                    TOrderCarlocationExample.Criteria cre = tOrderCarlocationExample.createCriteria();
                                                    cre.andOrderCodeEqualTo(tocl.getOrderCode());
                                                    cre.andEnableEqualTo(false);
                                                    List<TOrderCarlocation> tOrderCarlocationList = tOrderCarlocationMapper.selectByExample(tOrderCarlocationExample);
                                                    if(tOrderCarlocationList.size()<1){
                                                        MQMessage mqMessage = new MQMessage();
                                                        mqMessage.setTopic(MqMessageTopic.ORDERCARLOCATION);
                                                        mqMessage.setTag(MqMessageTag.INSERT);
                                                        mqMessage.setBody(tocl);
                                                        mqMessage.setKey(IdWorkerUtil.getInstance().nextId());
                                                        mqAPI.sendMessage(mqMessage);
                                                    }else{
                                                        if((null!=tocl.getProvince() && !"".equals(tocl.getProvince())) &&
                                                                (null!=tocl.getCity() && !"".equals(tocl.getCity())) &&
                                                                (null!=tocl.getCountry() && !"".equals(tocl.getCountry())) ){
                                                            MQMessage mqMessage = new MQMessage();
                                                            mqMessage.setTopic(MqMessageTopic.ORDERCARLOCATION);
                                                            mqMessage.setTag(MqMessageTag.UPDATE);
                                                            mqMessage.setBody(tocl);
                                                            mqMessage.setKey(IdWorkerUtil.getInstance().nextId());
                                                            mqAPI.sendMessage(mqMessage);
                                                        }
                                                    }
                                                    trajectoryMongoDao.insert(transTimeManageV);
                                                }
                                            }else{
                                                TOrderCarlocationExample tOrderCarlocationExample = new TOrderCarlocationExample();
                                                TOrderCarlocationExample.Criteria cre = tOrderCarlocationExample.createCriteria();
                                                cre.andOrderCodeEqualTo(tocl.getOrderCode());
                                                cre.andEnableEqualTo(false);
                                                List<TOrderCarlocation> tOrderCarlocationList = tOrderCarlocationMapper.selectByExample(tOrderCarlocationExample);
                                                if(tOrderCarlocationList.size()<1){
                                                    MQMessage mqMessage = new MQMessage();
                                                    mqMessage.setTopic(MqMessageTopic.ORDERCARLOCATION);
                                                    mqMessage.setTag(MqMessageTag.INSERT);
                                                    mqMessage.setBody(tocl);
                                                    mqMessage.setKey(IdWorkerUtil.getInstance().nextId());
                                                    mqAPI.sendMessage(mqMessage);
                                                }else{
                                                    if((null!=tocl.getProvince() && !"".equals(tocl.getProvince())) &&
                                                            (null!=tocl.getCity() && !"".equals(tocl.getCity())) &&
                                                            (null!=tocl.getCountry() && !"".equals(tocl.getCountry())) ){
                                                        MQMessage mqMessage = new MQMessage();
                                                        mqMessage.setTopic(MqMessageTopic.ORDERCARLOCATION);
                                                        mqMessage.setTag(MqMessageTag.UPDATE);
                                                        mqMessage.setBody(tocl);
                                                        mqMessage.setKey(IdWorkerUtil.getInstance().nextId());
                                                        mqAPI.sendMessage(mqMessage);
                                                    }
                                                }
                                                trajectoryMongoDao.insert(transTimeManageV);
                                            }
                                            redisUtil.set(orderCodeMap.get(timeManageVResp.getVno()), new Date(),259200);//时间 存放redis 3天
                                        } else {//小于2 车辆执行每20分钟更新轨迹 并且车速小于130km
                                            //log.info("上次获取轨迹时间"+redisUtil.get(orderCodeMap.get(timeManageVResp.getVno())));
                                            Date dateOld = null;
                                            if(redisUtil.get(orderCodeMap.get(timeManageVResp.getVno()))!=null&&!"".equals(redisUtil.get(orderCodeMap.get(timeManageVResp.getVno())))){
                                                String dateStr = DateUtils.formatDateTime((Date)redisUtil.get(orderCodeMap.get(timeManageVResp.getVno())));
                                                dateOld = DateUtils.parseDate(dateStr);
                                            }else{
                                                dateOld = new Date();
                                            }
                                            Calendar cal = Calendar.getInstance();
                                            cal.setTime(dateOld);
                                            SysParam sysParam = sysParamAPI.getParamByKey("GPSDATEMINUTE");
                                            int dateMinute  = 20;
                                            if(sysParam.getParamValue()!=null &&!"".equals(sysParam.getParamValue())){
                                                dateMinute  = Integer.parseInt(sysParam.getParamValue());
                                            }
                                            cal.add(Calendar.MINUTE, dateMinute);
                                            if (cal.getTime().before(new Date()) && Double.parseDouble(timeManageVResp.getSpd()) < 130.0) {
                                                //插入运单轨迹表
                                                transTimeManageV.setId(IdWorkerUtil.getInstance().nextId());
                                                transTimeManageV.setTrajectoryCode(IdWorkerUtil.getInstance().nextId());
                                                transTimeManageV.setVehicleId(vehicleIdMap.get(timeManageVResp.getVno()));//车辆id
                                                transTimeManageV.setOrderCode(orderCodeMap.get(timeManageVResp.getVno())); //运单业务id
                                                transTimeManageV.setTrajectoryCode(IdWorkerUtil.getInstance().nextId());
                                                transTimeManageV.setVehiclePlateNo(timeManageVResp.getVno());
                                                transTimeManageV.setTrajectoryReceiveTime(DateUtils.parseDateTime(timeManageVResp.getUtc()));
                                                transTimeManageV.setTrajectoryReceiveMethod("SERVERCATCH");//服务端自动抓取
                                                if(timeManageVResp.getLon()!=null && !"".equals(timeManageVResp.getLon())){
                                                    transTimeManageV.setLongitude(String.valueOf(Double.parseDouble(timeManageVResp.getLon())/600000));//经度
                                                }
                                                if(timeManageVResp.getLat()!=null && !"".equals(timeManageVResp.getLat())){
                                                    transTimeManageV.setLatitudes(String.valueOf(Double.parseDouble(timeManageVResp.getLat())/600000));//纬度
                                                }
                                                transTimeManageV.setVehicleGeographyPosition(timeManageVResp.getAdr());//车辆地理位置信息
                                                transTimeManageV.setVehicleCurrentSpeed(timeManageVResp.getSpd());//车辆当前速度
                                                transTimeManageV.setVehicleCurrentDirection(timeManageVResp.getDrc());//车辆行驶方向
                                                transTimeManageV.setProvince(timeManageVResp.getProvince());//省
                                                transTimeManageV.setCity(timeManageVResp.getCity());//市
                                                transTimeManageV.setCountry(timeManageVResp.getCountry());//区县
                                                transTimeManageV.setDataEnable(true);//行数据可用性
                                                transTimeManageV.setEnable(false);
                                                transTimeManageV.setTtmData(timeManageVResp);

                                                //更新车辆中间表轨迹
                                                BeanUtils.copyProperties(transTimeManageV,tocl);
                                                if(redisUtil.get(orderCodeMap.get(timeManageVResp.getVno()) + "jwd")!=null && !"".equals(redisUtil.get(orderCodeMap.get(timeManageVResp.getVno()) + "jwd"))){
                                                    String jwd = (String) redisUtil.get(orderCodeMap.get(timeManageVResp.getVno()) + "jwd");//经纬度
                                                    String[] jwdArray =  jwd.split(",");
                                                    if(!timeManageVResp.getLon().equals(jwdArray[0])&&!timeManageVResp.getLat().equals(jwdArray[1])){
                                                        TOrderCarlocationExample tOrderCarlocationExample = new TOrderCarlocationExample();
                                                        TOrderCarlocationExample.Criteria cre = tOrderCarlocationExample.createCriteria();
                                                        cre.andOrderCodeEqualTo(tocl.getOrderCode());
                                                        cre.andEnableEqualTo(false);
                                                        List<TOrderCarlocation> tOrderCarlocationList = tOrderCarlocationMapper.selectByExample(tOrderCarlocationExample);
                                                        if(tOrderCarlocationList.size()<1){
                                                            MQMessage mqMessage = new MQMessage();
                                                            mqMessage.setTopic(MqMessageTopic.ORDERCARLOCATION);
                                                            mqMessage.setTag(MqMessageTag.INSERT);
                                                            mqMessage.setBody(tocl);
                                                            mqMessage.setKey(IdWorkerUtil.getInstance().nextId());
                                                            mqAPI.sendMessage(mqMessage);
                                                        }else{
                                                            if((null!=tocl.getProvince() && !"".equals(tocl.getProvince())) &&
                                                                    (null!=tocl.getCity() && !"".equals(tocl.getCity())) &&
                                                                    (null!=tocl.getCountry() && !"".equals(tocl.getCountry())) ){
                                                                MQMessage mqMessage = new MQMessage();
                                                                mqMessage.setTopic(MqMessageTopic.ORDERCARLOCATION);
                                                                mqMessage.setTag(MqMessageTag.UPDATE);
                                                                mqMessage.setBody(tocl);
                                                                mqMessage.setKey(IdWorkerUtil.getInstance().nextId());
                                                                mqAPI.sendMessage(mqMessage);
                                                            }
                                                        }
                                                        trajectoryMongoDao.insert(transTimeManageV);
                                                    }
                                                }else{
                                                    TOrderCarlocationExample tOrderCarlocationExample = new TOrderCarlocationExample();
                                                    TOrderCarlocationExample.Criteria cre = tOrderCarlocationExample.createCriteria();
                                                    cre.andOrderCodeEqualTo(tocl.getOrderCode());
                                                    cre.andEnableEqualTo(false);
                                                    List<TOrderCarlocation> tOrderCarlocationList = tOrderCarlocationMapper.selectByExample(tOrderCarlocationExample);
                                                    if(tOrderCarlocationList.size()<1){
                                                        MQMessage mqMessage = new MQMessage();
                                                        mqMessage.setTopic(MqMessageTopic.ORDERCARLOCATION);
                                                        mqMessage.setTag(MqMessageTag.INSERT);
                                                        mqMessage.setBody(tocl);
                                                        mqMessage.setKey(IdWorkerUtil.getInstance().nextId());
                                                        mqAPI.sendMessage(mqMessage);
                                                    }else{
                                                        if((null!=tocl.getProvince() && !"".equals(tocl.getProvince())) &&
                                                                (null!=tocl.getCity() && !"".equals(tocl.getCity())) &&
                                                                (null!=tocl.getCountry() && !"".equals(tocl.getCountry())) ){
                                                            MQMessage mqMessage = new MQMessage();
                                                            mqMessage.setTopic(MqMessageTopic.ORDERCARLOCATION);
                                                            mqMessage.setTag(MqMessageTag.UPDATE);
                                                            mqMessage.setBody(tocl);
                                                            mqMessage.setKey(IdWorkerUtil.getInstance().nextId());
                                                            mqAPI.sendMessage(mqMessage);
                                                        }
                                                    }
                                                    trajectoryMongoDao.insert(transTimeManageV);
                                                }
                                                //插入轨迹后记录时间
                                                redisUtil.set(orderCodeMap.get(timeManageVResp.getVno()), new Date(),259200);//时间 存放redis 3天
                                            }
                                        }
                                    } else {
                                        //插入运单轨迹表
                                        transTimeManageV.setId(IdWorkerUtil.getInstance().nextId());
                                        transTimeManageV.setTrajectoryCode(IdWorkerUtil.getInstance().nextId());
                                        transTimeManageV.setVehicleId(vehicleIdMap.get(timeManageVResp.getVno()));//车辆id
                                        transTimeManageV.setOrderCode(orderCodeMap.get(timeManageVResp.getVno())); //运单业务id
                                        transTimeManageV.setTrajectoryCode(IdWorkerUtil.getInstance().nextId());
                                        transTimeManageV.setVehiclePlateNo(timeManageVResp.getVno());
                                        transTimeManageV.setTrajectoryReceiveTime(DateUtils.parseDateTime(timeManageVResp.getUtc()));
                                        transTimeManageV.setTrajectoryReceiveMethod("SERVERCATCH");//服务端自动抓取
                                        transTimeManageV.setLongitude(String.valueOf(Double.parseDouble(timeManageVResp.getLon())/600000));//经度
                                        transTimeManageV.setLatitudes(String.valueOf(Double.parseDouble(timeManageVResp.getLat())/600000));//纬度
                                        transTimeManageV.setVehicleGeographyPosition(timeManageVResp.getAdr());//车辆地理位置信息
                                        transTimeManageV.setVehicleCurrentSpeed(timeManageVResp.getSpd());//车辆当前速度
                                        transTimeManageV.setVehicleCurrentDirection(timeManageVResp.getDrc());//车辆行驶方向
                                        transTimeManageV.setProvince(timeManageVResp.getProvince());//省
                                        transTimeManageV.setCity(timeManageVResp.getCity());//市
                                        transTimeManageV.setCountry(timeManageVResp.getCountry());//区县
                                        transTimeManageV.setDataEnable(true);//行数据可用性
                                        transTimeManageV.setEnable(false);
                                        transTimeManageV.setTtmData(timeManageVResp);

                                        //更新车辆中间表轨迹
                                        BeanUtils.copyProperties(transTimeManageV,tocl);
                                        if(redisUtil.get(orderCodeMap.get(timeManageVResp.getVno()) + "jwd")!=null && !"".equals(redisUtil.get(orderCodeMap.get(timeManageVResp.getVno()) + "jwd"))){
                                            String jwd = (String) redisUtil.get(orderCodeMap.get(timeManageVResp.getVno()) + "jwd");//经纬度
                                            String[] jwdArray =  jwd.split(",");
                                            if(!timeManageVResp.getLon().equals(jwdArray[0]) && !timeManageVResp.getLat().equals(jwdArray[1])){
                                                TOrderCarlocationExample tOrderCarlocationExample = new TOrderCarlocationExample();
                                                TOrderCarlocationExample.Criteria cre = tOrderCarlocationExample.createCriteria();
                                                cre.andOrderCodeEqualTo(tocl.getOrderCode());
                                                cre.andEnableEqualTo(false);
                                                List<TOrderCarlocation> tOrderCarlocationList = tOrderCarlocationMapper.selectByExample(tOrderCarlocationExample);
                                                if(tOrderCarlocationList.size()<1){
                                                    MQMessage mqMessage = new MQMessage();
                                                    mqMessage.setTopic(MqMessageTopic.ORDERCARLOCATION);
                                                    mqMessage.setTag(MqMessageTag.INSERT);
                                                    mqMessage.setBody(tocl);
                                                    mqMessage.setKey(IdWorkerUtil.getInstance().nextId());
                                                    mqAPI.sendMessage(mqMessage);
                                                }else{
                                                    if((null!=tocl.getProvince() && !"".equals(tocl.getProvince())) &&
                                                            (null!=tocl.getCity() && !"".equals(tocl.getCity())) &&
                                                            (null!=tocl.getCountry() && !"".equals(tocl.getCountry())) ){
                                                        MQMessage mqMessage = new MQMessage();
                                                        mqMessage.setTopic(MqMessageTopic.ORDERCARLOCATION);
                                                        mqMessage.setTag(MqMessageTag.UPDATE);
                                                        mqMessage.setBody(tocl);
                                                        mqMessage.setKey(IdWorkerUtil.getInstance().nextId());
                                                        mqAPI.sendMessage(mqMessage);
                                                    }
                                                }
                                                trajectoryMongoDao.insert(transTimeManageV);
                                            }
                                        }else{
                                            TOrderCarlocationExample tOrderCarlocationExample = new TOrderCarlocationExample();
                                            TOrderCarlocationExample.Criteria cre = tOrderCarlocationExample.createCriteria();
                                            cre.andOrderCodeEqualTo(tocl.getOrderCode());
                                            cre.andEnableEqualTo(false);
                                            List<TOrderCarlocation> tOrderCarlocationList = tOrderCarlocationMapper.selectByExample(tOrderCarlocationExample);
                                            if(tOrderCarlocationList.size()<1){
                                                MQMessage mqMessage = new MQMessage();
                                                mqMessage.setTopic(MqMessageTopic.ORDERCARLOCATION);
                                                mqMessage.setTag(MqMessageTag.INSERT);
                                                mqMessage.setBody(tocl);
                                                mqMessage.setKey(IdWorkerUtil.getInstance().nextId());
                                                mqAPI.sendMessage(mqMessage);
                                            }else{
                                                if((null!=tocl.getProvince() && !"".equals(tocl.getProvince())) &&
                                                        (null!=tocl.getCity() && !"".equals(tocl.getCity())) &&
                                                        (null!=tocl.getCountry() && !"".equals(tocl.getCountry())) ){
                                                    MQMessage mqMessage = new MQMessage();
                                                    mqMessage.setTopic(MqMessageTopic.ORDERCARLOCATION);
                                                    mqMessage.setTag(MqMessageTag.UPDATE);
                                                    mqMessage.setBody(tocl);
                                                    mqMessage.setKey(IdWorkerUtil.getInstance().nextId());
                                                    mqAPI.sendMessage(mqMessage);
                                                }
                                            }
                                            trajectoryMongoDao.insert(transTimeManageV);
                                        }
                                        redisUtil.set(orderCodeMap.get(timeManageVResp.getVno()), new Date(),259200);//时间 存放redis 3天
                                    }
                                    redisUtil.set(orderCodeMap.get(timeManageVResp.getVno()) + "2", timeManageVResp.getDrc(),259200);//角度 存放redis 3天
                                    if((null!=timeManageVResp.getLon() && !"".equals(timeManageVResp.getLon()))&&(null!=timeManageVResp.getLat()) && !"".equals(timeManageVResp.getLat())){
                                        String jwd = timeManageVResp.getLon()+","+timeManageVResp.getLat();
                                        redisUtil.set(orderCodeMap.get(timeManageVResp.getVno()) + "jwd",jwd,259200);//经纬度 存放redis 3天
                                    }
                                }else if("1006".equals(timeManageVResp.getStatus())){
                                    log.info("此车无法获取轨迹{}"+timeManageVResp);
                                    TOrderInfoDetail infoDetail = orderInfoDetailMapper.selectByOrderId(tOrderInfoVO.getId());
                                    infoDetail.setCloseGps(true);//表示北斗关闭过
                                    orderInfoDetailMapper.updateByPrimaryKeySelective(infoDetail);
                                }else{
                                    log.info("获取轨迹失败{}"+timeManageVResp);
                                }
                            }catch (RuntimeException e) {
                                log.error("获取车辆轨迹失败！运单业务id ： "+ orderCodeMap.get(timeManageVResp.getVno()),e);
                            }
                        }
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
                log.info("轨迹异常：", e);
            } finally {
                executor.shutdown();
            }
        }

        return new ResultUtil("执行成功！",list);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2020/7/1 11:58
     *  @Description: 获取司机自动抓取轨迹
     */
    @Override
    public ResultUtil gpsFdTask() {
        //查询已装货状态北斗状态不等于已经过卸货地
        log.info("获取司机自动抓取轨迹-----------------");
        Map<String,String> orderCodeMap = new HashMap<String,String>();//运单业务id
        Map<String,Integer> vehicleIdMap = new HashMap<String,Integer>();//车辆id
        TOrderInfo record = new TOrderInfo();
        record.setVehicleGpsBdStatus(DictEnum.AUTOFETCHGPS.code);
        List<TOrderInfoVO> list = tOrderInfoMapper.selectByExecuteStateCodeNew(record);
        String [] vclNs = new String[list.size()];//车牌号
        for(int i=0;i<list.size();i++){
            //根据车牌号存入运单id 和车辆id
            if(list.get(i).getVehicleNumber()!=null&&!"".equals(list.get(i).getVehicleNumber())){
                vclNs[i]=list.get(i).getVehicleNumber()+"_"+list.get(i).getLicensePlateColor();
                orderCodeMap.put(list.get(i).getVehicleNumber(),list.get(i).getCode());//运单业务id
                vehicleIdMap.put(list.get(i).getVehicleNumber(),list.get(i).getVehicleId());//车辆id
            }
        }
        //log.info("查询出的list："+JSONObject.toJSONString(list));
        SplitAryUtil splitAryUtil = new SplitAryUtil();
        int splitSize = 200;//分割的块大小
        Object[] subAry = splitAryUtil.splitAry(vclNs, splitSize);
        for(Object vclns:subAry){
            ThreadPoolExecutor executor = new ThreadPoolExecutor(5, 10, 10, TimeUnit.SECONDS,
                    new LinkedBlockingDeque<>(10));
            try {
                executor.execute(() -> {
                    if(list.size()>0){
                        // log.info("获取车辆轨迹开始车牌号："+JSONObject.toJSONString(vclNs));
                        //请求中交兴路接口查询车辆最新位置    vclNs车牌号
                        List<TransTimeManageVResp> transTimeManageVRespList = trajectoryRecentAPI.transTimeManageV3((String[]) vclns,1);
                        //log.info("获取的轨迹-------------"+resultList);
                        for(TransTimeManageVResp timeManageVResp:transTimeManageVRespList){//多条json字符串
                            log.info("获取单条轨迹{} -------------"+timeManageVResp);
                            if("1001".equals(timeManageVResp.getStatus())){
                                try {
                                    //查询运单信息
                                    TOrderInfoVO tOrderInfoVO = tOrderInfoMapper.selectByCodeAndIfSingin(orderCodeMap.get(timeManageVResp.getVno()));
                                    //根据运单表信息拿到线路id查询线路信息
                                    TLineInfo tLineInfo = lineService.selectById(tOrderInfoVO.getLineId());
                                    String jd = tLineInfo.getFromCoordinates().split(",")[0];//经度
                                    String wd = tLineInfo.getFromCoordinates().split(",")[1];//纬度
                                    Double fencingIdentifiedDistance = (Double) tLineInfo.getFencingIdentifiedDistance();//电子围栏识别距离
                                    //电子围栏距离为空时 查询系统参数配置表
                                    if(fencingIdentifiedDistance==null || "".equals(fencingIdentifiedDistance)){
                                        SysParam sysParam = sysParamAPI.getParamByKey("fencingIdentifiedDistance");
                                        if(sysParam!=null &&!"".equals(sysParam)){
                                            fencingIdentifiedDistance = Double.parseDouble(sysParam.getParamValue());
                                        }
                                    }
                                    //根据两组经纬度计算距离 （km）
                                    Point2D pointDD = null;
                                    Point2D pointXD = null;
                                    if((null!= timeManageVResp.getLon() && !"".equals(timeManageVResp.getLon())) &&(null!= timeManageVResp.getLat()&& !"".equals(timeManageVResp.getLat()))){
                                        pointDD = new Point2D.Double(Double.parseDouble(jd), Double.parseDouble(wd));

                                        pointXD = new Point2D.Double(Double.parseDouble(timeManageVResp.getLon())/600000, Double.parseDouble(timeManageVResp.getLon())/600000);
                                    }
                                    Double distance = 0.0d;
                                    //距离
                                    if(pointDD!=null&&pointXD !=null){
                                        distance = ElectronicFence.getDistance(pointDD, pointXD);
                                    }
                                    //车辆轨迹
                                    TrajectoryMongo transTimeManageV = new TrajectoryMongo();


                                    if (distance < fencingIdentifiedDistance) {
                                        transTimeManageV.setVehicleStatus("2");//车辆状态
                                        TOrderInfo tOrderInfo = new TOrderInfo();
                                        tOrderInfo.setId(tOrderInfoVO.getId());
                                        tOrderInfo.setVehicleGpsBdStatus(DictEnum.NORMALFETCHGPS.code);
                                        if(tOrderInfoVO.getIfLoadSingin()){
                                            tOrderInfo.setOrderExecuteStatus(DictEnum.M030.code);
                                            transTimeManageV.setTrajectoryReceiveMethod("DRIVERLOADSIGN");//服务端自动抓取
                                            //新增状态表当前车辆状态
                                            TOrderState orderState = new TOrderState();
                                            orderState.setCode(IdWorkerUtil.getInstance().nextId());
                                            orderState.setOrderCode(tOrderInfoVO.getCode());
                                            orderState.setOperateTime(new Date());
                                            orderState.setOperateMethod("WX");
                                            orderState.setOperatorId(CurrentUser.getCurrentUserID());
                                            orderState.setOperateGeographyPosition(tOrderInfoVO.getOperateGeographyPosition());
                                            orderState.setStateNodeValue("S0305");//自动定位确认装货
                                            orderState.setIfExpire(false);
                                            orderState.setCreateUser(CurrentUser.getUserNickname());
                                            orderState.setCreateTime(new Date());
                                            orderState.setEnable(false);
                                            TOrderStateExample example = new TOrderStateExample();
                                            TOrderStateExample.Criteria cr = example.createCriteria();
                                            cr.andOrderCodeEqualTo(tOrderInfoVO.getCode());
                                            List<String> stateNodeList = new ArrayList<>();
                                            stateNodeList.add("S0302");
                                            stateNodeList.add("S0305");
                                            cr.andStateNodeValueIn(stateNodeList);
                                            List<TOrderState> tOrderStateList = tOrderStateMapper.selectByExample(example);
                                            if(tOrderStateList.size()<1){
                                                tOrderStateMapper.insertSelective(orderState);
                                            }
                                            //新增状态表当前车辆状态 司机状态
                                            CarDriverRelVO carDriverRelVO = new CarDriverRelVO();
                                            carDriverRelVO.setCode(tOrderInfoVO.getCode());
                                            carDriverRelVO.setBizCode(tOrderInfoVO.getOrderBusinessCode());
                                            carDriverRelVO.setEndDriverId(tOrderInfoVO.getEndDriverId());
                                            carDriverRelVO.setEndcarId(tOrderInfoVO.getVehicleId());
                                            carDriverRelVO.setUserStatus("NOTCHECKED");
                                            carDriverRelVO.setCarStatus("ONROADCHECKED");
                                            carDriverRelVO.setCurrentReceiptsNo(tOrderInfoVO.getOrderBusinessCode());
                                            carDriverRelVO.setCurrentDriver(tOrderInfoVO.getRealName());
                                            carDriverRelVO.setPhone(tOrderInfoVO.getRealPhone());
                                            carDriverRelVO.setCurrentDriverAccountNo(tOrderInfoVO.getCurrentDriverAccountNo());
                                            appCommonAPI.updateCarEnduserStatusGps(carDriverRelVO);
                                        }else{
                                            transTimeManageV.setTrajectoryReceiveMethod("SERVERCATCH");//服务端自动抓取
                                        }
                                        tOrderInfoMapper.updateByPrimaryKeySelective(tOrderInfo);
                                        //想易煤网发送信息
                                        tOrderInfoService.sendOrderInfoToYimei(tOrderInfo.getOrderBusinessCode());
                                        //etc调用任务
                                        tOrderInfoService.sendEtcData(tOrderInfoVO.getOrderBusinessCode());
                                        /*if (digitlFlowUtil.selectOrderStatus(tOrderInfoVO.getCode(), DigitlFlowEnum.CreateFlowOrder.code)) {
                                            if (!digitlFlowUtil.selectOrderStatus(tOrderInfoVO.getCode(), DigitlFlowEnum.ClosedOrder.code)) {
                                                digitlFlowUtil.createOrderStart(tOrderInfoVO.getCode(),String.valueOf(jo.get("vno")), new Date(), tOrderInfoVO.getCarrierId(), tOrderInfoVO.getEndDriverId(),"");
                                            }
                                        }*/
                                        //插入运单轨迹表
                                        transTimeManageV.setId(IdWorkerUtil.getInstance().nextId());
                                        transTimeManageV.setTrajectoryCode(IdWorkerUtil.getInstance().nextId());
                                        transTimeManageV.setVehicleId(vehicleIdMap.get(timeManageVResp.getVno()));//车辆id
                                        transTimeManageV.setOrderCode(orderCodeMap.get(timeManageVResp.getVno())); //运单业务id
                                        transTimeManageV.setTrajectoryCode(IdWorkerUtil.getInstance().nextId());
                                        transTimeManageV.setVehiclePlateNo(timeManageVResp.getVno());
                                        transTimeManageV.setTrajectoryReceiveTime(DateUtils.parseDateTime(timeManageVResp.getUtc()));

                                        if(null!=timeManageVResp.getLon() && !"".equals(timeManageVResp.getLon())){
                                            transTimeManageV.setLongitude(String.valueOf(Double.parseDouble(timeManageVResp.getLon())/600000));//经度
                                        }
                                        if(null!= timeManageVResp.getLat() && !"".equals(timeManageVResp.getLat())){
                                            transTimeManageV.setLatitudes(String.valueOf(Double.parseDouble(timeManageVResp.getLat())/600000));//纬度
                                        }
                                        transTimeManageV.setVehicleGeographyPosition(timeManageVResp.getAdr());//车辆地理位置信息
                                        transTimeManageV.setVehicleCurrentSpeed(timeManageVResp.getSpd());//车辆当前速度
                                        transTimeManageV.setVehicleCurrentDirection(timeManageVResp.getDrc());//车辆行驶方向
                                        transTimeManageV.setProvince(timeManageVResp.getProvince());//省
                                        transTimeManageV.setCity(timeManageVResp.getCity());//市
                                        transTimeManageV.setCountry(timeManageVResp.getCountry());//区县
                                        transTimeManageV.setDataEnable(true);//行数据可用性
                                        transTimeManageV.setEnable(false);
                                        transTimeManageV.setTtmData(timeManageVResp);
                                        trajectoryMongoDao.insert(transTimeManageV);
                                    }else if("1006".equals(timeManageVResp.getStatus())){
                                        log.info("此车无法获取轨迹{}："+timeManageVResp);
                                    }else{
                                        log.info("获取轨迹失败{}："+timeManageVResp);
                                    }
                                }catch (RuntimeException e) {
                                    log.error("获取车辆轨迹失败！运单业务id ： "+ orderCodeMap.get(timeManageVResp.getVno()),e);
                                }
                            }
                        }
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
                log.info("轨迹异常：", e);
            } finally {
                executor.shutdown();
            }
        }
        return new ResultUtil("执行成功！",list);
    }
}
