package com.lz.service.impl.hxyh;

import com.lz.dao.THXOrderPayDetailMapper;
import com.lz.dao.TJDOrderPayDetailMapper;
import com.lz.dao.TOrderPayDetailMapper;
import com.lz.dto.THxOrderPayInfoDTO;
import com.lz.dto.TJDOrderPayInfoDTO;
import com.lz.dto.TOrderPayInfoDTO;
import com.lz.model.TOrderPayDetail;
import com.lz.service.TJDOrderPayDetailService;
import com.lz.service.TOrderPayDetailService;
import com.lz.service.hxyh.THXOrderPayDetailService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class THXOrderPayDetailServiceImpl implements THXOrderPayDetailService {

    @Resource
    private THXOrderPayDetailMapper hxOrderPayDetailMapper;

    @Override
    public TOrderPayDetail selectByCode(String code) {
        return hxOrderPayDetailMapper.selectByCode(code);
    }

    @Override
    public THxOrderPayInfoDTO selectByOrderPayDetailKey(String code) {
        return hxOrderPayDetailMapper.selectByOrderPayDetailKey(code);
    }

}
