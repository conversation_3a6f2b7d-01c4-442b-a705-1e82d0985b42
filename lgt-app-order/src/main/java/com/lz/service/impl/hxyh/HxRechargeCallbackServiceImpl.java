package com.lz.service.impl.hxyh;

import cn.hutool.json.JSONUtil;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.JDTradeTypeEnum;
import com.lz.common.dbenum.JdEnum;
import com.lz.common.model.hxPayment.response.CustomerBalancePayRes;
import com.lz.common.util.ResultUtil;
import com.lz.dao.THXOrderPayDetailMapper;
import com.lz.dto.THxOrderPayInfoDTO;
import com.lz.model.TOrderPayDetail;
import com.lz.model.TZtWallet;
import com.lz.payment.hxyh.HXPaymentUtil;
import com.lz.payment.hxyh.HXWalletUtil;
import com.lz.service.hxyh.HxRechargeCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdk.model.message.AsyncNotifyVirtualBalancePayMessageBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Service
public class HxRechargeCallbackServiceImpl implements HxRechargeCallbackService {

    @Resource
    private THXOrderPayDetailMapper orderPayDetailMapper;

    @Autowired
    private HXWalletUtil walletUtil;

    @Autowired
    private HXPaymentUtil paymentUtil;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil payFail(CustomerBalancePayRes response) {
        try {
            // 修改支付子表状态
            TOrderPayDetail detail = new TOrderPayDetail();
            detail.setCode(response.getBizOrderNo());
            detail.setReturnTime(new Date());
            detail.setTradeStatus(DictEnum.TRADE_FAILED.code);
            detail.setErrorMsg(response.getResponseDesc());
            detail.setErrorCode(response.getResponseCode());
            orderPayDetailMapper.updateByCode(detail);
            // 修改支付主表
            THxOrderPayInfoDTO orderPayInfoDTO = orderPayDetailMapper.selectByOrderPayDetailKey(response.getBizOrderNo());
            if (null != orderPayInfoDTO.getOrderPayId()) {
                paymentUtil.updateOrderPayInfo(orderPayInfoDTO.getOrderPayId(), DictEnum.M080.code, new Date());
            }
            // 修改支付主表
            if (null != orderPayInfoDTO.getOrderId()) {
                paymentUtil.updateOrderInfo(orderPayInfoDTO.getOrderId(), DictEnum.O060.code, DictEnum.M080.code, new Date());
                paymentUtil.insertOrderState(orderPayInfoDTO.getOrderCode(), DictEnum.S0800.code, new Date());
            }
            // 修改钱包
            TZtWallet wallet = walletUtil.selectByPartnerAccId(response.getOutPartnerAccId());
            if (null == wallet) {
                wallet = walletUtil.selectByPartnerAccId(response.getOutPartnerAccId());
                if (null == wallet) {
                    wallet = walletUtil.selectByPartnerAccId(response.getOutPartnerAccId());
                }
            }
            log.info("支付失败，修改出款方钱包, {}", JSONUtil.toJsonStr(wallet));
            BigDecimal entryAmount = wallet.getEntryAmount().subtract(response.getOrderAmount());
            BigDecimal accountBalance = wallet.getAccountBalance().add(response.getOrderAmount());
            wallet.setEntryAmount(entryAmount);
            wallet.setAccountBalance(accountBalance);
            wallet.setUpdateTime(new Date());
            walletUtil.updateByPrimaryKey(wallet);

            // 如果是账户服务费支付申请
           /* if (JdTradeType.ACCOUNTSERVICERE.code.equals(detail.getTradeType())) {
                // 修改记录状态为失败
                // 查询补交账户服务费支付请求消息, 获取账户服务费收取失败记录
                List<TMqMessage> mqMessages = orderMqMessageMapper.selectByMessageKey(response.getBizOrderNo(), MqMessageTopic.ACCOUNTSERVICEFEE, MqMessageTag.ACCOUNTSERVICEFEERETROACTIVE);

                if (null != mqMessages && !mqMessages.isEmpty()) {
                    TMqMessage tMqMessage = mqMessages.get(mqMessages.size() - 1);
                    if (null != tMqMessage.getBody() && StringUtils.isNotBlank(tMqMessage.getBody())) {
                        AccountServiceFailedRecordVO accountServiceFailedRecordVO = JSONUtil.toBean(tMqMessage.getBody(), AccountServiceFailedRecordVO.class);
                        List<Integer> recordIds = accountServiceFailedRecordVO.getRecordIds();
                        orderServicefeeRecordMapper.batchUpdateRecordStatus(recordIds, 0);
                    }
                }
            }*/
        } catch (Exception e) {
            log.error("支付申请失败，处理失败, {}", JSONUtil.toJsonStr(e));
        }

        return ResultUtil.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil carrierBalanceCompanyChargeCallback(AsyncNotifyVirtualBalancePayMessageBody messageBody) {
        Date returnTime = new Date();
        THxOrderPayInfoDTO dto = orderPayDetailMapper.selectByOrderPayDetailKey(messageBody.getBizOrderNo());
        log.info("华夏支付 - 承运方余额支付(企业充值)回调，支付字子表dto信息, {}", JSONUtil.toJsonStr(dto));
        if (JdEnum.PAY_SUCC.code.equals(messageBody.getOrderStatus())) {
            paymentUtil.updateOrderPayInfo(dto.getOrderPayId(), DictEnum.M090.code, returnTime);
            // 修改支付子表，添加回调结果、时间
            paymentUtil.updateOrderPayDetail(dto.getOrderPayDetailId(), DictEnum.TRADE_FINISHED.code, returnTime);
            // 修改钱包
            walletUtil.carrierBalanceCompanyChargeCallbackModifyWallet(messageBody, dto, true);
            // 记录钱包流水
            Integer walletLogId = paymentUtil.insertCarrierBalanceCompanyChargeWalletLog(messageBody, dto);
            // 电子回单
            paymentUtil.saveReceipt(messageBody.getOutPartnerAccId(), messageBody.getBizOrderNo(), JDTradeTypeEnum.SALE.code, walletLogId);
        } else {
            // 支付失败
            paymentUtil.updateOrderPayInfo(dto.getOrderPayId(), DictEnum.M080.code, returnTime);
            paymentUtil.updateFailOrderPayDetail(dto.getOrderPayDetailId(), messageBody.getResponseDesc(), messageBody.getResponseCode(), returnTime);
            // 恢复钱包
            walletUtil.carrierBalanceCompanyChargeCallbackModifyWallet(messageBody, dto, false);
        }
        return ResultUtil.ok();
    }

}
