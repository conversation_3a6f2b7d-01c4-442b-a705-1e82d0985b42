package com.lz.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.codingapi.txlcn.tc.annotation.DTXPropagation;
import com.codingapi.txlcn.tc.annotation.TxcTransaction;
import com.lowagie.text.pdf.PdfReader;
import com.lz.api.*;
import com.lz.common.dbenum.BankNameEnum;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.LicensePlateColorEnum;
import com.lz.common.dbenum.NetSignEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.util.DateUtils;
import com.lz.common.util.OrderMoneyUtil;
import com.lz.common.util.ResultUtil;
import com.lz.dao.*;
import com.lz.dto.JudgeEndUserDTO;
import com.lz.dto.OpenRoleStatusListDTO;
import com.lz.example.TOrderContractExample;
import com.lz.example.TOrderInfoExample;
import com.lz.model.*;
import com.lz.model.contract.req.ContractApplyCertReq;
import com.lz.model.contract.resp.ContractApplyCertResp;
import com.lz.model.contract.resp.ContractDownloadContResp;
import com.lz.model.contract.resp.ContractResp;
import com.lz.model.ht5Gq.contract.CyhtContract;
import com.lz.model.ht5Gq.contract.SkpzContract;
import com.lz.schedule.model.TTask;
import com.lz.schedule.model.TTaskHistory;
import com.lz.service.TOrderContractService;
import com.lz.system.api.DicCatItemAPI;
import com.lz.system.dto.DicItemDTO;
import com.lz.util.ThrowableUtil;
import com.lz.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * author dingweibo
 * 根据运单编号查询运单合同
 */
@Slf4j
@Service("tOrderContractService")
@RefreshScope
public class TOrderContractServiceImpl implements TOrderContractService {
    @Resource
    private TOrderContractMapper tOrderContractMapper;

    @Resource
    private TOrderInfoMapper tOrderInfoMapper;

    @Resource
    private TOrderStateMapper orderStateMapper;

    @Autowired
    private TEndSUserInfoAPI tEndSUserInfoAPI;

    @Autowired
    private CarrierService carrierService;

    @Resource
    private TOrderTaskMapper orderTaskMapper;

    @Resource
    private TOrderInfoDetailMapper orderInfoDetailMapper;

    @Resource
    private TOrderInsuranceMapper orderInsuranceMapper;
    /**
     * 易云章接口
     */
    @Resource
    private ContractAPI contractAPI;

    @Autowired
    private OssAPI ossAPI;

    @Autowired
    private TEndSUserInfoAPI endSUserInfoAPI;

    @Autowired
    private TCarrierEnduserCompanyRelAPI tCarrierEnduserCompanyRelAPI;

    @Autowired
    private JDOpenRoleCommonAPI openRoleCommonAPI;

    @Autowired
    private HXOpenRoleCommonAPI hxOpenRoleCommonAPI;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private TEndCarInfoAPI tEndCarInfoAPI;

    @Autowired
    private DicCatItemAPI dicCatItemAPI;

    @Value("${htPath}")
    private String htPath;

    /**
     * 根据运单编号查询运单合同
     * @param code
     * @return
     */
    @Override
    public TOrderContract selectByOrderBusinessCode(String code) {
        TOrderContractExample example = new TOrderContractExample();
        TOrderContractExample.Criteria  c = example.createCriteria();
        c.andOrderCodeEqualTo(code);
        c.andEnableEqualTo(false);
        List<TOrderContract> list = tOrderContractMapper.selectByExample(example);
        TOrderContract tOrderContract = null;
        if (list.size()>0){
             tOrderContract = list.get(0);
        }
        return tOrderContract;
    }

    /**
     * 根据运单编号查询运单合同
     * @param code
     * @return
     */
    @Override
    public TOrderContract selectByOrderCodeAnrType(String code,String contractType) {
        TOrderContractExample example = new TOrderContractExample();
        TOrderContractExample.Criteria  c = example.createCriteria();
        c.andOrderCodeEqualTo(code);
        c.andEnableEqualTo(false);
        if(contractType!=null &&!"".equals(contractType)){
            c.andContractTypeEqualTo(contractType);
        }
        List<TOrderContract> list = tOrderContractMapper.selectByExample(example);
        TOrderContract tOrderContract = null;
        if (list.size()>0){
            tOrderContract = list.get(0);
        }
        return tOrderContract;
    }
    /**
     * 根据运单编号查询运单合同
     * @param code
     * @return
     */
    @Override
    public TOrderContract selectByOrderCodeAnrTypeAnrEnable(String code,String contractType,boolean enablen) {
        TOrderContractExample example = new TOrderContractExample();
        TOrderContractExample.Criteria  c = example.createCriteria();
        example.setOrderByClause("create_time desc");
        c.andOrderCodeEqualTo(code);
        c.andEnableEqualTo(enablen);
        if(contractType!=null &&!"".equals(contractType)){
            c.andContractTypeEqualTo(contractType);
        }
        List<TOrderContract> list = tOrderContractMapper.selectByExample(example);
        TOrderContract tOrderContract = null;
        if (list.size()>0){
            tOrderContract = list.get(0);
        }
        return tOrderContract;
    }
    @TxcTransaction(propagation = DTXPropagation.SUPPORTS)
    @Override
    public int updateHt(TOrderContract record) {
        if (DictEnum.SKPZ.code.equals(record.getContractForm())) {
            return tOrderContractMapper.updateByPrimaryKeySelectiveWhereEnable(record);
        }
        return tOrderContractMapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateByOrderCodeModel(TOrderContract record) {
        return tOrderContractMapper.updateByOrderCodeModel(record);
    }

    @TxcTransaction(propagation = DTXPropagation.SUPPORTS)
    @Override
    public int add(TOrderContract record) {
        if (DictEnum.SKPZ.code.equals(record.getContractForm())) {
            // 如果是收款凭证，将之前的收款凭证合同删除
            TOrderContract orderContract = new TOrderContract();
            orderContract.setOrderCode(record.getOrderCode());
            orderContract.setContractForm(DictEnum.SKPZ.code);
            orderContract.setEnable(true);
            tOrderContractMapper.updateByOrderCodeModel(orderContract);
        }
        return tOrderContractMapper.insertSelective(record);
    }

    /**
     * 生成收款凭证下载
     * @param req
     * @return
     */
    @Override
    public ResultUtil createTxxy(VoucherReq req) {
        try {
            log.info("生成收款凭证入参："+JSONObject.toJSONString(req));
            TOrderContract tOrderContract = new TOrderContract();
            tOrderContract.setContractCode(IdWorkerUtil.getInstance().nextId());
            tOrderContract.setCreateTime(new Date());//生成时间
            ResultUtil resultUtil = contractAPI.createVoucher(req);
            //易云章返回合同编号
            String contractNum = JSONObject.parseObject(JSONObject.parseObject(resultUtil.getData().toString()).get("data").toString()).get("contractNum").toString();

            //企业签署合同
            Map<Object, Object> mapo = new HashMap<>();
            mapo.put("type", "2");//类型 1：个人 、2：企业
            mapo.put("cardType", "8");// 证件类型 0：身份证 1：军官证 ,2：护照、 3：驾驶证、4：工商登记证、5：税务登记证、6：组织机构代码、7：其他证件，8：统一社会信用代码
            mapo.put("idCardNum", req.getBusinessLicenseNo());// 证件号码
            mapo.put("name", req.getQName());//企业或者个人真实名称
            mapo.put("mobilePhone", req.getQphone());//企业联系手机号

            String phone = req.getMobilePhone();
            Integer endUserId = req.getEndUserId();
            // 判断用户是否已申请合同电子签名，没有就会去申请
            JudgeEndUserDTO judgeEndUserDTO = endSUserInfoAPI.judgeEndUserIfOnlineSign(phone,endUserId);
            boolean onlineSign = false;
            if(judgeEndUserDTO!=null&&!"".equals(judgeEndUserDTO)){
                onlineSign = judgeEndUserDTO.isIfOnlineSign();
            }
            if (!onlineSign) {
                ContractApplyCertReq data = new ContractApplyCertReq();
                //类型 1：个人 、2：企业
                data.setType("1");
                // 证件类型 0：身份证 1：军官证 ,2：护照、 3：驾驶证、4：工商登记证、5：税务登记证、6：组织机构代码、7：其他证件，8：统一社会信用代码
                data.setCardType("0");
                // 证件号码
                data.setIdCardNum(judgeEndUserDTO.getIdcard());
                //企业或者个人真实名称
                data.setName(judgeEndUserDTO.getRealName());
                //企业或者个人联系手机号
                data.setMobilePhone(req.getMobilePhone());
                ExecutorService executorService = Executors.newSingleThreadExecutor();
                Future<ContractApplyCertResp> future = executorService.submit(() -> {
                    ContractApplyCertResp cert = contractAPI.applyCert(data);
                    return cert;
                });
                TEndUserInfo tEndUserInfo = new TEndUserInfo();
                try {
                    ContractApplyCertResp cert = future.get();
                    if (cert.getCode().equals("0")) {
                        tEndUserInfo.setIssuer(cert.getIssuer());
                        tEndUserInfo.setSerialNumber(cert.getSerialNumber());
                        tEndUserInfo.setBeginTime(DateUtils.parseDate(cert.getCertNotBefore()));
                        tEndUserInfo.setEndTime(DateUtils.parseDate(cert.getCertNotAfter()));
                    }
                }catch (InterruptedException e) {
                    e.printStackTrace();
                } catch (ExecutionException e) {
                    e.printStackTrace();
                }finally {
                    executorService.shutdown();
                }
                tEndUserInfo.setId(judgeEndUserDTO.getId());
                endSUserInfoAPI.updateEndUserIfOnlineSign(tEndUserInfo);
            }

            tOrderContract.setFirstPartSignTime(new Date());
            //个人调用易云章 根据签署合同
            Map<Object, Object> mapp = new HashMap<>();
            mapp.put("type", "1");//类型 1：个人 、2：企业
            mapp.put("cardType", "0");// 证件类型 0：身份证 1：军官证 ,2：护照、 3：驾驶证、4：工商登记证、5：税务登记证、6：组织机构代码、7：其他证件，8：统一社会信用代码
            mapp.put("idCardNum", req.getIdCardNum());// 证件号码
            mapp.put("name", req.getDriverName());//企业或者个人真实名称
            mapp.put("mobilePhone", req.getMobilePhone());//企业或者个人联系手机号

            tOrderContract.setSecondPartSignTime(new Date());
            tOrderContract.setSignFinishTime(new Date());
            log.info("签署合同开始！");
            //组织参数
            ContractApplyCertResp ru22 = contractAPI.autoSign(mapo, contractNum, 1);
            ContractApplyCertResp ru = contractAPI.autoSign(mapp, contractNum, 0);
            //成功
            if ("0".equals(ru.getCode()) && "0".equals(ru22.getCode())) {
                log.info("签署合同成功！");
                //合同下载
                tOrderContract.setContractDownloadTime(new Date());//下载时间
                ContractDownloadContResp r = contractAPI.downloadCont(contractNum, req.getMobilePhone());
                log.info("收款凭证下载成功！");
                InputStream inputStream = new ByteArrayInputStream(r.getBytes());

                BufferedOutputStream bos = null;
                FileOutputStream fos = null;
                File file = null;
                log.info("写入pdf文件开始！" + htPath + contractNum + ".pdf");
                try {
                    File dir = new File(htPath + contractNum + ".pdf");
                    if (!dir.exists() && dir.isDirectory()) {//判断文件目录是否存在
                        dir.mkdirs();
                    }
                    fos = new FileOutputStream(dir);
                    bos = new BufferedOutputStream(fos);
                    bos.write(r.getBytes());
                    log.info("写入pdf文件成功！" + htPath + contractNum + ".pdf");
                } catch (Exception e) {
                    log.info("生成pdf失败！", e);
                    throw new RuntimeException("签署合同失败！");
                } finally {
                    if (bos != null) {
                        try {
                            bos.close();
                        } catch (IOException e1) {
                            e1.printStackTrace();
                            throw new RuntimeException("签署合同失败！");
                        }
                    }
                    if (fos != null) {
                        try {
                            fos.close();
                        } catch (IOException e1) {
                            throw new RuntimeException("签署合同失败！");
                        }
                    }
                }
                PDDocument pdDocument = null;
                try {
                    log.info("写入图片文件开始！" + htPath + contractNum + ".pdf");
                    File file2 = new File(htPath + contractNum + ".pdf");
                    pdDocument = PDDocument.load(file2);
                    PDFRenderer renderer = new PDFRenderer(pdDocument);
                    /* dpi越大转换后越清晰，相对转换速度越慢 */
                    PdfReader reader = new PdfReader(htPath + contractNum + ".pdf");
                    int pages = reader.getNumberOfPages();
                    for (int i = 0; i < 1; i++) {
                        String pngFilePath = htPath + contractNum + ".png";
                        BufferedImage image = renderer.renderImageWithDPI(i, 300);
                        ImageIO.write(image, "png", new File(pngFilePath));
                    }
                    log.info("写入图片文件成功！");
                } catch (IOException e) {
                    log.error("写入图片文件失败！",e);
                    throw new RuntimeException("签署合同失败！");
                }finally {
                    pdDocument.close();
                }
                MultipartFile multipartFile = null;
                MultipartFile multipartFileImg = null;
                try {
                    multipartFile = new MockMultipartFile("file", contractNum + ".pdf", "pdf", inputStream);
                } catch (IOException e) {
                    e.printStackTrace();
                    throw new RuntimeException("签署合同失败！");
                }
                //生成图片
                try {
                    File file3 = new File(htPath + contractNum + ".png");
                    InputStream In = new FileInputStream(file3);
                    multipartFileImg = new MockMultipartFile("file", contractNum + ".png", "png", In);
                } catch (IOException e) {
                    log.info("生成图片失败！", e);
                    throw new RuntimeException("签署合同失败！");
                }
                //文件上传
                //ResultUtil fastResult = fastdfsAPI.uploadFileSample(multipartFile);
                ResultUtil fastResult = ossAPI.uploadFileSample(multipartFile);
                //图片上传
                //ResultUtil fastResult2 = fastdfsAPI.uploadFileSample(multipartFileImg);
                ResultUtil fastResult2 = ossAPI.uploadFileSample(multipartFileImg);

                //合同下载路径
                String contractFilePath = fastResult.getData().toString();
                //合同下载路径
                String contractFilePath2 = fastResult2.getData().toString();
                tOrderContract.setContractType(req.getContractType());//提现协议
                tOrderContract.setContractForm("SKPZ");
                tOrderContract.setFirstPart(req.getPayUser());
                tOrderContract.setFirstPartPlatformId(req.getCarrierId());
                tOrderContract.setSecondPart(req.getRecUser());
                tOrderContract.setSecondPartPlatformId(req.getEndUserId());
                tOrderContract.setOrderCode(req.getOrderCode());
                tOrderContract.setContractFilePath(contractFilePath);
                tOrderContract.setParam1(contractNum + ".pdf");
                tOrderContract.setParam2(contractFilePath2);//合同图片路径
                tOrderContract.setParam3(contractNum + ".jpg");
                tOrderContractMapper.insertSelective(tOrderContract);
            } else {
                log.info("签署合同失败！");
                log.error("司机签署失败原因：" + ru.getMessage() + "|| 企业签署失败原因：" + ru22.getMessage());
                return ResultUtil.error("司机签署失败原因：" + ru.getMessage() + "|| 企业签署失败原因：" + ru22.getMessage());
            }
        }catch (Exception e){
            log.error("生成凭证失败,", e);
            throw new RuntimeException(e.getMessage());
        }
        return ResultUtil.ok();
    }

    @Override
    public List<TOrderContract> selectByCode(String code) {
        List<TOrderContract> tOrderContracts = tOrderContractMapper.pcSelectOrderTXContract(code);
        return tOrderContracts;
    }

    //合同作废 （目前不用换为task cancelContractTask）
    @Override
    public ResultUtil cancelContract(String orderCode, String voucherType) {
        try{
            TOrderInfoVO tOrderInfoVO = tOrderInfoMapper.selectByOrdeCode(orderCode);
            TOrderContractExample example = new TOrderContractExample();
            TOrderContractExample.Criteria  c = example.createCriteria();
            c.andOrderCodeEqualTo(orderCode);
            c.andEnableEqualTo(false);
            c.andContractTypeEqualTo(voucherType);
            List<TOrderContract> list = tOrderContractMapper.selectByExample(example);
            TOrderContract tOrderContract = null;
            if (list.size()>0){
                tOrderContract = list.get(0);
                tOrderContract.setEnable(true);
                //调用易云章
                ContractResp resp = contractAPI.cancelContract(tOrderContract.getContractCode(),tOrderInfoVO.getRealPhone());
                if("0".equals(resp.getCode())){
                    tOrderContractMapper.updateByPrimaryKeySelective(tOrderContract);
                    return ResultUtil.ok();
                }else{
                    return ResultUtil.error(resp.getMessage());
                }
            }else{
                return ResultUtil.error("此运单无收款凭证");
            }
        }catch (Exception e){
            log.error("作废收款凭证失败",e);
            return  ResultUtil.error("作废收款凭证失败");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2020/7/16 9:47
     *  @Description: 合同作废
     */
    @Override
    public ResultUtil cancelContractTask(String orderCode, Integer enduserId, String voucherType){
        try{
            TEndUserInfo tEndUserInfo = endSUserInfoAPI.selectByPrimaryKey(enduserId);
            Map<String, Object> map = new HashMap<>();
            map.put("cancel", 1);
            map.put("realPhone", tEndUserInfo.getPhone());
            map.put("orderCode", orderCode);
            String requestParameter = JSONObject.toJSONString(map);
            TTask tTask = new TTask();
            tTask.setRequestParameter(requestParameter);
            tTask.setTaskId(IdWorkerUtil.getInstance().nextId());
            tTask.setTaskType(DictEnum.SKPZ.code);//提现协议
            tTask.setBusinessType("TX");//提现
            tTask.setTaskTypeNode(voucherType);
            tTask.setSourceTablename("T_ORDER_INFO");
            tTask.setSourceFieldvalue(orderCode);
            tTask.setSourceFieldname("code");
            tTask.setRequestTimes(0);
            tTask.setCreateTime(new Date());
            tTask.setRequestDate(new Date());
            tTask.setIsSuccessed(false);
            tTask.setEnable(false);
            orderTaskMapper.insert(tTask);
            return ResultUtil.ok();
        }catch (Exception e){
            log.error("作废收款凭证失败",e);
            return  ResultUtil.error("作废收款凭证失败");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2020/3/12 18:43
     *  @Description: 创建收款凭证  voucherType driver司机  agent 经纪人
     */
    @Override
    public ResultUtil createVoucher(String orderCode, String voucherType) {
        try {
            TOrderContractExample example = new TOrderContractExample();
            TOrderContractExample.Criteria  c = example.createCriteria();
            example.setOrderByClause("create_time desc");
            c.andOrderCodeEqualTo(orderCode);
            c.andEnableEqualTo(true);
            c.andContractTypeEqualTo(voucherType);
            List<TOrderContract> list = tOrderContractMapper.selectByExample(example);
            TOrderContract tOrderContract = null;
            if (list.size()>0){
                tOrderContract = list.get(0);
            }
            //备注
            String remark = "";
            if(tOrderContract!=null &&!"".equals(tOrderContract)){
                remark = "凭证编号为"+tOrderContract.getContractCode()+"的合同已作废";
            }

            TOrderInfoVO tOrderInfoVO = tOrderInfoMapper.selectByOrdeCode(orderCode);
            //承运方信息
            TCarrierInfo tCarrierInfo = new TCarrierInfo();
            //经纪人信息
            TEndUserInfo agentUserInfo = new TEndUserInfo();
            //车队长信息
            TEndUserInfo captainUserInfo = new TEndUserInfo();
            TCarrierEnduserCompanyRel agentCarrierRel = new TCarrierEnduserCompanyRel();
            TCarrierEnduserCompanyRel driverCarrierRel = new TCarrierEnduserCompanyRel();
            TCarrierEnduserCompanyRel captainCarrierRel = new TCarrierEnduserCompanyRel();
            if (DictEnum.AGENTTXXY.code.equals(voucherType)) {
                ResultUtil agentEnd = tEndSUserInfoAPI.selectById(tOrderInfoVO.getAgentId());
                agentUserInfo = JSON.parseObject(JSONObject.toJSON(agentEnd.getData()).toString(), TEndUserInfo.class);
                //经纪人网商子账号信息
                agentCarrierRel = tCarrierEnduserCompanyRelAPI.selectByEndUser(tOrderInfoVO.getCarrierId(), tOrderInfoVO.getAgentId());
            }else if(DictEnum.DRIVERTXXY.code.equals(voucherType)){
                ResultUtil reCarr = carrierService.selectById(tOrderInfoVO.getCarrierId().toString());
                tCarrierInfo = JSON.parseObject(JSONObject.toJSON(reCarr.getData()).toString(), TCarrierInfo.class);
                //司机网商子账号信息
                driverCarrierRel = tCarrierEnduserCompanyRelAPI.selectByEndUser(tOrderInfoVO.getCarrierId(), tOrderInfoVO.getEndDriverId());
            }else if(DictEnum.CAPTAINTXXY.code.equals(voucherType)){
                ResultUtil reCarr = carrierService.selectById(tOrderInfoVO.getCarrierId().toString());
                tCarrierInfo = JSON.parseObject(JSONObject.toJSON(reCarr.getData()).toString(), TCarrierInfo.class);
                ResultUtil captainEnd = tEndSUserInfoAPI.selectById(tOrderInfoVO.getEndCarOwnerId());
                captainUserInfo = JSON.parseObject(JSONObject.toJSON(captainEnd.getData()).toString(), TEndUserInfo.class);
                //车队长网商子账号信息
                captainCarrierRel = tCarrierEnduserCompanyRelAPI.selectByEndUser(tOrderInfoVO.getCarrierId(), tOrderInfoVO.getEndCarOwnerId());
            }
            Map<String, Object> map = new HashMap<>();
            map.put("carrierId", tOrderInfoVO.getCarrierId());//承运方id
            map.put("AId", tOrderInfoVO.getEndDriverId());//司机id
            map.put("ACardNo", tOrderInfoVO.getIdcard());// 司机证件号码
            map.put("AName", tOrderInfoVO.getRealName());//司机真实名称
            map.put("Aphone", tOrderInfoVO.getRealPhone());//司机联系手机号
            map.put("orderCode", tOrderInfoVO.getCode());//运单code
            map.put("orderBusinessCode", tOrderInfoVO.getOrderBusinessCode());//运单号
            map.put("mobilePhone", "***********"); //平台注册手机号  固定的
            map.put("agentAmount", tOrderInfoVO.getUserConfirmServiceFee());//服务费
            map.put("remark",remark);//备注
            if ("0".equals(tOrderInfoVO.getPackStatus())) {//0 未打包运单去用户确认应付运费     1 打包运单 去打包后运费
                map.put("driverAmount", tOrderInfoVO.getUserConfirmPaymentAmount());
            } else {
                map.put("driverAmount", tOrderInfoVO.getSharePaymentAmount());
            }
            TTask tTask = new TTask();
            if (DictEnum.AGENTTXXY.code.equals(voucherType)) {
                map.put("BCardNo", agentUserInfo.getIdcard());//经纪人证件号码
                map.put("BName", agentUserInfo.getRealName());//经纪人名称
                map.put("Bphone", agentUserInfo.getPhone());//经纪人联系手机号
                map.put("BId", agentUserInfo.getId());// 经纪人id
                map.put("agentApplyType", "服务费("+tOrderInfoVO.getOrderBusinessCode()+")");
                map.put("agentRecCardNO", agentCarrierRel.getThridParySubAccount());
                tTask.setTaskTypeNode(DictEnum.AGENTTXXY.code);//经纪人收款凭证
            } else if(DictEnum.DRIVERTXXY.code.equals(voucherType)) {
                map.put("CCDBusinessNo", tCarrierInfo.getBusinessLicenseNo());// 承运方证件号码
                map.put("QName", tCarrierInfo.getCarrierName());//承运方名称
                map.put("Qphone", tCarrierInfo.getCompanyContactsPhone());//承运方联系手机号
                map.put("driverApplyType", "运费("+tOrderInfoVO.getOrderBusinessCode()+")");
                map.put("driverRecCardNO", driverCarrierRel.getThridParySubAccount());
                tTask.setTaskTypeNode(DictEnum.DRIVERTXXY.code);//司机收款凭证
            }else if(DictEnum.CAPTAINTXXY.code.equals(voucherType)){
                map.put("CCDBusinessNo", tCarrierInfo.getBusinessLicenseNo());// 承运方证件号码
                map.put("QName", tCarrierInfo.getCarrierName());//承运方名称
                map.put("Qphone", tCarrierInfo.getCompanyContactsPhone());//承运方联系手机号
                map.put("captainApplyType", "运费("+tOrderInfoVO.getOrderBusinessCode()+")");
                map.put("captainRecCardNO", captainCarrierRel.getThridParySubAccount());
                map.put("DId", tOrderInfoVO.getEndDriverId());//车队长id
                map.put("DCardNo", tOrderInfoVO.getIdcard());// 车队长证件号码
                map.put("DName", tOrderInfoVO.getRealName());//车队长真实名称
                map.put("Dphone", tOrderInfoVO.getRealPhone());//车队长联系手机号
                tTask.setTaskTypeNode(DictEnum.CAPTAINTXXY.code);//车队长收款凭证
            }
            String requestParameter = JSONObject.toJSONString(map);
            tTask.setRequestParameter(requestParameter);
            tTask.setTaskId(IdWorkerUtil.getInstance().nextId());
            tTask.setTaskType(DictEnum.SKPZ.code);//提现协议
            tTask.setBusinessType("TX");//提现
            tTask.setSourceTablename("T_ORDER_INFO");
            tTask.setSourceFieldvalue(orderCode);
            tTask.setSourceFieldname("code");
            tTask.setRequestTimes(0);
            tTask.setCreateTime(new Date());
            tTask.setRequestDate(new Date());
            tTask.setIsSuccessed(false);
            tTask.setEnable(false);
            orderTaskMapper.insert(tTask);
            return ResultUtil.ok();
        }catch (Exception e){
            log.error("创建"+voucherType+"收款凭证失败：",e);
            return ResultUtil.error("创建"+voucherType+"收款凭证失败:"+orderCode);
        }
    }

    @Override
    public ResultUtil createJdPayVoucher(String orderCode, String voucherType) {
        try {
            TOrderContractExample example = new TOrderContractExample();
            TOrderContractExample.Criteria c = example.createCriteria();
            example.setOrderByClause("create_time desc");
            c.andOrderCodeEqualTo(orderCode);
            c.andEnableEqualTo(true);
            c.andContractTypeEqualTo(voucherType);
            List<TOrderContract> list = tOrderContractMapper.selectByExample(example);
            TOrderContract tOrderContract = null;
            if (list.size() > 0) {
                tOrderContract = list.get(0);
            }
            //备注
            String remark = "";
            if (tOrderContract != null && !"".equals(tOrderContract)) {
                remark = "凭证编号为" + tOrderContract.getContractCode() + "的合同已作废";
            }

            TOrderInfoVO tOrderInfoVO = tOrderInfoMapper.selectByOrdeCode(orderCode);
            //承运方信息
            TCarrierInfo tCarrierInfo = new TCarrierInfo();
            ResultUtil reCarr = carrierService.selectById(tOrderInfoVO.getCarrierId().toString());
            tCarrierInfo = JSON.parseObject(JSONObject.toJSON(reCarr.getData()).toString(), TCarrierInfo.class);
            // 查询开户信息，承运方、司机、车队长
            SelectOpenRoleVO selectOpenRoleVO = new SelectOpenRoleVO();
            selectOpenRoleVO.setCarrierId(tOrderInfoVO.getCarrierId());
            if (DictEnum.DRIVERTXXY.code.equals(voucherType)) {
                selectOpenRoleVO.setDriverId(tOrderInfoVO.getEndDriverId());
            }
            if (DictEnum.AGENTTXXY.code.equals(voucherType)) {
                selectOpenRoleVO.setManagerId(tOrderInfoVO.getAgentId());
            }
            ResultUtil resultUtil = openRoleCommonAPI.selectCarrierCompanyEnduserOpenRoleStatus(selectOpenRoleVO);
            OpenRoleStatusListDTO dto = JSONUtil.toBean(JSONUtil.parseObj(resultUtil.getData()), OpenRoleStatusListDTO.class);
            log.info("创建收款凭证，查询开户信息, {}", JSONUtil.toJsonStr(dto));
            Map<String, Object> map = new HashMap<>();
            map.put("carrierId", tOrderInfoVO.getCarrierId());//承运方id
            map.put("AId", tOrderInfoVO.getEndDriverId());//司机id
            map.put("ACardNo", tOrderInfoVO.getIdcard());// 司机证件号码
            map.put("AName", tOrderInfoVO.getRealName());//司机真实名称
            map.put("Aphone", tOrderInfoVO.getRealPhone());//司机联系手机号
            map.put("orderCode", tOrderInfoVO.getCode());//运单code
            map.put("orderBusinessCode", tOrderInfoVO.getOrderBusinessCode());//运单号
            map.put("mobilePhone", "***********"); //平台注册手机号  固定的
            map.put("agentAmount", tOrderInfoVO.getUserConfirmServiceFee());//服务费
            map.put("remark", remark);//备注
            if ("0".equals(tOrderInfoVO.getPackStatus())) {//0 未打包运单去用户确认应付运费     1 打包运单 去打包后运费
                map.put("driverAmount", tOrderInfoVO.getUserConfirmPaymentAmount());
            } else {
                map.put("driverAmount", tOrderInfoVO.getSharePaymentAmount());
            }
            TTask tTask = new TTask();
            if (DictEnum.DRIVERTXXY.code.equals(voucherType)) {
                map.put("CCDBusinessNo", tCarrierInfo.getBusinessLicenseNo());// 承运方证件号码
                map.put("QName", tCarrierInfo.getCarrierName());//承运方名称
                map.put("Qphone", tCarrierInfo.getCompanyContactsPhone());//承运方联系手机号
                map.put("driverApplyType", "运费(" + tOrderInfoVO.getOrderBusinessCode() + ")");
                map.put("driverRecCardNO", dto.getDriverStatus().getPartnerAccId());
                tTask.setTaskTypeNode(DictEnum.DRIVERTXXY.code);//司机收款凭证
            } else if (DictEnum.AGENTTXXY.code.equals(voucherType)) {
                ResultUtil agentEnd = tEndSUserInfoAPI.selectById(tOrderInfoVO.getAgentId());
                TEndUserInfo agentUserInfo = JSON.parseObject(JSONObject.toJSON(agentEnd.getData()).toString(), TEndUserInfo.class);
                map.put("BCardNo", agentUserInfo.getIdcard());//经纪人证件号码
                map.put("BName", agentUserInfo.getRealName());//经纪人名称
                map.put("Bphone", agentUserInfo.getPhone());//经纪人联系手机号
                map.put("BId", agentUserInfo.getId());// 经纪人id
                map.put("agentApplyType", "服务费("+tOrderInfoVO.getOrderBusinessCode()+")");
                map.put("agentRecCardNO", dto.getManagerStatus().getPartnerAccId());
                tTask.setTaskTypeNode(DictEnum.AGENTTXXY.code);//经纪人收款凭证
            }
            String requestParameter = JSONObject.toJSONString(map);
            tTask.setRequestParameter(requestParameter);
            tTask.setTaskId(IdWorkerUtil.getInstance().nextId());
            tTask.setTaskType(DictEnum.SKPZ.code);//提现协议
            tTask.setBusinessType("TX");//提现
            tTask.setSourceTablename("T_ORDER_INFO");
            tTask.setSourceFieldvalue(orderCode);
            tTask.setSourceFieldname("code");
            tTask.setRequestTimes(0);
            tTask.setCreateTime(new Date());
            tTask.setRequestDate(new Date());
            tTask.setIsSuccessed(false);
            tTask.setEnable(false);
            orderTaskMapper.insert(tTask);
            return ResultUtil.ok();
        } catch (Exception e) {
            log.error("创建" + voucherType + "京东支付收款凭证失败：{}", e);
            return ResultUtil.error("创建" + voucherType + "京东支付收款凭证失败:" + orderCode);
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2020/8/27 16:31
     *  @Description: 运单跟踪中三方货物合同上传
     */
    @Override
    public ResultUtil threeGoodsContractUpload(TOrderContractUploadVo record) {
        try{
            TOrderInfoExample example = new TOrderInfoExample();
            TOrderInfoExample.Criteria cr = example.createCriteria();
            cr.andOrderBusinessCodeIn(record.getOrderBusinessCodeList());
            List<TOrderInfo>  tOrderInfoList = tOrderInfoMapper.selectByExample(example);
            for(TOrderInfo tOrderInfo:tOrderInfoList){
                TOrderContract tOrderContract = new TOrderContract();
                tOrderContract.setContractCode(IdWorkerUtil.getInstance().nextId());
                tOrderContract.setOrderCode(tOrderInfo.getCode());
                tOrderContract.setParam4(record.getAbnormalRemarks());
                tOrderContract.setContractType(DictEnum.THREEGOODS.code);
                tOrderContract.setContractForm(DictEnum.THREEGOODS.code);
                String imgsd = "";
                for(int i=0;i< record.getImgList().size();i++){
                    if(i==record.getImgList().size()-1){
                        imgsd+=record.getImgList().get(i);
                    }else{
                        imgsd+=record.getImgList().get(i)+",";
                    }
                }
                tOrderContract.setContractFilePath(imgsd);
                tOrderContract.setParam1(imgsd);
                TOrderContractExample tOrderContractExample = new TOrderContractExample();
                TOrderContractExample.Criteria c = tOrderContractExample.createCriteria();
                c.andOrderCodeEqualTo(tOrderContract.getOrderCode());
                c.andContractTypeEqualTo(tOrderContract.getContractType());
                c.andContractFormEqualTo(tOrderContract.getContractForm());
                List<TOrderContract> tOrderContractList = tOrderContractMapper.selectByExample(tOrderContractExample);
                for(TOrderContract orderContract:tOrderContractList){
                    orderContract.setEnable(true);
                    tOrderContractMapper.updateByPrimaryKeySelective(orderContract);
                }
                tOrderContractMapper.insertSelective(tOrderContract);
            }
            return ResultUtil.ok();
        }catch (Exception e){
            log.error("三方货物合同上传失败",e);
            return ResultUtil.error("三方货物合同上传失败");
        }
    }

    /**
     * 创建承运合同任务
     * @param orderCode
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil createAnySignCyht(String orderCode, Boolean reSign) {
        try {
            TOrderInfo orderInfo = tOrderInfoMapper.selectOrderByCode(orderCode);
            //承运方信息
            ResultUtil reCarr = carrierService.selectById(orderInfo.getCarrierId().toString());
            TCarrierInfo tCarrierInfo =  JSON.parseObject(JSONObject.toJSON(reCarr.getData()).toString(),TCarrierInfo.class);
            //企业信息
            TCompanyInfoQuery companyInfoQuery = new TCompanyInfoQuery().setCompanyId(orderInfo.getCompanyId());
            ResultUtil re = companyService.selectCompanyInfoById(companyInfoQuery);
            TCompanyInfo companyInfo =  JSON.parseObject(JSONObject.toJSON(re.getData()).toString(),TCompanyInfo.class);
            //司机信息 姓名电话
            ResultUtil reEnd = tEndSUserInfoAPI.selectById(orderInfo.getEndDriverId());
            TEndUserInfo tEndUserInfo =  JSON.parseObject(JSONObject.toJSON(reEnd.getData()).toString(),TEndUserInfo.class);
            //车辆信息
            ResultUtil reCar = tEndCarInfoAPI.selectById(orderInfo.getVehicleId());
            TEndCarInfo tEndCarInfo =  JSON.parseObject(JSONObject.toJSON(reCar.getData()).toString(),TEndCarInfo.class);

            String taskType = NetSignEnum.NETSIGN_CYHT.code;
            CyhtContract cyhtContract = new CyhtContract();
            cyhtContract.setAsignTime(DateUtils.formatDateTime(new Date()));
            cyhtContract.setBsignTime(cyhtContract.getAsignTime());
            if (reSign) {
                // 补签合同
                taskType = NetSignEnum.NETSIGN_REPAIR.code;
                Calendar deliverOrderTime = Calendar.getInstance();
                deliverOrderTime.setTime(orderInfo.getDeliverOrderTime());
                cyhtContract.setDelieverYear(String.valueOf(deliverOrderTime.get(Calendar.YEAR)));
                cyhtContract.setDelieverMonth(String.valueOf(deliverOrderTime.get(Calendar.MONTH) + 1));
                cyhtContract.setDelieverDay(String.valueOf(deliverOrderTime.get(Calendar.DAY_OF_MONTH)));
                cyhtContract.setDelieverHour(String.valueOf(deliverOrderTime.get(Calendar.HOUR_OF_DAY)));
                cyhtContract.setDelieverMinute(String.valueOf(deliverOrderTime.get(Calendar.MINUTE)));
                cyhtContract.setDelieverSecond(String.valueOf(deliverOrderTime.get(Calendar.SECOND)));
                Calendar receiveOrderTime = Calendar.getInstance();
                if (null != orderInfo.getReceiveOrderTime()) {
                    receiveOrderTime.setTime(orderInfo.getReceiveOrderTime());
                    cyhtContract.setArrivalTime(DateUtils.formatDate(orderInfo.getReceiveOrderTime(), DateUtils.parsePattern));
                } else {
                    List<String> stateNodeValueList = new ArrayList<>();
                    stateNodeValueList.add("S0400");
                    stateNodeValueList.add("S0401");
                    stateNodeValueList.add("S0402");
                    stateNodeValueList.add("S0403");
                    stateNodeValueList.add("S0404");
                    stateNodeValueList.add("S0405");
                    stateNodeValueList.add("S0500");
                    stateNodeValueList.add("S0501");
                    stateNodeValueList.add("S0502");
                    stateNodeValueList.add("S0503");
                    TOrderState tOrderState = orderStateMapper.selectOrderStateByOrderCode(stateNodeValueList, orderCode);
                    if (null != tOrderState) {
                        receiveOrderTime.setTime(tOrderState.getOperateTime());
                        cyhtContract.setArrivalTime(DateUtils.formatDate(tOrderState.getOperateTime(), DateUtils.parsePattern));
                    }
                }
                cyhtContract.setFinishYear(String.valueOf(receiveOrderTime.get(Calendar.YEAR)));
                cyhtContract.setFinishMonth(String.valueOf(receiveOrderTime.get(Calendar.MONTH) + 1));
                cyhtContract.setFinishDay(String.valueOf(receiveOrderTime.get(Calendar.DAY_OF_MONTH)));
                cyhtContract.setFinishHour(String.valueOf(receiveOrderTime.get(Calendar.HOUR_OF_DAY)));
                cyhtContract.setFinishMinute(String.valueOf(receiveOrderTime.get(Calendar.MINUTE)));
                cyhtContract.setFinishSecond(String.valueOf(receiveOrderTime.get(Calendar.SECOND)));
            }
            cyhtContract.setOrderCode(orderCode);
            cyhtContract.setCarrierId(orderInfo.getCarrierId());
            cyhtContract.setOrderBusinessCode(orderInfo.getOrderBusinessCode());
            cyhtContract.setCarrierName(tCarrierInfo.getCarrierName());
            cyhtContract.setCarrierBusinessNo(tCarrierInfo.getBusinessLicenseNo());
            cyhtContract.setDriverId(orderInfo.getEndDriverId());
            cyhtContract.setDriverName(tEndUserInfo.getRealName());
            cyhtContract.setDriverPhone(tEndUserInfo.getPhone());
            cyhtContract.setDriverIdCard(tEndUserInfo.getIdcard());
            cyhtContract.setVehicleNo(tEndCarInfo.getVehicleNumber());
            cyhtContract.setGoodsType(orderInfo.getGoodsName());
            if (null == orderInfo.getEstimateGoodsWeight()
                    || BigDecimal.valueOf(orderInfo.getEstimateGoodsWeight()).compareTo(BigDecimal.valueOf(0)) == 0) {
                Double primaryWeight = orderInfo.getPrimaryWeight();
                cyhtContract.setWeight(BigDecimal.valueOf(primaryWeight));
            } else {
                Double estimateGoodsWeight = orderInfo.getEstimateGoodsWeight();
                cyhtContract.setWeight(BigDecimal.valueOf(estimateGoodsWeight));
            }
            cyhtContract.setCompanyName(companyInfo.getCompanyName());
            cyhtContract.setLoadingAddress(orderInfo.getFromName());
            cyhtContract.setLoadingName(orderInfo.getDeliverGoodsContacter());
            cyhtContract.setLoadingPhone(orderInfo.getDeliverGoodsContacterPhone());
            cyhtContract.setUnLoadingAddress(orderInfo.getEndName());
            cyhtContract.setUnLoadingName(orderInfo.getReceiveGoodsContacter());
            cyhtContract.setUnLoadingPhone(orderInfo.getReceiveGoodsContacterPhone());
            cyhtContract.setLimitTime("30");
            cyhtContract.setFreightTotal(orderInfo.getEstimateTotalFee());
            //20231009优化合同新增字段
            //车牌颜色
            if(null != tEndCarInfo.getLicensePlateColor()){
                if("1".equals(tEndCarInfo.getLicensePlateColor())){
                    cyhtContract.setPlateColor(LicensePlateColorEnum.BLUE.value);
                } else if("2".equals(tEndCarInfo.getLicensePlateColor())){
                    cyhtContract.setPlateColor(LicensePlateColorEnum.YELLOW.value);
                } else if("3".equals(tEndCarInfo.getLicensePlateColor())){
                    cyhtContract.setPlateColor(LicensePlateColorEnum.KELLY.value);
                }
            }else {
                cyhtContract.setPlateColor(LicensePlateColorEnum.YELLOW.value);
            }
            //根据orderBusinessCode查询投保表，获取货值，如果为空，则赋值0
            TOrderInsurance orderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(orderInfo.getOrderBusinessCode());
            if(null != orderInsurance && null != orderInsurance.getCargoValue() &&
                    orderInsurance.getCargoValue().compareTo(BigDecimal.ZERO) >= 0){
                cyhtContract.setGoodsUnitPrice(orderInsurance.getCargoValue());//货值
            }else{
                cyhtContract.setGoodsUnitPrice(BigDecimal.ZERO);//货值
            }
            cyhtContract.setTransportationRoute(orderInfo.getLineName());//线路名称
            //查询装货签到时的运单子状态
            TOrderState orderState = orderStateMapper.selectOrderCodeAnrState(orderInfo.getCode(), DictEnum.S0302.code);
            Date deliverWeightNotesTime = null;
            //如果装货签到时间为空，则获取发单时间
            if(null != orderState && null != orderState.getOperateTime()){
                deliverWeightNotesTime = orderState.getOperateTime();
            }else{
                deliverWeightNotesTime = orderInfo.getDeliverOrderTime();
            }
            cyhtContract.setDepartureTime(DateUtils.formatDate(deliverWeightNotesTime, DateUtils.parsePattern));//出发时间
            if(null == cyhtContract.getArrivalTime()){
                cyhtContract.setArrivalTime(DateUtils.formatDate(DateUtils.addDay(deliverWeightNotesTime, 3), DateUtils.parsePattern));//到达时间--出发时间加3天
            }
            cyhtContract.setCountFreight(OrderMoneyUtil.transitionMoney(orderInfo.getEstimateTotalFee().doubleValue()));//运费总额:大写
            cyhtContract.setTotalFreight(orderInfo.getEstimateTotalFee());//运费总额:小写
            cyhtContract.setAdvancePayment(OrderMoneyUtil.transitionMoney(0D));//预付款：大写
            cyhtContract.setAdvance(BigDecimal.ZERO);//预付款：小写
            cyhtContract.setRemainingSum(OrderMoneyUtil.transitionMoney(0D));//余额：大写
            cyhtContract.setBalance(BigDecimal.ZERO);//余额：小写

            TTask task = new TTask();
            String requestParameter = JSONObject.toJSONString(cyhtContract);
            task.setRequestParameter(requestParameter);
            task.setTaskId(IdWorkerUtil.getInstance().nextId());
            task.setTaskType(taskType);
            task.setBusinessType(DictEnum.HT.code);
            task.setTaskTypeNode(DictEnum.HTCREATE.code);
            task.setSourceTablename("T_ORDER_INFO");
            task.setSourceFieldvalue(orderCode);
            task.setSourceFieldname("code");
            task.setRequestTimes(0);
            task.setCreateTime(new Date());
            task.setRequestDate(new Date());
            task.setIsSuccessed(false);
            task.setEnable(false);
            orderTaskMapper.insert(task);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("创建承运合同任务: {}", ThrowableUtil.getStackTrace(e));
            if (null != e.getMessage()) {
                return ResultUtil.error(e.getMessage());
            } else {
                return ResultUtil.error("创建承运合同任务失败");
            }
        }
        return ResultUtil.ok();
    }

    /**
     * 创建补签承运合同任务
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil createSignRepairCyht(String orderCode,Integer pushContractId) {
        try {
            TOrderInfo orderInfo = tOrderInfoMapper.selectOrderByCode(orderCode);
            //承运方信息
            ResultUtil reCarr = carrierService.selectById(orderInfo.getCarrierId().toString());
            TCarrierInfo tCarrierInfo =  JSON.parseObject(JSONObject.toJSON(reCarr.getData()).toString(),TCarrierInfo.class);
            //企业信息
            TCompanyInfoQuery companyInfoQuery = new TCompanyInfoQuery().setCompanyId(orderInfo.getCompanyId());
            ResultUtil re = companyService.selectCompanyInfoById(companyInfoQuery);
            TCompanyInfo companyInfo =  JSON.parseObject(JSONObject.toJSON(re.getData()).toString(),TCompanyInfo.class);
            //司机信息 姓名电话
            ResultUtil reEnd = tEndSUserInfoAPI.selectById(orderInfo.getEndDriverId());
            TEndUserInfo tEndUserInfo =  JSON.parseObject(JSONObject.toJSON(reEnd.getData()).toString(),TEndUserInfo.class);
            //车辆信息
            ResultUtil reCar = tEndCarInfoAPI.selectById(orderInfo.getVehicleId());
            TEndCarInfo tEndCarInfo =  JSON.parseObject(JSONObject.toJSON(reCar.getData()).toString(),TEndCarInfo.class);

            CyhtContract cyhtContract = new CyhtContract();
            cyhtContract.setAsignTime(DateUtils.formatDateTime(new Date()));
            cyhtContract.setBsignTime(cyhtContract.getAsignTime());
            // 补签合同
            String taskType = NetSignEnum.NETSIGN_REPAIR.code;
            Calendar deliverOrderTime = Calendar.getInstance();
            deliverOrderTime.setTime(orderInfo.getDeliverOrderTime());
            cyhtContract.setDelieverYear(String.valueOf(deliverOrderTime.get(Calendar.YEAR)));
            cyhtContract.setDelieverMonth(String.valueOf(deliverOrderTime.get(Calendar.MONTH) + 1));
            cyhtContract.setDelieverDay(String.valueOf(deliverOrderTime.get(Calendar.DAY_OF_MONTH)));
            cyhtContract.setDelieverHour(String.valueOf(deliverOrderTime.get(Calendar.HOUR_OF_DAY)));
            cyhtContract.setDelieverMinute(String.valueOf(deliverOrderTime.get(Calendar.MINUTE)));
            cyhtContract.setDelieverSecond(String.valueOf(deliverOrderTime.get(Calendar.SECOND)));
            cyhtContract.setPushContractId(pushContractId);//导入运单结果表ID
            Calendar receiveOrderTime = Calendar.getInstance();
            if (null != orderInfo.getReceiveOrderTime()) {
                receiveOrderTime.setTime(orderInfo.getReceiveOrderTime());
                cyhtContract.setArrivalTime(DateUtils.formatDate(orderInfo.getReceiveOrderTime(), DateUtils.parsePattern));
            } else {
                List<String> stateNodeValueList = new ArrayList<>();
                stateNodeValueList.add("S0400");
                stateNodeValueList.add("S0401");
                stateNodeValueList.add("S0402");
                stateNodeValueList.add("S0403");
                stateNodeValueList.add("S0404");
                stateNodeValueList.add("S0405");
                stateNodeValueList.add("S0500");
                stateNodeValueList.add("S0501");
                stateNodeValueList.add("S0502");
                stateNodeValueList.add("S0503");
                TOrderState tOrderState = orderStateMapper.selectOrderStateByOrderCode(stateNodeValueList, orderCode);
                if (null != tOrderState) {
                    receiveOrderTime.setTime(tOrderState.getOperateTime());
                    cyhtContract.setArrivalTime(DateUtils.formatDate(tOrderState.getOperateTime(), DateUtils.parsePattern));
                }
            }
            cyhtContract.setFinishYear(String.valueOf(receiveOrderTime.get(Calendar.YEAR)));
            cyhtContract.setFinishMonth(String.valueOf(receiveOrderTime.get(Calendar.MONTH) + 1));
            cyhtContract.setFinishDay(String.valueOf(receiveOrderTime.get(Calendar.DAY_OF_MONTH)));
            cyhtContract.setFinishHour(String.valueOf(receiveOrderTime.get(Calendar.HOUR_OF_DAY)));
            cyhtContract.setFinishMinute(String.valueOf(receiveOrderTime.get(Calendar.MINUTE)));
            cyhtContract.setFinishSecond(String.valueOf(receiveOrderTime.get(Calendar.SECOND)));
            cyhtContract.setOrderCode(orderCode);
            cyhtContract.setCarrierId(orderInfo.getCarrierId());
            cyhtContract.setOrderBusinessCode(orderInfo.getOrderBusinessCode());
            cyhtContract.setCarrierName(tCarrierInfo.getCarrierName());
            cyhtContract.setCarrierBusinessNo(tCarrierInfo.getBusinessLicenseNo());
            cyhtContract.setDriverId(orderInfo.getEndDriverId());
            cyhtContract.setDriverName(tEndUserInfo.getRealName());
            cyhtContract.setDriverPhone(tEndUserInfo.getPhone());
            cyhtContract.setDriverIdCard(tEndUserInfo.getIdcard());
            cyhtContract.setVehicleNo(tEndCarInfo.getVehicleNumber());
            cyhtContract.setGoodsType(orderInfo.getGoodsName());
            if (null == orderInfo.getEstimateGoodsWeight()
                    || BigDecimal.valueOf(orderInfo.getEstimateGoodsWeight()).compareTo(BigDecimal.valueOf(0)) == 0) {
                Double primaryWeight = orderInfo.getPrimaryWeight();
                cyhtContract.setWeight(BigDecimal.valueOf(primaryWeight));
            } else {
                Double estimateGoodsWeight = orderInfo.getEstimateGoodsWeight();
                cyhtContract.setWeight(BigDecimal.valueOf(estimateGoodsWeight));
            }
            cyhtContract.setCompanyName(companyInfo.getCompanyName());
            cyhtContract.setLoadingAddress(orderInfo.getFromName());
            cyhtContract.setLoadingName(orderInfo.getDeliverGoodsContacter());
            cyhtContract.setLoadingPhone(orderInfo.getDeliverGoodsContacterPhone());
            cyhtContract.setUnLoadingAddress(orderInfo.getEndName());
            cyhtContract.setUnLoadingName(orderInfo.getReceiveGoodsContacter());
            cyhtContract.setUnLoadingPhone(orderInfo.getReceiveGoodsContacterPhone());
            cyhtContract.setLimitTime("30");
            cyhtContract.setFreightTotal(orderInfo.getEstimateTotalFee());
            //20231009优化合同新增字段
            //车牌颜色
            if(null != tEndCarInfo.getLicensePlateColor()){
                if("1".equals(tEndCarInfo.getLicensePlateColor())){
                    cyhtContract.setPlateColor(LicensePlateColorEnum.BLUE.value);
                } else if("2".equals(tEndCarInfo.getLicensePlateColor())){
                    cyhtContract.setPlateColor(LicensePlateColorEnum.YELLOW.value);
                } else if("3".equals(tEndCarInfo.getLicensePlateColor())){
                    cyhtContract.setPlateColor(LicensePlateColorEnum.KELLY.value);
                }
            }else {
                cyhtContract.setPlateColor(LicensePlateColorEnum.YELLOW.value);
            }
            //根据orderBusinessCode查询投保表，获取货值，如果为空，则赋值0
            TOrderInsurance orderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(orderInfo.getOrderBusinessCode());
            if(null != orderInsurance && null != orderInsurance.getCargoValue() &&
                    orderInsurance.getCargoValue().compareTo(BigDecimal.ZERO) >= 0){
                cyhtContract.setGoodsUnitPrice(orderInsurance.getCargoValue());//货值
            }else{
                cyhtContract.setGoodsUnitPrice(BigDecimal.ZERO);//货值
            }
            cyhtContract.setTransportationRoute(orderInfo.getLineName());//线路名称
            //查询装货签到时的运单子状态
            TOrderState orderState = orderStateMapper.selectOrderCodeAnrState(orderInfo.getCode(), DictEnum.S0302.code);
            Date deliverWeightNotesTime = null;
            //如果装货签到时间为空，则获取发单时间
            if(null != orderState && null != orderState.getOperateTime()){
                deliverWeightNotesTime = orderState.getOperateTime();
            }else{
                deliverWeightNotesTime = orderInfo.getDeliverOrderTime();
            }
            cyhtContract.setDepartureTime(DateUtils.formatDate(deliverWeightNotesTime, DateUtils.parsePattern));//出发时间
            if(null == cyhtContract.getArrivalTime()){
                cyhtContract.setArrivalTime(DateUtils.formatDate(DateUtils.addDay(deliverWeightNotesTime, 3), DateUtils.parsePattern));//到达时间--出发时间加3天
            }
            cyhtContract.setCountFreight(OrderMoneyUtil.transitionMoney(orderInfo.getEstimateTotalFee().doubleValue()));//运费总额:大写
            cyhtContract.setTotalFreight(orderInfo.getEstimateTotalFee());//运费总额:小写
            cyhtContract.setAdvancePayment(OrderMoneyUtil.transitionMoney(0D));//预付款：大写
            cyhtContract.setAdvance(BigDecimal.ZERO);//预付款：小写
            cyhtContract.setRemainingSum(OrderMoneyUtil.transitionMoney(0D));//余额：大写
            cyhtContract.setBalance(BigDecimal.ZERO);//余额：小写

            TTask task = new TTask();
            String requestParameter = JSONObject.toJSONString(cyhtContract);
            task.setRequestParameter(requestParameter);
            task.setTaskId(IdWorkerUtil.getInstance().nextId());
            task.setTaskType(taskType);
            task.setBusinessType(DictEnum.HT.code);
            task.setTaskTypeNode(DictEnum.HTCREATE.code);
            task.setSourceTablename("T_ORDER_INFO");
            task.setSourceFieldvalue(orderCode);
            task.setSourceFieldname("code");
            task.setRequestTimes(0);
            task.setCreateTime(new Date());
            task.setRequestDate(new Date());
            task.setIsSuccessed(false);
            task.setEnable(false);
            orderTaskMapper.insert(task);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("创建承运合同任务: {}", ThrowableUtil.getStackTrace(e));
            if (null != e.getMessage()) {
                return ResultUtil.error(e.getMessage());
            } else {
                return ResultUtil.error("创建承运合同任务失败");
            }
        }
        return ResultUtil.ok();
    }

    /**
     * 创建随意签收款凭证任务
     * @param orderCode
     * @param voucherType 合同类型
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil createAnySignSkpz(String orderCode, String voucherType) {
        try {
            TOrderInfoVO tOrderInfoVO = tOrderInfoMapper.selectByOrdeCode(orderCode);
            //承运方信息
            TCarrierInfo tCarrierInfo;
            ResultUtil reCarr = carrierService.selectById(tOrderInfoVO.getCarrierId().toString());
            tCarrierInfo = JSON.parseObject(JSONObject.toJSON(reCarr.getData()).toString(), TCarrierInfo.class);
            // 查询开户信息，承运方、司机、车队长
            SelectOpenRoleVO selectOpenRoleVO = new SelectOpenRoleVO();
            selectOpenRoleVO.setCarrierId(tOrderInfoVO.getCarrierId());
            if (DictEnum.ANYSIGNDRIVERSKPZ.code.equals(voucherType)) {
                selectOpenRoleVO.setDriverId(tOrderInfoVO.getEndDriverId());
            }
            if (DictEnum.ANYSIGNDRIVERSKPZ.code.equals(voucherType)) {
                selectOpenRoleVO.setManagerId(tOrderInfoVO.getAgentId());
            }
            ResultUtil resultUtil = openRoleCommonAPI.selectCarrierCompanyEnduserOpenRoleStatus(selectOpenRoleVO);
            OpenRoleStatusListDTO dto = JSONUtil.toBean(JSONUtil.parseObj(resultUtil.getData()), OpenRoleStatusListDTO.class);
            log.info("创建收款凭证，查询开户信息, {}", JSONUtil.toJsonStr(dto));
            SkpzContract skpzContract = new SkpzContract();
            ResultUtil resultUtil1 = dicCatItemAPI.selectByItemCode(DictEnum.ANYSIGNTEMPLATENAME.code, DictEnum.ANYSIGNDRIVERSKPZ.code);
            DicItemDTO dicItemDTO = JSONUtil.toBean(JSONUtil.toJsonStr(resultUtil1.getData()), DicItemDTO.class);
            skpzContract.setTemplateName(dicItemDTO.getName());
            skpzContract.setOrderCode(orderCode);
            skpzContract.setOrderBusinessCode(tOrderInfoVO.getOrderBusinessCode());
            skpzContract.setCarrierId(tOrderInfoVO.getCarrierId());
            // 如果是随意签司机收款凭证合同
            if (DictEnum.ANYSIGNDRIVERSKPZ.code.equals(voucherType)) {
                skpzContract.setCarrierName(tCarrierInfo.getCarrierName());//承运方名称
                skpzContract.setCarrierBusinessNo(tCarrierInfo.getBusinessLicenseNo());
                skpzContract.setDriverId(tOrderInfoVO.getEndDriverId());//司机id
                skpzContract.setDriverApplyType("运费(" + tOrderInfoVO.getOrderBusinessCode() + ")");
                skpzContract.setDriverName(tOrderInfoVO.getRealName());
                skpzContract.setDriverIdCard(tOrderInfoVO.getIdcard());//司机姓名
                skpzContract.setDriverRecCardNO(dto.getDriverStatus().getPartnerAccId());
                //司机信息 姓名电话
                ResultUtil reEnd = tEndSUserInfoAPI.selectById(skpzContract.getDriverId());
                TEndUserInfo tEndUserInfo =  JSON.parseObject(JSONObject.toJSON(reEnd.getData()).toString(),TEndUserInfo.class);
                skpzContract.setDriverPhone(tEndUserInfo.getPhone());
                if (DictEnum.SINGLE.code.equals(tOrderInfoVO.getPackStatus())) {
                    skpzContract.setDriverAmount(tOrderInfoVO.getUserConfirmPaymentAmount());
                } else {
                    skpzContract.setDriverAmount(tOrderInfoVO.getSharePaymentAmount());
                }
            }
            skpzContract.setFinishDate(DateUtils.formatDate(new Date(), "yyyy-MM-dd"));
            skpzContract.setRemark(" ");

            TTask task = new TTask();
            String requestParameter = JSONObject.toJSONString(skpzContract);
            task.setRequestParameter(requestParameter);
            task.setTaskId(IdWorkerUtil.getInstance().nextId());
            task.setTaskType(voucherType);
            task.setBusinessType(DictEnum.HT.code);
            task.setTaskTypeNode(DictEnum.HTCREATE.code);
            task.setSourceTablename("T_ORDER_INFO");
            task.setSourceFieldvalue(orderCode);
            task.setSourceFieldname("code");
            task.setRequestTimes(0);
            task.setCreateTime(new Date());
            task.setRequestDate(new Date());
            task.setIsSuccessed(false);
            task.setEnable(false);
            orderTaskMapper.insert(task);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("创建随意签收款凭证任务：类型：{}, {}", voucherType, e);
        }

        return ResultUtil.ok();
    }

    /**
     * 华夏收款凭证
     * @param orderCode
     * @param voucherType
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil createHXAnySignSkpz(String orderCode, String voucherType) {
        return createSkpz(orderCode);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil createSkpz(String orderCode) {
        try {
            TOrderInfoVO tOrderInfoVO = tOrderInfoMapper.selectByOrdeCode(orderCode);
            //承运方信息
            TCarrierInfo tCarrierInfo;
            ResultUtil reCarr = carrierService.selectById(tOrderInfoVO.getCarrierId().toString());
            tCarrierInfo = JSON.parseObject(JSONObject.toJSON(reCarr.getData()).toString(), TCarrierInfo.class);
            // 查询开户信息，承运方、司机、车队长
            SelectOpenRoleVO selectOpenRoleVO = new SelectOpenRoleVO();
            selectOpenRoleVO.setCarrierId(tOrderInfoVO.getCarrierId());
            selectOpenRoleVO.setDriverId(tOrderInfoVO.getEndDriverId());
            selectOpenRoleVO.setChannelId(BankNameEnum.HXBANK.key);
            ResultUtil resultUtil = hxOpenRoleCommonAPI.selectCarrierCompanyEnduserOpenRoleStatus(selectOpenRoleVO);
            OpenRoleStatusListDTO dto = JSONUtil.toBean(JSONUtil.parseObj(resultUtil.getData()), OpenRoleStatusListDTO.class);
            log.info("创建收款凭证，查询开户信息, {}", JSONUtil.toJsonStr(dto));
            SkpzContract skpzContract = new SkpzContract();
            ResultUtil resultUtil1 = dicCatItemAPI.selectByItemCode(DictEnum.ANYSIGNTEMPLATENAME.code, DictEnum.ANYSIGNDRIVERSKPZ.code);
            DicItemDTO dicItemDTO = JSONUtil.toBean(JSONUtil.toJsonStr(resultUtil1.getData()), DicItemDTO.class);
            skpzContract.setTemplateName(dicItemDTO.getName());
            skpzContract.setOrderCode(orderCode);
            skpzContract.setOrderBusinessCode(tOrderInfoVO.getOrderBusinessCode());
            skpzContract.setCarrierId(tOrderInfoVO.getCarrierId());
            skpzContract.setCarrierName(tCarrierInfo.getCarrierName());//承运方名称
            skpzContract.setCarrierBusinessNo(tCarrierInfo.getBusinessLicenseNo());
            skpzContract.setDriverId(tOrderInfoVO.getEndDriverId());//司机id
            skpzContract.setDriverApplyType("运费(" + tOrderInfoVO.getOrderBusinessCode() + ")");
            skpzContract.setDriverName(tOrderInfoVO.getRealName());
            skpzContract.setDriverIdCard(tOrderInfoVO.getIdcard());//司机姓名
            skpzContract.setDriverRecCardNO(dto.getDriverStatus().getPartnerAccId());
            //司机信息 姓名电话
            ResultUtil reEnd = tEndSUserInfoAPI.selectById(skpzContract.getDriverId());
            TEndUserInfo tEndUserInfo =  JSON.parseObject(JSONObject.toJSON(reEnd.getData()).toString(),TEndUserInfo.class);
            skpzContract.setDriverPhone(tEndUserInfo.getPhone());
            if (DictEnum.SINGLE.code.equals(tOrderInfoVO.getPackStatus())) {
                skpzContract.setDriverAmount(tOrderInfoVO.getUserConfirmPaymentAmount());
            } else {
                skpzContract.setDriverAmount(tOrderInfoVO.getSharePaymentAmount());
            }
            skpzContract.setFinishDate(DateUtils.formatDate(new Date(), "yyyy-MM-dd"));
            skpzContract.setRemark(" ");

            TTask task = new TTask();
            String requestParameter = JSONObject.toJSONString(skpzContract);
            task.setRequestParameter(requestParameter);
            task.setTaskId(IdWorkerUtil.getInstance().nextId());
            task.setTaskType(NetSignEnum.NETSIGN_SKPZ.code);
            task.setBusinessType(DictEnum.HT.code);
            task.setTaskTypeNode(DictEnum.HTCREATE.code);
            task.setSourceTablename("T_ORDER_INFO");
            task.setSourceFieldvalue(orderCode);
            task.setSourceFieldname("code");
            task.setRequestTimes(0);
            task.setCreateTime(new Date());
            task.setRequestDate(new Date());
            task.setIsSuccessed(false);
            task.setEnable(false);
            orderTaskMapper.insert(task);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("创建收款凭证任务：类型：{}", ThrowableUtil.getStackTrace(e));
        }

        return ResultUtil.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int deleteHxAnySignSkpz(String orderCode, String voucherType) {
        TOrderContract tOrderContract = new TOrderContract();
        tOrderContract.setOrderCode(orderCode);
        tOrderContract.setContractType(DictEnum.ANYSIGNDRIVERSKPZ.code);
        tOrderContract.setEnable(true);
        tOrderContractMapper.updateByOrderCodeModel(tOrderContract);

        TTask task = new TTask();
        task.setSourceFieldvalue(orderCode);
        task.setTaskType(DictEnum.ANYSIGNDRIVERSKPZ.code);
        List<TTask> tTasks = orderTaskMapper.selectTaskByModel(task);
        if (null != tTasks && tTasks.size() > 0) {
            for (TTask tTask : tTasks) {
                TTaskHistory tTaskHistory = new TTaskHistory();
                tTaskHistory.setTaskHisId(IdWorkerUtil.getInstance().nextId());
                tTaskHistory.setTaskId(tTask.getTaskId());
                tTaskHistory.setTaskType(tTask.getTaskType());
                tTaskHistory.setTaskTypeNode(tTask.getTaskTypeNode());
                tTaskHistory.setBusinessType(tTask.getBusinessType());
                tTaskHistory.setSourceTablename(tTask.getSourceTablename());
                tTaskHistory.setSourcekeyFieldname(tTask.getSourcekeyFieldname());
                tTaskHistory.setSourceFieldname(tTask.getSourceFieldname());
                tTaskHistory.setSourceFieldvalue(tTask.getSourceFieldvalue());
                tTaskHistory.setRequestUrl(tTask.getRequestUrl());
                tTaskHistory.setRequestParameter(tTask.getRequestParameter());
                tTaskHistory.setRequestTimes(tTask.getRequestTimes());
                tTaskHistory.setRequestResult(tTask.getRequestResult());
                tTaskHistory.setErrorMessage(tTask.getErrorMessage());
                tTaskHistory.setRequestDate(tTask.getRequestDate());
                tTaskHistory.setDealTime(tTask.getDealTime());
                tTaskHistory.setIsSuccessed(tTask.getIsSuccessed());
                tTaskHistory.setToHisDate(tTask.getCreateTime());
                tTaskHistory.setToHisUserid(tTask.getCreateUser());
                tTaskHistory.setRemark(tTask.getRemark());
                tTaskHistory.setCreateTime(new DateTime());
                tTaskHistory.setEnable(false);
                orderTaskMapper.insertHistory(tTaskHistory);
                // 删除任务
                orderTaskMapper.delteByPrimaryKey(tTask.getId());
            }
        }
        return 1;
    }

    @Override
    public List<Integer> selectSignContractByOrderId(List<Integer> ids) {
        return tOrderContractMapper.selectSignContractByOrderId(ids);
    }

    @Override
    public TOrderContract getContractPhotoByOrderCode(String code) {
        return tOrderContractMapper.getContractPhotoByOrderCode(code);
    }

}
