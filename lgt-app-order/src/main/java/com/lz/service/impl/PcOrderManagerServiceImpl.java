package com.lz.service.impl;

import cn.hutool.json.JSONUtil;
import com.lz.api.CompanyService;
import com.lz.api.GoodsSourceAPI;
import com.lz.api.ProjectCarrierAPI;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.HXTradeTypeEnum;
import com.lz.common.dbenum.JDTradeTypeEnum;
import com.lz.common.dbenum.NetSignEnum;
import com.lz.common.util.*;
import com.lz.dao.*;
import com.lz.dao.mongodb.TrajectoryMongoDao;
import com.lz.dto.*;
import com.lz.example.TOrderInfoExample;
import com.lz.model.*;
import com.lz.model.mongodb.TrajectoryMongo;
import com.lz.payment.TradeType;
import com.lz.service.PcOrderManagerService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.util.OrderDeductionUtil;
import com.lz.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * PC端 运单管理
 * Yan
 */
@Slf4j
@Service
public class PcOrderManagerServiceImpl implements PcOrderManagerService {

    @Autowired
    TOrderStateMapper orderStateMapper;
    @Autowired
    TOrderInfoMapper orderInfoMapper;
    @Autowired
    TOrderPayRuleMapper tOrderPayRuleMapper;
    @Autowired
    TOrderCastCalcSnapshotMapper orderCastCalcSnapshotMapper;
    @Autowired
    TOrderVehicleTrajectoryMapper orderVehicleTrajectoryMapper;
    @Autowired
    TOrderAbnormalMapper orderAbnormalMapper;
    @Autowired
    TOrderContractMapper orderContractMapper;
    @Autowired
    TOrderCastChangesMapper orderCastChangesMapper;
    @Autowired
    private TOrderPayInfoMapper orderPayInfoMapper;

    @Autowired
    private TOrderInfoDetailMapper tOrderInfoDetailMapper;

    @Resource
    private THXOrderWalletChangeLogMapper hxOrderWalletChangeLogMapper;

    @Resource
    private TJdOrderWalletChangeLogMapper jdOrderWalletChangeLogMapper;

    @Resource
    private TOrderPayRequestMapper orderPayRequestMapper;

    @Autowired
    private ProjectCarrierAPI projectCarrierAPI;

    @Resource
    private TAdvanceOrderPayTmpMapper advanceOrderPayTmpMapper;

    @Autowired
    CompanyService companyService;
    @Autowired
    private SysParamAPI sysParamAPI;

    @Resource
    private TrajectoryMongoDao trajectoryMongoDao;

    @Resource
    private THXOrderPayDetailMapper hxOrderPayDetailMapper;

    @Resource
    private TOrderInfoWeightMapper orderInfoWeightMapper;

    @Autowired
    private GoodsSourceAPI goodsSourceAPI;

    @Resource
    private TOrderInsuranceMapper orderInsuranceMapper;

    /**
     * PC 端运单管理: 查询运单详情
     * Yan
     * @param code
     * @return
     */
    @Override
    public ResultUtil selectOrderDetailed(String code) throws RuntimeException {
        Map<String, Object> map = new HashMap<>();
        // 获取运单进度
        List<AppOrderProcessDTO> appOrderProcess = getAppOrderProcessDTOS(code);
        List<AppOrderProcessDTO> appOrderProces = pcProcess(appOrderProcess);
        map.put("process", appOrderProces);

        // 根据运单状态 未收单：获取货源，司机，车辆，货物信息
        OrderDetailDTO orderDetailDTO = orderInfoMapper.pcSelectOrderDetailed(code);
        //如果运单费用单位为箱，则查询t_order_info_weight表
        if(null != orderDetailDTO){
            if(DictEnum.CARRIAGE_PRICE_UNIT_BOX.code.equals(orderDetailDTO.getCarriagePriceUnit())){
                TOrderInfoWeight orderInfoWeight = orderInfoWeightMapper.selectByOrderId(orderDetailDTO.getId());
                if(null != orderInfoWeight){
                    TOrderInfoWeightVO vo = new TOrderInfoWeightVO();
                    BeanUtils.copyProperties(orderInfoWeight,vo);
                    orderDetailDTO.setOrderInfoWeight(vo);
                    orderDetailDTO.setFixCutFee(orderDetailDTO.getFixCutFee2());//重新给固定扣款赋值
                }
            }
            if(DictEnum.CARRIAGE_PRICE_UNIT_CAR.code.equals(orderDetailDTO.getCarriagePriceUnit())){
                orderDetailDTO.setFixCutFee(orderDetailDTO.getFixCutFee2());//重新给固定扣款赋值
            }
        }
        // 没有收单，就没有结算重量这里放 0.0000
        orderDetailDTO.setSettledWeight(0.0000);
        map.put("without", orderDetailDTO);
        if (StringUtils.isNotBlank(orderDetailDTO.getPayMethod())){
            map.put("payMethod", orderDetailDTO.getPayMethod());
        }else {
            map.put("payMethod", "");
        }
        List<String> jdlist = new ArrayList<>();
        TOrderNodeDetailVO tOrderNodeDetailVO = tOrderPayRuleMapper.selectNodeOrderPayDetailed(code);
        if (null!=tOrderNodeDetailVO){
            if (null!=tOrderNodeDetailVO.getZhRulePaymentFee() && tOrderNodeDetailVO.getZhRulePaymentFee().compareTo(BigDecimal.ZERO)>0){
                jdlist.add(DictEnum.M031.code);
            }
            if (null!=tOrderNodeDetailVO.getXhRulePaymentFee() && tOrderNodeDetailVO.getXhRulePaymentFee().compareTo(BigDecimal.ZERO)>0){
                jdlist.add(DictEnum.M041.code);
            }
            if (null!=tOrderNodeDetailVO.getSdRulePaymentFee() && tOrderNodeDetailVO.getSdRulePaymentFee().compareTo(BigDecimal.ZERO)>0){
                jdlist.add(DictEnum.M051.code);
            }
            if (null!=tOrderNodeDetailVO.getWkRulePaymentFee() && tOrderNodeDetailVO.getWkRulePaymentFee().compareTo(BigDecimal.ZERO)>0){
                jdlist.add(DictEnum.M091.code);
            }
        }
        map.put("jdlist", jdlist);
        try{
            // 根据运单状态 已收单：获取结算信息
            OrderDetailDTO orderDetailDTO1 = orderCastCalcSnapshotMapper.pcSelectSnapsByOrderCode(code);
            //if (orderDetailDTO.getPrimaryWeight() == 0) {
            //    orderDetailDTO1.setPrimaryWeight(orderDetailDTO.getEstimateGoodsWeight().doubleValue());
            //}

            orderDetailDTO1.setCurrentCarriageUnitPrice(orderDetailDTO.getGoodsUnitPrice());
            if (orderDetailDTO.getOrderExecuteStatus().equals(DictEnum.M010.code)
                    || orderDetailDTO.getOrderExecuteStatus().equals(DictEnum.M030.code)
                    || orderDetailDTO.getOrderExecuteStatus().equals(DictEnum.M040.code)
                    ){
                if (StringUtils.isNotEmpty(orderDetailDTO.getToleranceItem()) && orderDetailDTO.getToleranceItem().equals(DictEnum.ANDUNSHU.code)){
                    orderDetailDTO1.setTolerantValueWeight(orderDetailDTO.getToleranceItemValue());
                }
                if (StringUtils.isNotEmpty(orderDetailDTO.getToleranceItem()) && orderDetailDTO.getToleranceItem().equals(DictEnum.ANXISHU.code)){
                    orderDetailDTO1.setTolerantValueCoefficient(orderDetailDTO.getToleranceItemValue());
                }
            }
            BeanCopyUtil.copyPropertiesIgnoreNull(orderDetailDTO1, orderDetailDTO);
            //添加资金转移方式
            TOrderCastChanges tOrderCastChanges = orderCastChangesMapper.selectByNewOne(code);
            if(null!=tOrderCastChanges && StringUtils.isNotBlank(tOrderCastChanges.getCapitalTransferType())){
                orderDetailDTO.setCapitalTransferType(tOrderCastChanges.getCapitalTransferType());
            }

//            orderDetailDTO.setCarriageZeroCutPaymentName(orderDetailDTO1.getCarriageZeroCutPaymentRule());
            map.put("Acquiring", orderDetailDTO);
            return ResultUtil.ok(map);
        } catch (RuntimeException e) {
            log.error("ZJ-021:运单详情获取结算信息失败，参数：" + code + "错误信息：" + e);
            throw new RuntimeException("ZJ-021:运单详情查询失败!");
        }
    }

    /**
     * PC 端运单管理: 查询节点运单详情
     * Yan
     * @param code
     * @return
     */
    @Override
    public ResultUtil selectNodeOrderDetailed(String code, String paymentPlatforms) throws RuntimeException {

        try{
            OrderDetailDTO orderDetailDTO = orderCastCalcSnapshotMapper.appSelectSnapsByOrderCode(code);
            if(null!=orderDetailDTO &&!"".equals(orderDetailDTO)){
                if (null != orderDetailDTO.getRuleName() && orderDetailDTO.getRuleName().equals("null")) {
                    orderDetailDTO.setRuleName("");
                }
            }
            CollectOrderInfoDTO collectOrderInfo = orderInfoMapper.selectPayOrderDetail(code, CurrentUser.getUserAccountId());
            //如果有order_info_weight表ID，则表明一定是集装箱运单
            if(null != collectOrderInfo.getOrderInfoWeightId()){
                //根据order_info_weight表ID查询
                TOrderInfoWeight orderInfoWeight = orderInfoWeightMapper.selectByPrimaryKey(Long.valueOf(collectOrderInfo.getOrderInfoWeightId()));
                collectOrderInfo.setOrderInfoWeight(orderInfoWeight);
            }
            if(null != orderDetailDTO){
                if(null != orderDetailDTO.getCarriagePriceUnit()){
                    collectOrderInfo.setCarriagePriceUnit(orderDetailDTO.getCarriagePriceUnit());
                }
            }
            if (CurrentUser.accountIsCompanyAdmin()) {
                collectOrderInfo.setOrderPayPms(true);
            }
            if(null == orderDetailDTO){
                OrderDetailDTO orderDetailDto = orderInfoMapper.selectOrderDetail(code);
                BeanCopyUtil.copyPropertiesIgnoreNull(orderDetailDto, collectOrderInfo);
            }
            if(null!=orderDetailDTO &&!"".equals(orderDetailDTO)){
                BeanCopyUtil.copyPropertiesIgnoreNull(orderDetailDTO, collectOrderInfo);
            }

            Map<String, String> map = orderStateMapper.selectOrderSignForPay(code);
            String codea = null == map.get("codea") ? "" : map.get("codea");
            String codeb = null == map.get("codeb") ? "" : map.get("codeb");
            String sign;
            if (StringUtils.isNotEmpty(codea)){
                sign = codeb + "(" + codea + ")";
            }else {
                sign = codeb;
            }
            collectOrderInfo.setOrderState(sign);
            collectOrderInfo.setOrderSign(sign);
            collectOrderInfo.setToleranceItem("ANXISHU");
            collectOrderInfo.setToleranceItemValue(0d);
            if (null != collectOrderInfo.getTolerantValueCoefficient()){
                collectOrderInfo.setToleranceItem("ANXISHU");
                collectOrderInfo.setToleranceItemValue(collectOrderInfo.getTolerantValueCoefficient());
            } else if (null != collectOrderInfo.getTolerantValueWeight()){
                collectOrderInfo.setToleranceItem("ANDUNSHU");
                collectOrderInfo.setToleranceItemValue(collectOrderInfo.getTolerantValueWeight());
            }
            TOrderNodeDetailVO tOrderNodeDetailVO = tOrderPayRuleMapper.selectNodeOrderPayDetailed(code);
            if (null!=tOrderNodeDetailVO){
                BeanCopyUtil.copyPropertiesIgnoreNull(tOrderNodeDetailVO, collectOrderInfo);
                collectOrderInfo.setZhPaymentFee(tOrderNodeDetailVO.getZhPaymentFee());
                collectOrderInfo.setXhPaymentFee(tOrderNodeDetailVO.getXhPaymentFee());
                collectOrderInfo.setSdPaymentFee(tOrderNodeDetailVO.getSdPaymentFee());
            }
            // 判断运单是否第一次支付
            TOrderPayRule orderPayRule = tOrderPayRuleMapper.selectPaidNode(code);
            if (null != orderPayRule) {
                collectOrderInfo.setFirstPay(false);
                // 判断已支付节点是否是京东支付
                if (null != orderPayRule.getParam1() && DictEnum.JDPLATFORMS.code.equals(orderPayRule.getParam1())) {
                    collectOrderInfo.setPaymentPlatforms(DictEnum.JDPLATFORMS.code);
                }
                if (null != orderPayRule.getParam1() && DictEnum.HXPLATFORMS.code.equals(orderPayRule.getParam1())) {
                    collectOrderInfo.setPaymentPlatforms(DictEnum.HXPLATFORMS.code);
                }
            } else {
                collectOrderInfo.setFirstPay(true);
            }
            BigDecimal userConfirmCarriagePayment = collectOrderInfo.getUserConfirmCarriagePayment();
            BigDecimal sumFee = tOrderPayRuleMapper.selectSumFee(code);
            BigDecimal subtract = userConfirmCarriagePayment.subtract(sumFee);
            collectOrderInfo.setWkPaymentFee(subtract);
            return ResultUtil.ok(collectOrderInfo);
        } catch (RuntimeException e) {
            log.error("节点运单详情获取结算信息失败，参数：" + code + "错误信息：" ,e);
            throw new RuntimeException("节点运单详情获取结算信息失败!");
        }
    }

    /**
     * PC 运营统计
     * hwt
     * @return
     */
    @Override
    public ResultUtil selectOrderStatistics(TOrderStatisticsVO search)
    {
        Map<String, Object> map = new HashMap<>();
        ArrayList<TOrderStatisticsDTO> res = new ArrayList<TOrderStatisticsDTO>();
        //获取企业列表
        TCompanyInfoQuery companyInfoQuery = new TCompanyInfoQuery();
        companyInfoQuery.setPage(1);
        companyInfoQuery.setSize(Integer.MAX_VALUE);
        companyInfoQuery.setCompanyName(search.getCompanyName());
        if (null!=search.getBusinessId() && !"".equals(search.getBusinessId())){
            companyInfoQuery.setBusinessId(search.getBusinessId());
        }
        ResultUtil resultUtil = companyService.selectCompanyByPage(companyInfoQuery);
        ArrayList<LinkedHashMap> al = (ArrayList)resultUtil.getData();

        if(search.getStartDate() != null) {
            Calendar c = Calendar.getInstance();
            c.setTime(search.getStartDate());
            c.add(Calendar.DAY_OF_MONTH, 1);
            search.setNextDate(c.getTime());
        }

        //当日
        TOrderStatisticsVO  voday = new TOrderStatisticsVO();
        BeanUtils.copyProperties(search,voday);
        try {
            if(search.getStartDate() != null){
                voday.setStartDate(DateUtils.forDayDateStart(search.getStartDate()));
                voday.setNextDate(DateUtils.forDayDateEnd(search.getStartDate()));
            }else{
                voday.setStartDate(DateUtils.forDayDateStart(new Date()));
                voday.setNextDate(DateUtils.forDayDateEnd(new Date()));
            }
        } catch (ParseException e) {
            log.error("",e);
        }
        //从冻结变化表中统计最初始的的发单冻结数据
        List<TOrderStatisticsDTO> fdInfo = orderInfoMapper.selectFDInfo(voday);

        //当天完成的运单、运费、调度费
        List<TOrderStatisticsDTO> dayFinishInfo = orderInfoMapper.selectDayFinishInfo(voday);



        //当月
        TOrderStatisticsVO  voMon = new TOrderStatisticsVO();
        BeanUtils.copyProperties(search,voMon);
        try {
            if(search.getStartTime() != null &&  search.getEndTime()!=null){
                voMon.setStartMonthDate(search.getStartTime());
                voMon.setNextDate(search.getEndTime());
            }else if (search.getStartTime()!=null && search.getEndTime()==null){
                voMon.setNextDate(DateUtils.forDateEnd());
            }else if (search.getStartTime()==null && search.getEndTime()!=null){
                voMon.setStartMonthDate(DateUtils.forDateStart());
            } else{
                voMon.setStartMonthDate(DateUtils.forDateStart());
                voMon.setNextDate(DateUtils.forDateEnd());
            }
        } catch (ParseException e) {
            log.error("",e);
        }
        //当月完成的运单、运费、调度费
        List<TOrderStatisticsDTO> monthFinishInfo = orderInfoMapper.selectMonthFinishInfo(voMon);

        //钱包
        List<TOrderStatisticsDTO> walletInfo = orderInfoMapper.selectWalletInfo(search);

        // 判断是否勾选"仅展示已有完成运单的企业"
        ArrayList<LinkedHashMap> companys = new ArrayList<>();
        if (null != search.getDisplayFlag() && search.getDisplayFlag()) {
            al = dispayOnlyFinishedCompany(al, fdInfo, dayFinishInfo, monthFinishInfo);
        }
        if(al != null)
        {
            for (LinkedHashMap companyInfo : al) {
                TOrderStatisticsDTO orderStatisticsDTO = new TOrderStatisticsDTO();
                Integer companyId = (Integer) companyInfo.get("id");
                String companyName = (String) companyInfo.get("companyName");
                orderStatisticsDTO.setCompanyID(companyId);
                orderStatisticsDTO.setCompanyName(companyName);
                orderStatisticsDTO.setCarrierID(search.getCarrierID());
                orderStatisticsDTO.setCarrierName(search.getCarrierName());
                if (null!=companyInfo.get("businessName") && !"".equals(companyInfo.get("businessName"))){
                    orderStatisticsDTO.setBusinessName(companyInfo.get("businessName").toString());
                }

                for(TOrderStatisticsDTO item : fdInfo)
                {
                    if(item.getCompanyID().equals(companyId))
                    {
                        orderStatisticsDTO.setDayFaSourceOfGoods(item.getDayFaSourceOfGoods());//当日发单数
                        orderStatisticsDTO.setDayFaSGFreightTotal(item.getDayFaSGFreightTotal());//当日发单运费
                        orderStatisticsDTO.setDayFaSGTransferFee(item.getDayFaSGTransferFee());//当日发单调度费
                        break;
                    }
                }

                for(TOrderStatisticsDTO item : dayFinishInfo)
                {
                    if(item.getCompanyID().equals(companyId))
                    {
                        orderStatisticsDTO.setDayFinishSourceOfGoods(item.getDayFinishSourceOfGoods());
                        orderStatisticsDTO.setDayFiSGFreightTotal(item.getDayFiSGFreightTotal());//当日完成运费
                        orderStatisticsDTO.setDayFiSGTransferFee(item.getDayFiSGTransferFee());//当日完成调度费
                        break;
                    }
                }

                for(TOrderStatisticsDTO item : monthFinishInfo)
                {
                    if(item.getCompanyID().equals(companyId))
                    {
                        orderStatisticsDTO.setMonthFinishSourceOfGoods(item.getMonthFinishSourceOfGoods());
                        orderStatisticsDTO.setMonFiSGFreightTotal(item.getMonFiSGFreightTotal());//当月完结运费
                        orderStatisticsDTO.setMonFiSGTransferFee(item.getMonFiSGTransferFee());//当月完结调度费
                        break;
                    }
                }

                for(TOrderStatisticsDTO item : walletInfo)
                {
                    if(item.getCompanyID().equals(companyId))
                    {
                        orderStatisticsDTO.setAvailableAmount(item.getAvailableAmount());//可用金额
                        orderStatisticsDTO.setFreezingAmount(item.getFreezingAmount());//冻结金额
                        break;
                    }
                }

                res.add(orderStatisticsDTO);
            }
        }

        map.put("res", res);
        return ResultUtil.ok(map);
    }




    /**
     * PC 运营统计Excel导出
     * hwt
     * @return
     */
    @Override
    public ResultUtil orderStatisticsExcelExport(TOrderStatisticsVO search)
    {
        Map<String, Object> map = new HashMap<>();
        ArrayList<TOrderStatisticsDTO> res = new ArrayList<TOrderStatisticsDTO>();
        //获取企业列表
        TCompanyInfoQuery companyInfoQuery = new TCompanyInfoQuery();
        companyInfoQuery.setPage(1);
        companyInfoQuery.setSize(Integer.MAX_VALUE);
        companyInfoQuery.setCompanyName(search.getCompanyName());
        companyInfoQuery.setCompanyIdArray(search.getCompanyIdArray());
        if (null!=search.getBusinessId() && !"".equals(search.getBusinessId())){
            companyInfoQuery.setBusinessId(search.getBusinessId());
        }
        ResultUtil resultUtil = companyService.selectCompanyByPage(companyInfoQuery);
        ArrayList<LinkedHashMap> al = (ArrayList)resultUtil.getData();

        if(search.getStartDate() != null) {
            Calendar c = Calendar.getInstance();
            c.setTime(search.getStartDate());
            c.add(Calendar.DAY_OF_MONTH, 1);
            search.setNextDate(c.getTime());
        }

        //当日
        TOrderStatisticsVO  voday = new TOrderStatisticsVO();
        BeanUtils.copyProperties(search,voday);
        try {
            if(search.getStartDate() != null){
                voday.setStartDate(DateUtils.forDayDateStart(search.getStartDate()));
                voday.setNextDate(DateUtils.forDayDateEnd(search.getStartDate()));
            }else{
                voday.setStartDate(DateUtils.forDayDateStart(new Date()));
                voday.setNextDate(DateUtils.forDayDateEnd(new Date()));
            }
        } catch (ParseException e) {
            log.error("",e);
        }
        //从冻结变化表中统计最初始的的发单冻结数据
        List<TOrderStatisticsDTO> fdInfo = orderInfoMapper.selectFDInfo(voday);

        //当天完成的运单、运费、调度费
        List<TOrderStatisticsDTO> dayFinishInfo = orderInfoMapper.selectDayFinishInfo(voday);



        //当月
        TOrderStatisticsVO  voMon = new TOrderStatisticsVO();
        BeanUtils.copyProperties(search,voMon);
        try {
            if(search.getStartTime() != null &&  search.getEndTime()!=null){
                voMon.setStartMonthDate(search.getStartTime());
                voMon.setNextDate(search.getEndTime());
            }else if (search.getStartTime()!=null && search.getEndTime()==null){
                voMon.setNextDate(DateUtils.forDateEnd());
            }else if (search.getStartTime()==null && search.getEndTime()!=null){
                voMon.setStartMonthDate(DateUtils.forDateStart());
            } else{
                voMon.setStartMonthDate(DateUtils.forDateStart());
                voMon.setNextDate(DateUtils.forDateEnd());
            }
        } catch (ParseException e) {
            log.error("",e);
        }
        //当月完成的运单、运费、调度费
        List<TOrderStatisticsDTO> monthFinishInfo = orderInfoMapper.selectMonthFinishInfo(voMon);

        //钱包
        List<TOrderStatisticsDTO> walletInfo = orderInfoMapper.selectWalletInfo(search);

        // 判断是否勾选"仅展示已有完成运单的企业"
        ArrayList<LinkedHashMap> companys = new ArrayList<>();
        if (null != search.getDisplayFlag() && search.getDisplayFlag()) {
            al = dispayOnlyFinishedCompany(al, fdInfo, dayFinishInfo, monthFinishInfo);
        }

        if(al != null)
        {
            for (LinkedHashMap companyInfo : al) {
                TOrderStatisticsDTO orderStatisticsDTO = new TOrderStatisticsDTO();
                Integer companyId = (Integer) companyInfo.get("id");
                String companyName = (String) companyInfo.get("companyName");
                orderStatisticsDTO.setCompanyID(companyId);
                orderStatisticsDTO.setCompanyName(companyName);
                orderStatisticsDTO.setCarrierID(search.getCarrierID());
                orderStatisticsDTO.setCarrierName(search.getCarrierName());
                if (null!=companyInfo.get("businessName") && !"".equals(companyInfo.get("businessName"))){
                    orderStatisticsDTO.setBusinessName(companyInfo.get("businessName").toString());
                }
                for(TOrderStatisticsDTO item : fdInfo)
                {
                    if(item.getCompanyID().equals(companyId))
                    {
                        orderStatisticsDTO.setDayFaSourceOfGoods(item.getDayFaSourceOfGoods());//当日发单数
                        orderStatisticsDTO.setDayFaSGFreightTotal(item.getDayFaSGFreightTotal());//当日发单运费
                        orderStatisticsDTO.setDayFaSGTransferFee(item.getDayFaSGTransferFee());//当日发单调度费
                        break;
                    }
                }

                for(TOrderStatisticsDTO item : dayFinishInfo)
                {
                    if(item.getCompanyID().equals(companyId))
                    {
                        orderStatisticsDTO.setDayFinishSourceOfGoods(item.getDayFinishSourceOfGoods());
                        orderStatisticsDTO.setDayFiSGFreightTotal(item.getDayFiSGFreightTotal());//当日完成运费
                        orderStatisticsDTO.setDayFiSGTransferFee(item.getDayFiSGTransferFee());//当日完成调度费
                        break;
                    }
                }

                for(TOrderStatisticsDTO item : monthFinishInfo)
                {
                    if(item.getCompanyID().equals(companyId))
                    {
                        orderStatisticsDTO.setMonthFinishSourceOfGoods(item.getMonthFinishSourceOfGoods());
                        orderStatisticsDTO.setMonFiSGFreightTotal(item.getMonFiSGFreightTotal());//当月完结运费
                        orderStatisticsDTO.setMonFiSGTransferFee(item.getMonFiSGTransferFee());//当月完结调度费
                        break;
                    }
                }

                for(TOrderStatisticsDTO item : walletInfo)
                {
                    if(item.getCompanyID().equals(companyId))
                    {
                        orderStatisticsDTO.setAvailableAmount(item.getAvailableAmount());//可用金额
                        orderStatisticsDTO.setFreezingAmount(item.getFreezingAmount());//冻结金额
                        break;
                    }
                }

                res.add(orderStatisticsDTO);
            }
        }

        String[] headers =
                {
                        "企业名称","业务部门", "资金情况可用余额", "资金情况冻结金额", "当日发单数", "当日发单运费","当日发单调度费","当日完结运单数",
                        "当日完结运费","当日完结调度费","当月累计运单数", "当月累计完结运费","当月累计完结调度费"
                };
        String[] names =
                {
                        "companyName","businessName","availableAmount","freezingAmount","dayFaSourceOfGoods","dayFaSGFreightTotal","dayFaSGTransferFee","dayFinishSourceOfGoods",
                        "dayFiSGFreightTotal","dayFiSGTransferFee","monthFinishSourceOfGoods","monFiSGFreightTotal","monFiSGTransferFee"
                };
        map.put("headers",headers);
        map.put("names",names);
        map.put("list", res);
        return ResultUtil.ok(map);
    }

    /**
    * @description 过滤有完成运单的企业
    * <AUTHOR>
    * @date 2021/4/20 11:50
    * @param
    * @return
    */
    private ArrayList<LinkedHashMap> dispayOnlyFinishedCompany(ArrayList<LinkedHashMap> al,
                                                               List<TOrderStatisticsDTO> fdInfo,
                                                               List<TOrderStatisticsDTO> dayFinishInfo,
                                                               List<TOrderStatisticsDTO> monthFinishInfo) {
        ArrayList<LinkedHashMap> companys = new ArrayList<>();
        Map<Integer, LinkedHashMap> companyMap = new HashMap<>();
        al.forEach((item) -> companyMap.put(Integer.valueOf(item.get("id").toString()), item));
        Map<Integer, TOrderStatisticsDTO> fdMap = fdInfo.stream()
                .collect(Collectors.toMap(TOrderStatisticsDTO::getCompanyID, Function.identity(), (k2, k1) -> k1));
        Map<Integer, TOrderStatisticsDTO> dayMap = dayFinishInfo.stream()
                .collect(Collectors.toMap(TOrderStatisticsDTO::getCompanyID, Function.identity(), (k2, k1) -> k1));
        Map<Integer, TOrderStatisticsDTO> monthMap = monthFinishInfo.stream()
                .collect(Collectors.toMap(TOrderStatisticsDTO::getCompanyID, Function.identity(), (k2, k1) -> k1));
        for (LinkedHashMap company : al) {
            Object companyID = company.get("id");
            TOrderStatisticsDTO fd = fdMap.get(companyID);
            if (null != fd
                    && null != fd.getDayFaSourceOfGoods()
                    && !fd.getDayFaSourceOfGoods().equals(0)) {
                companys.add(company);
            } else {
                TOrderStatisticsDTO day = dayMap.get(companyID);
                if (null != day
                        && null != day.getDayFinishSourceOfGoods()
                        && !day.getDayFinishSourceOfGoods().equals(0)) {
                    companys.add(company);
                } else {
                    TOrderStatisticsDTO month = monthMap.get(companyID);
                    if (null != month
                            && null != month.getMonthFinishSourceOfGoods()
                            && !month.getMonthFinishSourceOfGoods().equals(0)) {
                        companys.add(company);
                    }
                }
            }
        }
        return companys;
    }

    /**
    * @Description 获取运单进度
    * <AUTHOR>
    * @Date   2019/6/25 20:52
    * @Param
    * @Return
    * @Exception
    *
    */
    public List<AppOrderProcessDTO> getAppOrderProcessDTOS(String code) {
        List<AppOrderProcessDTO> appOrderProcess = orderStateMapper.appOrderProcess(code, true);
        TOrderInfoExample example = new TOrderInfoExample();
        TOrderInfoExample.Criteria c = example.createCriteria();
        c.andCodeEqualTo(code);
        List<TOrderInfo> jdlist = orderInfoMapper.selectByExample(example);
        if(null!=jdlist && jdlist.size()>0){
            if (StringUtils.isNotBlank(jdlist.get(0).getPayMethod())){
                if ("NODEPAYFIXED".equals(jdlist.get(0).getPayMethod()) || "NODEPAYPROPORTION".equals(jdlist.get(0).getPayMethod())){
                    appOrderProcess.forEach(appOrderProcessDTO -> {
                        if ("S0903".equals(appOrderProcessDTO.getStateNode()) || "S0902".equals(appOrderProcessDTO.getStateNode()) || "S0901".equals(appOrderProcessDTO.getStateNode())){
                            appOrderProcessDTO.setStateNodeValue(DictEnum.M091.code);
                            appOrderProcessDTO.setStateValue("尾款支付");
                        }

                    });
                }
            }
        }
        ArrayList<String> process = new ArrayList<>();
        for (AppOrderProcessDTO processDTO: appOrderProcess){
            process.add(processDTO.getStateNodeValue());
        }
        if (null != appOrderProcess && appOrderProcess.size() > 0){
            AppOrderProcessDTO appOrderProcessDTO = appOrderProcess.get(appOrderProcess.size() - 1);
            //最近进度是召回完成
            if (null != appOrderProcessDTO && null != appOrderProcessDTO.getStateNodeValue()
                    && appOrderProcessDTO.getStateNodeValue().equals(DictEnum.M095.code)){
                for (int i = appOrderProcess.size() - 1; i >= 0; i--){
                    AppOrderProcessDTO orderProcessDTO = appOrderProcess.get(i);
                    if (orderProcessDTO.getStateNodeValue().equals(DictEnum.M050.code)){
                       break;
                    }
                    if (!orderProcessDTO.getStateNodeValue().equals(DictEnum.M050.code)){
                        appOrderProcess.remove(i);
                    }
                }
                if (process.contains(DictEnum.M050.code)){
                    if (!process.contains(DictEnum.M030.code)){
                        AppOrderProcessDTO processDTO = new AppOrderProcessDTO();
                        processDTO.setStateNodeValue(DictEnum.M030.code);
                        appOrderProcess.add(processDTO);
                    }
                    if (!process.contains(DictEnum.M040.code)){
                        AppOrderProcessDTO processDTO = new AppOrderProcessDTO();
                        processDTO.setStateNodeValue(DictEnum.M040.code);
                        appOrderProcess.add(processDTO);
                    }
                }else if (process.contains(DictEnum.M040.code)){
                    if (!process.contains(DictEnum.M030.code)){
                        AppOrderProcessDTO processDTO = new AppOrderProcessDTO();
                        processDTO.setStateNodeValue(DictEnum.M030.code);
                        appOrderProcess.add(processDTO);
                    }
                }
            }else {
                //最近进度不是召回完成
                //不是节点支付
                if(!process.contains(DictEnum.M091.code) && !process.contains(DictEnum.M051.code) && !process.contains(DictEnum.M041.code) && !process.contains(DictEnum.M031.code)){
                    if (process.contains(DictEnum.M090.code)){
                        if (!process.contains(DictEnum.M030.code)){
                            AppOrderProcessDTO processDTO = new AppOrderProcessDTO();
                            processDTO.setStateNodeValue(DictEnum.M030.code);
                            appOrderProcess.add(processDTO);
                        }
                        if (!process.contains(DictEnum.M040.code)){
                            AppOrderProcessDTO processDTO = new AppOrderProcessDTO();
                            processDTO.setStateNodeValue(DictEnum.M040.code);
                            appOrderProcess.add(processDTO);
                        }
                    }else if (process.contains(DictEnum.M050.code)){
                        if (!process.contains(DictEnum.M030.code)){
                            AppOrderProcessDTO processDTO = new AppOrderProcessDTO();
                            processDTO.setStateNodeValue(DictEnum.M030.code);
                            appOrderProcess.add(processDTO);
                        }
                        if (!process.contains(DictEnum.M040.code)){
                            AppOrderProcessDTO processDTO = new AppOrderProcessDTO();
                            processDTO.setStateNodeValue(DictEnum.M040.code);
                            appOrderProcess.add(processDTO);
                        }
                    }else if (process.contains(DictEnum.M040.code)){
                        if (!process.contains(DictEnum.M030.code)){
                            AppOrderProcessDTO processDTO = new AppOrderProcessDTO();
                            processDTO.setStateNodeValue(DictEnum.M030.code);
                            appOrderProcess.add(processDTO);
                        }
                    }
                }else {
                    //节点支付
                    if (process.contains(DictEnum.M091.code)){
                        if (!process.contains(DictEnum.M030.code)){
                            AppOrderProcessDTO processDTO = new AppOrderProcessDTO();
                            processDTO.setStateNodeValue(DictEnum.M030.code);
                            appOrderProcess.add(processDTO);
                        }
                        if (!process.contains(DictEnum.M040.code)){
                            AppOrderProcessDTO processDTO = new AppOrderProcessDTO();
                            processDTO.setStateNodeValue(DictEnum.M040.code);
                            appOrderProcess.add(processDTO);
                        }
                    }else if (process.contains(DictEnum.M050.code)){
                        if (!process.contains(DictEnum.M030.code)){
                            AppOrderProcessDTO processDTO = new AppOrderProcessDTO();
                            processDTO.setStateNodeValue(DictEnum.M030.code);
                            appOrderProcess.add(processDTO);
                        }
                        if (!process.contains(DictEnum.M040.code)){
                            AppOrderProcessDTO processDTO = new AppOrderProcessDTO();
                            processDTO.setStateNodeValue(DictEnum.M040.code);
                            appOrderProcess.add(processDTO);
                        }
                    }else if (process.contains(DictEnum.M040.code)){
                        if (!process.contains(DictEnum.M030.code)){
                            AppOrderProcessDTO processDTO = new AppOrderProcessDTO();
                            processDTO.setStateNodeValue(DictEnum.M030.code);
                            appOrderProcess.add(processDTO);
                        }
                    }

                    for (AppOrderProcessDTO appOrders: appOrderProcess){
                        if (null != appOrders.getStateNodeValue()
                                && appOrders.getStateNodeValue().equals(DictEnum.M090.code)){
                            appOrderProcess.remove(appOrders);
                            break;
                        }
                    }
                }


            }
        }
        Collections.sort(appOrderProcess, new Comparator<AppOrderProcessDTO>() {
            @Override
            public int compare(AppOrderProcessDTO o1, AppOrderProcessDTO o2) {
                return o1.getStateNodeValue().compareTo(o2.getStateNodeValue());
            }
        });

        ArrayList<AppOrderProcessDTO> appOrderProcessResult = new ArrayList<AppOrderProcessDTO>();
        for (AppOrderProcessDTO processDTO: appOrderProcess){
            boolean isExist = false;
            for(AppOrderProcessDTO item : appOrderProcessResult)
            {
                if(item.getStateNodeValue() == processDTO.getStateNodeValue())
                {
                    isExist = true;
                    break;
                }
            }

            boolean isNeed = processDTO.getStateNodeValue().equals(DictEnum.M010.code)
                    || processDTO.getStateNodeValue().equals(DictEnum.M011.code)
                    || processDTO.getStateNodeValue().equals(DictEnum.M020.code)
                    || processDTO.getStateNodeValue().equals(DictEnum.M030.code)
                    || processDTO.getStateNodeValue().equals(DictEnum.M040.code)
                    || processDTO.getStateNodeValue().equals(DictEnum.M050.code)
                    || processDTO.getStateNodeValue().equals(DictEnum.M090.code)
                    || processDTO.getStateNodeValue().equals(DictEnum.M130.code)
                    || processDTO.getStateNodeValue().equals(DictEnum.M031.code)
                    || processDTO.getStateNodeValue().equals(DictEnum.M041.code)
                    || processDTO.getStateNodeValue().equals(DictEnum.M051.code)
                    || processDTO.getStateNodeValue().equals(DictEnum.M091.code);
            if(isExist || !isNeed){
                continue;
            }

            appOrderProcessResult.add(processDTO);
        }

        return appOrderProcessResult;
    }

    /**
     * @Description: 高经理说，运单进度中没有时间的。就不再页面展示
     * @Author: Yan
     * @Date: 2019/7/11/011 14:56
     * @Param: appOrderProcess
     */
    private List<AppOrderProcessDTO> formatProcess(List<AppOrderProcessDTO> appOrderProcess){
        List<AppOrderProcessDTO> list = new ArrayList<>();
        for (AppOrderProcessDTO orderProcess : appOrderProcess) {
            if (orderProcess.getOperateTime() != null) {
                if (orderProcess.getStateNode().equals(DictEnum.S0951.code) || orderProcess.getStateNode().equals(DictEnum.S0952.code)){
                    continue;
                }
                list.add(orderProcess);
            }
        }
        return list;
    }

    /**
     * PC端 运单管理: 运单轨迹
     * Yan
     * @param code
     * @return
     */
    @Override
    public ResultUtil selectOrderTrajectory(String code, boolean isPC) {
        Map<String, Object> map = new HashMap<>();
        if (isPC) {
            // PC获取运单进度
            List<AppOrderProcessDTO> pcOrderProcess = orderStateMapper.appOrderProcess(code, isPC);
            List<AppOrderProcessDTO> pcOrderProcessDTOS = pcProcess(pcOrderProcess);
            map.put("process", pcOrderProcessDTOS);
        } else {
            // APP获取运单进度
            List<AppOrderProcessDTO> appOrderProcess = orderStateMapper.appOrderProcess(code, isPC);
            TOrderInfoExample example = new TOrderInfoExample();
            TOrderInfoExample.Criteria c = example.createCriteria();
            c.andCodeEqualTo(code);
            List<TOrderInfo> jdlist = orderInfoMapper.selectByExample(example);
            if(null!=jdlist && jdlist.size()>0){
                if (StringUtils.isNotBlank(jdlist.get(0).getPayMethod())){
                    if ("NODEPAYFIXED".equals(jdlist.get(0).getPayMethod()) || "NODEPAYPROPORTION".equals(jdlist.get(0).getPayMethod())){
                        appOrderProcess.forEach(appOrderProcessDTO -> {
                            if ("S0902".equals(appOrderProcessDTO.getStateNode()) || "S0901".equals(appOrderProcessDTO.getStateNode()) || "S0903".equals(appOrderProcessDTO.getStateNode())){
                                appOrderProcessDTO.setStateValue("尾款支付");
                            }

                        });
                    }
                }
            }
            map.put("process", appOrderProcess);
        }
        // 获取司机装货卸货签到时间和地点
        List<Map<String, Object>> driverOperatePositionTime = orderStateMapper.getDriverOperatePositionTime(code);
        map.put("driverOperate", driverOperatePositionTime);
        // 获取运单车辆轨迹
       // List<TOrderVehicleTrajectory> orderTrajectories = orderVehicleTrajectoryMapper.pcOrderProcess(code);

        // 获取运单车辆轨迹 从mongodb中取
        List<TrajectoryMongo> orderTrajectories =  trajectoryMongoDao.getAllByOrderCode(code);
        map.put("trajectories", orderTrajectories);

        // 获取起始点
        Map<String, Object> lineInfoByOrderCode = orderInfoMapper.getLineInfoByOrderCode(code);
        map.put("initialPoint", lineInfoByOrderCode );

        // 获取起始点和终点之间的一些坐标点
        //如果车辆没有行驶轨迹，则只获取起点跟终点的经纬度
        List<String[]> steps = new ArrayList<>();
        for (TrajectoryMongo orderTrajectory : orderTrajectories) {
            String[] str =  new String[2];
            if(null != orderTrajectory.getLongitude()){
                str[0] = String.format("%.6f", Double.parseDouble(orderTrajectory.getLongitude()));
            }
            if(null != orderTrajectory.getLatitudes()){
                str[1] = String.format("%.6f", Double.parseDouble(orderTrajectory.getLatitudes()));
            }
            if(null != orderTrajectory.getLatitudes() && null != orderTrajectory.getLongitude()){
                steps.add(str);
            }
        }
        //格式化终点经纬度
        String endCoordinates = lineInfoByOrderCode.get("endCoordinates").toString();
        if(null != endCoordinates){
            String[] split = endCoordinates.split(",");
            if(steps.size() == 1){
                String[] str =  new String[2];
                if(null != split[0]){
                    str[0] = String.format("%.6f", Double.parseDouble(split[0]));
                }
                if(null != split[1]){
                    str[1] = String.format("%.6f", Double.parseDouble(split[1]));
                }
                if(null != split[0] && null != split[1]){
                    steps.add(str);
                }
            }
        }
        map.put("steps", steps);

        //格式化起点经纬度
        String fromCoordinates = lineInfoByOrderCode.get("fromCoordinates").toString();
        if(null != fromCoordinates){
            String[] split = fromCoordinates.split(",");
            ArrayList<String> list = new ArrayList<>();
            for (int i = 0; i < split.length; i++) {
                list.add(split[i]);
            }
            map.put("fromCoordinates", list);
        }

        Map<String, Object> mape = new HashMap<>();
        List<Map<String, Object>> driverSignInPosition = orderStateMapper.getDriverSignInPosition(code);
        if (null != driverOperatePositionTime && driverSignInPosition.size() != 0) {
            for (Map<String, Object> stringObjectMap : driverSignInPosition) {
                String stateNodeValue = stringObjectMap.get("stateNodeValue").toString();
                if (DictEnum.S0302.code.equals(stateNodeValue) && null != stringObjectMap.get("operateGeographyPosition")) {
                    mape.put("driverStart", stringObjectMap.get("operateGeographyPosition").toString());
                } else if (DictEnum.S0402.code.equals(stateNodeValue) && null != stringObjectMap.get("operateGeographyPosition")){
                    mape.put("driverEnd", stringObjectMap.get("operateGeographyPosition").toString());
                }
            }
        }

        // 判断运单轨迹是否异常， 返回 null 证明没有异常
        Integer integer = orderAbnormalMapper.judgeTrajectory(code);
        if (integer != null) {
            List<Map<String, Object>> tOrderAbnormals = orderAbnormalMapper.selectOrderTrajectory(code);
            List<String> endPosition = new ArrayList<>();
            for (Map<String, Object> tOrderAbnormal : tOrderAbnormals) {
                String abnormalType = tOrderAbnormal.get("abnormalType").toString();
                if (abnormalType.equals(DictEnum.QIANDAOYC.code)) {
                    // 轨迹异常
                    endPosition.add(tOrderAbnormal.get("abnormalDescription").toString());
                }
            }
            if (endPosition.size() > 1) {
                mape.put("carStart", endPosition.get(0));
                mape.put("carEnd", endPosition.get(endPosition.size() - 1));
            } else if (endPosition.size() == 1) {
                mape.put("carStart", endPosition.get(0));
            }
        }
        map.put("loadingAbnormals", mape);

        //获取车辆信息
        OrderDetailDTO orderDetailDTO = orderInfoMapper.pcSelectOrderDetailed(code);
        map.put("orderDetail", orderDetailDTO);

        return ResultUtil.ok(map);
    }

    private List<AppOrderProcessDTO> pcProcess(List<AppOrderProcessDTO> orderProcess){
        AppOrderProcessDTO[] aop = new AppOrderProcessDTO[10];
        for (AppOrderProcessDTO orderProces : orderProcess) {
            String nodeVule = orderProces.getStateNodeValue();
            switch (nodeVule) {
                case "M011":
                    aop[0] = orderProces;
                    break;
                case "M020":
                    aop[0] = orderProces;
                    break;
                case "M030":
                    aop[1] = orderProces;
                    break;
                case "M031":
                    aop[2] = orderProces;
                    break;
                case "M040":
                    aop[3] = orderProces;
                    break;
                case "M041":
                    aop[4] = orderProces;
                    break;
                case "M050":
                    aop[5] = orderProces;
                    break;
                case "M051":
                    aop[6] = orderProces;
                    break;
                case "M090":
                    aop[7] = orderProces;
                    break;
                case "M091":
                    aop[8] = orderProces;
                    break;
                case "M130":
                    aop[9] = orderProces;
                    break;
                default:
                    break;
            }
        }
        List<AppOrderProcessDTO> collect = new ArrayList<>();
        for (AppOrderProcessDTO appOrderProcessDTO : aop) {
            if (null != appOrderProcessDTO) {
                collect.add(appOrderProcessDTO);
            }
        }
        if (DictEnum.M095.code.equals(orderProcess.get(orderProcess.size() - 1).getStateNodeValue())) {
            collect.remove(collect.size() - 1);
        }
        return collect;
    }

    /**
     * PC端 运单详情： 查询运单合同
     * Yan
     * @param code
     * @return
     */
    @Override
    public ResultUtil selectOrderContract(String code) {
        Map<String, Object> map = new HashMap<>();
        TOrderContract orderContracts = orderContractMapper.pcSelectOrderContract(code);

        if (null == orderContracts) {
            orderContracts = new TOrderContract();
            orderContracts.setParam1("");
            orderContracts.setParam2("");
            orderContracts.setSignFinishTime(null);
        }

        map.put("DriverContracts", orderContracts);

        Map<String,Object> mapd = new HashMap<>();
        TOrderInfo tOrderInfo = orderInfoMapper.selectOrderByCode(code);
        if (null != tOrderInfo.getOrderPayStatus() && DictEnum.M095.code.equals(tOrderInfo.getOrderPayStatus())) {

        } else {
            List<TOrderContract> tOrderContracts = orderContractMapper.pcSelectOrderTXContract(code);
            for (TOrderContract orderContract : tOrderContracts) {
                Map<String, Object> param = new HashMap<>();
                if (null != orderContract.getContractType() && StringUtils.isNotBlank(orderContract.getContractType())) {
                    if (DictEnum.AGENTTXXY.code.equals(orderContract.getContractType())) {
                        param.put("url", orderContract.getParam2());
                        param.put("signFinishTime", orderContract.getSignFinishTime());
                        mapd.put(DictEnum.AGENTTXXY.code,param);
                    }else if ("YFKTXXY".equals(orderContract.getContractType())) {
                        param.put("url", orderContract.getParam2());
                        param.put("signFinishTime", orderContract.getSignFinishTime());
                        mapd.put("YFKTXXY",param);
                    } else if (DictEnum.DRIVERTXXY.code.equals(orderContract.getContractType())
                            || DictEnum.TXXY.code.equals(orderContract.getContractType())
                            || DictEnum.ANYSIGNDRIVERSKPZ.code.equals(orderContract.getContractType())
                            || NetSignEnum.NETSIGN_SKPZ.code.equals(orderContract.getContractType())) {
                        param.put("url", orderContract.getParam2());
                        param.put("signFinishTime", orderContract.getSignFinishTime());
                        mapd.put(DictEnum.DRIVERTXXY.code, param);
                    } else if (DictEnum.CAPTAINTXXY.code.equals(orderContract.getContractType())) {
                        param.put("url", orderContract.getParam2());
                        param.put("signFinishTime", orderContract.getSignFinishTime());
                        mapd.put(DictEnum.CAPTAINTXXY.code, param);
                    }
                }
            }
        }
        map.put("OrderTXContracts", mapd);
        //Map<String, Object> map1 = orderInfoMapper.selectCompanyContractByOrderCode(code);
        Map<String, Object> map1 = projectCarrierAPI.selectContractPhotoByOrderCode(code);
        if(null == map1)
        {
            map1 = new HashMap<String, Object>();
        }
        map.put("CompanyContracts", map1);

        TOrderContract orderThreegoods = orderContractMapper.pcSelectOrderThreegoods(code);
        map.put("orderThreegoods",orderThreegoods);
        List<TEtcInvoiceDetails> tEtcInvoiceDetails = orderContractMapper.selectEtcInvoice(code);
        map.put("etcInvoice",tEtcInvoiceDetails);
        return ResultUtil.ok(map);
    }

    /**
     * PC端 运单管理： 查询运单资金变动
     * @param code
     * @return
     */
    @Override
    public ResultUtil selectOrderFundChanges(String code) {
        TOrderInfo tOrderInfo = orderInfoMapper.selectOrderByCode(code);
        // 已计划运单无资金流动
        if (null != tOrderInfo.getOrderExecuteStatus() && !DictEnum.M010.code.equals(tOrderInfo.getOrderExecuteStatus())) {
            TOrderCastChanges tOrderCastChanges = orderCastChangesMapper.selectByNewOne(code);
            List<OrderFundChangesDTO> accountFundProcess = new ArrayList<>();
            if (null != tOrderInfo && null != tOrderInfo.getPackStatus()) {
                // 新版打包单支付
                if (null != tOrderCastChanges.getCapitalTransferPattern()
                        && StringUtils.isNotBlank(tOrderCastChanges.getCapitalTransferPattern())
                        && tOrderInfo.getPackStatus().equals("1")) {
                    accountFundProcess = orderCastChangesMapper.pcNewSelectPackAccountFundChanges(code);
                } else if (tOrderInfo.getPackStatus().equals("0")) {
                    accountFundProcess = orderCastChangesMapper.pcSelectAccountFundChanges(code);
                } else if (tOrderInfo.getPackStatus().equals("1")){
                    accountFundProcess = orderCastChangesMapper.pcSelectPackAccountFundChanges(code);
                }
            }
            List<FundFlowDTO> ffd = new ArrayList<>();
            if (null != accountFundProcess && accountFundProcess.size() > 0){
                OrderFundChangesDTO orderFundChanges = accountFundProcess.get(accountFundProcess.size() - 1);
                if (orderFundChanges.getItemCode().equals(TradeType.Recall.code)
                        || orderFundChanges.getItemCode().equals(TradeType.DBRecall.code)){
                    for (int i = accountFundProcess.size() - 1; i >= 0; i--){
                        OrderFundChangesDTO orderFundChangesDTO = accountFundProcess.get(i);
                        if (orderFundChangesDTO.getItemCode().equals(TradeType.CollectionOrder.code)
                                || orderFundChangesDTO.getItemCode().equals(TradeType.ReCollectionOrder.code)){
                            break;
                        }
                        if (!orderFundChangesDTO.getItemCode().equals(TradeType.CollectionOrder.code)
                                && !orderFundChangesDTO.getItemCode().equals(TradeType.ReCollectionOrder.code)){
                            accountFundProcess.remove(i);
                        }
                    }
                }
            }

            for (int i = 0; i < accountFundProcess.size(); i++) {
                FundFlowDTO fundFlow = new FundFlowDTO();
                OrderFundChangesDTO fundProcess = accountFundProcess.get(i);
                fundFlow.setNodeTitle(fundProcess.getItemValue());
                fundFlow.setNodeTime(fundProcess.getCreateTime());
                fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
                fundFlow.setPayCode(fundProcess.getPayCode());
                if(TradeType.Invoice.code.equals(fundProcess.getItemCode())) {
                    // 发单冻结
                    fundFlow.setPayer(fundProcess.getBParySubAccount());
                    fundFlow.setMoney(fundProcess.getTotalFee());
                    fundFlow.setFundType("总费用");
                    ffd.add(fundFlow);
                }else if (TradeType.CollectionOrder.code.equals(fundProcess.getItemCode())) {
                    // 收单冻结
                    fundFlow.setPayer(fundProcess.getBParySubAccount());
                    fundFlow.setMoney(fundProcess.getTotalFee());
                    fundFlow.setFundType("总费用");
                    FundFlowDTO fundFlowDTO = new FundFlowDTO();
                    for(FundFlowDTO item : ffd)
                    {
                        if(item.getNodeTitle().equals(fundFlow.getNodeTitle()))
                        {
                            fundFlowDTO = item;
                        }
                    }
                    ffd.remove(fundFlowDTO);
                    ffd.add(fundFlow);
                }else if (TradeType.PayMent.code.equals(fundProcess.getItemCode())) {
                    fundFlow.setNodeTitle(fundProcess.getPayState());
                    // 支付
                    if (DictEnum.YFRZ.code.equals(fundProcess.getPayCode())) {
                        fundFlow.setPayer(fundProcess.getBParySubAccount());
                        fundFlow.setPayee(fundProcess.getCParySubAccount());
                        fundFlow.setMoney(fundProcess.getCarriageFee());
                        fundFlow.setFundType("运费");
                        FundFlowDTO fundFlowDTO = new FundFlowDTO();
                        for(FundFlowDTO item : ffd)
                        {
                            if(item.getNodeTitle().equals(fundFlow.getNodeTitle()) && item.getPayCode().equals(fundFlow.getPayCode()))
                            {
                                fundFlowDTO = item;
                            }
                        }
                        ffd.remove(fundFlowDTO);
                        ffd.add(fundFlow);
                    } else if (DictEnum.DDFRZ.code.equals(fundProcess.getPayCode())) {
                        fundFlow.setPayer(fundProcess.getBParySubAccount());
                        fundFlow.setPayee(fundProcess.getPParySubAccount());
                        fundFlow.setMoney(fundProcess.getDispatchFee());
                        fundFlow.setFundType("调度费");
                        FundFlowDTO fundFlowDTO = new FundFlowDTO();
                        for(FundFlowDTO item : ffd)
                        {
                            if(item.getNodeTitle().equals(fundFlow.getNodeTitle()) && item.getPayCode().equals(fundFlow.getPayCode()))
                            {
                                fundFlowDTO = item;
                            }
                        }
                        ffd.remove(fundFlowDTO);
                        ffd.add(fundFlow);
                    } else if (DictEnum.FWFRZ.code.equals(fundProcess.getPayCode())) {
                        fundFlow.setPayer(fundProcess.getCParySubAccount());
                        fundFlow.setPayee(fundProcess.getAParySubAccount());
                        fundFlow.setMoney(fundProcess.getServiceFee());
                        fundFlow.setFundType("服务费");
                        FundFlowDTO fundFlowDTO = new FundFlowDTO();
                        for(FundFlowDTO item : ffd)
                        {
                            if(item.getNodeTitle().equals(fundFlow.getNodeTitle()) && item.getPayCode().equals(fundFlow.getPayCode()))
                            {
                                fundFlowDTO = item;
                            }
                        }
                        ffd.remove(fundFlowDTO);
                        ffd.add(fundFlow);
                    } else if (DictEnum.OYFRZ.code.equals(fundProcess.getPayCode())) {
                        fundFlow.setPayer(fundProcess.getCParySubAccount());
                        fundFlow.setPayee(fundProcess.getOParySubAccount());
                        fundFlow.setMoney(fundProcess.getCarriageFee().subtract(fundProcess.getServiceFee()));
                        fundFlow.setFundType("运费");
                        FundFlowDTO fundFlowDTO = new FundFlowDTO();
                        for(FundFlowDTO item : ffd)
                        {
                            if(item.getNodeTitle().equals(fundFlow.getNodeTitle()) && item.getPayCode().equals(fundFlow.getPayCode()))
                            {
                                fundFlowDTO = item;
                            }
                        }
                        ffd.remove(fundFlowDTO);
                        ffd.add(fundFlow);
                    }
                } else if (TradeType.TX.code.equals(fundProcess.getItemCode())) {
                    // 提现
                    fundFlow.setPayer(fundProcess.getCParySubAccount());
                    fundFlow.setPayee(fundProcess.getBankNo());
                    BigDecimal serviceFee  = null == fundProcess.getServiceFee() ? BigDecimal.ZERO : fundProcess.getServiceFee();
                    fundFlow.setMoney(fundProcess.getCarriageFee().subtract(serviceFee));
                    fundFlow.setFundType("运费");
                    FundFlowDTO fundFlowDTO = new FundFlowDTO();
                    for(FundFlowDTO item : ffd)
                    {
                        if(item.getNodeTitle().equals(fundFlow.getNodeTitle()))
                        {
                            fundFlowDTO = item;
                        }
                    }
                    ffd.remove(fundFlowDTO);
                    ffd.add(fundFlow);
                }
            }

            if(null != tOrderInfo && tOrderInfo.getPackStatus().equals("1") && (null == tOrderCastChanges.getCapitalTransferPattern() || StringUtils.isBlank(tOrderCastChanges.getCapitalTransferPattern()))) {
                log.info(tOrderCastChanges.getCapitalTransferPattern());
                for (int i = 0; i < accountFundProcess.size(); i++) {
                    FundFlowDTO fundFlow = new FundFlowDTO();
                    OrderFundChangesDTO fundProcess = accountFundProcess.get(i);
                    fundFlow.setNodeTitle(fundProcess.getItemValue());
                    fundFlow.setNodeTime(fundProcess.getCreateTime());
                    fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());

                    if (TradeType.DBPayMent.code.equals(fundProcess.getItemCode())) {
                        fundFlow.setNodeTitle(fundProcess.getPayState());
                        // 支付
                        if (DictEnum.YFRZ.code.equals(fundProcess.getPayCode())) {
                            fundFlow.setPayer(fundProcess.getBParySubAccount());
                            fundFlow.setPayee(fundProcess.getCParySubAccount());
                            fundFlow.setMoney(fundProcess.getCarriageFee());
                            fundFlow.setFundType("运费");
                            FundFlowDTO fundFlowDTO = new FundFlowDTO();
                            for(FundFlowDTO item : ffd)
                            {
                                if(item.getNodeTitle().equals(fundFlow.getNodeTitle()))
                                {
                                    fundFlowDTO = item;
                                }
                            }
                            ffd.remove(fundFlowDTO);
                            ffd.add(fundFlow);
                        } else if (DictEnum.DDFRZ.code.equals(fundProcess.getPayCode())) {
                            fundFlow.setPayer(fundProcess.getBParySubAccount());
                            fundFlow.setPayee(fundProcess.getPParySubAccount());
                            fundFlow.setMoney(fundProcess.getDispatchFee());
                            fundFlow.setFundType("调度费");
                            FundFlowDTO fundFlowDTO = new FundFlowDTO();
                            for(FundFlowDTO item : ffd)
                            {
                                if(item.getNodeTitle().equals(fundFlow.getNodeTitle()))
                                {
                                    fundFlowDTO = item;
                                }
                            }
                            ffd.remove(fundFlowDTO);
                            ffd.add(fundFlow);
                        }
                    } else if (TradeType.DBTX.code.equals(fundProcess.getItemCode())) {
                        // 提现
                        fundFlow.setPayer(fundProcess.getCParySubAccount());
                        fundFlow.setPayee(fundProcess.getBankNo());
                        fundFlow.setMoney(fundProcess.getCarriageFee());
                        fundFlow.setFundType("运费");
                        FundFlowDTO fundFlowDTO = new FundFlowDTO();
                        for(FundFlowDTO item : ffd)
                        {
                            if(item.getNodeTitle().equals(fundFlow.getNodeTitle()))
                            {
                                fundFlowDTO = item;
                            }
                        }
                        ffd.remove(fundFlowDTO);
                        ffd.add(fundFlow);
                    }
                }
            }

            return ResultUtil.ok(ffd);
        } else {
            return ResultUtil.ok();
        }

    }

    /**
     * 运单管理-收单: 根据32CODE查询运单基础信息
     * Yan
     * @param code
     * @return
     */
    @Override
    public ResultUtil acceptOrder(String code) throws RuntimeException {
        Integer userAccountId = CurrentUser.getUserAccountId();
        Boolean isPay = orderInfoMapper.judgeUserWhetherHavePay(code, userAccountId);
        Boolean aBoolean = orderCastCalcSnapshotMapper.judgeOrderIsIncome(code);
        OrderDetailDTO collectOrderInfo = new OrderDetailDTO();
        if (aBoolean) {
            try {
                // 根据32CODE查询运单基础信息: 为收单的查原始信息
                collectOrderInfo = orderInfoMapper.selectCollectOrderBasisInfo(code);
            } catch (RuntimeException e) {
                throw new RuntimeException("Y3001--运单收单查询，收单原始数据失败。参数：" + code + "错误信息:" + e);
            }
        } else {
            try {
                // 重新收单的查快照表
                collectOrderInfo = orderCastCalcSnapshotMapper.appSelectSnapsByOrderCode(code);
                if (null != collectOrderInfo.getRuleName()) {
                    if (null != collectOrderInfo.getTolerantValueWeight()) {
                        collectOrderInfo.setToleranceItemValue(collectOrderInfo.getTolerantValueWeight());
                        collectOrderInfo.setToleranceItem(DictEnum.ANDUNSHU.code);
                    } else if (null != collectOrderInfo.getTolerantValueCoefficient()){
                        collectOrderInfo.setToleranceItemValue(collectOrderInfo.getTolerantValueCoefficient());
                        collectOrderInfo.setToleranceItem(DictEnum.ANXISHU.code);
                    }
                }
            } catch (RuntimeException e) {
                throw new RuntimeException("Y3002--运单收单查询，收单快照数据失败。参数：" + code + "错误信息:" + e);
            }
        }
        if ("NODEPAYPROPORTION".equals(collectOrderInfo.getPayMethod()) || "NODEPAYFIXED".equals(collectOrderInfo.getPayMethod()) ){
            TOrderPayRule tOrderPayRule = tOrderPayRuleMapper.selectByCodeAndStatus(collectOrderInfo.getCode());
            if(null!=tOrderPayRule ){
                collectOrderInfo.setPayNodeType(tOrderPayRule.getPayNodeType());
            }
        }
        Map<String, String> map = orderStateMapper.selectOrderSign(code);
        String codea = map.get("codea");
        String codeb = map.get("codeb");
        String sign = "";
        if (codeb != null || codeb != "") {
            sign = codea + "(" + codeb + ")";
        } else {
            sign = codea;
        }
        collectOrderInfo.setOrderSign(sign);
        collectOrderInfo.setReceiverOrderTime(new Date());
        List<Map<String, Object>> maps = formatFee(collectOrderInfo);
        collectOrderInfo.setPayment(maps);
        collectOrderInfo.setIsPay(isPay);

        // 查询合同
        List<Map<String, Object>> contractlist = orderContractMapper.appSelectOrderContract(code);
        // 合同照片
        for (Map<String, Object> me:contractlist) {
            if (null != me &&  null != me.get("param2")) {
                String photo = me.get("param2").toString();
                String[] split = photo.split(",");
                List<String> contract = new ArrayList<>();
                for (String spl : split) {
                    if (StringUtils.isNotBlank(spl)) {
                        contract.add(spl);
                    }
                }
                collectOrderInfo.setContractlist(contract);
            }
        }
        //添加资金转移方式
        TOrderCastChanges tOrderCastChanges = orderCastChangesMapper.selectByNewOne(code);
        if(null!=tOrderCastChanges && StringUtils.isNotBlank(tOrderCastChanges.getCapitalTransferType())){
            collectOrderInfo.setCapitalTransferType(tOrderCastChanges.getCapitalTransferType());
        }
        TOrderInfo tOrderInfo = orderInfoMapper.selectOrderByCode(code);
        TOrderInfoDetail tOrderInfoDetail = tOrderInfoDetailMapper.selectByOrderId(tOrderInfo.getId());
        collectOrderInfo.setLoadingCarPhoto(tOrderInfoDetail.getLoadingCarPhoto());
        if(null != tOrderInfoDetail.getLoadingCarPhotoInfo() && StringUtils.isNotBlank(tOrderInfoDetail.getLoadingCarPhotoInfo())) {
            collectOrderInfo.setLoadingCarPhotoInfo(tOrderInfoDetail.getLoadingCarPhotoInfo());
        }
        collectOrderInfo.setUnloadCarPhoto(tOrderInfoDetail.getUnloadCarPhoto());
        if(null != tOrderInfoDetail.getUnloadCarPhotoInfo() && StringUtils.isNotBlank(tOrderInfoDetail.getUnloadCarPhotoInfo())){
            collectOrderInfo.setUnloadCarPhotoInfo(tOrderInfoDetail.getUnloadCarPhotoInfo());
        }
        return ResultUtil.ok(collectOrderInfo);
    }
    /**
     * 运单管理-收单-重新收单: PC根据32CODE查询运单基础信息
     * Yan
     * @param code
     * @return
     */
    @Override
    public ResultUtil pcIncomOrder(String code) {
        CollectOrderInfoDTO collectOrderInfoDTO = orderInfoMapper.pcGetCollectOrderBasisInfo(code);
        if (null == collectOrderInfoDTO.getFixCutFee()) {
            collectOrderInfoDTO.setFixCutFee(new BigDecimal("0"));
        }
        if (null == collectOrderInfoDTO.getGoodsCutImpurities()) {
            collectOrderInfoDTO.setGoodsCutImpurities(0.0);
        }
        if (null == collectOrderInfoDTO.getGoodsCutWater()) {
            collectOrderInfoDTO.setGoodsCutWater(0.0);
        }
        if (null == collectOrderInfoDTO.getOtherCutFee1()) {
            collectOrderInfoDTO.setOtherCutFee1(new BigDecimal("0"));
        }
        if (null == collectOrderInfoDTO.getOtherCutFee2()) {
            collectOrderInfoDTO.setOtherCutFee2(new BigDecimal("0"));
        }
        if (null == collectOrderInfoDTO.getOtherCutFee3()) {
            collectOrderInfoDTO.setOtherCutFee3(new BigDecimal("0"));
        }
        if (null == collectOrderInfoDTO.getOtherCutFee4()) {
            collectOrderInfoDTO.setOtherCutFee4(new BigDecimal("0"));
        }
        SysParam ordereditpmsmark = sysParamAPI.getParamByKey("ORDEREDITPMSMARK");
        ArrayList<String> companyIds = new ArrayList<>();
        if (null != ordereditpmsmark && null != ordereditpmsmark.getParamValue()) {
            String ordereditpmsmarkParamValue = ordereditpmsmark.getParamValue();
            if (StringUtils.isNotBlank(ordereditpmsmarkParamValue)) {
                String[] companys = ordereditpmsmarkParamValue.split(",");
                Arrays.asList(companys).stream().forEach(id-> companyIds.add(id));
            }
        }
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        collectOrderInfoDTO.setOrderUnqualified(false);
        if(null != userCompanyId){
            companyIds.stream().forEach(id -> {
                if (userCompanyId.contains(id)) {
                    collectOrderInfoDTO.setOrderUnqualified(true);
                    return;
                }
            });
        }
        if (null==collectOrderInfoDTO.getReceiverOrderTime()){
            collectOrderInfoDTO.setReceiverOrderTime(new Date());
        }
        if(DictEnum.CARRIAGE_PRICE_UNIT_BOX.code.equals(collectOrderInfoDTO.getCarriagePriceUnit()) || DictEnum.CARRIAGE_PRICE_UNIT_CAR.code.equals(collectOrderInfoDTO.getCarriagePriceUnit())){
            collectOrderInfoDTO.setFixCutFee(collectOrderInfoDTO.getFixCutFee2());
        }
        TOrderInfo tOrderInfo = orderInfoMapper.selectOrderByCode(code);
        TOrderInfoDetail tOrderInfoDetail = tOrderInfoDetailMapper.selectByOrderId(tOrderInfo.getId());
        collectOrderInfoDTO.setLoadingCarPhoto(tOrderInfoDetail.getLoadingCarPhoto());
        if(null != tOrderInfoDetail.getLoadingCarPhotoInfo() && StringUtils.isNotBlank(tOrderInfoDetail.getLoadingCarPhotoInfo())) {
            collectOrderInfoDTO.setLoadingCarPhotoInfo(tOrderInfoDetail.getLoadingCarPhotoInfo());
        }
        collectOrderInfoDTO.setUnloadCarPhoto(tOrderInfoDetail.getUnloadCarPhoto());
        if(null != tOrderInfoDetail.getUnloadCarPhotoInfo() && StringUtils.isNotBlank(tOrderInfoDetail.getUnloadCarPhotoInfo())){
            collectOrderInfoDTO.setUnloadCarPhotoInfo(tOrderInfoDetail.getUnloadCarPhotoInfo());
        }
        return ResultUtil.ok(collectOrderInfoDTO);
    }

    private List<Map<String, Object>> formatFee(OrderDetailDTO ruleInfo){
        List<Map<String, Object>> list = new ArrayList<>();
        if (ruleInfo.getOtherCutFee1()!=null && ruleInfo.getOtherCutRemark1()!=null){
            Map<String, Object> map1 = new HashMap<>();
            String cutPaymentNote1 = ruleInfo.getOtherCutRemark1();
            BigDecimal otherCutPayment1 = ruleInfo.getOtherCutFee1();
            map1.put("pn", cutPaymentNote1);
            map1.put("cp", otherCutPayment1);
            list.add(map1);
        }
        if (ruleInfo.getOtherCutFee2()!=null && ruleInfo.getOtherCutRemark2()!=null){
            Map<String, Object> map2 = new HashMap<>();
            String cutPaymentNote2 = ruleInfo.getOtherCutRemark2();
            BigDecimal otherCutPayment2 = ruleInfo.getOtherCutFee2();
            map2.put("pn", cutPaymentNote2);
            map2.put("cp", otherCutPayment2);
            list.add(map2);
        }
        if (ruleInfo.getOtherCutFee3()!=null && ruleInfo.getOtherCutRemark3()!=null){
            Map<String, Object> map3 = new HashMap<>();
            String cutPaymentNote3 = ruleInfo.getOtherCutRemark3();
            BigDecimal otherCutPayment3 = ruleInfo.getOtherCutFee3();
            map3.put("pn", cutPaymentNote3);
            map3.put("cp", otherCutPayment3);
            list.add(map3);
        }
        if (ruleInfo.getOtherCutFee4()!=null && ruleInfo.getOtherCutRemark4()!=null){
            Map<String, Object> map4 = new HashMap<>();
            String cutPaymentNote4 = ruleInfo.getOtherCutRemark4();
            BigDecimal otherCutPayment4 = ruleInfo.getOtherCutFee4();
            map4.put("pn", cutPaymentNote4);
            map4.put("cp", otherCutPayment4);
            list.add(map4);
        }
        return list;
    }


    /**
    * @Description 去支付详情
    * <AUTHOR>
    * @Date   2019/6/13 21:54
    * @Param
    * @Return
    * @Exception
    *
    */
    @Override
    public ResultUtil payOrderDetail(String code, String paymentPlatforms) {
        OrderDetailDTO orderDetailDTO = orderCastCalcSnapshotMapper.appSelectSnapsByOrderCode(code);
        if (null != orderDetailDTO.getRuleName() && orderDetailDTO.getRuleName().equals("null")) {
            orderDetailDTO.setRuleName("");
        }
        CollectOrderInfoDTO collectOrderInfo = orderInfoMapper.selectPayOrderDetail(code, CurrentUser.getUserAccountId());
        if(null != collectOrderInfo.getOrderInfoWeightId()){
            //根据order_info_weight表ID查询
            TOrderInfoWeight orderInfoWeight = orderInfoWeightMapper.selectByPrimaryKey(Long.valueOf(collectOrderInfo.getOrderInfoWeightId()));
            collectOrderInfo.setOrderInfoWeight(orderInfoWeight);
        }
        if (CurrentUser.accountIsCompanyAdmin()) {
            collectOrderInfo.setOrderPayPms(true);
        }
        BeanCopyUtil.copyPropertiesIgnoreNull(orderDetailDTO, collectOrderInfo);
        String type = null;
        if (DictEnum.PAYTODRIVER.code.equals(collectOrderInfo.getCapitalTransferType())
                || DictEnum.FIRSTBROKERDRIVER.code.equals(collectOrderInfo.getCapitalTransferType()) )
        {
            type = "end_driver_id";
        } else if (DictEnum.PAYTOBELONGER.code.equals(collectOrderInfo.getCapitalTransferType())
            ||DictEnum.FIRSTBROKERBELONGER.code.equals(collectOrderInfo.getCapitalTransferType())
            ||DictEnum.PAYTOCAPTAIN.code.equals(collectOrderInfo.getCapitalTransferType())) {
            type  = "end_car_owner_id";
        }
        List<String> receiver = new ArrayList<>();
        if (DictEnum.JDPLATFORMS_CODE.code.equals(paymentPlatforms)) {
            receiver = orderInfoMapper.selectOrderReceiverBankCard(code, type);
        } else if (DictEnum.HXPLATFORMS_CODE.code.equals(paymentPlatforms)) {
            List<TBankInfoVo> hxBankList  = orderInfoMapper.selectOrderReceiverHxBankCard(code, type);
            hxBankList.removeAll(Collections.singleton(null)); //移除所有的null元素
            if(null!= hxBankList && hxBankList.size()>0){
                if(DictEnum.PAYTOCAPTAIN.code.equals(collectOrderInfo.getCapitalTransferType())){
                    collectOrderInfo.setReceiverName(collectOrderInfo.getEndCarOwnerName());
                }else{
                    collectOrderInfo.setReceiverName(collectOrderInfo.getDriverUser());
                }
                List<HashMap> cards = new ArrayList<>();
                for(TBankInfoVo tBankInfoVo:hxBankList){
                    HashMap<String, String> card = new HashMap<>();
                    card.put("bankId", String.valueOf(tBankInfoVo.getBankId()));
                    card.put("cardHolder", tBankInfoVo.getCardHolder());
                    cards.add(card);
                }
                collectOrderInfo.setReceiverBankCard(cards);
            }
        }
        if (null !=receiver && receiver.size() > 0){
            List<HashMap> cards = new ArrayList<>();
            for (String cardInfo: receiver){
                if(DictEnum.PAYTOCAPTAIN.code.equals(collectOrderInfo.getCapitalTransferType())){
                    collectOrderInfo.setReceiverName(collectOrderInfo.getEndCarOwnerName());
                }else{
                    collectOrderInfo.setReceiverName(collectOrderInfo.getDriverUser());
                }
                if(StringUtils.isNotEmpty(cardInfo)){
                    HashMap<String, String> card = new HashMap<>();
                    String[] info = cardInfo.split(",");
                    card.put("bankId", info[0]);
                    card.put("cardHolder", info[1] + " " + info[2]);
                    cards.add(card);
                }
            }
            collectOrderInfo.setReceiverBankCard(cards);
        }
        Map<String, String> map = orderStateMapper.selectOrderSignForPay(code);
        String codea = null == map.get("codea") ? "" : map.get("codea");
        String codeb = null == map.get("codeb") ? "" : map.get("codeb");
        String sign;
        if (StringUtils.isNotEmpty(codea)){
            sign = codeb + "(" + codea + ")";
        }else {
            sign = codeb;
        }
        collectOrderInfo.setOrderState(sign);
        collectOrderInfo.setOrderSign(sign);
        collectOrderInfo.setToleranceItem("ANXISHU");
        collectOrderInfo.setToleranceItemValue(0d);
        if (null != collectOrderInfo.getTolerantValueCoefficient()){
            collectOrderInfo.setToleranceItem("ANXISHU");
            collectOrderInfo.setToleranceItemValue(collectOrderInfo.getTolerantValueCoefficient());
        } else if (null != collectOrderInfo.getTolerantValueWeight()){
            collectOrderInfo.setToleranceItem("ANDUNSHU");
            collectOrderInfo.setToleranceItemValue(collectOrderInfo.getTolerantValueWeight());
        }
        TJdAdvanceOrderPayTempInfoDTO tJdAdvanceOrderPayTempInfoDTO = advanceOrderPayTmpMapper.selectJdAdvanceOrderPayTempByOrderCode(code);
        if (null != tJdAdvanceOrderPayTempInfoDTO) {
            collectOrderInfo.setYfk(true);
            if (DictEnum.JDPLATFORMS.code.equals(tJdAdvanceOrderPayTempInfoDTO.getPaymentPlatforms())) {
                collectOrderInfo.setPaymentPlatforms(DictEnum.JDPLATFORMS.code);
            }
            if (DictEnum.HXPLATFORMS.code.equals(tJdAdvanceOrderPayTempInfoDTO.getPaymentPlatforms())) {
                collectOrderInfo.setPaymentPlatforms(DictEnum.HXPLATFORMS.code);
            }
        }
        if (null == collectOrderInfo.getFixCutFee()) {
            collectOrderInfo.setFixCutFee(new BigDecimal("0"));
        }
        if (null == collectOrderInfo.getGoodsCutImpurities()) {
            collectOrderInfo.setGoodsCutImpurities(0.0);
        }
        if (null == collectOrderInfo.getGoodsCutWater()) {
            collectOrderInfo.setGoodsCutWater(0.0);
        }
        if (null == collectOrderInfo.getOtherCutFee1()) {
            collectOrderInfo.setOtherCutFee1(new BigDecimal("0"));
        }
        if (null == collectOrderInfo.getOtherCutFee2()) {
            collectOrderInfo.setOtherCutFee2(new BigDecimal("0"));
        }
        if (null == collectOrderInfo.getOtherCutFee3()) {
            collectOrderInfo.setOtherCutFee3(new BigDecimal("0"));
        }
        if (null == collectOrderInfo.getOtherCutFee4()) {
            collectOrderInfo.setOtherCutFee4(new BigDecimal("0"));
        }
        TOrderInfoDetail tOrderInfoDetail = tOrderInfoDetailMapper.selectByOrderId(collectOrderInfo.getOrderId());
        collectOrderInfo.setLoadingCarPhoto(tOrderInfoDetail.getLoadingCarPhoto());
        if(null != tOrderInfoDetail.getLoadingCarPhotoInfo() && StringUtils.isNotBlank(tOrderInfoDetail.getLoadingCarPhotoInfo())) {
            collectOrderInfo.setLoadingCarPhotoInfo(tOrderInfoDetail.getLoadingCarPhotoInfo());
        }
        collectOrderInfo.setUnloadCarPhoto(tOrderInfoDetail.getUnloadCarPhoto());
        if(null != tOrderInfoDetail.getUnloadCarPhotoInfo() && StringUtils.isNotBlank(tOrderInfoDetail.getUnloadCarPhotoInfo())){
            collectOrderInfo.setUnloadCarPhotoInfo(tOrderInfoDetail.getUnloadCarPhotoInfo());
        }
        return ResultUtil.ok(collectOrderInfo);
    }

    /*
     * <AUTHOR>
     * @Description 分润支付查询资金流动
     * @Date 2020/7/10 15:34
     * @Param
     * @return
    **/
    @Override
    public ResultUtil selectRoyaltyOrderFundChanges(String code) {
        TOrderInfo tOrderInfo = orderInfoMapper.selectOrderByCode(code);
        TOrderCastChanges tOrderCastChanges = orderCastChangesMapper.selectByNewOne(code);
        TOrderPayInfo orderPayInfo = orderPayInfoMapper.selectOnePayInfoByOrderCode(code);
        List<OrderFundChangesDTO> accountFundProcess = new ArrayList<>();

        if ("2".equals(orderPayInfo.getParam1()) || "3".equals(orderPayInfo.getParam1())) {
            if ("2".equals(orderPayInfo.getParam1())) {
                accountFundProcess = orderCastChangesMapper.jdPayFundChanges(code);
            }
            if ("3".equals(orderPayInfo.getParam1())) {
                accountFundProcess = orderCastChangesMapper.hxPayFundChanges(code);
            }
            if (DictEnum.PACK.code.equals(tOrderInfo.getPackStatus())) {
                if (DictEnum.M095.code.equals(tOrderInfo.getOrderPayStatus())) {
                    OrderFundChangesDTO dto = new OrderFundChangesDTO();
                    dto.setItemCode(TradeType.DBRecall.code);
                    accountFundProcess.add(dto);
                }
            }
        } else {
            if ("0".equals(tOrderInfo.getPackStatus())) {
                accountFundProcess = orderCastChangesMapper.pcSelectAccountFundChanges(code);
            } else {
                accountFundProcess = orderCastChangesMapper.pcNewSelectPackAccountFundChanges(code);
            }
        }

        List<FundFlowDTO> ffd = new ArrayList<>();
        if (null != accountFundProcess && accountFundProcess.size() > 0){
            OrderFundChangesDTO orderFundChanges = accountFundProcess.get(accountFundProcess.size() - 1);
            if (orderFundChanges.getItemCode().equals(TradeType.Recall.code)
                    || orderFundChanges.getItemCode().equals(TradeType.DBRecall.code)){
                for (int i = accountFundProcess.size() - 1; i >= 0; i--){
                    OrderFundChangesDTO orderFundChangesDTO = accountFundProcess.get(i);
                    if (orderFundChangesDTO.getItemCode().equals(TradeType.CollectionOrder.code)
                            || orderFundChangesDTO.getItemCode().equals(TradeType.ReCollectionOrder.code)){
                        break;
                    }
                    if (!orderFundChangesDTO.getItemCode().equals(TradeType.CollectionOrder.code)
                            && !orderFundChangesDTO.getItemCode().equals(TradeType.ReCollectionOrder.code)){
                        accountFundProcess.remove(i);
                    }
                }
            }
        }

        log.info("资金流动，{}", JSONUtil.toJsonStr(accountFundProcess));

        List<OrderFundChangesDTO> process = new ArrayList<>();
        // 京东支付
        if (DictEnum.JDPLATFORMS.code.equals(orderPayInfo.getPaymentPlatforms()) || DictEnum.HXPLATFORMS.code.equals(orderPayInfo.getPaymentPlatforms())) {
            LinkedHashMap<String, OrderFundChangesDTO> processSet = new LinkedHashMap<>();
            for (int i = 0; i < accountFundProcess.size(); i++) {
                OrderFundChangesDTO fundProcess = accountFundProcess.get(i);
                if (StringUtils.isBlank(fundProcess.getPayCode())) {
                    processSet.put(fundProcess.getItemCode(), fundProcess);
                } else {
                    processSet.put(fundProcess.getPayCode(), fundProcess);
                }
            }
            for (Map.Entry entry : processSet.entrySet()) {
                process.add((OrderFundChangesDTO) entry.getValue());
            }
        } else {
            // 网商支付
            // 网商节点支付
            if(null!= tOrderInfo && StringUtils.isNotBlank(tOrderInfo.getPayMethod())
                    &&(DictEnum.NODEPAYPROPORTION.code.equals(tOrderInfo.getPayMethod()) || DictEnum.NODEPAYFIXED.code.equals(tOrderInfo.getPayMethod()))){
                process = accountFundProcess;
            } else {
                LinkedHashMap<String, OrderFundChangesDTO> processSet = new LinkedHashMap<>();
                for (int i = 0; i < accountFundProcess.size(); i++) {
                    OrderFundChangesDTO fundProcess = accountFundProcess.get(i);
                    processSet.put(fundProcess.getItemCode(), fundProcess);
                }
                for (Map.Entry entry : processSet.entrySet()) {
                    process.add((OrderFundChangesDTO) entry.getValue());
                }
            }
        }

        for (int i = 0; i < process.size(); i++) {
            OrderFundChangesDTO fundProcess = process.get(i);

            if(TradeType.Invoice.code.equals(fundProcess.getItemCode())) {
                FundFlowDTO fundFlow = new FundFlowDTO();
                // 发单冻结
                fundFlow.setNodeTitle(fundProcess.getItemValue());
                fundFlow.setNodeTime(fundProcess.getCreateTime());
                fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
                fundFlow.setPayCode(fundProcess.getPayCode());
                fundFlow.setPayer(fundProcess.getBParySubAccount());
                fundFlow.setMoney(fundProcess.getTotalFee());
                fundFlow.setFundType("总费用");
                ffd.add(fundFlow);
            }else if(TradeType.ZHPAYNODE.code.equals(fundProcess.getItemCode()) && StringUtils.isNotBlank(fundProcess.getTradeType()) && !"TX".equals(fundProcess.getTradeType())){
                //装货支付
                FundFlowDTO fundFlow = new FundFlowDTO();
                fundFlow.setNodeTitle(fundProcess.getItemValue());
                fundFlow.setNodeTime(fundProcess.getCreateTime());
                fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
                fundFlow.setPayCode(fundProcess.getPayCode());
                fundFlow.setPayer(fundProcess.getBParySubAccount());
                fundFlow.setMoney(fundProcess.getTotalFee());
                fundFlow.setPayee(fundProcess.getCParySubAccount());
                fundFlow.setFundType("装货支付费用");
                // 查询电子回单
                String payReceipt = queryNodePayReceipt(code, DictEnum.ZHPAYNODE.code, HXTradeTypeEnum.HX_CABALANCE_PAY.code, TradeType.PPAY.code);
                fundFlow.setPayReceipt(payReceipt);
                ffd.add(fundFlow);
            }else if(TradeType.ZHPAYNODE.code.equals(fundProcess.getItemCode()) && StringUtils.isNotBlank(fundProcess.getTradeType()) && "TX".equals(fundProcess.getTradeType()) && StringUtils.isNotBlank(fundProcess.getZhTxBankNo())){
                //装货支付提现
                FundFlowDTO fundFlow = new FundFlowDTO();
                fundFlow.setNodeTime(fundProcess.getCreateTime());
                fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
                fundFlow.setPayCode(fundProcess.getPayCode());
                fundFlow.setNodeTitle("装货运费提现");
                fundFlow.setPayer(fundProcess.getCParySubAccount());
                fundFlow.setMoney(fundProcess.getTotalFee());
                if (StringUtils.isNotBlank(fundProcess.getZhTxBankNo())){
                    fundFlow.setPayee(fundProcess.getZhTxBankNo());
                }
                fundFlow.setFundType("装货运费提现");
                ffd.add(fundFlow);
            }else if(TradeType.XHPAYNODE.code.equals(fundProcess.getItemCode()) && StringUtils.isNotBlank(fundProcess.getTradeType()) && !"TX".equals(fundProcess.getTradeType())){
                //卸货支付
                FundFlowDTO fundFlow = new FundFlowDTO();
                fundFlow.setNodeTitle(fundProcess.getItemValue());
                fundFlow.setNodeTime(fundProcess.getCreateTime());
                fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
                fundFlow.setPayCode(fundProcess.getPayCode());
                fundFlow.setPayer(fundProcess.getBParySubAccount());
                fundFlow.setPayee(fundProcess.getCParySubAccount());
                fundFlow.setMoney(fundProcess.getTotalFee());
                fundFlow.setFundType("卸货支付费用");
                // 查询电子回单
                String payReceipt = queryNodePayReceipt(code, DictEnum.XHPAYNODE.code, HXTradeTypeEnum.HX_CABALANCE_PAY.code, TradeType.PPAY.code);
                fundFlow.setPayReceipt(payReceipt);
                ffd.add(fundFlow);
            }else if(TradeType.XHPAYNODE.code.equals(fundProcess.getItemCode()) && StringUtils.isNotBlank(fundProcess.getTradeType()) && "TX".equals(fundProcess.getTradeType()) && StringUtils.isNotBlank(fundProcess.getXhTxBankNo())){
                //卸货支付提现
                FundFlowDTO fundFlow = new FundFlowDTO();
                fundFlow.setNodeTime(fundProcess.getCreateTime());
                fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
                fundFlow.setPayCode(fundProcess.getPayCode());
                fundFlow.setNodeTitle("卸货运费提现");
                fundFlow.setPayer(fundProcess.getCParySubAccount());
                fundFlow.setMoney(fundProcess.getTotalFee());
                if (StringUtils.isNotBlank(fundProcess.getXhTxBankNo())){
                    fundFlow.setPayee(fundProcess.getXhTxBankNo());
                }
                fundFlow.setFundType("卸货运费提现");
                ffd.add(fundFlow);
            } else if (TradeType.CollectionOrder.code.equals(fundProcess.getItemCode())) {
                FundFlowDTO fundFlow = new FundFlowDTO();
                // 收单冻结
                fundFlow.setNodeTitle(fundProcess.getItemValue());
                fundFlow.setNodeTime(fundProcess.getCreateTime());
                fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
                fundFlow.setPayCode(fundProcess.getPayCode());
                fundFlow.setPayer(fundProcess.getBParySubAccount());
                fundFlow.setMoney(fundProcess.getTotalFee());
                fundFlow.setFundType("总费用");
                FundFlowDTO fundFlowDTO = new FundFlowDTO();
                for(FundFlowDTO item : ffd)
                {
                    if(item.getNodeTitle().equals(fundFlow.getNodeTitle()))
                    {
                        fundFlowDTO = item;
                    }
                }
                ffd.remove(fundFlowDTO);
                ffd.add(fundFlow);
            }else if(TradeType.SDPAYNODE.code.equals(fundProcess.getItemCode()) && StringUtils.isNotBlank(fundProcess.getTradeType()) && !"TX".equals(fundProcess.getTradeType())){
                //收单支付
                FundFlowDTO fundFlow = new FundFlowDTO();
                fundFlow.setNodeTitle(fundProcess.getItemValue());
                fundFlow.setNodeTime(fundProcess.getCreateTime());
                fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
                fundFlow.setPayCode(fundProcess.getPayCode());
                fundFlow.setPayer(fundProcess.getBParySubAccount());
                fundFlow.setPayee(fundProcess.getCParySubAccount());
                fundFlow.setMoney(fundProcess.getTotalFee());
                fundFlow.setFundType("收单支付费用");
                // 查询电子回单
                String payReceipt = queryNodePayReceipt(code, DictEnum.SDPAYNODE.code, HXTradeTypeEnum.HX_CABALANCE_PAY.code, TradeType.PPAY.code);
                fundFlow.setPayReceipt(payReceipt);
                ffd.add(fundFlow);
            }else if(TradeType.SDPAYNODE.code.equals(fundProcess.getItemCode()) && StringUtils.isNotBlank(fundProcess.getTradeType()) && "TX".equals(fundProcess.getTradeType()) && StringUtils.isNotBlank(fundProcess.getSdTxBankNo())){
                //收单支付提现
                FundFlowDTO fundFlow = new FundFlowDTO();
                fundFlow.setNodeTime(fundProcess.getCreateTime());
                fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
                fundFlow.setPayCode(fundProcess.getPayCode());
                fundFlow.setNodeTitle("收单运费提现");
                fundFlow.setPayer(fundProcess.getCParySubAccount());
                fundFlow.setMoney(fundProcess.getTotalFee());
                if (StringUtils.isNotBlank(fundProcess.getSdTxBankNo())){
                    fundFlow.setPayee(fundProcess.getSdTxBankNo());
                }
                fundFlow.setFundType("收单运费提现");
                ffd.add(fundFlow);
            }else if(TradeType.WKPAYNODE.code.equals(fundProcess.getItemCode()) && StringUtils.isNotBlank(fundProcess.getTradeType()) && !"TX".equals(fundProcess.getTradeType())){
                //企业尾款支付
                FundFlowDTO fundFlow = new FundFlowDTO();
                fundFlow.setNodeTitle(fundProcess.getItemValue());
                fundFlow.setNodeTime(fundProcess.getCreateTime());
                fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
                fundFlow.setPayCode(fundProcess.getPayCode());
                fundFlow.setPayer(fundProcess.getBParySubAccount());
                fundFlow.setPayee(fundProcess.getPParySubAccount());
                fundFlow.setMoney(fundProcess.getTotalFee());
                StringBuffer fundType = new StringBuffer("尾款费用");
                if (tOrderCastChanges.getDispatchFee().compareTo(BigDecimal.ZERO) > 0) {
                    fundType.append((" + 调度费 "));
                }
                if (tOrderCastChanges.getServiceFee().compareTo(BigDecimal.ZERO) > 0) {
                    fundType.append("+ 服务费");
                }
                fundFlow.setFundType(fundType.toString());
                // 查询电子回单
                String payReceipt = queryNodePayReceipt(code, DictEnum.WKPAYNODE.code, HXTradeTypeEnum.HX_COMBALANCE_PAY.code, TradeType.BPAY.code);
                fundFlow.setPayReceipt(payReceipt);
                ffd.add(fundFlow);
                // 承运方支付
                FundFlowDTO carrierFlow = new FundFlowDTO();
                carrierFlow.setNodeTitle("承运方支付");
                carrierFlow.setNodeTime(fundProcess.getCreateTime());
                carrierFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
                carrierFlow.setPayCode(fundProcess.getPayCode());
                carrierFlow.setPayer(fundProcess.getPParySubAccount());
                carrierFlow.setPayee(fundProcess.getCParySubAccount());
                BigDecimal insuredAmount = BigDecimal.ZERO;
                TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(tOrderInfo.getOrderBusinessCode());
                if (null != tOrderInsurance && OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance)) {
                    insuredAmount = tOrderInsurance.getInsuredAmount();
                }
                carrierFlow.setMoney(fundProcess.getCarriageFee().subtract(insuredAmount));
                fundType = new StringBuffer("尾款费用");
                if (tOrderCastChanges.getServiceFee().compareTo(BigDecimal.ZERO) > 0) {
                    fundType.append("+ 服务费");
                }
                carrierFlow.setFundType(fundType.toString());
                // 查询电子回单
                String carrierPayReceipt = queryNodePayReceipt(code, DictEnum.WKPAYNODE.code, HXTradeTypeEnum.HX_CABALANCE_PAY.code, TradeType.PPAY.code);
                carrierFlow.setPayReceipt(carrierPayReceipt);
                ffd.add(carrierFlow);
                if (DictEnum.MANAGERPATTERN.code.equals(tOrderCastChanges.getCapitalTransferPattern())) {
                    // 司机支付
                    FundFlowDTO driverFlow = new FundFlowDTO();
                    driverFlow.setNodeTitle("司机支付");
                    driverFlow.setNodeTime(fundProcess.getCreateTime());
                    driverFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
                    driverFlow.setPayCode(fundProcess.getPayCode());
                    driverFlow.setPayer(fundProcess.getCParySubAccount());
                    driverFlow.setPayee(fundProcess.getAParySubAccount());
                    driverFlow.setMoney(fundProcess.getServiceFee());
                    driverFlow.setFundType("服务费");
                    String driverReceipt = queryNodePayReceipt(code, DictEnum.WKPAYNODE.code, HXTradeTypeEnum.HX_CDBALANCE_PAY.code, TradeType.CMFEEAPY.code);
                    driverFlow.setPayReceipt(driverReceipt);
                    ffd.add(driverFlow);
                }
            }else if(TradeType.WKPAYNODE.code.equals(fundProcess.getItemCode()) && StringUtils.isNotBlank(fundProcess.getTradeType()) && "TX".equals(fundProcess.getTradeType()) && StringUtils.isNotBlank(fundProcess.getWkTxBankNo())){
                //尾款支付提现
                FundFlowDTO fundFlow = new FundFlowDTO();
                fundFlow.setNodeTime(fundProcess.getCreateTime());
                fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
                fundFlow.setPayCode(fundProcess.getPayCode());
                fundFlow.setNodeTitle("尾款运费提现");
                fundFlow.setPayer(fundProcess.getCParySubAccount());
                fundFlow.setMoney(fundProcess.getCarriageFee().subtract(fundProcess.getServiceFee()));
                if (StringUtils.isNotBlank(fundProcess.getWkTxBankNo())){
                    fundFlow.setPayee(fundProcess.getWkTxBankNo());
                }
                fundFlow.setFundType("尾款运费提现");
                ffd.add(fundFlow);
            } else if (TradeType.PayMent.code.equals(fundProcess.getItemCode())) {
                // 京东支付
                if (DictEnum.JDPLATFORMS.code.equals(orderPayInfo.getPaymentPlatforms()) || DictEnum.HXPLATFORMS.code.equals(orderPayInfo.getPaymentPlatforms())) {
                    if (DictEnum.NODEPAYPROPORTION.code.equals(tOrderInfo.getPayMethod()) || DictEnum.NODEPAYFIXED.code.equals(tOrderInfo.getPayMethod())) {
                        // 节点支付
                        jdNodePaymentChanges(ffd, fundProcess);
                    } else {
                        jdPaymentChanges(tOrderCastChanges.getCapitalTransferType(), tOrderCastChanges.getCapitalTransferPattern(),
                                ffd, fundProcess);
                    }

                } else {
                    // 网商支付
                    wsPaymentChanges(tOrderCastChanges, ffd, fundProcess);
                }
            } else if (TradeType.TX.code.equals(fundProcess.getItemCode())) {
                // 提现
                FundFlowDTO fundFlow = new FundFlowDTO();
                fundFlow.setNodeTitle("提现");
                fundFlow.setNodeTime(fundProcess.getCreateTime());
                fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
                fundFlow.setPayer(fundProcess.getCParySubAccount());
                fundFlow.setPayee(fundProcess.getBankNo());
                BigDecimal serviceFee  = null == fundProcess.getServiceFee() ? BigDecimal.ZERO : fundProcess.getServiceFee();
                fundFlow.setMoney(fundProcess.getCarriageFee().subtract(serviceFee));
                fundFlow.setFundType("运费");
                FundFlowDTO fundFlowDTO = new FundFlowDTO();
                for(FundFlowDTO item : ffd)
                {
                    if(item.getNodeTitle().equals(fundFlow.getNodeTitle()))
                    {
                        fundFlowDTO = item;
                    }
                }
                ffd.remove(fundFlowDTO);
                ffd.add(fundFlow);
            }
        }
        // 判断是否有企业支付，如果没有，补充一条
        boolean comPay = ffd.stream().anyMatch(fundFlow -> HXTradeTypeEnum.HX_COMBALANCE_PAY.code.equals(fundFlow.getPayCode()));
        if (!comPay) {
            for (int i = ffd.size() - 1; i >= 0; i--) {
                if (HXTradeTypeEnum.HX_CABALANCE_PAY.code.equals(ffd.get(i).getPayCode())) {
                    FundFlowDTO fundFlow = new FundFlowDTO();
                    fundFlow.setNodeTitle("企业支付");
                    TOrderPayDetail orderPayDetail = hxOrderPayDetailMapper.selectByOrderCastChangeCodeAndTradeType(tOrderCastChanges.getCode(), HXTradeTypeEnum.HX_CABALANCE_PAY.code);
                    Date tradeTime = tOrderInfo.getOrderFinishTime();
                    if (null != orderPayDetail) {
                        tradeTime = orderPayDetail.getOperateTime();
                    }
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(tradeTime);
                    calendar.add(Calendar.SECOND, -1);
                    fundFlow.setNodeTime(calendar.getTime());
                    fundFlow.setNodeTimeStr(DateUtils.formatDatePattern(fundFlow.getNodeTime(), "yyyy-MM-dd HH:mm:ss.S"));
                    fundFlow.setPayCode(HXTradeTypeEnum.HX_COMBALANCE_PAY.code);
                    fundFlow.setPayer(ffd.get(0).getPayer());
                    fundFlow.setPayee(ffd.get(i).getPayer());
                    fundFlow.setMoney(tOrderCastChanges.getTotalFee());
                    fundFlow.setFundType("运费 + 调度费");
                    String requestCodeByOrderId = orderPayRequestMapper.selectRequestCodeByOrderId(tOrderInfo.getId());
                    String payReceipt = queryPayReceipt(requestCodeByOrderId, TradeType.PayMent.code, HXTradeTypeEnum.HX_COMBALANCE_PAY.code, TradeType.BPAY.code);
                    fundFlow.setPayReceipt(payReceipt);
                    ffd.add(i, fundFlow);
                }
            }

        }
        ffd.sort(Comparator.comparing(FundFlowDTO::getNodeTimeStr));
        return ResultUtil.ok(ffd);
    }

    private void wsPaymentChanges(TOrderCastChanges tOrderCastChanges, List<FundFlowDTO> ffd, OrderFundChangesDTO fundProcess) {
        // 支付
        if (tOrderCastChanges.getCarriageFee().compareTo(BigDecimal.ZERO) > 0) {
            log.info("运费");
            FundFlowDTO fundFlow = new FundFlowDTO();
            fundFlow.setNodeTitle("运费");
            fundFlow.setNodeTime(fundProcess.getCreateTime());
            fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
            fundFlow.setPayCode(fundProcess.getPayCode());
            fundFlow.setPayer(fundProcess.getBParySubAccount());
            fundFlow.setPayee(fundProcess.getCParySubAccount());
            fundFlow.setMoney(fundProcess.getCarriageFee());
            fundFlow.setFundType("运费");
            ffd.add(fundFlow);
        }
        if (tOrderCastChanges.getDispatchFee().compareTo(BigDecimal.ZERO) > 0) {
            log.info("调度费");
            FundFlowDTO fundFlow = new FundFlowDTO();
            fundFlow.setNodeTitle("调度费");
            fundFlow.setNodeTime(fundProcess.getCreateTime());
            fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
            fundFlow.setPayCode(fundProcess.getPayCode());
            fundFlow.setPayer(fundProcess.getBParySubAccount());
            fundFlow.setPayee(fundProcess.getPParySubAccount());
            fundFlow.setMoney(fundProcess.getDispatchFee());
            fundFlow.setFundType("调度费");
            ffd.add(fundFlow);
        }
        if (tOrderCastChanges.getServiceFee().compareTo(BigDecimal.ZERO) > 0) {
            log.info("服务费");
            FundFlowDTO fundFlow = new FundFlowDTO();
            fundFlow.setNodeTitle("服务费");
            fundFlow.setNodeTime(fundProcess.getCreateTime());
            fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
            fundFlow.setPayCode(fundProcess.getPayCode());
            fundFlow.setPayer(fundProcess.getCParySubAccount());
            fundFlow.setPayee(fundProcess.getAParySubAccount());
            fundFlow.setMoney(fundProcess.getServiceFee());
            fundFlow.setFundType("服务费");
            ffd.add(fundFlow);
        }
        if (DictEnum.PAYTOBELONGER.code.equals(tOrderCastChanges.getCapitalTransferType())) {
            log.info("车主运费");
            FundFlowDTO fundFlow = new FundFlowDTO();
            fundFlow.setNodeTitle("运费");
            fundFlow.setNodeTime(fundProcess.getCreateTime());
            fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
            fundFlow.setPayCode(fundProcess.getPayCode());
            fundFlow.setPayer(fundProcess.getCParySubAccount());
            fundFlow.setPayee(fundProcess.getOParySubAccount());
            fundFlow.setMoney(fundProcess.getCarriageFee().subtract(fundProcess.getServiceFee()));
            fundFlow.setFundType("运费");
            ffd.add(fundFlow);
        }
    }

    private void jdPaymentChanges(String captalTransferType, String captalTransferPattern, List<FundFlowDTO> ffd, OrderFundChangesDTO fundProcess) {
        // 支付
        if (JDTradeTypeEnum.COMBALANCE_PAY.code.equals(fundProcess.getPayCode())) {
            log.info("运费");
            FundFlowDTO fundFlow = new FundFlowDTO();
            fundFlow.setNodeTitle("企业支付");
            fundFlow.setNodeTime(fundProcess.getCreateTime());
            fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
            fundFlow.setPayCode(fundProcess.getPayCode());
            fundFlow.setPayer(fundProcess.getBParySubAccount());
            fundFlow.setPayee(fundProcess.getPParySubAccount());
            fundFlow.setMoney(fundProcess.getTotalFee());
            fundFlow.setFundType("运费 + 调度费");
            String payReceipt = queryPayReceipt(fundProcess.getOrderCode(), TradeType.PayMent.code, HXTradeTypeEnum.HX_COMBALANCE_PAY.code, TradeType.BPAY.code);
            fundFlow.setPayReceipt(payReceipt);
            ffd.add(fundFlow);
        }
        if (JDTradeTypeEnum.CABALANCE_PAY.code.equals(fundProcess.getPayCode())) {
            log.info("运费");
            FundFlowDTO fundFlow = new FundFlowDTO();
            fundFlow.setNodeTitle("承运方支付");
            fundFlow.setNodeTime(fundProcess.getCreateTime());
            fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
            fundFlow.setPayCode(fundProcess.getPayCode());
            fundFlow.setPayer(fundProcess.getPParySubAccount());
            fundFlow.setPayee(fundProcess.getCParySubAccount());
            BigDecimal insuredAmount = BigDecimal.ZERO;
            TOrderInfo tOrderInfo = orderInfoMapper.selectOrderByCode(fundProcess.getOrderCode());
            TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(tOrderInfo.getOrderBusinessCode());
            if (null != tOrderInsurance && OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance)) {
                insuredAmount = tOrderInsurance.getInsuredAmount();
            }
            fundFlow.setMoney(fundProcess.getCarriageFee().subtract(insuredAmount));
            fundFlow.setFundType("运费");
            String payReceipt = queryPayReceipt(fundProcess.getOrderCode(), TradeType.PayMent.code, HXTradeTypeEnum.HX_CABALANCE_PAY.code, TradeType.PPAY.code);
            fundFlow.setPayReceipt(payReceipt);
            ffd.add(fundFlow);
        }
        if (JDTradeTypeEnum.CDBALANCE_PAY.code.equals(fundProcess.getPayCode())) {
            log.info("运费");
            FundFlowDTO fundFlow = new FundFlowDTO();
            fundFlow.setNodeTitle("司机支付");
            fundFlow.setNodeTime(fundProcess.getCreateTime());
            fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
            fundFlow.setPayCode(fundProcess.getPayCode());
            fundFlow.setPayer(fundProcess.getCParySubAccount());
            if (DictEnum.PAYTOCAPTAIN.code.equals(captalTransferType)) {
                fundFlow.setPayee(fundProcess.getOParySubAccount());
                BigDecimal insuredAmount = BigDecimal.ZERO;
                TOrderInfo tOrderInfo = orderInfoMapper.selectOrderByCode(fundProcess.getOrderCode());
                TOrderInsurance tOrderInsurance = orderInsuranceMapper.selectByOrderBusinessCode(tOrderInfo.getOrderBusinessCode());
                if (null != tOrderInsurance && OrderDeductionUtil.isInsuranceApplicable(tOrderInsurance)) {
                    insuredAmount = tOrderInsurance.getInsuredAmount();
                }
                fundFlow.setMoney(fundProcess.getCarriageFee().subtract(fundProcess.getServiceFee()).subtract(insuredAmount));
                fundFlow.setFundType("运费");
                String payReceipt = queryPayReceipt(fundProcess.getOrderCode(), TradeType.PayMent.code, HXTradeTypeEnum.HX_CDBALANCE_PAY.code, TradeType.CPAY.code);
                fundFlow.setPayReceipt(payReceipt);
                ffd.add(fundFlow);
            } else if (DictEnum.MANAGERPATTERN.code.equals(captalTransferPattern)) {
                if (fundProcess.getServiceFee().compareTo(BigDecimal.ZERO) > 0) {
                    fundFlow.setPayee(fundProcess.getAParySubAccount());
                    fundFlow.setMoney(fundProcess.getServiceFee());
                    fundFlow.setFundType("服务费");
                    String payReceipt = queryPayReceipt(fundProcess.getOrderCode(), TradeType.PayMent.code, HXTradeTypeEnum.HX_CMBALANCE_PAY.code, TradeType.CMFEEAPY.code);
                    fundFlow.setPayReceipt(payReceipt);
                    ffd.add(fundFlow);
                }
            }
        }
        if (HXTradeTypeEnum.HX_CCBALANCE_PAY.code.equals(fundProcess.getPayCode())) {
            log.info("运费");
            FundFlowDTO fundFlow = new FundFlowDTO();
            fundFlow.setNodeTitle("司机支付");
            fundFlow.setNodeTime(fundProcess.getCreateTime());
            fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
            fundFlow.setPayCode(fundProcess.getPayCode());
            fundFlow.setPayer(fundProcess.getCParySubAccount());
            fundFlow.setPayee(fundProcess.getOParySubAccount());
            fundFlow.setMoney(fundProcess.getCarriageFee().subtract(fundProcess.getServiceFee()).subtract(fundProcess.getInsuredAmount()));
            fundFlow.setFundType("运费");
            String payReceipt = queryPayReceipt(fundProcess.getOrderCode(), TradeType.PayMent.code, HXTradeTypeEnum.HX_CCBALANCE_PAY.code, TradeType.CPAY.code);
            fundFlow.setPayReceipt(payReceipt);
            ffd.add(fundFlow);
        }
        if (HXTradeTypeEnum.HX_CMBALANCE_PAY.code.equals(fundProcess.getPayCode())) {
            log.info("运费");
            FundFlowDTO fundFlow = new FundFlowDTO();
            fundFlow.setNodeTitle("司机支付");
            fundFlow.setNodeTime(fundProcess.getCreateTime());
            fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
            fundFlow.setPayCode(fundProcess.getPayCode());
            fundFlow.setPayer(fundProcess.getCParySubAccount());
            fundFlow.setPayee(fundProcess.getAParySubAccount());
            fundFlow.setMoney(fundProcess.getServiceFee());
            fundFlow.setFundType("服务费");
            String payReceipt = queryPayReceipt(fundProcess.getOrderCode(), TradeType.PayMent.code, HXTradeTypeEnum.HX_CMBALANCE_PAY.code, TradeType.CMFEEAPY.code);
            fundFlow.setPayReceipt(payReceipt);
            ffd.add(fundFlow);
        }
        if (JDTradeTypeEnum.SPLIT_FALL.code.equals(fundProcess.getPayCode())) {
            log.info("运费");
            FundFlowDTO carrierDTO = new FundFlowDTO();
            carrierDTO.setNodeTitle("承运方支付");
            carrierDTO.setNodeTime(fundProcess.getCreateTime());
            carrierDTO.setNodeTimeStr(fundProcess.getCreateTimeStr());
            carrierDTO.setPayCode(fundProcess.getPayCode());
            carrierDTO.setPayer(fundProcess.getPParySubAccount());
            carrierDTO.setPayee(fundProcess.getCParySubAccount());
            carrierDTO.setMoney(fundProcess.getCarriageFee());
            carrierDTO.setFundType("运费");
            String payReceipt = queryPayReceipt(fundProcess.getOrderCode(), TradeType.PayMent.code, JDTradeTypeEnum.SPLIT_FALL.code, TradeType.PPAY.code);
            carrierDTO.setPayReceipt(payReceipt);
            ffd.add(carrierDTO);
            FundFlowDTO captainDTO = new FundFlowDTO();
            captainDTO.setNodeTitle("司机支付");
            captainDTO.setNodeTime(fundProcess.getCreateTime());
            captainDTO.setNodeTimeStr(fundProcess.getCreateTimeStr());
            captainDTO.setPayCode(fundProcess.getPayCode());
            captainDTO.setPayer(fundProcess.getCParySubAccount());
            captainDTO.setPayee(fundProcess.getOParySubAccount());
            captainDTO.setMoney(fundProcess.getCarriageFee());
            captainDTO.setFundType("运费");
            ffd.add(captainDTO);
            FundFlowDTO agentDTO = new FundFlowDTO();
            agentDTO.setNodeTitle("司机支付");
            agentDTO.setNodeTime(fundProcess.getCreateTime());
            agentDTO.setNodeTimeStr(fundProcess.getCreateTimeStr());
            agentDTO.setPayCode(fundProcess.getPayCode());
            agentDTO.setPayer(fundProcess.getCParySubAccount());
            agentDTO.setPayee(fundProcess.getAParySubAccount());
            agentDTO.setMoney(fundProcess.getServiceFee());
            agentDTO.setFundType("服务费");
            ffd.add(agentDTO);
        }
        if (JDTradeTypeEnum.COMBALANCE_WKPAY.code.equals(fundProcess.getPayCode())) {
            log.info("运费");
            FundFlowDTO fundFlow = new FundFlowDTO();
            fundFlow.setNodeTitle("企业支付");
            fundFlow.setNodeTime(fundProcess.getCreateTime());
            fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
            fundFlow.setPayCode(fundProcess.getPayCode());
            fundFlow.setPayer(fundProcess.getBParySubAccount());
            fundFlow.setPayee(fundProcess.getPParySubAccount());
            fundFlow.setMoney(fundProcess.getTotalFee());
            fundFlow.setFundType("运费 + 调度费");
            ffd.add(fundFlow);
            log.info("运费");
            FundFlowDTO carrierDTO = new FundFlowDTO();
            carrierDTO.setNodeTitle("承运方支付");
            carrierDTO.setNodeTime(fundProcess.getCreateTime());
            carrierDTO.setNodeTimeStr(fundProcess.getCreateTimeStr());
            carrierDTO.setPayCode(fundProcess.getPayCode());
            carrierDTO.setPayer(fundProcess.getPParySubAccount());
            carrierDTO.setPayee(fundProcess.getCParySubAccount());
            carrierDTO.setMoney(fundProcess.getCarriageFee());
            carrierDTO.setFundType("运费");
            String payReceipt = queryPayReceipt(fundProcess.getOrderCode(), TradeType.PayMent.code, JDTradeTypeEnum.CABALANCE_WKEPAY.code, TradeType.PPAY.code);
            carrierDTO.setPayReceipt(payReceipt);
            ffd.add(carrierDTO);
        }
    }

    private void jdNodePaymentChanges(List<FundFlowDTO> ffd, OrderFundChangesDTO fundProcess) {
        // 支付
        if (JDTradeTypeEnum.COMBALANCE_PAY.code.equals(fundProcess.getPayCode())) {
            log.info("运费");
            FundFlowDTO fundFlow = new FundFlowDTO();
            fundFlow.setNodeTitle("企业支付");
            fundFlow.setNodeTime(fundProcess.getCreateTime());
            fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
            fundFlow.setPayCode(fundProcess.getPayCode());
            fundFlow.setPayer(fundProcess.getBParySubAccount());
            fundFlow.setPayee(fundProcess.getCParySubAccount());
            fundFlow.setMoney(fundProcess.getTotalFee());
            fundFlow.setFundType("运费 + 调度费");
            ffd.add(fundFlow);
        }
        if (JDTradeTypeEnum.CABALANCE_PAY.code.equals(fundProcess.getPayCode())) {
            log.info("运费");
            FundFlowDTO fundFlow = new FundFlowDTO();
            fundFlow.setNodeTitle("承运方支付");
            fundFlow.setNodeTime(fundProcess.getCreateTime());
            fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
            fundFlow.setPayCode(fundProcess.getPayCode());
            fundFlow.setPayer(fundProcess.getPParySubAccount());
            fundFlow.setPayee(fundProcess.getCParySubAccount());
            fundFlow.setMoney(fundProcess.getCarriageFee());
            fundFlow.setFundType("运费");
            ffd.add(fundFlow);
        }
        if (JDTradeTypeEnum.CDBALANCE_PAY.code.equals(fundProcess.getPayCode())) {
            log.info("运费");
            FundFlowDTO fundFlow = new FundFlowDTO();
            fundFlow.setNodeTitle("司机支付");
            fundFlow.setNodeTime(fundProcess.getCreateTime());
            fundFlow.setNodeTimeStr(fundProcess.getCreateTimeStr());
            fundFlow.setPayCode(fundProcess.getPayCode());
            fundFlow.setPayer(fundProcess.getCParySubAccount());
            fundFlow.setPayee(fundProcess.getAParySubAccount());
            fundFlow.setMoney(fundProcess.getServiceFee());
            fundFlow.setFundType("服务费");
            ffd.add(fundFlow);
        }
        if (JDTradeTypeEnum.SPLIT_FALL.code.equals(fundProcess.getPayCode())) {
            log.info("运费");
            FundFlowDTO carrierDTO = new FundFlowDTO();
            carrierDTO.setNodeTitle("承运方支付");
            carrierDTO.setNodeTime(fundProcess.getCreateTime());
            carrierDTO.setNodeTimeStr(fundProcess.getCreateTimeStr());
            carrierDTO.setPayCode(fundProcess.getPayCode());
            carrierDTO.setPayer(fundProcess.getPParySubAccount());
            carrierDTO.setPayee(fundProcess.getCParySubAccount());
            carrierDTO.setMoney(fundProcess.getCarriageFee().subtract(fundProcess.getServiceFee()));
            carrierDTO.setFundType("运费");
            ffd.add(carrierDTO);
            FundFlowDTO captainDTO = new FundFlowDTO();
            captainDTO.setNodeTitle("司机支付");
            captainDTO.setNodeTime(fundProcess.getCreateTime());
            captainDTO.setNodeTimeStr(fundProcess.getCreateTimeStr());
            captainDTO.setPayCode(fundProcess.getPayCode());
            captainDTO.setPayer(fundProcess.getCParySubAccount());
            captainDTO.setPayee(fundProcess.getOParySubAccount());
            captainDTO.setMoney(fundProcess.getCarriageFee().subtract(fundProcess.getServiceFee()));
            captainDTO.setFundType("运费");
            ffd.add(captainDTO);
            FundFlowDTO agentDTO = new FundFlowDTO();
            agentDTO.setNodeTitle("司机支付");
            agentDTO.setNodeTime(fundProcess.getCreateTime());
            agentDTO.setNodeTimeStr(fundProcess.getCreateTimeStr());
            agentDTO.setPayCode(fundProcess.getPayCode());
            agentDTO.setPayer(fundProcess.getCParySubAccount());
            agentDTO.setPayee(fundProcess.getAParySubAccount());
            agentDTO.setMoney(fundProcess.getServiceFee());
            agentDTO.setFundType("服务费");
            ffd.add(agentDTO);
        }
    }

    private String queryPayReceipt(String orderCode, String userOper, String tradeType, String walletTradeType) {
        try {
            TWalletLogQueryVO tWalletLogQueryVO = new TWalletLogQueryVO();
            tWalletLogQueryVO.setOrderCode(orderCode);
            tWalletLogQueryVO.setUserOper(userOper);
            tWalletLogQueryVO.setTradeType(tradeType);
            tWalletLogQueryVO.setWalletTradeType(walletTradeType);
            TOrderPayInfo tOrderPayInfo = orderPayInfoMapper.selectOnePayInfoByOrderCode(orderCode);
            if (DictEnum.JDPLATFORMS.code.equals(tOrderPayInfo.getPaymentPlatforms())) {
                TJdWalletChangeLog tJdWalletChangeLog = jdOrderWalletChangeLogMapper.selectBySelective(tWalletLogQueryVO);
                if (null != tJdWalletChangeLog && null != tJdWalletChangeLog.getFileUrl()) {
                    return tJdWalletChangeLog.getFileUrl();
                }
            } else if (DictEnum.HXPLATFORMS.code.equals(tOrderPayInfo.getPaymentPlatforms())) {
                TZtWalletChangeLog tZtWalletChangeLog = hxOrderWalletChangeLogMapper.selectBySelective(tWalletLogQueryVO);
                if (null != tZtWalletChangeLog && null != tZtWalletChangeLog.getFileUrl()) {
                    return tZtWalletChangeLog.getFileUrl();
                } else {
                    tZtWalletChangeLog = hxOrderWalletChangeLogMapper.selectByRequestCodeSelective(tWalletLogQueryVO);
                    if (null != tZtWalletChangeLog && null != tZtWalletChangeLog.getFileUrl()) {
                        return tZtWalletChangeLog.getFileUrl();
                    }
                }
            }

        } catch (Exception e) {
            log.error("查询支付凭证异常, {}", ThrowableUtil.getStackTrace(e));
        }
        return null;
    }

    private String queryNodePayReceipt(String orderCode, String payNode, String tradeType, String walletTradeType) {
        try {
            TWalletLogQueryVO tWalletLogQueryVO = new TWalletLogQueryVO();
            tWalletLogQueryVO.setOrderCode(orderCode);
            tWalletLogQueryVO.setUserOper(payNode);
            tWalletLogQueryVO.setTradeType(tradeType);
            tWalletLogQueryVO.setWalletTradeType(walletTradeType);
            TOrderPayInfo tOrderPayInfo = orderPayInfoMapper.selectOnePayInfoByOrderCode(orderCode);
            if (DictEnum.JDPLATFORMS.code.equals(tOrderPayInfo.getPaymentPlatforms())) {
                TJdWalletChangeLog tJdWalletChangeLog = jdOrderWalletChangeLogMapper.selectBySelective(tWalletLogQueryVO);
                if (null != tJdWalletChangeLog && null != tJdWalletChangeLog.getFileUrl()) {
                    return tJdWalletChangeLog.getFileUrl();
                }
            } else if (DictEnum.HXPLATFORMS.code.equals(tOrderPayInfo.getPaymentPlatforms())) {
                TZtWalletChangeLog tZtWalletChangeLog = hxOrderWalletChangeLogMapper.selectBySelective(tWalletLogQueryVO);
                if (null != tZtWalletChangeLog && null != tZtWalletChangeLog.getFileUrl()) {
                    return tZtWalletChangeLog.getFileUrl();
                }
            }

        } catch (Exception e) {
            log.error("查询支付凭证异常, {}", ThrowableUtil.getStackTrace(e));
        }
        return null;
    }

}
