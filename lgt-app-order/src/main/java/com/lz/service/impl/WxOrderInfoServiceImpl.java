package com.lz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.codingapi.txlcn.tc.annotation.LcnTransaction;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lz.api.*;
import com.lz.common.config.RedisUtil;
import com.lz.common.constants.MqMessageTag;
import com.lz.common.constants.MqMessageTopic;
import com.lz.common.dbenum.BusinessCode;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.exception.BadRequestException;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.CodeEnum;
import com.lz.common.model.MQDelayMessage;
import com.lz.common.util.*;
import com.lz.dao.*;
import com.lz.dao.mongodb.TrajectoryMongoDao;
import com.lz.dto.AppOrderListDTO;
import com.lz.dto.CompanySourceDTO;
import com.lz.dto.EndUserLineDTO;
import com.lz.dto.LineInfoPrincipalDTO;
import com.lz.enums.InsuranceMethodsEnum;
import com.lz.example.TOrderStateExample;
import com.lz.model.*;
import com.lz.model.mongodb.TrajectoryMongo;
import com.lz.model.trajectory.resp.recent.TransTimeManageVResp;
import com.lz.payment.Payment;
import com.lz.schedule.model.TTask;
import com.lz.service.*;
import com.lz.sms.service.SmsClientService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.util.OrderUtil;
import com.lz.util.SearchOrderFilter;
import com.lz.util.SendOrderUtil;
import com.lz.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.awt.geom.Point2D;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2019/5/22 - 19:03
 **/
@Slf4j
@Service
public class WxOrderInfoServiceImpl implements WxOrderInfoService {

    /**
     * 证件类型 - 身份证
     */
    private static final String CERTIFICATE_TYPE = "ID_CARD";

    @Resource
    private TOrderInfoMapper orderInfoMapper;

    @Resource
    private TOrderStateMapper orderStateMapper;

    @Resource
    private TOrderDeleteLogMapper orderDeleteLogMapper;

    @Resource
    private TOrderPackInfoMapper tOrderPackInfoMapper;

    @Resource
    private TOrderGoodsSourceInfoMapper orderGoodsSourceInfoMapper;

    /**
     * 货源API
     */
    @Autowired
    private LineService lineService;

    @Autowired
    private TCarrierEnduserCompanyRelAPI carrierEnduserCompanyRelAPI;

    /**
     * 承运方API
     */
    @Autowired
    private CarrierService carrierService;

    @Autowired
    private AppCommonAPI appCommonAPI;

    @Autowired
    private WalletService walletService;

    @Autowired
    private TEndSUserInfoAPI tEndSUserInfoAPI;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private TEndCarInfoAPI tEndCarInfoAPI;

    @Autowired
    private TOrderInfoMapper tOrderInfoMapper;

    @Resource
    private TWeixinOrderInfoMapper weixinOrderInfoMapper;
    @Autowired
    TOrderAbnormalMapper orderAbnormalMapper;

    @Autowired
    private TrajectoryRecentAPI trajectoryRecentAPI;

    @Autowired
    private TOrderAbnormalMapper tOrderAbnormalMapper;

    @Resource
    private TOrderVehicleTrajectoryMapper orderVehicleTrajectoryMapper;

    @Resource
    private TrajectoryMongoDao trajectoryMongoDao;

    @Resource
    private TGoodsSourceVehicleDriverInfoMapper vehicleDriverInfoMapper;

    @Autowired
    private SysParamAPI sysParamAPI;//查询系统参数

    @Autowired
    private WalletChangeLogAPI walletChangeLogAPI;

    @Autowired
    private TOrderPayInfoMapper orderPayInfoMapper;

    @Autowired
    private GoodsSourceAPI goodsSourceAPI;

    @Autowired
    private ProjectCarrierAPI projectCarrierAPI;

    @Autowired
    private TEndSUserInfoAPI endSUserInfoAPI;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private ComplaintsAPI complaintsAPI;

    @Autowired
    private TOrderInfoService tOrderInfoService;

    @Autowired
    private SendOrderUtil sendOrderUtil;

    @Autowired
    private TOrderCastChangesMapper orderCastChangesMapper;

    @Autowired
    private TBusinessAPI tBusinessAPI;

    @Autowired
    private TOrderPayRuleMapper tOrderPayRuleMapper;

    @Autowired
    private TOrderPayRuleService tOrderPayRuleService;

    @Autowired
    private TJdPayWalletMapper jdPayWalletMapper;

    @Autowired
    private TOrderContractService orderContractService;

    @Autowired
    private MqAPI mqAPI;

    @Resource
    private THXPayWalletMapper hxPayWalletMapper;
    @Resource
    private THxPayBankCardMapper hxPayBankCardMapper;
    @Resource
    private AccountService accountService;

    @Value("${domainURL}")
    private String domainURL;
    @Resource
    private SmsClientService smsApi;
    @Autowired
    private PoundRoomService poundRoomService;

    @Autowired
    private GoodsSourceDisposeAPI goodsSourceDisposeAPI;

    @Resource
    private TOrderInfoDetailMapper orderInfoDetailMapper;

    @Resource
    private TOrderInfoWeightMapper tOrderInfoWeightMapper;

    @Resource
    private TOrderBusinessCompanyMapper orderBusinessCompanyMapper;

    @Resource
    private TOrderInsuranceMapper orderInsuranceMapper;

    @Resource
    private CcAdministrativeDivisionsMapper administrativeDivisionsMapper;

    @Resource
    private TOrderTaskMapper orderTaskMapper;

    @Resource
    private TCarInsuranceMapper carInsuranceMapper;


    /**
     * WX司机查询运单
     * Yan
     *
     * @param search
     * @return
     */
    @Override
    public ResultUtil wxDriverOrder(AppOrderSearchVO search) {
//        SysParam param = sysParamAPI.getParamByKey("ISSCOPEDAY");
//        search.setScoreDay(Integer.parseInt(param.getParamValue()));
        Page<Object> objects = PageHelper.startPage(search.getPage(), search.getSize());
        Integer userAccountId = CurrentUser.getUserAccountId();
        Integer endUserId = CurrentUser.getEndUserId();
        search.setEnduserId(endUserId);
        String userNickname = CurrentUser.getUserNickname();
        search.setUserId(userAccountId);
        // 判断筛选条件
        AppOrderSearchVO judge = SearchOrderFilter.judge(search);
        // 判断是否是车主查询运单检查
        Integer enduserId = CurrentUser.getEndUserId();
        String userLogisticsRole = CurrentUser.getUserLogisticsRole();
        if (DictEnum.CTYPECAPTAIN.code.equals(userLogisticsRole)) {
            judge.setCarOwnerId(enduserId);
            judge.setIfCtypecaptain(true);
        }else if(DictEnum.CTYPEBOSS.code.equals(userLogisticsRole)){
            judge.setCarOwnerId(enduserId);
        }
        // 搜索运单
        List<AppOrderListDTO> appOrderListDTOS = weixinOrderInfoMapper.wxDriverSelectOrder(judge);

        ExecutorService executorService = Executors.newSingleThreadExecutor();
        Future<BigDecimal> future = executorService.submit(() -> weixinOrderInfoMapper.wxDriverSelectOrderSum(judge));
        // 查询司所有运单的线路
        List<EndUserLineDTO> endUserLineDTOS = new ArrayList<>();
        if (null != enduserId && StringUtils.isNotBlank(userLogisticsRole)) {
           endUserLineDTOS = weixinOrderInfoMapper.getWXUserAdministrativeLine(enduserId, userLogisticsRole);
        }
        /// 查询系统参数: 是否获取咨询投诉信息
        boolean complaints = false;
        SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.COMPLAINTS.code);
        if (null != paramByKey && null != paramByKey.getParamValue()) {
            if ("1".equals(paramByKey.getParamValue())) {
                complaints = true;
            }
        }
        for (AppOrderListDTO al : appOrderListDTOS) {
            List<Map<String, String>> abnormalType = orderAbnormalMapper.wxJudgeOrderIsError(al.getCode(), "WX",userNickname);

            if (abnormalType != null && !abnormalType.isEmpty()) {
                al.setAbnormalType(abnormalType);
            }
            boolean isFull = true;
            if (al.getDeliverWeightNotesPhoto() == null || al.getReceiveWeightNotesPhoto() == null) {
                isFull = false;
            }
            al.setInfoFull(isFull);
            /*咨询投诉信息 与货物信息*/
            if (complaints) {
                try {
                    OrderFeedbackVO build = OrderFeedbackVO.builder().orderCode(al.getCode()).build();
                    ResultUtil orderFeedbackInfo = tOrderInfoService.getFeedbackOrderInfo(al.getCode());
                    if (orderFeedbackInfo.getData().toString() != null && !"".equals(orderFeedbackInfo.getData().toString()) && orderFeedbackInfo.getData().toString().length() != 0) {
//                LinkedHashMap stringObjectMap = (LinkedHashMap) orderFeedbackInfo.getData();
                        Map<String, Object> stringObjectMap = EntityUtils.entityToMap(orderFeedbackInfo.getData());
                        build.setFeedbackUserEndType(CurrentUser.getUsertype());
                        ResultUtil orderProblemDescription = complaintsAPI.getOrderProblemDescription(build);
                        if (orderProblemDescription.getData() != null) {
                            if (orderProblemDescription.getData().toString() != null && !"".equals(orderProblemDescription.getData().toString()) && orderProblemDescription.getData().toString().length() != 0) {
                                List<Map> mapData = (List<Map>) orderProblemDescription.getData();
                                for (Map map : mapData) {
                                    map.put("oi", stringObjectMap);
                                }
                                al.setFeedbackMap(mapData);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("咨询投诉查询异常, {}", ThrowableUtil.getStackTrace(e));
                }
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("waybill", appOrderListDTOS);
        result.put("line", endUserLineDTOS);
        // 查询总重
        BigDecimal sum = BigDecimal.ZERO;
        try {
            sum = future.get();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            executorService.shutdown();
        }
        result.put("weight", OrderUtil.weight(sum));
        if (DictEnum.CTYPEBOSS.code.equals(CurrentUser.getUserLogisticsRole())) {
            result.put("permission", 0);
        }else if(DictEnum.CTYPECAPTAIN.code.equals(CurrentUser.getUserLogisticsRole())){
            result.put("permission", 0);
        }else {
            result.put("permission", 1);
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), result, objects.getTotal());
    }
    /**
     * 抢单、装货、签到
     *
     * @param sendOrderVO
     * @return
     */
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil grabOrder(SendOrderVO sendOrderVO) {
        try {
            //是否提示卸货地签到操作
            if(sendOrderVO.getIfWarn()){
                //司机扫描货源码，进入“抢单+装货签到”页面后，
                // 判断该司机距离上次扫码运单的时间间隔（以扫码后进入“抢单+装货签到”页面的时间为准），若时间间隔在30分钟以内，则弹窗提示“扫码接单操作频繁”。
                TOrderInfoVO tOrderInfoVO = orderInfoMapper.selectByEndDriverIdAndPhone(sendOrderVO.getCarDriverRelVO().getEnduserId());
                if(null!=tOrderInfoVO){
                    Calendar cal = Calendar.getInstance();
                    cal.setTime(tOrderInfoVO.getCreateTime());
                    SysParam sysParam = sysParamAPI.getParamByKey("QIANDAOTIME");
                    int dateMinute  = 30;
                    if(null!=sysParam && sysParam.getParamValue()!=null &&!"".equals(sysParam.getParamValue())){
                        dateMinute  = Integer.parseInt(sysParam.getParamValue());
                    }
                    cal.add(Calendar.MINUTE, dateMinute);
                    if (cal.getTime().after(new Date())) {
                        ResultUtil result = new ResultUtil();
                        BusinessVO businessVO = new BusinessVO();
                        businessVO.setCompanyId(sendOrderVO.getCompanyId());
                        businessVO.setProjectId(sendOrderVO.getProjectId());
                        List<TBusinessBasic> tBusinessBasicList = tBusinessAPI.selectByCompanyIdAndProjectId(businessVO);
                        String phone = "";
                        if(null!=tBusinessBasicList && !"".equals(tBusinessBasicList) && tBusinessBasicList.size()>0){
                            if(null!=tBusinessBasicList.get(0) &&!"".equals(tBusinessBasicList.get(0))){
                                phone = tBusinessBasicList.get(0).getAccountNo();
                            }
                        }else{
                            BusinessVO businessVONew = new BusinessVO();
                            businessVONew.setCompanyId(sendOrderVO.getCompanyId());
                            List<TBusinessBasic> tBusinessBasicListNew = tBusinessAPI.selectByCompanyIdAndProjectId(businessVONew);
                            if(null!=tBusinessBasicListNew &&!"".equals(tBusinessBasicListNew) && tBusinessBasicListNew.size()>0){
                                if(null!=tBusinessBasicListNew.get(0) &&!"".equals(tBusinessBasicListNew.get(0))){
                                    phone = tBusinessBasicListNew.get(0).getAccountNo();
                                }
                            }
                        }
                        result.setCode(CodeEnum.WARN.getCode());
                        result.setData(phone);
                        return result;
                    }
                }
            }

            //TODO 判断是否已抢单(根据
            // 随机数查找运单主表是否已经存在)
            //微信过渡期暂时去掉验证
            if (StringUtils.isNotEmpty(sendOrderVO.getRandom()) && sendOrderVO.getRandom().length() >= 32){
                TOrderInfo orderInfo = orderInfoMapper.selectOrderInfoByRandom(sendOrderVO.getRandom());
                if (null != orderInfo){
                   return ResultUtil.error("已完成抢单，请勿重复抢单");
                }
            }
            //检查车辆是否可用
            CarDriverRelVO carDriverRelVO = sendOrderVO.getCarDriverRelVO();
            carDriverRelVO.setEndDriverId(carDriverRelVO.getEnduserId());
            EnduserCarStatus enduserCarStatus = selectEnduserCarStatus(carDriverRelVO);
            if (null == enduserCarStatus) {
                return ResultUtil.error("查询司机车辆信息失败");
            }
            HashMap<String, Object> map = checkUserCarStatus(carDriverRelVO, enduserCarStatus);
            String unable = null != map.get("unable") ? map.get("unable").toString() : "";
            if (!unable.isEmpty()) {
                return ResultUtil.error(unable);
            }
            // 司机信息不完善
            if (null != map.get("idCardPerfect")) {
                ResultUtil resultUtil = ResultUtil.error();
                resultUtil.setData(DictEnum.DRIVER.code);
                return resultUtil;
            }

            //承运方与企业合作的项目是否开启
            TProjectCarrierRel projectCarrierRel = new TProjectCarrierRel();
            projectCarrierRel.setCompanyId(sendOrderVO.getCompanyId());
            projectCarrierRel.setProjectId(sendOrderVO.getProjectId());
            projectCarrierRel.setIfEfficient(false);
            ResultUtil selectProjectCarrier = projectCarrierAPI.selectProjectCarrier(projectCarrierRel);
            if (null != selectProjectCarrier.getCode() && selectProjectCarrier.getCode().equals("success")){
                ArrayList data = (ArrayList) selectProjectCarrier.getData();
                if (null != data){
                    if (data.size() == 0){
                        return ResultUtil.error("此项目企业未与任何承运方开启");
                    }
                    if (data.size() > 1){
                        return ResultUtil.error("此项目企业与承运方的关系错误");
                    }
                    LinkedHashMap projectCarrier = (LinkedHashMap) data.get(0);
                    Integer carrierId = (Integer) projectCarrier.get("carrierId");
                    sendOrderVO.setCarrierId(carrierId);
                } else  {
                    return ResultUtil.error("此项目未开启承运方");
                }
            }

            ObjectMapper objectMapper = new ObjectMapper();
            Integer goodsSourceInfoId = sendOrderVO.getGoodsSourceInfoId();
            //查询运单资源
            //查询运单资源
            ResultUtil goodsSourceAndLineInfo = goodsSourceAPI.selectGoodsSourceAndLineInfo(goodsSourceInfoId);
            if (null != goodsSourceAndLineInfo) {
                if (null != goodsSourceAndLineInfo.getCode() && StringUtils.isNotEmpty(goodsSourceAndLineInfo.getCode())
                        && "error".equals(goodsSourceAndLineInfo.getCode())) {
                    String msg = null != goodsSourceAndLineInfo.getMsg() ? goodsSourceAndLineInfo.getMsg() : "获取货源信息失败";
                    return ResultUtil.error(msg);
                }
            }

            //查询线路信息
            LinkedHashMap goodsSourceAndLineInfoData = (LinkedHashMap) goodsSourceAndLineInfo.getData();
            LinkedHashMap source = (LinkedHashMap) goodsSourceAndLineInfoData.get("source");
            CompanySourceDTO companySourceDTO = objectMapper.convertValue(source, CompanySourceDTO.class);
            LinkedHashMap receivePrincipal = (LinkedHashMap) goodsSourceAndLineInfoData.get("receivePrincipal");
            LineInfoPrincipalDTO receiver = objectMapper.convertValue(receivePrincipal, LineInfoPrincipalDTO.class);
            LinkedHashMap deliverPrincipal = (LinkedHashMap) goodsSourceAndLineInfoData.get("deliverPrincipal");
            LineInfoPrincipalDTO deliver = objectMapper.convertValue(deliverPrincipal, LineInfoPrincipalDTO.class);
            log.info("货源信息, {}", JSONUtil.toJsonStr(companySourceDTO));
            if (null != deliver) {
                if (null == deliver.getPrincipalAccountId() || null == deliver.getPrincipalName() || null == deliver.getPrincipalPhone()) {
                    return ResultUtil.error("当前货源无发单员，请先联系企业管理员或运营平台进行维护。");
                }
            }
            if (null != receiver) {
                if (null == receiver.getPrincipalAccountId() || null == receiver.getPrincipalName() || null == receiver.getPrincipalPhone()) {
                    return ResultUtil.error("当前货源无收单员，请先联系企业管理员或运营平台进行维护。");
                }

            }

            if (DictEnum.PAYTOCAPTAIN.code.equals(companySourceDTO.getCapitalTransferType()) && null == carDriverRelVO.getCaptainId()) {
                return ResultUtil.error("请选择车队长");
            }

            /*if(companySourceDTO.getIfElectronicFence()){
                if(!sendOrderVO.getDatefrom().equals(DictEnum.DRIAPP.code)){
                    return ResultUtil.error("该货源仅支持陆港通司机端APP扫码抢单");
                }
            }*/
            //车辆司机信息
            TEndCarInfo tEndCarInfo = tEndCarInfoAPI.selectByPrimaryKey(carDriverRelVO.getEndcarId());
            if(companySourceDTO.getIfSendOrderStatus()){
                TEndUserInfo tEndUserInfo = tEndSUserInfoAPI.selectByPrimaryKey(carDriverRelVO.getEnduserId());

                if(null!=carDriverRelVO.getCaptainId() && !"".equals(carDriverRelVO.getCaptainId())){
                    TEndUserInfo tEndUserInfo2 = tEndSUserInfoAPI.selectByPrimaryKey(carDriverRelVO.getCaptainId());
                    if(!DictEnum.PASSNODE.code.equals(tEndUserInfo2.getAuditStatus())){
                        return ResultUtil.error("请车队长先完成实名认证后再扫码抢单");
                    }
                }
                if(!DictEnum.PASSNODE.code.equals(tEndUserInfo.getAuditStatus())){
                    return ResultUtil.error("请先完成实名认证后再扫码抢单");
                }
                if(!DictEnum.PASSNODE.code.equals(tEndCarInfo.getAuditStatus())){
                    return ResultUtil.error("请先完善车辆资料并审核通过后再扫码抢单");
                }
            }


            //创建运单详情
            TOrderInfo orderInfo = createOderInfo(companySourceDTO, receiver, deliver, sendOrderVO);

            //车辆司机信息
           /* orderInfo.setEndDriverId(carDriverRelVO.getEnduserId());
            orderInfo.setVehicleId(carDriverRelVO.getEndcarId());
            if (null != carDriverRelVO.getCaptainId()) {
                orderInfo.setEndCarOwnerId(carDriverRelVO.getCaptainId());
            } else {
                if(null != carDriverRelVO.getEndCarOwnerId()){
                    orderInfo.setEndCarOwnerId(carDriverRelVO.getEndCarOwnerId());
                }

            }
            orderInfo.setEndUserCarRelId(carDriverRelVO.getEnduserCarRelId());*/

            //线路起点坐标
            String fromCoordinates = companySourceDTO.getFromCoordinates();
            Point2D lineFromPoint = null;
            if (StringUtils.isNotEmpty(fromCoordinates)){
                String[] coordinates = fromCoordinates.split(",");
                String jd = coordinates[0];
                String wd = coordinates[1];
                lineFromPoint = new Point2D.Double(Double.valueOf(jd), Double.valueOf(wd));
                log.info("起点位置：carLon:"+jd+"--------------carLat："+wd);
            }

            //根据运单表信息拿到线路id查询线路信息
            TLineInfo tLineInfo = lineService.selectById(orderInfo.getLineId());
            //log.info("线路参数："+JSONObject.toJSONString(tLineInfo));
            String jd = tLineInfo.getEndCoordinates().split(",")[0];//经度
            String wd = tLineInfo.getEndCoordinates().split(",")[1];//纬度

            //终点电子围栏距离
            Double fenceRecognitionDistance = (Double) tLineInfo.getFenceRecognitionDistance();//电子围栏识别距离

            //起点电子围栏距离
            Double fencingIdentifiedDistance = (Double) tLineInfo.getFencingIdentifiedDistance();//电子围栏识别距离

            log.info("LineId:"+orderInfo.getLineId()+"--------------fencingIdentifiedDistance："+fencingIdentifiedDistance);

            //终点电子围栏距离为空时 查询系统参数配置表
            if(fenceRecognitionDistance==null || "".equals(fenceRecognitionDistance)){
                SysParam sysParam = sysParamAPI.getParamByKey("fenceRecognitionDistance");
                if(sysParam!=null &&!"".equals(sysParam)){
                    fenceRecognitionDistance = Double.parseDouble(sysParam.getParamValue());
                }
            }
            //起点电子围栏距离为空时 查询系统参数配置表
            if(fencingIdentifiedDistance==null || "".equals(fencingIdentifiedDistance)){
                SysParam sysParam = sysParamAPI.getParamByKey("fencingIdentifiedDistance");
                if(sysParam!=null &&!"".equals(sysParam)){
                    fencingIdentifiedDistance = Double.parseDouble(sysParam.getParamValue());
                }
            }

            if(sendOrderVO.getDatefrom().equals(DictEnum.DRIWX.code)){
                //若线路设置电子围栏>50km，手机位置按照50km范围校验
                if(null == fencingIdentifiedDistance || fencingIdentifiedDistance>50){
                    fencingIdentifiedDistance = 50D;
                }
            }

            //根据车牌号获取车辆轨迹
            Point2D carPoint;
            TransTimeManageVResp resultData = trajectoryRecentAPI.transTimeManageV3One(carDriverRelVO.getVehicleNumber()+"_"+tEndCarInfo.getLicensePlateColor(),1);
            if ("0".equals(resultData.getCode())){
               return ResultUtil.error(resultData.getMsg());
            }
            String carLon = "0";
            String carLat = "0";
            String vehicleStatus = "";
            boolean dataEnable = false;
            //如果是微信扫码抢单，则手机经纬度由前端传过来
            if(companySourceDTO.getIfElectronicFence() && sendOrderVO.getDatefrom().equals(DictEnum.DRIWX.code)){
                String operateGeographyPosition = sendOrderVO.getOperateGeographyPosition();
                String replace = operateGeographyPosition.replaceAll("[, ]", "");
                if(replace.length() > 0){
                    String[] split = operateGeographyPosition.split(",");
                    if(split.length == 2){
                        if(null != split[0] && !"".equals(split[0]) &&
                                null != split[1] && !"".equals(split[1])){
                            //获取手机位置的点
                            carPoint = new Point2D.Double(Double.parseDouble(split[0]), Double.parseDouble(split[1]));
                            //距离
                            if(null != lineFromPoint){
                                double distance = ElectronicFence.getDistance(lineFromPoint, carPoint);
                                BigDecimal bigDecimal = BigDecimal.valueOf(distance).setScale(2, RoundingMode.HALF_UP);
                                double v = bigDecimal.doubleValue();
                                log.info("车辆到装货起点的距离:"+v+"，起点电子围栏的距离"+fencingIdentifiedDistance);
                                if (null != fencingIdentifiedDistance && v > fencingIdentifiedDistance){
                                    return ResultUtil.error("请到达装货地后再进行扫码抢单签到");
                                }
                            }else{
                                return ResultUtil.error("获取起点坐标点失败");
                            }
                        }else{
                            return ResultUtil.error("请打开手机定位，否则无法装货签到");
                        }
                    }else{
                        return ResultUtil.error("请打开手机定位，否则无法装货签到");
                    }
                }else{
                    return ResultUtil.error("请打开手机定位，否则无法装货签到");
                }
            }

            if("1001".equals(resultData.getStatus())){
                //经度
                carLon = resultData.getLon();
                //纬度
                carLat = resultData.getLat();

                log.info("车辆扫码签到位置：carLon:"+carLon+"--------------carLat："+carLat);
                //根据两组经纬度计算距离 （km）
                double v = Double.parseDouble(carLon);
                double v1 = Double.parseDouble(carLat);

                carPoint = new Point2D.Double(v/600000, v1/600000);
                //距离
                Double distance = ElectronicFence.getDistance(lineFromPoint, carPoint);
                //距离：不在电子围栏范围内
                if (null != lineFromPoint && null != carPoint){
                    //提醒车辆不在装货地签到附近  暂时注释
                   /* if (distance > fenceRecognitionDistance){
                        if (null == sendOrderVO.getAbnormalDescription() ||
                                (StringUtils.isEmpty(sendOrderVO.getAbnormalDescription()) && sendOrderVO.getAbnormalDescription().length() == 0)){
                            ResultUtil failResult = new ResultUtil();
                            failResult.setCode("fail");
                            return failResult;
                        }
                    }*/
                    log.info("distance"+distance+"--------------fencingIdentifiedDistance："+fencingIdentifiedDistance);
                    log.info("distance"+distance+"--------------fenceRecognitionDistance："+fenceRecognitionDistance);
                    if (distance < fenceRecognitionDistance) {
                        vehicleStatus = "2";
                        dataEnable = true;
                    } else {
                        vehicleStatus = "1";//车辆状态
                    }
                }

            }/*else{
                //提醒车辆不在装货地签到附近  暂时注释
                if (null == sendOrderVO.getAbnormalDescription() ||
                        (StringUtils.isEmpty(sendOrderVO.getAbnormalDescription()) && sendOrderVO.getAbnormalDescription().length() == 0)){
                    ResultUtil failResult = new ResultUtil();
                    failResult.setCode("fail");
                    return failResult;
                }
            }*/
            if(companySourceDTO.getIfElectronicFence()){
                if(sendOrderVO.getDatefrom().equals(DictEnum.DRIAPP.code)){
                    if(!sendOrderVO.getIfAppDzwl()){
                        ResultUtil resultUtil = new ResultUtil();
                        Map map1 = new HashMap();
                        map1.put("fencingIdentifiedDistance",fencingIdentifiedDistance);
                        if (StringUtils.isNotEmpty(fromCoordinates)) {
                            String[] coordinates = fromCoordinates.split(",");
                            map1.put("jd", Double.parseDouble(coordinates[0]));
                            map1.put("wd", Double.parseDouble(coordinates[1]));
                        }
                        resultUtil.setCode("DZWL");
                        resultUtil.setData(map1);
                        return resultUtil;
                    }
                }
            }
            //创建运单异常
            if (StringUtils.isNotEmpty(sendOrderVO.getAbnormalDescription())){
                TOrderAbnormal abnormal = new TOrderAbnormal();
                abnormal.setCode(IdWorkerUtil.getInstance().nextId());
                abnormal.setOrderCode(orderInfo.getCode());
                abnormal.setAbnormalDescription(sendOrderVO.getAbnormalDescription());
                abnormal.setOperateMethod("Wx");
                abnormal.setAbnormalType("QIANDAOYC");//签到异常
                abnormal.setOperateGeographyPosition(sendOrderVO.getOperateGeographyPosition());
                if (StringUtils.isNotEmpty(sendOrderVO.getOperateGeographyPosition())){
                    String[] split = sendOrderVO.getOperateGeographyPosition().split(",");
                    try {
                        abnormal.setLongitude(split[0]);
                    } catch (Exception e){}
                    try {
                        abnormal.setLatitudes(split[1]);
                    } catch (Exception e){}
                }
                abnormal.setEnable(false);
                tOrderAbnormalMapper.insertSelective(abnormal);
            }

            //车辆轨迹
            createVehicleTrajectory(sendOrderVO, companySourceDTO, orderInfo, carDriverRelVO, resultData, vehicleStatus, dataEnable);

            //查询承运方信息
            ResultUtil carrierResult = carrierService.selectById(String.valueOf(sendOrderVO.getCarrierId()));
            LinkedHashMap carrier = (LinkedHashMap) carrierResult.getData();
            if (null != carrier.get("platformDid")) {
                companySourceDTO.setPlatformDid(String.valueOf(carrier.get("platformDid")));
            }

            //资金转移方式
            //根据资金转移方式创建相关人员子账号、钱包：1.经纪人 2.车队长
            // 2022-03-31 网商停止，注释
            /*sendOrderUtil.createCarrierAndEnduserRel(sendOrderVO, carDriverRelVO, sendOrderVO.getCapitalTransferType(), carrier,
                    orderInfo.getCode(), companySourceDTO);*/

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(Calendar.SECOND, 1);
            //createOrderState(grabTime, orderInfo.getCode(), "S0104", sendOrderVO);
            //创建运单执行状态子表：执行状态：司机已接单S0201
            calendar.add(Calendar.SECOND, 1);
            Date derlieveTime = calendar.getTime();
            createOrderState(derlieveTime, orderInfo.getCode(), "S0204", sendOrderVO);
            //创建运单执行状态子表：执行状态：已装货S0302
            calendar.add(Calendar.SECOND, 1);
            Date uptime = calendar.getTime();
            createOrderState(uptime, orderInfo.getCode(), "S0302", sendOrderVO);
            //货源车辆司机信息子表
            TGoodsSourceVehicleDriverInfo vehicleDriverInfo = new TGoodsSourceVehicleDriverInfo();
            vehicleDriverInfo.setEndAgentId(carDriverRelVO.getEndAgentId());
            vehicleDriverInfo.setEndDriverId(carDriverRelVO.getEnduserId());
            vehicleDriverInfo.setEndUserCarRelId(carDriverRelVO.getEnduserCarRelId());
            vehicleDriverInfo.setEndCarId(carDriverRelVO.getEndcarId());
            if (null != orderInfo.getEndCarOwnerId()) {
                vehicleDriverInfo.setEndCarOwnerId(orderInfo.getEndCarOwnerId());
            }
            vehicleDriverInfo.setCode(IdWorkerUtil.getInstance().nextId());
            vehicleDriverInfo.setGoodsSourceCode(companySourceDTO.getCode());
            vehicleDriverInfo.setOrderBusinessCode(orderInfo.getOrderBusinessCode());
            vehicleDriverInfo.setOrderId(orderInfo.getCode());
            vehicleDriverInfo.setGoodsExecuteStatus(DictEnum.CREATEORDER.code);
            vehicleDriverInfo.setOrderGenerateType(DictEnum.SCANORDER.code);
            vehicleDriverInfoMapper.insertSelective(vehicleDriverInfo);

            orderInfo.setGoodsSourceVehicleDriverInfoCode(vehicleDriverInfo.getCode());
            // 放入当前月索引 Yan
            orderInfo.setParam4(OrderUtil.indexTime());
           /* if (null != companySourceDTO.getBlockchainPass() && companySourceDTO.getBlockchainPass()) {
                orderInfo.setVehicleGpsBdStatus(DictEnum.AUTOFETCHGPS.code);
            } else {
                orderInfo.setVehicleGpsBdStatus(DictEnum.NOFETCHGP.code);
            }*/
            orderInfo.setVehicleGpsBdStatus(DictEnum.NORMALFETCHGPS.code);
            if (null!= sendOrderVO.getUploadDatas() && "1".equals(sendOrderVO.getUploadDatas())){
                orderInfo.setUploadData(true);
            }
            orderInfo.setCompanyEntrust(sendOrderVO.getCompanyEntrust());
            orderInfo.setCompanyClient(sendOrderVO.getCompanyClient());
            orderInfo.setEndAgentId(sendOrderVO.getEndAgentId());
            orderInfoMapper.insertSelective(orderInfo);
            TOrderInfo info = new TOrderInfo();
            //运费单价单位为“元/箱”时，将信息保存到t_order_info_weight表
            if(null != sendOrderVO.getCarriagePriceUnit() &&
                    DictEnum.CARRIAGE_PRICE_UNIT_BOX.code.equals(sendOrderVO.getCarriagePriceUnit())){
                TOrderInfoWeight torderInfoweight = sendOrderVO.getOrderInfoWeight();
                torderInfoweight.setOrderId(orderInfo.getId());//运单ID
                info.setId(orderInfo.getId());
                if(torderInfoweight.getBoxNum()>0){
                    if(torderInfoweight.getBoxNum()==1){
                        torderInfoweight.setPrimaryWeight1(new BigDecimal(String.valueOf(orderInfo.getPrimaryWeight())));//原发重量1
                        torderInfoweight.setCarriageUnitPrice1(orderInfo.getCurrentCarriageUnitPrice());//运费单价1
                    }else{
                        torderInfoweight.setPrimaryWeight1(BigDecimal.valueOf(companySourceDTO.getEstimateGoodsWeight()));//原发重量1
                        torderInfoweight.setPrimaryWeight2(BigDecimal.valueOf(companySourceDTO.getEstimateGoodsWeight()));//原发重量2
                        torderInfoweight.setCarriageUnitPrice1(orderInfo.getCurrentCarriageUnitPrice());//运费单价1
                        torderInfoweight.setCarriageUnitPrice2(orderInfo.getCurrentCarriageUnitPrice());//运费单价2
                    }

                    //如果是两箱时装货重量取两箱之和
                    if(null != torderInfoweight.getDeliverWeightNotesWeight1() && null != torderInfoweight.getDeliverWeightNotesWeight2()){
                        info.setDeliverWeightNotesWeight(torderInfoweight.getDeliverWeightNotesWeight1().add(torderInfoweight.getDeliverWeightNotesWeight2()).doubleValue());
                    } else if (null != torderInfoweight.getDeliverWeightNotesWeight1()) {
                        info.setDeliverWeightNotesWeight(torderInfoweight.getDeliverWeightNotesWeight1().doubleValue());
                    } else if (null != torderInfoweight.getDeliverWeightNotesWeight2()) {
                        info.setDeliverWeightNotesWeight(torderInfoweight.getDeliverWeightNotesWeight2().doubleValue());
                    }
                    //装货磅单时间，取第一箱的时间
                    if(null != torderInfoweight.getDeliverWeightNotesTime1() && null != torderInfoweight.getDeliverWeightNotesTime2()){
                        info.setDeliverWeightNotesTime(torderInfoweight.getDeliverWeightNotesTime1());
                    } else if(null != torderInfoweight.getDeliverWeightNotesTime1()){
                        info.setDeliverWeightNotesTime(torderInfoweight.getDeliverWeightNotesTime1());
                    } else if (null != torderInfoweight.getDeliverWeightNotesTime2()) {
                        info.setDeliverWeightNotesTime(torderInfoweight.getDeliverWeightNotesTime2());
                    }
                }
                tOrderInfoWeightMapper.insertSelective(torderInfoweight);
                orderInfoMapper.updateByPrimaryKeySelective(info);
            }


            // 记录运单信息详情
            try {
                TOrderInfoDetail orderInfoDetail = new TOrderInfoDetail();
                orderInfoDetail.setOrderId(orderInfo.getId());
                orderInfoDetail.setEstimatedTravelTime(companySourceDTO.getEstimatedTravelTime());//预计行驶时间
                // 记录司机、车辆审核状态
                if (null != enduserCarStatus.getUserAuditStatus()
                        && DictEnum.PASSNODE.code.equals(enduserCarStatus.getUserAuditStatus())) {
                    orderInfoDetail.setDriverAuditStatus(DictEnum.PASSNODE.code);
                } else {
                    orderInfoDetail.setDriverAuditStatus(DictEnum.NOTPASSNODE.code);
                }
                if (null != enduserCarStatus.getCarAuditStatus()
                        && DictEnum.PASSNODE.code.equals(enduserCarStatus.getCarAuditStatus())) {
                    orderInfoDetail.setCarAuditStatus(DictEnum.PASSNODE.code);
                } else {
                    orderInfoDetail.setCarAuditStatus(DictEnum.NOTPASSNODE.code);
                }
                if(null != sendOrderVO.getCarriagePriceUnit()) {
                    orderInfoDetail.setCarriagePriceUnit(sendOrderVO.getCarriagePriceUnit());
                }
                //如果货源开启了“校验司机是否违规”选项，并且运单是单笔支付，则保存违规扣款金额到运单详情表
                if(null != orderInfo.getPayMethod() && DictEnum.SINGLEPAY.code.equals(orderInfo.getPayMethod()) &&
                        companySourceDTO.getIllegal()){
                    if(null != companySourceDTO.getDeduction()){
                        orderInfoDetail.setDeduction(companySourceDTO.getDeduction());
                    }
                }
                // 扫码抢单时，获取货源“是否开启违规校验”的值保存到运单详情表
                orderInfoDetail.setIllegalCheck(companySourceDTO.getIllegal());
                orderInfoDetail.setLoadingCarPhoto(sendOrderVO.getLoadingCarPhoto());
                orderInfoDetail.setLoadingCarPhotoInfo(sendOrderVO.getLoadingCarPhotoInfo());
                orderInfoDetailMapper.insertSelective(orderInfoDetail);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("记录运单信息详情异常, {}", ThrowableUtil.getStackTrace(e));
            }

            //修改车辆司机状态
            carDriverRelVO.setEndDriverId(carDriverRelVO.getEnduserId());
            carDriverRelVO.setCode(orderInfo.getCode());
            carDriverRelVO.setCarStatus(DictEnum.ONROADCHECKED.code);
            carDriverRelVO.setUserStatus(DictEnum.NOTCHECKED.code);
            carDriverRelVO.setBizCode(orderInfo.getOrderBusinessCode());
            appCommonAPI.updateCarEnduserStatus(carDriverRelVO);

            // 保存司机、车辆运单统计
            sendOrderUtil.saveDriverAndCarOrderCount(carDriverRelVO);

            //支付方式|费用结算模式
            Payment payment = new Payment();
            payment.Invoice(orderInfo);

            //发单节点支付模式
            if(DictEnum.NODEPAYFIXED.code.equals(companySourceDTO.getCarriagePayType())
                    || DictEnum.NODEPAYPROPORTION.code.equals(companySourceDTO.getCarriagePayType())){
                // 创建节点支付规则
                sendOrderUtil.createOrderPayRule(orderInfo.getCode(), companySourceDTO);
            }

            //修改货源扫码次数
            goodsSourceAPI.auditSourceUseNumber(sendOrderVO.getGoodsSourceInfoId());

            // 先按车牌号查询车辆投保表，如果状态为审核通过，则未投保原因为“车辆已投保”
            // 如果车辆投保信息未审核或审核不通过。则判断投保方式，不为空，则进入下个判断，
            // 如果自主选择状态为true或者投保方式为必需投保，
            // 则调用投保接口，生成投保信息。其余情况则不调用接口，只生成投保信息
            TOrderInsurance orderInsurance = new TOrderInsurance();
            orderInsurance.setInsure(0);
            TOrderInfo tOrderInfo = orderInfoMapper.selectByPrimaryKey(orderInfo.getId());
            List<String> errorList = new ArrayList<>();
            if(null == sendOrderVO.getInsuranceMethod()){
                if (null != companySourceDTO.getInsuranceMethod()) {
                    sendOrderVO.setInsuranceMethod(companySourceDTO.getInsuranceMethod());
                }else{
                    sendOrderVO.setInsuranceMethod(InsuranceMethodsEnum.NOTINSURED.getKey());
                }
            }
            TCarInsuranceVO carInsurance = carInsuranceMapper.selectByVehicleNumber(tEndCarInfo.getVehicleNumber());
            boolean carInsuranceStatus = true;
            //20250510 如果是车队长模式，则获取不需要投保的数据，查询 t_sys_param
            boolean isCaptian = false;//是否是 车队长模式并且是不需要投保的
            if (null != companySourceDTO.getCapitalTransferType() &&
                    DictEnum.PAYTOCAPTAIN.code.equals(companySourceDTO.getCapitalTransferType())) {
                SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.NOINSURANCE.code);
                if(null != paramByKey){
                    String[] split = paramByKey.getParamValue().split(",");
                    if(split.length > 0){
                        List<String> list = Arrays.asList(split);
                        if (null != carDriverRelVO.getCaptainId()) {
                            if(list.contains(carDriverRelVO.getCaptainId().toString())){
                                isCaptian = true;
                                TEndUserInfo tEndUserInfo = tEndSUserInfoAPI.selectByPrimaryKey(carDriverRelVO.getCaptainId());
                                errorList.add("车队长"+tEndUserInfo.getRealName()+"不投保");
                            }
                        }
                    }
                }
            }
            orderInsurance.setInsuranceMethod(sendOrderVO.getInsuranceMethod());
            if(!isCaptian){
                if(!InsuranceMethodsEnum.NOTINSURED.getKey().equals(sendOrderVO.getInsuranceMethod())){
                    if(BeanUtil.isNotEmpty(carInsurance) && null != carInsurance.getAuditStatus() && DictEnum.PASSNODE.code.equals(carInsurance.getAuditStatus())){
                        errorList.add("车辆已投保");
                        carInsuranceStatus = false;
                    }else if(null != sendOrderVO.getInsuranceMethod()){
                        if(InsuranceMethodsEnum.INDEPENDENTCHOICE.getKey().equals(sendOrderVO.getInsuranceMethod()) &&
                                !sendOrderVO.getAutoselect()){
                            errorList.add("司机选择不投保");
                        }
                    }
                    if(carInsuranceStatus){
                        if(sendOrderVO.getAutoselect() ||
                                InsuranceMethodsEnum.MUSTBEINSURED.getKey().equals(sendOrderVO.getInsuranceMethod())){
                            if(null != tOrderInfo && tOrderInfo.getDeliverWeightNotesWeight() <= 38){
                                TOrderInfoVO tOrderInfoVO = new TOrderInfoVO();
                                orderInsurance.setInsure(1);
                                //组装参数
                                BeanUtils.copyProperties(tOrderInfo,tOrderInfoVO);
                                tOrderInfoVO.setInsuranceMethod(sendOrderVO.getInsuranceMethod());
                                tOrderInfoVO.setInsuredGoodsType(sendOrderVO.getInsuredGoodsType());
                                tOrderInfoVO.setCarriagePriceUnit(sendOrderVO.getCarriagePriceUnit());
                                tOrderInfoVO.setInsuredAmount(sendOrderVO.getInsuredAmount());
                                if(null != tOrderInfo.getLineId()){
                                    TLineInfo line = lineService.selectById(tOrderInfo.getLineId());
                                    tOrderInfoVO.setFromName(line.getProviceFrom()+line.getCityFrom()+line.getCountryFrom());
                                    tOrderInfoVO.setEndName(line.getProviceEnd()+line.getCityEnd()+line.getCountryEnd());
                                }
                                if(null == tOrderInfoVO.getGoodsUnitPrice()){
                                    tOrderInfoVO.setGoodsUnitPrice(companySourceDTO.getGoodsUnitPrice());
                                }
                                if(null == tOrderInfoVO.getInsuredGoodsType()){
                                    tOrderInfoVO.setInsuredGoodsType(companySourceDTO.getInsuredGoodsType());
                                }
                                //信息数据保存到任务表
                                TTask task = new TTask();
                                task.setTaskId(IdWorkerUtil.getInstance().nextId());
                                task.setTaskType(DictEnum.INSURANCE.code);
                                task.setTaskTypeNode(DictEnum.TOUBAO.code);
                                task.setBusinessType(DictEnum.TB.code);
                                task.setSourceTablename("T_ORDER_INFO");
                                task.setSourcekeyFieldname("id");
                                task.setSourceFieldname("order_business_code");
                                task.setRequestParameter(JSON.toJSONString(tOrderInfoVO));
                                task.setSourceFieldvalue(tOrderInfo.getOrderBusinessCode());
                                task.setIsSuccessed(false);
                                task.setRequestTimes(0);
                                task.setEnable(false);
                                orderTaskMapper.insert(task);
                            }else{
                                errorList.add("装货重量大于38吨");
                            }
                        }
                    }
                }
            }
            orderInsurance.setOrderBusinessCode(orderInfo.getOrderBusinessCode());
            Integer endDriverId = orderInfo.getEndDriverId();
            TEndUserInfo tEndUserInfo = tEndSUserInfoAPI.selectByPrimaryKey(endDriverId);
            if(null != tEndUserInfo){
                orderInsurance.setDriverName(tEndUserInfo.getRealName());
                orderInsurance.setDriverPhone(tEndUserInfo.getPhone());
            }
            String sourceName = "["+orderInfo.getGoodsName()+"]"+tLineInfo.getLineName();
            orderInsurance.setGoodsName(sourceName);
            if(null != orderInfo.getCompanyId()){
                TCompanyInfo tCompanyInfo = companyService.selectByCompanyIdAndAccountId(orderInfo.getCompanyId());
                if(null != tCompanyInfo){
                    orderInsurance.setCompanyName(tCompanyInfo.getCompanyName());
                }
            }
            if(null != orderInfo.getCarrierId()){
                TCarrierInfo tCarrierInfo = carrierService.selectCarrierById(String.valueOf(orderInfo.getCarrierId()));
                if(null != tCarrierInfo){
                    orderInsurance.setCarrierName(tCarrierInfo.getCarrierName());
                }
            }
            if(1 == orderInsurance.getInsure()){
                orderInsurance.setInsuredAmount(sendOrderVO.getInsuredAmount());
            }
            if(errorList.size() > 0){
                orderInsurance.setUninsuredCause(String.join(",", errorList));//未投保原因
            }
            orderInsurance.setCreateUser(CurrentUser.getCurrentUsername());
            orderInsurance.setCreateTime(new Date());
            orderInsurance.setUpdateUser(CurrentUser.getCurrentUsername());
            orderInsurance.setUpdateTime(new Date());
            orderInsurance.setEnable(false);
            orderInsurance.setInsuranceCancellation(false);
            if(null != companySourceDTO.getGoodsUnitPrice() && null != tOrderInfo && null != tOrderInfo.getDeliverWeightNotesWeight()){
                orderInsurance.setCargoValue(companySourceDTO.getGoodsUnitPrice().multiply(
                        BigDecimal.valueOf(tOrderInfo.getDeliverWeightNotesWeight())).setScale(2, RoundingMode.HALF_UP));
            }
            //费率，对应的货物类型，去 t_sys_param 获取
            if(null != companySourceDTO.getInsuranceMethod() && !InsuranceMethodsEnum.NOTINSURED.getKey().equals(companySourceDTO.getInsuranceMethod())){
                if(null != sendOrderVO.getInsuredGoodsType()){
                    SysParam paramByKey = sysParamAPI.getParamByKey(sendOrderVO.getInsuredGoodsType());
                    if(null != paramByKey){
                        orderInsurance.setRate(new BigDecimal(paramByKey.getParamValue()));
                    }
                }else{
                    if(null != orderInfo.getGoodSourceCode()){
                        TGoodsSourceInfo dataByGoodsSourceCode = goodsSourceAPI.getDataByGoodsSourceCode(orderInfo.getGoodSourceCode());
                        SysParam paramByKey = sysParamAPI.getParamByKey(dataByGoodsSourceCode.getInsuredGoodsType());
                        if(null != paramByKey){
                            orderInsurance.setRate(new BigDecimal(paramByKey.getParamValue()));
                        }
                    }
                }
            }
            orderInsuranceMapper.insertSelective(orderInsurance);
            Map<String,Object> remap = new HashMap<>();
            remap.put("code",orderInfo.getOrderBusinessCode());
            remap.put("cityFromCode",orderInfo.getCityFromCode());
            remap.put("cityEndCode",orderInfo.getCityEndCode());
            remap.put("orderBusinessCode",orderInfo.getOrderBusinessCode());
            //想易煤网发送信息
            tOrderInfoService.sendOrderInfoToYimei(orderInfo.getOrderBusinessCode());

            //etc备案
            tOrderInfoService.sendEtcData(orderInfo.getOrderBusinessCode());

            // 创建随意签承运合同
            if (!DictEnum.YZID.code.equals(orderInfo.getContractStatus())) {
                if (null != companySourceDTO && (null == companySourceDTO.getSignContract() || companySourceDTO.getSignContract())) {
                    orderContractService.createAnySignCyht(orderInfo.getCode(), false);
                }
            }

            //上传磅房运单信息 判断当前货源是否推送
            TGoodsSourceDispose tGoodsSourceDispose = goodsSourceDisposeAPI.selectByGoodsSourceCode(orderInfo.getGoodSourceCode());
            if(null != tGoodsSourceDispose && !"".equals(tGoodsSourceDispose)){
                if(tGoodsSourceDispose.getIsWeigh()){
                    poundRoomService.bangfangOrderReportTask(orderInfo);
                }
            }

            return ResultUtil.ok(remap);
        }catch (Exception e){
            e.printStackTrace();
            log.error("抢单失败, 错误码：ZJJ010", e);
            String message = e.getMessage();
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)){
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再发单。";
            }
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)){
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再发单";
            }
            if (StringUtils.checkChineseCharacter(message)){
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("抢单失败, 错误码：ZJJ010");
            }
        }

    }


    /**
    * @Description 创建车辆轨迹
    * <AUTHOR>
    * @Date   2019/6/25 11:28
    * @Param
    * @Return
    * @Exception
    *
    */
    public void createVehicleTrajectory(SendOrderVO sendOrderVO, CompanySourceDTO companySourceDTO, TOrderInfo orderInfo,
                                        CarDriverRelVO carDriverRelVO, TransTimeManageVResp resultData, String vehicleStatus,
                                        boolean dataEnable) {
        // 线路起点坐标
        String fromCoordinates = companySourceDTO.getFromCoordinates();
        //1. 发单签到
        TrajectoryMongo trajectoryMongo = new TrajectoryMongo();
        trajectoryMongo.setId(IdWorkerUtil.getInstance().nextId());
        trajectoryMongo.setTrajectoryCode(IdWorkerUtil.getInstance().nextId());
        trajectoryMongo.setOrderCode(orderInfo.getCode());
        trajectoryMongo.setVehicleId(carDriverRelVO.getEndcarId());
        trajectoryMongo.setVehiclePlateNo(carDriverRelVO.getVehicleNumber());
        trajectoryMongo.setTrajectoryReceiveTime(new Date());
        trajectoryMongo.setTrajectoryReceiveMethod(DictEnum.SENDORDERSIGN.code);
        //地经、纬度
        String[] coordinates = fromCoordinates.split(",");
        trajectoryMongo.setLongitude(coordinates[0]);
        trajectoryMongo.setLatitudes(coordinates[1]);
        trajectoryMongo.setVehicleGeographyPosition(companySourceDTO.getFromName());
        trajectoryMongo.setVehicleStatus("0");
        trajectoryMongo.setVehicleCurrentSpeed("0");
        trajectoryMongo.setDataEnable(true);
        trajectoryMongo.setEnable(false);
        if ("1001".equals(resultData.getStatus())) {
            trajectoryMongo.setProvince(resultData.getProvince());
            trajectoryMongo.setCity(resultData.getCity());
            trajectoryMongo.setCountry(resultData.getCountry());
        }
        trajectoryMongoDao.insert(trajectoryMongo);

        //2. 装货签到

        TrajectoryMongo trajectoryMongo2 = new TrajectoryMongo();
        trajectoryMongo2.setId(IdWorkerUtil.getInstance().nextId());
        trajectoryMongo2.setTrajectoryCode(IdWorkerUtil.getInstance().nextId());
        trajectoryMongo2.setOrderCode(orderInfo.getCode());
        trajectoryMongo2.setVehicleId(carDriverRelVO.getEndcarId());
        trajectoryMongo2.setVehiclePlateNo(carDriverRelVO.getVehicleNumber());
        trajectoryMongo2.setTrajectoryReceiveTime(new Date());
        trajectoryMongo2.setTrajectoryReceiveMethod(DictEnum.AUTOLOADSIGN.code);
        if ("1001".equals(resultData.getStatus())) {
            //地经、纬度
            //经度
            try {
                String carLon = resultData.getLon();
                Double aDouble = Double.valueOf(carLon);
                double v = aDouble / 600000;
                trajectoryMongo2.setLongitude(String.valueOf(v));
            } catch (Exception e){
            }
            try {
                //纬度
                String carLat = resultData.getLat();
                Double aDouble = Double.valueOf(carLat);
                double v = aDouble / 600000;
                trajectoryMongo2.setLatitudes(String.valueOf(v));
            } catch (Exception e){
            }
            trajectoryMongo2.setTrajectoryReceiveTime(new Date(Long.valueOf(resultData.getUtc())));
            trajectoryMongo2.setVehicleGeographyPosition(resultData.getAdr());//车辆地理位置信息
            trajectoryMongo2.setVehicleCurrentSpeed(resultData.getSpd());//车辆当前速度
            trajectoryMongo2.setVehicleCurrentDirection(resultData.getDrc());//车辆行驶方向
            trajectoryMongo2.setProvince(resultData.getProvince());
            trajectoryMongo2.setCity(resultData.getCity());
            trajectoryMongo2.setCountry(resultData.getCountry());
        }

        trajectoryMongo2.setVehicleGeographyPosition(companySourceDTO.getFromName());
        trajectoryMongo2.setVehicleStatus("0");
        trajectoryMongo2.setVehicleCurrentSpeed("0");
        trajectoryMongo2.setDataEnable(true);
        trajectoryMongo2.setEnable(false);
        trajectoryMongoDao.insert(trajectoryMongo2);

        //3. 司机手动签到
        //扫码接单时，会获取进行装货签到的操作的用户的位置，经与瑞茂通的沟通，现需要关闭此位置获取
        /*TOrderVehicleTrajectory handSign = new TOrderVehicleTrajectory();
        handSign.setTrajectoryCode(IdWorkerUtil.getInstance().nextId());
        handSign.setOrderCode(orderInfo.getCode());
        handSign.setVehicleId(carDriverRelVO.getEndcarId());
        handSign.setVehiclePlateNo(carDriverRelVO.getVehicleNumber());
        handSign.setTrajectoryReceiveTime(new Date());
        handSign.setTrajectoryReceiveMethod(DictEnum.DRIVERLOADSIGN.code);
        //地经、纬度
        String[] operaCoordinates = sendOrderVO.getOperateGeographyPosition().split(",");
        try {
            handSign.setLongitude(operaCoordinates[0]);
        } catch (ArrayIndexOutOfBoundsException e){
        }
        try {
            handSign.setLatitudes(operaCoordinates[1]);
        } catch (ArrayIndexOutOfBoundsException e){
        }

        handSign.setVehicleGeographyPosition(companySourceDTO.getFromName());
        handSign.setVehicleStatus("0");
        handSign.setVehicleCurrentSpeed("0");
        handSign.setDataEnable(true);
        handSign.setEnable(false);
        orderVehicleTrajectoryMapper.insertSelective(handSign);*/
    }

    @LcnTransaction
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil grabHallOrder(SendOrderVO sendOrderVO) {
        try {
            //检查车辆是否可用
            CarDriverRelVO carDriverRelVO = sendOrderVO.getCarDriverRelVO();
            carDriverRelVO.setEndDriverId(carDriverRelVO.getEnduserId());
            EnduserCarStatus enduserCarStatus = selectEnduserCarStatus(carDriverRelVO);
            if (null == enduserCarStatus) {
                return ResultUtil.error("查询司机车辆信息失败");
            }
            /*HashMap<String, Object> map = checkUserCarStatus(carDriverRelVO, enduserCarStatus);
            String unable = null != map.get("unable") ? map.get("unable").toString() : "";
            if (unable.length() > 0) {
                return ResultUtil.error(unable);
            }*/
            boolean checkUserCarUseable = checkUserCarUseable(enduserCarStatus);
            if (!checkUserCarUseable) {
                return ResultUtil.error("您有未完成运输的运单, 请先完成上个运单 (装卸货签到) 再进行抢单。");
            }
            // 检查司机、车辆审核状态
            String checkUserCarAuditStatus = checkUserCarAuditStatus(carDriverRelVO, enduserCarStatus);
            if (StringUtils.isNotBlank(checkUserCarAuditStatus)) {
                return ResultUtil.error(checkUserCarAuditStatus);
            }

            //承运方与企业合作的项目是否开启
            TProjectCarrierRel projectCarrierRel = new TProjectCarrierRel();
            projectCarrierRel.setCompanyId(sendOrderVO.getCompanyId());
            projectCarrierRel.setProjectId(sendOrderVO.getProjectId());
            projectCarrierRel.setIfEfficient(false);
            ResultUtil selectProjectCarrier = projectCarrierAPI.selectProjectCarrier(projectCarrierRel);
            if (null != selectProjectCarrier.getCode() && selectProjectCarrier.getCode().equals("success")) {
                ArrayList data = (ArrayList) selectProjectCarrier.getData();
                if (null != data) {
                    if (data.size() == 0) {
                        return ResultUtil.error("此项目企业未与任何承运方开启");
                    }
                    if (data.size() > 1) {
                        return ResultUtil.error("此项目企业与承运方的关系错误");
                    }
                    LinkedHashMap projectCarrier = (LinkedHashMap) data.get(0);
                    Integer carrierId = (Integer) projectCarrier.get("carrierId");
                    sendOrderVO.setCarrierId(carrierId);
                } else {
                    return ResultUtil.error("此项目未开启承运方");
                }
            }

            ObjectMapper objectMapper = new ObjectMapper();
            Integer goodsSourceInfoId = sendOrderVO.getGoodsSourceInfoId();
            //查询运单资源
            ResultUtil goodsSourceAndLineInfo = goodsSourceAPI.selectGoodsSourceAndLineInfo(goodsSourceInfoId);
            if (null != goodsSourceAndLineInfo) {
                if (null != goodsSourceAndLineInfo.getCode() && StringUtils.isNotEmpty(goodsSourceAndLineInfo.getCode())
                        && "error".equals(goodsSourceAndLineInfo.getCode())) {
                    String msg = null != goodsSourceAndLineInfo.getMsg() ? goodsSourceAndLineInfo.getMsg() : "获取货源信息失败";
                    return ResultUtil.error(msg);
                }
            }

            //查询线路信息
            LinkedHashMap goodsSourceAndLineInfoData = (LinkedHashMap) goodsSourceAndLineInfo.getData();
            LinkedHashMap source = (LinkedHashMap) goodsSourceAndLineInfoData.get("source");
            CompanySourceDTO companySourceDTO = objectMapper.convertValue(source, CompanySourceDTO.class);
            LinkedHashMap receivePrincipal = (LinkedHashMap) goodsSourceAndLineInfoData.get("receivePrincipal");
            LineInfoPrincipalDTO receiver = objectMapper.convertValue(receivePrincipal, LineInfoPrincipalDTO.class);
            LinkedHashMap deliverPrincipal = (LinkedHashMap) goodsSourceAndLineInfoData.get("deliverPrincipal");
            LineInfoPrincipalDTO deliver = objectMapper.convertValue(deliverPrincipal, LineInfoPrincipalDTO.class);
            log.info("货源信息, {}", JSONUtil.toJsonStr(companySourceDTO));
            if (null != deliver) {
                if (null == deliver.getPrincipalAccountId() || null == deliver.getPrincipalName() || null == deliver.getPrincipalPhone()) {
                    return ResultUtil.error("当前货源无发单员，请先联系企业管理员或运营平台进行维护。");
                }
            }
            if (null != receiver) {
                if (null == receiver.getPrincipalAccountId() || null == receiver.getPrincipalName() || null == receiver.getPrincipalPhone()) {
                    return ResultUtil.error("当前货源无收单员，请先联系企业管理员或运营平台进行维护。");
                }
            }

            //修改货源库存
            TGoodsSourceInfo goodsSourceInfo = orderGoodsSourceInfoMapper.selectByPrimaryKey(sendOrderVO.getGoodsSourceInfoId());
            TGoodsSourceInfo goodsSourceInfoForUpdate = new TGoodsSourceInfo();
            goodsSourceInfoForUpdate.setId(sendOrderVO.getGoodsSourceInfoId());
            goodsSourceInfoForUpdate.setEstimateGoodsWeight(goodsSourceInfo.getEstimateGoodsWeight() - sendOrderVO.getEstimatedLoadingWeight());
            if (goodsSourceInfoForUpdate.getEstimateGoodsWeight().compareTo(0d) < 0) {
                return ResultUtil.error("货源库存不足");
            } else if (goodsSourceInfoForUpdate.getEstimateGoodsWeight().compareTo(0d) == 0) {
                goodsSourceInfoForUpdate.setStatus(DictEnum.CLOSESOURCE.code);
            }
            goodsSourceInfoForUpdate.setUpdateTime(new Date());
            orderGoodsSourceInfoMapper.updateEstimateGoodsWeight(goodsSourceInfoForUpdate);

            //创建运单详情
            TOrderInfo orderInfo = createResourceHallOderInfo(companySourceDTO, receiver, deliver, sendOrderVO);

            //查询承运方信息
            ResultUtil carrierResult = carrierService.selectById(String.valueOf(sendOrderVO.getCarrierId()));
            LinkedHashMap carrier = (LinkedHashMap) carrierResult.getData();
            if (null != carrier.get("platformDid")) {
                companySourceDTO.setPlatformDid(String.valueOf(carrier.get("platformDid")));
            }

            // 创建运单状态
            createOrderState(new Date(), orderInfo.getCode(), DictEnum.S0106.code, sendOrderVO);
            //货源车辆司机信息子表
            TGoodsSourceVehicleDriverInfo vehicleDriverInfo = new TGoodsSourceVehicleDriverInfo();
            vehicleDriverInfo.setEndDriverId(carDriverRelVO.getEnduserId());
            vehicleDriverInfo.setEndUserCarRelId(carDriverRelVO.getEnduserCarRelId());
            vehicleDriverInfo.setEndCarId(carDriverRelVO.getEndcarId());
            if (null != orderInfo.getEndCarOwnerId()) {
                vehicleDriverInfo.setEndCarOwnerId(orderInfo.getEndCarOwnerId());
            }
            vehicleDriverInfo.setCode(IdWorkerUtil.getInstance().nextId());
            vehicleDriverInfo.setGoodsSourceCode(companySourceDTO.getCode());
            vehicleDriverInfo.setOrderBusinessCode(orderInfo.getOrderBusinessCode());
            vehicleDriverInfo.setOrderId(orderInfo.getCode());
            vehicleDriverInfo.setGoodsExecuteStatus(DictEnum.CREATEORDER.code);
            vehicleDriverInfo.setOrderGenerateType(DictEnum.RESOURCEHALLSEND.code);
            vehicleDriverInfoMapper.insertSelective(vehicleDriverInfo);

            orderInfo.setGoodsSourceVehicleDriverInfoCode(vehicleDriverInfo.getCode());
            // 放入当前月索引 Yan
            orderInfo.setParam4(OrderUtil.indexTime());
            orderInfo.setVehicleGpsBdStatus(DictEnum.NORMALFETCHGPS.code);
            if (null != sendOrderVO.getUploadDatas() && "1".equals(sendOrderVO.getUploadDatas())) {
                orderInfo.setUploadData(true);
            }
            orderInfo.setCompanyEntrust(sendOrderVO.getCompanyEntrust());
            orderInfo.setCompanyClient(sendOrderVO.getCompanyClient());
            orderInfoMapper.insertSelective(orderInfo);

            // 记录运单信息详情
            TOrderInfoDetail orderInfoDetail = new TOrderInfoDetail();
            orderInfoDetail.setOrderId(orderInfo.getId());
            orderInfoDetail.setEstimatedTravelTime(companySourceDTO.getEstimatedTravelTime());
            // 记录司机、车辆审核状态
            if (null != enduserCarStatus.getUserAuditStatus()
                    && DictEnum.PASSNODE.code.equals(enduserCarStatus.getUserAuditStatus())) {
                orderInfoDetail.setDriverAuditStatus(DictEnum.PASSNODE.code);
            } else {
                orderInfoDetail.setDriverAuditStatus(DictEnum.NOTPASSNODE.code);
            }
            if (null != enduserCarStatus.getCarAuditStatus()
                    && DictEnum.PASSNODE.code.equals(enduserCarStatus.getCarAuditStatus())) {
                orderInfoDetail.setCarAuditStatus(DictEnum.PASSNODE.code);
            } else {
                orderInfoDetail.setCarAuditStatus(DictEnum.NOTPASSNODE.code);
            }
            orderInfoDetailMapper.insertSelective(orderInfoDetail);

            //修改车辆司机状态
            carDriverRelVO.setEndDriverId(carDriverRelVO.getEnduserId());
            carDriverRelVO.setCode(orderInfo.getCode());
            carDriverRelVO.setCarStatus(DictEnum.ONROADRECIEVEORDER.code);
            carDriverRelVO.setUserStatus(DictEnum.NOTRECIEVEORDER.code);
            carDriverRelVO.setBizCode(orderInfo.getOrderBusinessCode());
            appCommonAPI.updateCarEnduserStatus(carDriverRelVO);

            // 保存司机、车辆运单统计
            sendOrderUtil.saveDriverAndCarOrderCount(carDriverRelVO);

            //支付方式|费用结算模式
            Payment payment = new Payment();
            payment.Invoice(orderInfo);

            Map<String, Object> remap = new HashMap<>();
            remap.put("code", orderInfo.getOrderBusinessCode());

            MQDelayMessage mqDelayMessage = new MQDelayMessage();
            mqDelayMessage.setTopic(MqMessageTopic.RESOURCEHALLORDER);
            mqDelayMessage.setTag(MqMessageTag.LOADINGSINGINNOTIFY);
            mqDelayMessage.setKey(orderInfo.getCode());
            // 提醒装货
            // 延时22小时
            Object loadingSignTime = redisUtil.get(DictEnum.RESOURCE_HALL_LOADING_SIGN_TIME.code);
            if (null != loadingSignTime) {
                mqDelayMessage.setStartDeliverTime(Long.parseLong(Objects.toString(loadingSignTime)) * 1000);
            } else {
                mqDelayMessage.setStartDeliverTime(60 * 60 * 22 * 1000);
            }
            mqAPI.sendDelayMessage(mqDelayMessage);

            // 过期取消
            mqDelayMessage.setTag(MqMessageTag.RESOURCEHALLORDERCANCEL);
            // 延时24小时
            Object cancelTime = redisUtil.get(DictEnum.RESOURCE_HALL_ORDER_CANCEL_TIME.code);
            if (null != cancelTime) {
                mqDelayMessage.setStartDeliverTime(Long.parseLong(Objects.toString(cancelTime)) * 1000);
            } else {
                mqDelayMessage.setStartDeliverTime(60 * 60 * 24 * 1000);
            }
            mqAPI.sendDelayMessage(mqDelayMessage);

            return ResultUtil.ok(remap);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("抢单失败, 错误码：ZJJ011, {}", ThrowableUtil.getStackTrace(e));
            String message = e.getMessage();
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)) {
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再发单。";
            }
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)) {
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再发单";
            }
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("抢单失败, 错误码：ZJJ011");
            }
        }
    }

    /**
     * 取消运单
     *
     * @param orderInfoVO
     * @return
     * <AUTHOR>
     */
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil cancleOrder(TOrderInfoVO orderInfoVO) {
        try {
            TOrderInfo tOrderInfo = orderInfoMapper.selectOrderByCode(orderInfoVO.getCode());
            //发单节点支付模式
            if(DictEnum.NODEPAYFIXED.code.equals(tOrderInfo.getPayMethod())
                    || DictEnum.NODEPAYPROPORTION.code.equals(tOrderInfo.getPayMethod())){
                boolean flag = tOrderPayRuleService.isNodePay(tOrderInfo);
                if(flag){
                    return ResultUtil.error("当前运单已节点支付， 不可取消");
                }
            }
            //已建单、已接单、已装货
            if (tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M010.code)
                    || tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M020.code)
                    || tOrderInfo.getOrderExecuteStatus().equals(DictEnum.M030.code)){
                //1. 修改运单主表状态
                TOrderInfo info = new TOrderInfo();
                info.setId(orderInfoVO.getId());
                //已取消
                info.setOrderExecuteStatus("M-10");
                orderInfoMapper.updateByPrimaryKeySelective(info);
                //2. 修改运单执行状态子表
                TOrderState orderState = new TOrderState();
                //司机取消运单
                orderState.setStateNodeValue("S-104");
                TOrderStateExample orderStateExample = new TOrderStateExample();
                orderStateExample.createCriteria().andOrderCodeEqualTo(orderInfoVO.getCode());
                orderStateMapper.updateByExampleSelective(orderState, orderStateExample);

                //运单删除记录
                TOrderDeleteLog orderDeleteLog = new TOrderDeleteLog();
                orderDeleteLog.setCode(IdWorkerUtil.getInstance().nextId());
                orderDeleteLog.setOrderCode(orderInfoVO.getCode());
                orderDeleteLog.setOrderBusinessCode(orderInfoVO.getOrderBusinessCode());
                orderDeleteLog.setDeletePerson(CurrentUser.getCurrentUsername());
                orderDeleteLog.setDeletePersonPhone(orderInfoVO.getDeletePersonPhone());
                orderDeleteLog.setVerificationCode(orderInfoVO.getVerificationCode());
                orderDeleteLog.setDeleteTime(orderInfoVO.getCreateTime());
                orderDeleteLog.setCreateUser(orderDeleteLog.getCreateUser());
                orderDeleteLog.setCreateTime(orderInfoVO.getCreateTime());
                orderDeleteLogMapper.insertSelective(orderDeleteLog);


                //解冻
                Payment payment = new Payment();
                payment.cancelOrder(tOrderInfo);

                // 数字物流关闭运单
                /*TOrderCastChanges orderCastChanges = orderCastChangesService.selectByNewOne(tOrderInfo.getCode());
                if (null != orderCastChanges.getCapitalTransferPattern() && StringUtils.isNotBlank(orderCastChanges.getCapitalTransferPattern())) {
                    if (digitlFlowUtil.selectOrderStatus(tOrderInfo.getCode(), DigitlFlowEnum.CreateFlowOrder.code)) {
                        if (!digitlFlowUtil.selectOrderStatus(tOrderInfo.getCode(), DigitlFlowEnum.ClosedOrder.code)) {
                            digitlFlowUtil.orderClose(tOrderInfo.getCarrierId(), tOrderInfo.getCode());
                        }

                    }
                }*/

                //恢复车辆司机状态
                CarDriverRelVO carDriverRelVO = new CarDriverRelVO();
                carDriverRelVO.setBizCode(tOrderInfo.getOrderBusinessCode());
                carDriverRelVO.setCarStatus("AVAILABLECANCLE");
                carDriverRelVO.setUserStatus("DISTRIBUTIONCANCLE");
                //想易煤网发送信息
                tOrderInfoService.sendOrderInfoToYimei(tOrderInfo.getOrderBusinessCode());
                try {
                    appCommonAPI.updateCarEnduserStatus(carDriverRelVO);
                } catch (Exception e){
                    log.error("修改车辆司机状态失败", e);
                    throw new RuntimeException("修改车辆司机状态失败");
                }
            } else {
                return ResultUtil.error("运单状态错误， 不可取消");
            }
        } catch (Exception e) {
            String message = e.getMessage();
            if (message.equals(BusinessCode.PROJECTWALLETINSUFFICIENT.code)){
                message = "当前货源钱包余额不足，请联系企业管理员/会计充值后再取消";
                throw new RuntimeException(message);
            }
            if (message.equals(BusinessCode.WALLETAMOUNTINSUFFICIENT.code)){
                message = "当前货源企业钱包余额不足，请联系企业管理员/会计充值后再取消";
                throw new RuntimeException(message);
            }
            if (StringUtils.checkChineseCharacter(message)){
                throw new RuntimeException(message);
            }
        }
        return ResultUtil.ok();
    }

    @Override
    public TOrderInfo selectByPrimaryKey(Integer orderinfoId) {
        return orderInfoMapper.selectByPrimaryKey(orderinfoId);
    }

    @Override
    public ResultUtil selectTiXianPage(Integer endUserId) {
        TEndUserInfo tEndUserInfo = tEndSUserInfoAPI.selectByPrimaryKey(endUserId);
        String logisticsRole = CurrentUser.getUserLogisticsRole();
        Map<String, Object> map = new HashMap<String, Object>();
        List<TxVO> list1 = null;
        List<TxVO> list2 = null;
        List<TxVO> list3 = null;
        List<TxVO> list4 = null;
        //网商支付 type
        // 1: 实体运单 2: 打包运单支付 3: 预付款运单 4: 节点支付
        // 京东支付 type
        // 5: 实体运单 6: 打包运单支付 7: 节点支付
        if (logisticsRole.equals("CTYPEDRVIVER")) {//司机
            list1 = orderInfoMapper.selectByRole1(endUserId);
            map.put("listPage", list1);
        } else if (logisticsRole.equals("CTYPEBOSS")) {//车老板
            list2 = orderInfoMapper.selectByRole2(endUserId);
            map.put("listPage", list2);
        } else if (logisticsRole.equals("CTYPEMANAGER")) {//经纪人
            list3 = orderInfoMapper.selectByRole3(endUserId);
            map.put("listPage", list3);
        }else if (logisticsRole.equals("CTYPECAPTAIN")) {//车队长
            list4 = orderInfoMapper.selectByRole4(endUserId);
            map.put("listPage", list4);
        }
        Object tBankCard = tEndSUserInfoAPI.idDefault();
        if (tBankCard == null) {
            map.put("tBankCard", "");
        } else {
            map.put("tBankCard", tBankCard);
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), map);
    }

    @Override
    public ResultUtil jdTixianPage() {
        Integer enduserId = CurrentUser.getEndUserId();
        Map<String, Object> map = new HashMap<>();
        // 查询钱包数据
        TJdWallet tJdWallet = jdPayWalletMapper.selectByEndUserId(enduserId);
        map.put("wallet", tJdWallet);
        // 查询默认银行卡
        Object tBankCard = tEndSUserInfoAPI.idDefault();
        if (tBankCard == null) {
            map.put("tBankCard", "");
        } else {
            map.put("tBankCard", tBankCard);
        }
        return ResultUtil.ok(map);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/12/7 9:05
     *  @Description: 华夏提现页面
     */
    @Override
    public ResultUtil hxTixianPage() {
        Integer accountId = CurrentUser.getUserAccountId();
        Map<String, Object> map = new HashMap<>();
        // 查询钱包数据
        TZtWallet tZtWallet = hxPayWalletMapper.selectByAccountId(accountId);
        map.put("wallet", tZtWallet);
        // 查询默认银行卡
        TZtBankCard tZtBankCard = hxPayBankCardMapper.selectIsDefault(accountId);
        if (tZtBankCard == null) {
            map.put("tBankCard", "");
        } else {
            map.put("tBankCard", tZtBankCard);
        }
        return ResultUtil.ok(map);
    }

    /**
     * 创建运单详情
     *
     * @param companySourceDTO
     * @param receiver
     * @param deliver
     * @param sendOrderVO
     * @return
     */
    public TOrderInfo createOderInfo(CompanySourceDTO companySourceDTO, LineInfoPrincipalDTO
            receiver, LineInfoPrincipalDTO deliver,SendOrderVO sendOrderVO) {
        TOrderInfo orderInfo = new TOrderInfo();
        orderInfo.setCode(IdWorkerUtil.getInstance().nextId());
        //运单编号：线路编码 + 年月日时分秒毫秒
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String date = sdf.format(new Date());
        String random = RandomUtil.randomString(3, 10);
        String lineTypeStr = companySourceDTO.getLineType();
        //修改扫码对应的线路类型
        lineTypeStr = changeLineType(lineTypeStr);
        orderInfo.setOrderBusinessCode(lineTypeStr + date + random);
        //承运方
        orderInfo.setCarrierId(sendOrderVO.getCarrierId());
        //运单资源信息表业务ID
        orderInfo.setGoodSourceCode(companySourceDTO.getCode());
        orderInfo.setLineGoodsRelId(companySourceDTO.getLineGoodsRelId());
        orderInfo.setCompanyId(sendOrderVO.getCompanyId());
        orderInfo.setCompanyProjectId(companySourceDTO.getProjectId());
        //货物相关信息
        orderInfo.setGoodsId(companySourceDTO.getGoodsId());
        orderInfo.setGoodsName(companySourceDTO.getGoodsName());
        orderInfo.setBigKindCode(companySourceDTO.getBigKindCode());
        orderInfo.setGoodsUnit(companySourceDTO.getGoodsUnit());
        orderInfo.setEstimateGoodsWeight(companySourceDTO.getEstimateGoodsWeight());
        orderInfo.setPrimaryWeight(companySourceDTO.getEstimateGoodsWeight());
        //获取当前运费单价
        orderInfo.setCurrentCarriageUnitPrice(new BigDecimal(String.valueOf(companySourceDTO.getCurrentCarriageUnitPrice())));
        //orderInfo.setPrimaryWeight(sendOrderVO.getPrimaryWeight());
        orderInfo.setDeliverWeightNotesWeight(sendOrderVO.getPrimaryWeight());
        //运费单价单位为“元/吨”时，与原有页面逻辑一致
        if(null != companySourceDTO.getCarriagePriceUnit() && DictEnum.CARRIAGE_PRICE_UNIT_CAR.code.equals(companySourceDTO.getCarriagePriceUnit())){
            orderInfo.setEstimateTotalFee(orderInfo.getCurrentCarriageUnitPrice());
        }else if(null != companySourceDTO.getCarriagePriceUnit() && DictEnum.CARRIAGE_PRICE_UNIT_BOX.code.equals(companySourceDTO.getCarriagePriceUnit())){
            Integer boxNum = sendOrderVO.getOrderInfoWeight().getBoxNum();
            orderInfo.setEstimateTotalFee(BigDecimal.valueOf(boxNum).multiply(companySourceDTO.getCurrentCarriageUnitPrice()).setScale(2, RoundingMode.HALF_UP));
            orderInfo.setPrimaryWeight(BigDecimal.valueOf(companySourceDTO.getEstimateGoodsWeight()).multiply(BigDecimal.valueOf(boxNum)).doubleValue());
            orderInfo.setEstimateGoodsWeight(BigDecimal.valueOf(companySourceDTO.getEstimateGoodsWeight()).multiply(BigDecimal.valueOf(boxNum)).doubleValue());
        }else {
            //运费总估计
            BigDecimal estimateTotalFee = BigDecimal.valueOf(companySourceDTO.getEstimateGoodsWeight()).multiply(companySourceDTO.getCurrentCarriageUnitPrice());
            orderInfo.setEstimateTotalFee(estimateTotalFee.setScale(2, RoundingMode.HALF_UP));
        }
        //线路相关信息
        orderInfo.setLineId(companySourceDTO.getLineId());
        orderInfo.setLineCode(companySourceDTO.getLineCode());
        orderInfo.setLineName(companySourceDTO.getLineName());
        orderInfo.setLineShortName(companySourceDTO.getLineShortName());
        orderInfo.setCityFromCode(companySourceDTO.getCityFromCode());
        orderInfo.setFromName(String.valueOf(companySourceDTO.getFromName()));
        orderInfo.setFromCoordinates(companySourceDTO.getFromCoordinates());
        orderInfo.setCityEndCode(companySourceDTO.getCityEndCode());
        orderInfo.setEndName(companySourceDTO.getEndName());
        orderInfo.setEndCoordinates(companySourceDTO.getEndCoordinates());
        orderInfo.setLineType(companySourceDTO.getLineType());

        //发单员id、发单联系人、手机号(线路负责人)
        orderInfo.setDeliverOrderUserId(deliver.getPrincipalAccountId());
        orderInfo.setDeliverGoodsContacter(deliver.getPrincipalName());
        orderInfo.setDeliverGoodsContacterPhone(deliver.getPrincipalPhone());
        //收单员id、收单联系人、手机号(线路负责人)
        orderInfo.setReceiveOrderUserId(receiver.getPrincipalAccountId());
        orderInfo.setReceiveGoodsContacter(receiver.getPrincipalName());
        orderInfo.setReceiveGoodsContacterPhone(receiver.getPrincipalPhone());

        //TODO 委托方、客户名称
        orderInfo.setCompanyClient(sendOrderVO.getCompanyClient());
        orderInfo.setCompanyEntrust(sendOrderVO.getCompanyEntrust());

        orderInfo.setDeliverWeightNotesPhoto(sendOrderVO.getDeliverWeightNotesPhoto());
        orderInfo.setDeliverWeightNotesTime(sendOrderVO.getDeliverWeightNotesTime());

        //委托发单员账号id:默认为空

        //费用结算模式
        orderInfo.setFeeSettlementWay(sendOrderVO.getPayMethod());
        //运单生成类型: 扫码发单
        orderInfo.setOrderCreateType(DictEnum.SCANORDER.code);
        //货源执行状态： 节点 -> 已装货
        orderInfo.setOrderExecuteStatus(DictEnum.M030.code);
        orderInfo.setContractStatus(DictEnum.WQD.code);
        //打包状态
        orderInfo.setPackStatus("0");
        orderInfo.setDeliverOrderTime(new Date());
        orderInfo.setOperateMethod(DictEnum.DRIWX.code);
        orderInfo.setRemark(sendOrderVO.getRemark());
        orderInfo.setRandomHy(sendOrderVO.getRandom());
        orderInfo.setPayMethod(companySourceDTO.getCarriagePayType());
        // 经纪人
        if (null != companySourceDTO.getCapitalTransferPattern()
                && DictEnum.MANAGERPATTERN.code.equals(companySourceDTO.getCapitalTransferPattern())) {
            if (null != companySourceDTO.getManagerId()) {
                orderInfo.setAgentId(companySourceDTO.getManagerId());
            }
        }
        // 业务部
        if (null != companySourceDTO.getBusinessAssist() && companySourceDTO.getBusinessAssist()) {
            orderInfo.setBusinessAssist(true);
            CarDriverRelVO carDriverRelVO = sendOrderVO.getCarDriverRelVO();
            if (null != carDriverRelVO.getEndAgentId()) {
                orderInfo.setEndAgentId(carDriverRelVO.getEndAgentId());
            }
        } else {
            orderInfo.setBusinessAssist(false);
        }

        //车辆司机信息
        CarDriverRelVO carDriverRelVO = sendOrderVO.getCarDriverRelVO();
        orderInfo.setEndDriverId(carDriverRelVO.getEnduserId());
        orderInfo.setVehicleId(carDriverRelVO.getEndcarId());
        orderInfo.setEndUserCarRelId(carDriverRelVO.getEnduserCarRelId());
        if (null != companySourceDTO.getCapitalTransferType() && DictEnum.PAYTOCAPTAIN.code.equals(companySourceDTO.getCapitalTransferType())) {
            if (null != carDriverRelVO.getCaptainId()) {
                orderInfo.setEndCarOwnerId(carDriverRelVO.getCaptainId());
            }
        } else {
            if (null != carDriverRelVO.getEnduserIdRel()) {
                orderInfo.setEndCarOwnerId(carDriverRelVO.getEnduserIdRel());
            }
        }

        if(null!=sendOrderVO.getGrossWeight() && !"".equals(sendOrderVO.getGrossWeight())){
            orderInfo.setGrossWeight(sendOrderVO.getGrossWeight());
        }

        return orderInfo;
    }

    public TOrderInfo createResourceHallOderInfo(CompanySourceDTO companySourceDTO, LineInfoPrincipalDTO
            receiver, LineInfoPrincipalDTO deliver,SendOrderVO sendOrderVO) {
        TOrderInfo orderInfo = new TOrderInfo();
        orderInfo.setCode(IdWorkerUtil.getInstance().nextId());
        //运单编号：线路编码 + 年月日时分秒毫秒
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String date = sdf.format(new Date());
        String random = RandomUtil.randomString(3, 10);
        // RH 货源大厅
        orderInfo.setOrderBusinessCode("RH" + date + random);
        //承运方
        orderInfo.setCarrierId(sendOrderVO.getCarrierId());
        //运单资源信息表业务ID
        orderInfo.setGoodSourceCode(companySourceDTO.getCode());
        orderInfo.setLineGoodsRelId(companySourceDTO.getLineGoodsRelId());
        orderInfo.setCompanyId(sendOrderVO.getCompanyId());
        orderInfo.setCompanyProjectId(companySourceDTO.getProjectId());
        //货物相关信息
        orderInfo.setGoodsId(companySourceDTO.getGoodsId());
        orderInfo.setGoodsName(companySourceDTO.getGoodsName());
        orderInfo.setBigKindCode(companySourceDTO.getBigKindCode());
        orderInfo.setGoodsUnit(companySourceDTO.getGoodsUnit());
        //预计装货重量
        orderInfo.setEstimateGoodsWeight(sendOrderVO.getEstimatedLoadingWeight());
        //预计装货时间
        orderInfo.setEstimateLoadTime(sendOrderVO.getEstimateLoadTime());
        orderInfo.setPrimaryWeight(sendOrderVO.getEstimatedLoadingWeight());
        //获取当前运费单价
        orderInfo.setCurrentCarriageUnitPrice(companySourceDTO.getCurrentCarriageUnitPrice());
        //orderInfo.setPrimaryWeight(sendOrderVO.getPrimaryWeight());
        //计算预计总运费
        orderInfo.setEstimateTotalFee(sendOrderVO.getCarriageFee());
        //线路相关信息
        orderInfo.setLineId(companySourceDTO.getLineId());
        orderInfo.setLineCode(companySourceDTO.getLineCode());
        orderInfo.setLineName(companySourceDTO.getLineName());
        orderInfo.setLineShortName(companySourceDTO.getLineShortName());
        orderInfo.setCityFromCode(companySourceDTO.getCityFromCode());
        orderInfo.setFromName(String.valueOf(companySourceDTO.getFromName()));
        orderInfo.setFromCoordinates(companySourceDTO.getFromCoordinates());
        orderInfo.setCityEndCode(companySourceDTO.getCityEndCode());
        orderInfo.setEndName(companySourceDTO.getEndName());
        orderInfo.setEndCoordinates(companySourceDTO.getEndCoordinates());
        orderInfo.setLineType(companySourceDTO.getLineType());

        //经纪人
        if(null != sendOrderVO.getCapitalTransferPattern()){
            if(sendOrderVO.getCapitalTransferPattern().equals(DictEnum.MANAGERPATTERN.code) && null != sendOrderVO.getManagerId()){
                orderInfo.setAgentId(sendOrderVO.getManagerId());
            }
        }

        //发单员id、发单联系人、手机号(线路负责人)
        orderInfo.setDeliverGoodsContacter(deliver.getPrincipalName());
        orderInfo.setDeliverGoodsContacterPhone(deliver.getPrincipalPhone());
        //收单员id、收单联系人、手机号(线路负责人)
        orderInfo.setReceiveOrderUserId(receiver.getPrincipalAccountId());
        orderInfo.setReceiveGoodsContacter(receiver.getPrincipalName());
        orderInfo.setReceiveGoodsContacterPhone(receiver.getPrincipalPhone());
        //费用结算模式 为了避免收单的时候空指针,则改为空字符串
        orderInfo.setFeeSettlementWay("");
        //运单生成类型: 货源大厅抢单
        orderInfo.setOrderCreateType(DictEnum.RESOURCEHALLSEND.code);
        //货源执行状态： 节点 -> 已装货
        orderInfo.setOrderExecuteStatus(DictEnum.M011.code);
        orderInfo.setContractStatus(DictEnum.WQD.code);
        //打包状态
        orderInfo.setPackStatus("0");
        orderInfo.setDeliverOrderTime(new Date());
        orderInfo.setOperateMethod(DictEnum.DRIWX.code);
        orderInfo.setRemark(sendOrderVO.getRemark());
        orderInfo.setPayMethod(companySourceDTO.getCarriagePayType());

        // 业务部
        orderInfo.setBusinessAssist(false);
        //车辆司机信息
        CarDriverRelVO carDriverRelVO = sendOrderVO.getCarDriverRelVO();
        orderInfo.setEndDriverId(carDriverRelVO.getEnduserId());
        orderInfo.setVehicleId(carDriverRelVO.getEndcarId());
        orderInfo.setEndUserCarRelId(carDriverRelVO.getEnduserCarRelId());
        if (null != carDriverRelVO.getEnduserIdRel()) {
            orderInfo.setEndCarOwnerId(carDriverRelVO.getEnduserIdRel());
        }

        return orderInfo;
    }

    /*
     * <AUTHOR>
     * @Description 扫码抢单修改扫码对应的线路类型
     * @Date 2019/11/11 15:04
     * @Param
     * @return
    **/
    private String changeLineType(String lineTypeStr) {
        switch (lineTypeStr) {
            case "DT":
                return "SD";
            case "CT":
                return "SC";
            case "LH":
                return "SL";
            case "JB":
                return "SJ";
            default:
                return lineTypeStr;
        }
    }

    /**
     * 创建运单子状态
     *
     * @param orderCode      运单主表业务id
     * @param stateNodeValue 执行状态子节点
     * @param sendBillVO
     * <AUTHOR>
     */
    private void createOrderState(Date time, String orderCode, String stateNodeValue, SendOrderVO sendBillVO) {
        //TODO 新增运单执行状态子表 TOrderState
        TOrderState orderState = new TOrderState();
        orderState.setCode(IdWorkerUtil.getInstance().nextId());
        orderState.setOrderCode(orderCode);
        //节点状态
        orderState.setStateNodeValue(stateNodeValue);
        orderState.setIfExpire(false);
        orderState.setOperatorId(CurrentUser.getCurrentUserID());
        orderState.setOperateMethod(DictEnum.DRIWX.code);
        orderState.setCreateUser(CurrentUser.getUserNickname());
        orderState.setOperateTime(time);
        orderState.setCreateTime(time);
        orderState.setEnable(false);
        orderStateMapper.insertSelective(orderState);
    }

    /**
     * 创建承运方与C端的关系
     *
     * @param carrierId        承运方id
     * @param enduserCompanyId 车辆司机信息(C端用户表ID)
     * @return createInfo 创建信息
     * <AUTHOR>
     */
    private LinkedHashMap createEnduserCarrierRel(Integer carrierId, Integer enduserCompanyId) {
        //TODO 创建承运方与C端的关系
        TCarrierEnduserCompanyRel carrierEnduserRel = new TCarrierEnduserCompanyRel();
        carrierEnduserRel.setCarrierId(carrierId);
        carrierEnduserRel.setEnduserCompanyId(enduserCompanyId);
        carrierEnduserRel.setDatasouce("CD");
        carrierEnduserRel.setUid(IdWorkerUtil.getInstance().nextId());
        carrierEnduserRel.setEnable(false);
        carrierEnduserRel.setUid(IdWorkerUtil.getInstance().nextId());
        carrierEnduserRel.setCreateUser(String.valueOf(CurrentUser.getCurrentUserID()));
        carrierEnduserRel.setCreateTime(new Date());
        ResultUtil resultUtil = carrierEnduserCompanyRelAPI.save(carrierEnduserRel);
        LinkedHashMap carrierEnduserRelMap = (LinkedHashMap) resultUtil.getData();

        return carrierEnduserRelMap;
    }

    /**
     * 创建TASK，向网商申请子账号
     *
     * @param carrierInfo                 承运方信息
     * @param carrierEnduserComapnyRelUid 承运方与C端用户关系的uid
     * @param thirdPartSubAccountVO       第三方账号信息
     * <AUTHOR>
     */
    private void applyAccount(String orderCode, LinkedHashMap carrierInfo, String carrierEnduserComapnyRelUid, ThirdPartSubAccountVO thirdPartSubAccountVO) {
        TTask tTask = new TTask();
        tTask.setTaskType("SQZZH");
        tTask.setBusinessType("ZF");
        tTask.setSourceFieldname("t_order_info");
        tTask.setSourcekeyFieldname("code");
        tTask.setSourceFieldvalue(orderCode);
        tTask.setTaskId(IdWorkerUtil.getInstance().nextId());
        HashMap<String, Object> taskParam = new HashMap<>();
        taskParam.put("thirdPartyInterfaceManageAddress", carrierInfo.get("thirdPartyInterfaceManageAddress"));
        taskParam.put("bussinessPlatformSignCode", carrierInfo.get("bussinessPlatformSignCode"));
        taskParam.put("uid", carrierEnduserComapnyRelUid);
        taskParam.put("thirdPartyInterfaceMainAccountNo", carrierInfo.get("thirdPartyInterfaceMainAccountNo"));
        //会员名称。用户昵称(平台个人会员登录名)
        taskParam.put("memberName", thirdPartSubAccountVO.getPhone());
        //真实姓名
        taskParam.put("realName", thirdPartSubAccountVO.getRealName());
        taskParam.put("certificateType", thirdPartSubAccountVO.getCertificateCategoryName());
        taskParam.put("certificateNo", thirdPartSubAccountVO.getCertificateNo());
        taskParam.put("requestTimes", 0);
        tTask.setRequestParameter(JSONObject.toJSONString(taskParam));
        tTask.setEnable(false);
        orderTaskMapper.insert(tTask);
    }

    /**
     * 创建虚拟钱包
     *
     * @param carrierEnduserCompanyId   承运方C端用户关系id信息
     * @param purseCategory 钱包类型
     * <AUTHOR>
     */
    private void createWallet(Integer carrierEnduserCompanyId, String purseCategory) {
        TWallet wallet = new TWallet();
        wallet.setCarrierEnduserCompanyId(carrierEnduserCompanyId);
        wallet.setPurseCategory(purseCategory);
        wallet.setDatasource("CD");
        wallet.setCreateUser(String.valueOf(CurrentUser.getCurrentUserID()));
        wallet.setCreateTime(new Date());
        walletService.selectAndSave(wallet);
    }

    /**
     * 根据资金转移方式--经纪人--子账号、钱包
     *
     * @param carrierInfo         承运方信息
     * @param carDriverRelVO      车辆司机信息
     */
    public void capitalTransferTypeForAngent(String orderCode, LinkedHashMap carrierInfo, CarDriverRelVO carDriverRelVO) {
        //支付给经纪人
        //经纪人id
        Integer endAgentId = carDriverRelVO.getEndAgentId();
        //TODO 查询经纪人和承运方是否有子账号
        TCarrierEnduserCompanyRelVo carrierEnduserAgentRel = new TCarrierEnduserCompanyRelVo();
        Integer carrierId = Integer.valueOf(String.valueOf(carrierInfo.get("id")));
        carrierEnduserAgentRel.setCarrierId(carrierId);
        carrierEnduserAgentRel.setDatasource("CD");
        carrierEnduserAgentRel.setEnduserCompanyId(carDriverRelVO.getEndAgentId());
        ResultUtil carrierAgentRelResult = carrierEnduserCompanyRelAPI.selectCarrierRel(carrierEnduserAgentRel);
        Object carrierAgentRelResultData = carrierAgentRelResult.getData();
        String thridParySubAccount = "";
        Integer carrierEnduserCompanyId = 0;
        if (null != carrierAgentRelResultData){
            LinkedHashMap carrier = (LinkedHashMap) carrierAgentRelResultData;
            thridParySubAccount = String.valueOf(carrier.get("thridParySubAccount"));
            carrierEnduserCompanyId = (Integer) carrier.get("id");
        }
        if (null == carrierAgentRelResultData || StringUtils.isEmpty(thridParySubAccount)) {
            //创建承运方与C端(经纪人)的关系
            LinkedHashMap enduserCarrierRel = createEnduserCarrierRel(Integer.valueOf(carrierInfo.get("id").toString()), endAgentId);
            carrierEnduserCompanyId = (Integer) enduserCarrierRel.get("id");
            //并向Task添加一条任务：司机申请子账号
            ThirdPartSubAccountVO partSubAccountVO = new ThirdPartSubAccountVO();
            partSubAccountVO.setRealName(carDriverRelVO.getEndAgentName());
            partSubAccountVO.setPhone(carDriverRelVO.getEndAgentPhone());
            partSubAccountVO.setCertificateCategoryName(CERTIFICATE_TYPE);
            partSubAccountVO.setCertificateNo(carDriverRelVO.getEndAgentIdCard());
            applyAccount(orderCode, carrierInfo, enduserCarrierRel.get("uid").toString(), partSubAccountVO);
        }
        //TODO 创建虚拟钱包
        createWallet(carrierEnduserCompanyId, DictEnum.CMANAGER.code);
    }

    /**
     * 根据资金转移方式-车老板--子账号、钱包
     * @param carrierInfo         承运方信息
     * @param carDriverRelVO      车辆司机信息
     */
    public void capitalTransferTypeForCarBoss(String orderCode, LinkedHashMap carrierInfo, CarDriverRelVO carDriverRelVO) {
        //支付给车老板
        //经纪人id
        Integer carOwnerId = carDriverRelVO.getEnduserIdRel();
        //TODO 查询车老板和承运方是否有子账号
        TCarrierEnduserCompanyRelVo carrierEnduserCarBossRel = new TCarrierEnduserCompanyRelVo();
        Integer carrierId = Integer.valueOf(String.valueOf(carrierInfo.get("id")));
        carrierEnduserCarBossRel.setCarrierId(carrierId);
        carrierEnduserCarBossRel.setDatasource("CD");
        carrierEnduserCarBossRel.setEnduserCompanyId(carDriverRelVO.getEnduserIdRel());
        ResultUtil carrierCarBossRelResult = carrierEnduserCompanyRelAPI.selectCarrierRel(carrierEnduserCarBossRel);
        Object carrierBossRelResultData = carrierCarBossRelResult.getData();
        String thridParySubAccount = "";
        Integer carrierEnduserCompanyId = 0;
        if (null != carrierBossRelResultData){
            LinkedHashMap carrier = (LinkedHashMap) carrierBossRelResultData;
            thridParySubAccount = String.valueOf(carrier.get("thridParySubAccount"));
            carrierEnduserCompanyId = (Integer) carrier.get("id");
        }
        if (null == carrierBossRelResultData || StringUtils.isEmpty(thridParySubAccount)) {
            //创建承运方与C端(车老板)的关系
            LinkedHashMap enduserCarrierRel = createEnduserCarrierRel(Integer.valueOf(carrierInfo.get("id").toString()), carOwnerId);
            carrierEnduserCompanyId = (Integer) enduserCarrierRel.get("id");
            //并向Task添加一条任务：司机申请子账号
            ThirdPartSubAccountVO partSubAccountVO = new ThirdPartSubAccountVO();
            partSubAccountVO.setRealName(carDriverRelVO.getRealNameRel());
            partSubAccountVO.setPhone(carDriverRelVO.getPhoneRel());
            partSubAccountVO.setCertificateCategoryName(CERTIFICATE_TYPE);
            partSubAccountVO.setCertificateNo(carDriverRelVO.getIdCardRel());
            applyAccount(orderCode, carrierInfo, enduserCarrierRel.get("uid").toString(), partSubAccountVO);
        }
        //TODO 创建虚拟钱包
        createWallet(carrierEnduserCompanyId, DictEnum.CCARBOSS.code);
    }

    @Override
    public ResultUtil selectPackOrderPage(String code) {

        return  ResultUtil.ok(orderInfoMapper.selectPackOrderPage(code));
    }

    @Override
    public ResultUtil wxDriverResourceHallOrder(AppOrderSearchVO search) {
        SysParam param = sysParamAPI.getParamByKey("ISSCOPEDAY");
        search.setScoreDay(Integer.parseInt(param.getParamValue()));
        Page<Object> objects = PageHelper.startPage(search.getPage(), search.getSize());
        Integer userAccountId = CurrentUser.getUserAccountId();
        Integer endUserId = CurrentUser.getEndUserId();
        String usertype = CurrentUser.getUsertype();
        search.setUserId(userAccountId);
        search.setEnduserId(endUserId);
        String userNickname = CurrentUser.getUserNickname();
        // 判断筛选条件
        AppOrderSearchVO judge = SearchOrderFilter.judge(search);
        // 判断是否是车主查询运单检查
        Integer enduserId = CurrentUser.getEndUserId();
        String userLogisticsRole = CurrentUser.getUserLogisticsRole();
        if (DictEnum.CTYPECAPTAIN.code.equals(userLogisticsRole)) {
            judge.setCarOwnerId(enduserId);
            judge.setIfCtypecaptain(true);
        } else if (DictEnum.CTYPEBOSS.code.equals(userLogisticsRole)) {
            judge.setCarOwnerId(enduserId);
        }
        // 搜索运单
        List<AppOrderListDTO> appOrderListDTOS = tOrderInfoMapper.wxDriverSelectResourceHallOrder(judge);
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        Future<BigDecimal> future = executorService.submit(() -> weixinOrderInfoMapper.wxDriverSelectResourceHallOrderSum(judge));
        // 查询司所有运单的线路
        List<EndUserLineDTO> endUserLineDTOS = new ArrayList<>();
        if (null != enduserId && StringUtils.isNotBlank(userLogisticsRole)) {
            endUserLineDTOS = weixinOrderInfoMapper.getWXUserAdministrativeResourceHallLine(enduserId, userLogisticsRole);
        }
        List<AppOrderListDTO> resultslist = new ArrayList<>();
        for (AppOrderListDTO al : appOrderListDTOS) {
            List<Map<String, String>> abnormalType = orderAbnormalMapper.wxJudgeOrderIsError(al.getCode(), "WX", userNickname);
            if (!DictEnum.WQD.code.equals(al.getContractStatus())) {
                List<Map<String, String>> filteredList = abnormalType.stream()
                        .filter(map -> !"HETONGYC".equals(map.get("abnormalType")))
                        .collect(Collectors.toList());
                if (!abnormalType.isEmpty()) {
                    al.setAbnormalType(filteredList);
                }
            } else {
                al.setAbnormalType(abnormalType);
            }

            if (null != al.getOrderExecuteStatus() && DictEnum.M030.code.equals(al.getOrderExecuteStatus())) {
                resultslist.add(al);
            }
            Boolean isFull = true;
            if (al.getDeliverWeightNotesPhoto() == null || al.getReceiveWeightNotesPhoto() == null) {
                isFull = false;
            }
            al.setInfoFull(isFull);
            /*咨询投诉信息 与货物信息*/
            try {
                OrderFeedbackVO build = OrderFeedbackVO.builder().orderCode(al.getCode()).build();
                ResultUtil orderFeedbackInfo = tOrderInfoService.getFeedbackOrderInfo(al.getCode());
                if (orderFeedbackInfo.getData().toString() != null && !"".equals(orderFeedbackInfo.getData().toString()) && orderFeedbackInfo.getData().toString().length() != 0) {
//                LinkedHashMap stringObjectMap = (LinkedHashMap) orderFeedbackInfo.getData();
                    Map<String, Object> stringObjectMap = EntityUtils.entityToMap(orderFeedbackInfo.getData());
                    build.setFeedbackUserEndType(usertype);
                    ResultUtil orderProblemDescription = complaintsAPI.getOrderProblemDescription(build);
                    if (orderProblemDescription.getData() != null) {
                        if (orderProblemDescription.getData().toString() != null && !"".equals(orderProblemDescription.getData().toString()) && orderProblemDescription.getData().toString().length() != 0) {
                            List<Map> mapData = (List<Map>) orderProblemDescription.getData();
                            for (Map map : mapData) {
                                map.put("oi", stringObjectMap);
                            }
                            al.setFeedbackMap(mapData);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("咨询投诉查询异常", e);
            }

        }

        Map<String, Object> result = new HashMap<>();
        if (resultslist.size() > 0) {
            result.put("order", resultslist.get(0));
        }
        result.put("waybill", appOrderListDTOS);
        result.put("line", endUserLineDTOS);
        // 查询总重
        BigDecimal sum = BigDecimal.ZERO;
        try {
            sum = future.get();
        } catch (Exception e) {
            log.error("查询总重量失败! {}", ThrowableUtil.getStackTrace(e));
        } finally {
            executorService.shutdown();
        }
        result.put("weight", OrderUtil.weight(sum));
        if (DictEnum.CTYPEBOSS.code.equals(CurrentUser.getUserLogisticsRole())) {
            result.put("permission", 0);
        } else if (DictEnum.CTYPECAPTAIN.code.equals(CurrentUser.getUserLogisticsRole())) {
            result.put("permission", 0);
        } else {
            result.put("permission", 1);
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), result, objects.getTotal());
    }

    @Override
    public ResultUtil countCarriageFee(SendOrderVO record) {
        BigDecimal carriageFee = BigDecimal.valueOf(record.getEstimatedLoadingWeight()).
                multiply(BigDecimal.valueOf(record.getCurrentCarriageUnitPrice()));
        record.setCarriageFee(carriageFee.setScale(2,BigDecimal.ROUND_DOWN));
        return ResultUtil.ok(record);
    }

    @Override
    public ResultUtil getTiXianList(Integer endUserId) {
        TEndUserInfo tEndUserInfo = tEndSUserInfoAPI.selectByPrimaryKey(endUserId);
        Map<String, Object> map = new HashMap<String, Object>();
        List<TxVO> list1 = null;
        List<TxVO> list2 = null;
        List<TxVO> list3 = null;
        String userLogisticsRole = CurrentUser.getUserLogisticsRole();
        log.info("用户类型", userLogisticsRole);
        if (userLogisticsRole.contains(",")){
            userLogisticsRole = String.valueOf(redisUtil.get("CROLE" + endUserId));
        }
        log.info("用户类型", userLogisticsRole);
        if (userLogisticsRole.equals(DictEnum.CTYPEDRVIVER.code)) {//司机
            list1 = orderInfoMapper.getTiXianList1(endUserId);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), list1);
        } else if (userLogisticsRole.equals(DictEnum.CTYPEBOSS.code)
                || userLogisticsRole.equals(DictEnum.CTYPECAPTAIN.code)) {//车老板或车队长
            list2 = orderInfoMapper.getTiXianList2(endUserId);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), list2);
//        } else if (tEndUserInfo.getUserLogisticsRole().equals("CTYPEMANAGER")) {//经纪人
//            list3 = orderInfoMapper.getTiXianList3(endUserId);
//            return new ResultUtil(CodeEnum.SUCCESS.getCode(), list3);
        }else{
            list3 = orderInfoMapper.getTiXianList3(endUserId);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), list3);
        }

    }

    //提现金额是否超出当月限制
    private Boolean IsQuota(String cardNo,BigDecimal total){
        TWalletChangeLog tWalletChangeLog = walletChangeLogAPI.selectSumCashByBankNo(cardNo);
        SysParam sysParam= sysParamAPI.getParamByKey("Amount");
        if(null !=sysParam){
            BigDecimal amount=new BigDecimal(sysParam.getParamValue());
        }
        return  true;
    }
    @Override
    //@LcnTransaction
    @Transactional
    public ResultUtil aftertixianWeixin(CashOutVO cashOutVO, String operateMethod) {
        try {
            Integer enduserId = null;
            String code = "";
            BigDecimal carriageFee = BigDecimal.ZERO;
            TOrderCastChanges tOrderCastChanges = orderCastChangesMapper.selectByNewOne(cashOutVO.getCode());
            if (cashOutVO.getType().equals(1)) {//单笔\预付款
                TOrderInfo tOrderInfo = tOrderInfoMapper.selectOrderByCode(cashOutVO.getCode());
                if (tOrderInfo.getOrderExecuteStatus().equals("M100") && (tOrderInfo.getOrderPayStatus().equals("M120") || tOrderInfo.getOrderPayStatus().equals("M090"))) {
                    tOrderInfoMapper.updatePayStatusByOrderCode(tOrderInfo.getCode(), "P110");
                    //修改运单支付主表状态
                    orderPayInfoMapper.updateStatusByOrderCode(tOrderInfo.getCode(), "P110");
                    //添加运单执行状态子表
                    createOrderState(operateMethod, new ArrayList<String>() {{
                        add(tOrderInfo.getCode());
                    }});
                    Payment pay = new Payment();
                    pay.Withdrawal(cashOutVO);
                    if (DictEnum.PAYTODRIVER.code.equals(tOrderCastChanges.getCapitalTransferType()) || DictEnum.FIRSTBROKERDRIVER.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                        enduserId = tOrderInfo.getEndDriverId();
                    } else if (DictEnum.PAYTOBELONGER.code.equals(tOrderCastChanges.getCapitalTransferType()) || DictEnum.FIRSTBROKERBELONGER.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                        enduserId = tOrderInfo.getEndCarOwnerId();
                    }
                    code = cashOutVO.getCode();
                    carriageFee = tOrderCastChanges.getCarriageFee().subtract(tOrderCastChanges.getServiceFee());
                    sendOrderUtil.saveBankCardMonthlyAmountByBankId(cashOutVO.getBankId(), code, carriageFee);
                } else {
                    //throw new BadRequestException("运单状态错误，无法完成提现");
                }
            } else if (cashOutVO.getType().equals(2)) { //打包
                TOrderPackInfo tOrderPackInfo = tOrderPackInfoMapper.selectOrderPackByCode(cashOutVO.getCode());
                if (tOrderPackInfo.getPackStatus().equals(DictEnum.PACKPAID.code) || tOrderPackInfo.getPackStatus().equals(DictEnum.PACKEWITHDRAWERROR.code)) {
                    tOrderPackInfoMapper.updatePayStatusByOrderCode(tOrderPackInfo.getCode(), DictEnum.PACKEXTRACTPROCESSED.code);
                    tOrderInfoMapper.updateStatusByPayCode(tOrderPackInfo.getCode(), "M100", "P110");
                    //修改运单支付主表状态
                    orderPayInfoMapper.updateStatusByOrderCode(tOrderPackInfo.getCode(), "P110");
                    //添加运单执行状态子表
                    List<String> codes = tOrderPackInfoMapper.selectOrderCodeByPackCode(tOrderPackInfo.getCode());
                    createOrderState(operateMethod, codes);
                    Payment pay = new Payment();
                    pay.DBWithdrawal(cashOutVO);
                    enduserId = tOrderPackInfo.getEndUserId();
                    code = tOrderPackInfo.getCode();
                    carriageFee = tOrderPackInfo.getAppointmentPaymentCash().subtract(tOrderPackInfo.getTotalSelectedOrdersServiceFee());
                    sendOrderUtil.saveBankCardMonthlyAmountByBankId(cashOutVO.getBankId(), code, carriageFee);
                } else {
                    //throw new BadRequestException("运单状态错误，无法完成提现");
                }
            } else if (cashOutVO.getType().equals(3)){//预支付提现
                Payment pay = new Payment();
                pay.advanceWithdrawal(cashOutVO);
                TOrderInfo tOrderInfo = tOrderInfoMapper.selectOrderByCode(cashOutVO.getCode());
                if (DictEnum.PAYTODRIVER.code.equals(tOrderCastChanges.getCapitalTransferType()) || DictEnum.FIRSTBROKERDRIVER.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                    enduserId = tOrderInfo.getEndDriverId();
                } else if (DictEnum.PAYTOBELONGER.code.equals(tOrderCastChanges.getCapitalTransferType()) || DictEnum.FIRSTBROKERBELONGER.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                    enduserId = tOrderInfo.getEndCarOwnerId();
                }
                code = cashOutVO.getAdvanceCode();
                carriageFee = cashOutVO.getAdvanceFee();
                sendOrderUtil.saveBankCardMonthlyAmountByBankId(cashOutVO.getBankId(), code, carriageFee);
            }else if (cashOutVO.getType().equals(4)){//节点支付类型提现
                TOrderInfo tOrderInfo = tOrderInfoMapper.selectOrderByCode(cashOutVO.getCode());
                if(DictEnum.WKPAYNODE.code.equals(cashOutVO.getPayNodeType())){
                    if (tOrderInfo.getOrderExecuteStatus().equals("M100") && (tOrderInfo.getOrderPayStatus().equals("M120") || tOrderInfo.getOrderPayStatus().equals("M090"))) {
                        tOrderInfoMapper.updatePayStatusByOrderCode(tOrderInfo.getCode(), "P110");
                    }
                }
                //修改运单支付主表状态
                orderPayInfoMapper.updateStatusByOrderCodeAndPayMethod(tOrderInfo.getCode(), "P110",cashOutVO.getPayNodeType());
                TOrderPayRule re = new TOrderPayRule();
                re.setOrderCode(cashOutVO.getCode());
                re.setPayNodeType(cashOutVO.getPayNodeType());
                TOrderPayRule tOrderPayRule = tOrderPayRuleService.selectByOrderCodeAndType(re);
                tOrderPayRule.setWithdrawTime(new Date());
                tOrderPayRule.setWithdrawStatus(DictEnum.PACKEXTRACTPROCESSED.code);
                //修改支付规则表相应节点
                tOrderPayRuleMapper.updateByPrimaryKeySelective(tOrderPayRule);
                //添加运单执行状态子表
                nodeCreateOrderState(operateMethod, new ArrayList<String>() {{ add(tOrderInfo.getCode()); }},cashOutVO.getPayNodeType());
                Payment pay = new Payment();
                pay.nodePayWithdrawal(cashOutVO);
                code = cashOutVO.getCode();
                sendOrderUtil.saveBankCardMonthlyAmountByBankId(cashOutVO.getBankId(), code, cashOutVO.getNodeFee());
            }
        }catch (Exception e){
            log.error("ZJJ-048:运单提现失败!", e);
            String message = e.getMessage();
            if (message.equals(BusinessCode.CWALLETNSUFFICIENT.code)){
                throw new RuntimeException("余额不足，提现失败");
            }
            if (StringUtils.checkChineseCharacter(message)){
                throw new RuntimeException(message);
            }
            throw new RuntimeException("ZJJ-048:运单提现失败!");
        }
        return ResultUtil.ok("提现成功");
    }

    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil aftertixian(List<CashOutVO> list, Integer bankId,BigDecimal total, String operateMethod) {
        if(list == null || list.size() <= 0){
            throw new RuntimeException("提现单号为空，请重新提交！");
        }

        try {
            for (int i = 0; i < list.size(); i++) {
                CashOutVO cashOutVO = list.get(i);
                Integer enduserId = null;
                String code = "";
                BigDecimal carriageFee = BigDecimal.ZERO;
                if(cashOutVO.getType().equals(1)){//单笔
                    TOrderInfo tOrderInfo = tOrderInfoMapper.selectOrderByCode(cashOutVO.getCode());
                    if (tOrderInfo.getOrderExecuteStatus().equals("M100") && (tOrderInfo.getOrderPayStatus().equals("M120") || tOrderInfo.getOrderPayStatus().equals("M090"))) {
                        tOrderInfoMapper.updatePayStatusByOrderCode(tOrderInfo.getCode(), "P110");
                        //修改运单支付主表状态
                        orderPayInfoMapper.updateStatusByOrderCode(tOrderInfo.getCode(), "P110");
                        //添加运单执行状态子表
                        createOrderState(operateMethod, new ArrayList<String>(){{add(tOrderInfo.getCode());}});

                        Payment pay= new Payment();
                        pay.Withdrawal(cashOutVO);

                        TOrderCastChanges tOrderCastChanges = orderCastChangesMapper.selectByNewOne(cashOutVO.getCode());
                        if (DictEnum.PAYTODRIVER.code.equals(tOrderCastChanges.getCapitalTransferType()) || DictEnum.FIRSTBROKERDRIVER.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                            enduserId = tOrderInfo.getEndDriverId();
                        } else if (DictEnum.PAYTOBELONGER.code.equals(tOrderCastChanges.getCapitalTransferType()) || DictEnum.FIRSTBROKERBELONGER.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                            enduserId = tOrderInfo.getEndCarOwnerId();
                        }
                        carriageFee = tOrderCastChanges.getCarriageFee().subtract(tOrderCastChanges.getServiceFee());
                        sendOrderUtil.saveBankCardMonthlyAmountByBankId(bankId, cashOutVO.getCode(), carriageFee);
                    } else {
                        throw new BadRequestException("运单状态错误，无法完成提现");
                    }
                }else if (cashOutVO.getType().equals(2)){ //打包
                    TOrderPackInfo tOrderPackInfo = tOrderPackInfoMapper.selectOrderPackByCode(cashOutVO.getCode());
                    if (tOrderPackInfo.getPackStatus().equals(DictEnum.PACKPAID.code) || tOrderPackInfo.getPackStatus().equals(DictEnum.PACKEWITHDRAWERROR.code)) {
                        tOrderPackInfoMapper.updatePayStatusByOrderCode(tOrderPackInfo.getCode(), DictEnum.PACKEXTRACTPROCESSED.code);
                        tOrderInfoMapper.updateStatusByPayCode(tOrderPackInfo.getCode(),"M100","P110");
                        //修改运单支付主表状态
                        orderPayInfoMapper.updateStatusByOrderCode(tOrderPackInfo.getCode(), "P110");
                        //添加运单执行状态子表
                        List<String> codes = tOrderPackInfoMapper.selectOrderCodeByPackCode(tOrderPackInfo.getCode());
                        createOrderState( operateMethod, codes);
                        Payment pay= new Payment();
                        pay.DBWithdrawal(cashOutVO);
                        TOrderCastChanges tOrderCastChanges = orderCastChangesMapper.selectByNewOne(cashOutVO.getCode());
                        enduserId = tOrderPackInfo.getEndUserId();
                        carriageFee = tOrderCastChanges.getCarriageFee().subtract(tOrderCastChanges.getServiceFee());
                        sendOrderUtil.saveBankCardMonthlyAmountByBankId(bankId, cashOutVO.getCode(), carriageFee);
                    } else {
                        throw new BadRequestException("运单状态错误，无法完成提现");
                    }
                }else {
                    throw new BadRequestException("未知状态，无法完成提现");
                }
            }
        }catch (Exception e){
            log.error("ZJJ-048:运单提现失败!", e);
            String message = e.getMessage();
            if (message.equals(BusinessCode.CWALLETNSUFFICIENT.code)){
                throw new RuntimeException("钱包余额不足，无法提现");
            }
            if (StringUtils.checkChineseCharacter(message)){
                throw new RuntimeException(message);
            }
            throw new RuntimeException("ZJJ-048:运单提现失败!");
        }
        return ResultUtil.ok();
    }


    /**
    * @Description 创建运单执行状态子表
    * <AUTHOR>
    * @Date   2019/7/12 11:46
    * @Param  operateMethod 操作方式
    * @param codes 运单主表业务id 或打包主表业务id
    * @Return
    * @Exception
    *
    */
    private void createOrderState(String operateMethod, List<String> codes) {
        //添加运单执行状态子表
        IdWorkerUtil idWorkerUtil = IdWorkerUtil.getInstance();
        if (null != codes && codes.size() >=1 ){
            ArrayList<TOrderState> orderStates = new ArrayList<>();
            for (String code :codes){
                //添加运单执行状态子表
                TOrderState orderState = new TOrderState();
                String userLogisticsRole = CurrentUser.getUserLogisticsRole();
                if (null !=userLogisticsRole && userLogisticsRole.equals(DictEnum.CTYPEDRVIVER.code)){
                    orderState.setStateNodeValue(DictEnum.SP1101.code);
                } else if (null !=userLogisticsRole && userLogisticsRole.equals(DictEnum.CTYPEBOSS.code)){
                    orderState.setStateNodeValue(DictEnum.SP1102.code);
                } else if (null !=userLogisticsRole && userLogisticsRole.equals(DictEnum.CTYPEMANAGER.code)){
                    orderState.setStateNodeValue(DictEnum.SP1103.code);
                } else {
                    orderState.setStateNodeValue(DictEnum.SP1100.code);
                }
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(new Date());
                calendar.add(Calendar.SECOND, 1);
                orderState.setCreateUser(CurrentUser.getUserNickname());
                orderState.setCreateTime( calendar.getTime());
                orderState.setOperatorId(CurrentUser.getCurrentUserID());
                orderState.setOperatorIp("");
                orderState.setOperateMethod(operateMethod);
                orderState.setOperateTime(new Date());
                orderState.setOperateGeographyPosition("");
                orderState.setIfExpire(false);
                //添加运单执行状态子表
                orderState.setCode(idWorkerUtil.nextId());
                orderState.setOrderCode(code);
                orderState.setUpdateUser(CurrentUser.getUserNickname());
                orderState.setUpdateTime(orderState.getCreateTime());
                orderState.setEnable(false);
                orderStates.add(orderState);
            }
            orderStateMapper.batchInsert(orderStates);

        }
    }


    /**
     * @Description 创建运单执行状态子表
     * <AUTHOR>
     * @Date   2019/7/12 11:46
     * @Param  operateMethod 操作方式
     * @param codes 运单主表业务id 或打包主表业务id
     * @Return
     * @Exception
     *
     */
    private void nodeCreateOrderState(String operateMethod, List<String> codes,String payNodeType) {
        //添加运单执行状态子表
        IdWorkerUtil idWorkerUtil = IdWorkerUtil.getInstance();
        if (null != codes && codes.size() >=1 ){
            ArrayList<TOrderState> orderStates = new ArrayList<>();
            for (String code :codes){
                //添加运单执行状态子表
                TOrderState orderState = new TOrderState();
                String userLogisticsRole = CurrentUser.getUserLogisticsRole();
                if(DictEnum.ZHPAYNODE.code.equals(payNodeType)){
                    orderState.setStateNodeValue(DictEnum.SP1104.code);
                }else if(DictEnum.XHPAYNODE.code.equals(payNodeType)){
                    orderState.setStateNodeValue(DictEnum.SP1105.code);
                }else if(DictEnum.WKPAYNODE.code.equals(payNodeType)){
                    orderState.setStateNodeValue(DictEnum.SP1106.code);
                }else{
                    if (null !=userLogisticsRole && userLogisticsRole.equals(DictEnum.CTYPEDRVIVER.code)){
                        orderState.setStateNodeValue(DictEnum.SP1101.code);
                    } else if (null !=userLogisticsRole && userLogisticsRole.equals(DictEnum.CTYPEBOSS.code)){
                        orderState.setStateNodeValue(DictEnum.SP1102.code);
                    } else if (null !=userLogisticsRole && userLogisticsRole.equals(DictEnum.CTYPEMANAGER.code)){
                        orderState.setStateNodeValue(DictEnum.SP1103.code);
                    } else {
                        orderState.setStateNodeValue(DictEnum.SP1100.code);
                    }
                }
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(new Date());
                calendar.add(Calendar.SECOND, 1);
                orderState.setCreateUser(CurrentUser.getUserNickname());
                orderState.setCreateTime( calendar.getTime());
                orderState.setOperatorId(CurrentUser.getCurrentUserID());
                orderState.setOperatorIp("");
                orderState.setOperateMethod(operateMethod);
                orderState.setOperateTime(new Date());
                orderState.setOperateGeographyPosition("");
                orderState.setIfExpire(false);
                //添加运单执行状态子表
                orderState.setCode(idWorkerUtil.nextId());
                orderState.setOrderCode(code);
                orderState.setUpdateUser(CurrentUser.getUserNickname());
                orderState.setUpdateTime(orderState.getCreateTime());
                orderState.setEnable(false);
                orderStates.add(orderState);
            }
            orderStateMapper.batchInsert(orderStates);

        }
    }
    /**
     * @Description 发单查询车辆司机状态
     * <AUTHOR>
     * @Date   2019/6/2 11:58
     * @Param
     * @Return
     * @Exception
     *
     */
    public EnduserCarStatus selectEnduserCarStatus(CarDriverRelVO carDriverRelVO){
        List<EnduserCarStatus> enduserCarStatusList = new ArrayList<>();
        EnduserCarStatus status = new EnduserCarStatus();
        status.setEndcarId(carDriverRelVO.getEndcarId());
        status.setEnduserId(carDriverRelVO.getEnduserId());
        enduserCarStatusList.add(status);
        TEnduserCarStatus enduserCarStatus = new TEnduserCarStatus();
        enduserCarStatus.setEnduserCarStatuses(enduserCarStatusList);
        ResultUtil usercarStatus = endSUserInfoAPI.selectEnduserCarStatus(enduserCarStatus);
        if (null != usercarStatus && null != usercarStatus.getCode()) {
            if (usercarStatus.getCode().equals("error")) {
                return null;
            }
        }
        LinkedHashMap data = (LinkedHashMap) usercarStatus.getData();
        ArrayList<LinkedHashMap> enduserCarStatuses = (ArrayList) data.get("enduserCarStatuses");
        ObjectMapper objectMapper = new ObjectMapper();
        if (null != enduserCarStatuses && enduserCarStatuses.size() >0){
            LinkedHashMap map = enduserCarStatuses.get(0);
            EnduserCarStatus userCarStatus = objectMapper.convertValue(map, EnduserCarStatus.class);
            return userCarStatus;
        }
        return null;
    }


    /**
     * @Description 判断车辆司机状态
     * <AUTHOR>
     * @Date   2019/6/2 14:26
     * @Param
     * @Return 1: 可用司机车辆 2. 不可用司机车辆
     * @Exception
     *
     */
    public HashMap<String, Object> checkUserCarStatus(CarDriverRelVO carDriverRelVO, EnduserCarStatus enduserCarStatus) {
        HashMap<String, Object> map = new HashMap<>();

        StringBuilder sb = new StringBuilder();
        List<CarDriverRelVO> carUser = new ArrayList<>();
        if (enduserCarStatus.getEndcarId().equals(carDriverRelVO.getEndcarId())) {
            if (null != enduserCarStatus.getIdCardPerfect() && !enduserCarStatus.getIdCardPerfect()) {
                map.put("idCardPerfect", "1");
                return map;
            }
            if (!enduserCarStatus.getEndcarUseable()){
                sb.append(carDriverRelVO.getVehicleNumber()).append(":").append(enduserCarStatus.getEndcarStatus()).append(",");
            }
            if (!enduserCarStatus.getEnduserUseable()){
                sb.append(carDriverRelVO.getRealName()).append(":").append(enduserCarStatus.getEnduserStatus()).append(",");
            }
            if (enduserCarStatus.getEndcarUseable() && enduserCarStatus.getEnduserUseable()){
                carUser.add(carDriverRelVO);
            }
        }
        map.put("unable", sb.toString());
        map.put("useable", carUser);
        return map;
    }

    /**
     * 判断司机、车辆是否可用
     * @param enduserCarStatus
     * @return
     */
    public boolean checkUserCarUseable(EnduserCarStatus enduserCarStatus){
        return enduserCarStatus.getEndcarUseable() && enduserCarStatus.getEnduserUseable();
    }

    /**
     * @Description 判断司、车辆审核状态
     */
    public String checkUserCarAuditStatus(CarDriverRelVO carDriverRelVO, EnduserCarStatus enduserCarStatus) {
        StringBuilder sb = new StringBuilder();
        if (enduserCarStatus.getEndcarId().equals(carDriverRelVO.getEndcarId())) {
            if (!DictEnum.PASSNODE.code.equals(enduserCarStatus.getCarAuditStatus())) {
                sb.append(carDriverRelVO.getVehicleNumber()).append("车辆资料尚未完成认证，无法接单。请完善需要接单运输的车辆的资料，等待审核通过后，继续抢单。");
            }
            if (!DictEnum.PASSNODE.code.equals(enduserCarStatus.getUserAuditStatus())) {
                sb.append("您的个人资料尚未完成认证，无法接单。请立即前往完善资料，等待审核通过后，继续抢单。");
            }
        }
        return sb.toString();
    }

    @Override
    public ResultUtil businessOrder(AppOrderSearchVO search) {
        Map<String, Object> result = new HashMap<>();

        // 判断筛选条件
        AppOrderSearchVO judge = SearchOrderFilter.judge(search);
        search.setBusinessType(true);
        if (null == search.getCompanyId()) {
            result.put("waybill", Collections.emptyList());
            result.put("weight", 0);
            return new ResultUtil(CodeEnum.SUCCESS.getCode(), result, 0L);
        }
        if (!CurrentUser.isBusinessLeader()) {
            // 查询当前用户的业务线路
            Set<Integer> projects = orderBusinessCompanyMapper.selectBusinessProjects(CurrentUser.getCurrentUserID());
            judge.setProjectIds(new ArrayList<>(projects));
        }
        // 查询总重
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        Future<BigDecimal> future = executorService.submit(() -> weixinOrderInfoMapper.businessOrderOrderSum(judge));
        Page<Object> objects = PageHelper.startPage(search.getPage(), search.getSize());
        // 搜索运单
        List<AppOrderListDTO> appOrderListDTOS = weixinOrderInfoMapper.businessOrder(judge);
        result.put("waybill", appOrderListDTOS);

        // 获取总重
        BigDecimal sum = BigDecimal.ZERO;
        try {
            sum = future.get();
        } catch (Exception e) {
            log.error("查询总重量失败! ", e);
        } finally {
            executorService.shutdown();
        }
        result.put("weight", OrderUtil.weight(sum));
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), result, objects.getTotal());
    }
}
