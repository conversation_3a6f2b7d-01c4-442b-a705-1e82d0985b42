package com.lz.service.impl.thirdparty;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.lz.common.dbenum.BankNameEnum;
import com.lz.common.model.CodeEnum;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.dao.TOrderInfoMapper;
import com.lz.dao.TOrderTaskMapper;
import com.lz.model.TOrderInfo;
import com.lz.payment.hxyh.HXPayOrderUtil;
import com.lz.response.ThirdPartyOrderFileResponse;
import com.lz.response.ThirdPartyOrderResponse;
import com.lz.schedule.model.TTask;
import com.lz.schedule.model.TTaskHistory;
import com.lz.service.thirdparty.ThirdPartyOrderPayService;
import com.lz.util.ThirdPartyPayUtil;
import com.lz.vo.ThirdPayVO;
import com.lz.vo.hxyh.HXOrderPayVO;
import com.lz.vo.hxyh.HXSinglePayVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;

@Slf4j
@Service
public class ThirdPartyOrderPayServiceImpl implements ThirdPartyOrderPayService {

    @Resource
    private TOrderInfoMapper orderInfoMapper;

    @Resource
    private TOrderTaskMapper orderTaskMapper;

    @Resource
    private HXPayOrderUtil hxPayOrderUtil;

    @Resource
    private ThirdPartyPayUtil thirdPartyPayUtil;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil<String> pay(TTask task) {
        ThirdPayVO payVO = JSONUtil.toBean(task.getRequestParameter(), ThirdPayVO.class);
        TOrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(payVO.getOrderId());
        HXSinglePayVO hxSinglePayVO = new HXSinglePayVO();
        hxSinglePayVO.setUserConfirmCarriagePayment(orderInfo.getUserConfirmPaymentAmount());
        hxSinglePayVO.setUserConfirmServiceFee(orderInfo.getUserConfirmServiceFee());
        ResultUtil resultUtil = hxPayOrderUtil.checkOrderPay(orderInfo, hxSinglePayVO, BankNameEnum.HXBANK.key);
        if (null == resultUtil) {
            log.info("开始支付: {}", orderInfo.getCode());
            try {
                HXOrderPayVO hxOrderPayVO = new HXOrderPayVO();
                hxOrderPayVO.setId(orderInfo.getId());
                hxOrderPayVO.setCode(orderInfo.getCode());
                hxOrderPayVO.setNewUserConfirmCarriagePayment(orderInfo.getUserConfirmPaymentAmount());
                hxOrderPayVO.setServiceFee(BigDecimal.ZERO);
                hxOrderPayVO.setUserConfirmServiceFee(BigDecimal.ZERO);
                thirdPartyPayUtil.pay(task);
            } catch (Exception e) {
                log.error("第三方平台运单支付失败", e);
                throw e;
            }
            // hxOrderPayService.pay(hxOrderPayVO);
            orderTaskMapper.insertHistory(BeanUtil.toBean(task, TTaskHistory.class));
            orderTaskMapper.delteByPrimaryKey(task.getId());
            log.info("支付完成: {}", orderInfo.getCode());
        } else {
            ThirdPartyOrderResponse response = new ThirdPartyOrderResponse();
            response.setOrderBusinessCode(orderInfo.getOrderBusinessCode());
            response.setOrderCode(orderInfo.getCode());
            ResultUtil<String> result = new ResultUtil<>();
            result.setCode(CodeEnum.FAIL.getCode());
            result.setData(response);
            result.setMsg(StringUtils.checkChineseCharacter(result.getMsg()) ? result.getMsg() : "支付失败");
            String postResponse = HttpUtil.post(payVO.getNotifyUrl(), JSONUtil.toJsonStr(result));
            log.info("发送第三方平台运单支付回调结果, {}", postResponse);
            return result;
        }
        return ResultUtil.ok();

    }

    @Override
    public void pushFile(ThirdPartyOrderFileResponse responses) {
        HttpUtil.post(responses.getNotifyUrl(), JSONUtil.toJsonStr(responses));
    }

}
