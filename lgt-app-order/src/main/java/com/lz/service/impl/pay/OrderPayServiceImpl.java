package com.lz.service.impl.pay;

import com.lz.api.MqAPI;
import com.lz.common.config.HXPropertiesConfig;
import com.lz.common.constants.MqTagConstants;
import com.lz.common.constants.MqTopicConstants;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.HXTradeTypeEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.MQMessage;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.DateUtils;
import com.lz.dao.*;
import com.lz.dto.OrderPayRequestAmount;
import com.lz.model.TOrderPayDetail;
import com.lz.model.TOrderPayInfo;
import com.lz.model.TOrderPayRequest;
import com.lz.payment.TradeType;
import com.lz.payment.hxyh.HXWalletUtil;
import com.lz.service.pay.OrderPayService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.vo.pay.OrderBatchPay;
import commonSdk.requestModel.CustomerBalancePayRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.*;

@Slf4j
@Service
public class OrderPayServiceImpl implements OrderPayService {

    @Resource
    private TOrderPayRequestMapper orderPayRequestMapper;

    @Resource
    private TOrderPayRequestDetailMapper orderPayRequestDetailMapper;

    @Resource
    private OrderSearchMapper orderSearchMapper;

    @Resource
    private TOrderPayInfoMapper orderPayInfoMapper;

    @Resource
    private TOrderPayDetailMapper orderPayDetailMapper;

    @Resource
    private HXWalletUtil hxWalletUtil;

    @Resource
    private HXPropertiesConfig hxPropertiesConfig;

    @Resource
    private MqAPI mqAPI;

    @Resource
    private SysParamAPI sysParamAPI;

    @Transactional(rollbackOn = Exception.class)
    @Override
    public void batchPay(OrderBatchPay pay) {
        OrderPayRequestAmount orderPayRequestAmount = orderSearchMapper.selectFee(pay.getList());

        // 支付请求
        TOrderPayRequest orderPayRequest = new TOrderPayRequest();
        orderPayRequest.setCode(IdWorkerUtil.createWithRandomIds().nextId());
        orderPayRequest.setCompanyId(pay.getCompanyId());
        orderPayRequest.setCarrierId(pay.getCarrierId());
        orderPayRequest.setOperatorId(CurrentUser.getCurrentUserID());
        orderPayRequest.setStatus(1);
        orderPayRequestMapper.insertSelective(orderPayRequest);

        // 支付请求详情
        Map<String, Object> map = new HashMap<>();
        map.put("list", pay.getList());
        map.put("operatorId", CurrentUser.getUserAccountId());
        map.put("createUser", CurrentUser.getUserNickname());
        map.put("requestId", orderPayRequest.getId());
        orderPayRequestDetailMapper.insertBatch(map);

        // 支付主表
        TOrderPayInfo orderPayInfo = new TOrderPayInfo();
        orderPayInfo.setCode(IdWorkerUtil.createWithRandomIds().nextId());
        orderPayInfo.setPayMethod(DictEnum.SINGLEPAY.code);
        orderPayInfo.setOrderCode(orderPayRequest.getCode());
        orderPayInfo.setCarrierId(orderPayRequest.getCarrierId());
        orderPayInfo.setPaymentPlatforms(DictEnum.HXPLATFORMS.code);
        orderPayInfo.setOrderPayStatus(DictEnum.P070.code);
        orderPayInfo.setOrderPrepayAmount(orderPayRequestAmount.getTotalAmount());
        orderPayInfo.setOrderActualPayment(orderPayRequestAmount.getDispatchFee());
        orderPayInfo.setOrderTotalPayment(orderPayRequestAmount.getTotalAmount());
        // 华夏支付
        orderPayInfo.setParam1(DictEnum.HXPAY.code);
        orderPayInfoMapper.insertSelective(orderPayInfo);

        // 支付子表
        TOrderPayDetail orderPayDetail = new TOrderPayDetail();
        orderPayDetail.setCode(IdWorkerUtil.createWithRandomIds().nextId());
        orderPayDetail.setOrderPayCode(orderPayInfo.getCode());
        orderPayDetail.setTradeType(HXTradeTypeEnum.HX_COMBALANCE_PAY.code);
        orderPayDetail.setOperateState(TradeType.RZ.code);
        orderPayDetail.setOperateTime(new Date());
        orderPayDetail.setOperaterId(CurrentUser.getCurrentUserID());
        orderPayDetail.setCreateUser(CurrentUser.getUserNickname());
        orderPayDetailMapper.insertSelective(orderPayDetail);

        //修改企业钱包,将总费用挪到入账中
        hxWalletUtil.modifyWallet(pay.getCompanyWalletId(), orderPayRequestAmount.getTotalAmount(), "");

        // 构造支付请求参数
        CustomerBalancePayRequest req = new CustomerBalancePayRequest();
        req.setPartnerId(hxPropertiesConfig.getPartnerId());
        req.setRequestId(IdWorkerUtil.createWithRandomIds().nextId());
        req.setRequestTime(DateUtils.getRequestTime());
        req.setChannelId(hxPropertiesConfig.getChannelId());
        req.setBizOrderNo(orderPayDetail.getCode());
        req.setOutPartnerAccId(pay.getOutPartnerAccId());
        req.setInPartnerAccId(pay.getInPartnerAccId());
        req.setOrderAmount(orderPayRequestAmount.getTotalAmount());
        req.setTradeAbstract("运费");
        // 查询回调地址
        SysParam paramByKey = sysParamAPI.getParamByKey(DictEnum.PAYCALLBACKURL.code);
        req.setNotifyUrl(paramByKey.getParamValue());

        MQMessage message = new MQMessage();
        message.setTopic(MqTopicConstants.COMPANY_PAY);
        message.setTag(MqTagConstants.COMPANY_CARRIER);
        message.setKey(orderPayRequest.getCode());
        message.setBody(req);
        mqAPI.sendMessage(message);
    }

    @Override
    public Integer companyUnique(Integer[] ids) {
        return orderSearchMapper.companyUnique(ids);
    }

    @Override
    public Integer carrierUnique(Integer[] ids) {
        return orderSearchMapper.carrierUnique(ids);
    }

    @Override
    public Integer[] commonPatternUnique(Integer[] ids) {
        return orderSearchMapper.commonPatternUnique(ids);
    }

    @Override
    public Integer[] selectOrderIds(Set<String> orderBusinessCodes, Integer companyId) {
        return orderSearchMapper.selectOrderIds(orderBusinessCodes, companyId);
    }

    @Override
    public Integer[] selectOrderIds(Integer[] ids, Integer companyId) {
        return orderSearchMapper.selectOrderIdsByOrderIds(ids, companyId);
    }

    @Override
    public List<String> selectOrderBusinessCodes(Integer[] ids) {
        return orderSearchMapper.selectOrderBusinessCodes(ids);
    }

    @Override
    public Integer[] selectOrderIdsByOrderBusinessCode(List<String> orderBusinessCodes) {
        return orderSearchMapper.selectOrderIdsByOrderBusinessCode(orderBusinessCodes);
    }

}
