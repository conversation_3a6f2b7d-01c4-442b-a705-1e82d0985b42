package com.lz.service;


import com.lz.common.util.ResultUtil;
import com.lz.model.trajectory.resp.TrajectoryVLastLocationResp;
import com.lz.model.trajectory.resp.recent.TrajectoryRecentResp;
import com.lz.model.trajectory.resp.recent.TransTimeManageVResp;
import com.lz.tpu.web.reqeuest.PaymentRequest;
import com.lz.vo.TtoolboxVo;

import java.util.Map;

public interface TtoolboxService{
    public ResultUtil renewalOfContract(TtoolboxVo ttoolboxVo);

    public ResultUtil renewalOfTxxy(TtoolboxVo ttoolboxVo);

    public ResultUtil driverUnwrap(TtoolboxVo ttoolboxVo);

    public ResultUtil companyUnwrap(TtoolboxVo ttoolboxVo);

    public ResultUtil driverChangeMobile(TtoolboxVo ttoolboxVo);

    public TrajectoryRecentResp vHisTrack24(TtoolboxVo ttoolboxVo);

    ResultUtil fundsFallback(TtoolboxVo ttoolboxVo);

    public ResultUtil checkTruckExist(TtoolboxVo ttoolboxVo);

    public TransTimeManageVResp vLastLocation(String vln);

    public ResultUtil balanceAccount(PaymentRequest paymentRequest);

    public ResultUtil ImportExcel(TtoolboxVo ttoolboxVo);


    public ResultUtil ydhtqs(Map<String,Object> map) throws Exception;
}
