package com.lz.service;

import com.lz.common.util.ResultUtil;
import com.lz.model.TOrderInfo;
import com.lz.vo.AppOrderSearchVO;
import com.lz.vo.CashOutVO;
import com.lz.vo.SendOrderVO;
import com.lz.vo.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2019/5/22 - 19:02
 **/

public interface WxOrderInfoService {

    /**
     * WX司机查询运单
     * Yan
     *
     * @param search
     * @return
     */
    ResultUtil wxDriverOrder(AppOrderSearchVO search);

    /**
     * 抢单、装货、签到
     *
     * @param record
     * @return
     */
    ResultUtil grabOrder(SendOrderVO record);

    ResultUtil grabHallOrder(SendOrderVO record);

    /**
     * 司机取消运单
     *
     * @param orderInfoVO
     * @return
     */
    ResultUtil cancleOrder(TOrderInfoVO orderInfoVO);


    ResultUtil selectTiXianPage(Integer endUserId);

    ResultUtil jdTixianPage();

    ResultUtil hxTixianPage();

    TOrderInfo selectByPrimaryKey(Integer orderinfoId);

    ResultUtil aftertixian(List<CashOutVO> list, Integer bankId, BigDecimal total, String operateMethod);
    ResultUtil aftertixianWeixin(CashOutVO cashOutVO, String operateMethod);

    ResultUtil getTiXianList(Integer endUserId);

    ResultUtil selectPackOrderPage(String code);

    ResultUtil wxDriverResourceHallOrder(AppOrderSearchVO search);

    ResultUtil countCarriageFee(SendOrderVO record);

    ResultUtil businessOrder(AppOrderSearchVO search);

}
