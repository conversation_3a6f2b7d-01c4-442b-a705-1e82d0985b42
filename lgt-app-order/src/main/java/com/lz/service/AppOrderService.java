package com.lz.service;

import com.lz.common.util.ResultUtil;
import com.lz.dto.CompanySourceDTO;
import com.lz.dto.LineInfoPrincipalDTO;
import com.lz.model.TOrderInfo;
import com.lz.vo.*;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * 企业端 APP 发单、收单
 */
public interface AppOrderService {

    /** 发单 */
    ResultUtil saveSendOrder(String code, SendOrderVO sendBillVO, CarDriverRelVO carDriverRelVO, TOrderInfo orderInfo,
                             String linetype, String capitalTransferType, LinkedHashMap carrier, CompanySourceDTO companySourceDTO);

    //批量导入收单
    public ResultUtil batchExcelReceiveOrder(List<BatchExcelReceiveOrder>  receiveOrderList);

    /** 收单 */
    ResultUtil recieveOrder(OrderDetailVO record);

    /** 磅房自动收单 */
    ResultUtil recieveOrderAutomatic(OrderDetailVO record);

    /** 收单 */
    ResultUtil reRecieveOrder(OrderDetailVO record);

    /** 支付 */
    ResultUtil payOrder(TOrderInfoVO record);

    ResultUtil nodePay(TOrderInfo record,SendOrderVO sendOrderVO);

    /** 运单打包支付 */
    ResultUtil packPay(String codes);
    /**
     * App 根据 业务Id 查询运单详情
     * Yan
     * @param code
     * @return
     */
    ResultUtil appSelectOrderDetails(String code, String paymentPlatforms)  throws RuntimeException ;

    /**
     * APP 运单详情查询运单的结算规则  在快照表, 没有收单的查原始的
     * 这个是快照的规则
     * Yan
     * @param code
     * @return
     */
    ResultUtil appSelectOrderCarriageRule(String code);

    /**
     * App 根据 业务Id 查询运单进度
     * Yan
     * @param code
     * @return
     */
    ResultUtil appSelectOrderProcess(String code);

    /**
     * APP端 运单列表
     * Yan
     * @return
     */
    ResultUtil appSelectOrderList(AppOrderSearchVO orderVO) throws RuntimeException;

    ResultUtil deleteOrder(TOrderInfoVO orderInfoVO);
    ResultUtil cancelResourceHallOrder(TOrderInfoVO orderInfoVO);

    public ResultUtil orderBatchDelete(TOrderInfo tOrderInfo, TOrderInfoUploadReq record,TOrderInfoUploadVo tOrderInfoUploadVo);

    /**
    * @Description 运单召回
    * <AUTHOR>
    * @Date   2019/6/5 20:45
    * @Param
    * @Return
    * @Exception
    *
    */
    ResultUtil orderBack(TOrderInfoVO reocrd);

    /**
     * @Description 打包运单召回
     * <AUTHOR>
     * @Date   2019/7/15 11:57
     * @Param
     * @Return
     * @Exception
     *
     */
    ResultUtil orderBackDB(OrderPackVO record);

    /**
    * @Description 运单驳回
    * <AUTHOR>
    * @Date   2019/6/9 10:43
    * @Param
    * @Return
    * @Exception
    *
    */
    ResultUtil rejectOrder(AppOrderSearchVO record);


    /**
    * @Description 根据运单打包主表业务id查询运单主表信息
    * <AUTHOR>
    * @Date   2019/6/26 17:14
    * @Param
    * @Return
    * @Exception
    *
    */
    List<TOrderInfo> selectOrderInfoByPackOrderCode(String code);

    ResultUtil planOrder(String code, SendOrderVO sendBillVO, CarDriverRelVO carDriverRelVO, TOrderInfo orderInfo,
                         String lineType, String capitalTransferType, LinkedHashMap carrier, CompanySourceDTO companySourceDTO);

    ResultUtil sendPlanOrder(CompanySourceDTO companySourceDTO, LineInfoPrincipalDTO receiver, TOrderInfo orderInfo,
                             SendOrderVO record, LinkedHashMap carrier);

    ResultUtil createOrderTemplate(SendOrderVO record);

    ResultUtil deleteOrderTemplate(SendOrderVO record);

    ResultUtil editOrderInfo(CompanySourceDTO companySourceDTO, LineInfoPrincipalDTO receiver, TOrderInfo orderInfo, CarDriverRelVO carDriverRelVO, SendOrderVO sendOrderVO, LinkedHashMap carrier);

    /**
     * <AUTHOR>
     * @Description 新版打包支付
     * @Date 2020/3/2 8:58 上午
     * @Param 
     * @return 8:58 上午
    **/
    ResultUtil newPackPay(String code);

    /*
     * <AUTHOR>
     * @Description 分润打包支付
     * @Date 2020/7/9 10:56
     * @Param
     * @return
    **/
    ResultUtil royaltyPackPay(String code);

    ResultUtil selectHallOrderList(AppOrderSearchVO orderVo) throws RuntimeException ;
}
