package com.lz.service;

import com.lz.common.util.ResultUtil;
import com.lz.model.TOrderInsurance;
import com.lz.vo.TOrderInsuranceVO;

/**
 * <AUTHOR>
 */
public interface TOrderInsuranceService {
    ResultUtil selectByPage(TOrderInsuranceVO search);

    void updateByOrderBusinessCode(TOrderInsuranceVO search);

    TOrderInsurance selectByOrderBusinessCode(String orderBusinessCode);

    void updateByPrimaryKeySelective(TOrderInsurance search);

    ResultUtil selectDataByEndDriverId(TOrderInsuranceVO search);

    ResultUtil orderInsuranceExport(TOrderInsuranceVO search);
}
