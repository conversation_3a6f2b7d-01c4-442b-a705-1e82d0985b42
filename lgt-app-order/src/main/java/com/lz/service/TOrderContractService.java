package com.lz.service;

import com.lz.common.util.ResultUtil;
import com.lz.model.TOrderContract;
import com.lz.model.VoucherReq;
import com.lz.vo.TOrderContractUploadVo;
import com.lz.vo.TorderCodeVO;

import java.util.List;

/**
 * author dingweibo
 * 运单合同
 */
public interface TOrderContractService {
    public TOrderContract selectByOrderBusinessCode(String code);

    public TOrderContract selectByOrderCodeAnrType(String code,String contractType);

    public TOrderContract selectByOrderCodeAnrTypeAnrEnable(String code,String contractType,boolean enable);

    public int updateHt(TOrderContract record);

    int updateByOrderCodeModel(TOrderContract record);

    public int add(TOrderContract record);

    /**
     * 生成提现协议
     */
    public ResultUtil createTxxy(VoucherReq req);

    List<TOrderContract> selectByCode(String  code);

    /**
     *  @author: dingweibo
     *  @Date: 2020/3/13 14:43
     *  @Description: 收款凭证作废  voucherType driver司机  agent 经纪人
     */
    public ResultUtil cancelContract(String orderCode,String voucherType);

    /**
     *  @author: dingweibo
     *  @Date: 2020/3/13 14:43
     *  @Description: 收款凭证作废  voucherType driver司机  agent 经纪人
     */
    public ResultUtil cancelContractTask(String orderCode, Integer enduserId, String voucherType);

    /**
     *  @author: dingweibo
     *  @Date: 2020/3/12 18:43
     *  @Description: 创建收款凭证  voucherType driver司机  agent 经纪人
     */
    public ResultUtil createVoucher(String orderCode,String voucherType);

    /**
     * 创建京东收款凭证
     * <AUTHOR>
     * @Date 2021-09-27 19:28:00
     * @param orderCode
     * @param voucherType
     * @return
     */
    @Deprecated
    ResultUtil createJdPayVoucher(String orderCode, String voucherType);

    /**
     *  @author: dingweibo
     *  @Date: 2020/8/27 16:31
     *  @Description: 运单跟踪中三方货物合同上传
     */
    public ResultUtil threeGoodsContractUpload(TOrderContractUploadVo record);

    /**
     * 创建随意签承运合同
     * @param orderCode
     * @param reSign
     * @return
     */
    ResultUtil createAnySignCyht(String orderCode, Boolean reSign);

    /**
     * 创建随意签收款凭证合同
     * @param orderCode
     * @param voucherType 合同类型
     * @return
     */
    ResultUtil createAnySignSkpz(String orderCode, String voucherType);

    /**
     * 华夏支付，创建收款凭证
     * @param orderCode 运单code
     * @param voucherType 收款凭证类型
     * @deprecated The voucherType parameter is no longer used. Use {@link #createSkpz(String)}
     * @return
     */
    @Deprecated
    ResultUtil createHXAnySignSkpz(String orderCode, String voucherType);

    /**
     * 创建收款凭证
     * @param orderCode
     * @return
     */
    ResultUtil createSkpz(String orderCode);

    int deleteHxAnySignSkpz(String orderCode, String voucherType);

    List<Integer> selectSignContractByOrderId(List<Integer> ids);

    ResultUtil createSignRepairCyht(String orderCode,Integer pushContractId);

    TOrderContract getContractPhotoByOrderCode(String code);
}
