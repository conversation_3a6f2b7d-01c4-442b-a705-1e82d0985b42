package com.lz.service;

import com.lz.common.util.ResultUtil;
import com.lz.dto.OrderPackDTO;
import com.lz.dto.OrderSundryFeeDTO;
import com.lz.dto.TOrderInfoDtoInfo;
import com.lz.model.TOrderAuditLog;
import com.lz.model.TOrderInfo;
import com.lz.model.TOrderInfoWeight;
import com.lz.schedule.model.TTask;
import com.lz.tpu.web.response.NotifyCashResp;
import com.lz.tpu.web.response.NotifyPaymentResp;
import com.lz.tpu.web.response.NotifySubaccountRemResp;
import com.lz.vo.*;

import java.text.ParseException;
import java.util.HashMap;
import java.util.List;

/**
 * Created by sang<PERSON> on 2019/4/23.
 */
public interface TOrderInfoService {

    TOrderInfo selectByPrimaryKey(Integer id);

    /**
     * @Description: PC运单打包页面
     * @Author: Yan
     * @Date: 2019/8/14/014 16:32
     * @Param: search
     * @Return: TOrderInfoDTO
     */
    ResultUtil getOrderPackList(AppOrderSearchVO search);

    /**
     *  @author: dingweibo
     *  @Date: 2020/2/27 11:00
     *  @Description: 实体运单打包导出
     */
    ResultUtil sTOrderDbExpro(AppOrderSearchVO search);
    /**
     * 运单过程管理-运单检查-上传磅单： 修改运单信息
     * Yan
     * @param detail
     * @return
     */
    ResultUtil updateExamine(OrderDetailVO detail);


    /**
    * @Description 运单过程管理-运单检查-补充车辆轨迹
    * <AUTHOR>
    * @Date   2020/1/2 9:54
    * @Param
    * @Return
    * @Exception
    *
    */
    ResultUtil supplementCarTrajectory(TOrderInfoVO tOrderInfoVO);

    /**
     * 运单过程管理-运单检查-上传磅单： 回显运单检查的信息
     * Yan
     * @param code
     * @return
     */
    ResultUtil orderExamineInfo(String code);

    /**
     * PC端：未签合同上传合同照
     * Yan
     * @param file
     * @return
     */
    ResultUtil noSignContractUpload(String code, String[] file, String clientType, Integer contractId);

    /**
     * PC端：未签合同发送短信通知
     * Yan
     * @return
     */
    ResultUtil noSignContractSendText(AppOrderSearchVO search);

    /**
     * PC端： 查询未签合同的运单列表
     * Yan
     * @return
     */
    ResultUtil selectNoSignOrder(PcOrderSearchVO search);

    /**
     * 运单审核：只获取已经 货运完成 的运单
     * Yan
     * @return
     */
    ResultUtil selectOrderAuditList(PcOrderSearchVO search);

    /**
     * 运单审核：如果运单 已存在就更新 不存在就新增
     * Yan
     * @return
     */
    ResultUtil orderAuditUpdate(TOrderAuditLog audit);

    /**
     * PC 端查询运单
     *  企业只查自己的运单
     *  承运方运营查所有的运单
     * Yan
     * @return ResultUtil
     */
    ResultUtil selectOrderByUserType(AppOrderSearchVO search) throws RuntimeException;

    /**
     * PC端查询运单审核
     * hwt
     * @return ResultUtil
     */
    ResultUtil selectOrderJUDGEByUserType(AppOrderSearchVO search) throws RuntimeException;

    /**
     * PC端查询运单检查
     * hwt
     * @return ResultUtil
     */
    ResultUtil selectOrderCheckByUserType(AppOrderSearchVO search) throws RuntimeException;

    /**
     * PC端查询跟踪运单
     * hwt
     * @return ResultUtil
     */
    ResultUtil selectOrderTrackByUserType(AppOrderSearchVO search) throws RuntimeException;

    List selectExcelOrder(AppOrderSearchVO search);

    void PayCallBack(NotifyPaymentResp message);

    public List<TOrderInfo> selectByOrderCode(String code);

    public int update(TOrderInfo record);

    public TOrderInfo selectByOuterTradeNo(String outerTradeNo);

    public String selectByCarrierId(String outerTradeNo);

    public TOrderInfoVO selectByThirdPartyInterfacePublicKey(String outerTradeNo);

    public int selectByEndDriverId(Integer cid,String status, String ctype);

    public int selectByNoAppraiseCount(Integer cid,String status, String ctype);

    void withdrawalCallBack(NotifyCashResp message);

    void rechargeCallBack(NotifySubaccountRemResp message);

    ResultUtil insertSelective(TOrderInfo record);

    /**
     * 查询运单
     * <AUTHOR>
     * @param orderInfo
     * @return
     */
    ResultUtil selectOrder(TOrderInfoVO orderInfo);

    TOrderInfo selectOrderBusinessCode(String orderBusinessCode);

    /**
     * 删除运单
     * <AUTHOR>
     * @param orderInfo 运单表
     * @return
     */
    ResultUtil deleteOrder(TOrderInfoVO orderInfo);

    public List<TOrderInfo> selectByOrderCodeList(String orderCodeStr);

    public List<TOrderInfoVO> selectByOrderCodeListName(String orderCodeStr);

    public ResultUtil selectByOrderCodeListNamePage(TOrderInfoVO tOrderInfoVO);

    public List<TOrderInfoVO> selectByOrderCodeListName(TOrderInfoVO tOrderInfoVO);
    /**
     * 查询运单详情
     * @param orderInfoVO
     * @return
     */
    ResultUtil selectOrderInfoByOrderCode(TOrderInfoVO orderInfoVO);

    /**
     *  @author: dingweibo
     *  @Date: 2019/6/18 14:17
     *  @Description: 提现
     */
    public ResultUtil tixian(TOrderInfoVO orderInfoVO);

    /**
     * @Description 根据运单主表业务id查询运单信息
     * <AUTHOR>
     * @Date   2019/6/26 13:51
     * @Param
     * @Return
     * @Exception
     *
     */
    TOrderInfo selectOrderInfoByCode(String code);

    TOrderInfo selectOrderInfoByCodePack(String code);

    /**
    * @Description 根据打包主表业务id查询运单主表信息
    * <AUTHOR>
    * @Date   2019/6/26 17:54
    * @Param
    * @Return
    * @Exception
    *
    */
    List<TOrderInfo> selectOrderInfoByPackCode(String code);


    /**
    * @Description 查询运单收款人银行卡
    * <AUTHOR>
    * @Date   2019/6/26 23:29
    * @Param
    * @Return
    * @Exception
    *
    */
    List<String> selectOrderReceiverBankCard(String code, String type);


    List<TOrderInfo> selectOrderPackTotalFee(String packCode);

    ResultUtil selectDataReportByPage(DataReportVO search);

    ResultUtil selectVehicleByOrdercode(DataReportVO search);


    /**
    * @Description 根据员工查询订单
    * <AUTHOR>
    * @Date   2019/7/13 14:47
    * @Param
    * @Return
    * @Exception
    *
    */
    List<TOrderInfo> selectByAccountIdFeign(Integer accountId);

    public List<TOrderInfo> selectByAccountIdAnrLineIdFeign(TOrderInfoVO record);

    public int selectByAccountIdAnrLineIdCountFeign(TOrderInfoVO record);

    public int selectByAccountIdAnrLineGoodsRelIdCountFeign(TOrderInfoVO record);



    /**
     * @Description 根据司机id查询订单
     * <AUTHOR>
     * @Date   2019/7/13 14:47
     * @Param
     * @Return
     * @Exception
     *
     */
    List<TOrderInfo> selectByEndDriverIdFeign(Integer endUserId);

    Boolean selectByEndDriverIdFeignHt (Integer endUserId);

    List<TOrderInfo> selectByEndDriverIdAnrCarIdFeign(Integer endUserId,Integer endCarId);

    /**
     * @Description 根据车主id查询订单
     * <AUTHOR>
     * @Date   2019/7/13 14:47
     * @Param
     * @Return
     * @Exception
     *
     */
    List<TOrderInfo> selectByEndCarOwnerIdFeign(Integer endUserId);

    /**
     * 运单审核：修改运单信息（实发数量、实收数量、发货磅单、收货磅单）
     * hwt
     * @param detail
     * @return
     */
    ResultUtil updateOrderDetail(OrderDetailVO detail);

    /**
     * 根据线路货物关系Id查询运单
     * hwt
     * @param lineGoodsRelId
     * @return
     */
    public List<TOrderInfo> selectByLineGoodsRelId(Integer lineGoodsRelId);

    /**
     * @Description 根据车辆id查询订单
     * <AUTHOR>
     * @Date   2019/7/24 19:45
     * @Param
     * @Return
     * @Exception
     *
     */
    List<TOrderInfo> selectByEndCarIdFeign(Integer endCarId);

    List<TOrderInfo> selectByEndCarIdAnrUserIdFeign(Integer endCarId,Integer endUserId);



    /**
     *  @author: dingweibo
     *  @Date: 2019/7/25 17:19
     *  @Description: task任务失败的运单
     */
    public ResultUtil selectTaskByOrderInfoPage(TOrderInfoVO record);

    /**
     * @Description 运单检查excel导出
     * <AUTHOR>
     * @Date   2019/7/27 10:27
     * @Param
     * @Return
     * @Exception
     *
     */
    public ResultUtil ydjcExpro(AppOrderSearchVO search);


    /**
     * @Description 已支付实体运单excel导出
     * <AUTHOR>
     * @Date   2019/7/27 10:27
     * @Param
     * @Return
     * @Exception
     *
     */
    public ResultUtil yzfxtydExpro(AppOrderSearchVO search);



    /**
     *  @author: dingweibo
     *  @Date: 2019/7/30 8:58
     *  @Description: 失败运单重置任务
     */
    public ResultUtil updateTaskById(Integer taskId);

    ResultUtil selectAgentManagerUnCompleteOrder(List<HashMap<String, Integer>> record);

    /**
     * @Description: 获取提现记录列表
     * @Author: Yan
     * @Date: 2019/9/18/018 8:41
     * @Param: search
     * @Return:
     */
    ResultUtil getOrderCashList(AppOrderSearchVO search);

    /**
     * @Description: 根据运单号查询车辆ID
     * @Author: Yan
     * @Date: 2019/10/25/025 9:38
     * @Param: code
     * @Return: 车辆ID
     */
    Integer getCarId(String code);


    /**
    * @Description
    * <AUTHOR>
    * @Date   2019/11/4 13:58
    * @Param
    * @Return
    * @Exception   根据code 获取计划发单模板详情
    *
    */
    public TOrderInfoDtoInfo getOrderInfo(String code);

    /**
     * @Description: 根据打包的运单号查询车辆ID
     * @Author: Yan
     * @Date: 2019/11/14/014 10:33
     * @Param: packCode
     * @Return: 车辆ID
     */
    List<Integer> getCarIdByOrderPackCode(String packCode);

    /*
     * <AUTHOR>
     * @Description 运单支付审核
     * @Date 2019/11/19 17:07
     * @Param
     * @return
    **/
    ResultUtil paymentAudit(TOrderInfoVO record);

    ResultUtil judgeLineExistsOrder(Integer lineId);


    /**
    * @Description 陆港通物流大数据展示中心 交易金额统计
    * <AUTHOR>
    * @Date   2019/11/21 17:03
    * @Param
    * @Return
    * @Exception
    *
    */
    public ResultUtil transactionAmount(String type) throws ParseException;


    /**
    * @Description  实收重量统计
    * <AUTHOR>
    * @Date   2019/11/22 13:46
    * @Param
    * @Return
    * @Exception
    *
    */
    public ResultUtil settledWeight(String type) throws ParseException;

    /**
     * @Description  运单数量统计
     * <AUTHOR>
     * @Date   2019/11/22 13:46
     * @Param
     * @Return
     * @Exception
     *
     */
    public ResultUtil orderCount(String type) throws ParseException;


    /**
     *  @author: dingweibo
     *  @Date: 2019/11/21 16:48
     *  @Description: 陆港通物流大数据展示中心 电子运单明细
     */
    public ResultUtil bigOrderInfoList();

    /**
     *  @author: dingweibo
     *  @Date: 2019/11/21 16:48
     *  @Description: 陆港通物流大数据展示中心 电子运单明细   、资金流水明细
     */
    public ResultUtil bigOrderInfoAnrDriver();



    /**
    * @Description 陆港通物流大数据展示中心 中国地图 省
    * <AUTHOR>
    * @Date   2019/11/23 8:56
    * @Param
    * @Return
    * @Exception
    *
    */
    public ResultUtil bigOrderMap();

    /**
     * @Description 陆港通物流大数据展示中心 中国地图 市
     * <AUTHOR>
     * @Date   2019/11/23 8:56
     * @Param
     * @Return
     * @Exception
     *
     */
    public ResultUtil bigOrderMapCity(String province);



    /**
    * @Description
    * <AUTHOR> 陆港通物流大数据展示中心 中国地图 区县获取去运单车辆信息
    * @Date   2019/11/25 11:20
    * @Param
    * @Return
    * @Exception
    *
    */
    public ResultUtil bigOrderMapCountry(String city);


    /**
     * @Description
     * <AUTHOR> 陆港通物流大数据展示中心 中国地图 全部市
     * @Date   2021/06/10 10:00
     * @Param
     * @Return
     * @Exception
     *
     */
    public ResultUtil bigOrderMapProvinceAndCity();

    List<String> selectOrderBusinessCodeByCode(List<String> codes);


    ResultUtil orderUnqualifiedMark(OrderDetailVO record);

     Boolean recoverOrginOrderCarriageFeeAndDispatchFee(List<OrderSundryFeeDTO> orderSundryFee);

    /**
     * @Description: 企业获取已提现运单列表
     * @Author: Yan
     * @Date: 2020/1/2/002 8:52
     */
    ResultUtil getExtractMoneyOrder(AppOrderSearchVO search);


    /**
     * @Description: 企业获取已提现运单列表导出
     * @Author: liu
     * @Date: 2020/1/2/002 8:52
     */
    ResultUtil getExtractMoneyOrderEXCEL(AppOrderSearchVO search);

    ResultUtil getFeedbackOrderInfo(String orderCode);


    /**
    * @Description 执行定时任务补充运单轨迹
    * <AUTHOR>
    * @Date   2020/1/8 9:58
    * @Param
    * @Return
    * @Exception
    *
    */
    ResultUtil carTrajectory(TOrderInfoVO tOrderInfoVO);

    ResultUtil updateTaskRequestParameter(TOrderInfoVO tOrderInfoVO);

    List<OrderPackDTO> orderJudgeGetAllInfo(List<String> codes);

    /**
     * @Description 根据打包主表业务id查询运单主表信息
     * <AUTHOR>
     * @Date 2019/6/26 17:55
     * @Param
     * @Return
     * @Exception
     */

    public TOrderInfo selectOrderInfoOneByPackCode(String code);

    TOrderInfo selectByOrderBusinessCode(String orderBusinessCode);

    TOrderInfoVO selectByOrderVOBusinessCode(String orderBusinessCode);

    ResultUtil sendWxHt(TOrderInfoVO record);

    ResultUtil getYdbycode(String code);

    ResultUtil getYdbycodeNew();

    List sendWxHtList(List<TOrderInfoVO> records);

    List sendWxHtListUpload(List<TOrderInfoVO> records);

    /**
     *  @author: dingweibo
     *  @Date: 2020/3/7 13:59
     *  @Description: 上传三方协议回显页面参数
     */
    ResultUtil selectByContract(TOrderInfoVO record);

    ResultUtil statisticalCompany(TOrderInfoVO record) ;

    /**
     * 根据运单编号组装数据将数据插入task任务表
     *
     */
    ResultUtil sendOrderInfoToYimei(String orderCode);

    ResultUtil sendOrderInfoToKhy(String code);

    ResultUtil sendEtcData(String record);

    public ResultUtil khyExcelImport(TOrderInfoUploadReq record);

    public ResultUtil sdfcExcelImport(TOrderInfoUploadReq record);

    public ResultUtil parameterSdFcReset(List<TTask> taskList);

    public ResultUtil parameterKhyReset(List<TTask> taskList);

    ResultUtil updateOrderSendState(Integer orderInfoId);

    ResultUtil updateAhOrderSendState(Integer orderInfoId);

    ResultUtil pcSelectByPage(AppOrderSearchVO search);

    /**
     * 企业取消运单，公众号向司机推送消息（司机取消不推送）
     */
    //ResultUtil pcCancelOrder(TOrderInfoVO search);

    DataToEtcVO selectByEtcDate(String orderCode);

    ResultUtil wgydVehicleTrajectory(List<TOrderInfoVO> tOrderInfoVOs);

    TOrderInfo selectByRecieveOrder(BatchExcelReceiveOrder batchExcelReceiveOrder);

    TOrderInfoWeight selectByOrderId(Integer orderId);
}
