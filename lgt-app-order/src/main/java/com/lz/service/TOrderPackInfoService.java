package com.lz.service;

import com.lz.common.util.ResultUtil;
import com.lz.dto.TOrderPayInfoDTO;
import com.lz.model.TOrderInfo;
import com.lz.model.TOrderPackInfo;
import com.lz.schedule.model.TTask;
import com.lz.vo.OrderPackVO;
import com.lz.vo.PcOrderSearchVO;

import java.util.List;

/**
 * Created by sang<PERSON> on 2019/4/23.
 */
public interface TOrderPackInfoService {

    TOrderPackInfo selectByCode(String code);

    /**
     * 运单打包付款列表
     * 已经打好包的运单
     * dwb
     * @param search 未支付的
     * @return
     */
    ResultUtil getOrderFinishedPack(PcOrderSearchVO search) throws RuntimeException;

    /**
     * 运单打包付款列表
     * 已经打好包的运单
     * dwb
     * @param search 已支付的
     * @return
     */
    ResultUtil getOrderFinishedPackOk(PcOrderSearchVO search) throws RuntimeException;


    /**
     * 运单打包付款列表
     * 已经打好包的运单 已提现
     * dwb
     * @param search
     * @return
     */
    ResultUtil getOrderFinishedPackTxOk(PcOrderSearchVO search) throws RuntimeException;


    /**
     * 运单打包付款列表
     * 已经打好包的运单 导出
     * Yan
     * @param search
     * @return
     */
    ResultUtil finishedPackExproExcel(PcOrderSearchVO search) throws RuntimeException;


    /**
     * 运单过程管理-运单付款-生成批量支付记录
     * 回显打包信息
     * Yan
     * @param orderVO
     * @return
     */
    ResultUtil orderPackInfo(OrderPackVO packVO);

    /**
     * 运单打包支付
     * Yan
     * @param pack
     * @return
     */
    ResultUtil orderPack(OrderPackVO pack);

    /**
     *  @author: dingweibo
     *  @Date: 2021/1/18 14:49
     *  @Description: 运单打包task处理
     */
    ResultUtil orderDbTask(TTask tTask);
    /**
     * 运单拆包
     * Yan
     * @return
     */
    ResultUtil orderDismantledPack(String code);

    /**
     * 根据打包主表code获取
     * 打包运单的详细信息
     * Yan
     * @param code 打包主表code
     * @return
     */
    ResultUtil orderPackDetailInfo(PcOrderSearchVO search);

    /**
     * 移除已打包的运单
     * @param code 运单主表的 Code
     * Yan
     * @return
     */
    ResultUtil removeOrder(List<String> code);

    TOrderPayInfoDTO selectByPayInfoLimitOne(String code);

    int updateByPrimaryKey(TOrderPackInfo record);

    /**
     * @Description 经纪人提现列表
     * <AUTHOR>
     * @Date   2019/7/10 21:25
     * @Param
     * @Return
     * @Exception
     *
     */
    ResultUtil managerWithdrawPage(PcOrderSearchVO record);

    TOrderPackInfo selectPackInfoByCode(String code);


    /**
    * @Description 根据支付子表code查询打包主表信息
    * <AUTHOR>
    * @Date   2019/9/20 9:53
    * @param
    * @Return
    * @Exception
    *
    */
    TOrderPackInfo selectPackInfoByPayDetailCode(String outerTradeNo);

    List<String> selectVirtualOrderByCode(List<String> codes);

    List<TOrderInfo> getOrderInfoByPackCode(String code);

    List<String> getOrderCodesByPackId(Integer id);

    TOrderPackInfo selectJdPackInfoByOrderBusinessCode(String orderBusinessCode);
    TOrderPackInfo selectById(Integer id);

    TOrderPackInfo selectPackInfoByOrderBusinessCodePayTime(String orderBusinessCode, String tradeNo);

    TOrderPackInfo selectByVirtualOrderNo(String virtualOrderNo);

}
