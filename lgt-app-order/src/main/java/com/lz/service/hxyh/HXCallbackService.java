package com.lz.service.hxyh;

import com.lz.common.model.hxPayment.response.CustomerBalancePayRes;
import com.lz.common.model.hxPayment.response.CustomerWithdrawRes;
import com.lz.common.util.ResultUtil;
import com.lz.dto.THxAdvanceOrderPayTempInfoDTO;
import com.lz.dto.THxOrderPayInfoDTO;
import sdk.model.message.AsyncNotifyVirtualBalancePayMessageBody;
import sdk.model.message.AsyncNotifyVirtualMemberWithdrawMessageBody;

public interface HXCallbackService {

    ResultUtil singleBalancePayNotice(AsyncNotifyVirtualBalancePayMessageBody messageBody);

    ResultUtil nodeBalancePayNotice(AsyncNotifyVirtualBalancePayMessageBody messageBody);

    ResultUtil txNotice(AsyncNotifyVirtualMemberWithdrawMessageBody messageBody);

    ResultUtil refundNotice(AsyncNotifyVirtualBalancePayMessageBody messageBody);

    ResultUtil packRefundNotice(AsyncNotifyVirtualBalancePayMessageBody messageBody);

    ResultUtil prePayNotice(AsyncNotifyVirtualBalancePayMessageBody messageBody, THxAdvanceOrderPayTempInfoDTO dto);

    ResultUtil wkPayNotice(AsyncNotifyVirtualBalancePayMessageBody messageBody, THxOrderPayInfoDTO dto);

    ResultUtil payFail(CustomerBalancePayRes response);

    ResultUtil txFail(CustomerWithdrawRes response);

    ResultUtil accountServiceFeeCallback(AsyncNotifyVirtualBalancePayMessageBody messageBody);

    ResultUtil carrierBalanceCompanyChargeCallback(AsyncNotifyVirtualBalancePayMessageBody messageBody);

}
