package com.lz.service.hxyh;

import com.lz.common.util.ResultUtil;
import com.lz.model.TOrderInfo;
import com.lz.vo.HxOrderTxVO;
import com.lz.vo.NodePayVO;
import com.lz.vo.hxyh.HXOrderPayVO;

public interface THXOrderPayService {

    ResultUtil pay(HXOrderPayVO payVO);

    ResultUtil batchPay(TOrderInfo orderInfo);

    ResultUtil packPay(String packCode);

    ResultUtil nodePay(NodePayVO payVO);

    ResultUtil refund(String orderCode, String remark);

    ResultUtil packRefund(String packCode, String remark);

    ResultUtil tx(HxOrderTxVO hxOrderTxVO);

}
