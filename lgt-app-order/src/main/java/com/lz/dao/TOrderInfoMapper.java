package com.lz.dao;

import com.lz.dto.*;
import com.lz.example.TOrderInfoExample;
import com.lz.model.*;
import com.lz.schedule.model.TTask;
import com.lz.vo.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface TOrderInfoMapper {

    /**
     * @Description: APP收单前先判断当前用户是否有支付权限
     * @Author: Yan
     * @Date: 2019/7/16/016 11:33
     * @Param: code, accountId
     * @Return: Boolean
     */
    Boolean judgeUserWhetherHavePay(@Param("code") String code, @Param("accountId") Integer accountId);

    /**
     * 根据运单code 获取线路的起始点坐标
     * Yan
     *
     * @param code
     * @return
     */
    Map<String, Object> getLineInfoByOrderCode(String code);

    /**
     * PC端 运单管理
     * 查询运单
     * 企业只查自己的运单
     * 承运方运营查所有的运单
     * Yan
     *
     * @param search
     * @return TOrderInfo
     */
    List<TOrderInfoDTO> selectOrderByUserType(AppOrderSearchVO search);

    /**
     * PC端查询运单审核
     * hwt
     *
     * @param search
     * @return TOrderInfo
     */
    List<TOrderInfoDTO> selectOrderJUDGEByUserType(AppOrderSearchVO search);

    /**
     * PC端查询运单检查
     * hwt
     *
     * @param search
     * @return TOrderInfo
     */
    List<TOrderInfoDTO> selectOrderCheckByUserType(AppOrderSearchVO search);

    /**
     * PC端查询跟踪运单
     * hwt
     *
     * @param search
     * @return TOrderInfo
     */
    List<TOrderInfoDTO> selectOrderTrackByUserType(AppOrderSearchVO search);

    /**
     * 获取所有运单的原发重量
     * Yan
     *
     * @param search
     * @return
     */
    Map<String, Object> getOrderAllPrimaryWeigh(AppOrderSearchVO search);

    /**
     * 根据CODE查询运单
     * Yan
     *
     * @param codes
     * @return TOrderInfo
     */
    List<TOrderInfoDTO> getOrderListByCode(PcOrderSearchVO search);

    /**
     * PC端 运单管理
     * 查询运单
     * 获取承运方下的所有企业，然后去查询企业下的信息站
     * Yan
     *
     * @param carrierId
     * @return TOrderInfo
     */
    List<Integer> getOrderCarrierCompanyId(@Param("carrierId") Integer carrierId, @Param("field") String field);

    /**
     * PC端 运单管理
     * 查询运单
     * 查询缺少的信息
     * Yan
     *
     * @param code
     * @return TOrderInfo
     */
    TOrderInfoDTO getOrderLackInfo(String code);

    /**
     * WX司机查询运单-获取所有管理的线路
     * Yan
     *
     * @param accountId
     * @return
     */
    List<Map<String, Object>> getWXUserAdministrativeLine(@Param("accountId") Integer accountId, @Param("userType") String userType);

    /**
     * WX司机查询货源大厅运单-获取所有管理的线路
     * Yan
     *
     * @param accountId
     * @return
     */
    List<Map<String, Object>> getWXUserAdministrativeResourceHallLine(@Param("accountId") Integer accountId, @Param("userType") String userType);

    /**
     * APP查询运单-获取所有管理的线路
     * Yan
     *
     * @param accountId
     * @return
     */
    List<Map<String, Object>> getAPPUserAdministrativeLine(Integer accountId);

    /**
     * 打包支付：回显信息 获取运费调度费总和
     * Yan
     *
     * @param codes
     * @return
     */
    Map<String, Object> getSumDispatchCarriage(@Param("codes") List<String> codes);

    /**
     * 运单过程管理-运单检查-上传磅单： 回显运单检查的信息
     * Yan
     *
     * @param code
     * @return
     */
    OrderDetailDTO selectOrderExamineInfo(String code);

    /**
     * 获取司机所有运单的预估重量
     * Yan
     *
     * @param search
     * @return
     */
    List<AppOrderListDTO> wxGetOrderWight(AppOrderSearchVO search);

    /**
     * WX司机查询运单
     * Yan
     *
     * @param search
     * @return
     */
    List<AppOrderListDTO> wxDriverSelectOrder(AppOrderSearchVO search);

    /**
     * WX司机查询货源大厅运单
     *
     * @param search
     * @return
     */
    List<AppOrderListDTO> wxDriverSelectResourceHallOrder(AppOrderSearchVO search);

    /**
     * 签订合同：获取运单p,b,c端名称
     * Yan
     *
     * @param code
     * @return
     */
    OrderContractDTO selectOrderPBCName(String code);

    /**
     * 根据32位业务ID 获取企业承运方的合同照
     * Yan
     *
     * @param code
     * @return
     */
    Map<String, Object> selectCompanyContractByOrderCode(String code);

    /**
     * 根据 32位业务ID 更新运单主表
     * Yan
     *
     * @param info
     * @return
     */
    int updateByCodeSelective(TOrderInfo info);

    /**
     * PC端： 查询未签合同的运单列表
     * Yan
     *
     * @return
     */
    List<NoSignContractOrderDTO> pcSelectOrderNoSign(PcOrderSearchVO search);

    /**
     * 运单审核：只获取已经 货运完成 的运单
     * Yan
     *
     * @return
     */
    List<TOrderInfoDTO> pcSelectOrderAuditList(PcOrderSearchVO search);

    /**
     * 运单管理-收单: 根据32CODE查询运单基础信息 APP
     * Yan
     *
     * @param code
     * @return
     */
    OrderDetailDTO selectCollectOrderBasisInfo(@Param("code") String code);

    /**
     * 运单管理-收单: 根据32CODE查询运单基础信息 PC
     * Yan
     *
     * @param code
     * @return
     */
    CollectOrderInfoDTO pcGetCollectOrderBasisInfo(@Param("code") String code);

    /**
     * 运单管理-收单：根据32业务ID,和资金转移方式查询收款人和收款账户
     * Yan
     *
     * @param codes
     * @param types
     * @return
     */
    Map<String, String> selectOrderReceiver(@Param("codes") String codes, @Param("types") String types);

    /**
     * 运单管理-收单：根据32业务ID,和资金转移方式查询收款人和收款账户
     * Yan
     *
     * @param codes
     * @param types
     * @return
     */
    List<String> selectOrderReceiverBankCard(@Param("codes") String codes, @Param("types") String types);

    List<TBankInfoVo> selectOrderReceiverHxBankCard(@Param("codes") String codes, @Param("types") String types);

    List<UserBankCardDTO> selectEndUserCard(@Param("endUserId") Integer endUserId);

    /**
     * 根据32业务ID 查询运单
     * Yan
     *
     * @param code
     * @return
     */
    TOrderInfo selectOrderByCode(String code);

    TOrderInfo selectOrderInfoByCodePack(String code);

    /**
     * @Description: 根据 运单 code 获取合同状态
     * @Author: Yan
     * @Date: 2019/8/24/024 16:21
     * @Param: code
     * @Return: String
     */
    String judgeOrderContractStatus(String code);

    /**
     * 运单打包 ：获取打包运单要审核的信息
     * Yan
     *
     * @param codes
     * @return
     */
    List<OrderPackDTO> orderJudgeGetAllInfo(@Param("codes") List<String> codes);

    /**
     * 运单打包： 获取运单各项费用
     * Yan
     *
     * @param codes
     * @return
     */
    List<OrderSundryFeeDTO> selectOrderSundryFee(@Param("codes") List<String> codes);

    /**
     * @Description: APP运单列表查询本人所有运单的总重量
     * @Author: Yan
     * @Date: 2019/7/16/016 13:56
     * @Param:
     * @Return:
     */
    Double getCountOrderGoodsWeight(Integer userId);

    /**
     * APP端 运单列表
     * Yan
     *
     * @param search
     * @return
     */
    List<AppOrderListDTO> appSelectOrder(AppOrderSearchVO search);

    //add by xyz 2019.8.6 增加app运单查询导入es
    AppOrderListDTO appSelectOrderForEs(AppOrderSearchVO search);

    List<String> appSelectOrderCode(AppOrderSearchVO searchVO);

    /**
     * App端 查询运单详情
     * 运单状态：已装货，卸货
     * 查询运单磅单
     * Yan
     *
     * @param code
     * @param state
     * @return
     */
    OrderDetailDTO appHandlingOrder(@Param("code") String code, @Param("state") Integer state);


    /**
     * 查询运单详情
     * Yan
     *
     * @param code
     * @return
     */
    OrderDetailDTO getOrderDetailInfo(@Param("code") String code, @Param("accountId") Integer accountId);


    /**
     * @author: dingweibo
     * @Date: 2019/6/6 14:01
     * @Description:导出EcxcelS数据
     */
    List<OrderInfoExcelVo> selectOrderExcel(AppOrderSearchVO search);

    /**
     * 运单打包后修改运单主表的打包状态
     * Yan
     *
     * @param pack
     * @return
     */
    int orderPackAfterAuditState(OrderPackVO pack);

    /**
     * PC端 运单管理
     * 查询运单详情
     * Yan
     *
     * @param code 业务ID
     * @return
     */
    OrderDetailDTO pcSelectOrderDetailed(@Param("code") String code);

    long countByExample(TOrderInfoExample example);

    int deleteByExample(TOrderInfoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TOrderInfo record);

    int insertSelective(TOrderInfo record);

    List<TOrderInfo> selectByExample(TOrderInfoExample example);

    TOrderInfo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TOrderInfo record, @Param("example") TOrderInfoExample example);

    int updateByExample(@Param("record") TOrderInfo record, @Param("example") TOrderInfoExample example);

    int updateByPrimaryKeySelective(TOrderInfo record);

    int updateByPrimaryKeySelectiveNew(TOrderInfo record);

    int updateByPrimaryKey(TOrderInfo record);

    List<TOrderInfoVO> selectByExecuteState(TOrderInfo record);

    List<TOrderInfoVO> selectByExecuteStateCode(TOrderInfo record);

    List<TOrderInfoVO> selectByExecuteStateCodeNewOrGsp(TOrderInfo record);

    List<TOrderInfoVO> selectByExecuteStateCodeNew(TOrderInfo record);

    List<OrderInfoTotalPriceDTO> selectByOrderPageage(String packageID);

    TOrderInfo selectByOuterTradeNo(String outerTradeNo);

    List<TOrderInfo> selectByOrderPageageCode(String packageID);

    TOrderInfoAndTOrderPayInfo selectOrderInfoAndOrderPayInfoByOrderPayDetailCode(String code);

    TOrderInfo selectOrderInfoByPayDetailId(@Param("id") Integer id);

    int updateStatusByOrderCode(@Param("orderCode") String orderCode, @Param("status") String status, @Param("payStatus") String payStatus);

    int updateStatusByPayCode(@Param("packageCode") String packageCode, @Param("status") String status, @Param("payStatus") String payStatus);

    int selectByEndDriverId(@Param("cid") Integer cid, @Param("status") String status, @Param("ctype") String ctype);

    //查询司机端待评价运单数
    int selectByNoAppraiseCount(@Param("cid") Integer cid, @Param("status") String status, @Param("ctype") String ctype, @Param("scoreDay") Integer scoreDay);

    /**
     * 修改运单状态
     *
     * @param record
     * @return
     */
    int updateOrderInfoStatusByCode(TOrderInfo record);

    List<TOrderAndCastChangeVO> selectOrderAndCastChangeByOrderPageageCode(@Param("packageID") String packageID);

//    List<TOrderInfoDTO> selectOrderInfoForDelete(TOrderInfo orderInfo);

    List<TOrderInfoVO> selectByOrderCodeListName(List list);

    List<TOrderInfoDTO> selectOrderInfo(TOrderInfoVO orderInfo);

    /**
     * 查询司机状态
     *
     * @return
     */
    String selectEnduserStatus(TEndUserStatus record);

    List<String> selectEndcarStatus(HashMap param);

    int updateStatusByPayState(@Param("orderCode") String orderCode, @Param("status") String status);

    List<TxVO> selectByRole1(@Param("endUserId") Integer endUserId);

    List<TxVO> selectByRole2(@Param("endUserId") Integer endUserId);

    List<TxVO> selectByRole3(@Param("endUserId") Integer endUserId);

    List<TxVO> selectByRole4(@Param("endUserId") Integer endUserId);

    int updatePayStatusByOrderCode(@Param("code") String code, @Param("payStatus") String payStatus);

    int updateTotalFeeAndDispatchFeeByCode(@Param("code") String code, @Param("totalFee") BigDecimal totalFee, @Param("dispatchFee") BigDecimal dispatchFee);

    CollectOrderInfoDTO selectPayOrderDetail(@Param("code") String code, @Param("accountId") Integer accountId);

//    List<TOrderInfo> getTiXianList(@Param("endUserId") Integer endUserId);

    List<TOrderInfo> selectOrderInfoByPackCode(String code);

    TOrderInfoDTO selectOrderInfoByOrderCode(@Param(value = "orderBusinessCode") String orderBusinessCode);

    TOrderInfo selectOrderInfoByRandom(@Param(value = "random") String random);

    List<TxVO> selectPackOrderPage(@Param("code") String code);

    List<TxVO> getTiXianList1(Integer endUserId);

    List<TxVO> getTiXianList2(Integer endUserId);

    List<TxVO> getTiXianList3(Integer endUserId);

    int updatePayStatusByPayCode(@Param("packCode") String packCode, @Param("status") String status);

    List<TOrderStatisticsDTO> selectFDInfo(TOrderStatisticsVO search);

    List<TOrderStatisticsDTO> selectDayFinishInfo(TOrderStatisticsVO search);

    List<TOrderStatisticsDTO> selectMonthFinishInfo(TOrderStatisticsVO search);

    List<TOrderStatisticsDTO> selectWalletInfo(TOrderStatisticsVO search);

    List<TOrderStatisticsDTO> selectFinishInfo(TOrderStatisticsVO search);

    List<TOrderInfo> selectOrderPackTotalFee(@Param("packCode") String packCode);

    List<TOrderReport> selectDataReportByPage(DataReportVO search);

    List<TOrderReportDto> selectDataReportDtoByPage(DataReportVO search);

    /**
     * 根据 32位业务ID 更新运单装货签到时间
     * Yan
     *
     * @param info
     * @return
     */
    int updateOrderStateDRICONOperateTime(OrderDetailVO info);

    /**
     * 根据 32位业务ID 更新运单卸货签到时间
     * Yan
     *
     * @param info
     * @return
     */
    int updateOrderStateDRICONLOADOperateTime(OrderDetailVO info);


    /**
     * @Description 根据员工查询运单
     * <AUTHOR>
     * @Date 2019/7/13 14:51
     * @Param
     * @Return
     * @Exception
     */
    List<TOrderInfo> selectByAccountIdFeign(@Param("accountId") Integer accountId);


    List<TOrderInfo> selectByAccountIdAnrLineIdFeign(TOrderInfoVO record);

    int selectByAccountIdAnrLineIdCountFeign(TOrderInfoVO record);

    int selectByAccountIdAnrLineGoodsRelIdCountFeign(TOrderInfoVO record);


    /**
     * @Description 根据司机id查询运单
     * <AUTHOR>
     * @Date 2019/7/13 14:51
     * @Param
     * @Return
     * @Exception
     */
    List<TOrderInfo> selectByEndDriverIdFeign(@Param("endUserId") Integer endUserId);

    List<TOrderInfo> selectByEndDriverIdFeignHtNew(@Param("endUserId") Integer endUserId);
    Integer selectByEndDriverIdFeignHt(@Param("orderCode") String orderCode);
    Integer selectByEndDriverIdFeignHtRepairht(@Param("orderCode") String orderCode);

    List<TOrderInfoVO> selectByEndDriverIdOrder(@Param("codes") List codes);

    List<TOrderInfo> selectByEndDriverIdAnrCarIdFeign(@Param("endUserId") Integer endUserId, @Param("endCarId") Integer endCarId);


    /**
     * @Description 根据车主id查询运单
     * <AUTHOR>
     * @Date 2019/7/13 14:51
     * @Param
     * @Return
     * @Exception
     */
    List<TOrderInfo> selectByEndCarOwnerIdFeign(@Param("endUserId") Integer endUserId);


    int batchUpdateOrderSahrePaymentAnd(HashMap hashMap);

    int updateOrderSahrePaymentAndDispatchFee(@Param("sharePaymentAmount") BigDecimal sharePaymentAmount, @Param("shareDispatchFee") BigDecimal shareDispatchFee, @Param("code") String code);

    TOrderInfo selectShare(HashMap hashMap);

    int batchUpdateOrderPayStatus(HashMap hashMap);

    List<AppOrderListDTO> getCountOrderGoodsWeightParam(HashMap param);

    List<TOrderInfo> selectByEndCarIdFeign(@Param("endCarId") Integer endCarId);

    List<TOrderInfo> selectByEndCarIdAnrUserIdFeign(@Param("endCarId") Integer endCarId, @Param("endUserId") Integer endUserId);


    /**
     * @Description 失败运单管理
     * <AUTHOR>
     * @Date 2019/7/25 17:22
     * @Param
     * @Return
     * @Exception
     */
    List<TOrderInfoVO> selectTaskByOrderInfoPage(TOrderInfoVO record);

    //运单检查导出
    List<TOrderInfoVO> ydjcExpro(AppOrderSearchVO record);


    /**
     * @author: dingweibo
     * @Date: 2019/8/2 15:53
     * @Description:
     */
    TOrderInfoVO selectByOrderBusinessCode(@Param("orderBusinessCode") String orderBusinessCode);

    TOrderInfoVO selectByOrderBusinessCodeNew(@Param("orderBusinessCode") String orderBusinessCode);

    TOrderInfoVO selectByOrdeCode(@Param("orderCode") String orderCode);

    TOrderInfo selectOrderBusinessCode(@Param("orderBusinessCode") String orderBusinessCode);

    /**
     * @Description: PC运单打包页面
     * @Author: Yan
     * @Date: 2019/8/14/014 16:32
     * @Param: search
     * @Return: TOrderInfoDTO
     */
    List<TOrderInfoDTO> getOrderPackList(AppOrderSearchVO search);

    /**
     * @Description: PC 运单付款
     * @Author: Yan
     * @Date: 2019/8/15/015 10:14
     * @Param:
     * @Return:
     */
    List<TOrderInfoDTO> getOrderPayList(AppOrderSearchVO search);

    /**
     * @Description: 跟踪运单页面
     * @Author: Yan
     * @Date: 2019/8/17/017 16:11
     * @Param:
     * @Return:
     */
    List<TOrderInfoDTO> getGzorderList(AppOrderSearchVO search);

    Long selectAgentManagerUnCompleteOrder(TOrderInfoVO record);

    /**
     * @Description: 获取提现记录列表
     * @Author: Yan
     * @Date: 2019/9/18/018 8:41
     * @Param: search
     * @Return:
     */
    List<OrderCashListDTO> getCashList(AppOrderSearchVO search);

    /**
     * @Description: 根据运单号查询车辆ID
     * @Author: Yan
     * @Date: 2019/10/25/025 9:38
     * @Param: code
     * @Return: 车辆ID
     */
    Integer getCarIdByOrderCode(String code);

    TOrderInfoDtoInfo getOrderInfo(String code);

    List<EndCarUserInfoDto> selectGoodsSourceVehicleDriverInfoMapper(String code);

    TOrderInfoVO selectCarridNameAnrCompanyName(@Param("subAccount") String subAccount);

    /**
     * @Description: 根据打包的运单号查询车辆ID
     * @Author: Yan
     * @Date: 2019/11/14/014 10:33
     * @Param: packCode
     * @Return: 车辆ID
     */
    List<Integer> getCarIdByOrderPackCode(String packCode);

    int updatePayStatusById(@Param("id") Integer id, @Param("payStatus") String payStatus);

    int batchUpdateOrderPayStatusById(TOrderPackInfoVO record);

    Integer judgeLineExistsOrder(Integer lineId);


    /**
     * @Description 查询所有已完成运单 起始时间
     * <AUTHOR>
     * @Date 2019/11/21 17:06
     * @Param
     * @Return
     * @Exception
     */

    TOrderInfoVO getOrderFinishTime();

    /**
     * @Description 统计交易金额
     * <AUTHOR>
     * @Date 2019/11/22 10:24
     * @Param
     * @Return
     * @Exception
     */
    List<Map<String, Object>> transactionAmount(TBigInfoVO tBigInfoVO);

    //累加统计
    List<Map<String, Object>> transactionAmountLj(TBigInfoVO tBigInfoVO);

    /**
     * @Description 统计实收重量
     * <AUTHOR>
     * @Date 2019/11/22 10:24
     * @Param
     * @Return
     * @Exception
     */

    List<Map<String, Object>> settledWeight(TBigInfoVO tBigInfoVO);

    //累加统计
    List<Map<String, Object>> settledWeightLj(TBigInfoVO tBigInfoVO);

    /**
     * @Description 统计运单数量
     * <AUTHOR>
     * @Date 2019/11/22 10:24
     * @Param
     * @Return
     * @Exception
     */
    List<Map<String, Object>> orderCount(TBigInfoVO tBigInfoVO);

    //累加统计
    List<Map<String, Object>> orderCountLj(TBigInfoVO tBigInfoVO);


    /**
     * @author: dingweibo  陆港通物流大数据展示中心  电子运单明细
     * @Date: 2019/11/22 15:12
     * @Description:
     */
    List<BigOrderInfoAnrDriverVO> selectBigOrderInfoList(TBigInfoVO tBigInfoVO);


    /**
     * @author: dingweibo  陆港通物流大数据展示中心   资金流水明细
     * @Date: 2019/11/22 15:12
     * @Description:
     */
    List<BigOrderInfoAnrDriverVO> selectBigOrderInfoAnrDriver(TBigInfoVO tBigInfoVO);


    /**
     * @Description 陆港通物流大数据展示中心 中国地图 省，
     * <AUTHOR>
     * @Date 2019/11/25 10:54
     * @Param
     * @Return
     * @Exception
     */
    List<TBigOrderInfoVO> selectBigOrderMap(TBigInfoVO tBigInfoVO);

    /**
     * @Description 陆港通物流大数据展示中心 中国地图 市，
     * <AUTHOR>
     * @Date 2019/11/25 10:54
     * @Param
     * @Return
     * @Exception
     */
    List<TBigOrderInfoVO> selectBigOrderMapCity(TBigInfoVO tBigInfoVO);

    List<String> selectOrderBusinessCodeByCode(@Param("codes") List<String> codes);

    /**
     * @Description 陆港通物流大数据展示中心 中国地图 区县
     * <AUTHOR>
     * @Date 2019/11/25 11:23
     * @Param
     * @Return
     * @Exception
     */
    List<Map<String, Object>> selectBigOrderMapCountry(TBigInfoVO tBigInfoVO);

    int updateByPrimaryCodeSelective(TOrderInfo record);

    List<Map<String, Object>> selectBigOrderMapCountry(@Param("city") String city);

    List<String> getOrderIndexCount();

    TLineInfo selectLine(@Param("orderBusinessCode") String orderBusinessCode);


    FeedbackOrderInfoDTO getFeedbackOrderInfo(String orderCode);

    int updateOrginOrderCarriageFeeAndDispatchFee(@Param("list") List<OrderSundryFeeDTO> list);

    int recoverOrginOrderCarriageFeeAndDispatchFee(@Param("list") List<OrderSundryFeeDTO> list);

    TTask selectTaskById(String taskId);

    TEndUserInfo selectEndUser(@Param("uid") String uid);

    TBankCard selectBankByUid(String uid);

    void updateTask(@Param("taskId") String taskId, @Param("mapJson") String mapJson);

    TOrderInfo selectOrderInfoOneByPackCode(String code);

    TOrderInfo selectOrderInfoByAdvanceOrderPayCode(String code);

    TOrderInfoVO selectByThirdPartyInterfacePublicKey(@Param("code") String code);

    TOrderInfo selectOrderInfoByOrderBusinessCode(@Param("orderBusinessCode") String orderBusinessCode);

    /**
     * @return 5:22 下午
     * <AUTHOR>
     * @Description 新版支付: 打包单入账回调，修改原始运单状态
     * @Date 2020/3/2 5:22 下午
     * @Param
     **/
    int batchUpdateOrderStatusByPackCode(@Param("code") String code, @Param("orderExecuteStatus") String orderExecuteStatus,
                                         @Param("orderPayStatus") String orderPayStatus, @Param("updateTime") Date updateTime);


    TOrderInfoVO selectByContract(@Param("orderCode") String orderCode);


    /**
     * @author: dingweibo
     * @Date: 2020/3/24 9:39
     * @Description: 运单跟踪页面查询
     */
    List<TOrderInfoDTO> selectByYdgzPage(AppOrderSearchVO search);

    /**
     * @author: dingweibo
     * @Date: 2020/3/24 9:39
     * @Description: 运单跟踪导出
     */
    List<TOrderInfoDTO> selectByYdgzExcel(AppOrderSearchVO search);

    /**
     * @author: dingweibo
     * @Date: 2020/3/24 9:39
     * @Description: 运单跟踪页面累计
     */
    Map<String, Object> selectByYdgzSum(AppOrderSearchVO search);

    /**
     * @author: dingweibo
     * @Date: 2020/3/25 15:08
     * @Description: 运单检查页面查询
     */
    List<TOrderInfoDTO> selectByYdjcPage(AppOrderSearchVO search);


    /**
     * @author: dingweibo
     * @Date: 2020/3/25 15:08
     * @Description: 运单审核页面查询
     */
    List<TOrderInfoDTO> selectByYdshPage(AppOrderSearchVO search);

    int batchUpdateOrderPayStatusByOrderId(@Param("orderIds") List<Integer> orderIds, @Param("orderPayStatus") String orderPayStatus);

    List<TOrderInfoDTO> selectOrderInfoByCodes(@Param("codes") List codes);

    int batchUpdatesharePaymentAmountAndshareDispatchFee(@Param("list") List<TOrderInfoDTO> list);

    /**
     * @author: dingweibo
     * @Date: 2020/3/26 19:37
     * @Description: 已支付实体运单累计
     */
    Map<String, Object> selectByYzfstydSum(AppOrderSearchVO search);

    /**
     * @return 5:22 下午
     * <AUTHOR>
     * @Description 新版支付: 打包单入账回调，修改原始运单状态
     * @Date 2020/3/2 5:22 下午
     * @Param
     **/
    int RZHDbatchUpdateOrderStatusByPackCode(@Param("code") String code, @Param("orderExecuteStatus") String orderExecuteStatus,
                                             @Param("orderPayStatus") String orderPayStatus, @Param("returnTime") Date returnTime,
                                             @Param("updateTime") Date updateTime);

    /**
     * @return 4:48 下午
     * <AUTHOR>
     * @Description 查询(1)支付处理中且自动到卡(2)提现申请中的运单运费
     * @Date 2020/4/8 4:48 下午
     * @Param
     **/
    BigDecimal selectUnWithdrawAmount(@Param("enduserIds") List<Integer> enduserIds, @Param("enduserType") String enduserType,
                                      @Param("fromTime") Date fromTime, @Param("endTime") Date endTime);

    BigDecimal selectFQTXAmountByBankIds(@Param("list") List<String> list);

    List<StatisticalVO> statisticalCompany(TOrderInfoVO record);

    TLineGoodsRel selectLineGoodsRelByOrderCode(@Param("orderCode") String code);

    TOrderInfoVO selectByCodeAndIfSingin(@Param("code") String code);

    int recallBackBatchUpdateOrderStatusByPackCode(@Param("code") String code, @Param("orderExecuteStatus") String orderExecuteStatus,
                                         @Param("orderPayStatus") String orderPayStatus, @Param("updateTime") Date updateTime);
    /**
     * <AUTHOR>
     * @Description 根据打包主表code查询司机车辆、车主认证状态
     * @Date 2020/9/1 5:08 下午
     * @Param
     * @return
    **/
    List<OrderPackDTO> selectDriverOnwerCarStatusByPackCode(@Param("code") String code);

    TOrderInfoVO selectByEndDriverIdAndPhone(@Param("endDriverId") Integer endDriverId);

    TOrderInfoVO selectByCodeStatus(@Param("code") String code);

    DataToYimeiDTO selectSendOrderInfoToYimei(@Param("orderCode") String orderCode);

    DataToEtcVO sendToEtcData(@Param("code") String code);

    DataToEtcVO sendToEtcDataOrderCode(@Param("orderCode") String orderCode);

    OrderDetailDTO selectOrderPayRuleDetail(@Param("code")String code);

    String selectOrderPayRulePayNodeType(@Param("code")String code);

    OrderDetailDTO selectOrderDetail(@Param("code")String code);

    int updateByPackStatusBath(@Param("codeList") List<String> codeList);

    int updateByIdBath(@Param("orderInfoList") List<TOrderInfo> orderInfoList);

    TOrderInfoVO selectOrderInfoForAdvanceByOrderBusinessCode(@Param("orderBusinessCode") String orderBusinessCode);

    List<TOrderInfo> selectOrder(OrderDeleteCancelRequestParameterVO vo);

    List<TOrderInfo> selectFinishedOrder(OrderDeleteFinishedRequestParameterVO vo);

    List<TOrderInfo> selectOrderList(OrderDeleteFinishedRequestParameterVO vo);

    /**
     * APP端 货源大厅运单列表
     *
     * <AUTHOR>
     * @param search
     * @return
     */
    List<AppOrderListDTO> appSelectHallOrderList(AppOrderSearchVO search);

    /**
     * APP端 货源大厅运单列表总重量
     *
     * <AUTHOR>
     * @param search
     * @return
     */
    BigDecimal appSelectHallOrderSum(AppOrderSearchVO search);

    TOrderInfo selectByRecieveOrder(BatchExcelReceiveOrder batchExcelReceiveOrder);

    TOrderInfoVO selectHeNanInfoByOrderBusinessCode(@Param("orderBusinessCode") String orderBusinessCode,@Param("orderCode") String orderCode);

    TOrderMaintenanceRecordsVO updateOrderCaptainInfoByOrderCode(@Param("orderBusinessCode") String orderBusinessCode);

    List<TOrderMaintenanceRecordsVO> updateOrderCaptainInfoByOrderCodeList(@Param("orderBusinessCode") String orderBusinessCode);

    int updateEndCarOwnerByOrderBusinessCode(@Param("endCarOwnerId") Integer endCarOwnerId ,@Param("orderBusinessCode") String orderBusinessCode);

    int updateOrderExecuteStatusByOrderBusinessCode(@Param("orderBusinessCode") String orderBusinessCode);

    /**
     * 根据货源id 查询资金流转模式
     * @param sourceId
     */
    CapitalFlowModeVO selectCapitalFlowModeBySourceId(@Param("sourceId") Integer sourceId);
}