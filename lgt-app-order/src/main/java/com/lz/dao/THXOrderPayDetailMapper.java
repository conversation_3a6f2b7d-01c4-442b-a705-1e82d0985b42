package com.lz.dao;

import com.lz.dto.THxOrderPayInfoDTO;
import com.lz.dto.OrderInfoDTO;
import com.lz.dto.TOrderPayRequestDTO;
import com.lz.dto.TTxOrderPayDetailDTO;
import com.lz.model.TOrderPayDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface THXOrderPayDetailMapper {

    int insertSelective(TOrderPayDetail record);

    THxOrderPayInfoDTO selectByOrderPayDetailKey(@Param("code") String code);

    THxOrderPayInfoDTO selectOrderPackByOrderPayDetailKey(@Param("code") String code);

    OrderInfoDTO selectTxServiceTransferDetailByCode(@Param("code") String code);

    int updateByPrimaryKeySelective(TOrderPayDetail record);

    List<TOrderPayDetail> selectOrderPayDetailByPackId(@Param("id") Integer id);

    TOrderPayDetail selectByCode(@Param("code") String code);

    TOrderPayDetail selectByOrderCastChangeCodeAndTradeType(@Param("orderCastChangeCode") String orderCastChangeCode,
                                                            @Param("tradeType") String tradeType);

    TTxOrderPayDetailDTO selectTxOrderPayDetail(@Param("code") String code);

    List<TOrderPayDetail> selectOrderPayDetailByRemark(@Param("remark") String remark);

    int updateOrderPayDetailEnableByOrderCode(@Param("orderCode") String orderCode);

    int updateOrderPayDetailEnableByOrderPackCode(@Param("orderPackCode") String orderPackCode);

    int updateByCode(TOrderPayDetail detail);

    TOrderPayRequestDTO selectOrderPayInfo(@Param("code") String code);

}
