package com.lz.dao;

import com.lz.dto.OrderGroupDTO;
import com.lz.dto.KhyDataXmlDTO;
import com.lz.dto.KhyOrderInfoDTO;
import com.lz.dto.OrderInfoDTO;
import com.lz.model.TOrderInfo;
import com.lz.vo.TAdvanceOrderTmpExcel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface THXOrderInfoMapper {

    TOrderInfo selectByPrimaryKey(Integer id);

    int insertSelective(TOrderInfo orderInfo);

    int updateByPrimaryKeySelective(TOrderInfo orderInfo);

    int updateByPrimaryKey(TOrderInfo orderInfo);

    int deleteByPrimaryKey(TOrderInfo orderInfo);

    List<TOrderInfo> selectOrderInfoByPackCode(String packCode);

    OrderInfoDTO selectOrderInfoByPayDetailCode(@Param("code") String code);

    TOrderInfo selectOrderByCode(String code);

    int batchUpdateOrderPayStatus(@Param("list") List<String> list, @Param("orderPayStatus") String orderPayStatus);

    int refundUpdateOrderInfo(@Param("id") Integer id, @Param("orderExecuteStatus") String orderExecuteStatus, @Param("orderPayStatus") String orderPayStatus, @Param("returnTime") Date returnTime);

    int updateStateByOrderCode(@Param("orderCode") String orderCode, @Param("orderExecuteStatus") String orderExecuteStatus,
                               @Param("orderPayStatus") String orderPayStatus);

    List<OrderGroupDTO> selectOrderGroupByCompanyId(@Param("list") List<String> list);

    OrderInfoDTO selectOrderPackInfoByPayDetailCode(@Param("code") String code);

    int batchUpdateOrderExecutePayStatus(@Param("list") List<String> list,
                                         @Param("orderExecuteStatus") String orderExecuteStatus,
                                         @Param("orderPayStatus") String orderPayStatus,
                                         @Param("returnTime") Date returnTime);

    List<OrderGroupDTO> selectAdvanceOrderGroupByCompanyId(@Param("list") List<TAdvanceOrderTmpExcel> list);

    KhyDataXmlDTO selectKhyOrderReport(@Param("id") Integer id);

    KhyOrderInfoDTO selectKhyOrderInfo(@Param("id") Integer id);

}