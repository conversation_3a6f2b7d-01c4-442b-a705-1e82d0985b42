package com.lz.dao;

import com.lz.model.TJdWallet;
import com.lz.model.TWallet;
import com.lz.model.TZtWallet;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface THXPayWalletMapper {

    TZtWallet selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TZtWallet wallet);

    TZtWallet selectByEndUserId(@Param("endUserId") Integer endUserId);

    TZtWallet selectByCarrierCompanyPartnerAccId(@Param("partnerAccId") String partnerAccId, @Param("dataSource") String dataSource);

    TZtWallet selectByEnduserPartnerAccId(@Param("partnerAccId") String partnerAccId);

    TZtWallet selectByAccountId(@Param("accountId") Integer accountId);

}