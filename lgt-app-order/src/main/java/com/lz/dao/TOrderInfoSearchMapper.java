package com.lz.dao;

import com.lz.dto.NoSignContractOrderDTO;
import com.lz.dto.SelectedOrderPrimaryWeightDTO;
import com.lz.dto.TOrderInfoDTO;
import com.lz.vo.AppOrderSearchVO;
import com.lz.vo.PcOrderSearchVO;
import com.lz.vo.TOrderInfoVO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TOrderInfoSearchMapper {

    /**
     * 运单审核
     * @param search
     * @return
     */
    List<TOrderInfoDTO> selectByYdshPage(AppOrderSearchVO search);

    /**
     * 违规运单
     */
    List<TOrderInfoDTO> selectByWgydPage(AppOrderSearchVO search);

    /**
     * 运单检查
     * @param search
     * @return
     */
    List<TOrderInfoDTO> selectByYdjcPage(AppOrderSearchVO search);

    /**
     * 运单跟踪
     * @param search
     * @return
     */
    List<TOrderInfoDTO> selectByYdgzPage(AppOrderSearchVO search);

    List<TOrderInfoDTO> selectOrderByUserType(AppOrderSearchVO search);

    List<TOrderInfoDTO> getGzorderList(AppOrderSearchVO search);

    /**
     * 运单异常记录
     * @param record
     * @return
     */
    List<TOrderInfoVO> selectTaskByOrderInfoPage(TOrderInfoVO record);

    /**
     * PC端： 查询未签合同的运单列表
     */
    List<NoSignContractOrderDTO> pcSelectOrderNoSign(PcOrderSearchVO search);

    SelectedOrderPrimaryWeightDTO getOrderAllPrimaryWeigh(AppOrderSearchVO search);

    List<TOrderInfoDTO> getOrderPackList(AppOrderSearchVO search);

    List<TOrderInfoDTO> selectPaidOrderByPage(AppOrderSearchVO search);

    List<TOrderInfoDTO> selectPaidOrderByPageWuyang(AppOrderSearchVO search);

    List<TOrderInfoDTO> pcSelectResourceHallOrderByPage(AppOrderSearchVO search);

}