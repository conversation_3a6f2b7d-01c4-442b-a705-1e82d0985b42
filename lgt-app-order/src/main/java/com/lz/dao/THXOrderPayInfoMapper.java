package com.lz.dao;

import com.lz.model.TOrderPayInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface THXOrderPayInfoMapper {

    int insertSelective(TOrderPayInfo record);

    TOrderPayInfo selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TOrderPayInfo record);

    int updateByPrimaryKey(TOrderPayInfo record);

    TOrderPayInfo selectOnePayInfoByOrderCode(@Param("orderCode") String code);

    TOrderPayInfo selectOrderPayInfoByCode(@Param("code") String code);

    TOrderPayInfo selectPayInfoBySelective(@Param("orderCode") String orderCode, @Param("orderPayStatus") String orderPayStatus);

    int selectPayPlatformsUnique(@Param("list") List<String> list, @Param("paymentPlatforms") String paymentPlatforms);

    int updatePayStatusByOrderCode(@Param("orderCode") String orderCode, @Param("orderPayStatus") String orderPayStatus);

    int updatePackOrderOrgiOrderPayInfo(@Param("packId") Integer packId, @Param("orderPayStatus") String orderPayStatus, @Param("returnTime") Date returnTime);

    int refundRecallbackUpdateOrderPayInfo(@Param("id") Integer id, @Param("orderPayStatus") String orderPayStatus, @Param("returnTime") Date returnTime);

    int refundRecallbackUpdatePackOrderOrgiOrderPayInfo(@Param("packId") Integer packId, @Param("orderPayStatus") String orderPayStatus, @Param("returnTime") Date returnTime);

}