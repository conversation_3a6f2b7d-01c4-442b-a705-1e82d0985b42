package com.lz.dao;

import com.lz.dto.TZtBankCardDTO;
import com.lz.model.TZtBankCard;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface THxPayBankCardMapper {

    TZtBankCardDTO selectByEnduserIdAndBankCardId(@Param("enduserId") Integer enduserId, @Param("bankCardId") Integer bankCardId);

    TZtBankCardDTO selectOpenRoleBankCardInfo(@Param("bankCardId") Integer bankCardId);

    TZtBankCard selectIsDefault(@Param("accountId") Integer accountId);
}