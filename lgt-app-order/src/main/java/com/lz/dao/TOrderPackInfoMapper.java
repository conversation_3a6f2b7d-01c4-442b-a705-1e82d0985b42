package com.lz.dao;

import com.lz.dto.OrderPackDTO;
import com.lz.dto.TOrderPayInfoDTO;
import com.lz.example.TOrderPackInfoExample;
import com.lz.model.TOrderInfo;
import com.lz.model.TOrderPackInfo;
import com.lz.model.TOrderPayDetail;
import com.lz.vo.PcOrderSearchVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
public interface TOrderPackInfoMapper {

    /**
     * 打包之前判断一下打包主表的状态是否是已支付，如果已支付不能在拆包
     * Yan
     * @param code
     * @return
     */
    Boolean judgeOrderPackStatus(String code);

    /**
     * 运单打包支付-拆包： 根据打包主表 32 CODE
     * 逻辑删除打包主表数据
     * Yan
     * @param code
     * @return
     */
    int orderDismantledPackLogicDelete(String code);

    /**
     * 运单打包付款列表
     * 已经打好包的运单
     * Yan
     * @param search
     * @return
     */
    List<OrderPackDTO> getOrderPackAll(PcOrderSearchVO search);


    /**
     * 运单打包付款列表
     * 已经打好包的运单 未支付的
     * Yan
     * @param search
     * @return
     */
    List<OrderPackDTO> getOrderPackNo(PcOrderSearchVO search);


    /**
     * 运单打包付款列表
     * 已经打好包的运单 合计
     */
    Map<String,Object> getOrderPackNoSUM(PcOrderSearchVO search);

    /**
     * 运单打包付款列表
     * 已经打好包的运单 已支付的
     * Yan
     * @param search
     * @return
     */
    List<OrderPackDTO> getOrderPackOk(PcOrderSearchVO search);
    /**
     * 运单打包付款列表
     * 已经打好包的运单 已支付的累计
     * Yan
     * @param search
     * @return
     */
    Map<String,Object> getOrderPackOkSUM(PcOrderSearchVO search);


    /**
     * 运单打包付款列表
     * 已经打好包的运单 已提现的
     * dingweibo
     * @param search
     * @return
     */
    List<OrderPackDTO> getOrderPackTxOk(PcOrderSearchVO search);
    /**
     * 运单打包付款列表
     * 已经打好包的运单 已提现的累计
     * dingweibo
     * @param search
     * @return
     */
    Map<String,Object> getOrderPackTxOkSUM(PcOrderSearchVO search);

    /**
     * @Description: 打包支付页面，根据司机经纪人进行模糊搜索打包运单
     * @Author: Yan
     * @Date: 2019/8/25/025 15:06
     * @Param: search
     * @Return: Integer 打包主表ID
     */
    List<Integer> getOrderPackByDriverAgent(PcOrderSearchVO search);

    long countByExample(TOrderPackInfoExample example);

    int deleteByExample(TOrderPackInfoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TOrderPackInfo record);

    int insertSelective(TOrderPackInfo record);

    List<TOrderPackInfo> selectByExample(TOrderPackInfoExample example);

    TOrderPackInfo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TOrderPackInfo record, @Param("example") TOrderPackInfoExample example);

    int updateByExample(@Param("record") TOrderPackInfo record, @Param("example") TOrderPackInfoExample example);

    int updateByPrimaryKeySelective(TOrderPackInfo record);

    int updateByPrimaryKey(TOrderPackInfo record);

    TOrderPackInfo selectOrderPackByCode(@Param(value = "code") String code);

    int updateTotalFeeAndDispatchFeeByCode(@Param(value = "code") String code ,@Param(value = "dispatchFee")  BigDecimal dispatchFee);

    int updatePayStatusByOrderCode(@Param(value = "code")String code, @Param(value = "status")String status);

    TOrderPackInfo selectByPackInfo(String outerTradeNo);

    TOrderPayInfoDTO selectByPayInfoLimitOne(@Param("code")String code);

    List<OrderPackDTO> managerWithdrawPage(PcOrderSearchVO search);

    List<String> selectOrderCodeByPackCode(@Param("code") String code);

    int updatePackStatusByOrderCode(@Param(value = "code")String code, @Param(value = "packStatus")String packStatus);

    int updatePackLsCalculationCode(@Param(value = "code")String code);

    TOrderPayInfoDTO selectByBankno(@Param("orderCode")String orderCode);


    /**
    * @Description 根据支付子表code查询打包主表信息
    * <AUTHOR>
    * @Date   2019/9/20 9:52
    * @param
    * @Return
    * @Exception
    *
    */
    TOrderPackInfo selectPackInfoByPayDetailCode(@Param("outerTradeNo") String outerTradeNo);

    List<Integer> selectOrderIdByPackCode(@Param("code") String code);

    int updatePayStatusById(@Param("id") Integer id, @Param("status") String status);

    List<String> selectVirtualOrderByCode(@Param("codes") List<String> codes);

    List<TOrderInfo> getOrderInfoByPackCode(@Param("code") String code);

    /**
     * <AUTHOR>
     * @Description 根据原始运单查询打包主表信息
     * @Date 2020/3/2 5:35 下午
     * @Param
     * @return 5:35 下午
    **/
    TOrderPackInfo selectPackInfoByOneOrderCode(@Param("orderCode") String orderCode);

    TOrderPayDetail selectOrderDetailByOrdercode(String code);

    BigDecimal selectOldPackCarriageFeeByBankIds(@Param("list") List<Integer> list);

    BigDecimal selectNewPackCarriageFeeByBankIds(@Param("list") List<Integer> list);

    BigDecimal selectPackFQTXAmountByBankIds(@Param("list") List<String> list);

    List<String> selectOrderCodeByPackId(@Param("id") Integer id);

    TOrderPackInfo selectJdPackInfoByOrderBusinessCode(@Param("orderBusinessCode") String orderBusinessCode);

    TOrderPackInfo selectPackInfoByOrderCode(@Param("orderCode") String orderCode);

    List<Integer> getEndDriverIdByPackCode(@Param("packCode") String packCode);

    int updatePackStatusByCode(@Param(value = "code") String code, @Param(value = "packStatus") String packStatus,
                               @Param("returnTime") Date retrunTime);

    TOrderPackInfo selectPackInfoByOrderBusinessCodePayTime(@Param("orderBusinessCode") String orderBusinessCode, @Param("payTime") Date payTime);

    BigDecimal calculateInsuranceAmount(@Param("code") String code);

    TOrderPackInfo selectByVirtualOrderNo(@Param("virtualOrderNo") String virtualOrderNo);

}