package com.lz.dao;

import com.lz.dto.AppOrderProcessDTO;
import com.lz.dto.OrderSignInDTO;
import com.lz.example.TOrderStateExample;
import com.lz.model.TOrderState;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface TOrderStateMapper {

    /**
     * @Description: 获取运单 司机装货签到，卸货签到 位置
     * @Author: Yan
     * @Date: 2019/7/5/005 19:47
     * @Param: code
     * @Return: 
     */
    List<Map<String, Object>>getDriverSignInPosition(String code);

    /**
     * 运单管理高级搜索-下拉框：支付状态
     * Yan
     * @return
     */
    List<Map<String,Object>> getOrderManagerPaySelect();

    /**
     * 运单管理高级搜索-下拉框：执行状态
     * Yan
     * @return
     */
    List<Map<String,Object>> getOrderManagerExecuteSelect();

    /**
     * 运单管理-收单：根据32业务ID,查询运单执行标志
     * Yan
     * @param code
     * @return
     */
    Map<String, String> selectOrderSign(String code);

    /**
     * 运单管理-付款：根据32业务ID,查询运单执行标志
     * Yan
     * @param code
     * @return
     */
    Map<String, String> selectOrderSignForPay(String code);

    /**
     * App，PC 统一调用获取运单状态CODE值
     * Yan
     * @param code
     * @return
     */
    String selectOrderState(String code);

    /**
     * 根据运单 业务ID 查询运单执行过程
     * @param code  isPc判断是否是pc端
     * @return
     */
    List<AppOrderProcessDTO> appOrderProcess(@Param("code") String code, @Param("isPc") boolean isPc);

    /**
     * PC端运单详情：运输轨迹
     * Yan
     * @param code
     * @return
     */
    List<Map<String, Object>> getDriverOperatePositionTime(String code);

    /**
     * 根据业务ID 和状态 CODE 获取司机签到地点时间
     * @param code
     * @param state
     * @return
     */
    OrderSignInDTO selectOrderSignIn(@Param("code") String code, @Param("state") String state);

    long countByExample(TOrderStateExample example);

    int deleteByExample(TOrderStateExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TOrderState record);

    int insertSelective(TOrderState record);

    List<TOrderState> selectByExample(TOrderStateExample example);

    TOrderState selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TOrderState record, @Param("example") TOrderStateExample example);

    int updateByExample(@Param("record") TOrderState record, @Param("example") TOrderStateExample example);

    int updateByPrimaryKeySelective(TOrderState record);

    int updateByPrimaryKey(TOrderState record);

    int batchInsert(List<TOrderState> list);

    TOrderState selectOrderStateByOrderCode(@Param("states") List<String> states, @Param("orderCode") String orderCode);

    TOrderState selectNewOrderState(@Param("orderCode") String orderCode);

    TOrderState selectOrderCodeAnrState(@Param("orderCode") String orderCode,@Param("state") String state);
    List<Integer> judgeOrderStateExists(@Param("orderCode") String orderCode, @Param("orderState") String orderState);


    List<TOrderState> selectByOrderCodeAndStateNode(@Param("orderCode") String orderCode, @Param("nodeCode")String nodeCode);

    List<TOrderState> selectCaptainStatusByOrderCode(@Param("orderCode") String orderCode);

    int deleteByOrderCode(@Param("orderCode") String orderCode);

    List<TOrderState> selectNodePayInfoByOrderCode(@Param("orderCode") String orderCode);

}