package com.lz.dao;

import com.lz.dto.OrderFundChangesDTO;
import com.lz.dto.TOrderInfoAndTOrderPayInfo;
import com.lz.example.TOrderCastChangesExample;
import com.lz.model.TOrderCastChanges;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TOrderCastChangesMapper {

    /**
     * @Description: 根据资金变动 Code 获取资金转移方式和C端用户Id
     * @Author: Yan
     * @Date: 2019/8/7/007 16:56
     * @Param: code 资金变动子表 code
     * @Return: TOrderInfoAndTOrderPayInfo
     */
    TOrderInfoAndTOrderPayInfo getCapitalTransferAndEndUserId(String code);


    /**
     * PC端 运单管理: 获取订单网商子账户资金变动过程
     * Yan
     * @param code
     * @return
     */
    List<OrderFundChangesDTO> pcSelectAccountFundChanges(String code);

    List<OrderFundChangesDTO> pcSelectPackAccountFundChanges(String code);

    List<OrderFundChangesDTO> pcNewSelectPackAccountFundChanges(String code);

    List<OrderFundChangesDTO> jdPayFundChanges(String code);

    List<OrderFundChangesDTO> hxPayFundChanges(String code);

    List<OrderFundChangesDTO> jdPackPayFundChanges(String code);

    long countByExample(TOrderCastChangesExample example);

    int deleteByExample(TOrderCastChangesExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TOrderCastChanges record);

    int insertSelective(TOrderCastChanges record);

    List<TOrderCastChanges> selectByExample(TOrderCastChangesExample example);

    TOrderCastChanges selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TOrderCastChanges record, @Param("example") TOrderCastChangesExample example);

    int updateByExample(@Param("record") TOrderCastChanges record, @Param("example") TOrderCastChangesExample example);

    int updateByPrimaryKeySelective(TOrderCastChanges record);

    int updateByPrimaryKey(TOrderCastChanges record);

    //运单下资金变动表数据变为无效
    int doInvalid(String orderCode);

    TOrderCastChanges selectByNewOne(String orderCode);

    TOrderCastChanges selectByTradeType(@Param("orderCode")String orderCode,@Param("tradeType")String tradeType,@Param("userOper")String userOper);

    int doInvalidByPackage(String packageCode);

    List<TOrderCastChanges> selectByOrderCode(@Param("orderCode") String orderCode);

    TOrderCastChanges selectOrderCastChangeOneByPackCode(@Param("packCode") String packCode);

    int deleteByOrderCode(@Param("orderCode") String orderCode);

}