package com.lz.util;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lz.api.*;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.dao.*;
import com.lz.dto.*;
import com.lz.model.*;
import com.lz.schedule.model.TTask;
import com.lz.service.TOrderCastChangesService;
import com.lz.service.TOrderStateService;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
* @description 发单相关工具类
* <AUTHOR>
* @date 2020/2/10 14:13
* @param
* @return
*/
@Slf4j
@Component
public class SendOrderUtil {

    /**
     * 证件类型 - 身份证
     */
    private static final String CERTIFICATE_TYPE = "ID_CARD";

    @Resource
    private TOrderLineGoodsUserRelMemberMapper orderLineGoodsUserRelMemberMapper;

    @Resource
    private TAdvanceOrderTmpMapper advanceOrderTmpMapper;

    @Autowired
    private TCarrierEnduserCompanyRelAPI carrierEnduserCompanyRelAPI;

    @Autowired
    private TEndSUserInfoAPI endSUserInfoAPI;

    @Autowired
    private WalletService walletService;

    @Autowired
    private TOrderStateService orderStateService;

    @Autowired
    private CompanyProjectAPI companyProjectAPI;

    @Autowired
    private AppCommonAPI appCommonAPI;

    @Autowired
    private TBankCardAPI bankCardAPI;

    @Autowired
    private TOrderCastChangesService orderCastChangesService;

    @Autowired
    private SysParamAPI sysParamAPI;

    @Autowired
    private LinkGoodsRelAPI linkGoodsRelAPI;

    @Autowired
    private TOrderInfoMapper orderInfoMapper;

    @Resource
    private TOrderTaskMapper orderTaskMapper;

    @Autowired
    private TOrderPayRuleMapper tOrderPayRuleMapper;

    @Autowired
    private TBankcardMonthlyAPI bankcardMonthlyAPI;

    @Autowired
    private TOrderBankcardMonthlyMapper orderBankcardMonthlyMapper;

    @Resource
    private TOrderInfoDetailMapper orderInfoDetailMapper;

    @Resource
    private TOrderDriverCarStatisticsMapper orderDriverCarStatisticsMapper;

    /**
     * @param
     * @Description 创建C端用户和承运方的关系
     * <AUTHOR>
     * @Date 2019/7/15 19:30
     * @Return
     * @Exception
     */
    @Deprecated
    public void createCarrierAndEnduserRel(SendOrderVO sendBillVO, CarDriverRelVO carDriverRelVO,
                                           String capitalTransferType, LinkedHashMap carrier, String orderCode,
                                           CompanySourceDTO companySourceDTO) {
        // 如果承运方无子账号，不创建C端与承运方的管子，申请子账号，钱包，待之后使用工具批量处理
        if (null != carrier.get("thridParySubAccount") && StringUtils.isNotBlank(carrier.get("thridParySubAccount").toString())) {
            //支付给司机
            //TODO 司机与承运方是否有子账号，如果没有生成task
            TCarrierEnduserCompanyRelVo carrierEnduserCompanyRel = new TCarrierEnduserCompanyRelVo();
            carrierEnduserCompanyRel.setDatasource("CD");
            carrierEnduserCompanyRel.setCarrierId(sendBillVO.getCarrierId());
            carrierEnduserCompanyRel.setEnduserCompanyId(carDriverRelVO.getEndDriverId());
            ResultUtil carrierRelResult = carrierEnduserCompanyRelAPI.selectCarrierRel(carrierEnduserCompanyRel);
            Object carrierRelResultData = carrierRelResult.getData();
            Integer carrierEnduserComapnyRelId = 0;
            //如果司机与承运方无关系
            if (null == carrierRelResultData) {
                //创建承运方与C端(司机)的关系
                carrierEnduserComapnyRelId = createEnduserCarrierAndWallet(carrier, carDriverRelVO, orderCode);
            }
            if (null != carrierRelResultData) {
                LinkedHashMap carrierRel = (LinkedHashMap) carrierRelResultData;
                carrierEnduserComapnyRelId = (Integer) carrierRel.get("id");
                if (null == carrierRel.get("thridParySubAccount")) {
                    ThirdPartSubAccountVO partSubAccountVO = new ThirdPartSubAccountVO();
                    partSubAccountVO.setRealName(carDriverRelVO.getRealName());
                    partSubAccountVO.setPhone(carDriverRelVO.getPhone());
                    partSubAccountVO.setCertificateCategoryName(CERTIFICATE_TYPE);
                    partSubAccountVO.setCertificateNo(carDriverRelVO.getIdcard());
                    applyAccount(carrier, carrierRel.get("uid").toString(), partSubAccountVO, orderCode);
                }
            }
            createWallet(carrierEnduserComapnyRelId, "CDRIVER");

            //支付给车队长
            if (capitalTransferType.equals(DictEnum.PAYTOCAPTAIN.code)) {
                if (null != carDriverRelVO.getCaptainId()) {
                    TEndUserInfo endUserInfo = endSUserInfoAPI.selectByPrimaryKey(carDriverRelVO.getCaptainId());
                    carDriverRelVO.setCaptainIdCard(endUserInfo.getIdcard());
                    carDriverRelVO.setCaptainName(endUserInfo.getRealName());
                    carDriverRelVO.setCaptainPhone(endUserInfo.getPhone());
                    //TODO 查询车队长和承运方是否有子账号
                    TCarrierEnduserCompanyRelVo carrierEnduserCarBossRel = new TCarrierEnduserCompanyRelVo();
                    carrierEnduserCarBossRel.setDatasource("CD");
                    carrierEnduserCarBossRel.setCarrierId(sendBillVO.getCarrierId());
                    carrierEnduserCarBossRel.setEnduserCompanyId(carDriverRelVO.getCaptainId());
                    ResultUtil carrierCarBossRelResult = carrierEnduserCompanyRelAPI.selectCarrierRel(carrierEnduserCarBossRel);
                    Object carrierBossRelResultData = carrierCarBossRelResult.getData();
                    log.info("车队长信息{}", JSONUtil.toJsonPrettyStr(carrierBossRelResultData));
                    if (null == carrierBossRelResultData) {
                        carrierEnduserComapnyRelId = crerateCarbossCarrierRel(carrier, carDriverRelVO, orderCode);
                    } else {
                        LinkedHashMap carrierRel = (LinkedHashMap) carrierBossRelResultData;
                        carrierEnduserComapnyRelId = (Integer) carrierRel.get("id");
                        if (null == carrierRel.get("thridParySubAccount")) {
                            ThirdPartSubAccountVO partSubAccountVO = new ThirdPartSubAccountVO();
                            partSubAccountVO.setRealName(endUserInfo.getRealName());
                            partSubAccountVO.setPhone(endUserInfo.getPhone());
                            partSubAccountVO.setCertificateCategoryName(CERTIFICATE_TYPE);
                            partSubAccountVO.setCertificateNo(endUserInfo.getIdcard());
                            applyAccount(carrier, carrierRel.get("uid").toString(), partSubAccountVO, orderCode);
                        }
                    }

                    createWallet(carrierEnduserComapnyRelId, DictEnum.CCAPTAIN.code);
                }
            }

            // 如果是经纪人模式
            if (DictEnum.MANAGERPATTERN.code.equals(companySourceDTO.getCapitalTransferPattern())) {
                //经纪人id
                if (null != companySourceDTO.getManagerId()) {
                    TEndUserInfo endUserInfo = endSUserInfoAPI.selectByPrimaryKey(companySourceDTO.getManagerId());
                    if (null != endUserInfo && !endUserInfo.getEnable()) {
                        //TODO 查询经纪人和承运方是否有子账号
                        TCarrierEnduserCompanyRelVo carrierEnduserAgentRel = new TCarrierEnduserCompanyRelVo();
                        carrierEnduserAgentRel.setDatasource("CD");
                        carrierEnduserAgentRel.setCarrierId(sendBillVO.getCarrierId());
                        carrierEnduserAgentRel.setEnduserCompanyId(companySourceDTO.getManagerId());
                        ResultUtil carrierAgentRelResult = carrierEnduserCompanyRelAPI.selectCarrierRel(carrierEnduserAgentRel);
                        Object carrierAgentRelResultData = carrierAgentRelResult.getData();
                        if (null == carrierAgentRelResultData) {
                            carDriverRelVO.setAgentId(companySourceDTO.getManagerId());
                            carrierEnduserComapnyRelId = createAgentCarrierRelAndWallet(carrier, endUserInfo, orderCode);
                        }
                        if (null != carrierAgentRelResultData) {
                            LinkedHashMap carrierRel = (LinkedHashMap) carrierAgentRelResultData;
                            carrierEnduserComapnyRelId = (Integer) carrierRel.get("id");
                            if (null == carrierRel.get("thridParySubAccount")) {
                                //申请子账号
                                //并向Task添加一条任务：司机申请子账号
                                ThirdPartSubAccountVO partSubAccountVO = new ThirdPartSubAccountVO();
                                partSubAccountVO.setRealName(endUserInfo.getRealName());
                                partSubAccountVO.setPhone(endUserInfo.getPhone());
                                partSubAccountVO.setCertificateCategoryName(CERTIFICATE_TYPE);
                                partSubAccountVO.setCertificateNo(endUserInfo.getIdcard());
                                applyAccount(carrier, carrierRel.get("uid").toString(), partSubAccountVO, orderCode);
                            }
                        }
                        createWallet(carrierEnduserComapnyRelId, DictEnum.CMANAGER.code);
                    }
                }
            }
        }
    }

    /**
     * 创建承运方与C端的关系
     *
     * @param carrierId        承运方id
     * @param enduserCompanyId 车辆司机信息(C端用户表ID)
     * @return createInfo 创建信息
     * <AUTHOR>
     */
    private LinkedHashMap createEnduserCarrierRel(Integer carrierId, Integer enduserCompanyId) {
        //TODO 创建承运方与C端的关系
        TCarrierEnduserCompanyRel carrierEnduserRel = new TCarrierEnduserCompanyRel();
        carrierEnduserRel.setCarrierId(carrierId);
        carrierEnduserRel.setEnduserCompanyId(enduserCompanyId);
        carrierEnduserRel.setDatasouce("CD");
        carrierEnduserRel.setUid(IdWorkerUtil.getInstance().nextId());
        carrierEnduserRel.setEnable(false);
        carrierEnduserRel.setUid(IdWorkerUtil.getInstance().nextId());
        ResultUtil resultUtil = carrierEnduserCompanyRelAPI.save(carrierEnduserRel);
        LinkedHashMap carrierEnduserRelMap = (LinkedHashMap) resultUtil.getData();
        return carrierEnduserRelMap;
    }

    /**
     * <AUTHOR>
     * @Description 创建车队长和承运方的关系、申请子账号、创建钱包
     * @Date 2020/7/23 上午9:49
     * @Param
     * @return
     **/
    public Integer createCaptainCarrierRel(CarDriverRelVO carDriverRelVO, String orderCode, LinkedHashMap carrierInfo) {
        //TODO 车队长与承运方是否有子账号，如果没有生成task
        TCarrierEnduserCompanyRelVo carrierEnduserCompanyRel = new TCarrierEnduserCompanyRelVo();
        carrierEnduserCompanyRel.setDatasource("CD");
        carrierEnduserCompanyRel.setCarrierId(Integer.parseInt(carrierInfo.get("id").toString()));
        carrierEnduserCompanyRel.setEnduserCompanyId(carDriverRelVO.getCaptainId());
        ResultUtil carrierRelResult = carrierEnduserCompanyRelAPI.selectCarrierRel(carrierEnduserCompanyRel);
        Object carrierRelResultData = carrierRelResult.getData();
        Integer carrierEnduserComapnyRelId = 0;
        //如果车队长与承运方无关系
        if (null == carrierRelResultData) {
            //创建承运方与车队长的关系
            carrierEnduserComapnyRelId = createEnduserCarrierRelAndApplyAccount(carrierInfo, orderCode, carDriverRelVO.getCaptainId(),
                    carDriverRelVO.getCaptainName(), carDriverRelVO.getCaptainPhone(), carDriverRelVO.getCaptainIdCard());
        }
        if (null != carrierRelResultData){
            LinkedHashMap carrierRel = (LinkedHashMap) carrierRelResultData;
            carrierEnduserComapnyRelId = (Integer) carrierRel.get("id");
            if (null == carrierRel.get("thridParySubAccount")){
                ThirdPartSubAccountVO partSubAccountVO = new ThirdPartSubAccountVO();
                partSubAccountVO.setRealName(carDriverRelVO.getCaptainName());
                partSubAccountVO.setPhone(carDriverRelVO.getCaptainPhone());
                partSubAccountVO.setCertificateCategoryName(CERTIFICATE_TYPE);
                partSubAccountVO.setCertificateNo(carDriverRelVO.getCaptainIdCard());
                applyAccount(carrierInfo, carrierRel.get("uid").toString(), partSubAccountVO, orderCode);
            }
        }
        return carrierEnduserComapnyRelId;
    }

    /**
     * @Description 创建和承运方关系、申请子账号、虚拟钱包
     * <AUTHOR>
     * @Date   2019/6/8 17:12
     * @Param
     * @Return
     * @Exception
     *
     */
    private Integer createEnduserCarrierRelAndApplyAccount(LinkedHashMap carrierInfo, String orderCode, Integer enduserId, String realName,
                                                           String phone, String idcacrd) {
        LinkedHashMap enduserCarrierRel = createEnduserCarrierRel((Integer.parseInt(carrierInfo.get("id").toString())), enduserId);
        //并向Task添加一条任务：申请子账号
        ThirdPartSubAccountVO partSubAccountVO = new ThirdPartSubAccountVO();
        partSubAccountVO.setRealName(realName);
        partSubAccountVO.setPhone(phone);
        partSubAccountVO.setCertificateCategoryName(CERTIFICATE_TYPE);
        partSubAccountVO.setCertificateNo(idcacrd);
        applyAccount(carrierInfo, enduserCarrierRel.get("uid").toString(), partSubAccountVO,orderCode);
        Object id = enduserCarrierRel.get("id");
        return (Integer) id;
    }




    /**
     * 创建TASK，向网商申请子账号
     *
     * @param carrierInfo                 承运方信息
     * @param carrierEnduserComapnyRelUid 承运方与C端用户关系的uid
     * @param thirdPartSubAccountVO       第三方账号信息
     * <AUTHOR>
     */
    private void applyAccount(LinkedHashMap carrierInfo, String carrierEnduserComapnyRelUid, ThirdPartSubAccountVO thirdPartSubAccountVO, String orderCode) {
        TTask tTask = new TTask();
        tTask.setTaskType("SQZZH");
        tTask.setBusinessType("ZF");
        tTask.setSourceFieldname("t_order_info");
        tTask.setSourcekeyFieldname("code");
        tTask.setSourceFieldvalue(orderCode);
        tTask.setTaskId(IdWorkerUtil.getInstance().nextId());
        HashMap<String, Object> taskParam = new HashMap<>();
        taskParam.put("thirdPartyInterfaceManageAddress", carrierInfo.get("thirdPartyInterfaceManageAddress"));
        taskParam.put("bussinessPlatformSignCode", carrierInfo.get("bussinessPlatformSignCode"));
        taskParam.put("uid", carrierEnduserComapnyRelUid);
        taskParam.put("thirdPartyInterfaceMainAccountNo", carrierInfo.get("thirdPartyInterfaceMainAccountNo"));
        //会员名称。用户昵称(平台个人会员登录名)
        taskParam.put("memberName", thirdPartSubAccountVO.getPhone());
        //真实姓名
        taskParam.put("realName", thirdPartSubAccountVO.getRealName());
        taskParam.put("certificateType", thirdPartSubAccountVO.getCertificateCategoryName());
        taskParam.put("certificateNo", thirdPartSubAccountVO.getCertificateNo());
        tTask.setRequestTimes(0);
        tTask.setRequestParameter(JSONObject.toJSONString(taskParam));
        tTask.setEnable(false);
        orderTaskMapper.insert(tTask);
    }

    /**
     * 创建虚拟钱包
     *
     * @param carrierEnduserComapnyRelId 承运方与C端用户关系
     * @param purseCategory              钱包类型
     * <AUTHOR>
     */
    public Integer createWallet(Integer carrierEnduserComapnyRelId, String purseCategory) {
        ResultUtil save = null;
        String dataSource = "CD";
        TWallet wallet = new TWallet();
        wallet.setCarrierEnduserCompanyId(carrierEnduserComapnyRelId);
        wallet.setPurseCategory(purseCategory);
        wallet.setDatasource(dataSource);
        save = walletService.selectAndSave(wallet);
        if (null != save && null != save.getCode() && save.getCode().equals("success")) {
            LinkedHashMap walletMap = (LinkedHashMap) save.getData();
            Object id = walletMap.get("id");
            if (id != null) {
                return (Integer) id;
            }
        }

        return null;
    }

    /**
     * @Description 创建经纪人与承运方关系
     * <AUTHOR>
     * @Date 2019/6/5 18:01
     * @Param
     * @Return
     * @Exception
     */
    public Integer createAgentCarrierRelAndWallet(LinkedHashMap carrier, TEndUserInfo endUserInfo, String orderCode) {
        Integer carrierId = (Integer) carrier.get("id");
        //创建承运方与C端(经纪人)的关系
        LinkedHashMap enduserCarrierRel = createEnduserCarrierRel(carrierId, endUserInfo.getId());
        //并向Task添加一条任务：司机申请子账号
        ThirdPartSubAccountVO partSubAccountVO = new ThirdPartSubAccountVO();
        partSubAccountVO.setRealName(endUserInfo.getRealName());
        partSubAccountVO.setPhone(endUserInfo.getPhone());
        partSubAccountVO.setCertificateCategoryName(CERTIFICATE_TYPE);
        partSubAccountVO.setCertificateNo(endUserInfo.getIdcard());
        applyAccount(carrier, enduserCarrierRel.get("uid").toString(), partSubAccountVO, orderCode);
        Object id = enduserCarrierRel.get("id");
        return (Integer) id;
    }

    /**
     * @Description 创建司机和承运方关系、申请子账号、虚拟钱包
     * <AUTHOR>
     * @Date 2019/6/8 17:12
     * @Param
     * @Return
     * @Exception
     */
    private Integer createEnduserCarrierAndWallet(LinkedHashMap carrier, CarDriverRelVO carDriverRelVO, String orderCode) {
        Integer carrierId = (Integer) carrier.get("id");
        LinkedHashMap enduserCarrierRel = createEnduserCarrierRel(carrierId, carDriverRelVO.getEndDriverId());
        //并向Task添加一条任务：司机申请子账号
        ThirdPartSubAccountVO partSubAccountVO = new ThirdPartSubAccountVO();
        partSubAccountVO.setRealName(carDriverRelVO.getRealName());
        partSubAccountVO.setPhone(carDriverRelVO.getPhone());
        partSubAccountVO.setCertificateCategoryName(CERTIFICATE_TYPE);
        partSubAccountVO.setCertificateNo(carDriverRelVO.getIdcard());
        applyAccount(carrier, enduserCarrierRel.get("uid").toString(), partSubAccountVO, orderCode);
        Object id = enduserCarrierRel.get("id");
        return (Integer) id;
    }

    /**
     * @Description 创建车队长和承运方关系、申请子账号、虚拟钱包
     * <AUTHOR>
     * @Date 2019/6/8 17:11
     * @Param
     * @7Retur` n
     * @Exception
     */
    public Integer crerateCarbossCarrierRel(LinkedHashMap carrier, CarDriverRelVO carDriverRelVO, String orderCode) {
        Integer carrierId = (Integer) carrier.get("id");
        //创建承运方与C端(车队长)的关系
        LinkedHashMap enduserCarrierRel = createEnduserCarrierRel(carrierId, carDriverRelVO.getCaptainId());
        //并向Task添加一条任务：车队长申请子账号
        ThirdPartSubAccountVO partSubAccountVO = new ThirdPartSubAccountVO();
        partSubAccountVO.setRealName(carDriverRelVO.getCaptainName());
        partSubAccountVO.setPhone(carDriverRelVO.getCaptainPhone());
        partSubAccountVO.setCertificateCategoryName(CERTIFICATE_TYPE);
        partSubAccountVO.setCertificateNo(carDriverRelVO.getCaptainIdCard());
        applyAccount(carrier, enduserCarrierRel.get("uid").toString(), partSubAccountVO, orderCode);
        Object id = enduserCarrierRel.get("id");
        return (Integer) id;
    }

    /*
     * <AUTHOR>
     * @Description 签署合同
     * @Date 2020/2/7 17:46
     * @Param
     * @param taskType 创建合同：QSHT, 司机签合同 ：DRIVERQSHT
     * @return
     **/
    @Deprecated
    public void signContract(TOrderInfo orderInfo, LinkedHashMap carrier, CarDriverRelVO carDriverRelVO,
                             CompanySourceDTO companySourceDTO, String taskType) {

        // 车队长模式，未选择车队长，不创建合同任务
        if (null != companySourceDTO.getCapitalTransferType() && DictEnum.PAYTOCAPTAIN.code.equals(companySourceDTO.getCapitalTransferType())) {
            if (null == carDriverRelVO.getCaptainId()) {
                return;
            }
        }
        TEndUserInfo driverInfo = endSUserInfoAPI.selectByPrimaryKey(orderInfo.getEndDriverId());
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("orderBusinessCode", orderInfo.getOrderBusinessCode());
        map.put("orderCode", orderInfo.getCode());
        map.put("carrierId", orderInfo.getCarrierId());//承运方id
        map.put("AId", orderInfo.getEndDriverId());//司机id
        map.put("contractNum", IdWorkerUtil.getInstance().nextId());//合同编号
        map.put("mobilePhone", "***********"); //平台注册手机号  固定的
        map.put("QName", carrier.get("carrierName"));//承运方名称
        map.put("CCDBusinessNo", carrier.get("businessLicenseNo"));//营业执照号
        map.put("AName", driverInfo.getRealName());//司机名称
        map.put("Aphone", driverInfo.getPhone());//司机手机号
        map.put("ACarNo", carDriverRelVO.getVehicleNumber());//车牌号
        map.put("ACardNo", driverInfo.getIdcard());//身份证号
        map.put("CFNo", orderInfo.getCode());//运单号
        map.put("SGType", orderInfo.getGoodsName());//货物类型
        Double settledWeight;
        if (null == orderInfo.getEstimateGoodsWeight() || BigDecimal.valueOf(orderInfo.getEstimateGoodsWeight()).compareTo(BigDecimal.valueOf(0)) == 0) {
            Double primaryWeight = orderInfo.getPrimaryWeight();
            settledWeight = primaryWeight;
            map.put("SGFactCount", primaryWeight);//数量
        } else {
            Double estimateGoodsWeight = orderInfo.getEstimateGoodsWeight();
            settledWeight = estimateGoodsWeight;
            map.put("SGFactCount", estimateGoodsWeight);//数量
        }
        map.put("CDName", companySourceDTO.getCompanyName());//发货方
        map.put("SGLoadingAddress", orderInfo.getFromName());//装货地点
        map.put("SGUnLoadingName", orderInfo.getDeliverGoodsContacter());//装货联系人
        map.put("SGUnLoadingPhone", orderInfo.getDeliverGoodsContacterPhone());//装货联系电话
        map.put("SGUnLoadingAddress", orderInfo.getEndName());//收货地址
        map.put("SGLoadingName", orderInfo.getReceiveGoodsContacter());//收货联系人
        map.put("SGLoadingPhone", orderInfo.getReceiveGoodsContacterPhone());//收货联系人电话
        map.put("SGLimitTime", "30");//要求到货期限
        map.put("SGFreightTotal", orderInfo.getEstimateTotalFee());//总运费
        map.put("type", "1");//类型 1：个人 、2：企业
        map.put("cardType", "0");// 证件类型 0：身份证 1：军官证 ,2：护照、 3：驾驶证、4：工商登记证、5：税务登记证、6：组织机构代码、7：其他证件，8：统一社会信用代码
        map.put("Qphone", carrier.get("companyContactsPhone"));//企业联系手机号

        TTask tTask = new TTask();

        // 如果是经纪人模式，查询经纪人相关信息
        if (null != companySourceDTO.getCapitalTransferPattern()
                && DictEnum.MANAGERPATTERN.code.equals(companySourceDTO.getCapitalTransferPattern())) {
            if (null != companySourceDTO.getManagerId()) {
                TEndUserInfo endUserInfo = endSUserInfoAPI.selectByPrimaryKey(companySourceDTO.getManagerId());
                if (null != endUserInfo.getEnable() && !endUserInfo.getEnable()) {
                    map.put("BName", endUserInfo.getRealName());
                    map.put("Bphone", endUserInfo.getPhone());
                    map.put("BCardNo", endUserInfo.getIdcard());
                    map.put("BId", endUserInfo.getId());
//                    BigDecimal serviceFee = OrderMoneyUtil.getServiceFee(orderInfo.getEstimateTotalFee(),
//                            companySourceDTO.getShareMethod(), companySourceDTO.getShareValue());
                    map.put("SGServiceFee", calulateServiceFee(companySourceDTO, orderInfo.getEstimateTotalFee(), settledWeight));
                }
            } else {
                log.error("经纪人模式发单，货源无经纪人");
            }
            // 三方协议
            tTask.setTaskTypeNode(DictEnum.THREESIDES.code);//签署合同
        } else {
            // 双方协议
            tTask.setTaskTypeNode(DictEnum.BOTHSIDES.code);
        }

        // 支付到车队长
        if (null != companySourceDTO.getCapitalTransferType() && DictEnum.PAYTOCAPTAIN.code.equals(companySourceDTO.getCapitalTransferType())) {
            if (null != companySourceDTO.getCapitalTransferPattern()
                    && DictEnum.MANAGERPATTERN.code.equals(companySourceDTO.getCapitalTransferPattern())) {
                tTask.setTaskTypeNode(DictEnum.FOURSIDES.code);
            } else {
                tTask.setTaskTypeNode(DictEnum.CAPTAINTHREESIDES.code);
            }
            // 车队长信息
            map.put("DName", carDriverRelVO.getCaptainName());
            map.put("Dphone", carDriverRelVO.getCaptainPhone());
            map.put("DCardNo", carDriverRelVO.getCaptainIdCard());
            map.put("DId", carDriverRelVO.getCaptainId());
        }
        String requestParameter = JSONObject.toJSONString(map);

        tTask.setRequestParameter(requestParameter);
        tTask.setTaskId(IdWorkerUtil.getInstance().nextId());
        tTask.setTaskType(taskType);//运输合同
        tTask.setBusinessType("HT");//HT合同 ZF 支付
        tTask.setSourceTablename("T_ORDER_INFO");
        tTask.setSourceFieldvalue(orderInfo.getCode());
        tTask.setSourceFieldname("code");
        tTask.setSourceFieldvalue(orderInfo.getCode());
        tTask.setRequestTimes(0);
        tTask.setCreateTime(new Date());
        tTask.setRequestDate(new Date());
        tTask.setIsSuccessed(false);
        tTask.setEnable(false);
        orderTaskMapper.insert(tTask);
    }

    public static void main(String[] args) {
        log.info("", BigDecimal.valueOf(0.0).compareTo(BigDecimal.valueOf(0)) == 0);
    }

    /**
     * @return 1:49 下午
     * <AUTHOR>
     * @Description 支付前校验运单数据
     * @Date 2020/3/3 1:49 下午
     * @Param
     **/
    public ResultUtil checkOrderPay(TOrderInfo orderInfoByCoded, SendOrderVO record) {
        ResultUtil resultUtil = ResultUtil.error();
        if (null != orderInfoByCoded) {
            // 判断是否有支付权限
            if (!CurrentUser.accountIsCompanyAdmin()) {
                TAdvanceOrderTmp tAdvanceOrderTmp = advanceOrderTmpMapper.selectAdvanceOrderTempByOrderCode(orderInfoByCoded.getCode());
                // 判断是否预支付运单，预支付运单不判断支付权限
                if (null == tAdvanceOrderTmp) {
                    Integer payPms = orderLineGoodsUserRelMemberMapper.judgeOrderPayPms(orderInfoByCoded.getLineGoodsRelId(), CurrentUser.getUserAccountId());
                    if (null == payPms || payPms == 0) {
                        resultUtil.setCode("error");
                        resultUtil.setData(orderInfoByCoded.getOrderBusinessCode());
                        resultUtil.setMsg("请选择有支付权限的运单");
                        return resultUtil;
                    }
                }
            }
            Integer lineGoodsRelId = orderInfoByCoded.getLineGoodsRelId();
            if (null != orderInfoByCoded.getPackStatus()) {
                if (orderInfoByCoded.getPackStatus().equals("1")) {
                    resultUtil.setCode("error");
                    resultUtil.setData(orderInfoByCoded.getOrderBusinessCode());
                    resultUtil.setMsg("打包原始运单不可在此支付");
                    return resultUtil;
                } else {
                    TOrderState orderState = orderStateService.selectNewOrderState(orderInfoByCoded.getCode());
                    TOrderCastChanges tOrderCastChanges = orderCastChangesService.selectByNewOne(orderInfoByCoded.getCode());
                    String capitalTransferPattern = null == tOrderCastChanges.getCapitalTransferPattern() ? "" : tOrderCastChanges.getCapitalTransferPattern();
                    // 支付流程，且审核未通过
                    if (orderInfoByCoded.getOrderPayStatus().equals(DictEnum.M065.code) && !DictEnum.S0651.code.equals(orderState.getStateNodeValue())) {
                        if (DictEnum.S0650.code.equals(orderState.getStateNodeValue())) {
                            resultUtil.setMsg("当前运单支付前需要审核，审核员未审核，暂不能支付");
                        } else {
                            resultUtil.setMsg("当前运单审核未通过，暂不能支付");
                        }
                        resultUtil.setCode("error");
                        resultUtil.setData(orderInfoByCoded.getOrderBusinessCode());
                        return resultUtil;
                    }
                    if (null != record.getUserConfirmCarriagePayment() && !(record.getUserConfirmCarriagePayment().compareTo(BigDecimal.ZERO) > 0)) {
                        return ResultUtil.error("确认应付运费应当大于0");
                    }
                    if (null == record.getServiceFee()) {
                        record.setServiceFee(BigDecimal.ZERO);
                    }
                    if (null == record.getUserConfirmServiceFee()) {
                        record.setUserConfirmServiceFee(BigDecimal.ZERO);
                    }
                    if (null != tOrderCastChanges.getCapitalTransferPattern() &&
                            tOrderCastChanges.getCapitalTransferPattern().equals(DictEnum.MANAGERPATTERN.code)) {
                        if (null != record.getUserConfirmCarriagePayment()) {
                            if (record.getUserConfirmCarriagePayment().compareTo(record.getUserConfirmServiceFee()) <= 0) {
                                resultUtil.setMsg("该运单经纪人服务费大于或等于运费，请修改确认应付运费或经纪人服务费再支付");
                                resultUtil.setCode("error");
                                resultUtil.setData(orderInfoByCoded.getOrderBusinessCode());
                                return resultUtil;
                            }
                        } else {
                            if (orderInfoByCoded.getUserConfirmServiceFee().compareTo(orderInfoByCoded.getUserConfirmPaymentAmount()) >= 0) {
                                resultUtil.setMsg("该运单经纪人服务费大于或等于运费，请修改确认应付运费或经纪人服务费再支付");
                                resultUtil.setCode("error");
                                resultUtil.setData(orderInfoByCoded.getOrderBusinessCode());
                                return resultUtil;
                            }
                        }
                    }
                    if (null != record.getOperateMethod() && DictEnum.PC.code.equals(record.getOperateMethod())) {
                        if (null != record.getWithdrawType() && DictEnum.AUTOMATION.code.equals(record.getWithdrawType())) {
                            if (null == record.getBankId()) {
                                return ResultUtil.error("当前提现方式是自动到卡，请选择银行卡");
                            }
                        }
                    }
                    try {
                        if (orderInfoByCoded.getOrderExecuteStatus().equals(DictEnum.O060.code)
                                && (com.lz.common.util.StringUtils.isEmpty(orderInfoByCoded.getOrderPayStatus())
                                || orderInfoByCoded.getOrderPayStatus().equals(DictEnum.M065.code)
                                || orderInfoByCoded.getOrderPayStatus().equals(DictEnum.M080.code)
                                || orderInfoByCoded.getOrderPayStatus().equals(DictEnum.M095.code))) {
                            //TODO 1. 判断车辆和司机是否审核通过：线路货物关系表中
                            LineGoodsRelInfo lineGoodsRelInfo = linkGoodsRelAPI.selectLineGoodsRelInfo(new TGoodsSourceInfo().setLineGoodsRelId(orderInfoByCoded.getLineGoodsRelId()));
                            if (null != lineGoodsRelInfo && null != lineGoodsRelInfo.getTransportIdentityCheck()
                                    && lineGoodsRelInfo.getTransportIdentityCheck()) {
                                String checkUserCarStatus = checkUserCarStatus(orderInfoByCoded,
                                        tOrderCastChanges.getCapitalTransferType(), capitalTransferPattern);
                                if (com.lz.common.util.StringUtils.isNotBlank(checkUserCarStatus)) {
                                    resultUtil.setCode("error");
                                    resultUtil.setData(orderInfoByCoded.getOrderBusinessCode());
                                    resultUtil.setMsg(checkUserCarStatus);
                                    return resultUtil;
                                }
                            }
                            //TODO 2。判断合同
                            String contractStatus = orderInfoByCoded.getContractStatus();
                            if (contractStatus.equals(DictEnum.WQD.code)) {
                                resultUtil.setCode("error");
                                resultUtil.setData(orderInfoByCoded.getOrderBusinessCode());
                                resultUtil.setMsg("运单未签署电子合同，请联系司机签署线下合同。");
                                return resultUtil;
                            }
                            //TODO 3. 查询车主权益证明(资金转移方式到车主) 车队长上线后为了处理老数据 车老板不进行判断
                            /*if (DictEnum.PAYTOBELONGER.code.equals(tOrderCastChanges.getCapitalTransferType())
                                    || DictEnum.FIRSTBROKERBELONGER.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                                if (null == orderInfoByCoded.getEndCarOwnerId()) {
                                    resultUtil.setData(orderInfoByCoded.getOrderBusinessCode());
                                    resultUtil.setMsg("缺少车主信息");
                                    return resultUtil;
                                }
                                ResultUtil selectCarOwnerQY = appCommonAPI.selectCarOwnerQY(orderInfoByCoded.getEndCarOwnerId(), orderInfoByCoded.getVehicleId());
                                if (DictEnum.ERROR.code.equals(selectCarOwnerQY.getCode())) {
                                    selectCarOwnerQY.setData(orderInfoByCoded.getOrderBusinessCode());
                                    selectCarOwnerQY.setMsg(selectCarOwnerQY.getMsg());
                                    return selectCarOwnerQY;
                                }
                            }*/
                            //TODO 4. 获取最新资金变动表
                            String withdrawType = tOrderCastChanges.getWithdrawType();
                            Integer enduserId = null;
                            ResultUtil enduserIdByCapitalTransferType = getEnduserIdByCapitalTransferType(tOrderCastChanges.getCapitalTransferType(), orderInfoByCoded);
                            if (DictEnum.SUCCESS.code.equals(enduserIdByCapitalTransferType.getCode())) {
                                if (null != enduserIdByCapitalTransferType.getData()) {
                                    enduserId = (Integer) enduserIdByCapitalTransferType.getData();
                                }
                            } else {
                                enduserIdByCapitalTransferType.setData(orderInfoByCoded.getOrderBusinessCode());
                                return enduserIdByCapitalTransferType;
                            }

                            if (null != record.getUserConfirmServiceFee()) {
                                orderInfoByCoded.setUserConfirmServiceFee(record.getUserConfirmServiceFee());
                            }
                            if (null != record.getUserConfirmCarriagePayment()) {
                                orderInfoByCoded.setUserConfirmPaymentAmount(record.getUserConfirmCarriagePayment());
                            }

                            // comment 判断车主、经纪人钱包是否存在、子账号、uid
                            if (null != capitalTransferPattern && StringUtils.isNotBlank(capitalTransferPattern)) {
                                ResultUtil walletExist = getWalletExist(tOrderCastChanges.getCapitalTransferType(), capitalTransferPattern, orderInfoByCoded);
                                if (null != walletExist) {
                                    return walletExist;
                                }
                            }

                            //提现方式：自动到卡
                            if (withdrawType.equals(DictEnum.AUTOMATION.code)) {
                                String capitalTransferType = tOrderCastChanges.getCapitalTransferType();
                                ResultUtil res = ResultUtil.ok();
                                // 查询车辆司机审核状态
                                String checkUserCarStatus = checkUserCarStatus(orderInfoByCoded,
                                        tOrderCastChanges.getCapitalTransferType(), capitalTransferPattern);
                                if (com.lz.common.util.StringUtils.isNotBlank(checkUserCarStatus)) {
                                    resultUtil.setCode("error");
                                    resultUtil.setData(orderInfoByCoded.getOrderBusinessCode());
                                    resultUtil.setMsg(checkUserCarStatus);
                                    return resultUtil;
                                }
                                // 查询C端是否绑定银行卡，提现额度是否超额
                                if (enduserId == null) {
                                    res.setCode("error");
                                    res.setData(orderInfoByCoded.getOrderBusinessCode());
                                    res.setMsg("未找到C端用户");
                                    return resultUtil;
                                } else {
                                    Integer bankId = null == record.getBankId() ? null : record.getBankId();
                                    TBankCard tBankCard;
                                    if (null == bankId) {
                                        // 1. 查询默认银行卡
                                        tBankCard = selectDefaultBankCard(enduserId);
                                        if (null == tBankCard) {
                                            ResultUtil error = ResultUtil.error();
                                            error.setData(orderInfoByCoded.getOrderBusinessCode());
                                            String orderBusinessCode = orderInfoByCoded.getOrderBusinessCode().substring(orderInfoByCoded.getOrderBusinessCode().length() - 8);
                                            if (capitalTransferType.equals(DictEnum.PAYTODRIVER.code)
                                                    || capitalTransferType.equals(DictEnum.FIRSTBROKERDRIVER.code)) {
                                                error.setMsg("尾号为" + orderBusinessCode
                                                        + "的运单，未找到司机默认银行卡，请联系企业管理员或运营平台维护后再支付。");
                                            }
                                            if (capitalTransferType.equals(DictEnum.PAYTOBELONGER.code)
                                                    || capitalTransferType.equals(DictEnum.FIRSTBROKERBELONGER.code)) {
                                                error.setMsg("尾号为" + orderBusinessCode
                                                        + "的运单，未找到车老板默认银行卡，请联系企业管理员或运营平台维护后再支付。");
                                            }
                                            if (capitalTransferType.equals(DictEnum.PAYTOCAPTAIN.code)) {
                                                error.setMsg("尾号为" + orderBusinessCode
                                                        + "的运单，未找到车队长默认银行卡，请联系企业管理员或运营平台维护后再支付。");
                                            }
                                            return error;
                                        }
                                    } else {
                                        // 检测银行卡信息是否完整
                                        tBankCard = checkCardholder(bankId);
                                    }
                                    BigDecimal serviceFee = null == orderInfoByCoded.getUserConfirmServiceFee()
                                            ? BigDecimal.ZERO : orderInfoByCoded.getUserConfirmServiceFee();
                                    BigDecimal amount = orderInfoByCoded.getUserConfirmPaymentAmount().subtract(serviceFee);
                                    TCompanyProject tCompanyProject = companyProjectAPI.selectByCompanyProjectById(orderInfoByCoded.getCompanyProjectId());
                                    //此项目是否限额
                                    if(tCompanyProject.getIfQuota()){
                                        // 10万提现限额
                                        checkCardHolderWithdrawAmount(tBankCard, amount);
                                    } else {
                                        // 50万提现限额
                                        checkCardHolderWithdrawAmount50Limit(tBankCard, amount);
                                    }
                                }
                            }
                            if (contractStatus.equals(DictEnum.YZHUD.code)
                                    || contractStatus.equals(DictEnum.YZID.code)
                                    || contractStatus.equals(DictEnum.HBXXHT.code)
                                    || contractStatus.equals(DictEnum.XXHTSC.code)
                                    || contractStatus.equals(DictEnum.APPHTSC.code)) {
                                return null;
                            } else {
                                resultUtil.setCode("error");
                                resultUtil.setData(orderInfoByCoded.getOrderBusinessCode());
                                resultUtil.setMsg("运单未签署电子合同，请联系司机签署线下合同。");
                                return resultUtil;
                            }
                        } else {
                            resultUtil.setCode("error");
                            resultUtil.setData(orderInfoByCoded.getOrderBusinessCode());
                            resultUtil.setMsg("运单状态错误， 不可支付。");
                            return resultUtil;
                        }
                    } catch (Exception e) {
                        log.error("ZJJ-005:PC支付失败!", e);
                        resultUtil.setCode("error");
                        resultUtil.setData(orderInfoByCoded.getOrderBusinessCode());
                        String message = e.getMessage();
                        if (StringUtils.checkChineseCharacter(message)) {
                            resultUtil.setMsg(message);
                        } else {
                            resultUtil.setMsg("ZJJ-005:PC支付失败!");
                        }
                        return resultUtil;
                    }
                }
            } else {
                resultUtil.setCode("error");
                resultUtil.setData(orderInfoByCoded.getOrderBusinessCode());
                return resultUtil;
            }
        }
        return null;
    }


    /**
     * <AUTHOR>
     * @Description 节点支付前校验运单数据
     * @Date 2020/11/21 9:39
     * @Param
     * @return
     **/
    public ResultUtil checkNodePay(TOrderInfo orderInfoByCoded, SendOrderVO record) {
        ResultUtil resultUtil = ResultUtil.error();
        if (null != orderInfoByCoded) {
            if (null != orderInfoByCoded.getPackStatus()) {
                if (orderInfoByCoded.getPackStatus().equals("1")) {
                    resultUtil.setCode("error");
                    resultUtil.setData(orderInfoByCoded.getOrderBusinessCode());
                    resultUtil.setMsg("打包原始运单不可在此支付");
                    return resultUtil;
                } else {
                    TOrderCastChanges tOrderCastChanges = orderCastChangesService.selectByNewOne(orderInfoByCoded.getCode());
                    String capitalTransferPattern = null == tOrderCastChanges.getCapitalTransferPattern() ? "" : tOrderCastChanges.getCapitalTransferPattern();
                    try {
                        //TODO 1. 判断车辆和司机是否审核通过：线路货物关系表中
                        LineGoodsRelInfo lineGoodsRelInfo = linkGoodsRelAPI.selectLineGoodsRelInfo(new TGoodsSourceInfo().setLineGoodsRelId(orderInfoByCoded.getLineGoodsRelId()));
                        if (null != lineGoodsRelInfo && null != lineGoodsRelInfo.getTransportIdentityCheck()
                                && lineGoodsRelInfo.getTransportIdentityCheck()) {
                            String checkUserCarStatus = checkUserCarStatus(orderInfoByCoded,
                                    tOrderCastChanges.getCapitalTransferType(), capitalTransferPattern);
                            if (com.lz.common.util.StringUtils.isNotBlank(checkUserCarStatus)) {
                                resultUtil.setCode("error");
                                resultUtil.setData(orderInfoByCoded.getOrderBusinessCode());
                                resultUtil.setMsg(checkUserCarStatus);
                                return resultUtil;
                            }
                        }
                        //尾款支付判断是否签署合同
                        String contractStatus = orderInfoByCoded.getContractStatus();
                        if(record.getPayNodeType().equals(DictEnum.WKPAYNODE.code)){
                            //TODO 2。判断合同
                            if (contractStatus.equals(DictEnum.WQD.code)) {
                                resultUtil.setCode("error");
                                resultUtil.setData(orderInfoByCoded.getOrderBusinessCode());
                                resultUtil.setMsg("运单未签署电子合同，请联系司机签署线下合同。");
                                return resultUtil;
                            }
                            //TODO 4. 获取最新资金变动表
                            //尾款支付判断
                            ResultUtil enduserIdByCapitalTransferType = getEnduserIdByCapitalTransferType(tOrderCastChanges.getCapitalTransferType(), orderInfoByCoded);
                            if (!DictEnum.SUCCESS.code.equals(enduserIdByCapitalTransferType.getCode())) {
                                enduserIdByCapitalTransferType.setData(orderInfoByCoded.getOrderBusinessCode());
                                return enduserIdByCapitalTransferType;
                            }

                            if (contractStatus.equals(DictEnum.YZHUD.code)
                                    || contractStatus.equals(DictEnum.YZID.code)
                                    || contractStatus.equals(DictEnum.HBXXHT.code)
                                    || contractStatus.equals(DictEnum.XXHTSC.code)
                                    || contractStatus.equals(DictEnum.APPHTSC.code)) {
                                return null;
                            } else {
                                resultUtil.setCode("error");
                                resultUtil.setData(orderInfoByCoded.getOrderBusinessCode());
                                resultUtil.setMsg("运单未签署电子合同，请联系司机签署线下合同。");
                                return resultUtil;
                            }
                        }
                        // comment 判断车主、经纪人钱包是否存在、子账号、uid
                        if (null != capitalTransferPattern && StringUtils.isNotBlank(capitalTransferPattern)) {
                            ResultUtil walletExist = getWalletExist(tOrderCastChanges.getCapitalTransferType(), capitalTransferPattern, orderInfoByCoded);
                            if (null != walletExist) {
                                return walletExist;
                            }
                        }
                    } catch (Exception e) {
                        log.error("节点支付失败!", e);
                        resultUtil.setCode("error");
                        resultUtil.setData(orderInfoByCoded.getOrderBusinessCode());
                        String message = e.getMessage();
                        if (StringUtils.checkChineseCharacter(message)) {
                            resultUtil.setMsg(message);
                        } else {
                            resultUtil.setMsg("节点支付失败!");
                        }
                        return resultUtil;
                    }
                }
            } else {
                resultUtil.setCode("error");
                resultUtil.setData(orderInfoByCoded.getOrderBusinessCode());
                return resultUtil;
            }
        }
        return null;
    }


    /**
     * @return 12:00 下午
     * <AUTHOR>
     * @Description 检测司机车辆认证状态
     * @Date 2020/3/3 12:00 下午
     * @Param
     **/
    public String checkUserCarStatus(TOrderInfo orderInfo, String capitalTransferType, String capitalTransferPattern) {
        String msgPre = "支付请求失败: ";
        // 查询司机、车队长、车辆认证状态
        EnduserCarStatus enduserCarStatus = queryEnduserCarStatus(orderInfo, capitalTransferType, capitalTransferPattern);
        // 检测司机、车队长、车辆认证状态
        String checkResult = doCheckEnduserCarStatus(enduserCarStatus, orderInfo.getId());
        if (checkResult.length() > 0) {
            return msgPre + checkResult;
        }
        return null;
    }

    /**
     * 查询司机、车队长、车辆认证状态
     * @param orderInfo
     * @param capitalTransferType
     * @param capitalTransferPattern
     * @return
     */
    private EnduserCarStatus queryEnduserCarStatus(TOrderInfo orderInfo, String capitalTransferType, String capitalTransferPattern) {
        CarDriverRelVO carDriverRelVO = new CarDriverRelVO();
        carDriverRelVO.setEndcarId(orderInfo.getVehicleId());
        carDriverRelVO.setEndDriverId(orderInfo.getEndDriverId());
        if (DictEnum.PAYTOCAPTAIN.code.equals(capitalTransferType)) {
            if (null != orderInfo.getEndCarOwnerId()) {
                carDriverRelVO.setEndCarOwnerId(orderInfo.getEndCarOwnerId());
            }
        }
        if (DictEnum.MANAGERPATTERN.code.equals(capitalTransferPattern)) {
            if (null != orderInfo.getAgentId()) {
                carDriverRelVO.setAgentId(orderInfo.getAgentId());
            }
        }
        ResultUtil endcarAndUserAuditStatus = appCommonAPI.selectEndcarAndUserAuditStatus(carDriverRelVO);
        LinkedHashMap data = (LinkedHashMap) endcarAndUserAuditStatus.getData();
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.convertValue(data, EnduserCarStatus.class);
    }

    /**
     * 检测司机、车队长、车辆认证状态
     * @param enduserCarStatus
     * @param orderId
     * @return
     */
    private String doCheckEnduserCarStatus(EnduserCarStatus enduserCarStatus, Integer orderId) {
        StringBuffer message = new StringBuffer();
        boolean driverAuditStatus = true;
        boolean vehicleAuditStatus = true;
        // 司机审核状态
        if (!DictEnum.PASSNODE.code.equals(enduserCarStatus.getUserAuditStatus())) {
            driverAuditStatus = false;
            // 查询运单信息详情
            TOrderInfoDetail orderInfoDetail = orderInfoDetailMapper.selectByOrderId(orderId);
            if (null != orderInfoDetail && DictEnum.PASSNODE.code.equals(orderInfoDetail.getDriverAuditStatus())) {
                driverAuditStatus = true;
            }
        }
        if (!driverAuditStatus) {
            message.append(enduserCarStatus.getRealName() + ":" + "认证未通过" + ".");
        }
        if (!DictEnum.PASSNODE.code.equals(enduserCarStatus.getCarAuditStatus())) {
            vehicleAuditStatus = false;
            // 查询运单信息详情
            TOrderInfoDetail orderInfoDetail = orderInfoDetailMapper.selectByOrderId(orderId);
            if (null != orderInfoDetail && DictEnum.PASSNODE.code.equals(orderInfoDetail.getCarAuditStatus())) {
                vehicleAuditStatus = true;
            }
        }       // 车辆审核状态
        if (!vehicleAuditStatus) {
            message.append(enduserCarStatus.getVehicleNumber() + ":" + "认证未通过" + ".");
        }

        if (null != enduserCarStatus.getEndCarOwnerAuditStatus()) {
            if (!DictEnum.PASSNODE.code.equals(enduserCarStatus.getEndCarOwnerAuditStatus())) {
                if (null == enduserCarStatus.getEndCarOwnerName() || StringUtils.isBlank(enduserCarStatus.getEndCarOwnerName())) {
                    enduserCarStatus.setEndCarOwnerName("车队长");
                }
                message.append(enduserCarStatus.getEndCarOwnerName() + ":" + "认证未通过" + ".");
            }
        }
        if (null != enduserCarStatus.getAgentAuditStatus()) {
            if (!DictEnum.PASSNODE.code.equals(enduserCarStatus.getAgentAuditStatus())) {
                message.append(enduserCarStatus.getAgentName() + ":" + "认证未通过" + ".");
            }
        }
        return message.toString();
    }

    /**
     * @return 12:00 下午
     * <AUTHOR>
     * @Description 检测司机车辆认证状态
     * @Date 2020/3/3 12:00 下午
     * @Param
     **/
    public String receiveOrderCheckUserCarStatus(TOrderInfo orderInfo, String capitalTransferType, String capitalTransferPattern) {
        String msgPre = "收单请求失败: ";
        // 查询司机、车队长、车辆认证状态
        EnduserCarStatus enduserCarStatus = queryEnduserCarStatus(orderInfo, capitalTransferType, capitalTransferPattern);
        // 检测司机、车队长、车辆认证状态
        String check = doCheckEnduserCarStatus(enduserCarStatus, orderInfo.getId());
        if (check.length() > 0) {
            return msgPre + check;
        }
        return null;
    }

    /**
     *
     *
     * @return 9:49 上午
     * <AUTHOR>
     * @Description  检查运单C端用户id
     * @Date 2020/2/18 9:49 上午
     * @Param
     **/
    private ResultUtil getEnduserIdByCapitalTransferType(String capitalTransferType, TOrderInfo orderInfo) {
        Integer enduserId;
        if (capitalTransferType.equals(DictEnum.PAYTODRIVER.code)) {
            enduserId = orderInfo.getEndDriverId();
        } else if (capitalTransferType.equals(DictEnum.FIRSTBROKERDRIVER.code)) {
            if (null == orderInfo.getEndAgentId()) {
                ResultUtil error = ResultUtil.error();
                ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                exceptionSendOrder.setCount(1);
                exceptionSendOrder.setIsLogic(false);
                HashMap<String, Object> result = new HashMap<>();
                result.put("exceptionSendOrder", exceptionSendOrder);
                error.setMsg("尾号为" + orderInfo.getOrderBusinessCode().substring(orderInfo.getOrderBusinessCode().length() - 8) + "的运单， 缺少业务部数据,请在运单检查中选择业务部");
                //error.setMsg("缺少经纪人数据,请在运单检查中选择经纪人");
                error.setData(result);
                return error;
            }
            enduserId = orderInfo.getEndDriverId();
        } else if (capitalTransferType.equals(DictEnum.PAYTOBELONGER.code)) {
            if (null == orderInfo.getEndCarOwnerId()) {
                ResultUtil error = ResultUtil.error();
                ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                exceptionSendOrder.setCount(1);
                exceptionSendOrder.setIsLogic(false);
                HashMap<String, Object> result = new HashMap<>();
                result.put("exceptionSendOrder", exceptionSendOrder);
                error.setMsg("尾号为" + orderInfo.getOrderBusinessCode().substring(orderInfo.getOrderBusinessCode().length() - 8) + " 缺少车老板数据,请在运单检查中选择车老板");
                //error.setMsg("缺少车老板数据,请在运单检查中选择车老板");
                error.setData(result);
                return error;
            }
            enduserId = orderInfo.getEndCarOwnerId();
        }else if (capitalTransferType.equals(DictEnum.PAYTOCAPTAIN.code)) {
            if (null == orderInfo.getEndCarOwnerId()) {
                ResultUtil error = ResultUtil.error();
                ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                exceptionSendOrder.setCount(1);
                exceptionSendOrder.setIsLogic(false);
                HashMap<String, Object> result = new HashMap<>();
                result.put("exceptionSendOrder", exceptionSendOrder);
                error.setMsg("尾号为" + orderInfo.getOrderBusinessCode().substring(orderInfo.getOrderBusinessCode().length() - 8) + " 缺少车队长数据,请在运单检查中选择车队长");
                //error.setMsg("缺少车老板数据,请在运单检查中选择车老板");
                error.setData(result);
                return error;
            }
            enduserId = orderInfo.getEndCarOwnerId();
        } else if (capitalTransferType.equals(DictEnum.FIRSTBROKERBELONGER.code)) {
            if (null == orderInfo.getEndCarOwnerId()) {
                ResultUtil error = ResultUtil.error();
                ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                exceptionSendOrder.setCount(1);
                exceptionSendOrder.setIsLogic(false);
                HashMap<String, Object> result = new HashMap<>();
                result.put("exceptionSendOrder", exceptionSendOrder);
                error.setMsg("尾号为" + orderInfo.getOrderBusinessCode().substring(orderInfo.getOrderBusinessCode().length() - 8) + " 缺少车老板数据,请在运单检查中选择车老板");
                //error.setMsg("缺少车老板数据,请在运单检查中选择车老板");
                error.setData(result);
                return error;
            }
            if (null == orderInfo.getEndAgentId()) {
                ResultUtil error = ResultUtil.error();
                ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
                exceptionSendOrder.setCount(1);
                exceptionSendOrder.setIsLogic(false);
                HashMap<String, Object> result = new HashMap<>();
                result.put("exceptionSendOrder", exceptionSendOrder);
                error.setMsg("尾号为" + orderInfo.getOrderBusinessCode().substring(orderInfo.getOrderBusinessCode().length() - 8) + "的运单， 缺少业务部数据,请在运单检查中选择业务部");
                //error.setMsg("缺少车老板数据,请在运单检查中选择经纪人");
                error.setData(result);
                return error;
            }
            enduserId = orderInfo.getEndCarOwnerId();
        } else {
            ResultUtil error = ResultUtil.error();
            ExceptionSendOrder exceptionSendOrder = new ExceptionSendOrder();
            exceptionSendOrder.setCount(1);
            exceptionSendOrder.setIsLogic(false);
            HashMap<String, Object> result = new HashMap<>();
            result.put("exceptionSendOrder", exceptionSendOrder);
            error.setMsg("尾号为" + orderInfo.getOrderBusinessCode().substring(orderInfo.getOrderBusinessCode().length() - 8) + " 资金转移方式未找到");
            //error.setMsg("资金转移方式未找到");
            error.setData(result);
            return error;
        }
        return ResultUtil.ok(enduserId);
    }

    /**
     * @return 3:35 下午
     * <AUTHOR>
     * @Description 判断钱包是否存在
     * @Date 2020/3/11 3:35 下午
     * @Param
     **/
    ResultUtil getWalletExist(String capitalTransferType, String capitalTransferPattern, TOrderInfo orderInfo) {
        ResultUtil resultUtil = ResultUtil.error();
        WalletDTO walletDTO;
        if (null != orderInfo.getEndDriverId()) {
            walletDTO = walletService.selectWalletByEnduserCompanyId(orderInfo.getCarrierId(), orderInfo.getEndDriverId(),
                    DictEnum.CD.code, DictEnum.CDRIVER.code);
            if (null == walletDTO || null == walletDTO.getWalletId() || null == walletDTO.getThridParySubAccount()
                    || StringUtils.isBlank(walletDTO.getThridParySubAccount()) || null == walletDTO.getUid()
                    || StringUtils.isBlank(walletDTO.getUid())) {
                log.error("司机钱包数据异常");
                resultUtil.setMsg("错误码：ZYH102，请联系运营平台予以解决");
                resultUtil.setData(orderInfo.getOrderBusinessCode());
                return resultUtil;
            }
        }
        if (capitalTransferType.equals(DictEnum.PAYTOBELONGER.code)) {
            if (null == orderInfo.getEndCarOwnerId()) {
                resultUtil.setMsg("缺少车主信息");
                resultUtil.setData(orderInfo.getOrderBusinessCode());
                return resultUtil;
            }
            walletDTO = walletService.selectWalletByEnduserCompanyId(orderInfo.getCarrierId(), orderInfo.getEndCarOwnerId(),
                    DictEnum.CD.code, DictEnum.CCARBOSS.code);
            if (null == walletDTO.getWalletId() || null == walletDTO.getThridParySubAccount()
                    || StringUtils.isBlank(walletDTO.getThridParySubAccount()) || null == walletDTO.getUid()
                    || StringUtils.isBlank(walletDTO.getUid())) {
                log.error("车主钱包数据异常");
                resultUtil.setMsg("错误码：ZYH102，请联系运营平台予以解决");
                resultUtil.setData(orderInfo.getOrderBusinessCode());
                return resultUtil;
            }
        }
        if (capitalTransferPattern.equals(DictEnum.MANAGERPATTERN.code)) {
            if (orderInfo.getUserConfirmPaymentAmount().compareTo(orderInfo.getUserConfirmServiceFee()) > 0
                    && orderInfo.getUserConfirmServiceFee().compareTo(BigDecimal.ZERO) > 0) {
                if (null == orderInfo.getAgentId()) {
                    resultUtil.setMsg("缺少经纪人信息");
                    resultUtil.setData(orderInfo.getOrderBusinessCode());
                    return resultUtil;
                }
                walletDTO = walletService.selectWalletByEnduserCompanyId(orderInfo.getCarrierId(), orderInfo.getAgentId(),
                        DictEnum.CD.code, DictEnum.CMANAGER.code);
                if (null == walletDTO.getWalletId() || null == walletDTO.getThridParySubAccount()
                        || StringUtils.isBlank(walletDTO.getThridParySubAccount()) || null == walletDTO.getUid()
                        || StringUtils.isBlank(walletDTO.getUid())) {
                    log.error("经纪人钱包数据异常");
                    resultUtil.setMsg("错误码：ZYH102，请联系运营平台予以解决");
                    resultUtil.setData(orderInfo.getOrderBusinessCode());
                    return resultUtil;
                }
            }
        }

        return null;
    }

    /**
    * @description 检测银行卡
    * <AUTHOR>
    * @date 21/6/2021 09:39
    * @param
    * @return
    */
    public TBankCard checkCardholder(Integer bankId) {
        try {
            TBankCard bankCard = bankCardAPI.selectById(bankId);
            if (null != bankCard) {
                if (null == bankCard.getCardNo() || null == bankCard.getCardOwner()
                        || StringUtils.isBlank(bankCard.getCardNo()) || StringUtils.isBlank(bankCard.getCardOwner())) {
                    throw new RuntimeException("持卡人信息不完整，请先完善信息");
                }
                if (null == bankCard.getCardOwnerIdcard() || StringUtils.isBlank(bankCard.getCardOwnerIdcard())) {
                    throw new RuntimeException("持卡人未绑定身份证，请先绑定身份证");
                }
                return bankCard;
            } else {
                throw new RuntimeException("未找到银行卡");
            }
        } catch (Exception e) {
            log.info("检测持卡人失败, {}", e);
            e.printStackTrace();
            if (null != e.getMessage() && StringUtils.checkChineseCharacter(e.getMessage())) {
                throw new RuntimeException(e.getMessage());
            } else {
                throw new RuntimeException("申请失败");
            }
        }
    }

    /**
     * @return 11:59 上午
     * <AUTHOR>
     * @Description 是否提现超额
     * @Date 2020/3/3 11:59 上午
     * @Param
     **/
    public boolean checkCardHolderWithdrawAmount(TBankCard bankCard, @NotNull BigDecimal amount) {
        try {
            SysParam sysParam = sysParamAPI.getParamByKey("Amount");
            // 查询当月提现金额
            TBankcardMonthlyVo vo = new TBankcardMonthlyVo();
            vo.setCardOwnerIdcard(bankCard.getCardOwnerIdcard());
            vo.setAmount(amount);
            vo.setTxAmount(new BigDecimal(sysParam.getParamValue()));
            Boolean result = bankcardMonthlyAPI.selectByBankAmount(vo);
            if (!result) {
                log.info("系统配置提现总额{}", sysParam.getParamValue());
                String msg = "根据金融及税务政策要求，该收款人当月提现总额已超过（amount元），请更换其他收款人银行卡!";
                msg = msg.replace("amount", sysParam.getParamValue());
                throw new RuntimeException(msg);
            }
        } catch (Exception e) {
            log.info("{}", e);
            e.printStackTrace();
            if (null != e.getMessage() && StringUtils.checkChineseCharacter(e.getMessage())) {
                throw new RuntimeException(e.getMessage());
            } else {
                throw new RuntimeException("申请失败");
            }
        }

        return true;
    }

    /**
    * @description 50万提现限额
    * <AUTHOR>
    * @date 21/6/2021 15:08
    * @param
    * @return
    */
    public boolean checkCardHolderWithdrawAmount50Limit(TBankCard bankCard, BigDecimal amount) {
        try {
            SysParam sysParam = sysParamAPI.getParamByKey("50LimitAmount");
            // 查询当月提现金额
            TBankcardMonthlyVo vo = new TBankcardMonthlyVo();
            vo.setCardId(bankCard.getId());
            vo.setAmount(amount);
            vo.setTxAmount(new BigDecimal(sysParam.getParamValue()));
            Boolean result = bankcardMonthlyAPI.selectByBankAmount(vo);
            if (!result) {
                log.info("系统配置提现总额{}", sysParam.getParamValue());
                String msg = "根据金融及税务政策要求，该银行卡已达到本月提现金额上限（amount元），请更换银行卡后，重新发起!";
                msg = msg.replace("amount", sysParam.getParamValue());
                throw new RuntimeException(msg);
            }
        } catch (Exception e) {
            log.info("{}", e);
            e.printStackTrace();
            if (null != e.getMessage() && StringUtils.checkChineseCharacter(e.getMessage())) {
                throw new RuntimeException(e.getMessage());
            } else {
                throw new RuntimeException("申请失败");
            }
        }

        return true;
    }

    /**
     * @return 4:09 下午
     * <AUTHOR>
     * @Description 区分司机、和车主
     * @Date 2020/4/29 4:09 下午
     * @Param
     **/
    public Map<String, List<Integer>> differentiateDriverAndCarOwner(List<EndUserDTO> enduserByBankId) {
        Map<String, List<Integer>> endusers = new HashMap<>();
        List<Integer> drivers = new ArrayList<>();
        List<Integer> carOwners = new ArrayList<>();
        for (EndUserDTO endUserDTO : enduserByBankId) {
            if (endUserDTO.getUserLogisticsRole().contains(DictEnum.CTYPEDRVIVER.code)) {
                drivers.add(endUserDTO.getEnduserId());
            }
            if (endUserDTO.getUserLogisticsRole().contains(DictEnum.CTYPEBOSS.code)) {
                carOwners.add(endUserDTO.getEnduserId());
            }
        }
        if (!drivers.isEmpty()) {
            endusers.put("drivers", drivers);
        }
        if (!carOwners.isEmpty()) {
            endusers.put("carOwners", carOwners);
        }
        return endusers;
    }


    public BigDecimal calulateServiceFee(String code, BigDecimal userConfirmCarriagePayment, Double settledWeight) {
        TOrderCastChanges tOrderCastChanges = orderCastChangesService.selectByNewOne(code);
        if (null != tOrderCastChanges && null != tOrderCastChanges.getShareMethod()
                && DictEnum.FIXEDPROPORTION.code.equals(tOrderCastChanges.getShareMethod())) {
            if (null != tOrderCastChanges.getShareValue() && tOrderCastChanges.getShareValue().compareTo(BigDecimal.ZERO) > 0) {
                if (null != userConfirmCarriagePayment
                        && userConfirmCarriagePayment.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal serviceFee = userConfirmCarriagePayment.setScale(2, RoundingMode.HALF_UP).multiply(tOrderCastChanges.getShareValue());
                    serviceFee = serviceFee.setScale(2, RoundingMode.HALF_UP);
                    return serviceFee;
                } else {
                    return BigDecimal.ZERO;
                }
            }
        } else if (null != tOrderCastChanges && null != tOrderCastChanges.getShareMethod() && DictEnum.SETTLEMENTWEIGHT.code.equals(tOrderCastChanges.getShareMethod())) {
            if (null != tOrderCastChanges.getShareValue() && tOrderCastChanges.getShareValue().compareTo(BigDecimal.ZERO) > 0) {
                if (null != settledWeight && settledWeight > 0) {
                    return BigDecimal.valueOf(settledWeight)
                            .multiply(tOrderCastChanges.getShareValue())
                            .setScale(2, RoundingMode.HALF_UP);
                } else {
                    return BigDecimal.ZERO;
                }
            }
        } else if (null != tOrderCastChanges && null != tOrderCastChanges.getShareMethod() && DictEnum.FIXEDLIMIT.code.equals(tOrderCastChanges.getShareMethod())) {
            if (null != tOrderCastChanges.getShareValue() && tOrderCastChanges.getShareValue().compareTo(BigDecimal.ZERO) > 0) {
                return tOrderCastChanges.getShareValue();
            } else {
                return BigDecimal.ZERO;
            }
        }
        return null;
    }

    public BigDecimal calulateServiceFee(CompanySourceDTO companySourceDTO, BigDecimal userConfirmCarriagePayment, Double settledWeight) {

        TOrderCastChanges tOrderCastChanges = new TOrderCastChanges();
        tOrderCastChanges.setShareMethod(companySourceDTO.getShareMethod());
        tOrderCastChanges.setShareValue(companySourceDTO.getShareValue());

        if (null != tOrderCastChanges.getShareMethod() && DictEnum.FIXEDPROPORTION.code.equals(tOrderCastChanges.getShareMethod())) {
            if (null != tOrderCastChanges.getShareValue() && tOrderCastChanges.getShareValue().compareTo(BigDecimal.ZERO) > 0) {
                if (null != userConfirmCarriagePayment
                        && userConfirmCarriagePayment.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal serviceFee = userConfirmCarriagePayment.setScale(2, RoundingMode.HALF_UP).multiply(tOrderCastChanges.getShareValue());
                    serviceFee = serviceFee.setScale(2, RoundingMode.HALF_UP);
                    return serviceFee;
                } else {
                    return BigDecimal.ZERO;
                }
            }
        } else if (null != tOrderCastChanges.getShareMethod() && DictEnum.SETTLEMENTWEIGHT.code.equals(tOrderCastChanges.getShareMethod())) {
            if (null != tOrderCastChanges.getShareValue() && tOrderCastChanges.getShareValue().compareTo(BigDecimal.ZERO) > 0) {
                if (null != settledWeight && settledWeight > 0) {
                    return BigDecimal.valueOf(settledWeight)
                            .multiply(tOrderCastChanges.getShareValue())
                            .setScale(2, RoundingMode.HALF_UP);
                } else {
                    return BigDecimal.ZERO;
                }
            }
        } else if (null != tOrderCastChanges.getShareMethod() && DictEnum.FIXEDLIMIT.code.equals(tOrderCastChanges.getShareMethod())) {
            if (null != tOrderCastChanges.getShareValue() && tOrderCastChanges.getShareValue().compareTo(BigDecimal.ZERO) > 0) {
                return tOrderCastChanges.getShareValue();
            } else {
                return BigDecimal.ZERO;
            }
        }
        return null;
    }

    /**
     * @return
     * <AUTHOR>
     * @Description 查询C端默认银行卡
     * @Date 2020/8/3 上午11:51
     * @Param
     **/
    public TBankCard selectDefaultBankCard(Integer enduserId) {
        try {
            ResultUtil bankCards = bankCardAPI.selectBankCards(enduserId);
            if (bankCards.getCode().equals("success")) {
                List<LinkedHashMap> bankCardsData = (List<LinkedHashMap>) bankCards.getData();
                if (null != bankCardsData || bankCardsData.size() > 0) {
                    for (LinkedHashMap linkedHashMap : bankCardsData) {
                        Object ifDefault = linkedHashMap.get("ifDefault");
                        if (null != ifDefault && String.valueOf(ifDefault).equals("1")) {
                            if (null != linkedHashMap.get("id")) {
                                TBankCard bankCard = new TBankCard();
                                Integer id = (Integer) linkedHashMap.get("id");
                                String cardNo = "";
                                String cardOwner = "";
                                String cardOwnerPhone = "";
                                String cardOwnerIdcard = "";
                                bankCard.setId(id);
                                if (null != linkedHashMap.get("cardNo")) {
                                    cardNo = String.valueOf(linkedHashMap.get("cardNo"));
                                    bankCard.setCardNo(cardNo);
                                }
                                if (null != linkedHashMap.get("cardOwner")) {
                                    cardOwner = String.valueOf(linkedHashMap.get("cardOwner"));
                                    bankCard.setCardOwner(cardOwner);
                                }
                                if (null != linkedHashMap.get("cardOwnerPhone")) {
                                    cardOwnerPhone = String.valueOf(linkedHashMap.get("cardOwnerPhone"));
                                    bankCard.setCardOwnerPhone(cardOwnerPhone);
                                }
                                if (null != linkedHashMap.get("cardOwnerIdcard")) {
                                    cardOwnerIdcard = String.valueOf(linkedHashMap.get("cardOwnerIdcard"));
                                    bankCard.setCardOwnerIdcard(cardOwnerIdcard);
                                }
                                bankCard.setIfDefault(1);
                                if (null == linkedHashMap.get("cardNo")
                                        || null == linkedHashMap.get("cardOwner")
                                        || null == linkedHashMap.get("cardOwnerPhone")
                                        || StringUtils.isBlank(linkedHashMap.get("cardNo").toString())
                                        || StringUtils.isBlank(linkedHashMap.get("cardOwner").toString())
                                        || StringUtils.isBlank(linkedHashMap.get("cardOwnerPhone").toString())) {
                                    throw new RuntimeException("持卡人信息不完整，请先完善信息");
                                }
                                if (null == linkedHashMap.get("cardOwnerIdcard") ||  StringUtils.isBlank(linkedHashMap.get("cardOwnerIdcard").toString())) {
                                    throw new RuntimeException("持卡人未绑定身份证，请先绑定身份证");
                                }
                                return bankCard;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.info("查询银行卡失败, {}", e);
            e.printStackTrace();
            if (null != e.getMessage() && StringUtils.checkChineseCharacter(e.getMessage())) {
                throw new RuntimeException(e.getMessage());
            }
            throw new RuntimeException("查询银行卡失败");
        }

        return null;
    }

    /**
     * @return
     * <AUTHOR>
     * @Description 保存提现金额
     * @Date 2020/8/3 下午3:02
     * @Param
     **/
    public void saveBankCardMonthlyAmount(Integer enduserId, String code, BigDecimal amount) {
        try {
            // 查询默认银行卡
            TBankCard bankCard = selectDefaultBankCard(enduserId);
            if (null != bankCard) {
                // 保存提现金额
                TBankcardMonthlyVo bankcardMonthlyVo = new TBankcardMonthlyVo();
                bankcardMonthlyVo.setAmount(amount);
                bankcardMonthlyVo.setCardId(bankCard.getId());
                bankcardMonthlyVo.setCardNo(bankCard.getCardNo());
                bankcardMonthlyVo.setCardOwnerIdcard(bankCard.getCardOwnerIdcard());
                bankcardMonthlyVo.setCardOwner(bankCard.getCardOwner());
                bankcardMonthlyVo.setCurrentTime(new Date());
                bankcardMonthlyVo.setCreateUser(CurrentUser.getUserNickname());
                bankcardMonthlyVo.setCreateTime(new Date());
                bankcardMonthlyVo.setUpdateUser(CurrentUser.getUserNickname());
                bankcardMonthlyVo.setUpdateTime(new Date());
                bankcardMonthlyVo.setEnable(false);
                bankcardMonthlyVo.setParam1(code);
                log.info("记录提现金额，{}", JSONUtil.toJsonStr(bankcardMonthlyVo));
                orderBankcardMonthlyMapper.insertSelective(bankcardMonthlyVo);
            } else {

            }

        } catch (Exception e) {
            log.info("记录提现金额失败,{}", e);
            e.printStackTrace();
        }

    }

    /**
     * @return
     * <AUTHOR>
     * @Description 保存提现金额, 已选银行卡
     * @Date 2020/8/4 下午6:20
     * @Param
     **/
    public void saveBankCardMonthlyAmountByBankId(Integer bankId, String code, BigDecimal amount) {
        try {
            // 查询默认银行卡
            TBankCard bankCard = bankCardAPI.selectById(bankId);
            if (null != bankCard) {
                // 保存提现金额
                TBankcardMonthlyVo bankcardMonthlyVo = new TBankcardMonthlyVo();
                bankcardMonthlyVo.setAmount(amount);
                bankcardMonthlyVo.setCardId(bankCard.getId());
                bankcardMonthlyVo.setCardNo(bankCard.getCardNo());
                bankcardMonthlyVo.setCardOwnerIdcard(bankCard.getCardOwnerIdcard());
                bankcardMonthlyVo.setCardOwner(bankCard.getCardOwner());
                bankcardMonthlyVo.setCurrentTime(new Date());
                bankcardMonthlyVo.setCreateUser(CurrentUser.getUserNickname());
                bankcardMonthlyVo.setCreateTime(new Date());
                bankcardMonthlyVo.setUpdateUser(CurrentUser.getUserNickname());
                bankcardMonthlyVo.setUpdateTime(new Date());
                bankcardMonthlyVo.setEnable(false);
                bankcardMonthlyVo.setParam1(code);
                log.info("记录提现金额，{}", JSONUtil.toJsonStr(bankcardMonthlyVo));
                orderBankcardMonthlyMapper.insertSelective(bankcardMonthlyVo);
            } else {

            }

        } catch (Exception e) {
            log.info("记录提现金额失败,{}", e);
            e.printStackTrace();
        }

    }

    public void saveBankCardMonthlyAmountAndCreateUserAndBankId(Integer bankId, String code, BigDecimal amount, String createUser) {
        try {
            // 查询银行卡
            TBankCard bankCard = bankCardAPI.selectById(bankId);
            if (null != bankCard) {
                // 保存提现金额
                TBankcardMonthlyVo bankcardMonthlyVo = new TBankcardMonthlyVo();
                bankcardMonthlyVo.setAmount(amount);
                bankcardMonthlyVo.setCardId(bankCard.getId());
                bankcardMonthlyVo.setCardNo(bankCard.getCardNo());
                bankcardMonthlyVo.setCardOwnerIdcard(bankCard.getCardOwnerIdcard());
                bankcardMonthlyVo.setCardOwner(bankCard.getCardOwner());
                bankcardMonthlyVo.setCurrentTime(new Date());
                bankcardMonthlyVo.setCreateUser(createUser);
                bankcardMonthlyVo.setCreateTime(new Date());
                bankcardMonthlyVo.setUpdateUser(createUser);
                bankcardMonthlyVo.setUpdateTime(new Date());
                bankcardMonthlyVo.setEnable(false);
                bankcardMonthlyVo.setParam1(code);
                log.info("记录提现金额，{}", JSONUtil.toJsonStr(bankcardMonthlyVo));
                orderBankcardMonthlyMapper.insertSelective(bankcardMonthlyVo);
            } else {

            }

        } catch (Exception e) {
            log.info("记录提现金额失败,{}", e);
            e.printStackTrace();
        }

    }

    /**
     * @return
     * <AUTHOR>
     * @Description 判断打包运单司机、车主、车辆审核状态
     * @Date 2020/9/1 4:53 下午
     * @Param
     **/
    public ResultUtil checkPackOrderUserCarStatus(String code, String capitalTransferType, String capitalTransferPattern) {
        List<OrderPackDTO> orderPackDTOS = orderInfoMapper.selectDriverOnwerCarStatusByPackCode(code);
        HashSet<OrderPackDTO> driverHashSet = new HashSet<>();
        HashSet<OrderPackDTO> ownerHashSet = new HashSet<>();
        HashSet<OrderPackDTO> carHashSet = new HashSet<>();
        for (OrderPackDTO dto : orderPackDTOS) {
            if (!DictEnum.PASSNODE.code.equals(dto.getDriverStatus())) {
                OrderPackDTO orderPackDTO = new OrderPackDTO();
                orderPackDTO.setEndDriverId(dto.getEndDriverId());
                orderPackDTO.setRealName(dto.getRealName());
                orderPackDTO.setPhone(dto.getPhone());
                driverHashSet.add(orderPackDTO);
            }
            if (!DictEnum.PASSNODE.code.equals(dto.getCarStatus())) {
                OrderPackDTO carDTO = new OrderPackDTO();
                carDTO.setVehicleNumber(dto.getVehicleNumber());
                carHashSet.add(carDTO);
            }
            if (DictEnum.PAYTOBELONGER.code.equals(capitalTransferType)) {
                if (!DictEnum.PASSNODE.code.equals(dto.getCarOwnerStatus())) {
                    OrderPackDTO ownerDTO = new OrderPackDTO();
                    ownerDTO.setEndCarOwnerId(dto.getEndCarOwnerId());
                    ownerDTO.setCarOwnerName(dto.getCarOwnerName());
                    ownerDTO.setCarOwnerPhone(dto.getCarOwnerPhone());
                    ownerHashSet.add(ownerDTO);
                }
            }
        }
        StringBuffer driverMsg = new StringBuffer();
        StringBuffer carMsg = new StringBuffer();
        StringBuffer ownerMsg = new StringBuffer();
        if (!driverHashSet.isEmpty()) {
            for (OrderPackDTO dto : driverHashSet) {
                if (driverMsg.length() != 0) {
                    driverMsg.append("、");
                }
                driverMsg.append(dto.getRealName()).append(dto.getPhone());
            }
        }
        if (!carHashSet.isEmpty()) {
            for (OrderPackDTO dto : carHashSet) {
                if (carMsg.length() != 0) {
                    carMsg.append("、");
                }
                carMsg.append(dto.getVehicleNumber());
            }
        }
        if (!ownerHashSet.isEmpty()) {
            for (OrderPackDTO dto : ownerHashSet) {
                if (ownerMsg.length() != 0) {
                    ownerMsg.append("、");
                }
                ownerMsg.append(dto.getCarOwnerName()).append(dto.getCarOwnerPhone());
            }
        }
        HashMap<String, String> msg = new HashMap<>();
        if (driverMsg.length() > 0) {
            msg.put("driver", driverMsg.append("认证不通过").toString());
        }
        if (carMsg.length() > 0) {
            msg.put("car", carMsg.append("认证不通过").toString());
        }
        if (ownerMsg.length() > 0) {
            msg.put("owner", ownerMsg.append("认证不通过").toString());
        }
        if (!msg.isEmpty()) {
            msg.put("status", "true");
            return ResultUtil.error(JSONUtil.toJsonStr(msg));
        }

        return null;
    }

    /**
     * @description 检测预付款运单
     * <AUTHOR>
     * @date 2021/11/5 16:57
     */
    public HashMap<String, Object> checkPrePayment(TAdvanceOrderTmpExcel advanceOrderTmpExcel) {
        String orderBusinessCode = advanceOrderTmpExcel.getOrderBusinessCode();
        HashMap<String, Object> result = new HashMap<>();
        result.put("orderCode", orderBusinessCode);

        TOrderInfoVO tOrderInfoVO2 = orderInfoMapper.selectOrderInfoForAdvanceByOrderBusinessCode(advanceOrderTmpExcel.getOrderBusinessCode());

        if (null == tOrderInfoVO2 || tOrderInfoVO2.getOrderExecuteStatus().equals(DictEnum.M20.code)
                || tOrderInfoVO2.getOrderExecuteStatus().equals(DictEnum.M10.code)) {
            log.info("运单不存在");
            result.put("code", "error");
            result.put("msg", "运单不存在");
            return result;
        }
        if (tOrderInfoVO2.getPayMethod().equals(DictEnum.NODEPAYPROPORTION.code)
                || tOrderInfoVO2.getPayMethod().equals(DictEnum.NODEPAYFIXED.code)) {
            log.info("预支付不支持节点支付模式运单");
            result.put("code", "error");
            result.put("msg", "预支付不支持节点支付模式运单");
            return result;
        }
        if (null != tOrderInfoVO2.getOrderPayStatus()) {
            if (tOrderInfoVO2.getOrderPayStatus().equals(DictEnum.M090.code)
                    || tOrderInfoVO2.getOrderPayStatus().equals(DictEnum.P070.code)) {
                result.put("code", "error");
                result.put("msg", "运单已支付");
                return result;
            }
            if (tOrderInfoVO2.getOrderPayStatus().equals(DictEnum.M120.code)
                    || tOrderInfoVO2.getOrderPayStatus().equals(DictEnum.P110.code)
                    || tOrderInfoVO2.getOrderPayStatus().equals(DictEnum.M130.code)) {
                result.put("code", "error");
                result.put("msg", "运单已提现");
                return result;
            }
        }
        if (tOrderInfoVO2.getOrderPayStatus().equals(DictEnum.M090.code)
                || tOrderInfoVO2.getOrderPayStatus().equals(DictEnum.P070.code)) {
            result.put("code", "error");
            result.put("msg", "运单已支付");
            return result;
        }

        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        if (!userCompanyId.contains(tOrderInfoVO2.getCompanyId().toString())) {
            log.info("运单不属于当前企业");
            result.put("code", "error");
            result.put("msg", "运单不属于当前企业");
            return result;
        }
        boolean udFlag = true;
        if(!tOrderInfoVO2.getRealName().equals(advanceOrderTmpExcel.getDriverName())){
            udFlag =false;
        }
        if(!tOrderInfoVO2.getRealPhone().equals(advanceOrderTmpExcel.getDriverPhone())){
            udFlag =false;
        }
        if(!tOrderInfoVO2.getVehicleNumber().equals(advanceOrderTmpExcel.getVehicleNumber())){
            udFlag =false;
        }

        if(!udFlag){
            log.error("运单信息错误：{}", orderBusinessCode);
            result.put("code", "error");
            result.put("msg", "运单信息错误");
            return result;
        }
        TAdvanceOrderTmp tAdvanceOrderTmp = advanceOrderTmpMapper.selectAdvanceOrderTempByOrderBusinessCode(advanceOrderTmpExcel.getOrderBusinessCode());
        if (null != tAdvanceOrderTmp) {
            log.info("运单信息已存在");
            result.put("code", "error");
            result.put("msg", "运单信息已存在");
            return result;
        } else {
            if (null != advanceOrderTmpExcel.getAdvanceFee() && advanceOrderTmpExcel.getAdvanceFee().compareTo(BigDecimal.ZERO) > 0) {

            } else {
                log.info("运单运费不合法");
                result.put("code", "error");
                result.put("msg", "运单运费不合法");
                return result;
            }
        }
        return null;
    }

    /**
    * @description 检测预付款尾款支付
    * <AUTHOR>
    * @date 2021/11/15 15:23
    */
    public ResultUtil checkWkPayment(TOrderInfo tOrderInfo, SendOrderVO record) {
        ResultUtil resultUtil = ResultUtil.error();
        if (null != tOrderInfo) {
            if (null != tOrderInfo.getPackStatus()) {
                if (tOrderInfo.getPackStatus().equals("1")) {
                    resultUtil.setCode("error");
                    resultUtil.setData(tOrderInfo.getOrderBusinessCode());
                    resultUtil.setMsg("打包原始运单不可在此支付");
                    return resultUtil;
                } else {
                    TAdvanceOrderTmp tAdvanceOrderTmp = advanceOrderTmpMapper.selectAdvanceOrderTempByOrderCode(tOrderInfo.getCode());
                    if (null == tAdvanceOrderTmp) {
                        resultUtil.setCode("error");
                        resultUtil.setData(tOrderInfo.getOrderBusinessCode());
                        resultUtil.setMsg("未找到预付款运单");
                        return resultUtil;
                    }
                    TOrderState orderState = orderStateService.selectNewOrderState(tOrderInfo.getCode());
                    TOrderCastChanges tOrderCastChanges = orderCastChangesService.selectByNewOne(tOrderInfo.getCode());
                    String capitalTransferPattern = null == tOrderCastChanges.getCapitalTransferPattern() ? "" : tOrderCastChanges.getCapitalTransferPattern();
                    // 支付流程，且审核未通过
                    if (tOrderInfo.getOrderPayStatus().equals(DictEnum.M065.code) && !DictEnum.S0651.code.equals(orderState.getStateNodeValue())) {
                        if (DictEnum.S0650.code.equals(orderState.getStateNodeValue())) {
                            resultUtil.setMsg("当前运单支付前需要审核，审核员未审核，暂不能支付");
                        } else {
                            resultUtil.setMsg("当前运单审核未通过，暂不能支付");
                        }
                        resultUtil.setCode("error");
                        resultUtil.setData(tOrderInfo.getOrderBusinessCode());
                        return resultUtil;
                    }
                    if (null != record.getUserConfirmCarriagePayment() && !(record.getUserConfirmCarriagePayment().compareTo(BigDecimal.ZERO) > 0)) {
                        return ResultUtil.error("确认应付运费应当大于0");
                    }
                    try {
                        if (tOrderInfo.getOrderExecuteStatus().equals(DictEnum.O060.code)
                                && (com.lz.common.util.StringUtils.isEmpty(tOrderInfo.getOrderPayStatus())
                                || tOrderInfo.getOrderPayStatus().equals(DictEnum.M065.code)
                                || tOrderInfo.getOrderPayStatus().equals(DictEnum.M080.code)
                                || tOrderInfo.getOrderPayStatus().equals(DictEnum.M095.code))) {
                            //TODO 1. 判断车辆和司机是否审核通过：线路货物关系表中
                            LineGoodsRelInfo lineGoodsRelInfo = linkGoodsRelAPI.selectLineGoodsRelInfo(new TGoodsSourceInfo().setLineGoodsRelId(tOrderInfo.getLineGoodsRelId()));
                            if (null != lineGoodsRelInfo && null != lineGoodsRelInfo.getTransportIdentityCheck()
                                    && lineGoodsRelInfo.getTransportIdentityCheck()) {
                                String checkUserCarStatus = checkUserCarStatus(tOrderInfo,
                                        tOrderCastChanges.getCapitalTransferType(), capitalTransferPattern);
                                if (com.lz.common.util.StringUtils.isNotBlank(checkUserCarStatus)) {
                                    resultUtil.setCode("error");
                                    resultUtil.setData(tOrderInfo.getOrderBusinessCode());
                                    resultUtil.setMsg(checkUserCarStatus);
                                    return resultUtil;
                                }
                            }
                            //TODO 2。判断合同
                            String contractStatus = tOrderInfo.getContractStatus();
                            if (contractStatus.equals(DictEnum.WQD.code)) {
                                resultUtil.setCode("error");
                                resultUtil.setData(tOrderInfo.getOrderBusinessCode());
                                resultUtil.setMsg("运单未签署电子合同，请联系司机签署线下合同。");
                                return resultUtil;
                            }
                            //TODO 3. 查询车主权益证明(资金转移方式到车主) 车队长上线后为了处理老数据 车老板不进行判断
                            /*if (DictEnum.PAYTOBELONGER.code.equals(tOrderCastChanges.getCapitalTransferType())
                                    || DictEnum.FIRSTBROKERBELONGER.code.equals(tOrderCastChanges.getCapitalTransferType())) {
                                if (null == orderInfoByCoded.getEndCarOwnerId()) {
                                    resultUtil.setData(orderInfoByCoded.getOrderBusinessCode());
                                    resultUtil.setMsg("缺少车主信息");
                                    return resultUtil;
                                }
                                ResultUtil selectCarOwnerQY = appCommonAPI.selectCarOwnerQY(orderInfoByCoded.getEndCarOwnerId(), orderInfoByCoded.getVehicleId());
                                if (DictEnum.ERROR.code.equals(selectCarOwnerQY.getCode())) {
                                    selectCarOwnerQY.setData(orderInfoByCoded.getOrderBusinessCode());
                                    selectCarOwnerQY.setMsg(selectCarOwnerQY.getMsg());
                                    return selectCarOwnerQY;
                                }
                            }*/
                            if (contractStatus.equals(DictEnum.YZHUD.code)
                                    || contractStatus.equals(DictEnum.YZID.code)
                                    || contractStatus.equals(DictEnum.HBXXHT.code)
                                    || contractStatus.equals(DictEnum.XXHTSC.code)
                                    || contractStatus.equals(DictEnum.APPHTSC.code)) {
                                return null;
                            } else {
                                resultUtil.setCode("error");
                                resultUtil.setData(tOrderInfo.getOrderBusinessCode());
                                resultUtil.setMsg("运单未签署电子合同，请联系司机签署线下合同。");
                                return resultUtil;
                            }
                        } else {
                            resultUtil.setCode("error");
                            resultUtil.setData(tOrderInfo.getOrderBusinessCode());
                            resultUtil.setMsg("运单状态错误， 不可支付。");
                            return resultUtil;
                        }
                    } catch (Exception e) {
                        log.error("ZJJ-241:支付失败!", e);
                        resultUtil.setCode("error");
                        resultUtil.setData(tOrderInfo.getOrderBusinessCode());
                        String message = e.getMessage();
                        if (StringUtils.checkChineseCharacter(message)) {
                            resultUtil.setMsg(message);
                        } else {
                            resultUtil.setMsg("ZJJ-241:支付失败!");
                        }
                        return resultUtil;
                    }
                }
            } else {
                resultUtil.setCode("error");
                resultUtil.setData(tOrderInfo.getOrderBusinessCode());
                return resultUtil;
            }
        }
        return null;
    }

    /**
     * @description 创建节点支付规则
     * <AUTHOR>
     * @date 2022/3/21 10:11
     */
    public void createOrderPayRule(String orderCode, CompanySourceDTO companySourceDTO) {
        TOrderInfo tInfo = orderInfoMapper.selectOrderByCode(orderCode);
        boolean zhFlag = true;
        boolean xhFlag = true;
        boolean sdFlag = true;
        //装货支付节点
        if(null!= companySourceDTO.getValue1() && !(companySourceDTO.getValue1().compareTo(BigDecimal.ZERO)==0)){
            zhFlag = false;
            TOrderPayRule tOrderPayRule1 = new TOrderPayRule();
            tOrderPayRule1.setCode(IdWorkerUtil.getInstance().nextId());
            tOrderPayRule1.setPayMethod(companySourceDTO.getCarriagePayType());
            tOrderPayRule1.setOrderCode(tInfo.getCode());
            tOrderPayRule1.setLineGoodsCarriageRuleId(companySourceDTO.getLineGoodsCarriageRuleId());
            tOrderPayRule1.setPayNodeType(DictEnum.ZHPAYNODE.code);
            tOrderPayRule1.setPayStatus(DictEnum.PACKUNPAID.code);
            if(DictEnum.NODEPAYFIXED.code.equals(companySourceDTO.getCarriagePayType())){ //固定值
                tOrderPayRule1.setRulePaymentFee(companySourceDTO.getValue1());
            }else if(DictEnum.NODEPAYPROPORTION.code.equals(companySourceDTO.getCarriagePayType())){//按比例
                BigDecimal rulePaymentFee = companySourceDTO.getValue1().divide(new BigDecimal(100), RoundingMode.HALF_DOWN);
                tOrderPayRule1.setRulePaymentFee(tInfo.getEstimateTotalFee().multiply(rulePaymentFee));
            }
            tOrderPayRule1.setSurplusPaymentFee(tInfo.getEstimateTotalFee());
            tOrderPayRule1.setPayDispatchFee(tInfo.getDispatchFee());
            tOrderPayRule1.setEnable(false);
            tOrderPayRule1.setCreateUser(CurrentUser.getUserNickname());
            tOrderPayRule1.setUpdateUser(CurrentUser.getUserNickname());
            tOrderPayRule1.setCreateTime(new Date());
            tOrderPayRule1.setUpdateTime(new Date());
            tOrderPayRuleMapper.insertSelective(tOrderPayRule1);
        }
        //卸货支付节点
        if(null!= companySourceDTO.getValue2() && !(companySourceDTO.getValue2().compareTo(BigDecimal.ZERO)==0)){
            xhFlag = false;
            TOrderPayRule tOrderPayRule2 = new TOrderPayRule();
            tOrderPayRule2.setCode(IdWorkerUtil.getInstance().nextId());
            tOrderPayRule2.setPayMethod(companySourceDTO.getCarriagePayType());
            tOrderPayRule2.setOrderCode(tInfo.getCode());
            tOrderPayRule2.setLineGoodsCarriageRuleId(companySourceDTO.getLineGoodsCarriageRuleId());
            tOrderPayRule2.setPayNodeType(DictEnum.XHPAYNODE.code);
            if(zhFlag){//装货地支付金额为0或为null 时 卸货地支付状态为待支付
                tOrderPayRule2.setPayStatus(DictEnum.PACKUNPAID.code);
                tOrderPayRule2.setSurplusPaymentFee(tInfo.getEstimateTotalFee().subtract(tInfo.getDispatchFee()));
            }
            if(DictEnum.NODEPAYFIXED.code.equals(companySourceDTO.getCarriagePayType())){ //固定值
                tOrderPayRule2.setRulePaymentFee(companySourceDTO.getValue2());
            }else if(DictEnum.NODEPAYPROPORTION.code.equals(companySourceDTO.getCarriagePayType())){//按比例
                BigDecimal rulePaymentFee = companySourceDTO.getValue2().divide(new BigDecimal(100), RoundingMode.HALF_DOWN);
                tOrderPayRule2.setRulePaymentFee(tInfo.getEstimateTotalFee().multiply(rulePaymentFee));
            }
            tOrderPayRule2.setPayDispatchFee(tInfo.getDispatchFee());
            tOrderPayRule2.setEnable(false);
            tOrderPayRule2.setCreateUser(CurrentUser.getUserNickname());
            tOrderPayRule2.setUpdateUser(CurrentUser.getUserNickname());
            tOrderPayRule2.setCreateTime(new Date());
            tOrderPayRule2.setUpdateTime(new Date());
            tOrderPayRuleMapper.insertSelective(tOrderPayRule2);
        }

        //收单支付节点
        if(null!= companySourceDTO.getValue3() && !(companySourceDTO.getValue3().compareTo(BigDecimal.ZERO)==0)){
            sdFlag = false;
            TOrderPayRule tOrderPayRule3 = new TOrderPayRule();
            tOrderPayRule3.setCode(IdWorkerUtil.getInstance().nextId());
            tOrderPayRule3.setPayMethod(companySourceDTO.getCarriagePayType());
            tOrderPayRule3.setOrderCode(tInfo.getCode());
            tOrderPayRule3.setLineGoodsCarriageRuleId(companySourceDTO.getLineGoodsCarriageRuleId());
            tOrderPayRule3.setPayNodeType(DictEnum.SDPAYNODE.code);
            if(zhFlag && xhFlag){//装货、卸货地支付金额为0或为null 时 收单支付状态为待支付
                tOrderPayRule3.setPayStatus(DictEnum.PACKUNPAID.code);
                tOrderPayRule3.setSurplusPaymentFee(tInfo.getEstimateTotalFee().subtract(tInfo.getDispatchFee()));
            }
            if(DictEnum.NODEPAYFIXED.code.equals(companySourceDTO.getCarriagePayType())){ //固定值
                tOrderPayRule3.setRulePaymentFee(companySourceDTO.getValue3());
            }else if(DictEnum.NODEPAYPROPORTION.code.equals(companySourceDTO.getCarriagePayType())){//按比例
                BigDecimal rulePaymentFee = companySourceDTO.getValue3().divide(new BigDecimal(100), RoundingMode.HALF_DOWN);
                tOrderPayRule3.setRulePaymentFee(tInfo.getEstimateTotalFee().multiply(rulePaymentFee));
            }
            tOrderPayRule3.setPayDispatchFee(tInfo.getDispatchFee());
            tOrderPayRule3.setEnable(false);
            tOrderPayRule3.setCreateUser(CurrentUser.getUserNickname());
            tOrderPayRule3.setUpdateUser(CurrentUser.getUserNickname());
            tOrderPayRule3.setCreateTime(new Date());
            tOrderPayRule3.setUpdateTime(new Date());
            tOrderPayRuleMapper.insertSelective(tOrderPayRule3);
        }

        //尾款支付节点
        if(null!= companySourceDTO.getValue4() && !(companySourceDTO.getValue4().compareTo(BigDecimal.ZERO)==0)){
            TOrderPayRule tOrderPayRule4 = new TOrderPayRule();
            tOrderPayRule4.setCode(IdWorkerUtil.getInstance().nextId());
            tOrderPayRule4.setPayMethod(companySourceDTO.getCarriagePayType());
            tOrderPayRule4.setOrderCode(tInfo.getCode());
            tOrderPayRule4.setLineGoodsCarriageRuleId(companySourceDTO.getLineGoodsCarriageRuleId());
            tOrderPayRule4.setPayNodeType(DictEnum.WKPAYNODE.code);
            if(zhFlag && xhFlag && sdFlag){//装货、卸货地支付、收单支付金额为0或为null 时 尾款支付节点状态为待支付
                tOrderPayRule4.setPayStatus(DictEnum.PACKUNPAID.code);
                tOrderPayRule4.setSurplusPaymentFee(tInfo.getEstimateTotalFee().subtract(tInfo.getDispatchFee()));
            }

            if(DictEnum.NODEPAYFIXED.code.equals(companySourceDTO.getCarriagePayType())){ //固定值
                tOrderPayRule4.setRulePaymentFee(companySourceDTO.getValue4());
            }else if(DictEnum.NODEPAYPROPORTION.code.equals(companySourceDTO.getCarriagePayType())){//按比例
                BigDecimal rulePaymentFee = companySourceDTO.getValue4().divide(new BigDecimal(100), RoundingMode.HALF_DOWN);
                tOrderPayRule4.setRulePaymentFee(tInfo.getEstimateTotalFee().multiply(rulePaymentFee));
            }
            tOrderPayRule4.setPayDispatchFee(tInfo.getDispatchFee());
            tOrderPayRule4.setEnable(false);
            tOrderPayRule4.setCreateUser(CurrentUser.getUserNickname());
            tOrderPayRule4.setUpdateUser(CurrentUser.getUserNickname());
            tOrderPayRule4.setCreateTime(new Date());
            tOrderPayRule4.setUpdateTime(new Date());
            tOrderPayRuleMapper.insertSelective(tOrderPayRule4);
        }
    }

    /**
     * @Description 发单查询车辆司机状态
     * <AUTHOR>
     * @Date 2023/07/05 16:10:59
     * @Param
     * @Return
     * @Exception
     */
    public EnduserCarStatus selectEnduserCarStatus(Integer endUserId, Integer endCarId) {
        List<EnduserCarStatus> enduserCarStatusList = new ArrayList<>();
        EnduserCarStatus status = new EnduserCarStatus();
        status.setEndcarId(endCarId);
        status.setEnduserId(endUserId);
        enduserCarStatusList.add(status);
        TEnduserCarStatus enduserCarStatus = new TEnduserCarStatus();
        enduserCarStatus.setEnduserCarStatuses(enduserCarStatusList);
        ResultUtil<?> userCarStatus = endSUserInfoAPI.selectEnduserCarStatus(enduserCarStatus);
        LinkedHashMap<?, ?> data = (LinkedHashMap<?, ?>) userCarStatus.getData();
        ArrayList<?> endUserCarStatuses = (ArrayList<?>) data.get("enduserCarStatuses");
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper.convertValue(endUserCarStatuses.get(0), EnduserCarStatus.class);
    }

    /**
     * 司机、车辆运单数量大于1时，查询审核状态是否通过
     * @param vo
     * @return
     */
    public String checkDriverCarAuditStatusByTimes(CarDriverRelVO vo, boolean isScanOrder) {
        if (null == vo.getEndDriverId()) {
            vo.setEndDriverId(vo.getEnduserId());
        }
        Integer orderCount = orderDriverCarStatisticsMapper.selectOrderCount(vo);
        if (null == orderCount) {
            return null;
        }
        if (orderCount >= 1) {
            if (null == vo.getEndDriverId()) {
                vo.setEndDriverId(vo.getEnduserId());
            }
            ResultUtil endCarAndUserAuditStatus = appCommonAPI.selectEndcarAndUserAuditStatus(vo);
            EnduserCarStatus enduserCarStatus = JSONUtil.toBean(JSONUtil.toJsonStr(endCarAndUserAuditStatus.getData()), EnduserCarStatus.class);
            String message = null;
            if (null == enduserCarStatus.getUserAuditStatus() ||!enduserCarStatus.getUserAuditStatus().equals(DictEnum.PASSNODE.code)) {
                if (isScanOrder) {
                    message = "装货签到失败，请先完成实名认证后再扫码抢单。";
                } else {
                    String realName = null == enduserCarStatus.getRealName() ? "" : enduserCarStatus.getRealName();
                    message = "请司机" + realName + "先完成实名认证后再发单" + ".";
                }
            }
            if (null != message) {
                return message;
            }
            if (null == enduserCarStatus.getCarAuditStatus() || !enduserCarStatus.getCarAuditStatus().equals(DictEnum.PASSNODE.code)) {
                if (isScanOrder) {
                    message = "装货签到失败，请先完善车辆资料并审核通过后再扫码抢单。";
                } else {
                    message = "请司机先完善车辆" + enduserCarStatus.getVehicleNumber() + "的资料并审核通过后再发单" + ".";
                }
            }
            if (null != message) {
                return message;
            }
        }

        return null;
    }

    /**
     * 保存司机、车辆运单统计
     * @param vo
     * @return
     */
    public int saveDriverAndCarOrderCount(CarDriverRelVO vo) {
        TOrderDriverCarStatistics orderDriverCarStatistics = orderDriverCarStatisticsMapper.selectByDriverAndCar(vo);
        if (null != orderDriverCarStatistics) {
            orderDriverCarStatistics.setOrderCount(orderDriverCarStatistics.getOrderCount() + 1);
            orderDriverCarStatisticsMapper.updateByPrimaryKeySelective(orderDriverCarStatistics);
        } else {
            orderDriverCarStatistics = new TOrderDriverCarStatistics();
            orderDriverCarStatistics.setEndDriverId(vo.getEndDriverId());
            orderDriverCarStatistics.setCarId(vo.getEndcarId());
            orderDriverCarStatistics.setOrderCount(1);
            orderDriverCarStatisticsMapper.insertSelective(orderDriverCarStatistics);
        }
        return 1;
    }


}
