package com.lz.util;

import cn.hutool.json.JSONUtil;
import com.lz.api.HXOpenRoleCommonAPI;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.util.ResultUtil;
import com.lz.dao.TOrderCastCalcSnapshotMapper;
import com.lz.dao.TOrderInfoMapper;
import com.lz.dto.OpenRoleStatusListDTO;
import com.lz.dto.OpenRoleWalletListDTO;
import com.lz.dto.OrderPackDTO;
import com.lz.vo.SelectOrderPackOpenRoleVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 华夏支付，运单打包过滤
 */
@Component
public class HXPayOrderPackJudgeFilter {

    @Autowired
    private TOrderInfoMapper orderInfoMapper;

    @Autowired
    private TOrderCastCalcSnapshotMapper orderCastCalcSnapshotMapper;

    @Autowired
    private HXOpenRoleCommonAPI openRoleCommonAPI;

    private static HXPayOrderPackJudgeFilter filter;

    @PostConstruct
    public void init() {
        filter = this;
        filter.orderInfoMapper = this.orderInfoMapper;
        filter.orderCastCalcSnapshotMapper = this.orderCastCalcSnapshotMapper;
        filter.openRoleCommonAPI = this.openRoleCommonAPI;
    }

    public static Map<String, Object> judge(List<String> codes) {
        Map<String, Object> map = new HashMap<>();
        List<OrderPackDTO> orderPack = filter.orderInfoMapper.orderJudgeGetAllInfo(codes);
        //判断是否需要支付审核，如果需要，但未审核通过，则不能进行打包
        Map<String, Object> map1 = checkOrderAudit(map, orderPack);
        if (map1 != null) {
            return map1;
        }
        // 判断资金转移模式
        if (!judgeCapitalTransferPattern(orderPack)) {
            map.put("bool", false);
            map.put("msg", "资金流转模式不唯一!");
            return map;
        }
        // 判断资金转移方式 是否唯一
        if (!judgeCapitalTransferType(orderPack)) {
            map.put("bool", false);
            map.put("msg", "资金转移方式不唯一!");
            return map;
        }
        // 根据资金转移方式、资金流转模式，判断C端用户是否一致
        Map<String, Object> map2 = checkEnduserUnique(orderPack);
        if (map2 != null) {
            return map2;
        }
        map = judgeOther(orderPack);
        if (!(boolean) map.get("bool")) {
            return map;
        }
        // 查询开户状态、钱包
        Map<String, Object> openRoleStatusResult = selectOpenRoleStatusAndWallet(orderPack);
        if (null != openRoleStatusResult) {
            return openRoleStatusResult;
        }

        map.put("bool", true);
        map.put("msg", "验证通过");
        if (!orderPack.isEmpty()) {
            OrderPackDTO orderPackDTO = orderPack.get(0);
            if (null != orderPackDTO.getCompanyProjectId()) {
                map.put("projectId", orderPackDTO.getCompanyProjectId());
            }
        }
        map.put("orderPack", orderPack);
        return map;
    }

    /**
     * 查询开户状态、钱包
     *
     * @param orderPack
     */
    private static Map<String, Object> selectOpenRoleStatusAndWallet(List<OrderPackDTO> orderPack) {
        String capitalTransferType = orderPack.get(0).getCapitalTransferType();
        String capitalTransferPattern = null != orderPack.get(0).getCapitalTransferPattern()
                ? orderPack.get(0).getCapitalTransferPattern() : "";
        // 根据资金转移方式和资金流转模式，取出用户ID
        SelectOrderPackOpenRoleVO orderPackOpenRoleVO = getUserIdByCapitalTransfer(orderPack, capitalTransferType, capitalTransferPattern);

        // 查询开户状态
        Map<String, Object> openRoleStatus = selectOpenRoleStatus(capitalTransferType, capitalTransferPattern, orderPackOpenRoleVO);
        if (openRoleStatus != null && !openRoleStatus.isEmpty()) {
            return openRoleStatus;
        }

        // 查询钱包
        Map<String, Object> openRoleWallet = selectOpenRoleWallet(capitalTransferType, capitalTransferPattern, orderPackOpenRoleVO);
        if (null != openRoleWallet && !openRoleWallet.isEmpty()) {
            return openRoleWallet;
        }
        return null;
    }

    /**
     * @param
     * @return
     * @description 根据资金转移方式和资金流转模式，取出用户ID
     * <AUTHOR>
     * @date 2021/8/10 10:58
     */
    private static SelectOrderPackOpenRoleVO getUserIdByCapitalTransfer(List<OrderPackDTO> orderPack, String capitalTransferType, String capitalTransferPattern) {
        SelectOrderPackOpenRoleVO orderPackOpenRoleVO = new SelectOrderPackOpenRoleVO();
        // 取出用户ID
        orderPackOpenRoleVO.setCarrierId(orderPack.get(0).getCarrierId());
        orderPackOpenRoleVO.setCompanyId(orderPack.get(0).getCompanyId());
        if (null != capitalTransferPattern) {
            if (DictEnum.COMMONPATTERN.code.equals(capitalTransferPattern)) {
                if (DictEnum.PAYTODRIVER.code.equals(capitalTransferType)) {
                    orderPackOpenRoleVO.setDriverId(orderPack.get(0).getEndDriverId());
                } else if (DictEnum.PAYTOCAPTAIN.code.equals(capitalTransferType)) {
                    orderPackOpenRoleVO.setCaptainId(orderPack.get(0).getEndCarOwnerId());
                    List<Integer> driverList = orderPack.stream().map(order -> order.getEndDriverId()).collect(Collectors.toList());
                    orderPackOpenRoleVO.setDriverList(driverList);
                    orderPackOpenRoleVO.setCaptainId(orderPack.get(0).getEndCarOwnerId());
                }
            } else if (DictEnum.MANAGERPATTERN.code.equals(capitalTransferPattern)) {
                orderPackOpenRoleVO.setManagerId(orderPack.get(0).getAgentId());
                if (DictEnum.PAYTODRIVER.code.equals(capitalTransferType)) {
                    orderPackOpenRoleVO.setDriverId(orderPack.get(0).getEndDriverId());
                } else if (DictEnum.PAYTOCAPTAIN.code.equals(capitalTransferType)) {
                    List<Integer> driverList = orderPack.stream().map(order -> order.getEndDriverId()).collect(Collectors.toList());
                    orderPackOpenRoleVO.setDriverList(driverList);
                    orderPackOpenRoleVO.setCaptainId(orderPack.get(0).getEndCarOwnerId());
                }
            }
        }
        return orderPackOpenRoleVO;
    }

    /**
     * @param
     * @return
     * @description 查询开户状态
     * <AUTHOR>
     * @date 2021/8/10 09:40
     */
    private static Map<String, Object> selectOpenRoleStatus(String capitalTransferType,
                                                            String capitalTransferPattern,
                                                            SelectOrderPackOpenRoleVO openRoleStatus) {
        Map<String, Object> map = new HashMap<>(0);
        ResultUtil resultUtil = filter.openRoleCommonAPI.selectOrderPackCarrierCompanyEnduserOpenRoleStatus(openRoleStatus);
        if (null != resultUtil.getCode() && DictEnum.SUCCESS.code.equals(resultUtil.getCode())) {
            if (null != resultUtil.getData()) {
                OpenRoleStatusListDTO dto = JSONUtil.toBean(JSONUtil.toJsonStr(resultUtil.getData()), OpenRoleStatusListDTO.class);
                if (null == dto.getCarrierStatus() || !dto.getCarrierStatus().getStatus()) {
                    map.put("bool", false);
                    map.put("msg", "承运方未开户，请联系运营平台予以解决!");
                    return map;
                }

                if (null == dto.getCompanyStatus() || !dto.getCompanyStatus().getStatus()) {
                    map.put("bool", false);
                    map.put("msg", "企业未开户，请联系运营平台予以解决!");
                    return map;
                }
                StringBuffer sb = new StringBuffer();
                if (DictEnum.PAYTODRIVER.code.equals(capitalTransferType)) {
                    if (null == dto.getDriverStatus() || !dto.getDriverStatus().getStatus()) {
                        sb.append("司机：").append(dto.getDriverStatus().getRealName()).append("未开户。\n");
                    }
                }
                if (DictEnum.PAYTOCAPTAIN.code.equals(capitalTransferType)) {
                    StringBuffer drvierError = new StringBuffer();
                    if (null == dto.getDriverList() || dto.getDriverList().isEmpty()) {
                        sb.append("请检查司机开户状态").append("\n");
                    } else {
                        dto.getDriverList().forEach((driver) -> {
                            if (null == driver.getOpenStatus() || null == driver.getStatus() || driver.getStatus() != 1) {
                                drvierError.append(driver.getRealName()).append(driver.getOpenStatus()).append(",");
                            }
                        });
                        if (drvierError.length() > 0) {
                            sb.append("司机：").append(drvierError).append("\n");
                        }
                    }

                    if (null == dto.getCaptionStatus() || !dto.getCaptionStatus().getStatus()) {
                        sb.append("车队长：").append(dto.getCaptionStatus().getRealName()).append("未开户。\n");
                    }
                }
                if (DictEnum.MANAGERPATTERN.code.equals(capitalTransferPattern)) {
                    if (null == dto.getManagerStatus() || !dto.getManagerStatus().getStatus()) {
                        sb.append("经纪人：").append(dto.getManagerStatus().getRealName()).append("未开户。");;
                    }
                }
                if (sb.length() > 0) {
                    map.put("bool", false);
                    map.put("msg", sb.toString());
                    return map;
                }

            }
        }
        return null;
    }

    /**
     * @param
     * @return
     * @description 查询钱包
     * <AUTHOR>
     * @date 2021/8/10 10:24
     */
    private static Map<String, Object> selectOpenRoleWallet(String capitalTransferType,
                                                            String capitalTransferPattern,
                                                            SelectOrderPackOpenRoleVO openRoleStatus) {
        Map<String, Object> map = new HashMap<>(0);
        ResultUtil resultUtil = filter.openRoleCommonAPI.selectOrderPackCarrierCompanyEnduserOPenRoleWallet(openRoleStatus);
        if (null != resultUtil.getCode() && DictEnum.SUCCESS.code.equals(resultUtil.getCode())) {
            if (null != resultUtil.getData()) {
                OpenRoleWalletListDTO dto = JSONUtil.toBean(JSONUtil.toJsonStr(resultUtil.getData()), OpenRoleWalletListDTO.class);
                if (null == dto.getCarrierWallet()) {
                    map.put("bool", false);
                    map.put("msg", "承运方钱包未找到，请联系运营平台予以解决!");
                    return map;
                }
                if (null == dto.getCompanyWallet()) {
                    map.put("bool", false);
                    map.put("msg", "企业钱包未找到，请联系运营平台予以解决!");
                    return map;
                }
                if (DictEnum.PAYTODRIVER.code.equals(capitalTransferType)) {
                    if (null == dto.getDriverWallet()) {
                        map.put("bool", false);
                        map.put("msg", "司机钱包未找到，请联系运营平台予以解决!");
                        return map;
                    }
                }
                if (DictEnum.PAYTOCAPTAIN.code.equals(capitalTransferType)) {
                    if (null == dto.getCaptionWallet()) {
                        map.put("bool", false);
                        map.put("msg", "车队长钱包未找到，请联系运营平台予以解决!");
                        return map;
                    }
                    if (null == dto.getDriverWalletStatus() || !dto.getDriverWalletStatus()) {
                        map.put("bool", false);
                        map.put("msg", "司机钱包未找到，请联系运营平台予以解决!");
                        return map;
                    }
                }
                if (DictEnum.MANAGERPATTERN.code.equals(capitalTransferPattern)) {
                    if (null == dto.getManagerWallet()) {
                        map.put("bool", false);
                        map.put("msg", "经纪人钱包未找到，请联系运营平台予以解决!");
                        return map;
                    }
                }
            }
        }
        return null;
    }

    /**
     * @param
     * @return
     * @description 根据资金转移方式、资金流转模式，判断C端用户是否一致
     * <AUTHOR>
     * @date 2021/8/9 18:55
     */
    private static Map<String, Object> checkEnduserUnique(List<OrderPackDTO> orderPack) {
        Map<String, Object> map;
        String type = DictEnum.valueOf(orderPack.get(0).getCapitalTransferType()).code;
        String capitalTransferPattern = null != orderPack.get(0).getCapitalTransferPattern()
                ? orderPack.get(0).getCapitalTransferPattern() : "";
        if (type.equals(DictEnum.PAYTODRIVER.code)) {
            // 判断司机
            map = judgeDriver(orderPack);
            if (!(boolean) map.get("bool")) {
                return map;
            }
        }  else if (type.equals(DictEnum.PAYTOCAPTAIN.code)) {
            // 判断车队长
            map = judgeCaptain(orderPack);
            if (!(boolean) map.get("bool")) {
                return map;
            }
        }
        // 经纪人模式
        if (DictEnum.MANAGERPATTERN.code.equals(capitalTransferPattern)) {
            map = judgeAgent(orderPack);
            if (!(boolean) map.get("bool")) {
                return map;
            }
        }
        return null;
    }

    private static Map<String, Object> checkOrderAudit(Map<String, Object> map, List<OrderPackDTO> orderPack) {
        for (OrderPackDTO orderPackDTO : orderPack) {
            if (orderPackDTO.getOrderPayStatusCode().equals(DictEnum.M065.code) && !orderPackDTO.getPayChildStatus().equals(DictEnum.S0651.code)) {
                map.put("bool", false);
                map.put("msg", "有未审核通过的运单!");
                return map;
            }
            if (orderPackDTO.getPackStatus().equals(DictEnum.PACK.code)) {
                map.put("bool", false);
                map.put("msg", "运单已经被打包!");
                return map;
            }
        }
        return null;
    }

    /**
     * 资金转移模式--唯一校验
     */
    public static boolean judgeCapitalTransferPattern(List<OrderPackDTO> orderPack) {
        Set<String> ctt = new HashSet<>();
        for (OrderPackDTO pack : orderPack) {
            if (null != pack.getCapitalTransferPattern()) {
                ctt.add(pack.getCapitalTransferPattern());
            }
        }
        if (ctt.size() != 1) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 资金转移方式--唯一校验
     */
    public static boolean judgeCapitalTransferType(List<OrderPackDTO> orderPack) {
        Set<String> ctt = new HashSet<>();
        for (OrderPackDTO pack : orderPack) {
            String capitalTransferType = pack.getCapitalTransferType();
            ctt.add(capitalTransferType);
        }
        if (ctt.size() != 1) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 车老板--非空唯一校验
     */
    private static Map<String, Object> judgeCarOwner(List<OrderPackDTO> orderPack) {
        Map<String, Object> map = new HashMap<>();
        Set<Integer> ctt = new HashSet<>();
        boolean temp = true;
        for (OrderPackDTO pack : orderPack) {
            Integer endCarOwnerId = pack.getEndCarOwnerId();
            if (null != endCarOwnerId) {
                ctt.add(endCarOwnerId);
            } else {
                temp = false;
                map.put("bool", temp);
                map.put("msg", "已选运单车老板不能为空!");
                break;
            }
        }
        if (temp) {
            if (ctt.size() != 1) {
                temp = false;
                map.put("bool", temp);
                map.put("msg", "已选运单车老板不唯一!");
            } else {
                map.put("bool", temp);
            }
        }
        return map;
    }

    /**
     * 司机--非空唯一校验
     */
    private static Map<String, Object> judgeDriver(List<OrderPackDTO> orderPack) {
        Map<String, Object> map = new HashMap<>();
        Set<Integer> ctt = new HashSet<>();
        boolean temp = true;
        for (OrderPackDTO pack : orderPack) {
            Integer endDriverId = pack.getEndDriverId();
            if (null != endDriverId) {
                ctt.add(endDriverId);
            } else {
                temp = false;
                map.put("bool", temp);
                map.put("msg", "已选运单司机不能为空!");
                break;
            }
        }
        if (temp) {
            if (ctt.size() != 1) {
                temp = false;
                map.put("bool", temp);
                map.put("msg", "已选运单司机不唯一!");
            } else {
                map.put("bool", temp);
            }
        }
        return map;
    }

    /**
     * 经纪人--非空唯一校验
     */
    private static Map<String, Object> judgeEndAgent(List<OrderPackDTO> orderPack, String type) {
        Map<String, Object> map = new HashMap<>();
        Set<Integer> ctt = new HashSet<>();
        Set<Integer> enduserIds = new HashSet<>();
        boolean temp = true;
        for (OrderPackDTO pack : orderPack) {
            if (type.equals(DictEnum.FIRSTBROKERDRIVER.code)) {
                enduserIds.add(pack.getEndDriverId());
            }
            if (type.equals(DictEnum.FIRSTBROKERBELONGER.code)) {
                enduserIds.add(pack.getEndCarOwnerId());
            }
            Integer endAgent = pack.getEndAgentId();
            if (null != endAgent) {
                ctt.add(endAgent);
            } else {
                temp = false;
                map.put("bool", temp);
                map.put("msg", "已选运单经纪人不能为空!");
                break;
            }
        }
        if (temp) {
            if (ctt.size() != 1) {
                temp = false;
                map.put("bool", temp);
                map.put("msg", "已选运单经纪人不唯一!");
            } else {
                map.put("bool", temp);
                if (enduserIds.size() != 1) {
                    map.put("bool", false);
                    if (type.equals(DictEnum.FIRSTBROKERDRIVER.code)) {
                        map.put("msg", "已选运单司机不唯一!");
                    }
                    if (type.equals(DictEnum.FIRSTBROKERBELONGER.code)) {
                        map.put("msg", "已选运单车主不唯一!");
                    }
                }
            }
        }
        return map;
    }

    /**
     * 车队长--非空唯一校验
     */
    private static Map<String, Object> judgeCaptain(List<OrderPackDTO> orderPack) {
        Map<String, Object> map = new HashMap<>();
        Set<Integer> ctt = new HashSet<>();
        boolean temp = true;
        for (OrderPackDTO pack : orderPack) {
            Integer endCarOwnerId = pack.getEndCarOwnerId();
            if (null != endCarOwnerId) {
                ctt.add(endCarOwnerId);
            } else {
                temp = false;
                map.put("bool", temp);
                map.put("msg", "已选运单车队长不能为空!");
                break;
            }
        }
        if (temp) {
            if (ctt.size() != 1) {
                temp = false;
                map.put("bool", temp);
                map.put("msg", "已选运单车队长不唯一!");
            } else {
                map.put("bool", temp);
            }
        }
        return map;
    }

    /**
     * 判断经纪人是否唯一
     *
     * @param orderPack
     * @return
     */
    private static Map<String, Object> judgeAgent(List<OrderPackDTO> orderPack) {
        Map<String, Object> map = new HashMap<>();
        Set<Integer> ctt = new HashSet<>();
        boolean temp = true;
        for (OrderPackDTO pack : orderPack) {
            if (null != pack.getAgentId()) {
                ctt.add(pack.getAgentId());
            } else {
                temp = false;
                map.put("bool", temp);
                map.put("msg", "已选运单经纪人不能为空!");
                break;
            }
        }
        if (temp) {
            if (ctt.size() != 1) {
                temp = false;
                map.put("bool", temp);
                map.put("msg", "已选运单经纪人不唯一!");
            } else {
                map.put("bool", temp);
            }
        }
        return map;
    }

    private static Map<String, Object> judgeOther(List<OrderPackDTO> orderPack) {
        HashSet<String> contract = new HashSet<>();
        HashSet<String> driverStatus = new HashSet<>();
        HashSet<String> carStatus = new HashSet<>();
        HashSet<String> executeStatus = new HashSet<>();
        HashSet<Integer> project = new HashSet<>();
        HashSet<String> withdrawType = new HashSet<>();
        HashSet<String> companyId = new HashSet<>();
        HashSet<Integer> carrierId = new HashSet<>();
        HashSet<BigDecimal> dispatchFeeRate = new HashSet<>();
        HashSet<Boolean> packStatus = new HashSet<>();
        // 支付方式
        Set<String> feeSettlementWay = new HashSet<>();
        // 是否业务部辅助
        HashSet<Boolean> businessAssist = new HashSet<>();

        HashMap<String, Object> map = new HashMap<>();
        List<String> unSingContractOrder = new ArrayList<>();
        for (OrderPackDTO orderPackDTO : orderPack) {
            project.add(orderPackDTO.getCompanyProjectId());
            contract.add(orderPackDTO.getContractStatus());
            if (null == orderPackDTO.getContractStatus() || DictEnum.WQD.code.equals(orderPackDTO.getContractStatus())) {
                unSingContractOrder.add(orderPackDTO.getOrderBusinessCode());
            }
            executeStatus.add(orderPackDTO.getOrderExecuteStatus());
            driverStatus.add(orderPackDTO.getDriverStatus());
            carStatus.add(orderPackDTO.getCarStatus());
            withdrawType.add(orderPackDTO.getWithdrawType());
            companyId.add(orderPackDTO.getCompanyId().toString());
            carrierId.add(orderPackDTO.getCarrierId());
            feeSettlementWay.add(orderPackDTO.getFeeSettlementWay());
            dispatchFeeRate.add(orderPackDTO.getCurrentDispatchRate());
            packStatus.add(orderPackDTO.getStatus());
            businessAssist.add(orderPackDTO.getBusinessAssist());
        }
        for (String ct : driverStatus) {
            if (!DictEnum.PASSNODE.code.equals(ct)) {
                map.put("bool", false);
                map.put("msg", "已选运单司机认证未通过!");
                return map;
            }
        }
        for (String ct : carStatus) {
            if (!DictEnum.PASSNODE.code.equals(ct)) {
                map.put("bool", false);
                map.put("msg", "已选运单车辆认证未通过!");
                return map;
            }
        }
        for (String ct : contract) {
            if (DictEnum.WQD.code.equals(ct)) {
                map.put("bool", false);
                String msg = "您所勾选的运单中包含未签署运输合同的运单，##请补签合同后重新进行打包";
                if (null != unSingContractOrder && !unSingContractOrder.isEmpty()) {
                    String order = String.join("、", unSingContractOrder);
                    msg = msg.replace("##", order + "，");
                } else {
                    msg = msg.replace("##", "");
                }
                map.put("msg", msg);
                return map;
            }
        }
        for (String status : executeStatus) {
            if (!DictEnum.O060.code.equals(status)) {
                map.put("bool", false);
                map.put("msg", "已选运单货运未完成!");
                return map;
            }
        }
        if (project.size() != 1) {
            map.put("bool", false);
            map.put("msg", "已选运单项目不唯一!");
            return map;
        }
        if (withdrawType.size() != 1) {
            map.put("bool", false);
            map.put("msg", "已选运单提现方式不唯一!");
            return map;
        }
        if (companyId.size() != 1) {
            map.put("bool", false);
            map.put("msg", "已选运单企业不唯一!");
            return map;
        }
        if (carrierId.size() != 1) {
            map.put("bool", false);
            map.put("msg", "已选运单承运方不唯一!");
            return map;
        }
        // 判断调度费系数是否相同
        if (dispatchFeeRate.size() != 1) {
            map.put("bool", false);
            map.put("msg", "调度费系数不唯一!");
            return map;
        }
        if (orderPack.size() == 1) {
            OrderPackDTO orderPackDTO = orderPack.get(0);
            if (orderPackDTO.getStatus()) {
                map.put("bool", false);
                map.put("msg", "运单状态已变化，请重新选择运单!");
                return map;
            }
        } else if (orderPack.size() > 1) {
            if (packStatus.size() != 1) {
                map.put("bool", false);
                map.put("msg", "运单状态已变化，请重新选择运单!");
                return map;
            } else if (packStatus.size() == 1) {
                if (packStatus.contains(true)) {
                    map.put("bool", false);
                    map.put("msg", "运单状态已变化，请重新选择运单!");
                    return map;
                }
            }
        }
        // 判断是否业务不辅助
        if (businessAssist.size() != 1) {
            map.put("bool", false);
            map.put("msg", "是否业务部辅助不一致!");
            return map;
        } else {
            // 检查业务部相关信息
            HashMap<String, Object> stringObjectHashMap = checkBusinessAssist(orderPack, businessAssist, map);
            if (!stringObjectHashMap.isEmpty()) {
                return stringObjectHashMap;
            }
        }

        Integer endUserId = null;
        String type = DictEnum.valueOf(orderPack.get(0).getCapitalTransferType()).code;
        if (type.equals(DictEnum.PAYTODRIVER.code)) {
            // 判断司机
            endUserId = orderPack.get(0).getEndDriverId();
        } else if (type.equals(DictEnum.PAYTOBELONGER.code) || type.equals(DictEnum.PAYTOCAPTAIN.code)) {
            // 判断车老板
            endUserId = orderPack.get(0).getEndCarOwnerId();
        }

        map.put("bool", true);
        map.put("endUserId", endUserId);
        map.put("company", orderPack.get(0).getCompanyId());
        map.put("carrier", orderPack.get(0).getCarrierId());
        map.put("capitalTransfer", orderPack.get(0).getCapitalTransferType());
        map.put("driver", orderPack.get(0).getEndDriverId());
        map.put("owner", orderPack.get(0).getEndCarOwnerId());
        map.put("agent", orderPack.get(0).getAgentId());
        if (null != orderPack.get(0).getCapitalTransferPattern()) {
            map.put("capitalTransferPattern", orderPack.get(0).getCapitalTransferPattern());
        } else {
            map.put("capitalTransferPattern", "");
        }
        return map;
    }

    private static HashMap<String, Object> checkBusinessAssist(List<OrderPackDTO> orderPack, HashSet<Boolean> businessAssist, HashMap<String, Object> map) {
        if (businessAssist.size() == 0) {
            map.put("bool", false);
            map.put("msg", "未选择业务部!");
            return map;
        } else if (businessAssist.size() == 1) {

            Iterator<Boolean> endAgentIdIterator = businessAssist.iterator();
            if (endAgentIdIterator.hasNext()) {
                Boolean businessAssistState = endAgentIdIterator.next();
                // 如果业务部辅助，判断是不是同一业务部
                if (null != businessAssistState && businessAssistState) {
                    HashSet<Integer> endAgentIds = new HashSet<>();
                    int endAgentId = 0;
                    for (OrderPackDTO orderPackDTO : orderPack) {
                        if (null != orderPackDTO.getEndAgentId()) {
                            endAgentIds.add(orderPackDTO.getEndAgentId());
                            endAgentId++;
                        }
                    }
                    if (endAgentId != orderPack.size() || endAgentId == 0) {
                        map.put("bool", false);
                        map.put("msg", "运单未选择业务部!");
                        return map;
                    } else {
                        if (endAgentIds.size() != 1) {
                            map.put("bool", false);
                            map.put("msg", "业务部不唯一!");
                            return map;
                        }
                    }
                }
            }
        }
        return new HashMap<>();
    }

}
