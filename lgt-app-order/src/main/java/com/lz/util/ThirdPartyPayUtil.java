package com.lz.util;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.lz.api.MqAPI;
import com.lz.common.constants.MqMessageTag;
import com.lz.common.constants.MqMessageTopic;
import com.lz.common.dbenum.BankNameEnum;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.HXTradeTypeEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.MQDelayMessage;
import com.lz.common.model.hxPayment.response.CustomerBalancePayRes;
import com.lz.common.util.DateUtils;
import com.lz.dao.*;
import com.lz.model.*;
import com.lz.payment.TradeType;
import com.lz.payment.hxyh.HXPayOrderUtil;
import com.lz.payment.hxyh.HXPaymentUtil;
import com.lz.response.ThirdPartyOrderFileResponse;
import com.lz.schedule.model.TTask;
import com.lz.vo.ThirdPayVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import sdk.model.message.AsyncNotifyVirtualBalancePayMessageBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Component
public class ThirdPartyPayUtil {

    @Resource
    private TOrderInfoMapper orderInfoMapper;

    @Resource
    private TOrderCastChangesMapper orderCastChangesMapper;

    @Resource
    private TOrderPayDetailMapper orderPayDetailMapper;

    @Resource
    private THXOrderWalletChangeLogMapper hxOrderWalletChangeLogMapper;

    @Resource
    private HXPayOrderUtil hxPayOrderUtil;

    @Resource
    private HXPaymentUtil hxPaymentUtil;

    @Resource
    private MqAPI mqAPI;


    @Resource
    private TOrderTaskMapper orderTaskMapper;

    public void pay(TTask task) {
        try {
            // 支付主表code
            String nextId = IdWorkerUtil.getInstance().nextId();
            log.info("支付请求序号: {}", nextId);
            log.info("支付请求参数: {}", task.getRequestParameter());
            ThirdPayVO payVO = JSONUtil.toBean(task.getRequestParameter(), ThirdPayVO.class);
            log.info("支付请求参数: {}", JSONUtil.toJsonStr(payVO));
            TOrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(payVO.getOrderId());
            // 接收成功
            CustomerBalancePayRes payRes = new CustomerBalancePayRes();
            payRes.setOrderStatus("ACCEPT_SUCC");
            payRes.setBizOrderNo(nextId);
            payRes.setChannelId(BankNameEnum.HXBANK.key());
            payRes.setResponseCode("00000");
            payRes.setResponseDesc("成功");
            log.info("发送第三方平台运单支付回调结果, {}", JSONUtil.toJsonStr(payRes));
            String pushResponse = HttpUtil.post(payVO.getNotifyUrl(), JSONUtil.toJsonStr(payRes));
            log.info("发送第三方平台运单支付回调结果, {}", pushResponse);
            payRes.setOrderStatus("PAY_SUCC");
            payRes.setBizOrderNo(nextId);
            payRes.setChannelId(BankNameEnum.HXBANK.key());
            payRes.setResponseCode("00000");
            payRes.setResponseDesc("成功");
            log.info("发送第三方平台运单支付回调结果, {}", JSONUtil.toJsonStr(payRes));
            HttpUtil.post(payVO.getNotifyUrl(), JSONUtil.toJsonStr(payRes));

            MQDelayMessage mqDelayMessage = new MQDelayMessage();
            mqDelayMessage.setTopic(MqMessageTopic.THIRD_PARTY_ORDER);
            mqDelayMessage.setTag(MqMessageTag.THIRD_PARTY_ORDER_RECEIPT);
            mqDelayMessage.setKey(orderInfo.getCode());
            ThirdPartyOrderFileResponse response = new ThirdPartyOrderFileResponse();
            response.setFileType("ELECTRON_RECEIPT");
            response.setFileUrl("http://img.lugangtong56.com/ELECTRON_RECEIPT_1250416113629000189.pdf, http://img.lugangtong56.com/ELECTRON_RECEIPT_1250416113635000191.pdf");
            response.setTradeType("");
            response.setOrderCode(orderInfo.getCode());
            response.setOrderBusinessCode(orderInfo.getOrderBusinessCode());
            response.setBizOrderNo(nextId);
            response.setNotifyUrl(payVO.getNotifyUrl());
            mqDelayMessage.setBody(response);
            mqDelayMessage.setStartDeliverTime(1000 * 60);
            mqAPI.sendDelayMessage(mqDelayMessage);

            mqDelayMessage = new MQDelayMessage();
            mqDelayMessage.setTopic(MqMessageTopic.THIRD_PARTY_ORDER);
            mqDelayMessage.setTag(MqMessageTag.THIRD_PARTY_ORDER_SKPZ);
            mqDelayMessage.setKey(orderInfo.getCode());
            response.setFileType("SKPZ");
            response.setFileUrl("http://img.lugangtong56.com/18975497104919060482025030615283.pdf");
            response.setTradeType("");
            response.setBizOrderNo(nextId);
            mqDelayMessage.setBody(response);
            mqDelayMessage.setStartDeliverTime(2000 * 60);
            mqAPI.sendDelayMessage(mqDelayMessage);
        } catch (Exception e) {
            log.error("第三方平台运单支付失败", e);
            throw new RuntimeException(e);
        }

    }

    public void payTest(TOrderInfo orderInfo) {
        TOrderInfo forUpdateOrder = new TOrderInfo();
        forUpdateOrder.setId(orderInfo.getId());
        //运单状态：支付处理中
        forUpdateOrder.setOrderPayStatus(DictEnum.P070.code);
        orderInfo.setOrderPayStatus(DictEnum.P070.code);

        orderInfoMapper.updateByPrimaryKeySelective(forUpdateOrder);
        // 添加支付处理中子状态
        hxPaymentUtil.insertOrderState(orderInfo.getCode(), DictEnum.SP0701.code, new Date());

        TOrderCastChanges cc = orderCastChangesMapper.selectByNewOne(orderInfo.getCode());

        TOrderCastChanges castChanges = new TOrderCastChanges();
        BeanUtils.copyProperties(cc, castChanges);
        castChanges.setId(null);
        castChanges.setCode(IdWorkerUtil.getInstance().nextId());
        castChanges.setUserOper(TradeType.PayMent.code);
        castChanges.setTradeType(TradeType.RZ.code);
        castChanges.setCreateTime(new Date());
        castChanges.setUpdateTime(new Date());

        castChanges.setServiceFee(orderInfo.getUserConfirmServiceFee());
        // 添加最新资金变动
        orderCastChangesMapper.insertSelective(castChanges);
        cc.setDataEnable(false);
        // 将上一条资金变动置为无效
        orderCastChangesMapper.updateByPrimaryKeySelective(cc);

        // 添加支付主表
        TOrderPayInfo orderPayInfo = hxPaymentUtil.createOrderPayInfo(orderInfo);
        // 插入支付字表
        String orderPayDetailCode = hxPaymentUtil.createTxOrderPayDetail(orderPayInfo.getCode(),
                castChanges.getCode(), HXTradeTypeEnum.HX_COMBALANCE_PAY.code, TradeType.RZ.code);
        TOrderPayDetail tOrderPayDetail = orderPayDetailMapper.selectByCode(orderPayDetailCode);
        // 修改支付子表，添加回调结果、时间
        hxPaymentUtil.updateOrderPayDetail(tOrderPayDetail.getId(), DictEnum.TRADE_FINISHED.code, new Date());

        orderPayDetailCode = hxPaymentUtil.createTxOrderPayDetail(orderPayInfo.getCode(),
                castChanges.getCode(), HXTradeTypeEnum.HX_CABALANCE_PAY.code, TradeType.RZ.code);

    }

    public Integer createWalletLog(AsyncNotifyVirtualBalancePayMessageBody messageBody,
                                   Integer walletId, Integer traderWalletId, BigDecimal amount,
                                   String purseCategory, String orderBusinessCode,
                                   String tradeNo, String tradeType, Date tradeTime, String packStatus) {
        TZtWalletChangeLog walletChangeLog = new TZtWalletChangeLog();
        walletChangeLog.setWalletId(walletId);
        walletChangeLog.setAmount(amount);
        walletChangeLog.setOuterTradeNo(messageBody.getBankOrderNo());
        walletChangeLog.setOrderBusinessCode(orderBusinessCode);
        walletChangeLog.setWalletType(purseCategory);
        walletChangeLog.setTradeNo(tradeNo);
        walletChangeLog.setTradeType(tradeType);
        walletChangeLog.setTradeTime(tradeTime);
        walletChangeLog.setTraderWalletId(traderWalletId);
        walletChangeLog.setGmtClose(new Date());
        walletChangeLog.setParam1(packStatus);
        if (TradeType.BPAY.code.equals(tradeType) || TradeType.PPAY.code.equals(tradeType) || TradeType.CPAY.code.equals(tradeType) || TradeType.CMFEEAPY.code.equals(tradeType)) {
            walletChangeLog.setCarryOverAmount(messageBody.getCarryOverAmount());
        }
        if (HXTradeTypeEnum.HX_CTXTRANSFERZHICHU.code.equals(tradeType) || HXTradeTypeEnum.HX_CTXTRANSFERSHOURU.code.equals(tradeType)) {
            walletChangeLog.setCreateTime(DateUtils.addSeconds(new Date(), 4));
            walletChangeLog.setUpdateTime(walletChangeLog.getCreateTime());
        }
        hxOrderWalletChangeLogMapper.insertSelective(walletChangeLog);
        return walletChangeLog.getId();
    }
}
