<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.THXOrderPayInfoMapper">
  <resultMap id="BaseResultMap" type="com.lz.model.TOrderPayInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="pay_method" jdbcType="VARCHAR" property="payMethod" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="carrier_id" jdbcType="INTEGER" property="carrierId" />
    <result column="fee_settlement_way" jdbcType="VARCHAR" property="feeSettlementWay" />
    <result column="line_goods_carriage_rule_id" jdbcType="INTEGER" property="lineGoodsCarriageRuleId" />
    <result column="order_pay_status" jdbcType="VARCHAR" property="orderPayStatus" />
    <result column="order_prepay_amount" jdbcType="DECIMAL" property="orderPrepayAmount" />
    <result column="order_actual_payment" jdbcType="DECIMAL" property="orderActualPayment" />
    <result column="order_total_payment" jdbcType="DECIMAL" property="orderTotalPayment" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="param1" jdbcType="VARCHAR" property="param1" />
    <result column="param2" jdbcType="VARCHAR" property="param2" />
    <result column="param3" jdbcType="VARCHAR" property="param3" />
    <result column="param4" jdbcType="VARCHAR" property="param4" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="enable" jdbcType="BIT" property="enable" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <sql id="Base_Column_List">
    id, code, pay_method, order_code, fee_settlement_way, line_goods_carriage_rule_id, carrier_id,
    order_pay_status, payment_platforms, order_prepay_amount, order_actual_payment, order_total_payment,
    remark, param1, param2, param3, param4, create_user, create_time, update_user, update_time,
    `enable`
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_order_pay_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from t_order_pay_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.lz.example.TOrderPayInfoExample">
    delete from t_order_pay_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.lz.model.TOrderPayInfo" useGeneratedKeys="true">
    insert into t_order_pay_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="code != null">
        code,
      </if>
      <if test="payMethod != null">
        pay_method,
      </if>
      <if test="orderCode != null">
        order_code,
      </if>
      <if test="feeSettlementWay != null">
        fee_settlement_way,
      </if>
      <if test="lineGoodsCarriageRuleId != null">
        line_goods_carriage_rule_id,
      </if>
      <if test="orderPayStatus != null">
        order_pay_status,
      </if>
      <if test="paymentPlatforms != null">
        payment_platforms,
      </if>
      <if test="carrierId != null">
        carrier_id,
      </if>
      <if test="orderPrepayAmount != null">
        order_prepay_amount,
      </if>
      <if test="orderActualPayment != null">
        order_actual_payment,
      </if>
      <if test="orderTotalPayment != null">
        order_total_payment,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="param1 != null">
        param1,
      </if>
      <if test="param2 != null">
        param2,
      </if>
      <if test="param3 != null">
        param3,
      </if>
      <if test="param4 != null">
        param4,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null">
        #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="orderCode != null">
        #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="feeSettlementWay != null">
        #{feeSettlementWay,jdbcType=VARCHAR},
      </if>
      <if test="lineGoodsCarriageRuleId != null">
        #{lineGoodsCarriageRuleId,jdbcType=INTEGER},
      </if>
      <if test="orderPayStatus != null">
        #{orderPayStatus,jdbcType=VARCHAR},
      </if>
      <if test="paymentPlatforms != null">
        #{paymentPlatforms,jdbcType=VARCHAR},
      </if>
      <if test="carrierId != null">
        #{carrierId,jdbcType=INTEGER},
      </if>
      <if test="orderPrepayAmount != null">
        #{orderPrepayAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderActualPayment != null">
        #{orderActualPayment,jdbcType=DECIMAL},
      </if>
      <if test="orderTotalPayment != null">
        #{orderTotalPayment,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="param1 != null">
        #{param1,jdbcType=VARCHAR},
      </if>
      <if test="param2 != null">
        #{param2,jdbcType=VARCHAR},
      </if>
      <if test="param3 != null">
        #{param3,jdbcType=VARCHAR},
      </if>
      <if test="param4 != null">
        #{param4,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.lz.model.TOrderPayInfo">
    update t_order_pay_info
    <set>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null">
        pay_method = #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="orderCode != null">
        order_code = #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="feeSettlementWay != null">
        fee_settlement_way = #{feeSettlementWay,jdbcType=VARCHAR},
      </if>
      <if test="lineGoodsCarriageRuleId != null">
        line_goods_carriage_rule_id = #{lineGoodsCarriageRuleId,jdbcType=INTEGER},
      </if>
      <if test="orderPayStatus != null">
        order_pay_status = #{orderPayStatus,jdbcType=VARCHAR},
      </if>
      <if test="paymentPlatforms != null">
        payment_platforms = #{paymentPlatforms,jdbcType=VARCHAR},
      </if>
      <if test="carrierId != null">
        carrier_id = #{carrierId,jdbcType=INTEGER},
      </if>
      <if test="orderPrepayAmount != null">
        order_prepay_amount = #{orderPrepayAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderActualPayment != null">
        order_actual_payment = #{orderActualPayment,jdbcType=DECIMAL},
      </if>
      <if test="orderTotalPayment != null">
        order_total_payment = #{orderTotalPayment,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="param1 != null">
        param1 = #{param1,jdbcType=VARCHAR},
      </if>
      <if test="param2 != null">
        param2 = #{param2,jdbcType=VARCHAR},
      </if>
      <if test="param3 != null">
        param3 = #{param3,jdbcType=VARCHAR},
      </if>
      <if test="param4 != null">
        param4 = #{param4,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lz.model.TOrderPayInfo">
    update t_order_pay_info
    set code = #{code,jdbcType=VARCHAR},
        pay_method = #{payMethod,jdbcType=VARCHAR},
        order_code = #{orderCode,jdbcType=VARCHAR},
        fee_settlement_way = #{feeSettlementWay,jdbcType=VARCHAR},
        line_goods_carriage_rule_id = #{lineGoodsCarriageRuleId,jdbcType=INTEGER},
        order_pay_status = #{orderPayStatus,jdbcType=VARCHAR},
        payment_platforms = #{paymentPlatforms,jdbcType=VARCHAR},
        carrier_id = #{carrierId,jdbcType=INTEGER},
        order_prepay_amount = #{orderPrepayAmount,jdbcType=DECIMAL},
        order_actual_payment = #{orderActualPayment,jdbcType=DECIMAL},
        order_total_payment = #{orderTotalPayment,jdbcType=DECIMAL},
        remark = #{remark,jdbcType=VARCHAR},
        param1 = #{param1,jdbcType=VARCHAR},
        param2 = #{param2,jdbcType=VARCHAR},
        param3 = #{param3,jdbcType=VARCHAR},
        param4 = #{param4,jdbcType=VARCHAR},
        create_user = #{createUser,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_user = #{updateUser,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        `enable` = #{enable,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectOnePayInfoByOrderCode" resultType="com.lz.model.TOrderPayInfo">
    select
    <include refid="Base_Column_List" />
    from t_order_pay_info
    where
    order_code = #{orderCode}
    order by id desc limit 1
  </select>

  <select id="selectOrderPayInfoByCode" resultType="com.lz.model.TOrderPayInfo">
    select
        <include refid="Base_Column_List" />
    from t_order_pay_info
    where
      code = #{code}
  </select>

  <select id="selectPayInfoBySelective" resultType="com.lz.model.TOrderPayInfo">
    select
        <include refid="Base_Column_List" />
    from
         t_order_pay_info
    where
        order_code = #{orderCode}
      and order_pay_status = #{orderPayStatus}
    order by id desc limit 1
  </select>

  <select id="selectPayPlatformsUnique" resultType="java.lang.Integer">
    select count(1)
    from t_order_pay_info
    where payment_platforms = #{paymentPlatforms} and order_code in
      <foreach collection="list" item="code" index="index" open="(" close=")" separator=",">
        #{code}
      </foreach>
    group by payment_platforms
  </select>

  <update id="updatePayStatusByOrderCode">
    update
        t_order_pay_info
    set order_pay_status = #{orderPayStatus},
        update_time = now()
    where
        order_code = #{orderCode}
  </update>

  <update id="updatePackOrderOrgiOrderPayInfo">
    update
      t_order_pay_info topi
      left join t_order_pack_detail topd on topi.order_code = topd.order_code
      left join t_order_pack_info toppi on topd.pack_pay_code = toppi.code
      set
        topi.order_pay_status = #{orderPayStatus},
        topi.update_time = #{returnTime}
      where toppi.id = #{packId}
  </update>

  <update id="refundRecallbackUpdateOrderPayInfo">
    update
      t_order_pay_info
    set order_pay_status = #{orderPayStatus},
        payment_platforms = null,
        update_time = #{returnTime}
    where
      id = #{id}
  </update>

  <update id="refundRecallbackUpdatePackOrderOrgiOrderPayInfo">
    update
      t_order_pay_info topi
        left join t_order_pack_detail topd on topi.order_code = topd.order_code
        left join t_order_pack_info toppi on topd.pack_pay_code = toppi.code
    set
      topi.order_pay_status = #{orderPayStatus},
        topi.payment_platforms = null,
      topi.update_time = #{returnTime}
    where toppi.id = #{packId}
  </update>

</mapper>