<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TOrderInfoSearchMapper">

    <!--运单审核-->
    <select id="selectByYdshPage" parameterType="com.lz.vo.AppOrderSearchVO" resultType="com.lz.dto.TOrderInfoDTO">
        select
        toi.update_time updateTime,
        toi.`code`, <!--运单32CODE-->
        toi.line_goods_rel_id, <!-- 线路货物关系ID -->
        euis.org_name as orgName, <!-- 业务部 -->
        euis.real_name as orgRealName, <!-- 业务部联系人 -->
        euis.phone as orgPhone, <!-- 业务部联系人电话 -->
        eci.vehicle_number,  <!--车牌号-->
        eui.real_name, <!--司机姓名-->
        eui.phone, <!--司机手机号-->
        toi.company_id, <!-- 运单企业ID -->
        tcci.company_name, <!-- 运单企业名称 -->
        tcp.project_name, <!-- 运单项目名称 -->
        lgr.payment_process, <!-- 是否需要审核 -->
        tcpi.carrier_name, <!-- 承运方名 -->
        toi.current_carriage_unit_price, <!-- 货源单价（预估单价） -->
        toi.primary_weight, <!-- 原发重量 -->
        toi.discharge_weight, <!-- 实收重量 -->
        toi.estimate_total_fee, <!-- 发单运费 -->
        (CASE toi.pack_status WHEN '0' THEN toi.user_confirm_payment_amount WHEN '1' THEN toi.share_payment_amount END) as carriageFee,<!-- 结算运费 -->
        (CASE toi.pack_status WHEN '0' THEN toi.dispatch_fee WHEN '1' THEN toi.share_dispatch_fee END) AS dispatch_fee,  <!-- 结算调度费金额 -->
        toi.order_business_code, <!--运单自定义CODE-->
        toi.create_time, <!--运单生成日期-->
        toi.deliver_order_time,<!--运单发单日期  发单时间-->
        toi.receive_order_time,<!--运单收货日期  收单时间-->
        toi.order_finish_time, <!--运单完成时间-->
        IFNULL(toi.deliver_weight_notes_time, tosup.create_time) as deliver_weight_notes_time, <!--装货磅单时间-->
        IFNULL(toi.receive_weight_notes_time, tosdown.create_time) as receive_weight_notes_time, <!--卸货磅单时间-->
        toi.from_name, <!-- 起点   装货地-->
        toi.end_name, <!-- 终点  卸货地-->
        toi.company_entrust, <!-- 企业的委托方-->
        toi.company_client,<!-- 企业的客户-->
        toi.goods_id, <!-- 货物ID-->
        toi.goods_name,<!-- 货物名称类型-->
        toi.line_name, <!-- 线路名称 -->
        toi.total_fee as totalAmount, <!-- 总金额 -->
        toi.contract_status as contractStatusCode,
        toi.order_execute_status,
        osn.page_show_code as orderExecuteStatusValue, <!-- 运单状态 -->
        toi.order_pay_status,
        tosn.page_show_code as orderPayStatusValue, <!--付款标志 运单支付状态 -->
        toi.deliver_weight_notes_photo, <!--发货磅单-->
        toi.receive_weight_notes_photo, <!--收货磅单-->
        ifnull(dci.item_value, '未审核') as orderAudit, <!--运单审核状态-->
        oal.audit_status as auditCode, <!-- 审核状态 code -->
        oal.audit_remark, <!-- 审核意见 -->
        oal.not_pass_condition as notPassCondition, <!-- 运单审核不通过原因 -->
        eui.audit_status as driverStatus, <!--司机审核状态-->
        eci.audit_status as carStatus, <!--车辆审核状态-->
        eui.audit_opinion userAuditOpinion,
        eci.audit_opinion carAuditOpinion,
        concat("[",toi.goods_name,"]",toi.line_name) as sourceName,
        euic.real_name as carOwnerRealName, <!-- 车辆所有人 -->
        euic.phone as carOwnerRealPhone, <!-- 车辆所有人电话 -->
        agent.real_name as agentName,
        agent.phone as agentPhone,
        toi.agent_id,
        toi.user_confirm_service_fee,
        ta.nickname, <!--发单员姓名-->
        ta.account_no as nickPhone, <!--发单员电话-->
        ta1.nickname as receiverNickname, <!--收单员姓名-->
        ta1.account_no as receiverNickPhone, <!--收单员电话-->
        IFNULL(toi.deliver_weight_notes_weight,0.0000) as deliver_weight_notes_weight,
        IFNULL(toi.receive_weight_notes_weight,0.0000) as receive_weight_notes_weight,
        case
            when toid.id is null then ''
            when toid.car_audit_status ='PASSNODE' AND toid.driver_audit_status ='PASSNODE' then '审核通过'
            else '审核不通过' end as transportingAuditStatus,
        ifnull(toid.carriage_price_unit, 'DUN') as carriagePriceUnit,
        toiw.deliver_weight_notes_photo1, toiw.deliver_weight_notes_photo2, toiw.receive_weight_notes_photo1, toiw.receive_weight_notes_photo2
        FROM
        t_order_info toi
        LEFT JOIN t_account ta ON toi.deliver_order_user_id = ta.id
        LEFT JOIN t_account ta1 ON toi.receive_order_user_id = ta1.id
        LEFT JOIN t_company_info tcci ON toi.company_id = tcci.id AND tcci.`enable` = 0
        LEFT JOIN t_company_project tcp ON tcp.id = toi.company_project_id AND tcp.`enable` = 0
        LEFT JOIN t_line_goods_rel lgr ON lgr.id = toi.line_goods_rel_id AND lgr.`enable` = 0
        LEFT JOIN t_carrier_info tcpi ON toi.carrier_id = tcpi.id AND tcpi.`enable` = 0
        LEFT JOIN t_end_user_info euis ON toi.end_agent_id = euis.id AND euis.`enable` = 0
        LEFT JOIN t_end_user_info euic ON toi.end_car_owner_id = euic.id AND euic.`enable` = 0
        LEFT JOIN t_end_user_info agent on toi.agent_id = agent.id AND agent.`enable` = 0
        LEFT JOIN t_end_user_info eui ON toi.end_driver_id = eui.id AND eui.`enable` = 0
        LEFT JOIN t_end_car_info eci ON toi.vehicle_id = eci.id AND eci.`enable` = 0
        LEFT JOIN t_order_state_node osn ON toi.order_execute_status = osn.`code`
        LEFT JOIN t_order_state_node tosn ON toi.order_pay_status = tosn.`code`
        LEFT JOIN t_order_cast_changes occ ON toi.`code` = occ.order_code AND occ.data_enable = 1
        LEFT JOIN t_dic_cat_item tdci ON tdci.item_code = occ.capital_transfer_type
        left join t_dic_cat_item tdci2 ON tdci2.item_code = lgr.capital_transfer_type
        LEFT JOIN t_order_audit_log oal ON oal.order_code = toi.code
        LEFT JOIN t_order_state tosup ON tosup.state_node_value = 'S0302' AND  tosup.order_code = toi.`code` and tosup.`enable` = 0
        LEFT JOIN t_order_state tosdown ON tosdown.state_node_value = 'S0402' AND  tosdown.order_code = toi.`code` and tosdown.`enable` = 0
        LEFT JOIN t_dic_cat_item dci ON dci.item_code = oal.audit_status
        left join t_order_info_detail toid on toi.id = toid.order_id and toid.`enable` = 0
        left join t_order_info_weight toiw on toi.id = toiw.order_id
        <where>
            <if test="orderPackPage">
                AND toi.pack_status = 0
            </if>
            <if test="timeOfPaymentStart != null and timeOfPaymentStart != ''">
                and toi.order_finish_time >= #{timeOfPaymentStart}
            </if>
            <if test="timeOfPaymentEnd != null and timeOfPaymentEnd != ''">
                and toi.order_finish_time <![CDATA[<= ]]> #{timeOfPaymentEnd}
            </if>
            <if test="!isAdmin">
                <if test="!org">
                    <if test="lineGoodsRuleIds != null and lineGoodsRuleIds.size > 0">
                        and toi.line_goods_rel_id IN
                        <foreach collection="lineGoodsRuleIds" item="lgr" index="item" open="(" close=")" separator=",">
                            #{lgr}
                        </foreach>
                    </if>
                </if>
            </if>
            <!-- 支付审核员收单员是按照已分配的线路查询但是要是 t_line_goods_user_rel 的企业 -->
            <if test="companyIds != null">
                AND toi.company_id IN
                <foreach collection="companyIds" index="index" item="cid" open="(" separator="," close=")">
                    #{cid}
                </foreach>
            </if>
            <if test="companyId != null">
                AND toi.company_id = #{companyId}
            </if>
            <if test="payState != null">
                AND toi.order_pay_status IN
                <foreach collection="payState" item="pay" index="item" open="(" close=")" separator=",">
                    #{pay}
                </foreach>
            </if>
            <if test="status != null">
                AND toi.order_execute_status IN
                <foreach collection="status" index="index" item="st" open="(" close=")" separator=",">
                    #{st}
                </foreach>
            </if>
            <if test="vehicleNumber != null">
                AND eci.vehicle_number LIKE CONCAT('%',  #{vehicleNumber}, '%')
            </if>
            <if test="fromName != null">
                AND toi.from_name LIKE CONCAT('%',  #{fromName}, '%')
            </if>
            <if test="endName != null">
                AND toi.end_name LIKE CONCAT('%',  #{endName}, '%')
            </if>
            <if test="auditRemark != null">
                AND oal.audit_remark like concat('%', #{auditRemark}, '%')
            </if>
            <if test="auditStatus != null and auditStatus != 'NO'">
                AND oal.audit_status = #{auditStatus}
            </if>
            <if test="auditStatus == 'NO'">
                AND ISNULL(oal.audit_status)
            </if>
            <if test="auditsTime != null ">
                AND oal.audit_time >= #{auditsTime}
                AND oal.enable = 0
            </if>
            <if test="auditeTime != null">
                AND oal.audit_time &lt;= #{auditeTime}
                AND oal.enable = 0
            </if>
            <if test="carrierId != null">
                AND toi.carrier_id = #{carrierId}
            </if>
            <if test="lineGoodsId != null">
                AND toi.line_goods_rel_id = #{lineGoodsId}
            </if>
            <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
                and toi.line_goods_rel_id in
                <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                    #{lineGoodsRelId}
                </foreach>
            </if>
            <if test="carOwnerId != null">
                and toi.end_car_owner_id = #{carOwnerId}
            </if>
            <if test="captainId != null">
                and toi.end_car_owner_id = #{captainId}
            </if>
            <if test="goodsId != null">
                and toi.goods_id = #{goodsId}
            </if>
            <if test="companyEntrust != null">
                and toi.company_entrust like concat('%', #{companyEntrust}, '%')
            </if>
            <if test="companyClient != null">
                and toi.company_client = #{companyClient}
            </if>
            <if test="projectId != null">
                and toi.company_project_id = #{projectId}
            </if>
            <if test="endAgentId != null">
                and toi.end_agent_id = #{endAgentId}
            </if>
            <if test="agentId != null">
                AND toi.agent_id = #{agentId}
            </if>
            <if test="informationStation != null">
                AND euis.org_name = #{informationStation}
            </if>
            <if test="bizCode != null">
                AND toi.order_business_code like concat('%', #{bizCode}, '%')
            </if>
            <if test="driverName != null">
                AND eui.real_name like concat('%', #{driverName}, '%')
            </if>
            <if test="driverPhone != null">
                AND eui.phone like concat('%', #{driverPhone}, '%')
            </if>
            <if test="licensePlate != null">
                AND eci.vehicle_number like concat('%', #{licensePlate}, '%')
            </if>
            <if test="carAuditStatus != null">
                AND eci.audit_status = #{carAuditStatus}
            </if>
            <if test="origin != null">
                AND toi.from_name like concat('%', #{origin}, '%')
            </if>
            <if test="terminus != null">
                AND toi.end_name like concat('%', #{terminus}, '%')
            </if>
            <if test="fhsTime != null">
                AND toi.deliver_order_time >= #{fhsTime}
            </if>
            <if test="fheTime != null">
                AND toi.deliver_order_time &lt;= #{fheTime}
            </if>
            <if test="shsTime != null">
                AND toi.receive_order_time >= #{shsTime}
            </if>
            <if test="sheTime != null">
                AND toi.receive_order_time &lt;= #{sheTime}
            </if>
            <if test="fhbdsTime != null">
                AND (toi.deliver_weight_notes_time >= #{fhbdsTime} or tosup.create_time >= #{fhbdsTime})
            </if>
            <if test="fhbdeTime != null">
                AND (toi.deliver_weight_notes_time &lt;= #{fhbdeTime} or tosup.create_time &lt;= #{fhbdeTime})
            </if>
            <if test="shbdsTime != null">
                AND (toi.receive_weight_notes_time >= #{shbdsTime} or tosdown.create_time >= #{shbdsTime})
            </if>
            <if test="shbdeTime != null">
                AND (toi.receive_weight_notes_time &lt;= #{shbdeTime} or tosdown.create_time &lt;= #{shbdeTime})
            </if>
            <if test="inputSearch != null">
                AND (
                toi.order_business_code LIKE CONCAT('%', #{inputSearch}, '%')
                OR eci.vehicle_number LIKE CONCAT('%', #{inputSearch}, '%')
                )
            </if>
            <if test="idArray != null and idArray.size != 0 ">
                AND toi.code IN
                <foreach collection="idArray" item="orderCode" index="item" open="(" close=")" separator=",">
                    #{orderCode}
                </foreach>
            </if>
            <if test="orderUnqualified == true">
                and (toi.param5 = 1 or toi.param5 is null)
            </if>
            <!--不合格运单记录-->
            <if test="unqualifiedOrder == true">
                and toi.param5 = 0
            </if>
        </where>

            order by toi.update_time desc


    </select>

    <select id="selectByWgydPage" parameterType="com.lz.vo.AppOrderSearchVO" resultType="com.lz.dto.TOrderInfoDTO">
        select
        toi.`code`, <!--运单32CODE-->
        toi.order_business_code,<!--运单号-->
        eci.id vehicleId,<!--车辆id-->
        eci.vehicle_number,  <!--车牌号-->
        eui.id endDriverId,<!--司机id-->
        eui.real_name, <!--司机姓名-->
        eui.phone, <!--司机手机号-->
        case toi.order_create_type
        when 'SCANORDER' then '扫码发单'
        when 'HANDSEND' then '人工发单'
        when 'HANDBATCHSEND' then '人工批量发单'
        when 'TEMPLATESEND' then '模板发单'
        when 'INVITESEND' then '邀约发单'
        when 'PLANSEND' then '计划发单'
        else '货源大厅抢单'
        end as orderCreateType,<!--运单生成方式-->
        tcci.company_name,<!-- 企业名称 -->
        tcpi.carrier_name,<!-- 承运方 -->
        osn.page_show_code as orderExecuteStatusValue, <!-- 运单状态 -->
        concat("[",toi.goods_name,"]",toi.line_name) as goodsName,<!-- 货源名称-->
        tosn.page_show_code as orderPayStatusValue, <!--资金状态，付款标志 运单支付状态 -->
        toi.create_time, <!--建单时间，运单生成日期-->
        IFNULL(toi.deliver_weight_notes_time, tosup.create_time) as deliver_weight_notes_time, <!--装货（磅单）时间-->
        IFNULL(toi.receive_weight_notes_time, tosdown.create_time) as receive_weight_notes_time, <!--卸货（磅单）时间-->
        tosup.operate_time as operateDRICONTime, <!--装货签到操作时间 -->
        tosdown.operate_time as operateDRICONLOADTime, <!--卸货签到操作时间 -->
        toi.receive_order_time,<!--运单收货日期  收单时间-->
        (CASE toi.pack_status WHEN '0' THEN toi.user_confirm_payment_amount WHEN '1' THEN toi.share_payment_amount END) as carriageFee,<!-- 结算运费 -->
        toid.deduction,<!--违规扣款金额-->
        toid.illegal_order_reason<!--违规原因-->
        FROM
        t_order_info toi
        LEFT JOIN t_end_car_info eci ON toi.vehicle_id = eci.id AND eci.`enable` = 0
        LEFT JOIN t_end_user_info eui ON toi.end_driver_id = eui.id AND eui.`enable` = 0
        LEFT JOIN t_company_info tcci ON toi.company_id = tcci.id AND tcci.`enable` = 0
        LEFT JOIN t_carrier_info tcpi ON toi.carrier_id = tcpi.id AND tcpi.`enable` = 0
        LEFT JOIN t_order_state_node osn ON toi.order_execute_status = osn.`code`
        LEFT JOIN t_order_state_node tosn ON toi.order_pay_status = tosn.`code`
        LEFT JOIN t_order_info_detail toid ON toi.id = toid.order_id and toid.`enable` = 0
        LEFT JOIN t_order_audit_log oal ON toi.code = oal.order_code
        LEFT JOIN t_end_user_info euis ON toi.end_agent_id = euis.id AND euis.`enable` = 0
        LEFT JOIN t_order_state tosdown ON tosdown.state_node_value = 'S0402' AND  tosdown.order_code = toi.`code` and tosdown.`enable` = 0
        LEFT JOIN t_order_state tosup ON tosup.state_node_value = 'S0302' AND  tosup.order_code = toi.`code` and tosup.`enable` = 0
        <where>
            toid.illegal_order = '1'
            <if test="orderPackPage">
                AND toi.pack_status = 0
            </if>
            <if test="!isAdmin">
                <if test="!org">
                    <if test="lineGoodsRuleIds != null and lineGoodsRuleIds.size > 0">
                        and toi.line_goods_rel_id IN
                        <foreach collection="lineGoodsRuleIds" item="lgr" index="item" open="(" close=")" separator=",">
                            #{lgr}
                        </foreach>
                    </if>
                </if>
            </if>
            <!-- 支付审核员收单员是按照已分配的线路查询但是要是 t_line_goods_user_rel 的企业 -->
            <if test="companyIds != null">
                AND toi.company_id IN
                <foreach collection="companyIds" index="index" item="cid" open="(" separator="," close=")">
                    #{cid}
                </foreach>
            </if>
            <if test="companyId != null">
                AND toi.company_id = #{companyId}
            </if>
            <if test="payState != null">
                AND toi.order_pay_status IN
                <foreach collection="payState" item="pay" index="item" open="(" close=")" separator=",">
                    #{pay}
                </foreach>
            </if>
            <if test="status != null">
                AND toi.order_execute_status IN
                <foreach collection="status" index="index" item="st" open="(" close=")" separator=",">
                    #{st}
                </foreach>
            </if>
            <if test="vehicleNumber != null">
                AND eci.vehicle_number LIKE CONCAT('%',  #{vehicleNumber}, '%')
            </if>
            <if test="fromName != null">
                AND toi.from_name LIKE CONCAT('%',  #{fromName}, '%')
            </if>
            <if test="endName != null">
                AND toi.end_name LIKE CONCAT('%',  #{endName}, '%')
            </if>
            <if test="auditRemark != null">
                AND oal.audit_remark like concat('%', #{auditRemark}, '%')
            </if>
            <if test="auditStatus != null and auditStatus != 'NO'">
                AND oal.audit_status = #{auditStatus}
            </if>
            <if test="auditStatus == 'NO'">
                AND ISNULL(oal.audit_status)
            </if>
            <if test="auditsTime != null ">
                AND oal.audit_time >= #{auditsTime}
                AND oal.enable = 0
            </if>
            <if test="auditeTime != null">
                AND oal.audit_time &lt;= #{auditeTime}
                AND oal.enable = 0
            </if>
            <if test="carrierId != null">
                AND toi.carrier_id = #{carrierId}
            </if>
            <if test="lineGoodsId != null">
                AND toi.line_goods_rel_id = #{lineGoodsId}
            </if>
            <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
                and toi.line_goods_rel_id in
                <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                    #{lineGoodsRelId}
                </foreach>
            </if>
            <if test="carOwnerId != null">
                and toi.end_car_owner_id = #{carOwnerId}
            </if>
            <if test="captainId != null">
                and toi.end_car_owner_id = #{captainId}
            </if>
            <if test="goodsId != null">
                and toi.goods_id = #{goodsId}
            </if>
            <if test="companyEntrust != null">
                and toi.company_entrust like concat('%', #{companyEntrust}, '%')
            </if>
            <if test="companyClient != null">
                and toi.company_client = #{companyClient}
            </if>
            <if test="projectId != null">
                and toi.company_project_id = #{projectId}
            </if>
            <if test="endAgentId != null">
                and toi.end_agent_id = #{endAgentId}
            </if>
            <if test="agentId != null">
                AND toi.agent_id = #{agentId}
            </if>
            <if test="informationStation != null">
                AND euis.org_name = #{informationStation}
            </if>
            <if test="bizCode != null">
                AND toi.order_business_code like concat('%', #{bizCode}, '%')
            </if>
            <if test="driverName != null">
                AND eui.real_name like concat('%', #{driverName}, '%')
            </if>
            <if test="driverPhone != null">
                AND eui.phone like concat('%', #{driverPhone}, '%')
            </if>
            <if test="licensePlate != null">
                AND eci.vehicle_number like concat('%', #{licensePlate}, '%')
            </if>
            <if test="carAuditStatus != null">
                AND eci.audit_status = #{carAuditStatus}
            </if>
            <if test="origin != null">
                AND toi.from_name like concat('%', #{origin}, '%')
            </if>
            <if test="terminus != null">
                AND toi.end_name like concat('%', #{terminus}, '%')
            </if>
            <if test="fhsTime != null">
                AND toi.deliver_order_time >= #{fhsTime}
            </if>
            <if test="fheTime != null">
                AND toi.deliver_order_time &lt;= #{fheTime}
            </if>
            <if test="shsTime != null">
                AND toi.receive_order_time >= #{shsTime}
            </if>
            <if test="sheTime != null">
                AND toi.receive_order_time &lt;= #{sheTime}
            </if>
            <if test="fhbdsTime != null">
                AND (toi.deliver_weight_notes_time >= #{fhbdsTime} or tosup.create_time >= #{fhbdsTime})
            </if>
            <if test="fhbdeTime != null">
                AND (toi.deliver_weight_notes_time &lt;= #{fhbdeTime} or tosup.create_time &lt;= #{fhbdeTime})
            </if>
            <if test="shbdsTime != null">
                AND (toi.receive_weight_notes_time >= #{shbdsTime} or tosdown.create_time >= #{shbdsTime})
            </if>
            <if test="shbdeTime != null">
                AND (toi.receive_weight_notes_time &lt;= #{shbdeTime} or tosdown.create_time &lt;= #{shbdeTime})
            </if>
            <if test="inputSearch != null">
                AND (
                toi.order_business_code LIKE CONCAT('%', #{inputSearch}, '%')
                OR eci.vehicle_number LIKE CONCAT('%', #{inputSearch}, '%')
                )
            </if>
            <if test="idArray != null and idArray.size != 0 ">
                AND toi.code IN
                <foreach collection="idArray" item="orderCode" index="item" open="(" close=")" separator=",">
                    #{orderCode}
                </foreach>
            </if>
            <if test="orderUnqualified == true">
                and (toi.param5 = 1 or toi.param5 is null)
            </if>
            <!--不合格运单记录-->
            <if test="unqualifiedOrder == true">
                and toi.param5 = 0
            </if>
        </where>
        order by toi.update_time desc
    </select>

    <!--运单检查-->
    <select id="selectByYdjcPage" parameterType="com.lz.vo.AppOrderSearchVO" resultType="com.lz.dto.TOrderInfoDTO">
        select
        toi.code,
        toi.order_business_code,
        toi.company_id,
        toi.carrier_id,
        toi.deliver_weight_notes_photo,
        toi.receive_weight_notes_photo,
        toi.end_driver_id,
        toi.vehicle_id,
        eci.vehicle_number,
        eui.real_name,
        eui.phone,
        eui2.real_name as carOwnerRealName,
        eui2.phone as carOwnerRealPhone,
        eui3.real_name as orgRealName,
        eui3.phone as orgPhone,
        eui4.real_name as agentName,
        eui4.phone as agentPhone,
        toi.user_confirm_service_fee,<!-- 服务费 -->
        com.company_name,
        ca.carrier_name,
        toi.goods_name,
        toi.from_name,
        toi.end_name,
        case when toi.pack_status = 1 then '已打包' else '未打包' end pack_status,
        toi.order_execute_status,
        osn.page_show_code as orderExecuteStatusValue, <!-- 运单状态 -->
        toi.order_pay_status,
        tosn.page_show_code as orderPayStatusValue,<!-- 资金状态 -->
        case when tosdown.id is not null then '卸货签到'
        when tosup.id is not null then '装货签到'
        else '未签到' end signType,
        IFNULL(tosdown.id,IFNULL(tosup.id,0)) sign,<!--是否签到-->
        IF(toi.estimate_goods_weight=0, toi.primary_weight, toi.estimate_goods_weight) as estimateGoodsWeight, <!-- 货源重量 -->
        toi.current_carriage_unit_price,<!--参考单价-->
        toi.deliver_order_time,<!-- 发单时间 -->
        IFNULL(toi.deliver_weight_notes_time, tosup.create_time) as deliver_weight_notes_time, <!--装货磅单时间-->
        toi.primary_weight,<!-- 原发重量 -->
        occv.carriage_fee originalCarriageFee, <!-- 原发运费 -->
        occv.dispatch_fee as originalDispatchFee, <!-- 原发调度费 -->
        IFNULL(toi.receive_weight_notes_time, tosdown.create_time) as receive_weight_notes_time, <!--卸货磅单时间-->
        toi.discharge_weight,<!--实收重量-->
        toi.receive_order_time,<!--运单收货日期  收单时间-->
        ifnull(occs.settled_weight, 0) settled_weight,<!-- 结算重量 -->
        toi.user_confirm_payment_amount as carriageFee, <!-- 结算运费 -->
        ifnull(occs.carriage_unit_price, 0) settlementPrice,<!-- 结算运费单价 -->
        toi.dispatch_fee, <!-- 结算调度费金额 -->
        toi.total_fee, <!-- 确认总费用 -->
        case toi.order_pay_status
        when 'M060' then null
        when 'M095' then null
        else tocc.create_time
        end yfkFkTime, <!--付款时间-->
        case toi.order_pay_status
        when 'M060' then null
        when 'M095' then null
        else tocc.create_user
        end  payUser,<!--运单支付人-->
        toi.order_finish_time, <!--运单完成时间-->
        tdci.item_value as capitalTransferType, <!--资金转移方式-->
        txfx.item_value as withDrawType, <!-- 提现方式 -->
        tcp.project_name, <!-- 运单项目名称 -->
        concat("[",toi.goods_name,"]",toi.line_name) as sourceName,
        com.company_contacts, <!-- 运单企业联系人 -->
        com.company_contacts_phone, <!-- 运单企业联系人电话 -->
        ta.nickname, <!--发单员姓名-->
        ta.account_no as nickPhone, <!--发单员电话-->
        ta1.nickname as receiverNickname, <!--收单员姓名-->
        ta1.account_no as receiverNickPhone, <!--收单员电话-->
        toi.company_client,<!-- 企业的客户-->
        toi.company_entrust, <!-- 企业的委托方-->
        todl.delete_reason, <!-- 删除原因-->
        eci.audit_status as carStatus, <!-- 车审核状态 -->
        eui.audit_status as driverStatus, <!-- 司机审核状态 -->
        toi.update_time as updateTime,
        IF(tpi.id is null,'华夏', CASE tpi.payment_platforms WHEN '京东' THEN '京东' WHEN '华夏' THEN '华夏' ELSE '网商' END) paymentPlatforms,
        tgsi.id goodsSourceInfoId, tgsi.code goodsSourceCode,
        IFNULL(toi.deliver_weight_notes_weight,0.0000) as deliver_weight_notes_weight,
        IFNULL(toi.receive_weight_notes_weight,0.0000) as receive_weight_notes_weight,
        ifnull(toid.carriage_price_unit, 'DUN') as carriagePriceUnit,
        toiw.deliver_weight_notes_photo1, toiw.deliver_weight_notes_photo2, toiw.receive_weight_notes_photo1, toiw.receive_weight_notes_photo2
        FROM
        t_order_info toi
        LEFT JOIN t_end_user_info eui on eui.id = toi.end_driver_id
        LEFT JOIN t_end_user_info eui2 on eui2.id = toi.end_car_owner_id
        LEFT JOIN t_end_user_info eui3 on eui3.id = toi.end_agent_id
        LEFT JOIN t_end_user_info eui4 on eui4.id = toi.agent_id
        LEFT JOIN t_end_car_info eci on eci.id = toi.vehicle_id
        LEFT JOIN t_company_info com on com.id = toi.company_id
        LEFT JOIN t_company_project tcp ON tcp.id = toi.company_project_id
        LEFT JOIN t_carrier_info ca on ca.id = toi.carrier_id
        LEFT JOIN t_order_state_node osn ON osn.code = toi.order_execute_status
        LEFT JOIN t_order_state_node tosn ON tosn.code = toi.order_pay_status
        LEFT JOIN t_order_state tosup ON tosup.state_node_value = 'S0302' AND  tosup.order_code = toi.`code` and tosup.`enable` = 0
        LEFT JOIN t_order_state tosdown ON tosdown.state_node_value = 'S0402' AND  tosdown.order_code = toi.`code` and tosdown.`enable` = 0
        LEFT JOIN t_order_cast_calc_snapshot occs ON toi.`code` = occs.order_code and occs.data_enable = 1 and occs.enable = 0
        LEFT JOIN t_account ta ON toi.deliver_order_user_id = ta.id
        LEFT JOIN t_account ta1 ON toi.receive_order_user_id = ta1.id
        LEFT JOIN t_order_delete_log todl on toi.code = todl.order_code
        left join t_order_cast_changes tocc on ( select Max(id) as id from t_order_cast_changes where user_oper = 'PayMent' and order_code = toi.code) = tocc.id
        LEFT JOIN t_order_cast_changes occv ON toi.`code` = occv.order_code AND occv.user_oper = 'Invoice'
        left join t_goods_source_info tgsi on tgsi.line_goods_rel_id = toi.line_goods_rel_id
        LEFT JOIN t_dic_cat_item tdci ON tdci.item_code = occv.capital_transfer_type
        LEFT JOIN t_dic_cat_item txfx ON occv.withdraw_type = txfx.item_code
        LEFT JOIN t_order_pay_info tpi on tpi.order_code = toi.`code`
        LEFT JOIN t_order_info_detail toid on toid.order_id = toi.id
        left join t_order_info_weight toiw on toi.id = toiw.order_id
        where toi.order_create_type != 'RESOURCEHALLSEND'
        <if test="carrierId != null">
            AND toi.carrier_id = #{carrierId}
        </if>
        <if test="companyId != null">
            AND toi.company_id = #{companyId}
        </if>
        <if test="bizCode != null">
            AND toi.order_business_code like concat('%', #{bizCode}, '%')
        </if>
        <if test="driverName != null">
            AND eui.real_name like concat('%', #{driverName}, '%')
        </if>
        <if test="driverPhone != null">
            AND eui.phone like concat('%', #{driverPhone}, '%')
        </if>
        <if test="fromName != null">
            AND toi.from_name LIKE CONCAT('%',  #{fromName}, '%')
        </if>
        <if test="endName != null">
            AND toi.end_name LIKE CONCAT('%',  #{endName}, '%')
        </if>
        <if test="projectId != null">
            and toi.company_project_id = #{projectId}
        </if>
        <if test="lineGoodsId != null">
            AND toi.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
            and toi.line_goods_rel_id in
            <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                #{lineGoodsRelId}
            </foreach>
        </if>
        <if test="goodsId != null">
            and toi.goods_id = #{goodsId}
        </if>
        <if test="companyEntrust != null">
            and toi.company_entrust like concat('%', #{companyEntrust}, '%')
        </if>
        <if test="companyClient != null">
            and toi.company_client like concat('%', #{companyClient}, '%')
        </if>
        <if test="searchAgentId != null">
            AND toi.end_agent_id = #{searchAgentId}
        </if>
        <if test="agentId != null">
            AND toi.agent_id = #{agentId}
        </if>
        <if test="carOwnerId != null">
            AND toi.end_car_owner_id = #{carOwnerId}
        </if>
        <if test="captainId != null">
            and toi.end_car_owner_id = #{captainId}
        </if>
        <if test="orderState != null ">
            AND toi.order_execute_status IN
            <foreach collection="orderState" item="orderExecuteStatus" index="item" open="(" close=")" separator=",">
                #{orderExecuteStatus}
            </foreach>
        </if>
        <if test="payState != null">
            AND toi.order_pay_status IN
            <foreach collection="payState" item="orderPayStatus" index="item" open="(" close=")" separator=",">
                #{orderPayStatus}
            </foreach>
        </if>
        <if test="(orderState == null or orderState.length == 0) and (payState == null or payState.length == 0)">
             <if test="getDelete">
                 and toi.order_execute_status &lt;> 'M-10'
             </if>
            <if test="!org and !isAdmin">
                AND toi.order_execute_status &lt;> 'M-20'
            </if>
        </if>
        <if test="fhsTime != null">
            AND toi.deliver_order_time >= #{fhsTime}
        </if>
        <if test="fheTime != null">
            AND toi.deliver_order_time &lt;= #{fheTime}
        </if>
        <if test="shsTime != null">
            AND toi.receive_order_time >= #{shsTime}
        </if>
        <if test="sheTime != null">
            AND toi.receive_order_time &lt;= #{sheTime}
        </if>
        <if test="fhbdsTime != null">
            AND (toi.deliver_weight_notes_time >= #{fhbdsTime} or tosup.create_time >= #{fhbdsTime})
        </if>
        <if test="fhbdeTime != null">
            AND (toi.deliver_weight_notes_time &lt;= #{fhbdeTime} or tosup.create_time &lt;= #{fhbdeTime})
        </if>
        <if test="shbdsTime != null">
            AND (toi.receive_weight_notes_time >= #{shbdsTime} or tosdown.create_time >= #{shbdsTime})
        </if>
        <if test="shbdeTime != null">
            AND (toi.receive_weight_notes_time &lt;= #{shbdeTime} or tosdown.create_time &lt;= #{shbdeTime})
        </if>
        <if test="licensePlate != null">
            AND eci.vehicle_number like concat('%', #{licensePlate}, '%')
        </if>
        <if test="idArray != null and idArray.size != 0 ">
            AND toi.code IN
            <foreach collection="idArray" item="orderCode" index="item" open="(" close=")" separator=",">
                #{orderCode}
            </foreach>
        </if>

        <if test="informationStation != null">
            AND eui3.org_name = #{informationStation}
        </if>
        <if test="orderUnqualified">
            <if test="unqualifiedOrder">
                and toi.param5 = '0'
            </if>
            <if test="!unqualifiedOrder">
                and (toi.param5 = 1 or toi.param5 is null)
            </if>
        </if>
        <if test="companyIds != null and companyIds.size != 0">
            AND toi.company_id IN
            <foreach collection="companyIds" index="index" item="cid" open="(" separator="," close=")">
                #{cid}
            </foreach>
        </if>
        <if test="!isAdmin">
            <if test="!org">
                <if test="lineGoodsRuleIds != null and lineGoodsRuleIds.size > 0">
                    and toi.line_goods_rel_id IN
                    <foreach collection="lineGoodsRuleIds" item="lgr" index="item" open="(" close=")" separator=",">
                        #{lgr}
                    </foreach>
                </if>
            </if>
        </if>
        order by toi.update_time desc
    </select>

    <!--运单跟踪-->
    <select id="selectByYdgzPage" parameterType="com.lz.vo.AppOrderSearchVO" resultType="com.lz.dto.TOrderInfoDTO">
        select
        toi.code,
        toi.order_business_code,
        case when toi.pack_status = 1 then '已打包' else '未打包' end pack_status,
        toi.end_driver_id,
        toi.vehicle_id,
        eci.vehicle_number,
        eui.real_name,
        eui.phone,
        com.company_name,
        com.company_contacts, <!-- 运单企业联系人 -->
        com.company_contacts_phone, <!-- 运单企业联系人电话 -->
        ca.carrier_name,
        toi.order_execute_status,
        osn.page_show_code as orderExecuteStatusValue, <!-- 运单状态 -->
        toi.goods_name,
        toi.from_name,
        toi.end_name,
        IFNULL(if(toins.insure in (1,2) and toins.insurance_cancellation = 0,toins.insured_amount,0), 0) AS insuredAmount,
        toi.order_pay_status,
        tosn.page_show_code as orderPayStatusValue,<!-- 资金状态 -->
        eui2.real_name as carOwnerRealName,
        eui2.phone as carOwnerRealPhone,
        eui3.real_name as orgRealName,
        eui3.phone as orgPhone,
        eui4.real_name as agentName,
        eui4.phone as agentPhone,
        toi.user_confirm_service_fee,<!-- 服务费 -->
        toi.deliver_order_time,<!-- 发单时间 -->
        toi.primary_weight,<!-- 原发重量 -->
        toi.discharge_weight,<!--实收重量-->
        IFNULL(toi.settled_weight,0) settledWeight,<!-- 结算重量 -->
        IFNULL(toi.deliver_weight_notes_time, tosup.create_time) as deliver_weight_notes_time, <!--装货磅单时间-->
        IFNULL(toi.receive_weight_notes_time, tosdown.create_time) as receive_weight_notes_time, <!--卸货磅单时间-->
        toi.receive_order_time,<!--运单收货日期  收单时间-->
        toi.current_carriage_unit_price,<!-- 原发运费单价 -->
        (CASE toi.pack_status WHEN '0' THEN toi.user_confirm_payment_amount WHEN '1' THEN toi.share_payment_amount END) as carriageFee,<!-- 结算运费 -->
        ifnull(occs.carriage_unit_price, 0) settlementPrice,<!-- 结算运费单价 -->
        (CASE toi.pack_status WHEN '0' THEN toi.dispatch_fee WHEN '1' THEN toi.share_dispatch_fee END) AS dispatch_fee,  <!-- 结算调度费金额 -->
        case toi.order_pay_status
        when 'M060' then null
        when 'M095' then null
        else ifnull(tocc.create_time, toccn.create_time)
        end yfkFkTime, <!--付款时间-->
        toi.order_finish_time, <!--运单完成时间-->
        ifnull(tdci.item_value, tdci2.item_value) as capitalTransferType, <!--资金转移方式-->
        tcp.project_name, <!-- 运单项目名称 -->
        concat("[",toi.goods_name,"]",toi.line_name) as sourceName,
        ta.nickname, <!--发单员姓名-->
        ta.account_no as nickPhone, <!--发单员电话-->
        ta1.nickname as receiverNickname, <!--收单员姓名-->
        ta1.account_no as receiverNickPhone, <!--收单员电话-->
        todl.delete_reason, <!-- 删除原因-->
        eci.audit_status as carStatus, <!-- 车审核状态 -->
        eui.audit_status as driverStatus, <!-- 司机审核状态 -->
        toi.update_time as updateTime,
        IF(
            tpi.id is null,
            '',
            CASE tpi.payment_platforms
            WHEN '京东' THEN '京东'
            WHEN '华夏' THEN '华夏'
            WHEN '网商' THEN '网商'
            ELSE IFNULL(tpi.payment_platforms,
                    case tpi.param1
                    when 1 then '网商'
                    when 2 then '京东'
                    when 3 then '华夏'
                    else ''
                end)
        END) paymentPlatforms,
        IFNULL(toi.deliver_weight_notes_weight,0.0000) as deliver_weight_notes_weight,
        IFNULL(toi.receive_weight_notes_weight,0.0000) as receive_weight_notes_weight,
        occv.carriage_fee originalCarriageFee,
        occv.dispatch_fee originalDispatchFee,
        ifnull(toid.carriage_price_unit, 'DUN') as carriagePriceUnit,
        toiw.deliver_weight_notes_photo1, toiw.deliver_weight_notes_photo2, toiw.receive_weight_notes_photo1, toiw.receive_weight_notes_photo2
        FROM
        t_order_info toi
        LEFT JOIN t_end_user_info eui on eui.id = toi.end_driver_id
        LEFT JOIN t_end_user_info eui2 on eui2.id = toi.end_car_owner_id
        LEFT JOIN t_end_user_info eui3 on eui3.id = toi.end_agent_id
        LEFT JOIN t_end_user_info eui4 on eui4.id = toi.agent_id
        LEFT JOIN t_end_car_info eci on eci.id = toi.vehicle_id
        LEFT JOIN t_company_info com on com.id = toi.company_id
        LEFT JOIN t_company_project tcp ON tcp.id = toi.company_project_id
        LEFT JOIN t_carrier_info ca on ca.id = toi.carrier_id
        LEFT JOIN t_order_state_node osn ON osn.code = toi.order_execute_status
        LEFT JOIN t_order_state_node tosn ON tosn.code = toi.order_pay_status
        LEFT JOIN t_order_state tosup ON tosup.state_node_value = 'S0302' AND  tosup.order_code = toi.`code` and tosup.`enable` = 0
        LEFT JOIN t_order_state tosdown ON tosdown.state_node_value = 'S0402' AND  tosdown.order_code = toi.`code` and tosdown.`enable` = 0
        LEFT JOIN t_order_cast_calc_snapshot occs ON toi.`code` = occs.order_code and occs.data_enable = 1 and occs.enable = 0
        LEFT JOIN t_account ta ON toi.deliver_order_user_id = ta.id
        LEFT JOIN t_account ta1 ON toi.receive_order_user_id = ta1.id
        LEFT JOIN t_order_delete_log todl on toi.code = todl.order_code
        LEFT JOIN t_order_pack_detail opd ON toi.`code` = opd.order_code AND opd.`enable` = 0
        LEFT JOIN t_order_pack_info opi ON opd.pack_pay_code = opi.code AND opi.`enable` = 0
        LEFT JOIN t_bank_card tbc ON opi.bank_card_id = tbc.id
        LEFT JOIN t_end_user_info euipk ON opi.end_user_id = euipk.id
        LEFT JOIN t_order_cast_changes occ ON toi.`code` = occ.order_code AND occ.data_enable = 1
        LEFT JOIN t_line_goods_rel tlgr on toi.line_goods_rel_id = tlgr.id
        LEFT JOIN t_dic_cat_item tdci ON tdci.item_code = occ.capital_transfer_type
        LEFT JOIN t_dic_cat_item tdci2 on tlgr.capital_transfer_type = tdci2.item_code
        LEFT JOIN t_order_pay_info tpi on tpi.order_code = toi.`code`
        left join t_order_cast_changes tocc on ( select Max(id) as id from t_order_cast_changes where user_oper = 'PayMent' and order_code = toi.code) = tocc.id
        left join t_order_cast_changes toccn on ( select Max(id) as id from t_order_cast_changes where user_oper = 'WKPAYNODE' and order_code = toi.code) = toccn.id
        LEFT JOIN t_order_cast_changes occv ON toi.`code` = occv.order_code AND occv.user_oper = 'Invoice'
        left join t_order_info_detail toid on toi.id = toid.order_id
        left join t_order_info_weight toiw on toi.id = toiw.order_id
        left join t_order_insurance toins on toi.order_business_code = toins.order_business_code and toins.enable = 0
        where toi.order_create_type != 'RESOURCEHALLSEND'
        <if test="carrierId != null">
            AND toi.carrier_id = #{carrierId}
        </if>
        <if test="companyId != null">
            AND toi.company_id = #{companyId}
        </if>
        <if test="bizCode != null">
            AND toi.order_business_code like concat('%', #{bizCode}, '%')
        </if>
        <if test="driverName != null">
            AND eui.real_name like concat('%', #{driverName}, '%')
        </if>
        <if test="driverPhone != null">
            AND eui.phone like concat('%', #{driverPhone}, '%')
        </if>
        <if test="fromName != null">
            AND toi.from_name LIKE CONCAT('%',  #{fromName}, '%')
        </if>
        <if test="endName != null">
            AND toi.end_name LIKE CONCAT('%',  #{endName}, '%')
        </if>
        <if test="projectId != null">
            and toi.company_project_id = #{projectId}
        </if>
        <if test="lineGoodsId != null">
            AND toi.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
            and toi.line_goods_rel_id in
            <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                #{lineGoodsRelId}
            </foreach>
        </if>
        <if test="goodsId != null">
            and toi.goods_id = #{goodsId}
        </if>
        <if test="searchAgentId != null">
            AND toi.end_agent_id = #{searchAgentId}
        </if>
        <if test="agentId != null">
            AND toi.agent_id = #{agentId}
        </if>
        <if test="carOwnerId != null">
            AND toi.end_car_owner_id = #{carOwnerId}
        </if>
        <if test="captainId != null">
            and toi.end_car_owner_id = #{captainId}
        </if>
        <if test="orderState != null ">
            AND toi.order_execute_status IN
            <foreach collection="orderState" item="orderExecuteStatus" index="item" open="(" close=")" separator=",">
                #{orderExecuteStatus}
            </foreach>
        </if>
        <if test="payState != null">
            AND toi.order_pay_status IN
            <foreach collection="payState" item="orderPayStatus" index="item" open="(" close=")" separator=",">
                #{orderPayStatus}
            </foreach>
        </if>
        <if test="fhsTime != null">
            AND toi.deliver_order_time >= #{fhsTime}
        </if>
        <if test="fheTime != null">
            AND toi.deliver_order_time &lt;= #{fheTime}
        </if>
        <if test="shsTime != null">
            AND toi.receive_order_time >= #{shsTime}
        </if>
        <if test="sheTime != null">
            AND toi.receive_order_time &lt;= #{sheTime}
        </if>
            <if test="fhbdsTime != null">
                AND (toi.deliver_weight_notes_time >= #{fhbdsTime} or tosup.create_time >= #{fhbdsTime})
            </if>
            <if test="fhbdeTime != null">
                AND (toi.deliver_weight_notes_time &lt;= #{fhbdeTime} or tosup.create_time &lt;= #{fhbdeTime})
            </if>
            <if test="shbdsTime != null">
                AND (toi.receive_weight_notes_time >= #{shbdsTime} or tosdown.create_time >= #{shbdsTime})
            </if>
            <if test="shbdeTime != null">
                AND (toi.receive_weight_notes_time &lt;= #{shbdeTime} or tosdown.create_time &lt;= #{shbdeTime})
            </if>
        <if test="fihsTime != null">
            AND toi.order_pay_status !='M095' <!--按运单完成时间查询过滤召回状态-->
            AND toi.order_finish_time >= #{fihsTime}
        </if>
        <if test="fiheTime != null">
            AND toi.order_finish_time &lt;= #{fiheTime}
        </if>
        <if test="driverAuditStatus != null">
            AND eui.audit_status = #{driverAuditStatus}
        </if>
        <if test="carAuditStatus != null">
            AND eci.audit_status = #{carAuditStatus}
        </if>
        <if test="param1 != null and param1 != ''">
            AND tpi.param1 = #{param1}
        </if>
        <if test="licensePlate != null">
            AND eci.vehicle_number like concat('%', #{licensePlate}, '%')
        </if>
        <if test="idArray != null and idArray.size != 0 ">
            AND toi.code IN
            <foreach collection="idArray" item="orderCode" index="item" open="(" close=")" separator=",">
                #{orderCode}
            </foreach>
        </if>
        order by toi.update_time desc
    </select>

    <!--PC 端查询运单  企业只查自己的运单，  承运方运营查所有的运单 Yan-->
    <select id="selectOrderByUserType" parameterType="com.lz.vo.AppOrderSearchVO" resultType="com.lz.dto.TOrderInfoDTO">
        select
        toi.update_time updateTime,
        toi.`code`, <!--运单32CODE-->
        toi.line_goods_rel_id, <!-- 线路货物关系ID -->
        euis.org_name as orgName, <!-- 业务部 -->
        euis.real_name as orgRealName, <!-- 业务部联系人 -->
        euis.phone as orgPhone, <!-- 业务部联系人电话 -->
        eci.vehicle_number,  <!--车牌号-->
        eui.real_name, <!--司机姓名-->
        eui.phone, <!--司机手机号-->
        toi.company_id, <!-- 运单企业ID -->
        tcci.company_name, <!-- 运单企业名称 -->
        tcp.project_name, <!-- 运单项目名称 -->
        lgr.payment_process, <!-- 是否需要审核 -->
        tcci.company_contacts, <!-- 运单企业联系人 -->
        tcci.company_contacts_phone, <!-- 运单企业联系人电话 -->
        tcpi.carrier_name, <!-- 承运方名 -->
        toi.current_carriage_unit_price, <!-- 货源单价（预估单价） -->
        IF(toi.estimate_goods_weight=0, toi.primary_weight, toi.estimate_goods_weight) as estimateGoodsWeight, <!-- 货源重量 -->
        toi.primary_weight, <!-- 原发重量 -->
        toi.discharge_weight, <!-- 实收重量 -->
        toi.estimate_total_fee, <!-- 发单运费 -->
        toi.user_confirm_payment_amount as carriageFee, <!-- 结算运费 -->
        lgcr.goods_unit_price goodsUnitPrice, <!-- （计算使用的）货值单价 -->
        IFNULL(occs.carriage_unit_price, toi.current_carriage_unit_price) as carriageUnitPrice, <!--运费单价  应付单价-->
        toi.dispatch_fee, <!-- 调度费金额 -->
        toi.settled_weight, <!-- 结算重量 -->
        toi.order_business_code, <!--运单自定义CODE-->
        toi.create_time, <!--运单生成日期-->
        toi.deliver_order_time,<!--运单发单日期  发单时间-->
        toi.receive_order_time,<!--运单收货日期  收单时间-->
        toi.order_finish_time, <!--运单完成时间-->
        IFNULL(toi.deliver_weight_notes_time, tosup.create_time) as deliver_weight_notes_time, <!--装货磅单时间-->
        IFNULL(toi.receive_weight_notes_time, tosdown.create_time) as receive_weight_notes_time, <!--卸货磅单时间-->
        IFNULL(toi.deliver_weight_notes_weight,0.0000) as deliver_weight_notes_weight,
        IFNULL(toi.receive_weight_notes_weight,0.0000) as receive_weight_notes_weight,
        IFNULL(if(toi.gross_weight = '0.0000',null,toi.gross_weight),IFNULL(IFNULL(toiw.gross_weight1,0) + IFNULL(toiw.gross_weight2,0),0.0000)) as gross_weight,
        toi.from_name, <!-- 起点   装货地-->
        toi.end_name, <!-- 终点  卸货地-->
        toi.company_entrust, <!-- 企业的委托方-->
        toi.company_client,<!-- 企业的客户-->
        toi.goods_id, <!-- 货物ID-->
        toi.goods_name,<!-- 货物名称类型-->
        toi.line_name, <!-- 线路名称 -->
        toi.total_fee as totalAmount, <!-- 总金额 -->
        toi.contract_status as contractStatusCode,
        case when occs.tolerant_value_coefficient > 0 then occs.real_deliver_weight * occs.tolerant_value_coefficient
        when occs.tolerant_value_weight > 0 then occs.tolerant_value_weight else 0 end toleranceOfWeight, <!-- 容忍值 -->
        toi.user_confirm_payment_amount as rulePaymentAmount, <!-- 规则计算的应付运费金额 : 取的是快照表中的用户确认的应付运费金额-->
        occs.goods_cut_water, <!-- 扣水吨数-->
        occs.goods_cut_impurities,<!-- 扣杂吨数-->
        occs.lose_or_rise,<!-- 亏吨/涨吨   涨亏数-->
        occs.deficit_weight, <!-- 扣亏吨数-->
        occs.lose_or_rise_cut,<!-- 扣亏金额-->
        occs.carriage_zero_cut_payment, <!-- 扣零     抹零规则对应的抹零金额-->
        occs.remark as balanceRemark, <!-- 结算备注 -->
        ifnull(occs.carriage_unit_price, 0) settlementPrice,<!-- 结算单价 2019、8、9 -->
        occv.dispatch_fee as originalDispatchFee, <!-- 原发调度费 -->
        occv.carriage_fee as originalCarriageFee, <!-- 原发运费 -->
        toi.user_confirm_service_fee userConfirmServiceFee,
        (IFNULL(occs.other_cut_fee1,0) + IFNULL(occs.other_cut_fee2,0) + IFNULL(occs.other_cut_fee3,0) +
        IFNULL(occs.other_cut_fee4,0)) + IFNULL(occs.fix_cut_fee,0) as otherFeeCount, <!-- 其他扣款-->
        toi.order_execute_status,
        osn.page_show_code as orderExecuteStatusValue, <!-- 运单状态 -->
        toi.order_pay_status,
        tosn.page_show_code as orderPayStatusValue, <!--付款标志 运单支付状态 -->
        ta.nickname, <!--发单员姓名-->
        ta.account_no as nickPhone, <!--发单员电话-->
        ta1.nickname as receiverNickname, <!--收单员姓名-->
        ta1.account_no as receiverNickPhone, <!--收单员电话-->
        toi.deliver_weight_notes_photo, <!--发货磅单-->
        toi.receive_weight_notes_photo, <!--收货磅单-->
        ifnull(dci.item_value, '未审核') as orderAudit, <!--运单审核状态-->
        oal.audit_status as auditCode, <!-- 审核状态 code -->
        oal.audit_remark, <!-- 审核意见 -->
        oal.not_pass_condition as notPassCondition, <!-- 运单审核不通过原因 -->
        eui.audit_status as driverStatus, <!--司机审核状态-->
        eci.audit_status as carStatus, <!--车辆审核状态-->
        eui.audit_opinion userAuditOpinion,
        eci.audit_opinion carAuditOpinion,
        concat("[",toi.goods_name,"]",toi.line_name) as sourceName,
        euic.real_name as carOwnerRealName, <!-- 车辆所有人 -->
        euic.phone as carOwnerRealPhone, <!-- 车辆所有人电话 -->
        dci2.item_value as contractStatus,<!-- 合同签署状态 -->
        tosf.state_node_value as payChildStatus, <!-- 最新的支付子狀態 -->
        ctp.item_value AS orderCreateType, <!-- 运单生成方式 -->
        agent.real_name as agentName,
        agent.phone as agentPhone,
        toi.agent_id,
        toi.user_confirm_service_fee,
        case toi.pack_status when '0' then '未打包' when '1' then '已打包' end as pack_status,<!-- 打包状态 -->
        tdci.item_value as capitalTransferType, <!--资金转移方式-->
        todl.delete_reason, tcp.check_user_car_status,
        <!--  if(taopt.payment_platforms = '京东', '京东', if(topi.payment_platforms = '京东', '京东', '网商')) paymentPlatforms, -->
        IF(topi.id is null,'华夏', CASE topi.payment_platforms WHEN '京东' THEN '京东' WHEN '华夏' THEN '华夏' ELSE '网商' END) paymentPlatforms,
        ifnull(toid.carriage_price_unit, 'DUN') as carriagePriceUnit,
        toiw.deliver_weight_notes_photo1, toiw.deliver_weight_notes_photo2, toiw.receive_weight_notes_photo1, toiw.receive_weight_notes_photo2,
        null timeOfPayment <!--实体运单支付，无支付时间-->
        <if test="yfkPage">
            <!-- 下边是已付款运单页面多的字段 -->
            ,
            topi.order_total_payment yfkAmount, <!--付款金额-->
            toi.order_finish_time yfkFkTime, <!--付款时间-->
            '' yfkRemark <!--付款备注-->
        </if>
        FROM
        t_order_info toi
        LEFT JOIN t_company_info tcci ON toi.company_id = tcci.id AND tcci.`enable` = 0
        LEFT JOIN t_company_project tcp ON tcp.id = toi.company_project_id AND tcp.`enable` = 0
        LEFT JOIN t_line_goods_rel lgr ON lgr.id = toi.line_goods_rel_id AND lgr.`enable` = 0
        LEFT JOIN t_carrier_info tcpi ON toi.carrier_id = tcpi.id AND tcpi.`enable` = 0
        LEFT JOIN t_end_user_info euis ON toi.end_agent_id = euis.id AND euis.`enable` = 0
        LEFT JOIN t_end_user_info euic ON toi.end_car_owner_id = euic.id AND euic.`enable` = 0
        LEFT JOIN t_end_user_info agent on toi.agent_id = agent.id AND agent.`enable` = 0
        LEFT JOIN t_end_user_info eui ON toi.end_driver_id = eui.id AND eui.`enable` = 0
        LEFT JOIN t_end_car_info eci ON toi.vehicle_id = eci.id AND eci.`enable` = 0
        LEFT JOIN t_order_state_node osn ON toi.order_execute_status = osn.`code`
        LEFT JOIN t_account ta ON toi.deliver_order_user_id = ta.id
        LEFT JOIN t_account ta1 ON toi.receive_order_user_id = ta1.id
        LEFT JOIN t_order_state_node tosn ON toi.order_pay_status = tosn.`code`
        LEFT JOIN t_order_state tosf ON tosf.id = (
        SELECT
        MAX(tost.id)
        FROM t_order_state tost
        WHERE toi.`code` = tost.order_code
        )
        LEFT JOIN t_order_cast_changes occ ON toi.`code` = occ.order_code AND occ.data_enable = 1
        LEFT JOIN t_dic_cat_item tdci ON tdci.item_code = occ.capital_transfer_type
        LEFT JOIN t_order_cast_calc_snapshot occs ON toi.`code` = occs.order_code and occs.data_enable = 1
        LEFT JOIN t_line_goods_carriage_rule_detail lgcr ON toi.line_goods_rel_id = lgcr.line_goods_rel_id and lgcr.`enable`=0
        LEFT JOIN t_order_audit_log oal ON oal.order_code = toi.code
        LEFT JOIN t_order_state tosup ON tosup.state_node_value = 'S0302' AND  tosup.order_code = toi.`code` and tosup.`enable` = 0
        LEFT JOIN t_order_state tosdown ON tosdown.state_node_value = 'S0402' AND  tosdown.order_code = toi.`code` and tosdown.`enable` = 0
        LEFT JOIN t_dic_cat_item dci ON dci.item_code = oal.audit_status
        LEFT JOIN t_dic_cat_item ctp ON toi.order_create_type = ctp.item_code
        LEFT JOIN t_dic_cat_item dci2 on dci2.item_code = toi.contract_status
        left join t_order_pay_info topi on toi.code = topi.order_code
        LEFT JOIN t_order_cast_changes occv ON toi.`code` = occv.order_code AND occv.user_oper = 'Invoice'
        LEFT JOIN t_order_pack_detail opd ON toi.`code` = opd.order_code AND opd.`enable` = 0
        left join t_order_delete_log todl on toi.code = todl.order_code
        left join t_advance_order_tmp taot on taot.order_code = toi.code
        left join t_advance_order_pay_tmp taopt on taot.code = taopt.advance_code and taopt.trade_type = 'COMBALANCE_PREPAY'
        left join t_order_info_detail toid on toi.id = toid.order_id
        left join t_order_info_weight toiw on toi.id = toiw.order_id
        <!--实体运单支付菜单只查单笔支付运单-->
        <if test="state == 'PAY'">
            <if test="!ifAdvance">
                left join t_advance_order_tmp advance on advance.order_code = toi.code
            </if>
        </if>
        where toi.order_create_type != 'RESOURCEHALLSEND'
            <if test="orderPackPage">
                AND toi.pack_status = 0
            </if>
            <if test='state=="PAY"'>
                <if test="!ifAdvance">
                    and advance.order_code is null
                </if>
                and (toi.pay_method = 'SINGLEPAY' or  toi.pay_method is null or toi.pay_method ='')
            </if>
            <if test='state=="PAID"'>
                and (toi.pay_method = 'SINGLEPAY' or  toi.pay_method is null or toi.pay_method ='')
            </if>
            <if test="!isAdmin">
                <if test="!org">
                    <if test="lineGoodsRuleIds != null and lineGoodsRuleIds.size > 0">
                        and toi.line_goods_rel_id IN
                        <foreach collection="lineGoodsRuleIds" item="lgr" index="item" open="(" close=")" separator=",">
                            #{lgr}
                        </foreach>
                    </if>
                </if>
            </if>
            <!-- 支付审核员收单员是按照已分配的线路查询但是要是 t_line_goods_user_rel 的企业 -->
            <if test="companyIds != null">
                AND toi.company_id IN
                <foreach collection="companyIds" index="index" item="cid" open="(" separator="," close=")">
                    #{cid}
                </foreach>
            </if>
            <if test="companyId != null">
                AND toi.company_id = #{companyId}
            </if>
            <if test="payState != null">
                AND toi.order_pay_status IN
                <foreach collection="payState" item="pay" index="item" open="(" close=")" separator=",">
                    #{pay}
                </foreach>
                <if test='payStateNode != null and payStateNode !="" '>
                    and tosf.state_node_value = #{payStateNode}
                </if>
            </if>
            <if test="status != null">
                AND toi.order_execute_status IN
                <foreach collection="status" index="index" item="st" open="(" close=")" separator=",">
                    #{st}
                </foreach>
            </if>
            <if test="vehicleNumber != null">
                AND eci.vehicle_number LIKE CONCAT('%',  #{vehicleNumber}, '%')
            </if>
            <if test="fromName != null">
                AND toi.from_name LIKE CONCAT('%',  #{fromName}, '%')
            </if>
            <if test="endName != null">
                AND toi.end_name LIKE CONCAT('%',  #{endName}, '%')
            </if>
            <if test="auditRemark != null">
                AND oal.audit_remark like concat('%', #{auditRemark}, '%')
            </if>
            <if test="auditStatus != null and auditStatus != 'NO'">
                AND oal.audit_status = #{auditStatus}
            </if>
            <if test="auditStatus == 'NO'">
                AND ISNULL(oal.audit_status)
            </if>
            <if test="auditsTime != null ">
                AND oal.audit_time >= #{auditsTime}
                AND oal.enable = 0
            </if>
            <if test="auditeTime != null">
                AND oal.audit_time &lt;= #{auditeTime}
                AND oal.enable = 0
            </if>
            <if test="carrierId != null">
                AND toi.carrier_id = #{carrierId}
            </if>
            <if test="lineGoodsId != null">
                AND toi.line_goods_rel_id = #{lineGoodsId}
            </if>
            <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
                and toi.line_goods_rel_id in
                <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                    #{lineGoodsRelId}
                </foreach>
            </if>
            <if test="carOwnerId != null">
                and toi.end_car_owner_id = #{carOwnerId}
            </if>
            <if test="captainId != null">
                and toi.end_car_owner_id = #{captainId}
            </if>
            <if test="goodsId != null">
                and toi.goods_id = #{goodsId}
            </if>
            <if test="companyEntrust != null">
                and toi.company_entrust like concat('%', #{companyEntrust}, '%')
            </if>
            <if test="companyClient != null">
                and toi.company_client = #{companyClient}
            </if>
            <if test="projectId != null">
                and toi.company_project_id = #{projectId}
            </if>
            <if test="endAgentId != null">
                and toi.end_agent_id = #{endAgentId}
            </if>
            <if test="agentId != null">
                AND toi.agent_id = #{agentId}
            </if>
            <if test="informationStation != null">
                AND euis.org_name = #{informationStation}
            </if>
            <if test="bizCode != null">
                AND toi.order_business_code like concat('%', #{bizCode}, '%')
            </if>
            <if test="driverName != null">
                AND eui.real_name like concat('%', #{driverName}, '%')
            </if>
            <if test="driverPhone != null">
                AND eui.phone like concat('%', #{driverPhone}, '%')
            </if>
            <if test="licensePlate != null">
                AND eci.vehicle_number like concat('%', #{licensePlate}, '%')
            </if>
            <if test="carAuditStatus != null">
                AND eci.audit_status = #{carAuditStatus}
            </if>
            <if test="origin != null">
                AND toi.from_name like concat('%', #{origin}, '%')
            </if>
            <if test="terminus != null">
                AND toi.end_name like concat('%', #{terminus}, '%')
            </if>
            <if test="fhsTime != null">
                AND toi.deliver_order_time >= #{fhsTime}
            </if>
            <if test="fheTime != null">
                AND toi.deliver_order_time &lt;= #{fheTime}
            </if>
            <if test="shsTime != null">
                AND toi.receive_order_time >= #{shsTime}
            </if>
            <if test="sheTime != null">
                AND toi.receive_order_time &lt;= #{sheTime}
            </if>
            <if test="fhbdsTime != null">
                AND (toi.deliver_weight_notes_time >= #{fhbdsTime} or tosup.create_time >= #{fhbdsTime})
            </if>
            <if test="fhbdeTime != null">
                AND (toi.deliver_weight_notes_time &lt;= #{fhbdeTime} or tosup.create_time &lt;= #{fhbdeTime})
            </if>
            <if test="shbdsTime != null">
                AND (toi.receive_weight_notes_time >= #{shbdsTime} or tosdown.create_time >= #{shbdsTime})
            </if>
            <if test="shbdeTime != null">
                AND (toi.receive_weight_notes_time &lt;= #{shbdeTime} or tosdown.create_time &lt;= #{shbdeTime})
            </if>
            <if test="inputSearch != null">
                AND (
                toi.order_business_code LIKE CONCAT('%', #{inputSearch}, '%')
                OR eci.vehicle_number LIKE CONCAT('%', #{inputSearch}, '%')
                )
            </if>
            <if test="idArray != null and idArray.size != 0 ">
                AND toi.code IN
                <foreach collection="idArray" item="orderCode" index="item" open="(" close=")" separator=",">
                    #{orderCode}
                </foreach>
            </if>
            <if test="orderUnqualified == true">
                and (toi.param5 = 1 or toi.param5 is null)
            </if>
            <!--不合格运单记录-->
            <if test="unqualifiedOrder == true">
                and toi.param5 = 0
            </if>
            <if test="yfkPage">
                <if test="fksTime != null">
                    AND toi.order_finish_time >= #{fksTime}
                </if>
                <if test="fkeTime != null">
                    AND toi.order_finish_time &lt;= #{fkeTime}
                </if>
            </if>
        order by toi.update_time desc
    </select>

    <select id="getGzorderList" resultType="com.lz.dto.TOrderInfoDTO"
            parameterType="com.lz.vo.AppOrderSearchVO">
        SELECT
        toi.update_time updateTime,
        toi.`code`, <!--运单32CODE-->
        case when toi.pack_status = 1 then '已打包' else '未打包' end pack_status,
        case when tosdown.id is not null then '卸货签到'
        when tosup.id is not null then '装货签到'
        else '未签到' end signType,
        IFNULL(tosdown.id,IFNULL(tosup.id,0)) sign,
        toi.order_business_code, <!--运单自定义CODE-->
        toi.order_execute_status,
        toi.order_pay_status,
        toi.from_name, <!-- 起点   装货地-->
        toi.end_name, <!-- 终点  卸货地-->
        IF(toi.estimate_goods_weight=0, toi.primary_weight, toi.estimate_goods_weight) as estimateGoodsWeight, <!-- 货源重量 -->
        toi.current_carriage_unit_price, <!-- 货源单价（预估单价） -->
        toi.deliver_order_time,<!--运单发单日期  发单时间-->
        toi.primary_weight, <!-- 原发重量 -->
        toi.estimate_total_fee, <!-- 发单运费 -->
        toi.dispatch_fee, <!-- 调度费金额 -->
        toi.discharge_weight, <!-- 实收重量 -->
        toi.receive_order_time,<!--运单收货日期  收单时间-->
        toi.user_confirm_payment_amount as carriageFee, <!-- 结算运费 -->
        occv.dispatch_fee originalDispatchFee, <!-- 原发调度费 -->
        occv.carriage_fee originalCarriageFee, <!-- 原发运费 -->
        concat("[",toi.goods_name,"]",toi.line_name) as sourceName,
        IFNULL(toi.deliver_weight_notes_time, tosup.create_time) as deliver_weight_notes_time, <!--装货磅单时间-->
        IFNULL(toi.receive_weight_notes_time, tosdown.create_time) as receive_weight_notes_time, <!--卸货磅单时间-->
        IFNULL(toi.deliver_weight_notes_weight,0.0000) as deliver_weight_notes_weight,
        IFNULL(toi.receive_weight_notes_weight,0.0000) as receive_weight_notes_weight,
        toi.order_finish_time, <!--运单完成时间-->
        toi.goods_name,<!-- 货物名称类型-->
        toi.company_client,<!-- 企业的客户-->
        toi.company_entrust, <!-- 企业的委托方-->
        toi.total_fee, <!-- 总费用 -->
        osn.page_show_code as orderExecuteStatusValue, <!-- 运单状态 -->
        tosn.page_show_code as orderPayStatusValue, <!--付款标志 运单支付状态 -->
        ta.nickname, <!--发单员姓名-->
        ta.account_no as nickPhone, <!--发单员电话-->
        ta1.nickname as receiverNickname, <!--收单员姓名-->
        ta1.account_no as receiverNickPhone, <!--收单员电话-->
        eci.vehicle_number,  <!--车牌号-->
        eci.audit_status as carStatus, <!-- 车审核状态 -->
        eui.real_name, <!--司机姓名-->
        eui.phone, <!--司机手机号-->
        eui.audit_status as driverStatus, <!-- 司机审核状态 -->
        euic.real_name as carOwnerRealName, <!-- 车辆所有人 -->
        euic.phone as carOwnerRealPhone, <!-- 车辆所有人电话 -->
        euis.real_name as orgRealName, <!-- 经纪人 -->
        euis.phone as orgPhone, <!-- 业经纪人电话 -->
        ifnull(oal.audit_status, '未审核') as orderAudit, <!-- 审核状态 code -->
        ifnull(oal.audit_status, '') auditCode,
        ifnull(tdci.item_value, tdci2.item_value) as capitalTransferType, <!--资金转移方式-->
        txfx.item_value as withDrawType, <!-- 提现方式 -->
        tcpi.carrier_name, <!-- 承运方名 -->
        tcp.project_name, <!-- 运单项目名称 -->
        toi.company_id,
        tcci.company_name, <!-- 运单企业名称 -->
        tcci.company_contacts, <!-- 运单企业联系人 -->
        tcci.company_contacts_phone, <!-- 运单企业联系人电话 -->
        ctp.item_value AS orderCreateType, <!-- 运单生成方式 -->
        case when occs.tolerant_value_coefficient > 0 then occs.real_deliver_weight * occs.tolerant_value_coefficient
        when occs.tolerant_value_weight > 0 then occs.tolerant_value_weight else 0 end toleranceOfWeight,
        ifnull(occs.goods_cut_water, 0) goods_cut_water,
        ifnull(occs.goods_cut_impurities, 0) goods_cut_impurities,
        ifnull(occs.carriage_unit_price, 0) settlementPrice,
        ifnull(occs.lose_or_rise, 0) lose_or_rise,
        ifnull(occs.lose_or_rise_cut, 0) lose_or_rise_cut,
        ifnull(occs.deficit_weight, 0) deficit_weight,
        ifnull(occs.settled_weight, 0) settled_weight,
        ifnull(occs.carriage_zero_cut_payment, 0) carriage_zero_cut_payment,
        toi.user_confirm_payment_amount,
        toi.vehicle_id, <!-- 车辆ID -->
        toi.end_driver_id, <!-- 司机ID -->
        (
        SELECT
        GROUP_CONCAT(DISTINCT toa.abnormal_type)
        FROM
        t_order_abnormal toa
        WHERE
        toa.order_code = toi.`code`
        GROUP BY
        toa.order_code
        ) as orderAbnormal, <!-- 运单异常信息 -->
        tocc.create_time payTime,
        tocc.create_user payUser,
        ifnull(occs.fix_cut_fee, 0) + ifnull(occs.other_cut_fee1, 0)
        + ifnull(occs.other_cut_fee2, 0) + ifnull(occs.other_cut_fee3, 0) + ifnull(occs.other_cut_fee4, 0) otherFeeCount,
        toi.deliver_weight_notes_photo, toi.receive_weight_notes_photo,
        todl.delete_reason,
        tgsi.id goodsSourceInfoId, tgsi.code goodsSourceCode, toi.unqualified_remark, <!--实体运单支付，无支付时间-->
        agent.real_name as agentName,
        agent.phone as agentPhone,
        toi.agent_id,
        toi.user_confirm_service_fee,
        IF(tpi.id is null,'华夏', CASE tpi.payment_platforms WHEN '京东' THEN '京东' WHEN '华夏' THEN '华夏' ELSE '网商' END) paymentPlatforms,
        ifnull(toid.carriage_price_unit, 'DUN') as carriagePriceUnit,
        toiw.deliver_weight_notes_photo1, toiw.deliver_weight_notes_photo2, toiw.receive_weight_notes_photo1, toiw.receive_weight_notes_photo2
        FROM
        t_order_info toi
        LEFT JOIN t_order_state_node osn ON toi.order_execute_status = osn.`code`
        LEFT JOIN t_order_state_node tosn ON toi.order_pay_status = tosn.`code`
        LEFT JOIN t_account ta ON toi.deliver_order_user_id = ta.id
        LEFT JOIN t_account ta1 ON toi.receive_order_user_id = ta1.id
        LEFT JOIN t_end_car_info eci ON toi.vehicle_id = eci.id
        LEFT JOIN t_end_user_info eui ON toi.end_driver_id = eui.id
        LEFT JOIN t_end_user_info euic ON toi.end_car_owner_id = euic.id
        LEFT JOIN t_end_user_info euis ON toi.end_agent_id = euis.id
        LEFT JOIN t_end_user_info agent on toi.agent_id = agent.id
        LEFT JOIN t_order_cast_changes occ ON toi.`code` = occ.order_code AND occ.data_enable = 1
        LEFT JOIN t_dic_cat_item tdci ON tdci.item_code = occ.capital_transfer_type
        LEFT JOIN t_dic_cat_item ctp ON toi.order_create_type = ctp.item_code
        LEFT JOIN t_carrier_info tcpi ON toi.carrier_id = tcpi.id
        LEFT JOIN t_company_project tcp ON tcp.id = toi.company_project_id
        LEFT JOIN t_company_info tcci ON toi.company_id = tcci.id
        LEFT JOIN t_order_cast_calc_snapshot occs ON toi.`code` = occs.order_code and occs.data_enable = 1
        left join t_order_cast_changes tocc on ( select Max(id) as id from t_order_cast_changes where user_oper = 'PayMent' and order_code = toi.code) = tocc.id
        LEFT JOIN t_dic_cat_item txfx ON occ.withdraw_type = txfx.item_code
        LEFT JOIN t_order_pack_detail opd ON toi.`code` = opd.order_code AND opd.`enable` = 0
        LEFT JOIN t_order_pack_info opi ON opd.pack_pay_code = opi.code AND opi.`enable` = 0
        LEFT JOIN t_end_user_info euipk ON opi.end_user_id = euipk.id
        LEFT JOIN t_bank_card tbc ON opi.bank_card_id = tbc.id
        LEFT JOIN t_zt_bank_card tzbc ON opi.bank_card_id = tzbc.id
        LEFT JOIN t_order_state tosup ON tosup.state_node_value = 'S0302' AND  tosup.order_code = toi.`code` and tosup.`enable` = 0
        LEFT JOIN t_order_state tosdown ON tosdown.state_node_value = 'S0402' AND  tosdown.order_code = toi.`code` and tosdown.`enable` = 0
        LEFT JOIN t_order_cast_changes occv ON toi.`code` = occv.order_code AND occv.user_oper = 'Invoice'
        LEFT JOIN t_order_audit_log oal ON oal.order_code = toi.code
        left join t_order_delete_log todl on toi.code = todl.order_code
        left join t_goods_source_info tgsi on tgsi.line_goods_rel_id = toi.line_goods_rel_id
        left join t_order_state toss on toi.code = toss.order_code and toss.state_node_value = 'S0101'
        left join t_line_goods_rel tlgr on toi.line_goods_rel_id = tlgr.id
        left join t_dic_cat_item tdci2 on tlgr.capital_transfer_type = tdci2.item_code
        LEFT JOIN t_order_pay_info tpi on tpi.order_code = toi.`code`
        LEFT JOIN t_order_pay_detail tpdi ON tpdi.id =  ( SELECT MAX( tosc.id ) FROM t_order_pay_detail tosc WHERE tosc.order_pay_code = tpi.CODE )
        left join t_order_info_detail toid on toi.id = toid.order_id
        left join t_order_info_weight toiw on toi.id = toiw.order_id
        where toi.order_create_type != 'RESOURCEHALLSEND'
            <if test="orderPackPage">
                AND toi.pack_status = 0
            </if>
            <if test="!isAdmin">
                <if test="!org">
                    <if test="lineGoodsRuleIds != null and lineGoodsRuleIds.size > 0">
                        and toi.line_goods_rel_id IN
                        <foreach collection="lineGoodsRuleIds" item="lgr" index="item" open="(" close=")" separator=",">
                            #{lgr}
                        </foreach>
                    </if>
                </if>
            </if>
            <if test="payState != null or status != null">
                AND EXISTS (
                select distinct
                a.id
                from (
                select
                b.id
                from t_order_info b
                <where>
                    <if test="payState != null">
                        AND b.order_pay_status IN
                        <foreach collection="payState" item="pay" index="item" open="(" close=")" separator=",">
                            #{pay}
                        </foreach>
                    </if>
                    <if test="status != null">
                        AND b.order_execute_status IN
                        <foreach collection="status" index="index" item="st" open="(" close=")" separator=",">
                            #{st}
                        </foreach>
                    </if>
                </where>
                ) a
                where toi.id = a.id
                )
            </if>
            <if test="searchAgentId != null">
                AND toi.end_agent_id = #{searchAgentId}
            </if>
            <if test="companyId != null">
                AND toi.company_id = #{companyId}
            </if>
            <if test="carrierId != null">
                AND toi.carrier_id = #{carrierId}
            </if>
            <if test="fromName != null">
                AND toi.from_name LIKE CONCAT('%',  #{fromName}, '%')
            </if>
            <if test="endName != null">
                AND toi.end_name LIKE CONCAT('%',  #{endName}, '%')
            </if>
            <if test="licensePlate != null">
                AND eci.vehicle_number like concat('%', #{licensePlate}, '%')
            </if>
            <if test="carAuditStatus != null">
                AND eci.audit_status = #{carAuditStatus}
            </if>
            <if test="driverName != null">
                AND eui.real_name like concat('%', #{driverName}, '%')
            </if>
            <if test="driverPhone != null">
                AND eui.phone like concat('%', #{driverPhone}, '%')
            </if>
            <if test="driverAuditStatus != null">
                AND eui.audit_status = #{driverAuditStatus}
            </if>
            <if test="companyId != null">
                and tcci.id = #{companyId}
            </if>
            <if test="informationStation != null">
                AND euis.org_name = #{informationStation}
            </if>
            <if test="lineGoodsId != null">
                AND toi.line_goods_rel_id = #{lineGoodsId}
            </if>
            <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
                and toi.line_goods_rel_id in
                <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                    #{lineGoodsRelId}
                </foreach>
            </if>
            <if test="carOwnerId != null">
                and toi.end_car_owner_id = #{carOwnerId}
            </if>
            <if test="captainId != null">
                and toi.end_car_owner_id = #{captainId}
            </if>
            <if test="goodsId != null">
                and toi.goods_id = #{goodsId}
            </if>
            <if test="companyEntrust != null">
                and toi.company_entrust like concat('%', #{companyEntrust}, '%')
            </if>
            <if test="companyClient != null">
                and toi.company_client = #{companyClient}
            </if>
            <if test="projectId != null">
                and toi.company_project_id = #{projectId}
            </if>
            <if test="bizCode != null">
                AND toi.order_business_code like concat('%', #{bizCode}, '%')
            </if>
            <if test="origin != null">
                AND toi.from_name like concat('%', #{origin}, '%')
            </if>
            <if test="terminus != null">
                AND toi.end_name like concat('%', #{terminus}, '%')
            </if>
            <if test="fhsTime != null">
                AND toi.deliver_order_time >= #{fhsTime}
            </if>
            <if test="fheTime != null">
                AND toi.deliver_order_time &lt;= #{fheTime}
            </if>
            <if test="shsTime != null">
                AND toi.receive_order_time >= #{shsTime}
            </if>
            <if test="sheTime != null">
                AND toi.receive_order_time &lt;= #{sheTime}
            </if>
            <if test="fhbdsTime != null">
                AND (toi.deliver_weight_notes_time >= #{fhbdsTime} or tosup.create_time >= #{fhbdsTime})
            </if>
            <if test="fhbdeTime != null">
                AND (toi.deliver_weight_notes_time &lt;= #{fhbdeTime} or tosup.create_time &lt;= #{fhbdeTime})
            </if>
            <if test="shbdsTime != null">
                AND (toi.receive_weight_notes_time >= #{shbdsTime} or tosdown.create_time >= #{shbdsTime})
            </if>
            <if test="shbdeTime != null">
                AND (toi.receive_weight_notes_time &lt;= #{shbdeTime} or tosdown.create_time &lt;= #{shbdeTime})
            </if>
            <if test="fihsTime != null">
                AND toi.order_finish_time >= #{fihsTime}
            </if>
            <if test="fiheTime != null">
                AND toi.order_finish_time &lt;= #{fiheTime}
            </if>
            <if test="inputSearch != null">
                AND toi.order_business_code LIKE CONCAT('%', #{inputSearch}, '%')
            </if>
            <if test="auditStatus != null and auditStatus != 'NO'">
                AND oal.audit_status = #{auditStatus}
            </if>
            <if test="auditStatus == 'NO'">
                AND ISNULL(oal.audit_status)
            </if>
            <if test="auditPass">
                AND oal.audit_status = 'PASSNODE'
            </if>
            <if test="getDelete">
                AND toi.order_execute_status &lt;> 'M-10'
            </if>
            <if test="idArray != null and idArray.size != 0 ">
                AND toi.code IN
                <foreach collection="idArray" item="orderCode" index="item" open="(" close=")" separator=",">
                    #{orderCode}
                </foreach>
            </if>
            <if test="orderUnqualified">
                <if test="unqualifiedOrder">
                    and toi.param5 = '0'
                </if>
                <if test="!unqualifiedOrder">
                    and (toi.param5 = 1 or toi.param5 is null)
                </if>
            </if>
            <if test="companyIds != null and companyIds.size != 0">
                AND toi.company_id IN
                <foreach collection="companyIds" index="index" item="cid" open="(" separator="," close=")">
                    #{cid}
                </foreach>
            </if>
            <if test="agentId != null">
                AND toi.agent_id = #{agentId}
            </if>
        order by toi.update_time desc
    </select>

    <select id="selectTaskByOrderInfoPage" parameterType="com.lz.vo.TOrderInfoVO" resultType="com.lz.vo.TOrderInfoVO">
        SELECT
        t.id taskId,
        IFNULL( o.code, pi.code ) AS code,
        IFNULL( o.order_business_code, pi.virtual_order_no ) AS order_business_code,
        item3.item_value taskType,
        t.error_message,
        u.real_name,
        u.phone realPhone,
        c.vehicle_number,
        item.page_show_name order_execute_status,
        item2.page_show_name order_pay_status,
        o.goods_name,
        o.from_name,
        o.end_name,
        ctp.item_value AS orderCreateType,
        IFNULL( ca.carrier_name,ca1.carrier_name ) as carrier_name,
        IFNULL( com.company_name,com1.company_name ) as company_name,
        t.create_time,
        t.task_type as taskTypeCode,
        t.request_parameter,
        ifnull(u.audit_status, '') driverStatus,
        ifnull(c.audit_status, '') carStatus,
        IF(tpi.id is null,'华夏', CASE tpi.payment_platforms WHEN '京东' THEN '京东' WHEN '华夏' THEN '华夏' ELSE '网商' END) paymentPlatforms,
        ifnull(toal.audit_status, '') auditCode
        FROM
        t_task t
        LEFT JOIN t_order_info o ON t.source_fieldvalue = o.CODE
        LEFT JOIN t_order_pack_info pi ON pi.CODE = t.source_fieldvalue
        LEFT JOIN t_dic_cat_item ctp ON o.order_create_type = ctp.item_code
        LEFT JOIN t_end_user_info u ON u.id = o.end_driver_id
        LEFT JOIN t_end_car_info c ON c.id = o.vehicle_id
        LEFT JOIN t_order_state_node item ON item.CODE = o.order_execute_status
        LEFT JOIN t_order_state_node item2 ON item2.CODE = o.order_pay_status
        LEFT JOIN t_carrier_info ca ON ca.id = o.carrier_id
        LEFT JOIN t_company_info com ON com.id = o.company_id
        LEFT JOIN t_carrier_info ca1 ON ca1.id = pi.carrier_id
        LEFT JOIN t_company_info com1 ON com1.id = pi.company_id
        LEFT JOIN t_dic_cat_item item3 ON item3.item_code = t.task_type
        left join t_order_audit_log toal on o.code = toal.order_code
        LEFT JOIN t_order_pay_info tpi on tpi.order_code = o.`code`
        WHERE t.enable = 0  and t.request_times>0 and o.id is not null
        <if test="orderBusinessCode != null and orderBusinessCode != ''">
            AND o.order_business_code like '%${orderBusinessCode}%'
        </if>
        order by t.id desc
    </select>

    <select id="pcSelectOrderNoSign" parameterType="com.lz.vo.PcOrderSearchVO" resultType="com.lz.dto.NoSignContractOrderDTO">
        SELECT
        eci.vehicle_number, <!-- 车牌号 -->
        cp.project_name, <!-- 项目名称 -->
        CONCAT('[', toi.goods_name, ']', toi.line_name) as goodsSource, <!-- 货源 -->
        toi.`code`, <!-- 32业务ID -->
        toi.order_business_code, <!--运单编号-->
        tci.company_name, <!--企业名称-->
        eui.real_name, <!--司机名-->
        eui.phone, <!--司机手机号-->
        toi.deliver_order_time, <!--发单时间-->
        ctp.item_value AS orderCreateType, <!-- 运单生成类型 -->
        ctps.item_value AS contractStatus, <!-- 合同状态 -->
        osn.page_show_code as orderExecuteStatus, <!--运单执行状态-->
        toc.id as contractId, <!-- 合同ID -->
        toc.param2 as contract, <!-- 合同照片 -->
        <!-- (select abnormal_description from t_order_abnormal where order_code = toi.code and abnormal_type = 'HETONGYC' order by create_time desc limit 1) AS notPassCondition, 签合同失败原因-->
        if(toi.contract_status = 'WQD', (select abnormal_description from t_order_abnormal where order_code = toi.code and abnormal_type = 'HETONGYC' order by create_time desc limit 1), null) AS notPassCondition, <!--签合同失败原因-->
        ifnull(eui.audit_status, '') driverStatus,
        ifnull(eci.audit_status, '') carStatus
        FROM t_order_info toi
        LEFT JOIN t_company_info tci ON toi.company_id = tci.id
        LEFT JOIN t_end_user_info eui ON toi.end_driver_id = eui.id
        LEFT JOIN t_end_car_info eci ON toi.vehicle_id = eci.id
        LEFT JOIN t_company_project cp ON toi.company_project_id = cp.id
        LEFT JOIN t_order_state_node osn ON toi.order_execute_status = osn.`code`
        LEFT JOIN t_dic_cat_item ctp ON toi.order_create_type = ctp.item_code
        LEFT JOIN t_order_contract toc ON toi.code = toc.order_code AND toc.enable=0 AND toc.contract_type in ('CYHT','REPAIRHT')
        LEFT JOIN t_dic_cat_item ctps ON toi.contract_status = ctps.item_code
        WHERE
        toi.order_execute_status NOT IN ('M-20', 'M-10', 'M010')
        <if test="!initPage">
            AND toi.contract_status IN ('WQD', 'APPHTSC')
        </if>
        <if test="contractStatus.length != 0">
            AND toi.contract_status IN
            <foreach collection="contractStatus" item="cs" index="index" open="(" separator="," close=")">
                #{cs}
            </foreach>
        </if>
        <if test="license != null">
            AND toi.order_business_code like concat('%', #{license}, '%')
        </if>
        <if test="licencePlate != null">
            AND eci.vehicle_number like concat('%', #{licencePlate}, '%')
        </if>
        <if test="companyName != null">
            AND tci.company_name like concat('%', #{companyName}, '%')
        </if>
        <if test="driverName != null">
            AND eui.real_name like concat('%', #{driverName}, '%')
        </if>
        <if test="sendStartTime != null">
            AND toi.deliver_order_time >= #{sendStartTime}
        </if>
        <if test="sendEndTime != null">
            AND toi.deliver_order_time &lt;= #{sendEndTime}
        </if>
        <if test="phone != null">
            AND eui.phone like concat('%', #{phone}, '%')
        </if>
        <if test="auditState != null">
            AND toi.order_execute_status like concat('%', #{auditState}, '%')
        </if>
        ORDER BY toi.update_time DESC
    </select>
    <select id="getOrderAllPrimaryWeigh" resultType="com.lz.dto.SelectedOrderPrimaryWeightDTO">
        SELECT
        IFNULL(SUM(IFNULL(toi.primary_weight,0)), 0) as primaryWeight, <!-- 原发重量总和 -->
        IFNULL(SUM(IFNULL(toi.discharge_weight,0)), 0) as dischargeWeight, <!-- 实际卸货重量总和 -->
        IFNULL(SUM(IFNULL(toi.user_confirm_payment_amount,0)), 0) as rulePaymentAmount, <!-- 应付运费金额 -->
        IFNULL(SUM(IFNULL(toi.dispatch_fee,0)), 0) as dispatchFee, <!-- 调度费 -->
        IFNULL(SUM(IFNULL(toi.settled_weight,0)), 0) as settledWeight,  <!-- 结算重量 -->
        IFNULL(SUM(IFNULL(toi.user_confirm_service_fee,0)), 0) as userConfirmServiceFee  <!-- 服务费 -->
        FROM t_order_info toi
        <if test="auditRemark != null or (auditStatus != null and auditStatus != 'NO') or auditStatus == 'NO' or (auditeTime != null or auditsTime != null)">
        LEFT JOIN t_order_audit_log oal ON oal.order_code = toi.code
        </if>
        <if test="licensePlate != null or carAuditStatus != null">
        LEFT JOIN t_end_car_info eci ON toi.vehicle_id = eci.id
        </if>
        <if test="driverName != null or driverPhone != null">
        LEFT JOIN t_end_user_info eui ON toi.end_driver_id = eui.id AND eui.`enable` = 0
        </if>
        <!--实体运单支付菜单只查单笔支付运单-->
        <if test='payStateNode != null and payStateNode !="" '>
            LEFT JOIN t_order_state tosta ON tosta.id = (
            SELECT
            MAX(tosaa.id)
            FROM t_order_state tosaa
            WHERE   tosaa.order_code = toi.`code`
            )
        </if>
        <!--实体运单支付菜单只查单笔支付运单-->
        <if test="state == 'PAY'">
            <if test="!ifAdvance">
                left join t_advance_order_tmp advance on advance.order_code = toi.code
            </if>
        </if>
        <if test="fhbdsTime != null or fhbdeTime != null">
            LEFT JOIN t_order_state tosup ON tosup.state_node_value = 'S0302' AND  tosup.order_code = toi.`code` and tosup.`enable` = 0
        </if>
        <if test="shbdsTime != null or shbdeTime != null">
            LEFT JOIN t_order_state tosdown ON tosdown.state_node_value = 'S0402' AND  tosdown.order_code = toi.`code` and tosdown.`enable` = 0
        </if>
        <where>
            <if test="bizCode != null">
                and toi.order_business_code LIKE concat('%', #{bizCode},'%')
            </if>
            <if test="orderPackPage">
                AND toi.pack_status = 0
            </if>
            <if test="!isAdmin">
                <if test="!org">
                    <if test="lineGoodsRuleIds != null and lineGoodsRuleIds.size > 0">
                       and toi.line_goods_rel_id IN
                        <foreach collection="lineGoodsRuleIds" item="lgr" index="item" open="(" close=")" separator=",">
                            #{lgr}
                        </foreach>
                    </if>
                </if>
            </if>
            <if test="payState != null or status != null">
                <if test="payState != null">
                    AND toi.order_pay_status IN
                    <foreach collection="payState" item="pay" index="item" open="(" close=")" separator=",">
                        #{pay}
                    </foreach>
                </if>
                <if test="status != null">
                    AND toi.order_execute_status IN
                    <foreach collection="status" index="index" item="st" open="(" close=")" separator=",">
                        #{st}
                    </foreach>
                </if>
            </if>
            <if test='payStateNode != null and payStateNode !="" '>
                and tosta.state_node_value = #{payStateNode}
            </if>
            <if test="bizCode != null">
                AND toi.order_business_code like concat('%', #{bizCode}, '%')
            </if>
            <if test="driverName != null">
                AND eui.real_name like concat('%', #{driverName}, '%')
            </if>
            <if test="driverPhone != null">
                AND eui.phone like concat('%', #{driverPhone}, '%')
            </if>
            <if test="projectId != null">
                and toi.company_project_id = #{projectId}
            </if>
            <if test="lineGoodsId != null">
                AND toi.line_goods_rel_id = #{lineGoodsId}
            </if>
            <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
                and toi.line_goods_rel_id in
                <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                    #{lineGoodsRelId}
                </foreach>
            </if>
            <if test="goodsId != null">
                and toi.goods_id = #{goodsId}
            </if>
            <if test="companyEntrust != null">
                and toi.company_entrust like concat('%', #{companyEntrust}, '%')
            </if>
            <if test="companyClient != null">
                and toi.company_client = #{companyClient}
            </if>
            <if test="endAgentId != null">
                and toi.end_agent_id = #{endAgentId}
            </if>
            <if test="carOwnerId != null">
                and toi.end_car_owner_id = #{carOwnerId}
            </if>
            <if test="captainId != null">
                and toi.end_car_owner_id = #{captainId}
            </if>
            <if test="companyId != null">
                AND toi.company_id = #{companyId}
            </if>
            <if test="carrierId != null">
                AND toi.carrier_id = #{carrierId}
            </if>
            <if test="fhsTime != null">
                AND toi.deliver_order_time >= #{fhsTime}
            </if>
            <if test="fheTime != null">
                AND toi.deliver_order_time &lt;= #{fheTime}
            </if>
            <if test="shsTime != null">
                AND toi.receive_order_time >= #{shsTime}
            </if>
            <if test="sheTime != null">
                AND toi.receive_order_time &lt;= #{sheTime}
            </if>
            <if test="fihsTime != null">
                AND toi.order_finish_time >= #{fihsTime}
            </if>
            <if test="fiheTime != null">
                AND toi.order_finish_time &lt;= #{fiheTime}
            </if>
            <if test="fhbdsTime != null">
                AND (toi.deliver_weight_notes_time >= #{fhbdsTime} or tosup.create_time >= #{fhbdsTime})
            </if>
            <if test="fhbdeTime != null">
                AND (toi.deliver_weight_notes_time &lt;= #{fhbdeTime} or tosup.create_time &lt;= #{fhbdeTime})
            </if>
            <if test="shbdsTime != null">
                AND (toi.receive_weight_notes_time >= #{shbdsTime} or tosdown.create_time >= #{shbdsTime})
            </if>
            <if test="shbdeTime != null">
                AND (toi.receive_weight_notes_time &lt;= #{shbdeTime} or tosdown.create_time &lt;= #{shbdeTime})
            </if>
            <if test="agentId != null">
                AND toi.agent_id = #{agentId}
            </if>
            <if test="fksTime != null">
                AND toi.order_finish_time >= #{fksTime}
            </if>
            <if test="fkeTime != null">
                AND toi.order_finish_time &lt;= #{fkeTime}
            </if>
            <if test="orderUnqualified">
                <if test="unqualifiedOrder">
                    and toi.param5 = '0'
                </if>
                <if test="!unqualifiedOrder">
                    and (toi.param5 = 1 or toi.param5 is null)
                </if>
            </if>
            <if test="auditStatus != null and auditStatus != 'NO'">
                AND oal.audit_status = #{auditStatus}
            </if>
            <if test="auditStatus == 'NO'">
                AND ISNULL(oal.audit_status)
            </if>
            <if test="driverAuditStatus != null">
                AND eui.audit_status = #{driverAuditStatus}
            </if>
            <if test="licensePlate != null">
                AND eci.vehicle_number like concat('%', #{licensePlate}, '%')
            </if>
            <if test="carAuditStatus != null">
                AND eci.audit_status = #{carAuditStatus}
            </if>
            <!--实体运单支付菜单只查单笔支付运单-->
            <if test="state == 'PAY'">
                <if test="!ifAdvance">
                    and advance.order_code is null
                </if>
                AND (toi.pay_method = 'SINGLEPAY' or toi.pay_method is null or toi.pay_method ='')
            </if>
            <if test="state == 'YDDB'">
                AND (toi.pay_method = 'PACKPAY' or toi.pay_method is null or toi.pay_method ='')
            </if>
        </where>
    </select>

    <select id="getOrderPackList"  parameterType="com.lz.vo.AppOrderSearchVO" resultType="com.lz.dto.TOrderInfoDTO">

        SELECT
        toi.update_time updateTime,
        toi.`code`, <!--运单32CODE-->
        toi.order_business_code, <!--运单自定义CODE-->
        toi.from_name, <!-- 起点   装货地-->
        toi.end_name, <!-- 终点  卸货地-->
        toi.deliver_weight_notes_time, <!--发货磅单时间-->
        toi.primary_weight, <!-- 原发重量 -->
        ifnull(occv.carriage_fee, toi.estimate_total_fee) as originalCarriageFee, <!-- 原发运费 -->
        toi.current_carriage_unit_price, <!-- 货源单价（预估单价） -->
        toi.dispatch_fee, <!-- 调度费金额 -->
        toi.receive_weight_notes_time, <!--收货磅单时间-->
        toi.discharge_weight, <!-- 实收重量 -->
        toi.settled_weight, <!-- 结算重量 -->
        toi.goods_name,<!-- 货物名称类型-->
        toi.company_client,<!-- 企业的客户-->
        toi.company_entrust, <!-- 企业的委托方-->
        toi.deliver_order_time,
        toi.receive_order_time,
        IFNULL(toi.deliver_weight_notes_weight,0.0000) as deliver_weight_notes_weight,
        IFNULL(toi.receive_weight_notes_weight,0.0000) as receive_weight_notes_weight,
        case toi.pack_status when '0' then '未打包' when '1' then '已打包' end as pack_status,<!-- 打包状态 -->
        occs.goods_cut_water, <!-- 扣水吨数-->
        occs.goods_cut_impurities,<!-- 扣杂吨数-->
        occs.lose_or_rise,<!-- 亏吨/涨吨   涨亏数-->
        occs.deficit_weight, <!-- 扣亏吨数-->
        ifnull(occs.carriage_unit_price, 0) settlementPrice,<!-- 结算单价 2019、8、9 -->
        occs.lose_or_rise_cut,<!-- 扣亏金额-->
        occs.carriage_zero_cut_payment, <!-- 扣零     抹零规则对应的抹零金额-->
        occs.user_confirm_carriage_payment as carriageFee, <!-- 规则计算的应付运费金额 : 取的是快照表中的用户确认的应付运费金额-->
        occv.dispatch_fee as originalDispatchFee, <!-- 原发调度费 -->
        toi.user_confirm_service_fee userConfirmServiceFee,
        case when occs.tolerant_value_coefficient > 0 then occs.real_deliver_weight * occs.tolerant_value_coefficient
        when occs.tolerant_value_weight > 0 then occs.tolerant_value_weight else 0 end toleranceOfWeight, <!-- 容忍值 -->
        IFNULL(occs.fix_cut_fee+occs.other_cut_fee1+occs.other_cut_fee2+occs.other_cut_fee3+occs.other_cut_fee4,0) as otherFeeCount, <!-- 其他扣款-->
        osn.page_show_code as orderExecuteStatusValue, <!-- 运单状态 -->
        tosn.page_show_code as orderPayStatusValue, <!--付款标志 运单支付状态 -->
        concat("[",toi.goods_name,"]",toi.line_name) as sourceName, <!-- 货源名称 -->
        eci.vehicle_number,  <!--车牌号-->
        eui.real_name, <!--司机姓名-->
        eui.phone, <!--司机手机号-->
        euic.real_name as carOwnerRealName, <!-- 车辆所有人 -->
        euic.phone as carOwnerRealPhone, <!-- 车辆所有人电话 -->
        euis.real_name as orgRealName, <!-- 业务部 -->
        euis.phone as orgPhone, <!-- 业务部电话 -->
        agent.real_name as agentName,<!-- 经纪人 -->
        agent.phone as agentPhone,<!-- 经纪人电话 -->
        toi.agent_id,
        toi.user_confirm_service_fee,<!-- 用户确认服务费 -->
        tdci.item_value as capitalTransferType, <!--资金转移方式-->
        tcpi.carrier_name, <!-- 承运方名 -->
        tcci.company_name, <!-- 运单企业名称 -->
        ctp.item_value AS orderCreateType, <!-- 运单生成方式 -->
        tcp.project_name, <!-- 运单项目名称 -->
        eui.audit_opinion userAuditOpinion, eci.audit_opinion carAuditOpinion, eui.audit_status driverStatus, eci.audit_status carStatus,
        lgr.payment_process, <!-- 判断运单是否需要审核 -->
        IF(ISNULL(lgur.id), FALSE, TRUE) as payReviewPms, toi.order_pay_status orderPayStatusCode,
        case tpi.payment_platforms when '京东' then '京东' when '华夏' then '华夏' else '网商' end paymentPlatforms,
        tosf.state_node_value as payChildStatus, <!-- 最新的支付子狀態支付審核-->
        '' as timeOfPayment, <!-- 此页面无支付时间-->
        if(ifnull(toi.contract_status, false), false,
        case
        toi.contract_status
        when 'WQD' then false
        else true
        end
        ) contractFlag
        FROM
        t_order_info toi
        LEFT JOIN t_line_goods_rel lgr ON toi.line_goods_rel_id = lgr.id
        LEFT JOIN t_line_goods_user_rel lgur ON lgr.id = lgur.line_goods_rel_id AND lgur.role_code = 'PAYMENTREVIEW' AND lgur.account_info_id = #{accountId} and lgur.enable = 0
        LEFT JOIN t_line_goods_user_rel lgurs ON lgr.id = lgurs.line_goods_rel_id AND lgurs.role_code = 'PAYMENTREVIEW' AND lgurs.account_info_id = #{accountId} and lgurs.enable = 0
        LEFT JOIN t_order_cast_calc_snapshot occs ON (
        SELECT
        MAX(ost.id) as id
        FROM t_order_cast_calc_snapshot ost
        WHERE toi.`code` = ost.order_code
        ) = occs.id
        LEFT JOIN t_order_state_node osn ON toi.order_execute_status = osn.`code`
        LEFT JOIN t_order_state_node tosn ON toi.order_pay_status = tosn.`code`
        LEFT JOIN t_end_car_info eci ON toi.vehicle_id = eci.id
        LEFT JOIN t_end_user_info euis ON toi.end_agent_id = euis.id
        LEFT JOIN t_end_user_info euic ON toi.end_car_owner_id = euic.id
        LEFT JOIN t_end_user_info eui ON toi.end_driver_id = eui.id
        LEFT JOIN t_end_user_info agent on toi.agent_id = agent.id
        LEFT JOIN t_order_cast_changes occ ON toi.`code` = occ.order_code AND occ.data_enable = 1
        LEFT JOIN t_dic_cat_item tdci ON tdci.item_code = occ.capital_transfer_type
        LEFT JOIN t_carrier_info tcpi ON toi.carrier_id = tcpi.id
        LEFT JOIN t_company_info tcci ON toi.company_id = tcci.id
        LEFT JOIN t_company_project tcp ON tcp.id = toi.company_project_id
        LEFT JOIN t_dic_cat_item ctp ON toi.order_create_type = ctp.item_code
        LEFT JOIN t_order_cast_changes occv ON toi.`code` = occv.order_code AND occv.data_enable = 0 AND occv.user_oper = 'Invoice'
        LEFT JOIN t_order_pay_info tpi on tpi.order_code = toi.`code`
        LEFT JOIN t_order_state tosf ON tosf.id = (
        SELECT
        MAX(tost.id)
        FROM t_order_state tost
        WHERE toi.`code` = tost.order_code
        )
        <if test="fhbdsTime != null or fhbdeTime != null">
            LEFT JOIN t_order_state tosup ON tosup.state_node_value = 'S0302' AND  tosup.order_code = toi.`code` and tosup.`enable` = 0
        </if>
        <if test="shbdsTime != null or shbdeTime != null">
            LEFT JOIN t_order_state tosdown ON tosdown.state_node_value = 'S0402' AND  tosdown.order_code = toi.`code` and tosdown.`enable` = 0
        </if>
        <where>
            <if test="orderPackPage">
                AND toi.pack_status = 0
            </if>
            <if test="state == 'YDDB'">
                AND (toi.pay_method = 'PACKPAY' or toi.pay_method is null or toi.pay_method ='')
            </if>
            <if test="carrierId != null">
                AND toi.carrier_id=#{carrierId}
            </if>
            <if test="!isAdmin">
                <if test="!org">
                    <if test="lineGoodsRuleIds != null and lineGoodsRuleIds.size > 0">
                        and toi.line_goods_rel_id IN
                        <foreach collection="lineGoodsRuleIds" item="lgr" index="item" open="(" close=")" separator=",">
                            #{lgr}
                        </foreach>
                    </if>
                </if>
            </if>
            <if test="companyIds != null">
                AND toi.company_id in
                <foreach collection="companyIds" item="companyId" index="item" open="(" close=")" separator=",">
                    #{companyId}
                </foreach>

            </if>
            <if test="companyId != null">
                AND toi.company_id = #{companyId}
            </if>
            <if test="payState != null">
                AND toi.order_pay_status IN
                <foreach collection="payState" item="pay" index="item" open="(" close=")" separator=",">
                    #{pay}
                </foreach>
            </if>
            <if test='payStateNode != null and payStateNode !="" '>
                and tosf.state_node_value = #{payStateNode}
            </if>
            <if test="status != null">
                AND toi.order_execute_status IN
                <foreach collection="status" index="index" item="st" open="(" close=")" separator=",">
                    #{st}
                </foreach>
            </if>
            <if test="idArray != null and idArray.size != 0 ">
                AND toi.code IN
                <foreach collection="idArray" item="orderCode" index="item" open="(" close=")" separator=",">
                    #{orderCode}
                </foreach>
            </if>
            <if test="bizCode != null">
                AND toi.order_business_code like concat('%', #{bizCode}, '%')
            </if>
            <if test="projectId != null">
                AND toi.company_project_id = #{projectId}
            </if>
            <if test="driverName != null">
                AND eui.real_name like concat('%', #{driverName}, '%')
            </if>
            <if test="driverPhone != null">
                AND eui.phone like concat('%', #{driverPhone}, '%')
            </if>
            <if test="lineGoodsId != null">
                AND toi.line_goods_rel_id = #{lineGoodsId}
            </if>
            <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
                and toi.line_goods_rel_id in
                <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                    #{lineGoodsRelId}
                </foreach>
            </if>
            <if test="goodsId != null">
                and toi.goods_id = #{goodsId}
            </if>
            <if test="companyEntrust != null">
                and toi.company_entrust like concat('%', #{companyEntrust}, '%')
            </if>
            <if test="companyClient != null">
                and toi.company_client = #{companyClient}
            </if>
            <if test="licensePlate != null">
                AND eci.vehicle_number like concat('%', #{licensePlate} ,'%')
            </if>
            <if test="endAgentId != null">
                and toi.end_agent_id = #{endAgentId}
            </if>
            <if test="agentId != null">
                AND toi.agent_id = #{agentId}
            </if>
            <if test="carOwnerId != null">
                and toi.end_car_owner_id = #{carOwnerId}
            </if>
            <if test="captainId != null">
                and toi.end_car_owner_id = #{captainId}
            </if>
            <if test="fhsTime != null">
                AND toi.deliver_order_time >= #{fhsTime}
            </if>
            <if test="fheTime != null">
                AND toi.deliver_order_time &lt;= #{fheTime}
            </if>
            <if test="shsTime != null">
                AND toi.receive_order_time >= #{shsTime}
            </if>
            <if test="sheTime != null">
                AND toi.receive_order_time &lt;= #{sheTime}
            </if>
            <if test="fhbdsTime != null">
                AND (toi.deliver_weight_notes_time >= #{fhbdsTime} or tosup.create_time >= #{fhbdsTime})
            </if>
            <if test="fhbdeTime != null">
                AND (toi.deliver_weight_notes_time &lt;= #{fhbdeTime} or tosup.create_time &lt;= #{fhbdeTime})
            </if>
            <if test="shbdsTime != null">
                AND (toi.receive_weight_notes_time >= #{shbdsTime} or tosdown.create_time >= #{shbdsTime})
            </if>
            <if test="shbdeTime != null">
                AND (toi.receive_weight_notes_time &lt;= #{shbdeTime} or tosdown.create_time &lt;= #{shbdeTime})
            </if>
            <if test="inputSearch != null">
                AND (
                toi.order_business_code LIKE CONCAT('%', #{inputSearch}, '%')
                OR eci.vehicle_number LIKE CONCAT('%', #{inputSearch}, '%')
                )
            </if>
        </where>
        order by toi.id desc
    </select>

    <select id="selectPaidOrderByPage" parameterType="com.lz.vo.AppOrderSearchVO" resultType="com.lz.dto.TOrderInfoDTO">
        select
        toi.update_time updateTime,
        toi.`code`, <!--运单32CODE-->
        toi.line_goods_rel_id, <!-- 线路货物关系ID -->
        euis.org_name as orgName, <!-- 业务部 -->
        euis.real_name as orgRealName, <!-- 业务部联系人 -->
        euis.phone as orgPhone, <!-- 业务部联系人电话 -->
        eci.vehicle_number,  <!--车牌号-->
        eui.real_name, <!--司机姓名-->
        eui.phone, <!--司机手机号-->
        toi.company_id, <!-- 运单企业ID -->
        tcci.company_name, <!-- 运单企业名称 -->
        tcp.project_name, <!-- 运单项目名称 -->
        lgr.payment_process, <!-- 是否需要审核 -->
        tcci.company_contacts, <!-- 运单企业联系人 -->
        tcci.company_contacts_phone, <!-- 运单企业联系人电话 -->
        tcpi.carrier_name, <!-- 承运方名 -->
        toi.current_carriage_unit_price, <!-- 货源单价（预估单价） -->
        IF(toi.estimate_goods_weight=0, toi.primary_weight, toi.estimate_goods_weight) as estimateGoodsWeight, <!-- 货源重量 -->
        toi.primary_weight, <!-- 原发重量 -->
        toi.discharge_weight, <!-- 实收重量 -->
        toi.estimate_total_fee, <!-- 发单运费 -->
        (CASE toi.pack_status WHEN '0' THEN toi.user_confirm_payment_amount WHEN '1' THEN toi.share_payment_amount END) as carriageFee,<!-- 结算运费 -->
        (CASE toi.pack_status WHEN '0' THEN toi.dispatch_fee WHEN '1' THEN toi.share_dispatch_fee END) AS dispatch_fee,  <!-- 结算调度费金额 -->
        lgcr.goods_unit_price goodsUnitPrice, <!-- （计算使用的）货值单价 -->
        IFNULL(occs.carriage_unit_price, toi.current_carriage_unit_price) as carriageUnitPrice, <!--运费单价  应付单价-->
        toi.settled_weight, <!-- 结算重量 -->
        toi.order_business_code, <!--运单自定义CODE-->
        toi.create_time, <!--运单生成日期-->
        toi.deliver_order_time,<!--运单发单日期  发单时间-->
        toi.receive_order_time,<!--运单收货日期  收单时间-->
        toi.order_finish_time, <!--运单完成时间-->
        IFNULL(toi.deliver_weight_notes_time, tosup.create_time) as deliver_weight_notes_time, <!--装货磅单时间-->
        IFNULL(toi.receive_weight_notes_time, tosdown.create_time) as receive_weight_notes_time, <!--卸货磅单时间-->
        IFNULL(toi.deliver_weight_notes_weight,0.0000) as deliver_weight_notes_weight,
        IFNULL(toi.receive_weight_notes_weight,0.0000) as receive_weight_notes_weight,
        toi.from_name, <!-- 起点   装货地-->
        toi.end_name, <!-- 终点  卸货地-->
        toi.company_entrust, <!-- 企业的委托方-->
        toi.company_client,<!-- 企业的客户-->
        toi.goods_id, <!-- 货物ID-->
        toi.goods_name,<!-- 货物名称类型-->
        toi.line_name, <!-- 线路名称 -->
        (CASE toi.pack_status WHEN '0' THEN toi.dispatch_fee + toi.user_confirm_payment_amount WHEN '1' THEN toi.share_dispatch_fee + toi.share_payment_amount END) as totalAmount, <!-- 总金额 -->
        toi.contract_status as contractStatusCode,
        case when occs.tolerant_value_coefficient > 0 then occs.real_deliver_weight * occs.tolerant_value_coefficient
        when occs.tolerant_value_weight > 0 then occs.tolerant_value_weight else 0 end toleranceOfWeight, <!-- 容忍值 -->
        toi.user_confirm_payment_amount as rulePaymentAmount, <!-- 规则计算的应付运费金额 : 取的是快照表中的用户确认的应付运费金额-->
        occs.goods_cut_water, <!-- 扣水吨数-->
        occs.goods_cut_impurities,<!-- 扣杂吨数-->
        occs.lose_or_rise,<!-- 亏吨/涨吨   涨亏数-->
        occs.deficit_weight, <!-- 扣亏吨数-->
        occs.lose_or_rise_cut,<!-- 扣亏金额-->
        occs.carriage_zero_cut_payment, <!-- 扣零     抹零规则对应的抹零金额-->
        occs.remark as balanceRemark, <!-- 结算备注 -->
        ifnull(occs.carriage_unit_price, 0) settlementPrice,<!-- 结算单价 2019、8、9 -->
        occv.dispatch_fee as originalDispatchFee, <!-- 原发调度费 -->
        occv.carriage_fee as originalCarriageFee, <!-- 原发运费 -->
        toi.user_confirm_service_fee userConfirmServiceFee,
        (IFNULL(occs.other_cut_fee1,0) + IFNULL(occs.other_cut_fee2,0) + IFNULL(occs.other_cut_fee3,0) +
        IFNULL(occs.other_cut_fee4,0)) + IFNULL(occs.fix_cut_fee,0) as otherFeeCount, <!-- 其他扣款-->
        toi.order_execute_status,
        osn.page_show_code as orderExecuteStatusValue, <!-- 运单状态 -->
        toi.order_pay_status,
        tosn.page_show_code as orderPayStatusValue, <!--付款标志 运单支付状态 -->
        ta.nickname, <!--发单员姓名-->
        ta.account_no as nickPhone, <!--发单员电话-->
        ta1.nickname as receiverNickname, <!--收单员姓名-->
        ta1.account_no as receiverNickPhone, <!--收单员电话-->
        toi.deliver_weight_notes_photo, <!--发货磅单-->
        toi.receive_weight_notes_photo, <!--收货磅单-->
        ifnull(dci.item_value, '未审核') as orderAudit, <!--运单审核状态-->
        oal.audit_status as auditCode, <!-- 审核状态 code -->
        oal.audit_remark, <!-- 审核意见 -->
        oal.not_pass_condition as notPassCondition, <!-- 运单审核不通过原因 -->
        eui.audit_status as driverStatus, <!--司机审核状态-->
        eci.audit_status as carStatus, <!--车辆审核状态-->
        eui.audit_opinion userAuditOpinion,
        eci.audit_opinion carAuditOpinion,
        concat("[",toi.goods_name,"]",toi.line_name) as sourceName,
        euic.real_name as carOwnerRealName, <!-- 车辆所有人 -->
        euic.phone as carOwnerRealPhone, <!-- 车辆所有人电话 -->
        dci2.item_value as contractStatus,<!-- 合同签署状态 -->
        tosf.state_node_value as payChildStatus, <!-- 最新的支付子狀態 -->
        ctp.item_value AS orderCreateType, <!-- 运单生成方式 -->
        agent.real_name as agentName,
        agent.phone as agentPhone,
        toi.agent_id,
        toi.user_confirm_service_fee,
        case toi.pack_status when '0' then '未打包' when '1' then '已打包' end as pack_status,<!-- 打包状态 -->
        tdci.item_value as capitalTransferType, <!--资金转移方式-->
        todl.delete_reason, tcp.check_user_car_status,
        IF(topi.id is null,'华夏', CASE topi.payment_platforms WHEN '京东' THEN '京东' WHEN '华夏' THEN '华夏' ELSE '网商' END) paymentPlatforms,
        null timeOfPayment, <!--实体运单支付，无支付时间-->
        topi.order_total_payment yfkAmount, <!--付款金额-->
        toi.order_finish_time yfkFkTime, <!--付款时间-->
        '' yfkRemark <!--付款备注-->
        FROM
        t_order_info toi
        LEFT JOIN t_company_info tcci ON toi.company_id = tcci.id AND tcci.`enable` = 0
        LEFT JOIN t_company_project tcp ON tcp.id = toi.company_project_id AND tcp.`enable` = 0
        LEFT JOIN t_line_goods_rel lgr ON lgr.id = toi.line_goods_rel_id AND lgr.`enable` = 0
        LEFT JOIN t_carrier_info tcpi ON toi.carrier_id = tcpi.id AND tcpi.`enable` = 0
        LEFT JOIN t_end_user_info euis ON toi.end_agent_id = euis.id AND euis.`enable` = 0
        LEFT JOIN t_end_user_info euic ON toi.end_car_owner_id = euic.id AND euic.`enable` = 0
        LEFT JOIN t_end_user_info agent on toi.agent_id = agent.id AND agent.`enable` = 0
        LEFT JOIN t_end_user_info eui ON toi.end_driver_id = eui.id AND eui.`enable` = 0
        LEFT JOIN t_end_car_info eci ON toi.vehicle_id = eci.id AND eci.`enable` = 0
        LEFT JOIN t_order_state_node osn ON toi.order_execute_status = osn.`code`
        LEFT JOIN t_account ta ON toi.deliver_order_user_id = ta.id
        LEFT JOIN t_account ta1 ON toi.receive_order_user_id = ta1.id
        LEFT JOIN t_order_state_node tosn ON toi.order_pay_status = tosn.`code`
        LEFT JOIN t_order_state tosf ON tosf.id = (
        SELECT
        MAX(tost.id)
        FROM t_order_state tost
        WHERE toi.`code` = tost.order_code
        )
        LEFT JOIN t_order_cast_changes occ ON toi.`code` = occ.order_code AND occ.data_enable = 1
        LEFT JOIN t_dic_cat_item tdci ON tdci.item_code = occ.capital_transfer_type
        LEFT JOIN t_order_cast_calc_snapshot occs ON toi.`code` = occs.order_code and occs.data_enable = 1
        LEFT JOIN t_line_goods_carriage_rule_detail lgcr ON toi.line_goods_rel_id = lgcr.line_goods_rel_id and lgcr.`enable`=0
        LEFT JOIN t_order_audit_log oal ON oal.order_code = toi.code
        LEFT JOIN t_order_state tosup ON tosup.state_node_value = 'S0302' AND  tosup.order_code = toi.`code` and tosup.`enable` = 0
        LEFT JOIN t_order_state tosdown ON tosdown.state_node_value = 'S0402' AND  tosdown.order_code = toi.`code` and tosdown.`enable` = 0
        LEFT JOIN t_dic_cat_item dci ON dci.item_code = oal.audit_status
        LEFT JOIN t_dic_cat_item ctp ON toi.order_create_type = ctp.item_code
        LEFT JOIN t_dic_cat_item dci2 on dci2.item_code = toi.contract_status
        left join t_order_pay_info topi on toi.code = topi.order_code
        LEFT JOIN t_order_cast_changes occv ON toi.`code` = occv.order_code AND occv.user_oper = 'Invoice'
        LEFT JOIN t_order_pack_detail opd ON toi.`code` = opd.order_code AND opd.`enable` = 0
        left join t_order_delete_log todl on toi.code = todl.order_code
        where
            toi.order_pay_status = 'M090' and eui.audit_status = 'PASSNODE' and eci.audit_status = 'PASSNODE'
            <if test="companyId != null">
                AND toi.company_id = #{companyId}
            </if>
            <if test="vehicleNumber != null">
                AND eci.vehicle_number LIKE CONCAT('%',  #{vehicleNumber}, '%')
            </if>
            <if test="fromName != null">
                AND toi.from_name LIKE CONCAT('%',  #{fromName}, '%')
            </if>
            <if test="endName != null">
                AND toi.end_name LIKE CONCAT('%',  #{endName}, '%')
            </if>
            <if test="auditRemark != null">
                AND oal.audit_remark like concat('%', #{auditRemark}, '%')
            </if>
            <if test="auditStatus != null and auditStatus != 'NO'">
                AND oal.audit_status = #{auditStatus}
            </if>
            <if test="auditStatus == 'NO'">
                AND ISNULL(oal.audit_status)
            </if>
            <if test="auditsTime != null ">
                AND oal.audit_time >= #{auditsTime}
                AND oal.enable = 0
            </if>
            <if test="auditeTime != null">
                AND oal.audit_time &lt;= #{auditeTime}
                AND oal.enable = 0
            </if>
            <if test="carrierId != null">
                AND toi.carrier_id = #{carrierId}
            </if>
            <if test="lineGoodsId != null">
                AND toi.line_goods_rel_id = #{lineGoodsId}
            </if>
            <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
                and toi.line_goods_rel_id in
                <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                    #{lineGoodsRelId}
                </foreach>
            </if>
            <if test="carOwnerId != null">
                and toi.end_car_owner_id = #{carOwnerId}
            </if>
            <if test="captainId != null">
                and toi.end_car_owner_id = #{captainId}
            </if>
            <if test="goodsId != null">
                and toi.goods_id = #{goodsId}
            </if>
            <if test="companyEntrust != null">
                and toi.company_entrust like concat('%', #{companyEntrust}, '%')
            </if>
            <if test="companyClient != null">
                and toi.company_client = #{companyClient}
            </if>
            <if test="projectId != null">
                and toi.company_project_id = #{projectId}
            </if>
            <if test="endAgentId != null">
                and toi.end_agent_id = #{endAgentId}
            </if>
            <if test="agentId != null">
                AND toi.agent_id = #{agentId}
            </if>
            <if test="informationStation != null">
                AND euis.org_name = #{informationStation}
            </if>
            <if test="bizCode != null">
                AND toi.order_business_code like concat('%', #{bizCode}, '%')
            </if>
            <if test="driverName != null">
                AND eui.real_name like concat('%', #{driverName}, '%')
            </if>
            <if test="driverPhone != null">
                AND eui.phone like concat('%', #{driverPhone}, '%')
            </if>
            <if test="licensePlate != null">
                AND eci.vehicle_number like concat('%', #{licensePlate}, '%')
            </if>
            <if test="carAuditStatus != null">
                AND eci.audit_status = #{carAuditStatus}
            </if>
            <if test="origin != null">
                AND toi.from_name like concat('%', #{origin}, '%')
            </if>
            <if test="terminus != null">
                AND toi.end_name like concat('%', #{terminus}, '%')
            </if>
            <if test="fhsTime != null">
                AND toi.deliver_order_time >= #{fhsTime}
            </if>
            <if test="fheTime != null">
                AND toi.deliver_order_time &lt;= #{fheTime}
            </if>
            <if test="shsTime != null">
                AND toi.receive_order_time >= #{shsTime}
            </if>
            <if test="sheTime != null">
                AND toi.receive_order_time &lt;= #{sheTime}
            </if>
            <if test="fhbdsTime != null">
                AND (toi.deliver_weight_notes_time >= #{fhbdsTime} or tosup.create_time >= #{fhbdsTime})
            </if>
            <if test="fhbdeTime != null">
                AND (toi.deliver_weight_notes_time &lt;= #{fhbdeTime} or tosup.create_time &lt;= #{fhbdeTime})
            </if>
            <if test="shbdsTime != null">
                AND (toi.receive_weight_notes_time >= #{shbdsTime} or tosdown.create_time >= #{shbdsTime})
            </if>
            <if test="shbdeTime != null">
                AND (toi.receive_weight_notes_time &lt;= #{shbdeTime} or tosdown.create_time &lt;= #{shbdeTime})
            </if>
            <if test="inputSearch != null">
                AND (
                toi.order_business_code LIKE CONCAT('%', #{inputSearch}, '%')
                OR eci.vehicle_number LIKE CONCAT('%', #{inputSearch}, '%')
                )
            </if>
            <if test="fksTime != null">
                AND toi.order_finish_time >= #{fksTime}
            </if>
            <if test="fkeTime != null">
                AND toi.order_finish_time &lt;= #{fkeTime}
            </if>
        order by toi.update_time desc
    </select>

    <select id="selectPaidOrderByPageWuyang" parameterType="com.lz.vo.AppOrderSearchVO" resultType="com.lz.dto.TOrderInfoDTO">
        select t.* from (
            select
            toi.update_time updateTime,
            toi.`code`, <!--运单32CODE-->
            toi.line_goods_rel_id, <!-- 线路货物关系ID -->
            euis.org_name as orgName, <!-- 业务部 -->
            euis.real_name as orgRealName, <!-- 业务部联系人 -->
            euis.phone as orgPhone, <!-- 业务部联系人电话 -->
            eci.vehicle_number,  <!--车牌号-->
            eui.real_name, <!--司机姓名-->
            eui.phone, <!--司机手机号-->
            toi.company_id, <!-- 运单企业ID -->
            tcci.company_name, <!-- 运单企业名称 -->
            tcp.project_name, <!-- 运单项目名称 -->
            lgr.payment_process, <!-- 是否需要审核 -->
            tcci.company_contacts, <!-- 运单企业联系人 -->
            tcci.company_contacts_phone, <!-- 运单企业联系人电话 -->
            tcpi.carrier_name, <!-- 承运方名 -->
            toi.current_carriage_unit_price, <!-- 货源单价（预估单价） -->
            IF(toi.estimate_goods_weight=0, toi.primary_weight, toi.estimate_goods_weight) as estimateGoodsWeight, <!-- 货源重量 -->
            toi.primary_weight, <!-- 原发重量 -->
            toi.discharge_weight, <!-- 实收重量 -->
            toi.estimate_total_fee, <!-- 发单运费 -->
            (CASE toi.pack_status WHEN '0' THEN toi.user_confirm_payment_amount WHEN '1' THEN toi.share_payment_amount END) as carriageFee,<!-- 结算运费 -->
            (CASE toi.pack_status WHEN '0' THEN toi.dispatch_fee WHEN '1' THEN toi.share_dispatch_fee END) AS dispatch_fee,  <!-- 结算调度费金额 -->
            lgcr.goods_unit_price goodsUnitPrice, <!-- （计算使用的）货值单价 -->
            IFNULL(occs.carriage_unit_price, toi.current_carriage_unit_price) as carriageUnitPrice, <!--运费单价  应付单价-->
            toi.settled_weight, <!-- 结算重量 -->
            toi.order_business_code, <!--运单自定义CODE-->
            toi.create_time, <!--运单生成日期-->
            toi.deliver_order_time,<!--运单发单日期  发单时间-->
            toi.receive_order_time,<!--运单收货日期  收单时间-->
            toi.order_finish_time, <!--运单完成时间-->
            IFNULL(toi.deliver_weight_notes_time, tosup.create_time) as deliver_weight_notes_time, <!--装货磅单时间-->
            IFNULL(toi.receive_weight_notes_time, tosdown.create_time) as receive_weight_notes_time, <!--卸货磅单时间-->
            IFNULL(toi.deliver_weight_notes_weight,0.0000) as deliver_weight_notes_weight,
            IFNULL(toi.receive_weight_notes_weight,0.0000) as receive_weight_notes_weight,
            toi.from_name, <!-- 起点   装货地-->
            toi.end_name, <!-- 终点  卸货地-->
            toi.company_entrust, <!-- 企业的委托方-->
            toi.company_client,<!-- 企业的客户-->
            toi.goods_id, <!-- 货物ID-->
            toi.goods_name,<!-- 货物名称类型-->
            toi.line_name, <!-- 线路名称 -->
            (CASE toi.pack_status WHEN '0' THEN toi.dispatch_fee + toi.user_confirm_payment_amount WHEN '1' THEN toi.share_dispatch_fee + toi.share_payment_amount END) as totalAmount, <!-- 总金额 -->
            toi.contract_status as contractStatusCode,
            case when occs.tolerant_value_coefficient > 0 then occs.real_deliver_weight * occs.tolerant_value_coefficient
            when occs.tolerant_value_weight > 0 then occs.tolerant_value_weight else 0 end toleranceOfWeight, <!-- 容忍值 -->
            toi.user_confirm_payment_amount as rulePaymentAmount, <!-- 规则计算的应付运费金额 : 取的是快照表中的用户确认的应付运费金额-->
            occs.goods_cut_water, <!-- 扣水吨数-->
            occs.goods_cut_impurities,<!-- 扣杂吨数-->
            occs.lose_or_rise,<!-- 亏吨/涨吨   涨亏数-->
            occs.deficit_weight, <!-- 扣亏吨数-->
            occs.lose_or_rise_cut,<!-- 扣亏金额-->
            occs.carriage_zero_cut_payment, <!-- 扣零     抹零规则对应的抹零金额-->
            occs.remark as balanceRemark, <!-- 结算备注 -->
            ifnull(occs.carriage_unit_price, 0) settlementPrice,<!-- 结算单价 2019、8、9 -->
            occv.dispatch_fee as originalDispatchFee, <!-- 原发调度费 -->
            occv.carriage_fee as originalCarriageFee, <!-- 原发运费 -->
            toi.user_confirm_service_fee userConfirmServiceFee,
            (IFNULL(occs.other_cut_fee1,0) + IFNULL(occs.other_cut_fee2,0) + IFNULL(occs.other_cut_fee3,0) +
            IFNULL(occs.other_cut_fee4,0)) + IFNULL(occs.fix_cut_fee,0) as otherFeeCount, <!-- 其他扣款-->
            toi.order_execute_status,
            osn.page_show_code as orderExecuteStatusValue, <!-- 运单状态 -->
            toi.order_pay_status,
            tosn.page_show_code as orderPayStatusValue, <!--付款标志 运单支付状态 -->
            ta.nickname, <!--发单员姓名-->
            ta.account_no as nickPhone, <!--发单员电话-->
            ta1.nickname as receiverNickname, <!--收单员姓名-->
            ta1.account_no as receiverNickPhone, <!--收单员电话-->
            toi.deliver_weight_notes_photo, <!--发货磅单-->
            toi.receive_weight_notes_photo, <!--收货磅单-->
            ifnull(dci.item_value, '未审核') as orderAudit, <!--运单审核状态-->
            oal.audit_status as auditCode, <!-- 审核状态 code -->
            oal.audit_remark, <!-- 审核意见 -->
            oal.not_pass_condition as notPassCondition, <!-- 运单审核不通过原因 -->
            eui.audit_status as driverStatus, <!--司机审核状态-->
            eci.audit_status as carStatus, <!--车辆审核状态-->
            eui.audit_opinion userAuditOpinion,
            eci.audit_opinion carAuditOpinion,
            concat("[",toi.goods_name,"]",toi.line_name) as sourceName,
            euic.real_name as carOwnerRealName, <!-- 车辆所有人 -->
            euic.phone as carOwnerRealPhone, <!-- 车辆所有人电话 -->
            dci2.item_value as contractStatus,<!-- 合同签署状态 -->
            tosf.state_node_value as payChildStatus, <!-- 最新的支付子狀態 -->
            ctp.item_value AS orderCreateType, <!-- 运单生成方式 -->
            agent.real_name as agentName,
            agent.phone as agentPhone,
            toi.agent_id,
            toi.user_confirm_service_fee,
            case toi.pack_status when '0' then '未打包' when '1' then '已打包' end as pack_status,<!-- 打包状态 -->
            tdci.item_value as capitalTransferType, <!--资金转移方式-->
            todl.delete_reason, tcp.check_user_car_status,
            IF(topi.id is null,'华夏', CASE topi.payment_platforms WHEN '京东' THEN '京东' WHEN '华夏' THEN '华夏' ELSE '网商' END) paymentPlatforms,
            null timeOfPayment, <!--实体运单支付，无支付时间-->
            topi.order_total_payment yfkAmount, <!--付款金额-->
            toi.order_finish_time yfkFkTime, <!--付款时间-->
            '' yfkRemark, <!--付款备注-->
            IFNULL( toi.deliver_weight_notes_time, IFNULL(tosup.create_time,'2000-01-01 00:00:00') ) AS start_time,
            IFNULL( toi.receive_weight_notes_time, IFNULL(tosdown.create_time,'2099-12-31 00:00:00') ) AS end_time
            FROM
            t_order_info toi
            LEFT JOIN t_company_info tcci ON toi.company_id = tcci.id AND tcci.`enable` = 0
            LEFT JOIN t_company_project tcp ON tcp.id = toi.company_project_id AND tcp.`enable` = 0
            LEFT JOIN t_line_goods_rel lgr ON lgr.id = toi.line_goods_rel_id AND lgr.`enable` = 0
            LEFT JOIN t_carrier_info tcpi ON toi.carrier_id = tcpi.id AND tcpi.`enable` = 0
            LEFT JOIN t_end_user_info euis ON toi.end_agent_id = euis.id AND euis.`enable` = 0
            LEFT JOIN t_end_user_info euic ON toi.end_car_owner_id = euic.id AND euic.`enable` = 0
            LEFT JOIN t_end_user_info agent on toi.agent_id = agent.id AND agent.`enable` = 0
            LEFT JOIN t_end_user_info eui ON toi.end_driver_id = eui.id AND eui.`enable` = 0
            LEFT JOIN t_end_car_info eci ON toi.vehicle_id = eci.id AND eci.`enable` = 0
            LEFT JOIN t_order_state_node osn ON toi.order_execute_status = osn.`code`
            LEFT JOIN t_account ta ON toi.deliver_order_user_id = ta.id
            LEFT JOIN t_account ta1 ON toi.receive_order_user_id = ta1.id
            LEFT JOIN t_order_state_node tosn ON toi.order_pay_status = tosn.`code`
            LEFT JOIN t_order_state tosf ON tosf.id = (
            SELECT
            MAX(tost.id)
            FROM t_order_state tost
            WHERE toi.`code` = tost.order_code
            )
            LEFT JOIN t_order_cast_changes occ ON toi.`code` = occ.order_code AND occ.data_enable = 1
            LEFT JOIN t_dic_cat_item tdci ON tdci.item_code = occ.capital_transfer_type
            LEFT JOIN t_order_cast_calc_snapshot occs ON toi.`code` = occs.order_code and occs.data_enable = 1
            LEFT JOIN t_line_goods_carriage_rule_detail lgcr ON toi.line_goods_rel_id = lgcr.line_goods_rel_id and lgcr.`enable`=0
            LEFT JOIN t_order_audit_log oal ON oal.order_code = toi.code
            LEFT JOIN t_order_state tosup ON tosup.state_node_value = 'S0302' AND  tosup.order_code = toi.`code` and tosup.`enable` = 0
            LEFT JOIN t_order_state tosdown ON tosdown.state_node_value = 'S0402' AND  tosdown.order_code = toi.`code` and tosdown.`enable` = 0
            LEFT JOIN t_dic_cat_item dci ON dci.item_code = oal.audit_status
            LEFT JOIN t_dic_cat_item ctp ON toi.order_create_type = ctp.item_code
            LEFT JOIN t_dic_cat_item dci2 on dci2.item_code = toi.contract_status
            left join t_order_pay_info topi on toi.code = topi.order_code
            LEFT JOIN t_order_cast_changes occv ON toi.`code` = occv.order_code AND occv.user_oper = 'Invoice'
            LEFT JOIN t_order_pack_detail opd ON toi.`code` = opd.order_code AND opd.`enable` = 0
            left join t_order_delete_log todl on toi.code = todl.order_code
            where toi.enable= 0
            AND toi.company_id NOT IN ( 463, 466 ) AND toi.deliver_weight_notes_weight &lt; 38
            <if test="companyId != null">
                AND toi.company_id = #{companyId}
            </if>
            <if test="vehicleNumber != null">
                AND eci.vehicle_number LIKE CONCAT('%',  #{vehicleNumber}, '%')
            </if>
            <if test="fromName != null">
                AND toi.from_name LIKE CONCAT('%',  #{fromName}, '%')
            </if>
            <if test="endName != null">
                AND toi.end_name LIKE CONCAT('%',  #{endName}, '%')
            </if>
            <if test="auditRemark != null">
                AND oal.audit_remark like concat('%', #{auditRemark}, '%')
            </if>
            <if test="auditStatus != null and auditStatus != 'NO'">
                AND oal.audit_status = #{auditStatus}
            </if>
            <if test="auditStatus == 'NO'">
                AND ISNULL(oal.audit_status)
            </if>
            <if test="auditsTime != null ">
                AND oal.audit_time >= #{auditsTime}
                AND oal.enable = 0
            </if>
            <if test="auditeTime != null">
                AND oal.audit_time &lt;= #{auditeTime}
                AND oal.enable = 0
            </if>
            <if test="carrierId != null">
                AND toi.carrier_id = #{carrierId}
            </if>
            <if test="lineGoodsId != null">
                AND toi.line_goods_rel_id = #{lineGoodsId}
            </if>
            <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
                and toi.line_goods_rel_id in
                <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                    #{lineGoodsRelId}
                </foreach>
            </if>
            <if test="carOwnerId != null">
                and toi.end_car_owner_id = #{carOwnerId}
            </if>
            <if test="captainId != null">
                and toi.end_car_owner_id = #{captainId}
            </if>
            <if test="goodsId != null">
                and toi.goods_id = #{goodsId}
            </if>
            <if test="companyEntrust != null">
                and toi.company_entrust like concat('%', #{companyEntrust}, '%')
            </if>
            <if test="companyClient != null">
                and toi.company_client = #{companyClient}
            </if>
            <if test="projectId != null">
                and toi.company_project_id = #{projectId}
            </if>
            <if test="endAgentId != null">
                and toi.end_agent_id = #{endAgentId}
            </if>
            <if test="agentId != null">
                AND toi.agent_id = #{agentId}
            </if>
            <if test="informationStation != null">
                AND euis.org_name = #{informationStation}
            </if>
            <if test="bizCode != null">
                AND toi.order_business_code like concat('%', #{bizCode}, '%')
            </if>
            <if test="driverName != null">
                AND eui.real_name like concat('%', #{driverName}, '%')
            </if>
            <if test="driverPhone != null">
                AND eui.phone like concat('%', #{driverPhone}, '%')
            </if>
            <if test="licensePlate != null">
                AND eci.vehicle_number like concat('%', #{licensePlate}, '%')
            </if>
            <if test="carAuditStatus != null">
                AND eci.audit_status = #{carAuditStatus}
            </if>
            <if test="origin != null">
                AND toi.from_name like concat('%', #{origin}, '%')
            </if>
            <if test="terminus != null">
                AND toi.end_name like concat('%', #{terminus}, '%')
            </if>
            <if test="fhsTime != null">
                AND toi.deliver_order_time >= #{fhsTime}
            </if>
            <if test="fheTime != null">
                AND toi.deliver_order_time &lt;= #{fheTime}
            </if>
            <if test="shsTime != null">
                AND toi.receive_order_time >= #{shsTime}
            </if>
            <if test="sheTime != null">
                AND toi.receive_order_time &lt;= #{sheTime}
            </if>
            <if test="fhbdsTime != null">
                AND (toi.deliver_weight_notes_time >= #{fhbdsTime} or tosup.create_time >= #{fhbdsTime})
            </if>
            <if test="fhbdeTime != null">
                AND (toi.deliver_weight_notes_time &lt;= #{fhbdeTime} or tosup.create_time &lt;= #{fhbdeTime})
            </if>
            <if test="shbdsTime != null">
                AND (toi.receive_weight_notes_time >= #{shbdsTime} or tosdown.create_time >= #{shbdsTime})
            </if>
            <if test="shbdeTime != null">
                AND (toi.receive_weight_notes_time &lt;= #{shbdeTime} or tosdown.create_time &lt;= #{shbdeTime})
            </if>
            <if test="inputSearch != null">
                AND (
                toi.order_business_code LIKE CONCAT('%', #{inputSearch}, '%')
                OR eci.vehicle_number LIKE CONCAT('%', #{inputSearch}, '%')
                )
            </if>
            <if test="fksTime != null">
                AND toi.order_finish_time >= #{fksTime}
            </if>
            <if test="fkeTime != null">
                AND toi.order_finish_time &lt;= #{fkeTime}
            </if>
            <if test="orderState != null">
                AND toi.order_execute_status IN
                <foreach collection="orderState" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="payState != null">
                AND toi.order_pay_status IN
                <foreach collection="payState" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            ) as t
        where t.end_time > t.start_time + INTERVAL 10 MINUTE
        order by t.updateTime desc
    </select>
    <select id="pcSelectResourceHallOrderByPage" resultType="com.lz.dto.TOrderInfoDTO" parameterType="com.lz.vo.AppOrderSearchVO">
        select
        toi.id id,
        toi.update_time updateTime,
        toi.`code`, <!--运单32CODE-->
        toi.line_goods_rel_id, <!-- 线路货物关系ID -->
        eci.vehicle_number,  <!--车牌号-->
        eui.real_name, <!--司机姓名-->
        eui.phone, <!--司机手机号-->
        toi.company_id, <!-- 运单企业ID -->
        tcci.company_name, <!-- 运单企业名称 -->
        tcp.project_name, <!-- 运单项目名称 -->
        lgr.payment_process, <!-- 是否需要审核 -->
        tcci.company_contacts, <!-- 运单企业联系人 -->
        tcci.company_contacts_phone, <!-- 运单企业联系人电话 -->
        tcpi.carrier_name, <!-- 承运方名 -->
        toi.current_carriage_unit_price, <!-- 货源单价（预估单价） -->
        IF(toi.estimate_goods_weight=0, toi.primary_weight, toi.estimate_goods_weight) as estimateGoodsWeight, <!-- 货源重量 -->
        teui.id agentId,<!-- 经纪人ID -->
        teui.real_name agentName,<!-- 经纪人姓名 -->
        teui.phone agentPhone,<!-- 经纪人手机号 -->
        toi.primary_weight, <!-- 原发重量 -->
        toi.discharge_weight, <!-- 实收重量 -->
        toi.estimate_total_fee, <!-- 发单运费 -->
        toi.user_confirm_payment_amount carriageFee,<!-- 结算运费 -->
        toi.dispatch_fee,  <!-- 结算调度费金额 -->
        occ.service_fee,  <!-- 经纪人服务费 -->
        toi.user_confirm_service_fee userConfirmServiceFee,  <!-- 确认的经纪人服务费 -->
        tlgmr.share_method shareMethod,<!-- 抽成方式 -->
        tlgmr.share_value shareValue,<!-- 抽成数值 -->
        lgcr.goods_unit_price goodsUnitPrice, <!-- （计算使用的）货值单价 -->
        IFNULL(occs.carriage_unit_price, toi.current_carriage_unit_price) as carriageUnitPrice, <!--运费单价  应付单价-->
        toi.settled_weight, <!-- 结算重量 -->
        toi.order_business_code, <!--运单自定义CODE-->
        toi.create_time, <!--运单生成日期-->
        toi.estimate_load_time,
        toi.deliver_order_time,<!--运单发单日期  发单时间-->
        toi.receive_order_time,<!--运单收货日期  收单时间-->
        toi.order_finish_time, <!--运单完成时间-->
        IFNULL(toi.deliver_weight_notes_time, tosup.create_time) as deliver_weight_notes_time, <!--装货磅单时间-->
        IFNULL(toi.receive_weight_notes_time, tosdown.create_time) as receive_weight_notes_time, <!--卸货磅单时间-->
        IFNULL(toi.deliver_weight_notes_weight,0.0000) as deliver_weight_notes_weight,
        IFNULL(toi.receive_weight_notes_weight,0.0000) as receive_weight_notes_weight,
        toi.from_name, <!-- 起点   装货地-->
        toi.end_name, <!-- 终点  卸货地-->
        toi.company_entrust, <!-- 企业的委托方-->
        toi.company_client,<!-- 企业的客户-->
        toi.goods_id, <!-- 货物ID-->
        toi.goods_name,<!-- 货物名称类型-->
        toi.line_name, <!-- 线路名称 -->
        toi.total_fee totalAmount, <!-- 总金额 -->
        toi.contract_status as contractStatusCode,
        toi.user_confirm_payment_amount as rulePaymentAmount, <!-- 规则计算的应付运费金额 : 取的是快照表中的用户确认的应付运费金额-->
        occs.remark as balanceRemark, <!-- 结算备注 -->
        ifnull(occs.carriage_unit_price, 0) settlementPrice,<!-- 结算单价 2019、8、9 -->
        occv.dispatch_fee as originalDispatchFee, <!-- 原发调度费 -->
        occv.carriage_fee as originalCarriageFee, <!-- 原发运费 -->
        toi.order_execute_status,
        osn.page_show_code as orderExecuteStatusValue, <!-- 运单状态 -->
        toi.order_pay_status,
        tosn.page_show_code as orderPayStatusValue, <!--付款标志 运单支付状态 -->
        ta1.nickname as receiverNickname, <!--收单员姓名-->
        ta1.account_no as receiverNickPhone, <!--收单员电话-->
        toi.deliver_weight_notes_photo, <!--发货磅单-->
        toi.receive_weight_notes_photo, <!--收货磅单-->
        ifnull(dci.item_value, '未审核') as orderAudit, <!--运单审核状态-->
        oal.audit_status as auditCode, <!-- 审核状态 code -->
        oal.audit_remark, <!-- 审核意见 -->
        oal.not_pass_condition as notPassCondition, <!-- 运单审核不通过原因 -->
        eui.audit_status as driverStatus, <!--司机审核状态-->
        eci.audit_status as carStatus, <!--车辆审核状态-->
        eui.audit_opinion userAuditOpinion,
        eci.audit_opinion carAuditOpinion,
        concat("[",toi.goods_name,"]",toi.line_name) as sourceName,
        dci2.item_value as contractStatus,<!-- 合同签署状态 -->
        tosf.state_node_value as payChildStatus, <!-- 最新的支付子狀態 -->
        ctp.item_value AS orderCreateType, <!-- 运单生成方式 -->
        toi.pack_status,<!-- 打包状态 -->
        tdci.item_value as capitalTransferType, <!--资金转移方式-->
        todl.delete_reason, tcp.check_user_car_status,
        IF(topi.id is null,'华夏', CASE topi.payment_platforms WHEN '京东' THEN '京东' WHEN '华夏' THEN '华夏' ELSE '网商' END) paymentPlatforms
        FROM
        t_order_info toi
        LEFT JOIN t_company_info tcci ON toi.company_id = tcci.id AND tcci.`enable` = 0
        LEFT JOIN t_company_project tcp ON tcp.id = toi.company_project_id AND tcp.`enable` = 0
        LEFT JOIN t_line_goods_rel lgr ON lgr.id = toi.line_goods_rel_id AND lgr.`enable` = 0
        LEFT JOIN t_carrier_info tcpi ON toi.carrier_id = tcpi.id AND tcpi.`enable` = 0
        LEFT JOIN t_end_user_info eui ON toi.end_driver_id = eui.id AND eui.`enable` = 0
        LEFT JOIN t_end_car_info eci ON toi.vehicle_id = eci.id AND eci.`enable` = 0
        LEFT JOIN t_order_state_node osn ON toi.order_execute_status = osn.`code`
        LEFT JOIN t_account ta1 ON toi.receive_order_user_id = ta1.id
        LEFT JOIN t_order_state_node tosn ON toi.order_pay_status = tosn.`code`
        LEFT JOIN t_order_state tosf ON tosf.id = (
        SELECT
        MAX(tost.id)
        FROM t_order_state tost
        WHERE toi.`code` = tost.order_code
        )
        LEFT JOIN t_order_cast_changes occ ON toi.`code` = occ.order_code AND occ.data_enable = 1
        LEFT JOIN t_dic_cat_item tdci ON tdci.item_code = occ.capital_transfer_type
        LEFT JOIN t_order_cast_calc_snapshot occs ON toi.`code` = occs.order_code and occs.data_enable = 1
        LEFT JOIN t_line_goods_carriage_rule_detail lgcr ON toi.line_goods_rel_id = lgcr.line_goods_rel_id and lgcr.`enable`=0
        LEFT JOIN t_order_audit_log oal ON oal.order_code = toi.code
        LEFT JOIN t_order_state tosup ON tosup.state_node_value = 'S0302' AND  tosup.order_code = toi.`code` and tosup.`enable` = 0
        LEFT JOIN t_order_state tosdown ON tosdown.state_node_value = 'S0402' AND  tosdown.order_code = toi.`code` and tosdown.`enable` = 0
        LEFT JOIN t_dic_cat_item dci ON dci.item_code = oal.audit_status
        LEFT JOIN t_dic_cat_item ctp ON toi.order_create_type = ctp.item_code
        LEFT JOIN t_dic_cat_item dci2 on dci2.item_code = toi.contract_status
        left join t_order_pay_info topi on toi.code = topi.order_code
        LEFT JOIN t_order_cast_changes occv ON toi.`code` = occv.order_code AND occv.user_oper = 'Invoice'
        LEFT JOIN t_order_pack_detail opd ON toi.`code` = opd.order_code AND opd.`enable` = 0
        left join t_order_delete_log todl on toi.code = todl.order_code
        left join t_line_goods_manager_rel tlgmr on tlgmr.line_goods_rel_id = lgr.id AND tlgmr.`enable` = 0
        left join t_end_user_info teui on teui.id = toi.agent_id AND teui.`enable` = 0
        where
        toi.order_create_type= 'RESOURCEHALLSEND'
        <if test="companyId != null">
            AND toi.company_id = #{companyId}
        </if>
        <if test = "null != companyIds and companyIds.size() > 0">
            AND toi.company_id in
            <foreach collection="companyIds" item="companyId" open="(" separator="," close=")">
                #{companyId}
            </foreach>
        </if>
        <if test="null !=lineGoodsRuleIds and lineGoodsRuleIds.size() > 0">
            and toi.line_goods_rel_id IN
            <foreach collection="lineGoodsRuleIds" item="lgr" index="item" open="(" close=")" separator=",">
                #{lgr}
            </foreach>
        </if>
        <if test="orderState != null ">
            AND toi.order_execute_status IN
            <foreach collection="orderState" item="orderExecuteStatus" index="item" open="(" close=")" separator=",">
                #{orderExecuteStatus}
            </foreach>
        </if>
        <if test="vehicleNumber != null">
            AND eci.vehicle_number LIKE CONCAT('%',  #{vehicleNumber}, '%')
        </if>
        <if test="fromName != null">
            AND toi.from_name LIKE CONCAT('%',  #{fromName}, '%')
        </if>
        <if test="endName != null">
            AND toi.end_name LIKE CONCAT('%',  #{endName}, '%')
        </if>
        <if test="auditRemark != null">
            AND oal.audit_remark like concat('%', #{auditRemark}, '%')
        </if>
        <if test="auditStatus != null and auditStatus != 'NO'">
            AND oal.audit_status = #{auditStatus}
        </if>
        <if test="auditStatus == 'NO'">
            AND ISNULL(oal.audit_status)
        </if>
        <if test="auditsTime != null ">
            AND oal.audit_time >= #{auditsTime}
            AND oal.enable = 0
        </if>
        <if test="auditeTime != null">
            AND oal.audit_time &lt;= #{auditeTime}
            AND oal.enable = 0
        </if>
        <if test="carrierId != null">
            AND toi.carrier_id = #{carrierId}
        </if>
        <if test="lineGoodsId != null">
            AND toi.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="null !=lineGoodsRelIds and lineGoodsRelIds.size() > 0">
            and toi.line_goods_rel_id IN
            <foreach collection="lineGoodsRelIds" item="lgr" index="item" open="(" close=")" separator=",">
                #{lgr}
            </foreach>
        </if>
        <if test="carOwnerId != null">
            and toi.end_car_owner_id = #{carOwnerId}
        </if>
        <if test="captainId != null">
            and toi.end_car_owner_id = #{captainId}
        </if>
        <if test="goodsId != null">
            and toi.goods_id = #{goodsId}
        </if>
        <if test="companyEntrust != null">
            and toi.company_entrust like concat('%', #{companyEntrust}, '%')
        </if>
        <if test="companyClient != null">
            and toi.company_client = #{companyClient}
        </if>
        <if test="projectId != null">
            and toi.company_project_id = #{projectId}
        </if>
        <if test="endAgentId != null">
            and toi.end_agent_id = #{endAgentId}
        </if>
        <if test="agentId != null">
            AND toi.agent_id = #{agentId}
        </if>
        <if test="bizCode != null">
            AND toi.order_business_code like concat('%', #{bizCode}, '%')
        </if>
        <if test="driverName != null">
            AND eui.real_name like concat('%', #{driverName}, '%')
        </if>
        <if test="driverPhone != null">
            AND eui.phone like concat('%', #{driverPhone}, '%')
        </if>
        <if test="licensePlate != null">
            AND eci.vehicle_number like concat('%', #{licensePlate}, '%')
        </if>
        <if test="carAuditStatus != null">
            AND eci.audit_status = #{carAuditStatus}
        </if>
        <if test="origin != null">
            AND toi.from_name like concat('%', #{origin}, '%')
        </if>
        <if test="terminus != null">
            AND toi.end_name like concat('%', #{terminus}, '%')
        </if>
        <if test="fhsTime != null">
            AND toi.deliver_order_time >= #{fhsTime}
        </if>
        <if test="fheTime != null">
            AND toi.deliver_order_time &lt;= #{fheTime}
        </if>
        <if test="shsTime != null">
            AND toi.receive_order_time >= #{shsTime}
        </if>
        <if test="sheTime != null">
            AND toi.receive_order_time &lt;= #{sheTime}
        </if>
        <if test="fhbdsTime != null">
            AND (toi.deliver_weight_notes_time >= #{fhbdsTime} or tosup.create_time >= #{fhbdsTime})
        </if>
        <if test="fhbdeTime != null">
            AND (toi.deliver_weight_notes_time &lt;= #{fhbdeTime} or tosup.create_time &lt;= #{fhbdeTime})
        </if>
        <if test="shbdsTime != null">
            AND (toi.receive_weight_notes_time >= #{shbdsTime} or tosdown.create_time >= #{shbdsTime})
        </if>
        <if test="shbdeTime != null">
            AND (toi.receive_weight_notes_time &lt;= #{shbdeTime} or tosdown.create_time &lt;= #{shbdeTime})
        </if>
        <if test="inputSearch != null">
            AND (
            toi.order_business_code LIKE CONCAT('%', #{inputSearch}, '%')
            OR eci.vehicle_number LIKE CONCAT('%', #{inputSearch}, '%')
            )
        </if>
        <!--<if test="fksTime != null">
            AND toi.order_finish_time >= #{fksTime}
        </if>
        <if test="fkeTime != null">
            AND toi.order_finish_time &lt;= #{fkeTime}
        </if>-->
        <if test="fihsTime != null">
            AND toi.order_finish_time >= #{fihsTime}
        </if>
        <if test="fiheTime != null">
            AND toi.order_finish_time &lt;= #{fiheTime}
        </if>
        <if test="payState != null">
            AND toi.order_pay_status IN
            <foreach collection="payState" item="pay" index="item" open="(" close=")" separator=",">
                #{pay}
            </foreach>
        </if>
        order by toi.update_time desc
    </select>

</mapper>