<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TXinFaOrderInfoMapper">

    <select id="selectByAccountId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        select
            rel.line_goods_rel_id
            from t_line_goods_user_rel rel
        where rel.enable = 0
          and rel.account_info_id = #{accountId}
          and rel.role_code = 'XINFARECEIVEORDER'

    </select>

    <select id="selectPayLineByAccountId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        select
            rel.line_goods_rel_id
        from t_line_goods_user_rel rel
        where rel.enable = 0
          and rel.account_info_id = #{accountId}
          and rel.role_code = 'XINFAPAYER'
    </select>


    <select id="selectByTGoodsSourceInfo" parameterType="java.lang.Integer" resultType="com.lz.model.TGoodsSourceInfo">
        select
            tgsi.*
        from t_goods_source_info tgsi
        where tgsi.enable = 0
          and tgsi.line_goods_rel_id = #{lineGoodsRelId}
    </select>

    <select id="selectByLineGoodsRelId" parameterType="java.lang.Integer" resultType="com.lz.model.TOrderLineGoodsCarriageRuleDetail">
        select
            de.*
        from t_line_goods_carriage_rule_detail de
        where de.enable = 0
          and de.line_goods_rel_id = #{lineGoodsRelId}
    </select>

    <select id="selectById" parameterType="java.lang.Integer" resultType="com.lz.model.TAccount">
        select
            a.*
        from t_account a
        where a.enable = 0
          and a.id = #{accountId}
    </select>

    <select id="selectLineByAccountId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        select
            rel.line_goods_rel_id
        from t_line_goods_user_rel rel
        where rel.enable = 0
          and rel.account_info_id = #{accountId}

    </select>


    <select id="selectByCaptainAudit" parameterType="com.lz.vo.AppOrderSearchVO" resultType="com.lz.dto.TXinFaOrderInfoCaptainAuditDto">
        SELECT
        o.id,
        o.code,
        o.order_business_code,
        c.vehicle_number,
        u.real_name,
        o.deliver_weight_notes_weight,
        o.receive_weight_notes_weight,
        o.deliver_weight_notes_time,
        o.receive_weight_notes_time,
        o.deliver_weight_notes_photo,
        o.receive_weight_notes_photo,
        ifnull(toid.carriage_price_unit, 'DUN') as carriage_price_unit,
        toiw.box_num,
        toiw.deliver_weight_notes_photo1,
        toiw.receive_weight_notes_photo1,
        toiw.deliver_weight_notes_photo2,
        toiw.receive_weight_notes_photo2,
        item2.page_show_name order_pay_status,
        item.page_show_name order_execute_status,
        o.rule_payment_amount,
        o.line_name,
        o.goods_name,
        o.current_carriage_unit_price as carriageUnitPrice
        FROM  t_order_info o
        LEFT JOIN t_order_state tosf ON tosf.id = (
        SELECT
        MAX(tost.id)
        FROM t_order_state tost
        WHERE o.`code` = tost.order_code
        )
        left join t_order_info_weight toiw on toiw.order_id = o.id
        left join t_order_info_detail toid on toid.order_id = o.id
        LEFT JOIN t_line_goods_rel tlgr ON o.line_goods_rel_id = tlgr.id
        left join t_goods_source_info tgsi on tgsi.line_goods_rel_id = o.line_goods_rel_id and tgsi.enable = 0
        LEFT JOIN t_end_user_info u ON u.id = o.end_driver_id
        LEFT JOIN t_end_car_info c ON c.id = o.vehicle_id
        LEFT JOIN t_order_state_node item ON item.CODE = o.order_execute_status
        LEFT JOIN t_order_state_node_detail item2 ON item2.status_code = tosf.state_node_value
        WHERE o.enable = 0 and o.order_execute_status not in ('M-20', 'M-10')
          and  o.order_pay_status = 'M045'
          and o.order_execute_status !='O060'
          and tlgr.if_captain_audit = 1 <!-- 运单是否需要车队长审核 0否 1是 -->
        <if test='payStateNode != null and payStateNode !="" '>
            and tosf.state_node_value = #{payStateNode}
        </if>

        <if test="captainId != null">
            and o.end_car_owner_id = #{captainId}
        </if>
        <if test="carrierId != null">
            and o.carrier_id = #{carrierId}
        </if>
        <if test="bizCode != null">
            AND o.order_business_code like concat('%', #{bizCode}, '%')
        </if>
        <if test="driverName != null">
            AND u.real_name like concat('%', #{driverName}, '%')
        </if>
        <if test="vehicleNumber != null">
            AND c.vehicle_number like concat('%', #{vehicleNumber} ,'%')
        </if>
        <if test="projectId != null">
            and o.company_project_id = #{projectId}
        </if>
        <if test="lineGoodsId != null">
            and o.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="fhsTime != null">
            and o.deliver_order_time >= #{fhsTime}
        </if>
        <if test="fheTime != null">
            and o.deliver_order_time &lt;= #{fheTime}
        </if>
        <if test="fhbdsTime != null">
            AND o.deliver_weight_notes_time >= #{fhbdsTime}
        </if>
        <if test="fhbdeTime != null">
            AND o.deliver_weight_notes_time &lt;= #{fhbdeTime}
        </if>
        <if test="shbdsTime != null">
            AND o.receive_weight_notes_time >= #{shbdsTime}
        </if>
        <if test="shbdeTime != null">
            AND o.receive_weight_notes_time &lt;= #{shbdeTime}
        </if>
        <if test="idArray != null and idArray.size != 0 ">
            AND o.id IN
            <foreach collection="idArray" item="id" index="item" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="null != goodsId">
            and o.goods_id = #{goodsId}
        </if>
        <if test="captainIds != null and captainIds.size > 0">
            and o.end_car_owner_id in
            <foreach collection="captainIds" item="captainId" index="item" open="(" close=")" separator=",">
                #{captainId}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and o.company_project_id in
            <foreach collection="projectIds" item="projectId" index="item" open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>
        <if test="lineGoodsId != null">
            and o.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
            and o.line_goods_rel_id in
            <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                #{lineGoodsRelId}
            </foreach>
        </if>
        order by o.create_time desc
    </select>


    <select id="selectByCaptainAuditSum" parameterType="com.lz.vo.AppOrderSearchVO" resultType="com.lz.dto.TXinFaOrderWeightDTO">
        SELECT
        IFNULL(SUM(IFNULL(o.deliver_weight_notes_weight,0)), 0) as deliverWeightNotesWeight,
        IFNULL(SUM(IFNULL(o.receive_weight_notes_weight,0)), 0) as receiveWeightNotesWeight,
        IFNULL(SUM(IFNULL(o.rule_payment_amount,0)), 0) as rulePaymentAmount
        FROM  t_order_info o
        LEFT JOIN t_order_state tosf ON tosf.id = (
        SELECT
        MAX(tost.id)
        FROM t_order_state tost
        WHERE o.`code` = tost.order_code
        )
        LEFT JOIN t_line_goods_rel tlgr ON o.line_goods_rel_id = tlgr.id
        LEFT JOIN t_end_car_info c ON c.id = o.vehicle_id
        <if test="driverName != null">
            LEFT JOIN t_end_user_info u ON u.id = o.end_driver_id
        </if>
        WHERE o.enable = 0 and o.order_execute_status not in ('M-20', 'M-10')
        and  o.order_pay_status = 'M045'
        and o.order_execute_status !='O060'
        and tlgr.if_captain_audit = 1 <!-- 运单是否需要车队长审核 0否 1是 -->

        <if test='payStateNode != null and payStateNode !="" '>
            and tosf.state_node_value = #{payStateNode}
        </if>
        <if test="captainId != null">
            and o.end_car_owner_id = #{captainId}
        </if>
        <if test="carrierId != null">
            and o.carrier_id = #{carrierId}
        </if>
        <if test="bizCode != null">
            AND o.order_business_code like concat('%', #{bizCode}, '%')
        </if>
        <if test="driverName != null">
            AND u.real_name like concat('%', #{driverName}, '%')
        </if>
        <if test="vehicleNumber != null">
            AND c.vehicle_number like concat('%', #{vehicleNumber} ,'%')
        </if>
        <if test="projectId != null">
            and o.company_project_id = #{projectId}
        </if>
        <if test="lineGoodsId != null">
            and o.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="fhsTime != null">
            and o.deliver_order_time >= #{fhsTime}
        </if>
        <if test="fheTime != null">
            and o.deliver_order_time &lt;= #{fheTime}
        </if>
        <if test="fhbdsTime != null">
            AND o.deliver_weight_notes_time >= #{fhbdsTime}
        </if>
        <if test="fhbdeTime != null">
            AND o.deliver_weight_notes_time &lt;= #{fhbdeTime}
        </if>
        <if test="shbdsTime != null">
            AND o.receive_weight_notes_time >= #{shbdsTime}
        </if>
        <if test="shbdeTime != null">
            AND o.receive_weight_notes_time &lt;= #{shbdeTime}
        </if>
        <if test="null != goodsId">
            and o.goods_id = #{goodsId}
        </if>
        <if test="captainIds != null and captainIds.size > 0">
            and o.end_car_owner_id in
            <foreach collection="captainIds" item="captainId" index="item" open="(" close=")" separator=",">
                #{captainId}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and o.company_project_id in
            <foreach collection="projectIds" item="projectId" index="item" open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>
        <if test="lineGoodsId != null">
            and o.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
            and o.line_goods_rel_id in
            <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                #{lineGoodsRelId}
            </foreach>
        </if>
        order by o.create_time desc
    </select>


    <select id="selectByCaptainAuditOrderId" parameterType="com.lz.vo.AppOrderSearchVO" resultType="com.lz.dto.TXinFaOrderInfoCaptainAuditDto">
        SELECT
        o.id,
        o.order_business_code,
        c.vehicle_number,
        u.real_name,
        u.phone as realPhone,
        o.line_name,
        o.goods_name,
        o.deliver_weight_notes_weight,
        o.receive_weight_notes_weight,
        o.deliver_weight_notes_time,
        o.receive_weight_notes_time,
        o.deliver_weight_notes_photo,
        o.receive_weight_notes_photo,
        o.rule_payment_amount,
        ifnull(toid.carriage_price_unit, 'DUN') as carriage_price_unit,
        tgsi.current_carriage_unit_price as carriageUnitPrice,
        tgsi.fix_cut_fee as fixCutFee, <!-- 固定扣款 -->
        toiw.box_num,
        toiw.deliver_weight_notes_time1,
        toiw.deliver_weight_notes_weight1,
        toiw.deliver_weight_notes_photo1,
        toiw.receive_weight_notes_time1,
        toiw.receive_weight_notes_weight1,
        toiw.receive_weight_notes_photo1,
        toiw.gross_weight1,
        toiw.primary_weight1,
        toiw.discharge_weight1,
        toiw.carriage_unit_price1,
        toiw.deliver_weight_notes_time2,
        toiw.deliver_weight_notes_weight2,
        toiw.deliver_weight_notes_photo2,
        toiw.receive_weight_notes_time2,
        toiw.receive_weight_notes_weight2,
        toiw.receive_weight_notes_photo2,
        toiw.gross_weight2,
        toiw.primary_weight2,
        toiw.discharge_weight2,
        toiw.carriage_unit_price2
        FROM  t_order_info o
        left join t_order_info_weight toiw on toiw.order_id = o.id
        left join t_order_info_detail toid on toid.order_id = o.id
        left join t_goods_source_info tgsi on tgsi.line_goods_rel_id = o.line_goods_rel_id and tgsi.enable = 0
        LEFT JOIN t_end_user_info u ON u.id = o.end_driver_id
        LEFT JOIN t_end_car_info c ON c.id = o.vehicle_id
        WHERE  o.id = #{orderId}
    </select>


    <select id="selectByPutOrderPage" parameterType="com.lz.vo.AppOrderSearchVO" resultType="com.lz.dto.TXinFaOrderInfoCaptainAuditDto">
        SELECT
        o.id,
        o.code,
        o.order_business_code,
        c.vehicle_number,
        o.deliver_weight_notes_weight,
        o.receive_weight_notes_weight,
        o.deliver_weight_notes_time,
        o.receive_weight_notes_time,
        u.real_name,
        u2.real_name as captainName,
        o.settled_weight,
        o.line_name,
        o.goods_name,
        item.page_show_name order_execute_status,
        o.receive_goods_contacter,
        tosf.state_node_value as orderPayStatus,
        o.order_execute_status as orderExecuteStatusCode,
        o.line_goods_rel_id,
        o.dispatch_fee,
        o.user_confirm_payment_amount,
        tlgr.if_captain_audit,
        o.receive_order_time,
        IFNULL(shot.carriage_unit_price, o.current_carriage_unit_price) as carriageUnitPrice <!--运费单价  应付单价-->
        FROM  t_order_info o
        LEFT JOIN t_order_cast_calc_snapshot shot ON o.code = shot.order_code and shot.data_enable = 1
        LEFT JOIN t_line_goods_rel tlgr ON o.line_goods_rel_id = tlgr.id
        LEFT JOIN t_end_user_info u ON u.id = o.end_driver_id
        LEFT JOIN t_end_user_info u2 ON u2.id = o.end_car_owner_id
        LEFT JOIN t_end_car_info c ON c.id = o.vehicle_id
        LEFT JOIN t_order_state_node item ON item.CODE = o.order_execute_status
        LEFT JOIN t_order_state tosf ON tosf.id = (
        SELECT
        MAX(tost.id)
        FROM t_order_state tost
        WHERE o.`code` = tost.order_code
        )
        WHERE o.enable = 0 and o.order_execute_status not in ('M-20', 'M-10')
        <if test="lineGoodsRelId != null and lineGoodsRelId.size > 0">
            AND o.line_goods_rel_id  IN
            <foreach collection="lineGoodsRelId" item="line" index="item" open="(" close=")" separator=",">
                #{line}
            </foreach>
        </if>
        <if test="orderState != null and orderState.length != 0 ">
            AND o.order_execute_status IN
            <foreach collection="orderState" item="orderExecuteStatus" index="item" open="(" close=")" separator=",">
                #{orderExecuteStatus}
            </foreach>
        </if>
        <if test="!ifCaptainAudit">
          <!-- 是否仅展示车队长审核通过运单  false 否  true是 -->
            and tlgr.if_captain_audit = 0
            and  o.order_execute_status in('M020','M030','M040','O060')

        </if>
        <if test="ifCaptainAudit">
            <!-- 是否仅展示车队长审核通过运单  false 否  true是 -->
            and tlgr.if_captain_audit = 1
            and  o.order_pay_status = 'M045'
            AND tosf.state_node_value in('S0451','S0501')
        </if>
        <if test="captainId != null">
            and o.end_car_owner_id = #{captainId}
        </if>
        <if test="carrierId != null">
            and o.carrier_id = #{carrierId}
        </if>
        <if test="companyId != null">
            and o.company_id = #{companyId}
        </if>
        <if test="bizCode != null">
            AND o.order_business_code like concat('%', #{bizCode}, '%')
        </if>
        <if test="driverName != null">
            AND u.real_name like concat('%', #{driverName}, '%')
        </if>
        <if test="licensePlate != null">
            AND c.vehicle_number like concat('%', #{licensePlate} ,'%')
        </if>
        <if test="projectId != null">
            and o.company_project_id = #{projectId}
        </if>
        <if test="fhsTime != null">
            and o.deliver_order_time >= #{fhsTime}
        </if>
        <if test="fheTime != null">
            and o.deliver_order_time &lt;= #{fheTime}
        </if>
        <if test="shsTime != null">
            AND o.receive_order_time >= #{shsTime}
        </if>
        <if test="sheTime != null">
            AND o.receive_order_time &lt;= #{sheTime}
        </if>
        <if test="fhbdsTime != null">
            AND o.deliver_weight_notes_time >= #{fhbdsTime}
        </if>
        <if test="fhbdeTime != null">
            AND o.deliver_weight_notes_time &lt;= #{fhbdeTime}
        </if>
        <if test="shbdsTime != null">
            AND o.receive_weight_notes_time >= #{shbdsTime}
        </if>
        <if test="shbdeTime != null">
            AND o.receive_weight_notes_time &lt;= #{shbdeTime}
        </if>
        <if test="idArray != null and idArray.size != 0 ">
            AND o.id IN
            <foreach collection="idArray" item="id" index="item" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test = "null != receiveOrderUserId">
            and o.receive_order_user_id = #{receiveOrderUserId}
        </if>
        <if test="null != goodsId">
            and o.goods_id = #{goodsId}
        </if>
        <if test="captainIds != null and captainIds.size > 0">
            and o.end_car_owner_id in
            <foreach collection="captainIds" item="captainId" index="item" open="(" close=")" separator=",">
                #{captainId}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and o.company_project_id in
            <foreach collection="projectIds" item="projectId" index="item" open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>
        <if test="lineGoodsId != null">
            and o.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
            and o.line_goods_rel_id in
            <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                #{lineGoodsRelId}
            </foreach>
        </if>
        order by o.create_time desc
    </select>


    <select id="selectByPutOrderSum" parameterType="com.lz.vo.AppOrderSearchVO" resultType="com.lz.dto.TXinFaOrderWeightDTO">
        SELECT
        IFNULL(SUM(IFNULL(o.deliver_weight_notes_weight,0)), 0) as deliverWeightNotesWeight,
        IFNULL(SUM(IFNULL(o.receive_weight_notes_weight,0)), 0) as receiveWeightNotesWeight,
        IFNULL(SUM(IFNULL(o.settled_weight,0)), 0) as settledWeight,
        IFNULL(SUM(IFNULL(o.user_confirm_payment_amount,0)), 0) as userConfirmPaymentAmount,
        IFNULL(SUM(IFNULL(o.dispatch_fee,0)), 0) as dispatchFee
        FROM  t_order_info o
        LEFT JOIN t_line_goods_rel tlgr ON o.line_goods_rel_id = tlgr.id
        LEFT JOIN t_end_user_info u ON u.id = o.end_driver_id
        LEFT JOIN t_end_user_info u2 ON u2.id = o.end_car_owner_id
        LEFT JOIN t_end_car_info c ON c.id = o.vehicle_id
        LEFT JOIN t_order_state tosf ON tosf.id = (
        SELECT
        MAX(tost.id)
        FROM t_order_state tost
        WHERE o.`code` = tost.order_code
        )
        WHERE o.enable = 0 and o.order_execute_status not in ('M-20', 'M-10')
        <if test="lineGoodsRelId != null and lineGoodsRelId.size > 0">
            AND o.line_goods_rel_id  IN
            <foreach collection="lineGoodsRelId" item="line" index="item" open="(" close=")" separator=",">
                #{line}
            </foreach>
        </if>
        <if test="orderState != null and orderState.length != 0 ">
            AND o.order_execute_status IN
            <foreach collection="orderState" item="orderExecuteStatus" index="item" open="(" close=")" separator=",">
                #{orderExecuteStatus}
            </foreach>
        </if>
        <if test="!ifCaptainAudit">
            <!-- 是否仅展示车队长审核通过运单  false 否  true是 -->
            AND tlgr.if_captain_audit = 0
            and  o.order_execute_status in('M020','M030','M040','O060')

        </if>
        <if test="ifCaptainAudit">
            <!-- 是否仅展示车队长审核通过运单  false 否  true是 -->
            and tlgr.if_captain_audit = 1
            and  o.order_pay_status = 'M045'
            AND tosf.state_node_value in('S0451','S0501')
        </if>
        <if test="captainId != null">
            and o.end_car_owner_id = #{captainId}
        </if>
        <if test="carrierId != null">
            and o.carrier_id = #{carrierId}
        </if>
        <if test="companyId != null">
            and o.company_id = #{companyId}
        </if>
        <if test="bizCode != null">
            AND o.order_business_code like concat('%', #{bizCode}, '%')
        </if>
        <if test="driverName != null">
            AND u.real_name like concat('%', #{driverName}, '%')
        </if>
        <if test="licensePlate != null">
            AND c.vehicle_number like concat('%', #{licensePlate} ,'%')
        </if>
        <if test="projectId != null">
            and o.company_project_id = #{projectId}
        </if>
        <if test="lineGoodsId != null">
            and o.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="fhsTime != null">
            and o.deliver_order_time >= #{fhsTime}
        </if>
        <if test="fheTime != null">
            and o.deliver_order_time &lt;= #{fheTime}
        </if>
        <if test="shsTime != null">
            AND o.receive_order_time >= #{shsTime}
        </if>
        <if test="sheTime != null">
            AND o.receive_order_time &lt;= #{sheTime}
        </if>
        <if test="fhbdsTime != null">
            AND o.deliver_weight_notes_time >= #{fhbdsTime}
        </if>
        <if test="fhbdeTime != null">
            AND o.deliver_weight_notes_time &lt;= #{fhbdeTime}
        </if>
        <if test="shbdsTime != null">
            AND o.receive_weight_notes_time >= #{shbdsTime}
        </if>
        <if test="shbdeTime != null">
            AND o.receive_weight_notes_time &lt;= #{shbdeTime}
        </if>
        <if test="idArray != null and idArray.size != 0 ">
            AND o.id IN
            <foreach collection="idArray" item="id" index="item" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test = "null != receiveOrderUserId">
            and o.receive_order_user_id = #{receiveOrderUserId}
        </if>
        <if test="null != goodsId">
            and o.goods_id = #{goodsId}
        </if>
        <if test="captainIds != null and captainIds.size > 0">
            and o.end_car_owner_id in
            <foreach collection="captainIds" item="captainId" index="item" open="(" close=")" separator=",">
                #{captainId}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and o.company_project_id in
            <foreach collection="projectIds" item="projectId" index="item" open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>
        <if test="lineGoodsId != null">
            and o.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
            and o.line_goods_rel_id in
            <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                #{lineGoodsRelId}
            </foreach>
        </if>
        order by o.create_time desc
    </select>



    <select id="payOrderPage" parameterType="com.lz.vo.AppOrderSearchVO" resultType="com.lz.dto.TXinFaOrderInfoCaptainAuditDto">
        SELECT
        o.id,
        o.code,
        o.order_business_code,
        c.vehicle_number,
        u.real_name,
        u2.real_name as captainName,
        o.settled_weight,
        o.user_confirm_payment_amount,
        o.dispatch_fee,
        o.line_name,
        o.goods_name,
        o.order_execute_status,
        if(toprd.id is not null, case toprd.status when 0 then 'P070' when 1 then 'P070' else o.order_pay_status end, o.order_pay_status) as orderPayStatus,
        tosn.page_show_code as orderPayStatusValue,
        ca.carrier_name,
        o.contract_status as contractStatusCode
        FROM  t_order_info o
        LEFT JOIN t_carrier_info ca ON ca.id = o.carrier_id
        LEFT JOIN t_end_user_info u ON u.id = o.end_driver_id
        LEFT JOIN t_end_user_info u2 ON u2.id = o.end_car_owner_id
        LEFT JOIN t_end_car_info c ON c.id = o.vehicle_id
        LEFT JOIN t_order_state_node item ON item.CODE = o.order_execute_status
        LEFT JOIN t_order_state tosf ON tosf.id = (
        SELECT
        MAX(tost.id)
        FROM t_order_state tost
        WHERE o.`code` = tost.order_code
        )
        left join t_order_pay_request_detail toprd on toprd.id = (select max(id) from t_order_pay_request_detail where order_id = o.id)
        LEFT JOIN t_order_state_node tosn ON o.order_pay_status = tosn.`code`
        WHERE o.enable = 0 and o.order_execute_status not in ('M-20', 'M-10')
        and  (o.order_execute_status = 'O060' or o.order_pay_status in ('P070', 'M080'))
        <if test="lineGoodsRelId != null and lineGoodsRelId.size > 0">
            AND o.line_goods_rel_id  IN
            <foreach collection="lineGoodsRelId" item="line" index="item" open="(" close=")" separator=",">
                #{line}
            </foreach>
        </if>
        <if test="captainId != null">
            and o.end_car_owner_id = #{captainId}
        </if>
        <if test="carrierId != null">
            and o.carrier_id = #{carrierId}
        </if>
        <if test="companyId != null">
            and o.company_id = #{companyId}
        </if>
        <if test="bizCode != null">
            AND o.order_business_code like concat('%', #{bizCode}, '%')
        </if>
        <if test="driverName != null">
            AND u.real_name like concat('%', #{driverName}, '%')
        </if>
        <if test="licensePlate != null">
            AND c.vehicle_number like concat('%', #{licensePlate} ,'%')
        </if>
        <if test="projectId != null">
            and o.company_project_id = #{projectId}
        </if>
        <if test="lineGoodsId != null">
            and o.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="fhsTime != null">
            and o.deliver_order_time >= #{fhsTime}
        </if>
        <if test="fheTime != null">
            and o.deliver_order_time &lt;= #{fheTime}
        </if>
        <if test="shsTime != null">
            AND o.receive_order_time >= #{shsTime}
        </if>
        <if test="sheTime != null">
            AND o.receive_order_time &lt;= #{sheTime}
        </if>
        <if test="fhbdsTime != null">
            AND o.deliver_weight_notes_time >= #{fhbdsTime}
        </if>
        <if test="fhbdeTime != null">
            AND o.deliver_weight_notes_time &lt;= #{fhbdeTime}
        </if>
        <if test="shbdsTime != null">
            AND o.receive_weight_notes_time >= #{shbdsTime}
        </if>
        <if test="shbdeTime != null">
            AND o.receive_weight_notes_time &lt;= #{shbdeTime}
        </if>
        <if test="fksTime != null">
            AND o.order_finish_time >= #{fksTime}
        </if>
        <if test="fkeTime != null">
            AND o.order_finish_time &lt;= #{fkeTime}
        </if>
        <if test="idArray != null and idArray.size != 0 ">
            AND o.id IN
            <foreach collection="idArray" item="id" index="item" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="payState != null and payState.length != 0 ">
            AND o.order_pay_status IN
            <foreach collection="payState" item="pay" index="item" open="(" close=")" separator=",">
                #{pay}
            </foreach>
            <if test='payStateNode != null and payStateNode !="" '>
                and tosf.state_node_value = #{payStateNode}
            </if>
        </if>
        <if test="null != goodsId">
            and o.goods_id = #{goodsId}
        </if>
        <if test="captainIds != null and captainIds.size > 0">
            and o.end_car_owner_id in
            <foreach collection="captainIds" item="captainId" index="item" open="(" close=")" separator=",">
                #{captainId}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and o.company_project_id in
            <foreach collection="projectIds" item="projectId" index="item" open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>
        <if test="lineGoodsId != null">
            and o.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
            and o.line_goods_rel_id in
            <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                #{lineGoodsRelId}
            </foreach>
        </if>
        order by o.create_time desc
    </select>


    <select id="payOrderSum" parameterType="com.lz.vo.AppOrderSearchVO" resultType="com.lz.dto.TXinFaOrderWeightDTO">
        SELECT
        IFNULL(SUM(IFNULL(o.settled_weight,0)), 0) as settledWeight,
        IFNULL(SUM(IFNULL(o.user_confirm_payment_amount,0)), 0) as userConfirmPaymentAmount,
        IFNULL(SUM(IFNULL(o.dispatch_fee,0)), 0) as dispatchFee
        FROM  t_order_info o
        LEFT JOIN t_carrier_info ca ON ca.id = o.carrier_id
        LEFT JOIN t_end_user_info u ON u.id = o.end_driver_id
        LEFT JOIN t_end_user_info u2 ON u2.id = o.end_car_owner_id
        LEFT JOIN t_end_car_info c ON c.id = o.vehicle_id
        LEFT JOIN t_order_state_node item ON item.CODE = o.order_execute_status
        LEFT JOIN t_order_state tosf ON tosf.id = (
        SELECT
        MAX(tost.id)
        FROM t_order_state tost
        WHERE o.`code` = tost.order_code
        )
        WHERE o.enable = 0 and o.order_execute_status not in ('M-20', 'M-10')
        and  (o.order_execute_status = 'O060' or o.order_pay_status in ('P070', 'M080'))
        <if test="lineGoodsRelId != null and lineGoodsRelId.size > 0">
            AND o.line_goods_rel_id  IN
            <foreach collection="lineGoodsRelId" item="line" index="item" open="(" close=")" separator=",">
                #{line}
            </foreach>
        </if>
        <if test="captainId != null">
            and o.end_car_owner_id = #{captainId}
        </if>
        <if test="carrierId != null">
            and o.carrier_id = #{carrierId}
        </if>
        <if test="companyId != null">
            and o.company_id = #{companyId}
        </if>
        <if test="bizCode != null">
            AND o.order_business_code like concat('%', #{bizCode}, '%')
        </if>
        <if test="driverName != null">
            AND u.real_name like concat('%', #{driverName}, '%')
        </if>
        <if test="licensePlate != null">
            AND c.vehicle_number like concat('%', #{licensePlate} ,'%')
        </if>
        <if test="projectId != null">
            and o.company_project_id = #{projectId}
        </if>
        <if test="lineGoodsId != null">
            and o.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="fhsTime != null">
            and o.deliver_order_time >= #{fhsTime}
        </if>
        <if test="fheTime != null">
            and o.deliver_order_time &lt;= #{fheTime}
        </if>
        <if test="shsTime != null">
            AND o.receive_order_time >= #{shsTime}
        </if>
        <if test="sheTime != null">
            AND o.receive_order_time &lt;= #{sheTime}
        </if>
        <if test="fhbdsTime != null">
            AND o.deliver_weight_notes_time >= #{fhbdsTime}
        </if>
        <if test="fhbdeTime != null">
            AND o.deliver_weight_notes_time &lt;= #{fhbdeTime}
        </if>
        <if test="shbdsTime != null">
            AND o.receive_weight_notes_time >= #{shbdsTime}
        </if>
        <if test="shbdeTime != null">
            AND o.receive_weight_notes_time &lt;= #{shbdeTime}
        </if>
        <if test="fksTime != null">
            AND o.order_finish_time >= #{fksTime}
        </if>
        <if test="fkeTime != null">
            AND o.order_finish_time &lt;= #{fkeTime}
        </if>
        <if test="idArray != null and idArray.size != 0 ">
            AND o.id IN
            <foreach collection="idArray" item="id" index="item" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="payState != null and payState.length != 0 ">
            AND o.order_pay_status IN
            <foreach collection="payState" item="pay" index="item" open="(" close=")" separator=",">
                #{pay}
            </foreach>
            <if test='payStateNode != null and payStateNode !="" '>
                and tosf.state_node_value = #{payStateNode}
            </if>
        </if>
        <if test="orderState != null and orderState.length != 0 ">
            AND o.order_execute_status IN
            <foreach collection="orderState" index="index" item="st" open="(" close=")" separator=",">
                #{st}
            </foreach>
        </if>
        <if test="null != goodsId">
          and o.goods_id = #{goodsId}
        </if>
        <if test="captainIds != null and captainIds.size > 0">
            and o.end_car_owner_id in
            <foreach collection="captainIds" item="captainId" index="item" open="(" close=")" separator=",">
                #{captainId}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and o.company_project_id in
            <foreach collection="projectIds" item="projectId" index="item" open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>
        <if test="lineGoodsId != null">
            and o.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
            and o.line_goods_rel_id in
            <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                #{lineGoodsRelId}
            </foreach>
        </if>
        order by o.create_time desc
    </select>

    <select id="orderPage" parameterType="com.lz.vo.AppOrderSearchVO" resultType="com.lz.dto.TXinFaOrderInfoCaptainAuditDto">
        SELECT
        o.id,
        o.code,
        o.order_business_code,
        o.company_id,
        c.vehicle_number,
        u.real_name,
        u2.real_name as captainName,
        o.deliver_order_time,
        o.receive_order_time,
        o.deliver_weight_notes_weight,
        o.receive_weight_notes_weight,
        o.deliver_weight_notes_time,
        o.receive_weight_notes_time,
        o.settled_weight,
        o.receive_goods_contacter,
        o.user_confirm_payment_amount,
        o.dispatch_fee,
        o.line_name,
        o.goods_name,
        item.page_show_name order_execute_status,
        item2.page_show_name order_pay_status,
        ca.carrier_name,
        tdci.item_value as capitalTransferType,
        o.deliver_weight_notes_photo,
        o.receive_weight_notes_photo,
        IFNULL(shot.carriage_unit_price, o.current_carriage_unit_price) as carriageUnitPrice, <!--运费单价  应付单价-->
        ifnull(toid.carriage_price_unit, 'DUN') as carriagePriceUnit,
        toiw.deliver_weight_notes_photo1, toiw.deliver_weight_notes_photo2, toiw.receive_weight_notes_photo1, toiw.receive_weight_notes_photo2
        FROM  t_order_info o
        LEFT JOIN t_order_cast_changes occ ON o.`code` = occ.order_code AND occ.data_enable = 1
        LEFT JOIN t_order_cast_calc_snapshot shot ON o.code = shot.order_code and shot.data_enable = 1
        LEFT JOIN t_dic_cat_item tdci ON tdci.item_code = occ.capital_transfer_type
        LEFT JOIN t_carrier_info ca ON ca.id = o.carrier_id
        LEFT JOIN t_end_user_info u ON u.id = o.end_driver_id
        LEFT JOIN t_end_user_info u2 ON u2.id = o.end_car_owner_id
        LEFT JOIN t_end_car_info c ON c.id = o.vehicle_id
        LEFT JOIN t_order_state_node item ON item.CODE = o.order_execute_status
        LEFT JOIN t_order_state_node item2 ON item2.CODE = o.order_pay_status
        left join t_order_info_detail toid on o.id = toid.order_id
        left join t_order_info_weight toiw on o.id = toiw.order_id
        WHERE o.enable = 0 and o.order_execute_status not in ('M-20', 'M-10')
        <if test="lineGoodsRelId != null and lineGoodsRelId.size > 0">
            AND o.line_goods_rel_id  IN
            <foreach collection="lineGoodsRelId" item="line" index="item" open="(" close=")" separator=",">
                #{line}
            </foreach>
        </if>
        <if test="captainId != null">
            and o.end_car_owner_id = #{captainId}
        </if>
        <if test="carrierId != null">
            and o.carrier_id = #{carrierId}
        </if>
        <if test="companyId != null">
            and o.company_id = #{companyId}
        </if>
        <if test="bizCode != null">
            AND o.order_business_code like concat('%', #{bizCode}, '%')
        </if>
        <if test="driverName != null">
            AND u.real_name like concat('%', #{driverName}, '%')
        </if>
        <if test="licensePlate != null">
            AND c.vehicle_number like concat('%', #{licensePlate} ,'%')
        </if>
        <if test="projectId != null">
            and o.company_project_id = #{projectId}
        </if>
        <if test="lineGoodsId != null">
            and o.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
            and o.line_goods_rel_id in
            <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                #{lineGoodsRelId}
            </foreach>
        </if>
        <if test="fhsTime != null">
            and o.deliver_order_time >= #{fhsTime}
        </if>
        <if test="fheTime != null">
            and o.deliver_order_time &lt;= #{fheTime}
        </if>
        <if test="shsTime != null">
            AND o.receive_order_time >= #{shsTime}
        </if>
        <if test="sheTime != null">
            AND o.receive_order_time &lt;= #{sheTime}
        </if>
        <if test="fhbdsTime != null">
            AND o.deliver_weight_notes_time >= #{fhbdsTime}
        </if>
        <if test="fhbdeTime != null">
            AND o.deliver_weight_notes_time &lt;= #{fhbdeTime}
        </if>
        <if test="shbdsTime != null">
            AND o.receive_weight_notes_time >= #{shbdsTime}
        </if>
        <if test="shbdeTime != null">
            AND o.receive_weight_notes_time &lt;= #{shbdeTime}
        </if>
        <if test="fihsTime != null">
            AND o.order_finish_time >= #{fihsTime}
        </if>
        <if test="fiheTime != null">
            AND o.order_finish_time &lt;= #{fiheTime}
        </if>
        <if test="fksTime != null">
            AND o.order_finish_time >= #{fksTime}
        </if>
        <if test="fkeTime != null">
            AND o.order_finish_time &lt;= #{fkeTime}
        </if>
        <if test="idArray != null and idArray.size != 0 ">
            AND o.id IN
            <foreach collection="idArray" item="id" index="item" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="payState != null and payState.length != 0 ">
            AND o.order_pay_status IN
            <foreach collection="payState" item="pay" index="item" open="(" close=")" separator=",">
                #{pay}
            </foreach>
            <if test='payStateNode != null and payStateNode !="" '>
                and tosf.state_node_value = #{payStateNode}
            </if>
        </if>
        <if test="orderState != null and orderState.length != 0 ">
            AND o.order_execute_status IN
            <foreach collection="orderState" index="index" item="st" open="(" close=")" separator=",">
                #{st}
            </foreach>
        </if>
        <if test="null != goodsId">
            and o.goods_id = #{goodsId}
        </if>
        <if test="captainIds != null and captainIds.size > 0">
            and o.end_car_owner_id in
            <foreach collection="captainIds" item="captainId" index="item" open="(" close=")" separator=",">
                #{captainId}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and o.company_project_id in
            <foreach collection="projectIds" item="projectId" index="item" open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>
        <if test="lineGoodsId != null">
            and o.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
            and o.line_goods_rel_id in
            <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                #{lineGoodsRelId}
            </foreach>
        </if>
        order by o.create_time desc
    </select>


    <select id="orderPageSum" parameterType="com.lz.vo.AppOrderSearchVO" resultType="com.lz.dto.TXinFaOrderWeightDTO">
        SELECT
        IFNULL(SUM(IFNULL(o.deliver_weight_notes_weight,0)), 0) as deliverWeightNotesWeight,
        IFNULL(SUM(IFNULL(o.receive_weight_notes_weight,0)), 0) as receiveWeightNotesWeight,
        IFNULL(SUM(IFNULL(o.settled_weight,0)), 0) as settledWeight,
        IFNULL(SUM(IFNULL(o.user_confirm_payment_amount,0)), 0) as userConfirmPaymentAmount,
        IFNULL(SUM(IFNULL(o.dispatch_fee,0)), 0) as dispatchFee
        FROM  t_order_info o
        LEFT JOIN t_order_cast_changes occ ON o.`code` = occ.order_code AND occ.data_enable = 1
        LEFT JOIN t_dic_cat_item tdci ON tdci.item_code = occ.capital_transfer_type
        LEFT JOIN t_carrier_info ca ON ca.id = o.carrier_id
        LEFT JOIN t_end_user_info u ON u.id = o.end_driver_id
        LEFT JOIN t_end_user_info u2 ON u2.id = o.end_car_owner_id
        LEFT JOIN t_end_car_info c ON c.id = o.vehicle_id
        WHERE o.enable = 0 and o.order_execute_status not in ('M-20', 'M-10')
        <if test="lineGoodsRelId != null and lineGoodsRelId.size > 0">
            AND o.line_goods_rel_id  IN
            <foreach collection="lineGoodsRelId" item="line" index="item" open="(" close=")" separator=",">
                #{line}
            </foreach>
        </if>
        <if test="captainId != null">
            and o.end_car_owner_id = #{captainId}
        </if>
        <if test="carrierId != null">
            and o.carrier_id = #{carrierId}
        </if>
        <if test="companyId != null">
            and o.company_id = #{companyId}
        </if>
        <if test="bizCode != null">
            AND o.order_business_code like concat('%', #{bizCode}, '%')
        </if>
        <if test="driverName != null">
            AND u.real_name like concat('%', #{driverName}, '%')
        </if>
        <if test="licensePlate != null">
            AND c.vehicle_number like concat('%', #{licensePlate} ,'%')
        </if>
        <if test="projectId != null">
            and o.company_project_id = #{projectId}
        </if>
        <if test="lineGoodsId != null">
            and o.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
            and o.line_goods_rel_id in
            <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                #{lineGoodsRelId}
            </foreach>
        </if>
        <if test="fhsTime != null">
            and o.deliver_order_time >= #{fhsTime}
        </if>
        <if test="fheTime != null">
            and o.deliver_order_time &lt;= #{fheTime}
        </if>
        <if test="shsTime != null">
            AND o.receive_order_time >= #{shsTime}
        </if>
        <if test="sheTime != null">
            AND o.receive_order_time &lt;= #{sheTime}
        </if>
        <if test="fhbdsTime != null">
            AND o.deliver_weight_notes_time >= #{fhbdsTime}
        </if>
        <if test="fhbdeTime != null">
            AND o.deliver_weight_notes_time &lt;= #{fhbdeTime}
        </if>
        <if test="shbdsTime != null">
            AND o.receive_weight_notes_time >= #{shbdsTime}
        </if>
        <if test="shbdeTime != null">
            AND o.receive_weight_notes_time &lt;= #{shbdeTime}
        </if>
        <if test="fihsTime != null">
            AND o.order_finish_time >= #{fihsTime}
        </if>
        <if test="fiheTime != null">
            AND o.order_finish_time &lt;= #{fiheTime}
        </if>
        <if test="fksTime != null">
            AND o.order_finish_time >= #{fksTime}
        </if>
        <if test="fkeTime != null">
            AND o.order_finish_time &lt;= #{fkeTime}
        </if>
        <if test="idArray != null and idArray.size != 0 ">
            AND o.id IN
            <foreach collection="idArray" item="id" index="item" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="payState != null and payState.length != 0 ">
            AND o.order_pay_status IN
            <foreach collection="payState" item="pay" index="item" open="(" close=")" separator=",">
                #{pay}
            </foreach>
            <if test='payStateNode != null and payStateNode !="" '>
                and tosf.state_node_value = #{payStateNode}
            </if>
        </if>
        <if test="orderState != null and orderState.length != 0 ">
            AND o.order_execute_status IN
            <foreach collection="orderState" index="index" item="st" open="(" close=")" separator=",">
                #{st}
            </foreach>
        </if>
        <if test="null != goodsId">
            and o.goods_id = #{goodsId}
        </if>
        <if test="captainIds != null and captainIds.size > 0">
            and o.end_car_owner_id in
            <foreach collection="captainIds" item="captainId" index="item" open="(" close=")" separator=",">
                #{captainId}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and o.company_project_id in
            <foreach collection="projectIds" item="projectId" index="item" open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>
        <if test="lineGoodsId != null">
            and o.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
            and o.line_goods_rel_id in
            <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                #{lineGoodsRelId}
            </foreach>
        </if>
        order by o.create_time desc
    </select>
    <select id="receiveOrderUser" resultType="com.lz.dto.LineUserDTO">
        SELECT DISTINCT
            tsu.nickname AS receiveOrderUserName,
            ta.id AS receiveOrderUserId
        FROM
            t_line_goods_user_rel tlgur
                LEFT JOIN t_account ta ON tlgur.account_info_id = ta.id
                LEFT JOIN t_sys_user tsu ON ta.user_id = tsu.id
        WHERE
            tlgur.company_id = #{companyId}
          AND tlgur.role_code = 'XINFARECEIVEORDER'
          AND tlgur.ENABLE = 0
          AND ta.ENABLE = 0
          AND tsu.ENABLE = 0
    </select>

</mapper>