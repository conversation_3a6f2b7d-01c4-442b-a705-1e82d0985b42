<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.THXOrderInfoMapper">
  <resultMap id="BaseResultMap" type="com.lz.model.TOrderInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="order_business_code" jdbcType="VARCHAR" property="orderBusinessCode" />
    <result column="goods_source_vehicle_driver_info_code" jdbcType="VARCHAR" property="goodsSourceVehicleDriverInfoCode" />
    <result column="good_source_code" jdbcType="VARCHAR" property="goodSourceCode" />
    <result column="carrier_id" jdbcType="INTEGER" property="carrierId" />
    <result column="line_goods_rel_id" jdbcType="INTEGER" property="lineGoodsRelId" />
    <result column="line_id" jdbcType="INTEGER" property="lineId" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="company_project_id" jdbcType="INTEGER" property="companyProjectId" />
    <result column="line_code" jdbcType="VARCHAR" property="lineCode" />
    <result column="line_name" jdbcType="VARCHAR" property="lineName" />
    <result column="line_short_name" jdbcType="VARCHAR" property="lineShortName" />
    <result column="city_from_code" jdbcType="VARCHAR" property="cityFromCode" />
    <result column="from_name" jdbcType="VARCHAR" property="fromName" />
    <result column="from_coordinates" jdbcType="VARCHAR" property="fromCoordinates" />
    <result column="city_end_code" jdbcType="VARCHAR" property="cityEndCode" />
    <result column="end_name" jdbcType="VARCHAR" property="endName" />
    <result column="end_coordinates" jdbcType="VARCHAR" property="endCoordinates" />
    <result column="line_type" jdbcType="VARCHAR" property="lineType" />
    <result column="goods_id" jdbcType="INTEGER" property="goodsId" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="big_kind_code" jdbcType="VARCHAR" property="bigKindCode" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="estimate_goods_weight" jdbcType="DOUBLE" property="estimateGoodsWeight" />
    <result column="current_carriage_unit_price" jdbcType="DECIMAL" property="currentCarriageUnitPrice" />
    <result column="estimate_total_fee" jdbcType="DECIMAL" property="estimateTotalFee" />
    <result column="deliver_order_user_id" jdbcType="INTEGER" property="deliverOrderUserId" />
    <result column="receive_order_user_id" jdbcType="INTEGER" property="receiveOrderUserId" />
    <result column="principal_order_user_id" jdbcType="INTEGER" property="principalOrderUserId" />
    <result column="deliver_order_time" jdbcType="TIMESTAMP" property="deliverOrderTime" />
    <result column="receive_order_time" jdbcType="TIMESTAMP" property="receiveOrderTime" />
    <result column="order_finish_time" jdbcType="TIMESTAMP" property="orderFinishTime" />
    <result column="deliver_weight_notes_time" jdbcType="TIMESTAMP" property="deliverWeightNotesTime" />
    <result column="deliver_weight_notes_weight" jdbcType="DOUBLE" property="deliverWeightNotesWeight" />
    <result column="primary_weight" jdbcType="DOUBLE" property="primaryWeight" />
    <result column="receive_weight_notes_time" jdbcType="TIMESTAMP" property="receiveWeightNotesTime" />
    <result column="receive_weight_notes_weight" jdbcType="DOUBLE" property="receiveWeightNotesWeight" />
    <result column="discharge_weight" jdbcType="DOUBLE" property="dischargeWeight" />
    <result column="rule_payment_amount" jdbcType="DECIMAL" property="rulePaymentAmount" />
    <result column="deliver_weight_notes_photo" jdbcType="VARCHAR" property="deliverWeightNotesPhoto" />
    <result column="receive_weight_notes_photo" jdbcType="VARCHAR" property="receiveWeightNotesPhoto" />
    <result column="settled_weight" jdbcType="DECIMAL" property="settledWeight" />
    <result column="user_confirm_payment_amount" jdbcType="DECIMAL" property="userConfirmPaymentAmount" />
    <result column="contract_status" jdbcType="VARCHAR" property="contractStatus" />
    <result column="vehicle_id" jdbcType="INTEGER" property="vehicleId" />
    <result column="end_driver_id" jdbcType="INTEGER" property="endDriverId" />
    <result column="dispatch_fee" jdbcType="DECIMAL" property="dispatchFee" />
    <result column="service_fee" jdbcType="DECIMAL" property="serviceFee" />
    <result column="other_fee" jdbcType="DECIMAL" property="otherFee" />
    <result column="total_fee" jdbcType="DECIMAL" property="totalFee" />
    <result column="end_car_owner_id" jdbcType="INTEGER" property="endCarOwnerId" />
    <result column="end_agent_id" jdbcType="INTEGER" property="endAgentId" />
    <result column="agent_id" jdbcType="INTEGER" property="agentId" />
    <result column="end_user_car_rel_id" jdbcType="INTEGER" property="endUserCarRelId" />
    <result column="deliver_goods_contacter" jdbcType="VARCHAR" property="deliverGoodsContacter" />
    <result column="deliver_goods_contacter_phone" jdbcType="VARCHAR" property="deliverGoodsContacterPhone" />
    <result column="receive_goods_contacter" jdbcType="VARCHAR" property="receiveGoodsContacter" />
    <result column="receive_goods_contacter_phone" jdbcType="VARCHAR" property="receiveGoodsContacterPhone" />
    <result column="fee_settlement_way" jdbcType="VARCHAR" property="feeSettlementWay" />
    <result column="order_create_type" jdbcType="VARCHAR" property="orderCreateType" />
    <result column="order_execute_status" jdbcType="VARCHAR" property="orderExecuteStatus" />
    <result column="vehicle_gps_bd_status" jdbcType="VARCHAR" property="vehicleGpsBdStatus" />
    <result column="order_pay_status" jdbcType="VARCHAR" property="orderPayStatus" />
    <result column="company_entrust" jdbcType="VARCHAR" property="companyEntrust" />
    <result column="company_client" jdbcType="VARCHAR" property="companyClient" />
    <result column="pack_status" jdbcType="VARCHAR" property="packStatus" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="param1" jdbcType="VARCHAR" property="param1" />
    <result column="param2" jdbcType="VARCHAR" property="param2" />
    <result column="param3" jdbcType="VARCHAR" property="param3" />
    <result column="param4" jdbcType="VARCHAR" property="param4" />
    <result column="param5" jdbcType="VARCHAR" property="param5" />
    <result column="operate_method" jdbcType="VARCHAR" property="operateMethod" />
    <result column="operator_ip" jdbcType="VARCHAR" property="operatorIp" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="turn_history_time" jdbcType="TIMESTAMP" property="turnHistoryTime" />
    <result column="turn_history_type" jdbcType="VARCHAR" property="turnHistoryType" />
    <result column="turn_history_operator" jdbcType="VARCHAR" property="turnHistoryOperator" />
    <result column="share_payment_amount" jdbcType="DECIMAL" property="sharePaymentAmount" />
    <result column="share_dispatch_fee" jdbcType="DECIMAL" property="shareDispatchFee" />
    <result column="unqualified_remark" jdbcType="VARCHAR" property="unqualifiedRemark" />
    <result column="business_assist" jdbcType="BIT" property="businessAssist" />
    <result column="random_hy" jdbcType="VARCHAR" property="randomHy" />
    <result column="pay_method" jdbcType="VARCHAR" property="payMethod" />
  </resultMap>
  <sql id="Base_Column_List">
    id, code, order_business_code, goods_source_vehicle_driver_info_code, good_source_code, 
    carrier_id, line_goods_rel_id, line_id, company_id, company_project_id, line_code, 
    line_name, line_short_name, city_from_code, from_name, from_coordinates, city_end_code, 
    end_name, end_coordinates, line_type, goods_id, goods_name, big_kind_code, goods_unit, 
    estimate_goods_weight, current_carriage_unit_price, estimate_total_fee, deliver_order_user_id, 
    receive_order_user_id, principal_order_user_id, deliver_order_time, receive_order_time, 
    order_finish_time, deliver_weight_notes_time, deliver_weight_notes_weight, primary_weight, 
    receive_weight_notes_time, receive_weight_notes_weight, discharge_weight, rule_payment_amount, 
    deliver_weight_notes_photo, receive_weight_notes_photo, settled_weight, user_confirm_payment_amount, user_confirm_service_fee,
    contract_status, vehicle_id, end_driver_id, dispatch_fee, service_fee, other_fee, 
    total_fee, end_car_owner_id, end_agent_id, agent_id, end_user_car_rel_id, deliver_goods_contacter,
    deliver_goods_contacter_phone, receive_goods_contacter, receive_goods_contacter_phone, 
    fee_settlement_way, order_create_type, order_execute_status, vehicle_gps_bd_status, 
    order_pay_status, company_entrust, company_client, pack_status, remark, param1, param2, 
    param3, param4, param5, operate_method, operator_ip, create_user, create_time, update_user,
    update_time, `enable`, turn_history_time, turn_history_type, turn_history_operator, 
    share_payment_amount, share_dispatch_fee, unqualified_remark, business_assist,random_hy,pay_method
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_order_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from t_order_info
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.lz.model.TOrderInfo" useGeneratedKeys="true">
    insert into t_order_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="code != null">
        code,
      </if>
      <if test="orderBusinessCode != null">
        order_business_code,
      </if>
      <if test="goodsSourceVehicleDriverInfoCode != null">
        goods_source_vehicle_driver_info_code,
      </if>
      <if test="goodSourceCode != null">
        good_source_code,
      </if>
      <if test="carrierId != null">
        carrier_id,
      </if>
      <if test="lineGoodsRelId != null">
        line_goods_rel_id,
      </if>
      <if test="lineId != null">
        line_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyProjectId != null">
        company_project_id,
      </if>
      <if test="lineCode != null">
        line_code,
      </if>
      <if test="lineName != null">
        line_name,
      </if>
      <if test="lineShortName != null">
        line_short_name,
      </if>
      <if test="cityFromCode != null">
        city_from_code,
      </if>
      <if test="fromName != null">
        from_name,
      </if>
      <if test="fromCoordinates != null">
        from_coordinates,
      </if>
      <if test="cityEndCode != null">
        city_end_code,
      </if>
      <if test="endName != null">
        end_name,
      </if>
      <if test="endCoordinates != null">
        end_coordinates,
      </if>
      <if test="lineType != null">
        line_type,
      </if>
      <if test="goodsId != null">
        goods_id,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="bigKindCode != null">
        big_kind_code,
      </if>
      <if test="goodsUnit != null">
        goods_unit,
      </if>
      <if test="estimateGoodsWeight != null">
        estimate_goods_weight,
      </if>
      <if test="currentCarriageUnitPrice != null">
        current_carriage_unit_price,
      </if>
      <if test="estimateTotalFee != null">
        estimate_total_fee,
      </if>
      <if test="deliverOrderUserId != null">
        deliver_order_user_id,
      </if>
      <if test="receiveOrderUserId != null">
        receive_order_user_id,
      </if>
      <if test="principalOrderUserId != null">
        principal_order_user_id,
      </if>
      <if test="deliverOrderTime != null">
        deliver_order_time,
      </if>
      <if test="receiveOrderTime != null">
        receive_order_time,
      </if>
      <if test="orderFinishTime != null">
        order_finish_time,
      </if>
      <if test="deliverWeightNotesTime != null">
        deliver_weight_notes_time,
      </if>
      <if test="deliverWeightNotesWeight != null">
        deliver_weight_notes_weight,
      </if>
      <if test="primaryWeight != null">
        primary_weight,
      </if>
      <if test="receiveWeightNotesTime != null">
        receive_weight_notes_time,
      </if>
      <if test="receiveWeightNotesWeight != null">
        receive_weight_notes_weight,
      </if>
      <if test="dischargeWeight != null">
        discharge_weight,
      </if>
      <if test="rulePaymentAmount != null">
        rule_payment_amount,
      </if>
      <if test="deliverWeightNotesPhoto != null">
        deliver_weight_notes_photo,
      </if>
      <if test="receiveWeightNotesPhoto != null">
        receive_weight_notes_photo,
      </if>
      <if test="settledWeight != null">
        settled_weight,
      </if>
      <if test="userConfirmPaymentAmount != null">
        user_confirm_payment_amount,
      </if>
      <if test="userConfirmServiceFee != null">
        user_confirm_service_fee,
      </if>
      <if test="contractStatus != null">
        contract_status,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="endDriverId != null">
        end_driver_id,
      </if>
      <if test="dispatchFee != null">
        dispatch_fee,
      </if>
      <if test="serviceFee != null">
        service_fee,
      </if>
      <if test="otherFee != null">
        other_fee,
      </if>
      <if test="totalFee != null">
        total_fee,
      </if>
      <if test="endCarOwnerId != null">
        end_car_owner_id,
      </if>
      <if test="endAgentId != null">
        end_agent_id,
      </if>
      <if test="agentId != null">
        agent_id,
      </if>
      <if test="endUserCarRelId != null">
        end_user_car_rel_id,
      </if>
      <if test="deliverGoodsContacter != null">
        deliver_goods_contacter,
      </if>
      <if test="deliverGoodsContacterPhone != null">
        deliver_goods_contacter_phone,
      </if>
      <if test="receiveGoodsContacter != null">
        receive_goods_contacter,
      </if>
      <if test="receiveGoodsContacterPhone != null">
        receive_goods_contacter_phone,
      </if>
      <if test="feeSettlementWay != null">
        fee_settlement_way,
      </if>
      <if test="orderCreateType != null">
        order_create_type,
      </if>
      <if test="orderExecuteStatus != null">
        order_execute_status,
      </if>
      <if test="vehicleGpsBdStatus != null">
        vehicle_gps_bd_status,
      </if>
      <if test="orderPayStatus != null">
        order_pay_status,
      </if>
      <if test="companyEntrust != null">
        company_entrust,
      </if>
      <if test="companyClient != null">
        company_client,
      </if>
      <if test="packStatus != null">
        pack_status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="param1 != null">
        param1,
      </if>
      <if test="param2 != null">
        param2,
      </if>
      <if test="param3 != null">
        param3,
      </if>
      <if test="param4 != null">
        param4,
      </if>
      <if test="param5 != null">
        param5,
      </if>
      <if test="operateMethod != null">
        operate_method,
      </if>
      <if test="operatorIp != null">
        operator_ip,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="turnHistoryTime != null">
        turn_history_time,
      </if>
      <if test="turnHistoryType != null">
        turn_history_type,
      </if>
      <if test="turnHistoryOperator != null">
        turn_history_operator,
      </if>
      <if test="sharePaymentAmount != null">
        share_payment_amount,
      </if>
      <if test="shareDispatchFee != null">
        share_dispatch_fee,
      </if>
      <if test="unqualifiedRemark != null">
        unqualified_remark,
      </if>
      <if test="businessAssist != null">
        business_assist,
      </if>
      <if test="randomHy != null">
        random_hy,
      </if>
      <if test="payMethod != null">
        pay_method,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="orderBusinessCode != null">
        #{orderBusinessCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsSourceVehicleDriverInfoCode != null">
        #{goodsSourceVehicleDriverInfoCode,jdbcType=VARCHAR},
      </if>
      <if test="goodSourceCode != null">
        #{goodSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="carrierId != null">
        #{carrierId,jdbcType=INTEGER},
      </if>
      <if test="lineGoodsRelId != null">
        #{lineGoodsRelId,jdbcType=INTEGER},
      </if>
      <if test="lineId != null">
        #{lineId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyProjectId != null">
        #{companyProjectId,jdbcType=INTEGER},
      </if>
      <if test="lineCode != null">
        #{lineCode,jdbcType=VARCHAR},
      </if>
      <if test="lineName != null">
        #{lineName,jdbcType=VARCHAR},
      </if>
      <if test="lineShortName != null">
        #{lineShortName,jdbcType=VARCHAR},
      </if>
      <if test="cityFromCode != null">
        #{cityFromCode,jdbcType=VARCHAR},
      </if>
      <if test="fromName != null">
        #{fromName,jdbcType=VARCHAR},
      </if>
      <if test="fromCoordinates != null">
        #{fromCoordinates,jdbcType=VARCHAR},
      </if>
      <if test="cityEndCode != null">
        #{cityEndCode,jdbcType=VARCHAR},
      </if>
      <if test="endName != null">
        #{endName,jdbcType=VARCHAR},
      </if>
      <if test="endCoordinates != null">
        #{endCoordinates,jdbcType=VARCHAR},
      </if>
      <if test="lineType != null">
        #{lineType,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="bigKindCode != null">
        #{bigKindCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="estimateGoodsWeight != null">
        #{estimateGoodsWeight,jdbcType=DOUBLE},
      </if>
      <if test="currentCarriageUnitPrice != null">
        #{currentCarriageUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="estimateTotalFee != null">
        #{estimateTotalFee,jdbcType=DECIMAL},
      </if>
      <if test="deliverOrderUserId != null">
        #{deliverOrderUserId,jdbcType=INTEGER},
      </if>
      <if test="receiveOrderUserId != null">
        #{receiveOrderUserId,jdbcType=INTEGER},
      </if>
      <if test="principalOrderUserId != null">
        #{principalOrderUserId,jdbcType=INTEGER},
      </if>
      <if test="deliverOrderTime != null">
        #{deliverOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveOrderTime != null">
        #{receiveOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderFinishTime != null">
        #{orderFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliverWeightNotesTime != null">
        #{deliverWeightNotesTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliverWeightNotesWeight != null">
        #{deliverWeightNotesWeight,jdbcType=DOUBLE},
      </if>
      <if test="primaryWeight != null">
        #{primaryWeight,jdbcType=DOUBLE},
      </if>
      <if test="receiveWeightNotesTime != null">
        #{receiveWeightNotesTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveWeightNotesWeight != null">
        #{receiveWeightNotesWeight,jdbcType=DOUBLE},
      </if>
      <if test="dischargeWeight != null">
        #{dischargeWeight,jdbcType=DOUBLE},
      </if>
      <if test="rulePaymentAmount != null">
        #{rulePaymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="deliverWeightNotesPhoto != null">
        #{deliverWeightNotesPhoto,jdbcType=VARCHAR},
      </if>
      <if test="receiveWeightNotesPhoto != null">
        #{receiveWeightNotesPhoto,jdbcType=VARCHAR},
      </if>
      <if test="settledWeight != null">
        #{settledWeight,jdbcType=DECIMAL},
      </if>
      <if test="userConfirmPaymentAmount != null">
        #{userConfirmPaymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="userConfirmServiceFee != null">
        #{userConfirmServiceFee,jdbcType=DECIMAL},
      </if>
      <if test="contractStatus != null">
        #{contractStatus,jdbcType=VARCHAR},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=INTEGER},
      </if>
      <if test="endDriverId != null">
        #{endDriverId,jdbcType=INTEGER},
      </if>
      <if test="dispatchFee != null">
        #{dispatchFee,jdbcType=DECIMAL},
      </if>
      <if test="serviceFee != null">
        #{serviceFee,jdbcType=DECIMAL},
      </if>
      <if test="otherFee != null">
        #{otherFee,jdbcType=DECIMAL},
      </if>
      <if test="totalFee != null">
        #{totalFee,jdbcType=DECIMAL},
      </if>
      <if test="endCarOwnerId != null">
        #{endCarOwnerId,jdbcType=INTEGER},
      </if>
      <if test="endAgentId != null">
        #{endAgentId,jdbcType=INTEGER},
      </if>
      <if test="agentId != null">
        #{agentId,jdbcType=INTEGER},
      </if>
      <if test="endUserCarRelId != null">
        #{endUserCarRelId,jdbcType=INTEGER},
      </if>
      <if test="deliverGoodsContacter != null">
        #{deliverGoodsContacter,jdbcType=VARCHAR},
      </if>
      <if test="deliverGoodsContacterPhone != null">
        #{deliverGoodsContacterPhone,jdbcType=VARCHAR},
      </if>
      <if test="receiveGoodsContacter != null">
        #{receiveGoodsContacter,jdbcType=VARCHAR},
      </if>
      <if test="receiveGoodsContacterPhone != null">
        #{receiveGoodsContacterPhone,jdbcType=VARCHAR},
      </if>
      <if test="feeSettlementWay != null">
        #{feeSettlementWay,jdbcType=VARCHAR},
      </if>
      <if test="orderCreateType != null">
        #{orderCreateType,jdbcType=VARCHAR},
      </if>
      <if test="orderExecuteStatus != null">
        #{orderExecuteStatus,jdbcType=VARCHAR},
      </if>
      <if test="vehicleGpsBdStatus != null">
        #{vehicleGpsBdStatus,jdbcType=VARCHAR},
      </if>
      <if test="orderPayStatus != null">
        #{orderPayStatus,jdbcType=VARCHAR},
      </if>
      <if test="companyEntrust != null">
        #{companyEntrust,jdbcType=VARCHAR},
      </if>
      <if test="companyClient != null">
        #{companyClient,jdbcType=VARCHAR},
      </if>
      <if test="packStatus != null">
        #{packStatus,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="param1 != null">
        #{param1,jdbcType=VARCHAR},
      </if>
      <if test="param2 != null">
        #{param2,jdbcType=VARCHAR},
      </if>
      <if test="param3 != null">
        #{param3,jdbcType=VARCHAR},
      </if>
      <if test="param4 != null">
        #{param4,jdbcType=VARCHAR},
      </if>
      <if test="param5 != null">
        #{param5,jdbcType=VARCHAR},
      </if>
      <if test="operateMethod != null">
        #{operateMethod,jdbcType=VARCHAR},
      </if>
      <if test="operatorIp != null">
        #{operatorIp,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="turnHistoryTime != null">
        #{turnHistoryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="turnHistoryType != null">
        #{turnHistoryType,jdbcType=VARCHAR},
      </if>
      <if test="turnHistoryOperator != null">
        #{turnHistoryOperator,jdbcType=VARCHAR},
      </if>
      <if test="sharePaymentAmount != null">
        #{sharePaymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="shareDispatchFee != null">
        #{shareDispatchFee,jdbcType=DECIMAL},
      </if>
      <if test="unqualifiedRemark != null">
        #{unqualifiedRemark,jdbcType=VARCHAR},
      </if>
      <if test="businessAssist != null">
        #{businessAssist,jdbcType=BIT},
      </if>
      <if test="randomHy != null">
        #{randomHy,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null">
        #{payMethod,jdbcType=VARCHAR}
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.lz.model.TOrderInfo">
    update t_order_info
    <set>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="orderBusinessCode != null">
        order_business_code = #{orderBusinessCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsSourceVehicleDriverInfoCode != null">
        goods_source_vehicle_driver_info_code = #{goodsSourceVehicleDriverInfoCode,jdbcType=VARCHAR},
      </if>
      <if test="goodSourceCode != null">
        good_source_code = #{goodSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="carrierId != null">
        carrier_id = #{carrierId,jdbcType=INTEGER},
      </if>
      <if test="lineGoodsRelId != null">
        line_goods_rel_id = #{lineGoodsRelId,jdbcType=INTEGER},
      </if>
      <if test="lineId != null">
        line_id = #{lineId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyProjectId != null">
        company_project_id = #{companyProjectId,jdbcType=INTEGER},
      </if>
      <if test="lineCode != null">
        line_code = #{lineCode,jdbcType=VARCHAR},
      </if>
      <if test="lineName != null">
        line_name = #{lineName,jdbcType=VARCHAR},
      </if>
      <if test="lineShortName != null">
        line_short_name = #{lineShortName,jdbcType=VARCHAR},
      </if>
      <if test="cityFromCode != null">
        city_from_code = #{cityFromCode,jdbcType=VARCHAR},
      </if>
      <if test="fromName != null">
        from_name = #{fromName,jdbcType=VARCHAR},
      </if>
      <if test="fromCoordinates != null">
        from_coordinates = #{fromCoordinates,jdbcType=VARCHAR},
      </if>
      <if test="cityEndCode != null">
        city_end_code = #{cityEndCode,jdbcType=VARCHAR},
      </if>
      <if test="endName != null">
        end_name = #{endName,jdbcType=VARCHAR},
      </if>
      <if test="endCoordinates != null">
        end_coordinates = #{endCoordinates,jdbcType=VARCHAR},
      </if>
      <if test="lineType != null">
        line_type = #{lineType,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        goods_id = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="bigKindCode != null">
        big_kind_code = #{bigKindCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="estimateGoodsWeight != null">
        estimate_goods_weight = #{estimateGoodsWeight,jdbcType=DOUBLE},
      </if>
      <if test="currentCarriageUnitPrice != null">
        current_carriage_unit_price = #{currentCarriageUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="estimateTotalFee != null">
        estimate_total_fee = #{estimateTotalFee,jdbcType=DECIMAL},
      </if>
      <if test="deliverOrderUserId != null">
        deliver_order_user_id = #{deliverOrderUserId,jdbcType=INTEGER},
      </if>
      <if test="receiveOrderUserId != null">
        receive_order_user_id = #{receiveOrderUserId,jdbcType=INTEGER},
      </if>
      <if test="principalOrderUserId != null">
        principal_order_user_id = #{principalOrderUserId,jdbcType=INTEGER},
      </if>
      <if test="deliverOrderTime != null">
        deliver_order_time = #{deliverOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveOrderTime != null">
        receive_order_time = #{receiveOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderFinishTime != null">
        order_finish_time = #{orderFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliverWeightNotesTime != null">
        deliver_weight_notes_time = #{deliverWeightNotesTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliverWeightNotesWeight != null">
        deliver_weight_notes_weight = #{deliverWeightNotesWeight,jdbcType=DOUBLE},
      </if>
      <if test="primaryWeight != null">
        primary_weight = #{primaryWeight,jdbcType=DOUBLE},
      </if>
      <if test="receiveWeightNotesTime != null">
        receive_weight_notes_time = #{receiveWeightNotesTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveWeightNotesWeight != null">
        receive_weight_notes_weight = #{receiveWeightNotesWeight,jdbcType=DOUBLE},
      </if>
      <if test="dischargeWeight != null">
        discharge_weight = #{dischargeWeight,jdbcType=DOUBLE},
      </if>
      <if test="rulePaymentAmount != null">
        rule_payment_amount = #{rulePaymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="deliverWeightNotesPhoto != null">
        deliver_weight_notes_photo = #{deliverWeightNotesPhoto,jdbcType=VARCHAR},
      </if>
      <if test="receiveWeightNotesPhoto != null">
        receive_weight_notes_photo = #{receiveWeightNotesPhoto,jdbcType=VARCHAR},
      </if>
      <if test="settledWeight != null">
        settled_weight = #{settledWeight,jdbcType=DECIMAL},
      </if>
      <if test="userConfirmPaymentAmount != null">
        user_confirm_payment_amount = #{userConfirmPaymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="userConfirmServiceFee != null">
        user_confirm_service_fee = #{userConfirmServiceFee,jdbcType=DECIMAL},
      </if>
      <if test="contractStatus != null">
        contract_status = #{contractStatus,jdbcType=VARCHAR},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=INTEGER},
      </if>
      <if test="endDriverId != null">
        end_driver_id = #{endDriverId,jdbcType=INTEGER},
      </if>
      <if test="dispatchFee != null">
        dispatch_fee = #{dispatchFee,jdbcType=DECIMAL},
      </if>
      <if test="serviceFee != null">
        service_fee = #{serviceFee,jdbcType=DECIMAL},
      </if>
      <if test="otherFee != null">
        other_fee = #{otherFee,jdbcType=DECIMAL},
      </if>
      <if test="totalFee != null">
        total_fee = #{totalFee,jdbcType=DECIMAL},
      </if>
      <if test="endCarOwnerId != null">
        end_car_owner_id = #{endCarOwnerId,jdbcType=INTEGER},
      </if>
      <if test="endAgentId != null">
        end_agent_id = #{endAgentId,jdbcType=INTEGER},
      </if>
      <if test="agentId != null">
        agent_id = #{agentId,jdbcType=INTEGER},
      </if>
      <if test="endUserCarRelId != null">
        end_user_car_rel_id = #{endUserCarRelId,jdbcType=INTEGER},
      </if>
      <if test="deliverGoodsContacter != null">
        deliver_goods_contacter = #{deliverGoodsContacter,jdbcType=VARCHAR},
      </if>
      <if test="deliverGoodsContacterPhone != null">
        deliver_goods_contacter_phone = #{deliverGoodsContacterPhone,jdbcType=VARCHAR},
      </if>
      <if test="receiveGoodsContacter != null">
        receive_goods_contacter = #{receiveGoodsContacter,jdbcType=VARCHAR},
      </if>
      <if test="receiveGoodsContacterPhone != null">
        receive_goods_contacter_phone = #{receiveGoodsContacterPhone,jdbcType=VARCHAR},
      </if>
      <if test="feeSettlementWay != null">
        fee_settlement_way = #{feeSettlementWay,jdbcType=VARCHAR},
      </if>
      <if test="orderCreateType != null">
        order_create_type = #{orderCreateType,jdbcType=VARCHAR},
      </if>
      <if test="orderExecuteStatus != null">
        order_execute_status = #{orderExecuteStatus,jdbcType=VARCHAR},
      </if>
      <if test="vehicleGpsBdStatus != null">
        vehicle_gps_bd_status = #{vehicleGpsBdStatus,jdbcType=VARCHAR},
      </if>
      <if test="orderPayStatus != null">
        order_pay_status = #{orderPayStatus,jdbcType=VARCHAR},
      </if>
      <if test="companyEntrust != null">
        company_entrust = #{companyEntrust,jdbcType=VARCHAR},
      </if>
      <if test="companyClient != null">
        company_client = #{companyClient,jdbcType=VARCHAR},
      </if>
      <if test="packStatus != null">
        pack_status = #{packStatus,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="param1 != null">
        param1 = #{param1,jdbcType=VARCHAR},
      </if>
      <if test="param2 != null">
        param2 = #{param2,jdbcType=VARCHAR},
      </if>
      <if test="param3 != null">
        param3 = #{param3,jdbcType=VARCHAR},
      </if>
      <if test="param4 != null">
        param4 = #{param4,jdbcType=VARCHAR},
      </if>
      <if test="param5 != null">
        param5 = #{param5,jdbcType=VARCHAR},
      </if>
      <if test="operateMethod != null">
        operate_method = #{operateMethod,jdbcType=VARCHAR},
      </if>
      <if test="operatorIp != null">
        operator_ip = #{operatorIp,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
      <if test="uploadData != null">
        uploadData = #{uploadData,jdbcType=BIT},
      </if>
      <if test="turnHistoryTime != null">
        turn_history_time = #{turnHistoryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="turnHistoryType != null">
        turn_history_type = #{turnHistoryType,jdbcType=VARCHAR},
      </if>
      <if test="turnHistoryOperator != null">
        turn_history_operator = #{turnHistoryOperator,jdbcType=VARCHAR},
      </if>
      <if test="sharePaymentAmount != null">
        share_payment_amount = #{sharePaymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="shareDispatchFee != null">
        share_dispatch_fee = #{shareDispatchFee,jdbcType=DECIMAL},
      </if>
      <if test="unqualifiedRemark != null">
        unqualified_remark = #{unqualifiedRemark,jdbcType=VARCHAR},
      </if>
      <if test="businessAssist != null">
        business_assist = #{businessAssist,jdbcType=BIT},
      </if>
      <if test="randomHy != null">
        random_hy = #{randomHy,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null">
        pay_method = #{payMethod,jdbcType=VARCHAR}
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lz.model.TOrderInfo">
    update t_order_info
    set code = #{code,jdbcType=VARCHAR},
      order_business_code = #{orderBusinessCode,jdbcType=VARCHAR},
      goods_source_vehicle_driver_info_code = #{goodsSourceVehicleDriverInfoCode,jdbcType=VARCHAR},
      good_source_code = #{goodSourceCode,jdbcType=VARCHAR},
      carrier_id = #{carrierId,jdbcType=INTEGER},
      line_goods_rel_id = #{lineGoodsRelId,jdbcType=INTEGER},
      line_id = #{lineId,jdbcType=INTEGER},
      company_id = #{companyId,jdbcType=INTEGER},
      company_project_id = #{companyProjectId,jdbcType=INTEGER},
      line_code = #{lineCode,jdbcType=VARCHAR},
      line_name = #{lineName,jdbcType=VARCHAR},
      line_short_name = #{lineShortName,jdbcType=VARCHAR},
      city_from_code = #{cityFromCode,jdbcType=VARCHAR},
      from_name = #{fromName,jdbcType=VARCHAR},
      from_coordinates = #{fromCoordinates,jdbcType=VARCHAR},
      city_end_code = #{cityEndCode,jdbcType=VARCHAR},
      end_name = #{endName,jdbcType=VARCHAR},
      end_coordinates = #{endCoordinates,jdbcType=VARCHAR},
      line_type = #{lineType,jdbcType=VARCHAR},
      goods_id = #{goodsId,jdbcType=INTEGER},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      big_kind_code = #{bigKindCode,jdbcType=VARCHAR},
      goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      estimate_goods_weight = #{estimateGoodsWeight,jdbcType=DOUBLE},
      current_carriage_unit_price = #{currentCarriageUnitPrice,jdbcType=DECIMAL},
      estimate_total_fee = #{estimateTotalFee,jdbcType=DECIMAL},
      deliver_order_user_id = #{deliverOrderUserId,jdbcType=INTEGER},
      receive_order_user_id = #{receiveOrderUserId,jdbcType=INTEGER},
      principal_order_user_id = #{principalOrderUserId,jdbcType=INTEGER},
      deliver_order_time = #{deliverOrderTime,jdbcType=TIMESTAMP},
      receive_order_time = #{receiveOrderTime,jdbcType=TIMESTAMP},
      order_finish_time = #{orderFinishTime,jdbcType=TIMESTAMP},
      deliver_weight_notes_time = #{deliverWeightNotesTime,jdbcType=TIMESTAMP},
      deliver_weight_notes_weight = #{deliverWeightNotesWeight,jdbcType=DOUBLE},
      primary_weight = #{primaryWeight,jdbcType=DOUBLE},
      receive_weight_notes_time = #{receiveWeightNotesTime,jdbcType=TIMESTAMP},
      receive_weight_notes_weight = #{receiveWeightNotesWeight,jdbcType=DOUBLE},
      discharge_weight = #{dischargeWeight,jdbcType=DOUBLE},
      rule_payment_amount = #{rulePaymentAmount,jdbcType=DECIMAL},
      deliver_weight_notes_photo = #{deliverWeightNotesPhoto,jdbcType=VARCHAR},
      receive_weight_notes_photo = #{receiveWeightNotesPhoto,jdbcType=VARCHAR},
      settled_weight = #{settledWeight,jdbcType=DECIMAL},
      user_confirm_payment_amount = #{userConfirmPaymentAmount,jdbcType=DECIMAL},
      user_confirm_service_fee = #{userConfirmServiceFee,jdbcType=DECIMAL},
      contract_status = #{contractStatus,jdbcType=VARCHAR},
      vehicle_id = #{vehicleId,jdbcType=INTEGER},
      end_driver_id = #{endDriverId,jdbcType=INTEGER},
      dispatch_fee = #{dispatchFee,jdbcType=DECIMAL},
      service_fee = #{serviceFee,jdbcType=DECIMAL},
      other_fee = #{otherFee,jdbcType=DECIMAL},
      total_fee = #{totalFee,jdbcType=DECIMAL},
      end_car_owner_id = #{endCarOwnerId,jdbcType=INTEGER},
      end_agent_id = #{endAgentId,jdbcType=INTEGER},
      agent_id = #{agentId,jdbcType=INTEGER},
      end_user_car_rel_id = #{endUserCarRelId,jdbcType=INTEGER},
      deliver_goods_contacter = #{deliverGoodsContacter,jdbcType=VARCHAR},
      deliver_goods_contacter_phone = #{deliverGoodsContacterPhone,jdbcType=VARCHAR},
      receive_goods_contacter = #{receiveGoodsContacter,jdbcType=VARCHAR},
      receive_goods_contacter_phone = #{receiveGoodsContacterPhone,jdbcType=VARCHAR},
      fee_settlement_way = #{feeSettlementWay,jdbcType=VARCHAR},
      order_create_type = #{orderCreateType,jdbcType=VARCHAR},
      order_execute_status = #{orderExecuteStatus,jdbcType=VARCHAR},
      vehicle_gps_bd_status = #{vehicleGpsBdStatus,jdbcType=VARCHAR},
      order_pay_status = #{orderPayStatus,jdbcType=VARCHAR},
      company_entrust = #{companyEntrust,jdbcType=VARCHAR},
      company_client = #{companyClient,jdbcType=VARCHAR},
      pack_status = #{packStatus,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      param1 = #{param1,jdbcType=VARCHAR},
      param2 = #{param2,jdbcType=VARCHAR},
      param3 = #{param3,jdbcType=VARCHAR},
      param4 = #{param4,jdbcType=VARCHAR},
      param5 = #{param5,jdbcType=VARCHAR},
      operate_method = #{operateMethod,jdbcType=VARCHAR},
      operator_ip = #{operatorIp,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      `enable` = #{enable,jdbcType=BIT},
      turn_history_time = #{turnHistoryTime,jdbcType=TIMESTAMP},
      turn_history_type = #{turnHistoryType,jdbcType=VARCHAR},
      turn_history_operator = #{turnHistoryOperator,jdbcType=VARCHAR},
      share_payment_amount = #{sharePaymentAmount,jdbcType=DECIMAL},
      share_dispatch_fee = #{shareDispatchFee,jdbcType=DECIMAL},
      unqualified_remark = #{unqualifiedRemark,jdbcType=VARCHAR},
      business_assist = #{businessAssist,jdbcType=BIT},
      random_hy = #{randomHy,jdbcType=VARCHAR},
      pay_method = #{payMethod,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectOrderInfoByPackCode" parameterType="java.lang.String" resultType="com.lz.model.TOrderInfo">
    SELECT
      o.id,
      o.CODE,
      o.order_business_code,
      o.goods_source_vehicle_driver_info_code,
      o.good_source_code,
      o.carrier_id,
      o.line_goods_rel_id,
      o.line_id,
      o.company_id,
      o.company_project_id,
      o.line_code,
      o.line_name,
      o.line_short_name,
      o.city_from_code,
      o.from_name,
      o.from_coordinates,
      o.city_end_code,
      o.end_name,
      o.end_coordinates,
      o.line_type,
      o.goods_id,
      o.goods_name,
      o.big_kind_code,
      o.goods_unit,
      o.estimate_goods_weight,
      o.current_carriage_unit_price,
      o.estimate_total_fee,
      o.deliver_order_user_id,
      o.receive_order_user_id,
      o.principal_order_user_id,
      o.deliver_order_time,
      o.receive_order_time,
      o.order_finish_time,
      o.deliver_weight_notes_time,
      o.deliver_weight_notes_weight,
      o.primary_weight,
      o.receive_weight_notes_time,
      o.receive_weight_notes_weight,
      o.discharge_weight,
      o.rule_payment_amount,
      o.deliver_weight_notes_photo,
      o.receive_weight_notes_photo,
      o.settled_weight,
      o.user_confirm_payment_amount,
      o.user_confirm_service_fee,
      contract_status,
      o.vehicle_id,
      o.end_driver_id,
      o.dispatch_fee,
      o.service_fee,
      o.other_fee,
      o.total_fee,
      o.end_car_owner_id,
      o.end_agent_id,
      o.agent_id,
      o.end_user_car_rel_id,
      o.deliver_goods_contacter,
      deliver_goods_contacter_phone,
      o.receive_goods_contacter,
      o.receive_goods_contacter_phone,
      o.fee_settlement_way,
      o.order_create_type,
      o.order_execute_status,
      o.vehicle_gps_bd_status,
      o.order_pay_status,
      o.company_entrust,
      o.company_client,
      o.pack_status,
      o.remark,
      o.param1,
      o.param2,
      o.param3,
      o.param4,
      o.param5,
      o.operate_method,
      o.operator_ip,
      o.create_user,
      o.create_time,
      o.update_user,
      o.update_time,
      o.`enable`,
      o.turn_history_time,
      o.turn_history_type,
      o.turn_history_operator,
      o.share_payment_amount,
      o.share_dispatch_fee,
      o.unqualified_remark,
      o.business_assist,
      o.random_hy,
      o.pay_method
    FROM
      t_order_info o
        LEFT JOIN t_order_pack_detail od ON o.CODE = od.order_code
    WHERE
      od.pack_pay_code =  #{packCode}
  </select>

  <select id="selectOrderInfoByPayDetailCode" resultType="com.lz.dto.OrderInfoDTO">
    select toi.id, toi.code, toi.order_execute_status, toi.order_pay_status, toi.pack_status,
           tocc.capital_transfer_type, tocc.capital_transfer_pattern,
           topd.trade_type, topd.operate_state
    from t_order_info toi
    left join t_order_cast_changes tocc on toi.code = tocc.order_code
    left join t_order_pay_detail topd on tocc.code = topd.order_cast_change_code
    where topd.code = #{code}
  </select>

  <select id="selectOrderByCode" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"></include>
    FROM t_order_info toi
    WHERE toi.`code` = #{code}
  </select>

  <update id="batchUpdateOrderPayStatus">
    UPDATE t_order_info toi
    SET order_pay_status = #{orderPayStatus},
    update_time = NOW()
    WHERE
    toi.`code` in
    <foreach collection="list" index="index" item="code" open="(" close=")" separator=",">
      #{code}
    </foreach>
  </update>

  <update id="batchUpdateOrderExecutePayStatus">
    UPDATE t_order_info toi
    SET
    order_execute_status = #{orderExecuteStatus},
    order_pay_status = #{orderPayStatus},
    <if test="orderPayStatus == 'M095'">
      order_finish_time = null,
    </if>
    update_time = #{returnTime}
    WHERE
    toi.`code` in
    <foreach collection="list" index="index" item="code" open="(" close=")" separator=",">
      #{code}
    </foreach>
  </update>

  <update id="refundUpdateOrderInfo">
    UPDATE t_order_info toi
    SET toi.order_execute_status = #{orderExecuteStatus},
        toi.order_pay_status = #{orderPayStatus},
        toi.update_time = #{returnTime},
        toi.order_finish_time = null
    WHERE
      toi.id = #{id};
  </update>

  <update id="updateStateByOrderCode">
    update
      t_order_info
    <set>
      <if test="null != orderExecuteStatus">
        order_execute_status = #{orderExecuteStatus},
      </if>
      <if test="null != orderPayStatus">
        order_pay_status = #{orderPayStatus},
      </if>
      <if test="null != orderPayStatus or null != orderPayStatus">
        toi.update_time = now(),
      </if>
    </set>
    where code = #{orderCode}
  </update>

  <select id="selectOrderGroupByCompanyId" resultType="com.lz.dto.OrderGroupDTO">
    SELECT
      company_id,
      GROUP_CONCAT(code) codes,
      group_concat(order_business_code) order_buisness_code
    FROM
      t_order_info
    WHERE
      code in
        <foreach collection="list" item="code" index="index" open="(" close=")" separator=",">
          #{code}
        </foreach>
    GROUP BY
      company_id
  </select>

  <select id="selectOrderPackInfoByPayDetailCode" resultType="com.lz.dto.OrderInfoDTO">
    select
    tocc.capital_transfer_type, tocc.capital_transfer_pattern,
    topd.trade_type, topd.operate_state
    from t_order_pack_info topi
    left join t_order_cast_changes tocc on topi.code = tocc.order_code
    left join t_order_pay_detail topd on tocc.code = topd.order_cast_change_code
    where topd.code = #{code}
  </select>

  <select id="selectAdvanceOrderGroupByCompanyId" resultType="com.lz.dto.OrderGroupDTO">
    SELECT
    company_id,
    GROUP_CONCAT(code) codes,
    group_concat(order_business_code) order_buisness_code
    FROM
    t_order_info
    WHERE
    order_business_code in
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
      #{item.orderBusinessCode}
    </foreach>
    GROUP BY
    company_id
  </select>

  <select id="selectKhyOrderReport" resultType="com.lz.dto.KhyDataXmlDTO">
    SELECT
      DISTINCT
      orders.code orderCode,
      orders.deliver_order_time,
      orders.receive_order_time,
      tos.operate_time deliverWeightNotesTime,
      tos2.operate_time receiveWeightNotesTime,
      cp.project_name,
      line.line_name,
      orders.order_business_code,
      tci.company_name companyName,
      tci.company_contacts_phone,
      tci.business_license_no,
      case
        when orders.pack_status ='0' then  TRUNCATE((orders.user_confirm_payment_amount),2)
        when orders.pack_status ='1' then  TRUNCATE(orders.share_payment_amount,2)
        end userConfirmPaymentAmount,
      orders.deliver_goods_contacter consignor,
      orders.deliver_goods_contacter_phone consignorPhone,
      line.city_from_code cityFromCode,
      line.from_detail_name fromDetailName,
      orders.receive_goods_contacter consignee,
      orders.receive_goods_contacter_phone consigneePhone,
      line.city_end_code cityEndCode,
      line.end_detail_name endDetailName,
      goods.goods_name goodsInfo,
      case
        when orders.deliver_weight_notes_weight is null or deliver_weight_notes_weight = 0 then orders.primary_weight
        else orders.deliver_weight_notes_weight
        end deliverWeightNotesWeight,
      userI.real_name nameOfPerson,
      userI.phone telephoneNumber,
      userI.idcard personalIdentityDocument,
      car.vehicle_number vehicleNumber,
      case
        when orders.pack_status ='0' then  TRUNCATE((orders.dispatch_fee),2)
        when orders.pack_status ='1' then  TRUNCATE(orders.share_dispatch_fee,2)
        end dispatchFee,
      ifnull(teuor.open_real_name,userI.real_name) bankAccountName,
      teuor.bank_account_no bankCardNumber,
      ifnull(teuor.open_id_card,userI.idcard) payeeIdCard,
      tci.khyuploaded_status companyKhyUploadedStatus, userI.khyuploaded_status driverKhyUploadedStatus, car.khyuploaded_status carKhyUploadedStatus,
      tccor.partner_acc_id companyAccId, topd.operate_time payTime, topd.code orderPayDetailCode
    FROM
      t_order_info orders
        LEFT JOIN t_account tca ON orders.receive_order_user_id = tca.id
        LEFT JOIN t_company_project cp on cp.id = orders.company_project_id
        LEFT JOIN t_company_info tci on orders.company_id=tci.id
        left join t_company_account tcoma on tcoma.account_id = tca.id and tcoma.company_id = tci.id
        left join t_carrier_company_open_role tccor on tci.id = tccor.carrier_company_id and tccor.user_open_role = 'BD'
        LEFT JOIN t_carrier_info carrier ON orders.carrier_id = carrier.id
        LEFT JOIN t_end_car_info car ON car.id = orders.vehicle_id
        LEFT JOIN t_end_user_info userI ON userI.id = orders.end_driver_id
        left join t_end_user_open_role teuor on userI.id = teuor.end_user_id
        LEFT JOIN t_line_info line ON line.id = orders.line_id
        LEFT JOIN t_goods_info goods ON goods.id = orders.goods_id
      left join t_order_state tos on tos.order_code = orders.code and tos.state_node_value in ('S0300','S0301','S0302','S0303','S0304','S0305')
      left join t_order_state tos2 on tos2.order_code = orders.code and tos2.state_node_value in ('S0400','S0401','S0402','S0403','S0404','S0405')
        LEFT JOIN t_order_send send ON send.order_id = orders.id
        left join t_order_pay_info topi on orders.code = topi.order_code
        left join t_order_pay_detail topd on topi.code = topd.order_pay_code and topd.trade_type in ('CABALANCE_PAY', 'SPLIT_FALL') and topd.trade_status = 'TRADE_FINISHED'
    WHERE
      orders.id =#{id} and orders.enable = 0 and send.id is null
    limit 1

  </select>

  <select id="selectKhyOrderInfo" resultType="com.lz.dto.KhyOrderInfoDTO">
    select
        <include refid="Base_Column_List"></include>
    from t_order_info
    where id = #{id}
  </select>

</mapper>