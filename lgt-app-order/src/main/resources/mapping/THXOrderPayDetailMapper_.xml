<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.THXOrderPayDetailMapper">
    <sql id="Base_Column_List">
        id, code, order_pay_code, order_cast_change_code, bank_union_no, bank_no, card_holder,
        card_type, bank_card_id, trade_type, operate_state, operater_id, operate_time, return_time,
        trade_status, error_code, error_msg, inner_trade_no, remark, param1, param2, param3,
        param4, create_user, create_time, update_user, update_time, `enable`
    </sql>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.lz.model.TOrderPayDetail" useGeneratedKeys="true">
        insert into t_order_pay_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">
                code,
            </if>
            <if test="orderPayCode != null">
                order_pay_code,
            </if>
            <if test="orderCastChangeCode != null">
                order_cast_change_code,
            </if>
            <if test="bankUnionNo != null">
                bank_union_no,
            </if>
            <if test="bankNo != null">
                bank_no,
            </if>
            <if test="cardHolder != null">
                card_holder,
            </if>
            <if test="cardType != null">
                card_type,
            </if>
            <if test="bankCardId != null">
                bank_card_id,
            </if>
            <if test="tradeType != null">
                trade_type,
            </if>
            <if test="operateState != null">
                operate_state,
            </if>
            <if test="operaterId != null">
                operater_id,
            </if>
            <if test="operateTime != null">
                operate_time,
            </if>
            <if test="returnTime != null">
                return_time,
            </if>
            <if test="tradeStatus != null">
                trade_status,
            </if>
            <if test="errorCode != null">
                error_code,
            </if>
            <if test="errorMsg != null">
                error_msg,
            </if>
            <if test="innerTradeNo != null">
                inner_trade_no,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="param1 != null">
                param1,
            </if>
            <if test="param2 != null">
                param2,
            </if>
            <if test="param3 != null">
                param3,
            </if>
            <if test="param4 != null">
                param4,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="enable != null">
                `enable`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="orderPayCode != null">
                #{orderPayCode,jdbcType=VARCHAR},
            </if>
            <if test="orderCastChangeCode != null">
                #{orderCastChangeCode,jdbcType=VARCHAR},
            </if>
            <if test="bankUnionNo != null">
                #{bankUnionNo,jdbcType=VARCHAR},
            </if>
            <if test="bankNo != null">
                #{bankNo,jdbcType=VARCHAR},
            </if>
            <if test="cardHolder != null">
                #{cardHolder,jdbcType=VARCHAR},
            </if>
            <if test="cardType != null">
                #{cardType,jdbcType=VARCHAR},
            </if>
            <if test="bankCardId != null">
                #{bankCardId,jdbcType=INTEGER},
            </if>
            <if test="tradeType != null">
                #{tradeType,jdbcType=VARCHAR},
            </if>
            <if test="operateState != null">
                #{operateState,jdbcType=VARCHAR},
            </if>
            <if test="operaterId != null">
                #{operaterId,jdbcType=INTEGER},
            </if>
            <if test="operateTime != null">
                #{operateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="returnTime != null">
                #{returnTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tradeStatus != null">
                #{tradeStatus,jdbcType=VARCHAR},
            </if>
            <if test="errorCode != null">
                #{errorCode,jdbcType=VARCHAR},
            </if>
            <if test="errorMsg != null">
                #{errorMsg,jdbcType=VARCHAR},
            </if>
            <if test="innerTradeNo != null">
                #{innerTradeNo,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="param1 != null">
                #{param1,jdbcType=VARCHAR},
            </if>
            <if test="param2 != null">
                #{param2,jdbcType=VARCHAR},
            </if>
            <if test="param3 != null">
                #{param3,jdbcType=VARCHAR},
            </if>
            <if test="param4 != null">
                #{param4,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="enable != null">
                #{enable,jdbcType=BIT},
            </if>
        </trim>
    </insert>

    <select id="selectByOrderPayDetailKey" parameterType="java.lang.String" resultType="com.lz.dto.THxOrderPayInfoDTO">
        SELECT
            p.id orderPayDetailId,
            p.`code` AS orderPayDetailCode,
            p.order_cast_change_code,
            p.operate_time,
            p.create_time,
            p.trade_type,
            p.bank_union_no,
            p.bank_no,
            p.card_holder,
            p.bank_card_id,
            p.operate_state,
            p.param1 walletId,
            p.trade_status,
            p.inner_trade_no,
            p.create_user createUser,
            p.create_user txCreateUser,
            d.id orderPayId,
            d.CODE payCode,
            d.pay_method,
            d.fee_settlement_way,
            d.line_goods_carriage_rule_id,
            d.order_pay_status,
            d.carrier_id,
            d.order_prepay_amount,
            d.order_actual_payment,
            d.order_total_payment,
            d.param1,
            c.end_driver_wallet_id,
            c.company_wallet_id,
            c.company_project_wallet_id,
            c.carrier_wallet_id,
            c.order_code,
            c.carriage_fee,
            c.dispatch_fee,
            c.total_fee,
            c.withdraw_type,
            c.capital_transfer_type,
            c.current_dispatch_rate,
            ifnull( c.create_user, '' ) operateCreateUser,
            c.capital_transfer_pattern,
            ifnull( c.service_fee, 0 ) serviceFee,
            c.user_oper,
            c.end_user_type,
            toi.id orderId,
            toi.order_business_code,
            toi.end_car_owner_id,
            toi.agent_id,
            toi.end_driver_id,
            toi.company_id,
            tgsvdi.CODE cargoOrder,
            toi.create_time orderCreateTime,
            toi.vehicle_id,
            toi.goods_name,
            toi.settled_weight,
            toi.estimate_goods_weight,
            toi.primary_weight,
            toi.company_project_id,
            toi.pack_status,
            tgsvdi.goods_source_code,
            tzaoi2.partner_acc_id carrierAccId,
            tzaoi.partner_acc_id companyAccId,
            tzaoi3.partner_acc_id driverAccId,
            tzaoi4.partner_acc_id captainAccId,
            tzaoi5.partner_acc_id agentAccId,
            tzaoi3.id driverOpenRoleId,
            tzaoi4.id captainOpenRoleId,
            tzaoi5.id agentOpenRoleId,
            tzw.id captainWalletId,
            tzw2.id agentWalletId,
            p.remark,
            tci.business_license_no,
            toid.illegal_order,
            if(toid.illegal_order = 1, toid.deduction, 0) as illegalDeduction,
            toinsurance.id insuranceId, if(toinsurance.id is not null and toinsurance.insure > 0, toinsurance.insured_amount, 0) as insured_amount, toinsurance.insure,
            ifnull(toinsurance.insurance_cancellation, 0) as insurance_cancellation
        FROM
            t_order_pay_detail p
                LEFT JOIN t_order_pay_info d ON p.order_pay_code = d.`code`
                LEFT JOIN t_order_cast_changes c ON p.order_cast_change_code = c.`code`
                LEFT JOIN t_order_info toi ON toi.CODE = c.order_code
                LEFT JOIN t_company_info tci ON toi.company_id = tci.id
                LEFT JOIN t_goods_source_vehicle_driver_info tgsvdi ON toi.CODE = tgsvdi.order_id
                LEFT JOIN t_carrie_account tca2 ON tca2.carrier_id = toi.carrier_id
                LEFT JOIN t_account ta2 ON tca2.account_id = ta2.id
                LEFT JOIN t_zt_account_open_info tzaoi2 ON ta2.id = tzaoi2.account_id
                LEFT JOIN t_company_account tca ON toi.company_id = tca.company_id
                LEFT JOIN t_account ta ON tca.account_id = ta.id
                LEFT JOIN t_zt_account_open_info tzaoi ON ta.id = tzaoi.account_id
                LEFT JOIN t_enduser_account tea ON toi.end_driver_id = tea.enduser_id
                LEFT JOIN t_zt_account_open_info tzaoi3 ON tea.account_id = tzaoi3.account_id
                LEFT JOIN t_enduser_account tea2 ON toi.end_car_owner_id = tea2.enduser_id
                LEFT JOIN t_zt_account_open_info tzaoi4 ON tea2.account_id = tzaoi4.account_id
                LEFT JOIN t_enduser_account tea3 ON toi.agent_id = tea3.enduser_id
                LEFT JOIN t_zt_account_open_info tzaoi5 ON tea3.account_id = tzaoi5.account_id
                LEFT JOIN t_zt_wallet tzw ON tzaoi4.account_id = tzw.account_id
                LEFT JOIN t_zt_wallet tzw2 ON tzaoi5.account_id = tzw2.account_id
                left join t_order_info_detail toid on toi.id = toid.order_id
                left join t_order_insurance toinsurance on toi.order_business_code = toinsurance.order_business_code and toinsurance.enable = 0
        WHERE
            p.code = #{code,jdbcType=VARCHAR}
            AND ta.if_main_account = 1
            AND ta2.if_main_account = 1
            AND tgsvdi.ENABLE = 0
    </select>

    <select id="selectOrderPackByOrderPayDetailKey" resultType="com.lz.dto.THxOrderPayInfoDTO">
        select p.id                      orderPayDetailId,
        p.`code` as               orderPayDetailCode,
        topi.id packId,
        topi.code packCode,
        topi.virtual_order_no,
        topi.appointment_payment_cash carriage_fee,
        topi.recount_dispatch_fee dispatch_fee,
        topi.total_selected_orders_service_fee service_fee,
        topi.appointment_payment_cash + topi.recount_dispatch_fee as total_fee,
        p.order_cast_change_code,
        p.operate_time,
        p.create_time,
        p.trade_type,
        p.bank_union_no,
        p.bank_no,
        p.card_holder,
        p.bank_card_id,
        p.operate_state,
        p.param1                  walletId,
        p.trade_status,
        p.inner_trade_no,
        p.create_user             createUser,
        p.create_user             txCreateUser,
        d.id                      orderPayId,
        d.code                    payCode,
        d.pay_method,
        d.fee_settlement_way,
        d.line_goods_carriage_rule_id,
        d.order_pay_status,
        d.carrier_id,
        d.order_prepay_amount,
        d.order_actual_payment,
        d.order_total_payment,
        d.param1,
        c.end_driver_wallet_id,
        c.company_wallet_id,
        c.company_project_wallet_id,
        c.carrier_wallet_id,
        toi.code order_code,
        c.withdraw_type,
        c.capital_transfer_type,
        c.current_dispatch_rate,
        ifnull(c.create_user, '') operateCreateUser,
        c.capital_transfer_pattern,
        c.user_oper,
        c.end_user_type,
        toi.id                   orderId,
        toi.order_business_code,
        toi.end_car_owner_id,
        toi.agent_id,
        toi.end_driver_id,
        toi.company_id,
        tgsvdi.code               cargoOrder,
        toi.create_time           orderCreateTime,
        toi.vehicle_id,
        toi.goods_name, toi.settled_weight, toi.company_project_id, toi.pack_status,
        tgsvdi.goods_source_code,
        tzaoi2.partner_acc_id carrierAccId,
        tzaoi.partner_acc_id companyAccId,
        tzaoi3.partner_acc_id driverAccId,
        tzaoi4.partner_acc_id captainAccId,
        tzaoi5.partner_acc_id agentAccId,
        tzaoi3.id driverOpenRoleId,
        tzaoi4.id captainOpenRoleId,
        tzaoi5.id agentOpenRoleId,
        tzw.id captainWalletId,
        tzw2.id agentWalletId,
        p.remark
        from t_order_pay_detail p
        LEFT JOIN t_order_pay_info d on p.order_pay_code = d.`code`
        LEFT JOIN t_order_cast_changes c on p.order_cast_change_code = c.`code`
        left join t_order_pack_info topi on topi.code = c.order_code
        left join t_order_pack_detail topdd on topi.code = topdd.pack_pay_code
        left join t_order_info toi on topdd.order_code = toi.code
        left join t_goods_source_vehicle_driver_info tgsvdi on toi.code = tgsvdi.order_id and tgsvdi.enable = 0
        LEFT JOIN t_carrie_account tca2 ON tca2.carrier_id = toi.carrier_id
        LEFT JOIN t_account ta2 ON tca2.account_id = ta2.id
        LEFT JOIN t_zt_account_open_info tzaoi2 ON ta2.id = tzaoi2.account_id
        LEFT JOIN t_company_account tca ON toi.company_id = tca.company_id
        LEFT JOIN t_account ta ON tca.account_id = ta.id
        LEFT JOIN t_zt_account_open_info tzaoi ON ta.id = tzaoi.account_id
        LEFT JOIN t_enduser_account tea ON toi.end_driver_id = tea.enduser_id
        LEFT JOIN t_zt_account_open_info tzaoi3 ON tea.account_id = tzaoi3.account_id
        LEFT JOIN t_enduser_account tea2 ON toi.end_car_owner_id = tea2.enduser_id
        LEFT JOIN t_zt_account_open_info tzaoi4 ON tea2.account_id = tzaoi4.account_id
        LEFT JOIN t_enduser_account tea3 ON toi.agent_id = tea3.enduser_id
        LEFT JOIN t_zt_account_open_info tzaoi5 ON tea3.account_id = tzaoi5.account_id
        LEFT JOIN t_zt_wallet tzw ON tzaoi4.account_id = tzw.account_id
        LEFT JOIN t_zt_wallet tzw2 ON tzaoi5.account_id = tzw2.account_id
        where p.`code` = #{code,jdbcType=VARCHAR} AND ta.if_main_account = 1
          AND ta2.if_main_account = 1
        limit 1
    </select>

    <select id="selectTxServiceTransferDetailByCode" resultType="com.lz.dto.OrderInfoDTO">
        select
            trade_type
        from t_order_pay_detail
        where code = #{code}
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="com.lz.model.TOrderPayDetail">
        update t_order_pay_detail
        <set>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="orderPayCode != null">
                order_pay_code = #{orderPayCode,jdbcType=VARCHAR},
            </if>
            <if test="orderCastChangeCode != null">
                order_cast_change_code = #{orderCastChangeCode,jdbcType=VARCHAR},
            </if>
            <if test="bankUnionNo != null">
                bank_union_no = #{bankUnionNo,jdbcType=VARCHAR},
            </if>
            <if test="bankNo != null">
                bank_no = #{bankNo,jdbcType=VARCHAR},
            </if>
            <if test="cardHolder != null">
                card_holder = #{cardHolder,jdbcType=VARCHAR},
            </if>
            <if test="cardType != null">
                card_type = #{cardType,jdbcType=VARCHAR},
            </if>
            <if test="bankCardId != null">
                bank_card_id = #{bankCardId,jdbcType=INTEGER},
            </if>
            <if test="tradeType != null">
                trade_type = #{tradeType,jdbcType=VARCHAR},
            </if>
            <if test="operateState != null">
                operate_state = #{operateState,jdbcType=VARCHAR},
            </if>
            <if test="operaterId != null">
                operater_id = #{operaterId,jdbcType=INTEGER},
            </if>
            <if test="operateTime != null">
                operate_time = #{operateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="returnTime != null">
                return_time = #{returnTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tradeStatus != null">
                trade_status = #{tradeStatus,jdbcType=VARCHAR},
            </if>
            <if test="errorCode != null">
                error_code = #{errorCode,jdbcType=VARCHAR},
            </if>
            <if test="errorMsg != null">
                error_msg = #{errorMsg,jdbcType=VARCHAR},
            </if>
            <if test="innerTradeNo != null">
                inner_trade_no = #{innerTradeNo,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="param1 != null">
                param1 = #{param1,jdbcType=VARCHAR},
            </if>
            <if test="param2 != null">
                param2 = #{param2,jdbcType=VARCHAR},
            </if>
            <if test="param3 != null">
                param3 = #{param3,jdbcType=VARCHAR},
            </if>
            <if test="param4 != null">
                param4 = #{param4,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="enable != null">
                `enable` = #{enable,jdbcType=BIT},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectOrderPayDetailByPackId" resultType="com.lz.model.TOrderPayDetail">
        SELECT
            topdi.id, topdi.code, topdi.order_pay_code, topdi.order_cast_change_code, topdi.bank_union_no,
            topdi.bank_no, topdi.card_holder, topdi.card_type, topdi.bank_card_id, topdi.trade_type,
            topdi.operate_state, topdi.operater_id, topdi.operate_time, topdi.return_time, topdi.trade_status,
            topdi.error_code, topdi.error_msg, topdi.inner_trade_no, topdi.remark, topdi.param1, topdi.param2,
            topdi.param3, topdi.param4, topdi.create_user, topdi.create_time, topdi.update_user,
            topdi.update_time, topdi.`enable`
        FROM
            t_order_pack_info topi
            LEFT JOIN t_order_pack_detail topd ON topi.CODE = topd.pack_pay_code
            left join t_order_pay_info topi2 on topd.order_code = topi2.order_code
            left join t_order_pay_detail topdi on topdi.order_pay_code = topi2.code
            left join t_order_cast_changes tocc on topd.order_code = tocc.order_code
        WHERE
            topi.id = #{id} AND topdi.trade_type in ('COMBALANCE_PAY', 'CABALANCE_PAY', 'CDBALANCE_PAY', 'SPLIT_FALL', 'CCBALANCE_PAY', 'CMBALANCE_PAY') and topi.enable = 0 and topd.enable = 0
             and topdi.enable = 0 and topdi.trade_status = 'TRADE_FINISHED' and topdi.return_time is not null and tocc.data_enable = 1
    </select>

    <select id="selectByCode" resultType="com.lz.model.TOrderPayDetail">
        SELECT
            id, code, order_pay_code, order_cast_change_code, bank_union_no,
            bank_no, card_holder, card_type, bank_card_id, trade_type,
            operate_state, operater_id, operate_time, return_time, trade_status,
            error_code, error_msg, inner_trade_no, remark, param1, param2,
            param3, param4, create_user, create_time, update_user,
            update_time, `enable`
        FROM
            t_order_pay_detail
        where code = #{code}
    </select>

    <select id="selectByOrderCastChangeCodeAndTradeType" resultType="com.lz.model.TOrderPayDetail">
        select topd.id, topd.code, topd.order_pay_code, topd.order_cast_change_code, topd.bank_union_no,
               topd.bank_no, topd.card_holder, topd.card_type, topd.bank_card_id, topd.trade_type,
               topd.operate_state, topd.operater_id, topd.operate_time, topd.return_time, topd.trade_status,
               topd.error_code, topd.error_msg, topd.inner_trade_no, topd.remark, topd.param1, topd.param2,
               topd.param3, topd.param4, topd.create_user, topd.create_time, topd.update_user,
               topd.update_time, topd.`enable`
        from t_order_pay_detail topd
        left join t_order_cast_changes tocc on topd.order_cast_change_code = tocc.code
        where tocc.code = #{orderCastChangeCode} and topd.trade_type = #{tradeType} and topd.operate_state = 'RZ'
        order by tocc.id desc
        limit 1
    </select>

    <select id="selectTxOrderPayDetail" resultType="com.lz.dto.TTxOrderPayDetailDTO">
        select topd.id, topd.code orderPayDetailCode, topd.bank_card_id, topd.card_holder, topd.bank_no, topd.create_time,
               topd.param1 walletId, topi.order_actual_payment, topi.order_total_payment, topi.order_prepay_amount,
               topi.id orderPayId, topi.code orderPayCode, topd.param2 enduserId, topd.trade_type, if(topd.param3 = '1', true, false) selfCard,
               topd.remark txTime,topd.trade_status
        from t_order_pay_detail topd
        left join t_order_pay_info topi on topd.order_pay_code = topi.code
        where topd.code = #{code}
    </select>

    <select id="selectOrderPayDetailByRemark" resultType="com.lz.model.TOrderPayDetail">
        select
            <include refid="Base_Column_List"></include>
        from
            t_order_pay_detail
        where remark = #{remark} and trade_status = 'TRADE_FINISHED'
    </select>

    <select id="selectOrderPayInfo" resultType="com.lz.dto.TOrderPayRequestDTO">
        select topd.id orderPayDetailId, topd.code orderPayDetailCode, topd.create_time,
               topi.order_code, topi.order_actual_payment, topi.order_total_payment, topi.order_prepay_amount,
               topi.id orderPayId, topi.code orderPayCode, topd.trade_type, topd.trade_status, topd.operate_time, topd.operater_id, topd.create_user,
               topr.id payRequestId, topr.code payRequestCode,
               tzw.id companyWalletId, tzw2.id carrierWalletId,
               tzaoi2.partner_acc_id carrierAccId, tzaoi.partner_acc_id companyAccId
        from
            t_order_pay_detail topd
        left join t_order_pay_info topi on topd.order_pay_code = topi.code
        left join t_order_pay_request topr on topi.order_code = topr.code
        LEFT JOIN t_carrie_account tca2 ON tca2.carrier_id = topr.carrier_id
        LEFT JOIN t_account ta2 ON tca2.account_id = ta2.id
        LEFT JOIN t_zt_account_open_info tzaoi2 ON ta2.id = tzaoi2.account_id
        LEFT JOIN t_company_account tca ON topr.company_id = tca.company_id
        LEFT JOIN t_account ta ON tca.account_id = ta.id
        LEFT JOIN t_zt_account_open_info tzaoi ON ta.id = tzaoi.account_id
        left join t_zt_wallet tzw on tzaoi.id = tzw.zt_account_open_id
        left join t_zt_wallet tzw2 on tzaoi2.id = tzw2.zt_account_open_id
        where topd.code = #{code} and tzaoi.channel_id = 'HXBANK' and tzaoi2.channel_id = 'HXBANK' and ta.if_main_account = 1 and ta2.if_main_account = 1
    </select>

    <update id="updateOrderPayDetailEnableByOrderCode">
        update t_order_pay_detail topd
            left join t_order_pay_info topi on topd.order_pay_code = topi.code
            set topd.enable = true
        where topi.order_code = #{orderCode} and topd.operate_state = 'RZ' and topd.enable = false
    </update>

    <update id="updateOrderPayDetailEnableByOrderPackCode">
        update t_order_pay_detail topd
            left join t_order_pay_info topi on topd.order_pay_code = topi.code
        left join t_order_pack_detail topd2 on topi.order_code = topd2.order_code
        set topd.enable = true
        where topd2.pack_pay_code = #{orderPackCode} and topd.operate_state = 'RZ' and topd.enable = false
    </update>

    <update id="updateByCode" parameterType="com.lz.model.TOrderPayDetail">
        update t_order_pay_detail
        <set>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="orderPayCode != null">
                order_pay_code = #{orderPayCode,jdbcType=VARCHAR},
            </if>
            <if test="orderCastChangeCode != null">
                order_cast_change_code = #{orderCastChangeCode,jdbcType=VARCHAR},
            </if>
            <if test="bankUnionNo != null">
                bank_union_no = #{bankUnionNo,jdbcType=VARCHAR},
            </if>
            <if test="bankNo != null">
                bank_no = #{bankNo,jdbcType=VARCHAR},
            </if>
            <if test="cardHolder != null">
                card_holder = #{cardHolder,jdbcType=VARCHAR},
            </if>
            <if test="cardType != null">
                card_type = #{cardType,jdbcType=VARCHAR},
            </if>
            <if test="bankCardId != null">
                bank_card_id = #{bankCardId,jdbcType=INTEGER},
            </if>
            <if test="tradeType != null">
                trade_type = #{tradeType,jdbcType=VARCHAR},
            </if>
            <if test="operateState != null">
                operate_state = #{operateState,jdbcType=VARCHAR},
            </if>
            <if test="operaterId != null">
                operater_id = #{operaterId,jdbcType=INTEGER},
            </if>
            <if test="operateTime != null">
                operate_time = #{operateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="returnTime != null">
                return_time = #{returnTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tradeStatus != null">
                trade_status = #{tradeStatus,jdbcType=VARCHAR},
            </if>
            <if test="errorCode != null">
                error_code = #{errorCode,jdbcType=VARCHAR},
            </if>
            <if test="errorMsg != null">
                error_msg = #{errorMsg,jdbcType=VARCHAR},
            </if>
            <if test="innerTradeNo != null">
                inner_trade_no = #{innerTradeNo,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="param1 != null">
                param1 = #{param1,jdbcType=VARCHAR},
            </if>
            <if test="param2 != null">
                param2 = #{param2,jdbcType=VARCHAR},
            </if>
            <if test="param3 != null">
                param3 = #{param3,jdbcType=VARCHAR},
            </if>
            <if test="param4 != null">
                param4 = #{param4,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="enable != null">
                `enable` = #{enable,jdbcType=BIT},
            </if>
        </set>
        where code = #{code,jdbcType=VARCHAR}
    </update>

</mapper>