<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TOrderInsuranceMapper">
    <update id="updateByOrderBusinessCode">
        update t_order_insurance
        <set>
            <if test="insuranceStatus != null and insuranceStatus != ''">
                insurance_status = #{insuranceStatus,jdbcType=VARCHAR},
            </if>
            <if test="insuranceMessage != null and insuranceMessage != ''">
                insurance_message = #{insuranceMessage,jdbcType=VARCHAR},
            </if>
            <if test="policyPath != null and policyPath != ''">
                policy_path = #{policyPath,jdbcType=VARCHAR},
            </if>
            update_time = now()
        </set>
        where order_business_code = #{orderBusinessCode,jdbcType=VARCHAR}
    </update>

    <select id="selectByPage" resultType="com.lz.vo.TOrderInsuranceVO">
        select
        toi.id,toi.order_business_code,
        toi.insure,
        (case
            when toi.insure = 1 then "是"
            when toi.insure = 0 then "否"
        else "其他" end) as insureStr,
        toi.driver_name,toi.driver_phone,toi.goods_name,
        toi.company_name,toi.carrier_name,toi.insured_amount,
        toi.uninsured_cause,toi.remark,
        toi.rate,toi.insurance_status,
        toi.insurance_message,
        tosn.page_show_code as orderPayStatusValue,
        teui.real_name as captainName,
        teui.phone as captainPhone,
        toi2.deliver_order_time,
        toi2.receive_order_time,
        toi2.order_finish_time,
        IFNULL(toi2.deliver_weight_notes_time, tosup.create_time) as deliver_weight_notes_time,
        IFNULL(toi2.receive_weight_notes_time, tosdown.create_time) as receive_weight_notes_time,
        toi.insurance_cancellation,
        teci.vehicle_number,
        toi2.order_execute_status,
        tosn2.page_show_code as orderExecuteStatusValue
        from t_order_insurance toi
        LEFT JOIN t_order_info toi2 on toi.order_business_code = toi2.order_business_code and toi2.`enable` = 0
        LEFT JOIN t_goods_source_info tgsi on tgsi.line_goods_rel_id = toi2.line_goods_rel_id and tgsi.`enable` = 0
        LEFT JOIN t_end_user_info teui on teui.id = toi2.end_car_owner_id and teui.user_logistics_role = 'CTYPECAPTAIN' and teui.`enable` = 0
        LEFT JOIN t_order_state tosup ON tosup.state_node_value = 'S0302' AND  tosup.order_code = toi2.`code` and tosup.`enable` = 0
        LEFT JOIN t_order_state tosdown ON tosdown.state_node_value = 'S0402' AND  tosdown.order_code = toi2.`code` and tosdown.`enable` = 0
        LEFT JOIN t_order_state_node tosn ON toi2.order_pay_status = tosn.`code`
        LEFT JOIN t_order_state_node tosn2 ON toi2.order_execute_status = tosn2.`code`
        LEFT JOIN t_end_car_info teci ON toi2.vehicle_id = teci.id
        where toi.`enable` = 0
            and toi.insurance_method in ('INDEPENDENTCHOICE','MUSTBEINSURED')
            <if test="keyword != null and keyword != ''">
                and CONCAT(toi.driver_name,toi.driver_phone)like CONCAT("%",#{keyword,jdbcType=VARCHAR},"%")
            </if>
            <if test="orderBusinessCode != null and orderBusinessCode != ''">
                and toi.order_business_code like CONCAT("%",#{orderBusinessCode,jdbcType=VARCHAR},"%")
            </if>
            <if test="vehicleNumber != null and vehicleNumber != ''">
                and teci.vehicle_number like CONCAT("%",#{vehicleNumber,jdbcType=VARCHAR},"%")
            </if>
            <if test="carrierName != null and carrierName != ''">
                and toi.carrier_name = #{carrierName,jdbcType=VARCHAR}
            </if>
            <if test="insure != null">
                and toi.insure = #{insure,jdbcType=INTEGER}
            </if>
            <if test="insuranceCancellation != null">
                and toi.insurance_cancellation = #{insuranceCancellation,jdbcType=BIT}
            </if>
            <if test="orderExecuteStatus != null and orderExecuteStatus != ''">
                and toi2.order_execute_status = #{orderExecuteStatus,jdbcType=VARCHAR}
            </if>
            <if test="timeOfPaymentStart != null and timeOfPaymentStart != ''">
                and toi2.order_finish_time >= #{timeOfPaymentStart}
            </if>
            <if test="timeOfPaymentEnd != null and timeOfPaymentEnd != ''">
                and toi2.order_finish_time <![CDATA[<= ]]> #{timeOfPaymentEnd}
            </if>
            <if test="idArray != null and idArray.size != 0 ">
                and toi.id in
                <foreach collection="idArray" item="id" open="(" separator="," close=")">
                    #{id,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="captainId != null and captainId != ''">
                and toi2.end_car_owner_id = #{captainId,jdbcType=INTEGER}
            </if>
            <if test="fhsTime != null">
                AND toi2.deliver_order_time >= #{fhsTime}
            </if>
            <if test="fheTime != null">
                AND toi2.deliver_order_time &lt;= #{fheTime}
            </if>
            <if test="shsTime != null">
                AND toi2.receive_order_time >= #{shsTime}
            </if>
            <if test="sheTime != null">
                AND toi2.receive_order_time &lt;= #{sheTime}
            </if>
            <if test="fhbdsTime != null">
                AND (toi2.deliver_weight_notes_time >= #{fhbdsTime} or tosup.create_time >= #{fhbdsTime})
            </if>
            <if test="fhbdeTime != null">
                AND (toi2.deliver_weight_notes_time &lt;= #{fhbdeTime} or tosup.create_time &lt;= #{fhbdeTime})
            </if>
            <if test="shbdsTime != null">
                AND (toi2.receive_weight_notes_time >= #{shbdsTime} or tosdown.create_time >= #{shbdsTime})
            </if>
            <if test="shbdeTime != null">
                AND (toi2.receive_weight_notes_time &lt;= #{shbdeTime} or tosdown.create_time &lt;= #{shbdeTime})
            </if>
        order by toi.id desc
    </select>
    <select id="selectByOrderBusinessCode" resultType="com.lz.model.TOrderInsurance">
        select <include refid="Base_Column_List"/>
        from t_order_insurance
        where order_business_code = #{orderBusinessCode,jdbcType=VARCHAR}
    </select>
    <select id="selectDataByEndDriverId" resultType="com.lz.vo.TOrderInsuranceVO">
        SELECT
            toi.id,
            toi.order_business_code,
            toi.driver_name,
            toi.driver_phone,
            toi.insured_amount,
            toi.policy_path,
            toi.create_time,
            toi.ENABLE,
            toi.insurance_cancellation,
            toi2.code
        FROM t_order_insurance toi
        LEFT JOIN t_order_info toi2 ON toi.order_business_code = toi2.order_business_code and toi2.`enable` = 0
        WHERE  toi2.end_driver_id = #{endDriverId,jdbcType=INTEGER}
        and toi.`enable` = 0
        and toi.insure = 1
        order by toi.id desc
    </select>

</mapper>
