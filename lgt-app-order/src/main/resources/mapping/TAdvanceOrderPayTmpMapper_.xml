<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TAdvanceOrderPayTmpMapper">
	<select id="selectAdvanceOrderPayTemp" parameterType="java.lang.String" resultType="com.lz.dto.TAdvanceOrderPayTempInfoDTO">
		select taopt.trade_type, taopt.operater_id, taopt.code orderPatDetailCode,
		       taopt.create_time, taopt.trade_type, taopt.bank_union_no, taopt.bank_no, taopt.card_holder,
		       taopt.bank_card_id, taopt.operate_state, taopt.trade_status, taopt.payment_platforms,
		       tocc.end_driver_wallet_id,
		       tocc.company_wallet_id,
		       tocc.company_project_wallet_id,
		       tocc.carrier_wallet_id,
		       tocc.order_code,
		       taot.advance_fee carriage_fee,
		       taot.advance_dispatch_fee dispatch_fee,
		       tocc.withdraw_type,
		       tocc.capital_transfer_type,
		       ifnull(taot.create_user, '' ),
		       toi.fee_settlement_way,
		       toi.carrier_id,
		       taopt.inner_trade_no,
		       taopt.error_msg, taopt.create_user txCreateUser, toi.order_business_code
		from t_advance_order_pay_tmp taopt
		left join t_advance_order_tmp taot on taopt.advance_code = taot.code
		left join t_order_info toi on toi.code = taot.order_code
		left join t_order_cast_changes tocc on tocc.order_code = toi.code and tocc.user_oper = 'Invoice'
		where taopt.code = #{code}
	</select>

	<select id="selectAdvanceOrderPay" parameterType="com.lz.model.TAdvanceOrderPayTmp" resultType="com.lz.model.TAdvanceOrderPayTmp">
		select
		<include refid="Base_Column_List" />
		from t_advance_order_pay_tmp
		where
			advance_code = #{advanceCode}
			and trade_status = #{tradeStatus}
			and enable = false
</select>

	<update id="updateByModelSelective" parameterType="com.lz.model.TAdvanceOrderPayTmp">
		update t_advance_order_pay_tmp
		<set>
			<if test="advanceCode != null">
				advance_code = #{advanceCode,jdbcType=VARCHAR},
			</if>
			<if test="bankUnionNo != null">
				bank_union_no = #{bankUnionNo,jdbcType=VARCHAR},
			</if>
			<if test="bankNo != null">
				bank_no = #{bankNo,jdbcType=VARCHAR},
			</if>
			<if test="cardHolder != null">
				card_holder = #{cardHolder,jdbcType=VARCHAR},
			</if>
			<if test="cardType != null">
				card_type = #{cardType,jdbcType=VARCHAR},
			</if>
			<if test="bankCardId != null">
				bank_card_id = #{bankCardId,jdbcType=VARCHAR},
			</if>
			<if test="tradeType != null">
				trade_type = #{tradeType,jdbcType=VARCHAR},
			</if>
			<if test="operateState != null">
				operate_state = #{operateState,jdbcType=VARCHAR},
			</if>
			<if test="operaterId != null">
				operater_id = #{operaterId,jdbcType=VARCHAR},
			</if>
			<if test="operateTime != null">
				operate_time = #{operateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="returnTime != null">
				return_time = #{returnTime,jdbcType=TIMESTAMP},
			</if>
			<if test="tradeStatus != null">
				trade_status = #{tradeStatus,jdbcType=VARCHAR},
			</if>
			<if test="errorCode != null">
				error_code = #{errorCode,jdbcType=VARCHAR},
			</if>
			<if test="errorMsg != null">
				error_msg = #{errorMsg,jdbcType=VARCHAR},
			</if>
			<if test="innerTradeNo != null">
				inner_trade_no = #{innerTradeNo,jdbcType=VARCHAR},
			</if>
			<if test="paymentPlatforms != null">
				payment_platforms = #{paymentPlatforms,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				remark = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="param1 != null">
				param1 = #{param1,jdbcType=VARCHAR},
			</if>
			<if test="param2 != null">
				param2 = #{param2,jdbcType=VARCHAR},
			</if>
			<if test="param3 != null">
				param3 = #{param3,jdbcType=VARCHAR},
			</if>
			<if test="param4 != null">
				param4 = #{param4,jdbcType=VARCHAR},
			</if>
			<if test="createUser != null">
				create_user = #{createUser,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null">
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUser != null">
				update_user = #{updateUser,jdbcType=VARCHAR},
			</if>
			<if test="updateTime != null">
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="enable != null">
				`enable` = #{enable,jdbcType=BOOLEAN},
			</if>
		</set>
		where code = #{code,jdbcType=VARCHAR}
	</update>

	<select id="selectJdAdvanceOrderPayTemp" parameterType="java.lang.String" resultType="com.lz.dto.TJdAdvanceOrderPayTempInfoDTO">
		select taopt.id payId, taopt.trade_type, taopt.operater_id, taopt.code orderPatDetailCode,
			   taopt.create_time, taopt.bank_union_no, taopt.bank_no, taopt.card_holder,
			   taopt.bank_card_id, taopt.operate_state, taopt.trade_status, taopt.operater_id, taopt.payment_platforms,
			   taot.id advanceId, taot.code, taot.order_code,
			   taot.advance_fee carriage_fee,
			   taot.advance_dispatch_fee dispatch_fee,
			   tocc.withdraw_type,
			   tocc.capital_transfer_type,
			   ifnull(taot.create_user, '' ) create_user,
			   toi.fee_settlement_way,
			   toi.carrier_id,
			   taopt.inner_trade_no,
			   taopt.error_msg, taopt.create_user txCreateUser, toi.order_business_code,
		       tcom.partner_acc_id companyAccId, tca.partner_acc_id carrierAccId, tuser.partner_acc_id driverAccid,
		       toi.company_project_id
		from t_advance_order_pay_tmp taopt
		left join t_advance_order_tmp taot on taopt.advance_code = taot.code
		left join t_order_info toi on toi.code = taot.order_code
		left join t_order_cast_changes tocc on tocc.order_code = toi.code and tocc.user_oper = 'Invoice'
		left join t_carrier_company_open_role tcom on toi.company_id = tcom.carrier_company_id and tcom.user_open_role = 'BD' and tcom.open_status = 'OpenSuccess' and tcom.enable = 0
		left join t_carrier_company_open_role tca on toi.carrier_id = tca.carrier_company_id and tca.user_open_role = 'CA' and tca.open_status = 'OpenSuccess' and tca.enable = 0
		left join t_end_user_open_role tuser on toi.end_driver_id = tuser.end_user_id and tuser.open_status = 'OpenSuccess' and tuser.enable = 0
		where taopt.code = #{code}
	</select>

	<select id="selectJdAdvanceOrderPayTempByOrderCode" resultType="com.lz.dto.TJdAdvanceOrderPayTempInfoDTO">
		select taot.id advanceId, taopt.payment_platforms
		from
			t_advance_order_tmp taot
			left join t_advance_order_pay_tmp taopt on taot.code = taopt.advance_code and taopt.trade_type = 'COMBALANCE_PREPAY' and taopt.enable = 0
		where taot.order_code = #{orderCode} and taot.enable = 0
	</select>

	<select id="checkAdvancePaymentPlatforms" resultType="java.lang.String">
		select taopt.payment_platforms
		from t_advance_order_tmp taot
		left join t_advance_order_pay_tmp taopt on taot.code = taopt.advance_code and taopt.trade_type = 'COMBALANCE_PREPAY'
		where taopt.payment_platforms = #{platforms} and taot.order_code in
			<foreach collection="list" index="index" item="orderCode" separator="," open="(" close=")">
				#{orderCode}
			</foreach>
	</select>

	<delete id="deleteByAdvanceCode">
		delete
		from t_advance_order_pay_tmp
		where advance_code = #{advanceCode}
	</delete>

	<select id="selectHxAdvanceOrderPayTemp" parameterType="java.lang.String" resultType="com.lz.dto.THxAdvanceOrderPayTempInfoDTO">
		select taopt.id payId, taopt.trade_type, taopt.operater_id, taopt.code orderPatDetailCode,
			   taopt.create_time, taopt.bank_union_no, taopt.bank_no, taopt.card_holder,
			   taopt.bank_card_id, taopt.operate_state, taopt.trade_status, taopt.operater_id, taopt.payment_platforms,
			   taot.id advanceId, taot.code, taot.order_code,
			   taot.advance_fee as carriage_fee,
			   taot.advance_dispatch_fee as dispatch_fee,
			   tocc.withdraw_type,
			   tocc.capital_transfer_type,
			   ifnull(taot.create_user, '' ) as create_user,
			   toi.fee_settlement_way,
			   toi.carrier_id,
			   taopt.inner_trade_no,
			   taopt.error_msg, taopt.create_user txCreateUser, toi.order_business_code,
			   tzaoi.partner_acc_id companyAccId, tzaoi2.partner_acc_id carrierAccId, tzaoi3.partner_acc_id driverAccid,
			   toi.company_project_id
		from t_advance_order_pay_tmp taopt
				 left join t_advance_order_tmp taot on taopt.advance_code = taot.code
				 left join t_order_info toi on toi.code = taot.order_code
				 left join t_order_cast_changes tocc on tocc.order_code = toi.code and tocc.user_oper = 'Invoice'
		    	 left join t_company_account tca2 on toi.company_id = tca2.company_id
		    	 left join t_account ta on tca2.account_id = ta.id
		         left join t_zt_account_open_info tzaoi on ta.id = tzaoi.account_id and tzaoi.status = 1 and tzaoi.user_open_role = 'BD'
		    	 left join t_carrie_account tca3 on toi.carrier_id = tca3.carrier_id
		         left join t_account ta2 on tca3.account_id = ta2.id
		         left join t_zt_account_open_info tzaoi2 on ta2.id = tzaoi2.account_id and tzaoi2.status = 1 and tzaoi2.user_open_role = 'CA'
		    	 left join t_enduser_account tea on toi.end_driver_id = tea.enduser_id
		         left join t_zt_account_open_info tzaoi3 on tea.account_id = tzaoi3.account_id and tzaoi3.status = 1 and tzaoi3.user_open_role = 'CD'
		where taopt.code = #{code} and ta.if_main_account = 1 and ta2.if_main_account = 1
	</select>

</mapper>