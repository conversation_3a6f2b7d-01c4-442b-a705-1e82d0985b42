<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TOrderPackInfoMapper">

  <!--运单打包列表： 查询所有打包完成的运单信息 Yan-->
  <select id="getOrderPackAll" parameterType="com.lz.vo.PcOrderSearchVO" resultType="com.lz.dto.OrderPackDTO">
    SELECT
      distinct opi.code,
      opi.virtual_order_no, <!-- 打包主表虚拟单据号 -->
      opi.check_account_person, <!-- 操作用户（对账人）-->
      opi.order_start_query_time, <!-- 查询单据开始日期-->
      opi.order_end_query_time, <!-- 查询单据结束日期-->
      eui.real_name, <!-- 所属车老板-->
      eui.phone, <!-- 联系方式 -->
      opi.total_selected_orders, <!-- 选中运单累计单据数-->
      opi.total_selected_orders_weight, <!-- 选中运单累计总吨数-->
      opi.total_selected_orders_carriage_fee, <!-- 选中运单累计总运费-->
      opi.total_selected_orders_dispatch_fee, <!-- 选中运单累计总调度费-->
      opi.total_selected_orders_carriage_zero_cut_fee, <!-- 选中运单累计总运费扣款金额   选中运单累计总运费的抹零金额-->
      opi.appointment_payment_cash, <!--与车老板约定应支付总运费现金额-->
      opi.appointment_payment_other, <!-- 与车老板约定应支付总运费其他实物价值-->
      opi.recount_dispatch_fee, <!-- 按应支付总运费重新计算后的调度费-->
      opi.pack_status,
      dci.item_value as packStatusValue, <!-- 打包状态-->
      opi.content_describtion, <!-- 约定内容描述-->
      opi.create_time as createTimes, <!-- 创建时间-->
      opi.remark, <!-- 备注-->
      tdci.item_value as capitalTransferType, <!-- 资金转移方式 -->
      tbc.card_no, tbc.card_owner, tocct.create_time as yfkTxTime <!--提现时间-->
    FROM t_order_pack_info opi
      LEFT JOIN t_order_pack_detail opd ON opd.id = (SELECT MAX(opds.id) FROM t_order_pack_detail opds  WHERE opi.`code` = opds.pack_pay_code)
      LEFT JOIN t_order_cast_changes tocc ON tocc.id = (SELECT MAX(toccb.id) FROM t_order_cast_changes toccb WHERE opd.order_code = toccb.order_code)
      LEFT JOIN t_end_user_info eui ON opi.end_user_id = eui.id
      LEFT JOIN t_dic_cat_item dci ON opi.pack_status = dci.item_code
      LEFT JOIN t_dic_cat_item tdci on tocc.capital_transfer_type = tdci.item_code
      left join t_bank_card tbc on opi.bank_card_id = tbc.id
      LEFT JOIN t_order_cast_changes tocct ON tocct.id = (SELECT MAX(toccb.id) FROM t_order_cast_changes toccb WHERE opi.code = toccb.order_code and user_oper = 'TX')

    <where>
      opi.`enable` = 0
      <if test="packId != null and packId.size != 0">
        AND opi.id IN
        <foreach collection="packId" index="index" item="pack" open="(" separator="," close=")">
          #{pack}
        </foreach>
      </if>
      <if test="companyIds != null">
        AND opi.company_id IN
        <foreach collection="companyIds" item="ci" index="index" open="(" separator="," close=")">
          #{ci}
        </foreach>
      </if>
      <if test="endUserId != null">
        AND opi.end_user_id = #{endUserId}
      </if>
      <if test="virtualOrderNo != null">
        AND opi.virtual_order_no LIKE CONCAT('%', #{virtualOrderNo}, '%')
      </if>
      <if test="packStatus != null">
        AND opi.pack_status = #{packStatus}
      </if>
      <if test="startTime != null">
        AND opi.create_time >= #{startTime}
      </if>
      <if test="endTime != null">
        AND opi.create_time &lt;= #{endTime}
      </if>
      <if test="carOwnerId != null">
        AND eui.id = #{carOwnerId}
      </if>
        <if test="goodsId != null">
            AND o.goods_id = #{goodsId}
        </if>
    </where>
    ORDER BY opi.create_time DESC
  </select>





  <!--运单打包列表： 查询未支付打包完成的运单信息 dingweibo-->
  <select id="getOrderPackNo" parameterType="com.lz.vo.PcOrderSearchVO" resultType="com.lz.dto.OrderPackDTO">

      SELECT
        distinct opi.code,
        opi.update_time updateTime,
        opi.virtual_order_no,<!-- 打包主表虚拟单据号 -->
        opi.check_account_person,<!-- 操作用户（对账人）-->
        opi.order_start_query_time,<!-- 查询单据开始日期-->
        opi.order_end_query_time, <!-- 查询单据结束日期-->
        eui.real_name, <!-- 所属车老板-->
        eui.phone, <!-- 联系方式 -->
        opi.total_selected_orders, <!-- 选中运单累计单据数-->
        opi.total_selected_orders_weight, <!-- 选中运单累计总吨数-->
        opi.total_selected_orders_carriage_fee, <!-- 选中运单累计总运费-->
        opi.total_selected_orders_dispatch_fee, <!-- 选中运单累计总调度费-->
        opi.total_selected_orders_carriage_zero_cut_fee, <!-- 选中运单累计总运费扣款金额   选中运单累计总运费的抹零金额-->
        opi.appointment_payment_cash, <!--与车老板约定应支付总运费现金额-->
        opi.appointment_payment_other, <!-- 与车老板约定应支付总运费其他实物价值-->
        opi.recount_dispatch_fee, <!-- 按应支付总运费重新计算后的调度费-->
        opi.pack_status,
        dci.item_value as packStatusValue, <!-- 打包状态-->
        opi.content_describtion, <!-- 约定内容描述-->
        opi.create_time as createTimes, <!-- 创建时间-->
        opi.remark, <!-- 备注-->
        case opi.ls_calculation when 0 then '否' else '是' end lsCalculationStr,
        opi.ls_calculation,
        ifnull(tdci.item_value, tdcic.item_value) as capitalTransferType, <!-- 资金转移方式 -->
        case opi.payment_platforms when '京东' then tbc.card_no when '华夏' then tzbc.acct_no else tbc.card_no end cardNo,
        case opi.payment_platforms when '京东' then tbc.card_owner when '华夏' then tzbc.acct_name else tbc.card_owner end cardOwner,
        tocct.create_time as yfkTxTime, <!--提现时间-->
        o.goods_name,
        case opi.payment_platforms when '京东' then '京东' when '华夏' then '华夏' else '网商' end paymentPlatforms
      FROM t_order_pack_info opi
      LEFT JOIN t_order_pack_detail opd ON opd.id = (SELECT MAX(opds.id) FROM t_order_pack_detail opds WHERE opi.
      `code` = opds.pack_pay_code)
      LEFT JOIN t_order_info o on o.code = opd.order_code
      LEFT JOIN t_order_cast_changes tocc ON tocc.id = (SELECT MAX(toccb.id) FROM t_order_cast_changes toccb
      WHERE opd.order_code = toccb.order_code)
      LEFT JOIN t_end_user_info eui ON opi.end_user_id = eui.id
      LEFT JOIN t_dic_cat_item dci ON opi.pack_status = dci.item_code
      LEFT JOIN t_dic_cat_item tdci on tocc.capital_transfer_type = tdci.item_code
      left join t_bank_card tbc on opi.bank_card_id = tbc.id
      left join t_zt_bank_card tzbc on opi.bank_card_id = tzbc.id
      LEFT JOIN t_order_cast_changes tocct ON tocct.id = (SELECT MAX(toccb.id) FROM t_order_cast_changes toccb
      WHERE opi.code = toccb.order_code and user_oper = 'TX')
      left join t_order_pack_detail topd on opi.code = topd.pack_pay_code
      left join t_order_cast_changes toccc on topd.order_code = toccc.order_code
      left join t_dic_cat_item tdcic on toccc.capital_transfer_type = tdcic.item_code
      left join t_line_goods_carriage_pay carripay on carripay.line_goods_rel_id = o.line_goods_rel_id
      <where>
      opi.`enable` = 0
      <if test="packId != null and packId.size != 0">
        AND opi.id IN
        <foreach collection="packId" index="index" item="pack" open="(" separator="," close=")">
          #{pack}
        </foreach>
      </if>
      <if test="companyIds != null">
        AND opi.company_id IN
        <foreach collection="companyIds" item="ci" index="index" open="(" separator="," close=")">
          #{ci}
        </foreach>
      </if>
      <if test="endUserId != null">
        AND opi.end_user_id = #{endUserId}
      </if>
      <if test="virtualOrderNo != null">
        AND opi.virtual_order_no LIKE CONCAT('%', #{virtualOrderNo}, '%')
      </if>
      <choose>
          <when test="packStatus != null">
              AND opi.pack_status = #{packStatus}
          </when>
          <otherwise>
              and opi.pack_status in('PACKED','PACKORDERPAIDERROR','PACKRECALL')
          </otherwise>
      </choose>

      <if test="startTime != null">
        AND opi.create_time >= #{startTime}
      </if>
      <if test="endTime != null">
        AND opi.create_time &lt;= #{endTime}
      </if>
      <if test="carOwnerId != null">
        AND eui.id = #{carOwnerId}
      </if>
      <if test="codeArray != null and codeArray.size != 0 ">
        AND opi.code IN
        <foreach collection="codeArray" item="code" index="item" open="(" close=")" separator=",">
          #{code}
        </foreach>
      </if>
      <if test="cardNo != null and cardNo != '' ">
          and ( tbc.card_no LIKE CONCAT('%', #{cardNo}, '%') or  tzbc.acct_no LIKE CONCAT('%', #{cardNo}, '%'))
      </if>
      <if test="cardhoderMan != null and cardhoderMan != '' ">
          and (tbc.card_owner LIKE CONCAT('%', #{cardhoderMan}, '%') or tzbc.acct_name LIKE CONCAT('%', #{cardhoderMan}, '%'))
      </if>
      <if test="txStartTime != null ">
          and tocct.create_time >= #{txStartTime}
      </if>
      <if test="txEndTime != null ">
          and tocct.create_time &lt;=  #{txEndTime}
      </if>
      <if test="capitalId != null ">
          and tdci.item_code =  #{capitalId}
      </if>
      <if test="realName!=null and realName != ''">
          and eui.real_name LIKE CONCAT('%', #{realName}, '%')
      </if>
      <if test="realPhone!=null and realPhone != ''">
          and eui.phone LIKE CONCAT('%', #{realPhone}, '%')
      </if>
        <if test="goodsId != null">
            AND o.goods_id = #{goodsId}
        </if>
    </where>
    order by opi.update_time desc
  </select>

    <!--运单打包列表： 查询未支付打包完成的运单信息（合计） liuyang-->
    <select id="getOrderPackNoSUM" parameterType="com.lz.vo.PcOrderSearchVO" resultType="java.util.Map">
        SELECT
        IFNULL(SUM(IFNULL(a.total_selected_orders_weight,0)), 0) as totalSelectedOrdersWeight,
        IFNULL(SUM(IFNULL(a.appointment_payment_cash,0)), 0) as totalSelectedOrdersCarriageFee,
        IFNULL(SUM(IFNULL(a.recount_dispatch_fee,0)), 0) as totalSelectedOrdersDispatchFee
       from (
        SELECT
        distinct opi.code,
        opi.total_selected_orders_weight, <!-- 选中运单累计总吨数-->
        opi.appointment_payment_cash, <!-- -->
        opi.recount_dispatch_fee <!-- 选中运单累计总调度费-->
        FROM t_order_pack_info opi
        LEFT JOIN t_order_pack_detail opd ON opd.id = (SELECT MAX(opds.id) FROM t_order_pack_detail opds  WHERE opi.`code` = opds.pack_pay_code)
        LEFT JOIN t_order_info o on o.code = opd.order_code
        LEFT JOIN t_order_cast_changes tocc ON tocc.id = (SELECT MAX(toccb.id) FROM t_order_cast_changes toccb WHERE opd.order_code = toccb.order_code)
        LEFT JOIN t_end_user_info eui ON opi.end_user_id = eui.id
        LEFT JOIN t_dic_cat_item dci ON opi.pack_status = dci.item_code
        LEFT JOIN t_dic_cat_item tdci on tocc.capital_transfer_type = tdci.item_code
        left join t_bank_card tbc on opi.bank_card_id = tbc.id
        left join t_zt_bank_card tzbc on opi.bank_card_id = tzbc.id
        LEFT JOIN t_order_cast_changes tocct ON tocct.id = (SELECT MAX(toccb.id) FROM t_order_cast_changes toccb WHERE opi.code = toccb.order_code and user_oper = 'TX')
        left join t_line_goods_carriage_pay carripay on carripay.line_goods_rel_id = o.line_goods_rel_id
        <where>
            opi.`enable` = 0
            <if test="packId != null and packId.size != 0">
                AND opi.id IN
                <foreach collection="packId" index="index" item="pack" open="(" separator="," close=")">
                    #{pack}
                </foreach>
            </if>
            <if test="companyIds != null">
                AND opi.company_id IN
                <foreach collection="companyIds" item="ci" index="index" open="(" separator="," close=")">
                    #{ci}
                </foreach>
            </if>
            <if test="endUserId != null">
                AND opi.end_user_id = #{endUserId}
            </if>
            <if test="virtualOrderNo != null">
                AND opi.virtual_order_no LIKE CONCAT('%', #{virtualOrderNo}, '%')
            </if>
            <choose>
                <when test="packStatus != null">
                    AND opi.pack_status = #{packStatus}
                </when>
                <otherwise>
                    and opi.pack_status in('PACKED','PACKORDERPAIDERROR','PACKRECALL')
                </otherwise>
            </choose>

            <if test="startTime != null">
                AND opi.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND opi.create_time &lt;= #{endTime}
            </if>
            <if test="carOwnerId != null">
                AND eui.id = #{carOwnerId}
            </if>
            <if test="codeArray != null and codeArray.size != 0 ">
                AND opi.code IN
                <foreach collection="codeArray" item="code" index="item" open="(" close=")" separator=",">
                    #{code}
                </foreach>
            </if>
            <if test="cardNo != null and cardNo != '' ">
                and ( tbc.card_no LIKE CONCAT('%', #{cardNo}, '%') or  tzbc.acct_no LIKE CONCAT('%', #{cardNo}, '%'))
            </if>
            <if test="cardhoderMan != null and cardhoderMan != '' ">
                and (tbc.card_owner LIKE CONCAT('%', #{cardhoderMan}, '%') or tzbc.acct_name LIKE CONCAT('%', #{cardhoderMan}, '%'))
            </if>
            <if test="txStartTime != null ">
                and tocct.create_time >= #{txStartTime}
            </if>
            <if test="txEndTime != null ">
                and tocct.create_time &lt;=  #{txEndTime}
            </if>
            <if test="capitalId != null ">
                and tdci.item_code =  #{capitalId}
            </if>
            <if test="realName!=null and realName != ''">
                and eui.real_name LIKE CONCAT('%', #{realName}, '%')
            </if>
            <if test="realPhone!=null and realPhone != ''">
                and eui.phone LIKE CONCAT('%', #{realPhone}, '%')
            </if>
            <if test="goodsId != null">
                AND o.goods_id = #{goodsId}
            </if>
        </where>

        ) a

    </select>
    <select id="getOrderPackOk_COUNT" parameterType="com.lz.vo.PcOrderSearchVO" resultType="java.lang.Long">
        SELECT
         count(1)
        FROM t_order_pack_info opi
        <if test="(capitalId !=null and capitalId!= '') or goodsId != null">
            LEFT JOIN t_order_pack_detail opd ON opd.id = (SELECT MAX(opds.id) FROM t_order_pack_detail opds  WHERE opi.`code` = opds.pack_pay_code)
            LEFT JOIN t_order_info o on o.code = opd.order_code
            LEFT JOIN t_order_cast_changes tocc ON tocc.id = (SELECT MAX(toccb.id) FROM t_order_cast_changes toccb WHERE opd.order_code = toccb.order_code)
            LEFT JOIN t_dic_cat_item tdci on tocc.capital_transfer_type = tdci.item_code
        </if>
        <if test="carOwnerId != null or (realName != null and realName != '') or (realPhone!=null and realPhone != '')">
            LEFT JOIN t_end_user_info eui ON opi.end_user_id = eui.id
        </if>
        <if test="(cardhoderMan !=null and cardhoderMan!= '') or (cardNo !=null and cardNo!= '')">
            left join t_bank_card tbc on opi.bank_card_id = tbc.id
            left join t_zt_bank_card tzbc on opi.bank_card_id = tzbc.id
        </if>
        <if test="fkStartTime != null or fkEndTime != null">
            left join t_order_pay_info topi on topi.id = (select min(topi.id) from t_order_pack_detail topd left join t_order_pay_info topi on topd.order_code = topi.order_code where topd.pack_pay_code = opi.code )
            LEFT JOIN t_order_info toi on toi.code = topi.order_code
        </if>
        <where>
            opi.`enable` = 0
            <if test="fkStartTime != null">
                AND toi.order_finish_time >= #{fkStartTime}
            </if>
            <if test="fkEndTime != null">
                AND toi.order_finish_time &lt;= #{fkEndTime}
            </if>
            <if test="packId != null and packId.size != 0">
                AND opi.id IN
                <foreach collection="packId" index="index" item="pack" open="(" separator="," close=")">
                    #{pack}
                </foreach>
            </if>
            <if test="companyIds != null and companyIds.size != 0">
                AND opi.company_id IN
                <foreach collection="companyIds" item="ci" index="index" open="(" separator="," close=")">
                    #{ci}
                </foreach>
            </if>
            <if test="endUserId != null">
                AND opi.end_user_id = #{endUserId}
            </if>
            <if test="virtualOrderNo != null">
                AND opi.virtual_order_no LIKE CONCAT('%', #{virtualOrderNo}, '%')
            </if>
            <if test="packStatus != null">
                AND opi.pack_status = #{packStatus}
            </if>
            <choose>
                <when test="packStatus != null">
                    and opi.pack_status = #{packStatus}
                </when>
                <otherwise>
                    and opi.pack_status in('PACKEDHANDEL','PACKPAID','PACKWITHDRAW','PACKEWITHDRAWERROR','PACKRECALLPROCESSED','PACKEXTRACTPROCESSED')
                </otherwise>
            </choose>
            <if test="startTime != null">
                AND opi.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND opi.create_time &lt;= #{endTime}
            </if>
            <if test="carOwnerId != null">
                AND eui.id = #{carOwnerId}
            </if>
            <if test="codeArray != null and codeArray.size != 0 ">
                AND opi.code IN
                <foreach collection="codeArray" item="code" index="item" open="(" close=")" separator=",">
                    #{code}
                </foreach>
            </if>
            <if test="cardhoderMan !=null and cardhoderMan!= ''">
                and (tbc.card_owner like CONCAT('%', #{cardhoderMan}, '%') or tzbc.acct_name like CONCAT('%', #{cardhoderMan}, '%') )
            </if>
            <if test="cardNo !=null and cardNo!= ''">
                and (tbc.card_no like CONCAT('%', #{cardNo}, '%') or tzbc.acct_no like CONCAT('%', #{cardNo}, '%') )
            </if>
            <if test="capitalId !=null and capitalId!= ''">
                and tdci.item_code =  #{capitalId}
            </if>
            <if test="realName!=null and realName != ''">
                and eui.real_name LIKE CONCAT('%', #{realName}, '%')
            </if>
            <if test="realPhone!=null and realPhone != ''">
                and eui.phone LIKE CONCAT('%', #{realPhone}, '%')
            </if>
            <if test="goodsId != null">
                AND o.goods_id = #{goodsId}
            </if>
            <if test="projectId != null">
                and o.company_project_id = #{projectId}
            </if>
            <if test="carrierId != null">
                AND o.carrier_id = #{carrierId}
            </if>
        </where>
    </select>

  <!--运单打包列表： 查询已支付打包完成的运单信息 dingweibo-->
  <select id="getOrderPackOk" parameterType="com.lz.vo.PcOrderSearchVO" resultType="com.lz.dto.OrderPackDTO">

    SELECT
    opi.code,
    opi.update_time updateTime,
    opi.virtual_order_no, <!-- 打包主表虚拟单据号 -->
    opi.check_account_person, <!-- 操作用户（对账人）-->
    opi.order_start_query_time, <!-- 查询单据开始日期-->
    opi.order_end_query_time, <!-- 查询单据结束日期-->
    eui.real_name, <!-- 所属车老板-->
    eui.phone, <!-- 联系方式 -->
    opi.total_selected_orders, <!-- 选中运单累计单据数-->
    opi.total_selected_orders_weight, <!-- 选中运单累计总吨数-->
    opi.total_selected_orders_carriage_fee, <!-- 选中运单累计总运费-->
    opi.total_selected_orders_dispatch_fee, <!-- 选中运单累计总调度费-->
    opi.total_selected_orders_carriage_zero_cut_fee, <!-- 选中运单累计总运费扣款金额   选中运单累计总运费的抹零金额-->
    opi.appointment_payment_cash, <!--与车老板约定应支付总运费现金额-->
    opi.appointment_payment_other, <!-- 与车老板约定应支付总运费其他实物价值-->
    opi.recount_dispatch_fee, <!-- 按应支付总运费重新计算后的调度费-->
    opi.pack_status,
    dci.item_value as packStatusValue, <!-- 打包状态-->
    opi.content_describtion, <!-- 约定内容描述-->
    opi.create_time as createTimes, <!-- 创建时间-->
    opi.remark, <!-- 备注-->
    opi.payment_platforms,
    tdci.item_value as capitalTransferType, <!-- 资金转移方式 -->
    case topi.payment_platforms when '京东' then tbc.card_no
    when '华夏' then  tzbc.acct_no
    end card_no,
    case topi.payment_platforms when '京东' then tbc.card_owner
    when '华夏' then tzbc.acct_name
    end card_owner,
    o.goods_name,
    case opi.payment_platforms when '京东' then '京东' when '华夏' then '华夏' else '网商' end paymentPlatforms
    FROM t_order_pack_info opi
    LEFT JOIN t_order_pack_detail opd ON opd.id = (SELECT MAX(opds.id) FROM t_order_pack_detail opds  WHERE opi.`code` = opds.pack_pay_code)
    LEFT JOIN t_order_info o on o.code = opd.order_code
    LEFT JOIN t_order_cast_changes tocc ON tocc.id = (SELECT MAX(toccb.id) FROM t_order_cast_changes toccb WHERE opd.order_code = toccb.order_code)
    LEFT JOIN t_end_user_info eui ON opi.end_user_id = eui.id
    LEFT JOIN t_dic_cat_item dci ON opi.pack_status = dci.item_code
    LEFT JOIN t_dic_cat_item tdci on tocc.capital_transfer_type = tdci.item_code
    left join t_bank_card tbc on opi.bank_card_id = tbc.id
    left join t_zt_bank_card tzbc on opi.bank_card_id = tzbc.id
    left join t_order_pay_info topi on topi.id = (select min(topi.id) from t_order_pack_detail topd left join t_order_pay_info topi on topd.order_code = topi.order_code where topd.pack_pay_code = opi.code )
    <where>
      opi.`enable` = 0
        <if test="fkStartTime != null">
            AND o.order_finish_time >= #{fkStartTime}
        </if>
        <if test="fkEndTime != null">
            AND o.order_finish_time &lt;= #{fkEndTime}
        </if>
      <if test="packId != null and packId.size != 0">
        AND opi.id IN
        <foreach collection="packId" index="index" item="pack" open="(" separator="," close=")">
          #{pack}
        </foreach>
      </if>
      <if test="companyIds != null and companyIds.size != 0">
        AND opi.company_id IN
        <foreach collection="companyIds" item="ci" index="index" open="(" separator="," close=")">
          #{ci}
        </foreach>
      </if>
      <if test="endUserId != null">
        AND opi.end_user_id = #{endUserId}
      </if>
      <if test="virtualOrderNo != null">
        AND opi.virtual_order_no LIKE CONCAT('%', #{virtualOrderNo}, '%')
      </if>
      <if test="packStatus != null">
        AND opi.pack_status = #{packStatus}
      </if>
      <choose>
          <when test="packStatus != null">
              and opi.pack_status = #{packStatus}
          </when>
          <otherwise>
              and opi.pack_status in('PACKEDHANDEL','PACKPAID','PACKWITHDRAW','PACKEWITHDRAWERROR','PACKRECALLPROCESSED','PACKEXTRACTPROCESSED')
          </otherwise>
      </choose>
      <if test="startTime != null">
        AND opi.create_time >= #{startTime}
      </if>
      <if test="endTime != null">
        AND opi.create_time &lt;= #{endTime}
      </if>
      <if test="carOwnerId != null">
        AND eui.id = #{carOwnerId}
      </if>
      <if test="codeArray != null and codeArray.size != 0 ">
        AND opi.code IN
        <foreach collection="codeArray" item="code" index="item" open="(" close=")" separator=",">
          #{code}
        </foreach>
      </if>
      <if test="cardhoderMan !=null and cardhoderMan!= ''">
         and (tbc.card_owner like CONCAT('%', #{cardhoderMan}, '%') or tzbc.acct_name like CONCAT('%', #{cardhoderMan}, '%') )
      </if>
      <if test="cardNo !=null and cardNo!= ''">
         and (tbc.card_no like CONCAT('%', #{cardNo}, '%') or tzbc.acct_no like CONCAT('%', #{cardNo}, '%') )
      </if>
      <if test="capitalId !=null and capitalId!= ''">
          and tdci.item_code =  #{capitalId}
      </if>
        <if test="realName!=null and realName != ''">
            and eui.real_name LIKE CONCAT('%', #{realName}, '%')
        </if>
        <if test="realPhone!=null and realPhone != ''">
            and eui.phone LIKE CONCAT('%', #{realPhone}, '%')
        </if>
        <if test="goodsId != null">
            AND o.goods_id = #{goodsId}
        </if>
        <if test="projectId != null">
            and o.company_project_id = #{projectId}
        </if>
        <if test="carrierId != null">
            AND o.carrier_id = #{carrierId}
        </if>
    </where>
    order by opi.id desc
  </select>

    <!--运单打包列表： 查询已支付打包完成的运单信息 dingweibo-->
    <select id="getOrderPackOkSUM" parameterType="com.lz.vo.PcOrderSearchVO" resultType="java.util.Map">
        SELECT
        IFNULL(SUM(IFNULL(a.total_selected_orders_weight,0)), 0) as totalSelectedOrdersWeight, <!-- 选中运单累计总吨数-->
        IFNULL(SUM(IFNULL(a.appointment_payment_cash,0)), 0) as totalSelectedOrdersCarriageFee, <!-- 选中运单累计总运费-->
        IFNULL(SUM(IFNULL(a.recount_dispatch_fee,0)), 0) as totalSelectedOrdersDispatchFee  <!-- 选中运单累计总调度费-->
       from (
        SELECT
        distinct opi.code,
        opi.total_selected_orders_weight, <!-- 选中运单累计总吨数-->
        opi.appointment_payment_cash,   <!--与车老板约定应支付总运费现金额-->
        opi.recount_dispatch_fee   <!-- 按应支付总运费重新计算后的调度费-->
        FROM t_order_pack_info opi
        <if test="(capitalId !=null and capitalId!= '') or goodsId != null">
        LEFT JOIN t_order_pack_detail opd ON opd.id = (SELECT MAX(opds.id) FROM t_order_pack_detail opds  WHERE opi.`code` = opds.pack_pay_code)
        LEFT JOIN t_order_info o on o.code = opd.order_code
        LEFT JOIN t_order_cast_changes tocc ON tocc.id = (SELECT MAX(toccb.id) FROM t_order_cast_changes toccb WHERE opd.order_code = toccb.order_code)
        LEFT JOIN t_dic_cat_item tdci on tocc.capital_transfer_type = tdci.item_code
        </if>
        <if test="carOwnerId != null or (realName != null and realName != '') or (realPhone!=null and realPhone != '')">
        LEFT JOIN t_end_user_info eui ON opi.end_user_id = eui.id
        </if>
        <if test="(cardhoderMan !=null and cardhoderMan!= '') or (cardNo !=null and cardNo!= '')">
        left join t_bank_card tbc on opi.bank_card_id = tbc.id
        left join t_zt_bank_card tzbc on opi.bank_card_id = tzbc.id
        </if>
        <if test="fkStartTime != null or fkEndTime != null">
        left join t_order_pay_info topi on topi.id = (select min(topi.id) from t_order_pack_detail topd left join t_order_pay_info topi on topd.order_code = topi.order_code where topd.pack_pay_code = opi.code )
        LEFT JOIN t_order_info toi on toi.code = topi.order_code
        </if>
        <where>
            opi.`enable` = 0
            <if test="fkStartTime != null">
                AND toi.order_finish_time >= #{fkStartTime}
            </if>
            <if test="fkEndTime != null">
                AND toi.order_finish_time &lt;= #{fkEndTime}
            </if>
            <if test="packId != null and packId.size != 0">
                AND opi.id IN
                <foreach collection="packId" index="index" item="pack" open="(" separator="," close=")">
                    #{pack}
                </foreach>
            </if>
            <if test="companyIds != null and companyIds.size != 0">
                AND opi.company_id IN
                <foreach collection="companyIds" item="ci" index="index" open="(" separator="," close=")">
                    #{ci}
                </foreach>
            </if>
            <if test="endUserId != null">
                AND opi.end_user_id = #{endUserId}
            </if>
            <if test="virtualOrderNo != null">
                AND opi.virtual_order_no LIKE CONCAT('%', #{virtualOrderNo}, '%')
            </if>
            <choose>
                <when test="packStatus != null">
                    and opi.pack_status = #{packStatus}
                </when>
                <otherwise>
                    and opi.pack_status in('PACKEDHANDEL','PACKPAID','PACKWITHDRAW','PACKEWITHDRAWERROR','PACKRECALLPROCESSED','PACKEXTRACTPROCESSED')
                </otherwise>
            </choose>
            <if test="startTime != null">
                AND opi.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND opi.create_time &lt;= #{endTime}
            </if>
            <if test="carOwnerId != null">
                AND eui.id = #{carOwnerId}
            </if>
            <if test="codeArray != null and codeArray.size != 0 ">
                AND opi.code IN
                <foreach collection="codeArray" item="code" index="item" open="(" close=")" separator=",">
                    #{code}
                </foreach>
            </if>
            <if test="cardhoderMan !=null and cardhoderMan!= ''">
                and (tbc.card_owner like CONCAT('%', #{cardhoderMan}, '%') or tzbc.acct_name like CONCAT('%', #{cardhoderMan}, '%') )
            </if>
            <if test="cardNo !=null and cardNo!= ''">
                and (tbc.card_no like CONCAT('%', #{cardNo}, '%') or tzbc.acct_no like CONCAT('%', #{cardNo}, '%') )
            </if>

            <if test="capitalId !=null and capitalId!= ''">
                and tdci.item_code =  #{capitalId}
            </if>
            <if test="realName!=null and realName != ''">
                and eui.real_name LIKE CONCAT('%', #{realName}, '%')
            </if>
            <if test="realPhone!=null and realPhone != ''">
                and eui.phone LIKE CONCAT('%', #{realPhone}, '%')
            </if>
            <if test="goodsId != null">
                AND o.goods_id = #{goodsId}
            </if>
        </where>
        ) a

    </select>

    <!--运单打包列表： 查询已提现打包完成的运单信息 dingweibo-->
    <select id="getOrderPackTxOk" parameterType="com.lz.vo.PcOrderSearchVO" resultType="com.lz.dto.OrderPackDTO">
        SELECT
        distinct opi.code,
        opi.virtual_order_no, <!-- 打包主表虚拟单据号 -->
        opi.check_account_person, <!-- 操作用户（对账人）-->
        opi.order_start_query_time, <!-- 查询单据开始日期-->
        opi.order_end_query_time, <!-- 查询单据结束日期-->
        eui.real_name, <!-- 所属车老板-->
        eui.phone, <!-- 联系方式 -->
        opi.total_selected_orders, <!-- 选中运单累计单据数-->
        opi.total_selected_orders_weight, <!-- 选中运单累计总吨数-->
        opi.total_selected_orders_carriage_fee, <!-- 选中运单累计总运费-->
        opi.total_selected_orders_dispatch_fee, <!-- 选中运单累计总调度费-->
        opi.total_selected_orders_carriage_zero_cut_fee, <!-- 选中运单累计总运费扣款金额   选中运单累计总运费的抹零金额-->
        opi.appointment_payment_cash, <!--与车老板约定应支付总运费现金额-->
        opi.appointment_payment_other, <!-- 与车老板约定应支付总运费其他实物价值-->
        opi.recount_dispatch_fee, <!-- 按应支付总运费重新计算后的调度费-->
        opi.pack_status,
        dci.item_value as packStatusValue, <!-- 打包状态-->
        opi.content_describtion, <!-- 约定内容描述-->
        opi.create_time as createTimes, <!-- 创建时间-->
        opi.remark, <!-- 备注-->
        ifnull(tdci.item_value, tdcis.item_value) as capitalTransferType, <!-- 资金转移方式 -->
        case pay.payment_platforms when '京东' then ifnull(tx.bank_no, tbc.card_no)
        when '华夏' then  ifnull(tx.bank_no, tzbc.acct_no)
        else ifnull(tx.bank_no, tbc.card_no) end card_no,
        case pay.payment_platforms when '京东' then ifnull(tx.card_holder, tbc.card_owner)
        when '华夏' then  ifnull(tx.card_holder, tzbc.acct_name)
        else ifnull(tx.card_holder, tbc.card_owner) end card_owner,
        tocct.create_time as yfkTxTime, <!--提现时间-->
        toi.business_assist,
        toi.goods_name,
        case opi.payment_platforms when '京东' then '京东' else '网商' end paymentPlatforms
        FROM t_order_pack_info opi
        LEFT JOIN t_order_pack_detail opd ON opd.id = (SELECT MAX(opds.id) FROM t_order_pack_detail opds  WHERE opi.`code` = opds.pack_pay_code)
        LEFT JOIN t_order_cast_changes tocc ON tocc.id = (SELECT MAX(toccb.id) FROM t_order_cast_changes toccb WHERE opd.order_code = toccb.order_code)
        LEFT JOIN t_end_user_info eui ON opi.end_user_id = eui.id
        LEFT JOIN t_dic_cat_item dci ON opi.pack_status = dci.item_code
        LEFT JOIN t_dic_cat_item tdci on tocc.capital_transfer_type = tdci.item_code
        left join t_bank_card tbc on opi.bank_card_id = tbc.id
        left join t_zt_bank_card tzbc on opi.bank_card_id = tzbc.id
        LEFT JOIN t_order_cast_changes tocct ON tocct.id = (SELECT MAX(toccb.id) FROM t_order_cast_changes toccb WHERE opi.code = toccb.order_code and user_oper = 'TX')
        left join t_order_pay_info pay on pay.order_code = opi.code
        left join (select order_pay_code,operate_time,return_time from t_order_pay_detail where trade_type = 'YFRZ'  and trade_status='TRADE_FINISHED') zf on zf.order_pay_code = pay.code
        left join (select order_pay_code,operate_time,return_time, bank_no, card_holder from t_order_pay_detail where trade_type = 'DBTX'  and trade_status='TRADE_FINISHED') tx on tx.order_pay_code = pay.code
        left join t_order_info toi on opd.order_code = toi.code
        left join t_order_cast_changes toccs on toi.code = toccs.order_code and toccs.data_enable = 1
        left join t_dic_cat_item tdcis on toccs.capital_transfer_type = tdcis.item_code
        <where>
            opi.`enable` = 0
            and opi.pack_status ='PACKWITHDRAW'
            <if test="bankCard != null">
                AND tbc.card_no LIKE concat('%', #{bankCard}, '%')
            </if>
            <if test="fkStartTime != null">
                AND zf.operate_time >= #{fkStartTime}
            </if>
            <if test="fkEndTime != null">
                AND zf.operate_time &lt;= #{fkEndTime}
            </if>
            <if test="txStartTime != null">
                AND tocct.create_time >= #{txStartTime}
            </if>
            <if test="txEndTime != null">
                AND tocct.create_time &lt;= #{txEndTime}
            </if>
            <if test="dzStartTime != null">
                AND tx.return_time >= #{dzStartTime}
            </if>
            <if test="dzEndTime != null">
                AND tx.return_time &lt;= #{dzEndTime}
            </if>
            <if test="packId != null and packId.size != 0">
                AND opi.id IN
                <foreach collection="packId" index="index" item="pack" open="(" separator="," close=")">
                    #{pack}
                </foreach>
            </if>
            <if test="companyIds != null and companyIds.size != 0">
                AND opi.company_id IN
                <foreach collection="companyIds" item="ci" index="index" open="(" separator="," close=")">
                    #{ci}
                </foreach>
            </if>
            <!-- 如果是车主 -->
            <if test="carOwner and carOwnerId != null">
                and opi.end_user_id = #{carOwnerId} and (tocc.capital_transfer_type = 'PAYTOBELONGER' or toccs.capital_transfer_type = 'PAYTOBELONGER')
            </if>

            <!-- 如果是车队长 -->
            <if test="carCaptain and carCaptain != null">
                and opi.end_user_id = #{carCaptainId} and (tocc.capital_transfer_type = 'PAYTOCAPTAIN' or toccs.capital_transfer_type = 'PAYTOCAPTAIN')
            </if>
            <!-- 如果是经纪人 -->
            <if test="endAgent and endAgentId != null">
                AND ((opi.end_user_id = #{endAgentId} and toccs.capital_transfer_type like 'FIRST%')
                or (toi.business_assist = 1 and toi.end_agent_id = #{endAgentId})
                )
            </if>
            <if test="virtualOrderNo != null">
                AND opi.virtual_order_no LIKE CONCAT('%', #{virtualOrderNo}, '%')
            </if>
            <if test="packStatus != null">
                AND opi.pack_status = #{packStatus}
            </if>
            <if test="startTime != null">
                AND opi.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND opi.create_time &lt;= #{endTime}
            </if>
            <if test="carOwnerId != null">
                AND eui.id = #{carOwnerId}
            </if>
            <if test="carCaptainId != null">
                AND eui.id = #{carCaptainId}
            </if>
            <if test="codeArray != null and codeArray.size != 0 ">
                AND opi.code IN
                <foreach collection="codeArray" item="code" index="item" open="(" close=")" separator=",">
                    #{code}
                </foreach>
            </if>

            <if test="cardhoderMan !=null and cardhoderMan!= ''">
                and (tx.card_holder like CONCAT('%', #{cardhoderMan}, '%') or tbc.card_owner like CONCAT('%', #{cardhoderMan}, '%') or tzbc.acct_name like CONCAT('%', #{cardhoderMan}, '%') )
            </if>
            <if test="cardNo !=null and cardNo!= ''">
                and (tx.bank_no like CONCAT('%', #{cardNo}, '%') or tbc.card_no like CONCAT('%', #{cardNo}, '%') or tzbc.acct_no like CONCAT('%', #{cardNo}, '%') )
            </if>

            <if test="realName!=null and realName != ''">
                and eui.real_name LIKE CONCAT('%', #{realName}, '%')
            </if>
            <if test="realPhone!=null and realPhone != ''">
                and eui.phone LIKE CONCAT('%', #{realPhone}, '%')
            </if>
            <if test="capitalId != null ">
                and tdci.item_code =  #{capitalId}
            </if>
            <if test="goodsId != null">
                AND toi.goods_id = #{goodsId}
            </if>
        </where>
        ORDER BY opi.create_time DESC
    </select>


    <!--运单打包列表： 查询已提现打包完成的运单信息 liuyang-->
    <select id="getOrderPackTxOkSUM" parameterType="com.lz.vo.PcOrderSearchVO" resultType="java.util.Map">
        select
        IFNULL(SUM(IFNULL(a.total_selected_orders_weight,0)), 0) as totalSelectedOrdersWeight, <!-- 选中运单累计总吨数-->
        IFNULL(SUM(IFNULL(a.appointment_payment_cash,0)), 0) as totalSelectedOrdersCarriageFee, <!-- 选中运单累计总运费-->
        IFNULL(SUM(IFNULL(a.recount_dispatch_fee,0)), 0) as totalSelectedOrdersDispatchFee  <!-- 选中运单累计总调度费-->
        from (
        SELECT
        distinct opi.code,
        opi.total_selected_orders_weight, <!-- 选中运单累计总吨数-->
        opi.appointment_payment_cash,   <!--与车老板约定应支付总运费现金额-->
        opi.recount_dispatch_fee   <!-- 按应支付总运费重新计算后的调度费-->
        FROM t_order_pack_info opi
        LEFT JOIN t_order_pack_detail opd ON opd.id = (SELECT MAX(opds.id) FROM t_order_pack_detail opds  WHERE opi.`code` = opds.pack_pay_code)
        LEFT JOIN t_order_cast_changes tocc ON tocc.id = (SELECT MAX(toccb.id) FROM t_order_cast_changes toccb WHERE opd.order_code = toccb.order_code)
        LEFT JOIN t_end_user_info eui ON opi.end_user_id = eui.id
        LEFT JOIN t_dic_cat_item dci ON opi.pack_status = dci.item_code
        LEFT JOIN t_dic_cat_item tdci on tocc.capital_transfer_type = tdci.item_code
        left join t_bank_card tbc on opi.bank_card_id = tbc.id
        left join t_zt_bank_card tzbc on opi.bank_card_id = tzbc.id
        LEFT JOIN t_order_cast_changes tocct ON tocct.id = (SELECT MAX(toccb.id) FROM t_order_cast_changes toccb WHERE opi.code = toccb.order_code and user_oper = 'TX')
        left join t_order_pay_info pay on pay.order_code = opi.code
        left join t_order_info toi on opd.order_code = toi.code
        left join t_order_cast_changes toccs on toi.code = toccs.order_code and toccs.data_enable = 1
        left join (select order_pay_code,operate_time,return_time from t_order_pay_detail where trade_type = 'YFRZ'  and trade_status='TRADE_FINISHED') zf on zf.order_pay_code = pay.code
        left join (select order_pay_code,operate_time,return_time, bank_no, card_holder from t_order_pay_detail where trade_type = 'DBTX'  and trade_status='TRADE_FINISHED') tx on tx.order_pay_code = pay.code
        <where>
            opi.`enable` = 0
            and opi.pack_status ='PACKWITHDRAW'
            <if test="bankCard != null">
                AND tbc.card_no LIKE concat('%', #{bankCard}, '%')
            </if>
            <if test="fkStartTime != null">
                AND zf.operate_time >= #{fkStartTime}
            </if>
            <if test="fkEndTime != null">
                AND zf.operate_time &lt;= #{fkEndTime}
            </if>
            <if test="txStartTime != null">
                AND tocct.create_time >= #{txStartTime}
            </if>
            <if test="txEndTime != null">
                AND tocct.create_time &lt;= #{txEndTime}
            </if>
            <if test="dzStartTime != null">
                AND tx.return_time >= #{dzStartTime}
            </if>
            <if test="dzEndTime != null">
                AND tx.return_time &lt;= #{dzEndTime}
            </if>
            <if test="packId != null and packId.size != 0">
                AND opi.id IN
                <foreach collection="packId" index="index" item="pack" open="(" separator="," close=")">
                    #{pack}
                </foreach>
            </if>
            <if test="companyIds != null and companyIds.size != 0">
                AND opi.company_id IN
                <foreach collection="companyIds" item="ci" index="index" open="(" separator="," close=")">
                    #{ci}
                </foreach>
            </if>
            <!-- 如果是车主 -->
            <if test="carOwner and carOwnerId != null">
                and opi.end_user_id = #{carOwnerId} and (tocc.capital_transfer_type = 'PAYTOBELONGER' or toccs.capital_transfer_type = 'PAYTOBELONGER')
            </if>
            <!-- 如果是车队长 -->
            <if test="carCaptain and carCaptain != null">
                and opi.end_user_id = #{carCaptainId} and (tocc.capital_transfer_type = 'PAYTOCAPTAIN' or toccs.capital_transfer_type = 'PAYTOCAPTAIN')
            </if>
            <!-- 如果是经纪人 -->
            <if test="endAgent and endAgentId != null">
                AND ((opi.end_user_id = #{endAgentId} or (toi.business_assist = 1 and toi.end_agent_id = #{endAgentId}))
                and (tocc.capital_transfer_type like 'FIRST%' or toccs.capital_transfer_type like 'FIRST%')
                )
            </if>
            <if test="virtualOrderNo != null">
                AND opi.virtual_order_no LIKE CONCAT('%', #{virtualOrderNo}, '%')
            </if>
            <if test="packStatus != null">
                AND opi.pack_status = #{packStatus}
            </if>
            <if test="startTime != null">
                AND opi.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND opi.create_time &lt;= #{endTime}
            </if>
            <if test="carOwnerId != null">
                AND eui.id = #{carOwnerId}
            </if>
            <if test="carCaptainId != null">
                AND eui.id = #{carCaptainId}
            </if>
            <if test="codeArray != null and codeArray.size != 0 ">
                AND opi.code IN
                <foreach collection="codeArray" item="code" index="item" open="(" close=")" separator=",">
                    #{code}
                </foreach>
            </if>
            <if test="cardhoderMan !=null and cardhoderMan!= ''">
                and (tx.card_holder like CONCAT('%', #{cardhoderMan}, '%') or tbc.card_owner like CONCAT('%', #{cardhoderMan}, '%') or tzbc.acct_name like CONCAT('%', #{cardhoderMan}, '%') )
            </if>
            <if test="cardNo !=null and cardNo!= ''">
                and (tx.bank_no like CONCAT('%', #{cardNo}, '%') or tbc.card_no like CONCAT('%', #{cardNo}, '%') or tzbc.acct_no like CONCAT('%', #{cardNo}, '%') )
            </if>
            <if test="realName!=null and realName != ''">
                and eui.real_name LIKE CONCAT('%', #{realName}, '%')
            </if>
            <if test="realPhone!=null and realPhone != ''">
                and eui.phone LIKE CONCAT('%', #{realPhone}, '%')
            </if>
            <if test="capitalId != null ">
                and tdci.item_code =  #{capitalId}
            </if>
            <if test="goodsId != null">
                AND toi.goods_id = #{goodsId}
            </if>
        </where>
        ) a
    </select>

  <!--运单打包支付-拆包： 根据打包主表 32 CODE 逻辑删除打包主表数据 Yan-->
  <update id="orderDismantledPackLogicDelete" parameterType="java.lang.String">
    UPDATE
    t_order_pack_info opi
    SET opi.`enable` = 1
    WHERE opi.`code` = #{code}
  </update>

  <select id="selectOrderPackByCode" parameterType="java.lang.String" resultType="com.lz.model.TOrderPackInfo">
    select
    id, code, virtual_order_no, check_account_person, order_start_query_time, order_end_query_time,
    end_user_id, end_vehicle_id, total_selected_orders, total_selected_orders_weight,
    total_selected_orders_carriage_fee, total_selected_orders_dispatch_fee, total_selected_orders_service_fee,
    total_selected_orders_other_fee, total_selected_orders_fee, total_selected_orders_carriage_zero_cut_fee,
    appointment_payment_cash, appointment_payment_other, recount_dispatch_fee, content_describtion,
    pack_status, carrier_id, company_id, bank_card_id, remark, param1, param2, param3,
    param4, create_user, create_time, update_user, update_time, `enable`, share_coefficient, ls_calculation, payment_platforms
    from t_order_pack_info
    where code = #{code}
  </select>

  <update id="updateTotalFeeAndDispatchFeeByCode" >
    UPDATE
    t_order_pack_info opi
    SET opi.`recount_dispatch_fee` = #{dispatchFee}
    WHERE opi.`code` = #{code}
  </update>

  <update id="updatePayStatusByOrderCode" >
    UPDATE
    t_order_pack_info opi
    SET opi.`pack_status` = #{status}
    WHERE opi.`code` = #{code}
  </update>


  <select id="selectByPackInfo" parameterType="java.lang.String" resultType="com.lz.model.TOrderPackInfo">
      select a.*  from  t_order_pack_info a
      left join t_order_pay_info b on a.code =b.order_code
      left join t_order_pay_detail c on b.code = c.order_pay_code
      where c.code = #{outerTradeNo,jdbcType=VARCHAR}
  </select>
  <!--打包之前判断一下打包主表的状态是否是已支付，如果已支付不能在拆包 Yan-->
  <select id="judgeOrderPackStatus" parameterType="java.lang.String" resultType="java.lang.Boolean">
    SELECT
        not ISNULL(
            (
                SELECT
                    opi.id
                FROM
                    t_order_pack_info opi
                WHERE
                    opi.`code` = #{code}
                AND opi.pack_status in('PACKED', 'PACKPAID', 'PACKRECALL')
            )
        ) as asNull
  </select>

  <select id="selectByPayInfoLimitOne" parameterType="java.lang.String" resultType="com.lz.dto.TOrderPayInfoDTO">
select * from t_order_pack_info orderpay
LEFT JOIN t_order_pack_detail orderpaydetail
on orderpay.`code` = orderpaydetail.pack_pay_code
where orderpay.`code`=#{code,jdbcType=VARCHAR}
LIMIT 1
    </select>

  <select id="managerWithdrawPage" parameterType="com.lz.vo.PcOrderSearchVO" resultType="com.lz.dto.OrderPackDTO">
    select *
    from (
    SELECT
    opi.code,
    opi.virtual_order_no, <!-- 打包主表虚拟单据号 -->
    opi.check_account_person, <!-- 操作用户（对账人）-->
    opi.order_start_query_time, <!-- 查询单据开始日期-->
    opi.order_end_query_time, <!-- 查询单据结束日期-->
    eui.real_name, <!-- 所属车老板-->
    opi.total_selected_orders, <!-- 选中运单累计单据数-->
    opi.total_selected_orders_weight, <!-- 选中运单累计总吨数-->
    opi.total_selected_orders_carriage_fee, <!-- 选中运单累计总运费-->
    opi.total_selected_orders_dispatch_fee, <!-- 选中运单累计总调度费-->
    opi.total_selected_orders_service_fee, <!-- 选中运单服务费-->
    opi.total_selected_orders_carriage_zero_cut_fee, <!-- 选中运单累计总运费扣款金额   选中运单累计总运费的抹零金额-->
    opi.appointment_payment_cash, <!--与车老板约定应支付总运费现金额-->
    opi.appointment_payment_other, <!-- 与车老板约定应支付总运费其他实物价值-->
    opi.recount_dispatch_fee, <!-- 按应支付总运费重新计算后的调度费-->
    opi.pack_status,
    dci.item_value as packStatusValue, <!-- 打包状态-->
    opi.content_describtion, <!-- 约定内容描述-->
    opi.create_time as createTimes, <!-- 创建时间-->
    opi.remark, <!-- 备注-->
    ifnull(tdci.item_value, tdcis.item_value) as capitalTransferType, <!-- 资金转移方式 -->
    tbc.card_no, <!-- 收款银行卡 -->
    tbc.card_owner, <!-- 收款人 -->
    toi.business_assist,
    opi.update_time updateTime,
    case opi.payment_platforms when '京东' then '京东' else '网商' end paymentPlatforms
    FROM t_order_pack_info opi
    LEFT JOIN t_order_cast_changes tocc ON tocc.id = (
    SELECT
    toccb.id
    FROM t_order_cast_changes toccb
    WHERE opi.CODE = toccb.order_code  AND toccb.`enable` = 0
    ORDER BY toccb.create_time DESC
    LIMIT 1
    )
    LEFT JOIN t_order_pack_detail opd ON opd.id = (SELECT MAX(opds.id) FROM t_order_pack_detail opds  WHERE opi.`code` = opds.pack_pay_code)
    LEFT JOIN  t_order_info toi ON opd.order_code = toi.code
    LEFT JOIN t_end_user_info eui ON opi.end_user_id = eui.id
    LEFT JOIN t_dic_cat_item dci ON opi.pack_status = dci.item_code
    left join t_dic_cat_item tdci on tocc.capital_transfer_type = tdci.item_code
    LEFT JOIN t_bank_card tbc ON opi.bank_card_id = tbc.id
    left join t_order_pack_detail topd on (select max(id) from t_order_pack_detail where pack_pay_code = opi.code ) = topd.id
    left join t_order_cast_changes toccs on toi.code = toccs.order_code and toccs.data_enable = 1
    left join t_dic_cat_item tdcis on toccs.capital_transfer_type = tdcis.item_code
    <where>
      opi.`enable` = 0
      <if test="companyIds != null">
        AND opi.company_id IN
        <foreach collection="companyIds" item="ci" index="index" open="(" separator="," close=")">
          #{ci}
        </foreach>
      </if>
      <if test="packStatusArray != null">
        AND opi.pack_status IN
        <foreach collection="packStatusArray" index="index" item="psa" open="(" separator="," close=")">
          #{psa}
        </foreach>
      </if>
        <!-- 如果是车主 -->
        <if test="carOwner and carOwnerId != null">
            and opi.end_user_id = #{carOwnerId} and (tocc.capital_transfer_type = 'PAYTOBELONGER' or toccs.capital_transfer_type = 'PAYTOBELONGER')
        </if>

        <!-- 如果是车队长 -->
        <if test="carCaptain and carCaptainId != null">
            and opi.end_user_id = #{carCaptainId} and (tocc.capital_transfer_type = 'PAYTOCAPTAIN' or toccs.capital_transfer_type = 'PAYTOCAPTAIN')
        </if>
        <!-- 如果是经纪人 -->
        <if test="endAgent and endAgentId != null">
            AND ((opi.end_user_id = #{endAgentId} and toccs.capital_transfer_type like 'FIRST%')
            or (toi.business_assist = 1 and toi.end_agent_id = #{endAgentId})
            )
        </if>
      <if test="bankCard != null">
        AND tbc.card_no LIKE concat('%', #{bankCard}, '%')
      </if>
      <if test="virtualOrderNo != null">
        AND opi.virtual_order_no LIKE CONCAT('%', #{virtualOrderNo}, '%')
      </if>
      <if test="startTime != null">
        AND opi.create_time >= #{startTime}
      </if>
      <if test="endTime != null">
        AND opi.create_time &lt;= #{endTime}
      </if>
      <if test="carOwnerId != null">
        AND eui.id = #{carOwnerId}
      </if>
      <if test="carCaptainId != null">
        AND eui.id = #{carCaptainId}
      </if>

    </where>
    ) torder
    <if test="level != null and levelName != null">
      order by ${levelName} ${orderName}
    </if>
  </select>

  <select id="selectOrderCodeByPackCode" parameterType="java.lang.String" resultType="java.lang.String">
    select topd.order_code code
    from t_order_pack_info topi
    left join t_order_pack_detail topd on topi.code = topd.pack_pay_code
    where topi.code = #{code}
  </select>

  <update id="updatePackStatusByOrderCode" >
    UPDATE
      t_order_pack_info opi
    SET opi.`pack_status` = #{packStatus}
    WHERE opi.`code` = #{code}
  </update>

    <update id="updatePackLsCalculationCode" parameterType="java.lang.String">
    UPDATE
      t_order_pack_info opi
    SET opi.ls_calculation = 1
    WHERE opi.`code` = #{code}
  </update>


  <select id="selectByBankno" parameterType="java.lang.String" resultType="com.lz.dto.TOrderPayInfoDTO">
    select
    bank.card_no as bankNo,
    bank.card_owner as cardHolder
    from t_order_pack_detail topd
    left join t_order_pack_info topi on topi.code = topd.pack_pay_code
    left join t_bank_card bank on bank.id = topi.bank_card_id
    where topd.order_code = #{orderCode}
  </select>
  <!--打包支付页面，根据司机经纪人进行模糊搜索打包运单 Yan-->
  <select id="getOrderPackByDriverAgent" parameterType="com.lz.vo.PcOrderSearchVO" resultType="java.lang.Integer">
    SELECT DISTINCT
        opi.id
    FROM t_order_pack_info opi
    LEFT JOIN t_order_pack_detail opd ON opi.`code` = opd.pack_pay_code
    LEFT JOIN t_order_info toi ON opd.order_code = toi.`code`
    INNER JOIN t_end_user_info euia ON opi.end_user_id = euia.id
    <where>
      opi.`enable` = 0
      <if test="driverOrAgent != null">
        AND euia.real_name LIKE CONCAT('%', #{driverOrAgent}, '%')
      </if>
      <if test="companyIds != null and companyIds.size != 0">
        AND opi.company_id IN
        <foreach collection="companyIds" item="ci" index="index" open="(" separator="," close=")">
          #{ci}
        </foreach>
      </if>
    </where>

  </select>

  <select id="selectPackInfoByPayDetailCode" parameterType="java.lang.String" resultType="com.lz.model.TOrderPackInfo">
    select a.*  from  t_order_pack_info a
    left join t_order_pay_info b on a.code =b.order_code
    left join t_order_pay_detail c on b.code = c.order_pay_code
    where c.code = #{outerTradeNo,jdbcType=VARCHAR}
  </select>

  <select id="selectOrderIdByPackCode" parameterType="java.lang.String" resultType="java.lang.Integer">
    select topd.id
    from t_order_pack_info topi
    left join t_order_pack_detail topd on topi.code = topd.pack_pay_code
    where topi.code = #{code}
  </select>

  <update id="updatePayStatusById">
    UPDATE
    t_order_pack_info
    SET `pack_status` = #{status}
    WHERE id = #{id}
  </update>

  <select id="selectVirtualOrderByCode" resultType="java.lang.String">
    select virtual_order_no
    from t_order_pack_info
    where code in
    <foreach collection="codes" item="code" index="index" open="(" close=")" separator=",">
      #{code}
    </foreach>
  </select>

  <select id="getOrderInfoByPackCode" parameterType="java.lang.String" resultType="com.lz.model.TOrderInfo">
    select
    toi.*
    from t_order_pack_info topi
    left join t_order_pack_detail topd on topd.pack_pay_code = topi.code
      left join  t_order_info toi on toi.code = topd.order_code
      where topi.code = #{code}
  </select>

  <select id="selectPackInfoByOneOrderCode" parameterType="java.lang.String"
          resultType="com.lz.model.TOrderPackInfo">
      select
        topi.id, topi.code, topi.virtual_order_no, topi.check_account_person, topi.order_start_query_time,
        topi.order_end_query_time, topi.end_user_id, topi.end_vehicle_id, topi.total_selected_orders,
        topi.total_selected_orders_weight, topi.total_selected_orders_carriage_fee, topi.total_selected_orders_dispatch_fee,
        topi.total_selected_orders_service_fee, topi.total_selected_orders_other_fee, topi.total_selected_orders_fee,
        topi.total_selected_orders_carriage_zero_cut_fee, topi.appointment_payment_cash, topi.appointment_payment_other,
        topi.recount_dispatch_fee, topi.content_describtion, topi.pack_status, topi.carrier_id, topi.company_id,
        topi.bank_card_id, topi.remark, topi.param1, topi.param2, topi.param3, topi.param4, topi.create_user,
        topi.create_time, topi.update_user, topi.update_time, topi.`enable`, topi.share_coefficient
      from t_order_pack_info topi
        left join t_order_pack_detail topd on topd.pack_pay_code = topi.code
        left join t_order_info toi on toi.code = topd.order_code
    where toi.code = #{orderCode} and toi.enable = 0 and toi.pack_status = 1 and topi.enable = 0 and topd.enable = 0
  </select>
    <select id="selectOrderDetailByOrdercode" resultType="com.lz.model.TOrderPayDetail">
                SELECT
                    tod.id, tod.return_time
                FROM
                	t_order_pay_info top
                LEFT JOIN t_order_pay_detail tod ON top. CODE = tod.order_pay_code
                WHERE
                	top.order_pay_status = 'M130'
                AND top. ENABLE = 0
                AND tod. ENABLE = 0
                AND top.order_code = #{code,jdbcType=VARCHAR}
                AND tod.trade_type = 'TX'
                AND tod.trade_status='TRADE_FINISHED'
                limit 0,1
<!--                AND  TO_SECONDS(NOW())-TO_SECONDS(tod.return_time)&lt;=2592000-->
    </select>

    <select id="selectNewPackCarriageFeeByBankIds" resultType="java.math.BigDecimal">
        SELECT
        	ifnull(sum(topi.appointment_payment_cash - ifnull(topi.total_selected_orders_service_fee, 0)), 0) carriageFee
        FROM
        	t_order_pack_info topi
        	LEFT JOIN t_order_pack_detail topd ON topd.id = ( SELECT id FROM t_order_pack_detail WHERE topi.CODE = pack_pay_code LIMIT 1 )
        	LEFT JOIN t_order_cast_changes tocc ON ( SELECT Max( id ) AS id FROM t_order_cast_changes WHERE data_enable = 1 AND user_oper = 'Payment' AND withdraw_type = 'AUTOMATION' AND order_code = topd.order_code ) = tocc.id
        WHERE
            topi.pack_status= 'PACKEDHANDEL'
        	AND tocc.data_enable = 1
        	AND tocc.user_oper = 'Payment'
        	AND tocc.withdraw_type = 'AUTOMATION'
        	AND topi.bank_card_id IN
        	<foreach collection="list" index="index" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
    </select>

    <select id="selectOldPackCarriageFeeByBankIds" resultType="java.math.BigDecimal">
        SELECT
        	ifnull(sum(topi.appointment_payment_cash - ifnull(topi.total_selected_orders_service_fee, 0)), 0) carriageFee
        FROM
        	t_order_pack_info topi
        	LEFT JOIN t_order_cast_changes tocc ON topi.CODE = tocc.order_code
        WHERE
        	tocc.data_enable = 1
        	AND tocc.user_oper = 'DBPayment'
        	AND tocc.withdraw_type = 'AUTOMATION'
        	AND topi.bank_card_id IN
            <foreach collection="list" index="index" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
    </select>

    <select id="selectPackFQTXAmountByBankIds" resultType="java.math.BigDecimal">
        SELECT
        ifnull( sum(ifnull( topi.appointment_payment_cash, 0 ) - ifnull( topi.total_selected_orders_service_fee, 0 )), 0) amount
        FROM
        t_task ta
        LEFT JOIN t_order_pack_info topi ON ta.source_fieldvalue = topi.CODE
        WHERE
            ta.param1 IN
        <foreach collection="list" index="index" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
        AND ta.task_type_node = 'FQTX'
        AND ta.task_type = 'FQ';
    </select>

    <select id="selectOrderCodeByPackId" parameterType="java.lang.Integer" resultType="java.lang.String">
        select topd.order_code code
        from t_order_pack_info topi
                 left join t_order_pack_detail topd on topi.code = topd.pack_pay_code
        where topi.id = #{id}
    </select>

    <select id="selectJdPackInfoByOrderBusinessCode" parameterType="java.lang.String"
            resultType="com.lz.model.TOrderPackInfo">
        select
            topi.id, topi.code, topi.virtual_order_no, topi.check_account_person, topi.order_start_query_time,
            topi.order_end_query_time, topi.end_user_id, topi.end_vehicle_id, topi.total_selected_orders,
            topi.total_selected_orders_weight, topi.total_selected_orders_carriage_fee, topi.total_selected_orders_dispatch_fee,
            topi.total_selected_orders_service_fee, topi.total_selected_orders_other_fee, topi.total_selected_orders_fee,
            topi.total_selected_orders_carriage_zero_cut_fee, topi.appointment_payment_cash, topi.appointment_payment_other,
            topi.recount_dispatch_fee, topi.content_describtion, topi.pack_status, topi.carrier_id, topi.company_id,
            topi.bank_card_id, topi.remark, topi.param1, topi.param2, topi.param3, topi.param4, topi.create_user,
            topi.create_time, topi.update_user, topi.update_time, topi.`enable`, topi.share_coefficient
        from t_order_pack_info topi
        left join t_order_pack_detail topd on topd.pack_pay_code = topi.code
        left join t_order_info toi on toi.code = topd.order_code
        where toi.order_business_code = #{orderBusinessCode} and toi.enable = 0 and toi.pack_status = 1 and topi.enable = 0 and topd.enable = 0
    </select>

    <select id="selectPackInfoByOrderCode" resultType="com.lz.model.TOrderPackInfo">
        select
            topi.id, topi.code, topi.virtual_order_no, topi.check_account_person, topi.order_start_query_time,
            topi.order_end_query_time, topi.end_user_id, topi.end_vehicle_id, topi.total_selected_orders,
            topi.total_selected_orders_weight, topi.total_selected_orders_carriage_fee, topi.total_selected_orders_dispatch_fee,
            topi.total_selected_orders_service_fee, topi.total_selected_orders_other_fee, topi.total_selected_orders_fee,
            topi.total_selected_orders_carriage_zero_cut_fee, topi.appointment_payment_cash, topi.appointment_payment_other,
            topi.recount_dispatch_fee, topi.content_describtion, topi.pack_status, topi.carrier_id, topi.company_id,
            topi.bank_card_id, topi.remark, topi.param1, topi.param2, topi.param3, topi.param4, topi.create_user,
            topi.create_time, topi.update_user, topi.update_time, topi.`enable`, topi.share_coefficient
        from t_order_pack_info topi
        left join t_order_pack_detail topd on topd.pack_pay_code = topi.code
        where topd.order_code = #{orderCode} and topd.enable = 0
    </select>

    <select id="getEndDriverIdByPackCode" parameterType="java.lang.String" resultType="java.lang.Integer">
        select
            distinct toi.end_driver_id
        from t_order_pack_info topi
                 left join t_order_pack_detail topd on topd.pack_pay_code = topi.code
                 left join  t_order_info toi on toi.code = topd.order_code
        where topi.code = #{packCode}
    </select>

    <select id="selectPackInfoByOrderBusinessCodePayTime" resultType="com.lz.model.TOrderPackInfo">
        SELECT
            *
        FROM
        t_order_pack_info
        WHERE
            CODE IN ( SELECT pack_pay_code FROM t_order_pack_detail WHERE order_code = #{orderBusinessCode} GROUP BY pack_pay_code )
            AND create_time &lt;=  #{payTime} order by id desc limit 1
    </select>

    <select id="calculateInsuranceAmount" resultType="java.math.BigDecimal">
        select sum(ifnull(toinsurance.insured_amount,0)) as insurance_amount
        from t_order_pack_detail topd
        left join t_order_info toi on toi.code = topd.order_code
        left join t_order_insurance toinsurance on toi.order_business_code = toinsurance.order_business_code and toinsurance.insure > 0 and toinsurance.insurance_cancellation = 0 and toinsurance.enable = 0
        where topd.pack_pay_code = #{code}
    </select>

    <select id="selectByVirtualOrderNo" resultType="com.lz.model.TOrderPackInfo">
        select * from t_order_pack_info where virtual_order_no = #{virtualOrderNo}
    </select>

    <update id="updatePackStatusByCode" >
        UPDATE
            t_order_pack_info opi
        SET opi.`pack_status` = #{packStatus},
            opi.update_time = #{returnTime}
        WHERE opi.`code` = #{code}
    </update>

</mapper>