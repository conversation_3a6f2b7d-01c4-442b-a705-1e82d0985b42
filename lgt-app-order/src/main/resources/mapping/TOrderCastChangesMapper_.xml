<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TOrderCastChangesMapper">
  <select id="selectOrderCastChangeOneByPackCode" parameterType="java.lang.String" resultType="com.lz.model.TOrderCastChanges">
    SELECT
      tocc.id, tocc.code, tocc.pid, tocc.order_code, tocc.line_goods_carriage_rule_id, tocc.carrier_wallet_id,
      tocc.end_driver_wallet_id,
      tocc.end_user_type, tocc.company_wallet_id, tocc.company_project_wallet_id, tocc.carriage_fee, tocc.dispatch_fee,
      tocc.service_fee, tocc.other_fee, tocc.total_fee, tocc.user_oper, tocc.trade_type, tocc.current_dispatch_rate,
      tocc.withdraw_type, tocc.capital_transfer_type, tocc.data_enable, tocc.param1, tocc.param2, tocc.param3, tocc.param4,
      tocc.create_user, tocc.create_time, tocc.update_user, tocc.update_time, tocc.`enable`, tocc.capital_transfer_pattern,
      tocc.share_method, tocc.share_value
    FROM
      t_order_pack_info topi
        LEFT JOIN  t_order_pack_detail topd  ON  topi. CODE = topd.pack_pay_code
        LEFT JOIN t_order_info toi ON toi. CODE = topd.order_code
        left join t_order_cast_changes tocc on tocc.order_code = toi.code and tocc.data_enable = 1
    WHERE
      topi.code = #{packCode} and topi.enable = 0 and topd.enable = 0 limit 1
  </select>

  <update id="doInvalid" parameterType="java.lang.String">
    update t_order_cast_changes
    set
      data_enable = false
    where order_code = #{orderCode,jdbcType=VARCHAR}
  </update>

  <select id="selectByNewOne"  resultMap="BaseResultMap">
    select * from t_order_cast_changes
    where data_enable = true and enable = false and  order_code = #{orderCode,jdbcType=VARCHAR} order by create_time desc limit 1
  </select>

  <select id="selectByTradeType"  resultMap="BaseResultMap">
    select * from t_order_cast_changes
    where
    <!-- data_enable = true and enable = false and-->
    order_code = #{orderCode,jdbcType=VARCHAR}
    and trade_type = #{tradeType} and user_oper = #{userOper}
  </select>

  <!--PC端 运单管理: 获取订单网商子账户资金变动过程 Yan-->
  <select id="pcSelectAccountFundChanges" parameterType="java.lang.String" resultType="com.lz.dto.OrderFundChangesDTO">
    SELECT
    dci.item_code, #<!-- 交易类型CODE -->
    dci.item_value, #<!-- 交易类型-->
    occ.create_time, #<!--交易时间-->
    case when occ.user_oper='TX' then opd.return_time
    ELSE ifnull( opda.return_time, occ.create_time) end createTimeStr,#<!--交易时间:字符串-->
    occ.carriage_fee, #<!--运费-->
    occ.dispatch_fee, #<!--调度费-->
    ifnull(occ.service_fee, 0) serviceFee,
    occ.total_fee, #<!--总费用-->
    opda.trade_type AS payCode, #<!-- 支付状态CODE -->
    case opda.trade_type
    when 'YFRZ' then '运费'
    when 'DDFRZ' then '调度费'
    when 'FWFRZ' then '服务费'
    when 'OYFRZ' then '运费'
    end AS payState, #<!-- 支付状态 -->
    bcecr.thrid_pary_sub_account as BParySubAccount, #<!--B端网商子账户-->
    pcecr.thrid_pary_sub_account as PParySubAccount, #<!--P端网商子账户-->
    ccecr.thrid_pary_sub_account as CParySubAccount, #<!--C端网商子账户-->
    acecr.thrid_pary_sub_account as AParySubAccount, #<!--经纪人网商子账户-->
    ocecr.thrid_pary_sub_account as OParySubAccount, #<!--车主网商子账户-->
    opd.bank_no,
    occ.trade_type,
    opd1.bank_no as zhTxBankNo,
    opd2.bank_no as xhTxBankNo,
    opd3.bank_no as sdTxBankNo,
    opd4.bank_no as wkTxBankNo
    FROM t_order_cast_changes occ
    LEFT JOIN t_dic_cat_item dci ON dci.item_code = occ.user_oper
    LEFT JOIN t_wallet twp ON occ.carrier_wallet_id = twp.id
    LEFT JOIN t_wallet twb ON occ.company_wallet_id = twb.id
    LEFT JOIN t_wallet twc ON occ.end_driver_wallet_id = twc.id
    LEFT JOIN t_carrier_enduser_company_rel pcecr ON twp.carrier_enduser_company_id = pcecr.id and pcecr.datasouce = 'CA'
    LEFT JOIN t_carrier_enduser_company_rel bcecr ON twb.carrier_enduser_company_id = bcecr.id and bcecr.datasouce = 'BD'
    LEFT JOIN t_carrier_enduser_company_rel ccecr ON twc.carrier_enduser_company_id = ccecr.id and ccecr.datasouce = 'CD'
    LEFT JOIN t_order_pay_detail opd ON occ.`code` = opd.order_cast_change_code AND occ.user_oper = 'TX' AND opd.trade_status = 'TRADE_FINISHED'
    LEFT JOIN t_order_pay_detail opd1 ON occ.`code` = opd1.order_cast_change_code AND occ.user_oper = 'ZHPAYNODE' AND opd1.trade_status = 'TRADE_FINISHED' and opd1.enable=0
    LEFT JOIN t_order_pay_detail opd2 ON occ.`code` = opd2.order_cast_change_code AND occ.user_oper = 'XHPAYNODE' AND opd2.trade_status = 'TRADE_FINISHED' and opd2.enable=0
    LEFT JOIN t_order_pay_detail opd3 ON occ.`code` = opd3.order_cast_change_code AND occ.user_oper = 'SDPAYNODE' AND opd3.trade_status = 'TRADE_FINISHED' and opd3.enable=0
    LEFT JOIN t_order_pay_detail opd4 ON occ.`code` = opd4.order_cast_change_code AND occ.user_oper = 'WKPAYNODE' AND opd4.trade_status = 'TRADE_FINISHED' and opd4.enable=0
    LEFT JOIN t_order_pay_detail opda ON opda.id IN (
    SELECT
    opdb.id
    FROM t_order_pay_detail opdb
    WHERE occ.user_oper = 'PayMent' AND occ.`code` = opdb.order_cast_change_code AND opda.trade_status = 'TRADE_FINISHED'
    )
    LEFT JOIN t_dic_cat_item dcis ON opda.operate_state = dcis.item_code
    left join t_order_info toi on occ.order_code = toi.code
    LEFT JOIN t_carrier_enduser_company_rel acecr ON toi.agent_id = acecr.enduser_company_id and acecr.datasouce = 'CD'
    and acecr.`enable` = 0 and acecr.carrier_id = toi.carrier_id
    LEFT JOIN t_carrier_enduser_company_rel ocecr ON toi.end_car_owner_id = ocecr.enduser_company_id and ocecr.datasouce = 'CD'
    and ocecr.`enable` = 0 and ocecr.carrier_id = toi.carrier_id
    WHERE occ.order_code = #{code}
    ORDER BY createTimeStr ASC
  </select>
  <!--PC端 运单管理: 获取打包订单网商子账户资金变动过程 hwt-->
  <select id="pcSelectPackAccountFundChanges" parameterType="java.lang.String" resultType="com.lz.dto.OrderFundChangesDTO">
    SELECT
    case dci.item_code when 'TX' then 'DBTX' else dci.item_code end itemCode, #<!-- 交易类型CODE -->
    dci.item_value, #<!-- 交易类型-->
    case when occ.user_oper='TX' then opd.return_time
    ELSE ifnull( opda.return_time, occ.create_time) end createTimeStr,#<!--交易时间:字符串-->
    ifnull(oi.share_payment_amount, 0) AS carriageFee, #<!--运费-->
    ifnull(oi.share_dispatch_fee, 0) AS dispatchFee, #<!--调度费-->
    ifnull(oi.share_payment_amount, 0) + ifnull(oi.share_dispatch_fee, 0) AS totalFee, #<!--总费用-->
    opda.trade_type AS payCode, #<!-- 支付状态CODE -->
    case opda.trade_type when 'YFRZ' then '运费' when 'DDFRZ' then '调度费' end AS payState, #<!-- 支付状态 -->
    bcecr.thrid_pary_sub_account as BParySubAccount, #<!--B端网商子账户-->
    pcecr.thrid_pary_sub_account as PParySubAccount, #<!--P端网商子账户-->
    ccecr.thrid_pary_sub_account as CParySubAccount, #<!--C端网商子账户-->
    opd.bank_no
    FROM t_order_cast_changes occ
    INNER JOIN t_order_pack_detail topd ON topd.pack_pay_code = occ.order_code and topd.`enable` = 1
    INNER JOIN t_order_info oi ON oi.code = topd.order_code
    LEFT JOIN t_dic_cat_item dci ON dci.item_code = occ.user_oper
    LEFT JOIN t_wallet twp ON occ.carrier_wallet_id = twp.id
    LEFT JOIN t_wallet twb ON occ.company_wallet_id = twb.id
    LEFT JOIN t_wallet twc ON occ.end_driver_wallet_id = twc.id
    LEFT JOIN t_carrier_enduser_company_rel pcecr ON twp.carrier_enduser_company_id = pcecr.id
    LEFT JOIN t_carrier_enduser_company_rel bcecr ON twb.carrier_enduser_company_id = bcecr.id
    LEFT JOIN t_carrier_enduser_company_rel ccecr ON twc.carrier_enduser_company_id = ccecr.id
    LEFT JOIN t_order_pay_detail opd ON occ.`code` = opd.order_cast_change_code AND occ.user_oper = 'TX' AND opd.trade_status = 'TRADE_FINISHED'
    LEFT JOIN t_order_pay_detail opda ON opda.id IN (
    SELECT
    opdb.id
    FROM t_order_pay_detail opdb
    WHERE occ.user_oper = 'DBPayMent' AND occ.`code` = opdb.order_cast_change_code AND opda.trade_status = 'TRADE_FINISHED'
    )
    LEFT JOIN t_dic_cat_item dcis ON opda.operate_state = dcis.item_code
    WHERE oi.code = #{code}
    UNION
    SELECT
    dci.item_code,
    dci.item_value,
    occ.create_time,
    occ.carriage_fee, occ.dispatch_fee, occ.total_fee, '' as payCode, '' as payState,
    bcecr.thrid_pary_sub_account AS BParySubAccount,
    pcecr.thrid_pary_sub_account AS PParySubAccount,
    ccecr.thrid_pary_sub_account AS CParySubAccount,
    '' as bank_no
    FROM
    t_order_cast_changes occ
    LEFT JOIN t_order_info oi ON oi. CODE = occ.order_code
    LEFT JOIN t_dic_cat_item dci ON dci.item_code = occ.user_oper
    LEFT JOIN t_wallet twp ON occ.carrier_wallet_id = twp.id
    LEFT JOIN t_wallet twb ON occ.company_wallet_id = twb.id
    LEFT JOIN t_wallet twc ON occ.end_driver_wallet_id = twc.id
    LEFT JOIN t_carrier_enduser_company_rel pcecr ON twp.carrier_enduser_company_id = pcecr.id
    LEFT JOIN t_carrier_enduser_company_rel bcecr ON twb.carrier_enduser_company_id = bcecr.id
    LEFT JOIN t_carrier_enduser_company_rel ccecr ON twc.carrier_enduser_company_id = ccecr.id
    WHERE
    oi. CODE = #{code}
    ORDER BY createTimeStr ASC
  </select>

  <update id="doInvalidByPackage" parameterType="java.lang.String">
    update t_order_cast_changes
    set
      data_enable = false
    where order_code in
          (select order_code from t_order_pack_detail where pack_pay_code = #{packageCode,jdbcType=VARCHAR})
  </update>
  <!--根据资金变动 Code 获取资金转移方式和C端用户Id Yan-->
  <select id="getCapitalTransferAndEndUserId" parameterType="java.lang.String" resultType="com.lz.dto.TOrderInfoAndTOrderPayInfo">
    SELECT
    occ.capital_transfer_type, <!-- 资金转移方式 -->
    occ.carriage_fee userConfirmCarriagePayment, <!-- 用户确认的应付运费金额 -->
    occ.service_fee,
    toi.order_business_code, <!-- 运单编号 -->
    toi.deliver_order_time, <!-- 发单时间 -->
    toi.end_driver_id, <!-- 司机ID -->
    toi.end_car_owner_id, <!-- 车老板ID -->
    toi.end_agent_id <!-- 经纪人ID -->
    FROM t_order_cast_changes occ
    LEFT JOIN t_order_info toi ON occ.order_code = toi.`code`
    WHERE occ.`code` = #{code} AND occ.data_enable = 1
    limit 1
  </select>

  <select id="selectByOrderCode" resultMap="BaseResultMap">
    select
      *
    from
      t_order_cast_changes
    where
      order_code =#{orderCode,jdbcType=VARCHAR}
      and `enable`=false
      and  trade_type='TX'
  </select>


  <!--PC端 运单管理: 获取订单网商子账户资金变动过程 Yan-->
  <select id="pcNewSelectPackAccountFundChanges" parameterType="java.lang.String" resultType="com.lz.dto.OrderFundChangesDTO">
    SELECT
    dci.item_code, #<!-- 交易类型CODE -->
    dci.item_value, #<!-- 交易类型-->
    occ.create_time, #<!--交易时间-->
    case when occ.user_oper='TX' then opd.return_time
    ELSE ifnull( opda.return_time, occ.create_time) end createTimeStr,#<!--交易时间:字符串-->
    occ.carriage_fee, #<!--运费-->
    occ.dispatch_fee, #<!--调度费-->
    ifnull(occ.service_fee, 0) serviceFee,
    occ.total_fee, #<!--总费用-->
    opda.trade_type AS payCode, #<!-- 支付状态CODE -->
    case opda.trade_type
    when 'YFRZ' then '运费'
    when 'DDFRZ' then '调度费'
    when 'FWFRZ' then '服务费'
    when 'OYFRZ' then '运费'
    end AS payState, #<!-- 支付状态 -->
    bcecr.thrid_pary_sub_account as BParySubAccount, #<!--B端网商子账户-->
    pcecr.thrid_pary_sub_account as PParySubAccount, #<!--P端网商子账户-->
    ccecr.thrid_pary_sub_account as CParySubAccount, #<!--C端网商子账户-->
    acecr.thrid_pary_sub_account as AParySubAccount, #<!--经纪人网商子账户-->
    ocecr.thrid_pary_sub_account as OParySubAccount, #<!--车主网商子账户-->
    opd.bank_no
    FROM t_order_cast_changes occ
    LEFT JOIN t_dic_cat_item dci ON dci.item_code = occ.user_oper
    LEFT JOIN t_wallet twp ON occ.carrier_wallet_id = twp.id
    LEFT JOIN t_wallet twb ON occ.company_wallet_id = twb.id
    LEFT JOIN t_wallet twc ON occ.end_driver_wallet_id = twc.id
    LEFT JOIN t_carrier_enduser_company_rel pcecr ON twp.carrier_enduser_company_id = pcecr.id and pcecr.datasouce = 'CA'
    LEFT JOIN t_carrier_enduser_company_rel bcecr ON twb.carrier_enduser_company_id = bcecr.id and bcecr.datasouce = 'BD'
    LEFT JOIN t_carrier_enduser_company_rel ccecr ON twc.carrier_enduser_company_id = ccecr.id and ccecr.datasouce = 'CD'
    left join t_order_pack_detail topd on occ.order_code = topd.order_code and topd.`enable` = 1
    left join t_order_pack_info topi on topd.pack_pay_code = topi.code
    left join t_order_pay_info topi2 on topi.code = topi2.order_code
    LEFT JOIN t_order_pay_detail opd ON topi2.code = opd.order_pay_code AND opd.trade_type = 'DBTX' AND opd.trade_status = 'TRADE_FINISHED'
    LEFT JOIN t_order_pay_detail opda ON opda.id IN (
    SELECT
    opdb.id
    FROM t_order_pay_detail opdb
    WHERE occ.user_oper = 'PayMent' AND occ.`code` = opdb.order_cast_change_code AND opda.trade_status = 'TRADE_FINISHED'
    )
    LEFT JOIN t_dic_cat_item dcis ON opda.operate_state = dcis.item_code
    left join t_order_info toi on occ.order_code = toi.code
    LEFT JOIN t_carrier_enduser_company_rel acecr ON toi.agent_id = acecr.enduser_company_id and acecr.datasouce = 'CD'
    and acecr.`enable` = 0 and acecr.carrier_id = toi.carrier_id
    LEFT JOIN t_carrier_enduser_company_rel ocecr ON toi.end_car_owner_id = ocecr.enduser_company_id and ocecr.datasouce = 'CD'
    and ocecr.`enable` = 0 and ocecr.carrier_id = toi.carrier_id
    WHERE occ.order_code = #{code}
    UNION
    SELECT
    'TX' item_code,
    '提现' item_value,
    opd.operate_time createTime,
    opd.return_time createTimeStr,
    occ.carriage_fee,
    occ.dispatch_fee,
    ifnull( occ.service_fee, 0 ) serviceFee,
    occ.total_fee,
    '' AS payCode,
    '' AS payState,
    '' AS BParySubAccount,
    '' AS PParySubAccount,
    ccecr.thrid_pary_sub_account AS CParySubAccount,
    '' AS AParySubAccount,-- <!--经纪人网商子账户-->
    '' AS OParySubAccount,-- <!--车主网商子账户-->
    opd.bank_no AS bank_no
    FROM
    t_order_cast_changes occ
    LEFT JOIN t_wallet twc ON occ.end_driver_wallet_id = twc.id
    LEFT JOIN t_carrier_enduser_company_rel ccecr ON twc.carrier_enduser_company_id = ccecr.id
    AND ccecr.datasouce = 'CD'
    LEFT JOIN t_order_pack_detail topd ON occ.order_code = topd.order_code
    LEFT JOIN t_order_pack_info topi ON topd.pack_pay_code = topi.
    CODE LEFT JOIN t_order_pay_info topi2 ON topi.CODE = topi2.order_code
    LEFT JOIN t_order_pay_detail opd ON topi2.CODE = opd.order_pay_code
    AND opd.trade_type = 'DBTX'
    AND opd.trade_status = 'TRADE_FINISHED'
    WHERE
    topd.order_code = #{code}
    AND occ.data_enable = 1
    ORDER BY createTimeStr ASC
  </select>

  <select id="jdPayFundChanges" resultType="com.lz.dto.OrderFundChangesDTO">
    SELECT
      occ.order_code,
      dci.item_code,
      dci.item_value,
      occ.create_time,
      CASE WHEN occ.user_oper = 'TX' THEN
             opd.return_time
           ELSE
             ifnull(opda.return_time, occ.create_time)
        END createTimeStr,
      occ.carriage_fee,
      occ.dispatch_fee,
      ifnull(occ.service_fee, 0) serviceFee,
      occ.total_fee,
      opda.trade_type AS payCode,
      CASE opda.trade_type
        WHEN 'COMBALANCE_PAY' THEN
          '运费 + 调度费'
        WHEN 'CABALANCE_PAY' THEN
          '运费'
        WHEN 'CDBALANCE_PAY' THEN
          '运费'
        WHEN 'SPLIT_FALL' THEN
          '分账'
        END AS payState,
      tcompany.partner_acc_id AS BParySubAccount,
      tcarrier.partner_acc_id AS PParySubAccount,
      tdriver.partner_acc_id AS CParySubAccount,
      tagent.partner_acc_id AS AParySubAccount,
      tcaptain.partner_acc_id AS OParySubAccount,
      opd.bank_no,
      occ.trade_type,
      opd1.bank_no AS zhTxBankNo,
      opd2.bank_no AS xhTxBankNo,
      opd3.bank_no AS sdTxBankNo,
      opd4.bank_no AS wkTxBankNo
    FROM
      t_order_cast_changes occ
        LEFT JOIN t_order_info toi ON occ.order_code = toi.CODE
        LEFT JOIN t_carrier_company_open_role tcompany ON toi.company_id = tcompany.carrier_company_id
        AND tcompany.open_status = 'openSuccess' and tcompany.user_open_role ='BD'
        LEFT JOIN t_carrier_company_open_role tcarrier ON toi.carrier_id = tcarrier.carrier_company_id
        AND tcarrier.open_status = 'openSuccess' and tcarrier.user_open_role = 'CA'
        LEFT JOIN t_end_user_open_role tdriver ON toi.end_driver_id = tdriver.end_user_id
        AND tdriver.open_status = 'openSuccess'
        LEFT JOIN t_end_user_open_role tcaptain ON toi.end_car_owner_id = tcaptain.end_user_id
        AND tcaptain.open_status = 'openSuccess'
        LEFT JOIN t_end_user_open_role tagent ON toi.agent_id = tagent.end_user_id
        AND tagent.open_status = 'openSuccess'
        LEFT JOIN t_dic_cat_item dci ON dci.item_code = occ.user_oper
        LEFT JOIN t_order_pay_detail opd ON occ. `code` = opd.order_cast_change_code
        AND occ.user_oper = 'TX'
        AND opd.trade_status = 'TRADE_FINISHED'
        LEFT JOIN t_order_pay_detail opd1 ON occ. `code` = opd1.order_cast_change_code
        AND occ.user_oper = 'ZHPAYNODE'
        AND opd1.trade_status = 'TRADE_FINISHED'
        AND opd1.ENABLE = 0
        LEFT JOIN t_order_pay_detail opd2 ON occ. `code` = opd2.order_cast_change_code
        AND occ.user_oper = 'XHPAYNODE'
        AND opd2.trade_status = 'TRADE_FINISHED'
        AND opd2.ENABLE = 0
        LEFT JOIN t_order_pay_detail opd3 ON occ. `code` = opd3.order_cast_change_code
        AND occ.user_oper = 'SDPAYNODE'
        AND opd3.trade_status = 'TRADE_FINISHED'
        AND opd3.ENABLE = 0
        LEFT JOIN t_order_pay_detail opd4 ON occ. `code` = opd4.order_cast_change_code
        AND occ.user_oper = 'WKPAYNODE'
        AND opd4.trade_status = 'TRADE_FINISHED'
        AND opd4.ENABLE = 0
        LEFT JOIN t_order_pay_detail opda ON opda.id IN(
        SELECT
          opdb.id FROM t_order_pay_detail opdb
        WHERE
          occ.user_oper = 'PayMent'
          AND occ. `code` = opdb.order_cast_change_code
          AND opda.trade_status = 'TRADE_FINISHED')
        LEFT JOIN t_dic_cat_item dcis ON opda.operate_state = dcis.item_code
    WHERE
      occ.order_code = #{code}
    ORDER BY
      createTimeStr ASC
  </select>

  <select id="hxPayFundChanges" resultType="com.lz.dto.OrderFundChangesDTO">
    SELECT
      occ.order_code,
      dci.item_code,
      dci.item_value,
      occ.create_time,
      CASE WHEN occ.user_oper = 'TX' THEN
             opd.return_time
           ELSE
             ifnull(opda.return_time, occ.create_time)
        END createTimeStr,
      occ.carriage_fee,
      occ.dispatch_fee,
      ifnull(occ.service_fee, 0) serviceFee,
      occ.total_fee,
      opda.trade_type AS payCode,
      CASE opda.trade_type
        WHEN 'COMBALANCE_PAY' THEN
          '运费 + 调度费'
        WHEN 'CABALANCE_PAY' THEN
          '运费'
        WHEN 'CDBALANCE_PAY' THEN
          '运费'
        WHEN 'SPLIT_FALL' THEN
          '分账'
        END AS payState,
      tzaoi.partner_acc_id AS BParySubAccount,
      tzaoi2.partner_acc_id AS PParySubAccount,
      tzaoi3.partner_acc_id AS CParySubAccount,
      tzaoi5.partner_acc_id AS AParySubAccount,
      tzaoi4.partner_acc_id AS OParySubAccount,
      opd.bank_no,
      occ.trade_type,
      opd1.bank_no AS zhTxBankNo,
      opd2.bank_no AS xhTxBankNo,
      opd3.bank_no AS sdTxBankNo,
      opd4.bank_no AS wkTxBankNo,
      if(oi.insure = 1, oi.insured_amount, 0) AS insuredAmount
    FROM
      t_order_cast_changes occ
        LEFT JOIN t_order_info toi ON occ.order_code = toi.CODE
        LEFT JOIN t_carrie_account tca2 ON tca2.carrier_id = toi.carrier_id
        LEFT JOIN t_account ta2 ON tca2.account_id = ta2.id
        LEFT JOIN t_zt_account_open_info tzaoi2 ON ta2.id = tzaoi2.account_id and tzaoi2.channel_id = 'HXBANK'
        LEFT JOIN t_company_account tca ON toi.company_id = tca.company_id
        LEFT JOIN t_account ta ON tca.account_id = ta.id
        LEFT JOIN t_zt_account_open_info tzaoi ON ta.id = tzaoi.account_id and tzaoi.channel_id = 'HXBANK'
        LEFT JOIN t_enduser_account tea ON toi.end_driver_id = tea.enduser_id
        LEFT JOIN t_zt_account_open_info tzaoi3 ON tea.account_id = tzaoi3.account_id and tzaoi3.channel_id = 'HXBANK'
        LEFT JOIN t_enduser_account tea2 ON toi.end_car_owner_id = tea2.enduser_id
        LEFT JOIN t_zt_account_open_info tzaoi4 ON tea2.account_id = tzaoi4.account_id and tzaoi4.channel_id = 'HXBANK'
        LEFT JOIN t_enduser_account tea3 ON toi.agent_id = tea3.enduser_id
        LEFT JOIN t_zt_account_open_info tzaoi5 ON tea3.account_id = tzaoi5.account_id and tzaoi5.channel_id = 'HXBANK'
        LEFT JOIN t_dic_cat_item dci ON dci.item_code = occ.user_oper
        LEFT JOIN t_order_pay_detail opd ON occ. `code` = opd.order_cast_change_code
        AND occ.user_oper = 'TX'
        AND opd.trade_status = 'TRADE_FINISHED'
        LEFT JOIN t_order_pay_detail opd1 ON occ. `code` = opd1.order_cast_change_code
        AND occ.user_oper = 'ZHPAYNODE'
        AND opd1.trade_status = 'TRADE_FINISHED'
        AND opd1.ENABLE = 0
        LEFT JOIN t_order_pay_detail opd2 ON occ. `code` = opd2.order_cast_change_code
        AND occ.user_oper = 'XHPAYNODE'
        AND opd2.trade_status = 'TRADE_FINISHED'
        AND opd2.ENABLE = 0
        LEFT JOIN t_order_pay_detail opd3 ON occ. `code` = opd3.order_cast_change_code
        AND occ.user_oper = 'SDPAYNODE'
        AND opd3.trade_status = 'TRADE_FINISHED'
        AND opd3.ENABLE = 0
        LEFT JOIN t_order_pay_detail opd4 ON occ. `code` = opd4.order_cast_change_code
        AND occ.user_oper = 'WKPAYNODE'
        AND opd4.trade_status = 'TRADE_FINISHED'
        AND opd4.ENABLE = 0
        LEFT JOIN t_order_pay_detail opda ON opda.id IN(
        SELECT
          opdb.id FROM t_order_pay_detail opdb
        WHERE
          occ.user_oper = 'PayMent'
          AND occ. `code` = opdb.order_cast_change_code
          AND opda.trade_status = 'TRADE_FINISHED')
        LEFT JOIN t_dic_cat_item dcis ON opda.operate_state = dcis.item_code
        left join t_order_insurance oi on oi.order_business_code = toi.order_business_code
    WHERE
      occ.order_code = #{code} AND ta.if_main_account = 1
      AND ta2.if_main_account = 1
    ORDER BY
      createTimeStr, opda.id ASC
  </select>

  <select id="jdPackPayFundChanges" resultType="com.lz.dto.OrderFundChangesDTO">
    SELECT
      dci.item_code,
      dci.item_value,
      occ.create_time,
      CASE
        WHEN occ.user_oper = 'TX' THEN
          opd.return_time ELSE ifnull( opda.return_time, occ.create_time )
        END createTimeStr,
      occ.carriage_fee,
      occ.dispatch_fee,
      ifnull( occ.service_fee, 0 ) serviceFee,
      occ.total_fee,
      opda.trade_type AS payCode,
      CASE
        opda.trade_type
        WHEN 'COMBALANCE_PAY' THEN
          '运费 + 调度费'
        WHEN 'CABALANCE_PAY' THEN
          '运费'
        WHEN 'CDBALANCE_PAY' THEN
          '运费'
        WHEN 'SPLIT_FALL' THEN
          '分账'
        END AS payState,
      tcompany.partner_acc_id AS BParySubAccount,
      tcarrier.partner_acc_id AS PParySubAccount,
      tdriver.partner_acc_id AS CParySubAccount,
      tagent.partner_acc_id AS AParySubAccount,
      tcaptain.partner_acc_id AS OParySubAccount,
      opd.bank_no,
      occ.trade_type
    FROM
      t_order_cast_changes occ
        LEFT JOIN t_order_info toi ON occ.order_code = toi.CODE
        LEFT JOIN t_carrier_company_open_role tcompany ON toi.company_id = tcompany.carrier_company_id
        AND tcompany.open_status = 'openSuccess'
        AND tcompany.user_open_role = 'BD'
        LEFT JOIN t_carrier_company_open_role tcarrier ON toi.carrier_id = tcarrier.carrier_company_id
        AND tcarrier.open_status = 'openSuccess'
        AND tcarrier.user_open_role = 'CA'
        LEFT JOIN t_end_user_open_role tdriver ON toi.end_driver_id = tdriver.end_user_id
        AND tdriver.open_status = 'openSuccess'
        LEFT JOIN t_end_user_open_role tcaptain ON toi.end_car_owner_id = tcaptain.end_user_id
        AND tcaptain.open_status = 'openSuccess'
        LEFT JOIN t_end_user_open_role tagent ON toi.agent_id = tagent.end_user_id
        AND tagent.open_status = 'openSuccess'
        LEFT JOIN t_dic_cat_item dci ON dci.item_code = occ.user_oper
        LEFT JOIN t_order_pay_detail opd ON occ.`code` = opd.order_cast_change_code
        AND occ.user_oper = 'TX'
        AND opd.trade_status = 'TRADE_FINISHED'
        LEFT JOIN t_order_pay_detail opda ON opda.id IN ( SELECT opdb.id FROM t_order_pay_detail opdb WHERE occ.user_oper = 'PayMent' AND occ.`code` = opdb.order_cast_change_code AND opda.trade_status = 'TRADE_FINISHED' )
        LEFT JOIN t_dic_cat_item dcis ON opda.operate_state = dcis.item_code
    WHERE
      occ.order_code = #{orderCode}
    ORDER BY
      createTimeStr ASC
  </select>

  <delete id="deleteByOrderCode">
    delete from t_order_cast_changes where order_code = #{orderCode}
  </delete>

</mapper>