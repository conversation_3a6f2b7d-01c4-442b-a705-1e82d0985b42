<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TOrderZtAccountOpenInfoMapper">

  <select id="selectCompanyOpenInfo" resultType="com.lz.model.TZtAccountOpenInfo">
    select tozaoi.*
    from
      t_zt_account_open_info tozaoi
    left join t_company_info tci on tozaoi.account_id = tci.account_info_id
    where
      tci.id = #{id} and tozaoi.channel_id = 'HXBANK'
  </select>

  <select id="selectCarrierOpenInfo" resultType="com.lz.model.TZtAccountOpenInfo">
    select tozaoi.*
    from
      t_zt_account_open_info tozaoi
      left join t_carrie_account tca on tozaoi.account_id = tca.account_id
      left join t_carrier_info tci on tca.carrier_id = tci.id
    where
      tci.id = #{id} and tozaoi.channel_id = 'HXBANK'
  </select>

  <select id="selectEndUserOpenInfo" resultType="com.lz.model.TZtAccountOpenInfo">
    select tozaoi.*
    from
        t_zt_account_open_info tozaoi
        left join t_enduser_account tea on tozaoi.account_id = tea.account_id
    where
      tea.enduser_id = #{id} and tozaoi.channel_id = 'HXBANK'
  </select>

  <select id="selectEndDriverOpenOrder" resultType="java.lang.Integer">
    SELECT
        toi.id
    FROM t_order_info toi
    LEFT JOIN t_end_user_info teui ON toi.end_driver_id = teui.id
    LEFT JOIN t_enduser_account tea ON teui.id = tea.enduser_id
    LEFT JOIN t_zt_account_open_info tozaoi ON tea.account_id = tozaoi.account_id
    WHERE toi.id IN
    <foreach collection="ids" item="id" open="(" separator="," close=")">
          #{id}
    </foreach>
    AND tozaoi.status = 1
    AND tozaoi.channel_id = 'HXBANK'
  </select>

  <select id="selectCaptionNotOpenOrder" resultType="java.lang.Integer">
    SELECT
    toi.id
    FROM t_order_info toi
    LEFT JOIN t_end_user_info teui ON toi.end_car_owner_id = teui.id
    LEFT JOIN t_enduser_account tea ON teui.id = tea.enduser_id
    LEFT JOIN t_zt_account_open_info tozaoi ON tea.account_id = tozaoi.account_id
    WHERE toi.end_car_owner_id is not null and toi.id IN
    <foreach collection="ids" item="id" open="(" separator="," close=")">
          #{id}
    </foreach>
    AND (tozaoi.id is null or tozaoi.status IS NULL or (tozaoi.status != 1 and tozaoi.channel_id = 'HXBANK'))

  </select>

  <select id="selectEndDriverWalletOrder" resultType="java.lang.Integer">
    SELECT
    toi.id
    FROM t_order_info toi
    LEFT JOIN t_end_user_info teui ON toi.end_driver_id = teui.id
    LEFT JOIN t_enduser_account tea ON teui.id = tea.enduser_id
    LEFT JOIN t_zt_account_open_info tozaoi ON tea.account_id = tozaoi.account_id
    left join t_zt_wallet tzw on tozaoi.id = tzw.zt_account_open_id
    WHERE toi.id IN
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
    AND tozaoi.status = 1
    AND tozaoi.channel_id = 'HXBANK'
    and tzw.id is not null
  </select>

  <select id="selectCaptionNoWalletOrder" resultType="java.lang.Integer">
    SELECT
    toi.id
    FROM t_order_info toi
    LEFT JOIN t_end_user_info teui ON toi.end_car_owner_id = teui.id
    LEFT JOIN t_enduser_account tea ON teui.id = tea.enduser_id
    LEFT JOIN t_zt_account_open_info tozaoi ON tea.account_id = tozaoi.account_id
    left join t_zt_wallet tzw on tozaoi.id = tzw.zt_account_open_id
    WHERE toi.end_car_owner_id is not null and toi.id IN
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
    AND (tozaoi.id is null or tozaoi.status IS NULL or (tozaoi.status != 1 AND tozaoi.channel_id = 'HXBANK') or tzw.id is null)
  </select>

</mapper>