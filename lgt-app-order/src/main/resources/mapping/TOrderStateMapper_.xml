<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TOrderStateMapper">
  <!--只查询运单状态CODE -->
  <select id="selectOrderState" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT
      case
          when os.state_node_value = 'SP0706' then 'M030'
          when os.state_node_value = 'SP0707' then 'M030'
          when os.state_node_value = 'SP0708' then 'M040'
          when os.state_node_value = 'SP0709' then 'M040'
          when os.state_node_value = 'SP0710' then 'M050'
          when os.state_node_value = 'SP0711' then 'M050'
          else osn.code
      end stateNodeCode
    FROM
        t_order_state_node_detail osnd
        LEFT JOIN t_order_state_node osn ON osnd.node_id = osn.id
        LEFT JOIN t_order_state os ON state_node_value = osnd.status_code
    WHERE
      os.order_code = #{code}
    ORDER BY os.id DESC
    LIMIT 1
  </select>
    <!-- PC端运单详情：运输轨迹 Yan -->
    <select id="getDriverOperatePositionTime" parameterType="java.lang.String" resultType="hashmap">
        SELECT
            tos.operate_time as operateTime, <!-- 操作时间 -->
            tos.operate_geography_position as operateGeographyPosition, <!-- 操作地理位置 -->
            tos.state_node_value as stateNodeValue<!-- 节点状态 -->
        FROM t_order_state tos
        WHERE tos.order_code = #{code} AND tos.state_node_value IN ('S0302', 'S0402')
    </select>

  <!--App 运单详情 查询运单进度 Yan-->
  <select id="appOrderProcess" resultType="com.lz.dto.AppOrderProcessDTO">
    SELECT
    *
    FROM (
        SELECT DISTINCT
            tos.operate_time,
            osn.`code` as stateNodeValue,
            osn.page_show_code as stateValue,
            tos.state_node_value stateNode,
            tsu.nickname create_user,
            IFNULL(ta.account_no, tea.phone) AS phone
        FROM
            t_order_state tos
        LEFT JOIN t_order_state_node_detail osnd ON tos.state_node_value = osnd.status_code
        LEFT JOIN t_order_state_node osn ON osnd.node_id = osn.id
        LEFT JOIN t_account ta ON ta.user_id = tos.operator_id
        LEFT JOIN t_enduser_account tea ON ta.id = tea.account_id
        left join t_sys_user tsu on tsu.id = tos.operator_id
        WHERE
            tos.order_code = #{code} AND tos.state_node_value is not null
        <if test="isPc">
            ORDER BY tos.operate_time ASC
        </if>
        <!-- 这里不等于是因为召回流程中出现重复了，只取最后一个召回完成的 S0953 -->
        <if test="!isPc">
            AND osnd.status_code NOT IN ('S0952', "S0951")
        </if>
        <if test="!isPc">
            ORDER BY tos.operate_time DESC
        </if>
    ) ase
      <if test="isPc">
          WHERE ase.stateNode NOT IN ('S0301', 'S0303', 'S0304', 'S0401', 'S0403',  'S0405', 'SM0601', 'SM0602', 'SM0603',    'SP0706','SP0707','SP0708','SP0709','SP0710','SP0711','SP1104','SP1105','SP1106','SP1104','S1303','S1304','S1305')
      </if>
      <if test="!isPc">
          WHERE ase.stateNode NOT IN ('S0301', 'S0303', 'S0304', 'S0401', 'S0403', 'S0405',    'SP0706','SP0707','SP0708','SP0709','SP0710','SP0711','SP1104','SP1105','SP1106','SP1104','S1303','S1304','S1305')
      </if>
  </select>

  <!--根据业务ID 和状态 CODE 获取司机装货签到地点时间-->
  <select id="selectOrderSignIn" parameterType="java.lang.String" resultType="com.lz.dto.OrderSignInDTO">
    SELECT
        tos.operate_time,
        tos.operate_geography_position
    FROM
        t_order_state tos
    WHERE
        tos.order_code = #{code}
    AND tos.state_node_value = #{state}
  </select>

    <insert id="batchInsert"  parameterType="com.lz.model.TOrderState" >
        insert into t_order_state (code, order_code, operator_id,
        operate_method, operate_time, operator_ip, operate_geography_position, state_node_value,
        if_expire, create_user, create_time, update_user, update_time, `enable`, remark
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.code,jdbcType=VARCHAR}, #{item.orderCode,jdbcType=VARCHAR}, #{item.operatorId,jdbcType=INTEGER},
            #{item.operateMethod,jdbcType=VARCHAR}, #{item.operateTime,jdbcType=TIMESTAMP}, #{item.operatorIp,jdbcType=VARCHAR},
            #{item.operateGeographyPosition,jdbcType=VARCHAR}, #{item.stateNodeValue,jdbcType=VARCHAR},
            #{item.ifExpire,jdbcType=BIT}, #{item.createUser,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateUser,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.enable,jdbcType=BIT}, #{item.remark,jdbcType=VARCHAR}
            )
        </foreach>

    </insert>
    <!--运单管理-收单：根据32业务ID,查询运单执行标志 Yan-->
    <select id="selectOrderSign" parameterType="java.lang.String" resultType="hashmap">
        SELECT
            *
        FROM (
            SELECT
                osnd.page_show_code AS codea
            FROM t_order_state os
            LEFT JOIN t_order_state_node_detail osnd ON os.state_node_value = osnd.status_code
            WHERE os.order_code = #{code}
            ORDER BY os.operate_time DESC
            LIMIT 1
        ) a, (
            SELECT
            osn.page_show_code as codeb
            FROM t_order_info toi
            LEFT JOIN t_order_state_node osn ON toi.order_pay_status = osn.`code`
            WHERE  toi.`code` = #{code}
        ) b
    </select>
    <!--运单管理-付款：根据32业务ID,查询运单执行标志-->
    <select id="selectOrderSignForPay" parameterType="java.lang.String" resultType="hashmap">
        SELECT
            *
        FROM (
                 SELECT
                     osnd.page_show_code AS codea
                 FROM t_order_state os
                          LEFT JOIN t_order_state_node_detail osnd ON os.state_node_value = osnd.status_code
                 WHERE os.order_code = #{code}
                 ORDER BY os.operate_time DESC
                 LIMIT 1
             ) a, (
                 SELECT
                     osn.page_show_code as codeb
                 FROM t_order_info toi
                          LEFT JOIN t_order_state_node osn ON toi.order_execute_status = osn.`code`
                 WHERE  toi.`code` = #{code}
             ) b
    </select>


    <!--运单管理高级搜索-下拉框：执行状态 Yan-->
    <select id="getOrderManagerExecuteSelect" resultType="hashmap">
        SELECT
        osn.`code`, <!-- 运单状态CODE -->
        osn.page_show_code <!-- 页面展示 -->
        FROM t_order_state_node osn
        WHERE osn.system_code = '运单管理'
    </select>

    <!--运单管理高级搜索-下拉框：支付状态 Yan-->
    <select id="getOrderManagerPaySelect" resultType="hashmap">
        SELECT
            osn.`code`, <!-- 运单状态CODE -->
            osn.page_show_code <!-- 页面展示 -->
        FROM t_order_state_node osn
        WHERE osn.system_code IN ('支付管理', '提现管理')
    </select>

    <!-- 获取运单 司机装货签到，卸货签到 位置 Yan-->
    <select id="getDriverSignInPosition" parameterType="java.lang.String" resultType="hashmap">
        SELECT
            tos.operate_geography_position as operateGeographyPosition,
            tos.state_node_value as stateNodeValue,
            tos.operate_time as operateTime,
            tos.remark
        FROM
            t_order_state tos
        WHERE
            tos.order_code = #{code}
        AND tos.state_node_value IN ('S0302', 'S0402') and tos.enable = 0
    </select>

    <select id="selectOrderStateByOrderCode" resultType="com.lz.model.TOrderState">
        select
        id, code, order_code, operator_id, operate_method, operate_time, operator_ip,
        operate_geography_position, attachment, state_node_value, state_expire_time, if_expire,
        remark, param1, param2, param3, param4, create_user, create_time, update_user, update_time,
        `enable`
        from t_order_state
        where order_code = #{orderCode} and state_node_value in
        <foreach collection="states" index="index" item="state" open="(" close=")" separator=",">
            #{state}
        </foreach>
        order by create_time desc
        limit 1
    </select>

    <select id="selectNewOrderState" parameterType="java.lang.String" resultType="com.lz.model.TOrderState">
        select
         id, code, order_code, operator_id, operate_method, operate_time, operator_ip,
        operate_geography_position, attachment, state_node_value, state_expire_time, if_expire,
        remark, param1, param2, param3, param4, create_user, create_time, update_user, update_time,
        `enable`
        from t_order_state
        where order_code = #{orderCode}
        order by operate_time desc
        limit 1
    </select>
    <!-- 判断运单状态是否存在 -->
    <select id="judgeOrderStateExists" resultType="java.lang.Integer">
        SELECT
            tos.id
        FROM
            t_order_state tos
        WHERE
            tos.order_code = #{orderCode}
        AND tos.state_node_value = #{orderState}
    </select>

    <select id="selectOrderCodeAnrState" parameterType="java.lang.String" resultType="com.lz.model.TOrderState">
        select
         id, code, order_code, operator_id, operate_method, operate_time, operator_ip,
        operate_geography_position, attachment, state_node_value, state_expire_time, if_expire,
        remark, param1, param2, param3, param4, create_user, create_time, update_user, update_time,
        `enable`
        from t_order_state
        where order_code = #{orderCode}
        and state_node_value = #{state}
    </select>

    <!--只查询运单状态CODE -->
    <select id="selectByOrderCodeAndStateNode" parameterType="java.lang.String" resultType="com.lz.model.TOrderState">
    SELECT
      os.*
    FROM
        t_order_state_node_detail osnd
        LEFT JOIN t_order_state_node osn ON osnd.node_id = osn.id and osn.enable = 0
        LEFT JOIN t_order_state os ON state_node_value = osnd.status_code and os.enable = 0
    WHERE  osnd.enable = 0
    and os.order_code = #{orderCode}
    and osn.code = #{nodeCode}
  </select>

    <select id="selectCaptainStatusByOrderCode" resultType="com.lz.model.TOrderState">
        select * from t_order_state where  order_code = #{orderCode} and enable = 0 and (state_node_value = 'S0451' or state_node_value = 'S0452')
    </select>



    <delete id="deleteByOrderCode">
        delete from t_order_state where order_code = #{orderCode}
    </delete>

    <select id="selectNodePayInfoByOrderCode" resultType="com.lz.model.TOrderState">
        select * from t_order_state where  order_code = #{orderCode} and enable = 0
    </select>

</mapper>