<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TOrderPayDetailMapper">
  <resultMap id="BaseResultMapAndPayInfo" extends="BaseResultMap" type="com.lz.dto.TOrderPayInfoAndTOrderPayDetail">
    <result column="order_cast_change_code" jdbcType="VARCHAR" property="orderCastChangeCode" />
    <result column="trade_type" jdbcType="VARCHAR" property="tradeType" />
    <result column="operate_state" jdbcType="VARCHAR" property="operateState" />
    <result column="bank_union_no" jdbcType="VARCHAR" property="bankUnionNo" />
    <result column="bank_no" jdbcType="VARCHAR" property="bankNo" />
    <result column="card_holder" jdbcType="VARCHAR" property="cardHolder" />
    <result column="card_type" jdbcType="VARCHAR" property="cardType" />
    <result column="bank_card_id" jdbcType="VARCHAR" property="bankCardId" />
  </resultMap>

  <select id="selectByPayCode" parameterType="java.lang.String" resultMap="BaseResultMapAndPayInfo">
    select
      pi.*,pd.order_cast_change_code,pd.trade_type,pd.operate_state,pd.bank_union_no,pd.bank_no,pd.card_holder,pd.card_type,pd.bank_card_id
    from t_order_pay_detail pd
    left join t_order_pay_info pi on pi.code = pd.order_pay_code
    where pd.code = #{code,jdbcType=VARCHAR}
  </select>

  <update id="updateStatusByPayCode" parameterType="com.lz.vo.TOrderPayDetailVO">
    update t_order_pay_detail
    set
      return_time = #{returnTime,jdbcType=TIMESTAMP},
      trade_status = #{tradeStatus,jdbcType=VARCHAR},
      error_code = #{errorCode,jdbcType=VARCHAR},
      error_msg = #{errorMsg,jdbcType=VARCHAR},
      inner_trade_no = #{innerTradeNo,jdbcType=VARCHAR},
      update_time = #{returnTime,jdbcType=TIMESTAMP}
    where code = #{code,jdbcType=VARCHAR}
  </update>
  <select id="selectByOrderPayDetailKey" parameterType="java.lang.String" resultType="com.lz.dto.TOrderPayInfoDTO">
   select p.`code` as orderPatDetailCode,p.order_cast_change_code,p.create_time,p.trade_type,p.bank_union_no,p.bank_no,p.card_holder,p.bank_card_id,p.operate_state,p.param1 walletId, p.trade_status,
   p.inner_trade_no, p.create_user createUser, p.create_user txCreateUser,
d.*,c.end_driver_wallet_id,
c.company_wallet_id,c.company_project_wallet_id,c.carrier_wallet_id,c.order_code,c.carriage_fee,c.dispatch_fee,c.withdraw_type,c.capital_transfer_type, c.current_dispatch_rate,ifnull(c.create_user, '') operateCreateUser,
c.capital_transfer_pattern, ifnull(c.service_fee, 0) serviceFee, toi.end_car_owner_id, toi.agent_id, toi.end_driver_id, toi.company_id,
tgsvdi.code cargoOrder, toi.create_time orderCreateTime, toi.vehicle_id, tgsvdi.goods_source_code
 from t_order_pay_detail p
LEFT JOIN t_order_pay_info d
on p.order_pay_code = d.`code`
LEFT JOIN t_order_cast_changes c
on p.order_cast_change_code = c.`code`
   left join t_order_info toi on toi.code = c.order_code
   left join t_goods_source_vehicle_driver_info tgsvdi on toi.code = tgsvdi.order_id and tgsvdi.enable = 0
where p.`code`= #{code,jdbcType=VARCHAR}
  </select>

  <select id="selectVoucherReq" parameterType="java.lang.String" resultType="com.lz.model.VoucherReq">
     select topd.bank_no recCardNO, topd.card_holder recUser, toi.carrier_id, toi.end_driver_id endUserId,
        toi.order_business_code applyType, toi.user_confirm_payment_amount money, toi.code orderCode,
        tci.carrier_name payUser, teui.real_name driverName, teui.phone mobilePhone, teui.idcard idCardNum,
        tci.business_license_no as businessLicenseNo,tci.carrier_name as qName,tci.company_contacts_phone as qphone
    from t_order_info toi
	LEFT JOIN t_company_info companyinfo on toi.company_id=companyinfo.id
    left join t_order_pay_info topi on topi.order_code = toi.code
    left join t_order_pay_detail topd on topd.order_pay_code = topi.code
    left join t_carrier_info tci on toi.carrier_id = tci.id
    left join t_end_user_info teui on teui.id = toi.end_driver_id
    where toi.code = #{code} and topd.code = #{detailCode}
  </select>

  <select id="selectVoucherReqDB" parameterType="java.lang.String" resultType="com.lz.model.VoucherReq">
    select toi.carrier_id, toi.end_driver_id endUserId, toi.order_business_code applyType,
           toi.share_payment_amount money, toi.code orderCode, tci.carrier_name payUser,
           teui.real_name driverName, teui.phone mobilePhone, teui.idcard idCardNum,
           tci.business_license_no as businessLicenseNo,tci.carrier_name as qName,tci.company_contacts_phone as qphone,
           tcecr.thrid_pary_sub_account
    from t_order_info toi
    LEFT JOIN t_company_info companyinfo on toi.company_id=companyinfo.id
    left join t_carrier_info tci on toi.carrier_id = tci.id
    left join t_end_user_info teui on teui.id = toi.end_driver_id
    left join t_carrier_enduser_company_rel tcecr on tcecr.enduser_company_id = teui.id
        and tcecr.datasouce = 'CD' and tcecr.enable = 0
    where toi.code = #{code} and tcecr.carrier_id = toi.carrier_id
  </select>

  <select id="selectOrderpayDetailBankInfo" parameterType="java.lang.String" resultType="com.lz.model.TOrderPayDetail">
      select tbc.card_owner, CONCAT(IFNULL(tbc.bank_name,''), ' ','(',SUBSTRING(tbc.card_no,16), ')') bankNo,
      tbc.card_no param1
      from
          t_order_pack_info topi
          left join t_bank_card tbc on topi.bank_card_id = tbc.id
      WHERE
          topi.CODE = #{code}
  </select>

    <select id="selectByOrderCode" parameterType="java.lang.String" resultType="com.lz.dto.TOrderPayDetailDTO">
    select
        topd.id,
        topd.sduploaded_status,
        topd.ahuploaded_status,
        pay.payment_platforms
    from
    t_order_pay_info pay
    left join t_order_pay_detail topd on pay.code = topd.order_pay_code
    where pay.order_code = #{orderCode} and topd.trade_type in('CABALANCE_PAY','SPLIT_FALL')
  </select>

    <select id="selectByBankno" parameterType="java.lang.String" resultType="com.lz.dto.TOrderPayInfoDTO">
      select
      topd.bank_union_no,
      topd.bank_no,
      topd.card_holder,
      topd.card_type,
      topd.bank_card_id
    from
    t_order_pay_info pay
    left join t_order_pay_detail topd on pay.code = topd.order_pay_code
    where pay.order_code = #{orderCode} and topd.trade_type = 'TX'
  </select>

  <select id="selectByCode" parameterType="java.lang.String" resultType="com.lz.model.TOrderPayDetail">
    select id, code, order_pay_code, order_cast_change_code, bank_union_no, bank_no, card_holder,
           card_type, bank_card_id, trade_type, operate_state, operater_id, operate_time, return_time,
           trade_status, error_code, error_msg, inner_trade_no, remark, param1, param2, param3,
           param4, create_user, create_time, update_user, update_time, `enable`
    from t_order_pay_detail
    where code = #{code}
  </select>

  <update id="updateStatusById" parameterType="com.lz.vo.TOrderPayDetailVO">
  update t_order_pay_detail
  set
  return_time = #{returnTime,jdbcType=TIMESTAMP},
  trade_status = #{tradeStatus,jdbcType=VARCHAR},
  error_code = #{errorCode,jdbcType=VARCHAR},
  error_msg = #{errorMsg,jdbcType=VARCHAR},
  inner_trade_no = #{innerTradeNo,jdbcType=VARCHAR},
  update_time = #{returnTime,jdbcType=TIMESTAMP}
  where code = #{id}
  </update>

  <select id="selectSumCashByBankNo" resultType="java.math.BigDecimal">
    select
    ifnull(sum(tocc.carriage_fee - ifnull(tocc.service_fee, 0)), 0) carriage_fee
    FROM
    t_order_pay_detail topd
    LEFT JOIN t_order_cast_changes tocc ON topd.order_cast_change_code = tocc.CODE
    where topd.operate_time between #{fromTime} and #{endTime}
    and topd.trade_type in ('TX', 'DBTX')
    and (topd.trade_status is null or topd.trade_status = 'TRADE_FINISHED')
    and topd.bank_no in
    <foreach collection="cardNos" item="cardNo" open="(" close=")" separator=",">
      #{cardNo}
    </foreach>
  </select>

  <select id="selectAdvanceOrderPayVoucherReq" parameterType="java.lang.String" resultType="com.lz.model.VoucherReq">
    select taopt.bank_no recCardNO, taopt.card_holder recUser, toi.carrier_id, toi.end_driver_id endUserId,
           toi.order_business_code applyType,
           taot.advance_fee money, toi.code orderCode,
           tci.carrier_name payUser, teui.real_name driverName, teui.phone mobilePhone, teui.idcard idCardNum,
           tci.business_license_no as businessLicenseNo,tci.carrier_name as qName,tci.company_contacts_phone as qphone
    from t_order_info toi
           LEFT JOIN t_company_info companyinfo on toi.company_id=companyinfo.id
            left join t_advance_order_tmp taot on toi.code = taot.order_code
        left join t_advance_order_pay_tmp taopt on taot.code = taopt.advance_code
           left join t_carrier_info tci on toi.carrier_id = tci.id
           left join t_end_user_info teui on teui.id = toi.end_driver_id
        where toi.code = #{code} and taopt.trade_type = 'YFKTX' and taopt.trade_status ='TRADE_FINISHED'
  </select>

    <select id="selectOrderPayDetailByPackId" resultType="com.lz.model.TOrderPayDetail">
        SELECT
            topdi.id, topdi.code, topdi.order_pay_code, topdi.order_cast_change_code, topdi.bank_union_no,
            topdi.bank_no, topdi.card_holder, topdi.card_type, topdi.bank_card_id, topdi.trade_type,
            topdi.operate_state, topdi.operater_id, topdi.operate_time, topdi.return_time, topdi.trade_status,
            topdi.error_code, topdi.error_msg, topdi.inner_trade_no, topdi.remark, topdi.param1, topdi.param2,
            topdi.param3, topdi.param4, topdi.create_user, topdi.create_time, topdi.update_user,
            topdi.update_time, topdi.`enable`
        FROM
            t_order_pack_info topi
            LEFT JOIN t_order_pack_detail topd ON topi.CODE = topd.pack_pay_code
            left join t_order_cast_changes tocc
                on (select Max(id) as id from t_order_cast_changes where order_code = topd.order_code and data_enable = 1) = tocc.id
            left join t_order_pay_detail topdi on topdi.order_cast_change_code = tocc.code
        WHERE
            topi.id = #{id} AND topdi.operate_state = #{operateState} and topi.enable = 0 and topd.enable = 0 and
            tocc.enable = 0 and topdi.enable = 0 and topdi.trade_status = 'TRADE_FINISHED'
    </select>

    <select id="selectOrderPayDetailByorderCastChangeCode" parameterType="java.lang.String" resultType="com.lz.model.TOrderPayDetail">
        SELECT
            topdi.id, topdi.code, topdi.order_pay_code, topdi.order_cast_change_code, topdi.bank_union_no,
            topdi.bank_no, topdi.card_holder, topdi.card_type, topdi.bank_card_id, topdi.trade_type,
            topdi.operate_state, topdi.operater_id, topdi.operate_time, topdi.return_time, topdi.trade_status,
            topdi.error_code, topdi.error_msg, topdi.inner_trade_no, topdi.remark, topdi.param1, topdi.param2,
            topdi.param3, topdi.param4, topdi.create_user, topdi.create_time, topdi.update_user,
            topdi.update_time, topdi.`enable`
        FROM
            t_order_info toi
                LEFT JOIN t_order_pay_info topin ON toi.code = topin.order_code
                LEFT JOIN t_order_pay_detail topdi ON topin.CODE = topdi.order_pay_code
        WHERE
            topdi.order_cast_change_code = #{orderCastChangeCode} and toi.enable = 0 and topdi.enable = 0 and
            topin.enable = 0 and topdi.enable = 0 and topdi.trade_status = 'TRADE_FINISHED'
    </select>

    <select id="selectAgentVoucherReq" parameterType="java.lang.String" resultType="com.lz.model.VoucherReq">
        select topd.bank_no recCardNO, topd.card_holder recUser, toi.carrier_id, toi.end_driver_id endUserId,
               toi.order_business_code applyType, toi.user_confirm_service_fee money, toi.code orderCode,
               tci.carrier_name payUser, teui.real_name driverName, teui.phone mobilePhone, teui.idcard idCardNum,
               tci.business_license_no as businessLicenseNo,tci.carrier_name as qName,tci.company_contacts_phone as qphone,
               tcecr.thrid_pary_sub_account
        from t_order_info toi
        LEFT JOIN t_company_info companyinfo on toi.company_id=companyinfo.id
        left join t_order_pay_info topi on topi.order_code = toi.code
        left join t_order_pay_detail topd on topd.order_pay_code = topi.code
        left join t_carrier_info tci on toi.carrier_id = tci.id
        left join t_end_user_info teui on teui.id = toi.agent_id
        left join t_carrier_enduser_company_rel tcecr on tcecr.enduser_company_id = teui.id
            and tcecr.datasouce = 'CD' and tcecr.enable = 0
        where toi.code = #{code} and topd.code = #{detailCode}
    </select>
    <select id="selectBankByCastChanges" resultType="com.lz.dto.TOrderPayInfoDTO">
        SELECT
        *
        FROM
        t_order_cast_changes toi
        LEFT JOIN t_order_pay_detail topd ON toi.`code` = topd.order_cast_change_code
        WHERE
        toi.order_code = #{code,jdbcType=VARCHAR}
        AND topd.`enable` = FALSE
        and (topd.trade_type='TX' or topd.trade_type='DBTX') AND (topd.trade_status = 'TRADE_FINISHED' or topd.trade_status = 'SUCCESS') limit 1
    </select>

    <select id="selectOrderPayDetailByOrderCastChangeCodeLimit" resultType="com.lz.model.TOrderPayDetail">
        select
        <include refid="Base_Column_List" />
        from t_order_pay_detail
        where order_cast_change_code = #{orderCastChangeCode}
        order by id desc limit 1
    </select>

    <select id="selectOrderPayDetailRefundFinishedByPackId" resultType="com.lz.model.TOrderPayDetail">
        SELECT
            topdi.id, topdi.code, topdi.order_pay_code, topdi.order_cast_change_code, topdi.bank_union_no,
            topdi.bank_no, topdi.card_holder, topdi.card_type, topdi.bank_card_id, topdi.trade_type,
            topdi.operate_state, topdi.operater_id, topdi.operate_time, topdi.return_time, topdi.trade_status,
            topdi.error_code, topdi.error_msg, topdi.inner_trade_no, topdi.remark, topdi.param1, topdi.param2,
            topdi.param3, topdi.param4, topdi.create_user, topdi.create_time, topdi.update_user,
            topdi.update_time, topdi.`enable`
        FROM
            t_order_pack_info topi
                LEFT JOIN t_order_pack_detail topd ON topi.CODE = topd.pack_pay_code
                left join t_order_cast_changes tocc
                          on (select Max(id) as id from t_order_cast_changes where order_code = topd.order_code and data_enable = 1) = tocc.id
                left join t_order_pay_detail topdi on topdi.order_cast_change_code = tocc.code
        WHERE
            topi.id = #{id} AND topdi.operate_state = #{operateState} and topi.enable = 0 and topd.enable = 0 and
            tocc.enable = 0 and topdi.enable = 0 and topdi.trade_status = 'REFUND_FINISH'
    </select>

    <select id="selectTxRecord" resultType="com.lz.dto.TxRecordDTO">
        SELECT
            topi.id, topi.code,
            topi.order_pay_status,
            topi.order_prepay_amount,
            topi.order_actual_payment,
            topi.order_total_payment,
            CASE
                topi.order_pay_status
                WHEN 'M130' THEN
                    '提现成功'
                WHEN 'M120' THEN
                    '提现失败'
                WHEN 'P110' THEN
                    '提现中'
                WHEN 'P070' THEN
                    '提现中'
                WHEN 'M080' THEN
                    '提现失败'
                ELSE '提现中'
                END order_pay_status_str,
            topi.create_time,
            GROUP_CONCAT( topd.bank_no ) bank_no,
            GROUP_CONCAT( topd.card_holder ) card_holder,
            GROUP_CONCAT( topd.operate_time ) operate_time,
            GROUP_CONCAT( topd.trade_type ) trade_type,
            GROUP_CONCAT( topd.return_time ) return_time,
            GROUP_CONCAT( topd.trade_status ) trade_status,
            GROUP_CONCAT( topd.error_msg ) error_msg,
            GROUP_CONCAT( topd.error_code ) error_code
        FROM
            t_order_pay_info topi
            LEFT JOIN t_order_pay_detail topd ON topi.CODE = topd.order_pay_code
        WHERE
                topi.CODE IN
                    (SELECT
                            order_pay_code
                    FROM t_order_pay_detail
                    WHERE
                        order_cast_change_code IS NULL AND param1 IS NOT NULL AND param2 IS NOT NULL
                        <if test="null != walletId">
                            and param1 = #{walletId}
                        </if>
                        <if test="walletIds != null and walletIds.size() > 0">
                            and param1 in
                            <foreach collection="walletIds" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                        <if test="null != enduserId">
                            and param2 = #{enduserId}
                        </if>
                    GROUP BY order_pay_code
                    ORDER BY id
                    )
                <if test="null != startTime and '' != startTime">
                    <![CDATA[
                        and topi.create_time >= #{startTime}
                    ]]>
                </if>
                <if test="null != endTime and '' != endTime">
                    <![CDATA[
                        and topi.create_time <= #{endTime}
                    ]]>
                </if>
        GROUP BY
            topi.id desc
    </select>

    <select id="selectTxPcRecord" resultType="com.lz.dto.TxPcRecordDTO">
        SELECT
        t.id,
        t.CODE,
        t.order_prepay_amount,
        t.order_actual_payment,
        t.order_total_payment userConfirmPaymentAmount,
        t.order_pay_status,
        t.order_pay_status_code,
        t.create_time operateTime,
        t.enduserId,
        teui.phone,
        teui.real_name,
        teui.audit_status driverStatus,
        t.bank_no,
        t.card_holder,
        t.trade_type,
        t.return_time returnTimes,
        t.trade_status,
        t.error_msg,
        t.error_code,
        tjwcl.bank_order_no innerTradeNo
        FROM
        (
        SELECT
        topi.id,
        topi.CODE,
        topi.order_prepay_amount,
        topi.order_actual_payment,
        topi.order_total_payment,
        topi.order_pay_status order_pay_status_code,
        CASE
        topi.order_pay_status
        WHEN 'M130' THEN
        '提现成功'
        WHEN 'M120' THEN
        '提现失败'
        WHEN 'P110' THEN
        '提现中'
        WHEN 'M080' THEN
        '提现失败'
        WHEN 'P070' THEN
        '提现中'ELSE '提现中' END order_pay_status,
        topi.create_time,
        GROUP_CONCAT( topd.param2 ) enduserId,
        GROUP_CONCAT( topd.bank_no ) bank_no,
        GROUP_CONCAT( topd.card_holder ) card_holder,
        GROUP_CONCAT( topd.operate_time ) operate_time,
        GROUP_CONCAT( topd.trade_type ) trade_type,
        GROUP_CONCAT( topd.return_time ) return_time,
        GROUP_CONCAT( topd.trade_status ) trade_status,
        GROUP_CONCAT( topd.error_msg ) error_msg,
        GROUP_CONCAT( topd.error_code ) error_code
        FROM
        t_order_pay_info topi
        LEFT JOIN t_order_pay_detail topd ON topi.CODE = topd.order_pay_code
        WHERE
        topi.CODE IN (
        SELECT
        order_pay_code
        FROM
        t_order_pay_detail td
        left join t_order_pay_info topi2 on td.order_pay_code = topi2.code
        LEFT JOIN t_end_user_info tt ON td.param2 = tt.id
        WHERE
        topi2.payment_platforms != '华夏' and td.order_cast_change_code IS NULL
        AND td.param1 IS NOT NULL
        AND td.param2 IS NOT NULL
        <if test="null != driverName and '' != driverName ">
            AND tt.real_name LIKE concat('%', #{driverName}, '%')
        </if>
        <if test="null != phone and '' != phone ">
            AND tt.phone LIKE concat('%', #{phone}, '%')
        </if>
        GROUP BY
        td.order_pay_code
        ORDER BY
        td.id
        )
        <if test="null != orderPayStatus and '' != orderPayStatus">
            <choose>
                <when test="orderPayStatus == 'P110'">
                    AND topi.order_pay_status in ('P070', 'P110')
                </when>
                <when test="orderPayStatus == 'M120'">
                    AND topi.order_pay_status in ('M120', 'M080')
                </when>
                <when test="orderPayStatus == 'M130'">
                    AND topi.order_pay_status = 'M130'
                </when>
            </choose>
        </if>
        GROUP BY
        topi.id DESC
        ) t
        LEFT JOIN t_order_pay_detail t2 ON t.CODE = t2.order_pay_code AND t2.trade_type = 'TX'
        LEFT JOIN t_jd_wallet_change_log tjwcl ON t2.CODE = tjwcl.trade_no AND tjwcl.trade_type = 'CTIXIAN' AND t2.trade_type = 'TX'
        LEFT JOIN t_end_user_info teui on t.enduserId = teui.id
        <where>
            <if test="null != errorWithdrawReason and errorWithdrawReason == false">
                t.order_pay_status_code in ('M120', 'M080') and  (t.error_msg is not null or length(t.error_msg) > 0)
            </if>
            <if test="null != errorWithdrawReason and errorWithdrawReason == true">
                t.order_pay_status_code in ('M120', 'M080') and  (t.error_msg is null or length(t.error_msg) = 0)
            </if>
        </where>
        union
        SELECT
        twa.id,
        twa.CODE,
        0 AS order_prepay_amount,
        0 AS order_actual_payment,
        twa.amount userConfirmPaymentAmount,
        CASE
        twa.withdrawal_status
        WHEN 'PACKEWITHDRAWERROR' THEN
        '提现失败'
        WHEN 'PACKWITHDRAW' THEN
        '提现成功'
        WHEN 'PACKEXTRACTPROCESSED' THEN
        '提现中' ELSE '提现中'
        END pay_order_status,
        twa.withdrawal_status pay_order_status_code,
        twa.create_time operateTime,
        teuor.end_user_id enduserId,
        teui.phone,
        teui.real_name,
        '' AS driverStatus,
        twa.bank_account_no bank_no,
        twa.account_name card_holder,
        NULL AS returnTimes,
        topd.return_time,
        topd.trade_status,
        IF
        ( twa.withdrawal_status = 'PACKEWITHDRAWERROR', IFNULL( topd.error_msg, twa.withdrawal_info ), '' ) error_msg,
        topd.error_code,
        tjwcl.bank_order_no inner_trade_no
        FROM
        t_withdrawal_application twa
        LEFT JOIN t_end_user_open_role teuor ON twa.open_role_id = teuor.id
        LEFT JOIN t_end_user_info teui ON teuor.end_user_id = teui.id
        LEFT JOIN t_order_pay_detail topd ON twa.biz_order_no = topd.CODE AND topd.trade_type = 'MANGER_SERVICE_PAY'
        LEFT JOIN t_jd_wallet_change_log tjwcl ON twa.biz_order_no = tjwcl.trade_no
        WHERE
        twa.transaction_type = 'MANAGER'
        AND teui.user_logistics_role IN ( 'CTYPEAGENTPERSON', 'CTYPEAGENTCOMPANY' )
        <if test="null != orderPayStatus and '' != orderPayStatus">
            <choose>
                <when test="orderPayStatus == 'P110'">
                    AND twa.withdrawal_status = 'PACKEXTRACTPROCESSED'
                </when>
                <when test="orderPayStatus == 'M120'">
                    AND twa.withdrawal_status = 'PACKEWITHDRAWERROR'
                </when>
                <when test="orderPayStatus == 'M130'">
                    AND twa.withdrawal_status = 'PACKWITHDRAW'
                </when>
            </choose>
        </if>
        <if test="null != driverName and '' != driverName ">
            AND teui.real_name LIKE concat('%', #{driverName}, '%')
        </if>
        <if test="null != phone and '' != phone ">
            AND teui.phone LIKE concat('%', #{phone}, '%')
        </if>
        <if test="null != errorWithdrawReason and errorWithdrawReason == false">
            AND twa.withdrawal_status = 'PACKEWITHDRAWERROR' and (twa.withdrawal_info is not null or length(twa.withdrawal_info) > 0 or topd.error_msg is not null or length(topd.error_msg) > 0)
        </if>
        <if test="null != errorWithdrawReason and errorWithdrawReason == true">
            AND twa.withdrawal_status = 'PACKEWITHDRAWERROR' and (twa.withdrawal_info is null or length(twa.withdrawal_info) = 0) and (topd.error_msg is null or length(topd.error_msg) = 0)
        </if>
        order by operateTime desc
    </select>

    <select id="selectHxTxPcRecord" resultType="com.lz.dto.TxPcRecordDTO">
        SELECT
            t.id,
            t.CODE,
            t.order_prepay_amount,
            t.order_actual_payment,
            t.order_total_payment userConfirmPaymentAmount,
            t.order_pay_status,
            t.order_pay_status_code,
            t.create_time operateTime,
            t.enduserId,
            teui.phone,
            teui.real_name,
            teui.audit_status driverStatus,
            t.bank_no,
            t.card_holder,
            t.trade_type,
            t.return_time returnTimes,
            t.trade_status,
            t.error_msg,
            t.error_code,
            tzwcl.outer_trade_no innerTradeNo
        FROM
            (
                SELECT
                    topi.id,
                    topi.CODE,
                    topi.order_prepay_amount,
                    topi.order_actual_payment,
                    topi.order_total_payment,
                       topi.order_pay_status order_pay_status_code,
                    CASE
                        topi.order_pay_status
                        WHEN 'M130' THEN
                            '提现成功'
                        WHEN 'M120' THEN
                            '提现失败'
                        WHEN 'P110' THEN
                            '提现中'
                        WHEN 'M080' THEN
                            '提现失败'
                        WHEN 'P070' THEN
                        '提现中'ELSE '提现中' END order_pay_status,
                    topi.create_time,
                    GROUP_CONCAT( topd.param2 ) enduserId,
                    GROUP_CONCAT( topd.bank_no ) bank_no,
                    GROUP_CONCAT( topd.card_holder ) card_holder,
                    GROUP_CONCAT( topd.operate_time ) operate_time,
                    GROUP_CONCAT( topd.trade_type ) trade_type,
                    GROUP_CONCAT( topd.return_time ) return_time,
                    GROUP_CONCAT( topd.trade_status ) trade_status,
                    GROUP_CONCAT( topd.error_msg ) error_msg,
                    GROUP_CONCAT( topd.error_code ) error_code
                FROM
                    t_order_pay_info topi
                   LEFT JOIN t_order_pay_detail topd ON topi.CODE = topd.order_pay_code
                WHERE
                    topi.CODE IN (
                    SELECT
                        order_pay_code
                    FROM
                        t_order_pay_detail td
                        left join t_order_pay_info topi2 on td.order_pay_code = topi2.code
                        LEFT JOIN t_end_user_info tt ON td.param2 = tt.id
                    WHERE
                        topi2.payment_platforms = '华夏' and td.order_cast_change_code IS NULL
                      AND td.param1 IS NOT NULL
                      AND td.param2 IS NOT NULL
                      <if test="null != driverName and '' != driverName ">
                          AND tt.real_name LIKE concat('%', #{driverName}, '%')
                      </if>
                      <if test="null != phone and '' != phone ">
                          AND tt.phone LIKE concat('%', #{phone}, '%')
                      </if>
                    GROUP BY
                        td.order_pay_code
                    ORDER BY
                        td.id
                    )
                    <if test="null != orderPayStatus and '' != orderPayStatus">
                        <choose>
                            <when test="orderPayStatus == 'P110'">
                                AND topi.order_pay_status in ('P070', 'P110')
                            </when>
                            <when test="orderPayStatus == 'M120'">
                                AND topi.order_pay_status in ('M120', 'M080')
                            </when>
                            <when test="orderPayStatus == 'M130'">
                                AND topi.order_pay_status = 'M130'
                            </when>
                        </choose>
                    </if>
                GROUP BY
                    topi.id DESC
            ) t
            LEFT JOIN t_order_pay_detail t2 ON t.CODE = t2.order_pay_code AND t2.trade_type = 'TX'
            LEFT JOIN t_zt_wallet_change_log tzwcl ON t2.CODE = tzwcl.trade_no AND tzwcl.trade_type = 'CTIXIAN' AND t2.trade_type = 'TX'
            LEFT JOIN t_end_user_info teui on t.enduserId = teui.id
            <where>
                <if test="null != errorWithdrawReason and errorWithdrawReason == false">
                    t.order_pay_status_code in ('M120', 'M080') and  (t.error_msg is not null or length(t.error_msg) > 0)
                </if>
                <if test="null != errorWithdrawReason and errorWithdrawReason == true">
                    t.order_pay_status_code in ('M120', 'M080') and  (t.error_msg is null or length(t.error_msg) = 0)
                </if>
            </where>
        union
        SELECT
            twa.id,
            twa.CODE,
            0 AS order_prepay_amount,
            0 AS order_actual_payment,
            twa.amount userConfirmPaymentAmount,
        CASE
            twa.withdrawal_status
            WHEN 'PACKEWITHDRAWERROR' THEN
             '提现失败'
            WHEN 'PACKWITHDRAW' THEN
                '提现成功'
            WHEN 'PACKEXTRACTPROCESSED' THEN
                '提现中' ELSE '提现中'
         END pay_order_status,
            twa.withdrawal_status pay_order_status_code,
            twa.create_time operateTime,
            tea.enduser_id enduserId,
            teui.phone,
            teui.real_name,
            '' AS driverStatus,
            twa.bank_account_no bank_no,
            twa.account_name card_holder,
            NULL AS returnTimes,
            topd.return_time,
            topd.trade_status,
        IF
            ( twa.withdrawal_status = 'PACKEWITHDRAWERROR', IFNULL( topd.error_msg, twa.withdrawal_info ), '' ) error_msg,
            topd.error_code,
            tzwcl.outer_trade_no as inner_trade_no
        FROM
            t_withdrawal_application twa
            LEFT JOIN t_zt_account_open_info tzaoi ON twa.open_role_id = tzaoi.id
            left join t_enduser_account tea on tea.id = tzaoi.account_id
            LEFT JOIN t_end_user_info teui ON tea.enduser_id = teui.id
            LEFT JOIN t_order_pay_detail topd ON twa.biz_order_no = topd.CODE AND topd.trade_type = 'MANGER_SERVICE_PAY'
            LEFT JOIN t_zt_wallet_change_log tzwcl ON twa.biz_order_no = tzwcl.trade_no
        WHERE
            twa.transaction_type = 'MANAGER' and twa.param1 = 'HX' and tzwcl.trade_type = 'MANAGER'
            <if test="null != orderPayStatus and '' != orderPayStatus">
                <choose>
                    <when test="orderPayStatus == 'P110'">
                        AND twa.withdrawal_status = 'PACKEXTRACTPROCESSED'
                    </when>
                    <when test="orderPayStatus == 'M120'">
                        AND twa.withdrawal_status = 'PACKEWITHDRAWERROR'
                    </when>
                    <when test="orderPayStatus == 'M130'">
                        AND twa.withdrawal_status = 'PACKWITHDRAW'
                    </when>
                </choose>
            </if>
            <if test="null != driverName and '' != driverName ">
                AND teui.real_name LIKE concat('%', #{driverName}, '%')
            </if>
            <if test="null != phone and '' != phone ">
                AND teui.phone LIKE concat('%', #{phone}, '%')
            </if>
            <if test="null != errorWithdrawReason and errorWithdrawReason == false">
                AND twa.withdrawal_status = 'PACKEWITHDRAWERROR' and (twa.withdrawal_info is not null or length(twa.withdrawal_info) > 0 or topd.error_msg is not null or length(topd.error_msg) > 0)
            </if>
            <if test="null != errorWithdrawReason and errorWithdrawReason == true">
                AND twa.withdrawal_status = 'PACKEWITHDRAWERROR' and (twa.withdrawal_info is null or length(twa.withdrawal_info) = 0) and (topd.error_msg is null or length(topd.error_msg) = 0)
            </if>
        order by operateTime desc
    </select>

    <select id="selectListByPayCode" resultType="com.lz.model.TOrderPayDetail">
        select
            topd.id, topd.code
        from t_order_pay_detail topd
        where topd.order_pay_code = #{payCode,jdbcType=VARCHAR}
    </select>

    <select id="selectNewestOrderPayDetailByOrderCode" resultType="com.lz.model.TOrderPayDetail">
        select
            topd.*
        from t_order_pay_detail topd
                 left join t_order_pay_info topi on topd.order_pay_code = topi.code
        where topi.order_code = #{orderCode,jdbcType=VARCHAR} and topd.trade_type = 'COMBALANCE_PAY'
        order by topd.id desc limit 1
    </select>

    <select id="selectNewtestOrderDetailByOrdercode" resultType="com.lz.model.TOrderPayDetail">
        SELECT
            max(tod.id) id, tod.return_time
        FROM
            t_order_pay_info top
                LEFT JOIN t_order_pay_detail tod ON top. CODE = tod.order_pay_code
        WHERE
            top.order_pay_status = 'M090'
          AND top. ENABLE = 0
          AND tod. ENABLE = 0
          AND top.order_code = #{orderCode,jdbcType=VARCHAR}
          AND tod.trade_type = 'COMBALANCE_PAY'
          AND tod.trade_status='TRADE_FINISHED'
    </select>

    <delete id="deleteByPayCode">
        delete from t_order_pay_detail where order_pay_code = #{payCode}
    </delete>

</mapper>