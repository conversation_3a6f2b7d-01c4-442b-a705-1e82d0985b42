<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TOrderUploadMapper">
  <resultMap id="BaseResultMap" type="com.lz.model.TOrderUpload">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="receipt_upload_status" jdbcType="INTEGER" property="receiptUploadStatus" />
    <result column="receipt_upload_date" jdbcType="TIMESTAMP" property="receiptUploadDate" />
    <result column="trade_upload_status" jdbcType="INTEGER" property="tradeUploadStatus" />
    <result column="trade_upload_date" jdbcType="TIMESTAMP" property="tradeUploadDate" />
    <result column="pay_upload_status" jdbcType="INTEGER" property="payUploadStatus" />
    <result column="pay_upload_date" jdbcType="TIMESTAMP" property="payUploadDate" />
    <result column="flow_upload_status" jdbcType="INTEGER" property="flowUploadStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_code, receipt_upload_status, receipt_upload_date, trade_upload_status, 
    trade_upload_date, pay_upload_status, pay_upload_date, flow_upload_status, create_time
  </sql>
  <select id="selectByExample" parameterType="com.lz.example.TOrderUploadExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_order_upload
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_order_upload
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from t_order_upload
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.lz.example.TOrderUploadExample">
    delete from t_order_upload
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.lz.model.TOrderUpload" useGeneratedKeys="true">
    insert into t_order_upload (order_code, receipt_upload_status, receipt_upload_date, 
      trade_upload_status, trade_upload_date, pay_upload_status, 
      pay_upload_date, flow_upload_status, create_time
      )
    values (#{orderCode,jdbcType=VARCHAR}, #{receiptUploadStatus,jdbcType=INTEGER}, #{receiptUploadDate,jdbcType=TIMESTAMP}, 
      #{tradeUploadStatus,jdbcType=INTEGER}, #{tradeUploadDate,jdbcType=TIMESTAMP}, #{payUploadStatus,jdbcType=INTEGER}, 
      #{payUploadDate,jdbcType=TIMESTAMP}, #{flowUploadStatus,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.lz.model.TOrderUpload" useGeneratedKeys="true">
    insert into t_order_upload
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderCode != null">
        order_code,
      </if>
      <if test="receiptUploadStatus != null">
        receipt_upload_status,
      </if>
      <if test="receiptUploadDate != null">
        receipt_upload_date,
      </if>
      <if test="tradeUploadStatus != null">
        trade_upload_status,
      </if>
      <if test="tradeUploadDate != null">
        trade_upload_date,
      </if>
      <if test="payUploadStatus != null">
        pay_upload_status,
      </if>
      <if test="payUploadDate != null">
        pay_upload_date,
      </if>
      <if test="flowUploadStatus != null">
        flow_upload_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderCode != null">
        #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="receiptUploadStatus != null">
        #{receiptUploadStatus,jdbcType=INTEGER},
      </if>
      <if test="receiptUploadDate != null">
        #{receiptUploadDate,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeUploadStatus != null">
        #{tradeUploadStatus,jdbcType=INTEGER},
      </if>
      <if test="tradeUploadDate != null">
        #{tradeUploadDate,jdbcType=TIMESTAMP},
      </if>
      <if test="payUploadStatus != null">
        #{payUploadStatus,jdbcType=INTEGER},
      </if>
      <if test="payUploadDate != null">
        #{payUploadDate,jdbcType=TIMESTAMP},
      </if>
      <if test="flowUploadStatus != null">
        #{flowUploadStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.lz.example.TOrderUploadExample" resultType="java.lang.Long">
    select count(*) from t_order_upload
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_order_upload
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.orderCode != null">
        order_code = #{record.orderCode,jdbcType=VARCHAR},
      </if>
      <if test="record.receiptUploadStatus != null">
        receipt_upload_status = #{record.receiptUploadStatus,jdbcType=INTEGER},
      </if>
      <if test="record.receiptUploadDate != null">
        receipt_upload_date = #{record.receiptUploadDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tradeUploadStatus != null">
        trade_upload_status = #{record.tradeUploadStatus,jdbcType=INTEGER},
      </if>
      <if test="record.tradeUploadDate != null">
        trade_upload_date = #{record.tradeUploadDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.payUploadStatus != null">
        pay_upload_status = #{record.payUploadStatus,jdbcType=INTEGER},
      </if>
      <if test="record.payUploadDate != null">
        pay_upload_date = #{record.payUploadDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.flowUploadStatus != null">
        flow_upload_status = #{record.flowUploadStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_order_upload
    set id = #{record.id,jdbcType=INTEGER},
      order_code = #{record.orderCode,jdbcType=VARCHAR},
      receipt_upload_status = #{record.receiptUploadStatus,jdbcType=INTEGER},
      receipt_upload_date = #{record.receiptUploadDate,jdbcType=TIMESTAMP},
      trade_upload_status = #{record.tradeUploadStatus,jdbcType=INTEGER},
      trade_upload_date = #{record.tradeUploadDate,jdbcType=TIMESTAMP},
      pay_upload_status = #{record.payUploadStatus,jdbcType=INTEGER},
      pay_upload_date = #{record.payUploadDate,jdbcType=TIMESTAMP},
      flow_upload_status = #{record.flowUploadStatus,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.lz.model.TOrderUpload">
    update t_order_upload
    <set>
      <if test="orderCode != null">
        order_code = #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="receiptUploadStatus != null">
        receipt_upload_status = #{receiptUploadStatus,jdbcType=INTEGER},
      </if>
      <if test="receiptUploadDate != null">
        receipt_upload_date = #{receiptUploadDate,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeUploadStatus != null">
        trade_upload_status = #{tradeUploadStatus,jdbcType=INTEGER},
      </if>
      <if test="tradeUploadDate != null">
        trade_upload_date = #{tradeUploadDate,jdbcType=TIMESTAMP},
      </if>
      <if test="payUploadStatus != null">
        pay_upload_status = #{payUploadStatus,jdbcType=INTEGER},
      </if>
      <if test="payUploadDate != null">
        pay_upload_date = #{payUploadDate,jdbcType=TIMESTAMP},
      </if>
      <if test="flowUploadStatus != null">
        flow_upload_status = #{flowUploadStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lz.model.TOrderUpload">
    update t_order_upload
    set order_code = #{orderCode,jdbcType=VARCHAR},
      receipt_upload_status = #{receiptUploadStatus,jdbcType=INTEGER},
      receipt_upload_date = #{receiptUploadDate,jdbcType=TIMESTAMP},
      trade_upload_status = #{tradeUploadStatus,jdbcType=INTEGER},
      trade_upload_date = #{tradeUploadDate,jdbcType=TIMESTAMP},
      pay_upload_status = #{payUploadStatus,jdbcType=INTEGER},
      pay_upload_date = #{payUploadDate,jdbcType=TIMESTAMP},
      flow_upload_status = #{flowUploadStatus,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>