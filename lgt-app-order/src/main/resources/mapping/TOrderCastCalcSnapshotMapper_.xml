<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TOrderCastCalcSnapshotMapper">
  <!--APP 运单详情查询 Yan-->
  <select id="appSelectSnapsByOrderCode" resultType="com.lz.dto.OrderDetailDTO" parameterType="java.lang.String">
    SELECT
        occs.order_code, <!-- 运单主表code -->
        occs.real_deliver_weight as primaryWeight, <!--原发重量-->
        occs.real_dispath_weight as dischargeWeight, <!--实收重量 -->
        occs.deliver_weight_notes_upload_time as deliverWeightNotesTime,  <!--发货磅单时间 -->
        occs.receive_weight_notes_upload_time as receiveWeightNotesTime, <!--收货磅单时间 -->
        occs.deliver_weight_notes_photo AS deliverWeightNotesPhoto, <!--发货磅单 -->
        occs.receive_weight_notes_photo AS receiveWeightNotesPhoto, <!--收货磅单-->
        occs.goods_cut_water AS goodsCutWater, <!--扣水-->
        occs.goods_cut_impurities as goodsCutImpurities, <!--扣杂-->
        occs.settled_weight as settleAccountsWeight, <!--结算重量-->
        occs.settled_weight as settledWeight, <!--结算重量-->
        occs.carriage_payment, <!--应付运费 -->
        occs.deficit_weight, <!--亏损重量 add by zhangjiji 2019/7/4-->
        occs.lose_or_rise as loseOrRise, <!--亏吨涨吨（涨损重量）-->
        occs.lose_or_rise as lossWeight, <!--亏吨涨吨（涨损重量）-->
        occs.lose_or_rise_cut as loseOrRiseCut, <!--亏吨涨吨扣款-->
        occs.lose_or_rise_cut as lossDeduction, <!--亏吨涨吨扣款-->
        occs.fix_cut_fee, <!-- 固定扣款 -->
        occs.fix_cut_remark, <!-- 固定扣款备注 -->
        occs.other_cut_fee1, <!--扣款-->
        occs.other_cut_fee2,
        occs.other_cut_fee3,
        occs.other_cut_fee4,
        occs.other_cut_remark1, <!--扣款说明-->
        occs.other_cut_remark2,
        occs.other_cut_remark3,
        occs.other_cut_remark4,
        occs.remark calRemark, <!-- 结算备注 -->
        occs.rule_name, <!-- 规则名称 -->
        occs.carriage_zero_cut_payment as carriageZeroCutPayment, <!--抹零金额-->
        occs.carriage_zero_cut_payment_rule as carriageZeroCutPaymentRule, <!--抹零规则 code-->
        dci.item_value as carriageZeroCutPaymentName, <!-- 抹零名称 -->
        lgcrd.id lineGoodsCarriageRuleId,
        lgcrd.settled_weight_type,
        lgcrd.if_exceed_amount,
        occs.goods_unit_price as goodsUnitPrice, <!--货值单价-->
        occs.tolerant_value_coefficient as tolerantValueCoefficient, <!--按系数-->
        occs.tolerant_value_weight as tolerantValueWeight, <!--按吨数-->
        occs.carriage_unit_price, <!--运费单价-->
        occs.user_confirm_carriage_payment as userConfirmCarriagePayment, <!-- 用户确认的应付运费金额 -->
        occs.carriage_payment, <!-- 规则计算的应付运费金额 -->
        occs.cut_water_is_calcvalue, <!-- 扣水是否计算货值单价 -->
        occs.cut_impurities_is_calcvalue, <!-- 扣杂是否计算货值单价 -->
        occs.remark, <!-- 结算备注 -->
        toi.create_time AS orderCreateTime, <!--建单时间 -->
        toi.order_business_code, <!-- 运单编号 -->
        toi.deliver_goods_contacter, <!--发单员姓名-->
        toi.deliver_goods_contacter_phone, <!--发单员手机号-->
        toi.remark orderRemark, <!--发单备注-->
        toi.line_goods_rel_id, <!--线路货物关系id-->
        toi.deliver_weight_notes_weight, <!-- 发货磅单重量 -->
        toi.receive_weight_notes_weight, <!-- 收货磅单重量 -->
        toi.gross_weight,
        toi.deliver_order_time deliverOrderTime, <!--发单时间-->
        toi.receive_order_time receiverOrderTime, <!--收单时间-->
      toi.pay_method as payMethod,
      teui.id endDriverId,
      teui.real_name,
      teui.phone,
      teui.audit_status driverStateCode,
      tdci2.item_value auditStatus,
      teci.id endCarId,
      teci.vehicle_number,
      teci.audit_status carStateCode,
      tdci3.item_value carState,
      toi.end_car_owner_id AS endCarOwnerId,
      teui2.real_name owner,
      teui2.phone carOwnerPhone,
      teui2.audit_status carCaptainAuditStatus,
      CONCAT(
          teui2.real_name,
          ' ',
          teui2.phone
      ) AS endCarOwnerName,
      CONCAT(
          teui3.real_name,
          ' ',
          teui3.phone
      ) AS endAgentName,
      toi.contract_status contractStatusCode, tdci.item_value contractStatus, tlgr.payment_process, tlgr.business_assist,
      toi.end_agent_id, toi.company_id, tocc.share_method, tocc.share_value, tocc.capital_transfer_pattern, tlgr.update_service_fee,
           toi.service_fee, toi.user_confirm_service_fee,
      ifnull(toid.carriage_price_unit, 'DUN') as carriage_price_unit,
      toiw.box_num,
      toiw.deliver_weight_notes_time1,
      toiw.deliver_weight_notes_weight1,
      toiw.deliver_weight_notes_photo1,
      toiw.receive_weight_notes_time1,
      toiw.receive_weight_notes_weight1,
      toiw.receive_weight_notes_photo1,
      toiw.gross_weight1,
      toiw.primary_weight1,
      toiw.discharge_weight1,
      toiw.carriage_unit_price1,
      toiw.deliver_weight_notes_time2,
      toiw.deliver_weight_notes_weight2,
      toiw.deliver_weight_notes_photo2,
      toiw.receive_weight_notes_time2,
      toiw.receive_weight_notes_weight2,
      toiw.receive_weight_notes_photo2,
      toiw.gross_weight2,
      toiw.primary_weight2,
      toiw.discharge_weight2,
      toiw.carriage_unit_price2
    FROM
        t_order_cast_calc_snapshot occs
    LEFT JOIN t_line_goods_carriage_rule_detail lgcrd ON occs.line_goods_carriage_rule_id = lgcrd.id
    LEFT JOIN t_dic_cat_item dci ON occs.carriage_zero_cut_payment_rule = dci.item_code
    left join t_order_info toi on occs.order_code = toi.code
      left join t_order_info_detail toid on toid.order_id = toi.id
      left join t_order_info_weight toiw on toiw.order_id = toi.id
      left join t_goods_source_info tgsi on tgsi.line_goods_rel_id = toi.line_goods_rel_id and tgsi.enable = 0
      left join t_end_user_info teui on toi.end_driver_id = teui.id
      left join t_end_car_info teci on toi.vehicle_id = teci.id
      left join t_end_user_info teui2 on teui2.id = toi.end_car_owner_id
      left join t_end_user_info teui3 on teui3.id = toi.end_agent_id
      left join t_dic_cat_item tdci on toi.contract_status = tdci.item_code
      left join t_dic_cat_item tdci2 on teui.audit_status = tdci2.item_code
      left join t_dic_cat_item tdci3 on teci.audit_status = tdci3.item_code
      left join t_line_goods_rel tlgr on toi.line_goods_rel_id = tlgr.id
    left join t_order_cast_changes tocc on toi.code = tocc.order_code and tocc.data_enable = 1
    WHERE
        occs.order_code = #{code} and occs.data_enable = 1
    ORDER BY occs.create_time DESC
    LIMIT 1
  </select>
  <!--PC 运单详情查询已收单的 Yan-->
  <select id="pcSelectSnapsByOrderCode" resultType="com.lz.dto.OrderDetailDTO" parameterType="java.lang.String">
    SELECT
        gsi.estimate_goods_weight as estimateGoodsWeight,
        toi.line_goods_rel_id, <!-- 线路货物关系ID -->
        occs.real_deliver_weight as primaryWeight,<!-- 原发重量   toi.primary_weight  -->
        occs.real_dispath_weight as realDispathWeight, <!-- 实收重量-->
        occs.goods_cut_water as goodsCutWater, <!-- 扣水 -->
        occs.goods_cut_impurities as goodsCutImpurities, <!-- 扣杂-->
        occs.settled_weight as settledWeight, <!-- 结算重量 -->
        occs.deliver_weight_notes_photo, <!-- 发货磅单-->
        occs.receive_weight_notes_photo, <!-- 卸货磅单-->
        occs.carriage_unit_price as carriageUnitPrice, <!-- 运费单价-->
        occs.lose_or_rise as loseOrRise, <!-- 涨损 -->
        occs.lose_or_rise_cut as loseOrRiseCut, <!-- 亏吨扣款 -->
        occs.deficit_weight AS deficitWeight, <!-- 亏损重量 -->
        occs.tolerant_value_coefficient AS tolerantValueCoefficient, <!-- 约定物损 两个容忍值那个有显示那个-->
        occs.tolerant_value_weight AS tolerantValueWeight, <!-- 约定物损 两个容忍值那个有显示那个-->
        lgcr.id lineGoodsCarriageRuleId,
        lgcr.carriage_rule_id, <!-- 计算规则主表id-->
        occs.goods_unit_price, <!-- 货值单价 -->
        occs.fix_cut_remark AS fixCutRemark, <!-- 扣款备注: 固定扣款备注 -->
        occs.fix_cut_fee AS fixCutFee, <!-- 扣款备注: 固定扣款 -->
        occs.other_cut_fee1 AS otherCutFee1, <!-- 扣款金额-->
        occs.other_cut_remark1, <!-- 扣款备注-->
        occs.other_cut_fee2 AS otherCutFee2, <!-- 扣款金额-->
        occs.other_cut_remark2, <!-- 扣款备注-->
        occs.other_cut_fee3 AS otherCutFee3, <!-- 扣款金额-->
        occs.other_cut_remark3, <!-- 扣款备注-->
        occs.other_cut_fee4 AS otherCutFee4, <!-- 扣款金额-->
        occs.other_cut_remark4, <!-- 扣款备注-->
        tdci.item_value as carriageZeroCutPaymentRule, <!-- 抹零规则-->
        occs.cut_water_is_calcvalue as cutWaterIsCalcvalue, <!-- 扣水是否计算货值单价 -->
        occs.cut_impurities_is_calcvalue as cutImpuritiesIsCalcvalue, <!-- 扣杂是否计算货值单价 -->
        occs.lose_or_rise_cut AS loseOrRiseCut, <!-- 亏吨涨吨扣款-->
        occs.carriage_zero_cut_payment AS carriageZeroCutPayment, <!-- 抹零金额-->
        occs.carriage_payment AS carriagePayment, <!-- 规则计算的应付运费金额-->
        occs.user_confirm_carriage_payment AS userConfirmCarriagePayment, <!-- 规则计算应付金额:用户确认的应付运费金额-->
        occs.remark as snapshotRemark, <!-- 结算备注 -->
        toi.pack_status AS packStatus, <!-- 运单打包状态-->
        toi.share_payment_amount AS sharePaymentAmount, <!-- 打包后重新分配的运费金额-->
        tas.nickname as deliverOrderUserName,
        tas.account_no as deliverOrderUserPhone,
        tao.nickname as receiveOrderUserName,
        tao.account_no as receiveOrderUserPhone
    FROM t_order_info toi
        LEFT JOIN t_order_cast_calc_snapshot occs ON occs.id = (
          select s.id
          from t_order_cast_calc_snapshot s
          where s.data_enable = 1 AND toi.`code` = s.order_code
          order by s.create_time desc
          LIMIT 1)
        LEFT JOIN t_line_goods_carriage_rule_detail lgcr ON toi.line_goods_rel_id = lgcr.line_goods_rel_id and lgcr.`enable`=0
        LEFT JOIN t_dic_cat_item tdci ON tdci.item_code = occs.carriage_zero_cut_payment_rule
        LEFT JOIN t_goods_source_info gsi ON toi.line_goods_rel_id = gsi.line_goods_rel_id
        left join t_account tas on tas.id = toi.deliver_order_user_id
        left join t_account tao on tao.id = toi.receive_order_user_id
    WHERE
        toi.`code` = #{code}
  </select>

  <update id="updateDataEnableByCode" parameterType="java.lang.String">
        update t_order_cast_calc_snapshot
        set data_enable = 0
        where order_code = #{code} and data_enable = 1
  </update>
    <!--运单打包校验-判断运单快照表中的-用户确认的应付运费金额 是否大于 10W Yan-->
    <select id="orderJudgeCostCount" parameterType="java.lang.String" resultType="java.lang.Boolean">
        SELECT
            SUM(occs.user_confirm_carriage_payment) > 100000 AS cost
        FROM
            t_order_cast_calc_snapshot occs
        WHERE
            occs.enable = 0 and occs.data_enable = 1 and
            occs.order_code IN
            <foreach collection="codes" index="index" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
    </select>

    <!--APP 运单详情查询运单的结算规则  已收单在快照表, 没有收单的查原始的  这个是快照的规则Yan-->
    <select id="getOrderDetailCarriageRule" parameterType="java.lang.String" resultType="com.lz.dto.LineGoodsCarriageRuleDetailDTO">
        SELECT
            occs.rule_name, <!-- 规则名称 -->
            occs.goods_unit_price, <!-- 货值单价-->
            dci.item_value as carriageZeroCutPaymentRule, <!-- 抹零规则-->
            occs.tolerant_value_coefficient as tolerantValueCoefficient, <!-- 容忍值1（按系数-->
            occs.tolerant_value_weight as tolerantValueWeight, <!-- 容忍值2（按吨数-->
            occs.goods_cut_water, <!-- 扣水吨数-->
            occs.goods_cut_impurities, <!-- 扣杂吨数-->
            occs.cut_water_is_calcvalue, <!-- 扣水是否计算货值单价-->
            occs.cut_impurities_is_calcvalue, <!-- 扣杂是否计算货物价值-->
            occs.fix_cut_fee as cutFixFee, <!-- 固定扣款金额-->
            occs.fix_cut_remark as cutFixRemark, <!-- 固定扣款备注-->
            occs.other_cut_fee1 as otherCutPayment1, <!-- 其他扣款金额-->
            occs.other_cut_fee2 as otherCutPayment2,
            occs.other_cut_fee3 as otherCutPayment3,
            occs.other_cut_fee4 as otherCutPayment4,
            occs.other_cut_remark1 as cutPaymentNote1, <!-- 其他扣款备注-->
            occs.other_cut_remark2 as cutPaymentNote2,
            occs.other_cut_remark3 as cutPaymentNote3,
            occs.other_cut_remark4 as cutPaymentNote4,
            toi.company_id
        FROM t_order_cast_calc_snapshot occs
        LEFT JOIN t_dic_cat_item dci ON occs.carriage_zero_cut_payment_rule = dci.item_code
        left join t_order_info toi on occs.order_code = toi.code
        WHERE occs.order_code = #{code}
        ORDER BY occs.create_time DESC
        LIMIT 1
    </select>
    <!--收单，重新收单：先判断运单是否收过单 Yan-->
    <select id="judgeOrderIsIncome" parameterType="java.lang.String" resultType="java.lang.Boolean">
        SELECT
            ISNULL( (
                SELECT
                    occs.id
                FROM
                    t_order_cast_calc_snapshot occs
                WHERE
                    occs.order_code = #{code}
                LIMIT 1 )
            )
    </select>
</mapper>