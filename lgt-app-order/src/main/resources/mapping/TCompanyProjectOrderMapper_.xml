<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TCompanyProjectOrderMapper">
    <resultMap id="BaseResultMap" type="com.lz.model.TCompanyProject">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="company_id" jdbcType="INTEGER" property="companyId" />
        <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
        <result column="project_name" jdbcType="VARCHAR" property="projectName" />
        <result column="project_feature" jdbcType="VARCHAR" property="projectFeature" />
        <result column="project_online_contract_beginning_date" jdbcType="DATE" property="projectOnlineContractBeginningDate" />
        <result column="project_online_contract_end_date" jdbcType="DATE" property="projectOnlineContractEndDate" />
        <result column="project_join_marketing_person" jdbcType="VARCHAR" property="projectJoinMarketingPerson" />
        <result column="project_join_implement_person" jdbcType="VARCHAR" property="projectJoinImplementPerson" />
        <result column="stop_flag" jdbcType="BIT" property="stopFlag" />
        <result column="line_type" jdbcType="VARCHAR" property="lineType" />
        <result column="capital_transfer_type" jdbcType="VARCHAR" property="capitalTransferType" />
        <result column="withdraw_type" jdbcType="VARCHAR" property="withdrawType" />
        <result column="contract_photo2" jdbcType="VARCHAR" property="contractPhoto2" />
        <result column="other_content" jdbcType="VARCHAR" property="otherContent" />
        <result column="pay_method" jdbcType="VARCHAR" property="payMethod" />
        <result column="credit_line" jdbcType="DECIMAL" property="creditLine" />
        <result column="after_use_left_limit" jdbcType="DECIMAL" property="afterUseLeftLimit" />
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
        <result column="credit_days" jdbcType="INTEGER" property="creditDays" />
        <result column="pay_deadline" jdbcType="INTEGER" property="payDeadline" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="workflow_id" jdbcType="INTEGER" property="workflowId" />
        <result column="audit_status" jdbcType="VARCHAR" property="auditStatus" />
        <result column="audit_opinion" jdbcType="VARCHAR" property="auditOpinion" />
        <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
        <result column="param1" jdbcType="VARCHAR" property="param1" />
        <result column="param2" jdbcType="VARCHAR" property="param2" />
        <result column="param3" jdbcType="VARCHAR" property="param3" />
        <result column="param4" jdbcType="VARCHAR" property="param4" />
        <result column="operate_method" jdbcType="VARCHAR" property="operateMethod" />
        <result column="operator_ip" jdbcType="VARCHAR" property="operatorIp" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="enable" jdbcType="BIT" property="enable" />
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.lz.model.TCompanyProjectWithBLOBs">
        <result column="project_online_program" jdbcType="LONGVARCHAR" property="projectOnlineProgram" />
        <result column="contract_photo1" jdbcType="LONGVARCHAR" property="contractPhoto1" />
    </resultMap>

    <sql id="Base_Column_List">
        id, company_id, project_code, project_name, project_feature, project_online_contract_beginning_date,
    project_online_contract_end_date, project_join_marketing_person, project_join_implement_person,
    stop_flag, line_type, capital_transfer_type, withdraw_type, contract_photo2, other_content,
    pay_method, credit_line, after_use_left_limit, start_time, credit_days, pay_deadline,
    remark, workflow_id, audit_status, audit_opinion, audit_time, param1, param2, param3,
    param4, operate_method, operator_ip, create_user, create_time, update_user, update_time,
    `enable`
    </sql>
    <sql id="Blob_Column_List">
        project_online_program, contract_photo1
    </sql>

    <update id="updateQuota" >
        update t_company_project
        set
            after_use_left_limit = after_use_left_limit - #{totalPrice,jdbcType=DECIMAL}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="unfreeze" >
        update t_company_project
        set
            after_use_left_limit = after_use_left_limit + #{totalPrice,jdbcType=DECIMAL}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from t_company_project
        where id = #{id,jdbcType=INTEGER}
    </select>
</mapper>