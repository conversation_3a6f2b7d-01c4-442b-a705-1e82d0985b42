<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TOrderMaintenanceRecordsInfoMapper">

    <resultMap id="BaseResultMap" type="com.lz.model.TOrderMaintenanceRecordsInfo">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="orderMaintenanceRecordsId" column="order_maintenance_records_id" jdbcType="INTEGER"/>
            <result property="orderCode" column="order_code" jdbcType="VARCHAR"/>
            <result property="processingResults" column="processing_results" jdbcType="VARCHAR"/>
            <result property="newFreightUnitPrice" column="new_freight_unit_price" jdbcType="DECIMAL"/>
            <result property="migrateType" column="migrate_type" jdbcType="VARCHAR"/>
            <result property="dataEnable" column="data_enable" jdbcType="TINYINT"/>
            <result property="companyId" column="company_id" jdbcType="INTEGER"/>
            <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
            <result property="carrierId" column="carrier_id" jdbcType="INTEGER"/>
            <result property="carrierName" column="carrier_name" jdbcType="VARCHAR"/>
            <result property="sourceId" column="source_id" jdbcType="INTEGER"/>
            <result property="sourceName" column="source_name" jdbcType="VARCHAR"/>
            <result property="captainId" column="captain_id" jdbcType="INTEGER"/>
            <result property="captainName" column="captain_name" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="param1" column="param1" jdbcType="VARCHAR"/>
            <result property="param2" column="param2" jdbcType="VARCHAR"/>
            <result property="param3" column="param3" jdbcType="VARCHAR"/>
            <result property="param4" column="param4" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="enable" column="enable" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_maintenance_records_id,order_code,
        processing_results,new_freight_unit_price,migrate_type,
        data_enable,company_id,company_name,carrier_id,
        carrier_name,source_id,source_name,
        captain_id,captain_name,remark,
        param1,param2,param3,
        param4,create_time,create_user,
        update_time,update_user,enable
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_order_maintenance_records_info
        where  id = #{id,jdbcType=INTEGER} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from t_order_maintenance_records_info
        where  id = #{id,jdbcType=INTEGER} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.lz.model.TOrderMaintenanceRecordsInfo" useGeneratedKeys="true">
        insert into t_order_maintenance_records_info
        ( id,order_maintenance_records_id,order_code
        ,processing_results,new_freight_unit_price,migrate_type
        ,data_enable,company_id,company_name,carrier_id
        ,carrier_name,source_id,source_name
        ,captain_id,captain_name,remark
        ,param1,param2,param3
        ,param4,create_time,create_user
        ,update_time,update_user,enable
        )
        values (#{id,jdbcType=INTEGER},#{orderMaintenanceRecordsId,jdbcType=INTEGER},#{orderCode,jdbcType=VARCHAR}
        ,#{processingResults,jdbcType=VARCHAR},#{newFreightUnitPrice,jdbcType=DECIMAL},#{migrateType,jdbcType=VARCHAR}
        ,#{dataEnable,jdbcType=TINYINT},#{companyId,jdbcType=INTEGER},#{companyName,jdbcType=VARCHAR},#{carrierId,jdbcType=INTEGER}
        ,#{carrierName,jdbcType=VARCHAR},#{sourceId,jdbcType=INTEGER},#{sourceName,jdbcType=VARCHAR}
        ,#{captainId,jdbcType=INTEGER},#{captainName,jdbcType=VARCHAR},#{remark,jdbcType=VARCHAR}
        ,#{param1,jdbcType=VARCHAR},#{param2,jdbcType=VARCHAR},#{param3,jdbcType=VARCHAR}
        ,#{param4,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP},#{createUser,jdbcType=VARCHAR}
        ,#{updateTime,jdbcType=TIMESTAMP},#{updateUser,jdbcType=VARCHAR},#{enable,jdbcType=TINYINT}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.lz.model.TOrderMaintenanceRecordsInfo" useGeneratedKeys="true">
        insert into t_order_maintenance_records_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="orderMaintenanceRecordsId != null">order_maintenance_records_id,</if>
                <if test="orderCode != null">order_code,</if>
                <if test="processingResults != null">processing_results,</if>
                <if test="newFreightUnitPrice != null">new_freight_unit_price,</if>
                <if test="migrateType != null">migrate_type,</if>
                <if test="dataEnable != null">data_enable,</if>
                <if test="companyId != null">company_id,</if>
                <if test="companyName != null">company_name,</if>
                <if test="carrierId != null">carrier_id,</if>
                <if test="carrierName != null">carrier_name,</if>
                <if test="sourceId != null">source_id,</if>
                <if test="sourceName != null">source_name,</if>
                <if test="captainId != null">captain_id,</if>
                <if test="captainName != null">captain_name,</if>
                <if test="remark != null">remark,</if>
                <if test="param1 != null">param1,</if>
                <if test="param2 != null">param2,</if>
                <if test="param3 != null">param3,</if>
                <if test="param4 != null">param4,</if>
                <if test="createTime != null">create_time,</if>
                <if test="createUser != null">create_user,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="updateUser != null">update_user,</if>
                <if test="enable != null">enable,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=INTEGER},</if>
                <if test="orderMaintenanceRecordsId != null">#{orderMaintenanceRecordsId,jdbcType=INTEGER},</if>
                <if test="orderCode != null">#{orderCode,jdbcType=VARCHAR},</if>
                <if test="processingResults != null">#{processingResults,jdbcType=VARCHAR},</if>
                <if test="newFreightUnitPrice != null">#{newFreightUnitPrice,jdbcType=DECIMAL},</if>
                <if test="migrateType != null">#{migrateType,jdbcType=VARCHAR},</if>
                <if test="dataEnable != null">#{dataEnable,jdbcType=TINYINT},</if>
                <if test="companyId != null">#{companyId,jdbcType=INTEGER},</if>
                <if test="companyName != null">#{companyName,jdbcType=VARCHAR},</if>
                <if test="carrierId != null">#{carrierId,jdbcType=INTEGER},</if>
                <if test="carrierName != null">#{carrierName,jdbcType=VARCHAR},</if>
                <if test="sourceId != null">#{sourceId,jdbcType=INTEGER},</if>
                <if test="sourceName != null">#{sourceName,jdbcType=VARCHAR},</if>
                <if test="captainId != null">#{captainId,jdbcType=INTEGER},</if>
                <if test="captainName != null">#{captainName,jdbcType=VARCHAR},</if>
                <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
                <if test="param1 != null">#{param1,jdbcType=VARCHAR},</if>
                <if test="param2 != null">#{param2,jdbcType=VARCHAR},</if>
                <if test="param3 != null">#{param3,jdbcType=VARCHAR},</if>
                <if test="param4 != null">#{param4,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="createUser != null">#{createUser,jdbcType=VARCHAR},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="updateUser != null">#{updateUser,jdbcType=VARCHAR},</if>
                <if test="enable != null">#{enable,jdbcType=TINYINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.lz.model.TOrderMaintenanceRecordsInfo">
        update t_order_maintenance_records_info
        <set>
                <if test="orderMaintenanceRecordsId != null">
                    order_maintenance_records_id = #{orderMaintenanceRecordsId,jdbcType=INTEGER},
                </if>
                <if test="orderCode != null">
                    order_code = #{orderCode,jdbcType=VARCHAR},
                </if>
                <if test="processingResults != null">
                    processing_results = #{processingResults,jdbcType=VARCHAR},
                </if>
                <if test="newFreightUnitPrice != null">
                    new_freight_unit_price = #{newFreightUnitPrice,jdbcType=DECIMAL},
                </if>
                <if test="migrateType != null">
                    migrate_type = #{migrateType,jdbcType=VARCHAR},
                </if>
                <if test="dataEnable != null">
                    data_enable = #{dataEnable,jdbcType=TINYINT},
                </if>
                <if test="companyId != null">
                    company_id = #{companyId,jdbcType=INTEGER},
                </if>
                <if test="companyName != null">
                    company_name = #{companyName,jdbcType=VARCHAR},
                </if>
                <if test="carrierId != null">
                    carrier_id = #{carrierId,jdbcType=INTEGER},
                </if>
                <if test="carrierName != null">
                    carrier_name = #{carrierName,jdbcType=VARCHAR},
                </if>
                <if test="sourceId != null">
                    source_id = #{sourceId,jdbcType=INTEGER},
                </if>
                <if test="sourceName != null">
                    source_name = #{sourceName,jdbcType=VARCHAR},
                </if>
                <if test="captainId != null">
                    captain_id = #{captainId,jdbcType=INTEGER},
                </if>
                <if test="captainName != null">
                    captain_name = #{captainName,jdbcType=VARCHAR},
                </if>
                <if test="remark != null">
                    remark = #{remark,jdbcType=VARCHAR},
                </if>
                <if test="param1 != null">
                    param1 = #{param1,jdbcType=VARCHAR},
                </if>
                <if test="param2 != null">
                    param2 = #{param2,jdbcType=VARCHAR},
                </if>
                <if test="param3 != null">
                    param3 = #{param3,jdbcType=VARCHAR},
                </if>
                <if test="param4 != null">
                    param4 = #{param4,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createUser != null">
                    create_user = #{createUser,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateUser != null">
                    update_user = #{updateUser,jdbcType=VARCHAR},
                </if>
                <if test="enable != null">
                    enable = #{enable,jdbcType=TINYINT},
                </if>
        </set>
        where   id = #{id,jdbcType=INTEGER} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.lz.model.TOrderMaintenanceRecordsInfo">
        update t_order_maintenance_records_info
        set 
            order_maintenance_records_id =  #{orderMaintenanceRecordsId,jdbcType=INTEGER},
            order_code =  #{orderCode,jdbcType=VARCHAR},
            processing_results =  #{processingResults,jdbcType=VARCHAR},
            new_freight_unit_price =  #{newFreightUnitPrice,jdbcType=DECIMAL},
            migrate_type =  #{migrateType,jdbcType=VARCHAR},
            data_enable =  #{dataEnable,jdbcType=TINYINT},
            company_id =  #{companyId,jdbcType=INTEGER},
            company_name =  #{companyName,jdbcType=VARCHAR},
            carrier_id =  #{carrierId,jdbcType=INTEGER},
            carrier_name =  #{carrierName,jdbcType=VARCHAR},
            source_id =  #{sourceId,jdbcType=INTEGER},
            source_name =  #{sourceName,jdbcType=VARCHAR},
            captain_id =  #{captainId,jdbcType=INTEGER},
            captain_name =  #{captainName,jdbcType=VARCHAR},
            remark =  #{remark,jdbcType=VARCHAR},
            param1 =  #{param1,jdbcType=VARCHAR},
            param2 =  #{param2,jdbcType=VARCHAR},
            param3 =  #{param3,jdbcType=VARCHAR},
            param4 =  #{param4,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            create_user =  #{createUser,jdbcType=VARCHAR},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            update_user =  #{updateUser,jdbcType=VARCHAR},
            enable =  #{enable,jdbcType=TINYINT}
        where   id = #{id,jdbcType=INTEGER} 
    </update>
</mapper>
