<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.THxPayBankCardMapper">
    <sql id="Base_Column_List">
    id, acct_no, acct_name, acct_card, bank_code, bank_name, occ_bank_phone, remark,
    `enable`, create_user, create_time, update_user, update_time, bank_code_no
  </sql>


    <select id="selectByEnduserIdAndBankCardId" resultType="com.lz.dto.TZtBankCardDTO">
        select
          tzbbr.id bankBindRelationshipId,
          tzbbr.bind_status,
          tzbc.acct_no cardNo,
          tzbc.acct_name cardOwner,
          tzbc.occ_bank_phone cardOwnerPhone,
          tzbc.id as bankCardId
            from t_enduser_account tea
            left join t_zt_account_open_info tzaoi on tea.account_id = tzaoi.account_id
            left join t_zt_bank_bind_relationship tzbbr on tzaoi.id = tzbbr.account_open_id
            left join t_zt_bank_user tzbu on tzbbr.account_bank_id = tzbu.id
            left join t_zt_bank_card tzbc on tzbu.bank_id = tzbc.id
        where tea.enduser_id = #{enduserId} and tzbc.id = #{bankCardId} and tzbc.enable = 0 and tzbu.enable = 0
    </select>

    <select id="selectOpenRoleBankCardInfo" resultType="com.lz.dto.TZtBankCardDTO">
        select
              tbbr.bank_no,
              tzaoi.partner_acc_id,
              tbbr.bind_status,
              tzbc.id as bankCardId,
              tzbc.acct_no as cardNo,
              tzbc.acct_name as cardOwner,
              tzbc.occ_bank_phone as cardOwnerPhone
        from t_zt_bank_bind_relationship tbbr
        left join t_zt_account_open_info tzaoi on tbbr.account_open_id = tzaoi.id
        left join t_zt_bank_user tzbu on tbbr.account_bank_id = tzbu.id
        left join t_zt_bank_card tzbc on tzbu.bank_id = tzbc.id
        where tzbc.id = #{bankCardId} and tzbu.enable = 0
    </select>

    <select id="selectIsDefault" resultType="com.lz.model.TZtBankCard">
        select tzbc.*
            from t_zt_bank_user tzbu
            left join t_zt_bank_card tzbc on tzbu.bank_id = tzbc.id
            left join t_zt_bank_bind_relationship tbbr on tbbr.account_bank_id = tzbu.id
            where tzbu.account_id = #{accountId}
            and tzbu.is_default = 1
            and tbbr.bind_status = 'BIND'
            and tbbr.request_code = 1
            and tzbc.enable = 0
            and tzbu.enable = 0
    </select>

</mapper>