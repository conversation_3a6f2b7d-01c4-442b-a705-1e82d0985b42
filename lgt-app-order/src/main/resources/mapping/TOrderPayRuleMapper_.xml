<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TOrderPayRuleMapper">

    <select id="selectNodeByPage" resultType="com.lz.vo.TOrderPayRuleVo" parameterType="com.lz.vo.AppOrderSearchVO">
    SELECT
        DISTINCT
        toi.code as orderCode,
        toi.order_business_code,
        toi.end_driver_id,
        eui.real_name,
        eui.phone as realPhone,
        toi.vehicle_id,
        eci.vehicle_number,
        zhtopr.pay_status as zhPayStatus,
        xhtopr.pay_status as xhPayStatus,
        sdtopr.pay_status as sdPayStatus,
        wktopr.pay_status as wkPayStatus,
        tdci.item_value as zhPayStatusType,
        tdci1.item_value as xhPayStatusType,
        tdci2.item_value as sdPayStatusType,
        tdci3.item_value as wkPayStatusType,
        occv1.carriage_fee as userConfirmPaymentAmount,
        toi.goods_name,
        toi.from_name,
        toi.end_name,
        toi.line_name,
        ifnull(occv.carriage_fee, toi.estimate_total_fee) as originalCarriageFee,
        IFNULL(occv.dispatch_fee,0.00) as originalDispatchFee,
        IFNULL(toi.current_carriage_unit_price,0.00) as currentCarriageUnitPrice,
        IFNULL(toi.user_confirm_service_fee,0.00) as userConfirmServiceFee,
        occv1.dispatch_fee as dispatchFee,
        IFNULL(zhtopr.payment_fee,0.00) as zhPaymentFee,
        zhtopr.pay_time as zhPayTime,
        IFNULL(xhtopr.payment_fee,0.00) as xhPaymentFee,
        xhtopr.pay_time as xhPayTime,
        IFNULL(sdtopr.payment_fee,0.00) as sdPaymentFee,
        sdtopr.pay_time as sdPayTime,
        IFNULL(wktopr.payment_fee,0.00) as wkPaymentFee,
        wktopr.pay_time as wkPayTime,
        zhtopr.withdraw_time as zhWithdrawTime,
        xhtopr.withdraw_time as xhWithdrawTime,
        sdtopr.withdraw_time as sdWithdrawTime,
        wktopr.withdraw_time as wkWithdrawTime,
        IFNULL(zhtopr.rule_payment_fee,0.00) as zhRulePaymentFee,
        IFNULL(xhtopr.rule_payment_fee,0.00) as xhRulePaymentFee,
        IFNULL(sdtopr.rule_payment_fee,0.00) as sdRulePaymentFee,
        IFNULL(wktopr.rule_payment_fee,0.00) as wkRulePaymentFee,
        IFNULL(occv2.dispatch_fee,0.00) as payDispatchFee,
        IFNULL(zhtopr.payment_fee,0.00)+ifnull(xhtopr.payment_fee,0.00)+ifnull(sdtopr.payment_fee,0.00)+ifnull(wktopr.payment_fee,0.00) as yzfPaymentFee,
        IFNULL(dzftopr.surplus_payment_fee,0.00) as surplusPaymentFee,
        toi.service_fee,
        toi.user_confirm_service_fee,
        toi.company_id,
        toi.carrier_id,
        toi.contract_status,
        dci2.item_value as contractStatusStr,
        ca.carrier_name,
        com.company_name,
        tcp.project_name,
        concat("[",toi.goods_name,"]",toi.line_name) as sourceName,
        IFNULL( case wktopr.pay_status when 'PACKPAID' then wktopr.pay_dispatch_fee else 0 end,0 ) as yzfdispatchFee,
        IFNULL( case toi.order_execute_status when 'M100' then 0 when 'M060' then 0 else toi.dispatch_fee end, 0 )  as wzfdispatchFee,
        toi.update_time, wktopr.param1 paymentPlatforms
    FROM
        (SELECT * FROM t_order_info WHERE pay_method in ('NODEPAYPROPORTION','NODEPAYFIXED')) toi
        LEFT JOIN t_carrier_info ca ON ca.id = toi.carrier_id AND ca.enable = 0
        LEFT JOIN t_company_info com ON com.id = toi.company_id AND com.enable = 0
        LEFT JOIN t_company_project tcp ON tcp.id = toi.company_project_id
        LEFT JOIN t_end_user_info eui ON eui.id = toi.end_driver_id AND eui.enable = 0
        LEFT JOIN t_end_car_info eci ON eci.id = toi.vehicle_id AND eci.enable = 0
        LEFT JOIN t_order_cast_changes occv ON toi.`code` = occv.order_code AND occv.user_oper = 'Invoice'
        LEFT JOIN t_order_cast_changes occv1 ON occv1.id = (
        SELECT
        MAX(tos.id)
        FROM t_order_cast_changes tos
        WHERE  tos.user_oper = 'CollectionOrder' AND  tos.order_code = toi.`code`
        )
        LEFT JOIN t_order_cast_changes occv2 ON toi.`code` = occv2.order_code AND occv2.user_oper = 'WKPAYNODE' and occv2.trade_type = 'RZ'
        LEFT JOIN t_order_state tos ON tos.id = (
        SELECT
        MAX(tosa.id)
        FROM t_order_state tosa
        WHERE  tosa.state_node_value IN ('S1300', 'S1301', 'S1302') AND  tosa.order_code = toi.`code`
        )
        LEFT JOIN t_order_pay_rule zhtopr ON zhtopr.order_code = toi.code AND zhtopr.pay_node_type = 'ZHPAYNODE' AND zhtopr.enable = 0
        LEFT JOIN t_order_pay_rule xhtopr ON xhtopr.order_code = toi.code AND xhtopr.pay_node_type = 'XHPAYNODE' AND xhtopr.enable = 0
        LEFT JOIN t_order_pay_rule sdtopr ON sdtopr.order_code = toi.code AND sdtopr.pay_node_type = 'SDPAYNODE' AND sdtopr.enable = 0
        LEFT JOIN t_order_pay_rule wktopr ON wktopr.order_code = toi.code AND wktopr.pay_node_type = 'WKPAYNODE' AND wktopr.enable = 0
        LEFT JOIN t_order_pay_rule dzftopr ON dzftopr.order_code = toi.code AND dzftopr.pay_status in ('PACKUNPAID', 'PACKEDHANDEL')AND dzftopr.enable = 0
        left join t_dic_cat_item tdci on zhtopr.pay_status = tdci.item_code
        left join t_dic_cat_item tdci1 on xhtopr.pay_status = tdci1.item_code
        left join t_dic_cat_item tdci2 on sdtopr.pay_status = tdci2.item_code
        left join t_dic_cat_item tdci3 on wktopr.pay_status = tdci3.item_code
        LEFT JOIN t_dic_cat_item dci2 on dci2.item_code = toi.contract_status
    WHERE toi.pay_method in( 'NODEPAYPROPORTION','NODEPAYFIXED') and toi.order_execute_status not in ('M-20','M-10','M000','M010','M020') and toi.enable = 0
        <if test="orderIndex != null">
            AND toi.param4 IN
            <foreach collection="orderIndex" item="oix" index="index" open="(" separator="," close=")">
                #{oix}
            </foreach>
        </if>
        <if test="carrierId != null">
            AND toi.carrier_id = #{carrierId}
        </if>
        <if test="companyId != null">
            AND toi.company_id = #{companyId}
        </if>
        <if test="companyIds != null">
            AND toi.company_id in
            <foreach collection="companyIds" item="companyId" index="item" open="(" close=")" separator=",">
                #{companyId}
            </foreach>
        </if>
        <if test="bizCode != null">
            AND toi.order_business_code like concat('%', #{bizCode}, '%')
        </if>
        <if test="driverName != null">
            AND eui.real_name like concat('%', #{driverName}, '%')
        </if>
        <if test="driverPhone != null">
            AND eui.phone like concat('%', #{driverPhone}, '%')
        </if>
        <if test="fromName != null">
            AND toi.from_name LIKE CONCAT('%',  #{fromName}, '%')
        </if>
        <if test="endName != null">
            AND toi.end_name LIKE CONCAT('%',  #{endName}, '%')
        </if>
        <if test="projectId != null">
            and toi.company_project_id = #{projectId}
        </if>
        <if test="lineGoodsId != null">
            AND toi.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
            and toi.line_goods_rel_id in
            <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                #{lineGoodsRelId}
            </foreach>
        </if>
        <if test="goodsId != null">
            and toi.goods_id = #{goodsId}
        </if>
        <if test="searchAgentId != null">
            AND toi.end_agent_id = #{searchAgentId}
        </if>
        <if test="agentId != null">
            AND toi.agent_id = #{agentId}
        </if>
        <if test="carOwnerId != null">
            AND toi.end_car_owner_id = #{carOwnerId}
        </if>
        <if test="orderState != null ">
            AND toi.order_execute_status IN
            <foreach collection="orderState" item="orderExecuteStatus" index="item" open="(" close=")" separator=",">
                #{orderExecuteStatus}
            </foreach>
        </if>
        <if test="payState != null">
            AND toi.order_pay_status IN
            <foreach collection="payState" item="orderPayStatus" index="item" open="(" close=")" separator=",">
                #{orderPayStatus}
            </foreach>
        </if>
        <if test="fhsTime != null">
            AND toi.deliver_order_time >= #{fhsTime}
        </if>
        <if test="fheTime != null">
            AND toi.deliver_order_time &lt;= #{fheTime}
        </if>
        <if test="shsTime != null">
            AND toi.receive_order_time >= #{shsTime}
        </if>
        <if test="sheTime != null">
            AND toi.receive_order_time &lt;= #{sheTime}
        </if>
        <if test="fhbdsTime != null">
            AND toi.deliver_weight_notes_time >= #{fhbdsTime}
        </if>
        <if test="fhbdeTime != null">
            AND toi.deliver_weight_notes_time &lt;= #{fhbdeTime}
        </if>
        <if test="shbdsTime != null">
            AND toi.receive_weight_notes_time >= #{shbdsTime}
        </if>
        <if test="shbdeTime != null">
            AND toi.receive_weight_notes_time &lt;= #{shbdeTime}
        </if>

        <if test="zhPayTimeStr != null">
            AND zhtopr.pay_time >= #{zhPayTimeStr}
        </if>
        <if test="zhPayeTimeEnd != null">
            AND zhtopr.pay_time &lt;= #{zhPayeTimeEnd}
        </if>
        <if test="xhPayTimeStr != null">
            AND xhtopr.pay_time >= #{xhPayTimeStr}
        </if>
        <if test="xhPayTimeEnd != null">
            AND xhtopr.pay_time &lt;= #{xhPayTimeEnd}
        </if>
        <if test="sdPayTimeStr != null">
            AND sdtopr.pay_time >= #{sdPayTimeStr}
        </if>
        <if test="sdPayTimeEnd != null">
            AND sdtopr.pay_time &lt;= #{sdPayTimeEnd}
        </if>
        <if test="wkPaysTimeStr != null">
            AND wktopr.pay_time >= #{wkPaysTimeStr}
        </if>
        <if test="wkPayTimeEnd != null">
            AND wktopr.pay_time &lt;= #{wkPayTimeEnd}
        </if>

        <if test="zhWithdrawTimeStr != null">
            AND zhtopr.withdraw_time >= #{zhWithdrawTimeStr}
        </if>
        <if test="zhWithdrawTimeEnd != null">
            AND zhtopr.withdraw_time &lt;= #{zhWithdrawTimeEnd}
        </if>
        <if test="xhWithdrawTimeStr != null">
            AND xhtopr.withdraw_time >= #{xhWithdrawTimeStr}
        </if>
        <if test="xhWithdrawTimeEnd != null">
            AND xhtopr.withdraw_time &lt;= #{xhWithdrawTimeEnd}
        </if>
        <if test="sdWithdrawTimeStr != null">
            AND sdtopr.withdraw_time >= #{sdWithdrawTimeStr}
        </if>
        <if test="sdWithdrawTimeEnd != null">
            AND sdtopr.withdraw_time &lt;= #{sdWithdrawTimeEnd}
        </if>
        <if test="wkWithdrawTimeStr != null">
            AND wktopr.withdraw_time >= #{wkWithdrawTimeStr}
        </if>
        <if test="wkWithdrawTimeEnd != null">
            AND wktopr.withdraw_time &lt;= #{wkWithdrawTimeEnd}
        </if>

        <if test="companyId != null">
            AND toi.company_id = #{companyId}
        </if>
        <if test="licensePlate != null">
            AND eci.vehicle_number like concat('%', #{licensePlate}, '%')
        </if>
        <if test="codeList != null and codeList.size != 0 ">
            AND toi.code IN
            <foreach collection="codeList" item="orderCode" index="item" open="(" close=")" separator=",">
                #{orderCode}
            </foreach>
        </if>
        <if test="payNodeType == 'ZHPAYNODE'">
            AND zhtopr.pay_node_type = 'ZHPAYNODE' AND zhtopr.pay_status = 'PACKUNPAID'
        </if>
        <if test="payNodeType == 'XHPAYNODE'">
            AND xhtopr.pay_node_type = 'XHPAYNODE' AND xhtopr.pay_status  = 'PACKUNPAID'
        </if>
        <if test="payNodeType == 'SDPAYNODE'">
            AND sdtopr.pay_node_type = 'SDPAYNODE' AND sdtopr.pay_status  = 'PACKUNPAID'
        </if>
        <if test="payNodeType == 'WKPAYNODE'">
            AND wktopr.pay_node_type = 'WKPAYNODE' AND wktopr.pay_status  = 'PACKUNPAID'
        </if>
        <if test="payStatus != null">
            AND (zhtopr.pay_status = #{payStatus} OR xhtopr.pay_status  = #{payStatus} OR sdtopr.pay_status  = #{payStatus} OR wktopr.pay_status  = #{payStatus} )
        </if>
        order by toi.update_time desc
    </select>


    <select id="selectNodeSum" resultType="com.lz.dto.SelectNodeOrderSumDTO" parameterType="com.lz.vo.AppOrderSearchVO">
        select
        IFNULL(SUM(IFNULL(IFNULL(a.payment_feez,0.00)+ifnull(a.payment_feex,0.00)+ifnull(a.payment_fees,0.00)+ifnull(a.payment_feew,0.00),0.00)), 0.00) as yzfPaymentFeeSum,
        IFNULL(SUM(IFNULL( a.surplus_payment_fee ,0.00)), 0.00) as surplusPaymentFeeSum,<!-- 未支付运费 -->
        IFNULL( SUM( IFNULL( case a.pay_status when 'PACKPAID' then a.pay_dispatch_fee else 0 end, 0 ) ), 0 ) as yzfdispatchFeeSum,<!-- 已支付调度费 -->
        IFNULL( SUM( IFNULL( case a.order_execute_status when 'M100' then 0 when 'M060' then 0 else a.dispatch_fee end, 0 ) ), 0 ) as wzfdispatchFeeSum<!-- 未支付调度费 -->
        from (
            SELECT
              DISTINCT
            toi.order_business_code,
            zhtopr.payment_fee as payment_feez,
            xhtopr.payment_fee as payment_feex,
            sdtopr.payment_fee as payment_fees,
            wktopr.payment_fee as payment_feew,
            dzftopr.surplus_payment_fee,
            wktopr.pay_status,
            wktopr.pay_dispatch_fee,
            toi.order_execute_status,
            toi.dispatch_fee
            FROM
            (SELECT * FROM t_order_info WHERE pay_method in ('NODEPAYPROPORTION','NODEPAYFIXED')) toi
            LEFT JOIN t_carrier_info ca ON ca.id = toi.carrier_id AND ca.enable = 0
            LEFT JOIN t_end_user_info eui ON eui.id = toi.end_driver_id AND eui.enable = 0
            LEFT JOIN t_end_car_info eci ON eci.id = toi.vehicle_id AND eci.enable = 0
            LEFT JOIN t_order_cast_changes occv ON toi.`code` = occv.order_code AND occv.user_oper = 'Invoice'
            LEFT JOIN t_order_cast_changes occv1 ON occv1.id = (
            SELECT
            MAX(tos.id)
            FROM t_order_cast_changes tos
            WHERE  tos.user_oper = 'CollectionOrder' AND  tos.order_code = toi.`code`
            )
            LEFT JOIN t_order_state tos ON tos.id = (
            SELECT
            MAX(tosa.id)
            FROM t_order_state tosa
            WHERE  tosa.state_node_value IN ('S1300', 'S1301', 'S1302') AND  tosa.order_code = toi.`code`
            )
            LEFT JOIN t_order_pay_rule zhtopr ON zhtopr.order_code = toi.code AND zhtopr.pay_node_type = 'ZHPAYNODE' AND zhtopr.enable = 0
            LEFT JOIN t_order_pay_rule xhtopr ON xhtopr.order_code = toi.code AND xhtopr.pay_node_type = 'XHPAYNODE' AND xhtopr.enable = 0
            LEFT JOIN t_order_pay_rule sdtopr ON sdtopr.order_code = toi.code AND sdtopr.pay_node_type = 'SDPAYNODE' AND sdtopr.enable = 0
            LEFT JOIN t_order_pay_rule wktopr ON wktopr.order_code = toi.code AND wktopr.pay_node_type = 'WKPAYNODE' AND wktopr.enable = 0
            LEFT JOIN t_order_pay_rule dzftopr ON dzftopr.order_code = toi.code AND dzftopr.pay_status in ('PACKUNPAID', 'PACKEDHANDEL')AND dzftopr.enable = 0
            left join t_dic_cat_item tdci on zhtopr.pay_status = tdci.item_code
            left join t_dic_cat_item tdci1 on xhtopr.pay_status = tdci1.item_code
            left join t_dic_cat_item tdci2 on sdtopr.pay_status = tdci2.item_code
            left join t_dic_cat_item tdci3 on wktopr.pay_status = tdci3.item_code
            WHERE toi.pay_method in( 'NODEPAYPROPORTION','NODEPAYFIXED') and toi.order_execute_status not in ('M-20','M-10','M000','M010','M020') and toi.enable = 0
            <if test="orderIndex != null">
                AND toi.param4 IN
                <foreach collection="orderIndex" item="oix" index="index" open="(" separator="," close=")">
                    #{oix}
                </foreach>
            </if>
            <if test="carrierId != null">
                AND toi.carrier_id = #{carrierId}
            </if>
            <if test="companyId != null">
                AND toi.company_id = #{companyId}
            </if>
            <if test="companyIds != null">
                AND toi.company_id in
                <foreach collection="companyIds" item="companyId" index="item" open="(" close=")" separator=",">
                    #{companyId}
                </foreach>
            </if>
            <if test="bizCode != null">
                AND toi.order_business_code like concat('%', #{bizCode}, '%')
            </if>
            <if test="driverName != null">
                AND eui.real_name like concat('%', #{driverName}, '%')
            </if>
            <if test="driverPhone != null">
                AND eui.phone like concat('%', #{driverPhone}, '%')
            </if>
            <if test="fromName != null">
                AND toi.from_name LIKE CONCAT('%',  #{fromName}, '%')
            </if>
            <if test="endName != null">
                AND toi.end_name LIKE CONCAT('%',  #{endName}, '%')
            </if>
            <if test="projectId != null">
                and toi.company_project_id = #{projectId}
            </if>
            <if test="lineGoodsId != null">
                AND toi.line_goods_rel_id = #{lineGoodsId}
            </if>
            <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
                and toi.line_goods_rel_id in
                <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                    #{lineGoodsRelId}
                </foreach>
            </if>
            <if test="goodsId != null">
                and toi.goods_id = #{goodsId}
            </if>
            <if test="searchAgentId != null">
                AND toi.end_agent_id = #{searchAgentId}
            </if>
            <if test="agentId != null">
                AND toi.agent_id = #{agentId}
            </if>
            <if test="carOwnerId != null">
                AND toi.end_car_owner_id = #{carOwnerId}
            </if>
            <if test="orderState != null ">
                AND toi.order_execute_status IN
                <foreach collection="orderState" item="orderExecuteStatus" index="item" open="(" close=")" separator=",">
                    #{orderExecuteStatus}
                </foreach>
            </if>
            <if test="payState != null">
                AND toi.order_pay_status IN
                <foreach collection="payState" item="orderPayStatus" index="item" open="(" close=")" separator=",">
                    #{orderPayStatus}
                </foreach>
            </if>
            <if test="fhsTime != null">
                AND toi.deliver_order_time >= #{fhsTime}
            </if>
            <if test="fheTime != null">
                AND toi.deliver_order_time &lt;= #{fheTime}
            </if>
            <if test="shsTime != null">
                AND toi.receive_order_time >= #{shsTime}
            </if>
            <if test="sheTime != null">
                AND toi.receive_order_time &lt;= #{sheTime}
            </if>
            <if test="fhbdsTime != null">
                AND toi.deliver_weight_notes_time >= #{fhbdsTime}
            </if>
            <if test="fhbdeTime != null">
                AND toi.deliver_weight_notes_time &lt;= #{fhbdeTime}
            </if>
            <if test="shbdsTime != null">
                AND toi.receive_weight_notes_time >= #{shbdsTime}
            </if>
            <if test="shbdeTime != null">
                AND toi.receive_weight_notes_time &lt;= #{shbdeTime}
            </if>

            <if test="zhPayTimeStr != null">
                AND zhtopr.pay_time >= #{zhPayTimeStr}
            </if>
            <if test="zhPayeTimeEnd != null">
                AND zhtopr.pay_time &lt;= #{zhPayeTimeEnd}
            </if>
            <if test="xhPayTimeStr != null">
                AND xhtopr.pay_time >= #{xhPayTimeStr}
            </if>
            <if test="xhPayTimeEnd != null">
                AND xhtopr.pay_time &lt;= #{xhPayTimeEnd}
            </if>
            <if test="sdPayTimeStr != null">
                AND sdtopr.pay_time >= #{sdPayTimeStr}
            </if>
            <if test="sdPayTimeEnd != null">
                AND sdtopr.pay_time &lt;= #{sdPayTimeEnd}
            </if>
            <if test="wkPaysTimeStr != null">
                AND wktopr.pay_time >= #{wkPaysTimeStr}
            </if>
            <if test="wkPayTimeEnd != null">
                AND wktopr.pay_time &lt;= #{wkPayTimeEnd}
            </if>

            <if test="zhWithdrawTimeStr != null">
                AND zhtopr.withdraw_time >= #{zhWithdrawTimeStr}
            </if>
            <if test="zhWithdrawTimeEnd != null">
                AND zhtopr.withdraw_time &lt;= #{zhWithdrawTimeEnd}
            </if>
            <if test="xhWithdrawTimeStr != null">
                AND xhtopr.withdraw_time >= #{xhWithdrawTimeStr}
            </if>
            <if test="xhWithdrawTimeEnd != null">
                AND xhtopr.withdraw_time &lt;= #{xhWithdrawTimeEnd}
            </if>
            <if test="sdWithdrawTimeStr != null">
                AND sdtopr.withdraw_time >= #{sdWithdrawTimeStr}
            </if>
            <if test="sdWithdrawTimeEnd != null">
                AND sdtopr.withdraw_time &lt;= #{sdWithdrawTimeEnd}
            </if>
            <if test="wkWithdrawTimeStr != null">
                AND wktopr.withdraw_time >= #{wkWithdrawTimeStr}
            </if>
            <if test="wkWithdrawTimeEnd != null">
                AND wktopr.withdraw_time &lt;= #{wkWithdrawTimeEnd}
            </if>

            <if test="companyId != null">
                AND toi.company_id = #{companyId}
            </if>
            <if test="licensePlate != null">
                AND eci.vehicle_number like concat('%', #{licensePlate}, '%')
            </if>
            <if test="codeList != null and codeList.size != 0 ">
                AND toi.code IN
                <foreach collection="codeList" item="orderCode" index="item" open="(" close=")" separator=",">
                    #{orderCode}
                </foreach>
            </if>
            <if test="payNodeType == 'ZHPAYNODE'">
                AND zhtopr.pay_node_type = 'ZHPAYNODE' AND zhtopr.pay_status = 'PACKUNPAID'
            </if>
            <if test="payNodeType == 'XHPAYNODE'">
                AND xhtopr.pay_node_type = 'XHPAYNODE' AND xhtopr.pay_status  = 'PACKUNPAID'
            </if>
            <if test="payNodeType == 'SDPAYNODE'">
                AND sdtopr.pay_node_type = 'SDPAYNODE' AND sdtopr.pay_status  = 'PACKUNPAID'
            </if>
            <if test="payNodeType == 'WKPAYNODE'">
                AND wktopr.pay_node_type = 'WKPAYNODE' AND wktopr.pay_status  = 'PACKUNPAID'
            </if>
            <if test="payStatus != null">
                AND (zhtopr.pay_status = #{payStatus} OR xhtopr.pay_status  = #{payStatus} OR sdtopr.pay_status  = #{payStatus} OR wktopr.pay_status  = #{payStatus} )
            </if>
        ) a
    </select>
    <select id="selectNodeOrderPayDetailed" parameterType="java.lang.String" resultType="com.lz.vo.TOrderNodeDetailVO">
        SELECT distinct
            (select t1.rule_payment_fee from t_order_pay_rule t1 where t1.pay_node_type = 'ZHPAYNODE' and order_code = #{code} and t1.enable = 0) as zhRulePaymentFee,
            (select t1.rule_payment_fee from t_order_pay_rule t1 where t1.pay_node_type = 'XHPAYNODE' and order_code =  #{code} and t1.enable = 0) as xhRulePaymentFee,
            (select t1.rule_payment_fee from t_order_pay_rule t1 where t1.pay_node_type = 'SDPAYNODE' and order_code =  #{code} and t1.enable = 0) as sdRulePaymentFee,
            (select t1.rule_payment_fee from t_order_pay_rule t1 where t1.pay_node_type = 'WKPAYNODE' and order_code =  #{code} and t1.enable = 0) as wkRulePaymentFee,
            (select t1.payment_fee from t_order_pay_rule t1 where t1.pay_node_type = 'ZHPAYNODE' and order_code =  #{code} and t1.enable = 0) as zhPaymentFee,
            (select t1.payment_fee from t_order_pay_rule t1 where t1.pay_node_type = 'XHPAYNODE' and order_code = #{code} and t1.enable = 0) as xhPaymentFee,
            (select t1.payment_fee from t_order_pay_rule t1 where t1.pay_node_type = 'SDPAYNODE' and order_code = #{code} and t1.enable = 0) as sdPaymentFee,
            (select t1.payment_fee from t_order_pay_rule t1 where t1.pay_node_type = 'WKPAYNODE' and order_code = #{code} and t1.enable = 0) as wkPaymentFee,
            (select t1.remark from t_order_pay_rule t1 where t1.pay_node_type = 'ZHPAYNODE' and order_code = #{code} and t1.enable = 0) as zhRemark,
            (select t1.remark from t_order_pay_rule t1 where t1.pay_node_type = 'XHPAYNODE' and order_code = #{code} and t1.enable = 0) as xhRemark,
            (select t1.remark from t_order_pay_rule t1 where t1.pay_node_type = 'SDPAYNODE' and order_code = #{code} and t1.enable = 0) as sdRemark,
            (select t1.remark from t_order_pay_rule t1 where t1.pay_node_type = 'WKPAYNODE' and order_code = #{code} and t1.enable = 0) as wkRemark,
            (select t1.pay_time from t_order_pay_rule t1 where t1.pay_node_type = 'ZHPAYNODE' and order_code = #{code} and t1.enable = 0) as zhupdateTime,
            (select t1.pay_time from t_order_pay_rule t1 where t1.pay_node_type = 'XHPAYNODE' and order_code = #{code} and t1.enable = 0) as xhupdateTime,
            (select t1.pay_time from t_order_pay_rule t1 where t1.pay_node_type = 'SDPAYNODE' and order_code = #{code} and t1.enable = 0) as sdupdateTime,
            (select t1.pay_time from t_order_pay_rule t1 where t1.pay_node_type = 'WKPAYNODE' and order_code = #{code} and t1.enable = 0) as wkupdateTime
        FROM
            t_order_pay_rule

    </select>
    <select id="selectByCodeAndStatus" parameterType="java.lang.String" resultType="com.lz.model.TOrderPayRule">
        SELECT * FROM t_order_pay_rule WHERE order_code = #{code} AND `pay_status` = 'PACKUNPAID' and enable = 0
    </select>

    <select id="selectByOrderCodeAndType" parameterType="com.lz.model.TOrderPayRule" resultType="com.lz.model.TOrderPayRule">
        SELECT * FROM t_order_pay_rule WHERE
        order_code = #{orderCode}
        <if test="payNodeType != null">
            AND pay_node_type = #{payNodeType}
        </if>
        <if test="payStatus != null">
            AND pay_status = #{payStatus}
        </if>
        and enable = 0
    </select>

    <select id="selectByOrderCodeAndTypes" parameterType="com.lz.model.TOrderPayRule" resultType="com.lz.model.TOrderPayRule">
        SELECT * FROM t_order_pay_rule WHERE
        order_code = #{orderCode}
        AND pay_node_type  in
        <foreach collection="payNodeTypes" item="payNodeType" index="index" open="(" separator="," close=")">
            #{payNodeType}
        </foreach>
        <if test="payStatus != null">
            AND pay_status = #{payStatus}
        </if>
        and enable = 0
    </select>

    <select id="selectSumFee" parameterType="java.lang.String" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(payment_fee), 0)  FROM t_order_pay_rule WHERE order_code = #{orderCode} and  `pay_status` = 'PACKPAID'
    </select>

    <select id="selectPayRuleByOrderCodeAndType" resultType="com.lz.model.TOrderPayRule">
        SELECT
               *
        FROM
             t_order_pay_rule
        WHERE
            order_code = #{orderCode} AND pay_node_type = #{payNodeType}
            and enable = 0
    </select>

    <select id="selectOrderPayRuleByOrderCode" resultType="com.lz.dto.TOrderPayRuleNodeTypeSortDTO">
        select
            pay_node_type, pay_status,
               case
                when pay_node_type = 'ZHPAYNODE' then 1
                when pay_node_type = 'XHPAYNODE' then 2
                when pay_node_type = 'SDPAYNODE' then 3
                when pay_node_type = 'WKPAYNODE' then 4
                   else 0
                end sort
        from t_order_pay_rule
        where order_code = #{orderCode} and enable = 0
    </select>

    <select id="selectPaidNode" resultType="com.lz.model.TOrderPayRule">
        select
            <include refid="Base_Column_List" />
        from t_order_pay_rule
        where pay_status = 'PACKPAID' and enable = 0 and order_code = #{orderCode}
        order by id desc
        limit 1
    </select>

    <update id="updatePaymentPlatformsByOrderCode">
        update
            t_order_pay_rule
                set param1 = #{paymentPlatforms},
                    update_time = now()
            where order_code = #{orderCode}
    </update>

    <delete id="deleteByOrderCode">
        delete from t_order_pay_rule where order_code = #{orderCode}
    </delete>

    <select id="selectByOrderCodeAndPayNodeType" parameterType="com.lz.model.TOrderPayRule" resultType="com.lz.model.TOrderPayRule">
        SELECT * FROM t_order_pay_rule WHERE
        order_code = #{orderCode}
        <if test="payNodeType != null">
            AND pay_node_type = #{payNodeType}
        </if>
        and enable = 0
    </select>

    <select id="selectAllNode" resultType="java.lang.String">
        select
            pay_node_type
        from t_order_pay_rule
        where order_code = #{orderCode}
        order by id
    </select>


</mapper>