<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TOrderInfoSearchMapper">

    <!--运单审核-->
    <select id="selectByYdshPage_COUNT" parameterType="com.lz.vo.AppOrderSearchVO" resultType="java.lang.Long">
        select
            count(1)
        FROM
        t_order_info toi
        LEFT JOIN t_account ta ON toi.deliver_order_user_id = ta.id
        LEFT JOIN t_account ta1 ON toi.receive_order_user_id = ta1.id
        LEFT JOIN t_company_info tcci ON toi.company_id = tcci.id AND tcci.`enable` = 0
        LEFT JOIN t_company_project tcp ON tcp.id = toi.company_project_id AND tcp.`enable` = 0
        LEFT JOIN t_line_goods_rel lgr ON lgr.id = toi.line_goods_rel_id AND lgr.`enable` = 0
        LEFT JOIN t_carrier_info tcpi ON toi.carrier_id = tcpi.id AND tcpi.`enable` = 0
        LEFT JOIN t_end_user_info euis ON toi.end_agent_id = euis.id AND euis.`enable` = 0
        LEFT JOIN t_end_user_info euic ON toi.end_car_owner_id = euic.id AND euic.`enable` = 0
        LEFT JOIN t_end_user_info agent on toi.agent_id = agent.id AND agent.`enable` = 0
        LEFT JOIN t_end_user_info eui ON toi.end_driver_id = eui.id AND eui.`enable` = 0
        LEFT JOIN t_end_car_info eci ON toi.vehicle_id = eci.id AND eci.`enable` = 0
        LEFT JOIN t_order_state_node osn ON toi.order_execute_status = osn.`code`
        LEFT JOIN t_order_state_node tosn ON toi.order_pay_status = tosn.`code`
        LEFT JOIN t_order_cast_changes occ ON toi.`code` = occ.order_code AND occ.data_enable = 1
        LEFT JOIN t_dic_cat_item tdci ON tdci.item_code = occ.capital_transfer_type
        left join t_dic_cat_item tdci2 ON tdci2.item_code = lgr.capital_transfer_type
        LEFT JOIN t_order_audit_log oal ON oal.order_code = toi.code
        LEFT JOIN t_order_state tosup ON tosup.state_node_value = 'S0302' AND  tosup.order_code = toi.`code` and tosup.`enable` = 0
        LEFT JOIN t_order_state tosdown ON tosdown.state_node_value = 'S0402' AND  tosdown.order_code = toi.`code` and tosdown.`enable` = 0
        LEFT JOIN t_dic_cat_item dci ON dci.item_code = oal.audit_status
        left join t_order_info_detail toid on toi.id = toid.order_id and toid.`enable` = 0
        left join t_order_info_weight toiw on toi.id = toiw.order_id
        <where>
            <if test="orderPackPage">
                AND toi.pack_status = 0
            </if>
            <if test="timeOfPaymentStart != null and timeOfPaymentStart != ''">
                and toi.order_finish_time >= #{timeOfPaymentStart}
            </if>
            <if test="timeOfPaymentEnd != null and timeOfPaymentEnd != ''">
                and toi.order_finish_time <![CDATA[<= ]]> #{timeOfPaymentEnd}
            </if>
            <if test="!isAdmin">
                <if test="!org">
                    <if test="lineGoodsRuleIds != null and lineGoodsRuleIds.size > 0">
                        and toi.line_goods_rel_id IN
                        <foreach collection="lineGoodsRuleIds" item="lgr" index="item" open="(" close=")" separator=",">
                            #{lgr}
                        </foreach>
                    </if>
                </if>
            </if>
            <!-- 支付审核员收单员是按照已分配的线路查询但是要是 t_line_goods_user_rel 的企业 -->
            <if test="companyIds != null">
                AND toi.company_id IN
                <foreach collection="companyIds" index="index" item="cid" open="(" separator="," close=")">
                    #{cid}
                </foreach>
            </if>
            <if test="companyId != null">
                AND toi.company_id = #{companyId}
            </if>
            <if test="payState != null">
                AND toi.order_pay_status IN
                <foreach collection="payState" item="pay" index="item" open="(" close=")" separator=",">
                    #{pay}
                </foreach>
            </if>
            <if test="status != null">
                AND toi.order_execute_status IN
                <foreach collection="status" index="index" item="st" open="(" close=")" separator=",">
                    #{st}
                </foreach>
            </if>
            <if test="vehicleNumber != null">
                AND eci.vehicle_number LIKE CONCAT('%',  #{vehicleNumber}, '%')
            </if>
            <if test="fromName != null">
                AND toi.from_name LIKE CONCAT('%',  #{fromName}, '%')
            </if>
            <if test="endName != null">
                AND toi.end_name LIKE CONCAT('%',  #{endName}, '%')
            </if>
            <if test="auditRemark != null">
                AND oal.audit_remark like concat('%', #{auditRemark}, '%')
            </if>
            <if test="auditStatus != null and auditStatus != 'NO'">
                AND oal.audit_status = #{auditStatus}
            </if>
            <if test="auditStatus == 'NO'">
                AND ISNULL(oal.audit_status)
            </if>
            <if test="auditsTime != null ">
                AND oal.audit_time >= #{auditsTime}
                AND oal.enable = 0
            </if>
            <if test="auditeTime != null">
                AND oal.audit_time &lt;= #{auditeTime}
                AND oal.enable = 0
            </if>
            <if test="carrierId != null">
                AND toi.carrier_id = #{carrierId}
            </if>
            <if test="lineGoodsId != null">
                AND toi.line_goods_rel_id = #{lineGoodsId}
            </if>
            <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
                and toi.line_goods_rel_id in
                <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                    #{lineGoodsRelId}
                </foreach>
            </if>
            <if test="carOwnerId != null">
                and toi.end_car_owner_id = #{carOwnerId}
            </if>
            <if test="captainId != null">
                and toi.end_car_owner_id = #{captainId}
            </if>
            <if test="goodsId != null">
                and toi.goods_id = #{goodsId}
            </if>
            <if test="companyEntrust != null">
                and toi.company_entrust like concat('%', #{companyEntrust}, '%')
            </if>
            <if test="companyClient != null">
                and toi.company_client = #{companyClient}
            </if>
            <if test="projectId != null">
                and toi.company_project_id = #{projectId}
            </if>
            <if test="endAgentId != null">
                and toi.end_agent_id = #{endAgentId}
            </if>
            <if test="agentId != null">
                AND toi.agent_id = #{agentId}
            </if>
            <if test="informationStation != null">
                AND euis.org_name = #{informationStation}
            </if>
            <if test="bizCode != null">
                AND toi.order_business_code like concat('%', #{bizCode}, '%')
            </if>
            <if test="driverName != null">
                AND eui.real_name like concat('%', #{driverName}, '%')
            </if>
            <if test="driverPhone != null">
                AND eui.phone like concat('%', #{driverPhone}, '%')
            </if>
            <if test="licensePlate != null">
                AND eci.vehicle_number like concat('%', #{licensePlate}, '%')
            </if>
            <if test="carAuditStatus != null">
                AND eci.audit_status = #{carAuditStatus}
            </if>
            <if test="origin != null">
                AND toi.from_name like concat('%', #{origin}, '%')
            </if>
            <if test="terminus != null">
                AND toi.end_name like concat('%', #{terminus}, '%')
            </if>
            <if test="fhsTime != null">
                AND toi.deliver_order_time >= #{fhsTime}
            </if>
            <if test="fheTime != null">
                AND toi.deliver_order_time &lt;= #{fheTime}
            </if>
            <if test="shsTime != null">
                AND toi.receive_order_time >= #{shsTime}
            </if>
            <if test="sheTime != null">
                AND toi.receive_order_time &lt;= #{sheTime}
            </if>
            <if test="fhbdsTime != null">
                AND (toi.deliver_weight_notes_time >= #{fhbdsTime} or tosup.create_time >= #{fhbdsTime})
            </if>
            <if test="fhbdeTime != null">
                AND (toi.deliver_weight_notes_time &lt;= #{fhbdeTime} or tosup.create_time &lt;= #{fhbdeTime})
            </if>
            <if test="shbdsTime != null">
                AND (toi.receive_weight_notes_time >= #{shbdsTime} or tosdown.create_time >= #{shbdsTime})
            </if>
            <if test="shbdeTime != null">
                AND (toi.receive_weight_notes_time &lt;= #{shbdeTime} or tosdown.create_time &lt;= #{shbdeTime})
            </if>
            <if test="inputSearch != null">
                AND (
                toi.order_business_code LIKE CONCAT('%', #{inputSearch}, '%')
                OR eci.vehicle_number LIKE CONCAT('%', #{inputSearch}, '%')
                )
            </if>
            <if test="idArray != null and idArray.size != 0 ">
                AND toi.code IN
                <foreach collection="idArray" item="orderCode" index="item" open="(" close=")" separator=",">
                    #{orderCode}
                </foreach>
            </if>
            <if test="orderUnqualified == true">
                and (toi.param5 = 1 or toi.param5 is null)
            </if>
            <!--不合格运单记录-->
            <if test="unqualifiedOrder == true">
                and toi.param5 = 0
            </if>
        </where>

        order by toi.update_time desc
    </select>

    <!--运单检查-->
    <select id="selectByYdjcPage_COUNT" parameterType="com.lz.vo.AppOrderSearchVO" resultType="java.lang.Long">
        select count(1)
        FROM
        t_order_info toi
        LEFT JOIN t_end_user_info eui on eui.id = toi.end_driver_id
        LEFT JOIN t_end_user_info eui2 on eui2.id = toi.end_car_owner_id
        LEFT JOIN t_end_user_info eui3 on eui3.id = toi.end_agent_id
        LEFT JOIN t_end_user_info eui4 on eui4.id = toi.agent_id
        LEFT JOIN t_end_car_info eci on eci.id = toi.vehicle_id
        LEFT JOIN t_company_info com on com.id = toi.company_id
        LEFT JOIN t_company_project tcp ON tcp.id = toi.company_project_id
        LEFT JOIN t_carrier_info ca on ca.id = toi.carrier_id
        LEFT JOIN t_order_state_node osn ON osn.code = toi.order_execute_status
        LEFT JOIN t_order_state_node tosn ON tosn.code = toi.order_pay_status
        LEFT JOIN t_order_state tosup ON tosup.state_node_value = 'S0302' AND  tosup.order_code = toi.`code` and tosup.`enable` = 0
        LEFT JOIN t_order_state tosdown ON tosdown.state_node_value = 'S0402' AND  tosdown.order_code = toi.`code` and tosdown.`enable` = 0
        LEFT JOIN t_order_cast_calc_snapshot occs ON toi.`code` = occs.order_code and occs.data_enable = 1 and occs.enable = 0
        LEFT JOIN t_account ta ON toi.deliver_order_user_id = ta.id
        LEFT JOIN t_account ta1 ON toi.receive_order_user_id = ta1.id
        LEFT JOIN t_order_delete_log todl on toi.code = todl.order_code
        left join t_order_cast_changes tocc on ( select Max(id) as id from t_order_cast_changes where user_oper = 'PayMent' and order_code = toi.code) = tocc.id
        LEFT JOIN t_order_cast_changes occv ON toi.`code` = occv.order_code AND occv.user_oper = 'Invoice'
        left join t_goods_source_info tgsi on tgsi.line_goods_rel_id = toi.line_goods_rel_id
        LEFT JOIN t_dic_cat_item tdci ON tdci.item_code = occv.capital_transfer_type
        LEFT JOIN t_dic_cat_item txfx ON occv.withdraw_type = txfx.item_code
        LEFT JOIN t_order_pay_info tpi on tpi.order_code = toi.`code`
        LEFT JOIN t_order_info_detail toid on toid.order_id = toi.id
        left join t_order_info_weight toiw on toi.id = toiw.order_id
        where toi.order_create_type != 'RESOURCEHALLSEND'
        <if test="carrierId != null">
            AND toi.carrier_id = #{carrierId}
        </if>
        <if test="companyId != null">
            AND toi.company_id = #{companyId}
        </if>
        <if test="bizCode != null">
            AND toi.order_business_code like concat('%', #{bizCode}, '%')
        </if>
        <if test="driverName != null">
            AND eui.real_name like concat('%', #{driverName}, '%')
        </if>
        <if test="driverPhone != null">
            AND eui.phone like concat('%', #{driverPhone}, '%')
        </if>
        <if test="fromName != null">
            AND toi.from_name LIKE CONCAT('%',  #{fromName}, '%')
        </if>
        <if test="endName != null">
            AND toi.end_name LIKE CONCAT('%',  #{endName}, '%')
        </if>
        <if test="projectId != null">
            and toi.company_project_id = #{projectId}
        </if>
        <if test="lineGoodsId != null">
            AND toi.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
            and toi.line_goods_rel_id in
            <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                #{lineGoodsRelId}
            </foreach>
        </if>
        <if test="goodsId != null">
            and toi.goods_id = #{goodsId}
        </if>
        <if test="companyEntrust != null">
            and toi.company_entrust like concat('%', #{companyEntrust}, '%')
        </if>
        <if test="companyClient != null">
            and toi.company_client like concat('%', #{companyClient}, '%')
        </if>
        <if test="searchAgentId != null">
            AND toi.end_agent_id = #{searchAgentId}
        </if>
        <if test="agentId != null">
            AND toi.agent_id = #{agentId}
        </if>
        <if test="carOwnerId != null">
            AND toi.end_car_owner_id = #{carOwnerId}
        </if>
        <if test="captainId != null">
            and toi.end_car_owner_id = #{captainId}
        </if>
        <if test="orderState != null ">
            AND toi.order_execute_status IN
            <foreach collection="orderState" item="orderExecuteStatus" index="item" open="(" close=")" separator=",">
                #{orderExecuteStatus}
            </foreach>
        </if>
        <if test="payState != null">
            AND toi.order_pay_status IN
            <foreach collection="payState" item="orderPayStatus" index="item" open="(" close=")" separator=",">
                #{orderPayStatus}
            </foreach>
        </if>
        <if test="(orderState == null or orderState.length == 0) and (payState == null or payState.length == 0)">
            <if test="getDelete">
                and toi.order_execute_status &lt;> 'M-10'
            </if>
            <if test="!org and !isAdmin">
                AND toi.order_execute_status &lt;> 'M-20'
            </if>
        </if>
        <if test="fhsTime != null">
            AND toi.deliver_order_time >= #{fhsTime}
        </if>
        <if test="fheTime != null">
            AND toi.deliver_order_time &lt;= #{fheTime}
        </if>
        <if test="shsTime != null">
            AND toi.receive_order_time >= #{shsTime}
        </if>
        <if test="sheTime != null">
            AND toi.receive_order_time &lt;= #{sheTime}
        </if>
        <if test="fhbdsTime != null">
            AND (toi.deliver_weight_notes_time >= #{fhbdsTime} or tosup.create_time >= #{fhbdsTime})
        </if>
        <if test="fhbdeTime != null">
            AND (toi.deliver_weight_notes_time &lt;= #{fhbdeTime} or tosup.create_time &lt;= #{fhbdeTime})
        </if>
        <if test="shbdsTime != null">
            AND (toi.receive_weight_notes_time >= #{shbdsTime} or tosdown.create_time >= #{shbdsTime})
        </if>
        <if test="shbdeTime != null">
            AND (toi.receive_weight_notes_time &lt;= #{shbdeTime} or tosdown.create_time &lt;= #{shbdeTime})
        </if>
        <if test="licensePlate != null">
            AND eci.vehicle_number like concat('%', #{licensePlate}, '%')
        </if>
        <if test="idArray != null and idArray.size != 0 ">
            AND toi.code IN
            <foreach collection="idArray" item="orderCode" index="item" open="(" close=")" separator=",">
                #{orderCode}
            </foreach>
        </if>

        <if test="informationStation != null">
            AND eui3.org_name = #{informationStation}
        </if>
        <if test="orderUnqualified">
            <if test="unqualifiedOrder">
                and toi.param5 = '0'
            </if>
            <if test="!unqualifiedOrder">
                and (toi.param5 = 1 or toi.param5 is null)
            </if>
        </if>
        <if test="companyIds != null and companyIds.size != 0">
            AND toi.company_id IN
            <foreach collection="companyIds" index="index" item="cid" open="(" separator="," close=")">
                #{cid}
            </foreach>
        </if>
        <if test="!isAdmin">
            <if test="!org">
                <if test="lineGoodsRuleIds != null and lineGoodsRuleIds.size > 0">
                    and toi.line_goods_rel_id IN
                    <foreach collection="lineGoodsRuleIds" item="lgr" index="item" open="(" close=")" separator=",">
                        #{lgr}
                    </foreach>
                </if>
            </if>
        </if>
        order by toi.update_time desc
    </select>

    <!--运单跟踪-->
    <select id="selectByYdgzPage_COUNT" parameterType="com.lz.vo.AppOrderSearchVO" resultType="java.lang.Long">
        select count(1)
        FROM
        t_order_info toi
        LEFT JOIN t_end_user_info eui on eui.id = toi.end_driver_id
        LEFT JOIN t_end_user_info eui2 on eui2.id = toi.end_car_owner_id
        LEFT JOIN t_end_user_info eui3 on eui3.id = toi.end_agent_id
        LEFT JOIN t_end_user_info eui4 on eui4.id = toi.agent_id
        LEFT JOIN t_end_car_info eci on eci.id = toi.vehicle_id
        LEFT JOIN t_company_info com on com.id = toi.company_id
        LEFT JOIN t_company_project tcp ON tcp.id = toi.company_project_id
        LEFT JOIN t_carrier_info ca on ca.id = toi.carrier_id
        LEFT JOIN t_order_state_node osn ON osn.code = toi.order_execute_status
        LEFT JOIN t_order_state_node tosn ON tosn.code = toi.order_pay_status
        LEFT JOIN t_order_state tosup ON tosup.state_node_value = 'S0302' AND  tosup.order_code = toi.`code` and tosup.`enable` = 0
        LEFT JOIN t_order_state tosdown ON tosdown.state_node_value = 'S0402' AND  tosdown.order_code = toi.`code` and tosdown.`enable` = 0
        LEFT JOIN t_order_cast_calc_snapshot occs ON toi.`code` = occs.order_code and occs.data_enable = 1 and occs.enable = 0
        LEFT JOIN t_account ta ON toi.deliver_order_user_id = ta.id
        LEFT JOIN t_account ta1 ON toi.receive_order_user_id = ta1.id
        LEFT JOIN t_order_delete_log todl on toi.code = todl.order_code
        LEFT JOIN t_order_pack_detail opd ON toi.`code` = opd.order_code AND opd.`enable` = 0
        LEFT JOIN t_order_pack_info opi ON opd.pack_pay_code = opi.code AND opi.`enable` = 0
        LEFT JOIN t_bank_card tbc ON opi.bank_card_id = tbc.id
        LEFT JOIN t_end_user_info euipk ON opi.end_user_id = euipk.id
        LEFT JOIN t_order_cast_changes occ ON toi.`code` = occ.order_code AND occ.data_enable = 1
        LEFT JOIN t_line_goods_rel tlgr on toi.line_goods_rel_id = tlgr.id
        LEFT JOIN t_dic_cat_item tdci ON tdci.item_code = occ.capital_transfer_type
        LEFT JOIN t_dic_cat_item tdci2 on tlgr.capital_transfer_type = tdci2.item_code
        LEFT JOIN t_order_pay_info tpi on tpi.order_code = toi.`code`
        left join t_order_cast_changes tocc on ( select Max(id) as id from t_order_cast_changes where user_oper = 'PayMent' and order_code = toi.code) = tocc.id
        left join t_order_cast_changes toccn on ( select Max(id) as id from t_order_cast_changes where user_oper = 'WKPAYNODE' and order_code = toi.code) = toccn.id
        LEFT JOIN t_order_cast_changes occv ON toi.`code` = occv.order_code AND occv.user_oper = 'Invoice'
        left join t_order_info_detail toid on toi.id = toid.order_id
        left join t_order_info_weight toiw on toi.id = toiw.order_id
        left join t_order_insurance toins on toi.order_business_code = toins.order_business_code and toins.enable = 0
        where toi.order_create_type != 'RESOURCEHALLSEND'
        <if test="carrierId != null">
            AND toi.carrier_id = #{carrierId}
        </if>
        <if test="companyId != null">
            AND toi.company_id = #{companyId}
        </if>
        <if test="bizCode != null">
            AND toi.order_business_code like concat('%', #{bizCode}, '%')
        </if>
        <if test="driverName != null">
            AND eui.real_name like concat('%', #{driverName}, '%')
        </if>
        <if test="driverPhone != null">
            AND eui.phone like concat('%', #{driverPhone}, '%')
        </if>
        <if test="fromName != null">
            AND toi.from_name LIKE CONCAT('%',  #{fromName}, '%')
        </if>
        <if test="endName != null">
            AND toi.end_name LIKE CONCAT('%',  #{endName}, '%')
        </if>
        <if test="projectId != null">
            and toi.company_project_id = #{projectId}
        </if>
        <if test="lineGoodsId != null">
            AND toi.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
            and toi.line_goods_rel_id in
            <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                #{lineGoodsRelId}
            </foreach>
        </if>
        <if test="goodsId != null">
            and toi.goods_id = #{goodsId}
        </if>
        <if test="searchAgentId != null">
            AND toi.end_agent_id = #{searchAgentId}
        </if>
        <if test="agentId != null">
            AND toi.agent_id = #{agentId}
        </if>
        <if test="carOwnerId != null">
            AND toi.end_car_owner_id = #{carOwnerId}
        </if>
        <if test="captainId != null">
            and toi.end_car_owner_id = #{captainId}
        </if>
        <if test="orderState != null ">
            AND toi.order_execute_status IN
            <foreach collection="orderState" item="orderExecuteStatus" index="item" open="(" close=")" separator=",">
                #{orderExecuteStatus}
            </foreach>
        </if>
        <if test="payState != null">
            AND toi.order_pay_status IN
            <foreach collection="payState" item="orderPayStatus" index="item" open="(" close=")" separator=",">
                #{orderPayStatus}
            </foreach>
        </if>
        <if test="fhsTime != null">
            AND toi.deliver_order_time >= #{fhsTime}
        </if>
        <if test="fheTime != null">
            AND toi.deliver_order_time &lt;= #{fheTime}
        </if>
        <if test="shsTime != null">
            AND toi.receive_order_time >= #{shsTime}
        </if>
        <if test="sheTime != null">
            AND toi.receive_order_time &lt;= #{sheTime}
        </if>
        <if test="fhbdsTime != null">
            AND (toi.deliver_weight_notes_time >= #{fhbdsTime} or tosup.create_time >= #{fhbdsTime})
        </if>
        <if test="fhbdeTime != null">
            AND (toi.deliver_weight_notes_time &lt;= #{fhbdeTime} or tosup.create_time &lt;= #{fhbdeTime})
        </if>
        <if test="shbdsTime != null">
            AND (toi.receive_weight_notes_time >= #{shbdsTime} or tosdown.create_time >= #{shbdsTime})
        </if>
        <if test="shbdeTime != null">
            AND (toi.receive_weight_notes_time &lt;= #{shbdeTime} or tosdown.create_time &lt;= #{shbdeTime})
        </if>
        <if test="fihsTime != null">
            AND toi.order_pay_status !='M095' <!--按运单完成时间查询过滤召回状态-->
            AND toi.order_finish_time >= #{fihsTime}
        </if>
        <if test="fiheTime != null">
            AND toi.order_finish_time &lt;= #{fiheTime}
        </if>
        <if test="driverAuditStatus != null">
            AND eui.audit_status = #{driverAuditStatus}
        </if>
        <if test="carAuditStatus != null">
            AND eci.audit_status = #{carAuditStatus}
        </if>
        <if test="param1 != null and param1 != ''">
            AND tpi.param1 = #{param1}
        </if>
        <if test="licensePlate != null">
            AND eci.vehicle_number like concat('%', #{licensePlate}, '%')
        </if>
        <if test="idArray != null and idArray.size != 0 ">
            AND toi.code IN
            <foreach collection="idArray" item="orderCode" index="item" open="(" close=")" separator=",">
                #{orderCode}
            </foreach>
        </if>
        order by toi.update_time desc
    </select>

    <!--PC 端查询运单  企业只查自己的运单，  承运方运营查所有的运单 Yan-->
    <select id="selectOrderByUserType_COUNT" parameterType="com.lz.vo.AppOrderSearchVO" resultType="java.lang.Long">
        select count(1)
        FROM
        t_order_info toi
        LEFT JOIN t_company_info tcci ON toi.company_id = tcci.id AND tcci.`enable` = 0
        LEFT JOIN t_company_project tcp ON tcp.id = toi.company_project_id AND tcp.`enable` = 0
        LEFT JOIN t_line_goods_rel lgr ON lgr.id = toi.line_goods_rel_id AND lgr.`enable` = 0
        LEFT JOIN t_carrier_info tcpi ON toi.carrier_id = tcpi.id AND tcpi.`enable` = 0
        LEFT JOIN t_end_user_info euis ON toi.end_agent_id = euis.id AND euis.`enable` = 0
        LEFT JOIN t_end_user_info euic ON toi.end_car_owner_id = euic.id AND euic.`enable` = 0
        LEFT JOIN t_end_user_info agent on toi.agent_id = agent.id AND agent.`enable` = 0
        LEFT JOIN t_end_user_info eui ON toi.end_driver_id = eui.id AND eui.`enable` = 0
        LEFT JOIN t_end_car_info eci ON toi.vehicle_id = eci.id AND eci.`enable` = 0
        LEFT JOIN t_order_state_node osn ON toi.order_execute_status = osn.`code`
        LEFT JOIN t_account ta ON toi.deliver_order_user_id = ta.id
        LEFT JOIN t_account ta1 ON toi.receive_order_user_id = ta1.id
        LEFT JOIN t_order_state_node tosn ON toi.order_pay_status = tosn.`code`
        LEFT JOIN t_order_state tosf ON tosf.id = (
        SELECT
        MAX(tost.id)
        FROM t_order_state tost
        WHERE toi.`code` = tost.order_code
        )
        LEFT JOIN t_order_cast_changes occ ON toi.`code` = occ.order_code AND occ.data_enable = 1
        LEFT JOIN t_dic_cat_item tdci ON tdci.item_code = occ.capital_transfer_type
        LEFT JOIN t_order_cast_calc_snapshot occs ON toi.`code` = occs.order_code and occs.data_enable = 1
        LEFT JOIN t_line_goods_carriage_rule_detail lgcr ON toi.line_goods_rel_id = lgcr.line_goods_rel_id and lgcr.`enable`=0
        LEFT JOIN t_order_audit_log oal ON oal.order_code = toi.code
        LEFT JOIN t_order_state tosup ON tosup.state_node_value = 'S0302' AND  tosup.order_code = toi.`code` and tosup.`enable` = 0
        LEFT JOIN t_order_state tosdown ON tosdown.state_node_value = 'S0402' AND  tosdown.order_code = toi.`code` and tosdown.`enable` = 0
        LEFT JOIN t_dic_cat_item dci ON dci.item_code = oal.audit_status
        LEFT JOIN t_dic_cat_item ctp ON toi.order_create_type = ctp.item_code
        LEFT JOIN t_dic_cat_item dci2 on dci2.item_code = toi.contract_status
        left join t_order_pay_info topi on toi.code = topi.order_code
        LEFT JOIN t_order_cast_changes occv ON toi.`code` = occv.order_code AND occv.user_oper = 'Invoice'
        LEFT JOIN t_order_pack_detail opd ON toi.`code` = opd.order_code AND opd.`enable` = 0
        left join t_order_delete_log todl on toi.code = todl.order_code
        left join t_advance_order_tmp taot on taot.order_code = toi.code
        left join t_advance_order_pay_tmp taopt on taot.code = taopt.advance_code and taopt.trade_type = 'COMBALANCE_PREPAY'
        left join t_order_info_detail toid on toi.id = toid.order_id
        left join t_order_info_weight toiw on toi.id = toiw.order_id
        <!--实体运单支付菜单只查单笔支付运单-->
        <if test="state == 'PAY'">
            <if test="!ifAdvance">
                left join t_advance_order_tmp advance on advance.order_code = toi.code
            </if>
        </if>
        where toi.order_create_type != 'RESOURCEHALLSEND'
        <if test="orderPackPage">
            AND toi.pack_status = 0
        </if>
        <if test='state=="PAY"'>
            <if test="!ifAdvance">
                and advance.order_code is null
            </if>
            and (toi.pay_method = 'SINGLEPAY' or  toi.pay_method is null or toi.pay_method ='')
        </if>
        <if test='state=="PAID"'>
            and (toi.pay_method = 'SINGLEPAY' or  toi.pay_method is null or toi.pay_method ='')
        </if>
        <if test="!isAdmin">
            <if test="!org">
                <if test="lineGoodsRuleIds != null and lineGoodsRuleIds.size > 0">
                    and toi.line_goods_rel_id IN
                    <foreach collection="lineGoodsRuleIds" item="lgr" index="item" open="(" close=")" separator=",">
                        #{lgr}
                    </foreach>
                </if>
            </if>
        </if>
        <!-- 支付审核员收单员是按照已分配的线路查询但是要是 t_line_goods_user_rel 的企业 -->
        <if test="companyIds != null">
            AND toi.company_id IN
            <foreach collection="companyIds" index="index" item="cid" open="(" separator="," close=")">
                #{cid}
            </foreach>
        </if>
        <if test="companyId != null">
            AND toi.company_id = #{companyId}
        </if>
        <if test="payState != null">
            AND toi.order_pay_status IN
            <foreach collection="payState" item="pay" index="item" open="(" close=")" separator=",">
                #{pay}
            </foreach>
            <if test='payStateNode != null and payStateNode !="" '>
                and tosf.state_node_value = #{payStateNode}
            </if>
        </if>
        <if test="status != null">
            AND toi.order_execute_status IN
            <foreach collection="status" index="index" item="st" open="(" close=")" separator=",">
                #{st}
            </foreach>
        </if>
        <if test="vehicleNumber != null">
            AND eci.vehicle_number LIKE CONCAT('%',  #{vehicleNumber}, '%')
        </if>
        <if test="fromName != null">
            AND toi.from_name LIKE CONCAT('%',  #{fromName}, '%')
        </if>
        <if test="endName != null">
            AND toi.end_name LIKE CONCAT('%',  #{endName}, '%')
        </if>
        <if test="auditRemark != null">
            AND oal.audit_remark like concat('%', #{auditRemark}, '%')
        </if>
        <if test="auditStatus != null and auditStatus != 'NO'">
            AND oal.audit_status = #{auditStatus}
        </if>
        <if test="auditStatus == 'NO'">
            AND ISNULL(oal.audit_status)
        </if>
        <if test="auditsTime != null ">
            AND oal.audit_time >= #{auditsTime}
            AND oal.enable = 0
        </if>
        <if test="auditeTime != null">
            AND oal.audit_time &lt;= #{auditeTime}
            AND oal.enable = 0
        </if>
        <if test="carrierId != null">
            AND toi.carrier_id = #{carrierId}
        </if>
        <if test="lineGoodsId != null">
            AND toi.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
            and toi.line_goods_rel_id in
            <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                #{lineGoodsRelId}
            </foreach>
        </if>
        <if test="carOwnerId != null">
            and toi.end_car_owner_id = #{carOwnerId}
        </if>
        <if test="captainId != null">
            and toi.end_car_owner_id = #{captainId}
        </if>
        <if test="goodsId != null">
            and toi.goods_id = #{goodsId}
        </if>
        <if test="companyEntrust != null">
            and toi.company_entrust like concat('%', #{companyEntrust}, '%')
        </if>
        <if test="companyClient != null">
            and toi.company_client = #{companyClient}
        </if>
        <if test="projectId != null">
            and toi.company_project_id = #{projectId}
        </if>
        <if test="endAgentId != null">
            and toi.end_agent_id = #{endAgentId}
        </if>
        <if test="agentId != null">
            AND toi.agent_id = #{agentId}
        </if>
        <if test="informationStation != null">
            AND euis.org_name = #{informationStation}
        </if>
        <if test="bizCode != null">
            AND toi.order_business_code like concat('%', #{bizCode}, '%')
        </if>
        <if test="driverName != null">
            AND eui.real_name like concat('%', #{driverName}, '%')
        </if>
        <if test="driverPhone != null">
            AND eui.phone like concat('%', #{driverPhone}, '%')
        </if>
        <if test="licensePlate != null">
            AND eci.vehicle_number like concat('%', #{licensePlate}, '%')
        </if>
        <if test="carAuditStatus != null">
            AND eci.audit_status = #{carAuditStatus}
        </if>
        <if test="origin != null">
            AND toi.from_name like concat('%', #{origin}, '%')
        </if>
        <if test="terminus != null">
            AND toi.end_name like concat('%', #{terminus}, '%')
        </if>
        <if test="fhsTime != null">
            AND toi.deliver_order_time >= #{fhsTime}
        </if>
        <if test="fheTime != null">
            AND toi.deliver_order_time &lt;= #{fheTime}
        </if>
        <if test="shsTime != null">
            AND toi.receive_order_time >= #{shsTime}
        </if>
        <if test="sheTime != null">
            AND toi.receive_order_time &lt;= #{sheTime}
        </if>
        <if test="fhbdsTime != null">
            AND (toi.deliver_weight_notes_time >= #{fhbdsTime} or tosup.create_time >= #{fhbdsTime})
        </if>
        <if test="fhbdeTime != null">
            AND (toi.deliver_weight_notes_time &lt;= #{fhbdeTime} or tosup.create_time &lt;= #{fhbdeTime})
        </if>
        <if test="shbdsTime != null">
            AND (toi.receive_weight_notes_time >= #{shbdsTime} or tosdown.create_time >= #{shbdsTime})
        </if>
        <if test="shbdeTime != null">
            AND (toi.receive_weight_notes_time &lt;= #{shbdeTime} or tosdown.create_time &lt;= #{shbdeTime})
        </if>
        <if test="inputSearch != null">
            AND (
            toi.order_business_code LIKE CONCAT('%', #{inputSearch}, '%')
            OR eci.vehicle_number LIKE CONCAT('%', #{inputSearch}, '%')
            )
        </if>
        <if test="idArray != null and idArray.size != 0 ">
            AND toi.code IN
            <foreach collection="idArray" item="orderCode" index="item" open="(" close=")" separator=",">
                #{orderCode}
            </foreach>
        </if>
        <if test="orderUnqualified == true">
            and (toi.param5 = 1 or toi.param5 is null)
        </if>
        <!--不合格运单记录-->
        <if test="unqualifiedOrder == true">
            and toi.param5 = 0
        </if>
        <if test="yfkPage">
            <if test="fksTime != null">
                AND toi.order_finish_time >= #{fksTime}
            </if>
            <if test="fkeTime != null">
                AND toi.order_finish_time &lt;= #{fkeTime}
            </if>
        </if>
        order by toi.update_time desc
    </select>

    <select id="getGzorderList_COUNT" resultType="java.lang.Long"
            parameterType="com.lz.vo.AppOrderSearchVO">
        select count(*)
        FROM
        t_order_info toi
        LEFT JOIN t_order_state_node osn ON toi.order_execute_status = osn.`code`
        LEFT JOIN t_order_state_node tosn ON toi.order_pay_status = tosn.`code`
        LEFT JOIN t_account ta ON toi.deliver_order_user_id = ta.id
        LEFT JOIN t_account ta1 ON toi.receive_order_user_id = ta1.id
        LEFT JOIN t_end_car_info eci ON toi.vehicle_id = eci.id
        LEFT JOIN t_end_user_info eui ON toi.end_driver_id = eui.id
        LEFT JOIN t_end_user_info euic ON toi.end_car_owner_id = euic.id
        LEFT JOIN t_end_user_info euis ON toi.end_agent_id = euis.id
        LEFT JOIN t_end_user_info agent on toi.agent_id = agent.id
        LEFT JOIN t_order_cast_changes occ ON toi.`code` = occ.order_code AND occ.data_enable = 1
        LEFT JOIN t_dic_cat_item tdci ON tdci.item_code = occ.capital_transfer_type
        LEFT JOIN t_dic_cat_item ctp ON toi.order_create_type = ctp.item_code
        LEFT JOIN t_carrier_info tcpi ON toi.carrier_id = tcpi.id
        LEFT JOIN t_company_project tcp ON tcp.id = toi.company_project_id
        LEFT JOIN t_company_info tcci ON toi.company_id = tcci.id
        LEFT JOIN t_order_cast_calc_snapshot occs ON toi.`code` = occs.order_code and occs.data_enable = 1
        left join t_order_cast_changes tocc on ( select Max(id) as id from t_order_cast_changes where user_oper = 'PayMent' and order_code = toi.code) = tocc.id
        LEFT JOIN t_dic_cat_item txfx ON occ.withdraw_type = txfx.item_code
        LEFT JOIN t_order_pack_detail opd ON toi.`code` = opd.order_code AND opd.`enable` = 0
        LEFT JOIN t_order_pack_info opi ON opd.pack_pay_code = opi.code AND opi.`enable` = 0
        LEFT JOIN t_end_user_info euipk ON opi.end_user_id = euipk.id
        LEFT JOIN t_bank_card tbc ON opi.bank_card_id = tbc.id
        LEFT JOIN t_zt_bank_card tzbc ON opi.bank_card_id = tzbc.id
        LEFT JOIN t_order_state tosup ON tosup.state_node_value = 'S0302' AND  tosup.order_code = toi.`code` and tosup.`enable` = 0
        LEFT JOIN t_order_state tosdown ON tosdown.state_node_value = 'S0402' AND  tosdown.order_code = toi.`code` and tosdown.`enable` = 0
        LEFT JOIN t_order_cast_changes occv ON toi.`code` = occv.order_code AND occv.user_oper = 'Invoice'
        LEFT JOIN t_order_audit_log oal ON oal.order_code = toi.code
        left join t_order_delete_log todl on toi.code = todl.order_code
        left join t_goods_source_info tgsi on tgsi.line_goods_rel_id = toi.line_goods_rel_id
        left join t_order_state toss on toi.code = toss.order_code and toss.state_node_value = 'S0101'
        left join t_line_goods_rel tlgr on toi.line_goods_rel_id = tlgr.id
        left join t_dic_cat_item tdci2 on tlgr.capital_transfer_type = tdci2.item_code
        LEFT JOIN t_order_pay_info tpi on tpi.order_code = toi.`code`
        LEFT JOIN t_order_pay_detail tpdi ON tpdi.id =  ( SELECT MAX( tosc.id ) FROM t_order_pay_detail tosc WHERE tosc.order_pay_code = tpi.CODE )
        left join t_order_info_detail toid on toi.id = toid.order_id
        left join t_order_info_weight toiw on toi.id = toiw.order_id
        where toi.order_create_type != 'RESOURCEHALLSEND'
        <if test="orderPackPage">
            AND toi.pack_status = 0
        </if>
        <if test="!isAdmin">
            <if test="!org">
                <if test="lineGoodsRuleIds != null and lineGoodsRuleIds.size > 0">
                    and toi.line_goods_rel_id IN
                    <foreach collection="lineGoodsRuleIds" item="lgr" index="item" open="(" close=")" separator=",">
                        #{lgr}
                    </foreach>
                </if>
            </if>
        </if>
        <if test="payState != null or status != null">
            AND EXISTS (
            select distinct
            a.id
            from (
            select
            b.id
            from t_order_info b
            <where>
                <if test="payState != null">
                    AND b.order_pay_status IN
                    <foreach collection="payState" item="pay" index="item" open="(" close=")" separator=",">
                        #{pay}
                    </foreach>
                </if>
                <if test="status != null">
                    AND b.order_execute_status IN
                    <foreach collection="status" index="index" item="st" open="(" close=")" separator=",">
                        #{st}
                    </foreach>
                </if>
            </where>
            ) a
            where toi.id = a.id
            )
        </if>
        <if test="searchAgentId != null">
            AND toi.end_agent_id = #{searchAgentId}
        </if>
        <if test="companyId != null">
            AND toi.company_id = #{companyId}
        </if>
        <if test="carrierId != null">
            AND toi.carrier_id = #{carrierId}
        </if>
        <if test="fromName != null">
            AND toi.from_name LIKE CONCAT('%',  #{fromName}, '%')
        </if>
        <if test="endName != null">
            AND toi.end_name LIKE CONCAT('%',  #{endName}, '%')
        </if>
        <if test="licensePlate != null">
            AND eci.vehicle_number like concat('%', #{licensePlate}, '%')
        </if>
        <if test="carAuditStatus != null">
            AND eci.audit_status = #{carAuditStatus}
        </if>
        <if test="driverName != null">
            AND eui.real_name like concat('%', #{driverName}, '%')
        </if>
        <if test="driverPhone != null">
            AND eui.phone like concat('%', #{driverPhone}, '%')
        </if>
        <if test="driverAuditStatus != null">
            AND eui.audit_status = #{driverAuditStatus}
        </if>
        <if test="companyId != null">
            and tcci.id = #{companyId}
        </if>
        <if test="informationStation != null">
            AND euis.org_name = #{informationStation}
        </if>
        <if test="lineGoodsId != null">
            AND toi.line_goods_rel_id = #{lineGoodsId}
        </if>
        <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
            and toi.line_goods_rel_id in
            <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                #{lineGoodsRelId}
            </foreach>
        </if>
        <if test="carOwnerId != null">
            and toi.end_car_owner_id = #{carOwnerId}
        </if>
        <if test="captainId != null">
            and toi.end_car_owner_id = #{captainId}
        </if>
        <if test="goodsId != null">
            and toi.goods_id = #{goodsId}
        </if>
        <if test="companyEntrust != null">
            and toi.company_entrust like concat('%', #{companyEntrust}, '%')
        </if>
        <if test="companyClient != null">
            and toi.company_client = #{companyClient}
        </if>
        <if test="projectId != null">
            and toi.company_project_id = #{projectId}
        </if>
        <if test="bizCode != null">
            AND toi.order_business_code like concat('%', #{bizCode}, '%')
        </if>
        <if test="origin != null">
            AND toi.from_name like concat('%', #{origin}, '%')
        </if>
        <if test="terminus != null">
            AND toi.end_name like concat('%', #{terminus}, '%')
        </if>
        <if test="fhsTime != null">
            AND toi.deliver_order_time >= #{fhsTime}
        </if>
        <if test="fheTime != null">
            AND toi.deliver_order_time &lt;= #{fheTime}
        </if>
        <if test="shsTime != null">
            AND toi.receive_order_time >= #{shsTime}
        </if>
        <if test="sheTime != null">
            AND toi.receive_order_time &lt;= #{sheTime}
        </if>
        <if test="fhbdsTime != null">
            AND (toi.deliver_weight_notes_time >= #{fhbdsTime} or tosup.create_time >= #{fhbdsTime})
        </if>
        <if test="fhbdeTime != null">
            AND (toi.deliver_weight_notes_time &lt;= #{fhbdeTime} or tosup.create_time &lt;= #{fhbdeTime})
        </if>
        <if test="shbdsTime != null">
            AND (toi.receive_weight_notes_time >= #{shbdsTime} or tosdown.create_time >= #{shbdsTime})
        </if>
        <if test="shbdeTime != null">
            AND (toi.receive_weight_notes_time &lt;= #{shbdeTime} or tosdown.create_time &lt;= #{shbdeTime})
        </if>
        <if test="fihsTime != null">
            AND toi.order_finish_time >= #{fihsTime}
        </if>
        <if test="fiheTime != null">
            AND toi.order_finish_time &lt;= #{fiheTime}
        </if>
        <if test="inputSearch != null">
            AND toi.order_business_code LIKE CONCAT('%', #{inputSearch}, '%')
        </if>
        <if test="auditStatus != null and auditStatus != 'NO'">
            AND oal.audit_status = #{auditStatus}
        </if>
        <if test="auditStatus == 'NO'">
            AND ISNULL(oal.audit_status)
        </if>
        <if test="auditPass">
            AND oal.audit_status = 'PASSNODE'
        </if>
        <if test="getDelete">
            AND toi.order_execute_status &lt;> 'M-10'
        </if>
        <if test="idArray != null and idArray.size != 0 ">
            AND toi.code IN
            <foreach collection="idArray" item="orderCode" index="item" open="(" close=")" separator=",">
                #{orderCode}
            </foreach>
        </if>
        <if test="orderUnqualified">
            <if test="unqualifiedOrder">
                and toi.param5 = '0'
            </if>
            <if test="!unqualifiedOrder">
                and (toi.param5 = 1 or toi.param5 is null)
            </if>
        </if>
        <if test="companyIds != null and companyIds.size != 0">
            AND toi.company_id IN
            <foreach collection="companyIds" index="index" item="cid" open="(" separator="," close=")">
                #{cid}
            </foreach>
        </if>
        <if test="agentId != null">
            AND toi.agent_id = #{agentId}
        </if>
        order by toi.update_time desc
    </select>

    <select id="selectTaskByOrderInfoPage_COUNT" parameterType="com.lz.vo.TOrderInfoVO" resultType="java.lang.Long">
        SELECT count(*)
        FROM
        t_task t
        LEFT JOIN t_order_info o ON t.source_fieldvalue = o.CODE
        WHERE t.enable = 0  and t.request_times>0 and o.id is not null
        <if test="orderBusinessCode != null and orderBusinessCode != ''">
            AND o.order_business_code like '%${orderBusinessCode}%'
        </if>
    </select>

    <select id="pcSelectOrderNoSign_COUNT" parameterType="com.lz.vo.PcOrderSearchVO" resultType="java.lang.Long">
        SELECT
        count(*)
        FROM t_order_info toi
        <if test="companyName != null">
        LEFT JOIN t_company_info tci ON toi.company_id = tci.id
        </if>
        <if test="phone != null or driverName != null">
        LEFT JOIN t_end_user_info eui ON toi.end_driver_id = eui.id
        </if>
        <if test="licencePlate != null">
        LEFT JOIN t_end_car_info eci ON toi.vehicle_id = eci.id
        </if>
        WHERE
        toi.order_execute_status NOT IN ('M-20', 'M-10', 'M010')
        <if test="!initPage">
            AND toi.contract_status IN ('WQD', 'APPHTSC')
        </if>
        <if test="contractStatus.length != 0">
            AND toi.contract_status IN
            <foreach collection="contractStatus" item="cs" index="index" open="(" separator="," close=")">
                #{cs}
            </foreach>
        </if>
        <if test="license != null">
            AND toi.order_business_code like concat('%', #{license}, '%')
        </if>
        <if test="licencePlate != null">
            AND eci.vehicle_number like concat('%', #{licencePlate}, '%')
        </if>
        <if test="companyName != null">
            AND tci.company_name like concat('%', #{companyName}, '%')
        </if>
        <if test="driverName != null">
            AND eui.real_name like concat('%', #{driverName}, '%')
        </if>
        <if test="sendStartTime != null">
            AND toi.deliver_order_time >= #{sendStartTime}
        </if>
        <if test="sendEndTime != null">
            AND toi.deliver_order_time &lt;= #{sendEndTime}
        </if>
        <if test="phone != null">
            AND eui.phone like concat('%', #{phone}, '%')
        </if>
        <if test="auditState != null">
            AND toi.order_execute_status like concat('%', #{auditState}, '%')
        </if>
    </select>

    <select id="getOrderPackList_COUNT"  parameterType="com.lz.vo.AppOrderSearchVO" resultType="java.lang.Long">

        SELECT
            count(*)
        FROM
        t_order_info toi
        LEFT JOIN t_line_goods_rel lgr ON toi.line_goods_rel_id = lgr.id
        LEFT JOIN t_line_goods_user_rel lgur ON lgr.id = lgur.line_goods_rel_id AND lgur.role_code = 'PAYMENTREVIEW' AND lgur.account_info_id = #{accountId} and lgur.enable = 0
        LEFT JOIN t_line_goods_user_rel lgurs ON lgr.id = lgurs.line_goods_rel_id AND lgurs.role_code = 'PAYMENTREVIEW' AND lgurs.account_info_id = #{accountId} and lgurs.enable = 0
        LEFT JOIN t_order_cast_calc_snapshot occs ON (
        SELECT
        MAX(ost.id) as id
        FROM t_order_cast_calc_snapshot ost
        WHERE toi.`code` = ost.order_code
        ) = occs.id
        LEFT JOIN t_order_state_node osn ON toi.order_execute_status = osn.`code`
        LEFT JOIN t_order_state_node tosn ON toi.order_pay_status = tosn.`code`
        LEFT JOIN t_end_car_info eci ON toi.vehicle_id = eci.id
        LEFT JOIN t_end_user_info euis ON toi.end_agent_id = euis.id
        LEFT JOIN t_end_user_info euic ON toi.end_car_owner_id = euic.id
        LEFT JOIN t_end_user_info eui ON toi.end_driver_id = eui.id
        LEFT JOIN t_end_user_info agent on toi.agent_id = agent.id
        LEFT JOIN t_order_cast_changes occ ON toi.`code` = occ.order_code AND occ.data_enable = 1
        LEFT JOIN t_dic_cat_item tdci ON tdci.item_code = occ.capital_transfer_type
        LEFT JOIN t_carrier_info tcpi ON toi.carrier_id = tcpi.id
        LEFT JOIN t_company_info tcci ON toi.company_id = tcci.id
        LEFT JOIN t_company_project tcp ON tcp.id = toi.company_project_id
        LEFT JOIN t_dic_cat_item ctp ON toi.order_create_type = ctp.item_code
        LEFT JOIN t_order_cast_changes occv ON toi.`code` = occv.order_code AND occv.data_enable = 0 AND occv.user_oper = 'Invoice'
        LEFT JOIN t_order_pay_info tpi on tpi.order_code = toi.`code`
        LEFT JOIN t_order_state tosf ON tosf.id = (
        SELECT
        MAX(tost.id)
        FROM t_order_state tost
        WHERE toi.`code` = tost.order_code
        )
        <if test="fhbdsTime != null or fhbdeTime != null">
            LEFT JOIN t_order_state tosup ON tosup.state_node_value = 'S0302' AND  tosup.order_code = toi.`code` and tosup.`enable` = 0
        </if>
        <if test="shbdsTime != null or shbdeTime != null">
            LEFT JOIN t_order_state tosdown ON tosdown.state_node_value = 'S0402' AND  tosdown.order_code = toi.`code` and tosdown.`enable` = 0
        </if>
        <where>
            <if test="orderPackPage">
                AND toi.pack_status = 0
            </if>
            <if test="state == 'YDDB'">
                AND (toi.pay_method = 'PACKPAY' or toi.pay_method is null or toi.pay_method ='')
            </if>
            <if test="carrierId != null">
                AND toi.carrier_id=#{carrierId}
            </if>
            <if test="!isAdmin">
                <if test="!org">
                    <if test="lineGoodsRuleIds != null and lineGoodsRuleIds.size > 0">
                        and toi.line_goods_rel_id IN
                        <foreach collection="lineGoodsRuleIds" item="lgr" index="item" open="(" close=")" separator=",">
                            #{lgr}
                        </foreach>
                    </if>
                </if>
            </if>
            <if test="companyIds != null">
                AND toi.company_id in
                <foreach collection="companyIds" item="companyId" index="item" open="(" close=")" separator=",">
                    #{companyId}
                </foreach>

            </if>
            <if test="companyId != null">
                AND toi.company_id = #{companyId}
            </if>
            <if test="payState != null">
                AND toi.order_pay_status IN
                <foreach collection="payState" item="pay" index="item" open="(" close=")" separator=",">
                    #{pay}
                </foreach>
            </if>
            <if test='payStateNode != null and payStateNode !="" '>
                and tosf.state_node_value = #{payStateNode}
            </if>
            <if test="status != null">
                AND toi.order_execute_status IN
                <foreach collection="status" index="index" item="st" open="(" close=")" separator=",">
                    #{st}
                </foreach>
            </if>
            <if test="idArray != null and idArray.size != 0 ">
                AND toi.code IN
                <foreach collection="idArray" item="orderCode" index="item" open="(" close=")" separator=",">
                    #{orderCode}
                </foreach>
            </if>
            <if test="bizCode != null">
                AND toi.order_business_code like concat('%', #{bizCode}, '%')
            </if>
            <if test="projectId != null">
                AND toi.company_project_id = #{projectId}
            </if>
            <if test="driverName != null">
                AND eui.real_name like concat('%', #{driverName}, '%')
            </if>
            <if test="driverPhone != null">
                AND eui.phone like concat('%', #{driverPhone}, '%')
            </if>
            <if test="lineGoodsId != null">
                AND toi.line_goods_rel_id = #{lineGoodsId}
            </if>
            <if test="lineGoodsRelIds != null and lineGoodsRelIds.size > 0">
                and toi.line_goods_rel_id in
                <foreach collection="lineGoodsRelIds" item="lineGoodsRelId" index="item" open="(" close=")" separator=",">
                    #{lineGoodsRelId}
                </foreach>
            </if>
            <if test="goodsId != null">
                and toi.goods_id = #{goodsId}
            </if>
            <if test="companyEntrust != null">
                and toi.company_entrust like concat('%', #{companyEntrust}, '%')
            </if>
            <if test="companyClient != null">
                and toi.company_client = #{companyClient}
            </if>
            <if test="licensePlate != null">
                AND eci.vehicle_number like concat('%', #{licensePlate} ,'%')
            </if>
            <if test="endAgentId != null">
                and toi.end_agent_id = #{endAgentId}
            </if>
            <if test="agentId != null">
                AND toi.agent_id = #{agentId}
            </if>
            <if test="carOwnerId != null">
                and toi.end_car_owner_id = #{carOwnerId}
            </if>
            <if test="captainId != null">
                and toi.end_car_owner_id = #{captainId}
            </if>
            <if test="fhsTime != null">
                AND toi.deliver_order_time >= #{fhsTime}
            </if>
            <if test="fheTime != null">
                AND toi.deliver_order_time &lt;= #{fheTime}
            </if>
            <if test="shsTime != null">
                AND toi.receive_order_time >= #{shsTime}
            </if>
            <if test="sheTime != null">
                AND toi.receive_order_time &lt;= #{sheTime}
            </if>
            <if test="fhbdsTime != null">
                AND (toi.deliver_weight_notes_time >= #{fhbdsTime} or tosup.create_time >= #{fhbdsTime})
            </if>
            <if test="fhbdeTime != null">
                AND (toi.deliver_weight_notes_time &lt;= #{fhbdeTime} or tosup.create_time &lt;= #{fhbdeTime})
            </if>
            <if test="shbdsTime != null">
                AND (toi.receive_weight_notes_time >= #{shbdsTime} or tosdown.create_time >= #{shbdsTime})
            </if>
            <if test="shbdeTime != null">
                AND (toi.receive_weight_notes_time &lt;= #{shbdeTime} or tosdown.create_time &lt;= #{shbdeTime})
            </if>
            <if test="inputSearch != null">
                AND (
                toi.order_business_code LIKE CONCAT('%', #{inputSearch}, '%')
                OR eci.vehicle_number LIKE CONCAT('%', #{inputSearch}, '%')
                )
            </if>
        </where>
        order by toi.id desc
    </select>

</mapper>