<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.THXPayWalletMapper">

    <sql id="Base_Column_List">
    id, account_id, zt_account_open_id, data_source, purse_category, account_balance,
    account_exp, account_inc, frozen_amount, entry_amount, withdraw_amount, remit_amount,
    remark, param1, param2, param3, param4, create_user, create_time, update_user, update_time,
    `enable`
  </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultType="com.lz.model.TZtWallet">
        select
        <include refid="Base_Column_List" />
        from t_zt_wallet
        where id = #{id,jdbcType=INTEGER}
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="com.lz.model.TZtWallet">
        update t_zt_wallet
        <set>
            <if test="accountId != null">
                account_id = #{accountId,jdbcType=INTEGER},
            </if>
            <if test="ztAccountOpenId != null">
                zt_account_open_id = #{ztAccountOpenId,jdbcType=INTEGER},
            </if>
            <if test="dataSource != null">
                data_source = #{dataSource,jdbcType=VARCHAR},
            </if>
            <if test="purseCategory != null">
                purse_category = #{purseCategory,jdbcType=VARCHAR},
            </if>
            <if test="accountBalance != null">
                account_balance = #{accountBalance,jdbcType=DECIMAL},
            </if>
            <if test="accountExp != null">
                account_exp = #{accountExp,jdbcType=DECIMAL},
            </if>
            <if test="accountInc != null">
                account_inc = #{accountInc,jdbcType=DECIMAL},
            </if>
            <if test="frozenAmount != null">
                frozen_amount = #{frozenAmount,jdbcType=DECIMAL},
            </if>
            <if test="entryAmount != null">
                entry_amount = #{entryAmount,jdbcType=DECIMAL},
            </if>
            <if test="withdrawAmount != null">
                withdraw_amount = #{withdrawAmount,jdbcType=DECIMAL},
            </if>
            <if test="remitAmount != null">
                remit_amount = #{remitAmount,jdbcType=DECIMAL},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="param1 != null">
                param1 = #{param1,jdbcType=VARCHAR},
            </if>
            <if test="param2 != null">
                param2 = #{param2,jdbcType=VARCHAR},
            </if>
            <if test="param3 != null">
                param3 = #{param3,jdbcType=VARCHAR},
            </if>
            <if test="param4 != null">
                param4 = #{param4,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="enable != null">
                `enable` = #{enable,jdbcType=BOOLEAN},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByEndUserId" resultType="com.lz.model.TZtWallet">
        select tzw.*
        from t_zt_wallet tzw
        left join t_enduser_account tea on tea.account_id = tzw.account_id
        where tzw.enable = 0
        and tea.enable = 0
        and tea.enduser_id = #{endUserId}
        and tzw.data_source = 'CD'
    </select>

    <select id="selectByCarrierCompanyPartnerAccId" resultType="com.lz.model.TZtWallet">
        select tzw.*
        from t_zt_wallet tzw
         left join t_zt_account_open_info tzaoi on tzw.zt_account_open_id = tzaoi.id
        where tzw.enable = 0
          and tzaoi.partner_acc_id = #{partnerAccId}
          and tzw.data_source = #{dataSource}
    </select>

    <select id="selectByEnduserPartnerAccId" resultType="com.lz.model.TZtWallet">
        select tzw.*
        from t_zt_wallet tzw
         left join t_zt_account_open_info tzaoi on tzw.zt_account_open_id = tzaoi.id
        where tzw.enable = 0
          and tzaoi.partner_acc_id = #{partnerAccId}
          and tzw.data_source = 'CD'
    </select>

    <select id="selectByAccountId" resultType="com.lz.model.TZtWallet">
        select tzw.*
        from t_zt_wallet tzw
        where tzw.enable = 0
        and tzw.account_id = #{accountId}
        and tzw.data_source = 'CD'
    </select>

</mapper>