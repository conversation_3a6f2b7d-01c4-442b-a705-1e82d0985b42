eureka.client.serviceUrl.defaultZone=*********************************************/eureka/
spring.cloud.config.discovery.serviceId=lgt-app-config
spring.cloud.config.discovery.enabled=true
spring.cloud.config.profile=test
spring.cloud.config.label=master

logging.level.com.lz.dao=DEBUG

spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL5InnoDBDialect
spring.data.redis.repositories.enabled=false
spring.jpa.open-in-view=true
spring.jpa.hibernate.ddl-auto=update
spring.data.jpa.repositories.enabled=true

mybatis.configuration.map-underscore-to-camel-case=true

server.tomcat.accesslog.buffered=true
server.tomcat.accesslog.directory=/usr/local/src/log/access
server.tomcat.accesslog.enabled=true
server.tomcat.accesslog.file-date-format=.yyyy-MM-dd
server.tomcat.accesslog.pattern=common
server.tomcat.accesslog.prefix=access_log
server.tomcat.accesslog.rename-on-rotate=false
server.tomcat.accesslog.request-attributes-enabled=false
server.tomcat.accesslog.rotate=true
server.tomcat.accesslog.suffix=.log
htPath=/root/deploy_cmp/temp/

domainURL: http://ms.luzounet.com

accessId = 0176990385412e34d163f3444f833fa2

EtcPath = http://124.128.63.186:8210/invoice

## RocketMQ
rocketmq.groupId.GID_ORDER_MQ=GID_ORDER_MQ
rocketmq.groupId.GID_JD_PAY_COMMON_DRIVER=GID_JD_PAY_COMMON_DRIVER
rocketmq.groupId.GID_JD_PAY_COMMON_CAPTAIN=GID_JD_PAY_COMMON_CAPTAIN
rocketmq.groupId.GID_JD_PAY_MANAGER_DRIVER=GID_JD_PAY_MANAGER_DRIVER
rocketmq.groupId.GID_JD_PAY_MANAGER_CAPTAIN=GID_JD_PAY_MANAGER_CAPTAIN
rocketmq.groupId.GID_JD_PAY_PREPAYMENT=GID_JD_PAY_PREPAYMENT
rocketmq.groupId.GID_JD_PAY_WITHDRAW=GID_JD_PAY_WITHDRAW
rocketmq.groupId.GID_JD_MEMBER=GID_JD_MEMBER
rocketmq.groupId.GID_TEST_MQ=GID_TEST_MQ
rocketmq.accessKey=LTAI5tEBqBfojE4kD4KYYg1Y
rocketmq.secretKey=******************************
rocketmq.nameSrvAddr=http://MQ_INST_1364524203166562_BXSHCMfU.mq-internet-access.mq-internet.aliyuncs.com:80


spring.http.multipart.maxFileSize=50Mb
spring.http.multipart.maxRequestSize=50Mb