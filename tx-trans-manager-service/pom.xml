<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.lz</groupId>
	<artifactId>tx-trans-manager-service</artifactId>
	<version>0.0.1-SNAPSHOT</version>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
				<version>3.1</version>
                <configuration>
                    <source>1.6</source>
                    <target>1.6</target>
					<encoding>utf8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <properties>
		<!--<lcn.last.version>4.1.0</lcn.last.version>-->
		<codingapi.txlcn.version>5.0.2.RELEASE</codingapi.txlcn.version>
	</properties>
	<dependencies>
		<!--<dependency>
			<groupId>com.codingapi</groupId>
			<artifactId>transaction-springcloud</artifactId>
			<version>${lcn.last.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.codingapi</groupId>
			<artifactId>tx-plugins-db</artifactId>
			<version>${lcn.last.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>-->
		<!--update by xyz 219.7.2 分布式事务升级到5.0.2-->
		<dependency>
			<groupId>com.codingapi.txlcn</groupId>
			<artifactId>txlcn-tc</artifactId>
			<version>${codingapi.txlcn.version}</version>
		</dependency>

		<dependency>
			<groupId>com.codingapi.txlcn</groupId>
			<artifactId>txlcn-txmsg-netty</artifactId>
			<version>${codingapi.txlcn.version}</version>
		</dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>RELEASE</version>
        </dependency>


    </dependencies>
</project>