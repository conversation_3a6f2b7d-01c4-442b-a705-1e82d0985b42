<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.lz</groupId>
        <artifactId>lgt-app</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <artifactId>lgt-app-system</artifactId>
    <packaging>jar</packaging>
    <url>http://maven.apache.org</url>
    <dependencies>
        <dependency>
            <groupId>com.lz</groupId>
            <artifactId>lgt-app-common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--MD5加密用到-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.1</version>
        </dependency>
        <dependency>
            <groupId>com.lz</groupId>
            <artifactId>lgt-app-member-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.lz</groupId>
            <artifactId>lgt-app-system-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.lz</groupId>
            <artifactId>lgt-app-schedule-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.lz</groupId>
            <artifactId>lgt-app-logging</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.lz</groupId>
            <artifactId>lgt-app-fastdfs-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>commons-fileupload</artifactId>
                    <groupId>commons-fileupload</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>bcpkix-jdk15on</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.lz</groupId>
            <artifactId>tx-trans-manager-service</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.lz</groupId>
            <artifactId>lgt-app-message</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.lz</groupId>
            <artifactId>lgt-app-oss-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <!-- 阿里GTS -->
        <!--<dependency>
            <groupId>com.taobao.txc</groupId>
            <artifactId>txc-client</artifactId>
            <version>2.0.72</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/jar/txc-client-2.0.72.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.alibaba.dauth</groupId>
            <artifactId>sdk-client</artifactId>
            <version>1.2.3</version>
        </dependency>
        <dependency>
            <groupId>com.taobao.diamond</groupId>
            <artifactId>diamond-client</artifactId>
            <version>edas-3.7.3</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
            <version>1.1.7</version>
        </dependency>-->
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
            <!-- mybatis generator 自动生成代码插件 -->
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.2</version>
                <configuration>
                    <configurationFile>${basedir}/src/main/resources/generator/generatorConfig.xml</configurationFile>
                    <overwrite>true</overwrite>
                    <verbose>true</verbose>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
