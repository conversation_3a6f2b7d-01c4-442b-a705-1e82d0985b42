eureka.client.serviceUrl.defaultZone=*********************************************/eureka/
spring.cloud.config.discovery.serviceId=lgt-app-config
spring.cloud.config.discovery.enabled=true
spring.cloud.config.profile=test
spring.cloud.config.label=master

logging.level.com.lz.system.dao=DEBUG

spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL5InnoDBDialect
spring.data.redis.repositories.enabled=false
spring.jpa.open-in-view=true
spring.jpa.hibernate.ddl-auto=update
spring.data.jpa.repositories.enabled=true

mybatis.configuration.map-underscore-to-camel-case=true


server.tomcat.accesslog.buffered=false
server.tomcat.accesslog.directory=/usr/local/src/log/access
server.tomcat.accesslog.enabled=true
server.tomcat.accesslog.file-date-format=.yyyy-MM-dd
server.tomcat.accesslog.pattern=common
server.tomcat.accesslog.prefix=access_log
server.tomcat.accesslog.rename-on-rotate=false
server.tomcat.accesslog.request-attributes-enabled=false
server.tomcat.accesslog.rotate=true
server.tomcat.accesslog.suffix=.log
server.use-forward-headers = true
server.tomcat.remote-ip-header = X-Real-IP
server.tomcat.protocol-header = X-Forwarded-Proto

#��Ⱥzuul����޳�ʹ��
eureka.instance.lease-renewal-interval-in-seconds=1
eureka.instance.lease-expiration-duration-in-seconds=2
eureka.client.registry-fetch-interval-seconds=5