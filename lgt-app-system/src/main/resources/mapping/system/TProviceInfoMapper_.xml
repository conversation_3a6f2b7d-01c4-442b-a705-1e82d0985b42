<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.system.dao.TProviceInfoMapper">

  <!--查询所有省名和Code-->
  <resultMap id="treeCity" type="com.lz.system.util.ProvinceCityCounty">
    <id column="value" jdbcType="VARCHAR" property="value" />
    <id column="pid" jdbcType="VARCHAR" property="pid" />
    <id column="label" jdbcType="VARCHAR" property="label" />
    <collection property="children" column="value" ofType="com.lz.system.util.ProvinceCityCounty" select="selectAllCity" ></collection>
  </resultMap>
  <select id="selectAllProvice" resultMap="treeCity">
    SELECT
        tpi.provice_code AS `value`,
        0 AS pid,
        tpi.provice_name AS `label`
    FROM
        t_provice_info tpi
  </select>
  <resultMap id="areaCity" type="com.lz.system.util.ProvinceCityCounty">
    <id column="value" jdbcType="VARCHAR" property="value" />
    <id column="pid" jdbcType="VARCHAR" property="pid" />
    <id column="label" jdbcType="VARCHAR" property="label" />
    <collection property="children" column="value" ofType="com.lz.system.util.ProvinceCityCounty" select="selectAllArea" ></collection>
  </resultMap>
  <select id="selectAllCity" resultMap="areaCity">
    SELECT
        tpi.city_code AS `value`,
        tpi.provice_code AS pid,
        tpi.city_name AS `label`
    FROM
        t_city_info tpi
    where provice_code = #{value}
  </select>
  <select id="selectAllArea" resultType="com.lz.system.util.ProvinceCityCounty">
    SELECT
        tpi.country_code AS `value`,
        tpi.city_code AS pid,
        tpi.country_name AS `label`
    FROM
        t_country_info tpi
    where  city_code = #{value}
  </select>

</mapper>