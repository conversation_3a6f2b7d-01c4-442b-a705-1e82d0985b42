<?xml version="1.0" encoding="UTF-8"?>
		<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
				"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.system.dao.TSysPermissionsMapper">

<resultMap id="BaseRoleResultMap" type="com.lz.system.vo.TSysPermissionsVo">
	<id column="id" property="id" />
	<result column="name" property="name" jdbcType="VARCHAR"/>
	<result column="type" property="type" />
	<result column="pid" property="pid" jdbcType="INTEGER"/>
	<result column="alias" property="alias" jdbcType="VARCHAR" />
	<result column="url" property="url" jdbcType="VARCHAR" />
	<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
	<!--<result column="http_type" property="httpType" jdbcType="VARCHAR" />-->
	<result column="status" property="status" jdbcType="VARCHAR" />
	<!--<collection property="children" ofType="com.lz.system.vo.TSysPermissionsVo"
				javaType="java.util.ArrayList" select="getChildByParentId"
				column="id">
	</collection>-->
</resultMap>
<resultMap id="BasePermissionTreeResultMap" type="com.lz.system.vo.PermissionsVO">
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="name" property="label" jdbcType="VARCHAR"/>
</resultMap>

<resultMap id="ResultMapSelectPermissionsTree" type="com.lz.system.vo.TreeVO">
	<id column="id" jdbcType="INTEGER" property="id" />
	<result column="name" jdbcType="VARCHAR" property="label" />
	<collection property="children" column="id" ofType="com.lz.system.vo.TreeVO" select="getButtenPermissions" ></collection>
</resultMap>
<resultMap id="ResultMapgetAllMenuField" type="com.lz.system.vo.TreeVO2">
	<id column="id" jdbcType="INTEGER" property="id" />
	<result column="roleid" jdbcType="VARCHAR" property="roleid" />
	<collection property="children" ofType="com.lz.system.vo.TreeVO2" column="{id=id,roleid=roleid}" select="getFieldByMenu" ></collection>
</resultMap>
<resultMap id="ResultMapgetAllField" type="com.lz.system.vo.FieldVO">
	<id column="id" jdbcType="INTEGER" property="id" />
	<result column="name" jdbcType="VARCHAR" property="label" />
	<result column="code" jdbcType="VARCHAR" property="property" />
	<result column="sort" jdbcType="INTEGER" property="sort" />
	<result column="is_sort" jdbcType="BIT" property="isSort" />
</resultMap>
	<!--获取权限的父级ID Yan-->
	<select id="getPmsFatherId" parameterType="java.lang.Integer" resultType="com.lz.system.model.TSysPermissions">
		SELECT
			tsp.id,
			tsp.pid,
			tsp.type
		FROM t_sys_permissions tsp
		WHERE tsp.id = #{id}
	</select>

	<!-- Yan 逻辑删除权限-->
	<update id="logicDeletePermission" parameterType="hashmap">
		UPDATE t_sys_permissions tsp
		SET `enable` = 1
		WHERE EXISTS (
		SELECT *
		FROM (
			<foreach collection="ids" item="id" index="index" open="" close="" separator="UNION">
				SELECT #{id} as id
			</foreach>
		) a
		WHERE tsp.id = a.id
		)
	</update>
	<!-- Yan 逻辑删除Menu-->
	<update id="logicDeleteMenu" parameterType="hashmap">
		UPDATE t_sys_permissions tsp
		SET `enable` = 1
		WHERE EXISTS (
			SELECT *
			FROM (
				<foreach collection="ids" item="id" index="index" open="" close="" separator="UNION">
					SELECT #{id} as id
				</foreach>
			) a
			WHERE tsp.id = a.id
		)
	</update>

<select id="getSysPermissions" resultMap="BaseRoleResultMap" parameterType="com.lz.system.vo.TSysPermissionsVo">
	select p.id, p.name, p.pid, p.create_time, p.url, p.type, case p.type when 1 then '菜单' when 2 then '按钮' when 3 then '功能' end typeName,
	    p.org_id, p.alias, p.http_type as httpType, o.name orgName, case p.enable when 0 then '启用' when 1 then '禁用' end as status
	from t_sys_permissions p
	    left join t_oper_org o on p.org_id = o.id
	where 1=1 and p.`enable` = 0
	<if test="orgId != null">
		and p.org_id = #{orgId}
	</if>
	<if test="type != null">
		and p.type = #{type}
	</if>
	<if test="pid != null">
		and p.pid = #{pid}
	</if>
</select>
	<select id="getFirstLevelPermission" resultType="com.lz.system.vo.menu.MenuPermissionTreeVO">
		select p.id, p.name label, p.pid, p.type
		from t_sys_permissions p
		where p.enable =0 and  EXISTS (
		    <if test="list != null and list.size() != 0">
				select *
				from (
					 <foreach collection="list" item="menu" index="index" open="" close="" separator="UNION">
						 select #{menu.id} as id
					 </foreach>
					) a
				where a.id = p.pid
			</if>
		    <if test="list == null or list.size() == 0">
				select true
			</if>
		)
		<if test="orgId != null">
			and p.org_id = #{orgId}
		</if>
		<if test="type != null">
			and p.type = #{type}
		</if>
	</select>

	<select id="getUserPerissions" parameterType="java.lang.String" resultType="java.lang.String">
		select p.alias role
		from t_user_role ur
		  left join t_role_permissions rp on ur.role_id = rp.role_id
		  left join t_sys_permissions p on rp.permission_id = p.id
		where ur.user_id= #{userId}
	</select>

	<select id="getPermissionByMenuID" resultMap="BasePermissionTreeResultMap" parameterType="java.lang.Integer">
		select id,name from t_sys_permissions where pid = #{id}
	</select>

	<select id="getUserPermissions" parameterType="com.lz.system.model.User" resultType="com.lz.system.dto.menu.PermissionsDTO">
		SELECT
			p.id,
			p.id,
			rp.id,
			rp.permission_id,
			p.pid,
			p. NAME,
			p.url,
			p.sort,
			p.type,
			u.org_id
		FROM
			t_user_role ur
				LEFT JOIN t_role_permissions rp ON ur.role_id = rp.role_id
				LEFT JOIN t_sys_permissions p ON rp.permission_id = p.id
				left join t_sys_user u on ur.user_id = u.id
		WHERE
			ur.id = #{id} and u.org_id = #{orgId}
	</select>
	<select id="getAllPermission" resultType="com.lz.system.dto.menu.PermissionsDTO">
		SELECT
			id, pid, NAME, url, sort, type
		FROM
			t_sys_permissions
	</select>

	<select id="getPermissionByRole" parameterType="java.lang.Integer" resultType="com.lz.system.dto.menu.PermissionsDTO">
		SELECT
			p.id, p.pid, p.NAME, p.url, p.sort, p.type
		FROM
			t_role_permissions trp
				left join t_sys_permissions p on trp.permission_id = p.id
		where trp.role_id = #{id}
	</select>

	<select id="getSuperAdminPermission" resultType="com.lz.system.dto.menu.PermissionsDTO">
		SELECT
			id, pid, NAME, url, sort, type
		FROM
			t_sys_permissions
	</select>

	<select id="treeList" parameterType="com.lz.system.model.TSysPermissions" resultType="com.lz.system.dto.menu.MenuTree">
		select id, name label, pid
		from t_sys_permissions
		where enable =0
		<if test="type != null">
			and type = #{type}
		</if>
		<if test="orgId != null">
			and org_id = #{orgId}
		</if>
	</select>
	<select id="menus" parameterType="com.lz.system.vo.menu.MenuQueryVO" resultType="com.lz.system.dto.menu.Menus">
		SELECT
		p.id,
		p.NAME,
		p.pid,
		p.sort,
		p.icon,
		p.url,
		p.create_time,
		p.component,
		p.i_frame iFrame,
		p.enable,
		o.`name` org_name,
		o.id org_id
		FROM
		t_sys_permissions p
		LEFT JOIN t_oper_org o on p.org_id = o.id
		where 1=1 and p.type = 1 and p.enable = 0
		<if test="name != null">
			and p.name like concat('%', #{name, jdbcType=VARCHAR},'%')
		</if>
		<if test="orgId != null">
			and p.org_id = #{orgId}
		</if>
		order by p.sort
	</select>
	<!--优化登录-->
	<select id="menuListNew" parameterType="java.util.List" resultType="com.lz.system.dto.menu.MenuDTO">
		SELECT
			DISTINCT
			p.id,
			p.pid,
			p. NAME,
			p.url path,
			p.sort,
			p.component,
		       p.icon
		FROM
			t_role_permissions rp
				left JOIN t_sys_permissions p ON rp.permission_id = p.id
		WHERE EXISTS (
			SELECT *
			FROM (
				<foreach collection="roleIds" index="index" item="roleId" separator="UNION">
					SELECT #{roleId} as id
				</foreach>
			) a
			WHERE rp.role_id = a.id
		) and type = 1 and p.enable = 0
		order by sort
	</select>
	<select id="superAdminMenuList" parameterType="java.lang.Integer" resultType="com.lz.system.dto.menu.MenuDTO">
		SELECT
			p.id,
			p.pid,
			p. NAME,
			p.url path,
			p.sort,
			p.component,
			p.icon
		FROM
			 t_sys_permissions p
		WHERE
			p.org_id =#{orgId, jdbcType=VARCHAR} and type = 1
		order by sort
	</select>
	<select id="orgId" parameterType="com.lz.system.vo.menu.MenuQueryVO" resultType="java.lang.Integer">
		SELECT
			org_id
		FROM
			t_sys_permissions
		where 1=1
		<if test="orgId != null">
			org_id = #{orgId}
		</if>
		GROUP BY
			org_id
	</select>

	<!--根据角色ID查询按钮返回 Set-->
	<select id="selectButtonSole" parameterType="java.lang.Integer" resultMap="BaseResultMap">
		SELECT DISTINCT
			*
		FROM
			t_sys_permissions tsp
		LEFT JOIN t_role_permissions trp ON tsp.id = trp.permission_id
		WHERE
			tsp.`enable` = 0
		AND tsp.type = 2
		AND trp.role_id IN
		<foreach collection="roleIds" index="index" item="roleId" open="(" separator="," close=")">
			#{roleId}
		</foreach>
		ORDER BY sort
	</select>

	<select id="getButtenPermissions"  parameterType="java.lang.Integer" resultMap="ResultMapSelectPermissionsTree">
		SELECT id,`name` FROM t_sys_permissions WHERE pid = #{id} and (type = 2 or type = 3)
	</select>

	<select id="getAllMenuField"  parameterType="java.lang.Integer" resultMap="ResultMapgetAllMenuField">
		SELECT id,`name`,(SELECT GROUP_CONCAT(role_id) FROM t_user_role WHERE  user_id = #{userid}) roleid
		FROM t_sys_permissions
		WHERE type=1 and enable =0
		<if test="orgid != null">
			and org_id = #{orgid}
		</if>
		<if test="orgid == null">
			and org_id is null
		</if>
	</select>
	<select id="getFieldByMenu"  resultType="java.lang.Integer">
		SELECT f.id
		FROM t_sys_fields f
		WHERE
        EXISTS (
        <if test="list != null and list.size() != 0">
            SELECT *
            FROM (
            <foreach collection="list" item="id" index="index" open="" close="" separator="UNION">
                SELECT #{id} as menuId
            </foreach>
            ) a
            WHERE f.menu_id = a.menuId
        </if>
        <if test="list == null or list.size() == 0">
            select true
        </if>
        )
	</select>

	<insert id="batchInsertField">
		insert into t_role_field(role_id, field_id) values
		<foreach collection="list" item="menuId" index="index" separator=",">
			(#{roleId,}, #{menuId})
		</foreach>
	</insert>

	<select id="getUserMenuField" resultType="com.lz.system.model.TSysFields" parameterType="hashmap">
		SELECT
			DISTINCT sf.`code` property , sf.id, sf.sort, sf.name, sf.is_sort
		FROM
			t_sys_fields sf
				LEFT JOIN t_role_field rf on sf.id = rf.field_id
		where sf.menu_id=#{menuId} and sf.sort is not null and rf.role_id in
		  <foreach collection="list" index="index" item="id" open="(" close=")" separator=",">
			  #{id}
		  </foreach>
		ORDER BY sf.sort
	</select>

	<select id="getMenuPermissionTree" parameterType="com.lz.system.model.TSysPermissions"
			resultType="com.lz.system.vo.menu.MenuPermissionTreeVO">
		select id, name label, pid, create_time, url, type, org_id, alias, http_type
		from t_sys_permissions
		where 1=1 and `enable` = 0
		<if test="orgId != null">
			and org_id = #{orgId}
		</if>
		<if test="type != null">
			and type = #{type}
		</if>
		<if test="pid != null">
			and pid = #{pid}
		</if>
	</select>
	<!--获取pid下的最后一个按钮的 sort Yan-->
	<select id="getLastPermissionSort" parameterType="int" resultType="java.lang.Integer">
		SELECT
			tsp.sort
		FROM t_sys_permissions tsp
		WHERE tsp.pid = #{pid}
		ORDER BY tsp.sort DESC
		LIMIT 1
	</select>
	<select id="selectByMenu" resultType="com.lz.system.model.TSysPermissions" parameterType="com.lz.system.vo.UserRoleVO">
		select
		DISTINCT
			p.*
		from t_sys_permissions p
		left join t_role_permissions rp on rp.permission_id = p.id
		where  p.type=1 and p.pid !=0 and rp.role_id in
		<foreach collection="list" index="index" item="id" open="(" close=")" separator=",">
			#{id}
		</foreach>
	</select>

	
</mapper>