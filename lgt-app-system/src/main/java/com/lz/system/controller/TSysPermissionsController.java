package com.lz.system.controller;

import com.lz.common.model.CodeEnum;
import com.lz.common.util.ResultUtil;
import com.lz.system.model.TSysPermissions;
import com.lz.system.service.TSysMenuService;
import com.lz.system.service.TSysPermissionsService;
import com.lz.system.vo.TSysPermissionsVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * auto dingweibo
 * 权限管理
 */
@RestController
@RequestMapping("/system/tSysPermissions")
public class TSysPermissionsController {

    @Autowired
    private TSysPermissionsService tSysPermissionsService;
    @Autowired
    private TSysMenuService sysMenuService;

    @RequestMapping("/findList")
    public ResultUtil findList(TSysPermissions record){
        return tSysPermissionsService.findList(record);
    }



    /**
     * 新增
     * @param record
     * @return
     */
    @PostMapping("/save")
    public ResultUtil save(@RequestBody TSysPermissions record){
        tSysPermissionsService.save(record);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),"");
    }
    /**
     * 修改
     * @param record
     * @return
     */
    @PutMapping("/update")
    public ResultUtil update(@RequestBody TSysPermissions record){
        tSysPermissionsService.update(record);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),"");
    }
    /**
     * 删除
     * @param pems
     * @return
     */
    @DeleteMapping("/delete")
    public ResultUtil delete(@RequestBody TSysPermissionsVo pems){
        tSysPermissionsService.delete(pems.getIds());
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),"");
    }

    @GetMapping("/getRoleMenuTree")
    public ResultUtil getRoleMenuTree(@RequestParam(value = "orgId") Integer orgId){
        ResultUtil roleMenuTree = tSysPermissionsService.getRoleMenuTree(orgId);
        return roleMenuTree;
    }

    @GetMapping("/getPermissionTree")
    public ResultUtil getPermissionTree(@RequestParam(value = "orgId") Integer orgId) {
        TSysPermissions permissions = new TSysPermissions();
        permissions.setOrgId(orgId);
        ResultUtil tree = sysMenuService.tree(permissions);
        return tree;
    }

    @PostMapping("/selectByMenu")
    public List<TSysPermissions> selectByMenu(){

        return tSysPermissionsService.selectByMenu();
    }
}
