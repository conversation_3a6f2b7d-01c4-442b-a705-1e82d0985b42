package com.lz.system.controller;

import com.lz.common.exception.BadRequestException;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.ResultUtil;
import com.lz.system.dto.menu.Menus;
import com.lz.system.model.TSysPermissions;
import com.lz.system.service.TSysMenuService;
import com.lz.system.vo.menu.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @date 2019-03-25
*/
@Slf4j
@RestController
@RequestMapping("/system/menu")
public class TSysMenuController {

    @Autowired
    private TSysMenuService tSysMenuService;

    private static final String ENTITY_NAME = "tSysMenu";

    /**
     * 菜单树形结构
     * <AUTHOR>
     * @return
     */
    @GetMapping("/tSysMenu/tree")
    public ResultUtil menuTree(@RequestParam(value = "orgId") Integer orgId){
        TSysPermissions menu = new TSysPermissions();
        //如果orgId不为空
        if (null != orgId){
            menu.setOrgId(orgId);
        }else {
            if (!CurrentUser.isSuperAdmin()) {
                //如果orgId为空，默认：1.查询当前机构下的所有菜单 2.如果是超级管理员
                Integer orgID = null == CurrentUser.getCurrentUserOrgID() ? null : CurrentUser.getCurrentUserOrgID();
                menu.setOrgId(orgID);
            }
        }
        ResultUtil resultUtil = tSysMenuService.tree(menu);
        return resultUtil;
    }

    /**
     * 菜单管理列表
     * <AUTHOR>
     * @param menuQueryVO
     * @return
     */
    @GetMapping(value = "/tSysMenu/menus")
    public ResultUtil menus(@ModelAttribute MenuQueryVO menuQueryVO){
        if (!CurrentUser.isSuperAdmin()) {
            Integer orgID = null == CurrentUser.getCurrentUserOrgID() ? null : CurrentUser.getCurrentUserOrgID();
            menuQueryVO.setOrgId(orgID);
        }
        List<Menus> menus = tSysMenuService.menus(menuQueryVO);
        ResultUtil result = tSysMenuService.buildTree(menus);
        return result;
    }


    /**
     * 新增菜单
     * <AUTHOR>
     */
    @PostMapping(value = "/tSysMenu")
    public ResultUtil create(@Valid @RequestBody MenuAddVO resources){
        ResultUtil result = tSysMenuService.add(resources);
        return result;
    }

    /**
     * 修改菜单
     * <AUTHOR>
     * @param resources
     * @return
     */
    @PutMapping(value = "/tSysMenu")
    public ResultUtil update(@Valid @RequestBody MenuUpdateVO resources){
        if (resources.getId() == null) {
            throw new BadRequestException(ENTITY_NAME +" ID Can not be empty");
        }
        if (resources.getId().equals(resources.getPid())){
            return ResultUtil.error("菜单上级类目不能是自己");
        }
        ResultUtil result = tSysMenuService.update(resources);
        return result;
    }

    /**
     * 删除菜单
     *  <AUTHOR>
     * @param menuVO
     * @return
     */
    @DeleteMapping(value = "/tSysMenu")
    @PreAuthorize("hasAnyAuthority('/lgt-app-system/lgt-app-system/system/menu/tSysMenu')")
    public ResultUtil delete(@RequestBody MenuVO menuVO){
        tSysMenuService.delete(menuVO);
        return ResultUtil.ok(HttpStatus.OK);
    }

    /**
     * -****************************************桑斌
     */
    /**
     * 获取菜单下的按钮树
     * @param id  菜单id
     * @return
     */
    @ApiOperation(value = "获取所有菜单下的按钮功能权限", httpMethod = "GET")
    @GetMapping(value = "/tSysMenu/getButtenPermissions")
    public ResultUtil getButtenPermissions(Integer id){
        try {
            return ResultUtil.ok(tSysMenuService.getButtenPermissions(id));
        } catch (Exception e) {
            log.error("获取所有菜单下的按钮功能权限失败",e);
            return ResultUtil.error("获取所有菜单下的按钮功能权限失败");
        }
    }

    /**
     * 获取授权的按钮功能
     * @param id 角色id
     * @return
     */
    @ApiOperation(value = "", httpMethod = "GET")
    @GetMapping(value = "/tSysMenu/getMyPermissions")
    public ResultUtil getMyPermissions(Integer id){
        try {
            List<Map<String,Object>> ids = tSysMenuService.getMyPermissions(id);
            return ResultUtil.ok(ids);
        } catch (Exception e) {
            log.error("获取授权的按钮功能失败",e);
            return ResultUtil.error("获取授权的按钮功能失败");
        }
    }

    /**
     * 根据角色id 获取已授权菜单
     * @param id
     * @return
     */
    @GetMapping(value = "/tSysMenu/getHasMenu")
    public ResultUtil getHasMenu(Integer id){
        try {
            return ResultUtil.ok(tSysMenuService.getHasMenu(id));
        } catch (Exception e) {
            log.error("获取已有菜单权限失败",e);
            return ResultUtil.error("获取已有菜单权限失败");
        }
    }

    /**
     * 根绝菜单id获取获取所有的字段
     * @param id  菜单id
     * @return
     */
    @GetMapping(value = "/tSysMenu/getMenuFiled")
    public ResultUtil getMenuFiled(Integer id){
        try {

            return ResultUtil.ok(tSysMenuService.getMenuFiled(id));
        } catch (Exception e) {
            log.error("获取所有菜单字段失败",e);
            return ResultUtil.error("获取所有菜单字段失败");
        }
    }

    /**
     * 根绝角色获取所有的已经授权的可见字段
     * @param id
     * @return
     */
    @GetMapping(value = "/tSysMenu/getHasMenuFiled")
    public ResultUtil getHasMenuFiled(Integer id){
        try {
            return ResultUtil.ok(tSysMenuService.getHasMenuFiled(id));
        } catch (Exception e) {
            log.error("获取已有所有菜单字段权限失败",e);
            return ResultUtil.error("获取已有所有菜单字段权限失败");
        }
    }

    /**
     * 保存权限
     * @param id 角色id
     * @param MenuIds 菜单按钮权限
     * @param MenuFalfIds 菜单按钮权限 半选
     * @param FieldIds 字段权限
     * @return
     */
    @PostMapping(value = "/tSysMenu/savePermissions")
    public ResultUtil savePermissions(@RequestParam("id")Integer id ,@RequestParam("MenuIds") Integer[] MenuIds ,@RequestParam("MenuFalfIds") Integer[]  MenuFalfIds,@RequestParam("FieldIds") Integer[] FieldIds){
        try {
            //tSysMenuService.saveMenu(id,MenuIds,MenuFalfIds,FieldIds);
            return ResultUtil.ok();
        } catch (Exception e) {
            log.error("保存菜单权限失败",e);
            return ResultUtil.error("保存菜单权限失败");
        }
    }

      @ApiOperation(value = "保存菜单权限", httpMethod = "POST")
      @PostMapping(value = "/tSysMenu/saveMenu")
      public ResultUtil saveMenu(@RequestBody() MenuPermissionVO menuPermissionVO){
          try {
              ResultUtil resultUtil = tSysMenuService.saveMenu(menuPermissionVO);
              return resultUtil;
          } catch (Exception e) {
              log.error("保存菜单权限失败",e);
              return ResultUtil.error("保存菜单权限失败");
          }
      }

    /**
     *
     * @param id 角色id
     * @param menuId 菜单id
     * @param FieldIds 字段id
     * @return
     */
      @ApiOperation(value = "保存可见字段权限", httpMethod = "POST")
      @PostMapping(value = "/tSysMenu/saveField")
      public ResultUtil saveField(@RequestParam("id")Integer id, @RequestParam("menuId") Integer menuId, @RequestParam("FieldIds") Integer[] FieldIds){
        try {
            tSysMenuService.saveField(id, menuId, FieldIds);
            return ResultUtil.ok();
        } catch (Exception e) {
            log.error("保存可见字段权限失败",e);
            return ResultUtil.error("保存可见字段权限失败");
        }
      }
//
//    /**
//     * 保存按钮功能权限
//     * @param id 角色id
//     * @param permissions  权限ids
//     * @return
//     */
//    @ApiOperation(value = "保存按钮功能权限", httpMethod = "POST")
//    @PostMapping(value = "/tSysMenu/savePermissions")
////    @PreAuthorize("hasAnyRole('ADMIN')")
//    public ResultUtil savePermissions(@RequestParam("id")Integer id ,@RequestParam("permissions") Integer[] permissions){
//        try {
//            tSysMenuService.savePermissions(id,permissions);
//            return ResultUtil.ok();
//        } catch (Exception e) {
//            log.error("保存按钮功能权限失败",e);
//            return ResultUtil.error("保存按钮功能权限失败");
//        }
//    }

    @ApiOperation(value = "根据菜单获取按钮", httpMethod = "GET")
    @PostMapping(value = "/tSysMenu/getPermissionByMenuID")
    public ResultUtil getPermissionByMenuID(@RequestParam("id")Integer id){
        try {

            return ResultUtil.ok(tSysMenuService.getPermissionByMenuID(id));
        } catch (Exception e) {
            log.error("根据菜单获取按钮失败",e);
            return ResultUtil.error("根据菜单获取按钮失败");
        }
    }

    @ApiOperation(value = "根据菜单获取字段", httpMethod = "GET")
    @PostMapping(value = "/tSysMenu/getFieldByMenuID")
    public ResultUtil getFieldByMenuID(@RequestParam("id")Integer id){
        try {
            return ResultUtil.ok(tSysMenuService.getFieldByMenuID(id));
        } catch (Exception e) {
            log.error("根据菜单获取字段失败",e);
            return ResultUtil.error("根据菜单获取字段失败");
        }
    }
}