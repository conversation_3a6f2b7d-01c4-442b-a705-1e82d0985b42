package com.lz.system.controller;

import com.lz.common.util.ResultUtil;
import com.lz.system.model.TCityInfo;
import com.lz.system.service.TCityInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019/4/25 - 10:33
 **/
@RestController
@RequestMapping(value = "cityInfo")
public class TCityInfoController {

    @Autowired
    private TCityInfoService cityInfoService;

    @PostMapping(value = "/selectObject")
    public ResultUtil selectObject(@RequestBody TCityInfo cityInfo) {
        ResultUtil resultUtil = cityInfoService.selectObject(cityInfo);
        return resultUtil;
    }

    @PostMapping(value = "/selectFromEndCityCode")
    public ResultUtil selectFromEndCityCode(@RequestBody Map param ) {
        ResultUtil resultUtil = cityInfoService.selectFromEndCityCode(param);
        return resultUtil;
    }

    /**
     * 默认加载省市县名和code
     */
    @PostMapping(value = "/loadCityTree")
    public void loadCityTreeToRedis() {
        cityInfoService.selectProvice();
    }
}
