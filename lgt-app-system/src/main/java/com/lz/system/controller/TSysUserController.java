package com.lz.system.controller;

import cn.hutool.crypto.digest.BCrypt;
import com.alibaba.fastjson.JSONObject;
import com.lz.aop.log.Log;
import com.lz.api.*;
import com.lz.common.config.RedisUtil;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.exception.BadRequestException;
import com.lz.common.model.CodeEnum;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.common.util.ThrowableUtil;
import com.lz.model.TBusinessBasic;
import com.lz.model.TEndUserInfo;
import com.lz.model.TLineUserRolePro;
import com.lz.model.TVerificationCodeLog;
import com.lz.sms.model.SmsReq;
import com.lz.sms.service.SmsClientService;
import com.lz.system.dto.FormTemplateDTO;
import com.lz.system.dto.TUserInfoDto;
import com.lz.system.dto.UserDTO;
import com.lz.system.dto.menu.MenuDTO;
import com.lz.system.model.*;
import com.lz.system.service.*;
import com.lz.system.vo.*;
import com.lz.vo.CompanyOpenInfoDetailsVO;
import com.lz.vo.TUserVo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.ObjectUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-03-20
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class TSysUserController {

    @Autowired
    private TSysUserService tSysUserService;

    @Autowired
    private TSysPermissionsService permissionsService;
    @Autowired
    private TSysRoleService roleService;

    @Autowired
    private TOperOrgService operOrgService;

    @Autowired
    private FastdfsAPI fastdfsAPI;

    @Autowired
    private OssAPI ossAPI;

    @Autowired
    private TLineUserRoleProAPI lineUserRoleProAPI;

    @Autowired
    private DictService dictService;


    private static final String ENTITY_NAME = "tSysUser";

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private SysParamService sysParamService;
    @Autowired
    private TSysMenuService sysMenuService;

    @Autowired
    private FormPrintService formPrintService;

    @Autowired
    private TEndSUserInfoAPI endSUserInfoAPI;

    @Resource
    private SmsClientService smsApi;

    @Resource
    private TVerificationCodeLogAPI verificationCodeLogAPI;

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 根据用户名查询用户
     *
     * @param name
     * @return REsultUtil
     */
    @PostMapping("/user/search")
    public ResultUtil searchUserByName(@RequestBody String name) {
        List<User> user = tSysUserService.findUser(name);
        if (user != null) {
            return ResultUtil.ok(user);

        } else {
            return ResultUtil.error("用户不存在");
        }
    }


    /**
     * 删除用户
     *
     * @param userInfoVO .ids  用户ID集合
     * @return
     * <AUTHOR>
     */
    @PostMapping(value = "/user/delete")
    public ResultUtil deleteById(@RequestBody UserInfoVO userInfoVO) {
        if (userInfoVO.getIds().length != 0) {
            tSysUserService.delete(userInfoVO.getIds());
        } else {
            return new ResultUtil(CodeEnum.ERROR.getCode(), "请选择数据");
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), "删除成功");
    }

    /**
     * 用户列表
     *
     * @param page
     * @param size
     * @param name
     * @return
     */
    @GetMapping(value = "/tSysUser/selectByPage")
    public ResultUtil selectByPage(Integer page, Integer size, String name) {
        try {
            Integer orgId = null;
            List<Integer> orgIds = null;
            //如果当前用户不是超级管理员，查询当前机构及机构下的用户
            if (!CurrentUser.isSuperAdmin()) {
                orgId = CurrentUser.getCurrentUserOrgID();
                orgIds = operOrgService.getOrgArray(orgId, new ArrayList<>());
            } else {
                orgIds = operOrgService.selectAllOrgId();
            }

            ResultUtil resultUtil = tSysUserService.selectByPage(page, size, name, orgIds);
            return resultUtil;
        } catch (Exception e) {
            log.error("YL060--", e);
            return ResultUtil.ok();
        }
    }

    @GetMapping(value = "/tSysUser/{id}")
    public ResultUtil getTSysUser(@PathVariable Integer id) {
        return ResultUtil.ok(tSysUserService.findById(id));
    }

    @Log("新增TSysUser")
    @PostMapping(value = "/tSysUser")
    public ResultUtil save(@Valid @RequestBody TSysUserAddVO uservo) {
        if (uservo.getUsername() == null) {
            throw new BadRequestException("A new " + ENTITY_NAME + " cannot already have an ID");
        }
        try {
            TSysUser user = new TSysUser();
            BeanUtils.copyProperties(uservo, user);
            String key = "Account" + user.getUsername();
            RLock lock = redissonClient.getLock(key);
            try {
                String pwd = DigestUtils.md5Hex(uservo.getPassword());
                user.setPassword(pwd);
                return tSysUserService.addUser(user, uservo.getRoleIds());
            } catch (Exception e) {
                e.printStackTrace();
                log.error("新增失败, {}", ThrowableUtil.getStackTrace(e));
                return ResultUtil.error("新增失败");
            } finally {
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("新增失败, {}", ThrowableUtil.getStackTrace(e));
            return ResultUtil.error("新增失败");
        }
    }

    @Log("修改TSysUser")
    @PutMapping(value = "/tSysUser")
    public ResultUtil update(@RequestBody TSysUserAddVO uservo) {
        if (uservo.getUsername() == null || uservo.getId() == null) {
            throw new BadRequestException("A new " + ENTITY_NAME + " cannot already have an ID");
        }
//        UserInfoVO userInfoVO = tSysUserService.getUserInfo(uservo.getUsername());

//        if (userInfoVO != null){
//            return ResultUtil.error("用户账号已存在");
//        }
        try {
            tSysUserService.updateUser(uservo.getId(), uservo.getOrgId(), uservo.getNickname(),
                    uservo.getUsername(), uservo.getRemark(),
                    uservo.getRoleIds(), uservo.getEnable(),uservo.getStatus());
            return ResultUtil.ok();
        } catch (Exception e) {
            log.error("修改管理员信息错误", e);
            throw e;
//            return ResultUtil.error();
        }
    }

    //    @Log("变更管理员状态TSysUser")
    @PutMapping(value = "/tSysUser/updateStatus/{id}")
    public ResultUtil updateStatus(@PathVariable Integer id) {
        try {
            tSysUserService.updateStatus(id);
            return ResultUtil.ok();
        } catch (Exception e) {
            log.error("修改管理员状态出错", e);
            return ResultUtil.error();
        }
    }

    @PostMapping(value = "/tSysUser/selectById")
    public ResultUtil selectById(@PathVariable Integer id) {
        try {
            tSysUserService.selectById(id);
            return ResultUtil.ok();
        } catch (Exception e) {
            log.error("查询出错", e);
            throw e;
//            return ResultUtil.error();
        }
    }

    @PostMapping(value = "/selectByPrimaryKey")
    public TSysUser selectByPrimaryKey(@RequestParam(value = "id") Integer id) {
        return tSysUserService.selectById(id);
    }

    @ApiOperation(value = "获取用户详情")
    @PostMapping(value = "/user/getUserInfo")
    @ResponseBody
    public ResultUtil getUserInfo(HttpServletRequest request) {
        ResultUtil result = new ResultUtil();
        List<Map> array = new ArrayList<>();
        String usertype = CurrentUser.getUsertype();
        if (StringUtils.isNotEmpty(usertype)){
            if (usertype.equals(DictEnum.CD.code)){
                String userLogisticsRole = CurrentUser.getUserLogisticsRole();
                if (StringUtils.isNotEmpty(userLogisticsRole)
                        && !userLogisticsRole.equals(DictEnum.CTYPEMANAGER.code)){
                    String cphone = "CPHONE" + CurrentUser.getUserAccountNo();
                    if (null==redisUtil.get(cphone) || "".equals(redisUtil.get(cphone))){
                        if(null==CurrentUser.getEndUserId() || "".equals(CurrentUser.getEndUserId())){
                            boolean flag = false;
                            String[] userLogisticsRoleArray = userLogisticsRole.split(",");
                            if(userLogisticsRoleArray.length==1) {
                                flag = true;
                                String userAccount = CurrentUser.getUserAccountNo();
                                String cp = "CPHONE" + userAccount;
                                redisUtil.set(cp, userLogisticsRole);
                            }
                            if(userLogisticsRoleArray.length==2 && userLogisticsRole.contains(DictEnum.CTYPECAPTAIN.code) && userLogisticsRole.contains(DictEnum.CTYPEBOSS.code)){
                                HashMap<String, Object> ma = new HashMap<>();
                                List<TEndUserInfo> tEndUserInfoList = tSysUserService.selectEnduserRoleByAccountIdList(CurrentUser.getUserAccountId());
                                for(TEndUserInfo tEndUserInfo:tEndUserInfoList){
                                    String[] split = tEndUserInfo.getUserLogisticsRole().split(",");
                                    HashMap<String, Object> map = new HashMap<>();
                                    for(String userRole:split){
                                        map.put("endUserId",tEndUserInfo.getId());
                                        if (userRole.equals(DictEnum.CTYPEBOSS.code)){
                                            map.put("text", "车主");
                                            map.put("type",userRole);
                                            array.add(map);
                                        }
                                        if (userRole.equals(DictEnum.CTYPECAPTAIN.code)){
                                            map.put("text", "车队长");
                                            map.put("type",userRole);
                                            array.add(map);
                                        }
                                    }
                                }
                                result.setCode(DictEnum.ERROR.code);
                                result.setData(array);
                                return result;
                            }else if(userLogisticsRoleArray.length==2 && userLogisticsRole.contains(DictEnum.CTYPEDRVIVER.code)){
                                Integer endUserId = null;
                                String  endUserRole = null;
                                if(userLogisticsRoleArray[0].equals(DictEnum.CTYPECAPTAIN.code) || userLogisticsRoleArray[0].equals(DictEnum.CTYPEBOSS.code)){
                                    endUserId = Integer.parseInt(CurrentUser.getEndUserIds().split(",")[0]);
                                    endUserRole = userLogisticsRoleArray[0];
                                }else{
                                    endUserId = Integer.parseInt(CurrentUser.getEndUserIds().split(",")[1]);
                                    endUserRole = userLogisticsRoleArray[1];
                                }
                                flag = true;
                                String redisKey = "CROLE" + endUserId;
                                redisUtil.set(redisKey, endUserRole);
                                redisUtil.set("C"+CurrentUser.getUserAccountId(), endUserId);
                                String userAccount = CurrentUser.getUserAccountNo();
                                String cp = "CPHONE" + userAccount;
                                redisUtil.set(cp, userLogisticsRole);
                            }

                            if(userLogisticsRoleArray.length==3){
                                HashMap<String, Object> ma = new HashMap<>();
                                List<TEndUserInfo> tEndUserInfoList = tSysUserService.selectEnduserRoleByAccountIdList(CurrentUser.getUserAccountId());
                                if(tEndUserInfoList!=null&& !"".equals(tEndUserInfoList) && tEndUserInfoList.size()>0){
                                    for(TEndUserInfo tEndUserInfo:tEndUserInfoList){
                                        String[] split = tEndUserInfo.getUserLogisticsRole().split(",");
                                        HashMap<String, Object> map = new HashMap<>();
                                        for(String userRole:split){
                                            map.put("endUserId",tEndUserInfo.getId());
                                            if (userRole.equals(DictEnum.CTYPEBOSS.code)){
                                                map.put("text", "车主");
                                                map.put("type",userRole);
                                                array.add(map);
                                            }
                                            if (userRole.equals(DictEnum.CTYPECAPTAIN.code)){
                                                map.put("text", "车队长");
                                                map.put("type",userRole);
                                                array.add(map);
                                            }
                                        }
                                    }
                                }
                                result.setCode(DictEnum.ERROR.code);
                                result.setData(array);
                                return result;
                            }
                            if(flag){
                                TEndUserInfo endUserInfo = endSUserInfoAPI.selectByPrimaryKey(CurrentUser.getEndUserId());
                                if (null != endUserInfo
                                        && !endUserInfo.getUserLogisticsRole().contains(DictEnum.CTYPEBOSS.code)
                                        && !userLogisticsRole.equals(DictEnum.CTYPEAGENTPERSON.code)
                                        && !userLogisticsRole.equals(DictEnum.CTYPEAGENTCOMPANY.code)
                                        && !userLogisticsRole.contains(DictEnum.CTYPECAPTAIN.code)) {
                                    return ResultUtil.error("当前用户无法登录此平台");
                                }
                            }
                        }else{
                            TEndUserInfo endUserInfo = endSUserInfoAPI.selectByPrimaryKey(CurrentUser.getEndUserId());
                            if (null != endUserInfo
                                    && !endUserInfo.getUserLogisticsRole().contains(DictEnum.CTYPEBOSS.code)
                                    && !userLogisticsRole.equals(DictEnum.CTYPEAGENTPERSON.code)
                                    && !userLogisticsRole.equals(DictEnum.CTYPEAGENTCOMPANY.code)
                                    && !userLogisticsRole.contains(DictEnum.CTYPECAPTAIN.code)) {
                                return ResultUtil.error("当前用户无法登录此平台");
                            }
                        }
                    }
                }
            }
        }
        boolean userFlag = true;
        boolean mobileflag = true;
        UserDetails userDetails = CurrentUser.getCurrentUserDetails();
        Integer userID = CurrentUser.getCurrentUserID();
        //查询用户信息
        UserInfoVO userInfo = new UserInfoVO();
        BeanUtils.copyProperties(userDetails, userInfo);
        TSysUser tSysUser = tSysUserService.selectById(userID);
        if(null != tSysUser.getPhone() && !"".equals(tSysUser.getPhone())){
            userInfo.setPhone(tSysUser.getPhone());
        }
        TSysUser tSysUserMainAccount = tSysUserService.selectIfPayPasswordNullByAccountNo(tSysUser.getAccountNo());
        userInfo.setIfPayPasswordNull(true);
        if(null == tSysUserMainAccount  || (null != tSysUserMainAccount.getPayPassword()
                                              && !tSysUserMainAccount.getPayPassword().equals(""))){
            userInfo.setIfPayPasswordNull(false);
        }
        TSysUserVO selectIfOperationsPersonnel = tSysUserService.selectIfOperationsPersonnel(tSysUser.getUsername());
        userInfo.setIfOperationsPersonnel(false);
        if(null != selectIfOperationsPersonnel){
            userInfo.setIfOperationsPersonnel(true);
        }

        userInfo.setIfPasswordSecurity(tSysUser.getIfPasswordSecurity());
        if(tSysUser.getParam2()!=null&&!"".equals(tSysUser.getParam2())){
            JSONObject jsonObject = JSONObject.parseObject(tSysUser.getParam2());
            if(jsonObject.get("fsbtn")!=null&&!"".equals(jsonObject.get("fsbtn"))){
                userInfo.setFsbtn(Integer.parseInt(jsonObject.get("fsbtn").toString()));//用户偏好设置
            }
            if(jsonObject.get("ydyzs")!=null&&!"".equals(jsonObject.get("ydyzs"))){
                userInfo.setYdyzs(Integer.parseInt(jsonObject.get("ydyzs").toString()));//用户偏好设置
            }
        }
        //前端页面需要根据isSuperAdmin做判断，勿删除
        userInfo.setIsSuperAdmin(userInfo.getSuperAdmin());
        //查询移动端权限
        Integer accountId = CurrentUser.getUserAccountId();
        if (null != accountId){
            TLineUserRolePro lineUserRolePro = new TLineUserRolePro();
            lineUserRolePro.setAccountId(accountId);
            ResultUtil resultUtil = lineUserRoleProAPI.selectUserLineRole(lineUserRolePro);
            if (null == userInfo.getUserRoles()){
                userFlag = false;
            }
            LinkedHashMap data = (LinkedHashMap) resultUtil.getData();
            if (null != data){
                if (null == data.get("lineRole")) {
                    mobileflag = false;
                    if (userFlag && mobileflag){
                        return ResultUtil.error("未分配角色");
                    }
                }
                if (null != data.get("lineRole")) {
                    ArrayList lineRole = (ArrayList) data.get("lineRole");
                    if (lineRole.size() > 0) {
                        userInfo.setMobileRole(lineRole);
                    }
                }
                if (null != data.get("linePerms")) {
                    ArrayList<String> linePerms = (ArrayList) data.get("linePerms");
                    if (null != linePerms && linePerms.size() > 0){
                        ArrayList<String> perms = permissionSort(linePerms);
                        userInfo.setMobilePerms(perms);
                    }
                }
            }
        }
        //当登录账号为企业管理员时，为账号分配“运单”按钮，并查询企业下的所有运单信息
        if(CurrentUser.accountIsCompanyAdmin()){
            userInfo.setCompanyAdmin(true);
            userInfo.setMobilePerms(Collections.singletonList("invorder"));
            userInfo.setMobileRole(Collections.singletonList("BADMIN"));
        }

        //查询用户权限roles
        List<String> userPermissions = permissionsService.getUserPermissions(userID.toString());
        String[] permissions = new String[userPermissions.size()];
        userPermissions.toArray(permissions);
        userInfo.setRoles(permissions);
        userInfo.setPassword(null);
        //获取图片上传、回显IP
        HashMap<String, String> imageIPSysParam = sysParamService.getImageIPSysParam();
        userInfo.setImageIP(imageIPSysParam);
        SysParam iosVersion = sysParamService.getParamByKey("iosVersion");
        if (null != iosVersion && null != iosVersion.getParamValue() && StringUtils.isNotEmpty(iosVersion.getParamValue())) {
            userInfo.setIosVersion(iosVersion.getParamValue());
        }
        SysParam isOpenAboutUs = sysParamService.getParamByKey("isOpenAboutUs");
        if (null != isOpenAboutUs && null != isOpenAboutUs.getParamValue() && StringUtils.isNotEmpty(isOpenAboutUs.getParamValue())) {
            if ("0".equals(isOpenAboutUs.getParamValue())) {
                userInfo.setIsOpenAboutUs(false);
            } else {
                userInfo.setIsOpenAboutUs(true);
            }
        }
        try {
            userInfo.setUserLogisticsRole(CurrentUser.getUserLogisticsRoles());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResultUtil.ok(userInfo);
    }

    @GetMapping("/getUserTypeByPhone")
    public String getUserTypeByPhone(@RequestParam("username") String username) {
        List<TSysUser> tSysUsers = tSysUserService.selectByPhone(username);
        if (tSysUsers.isEmpty()) {
            TBusinessBasic tBusinessBasic = tSysUserService.selectByphone(username);
            if (null != tBusinessBasic) {
                return "BUSINESS";
            }
        } else {
            List<TSysUser> collect = tSysUsers.stream().filter((user) -> null == user.getUsertype() || StringUtils.isBlank(user.getUsertype())).collect(Collectors.toList());
            if (!collect.isEmpty()) {
                return null;
            }
            TSysUser tSysUser = tSysUsers.get(0);
            return tSysUser.getUsertype();

        }
        return null;
    }

    @GetMapping("/getUserType")
    public String getUserType() {
        String usertype = CurrentUser.getUsertype();
        return usertype;
    }


    /**
    * @Description 对APP页面排序
    * <AUTHOR> i
    * @Date   2019/6/22 10:05
    * @Param
    * @Return
    * @Exception
    *
    */
    public ArrayList<String> permissionSort(ArrayList<String> linePerms) {
        ArrayList<String> perms = new ArrayList<>();
        if (linePerms.contains(DictEnum.RESOURCE.code)){
            perms.add(DictEnum.RESOURCE.code);
        }
        if (linePerms.contains(DictEnum.INVORDER.code)){
            perms.add(DictEnum.INVORDER.code);
        }
        if (linePerms.contains(DictEnum.MEMBER.code)){
            perms.add(DictEnum.MEMBER.code);
        }

        return perms;
    }

    /**
     * @Description: 清除缓存
     * @Author: Yan
     * @Date: 2019/9/6/006 9:50
     * @Param:
     * @Return:
     */
    @GetMapping("/deleteCachePms/{menuId}/{type}/{mima}")
    public void deleteCacheField(@PathVariable("menuId") Integer id,
                                 @PathVariable("type") String type,
                                 @PathVariable("mima") String mima) {
        String deleteCache = "DEld482OkJ";
        if (deleteCache.equals(mima)) {
            if ("menu".equals(type)) {
                List<UserDTO> userDTOS = sysMenuService.getPmsByRoleMenuId(id);
                for (UserDTO userDTO : userDTOS){
                    if (userDTO != null){
                        redisUtil.del("sysUserId-" + userDTO.getId() + "-menu");
                    }
                }
            } else {
                String key = "-" + type;
                if (0 != id) {
                    key = "*-" + id + key;
                } else {
                    key = "*" + key;
                }
                redisUtil.slurDel(key);
            }
        }
    }

    /**
     * 根据权限ID获取页面信息
     *
     * @param id
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "根据权限ID获取页面信息", httpMethod = "GET")
    @GetMapping("/navigation/{id}")
    public ResultUtil buildPageInfo(@PathVariable("id") Integer id) {
        List<TSysPermissions> buttonByRolePermission = permissionsService.findButtonByRolePermission(id);
        List<TSysFields> userMenuField = permissionsService.getUserMenuField(id);

        List<FormTemplateDTO> formTemplate = formPrintService.getFormTemplate(id);

        // 返回导航，字段，按钮
        Map<String, Object> map = new HashMap<>();
        // 放入返回的按钮
        map.put("button", buttonByRolePermission);
        // 字段
        map.put("field", userMenuField);

        map.put("formPrint", formTemplate);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), map);
    }
    @ApiOperation(value = "创建菜单")
    @GetMapping(value = "/buildMenu")
    @ResponseBody
    public ResultUtil buildMenu() {
        String username = CurrentUser.getCurrentUsername();
        Integer userID = CurrentUser.getCurrentUserID();
        //用户角色
        List<UserRoleVO> rolesByUser = roleService.findRolesByUser(username);
        if (rolesByUser.size() == 0) {
            return ResultUtil.error("未分配角色");
        }
        //角色菜单
        List<MenuDTO> menuByRole = new ArrayList<>();
        String userKey = "sysUserId-" + String.valueOf(userID) + "-menu";
        menuByRole = permissionsService.findMenuByRole(rolesByUser);
        redisUtil.set(userKey, menuByRole);
        // 返回导航，字段，按钮
        Map<String, Object> map = new HashMap<>();
        // 放入返回的导航
        map.put("menu", menuByRole);
        return ResultUtil.ok(map);
    }


    /**
     * 校验旧密码
     * auth dingweibo
     *
     * @param pass
     * @return
     */
    @GetMapping(value = "/tSysUser/validatePass")
    public ResultUtil validatePass(String pass) {
        try {
            UserDetails userDetails = (UserDetails) SecurityContextHolder.getContext()
                    .getAuthentication()
                    .getPrincipal();
            CustomUserDetails details = (CustomUserDetails) userDetails;
            int userId = details.getId();
            TSysUser user = tSysUserService.findById(userId);
            if (pass.equals(user.getPassword())) {
                return new ResultUtil("200", "成功");
            } else {
                return new ResultUtil("400", "成功");
            }
        } catch (Exception e) {
            log.error("校验密码出错", e);
            return ResultUtil.error();
        }
    }

    /**
     * 校验旧密码
     * auth  dingweibo
     *
     * @param pass
     * @return
     */
    @GetMapping(value = "/tSysUser/updatePass")
    public ResultUtil updatePass(String pass) {
        try {
            UserDetails userDetails = (UserDetails) SecurityContextHolder.getContext()
                    .getAuthentication()
                    .getPrincipal();
            CustomUserDetails details = (CustomUserDetails) userDetails;
            int userId = details.getId();
            TSysUser user = new TSysUser();
            user.setId(userId);
            user.setPassword(pass);
            tSysUserService.updateByPass(user);
            return new ResultUtil("200", "修改成功！");
        } catch (Exception e) {
            log.error("修改密码出错", e);
            return ResultUtil.error();
        }
    }


    /**
     * 图片上传
     * auth dingweibo
     *
     * @param
     * @return
     */
    // @PostMapping(value = "/tSysUser/upload", produces = {MediaType.APPLICATION_JSON_UTF8_VALUE}, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResultUtil upload(MultipartFile file, HttpServletRequest request) {
        try {
            //ResultUtil resultUtil = fastdfsAPI.uploadFileSample(file);
            //Map<Object, Object> map = (Map<Object, Object>) resultUtil.getData();
            ResultUtil resultUtil = ossAPI.uploadFileSample(file);
            UserDetails userDetails = (UserDetails) SecurityContextHolder.getContext()
                    .getAuthentication()
                    .getPrincipal();
            CustomUserDetails details = (CustomUserDetails) userDetails;
            TSysUser user = new TSysUser();
            user.setId(details.getId());
            user.setHeadUrl(resultUtil.getData().toString());
            tSysUserService.updateByPass(user);
            return resultUtil;
        } catch (Exception e) {
            log.error("出错", e);
            return ResultUtil.error();
        }
    }

    /**
     * auth dingweibo
     * 新增用户 向会员系统提供接口
     *
     * @param user
     * @return
     */
    @PostMapping(value = "/saveUser")
    public ResultUtil saveUser(@RequestBody TSysUser user) {
        try {
            ResultUtil add = tSysUserService.add(user);
            return add;
        } catch (Exception e){
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * auth dingweibo
     * 新增
     *
     * @param user
     * @return
     */
    @PostMapping(value = "/addUser")
    public ResultUtil addUser(@RequestBody TSysUser user) {
        return tSysUserService.add(user);
    }

    /**
     * 向会员提供修改
     *
     * @param user
     * @return
     */
    @PostMapping(value = "/updateUser")
    public int updateUser(@RequestBody TSysUser user) {
        return tSysUserService.updateByPass(user);
    }

    /**
     * 根据account_no修改系统用户信息
     *
     * @param sysUserVO
     * @return
     * <AUTHOR>
     */
    @PostMapping(value = "/updateUserByAccountNo")
    public ResultUtil updateUserByAccountNo(@RequestBody TSysUserVO sysUserVO) {
        ResultUtil count = tSysUserService.updateUserByAccountNo(sysUserVO);
        return count;
    }

    /**
     * 更新sysuser表里的thirdId （openid）
     *
     * @param id
     * @param thridParyId
     * @return
     */
    @PostMapping(value = "/updateThridID")
    public int updateThridID(@RequestParam(value = "id") Integer id, @RequestParam(value = "thridParyId") String thridParyId) {
        TSysUser sysUser = tSysUserService.selectById(id);
        sysUser.setThridParyId(thridParyId);
        return tSysUserService.updateByPass(sysUser);
    }

    /**
     * 修改密码
     *
     * @param userid
     * @param password
     * @return
     */
    @PostMapping(value = "/updatePassword")
    public int aupdatePassword(@RequestParam(value = "userid") Integer userid, @RequestParam(value = "password") String password) {
        TSysUser sysUser = tSysUserService.selectById(userid);
        sysUser.setPassword(password);
        return tSysUserService.updateByPass(sysUser);
    }

    /**
     * 修改手机号
     *
     * @param userid
     * @param accountNo
     * @return
     */
    @PostMapping(value = "/updateAccountNo")
    public int updateAccountNo(@RequestParam(value = "userid") Integer userid, @RequestParam(value = "accountNo") String accountNo) {
        TSysUser sysUser = tSysUserService.selectById(userid);
        sysUser.setAccountNo(accountNo);
        sysUser.setUsername(accountNo);
        return tSysUserService.updateByPass(sysUser);
    }

    /**
     * 登出
     *
     * @param request
     * @param response
     * @return
     */
    @PostMapping(value = "/logout")
    public ResultUtil logout(HttpServletRequest request, HttpServletResponse response) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (null != authentication) {
            new SecurityContextLogoutHandler().logout(request, response, authentication);
        }
        return ResultUtil.ok();
    }
    @PostMapping(value = "/selectByPhone")
    public List<TSysUser> selectByPhone(@RequestParam(value = "phone") String phone){
        return tSysUserService.selectByPhone(phone);
    }

    @PostMapping(value = "/selectByPhoneAndId")
    public List<TSysUser> selectByPhoneAndId(@RequestParam(value = "phone") String phone,
                                             @RequestParam(value = "id") Integer id){
        return tSysUserService.selectByPhoneAndId(phone,id);
    }

    /**
     * 编辑用户查询用户信息
     * <AUTHOR>
     * @return
     */
    @PostMapping("/selectUserById")
    public ResultUtil selectUserById(@RequestParam("id") Integer id){
        TSysUser sysUser = new TSysUser();
        sysUser.setId(id);
        ResultUtil resultUtil = tSysUserService.selectUserById(sysUser);
        return resultUtil;
    }



    /**
     *  @author: dingweibo
     *  @Date: 2019/6/23 15:07
     *  @Description:新增权限表
     */
    @PostMapping("/saveUserRole")
    public ResultUtil saveUserRole(@RequestBody TUserRole tUserRole){

        return tSysUserService.saveUserRole(tUserRole);
    }

    @PostMapping("/saveUserRoles")
    public ResultUtil saveUserRoles(@RequestBody UserRoleSaveVO vo) {
        return tSysUserService.saveUserRoles(vo);
    }


    /**
    * @Description 保存系统用户，并分配角色
    * <AUTHOR>
    * @Date   2019/7/23 8:28
    * @param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/saveUserAndPCRole")
    public ResultUtil saveUserAndPCRole(@RequestBody TSysUserForAddVO record){
        ResultUtil resultUtil = tSysUserService.saveUserAndPCRole(record);
        return resultUtil;
    }

    @PostMapping("/saveAccountUserRole")
    public void saveAccountUserRole(@RequestBody TSysUserForAddVO record){
        tSysUserService.saveAccountUserRole(record);
    }

    @PostMapping("/updateAccountUserRole")
    public void updateAccountUserRole(@RequestBody TSysUserForAddVO record){
        tSysUserService.updateAccountUserRole(record);
    }

    @PostMapping("/phoneExists")
    public ResultUtil companyAdminPhoneExists(@RequestBody TSysUserForAddVO record) {
        return tSysUserService.judgePhoneExists(record.getAccountNo());
    }

    @PostMapping("/updateByAccountIdSelective")
    public ResultUtil updateByAccountIdSelective(@RequestBody TUserInfo record) {
        return tSysUserService.updateByAccountIdSelective(record);
    }

    @PostMapping("/insertUserInfo")
    public ResultUtil insertUserInfo(@RequestBody TUserInfo record) {
        return tSysUserService.insertUserInfo(record);
    }

    @PostMapping("/selectCaptainSysRole")
    public TSysRole selectCaptainSysRole() {
        return tSysUserService.selectCaptainSysRole();
    }

    @PostMapping("/insertCaptainUserRole")
    public ResultUtil insertCaptainUserRole(@RequestBody TUserRole tUserRole) {
        return tSysUserService.insertCaptainUserRole(tUserRole);
    }

    /**
     * 等保临时使用
     * 发送修改密码验证码
     * @param phone
     * @param smsType
     * @return
     */
    @PostMapping("/sendMessageByUpdatePassword")
    public ResultUtil sendMessageByUpdatePassword(@RequestBody TUserVo account) {
        try {
            if (StringUtils.isBlank(account.getAccountNo())) {
                return ResultUtil.error("请输入手机号");
            }
            if (StringUtils.isBlank(account.getSmsType())) {
                return ResultUtil.error("参数不合法");
            }
            List<TSysUser> users = tSysUserService.selectByPhone(account.getAccountNo());
            if (users.size() == 0) {
                return ResultUtil.error("账号填写错误");
            }
            if (users.size() > 1) {
                return ResultUtil.error("当前账号异常，请联系运营人员");
            }
            if (DictEnum.PC.code.equalsIgnoreCase(account.getSmsType())) {
                User user = new User();
                user.setUsername(account.getAccountNo());
                user = tSysUserService.selectUser(user);
                if (null == user) {
                    return ResultUtil.error("账号填写错误");
                }
                if (DictEnum.CD.code.equals(user.getAcctype())) {
                    return ResultUtil.error("此账号不能在此端进行修改");
                }
            } else {
                return ResultUtil.error("参数不合法");
            }

            SmsReq req = new SmsReq();
            req.setMobiles(account.getAccountNo());
            req.setType("CHANGEPWD");
            String key = "SENDCHANGEPWD" + account.getAccountNo();
            String oldcode = ObjectUtils.toString(redisUtil.get(key));
            if (StringUtils.isBlank(oldcode)) {
                redisUtil.set(key, 1, getSecondByEndToday());
            } else {
                Integer count = Integer.valueOf(oldcode);
                if (count >= 5) {
                    return ResultUtil.error("您今天已经超过5次，请明天再试");
                }
                count++;
                long expire = redisUtil.getExpire(key);
                redisUtil.set(key, count, expire);
            }
            smsApi.sendSmsType(req);


        } catch (IOException e) {
            log.error("验证码发送失败, {}", e);
            ResultUtil.error("验证码发送失败");
        }
        return ResultUtil.ok("发送成功");
    }

    /**
     * 等保临时使用
     * 修改密码
     * @param account
     * @return
     */
    @PostMapping("/forUpdatePassword")
    public ResultUtil forUpdatePassword(@RequestBody TUserVo account) {
        try{
            UserInfoVO userInfo = tSysUserService.getUserInfo(account.getAccountNo());
            // 账号被锁，稍后再试
            if (null != userInfo.getAccountStatus() && DictEnum.ACCOUNTLOCK.code.equals(userInfo.getAccountStatus())) {
                // 账号锁住
                return ResultUtil.error("验证码输错3次以上账号已锁定");
            }
            String oldcode = ObjectUtils.toString(redisUtil.get("CHANGEPWD" + account.getAccountNo()));
            List<TSysUser> users = tSysUserService.selectByPhone(account.getAccountNo());
            if (StringUtils.isEmpty(account.getAccountNo())) {
                return ResultUtil.error("请输入手机号码");
            } else if (users.size() == 0) {
                return ResultUtil.error("该手机号尚未注册");
            } else if (users.size() > 1) {
                return ResultUtil.error("当前账号异常，请联系运营人员");
            } else if (StringUtils.isEmpty(oldcode)) {
                return ResultUtil.error("请重新发送验证码");
            } else if (!account.getCode().equals(oldcode)) {
                // 记录密码错误次数
                tSysUserService.recordPassowordErrorNew(account.getAccountNo());
                return ResultUtil.error("验证码错误");
            } else if (StringUtils.isBlank(account.getPassword())) {
                return ResultUtil.error("请输入新密码");
            } else if (StringUtils.isBlank(account.getConfirmPassword())) {
                return ResultUtil.error("请输入确认密码");
            } else {
                if (!account.getPassword().equals(account.getConfirmPassword())) {
                   return ResultUtil.error("两次密码不一致");
                }
                String password = DigestUtils.md5Hex(account.getPassword());
                User user = new User();
                user.setUsername(account.getAccountNo());
                user = tSysUserService.selectUser(user);
                if (null == user) {
                    return ResultUtil.error("该手机号尚未注册");
                }
                if (DictEnum.CD.code.equals(user.getAcctype()) || DictEnum.CA.code.equals(user.getAcctype())) {
                    return ResultUtil.error("此账号不能在此端进行修改");
                }
                if (password.equals(user.getPassword())) {
                    //return ResultUtil.error("新密码不能与旧密码相同");
                }
                String newPassword = account.getPassword();
                if (!StringUtils.validatePassword(newPassword)) {
                    return ResultUtil.error("请输入8位或8位以上，不超过16位，包含数字、字母（大小写不限）、特殊字符");
                    //return ResultUtil.error("请输入8位或8位以上，不超过16位，包含数字、字母（大小写不限）");
                }
                tSysUserService.updatePassword(user.getId(), password);

                TVerificationCodeLog tVerificationCodeLog=new TVerificationCodeLog();
                tVerificationCodeLog.setReceivePhoneno(account.getAccountNo());
                tVerificationCodeLog.setVerificationCode(oldcode);
                verificationCodeLogAPI.updateIfUsed(tVerificationCodeLog);
                redisUtil.del("CHANGEPWD" + account.getAccountNo());
                return new ResultUtil(CodeEnum.SUCCESS.getCode(), "密码修改成功");

            }
        }catch (Exception e){
            e.printStackTrace();
            log.error("密码修改失败, {}", ThrowableUtil.getStackTrace(e));
            return ResultUtil.error("密码修改失败");
        }
    }

    /**
     * @param password
     * @return 判断密码是否为复杂类型（同时包含大小写和数字），是返回true ，非复杂返回false boolean
     *
     */
    public boolean pwdIsComplex(String password) {
        boolean flag = false;

        // 用于判断密码是否过于简单
        Pattern p1 = Pattern.compile("[a-zA-Z]+");
        Pattern p3 = Pattern.compile("[0-9]+");
        Pattern p4 = Pattern.compile("[^%&',;\\=\\[\\]\\{\\}+-<>!@#%*()?$\\x22]+");
        Matcher m = p1.matcher(password);
        // 没有小写
        if (!m.find()) {
            flag = false;
        } else {
            m.reset().usePattern(p3);
            // 没有数字
            if (!m.find()) {
                flag = false;
            } else {
                m.reset().usePattern(p4);
                if (!m.find()) {
                    flag = false;
                }
                flag = true;
            }
        }

        return flag;
    }


    public static long getSecondByEndToday() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR,1);
        calendar.set(Calendar.HOUR_OF_DAY,0);
        calendar.set(Calendar.SECOND,0);
        calendar.set(Calendar.MINUTE,0);
        calendar.set(Calendar.MILLISECOND,0);
        return ((calendar.getTimeInMillis()-System.currentTimeMillis()) / 1000) ;
    }

    /*
    根据userID和roleID查询t_user_role表获取数据
     */
    @PostMapping("/getDataByID")
    public TUserRole getDataByID(@RequestParam("userId") Integer userId,@RequestParam("roleId") Integer roleId){
        return tSysUserService.getDataByID(userId,roleId);
    }

    /*
    根据ID判断新增/更新
     */
    @PostMapping("/save")
    public void save(@RequestBody TUserRole tUserRole){
        tSysUserService.save(tUserRole);
    }

    @PostMapping(value = "/getSysUserById")
    public TSysUser getSysUserById(@RequestParam("id") Integer id) {
        return tSysUserService.findById(id);
    }

    @PostMapping(value = "/updateSysUserById")
    public void updateSysUserById(@RequestBody TSysUser tSysUser) {
        tSysUserService.updateById(tSysUser);
    }


    @PostMapping("/sendMessageBySetPayPassword")
    public ResultUtil sendMessageBySetPayPassword(@RequestBody TUserVo account) {
        try {
            if (StringUtils.isBlank(account.getAccountNo())) {
                return ResultUtil.error("请输入手机号");
            }
            if (StringUtils.isBlank(account.getSmsType())) {
                return ResultUtil.error("参数不合法");
            }
            List<TSysUser> users = tSysUserService.selectByPhone(account.getAccountNo());
            if (users.size() == 0) {
                return ResultUtil.error("该手机号尚未注册");
            }
            if (users.size() > 1) {
                return ResultUtil.error("当前账号异常，请联系运营人员");
            }
            if (DictEnum.PC.code.equalsIgnoreCase(account.getSmsType())) {
                User user = new User();
                user.setUsername(account.getAccountNo());
                user = tSysUserService.selectUser(user);
                if (null == user) {
                    return ResultUtil.error("该手机号尚未注册");
                }
                if (DictEnum.CD.code.equals(user.getAcctype())) {
                    return ResultUtil.error("此账号不能在此端进行修改");
                }
            } else {
                return ResultUtil.error("参数不合法");
            }

            SmsReq req = new SmsReq();
            req.setMobiles(account.getAccountNo());
            req.setType("CHANGEPAYPWD");
            smsApi.sendSmsType(req);

        } catch (IOException e) {
            log.error("验证码发送失败, {}", e);
            ResultUtil.error("验证码发送失败");
        }
        return ResultUtil.ok("发送成功");
    }


    @PostMapping(value = "/setPayPassword")
    public ResultUtil setPayPassword(@RequestBody TSysUserVO tSysUser) {
        try{
            String oldcode = ObjectUtils.toString(redisUtil.get("CHANGEPAYPWD" + tSysUser.getAccountNo()));
            List<TSysUser> users = tSysUserService.selectByPhone(tSysUser.getAccountNo());
            if (StringUtils.isEmpty(tSysUser.getAccountNo())) {
                return ResultUtil.error("请输入手机号码");
            } else if (users.size() == 0) {
                return ResultUtil.error("该手机号尚未注册");
            } else if (users.size() > 1) {
                return ResultUtil.error("当前账号异常，请联系运营人员");
            } else if (StringUtils.isEmpty(oldcode)) {
                return ResultUtil.error("请重新发送验证码");
            } else if (!tSysUser.getCode().equals(oldcode)) {
                return ResultUtil.error("验证码错误");
            } else if (StringUtils.isBlank(tSysUser.getPayPassword())) {
                return ResultUtil.error("请输入新支付密码");
            } else if (StringUtils.isBlank(tSysUser.getConfirmPayPassword())) {
                return ResultUtil.error("请输入确认支付密码");
            } else {
                if (!tSysUser.getPayPassword().equals(tSysUser.getConfirmPayPassword())) {
                    return ResultUtil.error("两次支付密码不一致");
                }
                TSysUser tSysUserMainAccount = tSysUserService.selectIfPayPasswordNullByAccountNo(tSysUser.getAccountNo());
                if(tSysUserMainAccount == null){
                    return ResultUtil.error("该账号不是企业管理员,不能进行支付密码设置！");
                }

                String payPassword = BCrypt.hashpw(tSysUser.getPayPassword(), BCrypt.gensalt());
                User user = new User();
                user.setUsername(tSysUser.getAccountNo());
                user = tSysUserService.selectUser(user);
                if (null == user) {
                    return ResultUtil.error("该手机号尚未注册");
                }
                if (DictEnum.CD.code.equals(user.getAcctype()) || DictEnum.CA.code.equals(user.getAcctype())) {
                    return ResultUtil.error("此账号不能在此端进行修改");
                }
                String newPayPassword = tSysUser.getPayPassword();
                if (!StringUtils.validatePayPassword(newPayPassword)) {
                    return ResultUtil.error("请输入六位纯数字支付密码！");
                }
                tSysUserService.updatePayPassword(user.getId(), payPassword);

                TVerificationCodeLog tVerificationCodeLog=new TVerificationCodeLog();
                tVerificationCodeLog.setReceivePhoneno(tSysUser.getAccountNo());
                tVerificationCodeLog.setVerificationCode(oldcode);
                verificationCodeLogAPI.updateIfUsed(tVerificationCodeLog);
                redisUtil.del("CHANGEPAYPWD" + tSysUser.getAccountNo());
                return new ResultUtil(CodeEnum.SUCCESS.getCode(), "支付密码修改成功");
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error("支付密码修改失败, {}", ThrowableUtil.getStackTrace(e));
            return ResultUtil.error("支付密码修改失败");
        }
    }

    @PostMapping(value = "/userGetUserInfo")
    public UserInfoVO userGetUserInfo(@RequestParam("username")String username) {
        return tSysUserService.getUserInfo(username);
    }

    @PostMapping(value = "/recordPassowordErrorNew")
    public void recordPassowordErrorNew(@RequestParam("username")String username) {
        tSysUserService.recordPassowordErrorNew(username);
    }

    @PostMapping("/checkPayPassword")
    public ResultUtil checkPayPassword(@RequestBody CompanyOpenInfoDetailsVO paramVo) {
        List<Integer> userCompanyId = CurrentUser.getUserCompanyIdInteger();
        if (!userCompanyId.contains(paramVo.getId())){
            return ResultUtil.error("无支付权限");
        }
        String payPassword = tSysUserService.checkPayPassword(paramVo.getId());
        if (org.apache.commons.lang3.StringUtils.isBlank(payPassword)){
            return ResultUtil.error("请联系管理员设置支付密码");
        }
        return ResultUtil.ok();
    }

    @PostMapping("/selectPayPassword")
    public String selectPayPassword(@RequestParam("companyId")Integer companyId) {
        return tSysUserService.checkPayPassword(companyId);
    }

    @PostMapping(value = "/operationsPersonnelLock")
    public ResultUtil operationsPersonnelLock(@RequestBody TSysUserVO tSysUser) {
        try {
            TSysUserVO selectIfOperationsPersonnel = tSysUserService.selectIfOperationsPersonnel(CurrentUser.getCurrentUsername());
            if(selectIfOperationsPersonnel == null){
                return ResultUtil.error("当前账号不是运营人员，不得锁定账号");
            }
            int result = new Date().compareTo(tSysUser.getOperationsPersonnelLockDate());
            if(result >= 0){
                return ResultUtil.error("锁定时间不得小于等于当前时间！");
            }

            TUserInfo tUserInfo = new TUserInfo();
            tUserInfo.setId(selectIfOperationsPersonnel.getUserInfoId());
            tUserInfo.setStatus(DictEnum.VOLUNTARILYACCOUNTLOCK.code);
            tUserInfo.setAccountLockTime(tSysUser.getOperationsPersonnelLockDate());
            ResultUtil resultUtil = tSysUserService.operationsPersonnelLock(tUserInfo);
            return resultUtil;
        }catch (Exception e){
            log.error("设置锁定运营账号失败！",e);
            return ResultUtil.error("设置锁定运营账号失败!");
        }
    }

    @PostMapping(value = "/userInfoSelectByPage")
    public ResultUtil userInfoSelectByPage(@RequestBody TUserInfoDto record){
        try{
            return tSysUserService.userInfoSelectByPage(record);
        }catch (Exception e){
            log.error("列表查询失败！",e);
            return ResultUtil.error("列表查询失败！");
        }
    }

    @PostMapping(value = "/updateUserInfoStatus")
    public ResultUtil updateUserInfoStatus(@RequestBody TUserInfoDto record){
        try{
            return tSysUserService.updateUserInfoStatus(record);
        }catch (Exception e){
            log.error("解锁失败！",e);
            return ResultUtil.error("解锁失败！");
        }
    }

}