package com.lz.system.dao;

import com.lz.model.TEndUserInfo;
import com.lz.system.dto.menu.MenuDTO;
import com.lz.system.dto.menu.MenuTree;
import com.lz.system.dto.menu.Menus;
import com.lz.system.dto.menu.PermissionsDTO;
import com.lz.system.model.TSysFields;
import com.lz.system.model.TSysPermissions;
import com.lz.system.model.User;
import com.lz.system.vo.TSysPermissionsVo;
import com.lz.system.vo.TreeVO;
import com.lz.system.vo.UserRoleVO;
import com.lz.system.vo.menu.MenuPermissionTreeVO;
import com.lz.system.vo.menu.MenuQueryVO;
import com.lz.vo.TEndUserInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface TSysPermissionsMapper {

    /**
     * 获取pid下的最后一个按钮的 sort
     * Yan
     * @param pid
     * @return
     */
    Integer getLastPermissionSort(Integer pid);

    /**
     * 根据RoelId查询按钮 type = 2 可去重
     * Yan
     * @param roleIds
     * @return
     */
    Set<TSysPermissions> selectButtonSole(@Param("roleIds") List<Integer> roleIds);

    /**
     * 逻辑批量删除权限
     * Yan
     * @param per
     * @return
     */
    int logicDeletePermission(Map<String, Object> per);

    /**
     * @Description: 获取权限的父级ID
     * @Author: Yan
     * @Date: 2019/9/9/009 14:25
     * @Param: 
     * @Return: 
     */
    TSysPermissions getPmsFatherId(Integer id);

    /**
     * 逻辑批量删除菜单
     * Yan
     * @param menu
     * @return
     */
    int logicDeleteMenu(Map<String, Object> menu);

    int deleteByPrimaryKey(Integer id);

    int insert(TSysPermissions record);

    int insertSelective(TSysPermissions record);

    TSysPermissions selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TSysPermissions record);

    int updateByPrimaryKey(TSysPermissions record);

    List<TSysPermissionsVo> getSysPermissions(TSysPermissions record);

    List<MenuPermissionTreeVO> getFirstLevelPermission(Map<String, Object> params);

    List<String> getUserPerissions(String userId);

    List<TSysPermissions> getPermissionByMenuID(Integer id);

    List<PermissionsDTO> getUserPermissions(User user);

    Set<PermissionsDTO> getAllPermission();

    Set<PermissionsDTO> getPermissionByRole(@Param(value = "id") Integer id);

    List<TSysPermissions> getList(HashMap hashmap);

    List<MenuTree> treeList(TSysPermissions menu);

    List<Menus> menus(MenuQueryVO menuQueryVO);

    List<TSysPermissions> selectByObject(TSysPermissions permissions);

    List<MenuDTO> menuListNew(@Param("roleIds") List<Integer> roleIds);

    List<TreeVO> getButtenPermissions(Integer id);

    List<TreeVO> getAllMenuField(@Param(value = "orgid")Integer orgid,@Param(value = "userid")Integer userid);

    List<Integer> orgId(Integer id);

    List<TSysFields> getUserMenuField(Map<String, Object> params);

    List<Integer> getFieldByMenu(Map<String, Object> params);

    void batchInsertField(Map<String, Object> params);

    List<MenuPermissionTreeVO> getMenuPermissionTree(TSysPermissions record);

    Set<PermissionsDTO> getSuperAdminPermission();

    List<MenuDTO> superAdminMenuList(@Param(value = "orgId") Integer orgId);

    List<TSysPermissions> selectByMenu(UserRoleVO userRoleVO);
}