package com.lz.system.dao;

import com.lz.system.model.TProviceInfo;
import com.lz.system.util.ProvinceCityCounty;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TProviceInfoMapper {
    /**
     * 查询所有省级名、code
     * Yan
     * @return
     */
    List<ProvinceCityCounty> selectAllProvice();

    int deleteByPrimaryKey(Integer id);

    int insert(TProviceInfo record);

    int insertSelective(TProviceInfo record);

    TProviceInfo selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TProviceInfo record);

    int updateByPrimaryKey(TProviceInfo record);
}