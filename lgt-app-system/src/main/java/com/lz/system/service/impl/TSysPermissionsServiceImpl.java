package com.lz.system.service.impl;

import com.lz.common.config.RedisUtil;
import com.lz.common.model.CodeEnum;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.system.dao.TRolePermissionsMapper;
import com.lz.system.dao.TSysPermissionsMapper;
import com.lz.system.dao.TSysRoleMapper;
import com.lz.system.dto.menu.MenuDTO;
import com.lz.system.model.Meta;
import com.lz.system.model.TSysFields;
import com.lz.system.model.TSysPermissions;
import com.lz.system.service.TSysPermissionsService;
import com.lz.system.vo.TSysPermissionsVo;
import com.lz.system.vo.TreeVO;
import com.lz.system.vo.UserRoleVO;
import com.lz.system.vo.menu.MenuPermissionTreeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * auto dingweibo
 * 权限管理
 */
@Service("tSysPermissionsService")
@Transactional
public class TSysPermissionsServiceImpl implements TSysPermissionsService {

    @Resource
    private TSysPermissionsMapper tSysPermissionsMapper;

    @Autowired
    private RedisUtil redisUtil;

    @Resource
    private TSysRoleMapper tSysRoleMapper;

    @Resource
    private TRolePermissionsMapper rolePermissionsMapper;


    /**
     * 根据角色ID,和权限ID(Type是1的) 查询 Redis缓存的按钮
     * <AUTHOR>
     * @param pmsnId
     * @return
     */
    @Override
    public List<TSysPermissions> findButtonByRolePermission(Integer pmsnId) {
        String username = CurrentUser.getCurrentUsername();
        List<UserRoleVO> roles = tSysRoleMapper.findUserRoles(username);
        List<TSysPermissions> button = new ArrayList<>();
        Set<TSysPermissions> result= new HashSet<>();
        // 登录获取当前用户sys id
        Integer userId = CurrentUser.getCurrentUserID();
        String key = "sysUserId-" + userId + "-" + "pmsn-" + pmsnId + "-button";
        if (!redisUtil.hasKey(key)) {
            // 所有角色集合
            List<Integer> roleIds = new ArrayList<>();
            for (UserRoleVO role : roles) {
                roleIds.add(Integer.parseInt(role.getId()));
            }
            Set<TSysPermissions> permissions = tSysPermissionsMapper.selectButtonSole(roleIds);
            List<TSysPermissions> tSysPermissions = gettSysPermissionsButtons(pmsnId, permissions);
            button = buttonSort(new HashSet<>(tSysPermissions));
            redisUtil.set(key, tSysPermissions);
        } else {
            button = (List<TSysPermissions>) redisUtil.get(key);
            List<TSysPermissions> tSysPermissions = buttonSort(new HashSet<>(button));
            button = tSysPermissions;
        }

        return button;
    }


    /**
    * @Description 获取当前菜单下的按钮
    * <AUTHOR>
    * @Date   2019/8/22 11:28
    * @param
    * @Return
    * @Exception
    *
    */
    private List<TSysPermissions> gettSysPermissionsButtons(Integer pmsnId, Set<TSysPermissions> result) {
        List<TSysPermissions> button;
        Set<TSysPermissions> res = new HashSet<>();
        for (TSysPermissions tp : result) {
            if (tp.getPid().equals(pmsnId)) {
                res.add(tp);
            }
        }
        List<TSysPermissions> tSysPermissions = buttonSort(res);
        res = new HashSet<>(tSysPermissions);
        for (TSysPermissions ta : res) {
            Set<TSysPermissions> child = new HashSet<>();
            for (TSysPermissions tb : result) {
                if (ta.getId().equals(tb.getPid())) {
                    child.add(tb);
                }
            }
            List<TSysPermissions> childButton = buttonSort(child);
            ta.setChild(childButton);
        }
        button = new ArrayList<>(res);
        return button;
    }

    // 按钮排序
    public List<TSysPermissions> buttonSort(Set<TSysPermissions> button) {
        List<TSysPermissions> buttons = new ArrayList<>(button);
        Collections.sort(buttons, new Comparator<TSysPermissions>() {
            @Override
            public int compare(TSysPermissions o1, TSysPermissions o2) {
                return o1.getSort() - o2.getSort();
            }
        });
        return buttons;
        /*TSysPermissions[] arr = new TSysPermissions[button.size()];
        Iterator<TSysPermissions> iterator = button.iterator();
        while (iterator.hasNext()) {
            TSysPermissions next = iterator.next();
            arr[next.getSort()-1] = next;
            if (next.getChild().size() != 0) {
                next.setChild(buttonSort(next.getChild()));
            }
        }
        return Arrays.asList(arr);*/
    }

    /**
     * 查询用户菜单字段
     * @param menuId
     * @return
     */
    @Override
    public List<TSysFields> getUserMenuField(Integer menuId){
        Map<String, Object> params = new HashMap<>();
        params.put("menuId", menuId);
        //用户角色
        String username = CurrentUser.getCurrentUsername();
        List<UserRoleVO> roleIds = tSysRoleMapper.findUserRoles(username);
        List<Integer> ids = new ArrayList<>();
        for (UserRoleVO ur: roleIds){
            ids.add(Integer.parseInt(ur.getId()));
        }
        params.put("list", ids);
        List<TSysFields> userMenuField;
        Integer userId = CurrentUser.getCurrentUserID();
        String key ="sysUserId" + "-" + userId + "-" + "pmsn" + "-" + menuId + "-" + "field";
        if (!redisUtil.hasKey(key)){
            userMenuField = tSysPermissionsMapper.getUserMenuField(params);
            redisUtil.set(key, userMenuField);
        }else {
            userMenuField = (List<TSysFields>) redisUtil.get(key);
            if (null == userMenuField || userMenuField.size() == 0){
                userMenuField = tSysPermissionsMapper.getUserMenuField(params);
                redisUtil.set(key, userMenuField);
            }
        }
        return userMenuField;
    }

    /**
     * 递归合并弹窗按钮
     * @param set
     * @param button
     * @return
     */
    public static TSysPermissions mergeChild(Set<TSysPermissions> set, TSysPermissions button) {
        Set<TSysPermissions> list = new HashSet<>();
        for (TSysPermissions tps:set) {
           if (button.getId().equals(tps.getPid())) {
               list.add(mergeChild(set, tps));
           }
        }
        button.setChild(list);
        return button;
    }

    /**
     * 整理所有pid
     * @param tSysPermissions
     * @return
     */
    public static Set<Integer> getMenuId(List<TSysPermissions> tSysPermissions){
        Set<Integer> menuId = new HashSet<>();
        for (TSysPermissions tsp: tSysPermissions){
            menuId.add(tsp.getPid());
        }
        return menuId;
    }

    /**
     * 查询
     * @param record
     * @return
     */
    @Override
    public ResultUtil findList(TSysPermissions record) {
        List<TSysPermissionsVo> menuTrees = tSysPermissionsMapper.getSysPermissions(record);
        List<TSysPermissionsVo> menuTreeList = new ArrayList<TSysPermissionsVo>();
        for (TSysPermissionsVo menu: menuTrees){
            //判断id 里含m 为菜单  pid为空 或为0 为权限功能
            if ("0".equals(menu.getPid()) || menu.getPid()==null ) {
                menuTreeList.add(menu);
            }
            for (TSysPermissionsVo it : menuTrees) {
                if(it.getPid() != null && it.getPid()!="0"){
                    if ((it.getPid()) .equals(menu.getId())){
                        if (menu.getChildren() == null) {
                            menu.setChildren(new ArrayList<>());
                        }
                        menu.getChildren().add(it);
                    }
                }
            }
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),menuTreeList);

    }

    /**
     * 新增
     * @param record
     * @return
     */
    @Override
    public int save(TSysPermissions record) {
        //如果当前用户不是超级管理员，则添加的权限是当前用户所在的机构
        if (!CurrentUser.isSuperAdmin()){
            record.setOrgId(CurrentUser.getCurrentUserOrgID());
        }
        Integer lastSort = tSysPermissionsMapper.getLastPermissionSort(record.getPid());
        if (lastSort != null && lastSort != 0) {
            record.setSort(lastSort + 1);
        } else {
            record.setSort(1);
        }
        return tSysPermissionsMapper.insertSelective(record);
    }

    /**
     * 修改
     * @param record
     * @return
     */
    @Override
    public int update(TSysPermissions record) {
        redisUtil.slurDel("*pmsn-" + record.getId() + "-button");
        record.setUpdateTime(new Date());
        return tSysPermissionsMapper.updateByPrimaryKeySelective(record);
    }

    /**
     *  删除
     * @param id
     * @return
     */
    @Override
    public int delete(String[] id) {
        Map<String, Object> map = new HashMap<>();
        List<Integer> list = new ArrayList<>();
        for (String i:id) {
            list.add(Integer.parseInt(i));
        }
        map.put("ids", list);
                //删除redis中以缓存的菜单按钮和字段
        for (Integer permissionId: list){
            if (null == permissionId) {
                continue;
            } else {
                permissionId = getPid(permissionId);
            }
            redisUtil.slurDel("*pmsn-" + permissionId + "*");
        }
        return tSysPermissionsMapper.logicDeletePermission(map);
    }

    public Integer getPid(Integer id) {
        TSysPermissions pv = tSysPermissionsMapper.getPmsFatherId(id);
        if (pv.getType().equals(1)) {
            return pv.getId();
        } else {
            return getPid(pv.getPid());
        }
    }


    /**
     * 用户权限
     * @param userId
     * @return
     */
    @Override
    public List<String> getUserPermissions(String userId) {
        return tSysPermissionsMapper.getUserPerissions(userId);
    }

    /**
     * 创建菜单树
     * <AUTHOR>
     * @param userRoleVO
     * @return
     */
    @Override
    public List<MenuDTO> findMenuByRole(List<UserRoleVO> userRoleVO) {
        List<Integer> roles = new ArrayList<>();

        for (UserRoleVO vo:userRoleVO) {
            roles.add(Integer.parseInt(vo.getId()));
        }

        List<MenuDTO> menuDTO = tSysPermissionsMapper.menuListNew(roles);
        for (MenuDTO m:menuDTO) {
            String component = m.getComponent();
            String componentName = "";
            if (StringUtils.isNotEmpty(component)){
                String[] split = component.split("/");
                if ( null != split && split.length > 1){
                    componentName = split[1];
                    m.setComponentName(StringUtils.upperCase(componentName));
                }
            }
            Meta meta = new Meta(m.getName(), m.getIcon());
            if ("0".equals(m.getPid())) {
                m.setComponent("Layout");
            }
            formatTreeMenu(menuDTO, m);
            m.setMeta(meta);
        }

        List<MenuDTO> list = new ArrayList<>();
        for (MenuDTO m:menuDTO) {
            if ("0".equals(m.getPid())){
                list.add(m);
            }
        }

        return list;
    }
    public MenuDTO formatTreeMenu(List<MenuDTO> tree, MenuDTO menu) {
        List<MenuDTO> list = new ArrayList<>();
        boolean show = false;
        for (MenuDTO m:tree) {
            if (menu.getId().equals(m.getPid())) {
                show = true;
                list.add(formatTreeMenu(tree, m));
            }
        }
        menu.setChildren(list);
        menu.setAlwaysShow(show);
        return menu;
    }

    @Override
    public List<TreeVO> getAllMenuField(Integer orgid) {
        return tSysPermissionsMapper.getAllMenuField(orgid, CurrentUser.getCurrentUserID());
    }

    @Override
    public ResultUtil getRoleMenuTree(Integer orgId) {
        //查询导航，父id为0
        TSysPermissions parentMenu = new TSysPermissions();
        parentMenu.setPid(0);
        parentMenu.setType(1);
        parentMenu.setOrgId(orgId);
        List<MenuPermissionTreeVO> parentSysPermissions = tSysPermissionsMapper.getMenuPermissionTree(parentMenu);
        //一级菜单
        Map<String, Object> params = new HashMap<>();
        params.put("orgId", orgId);
        params.put("type", 1);
        params.put("list", parentSysPermissions);
        List<MenuPermissionTreeVO> firstMenuLevelPermission = tSysPermissionsMapper.getFirstLevelPermission(params);
        //一级菜单按钮
        Map<String, Object> buttonParams = new HashMap<>();
        buttonParams.put("orgId", orgId);
        buttonParams.put("type", 2);
        List<MenuPermissionTreeVO> buttonPermission = tSysPermissionsMapper.getFirstLevelPermission(buttonParams);


        List<MenuPermissionTreeVO> menuTrees = new ArrayList<>();
        menuTrees.addAll(parentSysPermissions);
        menuTrees.addAll(firstMenuLevelPermission);
        menuTrees.addAll(buttonPermission);
        List<MenuPermissionTreeVO> menuTreeList = new ArrayList<MenuPermissionTreeVO>();
        for (MenuPermissionTreeVO menu: menuTrees){
            //判断id 里含m 为菜单  pid为空 或为0 为权限功能
            if (menu.getPid() == 0 ) {
                menuTreeList.add(menu);
            }
            for (MenuPermissionTreeVO it : menuTrees) {
                if(it.getPid() != null && it.getPid()!= 0){
                    if ((it.getPid()) .equals(menu.getId())){
                        if (menu.getChildren() == null) {
                            menu.setChildren(new ArrayList<>());
                        }
                        menu.getChildren().add(it);
                    }
                }
            }
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),menuTreeList);
    }

    @Override
    public ResultUtil getPermissionTree(Integer orgId) {


        return null;
    }

    @Override
    public List<TSysPermissions> selectByMenu() {
        String username = CurrentUser.getCurrentUsername();
        List<UserRoleVO> roles = tSysRoleMapper.findUserRoles(username);
        List list = new ArrayList();
        for(UserRoleVO vo:roles){
            list.add(vo.getId());
        }
        UserRoleVO userRoleVO = new UserRoleVO();
        userRoleVO.setList(list);
        return  tSysPermissionsMapper.selectByMenu(userRoleVO);

    }
}
