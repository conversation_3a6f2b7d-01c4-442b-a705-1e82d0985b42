package com.lz.system.service.impl;

//import com.codingapi.tx.annotation.TxTransaction;

import com.codingapi.txlcn.tc.annotation.DTXPropagation;
import com.codingapi.txlcn.tc.annotation.LcnTransaction;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lz.api.AccountService;
import com.lz.api.CompanyService;
import com.lz.api.TAccountAPI;
import com.lz.api.TEndSUserInfoAPI;
import com.lz.common.config.RedisUtil;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.model.CodeEnum;
import com.lz.common.model.UserDO;
import com.lz.common.model.UserRoleDTO;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.model.TBusinessBasic;
import com.lz.model.TEndUserInfo;
import com.lz.model.TVerificationCodeLog;
import com.lz.system.dao.*;
import com.lz.system.dto.TSysUserDto;
import com.lz.system.dto.TUserInfoDto;
import com.lz.system.dto.UserDTO;
import com.lz.system.dto.menu.PermissionsDTO;
import com.lz.system.model.*;
import com.lz.system.service.TSysUserService;
import com.lz.system.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @date 2019-03-20
*/
@Slf4j
@Service
public class TSysUserServiceImpl implements TSysUserService {
    /** 如果角色ID是1 ，就是超级管理员 Yan */
    private static final Integer ISADMIN = 1;

    @Resource
    private TSysUserMapper tSysUserMapper;

    @Resource

    private TUserRoleMapper tUserRoleMapper;

    @Resource
    private TSysPermissionsMapper permissionsMapper;

    @Resource
    private TOpruserOrgMapper opruserOrgMapper;

    @Resource
    private TUserInfoMapper userInfoMapper;

    @Autowired
    private CompanyService companyService;

    @Resource
    private DicCatMapper dicCatMapper;

    @Resource
    private DicCatItemMapper dicCatItemMapper;

    @Resource
    private TSysRoleMapper sysRoleMapper;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private AccountService accountService;

    @Autowired
    private TEndSUserInfoAPI endSUserInfoAPI;

    @Resource
    private TUserLoginLogMapper tUserLoginLogMapper;

    @Autowired
    private TEndSUserInfoAPI tEndSUserInfoAPI;


    @Override
    public TSysUser findById(Integer id) {
        TSysUser tSysUser = tSysUserMapper.selectByPrimaryKey(id);
        return tSysUser;
    }

    @Override
    @LcnTransaction(propagation = DTXPropagation.SUPPORTS)
    @Transactional
    public ResultUtil add(TSysUser resources) {
        TSysUser user = new TSysUser();
        user.setUsername(resources.getUsername());
        List<TSysUser> tSysUsers = tSysUserMapper.selectRepeatUsername(user);
        if (null != tSysUsers && tSysUsers.size() > 0){
            return ResultUtil.error("用户已存在");
        }
        tSysUserMapper.insertSelective(resources);
        return ResultUtil.ok(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TSysUser resources) {
        TSysUser tSysUser = tSysUserMapper.selectByPrimaryKey(resources.getId());

        // 此处需自己修改
        resources.setId(tSysUser.getId());
        tSysUserMapper.insertSelective(resources);
    }

    @LcnTransaction
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(int[] id) {
        userInfoMapper.logicDeleteUser(id);
        for(int i:id){
            TUserInfo userInfo = new TUserInfo();
            userInfo.setAccountId(i);
            userInfo.setEnable(true);
            userInfoMapper.updateByAccountIdSelective(userInfo);
        }
    }

    @Override
    public int updateStatus(Integer id) {
        return tSysUserMapper.updateStatus(id);
    }

    @LcnTransaction(propagation = DTXPropagation.SUPPORTS)
    @Transactional
    @Override
    public ResultUtil addUser(TSysUser user, Integer[] roleid) {
        UserInfoVO userInfoVO = tSysUserMapper.getUserName(user.getUsername());
        if (userInfoVO != null){
            return ResultUtil.error("用户账号已存在");
        }
        user.setEnable(false);
        tSysUserMapper.insertSelective(user);
        //添加账号表
        //添加用户机构关系表
        TOpruserOrg opruserOrg = new TOpruserOrg();
        opruserOrg.setOrgId(user.getOrgId());
        opruserOrg.setUserId(user.getId());
        opruserOrgMapper.insert(opruserOrg);
        //添加用户角色关系表
        List<TUserRole> list = new ArrayList<>();
        if (null != roleid && roleid.length > 0){
            for (int i = 0; i < roleid.length; i++) {
                TUserRole r = new TUserRole();
                r.setRoleId(roleid[i]);
                r.setUserId(user.getId());
                list.add(r);
            }
            tUserRoleMapper.insertBatch(list);
        }

        //添加账号信息表
        TUserInfo userInfo = new TUserInfo();
        userInfo.setAccountId(user.getId());
        userInfo.setStatus(DictEnum.ACCOUNTENABLE.code);
        userInfo.setEnable(false);
        userInfoMapper.insertSelective(userInfo);
        return  ResultUtil.ok(user);
    }

    @LcnTransaction(propagation = DTXPropagation.SUPPORTS)
    @Transactional
    @Override
    public void updateUser(Integer id, Integer orgId, String nickname, String name, String remark, Integer[] roleid,Boolean enable, Boolean status) {
        TSysUser user = tSysUserMapper.selectByPrimaryKey(id);
        user.setUsername(name);
        user.setNickname(nickname);
        user.setRemark(remark);
        user.setOrgId(orgId);
        user.setUpdateTime(new Date());
        user.setEnable(enable);
        user.setStatus(status);
        tSysUserMapper.updateByPrimaryKeySelective(user);
        opruserOrgMapper.deleteByUserId(id);
        //添加用户机构关系表
        TOpruserOrg opruserOrg = new TOpruserOrg();
        opruserOrg.setOrgId(user.getOrgId());
        opruserOrg.setUserId(user.getId());
        opruserOrgMapper.insert(opruserOrg);
        // 清除用户角色下菜单缓存
        redisUtil.del( "sysUserId-" + id  + "-menu");
        //删除用户角色关联表
        tUserRoleMapper.deleteByUserId(id);
        if (null != roleid && roleid.length > 0){
            List<TUserRole> list = new ArrayList<>();
            for (int i = 0; i < roleid.length; i++) {
                TUserRole r = new TUserRole();
                r.setRoleId(roleid[i]);
                r.setUserId(user.getId());
                list.add(r);
            }
            //添加用户角色关联表
            tUserRoleMapper.insertBatch(list);
        }

        //TODO 修改account表

        /*TAccount account = new TAccount();
        account.setUserId(user.getId());
        account.setAccountNo(user.getUsername());
        account.setNickname(user.getNickname());
        ResultUtil resultUtil = accountService.updateAccountForFeign(account);
        if (null != resultUtil && null != resultUtil.getCode() && resultUtil.getCode().equals("success")){
            try {
                LinkedHashMap data = (LinkedHashMap) resultUtil.getData();
                Integer accountId = (Integer) data.get("id");
                //TODO 修改t_end_user_info表
                TEndUserInfoVO endUserInfo = new TEndUserInfoVO();
                endUserInfo.setAccountId(accountId);
                endUserInfo.setRealName(user.getNickname());
                endUserInfo.setPhone(user.getUsername());
                endSUserInfoAPI.updateEnduserInfo(endUserInfo);
            } catch (Exception e){
                log.error("修改账号信息失败", e);
                throw new RuntimeException("修改账号信息失败");
            }
        }*/
    }

    @Override
    public ResultUtil selectByPage(Integer page, Integer size, String name, List<Integer> orgIds) {
        Map<String, Object> params = new HashMap<>();
        params.put("name", name);
        Page<Object> objectPage = PageHelper.startPage(page, size);
        List<TSysUser> tSysUserVOS = tSysUserMapper.selectByPage(params);
        for (int i = 0; i< tSysUserVOS.size(); i++){
            if (null != tSysUserVOS.get(i).getRoleId() && tSysUserVOS.get(i).getRoleId().length > 0){
                String[] ids = tSysUserVOS.get(i).getRoleIds().split(",");
                Integer[] roleId = new Integer[ids.length];
                for (int j = 0; j< ids.length; j++){
                    if (null != ids[j] && !"".equals(ids[j])){
                        roleId[j] = Integer.valueOf(ids[j]);
                    }
                }
                tSysUserVOS.get(i).setRoleId(roleId);
            }
        }
        ResultUtil resultUtil = new ResultUtil();
        resultUtil.setData(tSysUserVOS);
        resultUtil.setCount(objectPage.getTotal());
        return resultUtil;
    }

    @Override
    public List<User> findUser(String username) {
        return tSysUserMapper.findUserDimUserName(username);
    }

    @Override
    public TBusinessBasic selectByphone(String username) {

        return tSysUserMapper.selectByphone(username);
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        User user = new User();
        UserDO userDO = new UserDO();
        if (username.startsWith("openid")){
            String[] openids = username.split("openid");
            String openid = openids[1];
            user.setThridParyId(openid);
            user = tSysUserMapper.findUser(user);
            if (user == null) {
                throw new UsernameNotFoundException("Source must not be null");
            }
            BeanUtils.copyProperties(user, userDO);
            return new CustomUserDetails(userDO, user.getEnable(), true, true, true, null);
        }
        if (username.startsWith("business")){
            String[] businesses = username.split("business");
            String phone = businesses[1];
            TBusinessBasic tBusinessBasic = tSysUserMapper.selectByphone(phone);
            if (tBusinessBasic == null) {
                throw new UsernameNotFoundException("Source must not be null");
            }
            BeanUtils.copyProperties(tBusinessBasic, userDO);
            userDO.setUsertype("BUSINESS");
            return new CustomUserDetails(userDO, true, true, true, true, null);
        }

        user.setUsername(username);
        user = tSysUserMapper.findUser(user);

        if (null != user && null != user.getCompanyIds() && user.getCompanyIds().length() >0){
            String[] companys = user.getCompanyIds().split(",");
            List<String> companysId = Arrays.asList(companys);
            user.setCompanysId(companysId);
            String companyId = companysId.get(0);
            user.setCompanyId(Integer.valueOf(companyId));
        }

        /*if (user != null) {
            throw new UsernameNotFoundException("USER_NOT_EXIST");
        }*/
        BeanUtils.copyProperties(user, userDO);
        /*UserFrequentlyUsedInfoDTO infoDTO = tSysUserMapper.selectFrequentlyUsedInfo(user.getId());
        if (null != infoDTO){
            BeanUtils.copyProperties(infoDTO, userDO);

        }*/
        // 判断账号是否是企业管理员
        if (null != user.getCompanyId()){
            Boolean aBoolean = userInfoMapper.judgeAccountIsCompanyAdmin(user.getCompanyId(), user.getAccountId());
            userDO.setCompanyAdmin(aBoolean);
        }

        //用户角色
        List<Integer> userRoleId = tSysUserMapper.findUserRoleId(user);
        if (userRoleId.contains(ISADMIN)){
            userDO.setSuperAdmin(true);
        }else {
            userDO.setSuperAdmin(false);
        }
        List<UserRoleDTO> userRole = tUserRoleMapper.getUserRole(user);
        userDO.setUserRoles(userRole);

        //权限
        Set<PermissionsDTO> userPermissions = new HashSet<>();
        //如果是超级管理员,查询所有权限
        if (userDO.getSuperAdmin()){
            userPermissions = permissionsMapper.getAllPermission();
        }else {
            //查询用户所属角色的权限
            for (UserRoleDTO userRoleDTO: userRole){
                Set<PermissionsDTO> permissionByRole = permissionsMapper.getPermissionByRole(userRoleDTO.getRoleId());
                userPermissions.addAll(permissionByRole);
            }
        }

        List<PerimissionsDO> perimissionsDOS = new ArrayList<>();
        Iterator<PermissionsDTO> iterator = userPermissions.iterator();
        while (iterator.hasNext()){
            PerimissionsDO perimissionsDO = new PerimissionsDO();
            PermissionsDTO resources = iterator.next();
            if (null != resources) {
                BeanUtils.copyProperties(resources, perimissionsDO);
                perimissionsDOS.add(perimissionsDO);
            }
        }
        // Not involve authorities, so pass null to authorities
        //系统全局启用状态：0：启用 1：未启用；Spring Security：0：未启用 1： 启用
        //
        userDO.setCompanyId(user.getCompanyId());
        //查询C端用户角色
        List<TEndUserInfo> endUserInfoList = tSysUserMapper.selectEnduserRoleByAccountIdList(user.getAccountId());
        String endUserIds = "";
        String userLogisticsRole = "";
        if(endUserInfoList!=null&& !"".equals(endUserInfoList) && endUserInfoList.size()>0){
            for(TEndUserInfo endUserInfo:endUserInfoList){
                if(endUserInfoList.size()>1){
                    endUserIds += endUserInfo.getId()+",";
                    userLogisticsRole += endUserInfo.getUserLogisticsRole()+",";
                }else{
                    userDO.setEndUserId(endUserInfo.getId());
                    userLogisticsRole = endUserInfo.getUserLogisticsRole();
                    userDO.setUserLogisticsRole(endUserInfo.getUserLogisticsRole());
                }

            }
        }
        if (endUserInfoList.size()>0){
            userDO.setEndUserIds(endUserIds);
            userDO.setUserLogisticsRole(userLogisticsRole);
        }
        // 如果权限是空，判断是不是CA，如果是CA添加默认权限
        if (perimissionsDOS.isEmpty()) {
            if (null != userDO.getUsertype() && DictEnum.CA.code.equals(userDO.getUsertype())) {
                PerimissionsDO perimissionsDO = new PerimissionsDO();
                perimissionsDO.setId("1");
                perimissionsDOS.add(perimissionsDO);
            }
        }
        return new CustomUserDetails(userDO, user.getStatus(), true, true, true, perimissionsDOS);
    }
    @Override
    @LcnTransaction(propagation = DTXPropagation.SUPPORTS)
    @Transactional
    public int updateByPass(TSysUser record) {
        record.setIfPasswordSecurity(true);
        // 修改t_user_info密码重置
        TSysUser tSysUser = tSysUserMapper.selectByPrimaryKey(record.getId());
        UserInfoVO userInfoVO = tSysUserMapper.getUserInfo(tSysUser.getUsername());
        if (null != userInfoVO) {
            TUserInfo userInfo = new TUserInfo();
            userInfo.setAccountId(tSysUser.getId());
            userInfo.setPasswordResetTime(new Date());
            userInfo.setUpdateUser(record.getNickname());
            userInfo.setUpdateTime(new Date());
            userInfo.setPasswordErrorCount(0);
            if (null != userInfoVO.getStatus() && DictEnum.ACCOUNTLOCK.code.equals(userInfoVO.getAccountStatus())) {
                userInfo.setStatus(DictEnum.ACCOUNTENABLE.code);
            }
            userInfo.setPasswordResetTime(new Date());
            userInfoMapper.updateByAccountIdSelective(userInfo);
        }

        redisUtil.del(tSysUser.getUsername());

        return tSysUserMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public User selectUser(User user) {
        return tSysUserMapper.findUser(user);
    }

    @LcnTransaction(propagation = DTXPropagation.SUPPORTS)
    @Transactional
    @Override
    public ResultUtil updateUserByAccountNo(TSysUserVO sysUserVO) {

        TSysUser sysUser = new TSysUser();
        sysUser.setId(sysUserVO.getId());
        sysUser.setUsername(sysUserVO.getUsername());
        //查询是否已有此账号
        List<TSysUser> tSysUsers = tSysUserMapper.selectRepeatUsername(sysUser);
        if (null != tSysUsers && tSysUsers.size() > 0){
            return ResultUtil.error("此账号已存在");
        }
        sysUser.setNickname(sysUserVO.getNickname());
        sysUser.setAccountNo(sysUserVO.getAccountNo());
        sysUser.setUsername(sysUserVO.getAccountNo());
        tSysUserMapper.updateByPrimaryKeySelective(sysUser);

        if(null!=sysUserVO.getRolesCode()){
            tUserRoleMapper.deleteByUserId(sysUser.getId());
            HashSet<String> rolesCode = sysUserVO.getRolesCode();

            if (null != rolesCode && rolesCode.size() > 0){
                List<TUserRole> list = new ArrayList<>();
                for (String roleCode : rolesCode) {
                    if (roleCode.equals(DictEnum.COMPANYSENDORDER.code) || roleCode.equals(DictEnum.COMMONSENDORDER.code)){
                        TUserRole qjs = getUserRoleInstance(sysUser.getId(), "FD");
                        list.add(qjs);
                    } else if (roleCode.equals(DictEnum.PAYORDER1.code)){
                        TUserRole qjs = getUserRoleInstance(sysUser.getId(), "KJ");
                        list.add(qjs);
                    } else if (roleCode.equals(DictEnum.RECEIVEORDER.code)){
                        TUserRole qjs = getUserRoleInstance(sysUser.getId(), "SD");
                        list.add(qjs);
                    } else if (roleCode.equals(DictEnum.ACCOUNTCHECK.code)){
                        TUserRole qjs = getUserRoleInstance(sysUser.getId(), "QJS");
                        list.add(qjs);
                    }  else if (roleCode.equals(DictEnum.PAYMENTREVIEW.code)){
                        TUserRole qjs = getUserRoleInstance(sysUser.getId(), "qyzfshy");
                        list.add(qjs);
                    }
                }
                if (list != null && list.size() >0){
                    tUserRoleMapper.insertBatch(list);
                }
            }

            // 删除菜单
            redisUtil.del("sysUserId-" +  sysUser.getId()  + "-menu");
            // 删除字段按钮
            String key = "sysUserId-" + sysUser.getId() + "-" + "pmsn-*";
            redisUtil.slurDel(key);
        }

        return ResultUtil.ok();
    }

    @Override
    public List<TSysUser> selectByPhone(String phone) {
        return tSysUserMapper.selectByPhone(phone);
    }

    @Override
    public List<TSysUser> selectByPhoneAndId(String phone, Integer id) {
        return tSysUserMapper.selectByPhoneAndId(phone, id);
    }

    @Override
    public TSysUser selectById(Integer id) {
        return tSysUserMapper.selectByPrimaryKey(id);
    }

    @Override
    public TSysUser selectIfPayPasswordNullByAccountNo(String accountNo) {
        return tSysUserMapper.selectIfPayPasswordNullByAccountNo(accountNo);
    }

    @Override
    public TSysUserVO selectIfOperationsPersonnel(String username) {
        return tSysUserMapper.selectIfOperationsPersonnel(username);
    }

    @Override
    public ResultUtil operationsPersonnelLock(TUserInfo tUserInfo) {
        userInfoMapper.updateByPrimaryKeySelective(tUserInfo);
        return ResultUtil.ok();
    }

    @Override
    public ResultUtil selectUserById(TSysUser record) {
        TSysUser tSysUser = tSysUserMapper.selectUserById(record);
        return ResultUtil.ok(tSysUser);
    }

    @Override
    public UserInfoVO getUserInfo(String username){
        List<UserInfoVO> userInfoVOList = tSysUserMapper.getUserInfoList(username);
        UserInfoVO userInfo = null;
        if(userInfoVOList.size()>0){
            String userLogisticsRole = "";
            if (userInfoVOList.size() == 1) {
                userLogisticsRole = userInfoVOList.get(0).getUserLogisticsRole();
            } else {
                for(UserInfoVO userInfoVO:userInfoVOList){
                    userLogisticsRole +=userInfoVO.getUserLogisticsRole()+",";
                }
            }
            userInfo = userInfoVOList.get(0);
            userInfo.setUserLogisticsRole(userLogisticsRole);
        }
        return  userInfo;
    }


    public ResultUtil updateLoginPhone(String phone,String username){
        tSysUserMapper.updateLoginPhone(phone,username);
        return ResultUtil.ok();
    }

    public TVerificationCodeLog selectByPhoneAndCodeInfo(String phone,String code){
        TVerificationCodeLog selectByPhoneAndCodeInfo =  tSysUserMapper.selectByPhoneAndCodeInfo(phone,code);

        return selectByPhoneAndCodeInfo;
    }

    /**
     *  @author: dingweibo
     *  @Date: 2019/6/23 15:09
     *  @Description: 新增权限
     */
    @Override
    public ResultUtil saveUserRole(TUserRole tUserRole) {
        tUserRoleMapper.insertSelective(tUserRole);
        return  ResultUtil.ok();
    }

    @LcnTransaction
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil saveUserRoles(UserRoleSaveVO vo) {
        List<String> roleCodes = vo.getRoleCodes();
        for (int i = 0; i < roleCodes.size(); i++) {
            String roleCode = roleCodes.get(i);
            List<TUserRole> tUserRoles = tUserRoleMapper.selectRoleByUser(vo.getUserId(), roleCode);
            if (null == tUserRoles || tUserRoles.size() == 0){
                TUserRole tUserRole = new TUserRole();
                tUserRole.setUserId(vo.getUserId());
                List<TSysRole> tSysRoles = sysRoleMapper.selectByParam(vo.getRoleCodeParams().get(i));
                TSysRole tSysRole = tSysRoles.get(0);
                tUserRole.setRoleId(tSysRole.getId());
                tUserRoleMapper.insertSelective(tUserRole);
            }
        }
        // 清除当前用户菜单缓存
        redisUtil.del("sysUserId-" + vo.getUserId() + "-menu");
        return ResultUtil.ok();
    }

    /**
    * @Description 查询用户信息
    * <AUTHOR>
    * @Date   2019/6/27 9:26
    * @Param
    * @Return
    * @Exception
    *
    */
    @Override
    public UserDTO selectUserInfo(UserInfoVO record) {
        UserDTO userDTO = tSysUserMapper.selectUserInfo(record);
        return userDTO;
    }

    @LcnTransaction(propagation = DTXPropagation.SUPPORTS)
    @Transactional
    @Override
    public ResultUtil saveUserAndPCRole(TSysUserForAddVO record) {
        //保存系统用户
        TSysUser user = new TSysUser();
        user.setUsername(record.getUsername());
        List<TSysUser> tSysUsers = tSysUserMapper.selectRepeatUsername(user);
        if (null != tSysUsers && tSysUsers.size() > 0){
            return ResultUtil.error("用户已存在");
        }
        user.setAccountNo(record.getAccountNo());
        user.setNickname(record.getNickname());
        if (null != record.getAcctType() && StringUtils.isNotBlank(record.getAcctType())) {
            user.setAcctype(record.getAcctType());
        }
        if (null != record.getUserType() && StringUtils.isNotBlank(record.getUserType())) {
            user.setUsertype(record.getUserType());
        }
        if (null != record.getRegNode() && StringUtils.isNotBlank(record.getRegNode())) {
            user.setRegnode(record.getRegNode());
        }
        if (null != record.getDataFrom() && StringUtils.isNotBlank(record.getDataFrom())) {
            user.setDatafrom(record.getDataFrom());
        }
        String pwd = "";
        if(null != record.getPassword()){
            pwd = DigestUtils.md5Hex(record.getPassword());
        }else {
            pwd = DigestUtils.md5Hex("123456");
        }
        user.setPassword(pwd);
        user.setEnable(false);
        if (null != record.getUserType()) {
            user.setUsertype(record.getUserType());
        }
        tSysUserMapper.insertSelective(user);

        if(null!=record.getRolesCode()){
            //线路角色
            HashSet<String> rolesCode = record.getRolesCode();
            if (!rolesCode.isEmpty()){
                List<TUserRole> tUserRoles = tUserRoleMapper.selectRoleByUserId(user.getId());
                List<TUserRole> list = new ArrayList<>();
                for (String roleCode : rolesCode) {
                    if (roleCode.equals(DictEnum.COMPANYSENDORDER.code) || roleCode.equals(DictEnum.COMMONSENDORDER.code)){
                        TUserRole qjs = getUserRoleInstance(user.getId(), "FD");
                        list.add(qjs);
                    } else if (roleCode.equals(DictEnum.PAYORDER1.code)){
                        TUserRole qjs = getUserRoleInstance(user.getId(), "KJ");
                        list.add(qjs);
                    } else if (roleCode.equals(DictEnum.RECEIVEORDER.code)){
                        TUserRole qjs = getUserRoleInstance(user.getId(), "SD");
                        list.add(qjs);
                    } else if (roleCode.equals(DictEnum.ACCOUNTCHECK.code)){
                        TUserRole qjs = getUserRoleInstance(user.getId(), "QJS");
                        list.add(qjs);
                    }  else if (roleCode.equals(DictEnum.PAYMENTREVIEW.code)){
                        TUserRole qjs = getUserRoleInstance(user.getId(), "qyzfshy");
                        list.add(qjs);
                    } else if (roleCode.equals(DictEnum.RESOURCECOMPANYRECEIVE.code)) {
                        TUserRole qjs = getUserRoleInstance(user.getId(), "RFD");
                        list.add(qjs);
                    } else if (roleCode.equals(DictEnum.RESOURCECOMPANYPAYER.code)) {
                        TUserRole qjs = getUserRoleInstance(user.getId(), "RKJ");
                        list.add(qjs);
                    }
                }
                if(!list.isEmpty()){
                    if (!tUserRoles.isEmpty()) {
                        List<TUserRole> collect = list.stream().filter((role) -> tUserRoles.stream().noneMatch((role1) -> role1.getRoleId().equals(role.getRoleId()))).collect(Collectors.toList());
                        tUserRoleMapper.insertBatch(collect);
                    } else {
                        tUserRoleMapper.insertBatch(list);
                    }
                }
            }
        }

        return ResultUtil.ok(user);
    }

    @Override
    @LcnTransaction
    @Transactional
    public void saveAccountUserRole(TSysUserForAddVO record){
        List<TUserRole> tUserRoles = tUserRoleMapper.selectRoleByUserId(record.getUserId());
        //线路角色
        List<String> rolesCodeNew = record.getRolesCodeNew();
        List<TUserRole> list = new ArrayList<>();
        for (String roleCode : rolesCodeNew) {
            DicCatItem dicCatItem = dicCatItemMapper.selectByCode(roleCode);
            if (null != dicCatItem && StringUtils.isNotBlank(dicCatItem.getParam1())) {
                TUserRole qjs = getUserRoleInstance(record.getUserId(), dicCatItem.getParam1());
                list.add(qjs);
            }
        }
        if(!list.isEmpty()){
            if (!tUserRoles.isEmpty()) {
                for (int i = list.size() - 1; i >= 0; i--) {
                    TUserRole tUserRole = list.get(i);
                    for (TUserRole userRole : tUserRoles) {
                        if (tUserRole.getRoleId().equals(userRole.getRoleId())) {
                            list.remove(i);
                            break;
                        }
                    }
                }
                if (!list.isEmpty()) {
                    tUserRoleMapper.insertBatch(list);
                }
            } else {
                tUserRoleMapper.insertBatch(list);
            }
        }
    }

    private TUserRole getUserRoleInstance(Integer userId, String param) {
        List<TSysRole> fd = sysRoleMapper.selectByParam(param);
        TUserRole r = new TUserRole();
        if (null != fd && fd.size() > 0){
            TSysRole role = fd.get(0);
            r.setRoleId(role.getId());
            r.setUserId(userId);
        }
        return r;
    }

    @Override
    @LcnTransaction
    @Transactional
    public void updateAccountUserRole(TSysUserForAddVO record){
        tUserRoleMapper.deleteByUserId(record.getUserId());
        //线路角色
        List<String> rolesCodeNew = record.getRolesCodeNew();
        List<TUserRole> list = new ArrayList<>();
        for (String roleCode : rolesCodeNew) {
            DicCatItem dicCatItem = dicCatItemMapper.selectByCode(roleCode);
            if (null != dicCatItem && StringUtils.isNotBlank(dicCatItem.getParam1())) {
                TUserRole qjs = getUserRoleInstance(record.getUserId(), dicCatItem.getParam1());
                list.add(qjs);
            }
        }
        tUserRoleMapper.insertBatch(list);
    }

    @Override
    public ResultUtil updateParam2(TSysUserDto tSysUser) {
        TSysUser record = tSysUserMapper.selectByPrimaryKey(tSysUser.getId());
        StringBuffer stringBuffer = new StringBuffer("{\"fsbtn\":");
        stringBuffer.append(tSysUser.getFsbtn());
        stringBuffer.append(",\"ydyzs\":");
        stringBuffer.append(tSysUser.getYdyzs());
        stringBuffer.append("}");
        record.setParam2(stringBuffer.toString());
        tSysUserMapper.updateByPrimaryKeySelective(record);
        return ResultUtil.ok();
    }

    @Override
    public ResultUtil judgePhoneExists(String phone) {
        Map<String, String> exists = tSysUserMapper.judgePhoneExistsNew(phone);
        String msg = "";
        if (null == exists || exists.isEmpty()) {
            return ResultUtil.ok();
        } else {
            if ("BD".equals(exists.get("usertype"))) {
                msg = exists.get("companyName");
                if(msg != null) {
                    return ResultUtil.error("企业管理员手机号已被:"+ msg +"占用");
                }
            }
            return ResultUtil.error("企业管理员手机号已被:占用");
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2023/12/11 18:10
     *  @Description: 登录密码
     */
    @Transactional
    @Override
    public void recordPassowordError(String username) {
        List<UserInfoVO> userInfoVOList = tSysUserMapper.getUserInfoList(username);
        UserInfoVO userInfo = null;
        if(userInfoVOList.size()>0){
            String userLogisticsRole = "";
            if (userInfoVOList.size() == 1) {
                userLogisticsRole = userInfoVOList.get(0).getUserLogisticsRole();
            } else {
                for(UserInfoVO userInfoVO:userInfoVOList){
                    userLogisticsRole +=userInfoVO.getUserLogisticsRole()+",";
                }
            }
            userInfo = userInfoVOList.get(0);
            userInfo.setUserLogisticsRole(userLogisticsRole);
        }
        if (null != userInfo) {
            if (null != userInfo.getUserInfoId()) {
                TUserInfo tUserInfo = new TUserInfo();
                tUserInfo.setId(userInfo.getUserInfoId());
                if (null != userInfo.getPasswordErrorCount()) {
                    if (userInfo.getPasswordErrorCount().equals(4)) {
                        tUserInfo.setAccountLockTime(new Date());
                        tUserInfo.setStatus(DictEnum.ACCOUNTLOCK.code);
                    }
                    tUserInfo.setPasswordErrorCount(userInfo.getPasswordErrorCount() + 1);
                }else {
                    tUserInfo.setPasswordErrorCount(1);
                }
                tUserInfo.setUpdateUser(userInfo.getNickname());
                userInfoMapper.updateByPrimaryKeySelective(tUserInfo);
            }
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2023/12/11 18:10
     *  @Description: 重置密码
     */
    @Transactional
    @Override
    public void recordPassowordErrorNew(String username) {
        List<UserInfoVO> userInfoVOList = tSysUserMapper.getUserInfoList(username);
        UserInfoVO userInfo = null;
        if(userInfoVOList.size()>0){
            String userLogisticsRole = "";
            if (userInfoVOList.size() == 1) {
                userLogisticsRole = userInfoVOList.get(0).getUserLogisticsRole();
            } else {
                for(UserInfoVO userInfoVO:userInfoVOList){
                    userLogisticsRole +=userInfoVO.getUserLogisticsRole()+",";
                }
            }
            userInfo = userInfoVOList.get(0);
            userInfo.setUserLogisticsRole(userLogisticsRole);
        }
        if (null != userInfo) {
            if (null != userInfo.getUserInfoId()) {
                TUserInfo tUserInfo = new TUserInfo();
                tUserInfo.setId(userInfo.getUserInfoId());
                if (null != userInfo.getResetpasswordErrorCount()) {
                    if (userInfo.getResetpasswordErrorCount().equals(2)) {
                        tUserInfo.setAccountLockTime(new Date());
                        tUserInfo.setStatus(DictEnum.ACCOUNTLOCK.code);
                    }
                    tUserInfo.setResetpasswordErrorCount(userInfo.getResetpasswordErrorCount() + 1);
                }else {
                    tUserInfo.setResetpasswordErrorCount(1);
                }
                tUserInfo.setUpdateUser(userInfo.getNickname());
                userInfoMapper.updateByPrimaryKeySelective(tUserInfo);
            }
        }
    }

    /**
     *  @author: dingweibo
     *  @Date: 2023/12/11 18:10
     *  @Description: 验证码登录
     */
    @Transactional
    @Override
    public void recordPassowordErrorLogincode(String username) {
        List<UserInfoVO> userInfoVOList = tSysUserMapper.getUserInfoList(username);
        UserInfoVO userInfo = null;
        if(userInfoVOList.size()>0){
            String userLogisticsRole = "";
            if (userInfoVOList.size() == 1) {
                userLogisticsRole = userInfoVOList.get(0).getUserLogisticsRole();
            } else {
                for(UserInfoVO userInfoVO:userInfoVOList){
                    userLogisticsRole +=userInfoVO.getUserLogisticsRole()+",";
                }
            }
            userInfo = userInfoVOList.get(0);
            userInfo.setUserLogisticsRole(userLogisticsRole);
        }
        if (null != userInfo) {
            if (null != userInfo.getUserInfoId()) {
                TUserInfo tUserInfo = new TUserInfo();
                tUserInfo.setId(userInfo.getUserInfoId());
                if (null != userInfo.getLogincodeErrorCount()) {
                    if (userInfo.getLogincodeErrorCount().equals(2)) {
                        tUserInfo.setAccountLockTime(new Date());
                        tUserInfo.setStatus(DictEnum.ACCOUNTLOCK.code);
                    }
                    tUserInfo.setLogincodeErrorCount(userInfo.getLogincodeErrorCount() + 1);
                }else {
                    tUserInfo.setLogincodeErrorCount(1);
                }
                tUserInfo.setUpdateUser(userInfo.getNickname());
                userInfoMapper.updateByPrimaryKeySelective(tUserInfo);
            }
        }
    }

    @Override
    public void resetPasswordError(String username) {
        UserInfoVO userInfo = tSysUserMapper.getUserInfo(username);
        if (null != userInfo) {
            if (null != userInfo.getUserInfoId()) {
                TUserInfo tUserInfo = userInfoMapper.selectByPrimaryKey(userInfo.getUserInfoId());
                tUserInfo.setId(userInfo.getUserInfoId());
                tUserInfo.setAccountLockTime(null);
                tUserInfo.setPasswordErrorCount(0);
                tUserInfo.setLogincodeErrorCount(0);
                tUserInfo.setResetpasswordErrorCount(0);
                tUserInfo.setStatus(DictEnum.ACCOUNTENABLE.code);
                userInfoMapper.updateById(tUserInfo);
            }
        }
    }

    @Override
    public void accountLoginTime(String username) {
        TUserLoginLogVO tUserLoginLogVO = tUserLoginLogMapper.selectBySysUserName(username);
        if (null != tUserLoginLogVO) {
            if (null != tUserLoginLogVO.getId()) {
                TUserLoginLog tUserLoginLog = tUserLoginLogMapper.selectByPrimaryKey(tUserLoginLogVO.getId());
                tUserLoginLog.setLoginTime(new Date());
                tUserLoginLog.setCreateUser(username);
                tUserLoginLog.setCreateTime(new Date());
                tUserLoginLog.setUpdateUser(username);
                tUserLoginLog.setUpdateTime(new Date());
                tUserLoginLogMapper.updateByPrimaryKey(tUserLoginLog);
            }else {
                TUserLoginLog tUserLoginLog = new TUserLoginLog();
                tUserLoginLog.setUserId(tUserLoginLogVO.getUserId());
                tUserLoginLog.setLoginTime(new Date());
                tUserLoginLog.setCreateUser(username);
                tUserLoginLog.setCreateTime(new Date());
                tUserLoginLog.setUpdateUser(username);
                tUserLoginLog.setUpdateTime(new Date());
                tUserLoginLog.setEnable(false);
                tUserLoginLogMapper.insertSelective(tUserLoginLog);
            }
        }
    }

    @Override
    public ResultUtil updateByAccountIdSelective(TUserInfo record){
        userInfoMapper.updateByAccountIdSelective(record);
        return  ResultUtil.ok();
    }

    @Override
    @LcnTransaction(propagation = DTXPropagation.SUPPORTS)
    @Transactional
    public ResultUtil insertUserInfo(TUserInfo record){
        userInfoMapper.insertSelective(record);
        return  ResultUtil.ok();
    }

    @Override
    public List<TEndUserInfo> selectEnduserRoleByAccountIdList(Integer accountId){
        //查询C端用户角色
        return tSysUserMapper.selectEnduserRoleByAccountIdList(accountId);
    }

    @Override
    public TSysRole selectCaptainSysRole(){
        return tSysUserMapper.selectCaptainSysRole();
    }

    @Override
    public ResultUtil insertCaptainUserRole(TUserRole tUserRole){
        tSysUserMapper.insertCaptainUserRole(tUserRole);
        return  ResultUtil.ok();
    }

    public List<TEndUserInfo> selectUserNameRole(String userName){
        return tSysUserMapper.selectUserNameRole(userName);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updatePassword(Integer id, String password) {
        tSysUserMapper.updatePassword(id, password);
        // 标记已修改过密码
        TUserInfo tUserInfo = new TUserInfo();
        tUserInfo.setAccountId(id);
        tUserInfo.setParam1("1");
        tUserInfo.setPasswordResetTime(new Date());
        userInfoMapper.updateByAccountIdSelective(tUserInfo);

        TSysUser tSysUser = tSysUserMapper.selectByPrimaryKey(id);
        redisUtil.del(tSysUser.getUsername());
        return 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updatePayPassword(Integer id, String payPassword) {
        tSysUserMapper.updatePayPassword(id, payPassword);
        return 1;
    }


    @Override
    public TUserRole getDataByID(Integer userId, Integer roleId) {
        return tUserRoleMapper.getDataByID(userId,roleId);
    }

    @Override
    public void save(TUserRole tUserRole) {
        if(tUserRole.getId()!=null){
            tUserRoleMapper.updateByPrimaryKey(tUserRole);
        }else{
            tUserRoleMapper.insertSelective(tUserRole);
        }
    }
    @LcnTransaction
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateById(TSysUser tSysUser) {
        tSysUserMapper.updateByPrimaryKeySelective(tSysUser);
    }

    @Override
    public String checkPayPassword(Integer companyId) {
        return tSysUserMapper.checkPayPassword(companyId);
    }

    @Override
    public ResultUtil userInfoSelectByPage(TUserInfoDto record){
        Page<Object> objectPage = PageHelper.startPage(record.getPage(), record.getSize());
        List<TUserInfoVo> list = tSysUserMapper.userInfoSelectByPage(record);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),objectPage,objectPage.getTotal());
    }

    @Override
    public ResultUtil updateUserInfoStatus(TUserInfoDto record){
        TUserInfo tUserInfo =  userInfoMapper.selectUserInfoByUserId(record.getUserId());
        tUserInfo.setStatus(DictEnum.ACCOUNTENABLE.code);
        tUserInfo.setPasswordErrorCount(0);
        tUserInfo.setLogincodeErrorCount(0);
        tUserInfo.setResetpasswordErrorCount(0);
        tUserInfo.setAccountLockTime(null);
        return ResultUtil.ok(userInfoMapper.updateByPrimaryKey(tUserInfo));
    }

    @Override
    public ResultUtil updateDriverPhone(String phone, String newphone) {
        ResultUtil resultUtil = tEndSUserInfoAPI.updateDriverPhone(phone,newphone);
        if(resultUtil.getCode().equals(CodeEnum.ERROR.getCode())){
            return ResultUtil.error("修改手机号失败!");
        }
        tSysUserMapper.updateDriverPhone(phone,newphone);
        return ResultUtil.ok();
    }

}