package com.lz.system.service;

import com.lz.common.util.ResultUtil;
import com.lz.system.model.TCityInfo;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019/4/25 - 10:34
 **/
public interface TCityInfoService {

    ResultUtil selectObject(TCityInfo cityInfo);

    ResultUtil selectFromEndCityCode(Map<String, String> param);

    /**
     * 查询所有省 市 区的名称和Code 存到Redis
     * Yan
     * @return
     */
    void selectProvice();
}
