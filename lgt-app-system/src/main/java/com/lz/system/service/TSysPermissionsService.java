package com.lz.system.service;

import com.lz.common.util.ResultUtil;
import com.lz.system.dto.menu.MenuDTO;
import com.lz.system.model.TSysFields;
import com.lz.system.model.TSysPermissions;
import com.lz.system.vo.TreeVO;
import com.lz.system.vo.UserRoleVO;
import com.lz.vo.TEndUserInfoVO;

import java.util.List;

public interface TSysPermissionsService {

    public ResultUtil findList(TSysPermissions record);

    public int save(TSysPermissions record);

    public int update(TSysPermissions record);

    public int delete(String[] id);


    /**
     * zhangjiji
     *
     * @param userId
     * @return
     */
    List<String> getUserPermissions(String userId);


    List<MenuDTO> findMenuByRole(List<UserRoleVO> userRoleVO);

    /**
     * 根据角色ID,和权限ID(Type是1的) 查询 Redis缓存的按钮
     *
     * @param pmsnId
     * @return
     * <AUTHOR>
     */
    List<TSysPermissions> findButtonByRolePermission(Integer pmsnId);

    List<TreeVO> getAllMenuField(Integer orgid);

    /**
     * 获取用户菜单字段
     *
     * @return
     */
    List<TSysFields> getUserMenuField(Integer menuId);
    /**
     * 角色管理->菜单权限树
     * <AUTHOR>
     * @param orgId
     * @return
     */
    ResultUtil getRoleMenuTree(Integer orgId);

    ResultUtil getPermissionTree(Integer orgId);

    List<TSysPermissions> selectByMenu();
}

