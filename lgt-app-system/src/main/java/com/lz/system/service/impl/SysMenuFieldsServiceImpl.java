package com.lz.system.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lz.common.config.RedisUtil;
import com.lz.common.model.CodeEnum;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.ResultUtil;
import com.lz.system.dao.TSysFieldsMapper;
import com.lz.system.dto.menu.MenuDTO;
import com.lz.system.service.SysMenuFieldsService;
import com.lz.system.vo.MenuFieldVO;
import com.lz.system.vo.MenuFieldsVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


@Service
public class SysMenuFieldsServiceImpl implements SysMenuFieldsService {

    @Autowired
    private TSysFieldsMapper sysFieldsMapper;
    @Autowired
    private TSysPermissionsServiceImpl tSysPermissionsService;
    @Autowired
    private RedisUtil redisUtil;
    @Override
    public ResultUtil findList(Integer menuId) {
        List<MenuFieldsVO> menuFFieldsVOList = sysFieldsMapper.findAll(menuId);
        return ResultUtil.ok(menuFFieldsVOList);
    }

    @Override
    public ResultUtil findMenu()
    {
        List<MenuDTO> menu = sysFieldsMapper.findMenu();
        return ResultUtil.ok(menu);
    }

    /**
     * 添加信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(String name, Integer menuId, String code, Integer sort) {
        MenuFieldsVO menuFieldsVO = new MenuFieldsVO();
        menuFieldsVO.setName(name);
        menuFieldsVO.setMenuId(menuId);
        menuFieldsVO.setCode(code);
        menuFieldsVO.setStatus(1);
        menuFieldsVO.setSort(sort);
        sysFieldsMapper.add(menuFieldsVO);

        //删除缓存
        String key = "-" + "fields";
        if (0 != menuId) {
            key = "*-" + menuId + key;
        } else {
            key = "*" + key;
        }
        redisUtil.slurDel(key);
    }

    @Override
    public ResultUtil sort(String rule, String field) {
        if ("".equals(rule)|| "".equals(field)){
            ResultUtil.error("字段或排序为空");
        }
        List<MenuFieldsVO> sortList = sysFieldsMapper.sort(rule, field);
        return ResultUtil.ok(sortList);
    }

    @Override
    public ResultUtil findPage(Integer page, Integer limit) {
        Page<Object> objectPage = PageHelper.startPage(page, limit);
//        List<MenuFieldsVO> menuFFieldsVOList = sysFieldsMapper.findAll();
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),objectPage,objectPage.getTotal());
    }

    @Override
    public ResultUtil update(MenuFieldVO mfv) {
        sysFieldsMapper.update(mfv);
        //删除缓存
        String key = "-" + "fields";
        if (0 != mfv.getMenuId()) {
            key = "*-" + mfv.getMenuId() + key;
        } else {
            key = "*" + key;
        }
        redisUtil.slurDel(key);
        return ResultUtil.ok("修改成功");
    }
}
