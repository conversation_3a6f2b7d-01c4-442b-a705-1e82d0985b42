package com.lz.system.service.impl;

import com.lz.common.config.RedisUtil;
import com.lz.common.util.ResultUtil;
import com.lz.system.dao.TCityInfoMapper;
import com.lz.system.dao.TCountryInfoMapper;
import com.lz.system.dao.TProviceInfoMapper;
import com.lz.system.model.TCityInfo;
import com.lz.system.service.TCityInfoService;
import com.lz.system.util.ProvinceCityCounty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2019/4/25 - 10:34
 **/
@Service
public class TCityInfoServiceImpl implements TCityInfoService {

    /** 省市县Redis Key */
    private static final String SSX_KEY = "ProviceCityCountry";

    @Resource
    private TCityInfoMapper cityInfoMapper;

    @Autowired
    TProviceInfoMapper proviceInfoMapper;

    @Autowired
    TCountryInfoMapper countryInfoMapper;

    @Autowired
    RedisUtil redisUtil;


    /**
     * 查询所有省市县,存到redis中
     * System启动后自动执行
     * @return
     */
    @Override
    public void selectProvice() {
        if (!redisUtil.hasKey(SSX_KEY)) {
            // 所有 省 市 县
            List<ProvinceCityCounty> province = proviceInfoMapper.selectAllProvice();
            redisUtil.set(SSX_KEY, province, -1);
        }
    }

    @Override
    public ResultUtil selectObject(TCityInfo cityInfo) {
        List<TCityInfo> tCityInfos = cityInfoMapper.selectObject(cityInfo);
        return ResultUtil.ok(tCityInfos);
    }

    @Override
    public ResultUtil selectFromEndCityCode(Map<String, String> param) {
        String fromName = param.get("fromName");
        String endName = param.get("endName");
        Map<String, String> result = new HashMap<>();
        TCityInfo cityInfo = new TCityInfo();
        cityInfo.setCityName(fromName.replace("市", ""));
        List<TCityInfo> list = cityInfoMapper.selectObject(cityInfo);
        if(null != list && list.size() >0) {
            result.put("fromCityCode", list.get(0).getCityCode());
            result.put("fromCityLocaltion", list.get(0).getLatitude() + "," + list.get(0).getLongitude());
        }
        cityInfo.setCityName(endName.replace("市", ""));
        List<TCityInfo> list1 = cityInfoMapper.selectObject(cityInfo);
        if(null != list1 && list1.size() >0) {
            result.put("endCityCode", list1.get(0).getCityCode());
            result.put("endCityLocaltion", list1.get(0).getLatitude() + "," + list1.get(0).getLongitude());

        }
        return ResultUtil.ok(result);
    }
}
