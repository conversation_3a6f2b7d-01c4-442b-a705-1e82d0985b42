//package com.lz.config;
//
//import com.alibaba.druid.pool.DruidDataSource;
//import com.taobao.txc.client.aop.TxcTransactionScaner;
//import com.taobao.txc.datasource.cobar.TxcDataSource;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory;
//import org.mybatis.spring.SqlSessionFactoryBean;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//
///**
// *  @author: xyz
// *  @Date: 2019/6/28 13:43
// *  @Description: 数据库监工具类
// */
//@Configuration
//public class DruidConfig {
//    // 将所有前缀为spring.datasource下的配置项都加载到DataSource中
//    @ConfigurationProperties(prefix = "spring.datasource")
//    @Bean("druidDataSource")
//    @Primary
//    public DruidDataSource druidDataSource() {
//        return new DruidDataSource();
//    }
//    @Bean("dataSourceProxy")
//    public TxcDataSource dataSourceProxy(DruidDataSource dataSource) {
//        return new TxcDataSource(dataSource);
//    }
//
//    @Bean
//    public SqlSessionFactory sqlSessionFactory(TxcDataSource dataSourceProxy) throws Exception {
//        SqlSessionFactoryBean factoryBean = new SqlSessionFactoryBean();
//        factoryBean.setDataSource(dataSourceProxy);
//        factoryBean.setMapperLocations(new PathMatchingResourcePatternResolver()
//                .getResources("classpath*:/mapping/*/*.xml"));
//        factoryBean.setTransactionFactory(new JdbcTransactionFactory());
//        return factoryBean.getObject();
//    }
//    @Bean(name = "txcScanner")
//    @ConfigurationProperties(prefix="aluser")
//    public TxcTransactionScaner txcTransactionScaner()
//    {
//        //xxxx填写txc的逻辑组名
//        return  new TxcTransactionScaner("lgt.1364524203166562.ZJK");
//    }
//}