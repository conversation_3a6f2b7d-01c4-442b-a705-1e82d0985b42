package com.lz.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;

/**
 * <AUTHOR>
 */
@Configuration
@EnableResourceServer
@EnableGlobalMethodSecurity(prePostEnabled = true,securedEnabled =true)//激活方法上的PreAuthorize注解
public class ResourceServerConfiguration extends ResourceServerConfigurerAdapter {

    @Autowired
    CustomAuthenticationSuccessHandler customAuthenticationSuccessHandler;
    @Autowired
    private AuthenticationManager authenticationManager;
    @Autowired
    CustomAuthenticationFailerHandler customAuthenticationFailerHandler;

    @Autowired
    private MobileCodeAuthenticationSecurityConfig mobileCodeAuthenticationSecurityConfig;

    @Override
    public void configure(HttpSecurity http) throws Exception {
        http.formLogin().loginPage("/login.html")
                //登录需要经过的url请求
               // .loginProcessingUrl("/oauth/token")
                .successHandler(customAuthenticationSuccessHandler).permitAll(); //登录成功后可使用loginSuccessHandler()存储用户信息，可选。
        http
            .authorizeRequests()

                .antMatchers( "/html/**","/api/**","/hystrix.stream","/api/**","/system/**","/druid/**", "/app/login/smsCode","/lgt-app-system/system/sysParam/getParamByKey").permitAll()
                .antMatchers("/swagger-ui.html").anonymous()
                .antMatchers("/login").permitAll()
                .antMatchers("/swagger-resources/**").anonymous()
                .antMatchers("/webjars/**").anonymous()
                .antMatchers("/*/api-docs").anonymous()
                .anyRequest().authenticated()
                .and()
                .csrf().disable()
                .headers().frameOptions().disable().and().apply(mobileCodeAuthenticationSecurityConfig);
//       http.addFilterAfter(bhAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);
       // http.addFilter(bhAuthenticationFilter());
    }
    @Bean
    public BhAuthenticationFilter bhAuthenticationFilter() {
        BhAuthenticationFilter filter = new BhAuthenticationFilter();
        filter.setAuthenticationManager(authenticationManager);
        filter.setAuthenticationSuccessHandler(customAuthenticationSuccessHandler); //处理成功
        filter.setAuthenticationFailureHandler(customAuthenticationFailerHandler);//失败处理
        return filter;
    }

}
