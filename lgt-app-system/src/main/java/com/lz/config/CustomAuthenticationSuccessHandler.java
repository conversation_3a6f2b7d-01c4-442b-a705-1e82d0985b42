package com.lz.config;

import cn.hutool.core.map.MapUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lz.common.config.RedisUtil;
import com.lz.common.util.DateUtils;
import com.lz.domain.Log;
import com.lz.model.TBusinessBasic;
import com.lz.service.LogService;
import com.lz.system.dao.TSysUserMapper;
import com.lz.system.dao.TUserLoginLogMapper;
import com.lz.system.model.CustomUserDetails;
import com.lz.system.model.TUserLoginLog;
import com.lz.system.vo.TUserLoginLogVO;
import com.lz.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.security.core.session.SessionRegistryImpl;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 认证成功处理器
 *
 * <AUTHOR> CatalpaFlat
 * @date ：Create in 21:31 2017/12/20
 */
@Component
public class CustomAuthenticationSuccessHandler implements AuthenticationSuccessHandler /*extends SavedRequestAwareAuthenticationSuccessHandler*/ {
    private static final Logger logger = LoggerFactory.getLogger(CustomAuthenticationSuccessHandler.class.getName());

    @Autowired
    private ClientDetailsService clientDetailsService;
    @Autowired
    private AuthorizationServerTokenServices authorizationServerTokenServices;
    @Autowired
    ObjectMapper objectMapper;
    @Autowired
    private SessionRegistry sessionRegistry;
    @Autowired
    LogService logService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private TUserLoginLogMapper userLoginLogMapper;
    @Resource
    private TSysUserMapper sysUserMapper;

    public CustomAuthenticationSuccessHandler() {
        logger.info("CustomAuthenticationSuccessHandler loading ...");
    }

    /**
     * 登录成功被调用
     */
    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
                                        Authentication authentication) throws IOException, ServletException {
         /*
         * authentication:封装认证信息（用户信息等）
         */
        logger.info("Authentication success");
        CustomUserDetails user = (CustomUserDetails) authentication.getPrincipal();

        //TODO 修改用户登陆信息
        Log log = new Log();
        // 描述
        if (log != null) {
            log.setDescription("登录");
        }
        log.setLogType("login");
        log.setUsername(user.getUsername());
        // 获取IP地址
         log.setRequestIp(StringUtils.getIP(request));
         log.setMethod("login");
         logService.saveLogin(log);
//         String remoteAddress = ((WebAuthenticationDetails)  authentication.getDetails()).getRemoteAddress();//ip

//        List<SessionInformation> allSessions = sessionRegistry.getAllSessions(authentication.getPrincipal(), false);
//        SessionInformation sessionInformation = allSessions.get(0);//一处登录
//        sessionInformation.getLastRequest();//最后访问时间

        //设备信息
        String type = request.getParameter("type");

        String clientId = request.getParameter("client_id");
        String clientSecret = request.getParameter("client_secret");

        ClientDetails clientDetails = clientDetailsService.loadClientByClientId(clientId);
        TokenRequest tokenRequest = new TokenRequest(MapUtil.newHashMap(), clientId, clientDetails.getScope(), "password");
        OAuth2Request oAuth2Request = tokenRequest.createOAuth2Request(clientDetails);

        OAuth2Authentication oAuth2Authentication = new OAuth2Authentication(oAuth2Request, authentication);
        OAuth2AccessToken oAuth2AccessToken = authorizationServerTokenServices.createAccessToken(oAuth2Authentication);
        logger.info("获取token 成功：{}", oAuth2AccessToken.getValue());
        logger.info("token有效期: {}", DateUtils.formatDate(oAuth2AccessToken.getExpiration()));
        redisUtil.set(user.getUsername(), oAuth2AccessToken.getValue(), (oAuth2AccessToken.getExpiration().getTime() - System.currentTimeMillis()) / 1000);
        String userType = null == user.getUsertype() ? null : user.getUsertype();
        if (null == userType) {
            TBusinessBasic tBusinessBasic = sysUserMapper.selectByphone(user.getUsername());
            if (null != tBusinessBasic) {
                userType = "BUSINESS";
            }
        }
        redisUtil.set("USERTYPE" + user.getUsername(), userType);

        // 记录token
        TUserLoginLogVO tUserLoginLogVO = userLoginLogMapper.selectBySysUserName(user.getUsername());
        if (null != tUserLoginLogVO && null != tUserLoginLogVO.getId()) {
            TUserLoginLog userLoginLog = new TUserLoginLog();
            userLoginLog.setId(tUserLoginLogVO.getId());
            userLoginLog.setParam1(oAuth2AccessToken.getValue());
            userLoginLogMapper.updateByPrimaryKeySelective(userLoginLog);
        }

        response.setCharacterEncoding("utf-8");
        response.setStatus(HttpStatus.OK.value());
        response.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
        response.getOutputStream().println(objectMapper.writeValueAsString(oAuth2AccessToken));
//        response.getOutputStream().close();
//        response.getWriter().write(objectMapper.writeValueAsString(oAuth2AccessToken));
        response.getOutputStream().flush();
        response.getOutputStream().close();
        return;
    }
    @Bean
    public SessionRegistry sessionRegistry() {
        return new SessionRegistryImpl();
    }
}
