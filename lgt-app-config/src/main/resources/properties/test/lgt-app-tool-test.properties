spring.datasource.url=**************************************************************************************
spring.datasource.username=root
spring.datasource.password=hebei2018root@.COM
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.druid.initial-Size=5
spring.datasource.druid.min-Idle=5
spring.datasource.druid.max-Active=200
spring.datasource.druid.max-Wait=60000
spring.datasource.druid.time-Between-Eviction-Runs-Millis=60000
spring.datasource.druid.min-Evictable-Idle-Time-Millis=300000
spring.datasource.druid.validation-Query=SELECT 1 FROM DUAL
spring.datasource.druid.test-While-Idle=true
spring.datasource.druid.test-On-Borrow=false
spring.datasource.druid.test-On-Return=false
spring.datasource.druid.filters=stat,wall,logback

mybatis.mapper-locations=classpath:mapping/*.xml
configLocation=classpath:mybatis-config.xml

spring.redis.host=**************
spring.redis.port=6309
spring.redis.password=
# ���ӳ������������ʹ�ø�ֵ��ʾû�����ƣ�
spring.redis.pool.max-active=5000
# ���ӳ���������ȴ�ʱ�䣨ʹ�ø�ֵ��ʾû�����ƣ�
spring.redis.pool.max-wait=-1
# ���ӳ��е�����������
spring.redis.pool.max-idle=500
# ���ӳ��е���С��������
spring.redis.pool.min-idle=200
# ���ӳ�ʱʱ�䣨���룩
spring.redis.timeout=10000
redisson.address=redis://**************:6309
redisson.password=

spring.zipkin.base-url=http://localhost:9411

server.port=2007
security.basic.enabled=false
spring.rabbitmq.host=**************
spring.rabbitmq.port=5672
spring.rabbitmq.username=root
spring.rabbitmq.password=hebei2018root@.COM

## ˢʱرհȫ֤
management.security.enabled=false

spring.sleuth.sampler.percentage=1.0

#hystrix.command.default.execution.timeout.enabled=false
#hystrix.command.default.execution.isolation.strategy=SEMAPHORE

hystrix.command.default.execution.isolation.strategy=SEMAPHORE
hystrix.command.default.execution.timeout.enabled=false
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=60000
#超时时间
feign.hystrix.enabled=false
ribbon.ReadTimeout=30000
ribbon.ConnectTimeout=30000
ribbon.SocketTimeout=30000
ribbon.MaxAutoRetries=0
ribbon.MaxAutoRetriesNextServer=0

## tx-manager ����
tx-lcn.client.manager-address=*************:8070
tx-lcn.ribbon.loadbalancer.dtx.enabled=true
logging.level.com.codingapi.txlcn=DEBUG
tx-lcn.logger.enabled=true
tx-lcn.logger.driver-class-name=com.mysql.jdbc.Driver
tx-lcn.logger.jdbc-url=***************************************************************
tx-lcn.logger.username=root
tx-lcn.logger.password=hebei2018root@LZ

# 5G???
contract5g.APP_KEY= a00ee92959764ddc8e62c9cb55bee948


#mongodb
# ��¼�û����ڵ����ݿ�
spring.data.mongodb.authentication-database=admin
# ���ݿ��ip��ַ
spring.data.mongodb.host=*************
# MongoDB�˿ں�
spring.data.mongodb.port=27017
# �û��˺�
spring.data.mongodb.username=root
# �û�����
spring.data.mongodb.password=hebeiLz2019root@.COM
# ָ��ʹ�õ����ݿ� ����Ԥ�ȴ����������ڸ����ݿ���Զ�����
spring.data.mongodb.database=db_database
logging.level.org.springframework.data.mongodb.core.MongoTemplate=DEBUG