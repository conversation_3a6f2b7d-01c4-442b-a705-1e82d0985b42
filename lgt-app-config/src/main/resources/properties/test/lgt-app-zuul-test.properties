server.port=1101
security.basic.enabled=false
#zuul.addHostHeader=true
zuul.sensitiveHeaders="*"

zuul.routes.a.path=/lgt-app-system/**
zuul.routes.a.serviceId=lgt-app-system
#zuul.routes.a.stripPrefix=false
zuul.routes.b.path=/lgt-app-schedule/**
zuul.routes.b.serviceId=lgt-app-schedule
zuul.routes.c.path=/lgt-app-message/**
zuul.routes.c.serviceId=lgt-app-message
zuul.routes.d.path=/lgt-app-workflow/**
zuul.routes.d.serviceId=lgt-app-workflow
zuul.routes.code.path=/lgt-app-code/**
zuul.routes.code.serviceId=lgt-app-code
zuul.routes.member.path=/lgt-app-member/**
zuul.routes.member.serviceId=lgt-app-member
zuul.routes.fastdfs.path=/fastdfs/**
zuul.routes.fastdfs.serviceId=lgt-app-fastdfs
zuul.routes.tool.path=/lgt-app-tool/**
zuul.routes.tool.serviceId=lgt-app-tool
zuul.routes.order.path=/lgt-app-order/**
zuul.routes.order.serviceId=lgt-app-order
zuul.routes.payment.path=/lgt-app-payment/**
zuul.routes.payment.serviceId=lgt-app-payment
zuul.routes.finance.path=/lgt-app-finance/**
zuul.routes.finance.serviceId=lgt-app-finance
zuul.routes.big.path=/lgt-app-big/**
zuul.routes.big.serviceId=lgt-app-big
zuul.routes.oss.path=/lgt-app-oss/**
zuul.routes.oss.serviceId=lgt-app-oss

#zuul.routes.b.stripPrefix=false
#zuul.routes.a.url=http://localhost:2001/lgt-app
#zuul.routes.tx-manager.path=/tx/**
#zuul.routes.tx-manager.serviceId=tx-manager
zuul.routes.goods.path=/lgt-app-resource/**
zuul.routes.goods.serviceId=lgt-app-resource
spring.zipkin.base-url=http://localhost:9411

spring.rabbitmq.host=************
spring.rabbitmq.port=5672
spring.rabbitmq.username=root
spring.rabbitmq.password=hebei2018root@.COM


spring.sleuth.sampler.percentage=1.0

ribbon.MaxAutoRetriesNextServer=0
#zuul.max.host.connections=500
#zuul.host.socket-timeout-millis=60000
#zuul.host.connect-timeout-millis=60000
zuul.semaphore.max-semaphores=5000
hystrix.command.default.execution.isolation.strategy =SEMAPHORE
hystrix.command.default.execution.timeout.enabled=false
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=60000
#超时时间
ribbon.eureka.enabled=true
feign.hystrix.enabled=false
ribbon.ReadTimeout=60000
ribbon.ConnectTimeout=60000
ribbon.SocketTimeout=60000
ribbon.MaxAutoRetries=1

