server.context-path=/lgt-app-system
spring.datasource.url=**************************************************************************************
spring.datasource.username=root
spring.datasource.password=hebei2018root@.COM
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.druid.initial-Size=5
spring.datasource.druid.min-Idle=5
spring.datasource.druid.max-Active=200
spring.datasource.druid.max-Wait=60000
spring.datasource.druid.time-Between-Eviction-Runs-Millis=60000
spring.datasource.druid.min-Evictable-Idle-Time-Millis=300000
spring.datasource.druid.validation-Query=SELECT 1 FROM DUAL
spring.datasource.druid.test-While-Idle=true
spring.datasource.druid.test-On-Borrow=false
spring.datasource.druid.test-On-Return=false
spring.datasource.druid.filters=stat,wall,logback
# 通过connectProperties属性来打开mergeSql功能；慢SQL记录
spring.datasource.connectionProperties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
mybatis.mapper-locations=classpath:mapping/*/*.xml
configLocation=classpath:mybatis-config.xml

spring.redis.host=************
spring.redis.port=6309
spring.redis.password=
# ���ӳ������������ʹ�ø�ֵ��ʾû�����ƣ�
spring.redis.pool.max-active=5000
# ���ӳ���������ȴ�ʱ�䣨ʹ�ø�ֵ��ʾû�����ƣ�
spring.redis.pool.max-wait=-1
# ���ӳ��е�����������
spring.redis.pool.max-idle=500
# ���ӳ��е���С��������
spring.redis.pool.min-idle=200
# ���ӳ�ʱʱ�䣨���룩
spring.redis.timeout=10000000
redisson.address=redis://************:6309
redisson.password=

spring.zipkin.base-url=http://localhost:9411

server.port=2001

spring.rabbitmq.host=************
spring.rabbitmq.port=5672
spring.rabbitmq.username=root
spring.rabbitmq.password=hebei2018root@.COM

## ˢʱرհȫ֤
management.security.enabled=false

spring.sleuth.sampler.percentage=1.0

#hystrix.command.default.execution.timeout.enabled=false
#hystrix.command.default.execution.isolation.strategy=SEMAPHORE
hystrix.command.default.execution.isolation.strategy=SEMAPHORE

hystrix.command.default.execution.timeout.enabled=false
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=60000
#超时时间
feign.hystrix.enabled=false
ribbon.ReadTimeout=30000
ribbon.ConnectTimeout=30000
ribbon.SocketTimeout=30000
ribbon.MaxAutoRetries=1


#0 �ൺ 1���� 2����
smsType=2
#����
smsSvcUrl=http://smssh1.253.com/msg/send/json
cust_code=N6072213
smspassword=3OAV0G6piM5274

#�ൺ
#smsSvcUrl=http://*************:8081
userid=350610
apikey=dda90e09947a462da6783744c4192d90

#����
#cust_code=200227
#smspassword=OIFAKNZB6A
sp_code=8aaf07086ade4b2d016afc79ff0b184b
#smsSvcPort=8883



#�ʼ��ӿ�
#//163��������ַ
host=smtp.163.com
#�˺�
emailusername=<EMAIL>
#����
password=weibo825173
#�������˺�
from=<EMAIL>
#nick + from �������ķ�������Ϣ
nick=

## tx-manager 配置
tx-lcn.client.manager-address=*************:8070
tx-lcn.ribbon.loadbalancer.dtx.enabled=true
logging.level.com.codingapi.txlcn=DEBUG
tx-lcn.logger.enabled=true
tx-lcn.logger.driver-class-name=com.mysql.jdbc.Driver
tx-lcn.logger.jdbc-url=***********************************************************
tx-lcn.logger.username=root
tx-lcn.logger.password=hebei2018root@LZ
#aluser.accessKey=LTAIuvFGRmtnxSgr
#aluser.secretKey=EeGaCksqqMKNfIkVj8xppfUgCJWff8

