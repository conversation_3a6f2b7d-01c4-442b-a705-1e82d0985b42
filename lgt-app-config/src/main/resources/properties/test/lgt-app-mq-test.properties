spring.datasource.url=************************************************************************************
spring.datasource.username=root
spring.datasource.password=hebeiLz2019root@.COM
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.druid.initial-Size=5
spring.datasource.druid.min-Idle=5
spring.datasource.druid.max-Active=200
spring.datasource.druid.max-Wait=60000
spring.datasource.druid.time-Between-Eviction-Runs-Millis=60000
spring.datasource.druid.min-Evictable-Idle-Time-Millis=300000
spring.datasource.druid.validation-Query=SELECT 1 FROM DUAL
spring.datasource.druid.test-While-Idle=true
spring.datasource.druid.test-On-Borrow=false
spring.datasource.druid.test-On-Return=false
spring.datasource.druid.filters=stat,wall,logback

mybatis.mapper-locations=classpath:mapping/*.xml
configLocation=classpath:mybatis-config.xml
mybatis.configuration.map-underscore-to-camel-case= true
mybatis.configuration.use-generated-keys=true
spring.redis.host=*************
spring.redis.port=6319
spring.redis.password=hebei2018root@.COM
# ���ӳ������������ʹ�ø�ֵ��ʾû�����ƣ�
spring.redis.pool.max-active=200
# ���ӳ���������ȴ�ʱ�䣨ʹ�ø�ֵ��ʾû�����ƣ�
spring.redis.pool.max-wait=-1
# ���ӳ��е�����������
spring.redis.pool.max-idle=8
# ���ӳ��е���С��������
spring.redis.pool.min-idle=0
# ���ӳ�ʱʱ�䣨���룩
spring.redis.timeout=0
#�ֲ�ʽ��
redisson.address=redis://*************:6319
redisson.password=hebei2018root@.COM

spring.zipkin.base-url=http://localhost:9411



server.port=2021
security.basic.enabled=false
spring.rabbitmq.host=*************
spring.rabbitmq.port=5673
spring.rabbitmq.username=admin
spring.rabbitmq.password=admin
spring.rabbitmq.publisher-confirms=true
spring.rabbitmq.publisher-returns=true


management.security.enabled=false

spring.sleuth.sampler.percentage=1.0

hystrix.command.default.execution.isolation.strategy=SEMAPHORE

hystrix.command.default.execution.timeout.enabled=false
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=60000
#超时时间
feign.hystrix.enabled=false
ribbon.eureka.enabled=true
ribbon.ReadTimeout=30000
ribbon.ConnectTimeout=30000
ribbon.SocketTimeout=30000
ribbon.MaxAutoRetries=1
ribbon.MaxAutoRetriesNextServer=0

## tx-manager ����
tx-lcn.client.manager-address=127.0.0.1:8070
tx-lcn.ribbon.loadbalancer.dtx.enabled=true
logging.level.com.codingapi.txlcn=DEBUG
tx-lcn.logger.enabled=true
tx-lcn.logger.driver-class-name=com.mysql.jdbc.Driver
tx-lcn.logger.jdbc-url=***********************************************************************
tx-lcn.logger.username=root
tx-lcn.logger.password=hebeiLz2019root@.COM

jd.partnerId=71D36F3AF57EF6861E3CA269F0686F2E
jd.merchantCode=M1011252043000000000000000000001