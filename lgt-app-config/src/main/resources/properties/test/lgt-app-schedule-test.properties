spring.datasource.url=**************************************************************************************
spring.datasource.username=root
spring.datasource.password=hebei2018root@LZ
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.druid.initial-Size=5
spring.datasource.druid.min-Idle=5
spring.datasource.druid.max-Active=200
spring.datasource.druid.max-Wait=60000
spring.datasource.druid.time-Between-Eviction-Runs-Millis=60000
spring.datasource.druid.min-Evictable-Idle-Time-Millis=300000
spring.datasource.druid.validation-Query=SELECT 1 FROM DUAL
spring.datasource.druid.test-While-Idle=true
spring.datasource.druid.test-On-Borrow=false
spring.datasource.druid.test-On-Return=false
spring.datasource.druid.filters=stat,wall,logback

mybatis.mapper-locations=classpath:mapping/*.xml
configLocation=classpath:mybatis-config.xml
mybatis.configuration.map-underscore-to-camel-case= true

spring.redis.host=localhost
spring.redis.port=6309
spring.redis.password=
# ���ӳ������������ʹ�ø�ֵ��ʾû�����ƣ�
spring.redis.pool.max-active=5000
# ���ӳ���������ȴ�ʱ�䣨ʹ�ø�ֵ��ʾû�����ƣ�
spring.redis.pool.max-wait=-1
# ���ӳ��е�����������
spring.redis.pool.max-idle=500
# ���ӳ��е���С��������
spring.redis.pool.min-idle=200
# ���ӳ�ʱʱ�䣨���룩
spring.redis.timeout=10000
redisson.address=redis://**************:6309
redisson.password=

spring.zipkin.base-url=http://localhost:9411

server.port=2002
security.basic.enabled=false
spring.rabbitmq.host=localhost
spring.rabbitmq.port=5672
spring.rabbitmq.username=root
spring.rabbitmq.password=hebei2018root@.COM
spring.rabbitmq.publisher-confirms=true
spring.rabbitmq.publisher-returns=true

## ˢ��ʱ���رհ�ȫ��֤
management.security.enabled=false

spring.sleuth.sampler.percentage=1.0

#hystrix.command.default.execution.timeout.enabled=false
#hystrix.command.default.execution.isolation.strategy=SEMAPHORE
hystrix.command.default.execution.isolation.strategy=SEMAPHORE

hystrix.command.default.execution.timeout.enabled=false
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=60000
#��ʱʱ��
feign.hystrix.enabled=false
ribbon.ReadTimeout=30000
ribbon.ConnectTimeout=30000
ribbon.SocketTimeout=30000
ribbon.MaxAutoRetries=0
ribbon.MaxAutoRetriesNextServer=0
## tx-manager 配置
tx-lcn.client.manager-address=*************:8070
tx-lcn.ribbon.loadbalancer.dtx.enabled=true
logging.level.com.codingapi.txlcn=DEBUG
tx-lcn.logger.enabled=true
tx-lcn.logger.driver-class-name=com.mysql.jdbc.Driver
tx-lcn.logger.jdbc-url=***********************************************************
tx-lcn.logger.username=root
tx-lcn.logger.password=hebei2018root@LZ
