server.port=9411

spring.rabbitmq.host=************
spring.rabbitmq.port=5672
spring.rabbitmq.username=root
spring.rabbitmq.password=hebei2018root@.COM
spring.sleuth.sampler.percentage=1.0

spring.datasource.url=******************************************************************************
spring.datasource.username=root
spring.datasource.password=hebei2018root@.COM
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.druid.initial-Size=5
spring.datasource.druid.min-Idle=5
spring.datasource.druid.max-Active=200
spring.datasource.druid.max-Wait=60000
spring.datasource.druid.time-Between-Eviction-Runs-Millis=60000
spring.datasource.druid.min-Evictable-Idle-Time-Millis=300000
spring.datasource.druid.validation-Query=SELECT 1 FROM DUAL
spring.datasource.druid.test-While-Idle=true
spring.datasource.druid.test-On-Borrow=false
spring.datasource.druid.test-On-Return=false
spring.datasource.druid.filters=stat,wall,logback

spring.redis.host=************
spring.redis.port=6309
spring.redis.password=hebei2018root@.COM

zipkin.storage.type=mysql
