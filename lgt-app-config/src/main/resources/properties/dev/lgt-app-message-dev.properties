spring.datasource.url=***************************************************************************************
spring.datasource.username=root
spring.datasource.password=hebeiLz2019root@.COM
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.druid.initial-Size=5
spring.datasource.druid.min-Idle=5
spring.datasource.druid.max-Active=200
spring.datasource.druid.max-Wait=60000
spring.datasource.druid.time-Between-Eviction-Runs-Millis=60000
spring.datasource.druid.min-Evictable-Idle-Time-Millis=300000
spring.datasource.druid.validation-Query=SELECT 1 FROM DUAL
spring.datasource.druid.test-While-Idle=true
spring.datasource.druid.test-On-Borrow=false
spring.datasource.druid.test-On-Return=false
spring.datasource.druid.filters=stat,wall,logback

mybatis.mapper-locations=classpath:mapping/*/*.xml
configLocation=classpath:mybatis-config.xml

spring.redis.host=*************
spring.redis.port=6379
spring.redis.password=hebei2018root@.COM


spring.zipkin.base-url=http://localhost:9411

server.port=2015

spring.rabbitmq.host=*************
spring.rabbitmq.port=5673
spring.rabbitmq.username=admin
spring.rabbitmq.password=admin

## ??????
management.security.enabled=false

spring.sleuth.sampler.percentage=1.0