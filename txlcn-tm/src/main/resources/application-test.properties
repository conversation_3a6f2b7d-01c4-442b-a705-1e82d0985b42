##################
# ���������������������ļ���������application-xxx.properties �ǿ����ߵĸ��Ի����ã����ù��ġ�
# ������� https://txlcn.org/zh-cn/docs/setting/manager.html �������еĸ��Ի�����
#################

server.port=7970
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.url=*****************************************************************************************************
spring.datasource.username=root
spring.datasource.password=hebeiLz2019root@.COM
spring.jpa.database-platform=org.hibernate.dialect.MySQL5InnoDBDialect
spring.jpa.hibernate.ddl-auto=update

mybatis.configuration.map-underscore-to-camel-case=true
mybatis.configuration.use-generated-keys=true

spring.redis.host=************
spring.redis.port=6379
spring.redis.password=hebei2018root@.COM

tx-lcn.message.netty.attr-delay-time=10000
tx-lcn.manager.concurrent-level=160
tx-lcn.manager.dtx-time=36000

logging.level.com.codingapi.txlcn=DEBUG
tx-lcn.logger.enabled=true
tx-lcn.logger.driver-class-name=com.mysql.jdbc.Driver
tx-lcn.logger.jdbc-url=***********************************************************************
tx-lcn.logger.username=root
tx-lcn.logger.password=hebeiLz2019root@.COM

tx-lcn.manager.admin-key=123456
tx-lcn.manager.host=0.0.0.0
tx-lcn.manager.ex-url-enabled=true

## �����쳣֪ͨ���κ�httpЭ���ַ��δָ��Э��ʱ��ΪTM�ṩ���ù��ܽӿڣ���Ĭ�����ʼ�֪ͨ
tx-lcn.manager.ex-url=/provider/email-to/<EMAIL>
#spring.mail.host=smtp.qq.com
#spring.mail.port=587
#spring.mail.username=<EMAIL>
#spring.mail.password=rdhlcbrycvjjbfga