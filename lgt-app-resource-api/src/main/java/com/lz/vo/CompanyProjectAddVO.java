package com.lz.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2019/4/18 - 11:54
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CompanyProjectAddVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer projectId;

    //承运方表ID
    private Integer carrierId;

    //企业表/C端用户表ID
    private Integer companyId;

    private String projectName;

    private String projectCode;

    /**
     * 项目特点
     */
    private String projectFeature;

    /**
     * 调度费系数
     */
    private BigDecimal dispatchFeeCoefficient;

    /**
     * 项目上线合同起始日期
     */
    private Date projectOnlineContractBeginningDate;

    /**
     * 项目上线合同结束日期
     */
    private Date projectOnlineContractEndDate;

    /**
     * 项目对接市场专员
     */
    private String projectJoinMarketingPerson;

    /**
     * 项目对接实施专员
     */
    private String projectJoinImplementPerson;

    /**
     * 合同照片1
     */
    private String contractPhoto1;

    /**
     * 合同照片2
     */
    private String contractPhoto2;

    /**
     * 其他内容
     */
    private String otherContent;

    /**
     * 支付方式
     */
    private String payMethod;

    /**
     * 授信额度
     */
    private BigDecimal creditLine;

    /**
     * 使用后剩余额度
     */
    private Double afterUseLeftLimit;

    /**
     * 开启日期
     */
    private Date startTime;

    /**
     * 授信天数
     */
    private Integer creditDays;

    /**
     * （支付）还款期限
     */
    private Integer payDeadline;

    /**
     * 备注
     */
    private String remark;
    /**
     * 项目上线方案
     */
    private String projectOnlineProgram;


    /** 企业开票信息id */
    private Integer projectInvoiceInfoId;

    //企业名称
    private String companyName;

    //开户行
    private String accountBankName;
    //单位地址
    private String unitAddress;
    //纳税（人识别）号
    private String taxpayerNumber;
    //对公账号
    private String publicAccountNumber;
    //开户户名
    private String accountUserName;

    /**  开票信息确认 */
    private String invoiceDrawConfrim;
    private Integer invoiceDrawConfrimId;

    //财务联系人
    private String financeManager;
    //联系电话
    private String phone;
    //联系邮箱
    private String email;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /** 启用标志 */
    private Boolean stopFlag;

    /** 提现方式 */
    private String withdrawType;
    /** 资金转移方式 */
    private String capitalTransferType;
    /** 线路类型 */
    private String lineType;

    /** 企业营业执照 */
    private String businessLicenseNo;

    /** 支付时检验车辆司机审核状态是否通过 **/ //默认是0 0提现时  1支付时
    private Boolean checkUserCarStatus;

    /**
     * 1:是/0:否；由企业或运营配置是否需要业务部辅助企业及运输人进行相关操作。
     */
    private Boolean businessAssist;

    /**
     * 枚举值：经纪人模式、普通模式
     */
    private String capitalTransferPattern;

    /**
     * 是否可修改经纪人服务费
     */
    private Boolean updateServiceFee;

    /**
     * 运输方身份认证校验：指运输方（司机、车主、车辆）进行资金变动时，身份校验的节点——支付入账时、提现到卡时。默认继承自项目管理上的该字段信息，在具体货源上可以调整，以最后货源调整后的值为生效值
     */
    private Boolean transportIdentityCheck;

    /**
     * 自动签合同时间 分钟
     */
    private Integer signContractTime;

    /*
     * <AUTHOR>
     * @Description是否查询轨迹
     * @Date 2020/3/30 2:13 下午
     **/
    private Boolean queryTrajectory;

    private String registPhone;

    /* 是否给司机展示运费明细和货运单价*/
    private Boolean freightPrice;

    //项目二维码是否开启
    private Boolean openProjectQrcode;

    //项目二维码--司机微信端项目二维码
    private String projectQrcodeWeixin;

    //项目二维码--司机端APP项目二维码
    private String projectQrcodeApp;

    //是否限额
    private Boolean ifQuota;

    //第三方调用标识
    private String identification;


}
