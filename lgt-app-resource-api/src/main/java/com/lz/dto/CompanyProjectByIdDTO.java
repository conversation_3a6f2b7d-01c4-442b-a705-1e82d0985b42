package com.lz.dto;

import com.lz.model.TCompanyProject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2019/4/19 - 15:49
 **/
@Data
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CompanyProjectByIdDTO extends TCompanyProject implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer projectId;
    private Integer companyId;
    private String companyName;
    private Integer carrierId;
    private String carrierName;

    private Integer line;

    /** 支付类型字典表id */
    private Integer itemId;
    /** 支付类型 */
    private String payMethodName;
    /** 线路类型 */
    private String lineTypeName;
    /** 提现方式 */
    private String withdrawTypeName;
    /** 资金转移方式 */
    private String capitalTransferTtypeName;

    private Integer carrierCompanyId;

    private String dispatchFeeCoefficient;

    private String registPhone;

    private String projectQrcodeWeixin;

    private String projectQrcodeApp;

    //是否限额
    private Boolean ifQuota;

    public CompanyProjectByIdDTO() {

    }
}
