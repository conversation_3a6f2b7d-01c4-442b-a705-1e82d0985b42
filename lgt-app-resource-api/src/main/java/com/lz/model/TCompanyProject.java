package com.lz.model;

import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * t_company_project
 * <AUTHOR>
@ToString
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TCompanyProject implements Serializable {
    /**
     * 表主键ID
     */
    private Integer id;

    /**
     * 企业信息ID
     */
    private Integer companyId;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目特点
     */
    private String projectFeature;

    /**
     * 项目上线合同起始日期
     */
    private Date projectOnlineContractBeginningDate;

    /**
     * 项目上线合同结束日期
     */
    private Date projectOnlineContractEndDate;

    /**
     * 项目上线方案
     */
    private String projectOnlineProgram;

    /**
     * 项目对接市场专员
     */
    private String projectJoinMarketingPerson;

    /**
     * 项目对接实施专员
     */
    private String projectJoinImplementPerson;

    /**
     * 项目停止标记 默认值0，表示启用。1表示已停止。
     */
    private Boolean stopFlag;

    /**
     * 线路类型  枚举值：短途运输，长途运输，来回运输，接驳运输；编入CODE表；取code值；M22
     */
    private String lineType;

    /**
     * 资金转移方式 枚举值：1.直接支付到实际运输人（司机）；2.支付到车辆所有人（车老板）；3.支付给经纪人，再由经纪人支付给实际运输人（司机）；4.支付给经纪人，再由经纪人支付给车辆所有人（车老板）
     */
    private String capitalTransferType;

    /**
     * 提现方式 自主提现，自动到卡；
     */
    private String withdrawType;

    /**
     * 合同照片2
     */
    private String contractPhoto2;

    /**
     * 合同照片1
     */
    private String contractPhoto1;
    /**
     * 其他内容
     */
    private String otherContent;

    /**
     * 支付方式
     */
    private String payMethod;

    /**
     * 授信额度
     */
    private BigDecimal creditLine;

    /**
     * creditLine
     * 使用后剩余额度
     */
    private BigDecimal afterUseLeftLimit;

    /**
     * 开启日期
     */
    private Date startTime;

    /**
     * 授信天数
     */
    private Integer creditDays;

    /**
     * （支付）还款期限
     */
    private Integer payDeadline;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核流程ID
     */
    private Integer workflowId;

    /**
     * 审核状态
     */
    private String auditStatus;

    /**
     * 审核意见
     */
    private String auditOpinion;

    /**
     * 审核状态对应时间
     */
    private Date auditTime;

    /**
     * 扩展字段1
     */
    private String param1;

    /**
     * 扩展字段2
     */
    private String param2;

    /**
     * 扩展字段3
     */
    private String param3;

    /**
     * 扩展字段4
     */
    private String param4;

    /**
     * 操作方式
     */
    private String operateMethod;

    /**
     * 操作IP地址
     */
    private String operatorIp;

    /**
     * 支付流程
     */
    private Integer paymentProcess;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 : 1 true 已删除   ||||   0 false 未删除
     */
    private Boolean enable;

    private static final long serialVersionUID = 1L;

    /**
     * 支付时检验车辆司机审核状态是否通过
     */ //默认是0 0提现时  1支付时
    private Boolean checkUserCarStatus;

    /**
     * 运输方身份认证校验：指运输方（司机、车主、车辆）进行资金变动时，身份校验的节点——支付入账时、提现到卡时。默认继承自项目管理上的该字段信息，在具体货源上可以调整，以最后货源调整后的值为生效值
     */
    private Boolean transportIdentityCheck;

    /**
     * 自动签合同时间 分钟
     */
    private Integer signContractTime;

    /**
     * 1:是/0:否；由企业或运营配置是否需要业务部辅助企业及运输人进行相关操作。
     */
    private Boolean businessAssist;

    /**
     * 枚举值：经纪人模式、普通模式
     */
    private String capitalTransferPattern;

    /**
     * 是否可修改经纪人服务费
     */
    private Boolean updateServiceFee;

    /*
     * <AUTHOR>
     * @Description是否查询轨迹
     * @Date 2020/3/30 2:13 下午
    **/
    private Boolean queryTrajectory;

    /* 是否给司机展示运费明细和货运单价*/
    private Boolean freightPrice;

    //项目二维码是否开启
    private Boolean openProjectQrcode;

    //项目二维码--司机微信端项目二维码
    private String projectQrcodeWeixin;

    //项目二维码--司机端APP项目二维码
    private String projectQrcodeApp;

    //是否限额
    private Boolean ifQuota;
}