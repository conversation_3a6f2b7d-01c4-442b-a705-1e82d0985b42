package com.lz.model;

import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * t_line_goods_carriage_pay
 * <AUTHOR>
@Accessors(chain = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ToString
public class TLineGoodsCarriagePay implements Serializable {
    /**
     * 表主键ID
     */
    private Integer id;

    /**
     * 线路货物关系ID
     */
    private Integer lineGoodsRelId;

    /**
     * 运费支付方式
     */
    private String carriagePayType;

    /**
     * 值1
     */
    private BigDecimal value1;

    /**
     * 值2
     */
    private BigDecimal value2;

    /**
     * 值3
     */
    private BigDecimal value3;

    /**
     * 值4
     */
    private BigDecimal value4;

    /**
     * 值5
     */
    private BigDecimal value5;

    /**
     * 信息发布人ID
     */
    private Integer informationPublisherId;

    /**
     * 线路其他人员是否可见
     */
    private Boolean lineOtherUserEnable;

    /**
     * 是否生效
     */
    private Boolean ifEfficient;

    /**
     * 生效时间
     */
    private Date efficientTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 扩展字段1
     */
    private String param1;

    /**
     * 扩展字段2
     */
    private String param2;

    /**
     * 扩展字段3
     */
    private String param3;

    /**
     * 扩展字段4
     */
    private String param4;

    /**
     * 操作方式
     */
    private Integer operateMethod;

    /**
     * 操作IP地址
     */
    private String operatorIp;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Boolean enable;

    private static final long serialVersionUID = 1L;

    }