package com.lz.schedule.vo.anhui;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class DriverSendDataAnHuiZTInfoVO {

    private String methodPath = "com.lz.zt.execute.DriverDataEscalationApplyExecute";

    private Integer endUserId;

    private String driverName;

    private String taskId;

    private String carrierName;

    /**
     * 身份证
     */
    private String drivingLicense;

    /**
     * 准驾车型
     */
    private String vehicleClass;

    /**
     * 驾驶证发证机关
     */
    private String issuingOrganizations;

    /**
     * 驾驶证有效期自 YYYYMMDD
     */
    private String validPeriodFrom;

    /**
     * 驾驶证有效期至
     */
    private String validPeriodTo;

    /**
     * 从业资格证号
     */
    private String qualificationCertificate;

    /**
     * 电话
     */
    private String telephone;


    private String remark;
}
