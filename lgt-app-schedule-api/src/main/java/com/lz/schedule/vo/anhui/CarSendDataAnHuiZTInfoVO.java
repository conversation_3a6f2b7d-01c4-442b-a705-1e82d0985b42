package com.lz.schedule.vo.anhui;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CarSendDataAnHuiZTInfoVO {

    private String methodPath = "com.lz.zt.execute.VehicleDataEscalationApplyExecute";

    private Integer vehicleId;

    private String taskId;

    private String carrierName;

    /**
     * 车辆牌照号
     */
    private String vehicleNumber;

    /**
     * 车牌颜色代码
     */
    private String vehiclePlateColorCode;
    /**
     * 车辆类型代码
     */
    private String vehicleType;
    /**
     * 所有人
     */
    private String owner;

    /**
     * 使用性质
     */
    private String useCharacter;

    /**
     * 车辆识别代号
     */
    private String vIN;
    /**
     * 发证机关
     */
    private String issuingOrganizations;
    /**
     * 注册日期  YYYYMMDD
     */
    private String registerDate;
    /**
     * 发证日期   YYYYMMDD
     */
    private String issueDate;

    /**
     * 车辆能源类型
     */
    private String vehicleEnergyType;

    /**
     * 核定载质量
     */
    private BigDecimal vehicleTonnage;

    /**
     * 吨位
     */
    private BigDecimal grossMass;

    /**
     * 道路运输证号
     */
    private String roadTransportCertificateNumber;

    /**
     * 挂车牌照号
     */
    private String trailerVehiclePlateNumber;

    private String remark;
}
