package com.lz.schedule;

import com.lz.schedule.model.TTask;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/18 - 11:24
 * @description
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TTaskVO implements Serializable {

    private static final long serialVersionUID = 8752472508827377567L;

    private String taskId;

    /**
     * 任务类型
     */
    private String taskType;

    private List<String> taskTypeList;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 累计请求次数
     */
    private Integer requestTimes;

    private List<String> taskTypeNode;

    private String capitalTransferType;

    private List<String> capiList;

    private String operateState;

    private String sourceFieldvalue;

    private Integer limit;


    List<TTask> taskList;

}
