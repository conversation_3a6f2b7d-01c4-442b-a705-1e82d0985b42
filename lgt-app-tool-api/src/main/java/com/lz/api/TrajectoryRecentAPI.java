package com.lz.api;

import com.lz.common.util.ResultUtil;
import com.lz.model.trajectory.req.CheckQualificationV2Req;
import com.lz.model.trajectory.req.DrivingLicenseCheckReq;
import com.lz.model.trajectory.req.TrajectoryRouterPathReq;
import com.lz.model.trajectory.req.VehicleReq;
import com.lz.model.trajectory.resp.*;
import com.lz.model.trajectory.resp.recent.*;
import com.lz.util.FeignUploadFileSupportConfiguration;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * auth dingweibo
 * 中交兴路最新版
 */
@FeignClient(name="lgt-app-tool",configuration = FeignUploadFileSupportConfiguration.class)
public interface TrajectoryRecentAPI {

    /* *//**
     * 车辆轨迹查询（车牌号）接口 单辆车
     * 本接口提供指定车牌号，指定时间段查询车辆历史轨迹数据服务，开始时间和结束时间不能超过 24 小时。
     *@auth dingweibo
     * @param vclN
     * @return
     */

    @RequestMapping(value = "/trajectoryApi/transTimeManageV3One", method = RequestMethod.POST)
    TransTimeManageVResp transTimeManageV3One(@RequestParam("vclN") String vclN, @RequestParam("time") Integer time);
   /* *//**
     * 车辆轨迹查询（车牌号）接口
     * 本接口提供指定车牌号，指定时间段查询车辆历史轨迹数据服务，开始时间和结束时间不能超过 24 小时。
     *@auth dingweibo
     * @param vclNs
     * @return
     */
    @RequestMapping(value = "/trajectoryApi/transTimeManageV3", method = RequestMethod.POST)
    List<TransTimeManageVResp> transTimeManageV3(@RequestParam(value = "vclNs") String[] vclNs, @RequestParam(value = "time") Integer time);

    /**
     * 车辆入网验证接口
     * @auth dingweibo
     *  提供按车牌号判断指定车辆是否在全国货运平台入网服务
     * @param vclN 车牌号
     * @return
     */
    @RequestMapping(value = "/trajectoryApi/checkTruckExistV2", method = RequestMethod.POST)
    public ResultUtil checkTruckExistV2(@RequestParam(value = "vclN") String vclN);


    /**
     * 车辆行驶证信息查询
     * 本接口提供指定车牌号、车牌颜色的车辆行驶证信息查询
     *@auth dingweibo
     * @return
     */
    @RequestMapping(value = "/trajectoryApi/vQueryLicenseV2", method = RequestMethod.POST)
    public VQueryLicenseV2Resp vQueryLicenseV2(@RequestBody TrajectoryRouterPathReq req);


    /**
     * 道路运输证验证接口
     * 本接口通过指定车牌号、道路运输证号码，验证道路运输证信息是否准确。
     *@auth dingweibo
     * @return
     */
    @RequestMapping(value = "/trajectoryApi/checkRTCNoV2", method = RequestMethod.POST)
    public RtcNoResp checkRTCNoV2(@RequestBody TrajectoryRouterPathReq req);



    /**
     * 车辆轨迹查询（车牌号）接口
     * 本接口提供指定车牌号，指定时间段查询车辆历史轨迹数据服务，开始时间和结束时间不能超过 24 小时。
     *@auth dingweibo
     * @return
     */
    @RequestMapping(value = "/trajectoryApi/routerPath", method = RequestMethod.POST)
    public TrajectoryRecentResp routerPath(@RequestBody TrajectoryRouterPathReq req);

    /**
     * 身份证OCR识别
     * 身份证OCR识别
     *@auth
     * @param vehicleReq
     * @return
     */
    @RequestMapping(value = "/trajectoryApi/idCardLicenseV2", method = RequestMethod.POST)
    public IdCardLicenseV2Resp idCardLicenseV2(@RequestBody VehicleReq vehicleReq);

    /**
     * 驾驶证OCR识别
     * @param vehicleReq
     * @return
     */
    @RequestMapping(value = "/trajectoryApi/drivingLicenseOCRV2", method = RequestMethod.POST)
    DrivingLicenseOCRV2Resp drivingLicenseOCRV2(@RequestBody VehicleReq vehicleReq);

    /**
     * 道路运输许可证OCR识别
     * @param vehicleReq
     * @return
     */
    @RequestMapping(value = "/trajectoryApi/vehicleRoadLicenseOCR", method = RequestMethod.POST)
    VehicleRoadLicenseOcrResp vehicleRoadLicenseOCR(@RequestBody VehicleReq vehicleReq);

    /**
     * 车辆行驶证OCR识别
     * @param vehicleReq
     * @return
     */
    @RequestMapping(value = "/trajectoryApi/vehicleLicenseOCRV2", method = RequestMethod.POST)
    VehicleLicenseOCRV2Resp vehicleLicenseOCRV2(@RequestBody VehicleReq vehicleReq);


    /**
     *  @author: dingweibo
     *  @Date: 2023/5/19 15:23
     *  @Description: 驾驶证验证服务
     */
    @RequestMapping(value = "/trajectoryApi/drivingLicenseCheck", method = RequestMethod.POST)
    TrajectoryPluginUrlResp drivingLicenseCheck(@RequestBody DrivingLicenseCheckReq req);

    /**
     *  @author: dingweibo
     *  @Date: 2023/5/19 15:23
     *  @Description: 司机从业资格证核验服务 V2
     */
    @RequestMapping(value = "/trajectoryApi/checkQualificationV2", method = RequestMethod.POST)
    CheckQualificationV2Resp checkQualificationV2(@RequestBody CheckQualificationV2Req req);

}