package com.lz.model.contract.req;

import lombok.Data;

import java.io.Serializable;

/**
 *  @author: dingweibo
 *  @Date: 2020/3/12 16:39
 *  @Description: 司机、经纪人收款凭证请求参数
 */
@Data
public class ContractVoucherReq implements Serializable {
    //凭证编号
    private String voucherNo;
    //司机姓名
    private String driverName;
    //身份证号
    private String idCardNum;
    //支出项
    private String applyType;
    //金额
    private String money;
    //付款人
    private String payUser;
   // 收款人
    private String recUser;
    //收款账号
    private String recCardNO;
    //备注
    private String remark;
    //经纪人姓名
    private String agentName;

    //收款凭证类型
    private String VoucherType;


}
