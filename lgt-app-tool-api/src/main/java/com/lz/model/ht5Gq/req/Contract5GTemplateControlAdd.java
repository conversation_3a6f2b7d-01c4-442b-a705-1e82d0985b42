package com.lz.model.ht5Gq.req;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class Contract5GTemplateControlAdd implements Serializable {

    private static final long serialVersionUID = -4094589607206696632L;

    private String userId;

    /**
     * 模板id
     */
    @NotNull(message = "模板id不能为空")
    private Integer templateId;

    /**
     * 模板编号
     */
    @NotBlank(message = "模板编号不能为空")
    private String templateNumber;

    /**
     * 模板文件编号
     */
    @NotBlank(message = "模板文件编号不能为空")
    private String templateUploadFileNumber;

    /**
    * 控件名
    */
    @NotBlank(message = "控件名不能为空")
    private String controlName;

    /**
    * 填充字段
    */
    @NotBlank(message = "填充字段不能为空")
    private String controlField;

    /**
    * 控件类型
     * 1-单行文本
     * 2-数字
     * 3-日期
     * 4-签署区域
     * 5-多行文本
     * 6-图片，不支持编辑
    */
    @NotNull(message = "控件类型不能为空")
    private Integer controlType;

    // 控件大小
    @NotNull(message = "控件高度不能为空")
    private Float controlHeight;

    @NotNull(message = "控件宽度不能为空")
    private Float controlWidth;

    @NotNull(message = "控件X坐标不能为空")
    private Float coordinateX;

    @NotNull(message = "控件Y坐标不能为空")
    private Float coordinateY;

    // 控件页码
    @NotNull(message = "控件页码不能为空")
    private Integer controlPage;

    /**
     * 是否必填
     */
    private Boolean required;

}
