eureka.client.serviceUrl.defaultZone=*************************************************/eureka/
spring.cloud.config.discovery.serviceId=lgt-app-config
spring.cloud.config.discovery.enabled=true
spring.cloud.config.profile=test
spring.cloud.config.label=master

logging.level.com.lz.dao=DEBUG

spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL5InnoDBDialect
spring.data.redis.repositories.enabled=false
spring.jpa.open-in-view=true
spring.jpa.hibernate.ddl-auto=update
spring.data.jpa.repositories.enabled=true

mybatis.configuration.map-underscore-to-camel-case=true

#�н���· ����
#user=c3db7e9b-f0e9-4863-a63a-7f10228dfd44
#password=uI72m8wJ26q4oN2EZs3WW9ef9o6rLt
#clientId=6b410449-5531-44b8-bb3d-30e92636e96b
#openUrl=https://testopen.95155.com/apis

#�н���·��ʽ
user=bd9756e4-1926-4c85-bd53-8e0ba4c0f5cd
password=466j581Pc657T3RAe0oJiP03643Zbk
clientId=7c92862c-049a-4d74-858e-1eca067451ff
openUrl=https://zhiyunopenapi.95155.com/apis



#������ ���Ի���
#������ǩ��key
#ecloudappKey=yyzd39d26j9k88pgzx
##�����°汾��
#ecloudversion=1.0
##������secret
#ecloudsecret=acacd82035e4ebb3a1f3778a153d65c3
##�����»�����ַ https://api.ecloudsign.com
#ecloudurl=https://testapi.ecloudsign.com
##�����º�ͬģ��ID
#templateNumber=6C8AB5203410C5BE
#PDFPath=
#IMGPath=


#������ ��ʽ����
#������ǩ��key
ecloudappKey=yyzzs67wiz475tc2rn76
#�����°汾��
ecloudversion=1.0
#������secret
ecloudsecret=0b0ea689719a74f641f2904187e89781
#�����»�����ַ https://api.ecloudsign.com
ecloudurl=https://api.ecloudsign.com
#�����º�ͬģ��ID
templateNumber=53F35C42DA8F3544
PDFPath=
IMGPath=