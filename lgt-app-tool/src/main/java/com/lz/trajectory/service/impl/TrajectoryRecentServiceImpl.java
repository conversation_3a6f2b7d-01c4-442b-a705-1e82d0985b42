package com.lz.trajectory.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.model.CodeEnum;
import com.lz.common.model.jdPayment.util.FileBase64Util;
import com.lz.common.util.DateUtils;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.SplitAryUtil;
import com.lz.common.util.ThrowableUtil;
import com.lz.dao.mongodb.TrajectoryMongoDao;
import com.lz.model.mongodb.TrajectoryMongo;
import com.lz.model.trajectory.req.*;
import com.lz.model.trajectory.resp.*;
import com.lz.model.trajectory.resp.recent.*;
import com.lz.system.api.SysParamAPI;
import com.lz.system.model.SysParam;
import com.lz.trajectory.service.TrajectoryRecentService;
import com.openapi.sdk.service.DataExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.*;

/**
 * auth dingweibo
 * 中交兴路
 */
@Service("trajectoryRecentService")
@Slf4j
public class TrajectoryRecentServiceImpl implements TrajectoryRecentService {

    @Autowired
    private SysParamAPI sysParamAPI;

    @Resource
    private TrajectoryMongoDao trajectoryMongoDao;

    /** 车辆最新位置查询（车牌号）接口
     * 本接口提供指定车牌号的车辆最新位置查询。
     * vclN 车牌号
     * time 时间小时
     * */
    public TransTimeManageVResp transTimeManageV3One(String vclN, Integer time) {
        TransTimeManageVResp transTimeManageVResp = new TransTimeManageVResp();
        SysParam param = sysParamAPI.getParamByKey("TRAJECTORY");
        JSONObject jsonParam = JSONObject.parseObject(param.getParamValue());
        String openUrlNew = jsonParam.get("openUrlNew").toString();
        try {

            Map<String, String> map = new HashMap();
            map.put("cid", jsonParam.get("clientId").toString());
            map.put("srt", jsonParam.get("str").toString());
            map.put("timeNearby", String.valueOf(time));
            map.put("vnos", vclN);
            log.info("运输节点服务单车辆接口请求参数：{}",map);
            String url = openUrlNew + "/transTimeManageV3";
            DataExchangeService des = new DataExchangeService(15000, 15000);
            // 通过 https 方式调用，此方法内部会使用私钥生成签名参数 sign,私钥不会发送
            String res = des.postHttps(url, map);
            log.info("运输节点服务单车辆接口返回参数:" + res);
            JSONObject jsonObject = JSONObject.parseObject(res);
            if(!"1001".equals(jsonObject.get("status").toString())){
                if("1006".equals(jsonObject.get("status").toString())){
                    transTimeManageVResp.setMsg("无结果");
                    transTimeManageVResp.setCode(DictEnum.ERROR.code);
                }else{
                    if(jsonObject.get("result")!=null&&!"".equals(jsonObject.get("result"))){
                        transTimeManageVResp.setMsg(jsonObject.get("result").toString());
                        transTimeManageVResp.setCode(DictEnum.SUCCESS.code);
                    }
                }
            }else{
                if(jsonObject.get("result")!=null&&!"".equals(jsonObject.get("result"))){
                    Object firstVcl =  JSONObject.parseObject(jsonObject.get("result").toString()).get("firstVcl");
                    if(firstVcl!=null && !"".equals(firstVcl)){
                        transTimeManageVResp = JSONObject.parseObject(firstVcl.toString(),TransTimeManageVResp.class);
                    }
                    transTimeManageVResp.setCode(DictEnum.SUCCESS.code);
                }
            }
            transTimeManageVResp.setStatus(jsonObject.get("status").toString());
        } catch (Exception e) {
            log.info("运输节点服务单车辆失败:" ,e);
            return null;
        }
        return transTimeManageVResp;
    }

    /**
     * 运输节点服务
     *
     * @param vclNs
     * @return
     */
    public List<TransTimeManageVResp> transTimeManageV3(String[] vclNs,Integer time) {
        List<TransTimeManageVResp> transTimeManageVRespList = new ArrayList<>();
        SysParam param = sysParamAPI.getParamByKey("TRAJECTORY");
        JSONObject jsonParam = JSONObject.parseObject(param.getParamValue());
        String openUrlNew = jsonParam.get("openUrlNew").toString();
        try {
            SplitAryUtil splitAryUtil = new SplitAryUtil();
            int splitSize = 100;//分割的块大小
            Object[] subAry = splitAryUtil.splitAry(vclNs, splitSize);//分割后的子块数组
            for(Object obj: subAry){//打印输出结果
                String[] aryItem = (String[]) obj;
                String  vsN="";
                for(int k=0;k<aryItem.length;k++){
                    if(k==aryItem.length-1){
                        vsN+=aryItem[k];
                    }else{
                        vsN+=aryItem[k]+",";
                    }
                }
                Map<String, String> map = new HashMap();
                map.put("cid", jsonParam.get("clientId").toString());
                map.put("srt", jsonParam.get("str").toString());
                map.put("vnos", vsN);
                map.put("timeNearby", String.valueOf(time));
                String url = openUrlNew+"/transTimeManageV3";
                DataExchangeService des = new DataExchangeService(15000, 15000);
                log.info("运输节点服务请求参数：{}",map);
                String res = des.postHttps(url, map);
                log.info("运输节点服务返回参数:"+ res);
                JSONObject jsonObject = JSONObject.parseObject(res);
                if("1001".equals(jsonObject.get("status").toString())){
                    if(null!=jsonObject.get("result") && !"".equals(jsonObject.get("result"))){
                        JSONObject jsonObject2 = JSONObject.parseObject(jsonObject.get("result").toString());
                        if(null!=jsonObject2.get("others") && !"".equals(jsonObject2.get("others"))){
                            transTimeManageVRespList =  JSONObject.parseArray(jsonObject2.get("others").toString(),TransTimeManageVResp.class);
                        }
                        if(null!=jsonObject2.get("firstVcl") && !"".equals(jsonObject2.get("firstVcl"))){
                            TransTimeManageVResp transTimeManageVResp = new TransTimeManageVResp();
                            Object firstVcl =  jsonObject2.get("firstVcl");
                            if(firstVcl!=null && !"".equals(firstVcl)){
                                transTimeManageVResp = JSONObject.parseObject(firstVcl.toString(),TransTimeManageVResp.class);
                                transTimeManageVRespList.add(transTimeManageVResp);
                            }
                        }
                    }
                }else if("1006".equals(jsonObject.get("status").toString())){
                    TransTimeManageVResp transTimeManageVResp = new TransTimeManageVResp();
                    transTimeManageVResp.setStatus("1006");
                    String[] split = map.get("vnos").split("_");
                    if(split.length > 0){
                        transTimeManageVResp.setVno(split[0]);
                    }
                    transTimeManageVRespList.add(transTimeManageVResp);
                }
            }
        } catch (Exception e) {
            log.info("多车最新位置查询失败:" ,e);
            return null;
        }
        return  transTimeManageVRespList;
    }


    /**
     *  @author: dingweibo
     *  @Date: 2023/2/16 16:47
     *  @Description: 运输行程服务(最新版 按时间段查询车辆轨迹))
     */
    @Override
    public TrajectoryRecentResp routerPath(TrajectoryRouterPathReq req) {
        TrajectoryRecentResp trajectoryRecentResp = new TrajectoryRecentResp();
        SysParam param = sysParamAPI.getParamByKey("TRAJECTORY");
        JSONObject jsonParam = JSONObject.parseObject(param.getParamValue());
        String openUrlNew = jsonParam.get("openUrlNew").toString();
        try {
            if(null==req.getVclN()||"".equals(req.getVclN())){
                trajectoryRecentResp.setMsg("车牌号不能为空");
                trajectoryRecentResp.setCode(CodeEnum.ERROR.getCode());
                return trajectoryRecentResp;
            }
            if(null==req.getQryBtm()||"".equals(req.getQryBtm())){
                trajectoryRecentResp.setMsg("开始时间不能为空");
                trajectoryRecentResp.setCode(CodeEnum.ERROR.getCode());
                return trajectoryRecentResp;
            }
            if(null==req.getQryEtm()||"".equals(req.getQryEtm())){
                trajectoryRecentResp.setMsg("结束时间不能为空");
                trajectoryRecentResp.setCode(CodeEnum.ERROR.getCode());
                return trajectoryRecentResp;
            }
            Map<String, String> map = new HashMap();
            map.put("cid", jsonParam.get("clientId").toString());
            map.put("srt", jsonParam.get("str").toString());
            map.put("vclN", req.getVclN());
            map.put("vco", req.getVco());
            map.put("qryBtm", req.getQryBtm());
            map.put("qryEtm", req.getQryEtm());
            log.info("运输行程服务请求参数：{}",map);
            String url = openUrlNew + "/routerPath";
            DataExchangeService des = new DataExchangeService(15000, 15000);
            // 通过 https 方式调用，此方法内部会使用私钥生成签名参数 sign,私钥不会发送
            String res = des.postHttps(url, map);
            log.info("运输行程服务返回参数:" + res);
            JSONObject jsonObject = JSONObject.parseObject(res);
            if(!"1001".equals(jsonObject.get("status").toString())){
                if("1006".equals(jsonObject.get("status").toString())){
                    trajectoryRecentResp.setMsg("无结果");
                    trajectoryRecentResp.setCode(DictEnum.ERROR.code);
                }else{
                    if(jsonObject.get("result")!=null&&!"".equals(jsonObject.get("result"))){
                        trajectoryRecentResp.setMsg(jsonObject.get("result").toString());
                        trajectoryRecentResp.setCode(DictEnum.SUCCESS.code);
                    }
                }
            }else{
                if(jsonObject.get("result")!=null&&!"".equals(jsonObject.get("result"))){
                    Object trackArray =  JSONObject.parseObject(jsonObject.get("result").toString()).get("trackArray");
                    if(trackArray!=null && !"".equals(trackArray)){
                        trajectoryRecentResp.setData(JSONObject.parseArray(trackArray.toString(), RouterPathResp.class));
                    }
                    trajectoryRecentResp.setCode(DictEnum.SUCCESS.code);
                }
            }
            trajectoryRecentResp.setStatus(jsonObject.get("status").toString());
        } catch (Exception e) {
            log.info("运输行程服务失败:" ,e);
            return null;
        }
        return  trajectoryRecentResp;
    }
    /**
     *  @author: dingweibo
     *  @Date: 2022/6/22 14:52
     *  @Description: 入网验证服务接口
     */
    @Override
    public ResultUtil checkTruckExistV2(@RequestParam(value = "vclN")String vclN) {
        SysParam param = sysParamAPI.getParamByKey("TRAJECTORY");
        JSONObject jsonParam = JSONObject.parseObject(param.getParamValue());
        String openUrlNew = jsonParam.get("openUrlNew").toString();
        ResultUtil resultUtil = new ResultUtil();
        try {
            if(vclN==null||"".equals(vclN)){
                resultUtil.setMsg("车牌号不能为空");
                resultUtil.setCode(CodeEnum.ERROR.getCode());
                return resultUtil;
            }
            Map<String, String> map = new HashMap();
            map.put("cid", jsonParam.get("clientId").toString());
            map.put("srt", jsonParam.get("str").toString());
            map.put("vclN", vclN);
            log.info("入网验证服务接口请求参数：{}",map);
            String url = openUrlNew + "/checkTruckExistV2";
            DataExchangeService des = new DataExchangeService(15000, 15000);
            // 通过 https 方式调用，此方法内部会使用私钥生成签名参数 sign,私钥不会发送
            String res = des.postHttps(url, map);
            log.info("入网验证服务接口返回参数:" + res);
            JSONObject jsonObject = JSONObject.parseObject(res);
            if(!"1001".equals(jsonObject.get("status").toString())){
                if("1006".equals(jsonObject.get("status").toString())){
                    resultUtil.setMsg("无结果");
                    resultUtil.setCode(DictEnum.ERROR.code);
                }else{
                    if(jsonObject.get("result")!=null&&!"".equals(jsonObject.get("result"))){
                        resultUtil.setMsg(jsonObject.get("result").toString());
                        resultUtil.setCode(DictEnum.SUCCESS.code);
                    }
                }
            }else{
                if(jsonObject.get("result")!=null&&!"".equals(jsonObject.get("result"))){
                    resultUtil.setMsg(jsonObject.get("result").toString());
                    resultUtil.setCode(DictEnum.SUCCESS.code);
                }
            }
            resultUtil.setData(jsonObject.get("status"));
        } catch (Exception e) {
            log.error("车辆入网验证失败:" ,e);
            return null;
        }
        return  resultUtil;
    }

    /**
     * 道路运输证验证接口
     * 本接口通过指定车牌号、道路运输证号码，验证道路运输证信息是否准确。
     *
     * @param req
     * @return
     */
    public RtcNoResp checkRTCNoV2(TrajectoryRouterPathReq req) {
        RtcNoResp resp = new RtcNoResp();
        SysParam param = sysParamAPI.getParamByKey("TRAJECTORY");
        JSONObject jsonParam = JSONObject.parseObject(param.getParamValue());
        String openUrlNew = jsonParam.get("openUrlNew").toString();
        try {

            if(null==req.getVclN()||"".equals(req.getVclN())){
                resp.setMsg("车牌号不能为空");
                resp.setCode(CodeEnum.ERROR.getCode());
                return resp;
            }
            if(null==req.getVehicleRoadLicense()||"".equals(req.getVehicleRoadLicense())){
                resp.setMsg("道路运输证号码");
                resp.setCode(CodeEnum.ERROR.getCode());
                return resp;
            }
            Map<String, String> map = new HashMap();
            map.put("cid", jsonParam.get("clientId").toString());
            map.put("srt", jsonParam.get("str").toString());
            map.put("vno", req.getVclN());
            map.put("vco",req.getVco());
            map.put("vehicleRoadLicense",req.getVehicleRoadLicense());
            log.info("道路运输证验证服务接口请求参数：{}",map);
            String url = openUrlNew + "/checkRTCNoV2";
            DataExchangeService des = new DataExchangeService(15000, 15000);
            // 通过 https 方式调用，此方法内部会使用私钥生成签名参数 sign,私钥不会发送
            String res = des.postHttps(url, map);
            log.info("道路运输证验证服务接口返回参数:" + res);
            JSONObject jsonObject = JSONObject.parseObject(res);
            if(!"1001".equals(jsonObject.get("status").toString())){
                if("1006".equals(jsonObject.get("status").toString())){
                    resp.setMsg("无结果");
                    resp.setCode(DictEnum.ERROR.code);
                }else{
                    if(jsonObject.get("result")!=null&&!"".equals(jsonObject.get("result"))){
                        resp.setMsg(jsonObject.get("result").toString());
                        resp.setCode(DictEnum.SUCCESS.code);
                    }
                }
            }else{
                if(jsonObject.get("result")!=null&&!"".equals(jsonObject.get("result"))){
                    resp = JSONObject.parseObject(jsonObject.get("result").toString(), RtcNoResp.class);
                    resp.setCode(DictEnum.SUCCESS.code);
                }
            }
            resp.setStatus(jsonObject.get("status").toString());
        } catch (Exception e) {
            log.info("道路运输证验证失败:" ,e);
            return null;
        }
        return  resp;
    }

    /**
     * 行驶证信息核验服务
     * 本接口提供指定车牌号、车牌颜色的车辆行驶证信息查询
     * @param req
     * @return
     */
    @Override
    public VQueryLicenseV2Resp vQueryLicenseV2(TrajectoryRouterPathReq req) {
        SysParam param = sysParamAPI.getParamByKey("TRAJECTORY");
        JSONObject jsonParam = JSONObject.parseObject(param.getParamValue());
        String openUrlNew = jsonParam.get("openUrlNew").toString();
        VQueryLicenseV2Resp resp = new VQueryLicenseV2Resp();
        try {
            if(null == req.getVclN()||"".equals(req.getVclN())){
                resp.setMsg("车牌号不能为空");
                resp.setCode(CodeEnum.ERROR.getCode());
                return resp;
            }
            Map<String, String> map = new HashMap();
            map.put("cid", jsonParam.get("clientId").toString());
            map.put("srt", jsonParam.get("str").toString());
            map.put("vclN", req.getVclN());
            map.put("vco",req.getVco());
            log.info("行驶证信息核验服务接口请求参数：{}",map);
            String url = openUrlNew + "/vQueryLicenseV2";
            DataExchangeService des = new DataExchangeService(15000, 15000);
            // 通过 https 方式调用，此方法内部会使用私钥生成签名参数 sign,私钥不会发送
            String res = des.postHttps(url, map);
            log.info("行驶证信息核验服务返回参数:" + res);
            JSONObject jsonObject = JSONObject.parseObject(res);
            if(!"1001".equals(jsonObject.get("status").toString())){
                if("1006".equals(jsonObject.get("status").toString())){
                    resp.setMsg("无结果");
                    resp.setCode(DictEnum.ERROR.code);
                }else{
                    resp.setMsg(jsonObject.get("result").toString());
                    resp.setCode(DictEnum.ERROR.code);
                }
            }else{
                if(jsonObject.get("result")!=null&&!"".equals(jsonObject.get("result"))){
                    resp = JSONObject.parseObject(jsonObject.get("result").toString(),VQueryLicenseV2Resp.class);
                    resp.setCode(DictEnum.SUCCESS.code);
                }
            }
            resp.setStatus(jsonObject.get("status").toString());
        } catch (Exception e) {
            log.info("行驶证信息核验服务失败:" ,e);
            return null;
        }
        return  resp;
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/3/23 11:16
     *  @Description: 行驶证OCR识别
     */
    @Override
    public VehicleLicenseOCRV2Resp vehicleLicenseOCRV2(VehicleReq vehicleReq) {
        SysParam param = sysParamAPI.getParamByKey("TRAJECTORY");
        JSONObject jsonParam = JSONObject.parseObject(param.getParamValue());
        String openUrlNew = jsonParam.get("openUrlNew").toString();
        VehicleLicenseOCRV2Resp resp = new VehicleLicenseOCRV2Resp();
        try {
            if((null!=vehicleReq.getFaceData() &&!"".equals(vehicleReq.getFaceData().trim()))
                    || (null!=vehicleReq.getBackData()&&!"".equals(vehicleReq.getBackData().trim()))){
                log.info("行驶证OCR识别请求参数:{}" + vehicleReq);
                Map<String, String> map = new HashMap();
                map.put("cid", jsonParam.get("clientId").toString());
                map.put("srt", jsonParam.get("str").toString());
                log.info("行驶证OCR识别请求参数：{}",map);
                SysParam sysParam = sysParamAPI.getParamByKey("DISPLAYIMAGEIP");

                if (null != vehicleReq.getFaceData() && !"".equals(vehicleReq.getFaceData().trim())) {
                    if(vehicleReq.getFaceData().indexOf("group")>=0){
                        map.put("faceData", "data:image/" + vehicleReq.getFaceData().split("\\.")[1] + ";base64," + FileBase64Util.getFileBase64(sysParam.getParamValue()+vehicleReq.getFaceData()));
                    }else{
                        map.put("faceData", "data:image/" + vehicleReq.getFaceData().split("\\.")[3] + ";base64," + FileBase64Util.getFileBase64(vehicleReq.getFaceData()));
                    }
                }
                if (null != vehicleReq.getBackData() && !"".equals(vehicleReq.getBackData().trim())) {
                    if(vehicleReq.getBackData().indexOf("group")>=0){
                        map.put("backData", "data:image/" + vehicleReq.getBackData().split("\\.")[1] + ";base64," + FileBase64Util.getFileBase64(sysParam.getParamValue()+vehicleReq.getBackData()));
                    }else{
                        map.put("backData", "data:image/" + vehicleReq.getBackData().split("\\.")[3] + ";base64," + FileBase64Util.getFileBase64(vehicleReq.getBackData()));
                    }
                }
                String url = openUrlNew + "/vehicleLicenseOCRV2";
                DataExchangeService des = new DataExchangeService(15000, 15000);
                // 通过 https 方式调用，此方法内部会使用私钥生成签名参数 sign,私钥不会发送
                String res = des.postHttps(url, map);
                log.info("行驶证OCR识别返回参数:" + res);
                JSONObject jsonObject = JSONObject.parseObject(res);
                if (!"1001".equals(jsonObject.get("status").toString())) {
                    if("1006".equals(jsonObject.get("status").toString())){
                        resp.setMsg("无结果");
                        resp.setCode(DictEnum.ERROR.code);
                    }else{
                        resp.setMsg(jsonObject.get("result").toString());
                        resp.setCode(DictEnum.ERROR.code);
                    }
                } else {
                    if (jsonObject.get("result") != null && !"".equals(jsonObject.get("result"))) {
                        try {
                            String result = jsonObject.getString("result");
                            JSONObject parseObject = JSONObject.parseObject(result);
                            if (null != parseObject.getString("regTime")) {
                                String regTime = DateUtils.tryParseTime(parseObject.getString("regTime"));
                                parseObject.put("regTime", regTime);
                            }
                            if (null != parseObject.getString("licensedateOfissue")) {
                                String licensedateOfissue = DateUtils.tryParseTime(parseObject.getString("licensedateOfissue"));
                                parseObject.put("licensedateOfissue", licensedateOfissue);
                            }
                            resp = JSONObject.parseObject(parseObject.toJSONString(), VehicleLicenseOCRV2Resp.class);
                        } catch (Exception e) {
                            log.error("解析失败, {}", ThrowableUtil.getStackTrace(e));
                            resp = JSONObject.parseObject(jsonObject.get("result").toString(), VehicleLicenseOCRV2Resp.class);
                        }
                        resp.setCode(DictEnum.SUCCESS.code);
                        if(null!=resp.getInspectionRecord() && !"".equals(resp.getInspectionRecord())){
                            String a = resp.getInspectionRecord();
                            String str = "";
                            if(a.indexOf(";")>0){
                                str = a.split(";")[a.split(";").length-1];
                                str = str.split("检验有效期至")[1];
                            }else{
                                str = a.split("检验有效期至")[1];
                            }
                            String str2 = str.split("月")[0];
                            str2 =  str2.replace("年","-");
                            str2 = str2.replace("月","");
                            Date date = DateUtils.parseDate(str2,"yyyy-MM");
                            resp.setCardrivingLicencesValidUntil(date);
                        }
                    }
                }
                resp.setStatus(jsonObject.get("status").toString());
            }else{
                resp.setMsg("请传入图片");
                resp.setCode(DictEnum.ERROR.code);
            }
        } catch (Exception e) {
            log.info("行驶证OCR识别失败:" ,e);
            return null;
        }
        return  resp;
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/3/23 11:17
     *  @Description: 驾驶证OCR识别
     */
    @Override
    public DrivingLicenseOCRV2Resp drivingLicenseOCRV2(VehicleReq vehicleReq) {
        SysParam param = sysParamAPI.getParamByKey("TRAJECTORY");
        JSONObject jsonParam = JSONObject.parseObject(param.getParamValue());
        String openUrlNew = jsonParam.get("openUrlNew").toString();
        DrivingLicenseOCRV2Resp resp = new DrivingLicenseOCRV2Resp();
        try {
            if((null!=vehicleReq.getFaceData() &&!"".equals(vehicleReq.getFaceData().trim()))
                    || (null!=vehicleReq.getBackData()&&!"".equals(vehicleReq.getBackData().trim()))){
                log.info("驾驶证OCR识别请求参数:{}" + vehicleReq);
                Map<String, String> map = new HashMap();
                map.put("cid", jsonParam.get("clientId").toString());
                map.put("srt", jsonParam.get("str").toString());
                log.info("驾驶证OCR识别请求参数：{}",map);
                SysParam sysParam = sysParamAPI.getParamByKey("DISPLAYIMAGEIP");

                if (null != vehicleReq.getFaceData() && !"".equals(vehicleReq.getFaceData().trim())) {
                    if(vehicleReq.getFaceData().indexOf("group")>=0){
                        map.put("faceData", "data:image/" + vehicleReq.getFaceData().split("\\.")[1] + ";base64," + FileBase64Util.getFileBase64(sysParam.getParamValue()+vehicleReq.getFaceData()));
                    }else{
                        map.put("faceData", "data:image/" + vehicleReq.getFaceData().split("\\.")[3] + ";base64," + FileBase64Util.getFileBase64(vehicleReq.getFaceData()));
                    }
                }
                if (null != vehicleReq.getBackData() && !"".equals(vehicleReq.getBackData().trim())) {
                    if(vehicleReq.getBackData().indexOf("group")>=0){
                        map.put("backData", "data:image/" + vehicleReq.getBackData().split("\\.")[1] + ";base64," + FileBase64Util.getFileBase64(sysParam.getParamValue()+vehicleReq.getBackData()));
                    }else{
                        map.put("backData", "data:image/" + vehicleReq.getBackData().split("\\.")[3] + ";base64," + FileBase64Util.getFileBase64(vehicleReq.getBackData()));
                    }
                }
                String url =openUrlNew + "/drivingLicenseOCRV2";
                DataExchangeService des = new DataExchangeService(15000, 15000);
                // 通过 https 方式调用，此方法内部会使用私钥生成签名参数 sign,私钥不会发送
                String res = des.postHttps(url, map);
                log.info("驾驶证OCR识别返回参数:" + res);
                JSONObject jsonObject = JSONObject.parseObject(res);
                if(!"1001".equals(jsonObject.get("status").toString())){
                    if("1006".equals(jsonObject.get("status").toString())){
                        resp.setMsg("无结果");
                        resp.setCode(DictEnum.ERROR.code);
                    }else{
                        resp.setMsg(jsonObject.get("result").toString());
                        resp.setCode(DictEnum.ERROR.code);
                    }
                }else{
                    if(jsonObject.get("result")!=null&&!"".equals(jsonObject.get("result"))){
                        resp = JSONObject.parseObject(jsonObject.get("result").toString(),DrivingLicenseOCRV2Resp.class);
                        resp.setCode(DictEnum.SUCCESS.code);
                    }
                }
                resp.setStatus(jsonObject.get("status").toString());
            }else{
                resp.setMsg("请传入图片");
                resp.setCode(DictEnum.ERROR.code);
            }
        } catch (Exception e) {
            log.info("驾驶证OCR识别失败:" ,e);
            return null;
        }
        return  resp;
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/3/23 11:17
     *  @Description: 身份证OCR识别
     */
    @Override
    public IdCardLicenseV2Resp idCardLicenseV2(VehicleReq vehicleReq) {

        SysParam param = sysParamAPI.getParamByKey("TRAJECTORY");
        JSONObject jsonParam = JSONObject.parseObject(param.getParamValue());
        String openUrlNew = jsonParam.get("openUrlNew").toString();
        IdCardLicenseV2Resp resp = new IdCardLicenseV2Resp();
        try {
            if((null!=vehicleReq.getFaceData() &&!"".equals(vehicleReq.getFaceData().trim()))
                    || (null!=vehicleReq.getBackData()&&!"".equals(vehicleReq.getBackData().trim()))){
                log.info("身份证OCR识别请求参数:{}" + vehicleReq);
                Map<String, String> map = new HashMap();
                map.put("cid", jsonParam.get("clientId").toString());
                map.put("srt", jsonParam.get("str").toString());
                log.info("身份证OCR识别请求参数：{}",map);
                SysParam sysParam = sysParamAPI.getParamByKey("DISPLAYIMAGEIP");

                if (null != vehicleReq.getFaceData() && !"".equals(vehicleReq.getFaceData().trim())) {
                    if(vehicleReq.getFaceData().indexOf("group")>=0){
                        map.put("faceData", "data:image/" + vehicleReq.getFaceData().split("\\.")[1] + ";base64," + FileBase64Util.getFileBase64(sysParam.getParamValue()+vehicleReq.getFaceData()));
                    }else{
                        map.put("faceData", "data:image/" + vehicleReq.getFaceData().split("\\.")[3] + ";base64," + FileBase64Util.getFileBase64(vehicleReq.getFaceData()));
                    }
                }
                if (null != vehicleReq.getBackData() && !"".equals(vehicleReq.getBackData().trim())) {
                    if(vehicleReq.getBackData().indexOf("group")>=0){
                        map.put("backData", "data:image/" + vehicleReq.getBackData().split("\\.")[1] + ";base64," + FileBase64Util.getFileBase64(sysParam.getParamValue()+vehicleReq.getBackData()));
                    }else{
                        map.put("backData", "data:image/" + vehicleReq.getBackData().split("\\.")[3] + ";base64," + FileBase64Util.getFileBase64(vehicleReq.getBackData()));
                    }
                }
                String url = openUrlNew + "/idCardLicenseV2";
                DataExchangeService des = new DataExchangeService(15000, 15000);
                // 通过 https 方式调用，此方法内部会使用私钥生成签名参数 sign,私钥不会发送
                String res = des.postHttps(url, map);
                log.info("身份证OCR识别返回参数:" + res);
                JSONObject jsonObject = JSONObject.parseObject(res);
                if(!"1001".equals(jsonObject.get("status").toString())){
                    if("1006".equals(jsonObject.get("status").toString())){
                        resp.setMsg("无结果");
                        resp.setCode(DictEnum.ERROR.code);
                    }else{
                        resp.setMsg(jsonObject.get("result").toString());
                        resp.setCode(DictEnum.ERROR.code);
                    }
                }else{
                    if(jsonObject.get("result")!=null&&!"".equals(jsonObject.get("result"))){
                        resp = JSONObject.parseObject(jsonObject.get("result").toString(),IdCardLicenseV2Resp.class);
                        resp.setCode(DictEnum.SUCCESS.code);
                    }
                }
                resp.setStatus(jsonObject.get("status").toString());
            }else{
                resp.setMsg("请传入图片");
                resp.setCode(DictEnum.ERROR.code);
            }
        } catch (Exception e) {
            log.info("身份证OCR识别失败:" ,e);
            return null;
        }
        return  resp;
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/3/23 11:43
     *  @Description: 营业执照OCR识别
     */
    @Override
    public BusinessLicenseOcrResp businessLicenseOCR(VehicleReq vehicleReq) {
        SysParam param = sysParamAPI.getParamByKey("TRAJECTORY");
        JSONObject jsonParam = JSONObject.parseObject(param.getParamValue());
        String openUrlNew = jsonParam.get("openUrlNew").toString();
        BusinessLicenseOcrResp resp = new BusinessLicenseOcrResp();
        try {
            if(null!=vehicleReq.getFaceData() &&!"".equals(vehicleReq.getFaceData().trim())){
                log.info("营业执照OCR识别请求参数:{}" + vehicleReq);
                Map<String, String> map = new HashMap();
                map.put("cid", jsonParam.get("clientId").toString());
                map.put("srt", jsonParam.get("str").toString());
                log.info("营业执照OCR识别请求参数：{}",map);
                SysParam sysParam = sysParamAPI.getParamByKey("DISPLAYIMAGEIP");

                if(vehicleReq.getFaceData().indexOf("group")>=0){
                    map.put("faceData", "data:image/" + vehicleReq.getFaceData().split("\\.")[1] + ";base64," + FileBase64Util.getFileBase64(sysParam.getParamValue()+vehicleReq.getFaceData()));
                }else{
                    map.put("faceData", "data:image/" + vehicleReq.getFaceData().split("\\.")[3] + ";base64," + FileBase64Util.getFileBase64(vehicleReq.getFaceData()));
                }
                String url = openUrlNew + "/businessLicenseOCR";
                DataExchangeService des = new DataExchangeService(15000, 15000);
                // 通过 https 方式调用，此方法内部会使用私钥生成签名参数 sign,私钥不会发送
                String res = des.postHttps(url, map);
                log.info("营业执照OCR识别返回参数:" + res);
                JSONObject jsonObject = JSONObject.parseObject(res);
                if(!"1001".equals(jsonObject.get("status").toString())){
                    if("1006".equals(jsonObject.get("status").toString())){
                        resp.setMsg("无结果");
                        resp.setCode(DictEnum.ERROR.code);
                    }else{
                        resp.setMsg(jsonObject.get("result").toString());
                        resp.setCode(DictEnum.ERROR.code);
                    }
                }else{
                    if(jsonObject.get("result")!=null&&!"".equals(jsonObject.get("result"))){
                        resp = JSONObject.parseObject(jsonObject.get("result").toString(),BusinessLicenseOcrResp.class);
                        resp.setCode(DictEnum.SUCCESS.code);
                    }
                }
                resp.setStatus(jsonObject.get("status").toString());
            }else{
                resp.setMsg("请传入图片");
                resp.setCode(DictEnum.ERROR.code);
            }
        } catch (Exception e) {
            log.info("营业执照OCR识别失败:" ,e);
            return null;
        }
        return  resp;
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/3/23 11:43
     *  @Description: 车辆道路运输证OCR识别
     */
    @Override
    public VehicleRoadLicenseOcrResp vehicleRoadLicenseOCR(VehicleReq vehicleReq) {
        SysParam param = sysParamAPI.getParamByKey("TRAJECTORY");
        JSONObject jsonParam = JSONObject.parseObject(param.getParamValue());
        String openUrlNew = jsonParam.get("openUrlNew").toString();
        VehicleRoadLicenseOcrResp resp = new VehicleRoadLicenseOcrResp();
        try {
            if(null!=vehicleReq.getFaceData() &&!"".equals(vehicleReq.getFaceData().trim())){
                log.info("车辆道路运输证OCR识别请求参数:{}" + vehicleReq);
                Map<String, String> map = new HashMap();
                map.put("cid", jsonParam.get("clientId").toString());
                map.put("srt", jsonParam.get("str").toString());
                log.info("车辆道路运输证OCR识别请求参数：{}",map);
                SysParam sysParam = sysParamAPI.getParamByKey("DISPLAYIMAGEIP");

                if(vehicleReq.getFaceData().indexOf("group")>=0){
                    map.put("faceData", "data:image/" + vehicleReq.getFaceData().split("\\.")[1] + ";base64," + FileBase64Util.getFileBase64(sysParam.getParamValue()+vehicleReq.getFaceData()));
                }else{
                    map.put("faceData", "data:image/" + vehicleReq.getFaceData().split("\\.")[3] + ";base64," + FileBase64Util.getFileBase64(vehicleReq.getFaceData()));
                }
                String url = openUrlNew + "/vehicleRoadLicenseOCR";
                DataExchangeService des = new DataExchangeService(15000, 15000);
                // 通过 https 方式调用，此方法内部会使用私钥生成签名参数 sign,私钥不会发送
                String res = des.postHttps(url, map);
                log.info("车辆道路运输证OCR识别返回参数:" + res);
                JSONObject jsonObject = JSONObject.parseObject(res);
                if(!"1001".equals(jsonObject.get("status").toString())){
                    if("1006".equals(jsonObject.get("status").toString())){
                        resp.setMsg("无结果");
                        resp.setCode(DictEnum.ERROR.code);
                    }else{
                        resp.setMsg(jsonObject.get("result").toString());
                        resp.setCode(DictEnum.ERROR.code);
                    }
                }else{
                    if(jsonObject.get("result")!=null&&!"".equals(jsonObject.get("result"))){
                        try {
                            String result = jsonObject.getString("result");
                            JSONObject parseObject = JSONObject.parseObject(result);
                            if (null != parseObject.getString("issueDate")) {
                                String issueDate = DateUtils.tryParseTime(parseObject.getString("issueDate"));
                                parseObject.put("issueDate", issueDate);
                            }
                            resp = JSONObject.parseObject(parseObject.toJSONString(), VehicleRoadLicenseOcrResp.class);
                        } catch (Exception e) {
                            log.error("解析失败, {}", ThrowableUtil.getStackTrace(e));
                            resp = JSONObject.parseObject(jsonObject.get("result").toString(),VehicleRoadLicenseOcrResp.class);

                        }
                        resp.setCode(DictEnum.SUCCESS.code);
                    }
                }
                resp.setStatus(jsonObject.get("status").toString());
            }else{
                resp.setMsg("请传入图片");
                resp.setCode(DictEnum.ERROR.code);
            }
        } catch (Exception e) {
            log.info("车辆道路运输证OCR识别失败:" ,e);
            return null;
        }
        return  resp;
    }

    /**
     *  @author: dingweibo
     *  @Date: 2023/3/6 10:08
     *  @Description: 获取插件地址接口
     */
    @Override
    public TrajectoryPluginUrlResp pluginUrl(TrajectoryPluginUrlReq req) {
        TrajectoryPluginUrlResp resp = new TrajectoryPluginUrlResp();
        SysParam param = sysParamAPI.getParamByKey("TRAJECTORY");
        JSONObject jsonParam = JSONObject.parseObject(param.getParamValue());
        String openUrlNew = jsonParam.get("openUrlNew").toString();
        try {

            if(null==req.getType()||"".equals(req.getType())){
                resp.setMsg("类型不能为空");
                resp.setCode(CodeEnum.ERROR.getCode());
                return resp;
            }
            Map<String,String> map2 = new HashMap<>();
            if(null!=req.getVclN() &&!"".equals(req.getVclN())){
                map2.put("vclN",req.getVclN());
            }
            if(null!=req.getVco() &&!"".equals(req.getVco())){
                map2.put("vco",req.getVco());
            }else{
                map2.put("vco","2");
            }
            if(null!=req.getQryBtm() &&!"".equals(req.getQryBtm())){
                map2.put("qryBtm",req.getQryBtm());
            }
            if(null!=req.getQryEtm() &&!"".equals(req.getQryEtm())){
                map2.put("qryEtm",req.getQryEtm());
            }
            if(null!=req.getParkMins() &&!"".equals(req.getParkMins())){
                map2.put("parkMins",req.getParkMins());
            }
            if(null!=req.getStopCheck() &&!"".equals(req.getStopCheck())){
                map2.put("stopCheck",req.getStopCheck());
            }
            if(null!=req.getMilState() &&!"".equals(req.getMilState())){
                map2.put("milState",req.getMilState());
            }
            if(null!=req.getStartLonlat() &&!"".equals(req.getStartLonlat())){
                map2.put("startLonlat",req.getStartLonlat());
            }
            if(null!=req.getEndLonlat() &&!"".equals(req.getEndLonlat())){
                map2.put("endLonlat",req.getEndLonlat());
            }
            if(null!=req.getStartAreaCode() &&!"".equals(req.getStartAreaCode())){
                map2.put("startAreaCode",req.getStartAreaCode().split("#")[0].split(",")[2]);
            }
            if(null!=req.getEndAreaCode() &&!"".equals(req.getEndAreaCode())){
                map2.put("endAreaCode",req.getEndAreaCode().split("#")[0].split(",")[2]);
            }
            Map<String, String> map = new HashMap();
            map.put("cid", jsonParam.get("clientId").toString());
            map.put("srt", jsonParam.get("str").toString());
            map.put("type", req.getType());
            map.put("pluginParams",JSONObject.toJSONString(map2));
            log.info("获取插件地址接口请求参数：{}",map);
            String url = openUrlNew + "/pluginUrl";
            DataExchangeService des = new DataExchangeService(15000, 15000);
            // 通过 https 方式调用，此方法内部会使用私钥生成签名参数 sign,私钥不会发送
            String res = des.postHttps(url, map);
            log.info("获取插件地址接口返回参数:" + res);
            JSONObject jsonObject = JSONObject.parseObject(res);
            if(!"1001".equals(jsonObject.get("status").toString())){
                if("1006".equals(jsonObject.get("status").toString())){
                    resp.setMsg("无结果");
                    resp.setCode(DictEnum.ERROR.code);
                }else{
                    if(jsonObject.get("result")!=null&&!"".equals(jsonObject.get("result"))){
                        resp.setResult(jsonObject.get("result").toString());
                        resp.setCode(DictEnum.SUCCESS.code);
                    }
                }
            }else{
                if(jsonObject.get("result")!=null&&!"".equals(jsonObject.get("result"))){
                    resp.setResult(jsonObject.get("result").toString());
                    resp.setCode(DictEnum.SUCCESS.code);
                }
            }
            resp.setStatus(jsonObject.get("status").toString());
        } catch (Exception e) {
            log.info("获取插件地址接口失败:" ,e);
            return null;
        }
        return  resp;
    }

    @Override
    public ResultUtil test() {
       /* String [] vclNs = {"冀A6H498_2","冀AP4619_2,冀EV4209_2,冀J3R686_2"};
        List<TransTimeManageVResp> respList = transTimeManageV3(vclNs,1);
        for(TransTimeManageVResp resp:respList){
            TrajectoryMongo transTimeManageV = new TrajectoryMongo();
            transTimeManageV.setTtmId(IdWorkerUtil.getInstance().nextId());
            transTimeManageV.setVehiclePlateNo(resp.getVno());
            if(null!=resp.getUtc() && !"".equals(resp.getUtc())){
                transTimeManageV.setTrajectoryReceiveTime(new Date(Long.parseLong(resp.getUtc())));
            }
            transTimeManageV.setTtmData(resp);
            transTimeManageV.setTrajectoryReceiveTime(new Date());
            trajectoryMongoDao.insert(transTimeManageV);
        }*/
        List<TrajectoryMongo> timeManageVList = trajectoryMongoDao.getAllByVehiclePlateNo("陕A12345");
        int i= 0;
        for(TrajectoryMongo trajectoryMongo:timeManageVList){
            i++;
            trajectoryMongoDao.delete(trajectoryMongo);
        }
        return ResultUtil.ok(i);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2023/5/19 15:45
     *  @Description: 驾驶证验证服务
     */
    @Override
    public TrajectoryPluginUrlResp drivingLicenseCheck(DrivingLicenseCheckReq req) {
        SysParam param = sysParamAPI.getParamByKey("TRAJECTORY");
        JSONObject jsonParam = JSONObject.parseObject(param.getParamValue());
        String openUrlNew = jsonParam.get("openUrlNew").toString();
        TrajectoryPluginUrlResp resp = new TrajectoryPluginUrlResp();
        try {
            if(null == req.getName()||"".equals(req.getName())){
                resp.setMsg("司机姓名不能为空");
                resp.setCode(CodeEnum.ERROR.getCode());
                return resp;
            }
            if(null == req.getCardNo()||"".equals(req.getCardNo())){
                resp.setMsg("驾驶证编号不能为空");
                resp.setCode(CodeEnum.ERROR.getCode());
                return resp;
            }
            if(null == req.getRecordId()||"".equals(req.getRecordId())){
                resp.setMsg("档案编号不能为空");
                resp.setCode(CodeEnum.ERROR.getCode());
                return resp;
            }
            Map<String, String> map = new HashMap();
            map.put("cid", jsonParam.get("clientId").toString());
            map.put("srt", jsonParam.get("str").toString());
            map.put("name", req.getName());
            map.put("cardNo",req.getCardNo());
            map.put("recordId",req.getRecordId());
            log.info("驾驶证验证服务接口请求参数：{}",map);
            String url = openUrlNew + "/drivingLicenseCheck";
            DataExchangeService des = new DataExchangeService(15000, 15000);
            // 通过 https 方式调用，此方法内部会使用私钥生成签名参数 sign,私钥不会发送
            String res = des.postHttps(url, map);
            log.info("驾驶证验证服务接口返回参数:" + res);
            JSONObject jsonObject = JSONObject.parseObject(res);
            if(!"1001".equals(jsonObject.get("status").toString())){
                if("1006".equals(jsonObject.get("status").toString())){
                    resp.setMsg("无结果");
                    resp.setCode(DictEnum.ERROR.code);
                }else{
                    resp.setMsg(jsonObject.get("result").toString());
                    resp.setCode(DictEnum.ERROR.code);
                }
            }else{
                if(jsonObject!=null&&!"".equals(jsonObject)){
                    resp = JSONObject.parseObject(jsonObject.toString(),TrajectoryPluginUrlResp.class);
                    resp.setCode(DictEnum.SUCCESS.code);
                }
            }
            resp.setStatus(jsonObject.get("status").toString());
        } catch (Exception e) {
            log.info("驾驶证验证服务失败:" ,e);
            return null;
        }
        return  resp;
    }

    @Override
    public CheckQualificationV2Resp checkQualificationV2(CheckQualificationV2Req req) {
        SysParam param = sysParamAPI.getParamByKey("TRAJECTORY");
        JSONObject jsonParam = JSONObject.parseObject(param.getParamValue());
        String openUrlNew = jsonParam.get("openUrlNew").toString();
        CheckQualificationV2Resp resp = new CheckQualificationV2Resp();
        try {
            if(null == req.getPersonName()||"".equals(req.getPersonName())){
                resp.setMsg("姓名不能为空");
                resp.setCode(CodeEnum.ERROR.getCode());
                return resp;
            }
            if(null == req.getProvinceCode()||"".equals(req.getProvinceCode())){
                resp.setMsg("省份/直辖市/自治区编码不能为空");
                resp.setCode(CodeEnum.ERROR.getCode());
                return resp;
            }
            if(null == req.getUserQualification()||"".equals(req.getUserQualification())){
                resp.setMsg("从业资格证号不能为空");
                resp.setCode(CodeEnum.ERROR.getCode());
                return resp;
            }
            Map<String, String> map = new HashMap();
            map.put("cid", jsonParam.get("clientId").toString());
            map.put("srt", jsonParam.get("str").toString());
            map.put("personName", req.getPersonName());
            map.put("provinceCode",req.getProvinceCode());
            map.put("userQualification",req.getUserQualification());
            //map.put("citizenNo", req.getCitizenNo());
            log.info("司机从业资格证核验服务V2接口请求参数：{}",map);
            String url = openUrlNew + "/checkQualificationV2";
            DataExchangeService des = new DataExchangeService(15000, 15000);
            // 通过 https 方式调用，此方法内部会使用私钥生成签名参数 sign,私钥不会发送
            String res = des.postHttps(url, map);
            log.info("司机从业资格证核验服务V2接口返回参数:" + res);
            JSONObject jsonObject = JSONObject.parseObject(res);
            if(!"1001".equals(jsonObject.get("status").toString())){
                if("1006".equals(jsonObject.get("status").toString())){
                    resp.setMsg("无结果");
                    resp.setCode(DictEnum.ERROR.code);
                }else{
                    resp.setMsg(jsonObject.get("result").toString());
                    resp.setCode(DictEnum.ERROR.code);
                }
            }else{
                if(jsonObject!=null&&!"".equals(jsonObject)){
                    resp = JSONObject.parseObject(JSONObject.parseArray(jsonObject.get("result").toString()).get(0).toString(),CheckQualificationV2Resp.class);
                    resp.setCode(DictEnum.SUCCESS.code);
                }
            }
            resp.setStatus(jsonObject.get("status").toString());
        } catch (Exception e) {
            log.info("司机从业资格证核验服务V2失败:" ,e);
            return null;
        }
        return  resp;
    }

}
