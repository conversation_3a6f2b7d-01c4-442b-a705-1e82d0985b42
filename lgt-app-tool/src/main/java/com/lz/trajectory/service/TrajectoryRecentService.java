package com.lz.trajectory.service;

import com.lz.common.util.ResultUtil;
import com.lz.model.trajectory.req.*;
import com.lz.model.trajectory.resp.*;
import com.lz.model.trajectory.resp.recent.*;

import java.util.List;

/**
 * auth dingweibo
 * 中交兴路
 */
public interface TrajectoryRecentService {


    /** 车辆最新位置查询（车牌号）接口
     * 本接口提供指定车牌号的车辆最新位置查询。
     * vclN 车牌号
     * */
    public TransTimeManageVResp transTimeManageV3One(String vclN, Integer time);

    /**
     * 运输节点服务
     *
     * @param vclNs
     * @return
     */
    public List<TransTimeManageVResp> transTimeManageV3(String[] vclNs, Integer time);

    /**
     *  运输行程服务(最新版 按时间段查询车辆轨迹))
     * 本接口提供指定车牌号，指定时间段查询车辆历史轨迹数据服务，开始时间和结束时间不能超过 24 小时。
     *
     * @param req
     * @return
     */
    public TrajectoryRecentResp routerPath(TrajectoryRouterPathReq req);



    /**
     * 车辆入网验证服务
     * 提供按车牌号判断指定车辆是否在全国货运平台入网服务
     *
     * @param vclN
     * @return
     */
    public ResultUtil checkTruckExistV2(String vclN);

    /**
     * 道路运输证验证接口
     * 本接口通过指定车牌号、道路运输证号码，验证道路运输证信息是否准确。
     *
     * @param req
     * @return
     */
    public RtcNoResp checkRTCNoV2(TrajectoryRouterPathReq req);

    /**
     * 车辆行驶证信息查询
     * 本接口提供指定车牌号、车牌颜色的车辆行驶证信息查询
     * @param req
     * @return
     */
    public VQueryLicenseV2Resp vQueryLicenseV2(TrajectoryRouterPathReq req);

    /**
     *  @author: dingweibo
     *  @Date: 2022/3/23 11:40
     *  @Description: 行驶证OCR识别
     */
    public VehicleLicenseOCRV2Resp vehicleLicenseOCRV2(VehicleReq vehicleReq);

    /**
     *  @author: dingweibo
     *  @Date: 2022/3/23 11:40
     *  @Description: 驾驶证OCR识别
     */
    public DrivingLicenseOCRV2Resp drivingLicenseOCRV2(VehicleReq vehicleReq);

    /**
     *  @author: dingweibo
     *  @Date: 2022/3/23 11:40
     *  @Description: 身份证OCR识别
     */
    public IdCardLicenseV2Resp idCardLicenseV2(VehicleReq vehicleReq);

    /**
     *  @author: dingweibo
     *  @Date: 2022/3/23 11:40
     *  @Description: 营业执照OCR识别
     */
    public BusinessLicenseOcrResp businessLicenseOCR(VehicleReq vehicleReq);

    /**
     *  @author: dingweibo
     *  @Date: 2022/3/23 11:40
     *  @Description: 车辆道路运输证OCR识别
     */
    public VehicleRoadLicenseOcrResp vehicleRoadLicenseOCR(VehicleReq vehicleReq);

    /**
     *  @author: dingweibo
     *  @Date: 2023/3/6 10:08
     *  @Description: 获取插件地址接口
     */
    public TrajectoryPluginUrlResp pluginUrl(TrajectoryPluginUrlReq req);

    public ResultUtil test();

    public TrajectoryPluginUrlResp drivingLicenseCheck(DrivingLicenseCheckReq req);

    public CheckQualificationV2Resp checkQualificationV2(CheckQualificationV2Req req);
}
