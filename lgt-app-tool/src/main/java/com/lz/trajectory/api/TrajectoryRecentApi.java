package com.lz.trajectory.api;

import com.lz.common.util.ResultUtil;
import com.lz.model.trajectory.req.*;
import com.lz.model.trajectory.resp.*;
import com.lz.model.trajectory.resp.recent.*;
import com.lz.trajectory.service.TrajectoryRecentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * auth dingweibo
 * 中交兴路 6.0 SDK
 */
@Controller
@RequestMapping("/trajectoryApi")
public class TrajectoryRecentApi {

    @Autowired
    private TrajectoryRecentService trajectoryRecentService;


    /** 车辆最新位置查询（车牌号）接口
     * 本接口提供指定车牌号的车辆最新位置查询。
     * vclN 车牌号
     * */
    @RequestMapping("/transTimeManageV3One")
    @ResponseBody
    public TransTimeManageVResp transTimeManageV3One(@RequestParam(value = "vclN")String vclN, @RequestParam(value = "time") Integer time) {
        return trajectoryRecentService.transTimeManageV3One(vclN,time);
    }

    /**
     * 运输节点服务
     *@auth dingweibo
     * @param vclNs
     * @return
     */
    @RequestMapping("/transTimeManageV3")
    @ResponseBody
    public List<TransTimeManageVResp> transTimeManageV3(@RequestParam(value = "vclNs") String[] vclNs, @RequestParam(value = "time") Integer time) {
        return  trajectoryRecentService.transTimeManageV3(vclNs,time);
    }

    /**
     * 运输行程服务(最新版 按时间段查询车辆轨迹)
     * 本接口提供指定车牌号，指定时间段查询车辆历史轨迹数据服务，开始时间和结束时间不能超过 24 小时。
     *@auth dingweibo
     * @param req
     * @return
     */
    @RequestMapping("/routerPath")
    @ResponseBody
    public TrajectoryRecentResp routerPath(@RequestBody TrajectoryRouterPathReq req){
        return  trajectoryRecentService.routerPath(req);
    }

    /**
     * 车辆入网验证接口
     * 提供按车牌号判断指定车辆是否在全国货运平台入网服务
     *@auth dingweibo
     * @param vclN
     * @return
     */
    @RequestMapping("/checkTruckExistV2")
    @ResponseBody
    public ResultUtil checkTruckExistV2(@RequestParam(value = "vclN") String vclN) {
        return  trajectoryRecentService.checkTruckExistV2(vclN);
    }

    /**@auth dingweibo
     * 道路运输证验证接口
     * 本接口通过指定车牌号、道路运输证号码，验证道路运输证信息是否准确。
     * @param req
     * @return
     */
    @RequestMapping("/checkRTCNoV2")
    @ResponseBody
    public RtcNoResp checkRTCNoV2(@RequestBody TrajectoryRouterPathReq req) {
        return  trajectoryRecentService.checkRTCNoV2(req);
    }

    /**
     * 车辆行驶证信息查询
     * 本接口提供指定车牌号、车牌颜色的车辆行驶证信息查询
     * @param req
     * @return
     */
    @RequestMapping("/vQueryLicenseV2")
    @ResponseBody
    public VQueryLicenseV2Resp vQueryLicenseV2(@RequestBody TrajectoryRouterPathReq req) {
        return  trajectoryRecentService.vQueryLicenseV2(req);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/3/21 10:59
     *  @Description: 行驶证OCR
     */
    @RequestMapping("/vehicleLicenseOCRV2")
    @ResponseBody
    public VehicleLicenseOCRV2Resp vehicleLicenseOCRV2(@RequestBody VehicleReq vehicleReq) {
        return  trajectoryRecentService.vehicleLicenseOCRV2(vehicleReq);
    }


    /**
     *  @author: dingweibo
     *  @Date: 2022/3/21 10:59
     *  @Description: 驾驶证OCR识别
     */
    @RequestMapping("/drivingLicenseOCRV2")
    @ResponseBody
    public DrivingLicenseOCRV2Resp drivingLicenseOCRV2(@RequestBody VehicleReq vehicleReq) {
        return  trajectoryRecentService.drivingLicenseOCRV2(vehicleReq);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/3/21 10:59
     *  @Description: 身份证OCR识别
     */
    @RequestMapping("/idCardLicenseV2")
    @ResponseBody
    public IdCardLicenseV2Resp idCardLicenseV2(@RequestBody VehicleReq vehicleReq) {
        return  trajectoryRecentService.idCardLicenseV2(vehicleReq);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/3/21 10:59
     *  @Description: 营业执照OCR识别
     */
    @RequestMapping("/businessLicenseOCR")
    @ResponseBody
    public BusinessLicenseOcrResp businessLicenseOCR(@RequestBody VehicleReq vehicleReq) {
        return  trajectoryRecentService.businessLicenseOCR(vehicleReq);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2022/3/21 10:59
     *  @Description: 车辆道路运输证OCR识别
     */
    @RequestMapping("/vehicleRoadLicenseOCR")
    @ResponseBody
    public VehicleRoadLicenseOcrResp vehicleRoadLicenseOCR(@RequestBody VehicleReq vehicleReq) {
        return  trajectoryRecentService.vehicleRoadLicenseOCR(vehicleReq);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2023/3/6 10:04
     *  @Description: 获取插件地址接口
     */
    @RequestMapping("/pluginUrl")
    @ResponseBody
    public TrajectoryPluginUrlResp pluginUrl(@RequestBody TrajectoryPluginUrlReq req) {
        return  trajectoryRecentService.pluginUrl(req);
    }

    @RequestMapping("/test")
    @ResponseBody
    public ResultUtil test() {
        return  trajectoryRecentService.test();
    }

    /**
     *  @author: dingweibo
     *  @Date: 2023/5/19 15:23
     *  @Description: 驾驶证验证服务
     */
    @RequestMapping("/drivingLicenseCheck")
    @ResponseBody
    public TrajectoryPluginUrlResp drivingLicenseCheck(@RequestBody DrivingLicenseCheckReq req) {
        return  trajectoryRecentService.drivingLicenseCheck(req);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2023/5/19 15:23
     *  @Description: 司机从业资格证核验服务 V2
     */
    @RequestMapping("/checkQualificationV2")
    @ResponseBody
    public CheckQualificationV2Resp checkQualificationV2(@RequestBody CheckQualificationV2Req req) {
        return  trajectoryRecentService.checkQualificationV2(req);
    }
}