package com.lz.common.constants;

/**
 * 京东支付阿里MQ消息主题tag
 */
public class MqMessageTag {

    /**
     * 申请企业开户：开户结果通知
     */
    public static final String COMPANYOPENCALLBACK = "COMPANYOPENCALLBACK";
    /**
     * 打款验证结果通知
     */
    public static final String PAYMENTVERIFICATIONCALLBACK = "PAYMENTVERIFICATIONCALLBACK";

    /**
     * 余额支付服务费
     */
    public static final String SERVICEPAYCALLBACK = "SERVICEPAYCALLBACK";

    /**
     * 小工具余额支付
     */
    public static final String BALANCEPAYCALLBACK = "BALANCEPAYCALLBACK";

    /**
     * 账户服务费余额支付
     */
    public static final String ACCOUNTSERVICEPAYCALLBACK = "ACCOUNTSERVICEPAYCALLBACK";

    /**
     * 转账入金
     */
    public static final String TRANSFERDEPOSITCALLBACK = "TRANSFERDEPOSITCALLBACK";

    /**
     * 申请个人开户：开户结果通知
     */
    public static final String PERSONOPENCALLBACK = "PERSONOPENCALLBACK";
    /**
     * 绑定银行卡：绑卡、解绑结果通知
     */
    public static final String BINDUNBINDCALLBACK= "BINDUNBINDCALLBACK";

    /**
     * 支付到司机资金流水：企业支付承运方消息
     */
    public static final String COMMONDRIVERTOCARRIER = "COMMONDRIVERTOCARRIER";
    /**
     *  支付到司机资金流水：承运方支付司机消息
     */
    public static final String COMMONDRIVERTODRIVER= "COMMONDRIVERTODRIVER";
    /**
     * 支付到司机资金流水回调
     */
    public static final String COMMONDRIVERCALLBACK = "COMMONDRIVERCALLBACK";

    /**
     * 支付到车队长资金流水：企业支付承运方消息
     */
    public static final String COMMONCAPTIONTOCARRIER = "COMMONCAPTIONTOCARRIER";
    /**
     * 支付到车队长资金流水：承运方支付司机消息
     */
    public static final String COMMONCAPTIONTODRIVER = "COMMONCAPTIONTODRIVER";
    /**
     * 支付到车队长资金流水：司机支付车队长消息
     */
    public static final String COMMONCAPTIONTOCAPTION= "COMMONCAPTIONTOCAPTION";
    /**
     * 支付到车队长资金流水回调
     */
    public static final String COMMONCAPTIONCALLBACK = "COMMONCAPTIONCALLBACK";

    /**
     * 支付到司机+经纪人资金流水：企业支付承运方消息
     */
    public static final String MANAGERDRIVERTOCARRIER = "MANAGERDRIVERTOCARRIER";

    /**
     * 支付到司机+经纪人资金流水：承运方支付司机消息
     */
    public static final String MANAGERDRIVERTODRIVER = "MANAGERDRIVERTODRIVER";
    /**
     * 支付到司机+经纪人资金流水：司机支付经纪人消息
     */
    public static final String MANAGERDRIVERTOMANAGER= "MANAGERDRIVERTOMANAGER";
    /**
     * 支付到司机+经纪人资金流水回调
     */
    public static final String MANAGERDRIVERCALLBACK = "MANAGERDRIVERCALLBACK";

    /**
     * 支付到车队长+经纪人资金流水：企业支付承运方消息
     */
    public static final String MANAGERCAPTIONTOCARRIER = "MANAGERCAPTIONTOCARRIER";
    /**
     * 支付到车队长+经纪人资金流水：承运方支付司机消息
     */
    public static final String MANAGERCAPTIONTODRIVER = "MANAGERCAPTIONTODRIVER";

    /**
     * 支付到车队长+经纪人资金流水回调
     */
    public static final String MANAGERCAPTIONCALLBACK = "MANAGERCAPTIONCALLBACK";

    /**
     * 发起落地分账消息
     */
    public static final String SPLITFALL = "SPLITFALL";

    /**
     * 落地分账消息回调
     */
    public static final String SPLITFALLCALLBACK = "SPLITFALLCALLBACK";

    /**
     * 交易退款
     */
    public static final String REFUND = "REFUND";

    /**
     * 交易退款回调
     */
    public static final String REFUNDCALLBACK = "REFUNDCALLBACK";

    /**
     * 打包交易退款回调
     */
    public static final String REFUNDPACKCALLBACK = "REFUNDPACKCALLBACK";

    /**
     * 分账退款
     */
    public static final String SPLITFALLREFUND = "SPLITFALLREFUND";

    /**
     * 分账退款回调
     */
    public static final String SPLITFALLREFUNDCALLBACK = "SPLITFALLREFUNDCALLBACK";

    /**
     * 提现
     */
    public static final String WITHDRAW = "WITHDRAW";

    /**
     * 自然人提现手续费余额支付
     */
    public static final String TXSERVICEFEE = "TXSERVICEFEE";

    /**
     * 自然人提现手续费余额支付回调
     */
    public static final String TXSERVICEFEECALLBACK = "TXSERVICEFEECALLBACK";

    /**
     * 自然人提现转账
     */
    public static final String TXTRANSFER = "TXTRANSFER";

    /**
     * 自然人提现转账回调
     */
    public static final String TXTRANSFERCALLBACK = "TXTRANSFERCALLBACK";

    /**
     * 提现回调
     */
    public static final String WITHDRAWCALLBACK = "WITHDRAWCALLBACK";

    /**
     * 提现
     */
    public static final String WITHDRAWAPPLY = "WITHDRAWAPPLY";

    /**
     * 打包支付
     */
    public static final String PACKPAY = "PACKPAY";

    /**
     * 插入
     */
    public static final String INSERT = "INSERT";

    /**
     * 更新
     */
    public static final String UPDATE = "UPDATE";

    /**
     * 删除
     */
    public static final String DELETE = "DELETE";

    /**
     * 预付款
     */
    public static final String PREPAY = "PREPAY";

    /**
     * 预付款回调
     */
    public static final String PREPAYCALLBACK = "PREPAYCALLBACK";

    /**
     * 本人账户服务费
     */
    public static final String MYACCOUNTSERVICEFEE = "MYACCOUNTSERVICEFEE";

    /**
     * 非本人账户服务费
     */
    public static final String NOMYACCOUNTSERVICEFEE = "NOMYACCOUNTSERVICEFEE";


    /**
     * 转账人账户服务费
     */
    public static final String TRANSFERORACCOUNTSERVICEFEE = "TRANSFERORACCOUNTSERVICEFEE";

    /**
     * 补交账户服务费
     */
    public static final String ACCOUNTSERVICEFEERETROACTIVE = "ACCOUNTSERVICEFEERETROACTIVE";

    /**
     * 补交账户服务费回调
     */
    public static final String ACCOUNTSERVICEFEERETROACTIVECALLBACK = "ACCOUNTSERVICEFEERETROACTIVECALLBACK";

    /**
     * 承运方余额支付(企业充值)
     */
    public static final String CARRIERBALANCEBDCHARGE = "CARRIERBALANCEBDCHARGE";

    /**
     * 承运方余额支付回调(企业充值)
     */
    public static final String CARRIERBALANCEBDCHARGECALLBACK = "CARRIERBALANCEBDCHARGECALLBACK";


    /**
     * 司机提现转账到承运方
     */
    public static final String TXTRANSFERTOCARRIER = "TXTRANSFERTOCARRIER";

    /**
     * 司机提现转账到承运方回调
     */
    public static final String TXTRANSFERTOCARRIERCALLBACK = "TXTRANSFERTOCARRIERCALLBACK";


    /**
     * 申请企业开户：开户结果通知
     */
    public static final String HXYHOPENCALLBACK = "HXYHOPENCALLBACK";

    /**
     * 华夏支付
     * 承运方余额支付(企业充值)
     */
    public static final String HXCARRIERBALANCEBDCHARGE = "HXCARRIERBALANCEBDCHARGE";

    /**
     * 华夏支付
     * 承运方余额支付回调(企业充值)
     */
    public static final String HXCARRIERBALANCEBDCHARGECALLBACK = "HXCARRIERBALANCEBDCHARGECALLBACK";

    /**
     * 司机审核
     */
    public static final String DRIVER_AUDIT = "DRIVER_AUDIT";

    /**
     * 车辆审核
     */
    public static final String CAR_AUDIT = "CAR_AUDIT";

    /**
     * 货源大厅装货提醒
     */
    public static final String LOADINGSINGINNOTIFY = "LOADINGSINGINNOTIFY";

    /**
     * 货源大厅过期自动取消
     */
    public static final String RESOURCEHALLORDERCANCEL = "RESOURCEHALLORDERCANCEL";

    /**
     * 货源下架
     */
    public static final String RESOURCEHALLTAKEOFF = "RESOURCEHALLTAKEOFF";

    /**
     * 第三方平台运单支付
     */
    public static final String THIRD_PARTY_ORDER_PAY = "THIRD_PARTY_ORDER_PAY";

    /**
     * 第三方平台运单电子回单
     */
    public static final String THIRD_PARTY_ORDER_RECEIPT = "THIRD_PARTY_ORDER_RECEIPT";

    /**
     * 第三方平台运单收款凭证
     */
    public static final String THIRD_PARTY_ORDER_SKPZ = "THIRD_PARTY_ORDER_SKPZ";

}
