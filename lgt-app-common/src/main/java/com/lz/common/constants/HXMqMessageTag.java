package com.lz.common.constants;

/**
 * 京东支付阿里MQ消息主题tag
 */
public class HXMqMessageTag {

    /**
     * 申请企业开户：开户结果通知
     */
    public static final String HX_COMPANYOPENCALLBACK = "HX_COMPANYOPENCALLBACK";

    /**
     * 余额支付服务费
     */
    public static final String HX_SERVICEPAYCALLBACK = "HX_SERVICEPAYCALLBACK";

    /**
     * 小工具余额支付
     */
    public static final String HX_BALANCEPAYCALLBACK = "HX_BALANCEPAYCALLBACK";

    /**
     * 账户服务费余额支付
     */
    public static final String HX_ACCOUNTSERVICEPAYCALLBACK = "HX_ACCOUNTSERVICEPAYCALLBACK";

    /**
     * 转账入金
     */
    public static final String HX_TRANSFERDEPOSITCALLBACK = "HX_TRANSFERDEPOSITCALLBACK";

    /**
     * 申请个人开户：开户结果通知
     */
    public static final String HX_PERSONOPENCALLBACK = "HX_PERSONOPENCALLBACK";
    /**
     * 绑定银行卡：绑卡、解绑结果通知
     */
    public static final String HX_BINDUNBINDCALLBACK= "HX_BINDUNBINDCALLBACK";

    /**
     * 支付到司机资金流水：企业支付承运方消息
     */
    public static final String HX_COMMONDRIVERTOCARRIER = "HX_COMMONDRIVERTOCARRIER";
    /**
     *  支付到司机资金流水：承运方支付司机消息
     */
    public static final String HX_COMMONDRIVERTODRIVER= "HX_COMMONDRIVERTODRIVER";
    /**
     * 支付到司机资金流水回调
     */
    public static final String HX_COMMONDRIVERCALLBACK = "HX_COMMONDRIVERCALLBACK";

    /**
     * 支付到车队长资金流水：企业支付承运方消息
     */
    public static final String HX_COMMONCAPTIONTOCARRIER = "HX_COMMONCAPTIONTOCARRIER";
    /**
     * 支付到车队长资金流水：承运方支付司机消息
     */
    public static final String HX_COMMONCAPTIONTODRIVER = "HX_COMMONCAPTIONTODRIVER";
    /**
     * 支付到车队长资金流水：司机支付车队长消息
     */
    public static final String HX_COMMONCAPTIONTOCAPTION= "HX_COMMONCAPTIONTOCAPTION";
    /**
     * 支付到车队长资金流水回调
     */
    public static final String HX_COMMONCAPTIONCALLBACK = "HX_COMMONCAPTIONCALLBACK";

    /**
     * 支付到司机+经纪人资金流水：企业支付承运方消息
     */
    public static final String HX_MANAGERDRIVERTOCARRIER = "HX_MANAGERDRIVERTOCARRIER";

    /**
     * 支付到司机+经纪人资金流水：承运方支付司机消息
     */
    public static final String HX_MANAGERDRIVERTODRIVER = "HX_MANAGERDRIVERTODRIVER";
    /**
     * 支付到司机+经纪人资金流水：司机支付经纪人消息
     */
    public static final String HX_MANAGERDRIVERTOMANAGER= "HX_MANAGERDRIVERTOMANAGER";
    /**
     * 支付到司机+经纪人资金流水回调
     */
    public static final String HX_MANAGERDRIVERCALLBACK = "HX_MANAGERDRIVERCALLBACK";

    /**
     * 支付到车队长+经纪人资金流水：企业支付承运方消息
     */
    public static final String HX_MANAGERCAPTIONTOCARRIER = "HX_MANAGERCAPTIONTOCARRIER";
    /**
     * 支付到车队长+经纪人资金流水：承运方支付司机消息
     */
    public static final String HX_MANAGERCAPTIONTODRIVER = "HX_MANAGERCAPTIONTODRIVER";
    /**
     * 支付到车队长+经纪人资金流水：司机支付车队长消息
     */
    public static final String HX_MANAGERCAPTIONTOCAPTION = "HX_MANAGERCAPTIONTOCAPTION";
    /**
     * 支付到车队长+经纪人资金流水：司机支付经纪人消息
     */
    public static final String HX_MANAGERCAPTIONTOMANAGER = "HX_MANAGERCAPTIONTOMANAGER";

    /**
     * 支付到车队长+经纪人资金流水回调
     */
    public static final String HX_MANAGERCAPTIONCALLBACK = "HX_MANAGERCAPTIONCALLBACK";

    /**
     * 交易退款
     */
    public static final String HX_REFUND = "HX_REFUND";

    /**
     * 交易退款回调
     */
    public static final String HX_REFUNDCALLBACK = "HX_REFUNDCALLBACK";

    /**
     * 打包交易退款回调
     */
    public static final String HX_REFUNDPACKCALLBACK = "HX_REFUNDPACKCALLBACK";


    /**
     * 提现
     */
    public static final String HX_WITHDRAW = "HX_WITHDRAW";

    /**
     * 自然人提现手续费余额支付
     */
    public static final String HX_TXSERVICEFEE = "HX_TXSERVICEFEE";

    /**
     * 自然人提现手续费余额支付回调
     */
    public static final String HX_TXSERVICEFEECALLBACK = "HX_TXSERVICEFEECALLBACK";

    /**
     * 自然人提现转账
     */
    public static final String HX_TXTRANSFER = "HX_TXTRANSFER";

    /**
     * 自然人提现转账回调
     */
    public static final String HX_TXTRANSFERCALLBACK = "HX_TXTRANSFERCALLBACK";

    /**
     * 提现回调
     */
    public static final String HX_WITHDRAWCALLBACK = "HX_WITHDRAWCALLBACK";

    /**
     * 提现
     */
    public static final String HX_WITHDRAWAPPLY = "HX_WITHDRAWAPPLY";

    /**
     * 打包支付
     */
    public static final String HX_PACKPAY = "HX_PACKPAY";

    /**
     * 插入
     */
    public static final String HX_INSERT = "HX_INSERT";

    /**
     * 更新
     */
    public static final String HX_UPDATE = "HX_UPDATE";

    /**
     * 删除
     */
    public static final String HX_DELETE = "HX_DELETE";

    /**
     * 预付款
     */
    public static final String HX_PREPAY = "HX_PREPAY";

    /**
     * 预付款回调
     */
    public static final String HX_PREPAYCALLBACK = "HX_PREPAYCALLBACK";

    /**
     * 本人账户服务费
     */
    public static final String HX_MYACCOUNTSERVICEFEE = "HX_MYACCOUNTSERVICEFEE";

    /**
     * 非本人账户服务费
     */
    public static final String HX_NOMYACCOUNTSERVICEFEE = "HX_NOMYACCOUNTSERVICEFEE";


    /**
     * 转账人账户服务费
     */
    public static final String HX_TRANSFERORACCOUNTSERVICEFEE = "HX_TRANSFERORACCOUNTSERVICEFEE";

    /**
     * 补交账户服务费
     */
    public static final String HX_ACCOUNTSERVICEFEERETROACTIVE = "HX_ACCOUNTSERVICEFEERETROACTIVE";

    /**
     * 补交账户服务费回调
     */
    public static final String HX_ACCOUNTSERVICEFEERETROACTIVECALLBACK = "HX_ACCOUNTSERVICEFEERETROACTIVECALLBACK";

    /**
     * 承运方余额支付(企业充值)
     */
    public static final String HX_CARRIERBALANCEBDCHARGE = "HX_CARRIERBALANCEBDCHARGE";

    /**
     * 承运方余额支付回调(企业充值)
     */
    public static final String HX_CARRIERBALANCEBDCHARGECALLBACK = "HX_CARRIERBALANCEBDCHARGECALLBACK";


    /**
     * 司机提现转账到承运方
     */
    public static final String HX_TXTRANSFERTOCARRIER = "HX_TXTRANSFERTOCARRIER";

    /**
     * 司机提现转账到承运方回调
     */
    public static final String HX_TXTRANSFERTOCARRIERCALLBACK = "HX_TXTRANSFERTOCARRIERCALLBACK";


    /**
     * 申请企业开户：开户结果通知
     */
    public static final String HXYHOPENCALLBACK = "HXYHOPENCALLBACK";

    /**
     * 司机支付保险费
     */
    public static final String HX_INSURANCE = "HX_INSURANCE";

}
