package com.lz.common.model.sort;

/*
 * <AUTHOR>
 * @Description 运单收单字段
 * @Date 2020/1/11 13:44
 * @Param
 * @return
 **/
public enum RECIEVEORDER {

    orderBusinessCode("order_business_code", "0", ""),
    orderCreateType("orderCreateType", "1", ""),
    vehicleNumber("vehicle_number", "1", ""),
    realName("real_name", "1", ""),
    phone("phone", "0", ""),
    packStatus("pack_status", "", "1"),
    orderAudit("orderAudit", "1", ""),
    orderExecuteStatusValue("orderExecuteStatusValue", "1", ""),
    sourceName("sourceName", "1", ""),
    fromName("from_name", "1", ""),
    endName("end_name", "1", ""),
    carriageUnitPrice("carriageUnitPrice", "0", ""),
    carOwnerRealName("carOwnerRealName", "1", ""),
    carOwnerRealPhone("carOwnerRealPhone", "0", ""),
    orgRealName("orgRealName", "1", ""),
    orgPhone("orgPhone", "0", ""),
    deliverOrderTime("deliver_order_time", "0", ""),
    deliverWeightNotesTime("deliver_weight_notes_time", "0", ""),
    primaryWeight("primary_weight", "0", ""),
    receiveWeightNotesTime("receive_weight_notes_time", "0", ""),
    dischargeWeight("discharge_weight", "0", ""),
    receiveOrderTime("receive_order_time", "0", ""),
    settledWeight("settled_weight", "0", ""),
    dispatchFee("dispatch_fee", "0", ""),
    capitalTransferType("capitalTransferType", "1", ""),
    companyName("company_name", "1", ""),
    projectName("project_name", "1", ""),
    nickname("nickname", "1", ""),
    nickPhone("nickPhone", "0", ""),
    receiverNickPhone("receiverNickPhone", "0", ""),
    receiverNickname("receiverNickname", "1", ""),
    goodsName("goods_name", "1", ""),
    companyEntrust("company_entrust", "1", ""),
    companyClient("company_client", "1", ""),


    orderPayStatusValue("orderPayStatusValue", "1", ""),
    originalCarriageFee("originalCarriageFee", "0", ""),
    originalDispatchFee("originalDispatchFee", "0", ""),
    goodsCutWater("goods_cut_water", "0", ""),
    goodsCutImpurities("goods_cut_impurities", "0", ""),
    loseOrRise("lose_or_rise", "0", ""),
    toleranceOfWeight("toleranceOfWeight", "0", ""),
    deficitWeight("deficit_weight", "0", ""),
    settlementPrice("settlementPrice", "0", ""),
    loseOrRiseCut("lose_or_rise_cut", "0", ""),
    otherFeeCount("otherFeeCount", "0", ""),
    carriageZeroCutPayment("carriage_zero_cut_payment", "0", ""),
    carriageFee("carriageFee", "0", ""),
    payTime("payTime", "0", ""),
    carrierName("carrier_name", "1", ""),

    signType("signType", "1", ""),
    estimateGoodsWeight("estimateGoodsWeight", "0", ""),
    currentCarriageUnitPrice("current_carriage_unit_price", "0", ""),
    estimateTotalFee("estimate_total_fee", "0", ""),
    totalFee("total_fee", "0", ""),
    yfkFkTime("yfkFkTime", "0", ""),
    payUser("payUser", "0", ""),
    orderFinishTime("order_finish_time", "0", ""),
    withDrawUser("withDrawUser", "1", ""),
    withDrawType("withDrawType", "1", ""),
    yfkTxTime("yfkTxTime", "0", ""),
    yfkCardHolder("yfkCardHolder", "1", ""),
    yfkBankNo("yfkBankNo", "0", ""),
    companyContacts("company_contacts", "1", ""),
    companyContactsPhone("company_contacts_phone", "1", ""),
    deleteReason("delete_reason", "1", ""),

    timeOfPayment("timeOfPayment", "0", ""),

    virtualOrderNo("virtual_order_no", "0", ""),
    packStatusValue("packStatusValue", "1", ""),
    checkAccountPerson("check_account_person", "1", ""),
    totalSelectedOrders("total_selected_orders", "0", ""),
    totalSelectedOrdersWeight("total_selected_orders_weight", "0", ""),
    totalSelectedOrdersCarriageFee("total_selected_orders_carriage_fee", "0", ""),
    totalSelectedOrdersDispatchFee("total_selected_orders_dispatch_fee", "0", ""),
    totalSelectedOrdersCarriageZeroCutFee("total_selected_orders_carriage_zero_cut_fee", "0", ""),
    appointmentPaymentCash("appointment_payment_cash", "0", ""),
    appointmentPaymentOther("appointment_payment_other", "0", ""),
    recountDispatchFee("recount_dispatch_fee", "0", ""),
    contentDescribtion("content_describtion", "0", ""),
    createTimes("createTimes", "0", ""),
    cardOwner("card_owner", "1", ""),
    cardNo("card_no", "0", ""),

    orgName("orgName", "1", ""),
    unqualifiedRemark("unqualified_remark", "1", ""),

    deliverOrderTimeStr("unqualified_remark", "1", ""),
    orderFinishTimeStr("unqualified_remark", "1", ""),

    checkFlag("checkFlag", "1", ""),
    verifyFlag("verifyFlag", "1", ""),
    checkStatus("checkStatus", "1", ""),
    notMeetInvoiceCondition("notMeetInvoiceCondition", "1", ""),
    deliverWeightNotesTimeStr("deliverWeightNotesTimeStr", "0", ""),
    receiveWeightNotesTimeStr("receiveWeightNotesTimeStr", "0", ""),
    userConfirmPaymentAmount("userConfirmPaymentAmount", "0", ""),
    receiveOrderTimeStr("receiveOrderTimeStr", "0", ""),

    operateTime("operateTime", "0", ""),
    idcard("idcard", "0", ""),
    bankNo("bankNo", "0", ""),
    cardHolder("cardHolder", "0", ""),
    orderPayStatus("orderPayStatus", "1", ""),
    innerTradeNo("innerTradeNo", "0", ""),
    returnTime("returnTime", "0", ""),
    errorMsg("errorMsg", "1", ""),

    auditRemark("audit_remark", "1", ""),
    ;

    public String[] code;

    RECIEVEORDER(String... code) {
        this.code = code;
    }

    public String getValue(int index) {
        return this.code[index];
    }
}
