package com.lz.common.model.datareport.khydr.carrierContractReport;

import com.alibaba.fastjson.JSON;
import com.lz.common.model.datareport.base.DataReportDTO;
import com.lz.common.model.datareport.base.ReportResult;
import com.lz.common.model.datareport.khydr.khyExecute;
import com.rabbitmq.tools.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
@AllArgsConstructor
@NoArgsConstructor
/**
 * 承运合同
 */
public class khyCarrierContractReportDTO extends khyExecute implements DataReportDTO<khyCarrierContractReportDTO> {

    //承运人名称
    private String carrierName;

    //承运人证件号
    private String carrierIdCard;

    //业务模式
    private String bizType;

    //合同开始时间
    private Long beginTime;

    //合同结束时间
    private Long endTime;

    //签约时间
    private Long signTime;

    //附件 Filename
    private String attachment;

    private String fileType;

    @Override
    public ReportResult assemble(khyCarrierContractReportDTO khyCarrierContractReportDTO) throws Exception {

        khyCarrierContractReportDTO.setAttachment(postFile(getFileInputStream(khyCarrierContractReportDTO.getAttachment()), "附件" + khyCarrierContractReportDTO.getFileType()));
        log.info("快货运承运合同请求参数, {}", JSON.toJSONString(khyCarrierContractReportDTO));
        String driverJson = JSON.toJSONString(khyCarrierContractReportDTO);

        String result  = postJson("/contract/carrier/create", driverJson);
        log.info("快货运承运合同, {}", result);
        ReportResult reportResult = JSON.parseObject(result, ReportResult.class);
        return reportResult;
    }
}
