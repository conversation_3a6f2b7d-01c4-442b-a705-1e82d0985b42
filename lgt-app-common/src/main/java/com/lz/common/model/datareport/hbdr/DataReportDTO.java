package com.lz.common.model.datareport.hbdr;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lz.common.model.datareport.hbdr.reportreq.Body;
import com.lz.common.model.datareport.hbdr.reportreq.Header;
import com.lz.common.model.datareport.hbdr.reportreq.ReportReq;
import com.lz.common.model.datareport.hbdr.reportreq.Root;
import com.lz.common.util.DateUtils;
import com.lz.common.util.HttpUtils;
import com.lz.common.util.PropertiesUtils;
import com.lz.common.util.datareport.SM2Utils;
import com.lz.common.util.datareport.SM4Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 传参的顶端抽象类
 */
@Component
@Slf4j
public abstract class DataReportDTO<T> {

    private static Map<String, Object> map;

    private static Map<String, String> userMap;

    static {
        try {
            String json = PropertiesUtils.getJson("/hbdr.json");
            map = JSON.parseObject(json, HashMap.class);
            userMap = (Map<String, String>) map.get("userMap");
            userMap.put("passWord", SM2Utils.encrypt(userMap.get("passWord").getBytes()));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 组装处理数据
     */
    public abstract ReportResult assemble(T t) throws Exception;

    /**
     * 获取请求对象
     */
    public ReportReq getReportReq(ReportEnum reportEnum, T t) throws Exception {
        ReportReq reportReq = new ReportReq();
        //生成header
        Header header = new Header();
        header.setMessageReferenceNumber(UUID.randomUUID().toString().replace("-", ""));
        header.setDocumentName(reportEnum.code);
        header.setDocumentVersionNumber("2015WCCYR");
        header.setSenderCode("130023");
        header.setEnterpriseSenderCode("1391130101MA09TTC25F");
        header.setMessageSendingDateTime(DateUtils.formatDate(new Date(), "yyyyMMddHHmmss"));
        header.setIpcType(reportEnum.type);
        header.setToken(getToken());
        //获取加密钱的一些东西
        Map<String, String> encryptKeys = encryptBefore();
        //生成body
        Body body = new Body();
        body.setEncryptedCode(encryptKeys.get("encryptedCode"));
        body.setEncryptedContent(SM4Utils.encryptData_ECB(encryptKeys.get("rKey"), JSON.toJSONString(t)));
        //组装数据
        Root root = new Root();
        root.setHeader(header);
        root.setBody(body);
        reportReq.setRoot(root);
        System.out.println(JSON.toJSONString(reportReq));
        return reportReq;
    }


    /**
     * 准备加密的数据
     */
    private static Map<String, String> encryptBefore() throws IOException, NoSuchProviderException, NoSuchAlgorithmException {
        Map<String, String> result = new HashMap<>(4);
        String rKey = SM4Utils.getRandomKey();
        String encryptedCode = SM2Utils.encrypt(rKey.getBytes());
        System.out.println(rKey);
        System.out.println(encryptedCode);
        result.put("rKey", rKey);
        result.put("encryptedCode", encryptedCode);
        return result;
    }

    /**
     * 上报
     *
     * @param reportReq 上报对象
     * @return
     * @throws Exception
     */
    public static ReportResult report(ReportReq reportReq) throws Exception {
        ReportResult r = req(map.get("report_url").toString(), reportReq);
        System.out.println(r);
        return r;
    }


    /**
     * 获取token
     *
     * @throws Exception
     */
    private static String getToken() throws Exception {
        String result = null;
        //获取token
        ReportResult r = reqFormData(map.get("token_url").toString(), userMap);
        if ("1001".equals(r.getCode()) &&
                "true".equals(r.getSuccess())
        ) {
            result = r.getData();
        }
        return result;
    }

    /**
     * 封装post请求
     *
     * @throws Exception
     */
    private static ReportResult req(String url, Object o) throws Exception {
        String req = JSON.toJSONString(o);
        HttpResponse httpResponse = HttpUtils.doPostJSON(url, req);
        if (httpResponse.getStatusLine().getStatusCode() != 200) {
            throw new Exception();
        }
        String json = HttpUtils.bytesToString(HttpUtils.getData(httpResponse.getEntity()));
        return JSON.parseObject(json, ReportResult.class);
    }

    /**
     * 封装post请求(登陆)
     *
     * @throws Exception
     */
    private static ReportResult reqFormData(String url, Object o) throws Exception {
        Map<String, Object> map = JSONObject.parseObject(JSON.toJSONString(o));
        HttpResponse httpResponse = HttpUtils.doPost(url, null, new HashMap<>(), map, null);
        if (httpResponse.getStatusLine().getStatusCode() != 200) {
            throw new Exception();
        }
        String json = HttpUtils.bytesToString(HttpUtils.getData(httpResponse.getEntity()));
        return JSON.parseObject(json, ReportResult.class);
    }


    public static void main(String[] args) throws Exception {
        System.out.println(getToken());
    }

}
