package com.lz.common.factory;

import com.lz.common.constants.HXMqMessageTag;
import com.lz.common.constants.HXMqMessageTopic;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.HXTradeTypeEnum;
import com.lz.common.model.MQMessage;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class HXMQMessageFactory {

    public static MQMessage getMessage(String tradeType, String capitalTransferType, String capitalTransferPattern) {
        MQMessage mqMessage = new MQMessage();
        // 普通模式
        if (DictEnum.COMMONPATTERN.code.equals(capitalTransferPattern)) {
            // 支付到司机
            if (DictEnum.PAYTODRIVER.code.equals(capitalTransferType)) {
                mqMessage.setTopic(HXMqMessageTopic.HX_COMMONDRIVER);
                // 企业余额支付
                if (HXTradeTypeEnum.HX_COMBALANCE_PAY.code.equals(tradeType)) {
                    mqMessage.setTag(HXMqMessageTag.HX_COMMONDRIVERTOCARRIER);
                }
                // 承运方余额支付
                if (HXTradeTypeEnum.HX_CABALANCE_PAY.code.equals(tradeType)) {
                    mqMessage.setTag(HXMqMessageTag.HX_COMMONDRIVERTODRIVER);
                }
                // 打包支付
                if (HXTradeTypeEnum.DBRZ.code.equals(tradeType)) {
                    mqMessage.setTag(HXMqMessageTag.HX_PACKPAY);
                }
            } else if (DictEnum.PAYTOCAPTAIN.code.equals(capitalTransferType)) {
                // 支付到车队长
                mqMessage.setTopic(HXMqMessageTopic.HX_COMMONCAPTION);
                // 企业余额支付
                if (HXTradeTypeEnum.HX_COMBALANCE_PAY.code.equals(tradeType)) {
                    mqMessage.setTag(HXMqMessageTag.HX_COMMONCAPTIONTOCARRIER);
                }
                // 承运方余额支付
                if (HXTradeTypeEnum.HX_CABALANCE_PAY.code.equals(tradeType)) {
                    mqMessage.setTag(HXMqMessageTag.HX_COMMONCAPTIONTODRIVER);
                }
                // 司机余额支付
                if (HXTradeTypeEnum.HX_CDBALANCE_PAY.code.equals(tradeType)) {
                    mqMessage.setTag(HXMqMessageTag.HX_COMMONCAPTIONTOCAPTION);
                }
                // 打包支付
                if (HXTradeTypeEnum.DBRZ.code.equals(tradeType)) {
                    mqMessage.setTag(HXMqMessageTag.HX_PACKPAY);
                }
            }
        } else if (DictEnum.MANAGERPATTERN.code.equals(capitalTransferPattern)) {
            // 经纪人模式
            if (DictEnum.PAYTODRIVER.code.equals(capitalTransferType)) {
                // 支付到司机
                mqMessage.setTopic(HXMqMessageTopic.HX_MANAGERDRIVER);
                // 企业余额支付
                if (HXTradeTypeEnum.HX_COMBALANCE_PAY.code.equals(tradeType)) {
                    mqMessage.setTag(HXMqMessageTag.HX_MANAGERDRIVERTOCARRIER);
                }
                // 承运方余额支付
                if (HXTradeTypeEnum.HX_CABALANCE_PAY.code.equals(tradeType)) {
                    mqMessage.setTag(HXMqMessageTag.HX_MANAGERDRIVERTODRIVER);
                }
                // 司机余额支付
                if (HXTradeTypeEnum.HX_CDBALANCE_PAY.code.equals(tradeType)) {
                    mqMessage.setTag(HXMqMessageTag.HX_MANAGERDRIVERTOMANAGER);
                }
                // 打包支付
                if (HXTradeTypeEnum.DBRZ.code.equals(tradeType)) {
                    mqMessage.setTag(HXMqMessageTag.HX_PACKPAY);
                }
            } else if (DictEnum.PAYTOCAPTAIN.code.equals(capitalTransferType)) {
                // 支付到车队长
                mqMessage.setTopic(HXMqMessageTopic.HX_MANAGERCAPTION);
                if (HXTradeTypeEnum.HX_COMBALANCE_PAY.code.equals(tradeType)) {
                    // 企业余额支付
                    mqMessage.setTag(HXMqMessageTag.HX_MANAGERCAPTIONTOCARRIER);
                } else if (HXTradeTypeEnum.HX_CABALANCE_PAY.code.equals(tradeType)) {
                    // 承运方余额支付
                    mqMessage.setTag(HXMqMessageTag.HX_MANAGERCAPTIONTODRIVER);
                } else if (HXTradeTypeEnum.HX_CCBALANCE_PAY.code.equals(tradeType)) {
                    // 司机余额支付到车队长
                    mqMessage.setTag(HXMqMessageTag.HX_MANAGERCAPTIONTOCAPTION);
                } else if (HXTradeTypeEnum.HX_CMBALANCE_PAY.code.equals(tradeType)) {
                    // 司机余额支付到经纪人
                    mqMessage.setTag(HXMqMessageTag.HX_MANAGERCAPTIONTOMANAGER);
                }
                // 打包支付
                if (HXTradeTypeEnum.DBRZ.code.equals(tradeType)) {
                    mqMessage.setTag(HXMqMessageTag.HX_PACKPAY);
                }
            }
        }
        // 交易退款
        if (HXTradeTypeEnum.HX_REFUND.code.equals(tradeType)) {
            mqMessage.setTag(HXMqMessageTag.HX_REFUND);
        }
        if (HXTradeTypeEnum.HX_INSURANCE.code.equals(tradeType)) {
            mqMessage.setTag(HXMqMessageTag.HX_INSURANCE);
        }
        if (null == mqMessage.getTopic() || null == mqMessage.getTag()) {
            log.error("ZJJ-999:支付失败！");
            throw new RuntimeException("ZJJ-999:支付失败！");
        }
        return mqMessage;
    }

}
