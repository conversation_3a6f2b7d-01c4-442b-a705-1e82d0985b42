package com.lz.common.util;

import com.alibaba.fastjson.JSONObject;
import com.lz.common.dbenum.BankNameEnum;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class AliPayCardDetailUtil {
    public static Map<String,Object> getCardDetail(String cardNo) {
        // 创建HttpClient实例
        StringBuffer url = new StringBuffer("https://ccdcapi.alipay.com/validateAndCacheCardInfo.json?_input_charset=utf-8&cardNo=");
        url.append(cardNo);
        url.append("&cardBinCheck=true");
        StringBuilder sb = new StringBuilder();
        try {
            URL urlObject = new URL(url.toString());
            URLConnection uc = urlObject.openConnection();
            BufferedReader in = new BufferedReader(new InputStreamReader(uc.getInputStream()));
            String inputLine = null;
            while ( (inputLine = in.readLine()) != null) {
                sb.append(inputLine);
            }
            in.close();
        } catch (MalformedURLException e) {
            log.error("阿里控件获取银行卡户名失败",e);
        } catch (IOException e) {
            log.error("阿里控件获取连接失败",e);
        }
        log.error("查询开户行返回参数：{}",sb.toString());
        String bank = JSONObject.parseObject(sb.toString()).get("bank").toString();
        Map<String,Object> map = new HashMap<>();
        map.put("key",bank);
        map.put("value",BankNameEnum.getDesc(bank));
        map.put("validated",JSONObject.parseObject(sb.toString()).get("validated").toString());
        map.put("stat",JSONObject.parseObject(sb.toString()).get("stat").toString());
        return map;
    }


}
