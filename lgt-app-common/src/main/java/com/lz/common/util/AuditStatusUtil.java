package com.lz.common.util;

import com.lz.common.dbenum.DictEnum;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 证件认证工具
 */
public class AuditStatusUtil {

    /**
     * 根据各个证件认证状态，计算总认证状态
     * @return
     */
    public static String status(String... auditStatus) {
        if (auditStatus.length == 0) {
            return "";
        }
        if (Arrays.stream(auditStatus).anyMatch((status) -> DictEnum.NOTPASSNODE.code.equals(status))) {
            return DictEnum.NOTPASSNODE.code;
        }
        if (Arrays.stream(auditStatus).anyMatch((status) -> DictEnum.PAPERNEEDUPDATE.code.equals(status))) {
            return DictEnum.PAPERNEEDUPDATE.code;
        }
        if (Arrays.stream(auditStatus).anyMatch((status) -> DictEnum.MIDNODE.code.equals(status))) {
            return DictEnum.MIDNODE.code;
        }
        if (Arrays.stream(auditStatus).allMatch((status) -> DictEnum.PASSNODE.code.equals(status))) {
            return DictEnum.PASSNODE.code;
        }
        return "";
    }

    /**
     * 拼接审核意见
     * @param opinions
     * @return
     */
    public static String auditOpinion(String... opinions) {
        List<String> list = new ArrayList<>();
        for (String str : opinions) {
            if (str != null && StringUtils.isNotBlank(str)) {
                list.add(str);
            }
        }
        if (!list.isEmpty()) {
            return  String.join("、", list);
        }
        return "";
    }

}
