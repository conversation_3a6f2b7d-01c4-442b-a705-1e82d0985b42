package com.lz.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lz.common.model.LoginVerificationReq;
import com.lz.common.model.LoginVerificationResp;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 *  登陆滑动验证码
 */
@Slf4j
public class LoginVerificationUtil {
    public LoginVerificationResp txyzm(LoginVerificationReq req) {
        LoginVerificationResp loginVerificationResp = new LoginVerificationResp();
        log.info("登陆滑动验证码入参:{}",req);
        //接口地址
        String url = "https://api.253.com/open/txyzm/yzm-v2";
        //请求参数
        Map< String,Object> param = new HashMap<>();
        //应用 id
        param.put("appId", req.getAppId());
        //应用 key
        param.put("appKey", req.getAppKey());
        //验证码密钥
        param.put("AppSecretKey", req.getAppSecretKey());
        //验证码 appId
        param.put("CaptchaAppId", req.getCaptchaAppId());
        //验证票据需要的随机字符串
        param.put("RendStr", req.getRendStr());
        //验证码返回给用户的票据
        param.put("Ticket", req.getTicket());
        //用户操作来源的外网 IP
        param.put("IP", req.getIP());
        try {
            JSONObject jsonObject = HttpsUtilsNew.doPost(url, param);
            log.info("登陆滑动验证码回参:{}",jsonObject);
            if (jsonObject != null) {
                String message = jsonObject.getString("message");
                String code = jsonObject.get("code").toString();
                String chargeStatus = jsonObject.get("chargeStatus").toString();
                String tradeNo = jsonObject.get("tradeNo").toString();
                String data = JSONObject.parseArray(jsonObject.get("data").toString()).get(0).toString();
                loginVerificationResp = JSON.parseObject(data, LoginVerificationResp.class);
                loginVerificationResp.setMessage(message);
                loginVerificationResp.setCode(code);
                loginVerificationResp.setChargeStatus(chargeStatus);
                loginVerificationResp.setTradeNo(tradeNo);
                return loginVerificationResp;
            } else {
                loginVerificationResp.setMessage("登陆滑动验证码失败返回为空");
            }
        } catch (Exception e) {
            log.error("登陆滑动验证码接口失败",e);
        }
        return loginVerificationResp;
    }
}
