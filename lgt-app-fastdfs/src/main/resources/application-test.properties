server.port=2017

eureka.client.serviceUrl.defaultZone=*************************************************/eureka/
eureka.instance.prefer-ip-address=true
eureka.instance.ip-address=************

#################### FastDFS-Client Start ####################
#文件服务器地址
file_server_addr=localhost:80
#最大连接数 并发量较大的话可加大该连接数
max_storage_connection=100

##fastdfs为前缀的是FastDFS的配置
fastdfs.connect_timeout_in_seconds=10
fastdfs.network_timeout_in_seconds=30

fastdfs.charset=UTF-8

# token 防盗链功能
fastdfs.http_anti_steal_token=true
# 密钥
fastdfs.http_secret_key=HandFastDFSToken

# TrackerServer port
fastdfs.http_tracker_http_port=80

## Tracker Server, if more than one, separate with ","
# fastdfs.tracker_servers=***********:22122,***********:22122,***********:22122
#fastdfs.tracker_servers=${tracker_server_addr}:22122
fastdfs.tracker_servers=localhost:22122

#################### FastDFS-Client End ####################
server.tomcat.basedir = ./temp