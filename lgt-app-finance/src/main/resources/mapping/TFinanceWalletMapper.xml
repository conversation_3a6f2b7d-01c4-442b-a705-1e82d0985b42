<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TFinanceWalletMapper">

  <select id="selectWalletByEndUserId" resultType="com.lz.dto.TFinanceWalletDTO">
    <!--
    SELECT
      IFNULL( ws.accountBalance, 0 ) + IFNULL( jw.accountBalance, 0 ) + IFNULL( hx.accountBalance, 0 ) AS account_balance,
      IFNULL( ws.withdrawAmount, 0 ) + IFNULL( jw.withdrawAmount, 0 ) + IFNULL( hx.withdrawAmount, 0 ) AS withdraw_amount
    FROM
      t_end_user_info e
        LEFT JOIN (
        SELECT
          IFNULL( SUM( w.account_balance ), 0 ) accountBalance,
          IFNULL( SUM( w.withdraw_amount ), 0 ) withdrawAmount,
          c.enduser_company_id
        FROM
          `t_wallet` w
            LEFT JOIN ( SELECT * FROM t_carrier_enduser_company_rel WHERE datasouce = 'CD' ) c ON w.carrier_enduser_company_id = c.id
            AND w.datasource = c.datasouce
        WHERE
          w.datasource = 'CD'
          AND w.purse_category = #{purseCategory}
        GROUP BY
          c.enduser_company_id
      ) ws ON e.id = ws.enduser_company_id
        LEFT JOIN (
        SELECT
          IFNULL( SUM( w.account_balance ), 0 ) accountBalance,
          IFNULL( SUM( w.withdraw_amount ), 0 ) withdrawAmount,
          c.end_user_id
        FROM
          t_jd_wallet w
            LEFT JOIN t_end_user_open_role c ON w.open_role_id = c.id
        WHERE
          w.ENABLE = 0
          AND w.data_source = 'CD'
          AND w.purse_category = #{purseCategory}
        GROUP BY
          c.id
      ) jw ON e.id = jw.end_user_id
     LEFT JOIN (
                SELECT
                    IFNULL( SUM( w.account_balance ), 0 ) accountBalance,
                    IFNULL( SUM( w.withdraw_amount ), 0 ) withdrawAmount,
                     c.id as end_user_id
                FROM t_zt_wallet w
                LEFT JOIN t_enduser_account tea ON tea.account_id = w.account_id
                 LEFT JOIN t_end_user_info c ON tea.enduser_id = c.id and c.user_logistics_role like concat('%', #{logisticsRole}, '%' )
         WHERE
                 w.ENABLE = 0
                 and tea.ENABLE = 0
                 and c.ENABLE = 0
                    AND w.data_source = 'CD'
                GROUP BY
                    c.id
    ) hx ON e.id = hx.end_user_id
    WHERE
      e.ENABLE = FALSE
      AND e.id = #{endUserId}
      -->
      SELECT
          IFNULL( SUM( w.account_balance ), 0 ) account_balance,
          IFNULL( SUM( w.withdraw_amount ), 0 ) withdraw_amount,
          c.id as end_user_id
      FROM t_zt_wallet w
      LEFT JOIN t_enduser_account tea ON tea.account_id = w.account_id
      LEFT JOIN t_end_user_info c ON tea.enduser_id = c.id and c.user_logistics_role like concat('%', #{logisticsRole}, '%' )
      WHERE
          w.ENABLE = 0
          and tea.ENABLE = 0
          and c.ENABLE = 0
          AND w.data_source = 'CD'
          AND c.id = #{endUserId}
      GROUP BY
          c.id
  </select>

    <select id="selectCollectedAmount" resultType="com.lz.dto.TFinanceWalletDTO">
        SELECT
            sum( if(toi.pack_status ='0', toi.user_confirm_payment_amount, ifnull(toi.share_payment_amount, toi.user_confirm_payment_amount)) ) collectedAmount
        FROM
            t_order_info toi
        left join t_order_cast_changes tocc on toi.code = tocc.order_code and tocc.data_enable = 1
        WHERE
            toi.order_execute_status = 'O060'
        and toi.order_pay_status not in ( 'M090', 'M080', 'P070', 'M120', 'P110', 'M130')
        <if test="null != loginRole and loginRole == 'CDRIVER'">
            and toi.end_driver_id = #{endUserId} and tocc.capital_transfer_type = 'PAYTODRIVER'
        </if>
        <if test="null != loginRole and loginRole == 'CCAPTAIN'">
            and toi.end_car_owner_id = #{endUserId} and tocc.capital_transfer_type = 'PAYTOCAPTAIN'
        </if>
    </select>

</mapper>