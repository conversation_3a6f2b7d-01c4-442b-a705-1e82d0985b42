package com.lz.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.codingapi.txlcn.tc.annotation.DTXPropagation;
import com.codingapi.txlcn.tc.annotation.LcnTransaction;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lz.api.*;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.JdTradeType;
import com.lz.common.model.CodeEnum;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.DateUtils;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.dao.TFinanceWalletMapper;
import com.lz.dao.TFinanceWithdrawBehalfApplicationMapper;
import com.lz.dao.TFinanceZtWalletChangeLogMapper;
import com.lz.dao.TWalletChangeLogMapper;
import com.lz.dto.TFinanceWalletDTO;
import com.lz.dto.TOrderPayInfoDTO;
import com.lz.model.*;
import com.lz.service.TWalletChangeLogService;
import com.lz.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2019/4/18 - 16:05
 **/
@Service
@Slf4j
public class TWalletChangeLogServiceImpl implements TWalletChangeLogService {

    @Resource
    private TWalletChangeLogMapper tWalletChangeLogMapper;
    @Resource
    private TFinanceZtWalletChangeLogMapper ztWalletChangeLogMapper;
    @Resource
    private TOrderInfoAPI tOrderInfoAPI;
    @Resource
    private TOrderPackInfoAPI tOrderPackInfoAPI;

    @Resource
    private CompanyService companyService;

    @Resource
    private TOrderPayDetailAPI tOrderPayDetailAPI;


    @Resource
    private TServicefeeInfoAPI tServicefeeInfoAPI;

    @Resource
    private TFinanceWalletMapper financeWalletMapper;

    @Resource
    private TFinanceWithdrawBehalfApplicationMapper withdrawBehalfApplicationMapper;

    @Autowired
    private TBankCardAPI bankCardAPI;

    @Autowired
    private TZtAccountOpenInfoAPI ztAccountOpenInfoAPI;
    @Autowired
    private TZtBankUserAPI tZtBankUserAPI;

    @Override
    @LcnTransaction(propagation = DTXPropagation.SUPPORTS)
    @Transactional
    public TWalletChangeLog save(TWalletChangeLog tWalletChangeLog) {
        tWalletChangeLogMapper.insert(tWalletChangeLog);
        return tWalletChangeLog;
    }

    /**
     * 钱包列表
     *
     * @param endUserId
     * @param loginRole
     * @param param
     * @return
     */
    @Override
    public ResultUtil getWalletList(Integer endUserId, String loginRole, TwalletVO param,String logisticsRole) {
//        Page<Object> page = PageHelper.startPage(param.getPage(),param.getSize());
        Map<String, Object> map = new HashMap<String, Object>();
        List<Map<String, Object>> list = tWalletChangeLogMapper.getWalletList(endUserId, loginRole,
                param.getStartTime(), param.getEndTime(),logisticsRole);
        if (param.getPage().equals(1)) {
            Map<String, Object> tWallet = new HashMap<>();;
            TFinanceWalletDTO tFinanceWallet = financeWalletMapper.selectWalletByEndUserId(endUserId, loginRole, logisticsRole);
            if (null != tFinanceWallet) {
                if (null == tFinanceWallet.getWithdrawAmount()) {
                    tWallet.put("withdraw_amount", 0.0);
                } else {
                    tWallet.put("withdraw_amount", tFinanceWallet.getWithdrawAmount());
                }
                if (null == tFinanceWallet.getAccountBalance()) {
                    tWallet.put("account_balance", 0.0);
                } else {
                    tWallet.put("account_balance", tFinanceWallet.getAccountBalance());
                }
                // 待收金额
                TFinanceWalletDTO financeWalletDTO = new TFinanceWalletDTO();
                if (DictEnum.CDRIVER.code.equals(loginRole)) {
                    financeWalletDTO = financeWalletMapper.selectCollectedAmount(endUserId, DictEnum.CDRIVER.code);
                } else if (DictEnum.CCAPTAIN.code.equals(loginRole)) {
                    financeWalletDTO = financeWalletMapper.selectCollectedAmount(endUserId, DictEnum.CCAPTAIN.code);
                }
                if (null == financeWalletDTO || null == financeWalletDTO.getCollectedAmount()) {
                    tWallet.put("collected_amount", 0.0);
                } else {
                    tWallet.put("collected_amount", financeWalletDTO.getCollectedAmount());
                }
                map.put("tWallet", tWallet);
            } else {
                tWallet.put("withdraw_amount", 0.0);
                tWallet.put("account_balance", 0.0);
                // 待收金额
                tWallet.put("collected_amount", 0.0);
                map.put("tWallet", tWallet);
            }
        }
        map.put("listPage", list);
        Integer accountId = CurrentUser.getUserAccountId();
        //0未开户  1 开户成功  2开户中  3开户失败
        ResultUtil resultUtil = ztAccountOpenInfoAPI.ifOpenRole(accountId);
        JSONObject json = (JSONObject) JSON.toJSON(resultUtil.getData());
        map.put("ifOpenRole", json.get("ifOpenRole"));
        map.put("openResponseDesc",json.get("openResponseDesc"));
//        ResultUtil userFeeInfo = tServicefeeInfoAPI.selectEnduserAgreementFee();
//        map.put("userFee",userFeeInfo.getData());


//        ResultUtil resultUtil = new ResultUtil();
//        resultUtil.setData(map);
//        resultUtil.setCode("successs");
//        resultUtil.setCount(page.getTotal());
//        return resultUtil;

        // 查询是否存在提现转账未完成申请
        List<TWithdrawBehalfApplication> tWithdrawBehalfApplication = withdrawBehalfApplicationMapper.selectByEndUserId(CurrentUser.getEndUserId());
        if (null != tWithdrawBehalfApplication && !tWithdrawBehalfApplication.isEmpty()) {
            map.put("ifWithdraw", true);
            map.put("withdrawErrorMessage", "提现处理中，预计于次日到账，请耐心等待。");
        } else {
            map.put("ifWithdraw", false);
        }
        /*  京东已下线
        // 查询银行卡数量
        TEnduserAccount enduserAccount = new TEnduserAccount();
        enduserAccount.setEnduserId(CurrentUser.getEndUserId());
        enduserAccount.setAccountId(CurrentUser.getUserAccountId());
        //京东银行卡数量
        int bankCardNUms = bankCardAPI.selectUniqueBankCardNUms(enduserAccount);*/
        //华夏银行卡数量
        int hxyhBankNum = tZtBankUserAPI.selectByBankNum(CurrentUser.getUserAccountId());
        //map.put("bankNum", bankCardNUms);
        map.put("hxbankNum", hxyhBankNum);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), map);
    }

//    @Override
//    public ResultUtil getTiXianList(Integer endUserId, PageInfo pageInfo) {
//        Page<Object> page = PageHelper.startPage(pageInfo.getPage(),pageInfo.getSize());
//        List<TWallet> list=tWalletChangeLogMapper.getTiXianList(endUserId);
//        ResultUtil resultUtil = new ResultUtil();
//        resultUtil.setData(list);
//        resultUtil.setCount(page.getTotal());
//        return resultUtil;
//    }
//@Override
//public ResultUtil getList(Integer endUserId, String loginRole, TwalletVO param) {
////        Page<Object> page = PageHelper.startPage(param.getPage(),param.getSize());
//    Map<String, Object> map = new HashMap<String, Object>();
//    List<Map<String,Object>> list=tWalletChangeLogMapper.getWalletList(endUserId,loginRole,param.getEndTime(),param.getStartTime());
//    if(param.getPage().equals(1)){
//        Map<String,Object> tWallet=walletService.selectByEndUserId();
//        map.put("tWallet", tWallet);
//    }
//    map.put("listPage", list);
////        ResultUtil resultUtil = new ResultUtil();
////        resultUtil.setData(map);
////        resultUtil.setCode("successs");
////        resultUtil.setCount(page.getTotal());
////        return resultUtil;
//    return new ResultUtil(CodeEnum.SUCCESS.getCode(), map);
//}

    /**
     * @param record
     * @return
     */
    @Override
    public ResultUtil selectByWalletId(TWalletChangeLogVo record) {
        Map<String, Object> map = new HashMap<>();
        //网商类型
        if("ws".equals(record.getZftype())){
            TWalletChangeLog tWalletChangeLog = tWalletChangeLogMapper.selectByPrimaryKey(record.getId());
            String code = tWalletChangeLog.getInnerTradeNo();
            SimpleDateFormat sdf2= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat sdf1= new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy", Locale.ENGLISH);
            if (tWalletChangeLog != null && tWalletChangeLog.getParam1().equals("1")) {
                String tOrderInfoListJson = tOrderInfoAPI.selectByOrderCode(code);//根据code查询订单
                List<TOrderInfo> tOrderInfoList = JSON.parseArray(tOrderInfoListJson, TOrderInfo.class);//json转实体类
                TOrderInfo tOrderInfo = tOrderInfoList.get(0);
                map.put("liushui", tWalletChangeLog.getOuterTradeNo() == null ? "" : tWalletChangeLog.getOuterTradeNo());//流水号
                map.put("amount", tWalletChangeLog.getAmount() == null ? "" : tWalletChangeLog.getAmount().toString());//金额
                map.put("tradeTime", tWalletChangeLog.getTradeTime()== null ? "" :tWalletChangeLog.getTradeTime().getTime());//交易时间
                map.put("fromName", tOrderInfo.getFromName() == null ? "" : tOrderInfo.getFromName());//起始城市
                map.put("endName", tOrderInfo.getEndName() == null ? "" : tOrderInfo.getEndName());//终点城市
                map.put("deliverOrderTime",tOrderInfo.getDeliverOrderTime() == null ? "":tOrderInfo.getDeliverOrderTime());//发单时间
                map.put("code", tOrderInfo.getCode() == null ? "" : tOrderInfo.getCode());//code
                map.put("remark", tOrderInfo.getLineName() == null ? "" : tOrderInfo.getLineName());//code
                map.put("type", tWalletChangeLog.getParam1()== null? "" : tWalletChangeLog.getParam1());// 1单笔支付 2打包支付
                if (tOrderInfo.getOrderExecuteStatus().equals("M100") && tOrderInfo.getOrderPayStatus().equals("M130")) {
                    map.put("state", 1);//已经体现过
                } else {
                    map.put("state", 0);//未体线
                }
            } else {
                TOrderPackInfo t=tOrderPackInfoAPI.selectByCode(code);//根据code查询打包表订单
                map.put("liushui", tWalletChangeLog.getOuterTradeNo() == null ? "" : tWalletChangeLog.getOuterTradeNo());//流水号
                map.put("amount", tWalletChangeLog.getAmount() == null ? "" : tWalletChangeLog.getAmount().toString());//金额
                map.put("tradeTime", tWalletChangeLog.getTradeTime() == null ? "" : tWalletChangeLog.getTradeTime().toString());//交易时间
                map.put("deliverOrderTime", t.getCreateTime() == null ? "" : t.getCreateTime());//发单时间
                map.put("TotalOrders", t.getTotalSelectedOrders() == null ? "" : t.getTotalSelectedOrders());//共有几笔
                map.put("code", t.getCode() == null ? "" : t.getCode());//code
                map.put("remark", "打包运单，共"+t.getTotalSelectedOrders()+"单");//共几单
                map.put("type", tWalletChangeLog.getParam1()== null? "" : tWalletChangeLog.getParam1());// 1单笔支付 2打包支付
            }
            try {
                TOrderPayInfoDTO tOrderPayInfoDTO = tOrderPayDetailAPI.selectBankByCastChanges(code);
                if (null!=tOrderPayInfoDTO && !StringUtils.isEmpty(tOrderPayInfoDTO.getBankNo()) && !StringUtils.isEmpty(tOrderPayInfoDTO.getCardHolder()) ){
                    map.put("bankNo","***************"+tOrderPayInfoDTO.getBankNo().substring(tOrderPayInfoDTO.getBankNo().length() - 4));
                    map.put("cardHolder",tOrderPayInfoDTO.getCardHolder());
                }else {
                    map.put("bankNo",null);
                    map.put("cardHolder",null);
                }
            }catch (Exception e){
                log.info("银行卡信息查询异常",e);
            }
        }else if("jd".equals(record.getZftype())){//京东类型
            TJdWalletChangeLog tJdWalletChangeLog = tWalletChangeLogMapper.selectByJdId(record.getId());
            String orderBusinessCode = tJdWalletChangeLog.getOrderBusinessCode();
            if (tJdWalletChangeLog != null) {
                if (null != record.getItemCode()
                        && (record.getItemCode().equals(DictEnum.CTIXIAN.code)
                            || record.getItemCode().equals(JdTradeType.CDSERVICEFEE.code)
                            || record.getItemCode().equals(JdTradeType.CTXTRANSFERZHICHU.code))
                            || record.getItemCode().equals(JdTradeType.CTXTRANSFERSHOURU.code)
                            || record.getItemCode().equals(JdTradeType.MYACCOUNT_ACCOUNTSERVICE_ZC.code)
                            || record.getItemCode().equals(JdTradeType.NONPERSONALACCOUNT_ACCOUNTSERVICE_ZC.code)
                            || record.getItemCode().equals(JdTradeType.TRANSFERORACCOUNT_ACCOUNTSERVICE_ZC.code)
                            || record.getItemCode().equals(JdTradeType.CTXPAYRETURN.code)) {
                    map.put("liushui", tJdWalletChangeLog.getBankOrderNo() == null ? "" : tJdWalletChangeLog.getBankOrderNo());
                    map.put("amount", tJdWalletChangeLog.getAmount() == null ? "" : tJdWalletChangeLog.getAmount().toString());//金额
                    map.put("tradeTime", tJdWalletChangeLog.getTradeTime()== null ? "" :tJdWalletChangeLog.getTradeTime().getTime());//交易时间
                    map.put("type", tJdWalletChangeLog.getParam1()== null? "" : tJdWalletChangeLog.getParam1());// 1单笔支付 2打包支付
                } else {
                    if (tJdWalletChangeLog.getParam1().equals("2")
                            && (tJdWalletChangeLog.getTradeType().equals("CYUNFEIZH") || tJdWalletChangeLog.getTradeType().equals("CPYUNFEIZH"))) {
                        // 如果是打包单召回
                        TOrderPackInfo tOrderPackInfo = tOrderPackInfoAPI.selectJdPackInfoByOrderBusinessCode(tJdWalletChangeLog.getOrderBusinessCode());
                        map.put("liushui", tJdWalletChangeLog.getBankOrderNo());//流水号
                        map.put("amount", tJdWalletChangeLog.getAmount());//金额
                        map.put("tradeTime", tJdWalletChangeLog.getTradeTime());//交易时间
                        map.put("deliverOrderTime", tOrderPackInfo.getCreateTime());//发单时间
                        map.put("TotalOrders", tOrderPackInfo.getTotalSelectedOrders());//共有几笔
                        map.put("code", tOrderPackInfo.getCode());//code
                        map.put("remark", "打包运单，共" + tOrderPackInfo.getTotalSelectedOrders() + "单");
                        map.put("type", 2);// 1单笔支付 2打包支付
                    } else {
                        TOrderInfo tOrderInfo =  tOrderInfoAPI.selectByOrderBusinessCode(orderBusinessCode);//根据code查询订单
                        map.put("liushui", tJdWalletChangeLog.getBankOrderNo() == null ? "" : tJdWalletChangeLog.getBankOrderNo());//流水号
                        map.put("amount", tJdWalletChangeLog.getAmount() == null ? "" : tJdWalletChangeLog.getAmount().toString());//金额
                        map.put("tradeTime", tJdWalletChangeLog.getTradeTime()== null ? "" :tJdWalletChangeLog.getTradeTime().getTime());//交易时间
                        map.put("fromName", tOrderInfo.getFromName() == null ? "" : tOrderInfo.getFromName());//起始城市
                        map.put("endName", tOrderInfo.getEndName() == null ? "" : tOrderInfo.getEndName());//终点城市
                        map.put("deliverOrderTime",tOrderInfo.getDeliverOrderTime() == null ? "":tOrderInfo.getDeliverOrderTime());//发单时间
                        map.put("code", tOrderInfo.getCode() == null ? "" : tOrderInfo.getCode());//code
                        map.put("remark", tOrderInfo.getLineName() == null ? "" : tOrderInfo.getLineName());//code
                        map.put("type", tJdWalletChangeLog.getParam1()== null? "" : tJdWalletChangeLog.getParam1());// 1单笔支付 2打包支付
                        if (tOrderInfo.getOrderExecuteStatus().equals("M100") && tOrderInfo.getOrderPayStatus().equals("M130")) {
                            map.put("state", 1);//已经体现过
                        } else {
                            map.put("state", 0);//未体线
                        }
                    }
                }
            }
            if (!StringUtils.isEmpty(tJdWalletChangeLog.getBankCardNo()) && !StringUtils.isEmpty(tJdWalletChangeLog.getBankCardName()) ){
                map.put("bankNo","***************"+tJdWalletChangeLog.getBankCardNo().substring(tJdWalletChangeLog.getBankCardNo().length() - 4));
                map.put("cardHolder",tJdWalletChangeLog.getBankCardName());
            }else {
                map.put("bankNo",null);
                map.put("cardHolder",null);
            }
        } else if ("hx".equals(record.getZftype())) {
            TZtWalletChangeLog tZtWalletChangeLog = ztWalletChangeLogMapper.selectByPrimaryKey(record.getId());
            String orderBusinessCode = tZtWalletChangeLog.getOrderBusinessCode();
            if (null != record.getItemCode()
                    && (record.getItemCode().equals(DictEnum.CTIXIAN.code)
                    || record.getItemCode().equals(JdTradeType.CDSERVICEFEE.code)
                    || record.getItemCode().equals(JdTradeType.CTXTRANSFERZHICHU.code))
                    || record.getItemCode().equals(JdTradeType.CTXTRANSFERSHOURU.code)
                    || record.getItemCode().equals(JdTradeType.MYACCOUNT_ACCOUNTSERVICE_ZC.code)
                    || record.getItemCode().equals(JdTradeType.NONPERSONALACCOUNT_ACCOUNTSERVICE_ZC.code)
                    || record.getItemCode().equals(JdTradeType.TRANSFERORACCOUNT_ACCOUNTSERVICE_ZC.code)
                    || record.getItemCode().equals(JdTradeType.CTXPAYRETURN.code)
                    || record.getItemCode().equals(JdTradeType.TOOL_BALANCE_PAY_ZC.code)
                    || record.getItemCode().equals(JdTradeType.TOOL_BALANCE_PAY_SR.code)) {
                map.put("liushui", tZtWalletChangeLog.getOuterTradeNo() == null ? "" : tZtWalletChangeLog.getOuterTradeNo());
                map.put("amount", tZtWalletChangeLog.getAmount() == null ? "" : tZtWalletChangeLog.getAmount().toString());//金额
                map.put("tradeTime", tZtWalletChangeLog.getTradeTime()== null ? "" :tZtWalletChangeLog.getTradeTime().getTime());//交易时间
                map.put("type", tZtWalletChangeLog.getParam1()== null? "" : tZtWalletChangeLog.getParam1());// 1单笔支付 2打包支付
            } else {
                if (tZtWalletChangeLog.getParam1().equals("1")
                        && (tZtWalletChangeLog.getTradeType().equals("CYUNFEIZH") || tZtWalletChangeLog.getTradeType().equals("CPYUNFEIZH"))) {
                    // 如果是打包单召回
                    TOrderPackInfo tOrderPackInfo;
                    log.info("钱包流水信息：{}", JSONUtil.toJsonStr(tZtWalletChangeLog));
                    if (tZtWalletChangeLog.getOrderBusinessCode().startsWith("DB")) {
                        tOrderPackInfo = tOrderPackInfoAPI.selectByVirtualOrderNo(tZtWalletChangeLog.getOrderBusinessCode());
                    } else {
                        tOrderPackInfo = tOrderPackInfoAPI.selectPackInfoByOrderBusinessCodePayTime(orderBusinessCode, tZtWalletChangeLog.getTradeNo());
                    }
                    map.put("liushui", tZtWalletChangeLog.getOuterTradeNo() == null ? "" : tZtWalletChangeLog.getOuterTradeNo());//流水号
                    map.put("amount", tZtWalletChangeLog.getAmount() == null ? "" : tZtWalletChangeLog.getAmount().toString());//金额
                    map.put("tradeTime", tZtWalletChangeLog.getTradeTime()== null ? "" :tZtWalletChangeLog.getTradeTime().getTime());//交易时间
                    map.put("deliverOrderTime", tOrderPackInfo.getCreateTime());//发单时间
                    map.put("TotalOrders", tOrderPackInfo.getTotalSelectedOrders());//共有几笔
                    map.put("code", tOrderPackInfo.getCode());//code
                    map.put("remark", "打包运单，共" + tOrderPackInfo.getTotalSelectedOrders() + "单");
                    map.put("type", 2);// 1单笔支付 2打包支付
                } else {
                    TOrderInfo tOrderInfo =  tOrderInfoAPI.selectByOrderBusinessCode(orderBusinessCode);//根据code查询订单
                    map.put("liushui", tZtWalletChangeLog.getOuterTradeNo() == null ? "" : tZtWalletChangeLog.getOuterTradeNo());//流水号
                    map.put("amount", tZtWalletChangeLog.getAmount() == null ? "" : tZtWalletChangeLog.getAmount().toString());//金额
                    map.put("tradeTime", tZtWalletChangeLog.getTradeTime()== null ? "" :tZtWalletChangeLog.getTradeTime().getTime());//交易时间
                    map.put("fromName", tOrderInfo.getFromName() == null ? "" : tOrderInfo.getFromName());//起始城市
                    map.put("endName", tOrderInfo.getEndName() == null ? "" : tOrderInfo.getEndName());//终点城市
                    map.put("deliverOrderTime",tOrderInfo.getDeliverOrderTime() == null ? "":tOrderInfo.getDeliverOrderTime());//发单时间
                    map.put("code", tOrderInfo.getCode() == null ? "" : tOrderInfo.getCode());//code
                    map.put("remark", tOrderInfo.getLineName() == null ? "" : tOrderInfo.getLineName());//code
                    map.put("type", 1);// 1单笔支付 2打包支付
                    if (tOrderInfo.getOrderExecuteStatus().equals("M100") && tOrderInfo.getOrderPayStatus().equals("M130")) {
                        map.put("state", 1);//已经体现过
                    } else {
                        map.put("state", 0);//未体线
                    }
                }
            }
            if (!StringUtils.isEmpty(tZtWalletChangeLog.getCardNo()) && !StringUtils.isEmpty(tZtWalletChangeLog.getCardHolder()) ){
                map.put("bankNo","***************"+tZtWalletChangeLog.getCardNo().substring(tZtWalletChangeLog.getCardNo().length() - 4));
                map.put("cardHolder",tZtWalletChangeLog.getCardHolder());
            }else {
                map.put("bankNo",null);
                map.put("cardHolder",null);
            }
        }

        return new ResultUtil(CodeEnum.SUCCESS.getCode(), map);
    }

    @Override
    public ResultUtil companyCapitalFlow(CompanyCapitalFlowVo param) {
        //累计充值
        Double hjcz = 0.0;
        //累计提现
        Double hjtx = 0.0;
        //累计运费支出
        Double hjyfzc = 0.0;
        //累计调度费支出
        Double hjddfzc = 0.0;
        //累计调度费召回
        Double hjddfzh = 0.0;
        //累计运费召回
        Double hjyfzh = 0.0;
        //累计退款
        Double hjtk = 0.0;
        List<CompanyCapitalFlow> listHj = tWalletChangeLogMapper.companyCapitalFlow(param);

        for(CompanyCapitalFlow ccf :listHj){
            //累计充值
            if("BCHONGZHI".equals(ccf.getTradeTypeCode())){
                if(ccf.getAmount()!=null&&!"".equals(ccf.getAmount())){
                    hjcz+=Double.parseDouble(ccf.getAmount().toString());
                }
            }
            //累计提现
            if("BTIXIAN".equals(ccf.getTradeTypeCode())){
                if(ccf.getAmount()!=null&&!"".equals(ccf.getAmount())){
                    hjtx+=Double.parseDouble(ccf.getAmount().toString());
                }
            }
            //累计运费支出 + 预付款运费
            if("BYUNFEIZHICHU".equals(ccf.getTradeTypeCode()) || "CYFKZHICHU".equals(ccf.getTradeTypeCode())){
                if(ccf.getAmount()!=null&&!"".equals(ccf.getAmount())){
                    hjyfzc+=Double.parseDouble(ccf.getAmount().toString());
                }
            }
            //累计调度费支出 +预付款调度费
            if("BDIAODUFEIZHICHU".equals(ccf.getTradeTypeCode())||"CYFKDDFZHICHU".equals(ccf.getTradeTypeCode())){
                if(ccf.getAmount()!=null&&!"".equals(ccf.getAmount())){
                    hjddfzc+=Double.parseDouble(ccf.getAmount().toString());
                }
            }
            //累计调度费召回
            if("BDIAODUFEIZH".equals(ccf.getTradeTypeCode())){
                if(ccf.getAmount()!=null&&!"".equals(ccf.getAmount())){
                    hjddfzh+=Double.parseDouble(ccf.getAmount().toString());
                }
            }
            //累计运费召回
            if("BYUNFEIZH".equals(ccf.getTradeTypeCode())){
                if(ccf.getAmount()!=null&&!"".equals(ccf.getAmount())){
                    hjyfzh+=Double.parseDouble(ccf.getAmount().toString());
                }
            }
            //累计退款
            if("BTUIKUAN".equals(ccf.getTradeTypeCode())){
                if(ccf.getAmount()!=null&&!"".equals(ccf.getAmount())){
                    hjtk+=Double.parseDouble(ccf.getAmount().toString());
                }
            }

        }
        Page<List<CompanyCapitalFlow>> page = PageHelper.startPage(param.getPage(), param.getSize());
        if (param.getTradeTime() != null) {
            param.setStartTime(param.getTradeTime()[0]);
            param.setEndTime(param.getTradeTime()[1]);
        }
        List<CompanyCapitalFlow> list = tWalletChangeLogMapper.companyCapitalFlow(param);
        for(CompanyCapitalFlow map :list){
            if(map.getTradeTime()!=null&&!"".equals(map.getTradeTime())){
                map.setTradeTimeStr(DateUtils.formatDateTime((Date)map.getTradeTime()));
            }
            if("BDIAODUFEIZH".equals(map.getTradeTypeCode()) || "BYUNFEIZH".equals(map.getTradeTypeCode())){
               String zc =  map.getThridParySubAccountZC();
               String zr = map.getThridParySubAccountZR();
               map.setThridParySubAccountZC(zr);
               map.setThridParySubAccountZR(zc);
            }
        }
        TCompanyInfoVo record = new TCompanyInfoVo();
        record.setCarrierId(Integer.parseInt(param.getCarrierId()));
        record.setCompanyId(Integer.parseInt(param.getCompanyId()));
        TCompanyInfoVo tCompanyInfoVo = companyService.selectByCarrierIdAnrCompanyId(record);
        Map<String,Object> resp = new HashMap();
        resp.put("hjcz",hjcz);//累计充值
        resp.put("hjtx",hjtx);//累计提现
        resp.put("hjyfzc",hjyfzc);//累计运费支出
        resp.put("hjddfzc",hjddfzc);//累计调度费支出
        resp.put("hjddfzh",hjddfzh);//累计调度费召回
        resp.put("hjyfzh",hjyfzh);//累计运费召回
        resp.put("hjtk",hjtk);//累计退款
        resp.put("carrierName",tCompanyInfoVo.getCarrierName());//承运方名称
        resp.put("companyName",tCompanyInfoVo.getCompanyName());//企业名称
        resp.put("data",list);
        return ResultUtil.ok(resp, page.getTotal());
    }

    @Override
    public ResultUtil driverCapitalFlow(CompanyCapitalFlowVo param) {
        Page<List<DriverCapitalFlow>> page = PageHelper.startPage(param.getPage(), param.getSize());
        if (param.getTradeTime() != null) {
            param.setStartTime(param.getTradeTime()[0]);
            param.setEndTime(param.getTradeTime()[1]);
        }
        List<DriverCapitalFlow> list = tWalletChangeLogMapper.driverCapitalFlow(param);
        for(DriverCapitalFlow map :list) {
            if ("CTIXIAN".equals(map.getTradeType())||"CYUNFEIZH".equals(map.getTradeType())||"CYFKTX".equals(map.getTradeType())) {
                String zc = map.getThridParySubAccountZC();
                String zr = map.getThridParySubAccountZR();
                map.setThridParySubAccountZC(zr);
                map.setThridParySubAccountZR(zc);
            }
            if(map.getTradeTimeDate()!=null){
                map.setTradeTime(DateUtils.formatDateTime((Date) map.getTradeTimeDate()));
            }
        }
        return ResultUtil.ok(list, page.getTotal());
    }


    /**
     *  @author: dingweibo
     *  @Date: 2019/9/29 10:59
     *  @Description: 承运方钱包明细
     */
    @Override
    public ResultUtil carrierCapitalFlow(CompanyCapitalFlowVo param) {
        Page<List<CarrierCapitalFlow>> page = PageHelper.startPage(param.getPage(), param.getSize());
        if (param.getTradeTime() != null) {
            param.setStartTime(param.getTradeTime()[0]);
            param.setEndTime(param.getTradeTime()[1]);
        }
        List<CarrierCapitalFlow> list = tWalletChangeLogMapper.carrierCapitalFlow(param);
        for(CarrierCapitalFlow map :list) {
            if ("PYUNFEIZH".equals(map.getTradeTypeCode())|| "PTIXIAN".equals(map.getTradeTypeCode())) {
                String zc = map.getThridParySubAccountZC();
                String zr = map.getThridParySubAccountZR();
                map.setThridParySubAccountZC(zr);
                map.setThridParySubAccountZR(zc);
            }
            if(map.getTradeTime()!=null&&!"".equals(map.getTradeTime())){
                map.setTradeTimeStr(DateUtils.formatDateTime((Date)map.getTradeTime()));
            }
        }
        return ResultUtil.ok(list, page.getTotal());
    }
    /**
     *  @author: dingweibo
     *  @Date: 2019/6/25 20:08
     *  @Description:累计充值
     */
    @Override
    public TWalletChangeLog selectAccumulativeRecharge(Integer companyId) {

        return tWalletChangeLogMapper.selectAccumulativeRecharge(companyId);
    }

    /**
     *  @author: dingweibo
     *  @Date: 2019/6/29 14:26
     *  @Description: 根据银行卡号查询当前月累计提现
     */
    @Override
    public TWalletChangeLog selectSumCashByBankNo(String bankNo) {
        TWalletChangeLogVo tWalletChangeLogVo = new TWalletChangeLogVo();
        tWalletChangeLogVo.setBankNo(bankNo);//银行卡号
        try {
            tWalletChangeLogVo.setStartTime(DateUtils.forDateStart());//获取当前月第一天开始时间
            tWalletChangeLogVo.setEndTime(DateUtils.forDateEnd());//获取当前月最后一天结束时间
        } catch (ParseException e) {
            log.error("获取日期错误！");
        }
        return tWalletChangeLogMapper.selectSumCashByBankNo(tWalletChangeLogVo);
    }
}
