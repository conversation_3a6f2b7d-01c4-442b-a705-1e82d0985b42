package com.lz.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lz.api.TEndCarInfoAPI;
import com.lz.api.TEndSUserInfoAPI;
import com.lz.api.TOrderAuditLogAPI;
import com.lz.api.TOrderInfoAPI;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.CodeEnum;
import com.lz.common.model.sort.RECIEVEORDER;
import com.lz.common.util.*;
import com.lz.dao.TInvoiceInfoMapper;
import com.lz.dao.TInvoiceStatusMapper;
import com.lz.dao.TOrderAccountCheckInvoiceLogMapper;
import com.lz.dto.OrderCheckInvoiceSumDTO;
import com.lz.example.TOrderAccountCheckInvoiceLogExample;
import com.lz.model.*;
import com.lz.service.TOrderAccountCheckInvoiceLogService;
import com.lz.vo.TInvoiceInfoVo;
import com.lz.vo.TOrderAccountCheckInvoiceLogDto;
import com.lz.vo.TOrderAccountCheckInvoiceLogVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * author dingweibo
 * 运单对账及开票记录子表
 */
@Service("tOrderAccountCheckInvoiceLogService")
@Slf4j
public class TOrderAccountCheckInvoiceLogServiceImpl implements TOrderAccountCheckInvoiceLogService {

    @Resource
    private TOrderAccountCheckInvoiceLogMapper tOrderAccountCheckInvoiceLogMapper;

    @Resource
    private TInvoiceInfoMapper invoiceInfoMapper;

    @Resource
    private TInvoiceStatusMapper tInvoiceStatusMapper;

    @Resource
    private TOrderInfoAPI tOrderInfoAPI;

    @Resource
    private TEndSUserInfoAPI tEndSUserInfoAPI;

    @Resource
    private TEndCarInfoAPI tEndCarInfoAPI;

    @Resource
    private TOrderAuditLogAPI tOrderAuditLogAPI;


    /**
     * @Description
     * <AUTHOR>
     * @Date   2019/6/24 16:17
     * @Param
     * @Return     未开票发票列表
     * @Exception
     *
     */
    @Override
    public ResultUtil selectByPage(TOrderAccountCheckInvoiceLogVo tOrderAccountCheckInvoiceLogVo) {
        if(null != tOrderAccountCheckInvoiceLogVo && null != tOrderAccountCheckInvoiceLogVo.getNodeType() && "NOORDERCHECK".equals(tOrderAccountCheckInvoiceLogVo.getNodeType())){
            tOrderAccountCheckInvoiceLogVo.setNoNodeType("NOORDERCHECK");
            tOrderAccountCheckInvoiceLogVo.setNodeType(null);
        }
        /*if (null != tOrderAccountCheckInvoiceLogVo.getLevelName() && null != tOrderAccountCheckInvoiceLogVo.getLevel()
                && StringUtils.isNotBlank(tOrderAccountCheckInvoiceLogVo.getLevelName())) {
            levelSortNew(tOrderAccountCheckInvoiceLogVo);
        }*/
        Integer companyIdInteger = null;
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        if (null != userCompanyId && userCompanyId.size() > 0){
            String companyId = userCompanyId.get(0);
            companyIdInteger = Integer.valueOf(companyId);
            tOrderAccountCheckInvoiceLogVo.setCompanyId(companyIdInteger);
        }
        //发布时间
        if(tOrderAccountCheckInvoiceLogVo.getDeliverOrderTimeArry()!= null){
            tOrderAccountCheckInvoiceLogVo.setDeliverOrderTimeStar(tOrderAccountCheckInvoiceLogVo.getDeliverOrderTimeArry()[0]);
            tOrderAccountCheckInvoiceLogVo.setDeliverOrderTimeEnd(tOrderAccountCheckInvoiceLogVo.getDeliverOrderTimeArry()[1]);
        }
        //运单完成时间
        if(tOrderAccountCheckInvoiceLogVo.getOrderFinishTimeArry()!= null){
            tOrderAccountCheckInvoiceLogVo.setOrderFinishTimeStar(tOrderAccountCheckInvoiceLogVo.getOrderFinishTimeArry()[0]);
            tOrderAccountCheckInvoiceLogVo.setOrderFinishTimeEnd(tOrderAccountCheckInvoiceLogVo.getOrderFinishTimeArry()[1]);
        }
        //装货磅单时间
        if(tOrderAccountCheckInvoiceLogVo.getWeightNotesTimeArray()!= null){
            tOrderAccountCheckInvoiceLogVo.setDeliverWeightNotesTimeStar(tOrderAccountCheckInvoiceLogVo.getWeightNotesTimeArray()[0]);
            tOrderAccountCheckInvoiceLogVo.setDeliverWeightNotesTimeEnd(tOrderAccountCheckInvoiceLogVo.getWeightNotesTimeArray()[1]);
        }
        //卸货榜单时间
        if(tOrderAccountCheckInvoiceLogVo.getReceiveWeightNotesTimeArray()!=null){
            tOrderAccountCheckInvoiceLogVo.setReceiveWeightNotesTimeStar(tOrderAccountCheckInvoiceLogVo.getReceiveWeightNotesTimeArray()[0]);
            tOrderAccountCheckInvoiceLogVo.setReceiveWeightNotesTimeEnd(tOrderAccountCheckInvoiceLogVo.getReceiveWeightNotesTimeArray()[1]);
        }

        /*TOrderAccountCheckInvoiceLogDto tOrderAccountCheckInvoiceLogDto = new TOrderAccountCheckInvoiceLogDto();
        BeanUtils.copyProperties(tOrderAccountCheckInvoiceLogVo,tOrderAccountCheckInvoiceLogDto);

        Map<String, Object> listhj = tOrderAccountCheckInvoiceLogMapper.selectByList(tOrderAccountCheckInvoiceLogDto);*/
        //列表
        Page<Object> objectPage = PageHelper.startPage(tOrderAccountCheckInvoiceLogVo.getPage(), tOrderAccountCheckInvoiceLogVo.getSize());
        List<TOrderAccountCheckInvoiceLogVo> list = tOrderAccountCheckInvoiceLogMapper.selectByPage(tOrderAccountCheckInvoiceLogVo);
        for(TOrderAccountCheckInvoiceLogVo oaci:list){
            if(null != oaci.getUserConfirmPaymentAmount() && null != oaci.getInsuredAmount()){
                if(oaci.getUserConfirmPaymentAmount().compareTo(BigDecimal.ZERO) >= 0 && oaci.getInsuredAmount().compareTo(BigDecimal.ZERO) >= 0){
                    oaci.setUserConfirmPaymentAmount(oaci.getUserConfirmPaymentAmount().subtract(oaci.getInsuredAmount()).setScale(2, RoundingMode.HALF_UP));
                }
            }
            if("DWUWU".equals(oaci.getCheckFlag())){
                oaci.setCheckFlag("无误");
            }else if("DYZY".equals(oaci.getCheckFlag())){
                oaci.setCheckFlag("有争议");
            }else if("DXYZCHD".equals(oaci.getCheckFlag())){
                oaci.setCheckFlag("需再次核对");
            }

            if("HWUWU".equals(oaci.getVerifyFlag())){
                oaci.setVerifyFlag("无误");
            }else if("HYZY".equals(oaci.getVerifyFlag())){
                oaci.setVerifyFlag("有争议");
            }else if("HXYZCHD".equals(oaci.getVerifyFlag())){
                oaci.setVerifyFlag("需再次核对");
            }

            if("DDZWC".equals(oaci.getCheckStatus())){
                oaci.setCheckStatus("对账完成");
                oaci.setCheckStatusStr("DDZWC");
            }else if("DDZZ".equals(oaci.getCheckStatus())){
                oaci.setCheckStatus("对账中");
                oaci.setCheckStatusStr("DDZZ");
            }else{
                oaci.setCheckStatus("未对账");
            }
            if(oaci.getUserConfirmPaymentAmount()==null || "".equals(oaci.getUserConfirmPaymentAmount())){
                oaci.setUserConfirmPaymentAmount(new BigDecimal(0));
            }


            if("NEWNODE".equals(oaci.getAuditStatus())){
                oaci.setNotMeetInvoiceCondition("运单未审核");
            }else if("PASSNODE".equals(oaci.getAuditStatus())){
                oaci.setNotMeetInvoiceCondition("运单审核通过");
            }else if("NOTPASSNODE".equals(oaci.getAuditStatus())){
                oaci.setNotMeetInvoiceCondition("运单审核不通过");
            }

            if(oaci.getDeliverOrderTime()!=null){
                oaci.setDeliverOrderTimeStr(DateUtils.dateFormat(oaci.getDeliverOrderTime()));
            }
            if(oaci.getOrderFinishTime()!=null){
                oaci.setOrderFinishTimeStr(DateUtils.dateFormat(oaci.getOrderFinishTime()));
            }
            //无装货磅单时间取装货时间
            if(oaci.getDeliverWeightNotesTime()!=null){
                oaci.setDeliverWeightNotesTimeStr(DateUtils.dateFormat(oaci.getDeliverWeightNotesTime()));
            }
            //无卸货磅单时间取卸货时间
            if(oaci.getReceiveWeightNotesTime()!=null){
                oaci.setReceiveWeightNotesTimeStr(DateUtils.dateFormat(oaci.getReceiveWeightNotesTime()));
            }
            if(oaci.getReceiveOrderTime()!=null){

                oaci.setReceiveOrderTimeStr(DateUtils.dateFormat(oaci.getReceiveOrderTime()));
            }
        }
        /*if(list.size()>0){
            if(null!=listhj &&!"".equals(listhj)){
                list.get(0).setDischargeweightHj((Double) listhj.get("dischargeweightHj"));//实收重量合计
                list.get(0).setUserConfirmPaymentAmountHj(new BigDecimal(listhj.get("userConfirmPaymentAmountHjDou").toString()));//运费合计
                list.get(0).setDispatchFeeHj(new BigDecimal(listhj.get("dispatchFeeHjDou").toString()));//调度费合计
            }
        }*/
        return new ResultUtil(CodeEnum.SUCCESS.getCode(),objectPage,objectPage.getTotal());
    }

    @Override
    public ResultUtil selectSum(TOrderAccountCheckInvoiceLogVo tOrderAccountCheckInvoiceLogVo) {
        if(null != tOrderAccountCheckInvoiceLogVo && null != tOrderAccountCheckInvoiceLogVo.getNodeType() &&
                "NOORDERCHECK".equals(tOrderAccountCheckInvoiceLogVo.getNodeType())){
            tOrderAccountCheckInvoiceLogVo.setNoNodeType("NOORDERCHECK");
            tOrderAccountCheckInvoiceLogVo.setNodeType(null);
        }
        /*if (null != tOrderAccountCheckInvoiceLogVo.getLevelName() && null != tOrderAccountCheckInvoiceLogVo.getLevel()
                && StringUtils.isNotBlank(tOrderAccountCheckInvoiceLogVo.getLevelName())) {
            levelSortNew(tOrderAccountCheckInvoiceLogVo);
        }*/
        Integer companyIdInteger = null;
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        if (null != userCompanyId && userCompanyId.size() > 0){
            String companyId = userCompanyId.get(0);
            companyIdInteger = Integer.valueOf(companyId);
            tOrderAccountCheckInvoiceLogVo.setCompanyId(companyIdInteger);
        }
        //发布时间
        if(tOrderAccountCheckInvoiceLogVo.getDeliverOrderTimeArry()!= null){
            tOrderAccountCheckInvoiceLogVo.setDeliverOrderTimeStar(tOrderAccountCheckInvoiceLogVo.getDeliverOrderTimeArry()[0]);
            tOrderAccountCheckInvoiceLogVo.setDeliverOrderTimeEnd(tOrderAccountCheckInvoiceLogVo.getDeliverOrderTimeArry()[1]);
        }
        //运单完成时间
        if(tOrderAccountCheckInvoiceLogVo.getOrderFinishTimeArry()!= null){
            tOrderAccountCheckInvoiceLogVo.setOrderFinishTimeStar(tOrderAccountCheckInvoiceLogVo.getOrderFinishTimeArry()[0]);
            tOrderAccountCheckInvoiceLogVo.setOrderFinishTimeEnd(tOrderAccountCheckInvoiceLogVo.getOrderFinishTimeArry()[1]);
        }
        //装货磅单时间
        if(tOrderAccountCheckInvoiceLogVo.getWeightNotesTimeArray()!= null){
            tOrderAccountCheckInvoiceLogVo.setDeliverWeightNotesTimeStar(tOrderAccountCheckInvoiceLogVo.getWeightNotesTimeArray()[0]);
            tOrderAccountCheckInvoiceLogVo.setDeliverWeightNotesTimeEnd(tOrderAccountCheckInvoiceLogVo.getWeightNotesTimeArray()[1]);
        }
        //卸货榜单时间
        if(tOrderAccountCheckInvoiceLogVo.getReceiveWeightNotesTimeArray()!=null){
            tOrderAccountCheckInvoiceLogVo.setReceiveWeightNotesTimeStar(tOrderAccountCheckInvoiceLogVo.getReceiveWeightNotesTimeArray()[0]);
            tOrderAccountCheckInvoiceLogVo.setReceiveWeightNotesTimeEnd(tOrderAccountCheckInvoiceLogVo.getReceiveWeightNotesTimeArray()[1]);
        }

        TOrderAccountCheckInvoiceLogDto tOrderAccountCheckInvoiceLogDto = new TOrderAccountCheckInvoiceLogDto();
        BeanUtils.copyProperties(tOrderAccountCheckInvoiceLogVo,tOrderAccountCheckInvoiceLogDto);

        OrderCheckInvoiceSumDTO sumDTO = tOrderAccountCheckInvoiceLogMapper.selectSum(tOrderAccountCheckInvoiceLogDto);
        if(null != sumDTO){
            if(sumDTO.getUserConfirmPaymentAmountHj() >= 0 && sumDTO.getInsuredAmountSum() >= 0){
                double totalFreightCharges = sumDTO.getUserConfirmPaymentAmountHj() - sumDTO.getInsuredAmountSum();
                BigDecimal bigDecimal = new BigDecimal(totalFreightCharges);
                BigDecimal setScale = bigDecimal.setScale(2, RoundingMode.HALF_UP);
                sumDTO.setTotalFreightCharges(Double.parseDouble(String.valueOf(setScale)));
            }
        }
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), sumDTO);
    }

    private  TOrderAccountCheckInvoiceLogVo levelSortNew(TOrderAccountCheckInvoiceLogVo search) {
        boolean validEnum = EnumUtils.isValidEnum(RECIEVEORDER.class, search.getLevelName());
        if (validEnum) {
            if (search.getLevel() == 1) {
                search.setOrderName("asc");
            } else if (search.getLevel() == 0) {
                search.setOrderName("desc");
            }
        } else {
            search.setLevel(0);
            search.setLevelName("createTime");
            search.setOrderName("desc");
        }
        return search;
    }
    /**
     *  对账人修改状态
     * @return
     */
    @Override
    @Transactional
    public ResultUtil updateCheck(List<TOrderAccountCheckInvoiceLogVo> listvo) {
        for(TOrderAccountCheckInvoiceLogVo vo : listvo){
            boolean flag = true;
            TOrderAccountCheckInvoiceLogExample example = new TOrderAccountCheckInvoiceLogExample();
            TOrderAccountCheckInvoiceLogExample.Criteria cr = example.createCriteria();
            cr.andOrderCodeEqualTo(vo.getOrderCode());
            cr.andEnableEqualTo(false);
            List<TOrderAccountCheckInvoiceLog> list = tOrderAccountCheckInvoiceLogMapper.selectByExample(example);
            TOrderAccountCheckInvoiceLog record =null;
            if(list.size()>0){
                record = list.get(0);
                record.setOrderBusinessCode(vo.getOrderBusinessCode());
                record.setOrderCode(vo.getOrderCode());
            }else{
                flag = false;
                record = new TOrderAccountCheckInvoiceLog();
                record.setCode(IdWorkerUtil.getInstance().nextId());
                record.setEnable(false);
                record.setOrderBusinessCode(vo.getOrderBusinessCode());
                record.setOrderCode(vo.getOrderCode());

            }
            if("0".equals(vo.getBtnType())){//0 无误
                record.setDifferAmount(new BigDecimal(0));
                record.setChecker(CurrentUser.getCurrentUserID().toString());
                record.setCheckTime(new Date());
                record.setCheckFlag("DWUWU");
            }else if("1".equals(vo.getBtnType())){//1 需要再次核对
                record.setChecker(CurrentUser.getCurrentUserID().toString());
                record.setCheckTime(new Date());
                record.setCheckFlag("DXYZCHD");
            }else if("2".equals(vo.getBtnType())){//2 有争议
                record.setChecker(CurrentUser.getCurrentUserID().toString());
                record.setCheckTime(new Date());
                record.setCheckFlag("DYZY");
            }
            if(!"HWUWU".equals(record.getVerifyFlag()) || !"DWUWU".equals(record.getCheckFlag())){
                record.setCheckStatus("DDZZ");
            }else if("DWUWU".equals(record.getCheckFlag())&&"HWUWU".equals(record.getVerifyFlag())){
                record.setCheckStatus("DDZWC");
            }
            if(flag){
                tOrderAccountCheckInvoiceLogMapper.updateByPrimaryKeySelective(record);
            }else{
                tOrderAccountCheckInvoiceLogMapper.insertSelective(record);

            }
        }
        return ResultUtil.ok();
    }

    /**
     *核账人修改状态
     * @param listvo
     * @return
     */
    @Override
    @Transactional
    public ResultUtil updateVerify(List<TOrderAccountCheckInvoiceLogVo> listvo) {
        for(TOrderAccountCheckInvoiceLogVo vo:listvo){
            boolean flag = true;
            TOrderAccountCheckInvoiceLogExample example = new TOrderAccountCheckInvoiceLogExample();
            TOrderAccountCheckInvoiceLogExample.Criteria cr = example.createCriteria();
            cr.andOrderCodeEqualTo(vo.getOrderCode());
            cr.andEnableEqualTo(false);
            List<TOrderAccountCheckInvoiceLog> list = tOrderAccountCheckInvoiceLogMapper.selectByExample(example);
            TOrderAccountCheckInvoiceLog record =null;
            if(list.size()>0){
                record = list.get(0);
                record.setOrderBusinessCode(vo.getOrderBusinessCode());
                record.setOrderCode(vo.getOrderCode());
            }else{
                flag = false;
                record = new TOrderAccountCheckInvoiceLog();
                record.setCode(IdWorkerUtil.getInstance().nextId());
                record.setEnable(false);
                record.setOrderBusinessCode(vo.getOrderBusinessCode());
                record.setOrderCode(vo.getOrderCode());

            }
            if("0".equals(vo.getBtnType())){//0 无误
                record.setDifferAmount(new BigDecimal(0));
                record.setVerifyPerson(CurrentUser.getCurrentUserID().toString());
                record.setVerifyTime(new Date());
                record.setVerifyFlag("HWUWU");
                //添加开票状态表 子状态
                TInvoiceStatus invoiceStatus = new TInvoiceStatus();
                invoiceStatus.setCode(IdWorkerUtil.getInstance().nextId());
                invoiceStatus.setInvoiceCode(record.getInvoiceCode());
                invoiceStatus.setInvoiceBusinessCode(record.getInvoiceBusinessCode());
                invoiceStatus.setEnable(false);
                invoiceStatus.setOperSubStatus("DDZWC");
                tInvoiceStatusMapper.insertSelective(invoiceStatus);
            }else if("1".equals(vo.getBtnType())){//1 需要再次核对
                record.setVerifyPerson(CurrentUser.getCurrentUserID().toString());
                record.setVerifyTime(new Date());
                record.setVerifyFlag("HXYZCHD");
            }else if("2".equals(vo.getBtnType())){//2 有争议
                record.setVerifyPerson(CurrentUser.getCurrentUserID().toString());
                record.setVerifyTime(new Date());
                record.setVerifyFlag("HYZY");
            }

            if(!"DWUWU".equals(record.getCheckFlag())||!"HWUWU".equals(record.getVerifyFlag())){
                record.setCheckStatus("DDZZ");
            }else if("DWUWU".equals(record.getCheckFlag())&&"HWUWU".equals(record.getVerifyFlag())){
                record.setCheckStatus("DDZWC");
            }
            if(flag){
                tOrderAccountCheckInvoiceLogMapper.updateByPrimaryKeySelective(record);
            }else{
                tOrderAccountCheckInvoiceLogMapper.insertSelective(record);
            }
        }
        return ResultUtil.ok();
    }

    /**
     * 申请开票
     * @param vo
     * @return
     */
    @Override
    @Transactional
    public ResultUtil applyInvoice(TInvoiceInfoVo vo) {
        try{
            ResultUtil resultUtil = new ResultUtil();
            Double sumDeliverWeightNotesWeight = 0.0;//运输原发量
            Double sumReceiveWeightNotesWeight = 0.0;//运输实收重量
            Double sumTotalFee = 0.0;//选中单据运费金额

            Double totalDispatchFee = 0.0;//选中调度费
            TInvoiceInfo invoiceInfo = new TInvoiceInfo();
            BeanUtils.copyProperties(vo,invoiceInfo);
            invoiceInfo.setInvoiceCode(IdWorkerUtil.getInstance().nextId());//表业务Id

            String random = RandomUtil.randomString(3, 10);
            String  invoiceBusinessCode = DateUtils.getDateRandom();

            invoiceInfo.setInvoiceBusinessCode(invoiceBusinessCode+random);//开票单据号
            invoiceInfo.setInvoiceNumber(vo.getOrderCodeList().size());//选中单据数
            //运单业务id集合
            List<String> orderCodeList = new ArrayList<String>();

            //分割多次请求feig 每次100个运单
            SplitAryUtil splitAryUtil = new SplitAryUtil();
            int splitSize = 100;//分割的块大小
            Object[] subAry = splitAryUtil.splitList(vo.getOrderCodeList(), splitSize);//分割后的子块数组
            List<TOrderInfo> orderInfoList = new ArrayList<TOrderInfo>();
            for(Object obj: subAry){
                List<TOrderInfo> infoList = tOrderInfoAPI.selectByOrderCodeList(JSONObject.toJSONString(obj));
                orderInfoList.addAll(infoList);
            }
            boolean ydzt = true;
            boolean auditFalg = false;
             String auditMsg = "";
            boolean auditFalg3 = false;
            String auditMsg3 = "";
            for(TOrderInfo er : orderInfoList){
                orderCodeList.add(er.getCode());
            }
            //运单审核集合
            //分割多次请求feig 每次100个运单
            SplitAryUtil splitAryUtil2 = new SplitAryUtil();
            int splitSize2 = 100;//分割的块大小
            Object[] subAry2 = splitAryUtil2.splitList(orderCodeList, splitSize2);//分割后的子块数组
            List<TOrderAuditLog> tOrderAuditLogList = new ArrayList<TOrderAuditLog>();
            for(Object obj: subAry2){
                List<TOrderAuditLog> auditLogList = tOrderAuditLogAPI.selectByCodeListFeign(JSONObject.toJSONString(obj));
                tOrderAuditLogList.addAll(auditLogList);
            }
            log.info("查询运单审核集合"+JSONObject.toJSONString(tOrderAuditLogList));
            TOrderAccountCheckInvoiceLogExample example2 = new TOrderAccountCheckInvoiceLogExample();
            TOrderAccountCheckInvoiceLogExample.Criteria cr2 = example2.createCriteria();
            cr2.andOrderCodeIn(vo.getOrderCodeList());
            cr2.andEnableEqualTo(false);
            List<TOrderAccountCheckInvoiceLog> tOrderAccountCheckInvoiceLogList = tOrderAccountCheckInvoiceLogMapper.selectByExample(example2);
            if(tOrderAccountCheckInvoiceLogList.size()<1){
                resultUtil.setCode("error");
                resultUtil.setMsg("选中包含运单对账状态未完成不允许继续开票！");
                return resultUtil;
            }
            for(TOrderAccountCheckInvoiceLog ail:tOrderAccountCheckInvoiceLogList){
                if(!"DDZWC".equals(ail.getCheckStatus())){
                    auditFalg=true;
                    auditMsg+="运单号："+ail.getOrderBusinessCode()+"运单对账状态未完成不允许继续开票！";
                }
            }
            if(auditFalg){
                resultUtil.setCode("error");
                resultUtil.setMsg(auditMsg);
                return resultUtil;
            }
            for(TOrderInfo info:orderInfoList){
                for(TOrderAuditLog auditLog:tOrderAuditLogList){
                    //运单审核状态
                    if(info.getCode().equals(auditLog.getOrderCode())){
                        ydzt = false;
                        if("NOTPASSNODE".equals(auditLog.getAuditStatus())){
                            auditFalg3=true;
                            if(auditMsg3.indexOf(info.getOrderBusinessCode())<0){
                                auditMsg3+="运单号："+info.getOrderBusinessCode()+"运单状态为审核不通过，请审核通过后申请开票！";
                            }
                        }
                    }
                }
               /* if(ydzt){
                    auditFalg3=true;
                    if(auditMsg3.indexOf(info.getOrderBusinessCode())<0){
                        auditMsg3+="运单号："+info.getOrderBusinessCode()+"运单状态为未审核，请审核通过后申请开票！";
                    }
                }*/
                if(ydzt || tOrderAuditLogList.size()<1){
                    auditFalg3=true;
                    if(auditMsg3.indexOf(info.getOrderBusinessCode())<0){
                        auditMsg3+="运单号："+info.getOrderBusinessCode()+"运单状态为未审核，请审核通过后申请开票！";
                    }
                }

                if(!orderInfoList.get(0).getCompanyProjectId().equals(info.getCompanyProjectId())){
                    resultUtil.setCode("error");
                    resultUtil.setMsg("你所选择的记录企业项目不一致！");
                    return resultUtil;
                }
                if(info.getPrimaryWeight()!=null&&!"".equals(info.getPrimaryWeight())){

                    sumDeliverWeightNotesWeight+=info.getPrimaryWeight();
                }
                if(info.getDischargeWeight()!=null&&!"".equals(info.getDischargeWeight())){

                    sumReceiveWeightNotesWeight+=Double.parseDouble(info.getDischargeWeight().toString());
                }
                if(info.getDispatchFee()!=null && !"".equals(info.getDispatchFee())){
                    totalDispatchFee+=Double.parseDouble(info.getDispatchFee().toString());
                }


                if("0".equals(info.getPackStatus())){
                    if(null!=info.getTotalFee() && !"".equals(info.getTotalFee())){
                        sumTotalFee+=Double.parseDouble(info.getTotalFee().toString());
                    }
                }else if("1".equals(info.getPackStatus())){
                    BigDecimal am = info.getSharePaymentAmount().add(info.getShareDispatchFee());
                    sumTotalFee+=Double.parseDouble(am.toString());
                }
            }
            if(auditFalg3){
                resultUtil.setCode("error");
                resultUtil.setMsg(auditMsg3);
                return resultUtil;
            }
            invoiceInfo.setTotalOriginalWeight(new BigDecimal(sumDeliverWeightNotesWeight));//选中运输原发质量
            invoiceInfo.setTotalRealWeight(new BigDecimal(sumReceiveWeightNotesWeight));//选中运输实收质量
            invoiceInfo.setTotalCarriageFee(new BigDecimal(sumTotalFee-totalDispatchFee));//选中单据运费金额
            invoiceInfo.setSellerId(orderInfoList.get(0).getCarrierId());//销售方 （承运方）
            invoiceInfo.setBuyerId(orderInfoList.get(0).getCompanyId());//购买方 （企业）
            invoiceInfo.setProjectId(orderInfoList.get(0).getCompanyProjectId());//购买方 （企业）
            invoiceInfo.setTotalDispatchFee(new BigDecimal(totalDispatchFee));//选择的调度费
            invoiceInfo.setInvoiceStatus("SHENQING");
            invoiceInfo.setEnable(false);
            invoiceInfoMapper.insertSelective(invoiceInfo);
            for(TOrderAccountCheckInvoiceLog record:tOrderAccountCheckInvoiceLogList){
                record.setInvoiceCode(invoiceInfo.getInvoiceCode()); //业务code
                record.setInvoiceBusinessCode(invoiceInfo.getInvoiceBusinessCode());//开票主表开票单据号
                tOrderAccountCheckInvoiceLogMapper.updateByPrimaryKeySelective(record);
            }
            //添加开票状态表 子状态
            TInvoiceStatus invoiceStatus = new TInvoiceStatus();
            invoiceStatus.setCode(IdWorkerUtil.getInstance().nextId());
            invoiceStatus.setInvoiceCode(invoiceInfo.getInvoiceCode());
            invoiceStatus.setInvoiceBusinessCode(invoiceInfo.getInvoiceBusinessCode());
            invoiceStatus.setEnable(false);
            invoiceStatus.setOperator(CurrentUser.getCurrentUsername());
            invoiceStatus.setOperTime(new Date());
            invoiceStatus.setOperSubStatus("SHENQING");
            tInvoiceStatusMapper.insertSelective(invoiceStatus);
            return ResultUtil.ok("申请开票成功！");
        }catch (Exception e){
            log.error("申请开票失败！",e);
            return  ResultUtil.error("申请开票失败！");
        }
    }

    /**
    * @Description 运单对账导出
    * <AUTHOR>
    * @Date   2019/7/29 9:16
    * @Param
    * @Return
    * @Exception
    *
    */
    @Override
    public ResultUtil yddzExpro(TOrderAccountCheckInvoiceLogVo tOrderAccountCheckInvoiceLogVo) {
        Integer companyIdInteger = null;
        List<String> userCompanyId = CurrentUser.getUserCompanyId();
        if (null != userCompanyId && userCompanyId.size() > 0){
            String companyId = userCompanyId.get(0);
            companyIdInteger = Integer.valueOf(companyId);
            tOrderAccountCheckInvoiceLogVo.setCompanyId(companyIdInteger);
        }
        if(tOrderAccountCheckInvoiceLogVo.getDeliverOrderTimeArry()!= null){
            tOrderAccountCheckInvoiceLogVo.setDeliverOrderTimeStar(tOrderAccountCheckInvoiceLogVo.getDeliverOrderTimeArry()[0]);
            tOrderAccountCheckInvoiceLogVo.setDeliverOrderTimeEnd(tOrderAccountCheckInvoiceLogVo.getDeliverOrderTimeArry()[1]);
        }
        if(tOrderAccountCheckInvoiceLogVo.getOrderFinishTimeArry()!= null){
            tOrderAccountCheckInvoiceLogVo.setOrderFinishTimeStar(tOrderAccountCheckInvoiceLogVo.getOrderFinishTimeArry()[0]);
            tOrderAccountCheckInvoiceLogVo.setOrderFinishTimeEnd(tOrderAccountCheckInvoiceLogVo.getOrderFinishTimeArry()[1]);
        }
        if(tOrderAccountCheckInvoiceLogVo.getWeightNotesTimeArray()!= null){
            tOrderAccountCheckInvoiceLogVo.setDeliverWeightNotesTimeStar(tOrderAccountCheckInvoiceLogVo.getWeightNotesTimeArray()[0]);
            tOrderAccountCheckInvoiceLogVo.setDeliverWeightNotesTimeEnd(tOrderAccountCheckInvoiceLogVo.getWeightNotesTimeArray()[1]);
        }
        //卸货榜单时间
        if(tOrderAccountCheckInvoiceLogVo.getReceiveWeightNotesTimeArray()!=null){
            tOrderAccountCheckInvoiceLogVo.setReceiveWeightNotesTimeStar(tOrderAccountCheckInvoiceLogVo.getReceiveWeightNotesTimeArray()[0]);
            tOrderAccountCheckInvoiceLogVo.setReceiveWeightNotesTimeEnd(tOrderAccountCheckInvoiceLogVo.getReceiveWeightNotesTimeArray()[1]);
        }
        //列表
        List<TOrderAccountCheckInvoiceLogVo> list = tOrderAccountCheckInvoiceLogMapper.selectByPage(tOrderAccountCheckInvoiceLogVo);
        for(TOrderAccountCheckInvoiceLogVo oaci:list){
            if(null != oaci.getUserConfirmPaymentAmount() && null != oaci.getInsuredAmount()){
                if(oaci.getUserConfirmPaymentAmount().compareTo(BigDecimal.ZERO) >= 0 && oaci.getInsuredAmount().compareTo(BigDecimal.ZERO) >= 0){
                    oaci.setUserConfirmPaymentAmount(oaci.getUserConfirmPaymentAmount().subtract(oaci.getInsuredAmount()).setScale(2, RoundingMode.HALF_UP));
                }
            }
            if("DWUWU".equals(oaci.getCheckFlag())){
                oaci.setCheckFlag("无误");
            }else if("DYZY".equals(oaci.getCheckFlag())){
                oaci.setCheckFlag("有争议");
            }else if("DXYZCHD".equals(oaci.getCheckFlag())){
                oaci.setCheckFlag("需再次核对");
            }

            if("HWUWU".equals(oaci.getVerifyFlag())){
                oaci.setVerifyFlag("无误");
            }else if("HYZY".equals(oaci.getVerifyFlag())){
                oaci.setVerifyFlag("有争议");
            }else if("HXYZCHD".equals(oaci.getVerifyFlag())){
                oaci.setVerifyFlag("需再次核对");
            }
            if("DDZWC".equals(oaci.getCheckStatus())){
                oaci.setCheckStatus("对账完成");
                oaci.setCheckStatusStr("DDZWC");
            }else if("DDZZ".equals(oaci.getCheckStatus())){
                oaci.setCheckStatus("对账中");
                oaci.setCheckStatusStr("DDZZ");
            }else{
                oaci.setCheckStatus("未对账");
            }
            if("NEWNODE".equals(oaci.getAuditStatus())){
                oaci.setNotMeetInvoiceCondition("运单未审核");
            }else if("PASSNODE".equals(oaci.getAuditStatus())){
                oaci.setNotMeetInvoiceCondition("运单审核通过");
            }else if("NOTPASSNODE".equals(oaci.getAuditStatus())){
                oaci.setNotMeetInvoiceCondition("运单审核不通过");
            }
            if(oaci.getDeliverOrderTime()!=null){

                oaci.setDeliverOrderTimeStr(DateUtils.dateFormat(oaci.getDeliverOrderTime()));
            }
            if(oaci.getOrderFinishTime()!=null){

                oaci.setOrderFinishTimeStr(DateUtils.dateFormat(oaci.getOrderFinishTime()));
            }
            if(oaci.getReceiveOrderTime()!=null){

                oaci.setReceiveOrderTimeStr(DateUtils.dateFormat(oaci.getReceiveOrderTime()));
            }
            //无装货磅单时间取装货时间
            if(oaci.getDeliverWeightNotesTime()==null){
                if(oaci.getDeliverOrderTime()!=null){
                    oaci.setDeliverWeightNotesTimeStr(DateUtils.dateFormat(oaci.getDeliverOrderTime()));
                }
            }else{
                oaci.setDeliverWeightNotesTimeStr(DateUtils.dateFormat(oaci.getDeliverWeightNotesTime()));

            }
            //无卸货磅单时间取卸货时间
            if(oaci.getReceiveWeightNotesTime()==null){
                if(oaci.getReceiveOrderTime()!=null){
                    oaci.setReceiveWeightNotesTimeStr(DateUtils.dateFormat(oaci.getReceiveOrderTime()));
                }
            }else{
                oaci.setReceiveWeightNotesTimeStr(DateUtils.dateFormat(oaci.getReceiveWeightNotesTime()));
            }

        }
        Map<String,Object> map = new HashMap<>();
        String[] headers =
                {
                        "运单号", "建单时间", "货物类型", "出发地", "目的地","车队长","车队长电话","业务部","运输人","运输人电话",
                        "运输车辆","原发重量","装货重量(吨)","装货（磅单）时间", "实收重量","卸货重量(吨)","实收运费单价","卸货（磅单）时间","收单时间","结算备注","结算重量",
                        "运费","调度费","支付完成时间","运单完成时间","承运方名称","项目名称","企业名称","委托方","客户名称",
                        "对账人标记","核账人标记","对账状态","不满足开票条件情形","支付平台","毛重（吨）","保费金额"
                };
        String[] names =
                {
                        "orderBusinessCode","deliverOrderTimeStr","goodsName","fromName","endName","carOwnerName","carOwnerPhone","endAgentName","realName","phone",
                        "vehicleNumber","primaryWeight","deliverWeightNotesWeight","deliverWeightNotesTimeStr","dischargeWeight","receiveWeightNotesWeight","carriageUnitPrice","receiveWeightNotesTimeStr",
                        "receiveOrderTimeStr","remark","settledWeight","userConfirmPaymentAmount","dispatchFee","orderFinishTimeStr","orderFinishTimeStr",
                        "carrierName","projectName","companyName","companyEntrust", "companyClient",
                        "checkFlag","verifyFlag","checkStatus","notMeetInvoiceCondition","paymentPlatforms", "grossWeight","insuredAmount"
                };
        map.put("headers",headers);
        map.put("names",names);
        map.put("list",list);
        return new ResultUtil(CodeEnum.SUCCESS.getCode(), map);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil cancelCheck(List<Integer> ids) {
        tOrderAccountCheckInvoiceLogMapper.batchUpdateCheckLogEnable(ids);
        return ResultUtil.ok();
    }

}
