package com.lz.controller;

import com.lz.api.TEndSUserInfoAPI;
import com.lz.common.util.CurrentUser;
import com.lz.common.util.ResultUtil;
import com.lz.model.TEndUserInfo;
import com.lz.model.TWalletChangeLog;
import com.lz.service.TWalletChangeLogService;
import com.lz.vo.CompanyCapitalFlowVo;
import com.lz.vo.TWalletChangeLogVo;
import com.lz.vo.TwalletVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @create 2019/4/18 - 16:29
 * @description 钱包变更记录controller
 **/
@Slf4j
@RestController
@RequestMapping(value = "/walletChangeLog")
public class TWalletChangeLogController {

    @Autowired
    private TWalletChangeLogService tWalletChangeLogService;
    @Autowired
    private TEndSUserInfoAPI tEndSUserInfoAPI;



    @PostMapping(value = "/save")
    public TWalletChangeLog ssave(@RequestBody TWalletChangeLog tWalletChangeLog) {
        return tWalletChangeLogService.save(tWalletChangeLog);
    }
    /**
     * 钱包列表
     * zhangxin
     *
     * @param
     * @return REsultUtil
     */
    @PostMapping("/walletList")
    public ResultUtil walletList(@RequestBody TwalletVO param) {
        Integer endUserId = CurrentUser.getEndUserId();
        String logisticsRole = CurrentUser.getUserLogisticsRole();
        String loginRole="";
        if(logisticsRole.equals("CTYPEDRVIVER")){//司机
            loginRole="CDRIVER";
        }else if(logisticsRole.equals("CTYPEBOSS")){
            loginRole="CCARBOSS";
        }else if(logisticsRole.equals("CTYPEMANAGER")){
            loginRole="CMANAGER";
        }else if(logisticsRole.equals("CTYPETRANSPORT")){
            loginRole="CWULIU";
        }else if(logisticsRole.equals("CTYPECAPTAIN")){
            loginRole = "CCAPTAIN";
        }
        ResultUtil getData = tWalletChangeLogService.getWalletList(endUserId,loginRole,param,logisticsRole);
        return getData;
    }


    /**
     *  钱包详情
     * zhangxin
     *
     * @param
     * @return REsultUtil
     */
    @PostMapping("/getWalletDetail")
    public ResultUtil getWalletDetail(@RequestBody TWalletChangeLogVo record) {
        ResultUtil getData = tWalletChangeLogService.selectByWalletId(record);
        return getData;
    }
    
    /**
     *  @author: sangbin
     *  @Date: 2019/6/28 14:06
     *  @Description:
     */
    @PostMapping(value = "/companyCapitalFlow")
    public ResultUtil companyCapitalFlow(@RequestBody CompanyCapitalFlowVo companyCapitalFlowVo) {
        try {
            return tWalletChangeLogService.companyCapitalFlow(companyCapitalFlowVo);
        } catch (Exception e) {
            log.error("获取企业资金流水失败！",e);
            return ResultUtil.error("dw-042:获取企业资金流水失败！");
        }
    }

    
    /**
     *  @author: sangbin
     *  @Date: 2019/6/28 14:06
     *  @Description:
     */
    @PostMapping(value = "/driverCapitalFlow")
    public ResultUtil driverCapitalFlow(@RequestBody CompanyCapitalFlowVo companyCapitalFlowVo) {
        try {
            return tWalletChangeLogService.driverCapitalFlow(companyCapitalFlowVo);
        } catch (Exception e) {
            log.error("获取司机资金流水失败",e);
            return ResultUtil.error("dw-043:获取司机资金流水失败！");
        }
    }

    
    /**
     *  @author: dingweibo
     *  @Date: 2019/9/29 10:59
     *  @Description: 承运方钱包流水
     */ 
    @PostMapping(value = "/carrierCapitalFlow")
    public ResultUtil carrierCapitalFlow(@RequestBody CompanyCapitalFlowVo companyCapitalFlowVo) {
        try {
            return tWalletChangeLogService.carrierCapitalFlow(companyCapitalFlowVo);
        } catch (Exception e) {
            log.error("获取承运方资金流水失败",e);
            return ResultUtil.error("dw:获取承运方资金流水失败！");
        }
    }


    /**
     *  @author: dingweibo
     *  @Date: 2019/6/25 20:08
     *  @Description:累计充值
     */
    @PostMapping(value = "/selectAccumulativeRecharge")
    public TWalletChangeLog selectAccumulativeRecharge(@RequestParam(value = "companyId") Integer companyId) {

        return tWalletChangeLogService.selectAccumulativeRecharge(companyId);
    }



    /**
     *  @author: dingweibo
     *  @Date: 2019/6/29 14:26
     *  @Description: 根据银行卡号查询当前月累计提现
     */
    @PostMapping(value = "/selectSumCashByBankNo")
    public TWalletChangeLog selectSumCashByBankNo(@RequestParam(value = "bankNo") String bankNo) {

        return tWalletChangeLogService.selectSumCashByBankNo(bankNo);
    }


}
