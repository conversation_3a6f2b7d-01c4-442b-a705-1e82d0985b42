package com.lz.request.thirdparty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ThirdPartyRequest {
    //运单编号
    private String orderNo;

    //货源编码
    private String goodsSourceCode;

    //司机手机号
    private String phone;

    //司机姓名
    private String realName;

    //车牌号
    private String vehicleNumber;

    //运单状态
    private String orderStatus;

    //车辆信息
    private List<CarInfo> carInfos;

    //货物信息
    private List<String> goodsSource;

    //装车磅单URL
    private String loadBillUrl;

    //卸车磅单URL
    private String unloadBillUrl;
}
