package com.lz.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lz.model.TOrderInfo;
import com.lz.model.TOrderInfoWeight;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.DecimalMax;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * t_order_info
 * <AUTHOR>
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class TOrderInfoVO extends TOrderInfo {

    /**
     * 车牌号
     */
    private String  vehicleNumber;

    //车牌颜色 1:蓝色；2：黄色；3：黄绿色
    private String licensePlateColor;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 承运方名称
     *
     */
    private String carrierName;

    //承运方管理员手机号
    private String companyContactsPhone;

    /**
     * 司机名称
     */
    private String realName;

    /**
     * 司机电话
     */
    private String realPhone;


    //司机身份证
    private String idcard;

    /**
     * 用户组织名称
     */
    private String shortName;


    //经纪人名称
    private String agentName;

    //经纪人手机号
    private String agentPhone;

    /**
     * 是否签到 0签到  1取消
     *
     */
    private String ifSignIn;

    /**
     * 卸货签到 0签到 1暂存
     */
    private String typeSign;


    /**
     *
     * 操作人位置
     */
    private String operateGeographyPosition;

    /**
     * 企业ID
     */
    private Integer companyId;

    /**
     * 运单执行状态
     */
    private String orderExecuteStatus;

    /** 删单人联系电话 */
    private String deletePersonPhone;

    /** 删单验证码 */
    private String verificationCode;

    /** 取消运单操作人联系电话 */
    private String cancelHallPersonPhone;

    /** 取消验证码 */
    private String cancelHallVerificationCode;

    /**
     * 删除原因
     */
    private String deleteReason;


    /**
     * 车辆状态 0异常 1 正常
     */
    private String carStatus;

    /**
     * 异常内容
     */
    private String  errorMemo;

    /**
     * 轨迹列表
     */
    private List<Map<String,Object>> positionList;

    private String txStr;

    private Integer bankId;

    private String phone;

    private String code;

    private String operateMethod;

    private BigDecimal totalFee;

    //发布时间
    private String deliverOrderTimeStr;

    //完成时间
    private String receiveOrderTimeStr;

    //task表主键
    private String taskId;

    //task 任务类型
    private String taskType;

    //task 请求参数
    private String requestParameter;

    //task 任务类型 code
    private String taskTypeCode;

    //错误信息
    private String errorMessage;

    //使用运单类型
    private String type;

    //创建运单字符串时间
    private String createTimeStr;

    //营业执照号
    private String businessLicenseNo;

    //多个运单业务id
    private String orderCodeStr;

    //线路起点坐标
    private String fromCoordinates;

    //线路起点
    private String fromName;

    //线路终点坐标
    private String endCoordinates;

    //线路终点
    private String endName;

    //身份类型  CCAPTAIN 车队长  CTYPEDRVIVER 司机
    private String roleType;

    private Integer accountId;

    private String smsType;
    /** 运单生成类型 */
    private String orderCreateType;

    /**
     * 支付时，用户确认的应付运费金额
     */
    @DecimalMax(value = "**********.99", message = "用户确认的应付运费金额不能超过**********.99")
    private BigDecimal newUserConfirmCarriagePayment;

    private String withdrawType;

    /*
     * <AUTHOR>
     * @Description 支付流程审核意见
     * @Date 2019/11/19 17:01
    **/
    private String paymentAuditRemark;

    /*
     * <AUTHOR>
     * @Description 支付流程 1: 支付需要审核  0: 支付不需要审核
     * @Date 2019/11/19 8:34
     **/
    private Integer paymentProcess;

    /*
     * <AUTHOR>
     * @Description 运单支付审核传递运单号
     * @Date 2019/11/20 14:11
    **/
    private String[] codes;

    /*
     * <AUTHOR>
     * @Description 运单支付审核状态
     * @Date 2019/11/20 14:16
    **/
    private String auditStatus;

    /*
     * <AUTHOR>
     * @Description 审核类型：PAY 实体运单支付 PACK 实体运单打包
     * @Date 2019/11/26 13:21
    **/
    private String auditType;
    //最小时间
    private Date minTime;

    //最大时间
    private Date maxTime;

    private String orderCode;

    private String stateRemark;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    private String ifCarTrajectory; //是否补充轨迹 0 默认 1 补充

    private String auditCode;

    private String driverStatus;

    /*
     * <AUTHOR>
     * @Description预付款支付，支付剩余尾款
     * @Date 2020/2/24 8:46 下午
     **/
    private Boolean advancePayment;

    private BigDecimal serviceFee;


    /** 经纪人资料 */
    private Integer agentId;//经纪人id
    private String agentCertificateNo;//经纪人企业营业执照号
    private Date agentCertificateValidUntil;//经纪人营业执照号有效期至
    private String agentCertificatePhoto1;//经纪人营业执照照片
    private String agentCertificatePhoto2;//经纪人营业执照照片
    private String agentIdcard;//经纪人身份证号
    private String agentIdcardPhoto1;//经纪人身份证照片1
    private String agentIdcardPhoto2;//经纪人身份证照片2
    private Date agentIdcardValidUntil;//经纪人身份证有效期至
    private String agentUserLogisticsRole;//经纪人类型
    private String companyProjectName;//项目名称

    /** 业务部手机号 */
    private String shortPhone;

    //项目表中是否显示运费明细
    private String isDisplay;

    /*排序 ASC升序  DESC降序*/
    private String sort;

    /*统计类型 yd  yf*/
    private String statisticalType;

    /*企业id集合*/
    private Integer[] companyIds;


    private String openId;

    private Boolean royaltPayment;

    //承运方公钥
    private String thirdPartyInterfacePublicKey;
    //司机端APP签到 签到为1
    private String uploadDatas;

    //司机进入电子围栏自动装卸货是否签到
    private Boolean ifLoadSingin;

    private String currentDriverAccountNo;

    //资金转移方式：1.直接支付到实际运输人（司机）2.支付到车辆所有人（车老板）3.支付给经纪人，再由经济人支付给实际运输人（司机）4.支付给经纪人，再由经纪人支付给车辆所有人（车老板）
    private String capitalTransferType;

    //枚举值：经纪人模式、普通模式
    private String capitalTransferPattern;

    private Date stateCreateTime;

    //是否提示卸货地签到操作
    private Boolean ifWarn;

    //车队长名称
    private String captainName;

    private Integer captainId;

    //车队长身份证号
    private String captainIdcard;

    //车队长手机号
    private String captainPhone;

    //是否装货地才选车队长
    private Boolean ifCaptain;

    private String paymentPlatforms;

    private String endDriverSignStatus;
    //司机是否注册电子印章
    private String endDriverSignStatusStr;

    private String endDriverSignSerialNo;
    //印章图片
    private String endDriverSealImage;

    private String signUserId;

    private Boolean signContract;

    private String carStatusValue;

    private String sourceName;


    /** 当运费单价单位是元/箱时会用到 */
    private TOrderInfoWeight orderInfoWeight;

    /** 运费单价单位 */
    private String carriagePriceUnit;

    /** 固定扣款 */
    public BigDecimal fixCutFee;

    private String idcardStatus;

    //装货地签到图片
    private String loadingCarPhoto;

    private String loadingCarPhotoInfo;

    //卸货地签到图片
    private String unloadCarPhoto;

    private String unloadCarPhotoInfo;

    private Date goodsReceiptDateTime;

    /** 投保方式 */
    private String insuranceMethod;

    /** 投保货物类型 */
    private String insuredGoodsType;

    /** 保险金额（元）*/
    private BigDecimal insuredAmount;

    /** 货值单价*/
    private BigDecimal goodsUnitPrice;

    /** 当投保方式为自主选择时，会用到*/
    private Boolean autoselect;

    /** 投保费率*/
    private BigDecimal rate;

    /** 人保货运险最低金额*/
    private BigDecimal piccFreightInsurance;

    /**
     * 货源名称
     */
    private String goodsSourceName;

    /** 车辆保险是否审核通过 */
    private String carInsuranceStatus;

    private String uploadCarrierId;

    //投保状态
    private Integer insure;

    //是否已退保
    private Boolean insuranceCancellation;

    //车头图片
    private String carHeadPhoto;

    //车尾图片
    private String carTailPhoto;

    //车辆45° 图片
    private String CarAnglePhoto;

    //货物照片
    private List<String> goodsPhoto;

    //合同类型  REPAIRHT  CYHT
    private String contractType;
}