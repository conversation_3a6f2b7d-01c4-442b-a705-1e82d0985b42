package com.lz.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2019/5/17 - 16:36
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TxVO {

    /**
     *  编码
     */
    private String code;
    /**
     *  编码
     */
    private String bcode;
    /**
     * 可提现金额
     */
    private BigDecimal money;
    /**
     * 1 单笔  2打包
     */
    private Integer type;
    /**
     *  描述信息
     */
    private String remark;
    /**
     *  时间
     */
    private Date createTime;

    //建单时间（发单时间）
    private Date deliverOrderTime;

    //货物类型
    private String goodsName;

    //提现时间
    private Date withdrawalTime;

    private String carAuditStatus;
    private String userAuditStatus;
    private String vehicleNumber;
    private Integer vehicleId;
    private Integer enduserId;

    //是否为业务部辅助
    private Boolean businessAssist;

    private String itemValue;

    private String payNodeType;
}
