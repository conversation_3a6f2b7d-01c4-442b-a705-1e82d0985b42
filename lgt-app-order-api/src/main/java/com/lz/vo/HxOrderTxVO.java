package com.lz.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class HxOrderTxVO implements Serializable {

    private static final long serialVersionUID = -4169625176028861291L;

    private String code;

    private String phone;

    private Integer bankCardId;

    private String cardOwner;

    private String cardNo;

    private BigDecimal totalFee;

    private BigDecimal txServiceFee;

    private String operateMethod;

    private Boolean selfCard;

    private String openAccountType;

    private String partnerAccId;

    private String tradeAbstract;

    private Boolean ifSmsCode = true;

    //是否人像对比
    private Boolean ifRxdb;

}
