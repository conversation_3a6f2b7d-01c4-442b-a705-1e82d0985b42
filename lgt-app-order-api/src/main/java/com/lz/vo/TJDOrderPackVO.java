package com.lz.vo;

import lombok.Data;

import javax.validation.constraints.DecimalMax;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class TJDOrderPackVO implements Serializable {

    private static final long serialVersionUID = -2437173893786566260L;

    private List<String> codes;

    /** 银行卡Id */
    private Integer bankCardId;

    private String bankNo;

    /** 总运费 */
    private BigDecimal countFreight;

    /** 总调度费 */
    private BigDecimal dispatch;

    private BigDecimal appointmentPaymentCash;

    /** 打包约定应支付总运费其他实物价值 */
    private BigDecimal appointmentPaymentOther;

    /** 运单抹零总数 */
    private BigDecimal countErase;

    private BigDecimal recountDispatchFee;

    /** 描述 */
    private String remark;

    private Object owner;

    private Object endUserIds;

    /**
     * 选中运单服务费金额
     */
    @DecimalMax(value = "99999.99", message = "选中运单服务费金额不能大于等于10万")
    private BigDecimal totalSelectedOrdersServiceFee;

    private Integer companyId;

    private Integer carrierId;

}
