package com.lz.vo;

import com.lz.common.util.PageInfo;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * App 查询运单
 */
@ToString
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AppOrderSearchVO extends PageInfo {
    private Long orderId;
    /** 运单主表索引字段引用主表 param4  */
    private List<String> orderIndex;
    /** 是否打包单 1 是 ， 0 不是*/
    private String packStatus;



    /** 建单时间 */
    private String createOrderTime;
    /** 业务Id */
    private String code;
    /** 运单编号 */
    private String orderBusinessCode;
    /** 司机电话 */
    private String phone;
    /** 项目id */
    private Integer projectId;
    /** 货物Id */
    private Integer goodsId;
    /** 企业的委托方 */
    private String companyEntrust;
    /**企业名称*/
    private String company;
    /** 企业的客户 */
    private String companyClient;
    private String[] files;
    /** 自定义业务Id */
    private String bizCode;
    /** 业务Id集合  */
    private List<String> codes;
    /** 是否是超级管理员 */
    private Boolean isAdmin;
    /** 线路货物Id */
    private List<Integer> lineGoodsRelId;
    /** 线路货物关系ID单项搜索 */
    private Integer lineGoodsId;
    /** 业务部id*/
    private Integer endAgentId;
    private Integer searchAgentId;
    /** 开始时间 */
    private Date startTime;
    /** 结束时间 */
    private Date endTime;
    /** 多个：运单状态 */
    private String[] wayBillState;
    /** 多个：运单状态 */
    private String[] orderState;

    /** 多个：运单支付状态 */
    private String[] payState;
    /** 多个：运单支付子状态 */
    private String payStateNode;
    /** 多个：运单执行状态 */
    private String[] status;

    private String[] weightEx;

    private String[] weightPa;
    /** 线路 */
    private Integer[] lineId;
    /** 搜索框条件 */
    private String inputSearch;
    /** 当前用户的Id 每次查询必须传的 */
    private Integer userId;
    /** 运单收货只查询 M040之前的运单 固定的值为 YDSH */
    private String state;
    /** 车辆所有人Id */
    private Integer carOwnerId;
    /** 判断是否是车主 */
    private boolean carOwner;
    /** 判断是否是车队长 */
    private boolean carCaptain;
    /** 车队长Id */
    private Integer captainId;
    /** 判断是否是车队长 */
    private boolean captain;
    /** APP 运单查询高级搜索：无条件 */
    private Integer noneState;
    /** APP 运单查询高级搜索：常用条件 */
    private Integer oftenUseState;
    /** APP 运单查询高级搜索：其他条件 */
    private Integer otherState;
    /** APP 运单查询高级搜索：创建时间 */
    private Integer createTimeState;
    /** APP 运单查询高级搜索：货源条件 */
    private Integer goodsSourceState;
    /** APP 运单查询高级搜索：UNION  用来控制 APP 高级搜索中 的执行状态和支付状态，如果同时有值 为 true */
    private boolean unIoa;
    /** APP 运单查询高级搜索：UNION 如果执行状态和支付状态都为空 false*/
    private boolean unIob;
    /** APP 运单查询高级搜索：UNION 如果执行状态和支付状态都为空 false*/
    private boolean unIoc;
    private boolean unIod;
    /** 承运方或企业或经纪人 */
    private String type;
    /** 承运方或企业或经纪人 id */
    private List<String> value;
    /** 司机名 */
    private String driverName;
    /** 司机手机号 */
    private String driverPhone;
    /** 车牌照 */
    private String licensePlate;
    /** 起点 */
    private String origin;
    /** 终点 */
    private String terminus;
    /** 搜索条件信息站 */
    private String informationStation;
    /** 企业发单、收单员登录 运单页面或运单收货页 */
    private String wayBill;
    /** 账号ID */
    private Integer accountId;
    /** 是否企业下的管理员 */
    private Boolean companyAdmin;
    /** 发货时间 */
    private Date[] fhTime;
    /** 发货磅单时间 */
    private Date[] fhbdTime;
    /** 收货磅单时间 */
    private Date[] shbdTime;
    /** 收货时间 */
    private Date[] shTime;
    private Date[] dzTime;
    private Date[] fkTime;
    private Date[] txTime;
    private Date[] auditTime;


    //装货支付时间
    private Date[] zhPayTime;
    //卸货支付时间
    private Date[] xhPayTime;
    //收单支付时间
    private Date[] sdPayTime;
    //尾款支付时间
    private Date[] wkPayTime;

    //装货提现时间
    private Date[] zhWithdrawTime;
    //卸货提现时间
    private Date[] xhWithdrawTime;
    //收单提现时间
    private Date[] sdWithdrawTime;
    //尾款提现时间
    private Date[] wkWithdrawTime;


    /** 运单完成时间 */
    private Date[] orderFinishTimeArry;
    /** 发货时间 */
    private Date fhsTime;
    private Date fheTime;
    /** 发货磅单时间 */
    private Date fhbdsTime;
    private Date fhbdeTime;
    /** 收货磅单时间 */
    private Date shbdsTime;
    private Date shbdeTime;
    /** 收货时间 */
    private Date shsTime;
    private Date sheTime;
    /** 到账时间 */
    private Date dzsTime;
    private Date dzeTime;
    /** 付款时间 */
    private Date fksTime;
    private Date fkeTime;
    /** 提现时间 */
    private Date txsTime;
    private Date txeTime;
    /** 完成时间 */
    private Date fihsTime;
    private Date fiheTime;
    /** 运单审核时间 */
    private Date auditsTime;
    private Date auditeTime;


    //装货支付时间
    private Date zhPayTimeStr;
    private Date zhPayeTimeEnd;
    //卸货支付时间
    private Date xhPayTimeStr;
    private Date xhPayTimeEnd;
    //收单支付时间
    private Date sdPayTimeStr;
    private Date sdPayTimeEnd;
    //尾款支付时间
    private Date wkPaysTimeStr;
    private Date wkPayTimeEnd;
    //装货提现时间
    private Date zhWithdrawTimeStr;
    private Date zhWithdrawTimeEnd;
    //卸货提现时间
    private Date xhWithdrawTimeStr;
    private Date xhWithdrawTimeEnd;
    //收单提现时间
    private Date sdWithdrawTimeStr;
    private Date sdWithdrawTimeEnd;
    //尾款提现时间
    private Date wkWithdrawTimeStr;
    private Date wkWithdrawTimeEnd;

    /** 原发重量 add by zhangjiji 2019/6/23 */
    private Double primaryWeight;
    /** 实收重量 add by zhangjiji 2019/6/23 */
    private Double dischargeWeight;
    /** 已付款页面要多查点数据，这里放个 boolean 在 xml 中好做判断 */
    private boolean yfkPage;
    /** 承运方ID */
    private Integer carrierId;
    /** 企业搜索 */
    private Integer companyId;
    /** 排序 */
    private String sort;
    //导出类型 0 运单检查 1运单付款 2运单收货
    private String exproType;
    //add by xyz 2019.8.8增加订单能够查看的所有用户id
    private String orderUserIds;
    /** 是否是运单打包页面 */
    private boolean orderPackPage;
    /** 判断页面是否需要查询所有运单的总重量 */
    private Boolean orderWeight;
    /** 是否进行查询 */
    private boolean search;
    /** 是否是初始化数据 */
    private boolean initPage;

    /** 运单检跟踪查页面 */
    /** 判断当前用户是企业，承运方 */
    private String userType;
    /** 用来判断用户是否是承运方账号 */
    private boolean org;
    /** 会计、收单：线路 */
    private boolean lineInspection;
    /** 发单：发的单 */
    private boolean sendInspection;
    /** 企业管理员：企业 */
    private boolean companyInspection;
    /** 经济人： 发的单，  运单经纪人 */
    private boolean agentInspection;
    /** PC 用 union all   a */
    private boolean insUna;
    /** PC 用 union all   b */
    private boolean insUnb;
    /** PC 用 union all   c */
    private boolean insUnc;
    private Set<Integer> lineGoodsRuleIds;
    private Set<Integer> companyIds;
    /** 运单检查页面 */

    /** 判断是否查询删除的运单 */
    private boolean searchDel;

    private List<Integer> bdLine;
    /** 发单员的线路 */
    private List<Integer> fdLine;

    private boolean lineUnion;

    private List idArray;

    /** 车牌号 */
    private String vehicleNumber;
    /** 起点 */
    private String fromName;
    /** 终点 */
    private String endName;
    /** 审核状态 */
    private String auditStatus;
    /** 客户端类型 */
    private String clientType;
    /** 合同ID */
    private Integer contractId;
    /** 审核意见 */
    private String auditRemark;
    /** 支付状态code ：提现记录列表再用 */
    private String orderPayStatus;

    /**承运人*/
    private String  carrierName;

    private Integer id;

    /**运单路径*/
    private String orderPath;
    /**资金流水单路径*/
    private String capitalPath;

    /**
     * 运单推送状态
     */
    private String sendState;

    /**
     *轨迹是否通过  0未通过  1通过
     */
    private Integer locusState;

    /**
     * 必填项是否验证  1是  0否
     */
    private boolean mustFill;

    /**
     *毛重是否校验
     */
    private boolean weightCheck;

    /** 存放当前账户是付款员的线路， 用来在实体运单支付页面根据负责的线路做权限   负责的线路才可以去支付 */
    private Set<Integer> payLine;
    /** 存放当前账户是收单员的线路， 有来判断是否有收单权限 */
    private Set<Integer> receiverLine;
    /** 是支付审核员的线路 */
    private Set<Integer> payReviewLine;
    /** 车辆审核状态 */
    private String carAuditStatus;
    /** 司机审核状态 */
    private String driverAuditStatus;
    /** 运单提现列表：提现状态如果是完成  true */
    private boolean carryOut;
    /** 提现失败原因是否为空 */
    private Boolean errorWithdrawReason;
    /** 是否根据创建时间排序 */
    private boolean createSort;
    /** 是否查询已删除运单 */
    private boolean getDelete;
    /** 审核运单页面按角色查询  是否默认查询已审核通过的运单 */
    private boolean auditPass;

    private Set<Integer> sendLines;

    /** 是否是需要合格不合格的企业 */
    private Boolean orderUnqualified;

    /** 查询不合格的运单 */
    private Boolean unqualifiedOrder;

    /** 不合格运单标记 */
    private String param2;
    /** 银行卡号 */
    private String bankCard;

    /*
     * <AUTHOR>
     * @Description 排序字段
     * @Date 2020/1/11 10:17
    **/
    private String levelName;

    /*
     * <AUTHOR>
     * @Description 排序 1：正序  0：倒序
     * @Date 2020/1/11 10:16
    **/
    private Integer level;

    /*
     * <AUTHOR>
     * @Description 排序 ： 1 asc 0 desc
     * @Date 2020/1/11 10:38
    **/
    private String orderName;

    /*
     * <AUTHOR>
     * @Description admin 和 运营 已提现运单:查询实体和打包
     * @Date 2020/1/13 19:58
    **/
    private boolean withdrawOrder;

    /**
     * 已提现导出传入的code集合
     */
    private String[] codeArray;

    /**
     * 导出
     */
    private List<String> codeList;

    /**
     * 经纪人id
     */
    private Integer agentId;

    //经纪人姓名/手机号/身份证号
    private String agentParam;

    private Boolean agentWalletType = false;

    /**
     * 车主id
     */
    private Integer endCarOwnerId;

    private String userLogisticsRole;

    private Integer enduserId;

    /**
     * 是否是业务部
     */
    private Boolean endAgent;

    //是否待评价
    private Boolean isDPJ;

    //评价截至日期
    private Date scoreDate;

    //true 为查询待评价
    private Boolean waitcommentFlag;

    //提现多少天后不展示待评价
    private Integer scoreDay;

    //是否查询待评价运单
    private Boolean ifdpjFlag;

    //是否展示预支付运单
    private Boolean ifAdvance;

    //查询待支付
    private String packunpaid;

    //true查询待支付
    private Boolean ifPackunpaid;

    /**
     * 司机app端是否上传司机信息 (默认)flase 0 上传后为 true 1
     */
    private boolean uploadData;

    //当前登录账号是否是车队长身份
    private Boolean ifCtypecaptain;

    //支付节点
    private String payNodeType;

    //支付状态
    private String payStatus;

    private String lineName;

    private String paymentPlatform;

    private String paymentPlatforms;

    //信发业务 是否仅展示车队长审核通过运单  false 否  true是
    private boolean ifCaptainAudit;

    private List<Integer> ids;

    /**
     * 收单员
     */
    private Integer receiveOrderUserId;

    /**
     * 项目多选
     */
    private List<Integer> projectIds;

    /**
     * 货源多选
     */
    private List<Integer> lineGoodsRelIds;

    /**
     * 车队长多选
     */
    private List<Integer> captainIds;

    /**
     * 业务部登录
     */
    private Boolean businessType;

    /**
     * 支付平台 1网商；2：京东；3：华夏
     */
    private Integer param1;

    /**
     * 付款时间数组
     */
    private String[] timeOfPayment;

    /**
     * 付款时间开始
     */
    private String timeOfPaymentStart;

    /**
     * 付款时间结束
     */
    private String timeOfPaymentEnd;

}
