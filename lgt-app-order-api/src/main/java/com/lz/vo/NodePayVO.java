package com.lz.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class NodePayVO implements Serializable {

    private static final long serialVersionUID = 5145100335768920681L;

    private String code;

    private String payNodeType;

    private String macAddress;

    private String payPassword;

    //ANDROID/IOS/PC
    private String sourceChannel;

    private BigDecimal rulePaymentFee;

    private BigDecimal paymentFee;

    private BigDecimal serviceFee;

    private BigDecimal userConfirmServiceFee;

}
