package com.lz.vo.hxyh;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class HXOrderPayVO implements Serializable {

    private static final long serialVersionUID = 5250703385360586967L;

    /**
     * 运单ID
     */
    private Integer id;

    /**
     * 运单code
     */
    private String code;

    //银行卡id
    private Integer bankId;

    //新的用户确认运费
    private BigDecimal newUserConfirmCarriagePayment;

    //服务费
    private BigDecimal serviceFee;

    //用户确认服务费
    private BigDecimal userConfirmServiceFee;

    private String remark;


}
