package com.lz.vo;

import com.lz.common.util.PageInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PcOrderSearchVO extends PageInfo {

    private int id;

    private List<String> orderIndex;

    private String code;

    List<Integer> companyIds;

    private List<String> codes;
    /** 车老板Id */
    private Integer carOwnerId;
    /** 运单号 */
    private String bizCode;
    /** 企业名名称 */
    private String companyName;
    /** 姓名 */
    private String driverName;
    /** 车牌号 */
    private String licencePlate;
    /** 手机号 */
    private String phone;
    /** 起点 */
    private String  startName;
    /** 终点 */
    private String endName;
    /** 审核状态 */
    private String auditState;
    /** 发货开始时间 */
    private Date sendStartTime;
    /** 发货结束时间 */
    private Date sendEndTime;
    /** 运单完成开始时间 */
    private Date achieveStartTime;
    /** 运单完成结束时间 */
    private Date achieveEndTime;
    /** 承运方 */
    private String carrierName;
    /** 备注 */
    private String remark;
    /** 未签合同运单搜索框。搜索内容 */
    private String license;
    /** 车老板经纪人模糊条件 */
    private String driverOrAgent;
    /** 打包主表ID */
    private List<Integer> packId;
    // ------ 运单打包支付页面搜索条件 ----
    /** 车老板 */
    private Integer endUserId;
    /** 单号 */
    private String virtualOrderNo;
    /** 打包状态 */
    private String packStatus;
    /** 运单打包页面数组 */
    private Date[] packTimes;
    /** 创建开始时间 */
    private Date startTime;
    /** 创建结束时间 */
    private Date endTime;
    /** 打包状态集合 */
    private String[] packStatusArray;
    /** 发单时间 */
    private Date[] time;
    /** 未签合同页面：合同状态 */
    private String[] contractStatus;
    /** 是否初始化页面 */
    private boolean initPage;

    /** 付款开始时间 */
    private Date fkStartTime;
    /** 付款结束时间 */
    private Date fkEndTime;

    /** 提现开始时间 */
    private Date txStartTime;
    /** 提现结束时间 */
    private Date txEndTime;

    /** 到账开始时间 */
    private Date dzStartTime;
    /** 到账结束时间 */
    private Date dzEndTime;


    private Date[] txTime;

    private Date[] dzTime;

    private Date[] fkTime;

    private Date[] orderTime;

    //导出类型 0 未支付打包  1 已支付打包
    private String exproType;

    private List codeArray;

    /** 银行卡号 */
    private String bankCard;

    /*
     * <AUTHOR>
     * @Description 排序字段
     * @Date 2020/1/11 10:17
     **/
    private String levelName;

    /*
     * <AUTHOR>
     * @Description 排序 1：正序  0：倒序
     * @Date 2020/1/11 10:16
     **/
    private Integer level;

    /*
     * <AUTHOR>
     * @Description 排序 ： 1 asc 0 desc
     * @Date 2020/1/11 10:38
     **/
    private String orderName;

    private String orderBusinessCode;

    private String driverPhone;

    private String fromName;

    private Integer projectId;

    private Integer lineGoodsId;

    private Integer goodsId;

    private String companyEntrust;

    private String companyClient;

    private Integer endAgentId;

    /** 发货时间 */
    private Date fhsTime;
    private Date fheTime;
    /** 发货磅单时间 */
    private Date fhbdsTime;
    private Date fhbdeTime;
    /** 收货磅单时间 */
    private Date shbdsTime;
    private Date shbdeTime;
    /** 收货时间 */
    private Date shsTime;
    private Date sheTime;
    //收款账号
    private String cardNo;
    //持卡人
    private String cardhoderMan;
    //资金转移方式
    private String capitalId;
    //是否查询合计
    private boolean orderWeight = false;
    //运输人
    private String realName;
    //运输人电话
    private String realPhone;

    /** 判断是否是车主 */
    private boolean carOwner;

    /*
      * 车主id
     */
    private Integer endCarOwnerId;

    /** 判断是否是车队长 */
    private boolean carCaptain;

    /*
     * 车队长id
     */
    private Integer carCaptainId;

    private Integer captainId;

    private String userLogisticsRole;

    /**
     * 是否是业务部
     */
    private Boolean endAgent;

    /** 承运方ID */
    private Integer carrierId;
}
