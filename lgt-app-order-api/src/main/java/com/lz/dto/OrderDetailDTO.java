package com.lz.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lz.common.util.BigDecimalSerialize;
import com.lz.common.util.DoubleSerialize;
import com.lz.vo.TOrderInfoWeightVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * App,PC 运单详情
 */
@EqualsAndHashCode
@Accessors(chain = true)
@NoArgsConstructor
@Data
public class OrderDetailDTO {

    private Integer id;
    /** 表业务ID */
    private String code;

    private Boolean isPay;

    private String orderCode;
    /** 运单编号 */
    private String orderBusinessCode;
    /** 企业名称 */
    private String companyName;
    /** 企业审核状态 */
    private String companyAuditStatus;
    /** 线路id */
    private Integer lineId;
    /** 线路名称 */
    private String lineName;
    /** 起点 */
    private String fromName;
    /** 终点 */
    private String endName;
    /** 货物类型 */
    private String goodsName;
    /** 运费单价 */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal carriageUnitPrice;
    /** 运单预估货源重量： 发单时候填写的发单重量 */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal estimateGoodsWeight;
    /** 运费单价更新时间 */
    @JsonFormat(pattern = "MM-dd HH:mm", timezone = "GMT+8")
    private Date efficientTime;
    /** 结算规则Id */
    private String carriageRuleId;
    /** 结算规则名称 */
    private String ruleName;
    /** 规则Id */
    private Integer ruleId;
    /** 运单节点状态CODE */
    private String stateNodeCode;
    /** 运单进度 */
    private String stateNodeValue;
    /** 运单进度执行时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operateTime;
    /** 司机姓名 */
    private String realName;
    /** 司机手机号 */
    private String phone;
    /** 业务部姓名 */
    private String broker;
    /** 司机认证状态 */
    private String auditStatus;
    /** 协议认证状态 */
    private String contractStatus;
    /** 车牌号 */
    private String vehicleNumber;
    /** 车老板姓名 (车队长模式上线后为车队长姓名)*/
    private String owner;
    /** 车队长认证状态*/
    private String carCaptainAuditStatus;
    /** 车队长审核意见*/
    private String carCaptainAuditOpinion;
    /** 车老板手机号 (车队长模式上线后为车队长手机)*/
    private String carOwnerPhone;
    /** 建单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderCreateTime;
    /** 发单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliverOrderTime;
    /** 收单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receiverOrderTime;
    /** 发货磅单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliverWeightNotesTime;
    /** 收货磅单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receiveWeightNotesTime;
    /** 发货磅单上的时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliverWeightNotesUploadTime;
    /** 收货磅单上的时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receiveWeightNotesUploadTime;
    /** 运单状态更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date statusTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /** 装货签到时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operateDRICONTime;
    /** 卸货签到时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operateDRICONLOADTime;
    /** 结算重量 */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double settledWeight;
    /** 司机审核意见 */
    private String auditOpinion;

    private String pageShowCode;
    // 已经装货，卸货---------------------------------
    /** 发货磅单 */
    private String deliverWeightNotesPhoto;
    /** 收货磅单 */
    private String receiveWeightNotesPhoto;
    /** 原发重量 */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double primaryWeight;
    /** 卸货重量 */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double dischargeWeight;

    /** 毛重 */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double grossWeight;

    // 已收单 ---------------------------------------
    /** 运单标志 */
    private String orderSign;

    private List<Map<String, Object>> payment;

    /** 车辆审核状态 */
    private String carStateCode;
    /** 车辆审核意见 */
    private String carAuditOpinion;
    /** 司机审核状态 */
    private String driverStateCode;
    /**
     * 线路货物（运费）计算规则表ID
     */
    private Integer lineGoodsCarriageRuleId;
    /**
     * 货物类型
     */
    private String goodsType;

    /**
     * @Description: 货值单价
     * @Author: Yan
     * @Date: 2019/7/8/008 18:42
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal goodsUnitPrice;

    /**
     * 线路货物（运费）单价变动表ID
     */
    private Integer lineGoodsCarriageChangeId;

    /**
     * 实发数量
     */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double realDeliverWeight;

    /**
     * 实收数量
     */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double realDispathWeight;

    /**
     * 涨损
     */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double loseOrRise;

    /**
     * 涨损
     */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double lossWeight;
    /**
     *亏损重量
     */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double deficitWeight;

    /**
     * 容忍值1（按系数  约定物损
     */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double tolerantValueCoefficient;

    /**
     * 容忍值2（按吨数  约定物损
     */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double tolerantValueWeight;

    /**
     * 亏吨扣款表达式
     */
    private String lossCutExpression;

    /**
     * 涨吨扣款表达式
     */
    private String riseCutExpression;

    /**
     * 亏吨涨吨扣款
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal loseOrRiseCut;

    /**
     * 亏吨涨吨扣款
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal lossDeduction;

    /**
     * 扣水吨数
     */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double goodsCutWater;

    /**
     * 扣杂吨数
     */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double goodsCutImpurities;

    /** 约定物损 : 容忍值已选定项 */
    private String toleranceItem;

    /** 容忍值 */
    @JsonSerialize(using = DoubleSerialize.class)
    private double toleranceItemValue;
    /** 扣水是否计算货值单价  */
    private Boolean cutWaterIsCalcvalue;
    /**  扣杂是否计算货值单价 */
    private Boolean cutImpuritiesIsCalcvalue;
    /**
     * 固定扣款金额
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal fixCutFee;

    /**
     * 固定扣款备注
     */
    private String fixCutRemark;

    /**
     * 其他扣款金额1
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal otherCutFee1;

    /**
     * 其他扣款金额2
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal otherCutFee2;

    /**
     * 其他扣款金额3
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal otherCutFee3;

    /**
     * 其他扣款金额4
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal otherCutFee4;

    /**
     * 其他扣款备注1
     */
    private String otherCutRemark1;

    /**
     * 其他扣款备注2
     */
    private String otherCutRemark2;

    /**
     * 其他扣款备注3
     */
    private String otherCutRemark3;

    /**
     * 其他扣款备注4
     */
    private String otherCutRemark4;

    /**
     * 扣款列表
     */
    private List<Map<String, Object>> otherCutFee;

    /**
     * （亏吨）应付运费表达式
     */
    private String lossPayableExpression;

    /**
     * （涨吨）应付运费表达式
     */
    private String risePayableExpression;

    /**
     * （计算使用的）抹零规则code
     */
    private String carriageZeroCutPaymentRule;
    /**
     * 抹零规则名称
     */
    private String carriageZeroCutPaymentName;

    /**
     * 抹零规则对应的抹零金额
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal carriageZeroCutPayment;

    /**
     * 规则计算的应付运费金额
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal carriagePayment;

    /**
     * 用户确认的应付运费金额
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal userConfirmCarriagePayment;


    /**
     * 行数据可用性
     */
    private Boolean dataEnable;

    /**
     * 发单备注
     */
    private String remark;

    /**
     * 结算备注
     */
    private String snapshotRemark;

    /**
     * 扩展字段1
     */
    private String param1;

    /**
     * 扩展字段2
     */
    private String param2;

    /**
     * 扩展字段3
     */
    private String param3;

    /**
     * 扩展字段4
     */
    private String param4;
    /** 运单执行状态CODE */
    private String orderExecuteStatus;
    /** 运单执行状态码  */
    private String orderExecuteName;

    /** 营业执照注册号 */
    private String businessLicenseNo;
    /** 营业执照 */
    private String businessLicensePhoto;
    /** 司机身份证号 */
    private String idcard;
    /** 身份证图1 */
    private String idcardPhoto1;
    /** 身份证图2 */
    private String idcardPhoto2;
    /** 驾驶证1 */
    private String drivingLicencesPhoto1;
    /** 驾驶证2 */
    private String drivingLicencesPhoto2;
    /** 从业资格证照1 */
    private String certificatePhoto1;
    /** 从业资格证照2 */
    private String certificatePhoto2;
    /** 司机审核状态 */
    private String driverState;
    /** 审核流程ID */
    private String workflowId;
    /** 道路运输许可证号 */
    private String roadTransportCertificateNumber;
    /** 行驶证照1 */
    private String steerPhoto1;
    /** 行驶证照2 */
    private String steerPhoto2;
    /** 道路运输许可证1 */
    private String roadTransportOperationLicensePhoto1;
    /** 道路运输许可证2 */
    private String roadTransportOperationLicensePhoto2;
    /** 车辆审核状态 */
    private String carState;

    /** 线路货物关系ID */
    private String lineGoodsRelId;

    /** 结算重量 */
    private String settleAccountsWeight;

    /**  运费小计没有 自己计算 */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal subtotal;

    /** 扣款总和 自己计算 */
    private String  cutCount;
    /** 运单合同 */
    private List<String> contractlist;

    /** C端司机Id */
    private Integer endDriverId;
    /** 车辆ID */
    private Integer endCarId;
    private Integer vehicleId;
    /**
     * @Description 车老板id(车队长模式上线后为车队长id)
     * <AUTHOR>
     * @Date   2019/6/29 16:36
     *
     */
    private Integer endCarOwnerId;
    /** 客户名 */
    private String companyEntrust;
    /** 委托方 */
    private String companyClient;
    /** 业务部Id */
    private Integer endAgentId;
    /** 司机签到状态 */
    private String driverSignStatus;
    /**
     * 货源单价（预估单价）
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal currentCarriageUnitPrice;

    /** 合同签订状态CODE */
    private String contractStatusCode;

    public Boolean superAdmin;

    /**
     * 打包后重新分配的运费金额
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal sharePaymentAmount;
    /** 运单打包状态 默认为0，打包时为1 */
    private Boolean packStatus;

    /** 车辆入网验证 默认值0，表示未验证。1表示已通过验证 */
    private Integer verass;
    /** 车辆识别代号 */
    private String vehicleIdentificationCode;

    /** 发单员姓名 */
    private String deliverGoodsContacter;

    /** 发单员手机号 */
    private String deliverGoodsContacterPhone;

    /** 运单备注 */
    private String orderRemark;

    /** 结算备注 */
    private String calRemark;

    /** 运单执行状态备注 */
    private String stateRemark;

    private String receiveGoodsContacterPhone;

    /** 发货磅单重量 */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double deliverWeightNotesWeight;
    /** 收货磅单重量 */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double receiveWeightNotesWeight;
    /** 项目名称 */
    private String projectName;
    /** 用来判断当前用户是否拥有修改运单的权限 */
    private boolean editPms;

    private String endAgentName;

    private String contractFilePath;

    private String endCarOwnerName;

    private Integer paymentProcess;

    /** 支付审核意见 */
    private String paymentAuditRemark;

    /** 支付审核状态 */
    private String paymentAuditState;

    private String orderPayStatus;

    //是否有反馈记录
    private boolean feedbackStatus;

    //装货 是否提问
    private boolean loading;

    //卸货 是否提问过
    private boolean unloading;

    //司机卸货
    private boolean driverUnloading;
    //司机收单
    private boolean driverCollect;
    //问题与提问界面
    private List<Map> feedbackMap;
    private Integer companyId;

    /** 是否是需要合格不合格的企业 */
    private Boolean orderUnqualified;

    private boolean orgOrAdmin;

    /** 经纪人资料 */
    private Integer agentId;//经纪人id
    private String agentName;//经纪人名称
    private String agentPhone;//经纪人手机号
    private String agentCertificateNo;//经纪人企业营业执照号
    private Date agentCertificateValidUntil;//经纪人营业执照号有效期至
    private String agentCertificatePhoto1;//经纪人营业执照照片
    private String agentCertificatePhoto2;//经纪人营业执照照片
    private String agentIdcard;//经纪人身份证号
    private String agentIdcardPhoto1;//经纪人身份证照片1
    private String agentIdcardPhoto2;//经纪人身份证照片2
    private Date agentIdcardValidUntil;//经纪人身份证有效期至
    private String agentUserLogisticsRole;//经纪人类型

    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal serviceFee;

    /*
     * <AUTHOR>
     * @Description用户确认的服务费
     * @Date 2020/3/3 3:32 下午
     **/
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal userConfirmServiceFee;

    private Boolean updateServiceFee;

    private String shareMethod;

    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal shareValue;

    private String capitalTransferPattern;

    //提现方式
    private String withdrawType;

    private String capitalTransferType;

    private String agentStateCode;

    private String agentStatus;

    /** 经纪人姓名 */
    private String managerName;

    /** 经纪人号码 */
    private String managerPhone;

    /** 经纪人营业执照号码 */
    private String certificateNo;

    /** 经纪人营业执照有效期 */
    private Date certificateValidUntil;

    /** 经纪人营业执照图片 */
    private String managerCertificatePhoto;

    /** 是否业务部辅助 */
    private Boolean businessAssist;

    private Integer managerId;

    private BigDecimal managerPayment;

    /*
     * <AUTHOR>
     * @Description运单生成方式
     * @Date 2020/3/30 11:14 上午
    **/
    private String orderCreateType;

    //项目表中是否显示运费明细
    private String isDisplay;

    /** 收款人姓名 */
    private String receiverName;
    /** 收款人收款账户 */
    private String receiverAccount;
    /** 收款人收款账户 */
    private List<HashMap> receiverBankCard;

    private String driverUser;
    //支付方式
    private String payMethod;

    /**
     * 装货应付运费
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal zhRulePaymentFee;

    /**
     * 卸货应付运费
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal xhRulePaymentFee;

    /**
     * 收单应付运费
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal sdRulePaymentFee;

    /**
     * 尾款应付运费
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal wkRulePaymentFee;

    /**
     * 装货已付运费
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal zhPaymentFee;

    /**
     * 卸货已付运费
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal xhPaymentFee;

    /**
     * 收单已付运费
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal sdPaymentFee;

    /**
     * 尾款已付运费
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal wkPaymentFee;

    /**
     * 装货支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date zhupdateTime;

    /**
     * 卸货支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date xhupdateTime;

    /**
     * 收单支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sdupdateTime;

    /**
     * 尾款支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date wkupdateTime;


    //规则应付运费
    private String estimateTotalFee;

    private Date zhCreateTime;

    private Date xhCreateTime;

    private Date sdCreateTime;

    private Date wkCreateTime;

    private String payNodeType;

    /** 预计装货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date estimateLoadTime;
    /**预估装货重量*/
    private Double estimatedLoadingWeight;

    //预计行驶时间
    private Double estimatedTravelTime;

    /**
     *  结算重量取值  1 按最小   2 按原发
     */
    private Integer settledWeightType;

    //超过约定物损时是否全扣 0否  1 是
    private Integer ifExceedAmount;


    private String carriagePriceUnit;

    private Integer boxNum;
    private Date deliverWeightNotesTime1;
    private BigDecimal deliverWeightNotesWeight1;
    private String deliverWeightNotesPhoto1;
    private Date receiveWeightNotesTime1;
    private BigDecimal receiveWeightNotesWeight1;
    private String receiveWeightNotesPhoto1;
    private BigDecimal grossWeight1;
    private BigDecimal primaryWeight1;
    private BigDecimal dischargeWeight1;
    private BigDecimal carriageUnitPrice1;

    private Date deliverWeightNotesTime2;
    private BigDecimal deliverWeightNotesWeight2;
    private String deliverWeightNotesPhoto2;
    private Date receiveWeightNotesTime2;
    private BigDecimal receiveWeightNotesWeight2;
    private String receiveWeightNotesPhoto2;
    private BigDecimal grossWeight2;
    private BigDecimal primaryWeight2;
    private BigDecimal dischargeWeight2;
    private BigDecimal carriageUnitPrice2;



    /** order_info_weight 表ID */
    private Integer orderInfoWeightId;

    /** 当运费单价单位是元/箱时会用到 */
    private TOrderInfoWeightVO orderInfoWeight;

    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal fixCutFee2;

    private String deliverOrderUserName;

    private String deliverOrderUserPhone;

    private String receiveOrderUserName;

    private String receiveOrderUserPhone;

    //装货地签到图片
    private String loadingCarPhoto;

    private String loadingCarPhotoInfo;

    //卸货地签到图片
    private String unloadCarPhoto;

    private String unloadCarPhotoInfo;

    //保费金额
    //private BigDecimal insuredAmount;

    //投保是否成功，S为成功，F为失败
    private String insuranceStatus;

}
