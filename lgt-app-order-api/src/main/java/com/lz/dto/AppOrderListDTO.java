package com.lz.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lz.common.util.BigDecimalSerializeFour;
import com.lz.common.util.DoubleSerialize;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * APP 运单列表
 */
@EqualsAndHashCode
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AppOrderListDTO implements Serializable {
    private static final long serialVersionUID = 343792643940274588L;
    /** 32业务ID */
    private String code;
    private Integer carrierId;
    /** 线路ID */
    private String lineId;
    /** 根据线路货物关系ID和 accountId 查询出用户当前运单所拥有的权限按钮 */
    private String buttons;
    /** 查询出用户当前运单所拥有的权限按钮  去重后 */
    private Set<String> button;
    /** 线路货物关系ID */
    private Integer lineGoodsRelId;
    /** 建单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime;
    /** 运单自定义业务ID */
    private String orderBusinessCode;
    /** 发单员账号ID */
    private Integer deliverOrderUserId;
    /** 运单收单员ID */
    private Integer receiveOrderUserId;
    /** 运单经济人ID */
    private Integer endAgentId;
    /** 发单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliverOrderTime;
    /** 货源重量（预估重量） */
    @JsonSerialize(using = BigDecimalSerializeFour.class)
    private BigDecimal estimateGoodsWeight;
    /** 起点 */
    private String fromName;
    /** 终点 */
    private String endName;
    /** 线路名 */
    private String lineName;
    /** 车牌号 */
    private String vehicleNumber;
    /** 车辆状态*/
    private String carAuditStatus;
    /** 运单状态中文 */
    private String pageShowCode;
    /** 运单状态CODE */
    private String orderExecuteStatus;
    /** 司机名 */
    private String realName;
    /** 司机状态*/
    private String userAuditStatus;
    /** 司机手机号 */
    private String phone;
    /** 货物类型 */
    private String goodsName;
    /** 发货磅单 */
    private String deliverWeightNotesPhoto;
    /** 收货磅单 */
    private String receiveWeightNotesPhoto;
    /** 计算规则Id */
    private Integer ruleId;
    /** 运单异常CODE */
    private List<Map<String, String>> errorType;
    /** 运单支付状态 */
    private String orderPayStatus;
    /**
     * 签订合同状态
     */
    private String contractStatus;
    // 微信司机查询运单----------------------------
    /** 运单异常状态CODE */
    private List<Map<String, String>> abnormalType;
    /** 判断信息是否完整 */
    private Boolean infoFull;
    /** 发单员手机号 */
    private String deliverOrderUserPhone;
    /** 打包状态 */
    private Integer packStatus;
    //add by xyz 2019.8.8增加订单能够查看的所有用户id
    private String orderUserIds;
    @JsonSerialize(using = DoubleSerialize.class)
    private Double dischargeWeight;
    @JsonSerialize(using = DoubleSerialize.class)
    private Double primaryWeight;
    @JsonSerialize(using = DoubleSerialize.class)
    private Double deliverWeightNotesWeight;
    // 实时子状态,如果运单中支付状态还没有会写上，切他是支付状态就用它代替
    private String childStatus;

    private Integer goodsSourceInfoId;

    private String goodsSourceCode;

    private Double currentCarriageUnitPrice;

    private String payChildStatus;

    //问题与提问界面
    private List<Map> feedbackMap;

    /*  0未评价    1 已评价    2超过设定天数 3找不到提现记录 4打包单 5非M130  6 找不到提现时间* */
    private String isScore;

    //是否显示评价按钮  true 显示  false 不显示
    private boolean waitComment;
    //发车地域代码
    private String fromCode;
    //卸车地域代码
    private String endCode;

    private String fromCoordinates;
    private String endCoordinates;

    private String uploadCarrier;

    private String transportDistance;

    //是否是节点支付
    private String payMethod;

    //节点支付节点位置
    private String payNodeType;

    private String payNodeTypeNew;
    //是否是不合格运单
    private String param5;

    private Boolean firstPay;

    private String paymentPlatforms;

    //是否投保
    //private Boolean insure;

    //投保是否成功，S为成功，F为失败
    private String insuranceStatus;

}
