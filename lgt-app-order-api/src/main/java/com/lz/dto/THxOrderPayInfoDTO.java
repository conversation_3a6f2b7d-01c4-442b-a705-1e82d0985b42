package com.lz.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lz.common.util.BigDecimalSerialize;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class THxOrderPayInfoDTO {

    /*
     * <AUTHOR>
     * @Description支付主表id 1:经纪人提现修改支付主表提现完成使用
     * @Date 2020/3/13 11:45 上午
     **/
    private Integer id;

    private Integer orderId;

    private String orderBusinessCode;

    private Integer packId;

    private String virtualOrderNo;

    private Integer orderPayId;

    private String feeSettlementWay;

    private Integer orderPayDetailId;

    private String payCode;

    private String orderPayDetailCode;

    private String orderCastChangeCode;
    //C端钱包
    private Integer endDriverWalletId;
    //企业钱包
    private Integer companyWalletId;
    //企业项目钱包
    private Integer companyProjectWalletId;
    //承运方钱包
    private Integer carrierWalletId;
    //运单Code
    private String orderCode;

    private Date operateTime;

    private Date createTime;
    //交易类型
    private String tradeType;

    private String operateState;

    private BigDecimal currentDispatchRate;
    //资金转移方式
    private String capitalTransferType;
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal carriageFee;
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal dispatchFee;
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal totalFee;
    //提现方式
    private String withdrawType;

    //银行卡联行号
    private String bankUnionNo;
    //银行卡卡号
    private String bankNo;
    //持卡人
    private String cardHolder;
    //银行卡表Id
    private Integer bakCardId;
    //银行卡类型
    private String cardType;
    private String createUser;

    private String packCode;

    private Integer walletId;

    private Date returnTime;

    private String capitalTransferPattern;

    private BigDecimal serviceFee;

    private BigDecimal txServiceFee;

    private Integer carrierId;

    private Integer endCarOwnerId;

    private Integer agentId;

    private String tradeStatus;

    private Integer endDriverId;

    private Integer carOwnerWalletId;

    /**
     * 支付系统交易号
     */
    private String inner_trade_no;

    /**
     * 交易处理完成时间
     */
    private String gmt_close;

    private Integer companyId;

    /*
     * <AUTHOR>
     * @Description货源单号
     * @Date 2020/4/23 3:06 下午
     **/
    private String cargoOrder;

    private Date orderCreateTime;

    private Integer vehicleId;

    private String goodsSourceCode;

    private String operateCreateUser;

    private String innerTradeNo;

    private String errorMsg;

    private String param1;

    private Integer bankCardId;

    private String txCreateUser;

    /**
     * 承运方会员编号
     */
    private String carrierAccId;

    /**
     * 企业会员编号
     */
    private String companyAccId;

    /**
     * 司机会员编号
     */
    private String driverAccId;

    /**
     * 车队长会员编号
     */
    private String captainAccId;

    /**
     * 经纪人会员编号
     */
    private String agentAccId;

    private String goodsName;

    private String settledWeight;

    private Integer captainWalletId;

    private Integer agentWalletId;

    private Integer companyProjectId;

    private String packStatus;

    /**
     * 资金变动表用户操作类型
     */
    private String userOper;

    private String endUserType;

    private String remark;

    private Integer driverOpenRoleId;

    private Integer captainOpenRoleId;

    private Integer agentOpenRoleId;

    private BigDecimal orderTotalPayment;

    /** 营业执照号 、社会统一信用代码*/
    private String businessLicenseNo;

    private Double estimateGoodsWeight;

    private Double primaryWeight;

    /**
     * 是否违规订单
     */
    private Boolean illegalOrder;

    /**
     * 违规扣款
     */
    private BigDecimal illegalDeduction;

    /**
     * 保险id
     */
    private Integer insuranceId;

    private Integer insure;

    /**
     * 保险金额
     */
    private BigDecimal insuredAmount;

    /**
     * 退保状态
     */
    private Boolean insuranceCancellation;

}
