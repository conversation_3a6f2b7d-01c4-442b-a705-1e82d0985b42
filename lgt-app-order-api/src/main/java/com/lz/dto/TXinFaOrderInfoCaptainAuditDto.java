package com.lz.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lz.common.util.BigDecimalSerialize;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class TXinFaOrderInfoCaptainAuditDto {

    private Integer id;

    /** 运单号 */
    private String orderBusinessCode;

    /** 车牌号 */
    private String vehicleNumber;

    /** 司机姓名 */
    private String realName;

    /** 发货磅单重量 */
    private Double deliverWeightNotesWeight;

    /** 卸货磅单重量 */
    private Double receiveWeightNotesWeight;

    /**
     * 原发重量 -- 计算使用
     */
    private Double primaryWeight;

    /**
     * 实收重量 -- 计算使用
     */
    private Double dischargeWeight;

    /** 车队长审核状态 */
    private String orderPayStatus;

    private String orderPayStatusValue;

    //运单状态
    private String orderExecuteStatus;

    private String orderExecuteStatusCode;

    //线路名称
    private String lineName;

    //货物类型
    private String goodsName;


    //司机手机号
    private String realPhone;

    //发单时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "GMT+8")
    private Date deliverOrderTime;

    //收单时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "GMT+8")
    private Date receiveOrderTime;

    //装货磅单时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "GMT+8")
    private Date deliverWeightNotesTime;

    //卸货磅单时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "GMT+8")
    private Date receiveWeightNotesTime;

    //规则计算的应付运费（元）
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal rulePaymentAmount;

    //装货磅单
    private String deliverWeightNotesPhoto;

    //卸货磅单
    private String receiveWeightNotesPhoto;

    //车队长名称
    private String captainName;

    //结算重量
    private BigDecimal settledWeight;

    //收单员
    private String receiveGoodsContacter;

    //用户确认的应付运费金额
    private BigDecimal userConfirmPaymentAmount;

    //调度费
    private BigDecimal dispatchFee;

    //承运方
    private String carrierName;

    //资金转移方式
    private String capitalTransferType;
    /** 判断是否有审核权限 */
    private boolean payReviewPms;

    /** 运单支付：判断是否有支付权限 */
    private boolean orderPayPms;
    /** 判断是否有收单权限 */
    private boolean orderReceiverPms;

    private Integer lineGoodsRelId;

    private String code;

    private Integer ifCaptainAudit;

    /**
     * （计算使用的）运费单价
     */
    private BigDecimal carriageUnitPrice;

    private Integer companyId;

    private String contractStatusCode;

    private String deliverWeightNotesPhoto1;

    private String deliverWeightNotesPhoto2;

    private String receiveWeightNotesPhoto1;

    private String receiveWeightNotesPhoto2;

    //运费单价结算方式：元/吨(DUN)，元/箱(BOX)，元/车(CAR)
    private String carriagePriceUnit;

    //运费单价
    private BigDecimal currentCarriageUnitPrice;

    //固定扣款
    private BigDecimal fixCutFee;

    //固定扣款
    private BigDecimal fixCutFee2;
    private Integer boxNum;
    private Date deliverWeightNotesTime1;
    private BigDecimal deliverWeightNotesWeight1;
    private Date receiveWeightNotesTime1;
    private BigDecimal receiveWeightNotesWeight1;
    private BigDecimal grossWeight1;
    private BigDecimal primaryWeight1;
    private BigDecimal dischargeWeight1;
    private BigDecimal carriageUnitPrice1;

    private Date deliverWeightNotesTime2;
    private BigDecimal deliverWeightNotesWeight2;
    private Date receiveWeightNotesTime2;
    private BigDecimal receiveWeightNotesWeight2;
    private BigDecimal grossWeight2;
    private BigDecimal primaryWeight2;
    private BigDecimal dischargeWeight2;
    private BigDecimal carriageUnitPrice2;
}
