package com.lz.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lz.common.util.BigDecimalSerialize;
import com.lz.common.util.DoubleSerialize;
import com.lz.model.TOrderPackInfo;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OrderPackDTO extends TOrderPackInfo {

    private String orderBusinessCode;

    /** 所属车老板 */
    private String realName;
    /** 打包主表code */
    private String code;
    private String packStatus;
    /** 项目ID */
    private Integer companyProjectId;
    /** 司机ID */
    private Integer endDriverId;
    /** 经纪人Id */
    private Integer endAgentId;
    /** 车老板 */
    private Integer endCarOwnerId;
    /** 合同状态 */
    private String contractStatus;
    /** 运单执行状态 */
    private String orderExecuteStatus;
    /** 资金转移方式 */
    private String capitalTransferType;
    /** 司机审核状态 */
    private String driverStatus;
    /** 车辆审核状态 */
    private String carStatus;
    /** 提现方式 */
    private String withdrawType;
    /** 承运方ID */
    private Integer carrierId;
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimes;
    /** 车辆ID */
    private Integer vehicleId;

    private Date payTime;

    private String packStatusValue;

    private String phone;

    /**
     * 选中运单累计总吨数
     */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double totalSelectedOrdersWeight;

    /**
     * 选中运单累计总运费
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal totalSelectedOrdersCarriageFee;

    /**
     * 选中运单累计总调度费
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal totalSelectedOrdersDispatchFee;

    /**
     * 选中运单服务费金额
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal totalSelectedOrdersServiceFee;

    /**
     * 选中运单其他费用金额
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal totalSelectedOrdersOtherFee;

    /**
     * 选中运单累计总费用
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal totalSelectedOrdersFee;

    /**
     * 选中运单累计总运费的抹零金额
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal totalSelectedOrdersCarriageZeroCutFee;

    /**
     * 打包约定应支付总运费现金金额
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal appointmentPaymentCash;

    /**
     * 打包约定应支付总运费其他实物价值
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal appointmentPaymentOther;

    /**
     * 按应支付总运费现金金额重新计算的调度费
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal recountDispatchFee;

    /*
     * <AUTHOR>
     * @Description 支付方式
     * @Date 2019/11/22 10:50
    **/
    private String feeSettlementWay;

    private String payChildStatus;

    private String orderPayStatusCode;

    private String cardNo;

    private String cardOwner;
    /** 提现时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "GMT+8")
    private Date yfkTxTime;

    /*
     * <AUTHOR>
     * @Description 调度费系数
     * @Date 2020/1/7 16:21
    **/
    private BigDecimal currentDispatchRate;

    private Boolean status;

    /** 资金转移模式 */
    private String capitalTransferPattern;

    /** 是否业务部辅助 */
    private Boolean businessAssist;

    //货物类型
    private String goodsName;

    private String carOwnerName;

    private String carOwnerPhone;

    private String carOwnerStatus;

    private String vehicleNumber;

    private String lsCalculationStr;

    private Boolean lsCalculation;

    private Integer agentId;

    private BigDecimal userConfirmServiceFee;

    private String paymentPlatforms;

    private BigDecimal rulePaymentAmount;
}
