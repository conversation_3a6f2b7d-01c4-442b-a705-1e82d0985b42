package com.lz.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lz.common.util.BigDecimalSerialize;
import com.lz.common.util.DoubleSerialize;
import com.lz.model.TOrderInfoWeight;
import com.lz.vo.TOrderInfoWeightVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * PC运单管理-收单
 * Yan
 */
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CollectOrderInfoDTO {
    /** 运单32 Code */
    private String orderCode;
    /** 运单ID */
    private Integer orderId;
    /** 起点 */
    private String fromName;
    /** 终点 */
    private String endName;
    /** 发单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliverOrderTime;
    /**  货物类型*/
    private String goodsName;
    /**  发货联系人*/
    private String deliverUser;
    /**  收货联系人 */
    private String receiveUser;
    /** 自定义业务Id */
    private String orderBusinessCode;
    /** 资金转移方式，根据资金转移方式CODE查询收款人 和 收款账户 */
    private String capitalTransferType;
    /** 司机姓名联系方式 */
    private String driverUser;
    /** 司机ID */
    private Integer endDriverId;
    /** 车牌号 */
    private String vehicleNumber;
    /** 车辆ID */
    private Integer vehicleId;
    /** 运单的执行 */
    private String orderState;
    /** 收款人姓名 */
    private String receiverName;
    /** 收款人收款账户 */
    private String receiverAccount;
    /** 收款人收款账户 */
    private List<HashMap> receiverBankCard;
    /** 运费单价 */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal carriageUnitPrice;
    /** 原发重量 */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double primaryWeight;
    /** 发货磅单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliverWeightNotesTime;
    /** 发货磅单照 */
    private String deliverWeightNotesPhoto;
    /** 卸货重量 */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double dischargeWeight;
    /** 收货磅单时间 */
    private Date receiveWeightNotesTime;
    /** 收货磅单照 */
    private String receiveWeightNotesPhoto;
    /** 容忍值CODE */
    private String toleranceItem;
    @JsonSerialize(using = DoubleSerialize.class)
    private Double tolerantValueCoefficient;
    @JsonSerialize(using = DoubleSerialize.class)
    private Double tolerantValueWeight;

    /** 线路货物（运费）计算规则 子 表ID */
    private Integer lineGoodsCarriageRuleId;
    /** 结算规则名称 */
    private String ruleName;
    /** 线路名称 */
    private String lineName;
    /** 货值单价 */
    private BigDecimal goodsUnitPrice;
    /** 扣水 */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double goodsCutWater;
    /** 扣杂 */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double goodsCutImpurities;
    /** 容忍值 */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double toleranceItemValue;
    /** 容忍值类型名 */
    private String itemValue;
    /** 亏吨涨吨 */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double loseOrRise;
    /** 扣亏金额（亏吨涨吨扣款） */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal loseOrRiseCut;
    /** 其他扣款金额总和 */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal otherCutFeeCount;
    /** 抹零金额 */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal carriageZeroCutPayment;
    /** 抹零规则CODE */
    private String carriageZeroCutPaymentRule;
    /** 结算重量 */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double settledWeight;
    /** 应付金额 */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal carriagePayment;
    /** 收单员电话 */
    private String currentAccountPhone;
    /** 运单标志 */
    private String orderSign;
    /** 收单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receiverOrderTime;
    //没有涨损重量
    /** 线路货物（运费）单价变动表ID */
    private Integer lineGoodsCarriageChangeId;
    /** （亏吨）应付运费表达式 */
    private String lossPayableExpression;
    /** （涨吨）应付运费表达式 */
    private String risePayableExpression;
    /** 发货磅单上的时间 */
    private Date deliverWeightNotesUploadTime;
    /** 收货磅单上的时间 */
    private Date receiveWeightNotesUploadTime;
    /** 固定扣款金额  */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal fixCutFee;
    /** 固定扣款备注  */
    private String fixCutRemark;
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal otherCutFee1;
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal otherCutFee2;
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal otherCutFee3;
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal otherCutFee4;
    /**
     * 其他扣款备注1
     */
    private String otherCutRemark1;

    /**
     * 其他扣款备注2
     */
    private String otherCutRemark2;

    /**
     * 其他扣款备注3
     */
    private String otherCutRemark3;

    /**
     * 其他扣款备注4
     */
    private String otherCutRemark4;
    /** 扣水是否计算货值单价 */
    private Boolean cutWaterIsCalcvalue;
    /** 扣杂是否计算货值单价 */
    private Boolean cutImpuritiesIsCalcvalue;


    private List<Map<String, Object>> payment;


    /** 用户确认的应付运费 add by zhangjiji 2019/6/13*/
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal userConfirmCarriagePayment;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payTime;

    private String orderPayStatus;
    private Date withdrawTime;

    private String tradeStatus;

    /**
     * @Description 合同状态
     * <AUTHOR>
     * @Date   2019/7/4 16:15
     *
     */
    private String contractStatus;

    /**
     * @Description 亏损重量
     * <AUTHOR>
     * @Date   2019/7/4 16:28
     *
     */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double deficitWeight;

    private Integer lineGoodsRelId;

    private String remark;

    private String receiveGoodsContacterPhone;

    private String deliverWeightNotesWeight;

    private String receiveWeightNotesWeight;

    /**
     * 提现方式
     */
    private String withdrawType;

    /*
     * <AUTHOR>
     * @Description 支付流程 1: 支付需要审核  0: 支付不需要审核
     * @Date 2019/11/19 8:34
     **/
    private Integer paymentProcess;

    private String payChildStatus;

    private boolean payReviewPms;

    /*
     * <AUTHOR>
     * @Description 支付审核意见
     * @Date 2019/11/25 11:59
    **/
    private String paymentAuditRemark;

    private String orderPayStatusCode;

    /** 运单支付：判断是否有支付权限 */
    private boolean orderPayPms;

    private Boolean orderUnqualified;

    //经纪人
    private String endAgentName;

    //车老板
    private String endCarOwnerName;

    private Integer endCarOwnerId;

    // 车主姓名
    private String realNameRel;

    // 车主手机号
    private String phoneRel;

    // 车主身份证号码
    private String idCardRel;

    private Integer endAgentId;

    /*
     * <AUTHOR>
     * @Description资金转移模式
     * @Date 2020/3/3 4:56 下午
    **/
    private String capitalTransferPattern;

    /*
     * <AUTHOR>
     * @Description是否可修改经纪人服务费
     * @Date 2020/3/3 5:00 下午
    **/
    private Boolean updateServiceFee;

    /*
     * <AUTHOR>
     * @Description是否业务部辅助
     * @Date 2020/3/3 5:05 下午
    **/
    private Boolean businessAssist;

    /*
     * <AUTHOR>
     * @Description服务费抽成方式 ： FIXEDLIMIT 固定金额 FIXEDPROPORTION 固定比例
     * @Date 2020/3/3 7:12 下午
    **/
    private String shareMethod;

    /*
     * <AUTHOR>
     * @Description服务费抽成数值
     * @Date 2020/3/3 7:12 下午
    **/
    private BigDecimal shareValue;

    /*
     * <AUTHOR>
     * @Description规则计算服务费
     * @Date 2020/3/3 7:43 下午
    **/
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal serviceFee;

    /*
     * <AUTHOR>
     * @Description用户确认服务费
     * @Date 2020/3/3 7:43 下午
    **/
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal userConfirmServiceFee;

    /**
     * 预估总费用
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal estimateTotalFee;

    /**
     * 装货应付运费
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal zhRulePaymentFee;

    /**
     * 卸货应付运费
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal xhRulePaymentFee;

    /**
     * 收单应付运费
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal sdRulePaymentFee;

    /**
     * 尾款应付运费
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal wkRulePaymentFee;

    /**
     * 装货已付运费
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal zhPaymentFee;

    /**
     * 卸货已付运费
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal xhPaymentFee;

    /**
     * 收单已付运费
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal sdPaymentFee;

    /**
     * 尾款已付运费
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal wkPaymentFee;

    /**
     * 装货的备注
     */
    private String zhRemark;

    /**
     * 卸货的备注
     */
    private String xhRemark;

    /**
     * 收单的备注
     */
    private String sdRemark;

    /**
     * 尾款的备注
     */
    private String wkRemark;

    private String paymentPlatforms;

    private Boolean firstPay;

    private Boolean yfk;

    /**  运单生成类型 */
    private String orderCreateType;

    /** 毛重 */
    @JsonSerialize(using = DoubleSerialize.class)
    private Double grossWeight;

    /**
     *  结算重量取值  1 按最小   2 按原发
     */
    private Integer settledWeightType;

    //超过约定物损时是否全扣 0否  1 是
    private Integer ifExceedAmount;

    private String carriagePriceUnit;

    private Integer boxNum;
    private Date deliverWeightNotesTime1;
    private BigDecimal deliverWeightNotesWeight1;
    private String deliverWeightNotesPhoto1;
    private Date receiveWeightNotesTime1;
    private BigDecimal receiveWeightNotesWeight1;
    private String receiveWeightNotesPhoto1;
    private BigDecimal grossWeight1;
    private BigDecimal primaryWeight1;
    private BigDecimal dischargeWeight1;
    private BigDecimal carriageUnitPrice1;

    private Date deliverWeightNotesTime2;
    private BigDecimal deliverWeightNotesWeight2;
    private String deliverWeightNotesPhoto2;
    private Date receiveWeightNotesTime2;
    private BigDecimal receiveWeightNotesWeight2;
    private String receiveWeightNotesPhoto2;
    private BigDecimal grossWeight2;
    private BigDecimal primaryWeight2;
    private BigDecimal dischargeWeight2;
    private BigDecimal carriageUnitPrice2;


    /** 当运费单价单位是元/箱时会用到 */
    private TOrderInfoWeight orderInfoWeight;

    /** order_info_weight 表ID */
    private Integer orderInfoWeightId;

    /** 固定扣款金额  */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal fixCutFee2;
    /** 固定扣款备注  */
    private String fixCutRemark2;

    //装货地签到图片
    private String loadingCarPhoto;

    private String loadingCarPhotoInfo;

    //卸货地签到图片
    private String unloadCarPhoto;

    private String unloadCarPhotoInfo;

    //保费金额
    //private BigDecimal insuredAmount;
}

