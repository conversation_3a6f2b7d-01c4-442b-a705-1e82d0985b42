package com.lz.api;

import com.lz.common.util.ResultUtil;
import com.lz.model.TOrderInfo;
import com.lz.vo.DataToEtcVO;
import com.lz.vo.TOrderInfoVO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * 运单
 */
@FeignClient(value = "lgt-app-order")
public interface TOrderInfoAPI {

    /**
     * 根据运单编号查询
     * <AUTHOR>
     * @param
     * @return
     */
    @RequestMapping(value = "/tOrderInfo/selectByOrderCode", method = RequestMethod.POST)
    String selectByOrderCode(@RequestParam(value = "code") String code);

    /**
     * 修改
     * <AUTHOR>
     * @param
     * @return
     */
    @RequestMapping(value = "/tOrderInfo/edit", method = RequestMethod.POST)
    ResultUtil edit(@RequestBody TOrderInfo record);

    /**
     * 根据运单支付主表code查询
     * <AUTHOR>
     * @param
     * @return
     */
    @RequestMapping(value = "/tOrderInfo/selectByOuterTradeNo", method = RequestMethod.POST)
    TOrderInfo selectByOuterTradeNo(@RequestParam(value = "outerTradeNo") String outerTradeNo);

    /**
     * 根据运单支付主表orderBusinessCode查询
     * <AUTHOR>
     * @param
     * @return
     */
    @RequestMapping(value = "/tOrderInfo/selectByOrderBusinessCode", method = RequestMethod.POST)
    TOrderInfo selectByOrderBusinessCode(@RequestParam(value = "orderBusinessCode") String orderBusinessCode);
    /**
     * 根据运单支付主表code查询
     * @param
     * <AUTHOR>
     * @return
     */
    @RequestMapping(value = "/tOrderInfo/selectByCarrierId", method = RequestMethod.POST)
    String selectByCarrierId(@RequestParam(value = "outerTradeNo") String outerTradeNo);

    /**
     * 根据运单支付主表code查询承运方验签公钥
     * @param
     * <AUTHOR>
     * @return
     */
    @RequestMapping(value = "/tOrderInfo/selectByThirdPartyInterfacePublicKey", method = RequestMethod.POST)
    TOrderInfoVO selectByThirdPartyInterfacePublicKey(@RequestParam(value = "outerTradeNo") String outerTradeNo);

    /**
     * 根据终端司机统计不同订单状态的订单数量
     * @param
     * <AUTHOR>
     * @return
     */
    @RequestMapping(value = "/tOrderInfo/selectByEndDriverId", method = RequestMethod.POST)
    Integer selectByEndDriverId(@RequestParam(value = "enduserinfoid") Integer enduserinfoid,@RequestParam(value = "status") String status, @RequestParam(value = "ctype") String ctype);


    /**
     * 根据终端司机统计待评价订单数量
     * @param
     * <AUTHOR>
     * @return
     */
    @RequestMapping(value = "/tOrderInfo/selectByNoAppraiseCount", method = RequestMethod.POST)
    Integer selectByNoAppraiseCount(@RequestParam(value = "enduserinfoid") Integer enduserinfoid,@RequestParam(value = "status") String status, @RequestParam(value = "ctype") String ctype);

    /*
     * @Description  从task读取回调后，执行任务逻辑
     * @param requestParameter
     * @return com.lz.common.util.ResultUtil
     * <AUTHOR>
     * @Date 2019/5/20 15:00
     **/
    @RequestMapping(value = "/callback/entry", method = RequestMethod.POST)
    ResultUtil callBackEntry(@RequestParam("msg") String msg);
    
    /**
     *  @author:  dwb
     *  @Date: 2019/6/28 15:47
     *  @Description: 根据单号查询订单信息（财务系统使用）
     */
    @RequestMapping(value = "/tOrderInfo/selectByOrderCodeList", method = RequestMethod.POST)
    List<TOrderInfo> selectByOrderCodeList(@RequestParam("orderCodeStr") String orderCodeStr);
    /**
     *  @author:  dwb
     *  @Date: 2019/6/28 15:47
     *  @Description: 根据单号查询订单信息（财务系统使用）
     */
    @RequestMapping(value = "/tOrderInfo/selectByOrderCodeListName", method = RequestMethod.POST)
    String selectByOrderCodeListName(@RequestParam("orderCodeStr") String orderCodeStr);

    @RequestMapping(value = "/tOrderInfo/selectByOrderCodeListNamePage", method = RequestMethod.POST)
    ResultUtil selectByOrderCodeListNamePage(@RequestBody TOrderInfoVO tOrderInfoVO);

    @RequestMapping(value = "/tOrderInfo/selectByListName", method = RequestMethod.POST)
    List<TOrderInfoVO> selectByListName(@RequestBody TOrderInfoVO tOrderInfoVO);

    /**
     * @Description 根据运单编号查询运单详情
     * <AUTHOR>
     * @Date   2019/6/13 19:03
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/tOrderInfo/selectOrderInfoByOrderCode")
    ResultUtil selectOrderInfoByOrderCode(@RequestBody TOrderInfoVO record);


    @PostMapping("/tOrderInfo/selectOrderInfoByCode")
    TOrderInfo selectOrderInfoByCode(@RequestParam(value = "code") String code);


    /**
    * @Description根据员工查询订单 feign
    * <AUTHOR>
    * @Date   2019/7/13 14:45
    * @Param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/tOrderInfo/selectByAccountIdFeign")
    List<TOrderInfo> selectByAccountIdFeign(@RequestParam(value = "accountId") Integer accountId);

    @PostMapping("/tOrderInfo/selectByAccountIdAnrLineIdFeign")
    List<TOrderInfo> selectByAccountIdAnrLineIdFeign(@RequestBody TOrderInfoVO record);

    @PostMapping("/tOrderInfo/selectByAccountIdAnrLineIdCountFeign")
    ResultUtil selectByAccountIdAnrLineIdCountFeign(@RequestBody TOrderInfoVO record);

    @PostMapping("/tOrderInfo/selectByAccountIdAnrLineGoodsRelIdCountFeign")
    ResultUtil selectByAccountIdAnrLineGoodsRelIdCountFeign(@RequestBody TOrderInfoVO record);

    /**
     * @Description根据司机id查询订单 feign
     * <AUTHOR>
     * @Date   2019/7/13 14:45
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/tOrderInfo/selectByEndDriverIdFeign")
    List<TOrderInfo> selectByEndDriverIdFeign(@RequestParam(value = "endUserId") Integer endUserId);

    @PostMapping("/tOrderInfo/selectByEndDriverIdFeignHt")
    Boolean selectByEndDriverIdFeignHt(@RequestParam(value = "endUserId") Integer endUserId);


    @PostMapping("/tOrderInfo/selectByEndDriverIdAnrCarIdFeign")
    List<TOrderInfo> selectByEndDriverIdAnrCarIdFeign(@RequestParam(value = "endUserId") Integer endUserId,@RequestParam(value = "endCarId") Integer endCarId);

    /**
     * @Description根据车主id查询订单 feign
     * <AUTHOR>
     * @Date   2019/7/13 14:45
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/tOrderInfo/selectByEndCarOwnerIdFeign")
    List<TOrderInfo> selectByEndCarOwnerIdFeign(@RequestParam(value = "endUserId") Integer endUserId);

    /**
     * @Description根据线路货物关系Id查询运单
     * <AUTHOR>
     * @Date   2019/7/24 8:55
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/tOrderInfo/selectByLineGoodsRelId")
    List<TOrderInfo> selectByLineGoodsRelId(@RequestParam(value = "lineGoodsRelId") Integer lineGoodsRelId);

    /**
     * @Description根据车辆id查询订单 feign
     * <AUTHOR>
     * @Date   2019/7/24 19:45
     * @Param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/tOrderInfo/selectByEndCarIdFeign")
    List<TOrderInfo> selectByEndCarIdFeign(@RequestParam(value = "endCarId") Integer endCarId);

    @PostMapping("/tOrderInfo/selectByEndCarIdAnrUserIdFeign")
    List<TOrderInfo> selectByEndCarIdAnrUserIdFeign(@RequestParam(value = "endCarId") Integer endCarId,@RequestParam(value = "endUserId") Integer endUserId);

    /**
     * @Description 查询经纪人是否有未完成的运单: Fegin接口，勿修改勿使用
     * <AUTHOR>
     * @Date   2019/8/24 18:13
     * @param
     * @Return
     * @Exception
     *
     */
    @PostMapping("/tOrderInfo/selectAgentManagerUnCompleteOrder")
    ResultUtil selectAgentManagerUnCompleteOrder(@RequestBody List<HashMap<String, Integer>> record);


    /**
    * @Description 执行定时任务补充运单轨迹
    * <AUTHOR>
    * @Date   2020/1/8 10:01
    * @Param
    * @Return
    * @Exception
    *
    */
    @PostMapping("/tOrderInfo/carTrajectory")
    ResultUtil carTrajectory(@RequestBody TOrderInfoVO tOrderInfoVO);

    @PostMapping("/tOrderInfo/feedbackInfo")
    ResultUtil getOrderFeedbackInfo(@RequestParam("orderCode") String orderCode);

    @PostMapping("/scoreDetail/qzUserScore")
    public ResultUtil qzUserScore();

    @PostMapping("/tOrderInfo/selectByEtcDate")
    DataToEtcVO selectByEtcDate(@RequestParam("orderCode") String orderCode);

}
