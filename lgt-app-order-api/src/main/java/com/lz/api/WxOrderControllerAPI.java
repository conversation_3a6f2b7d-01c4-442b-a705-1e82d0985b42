package com.lz.api;

import com.lz.common.util.ResultUtil;
import com.lz.vo.HxOrderTxVO;
import com.lz.vo.TOrderInfoVO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;


/**
 *  @author: dingweibo
 *  @Date: 2023/4/4 10:20
 *  @Description:
 */
@FeignClient(value = "lgt-app-order")
public interface WxOrderControllerAPI {

    @PostMapping("/wx/order/hx/tx")
    public ResultUtil hxTx(@RequestBody HxOrderTxVO txVO);
}
