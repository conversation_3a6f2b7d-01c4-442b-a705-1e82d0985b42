package com.lz.api;


import com.lz.common.model.hxPayment.response.CustomerBalancePayRes;
import com.lz.common.model.hxPayment.response.CustomerWithdrawRes;
import com.lz.common.util.ResultUtil;
import com.lz.model.TOrderPayDetail;
import com.lz.vo.CallbackOrderInfoVO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import sdk.model.message.AsyncNotifyVirtualBalancePayMessageBody;
import sdk.model.message.AsyncNotifyVirtualMemberWithdrawMessageBody;

/**
 *  @author: dingweibo
 *  @Date: 2022/11/25 10:16
 *  @Description:  华夏支付回调处理类
 */
@FeignClient(value = "lgt-app-order")
public interface HXCallbackAPI {

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/25 10:18
     *  @Description: 余额支付回调
     */
    @PostMapping("/hx/callback/entry/balance/pay/notice")
    ResultUtil balancePayNotice(@RequestBody AsyncNotifyVirtualBalancePayMessageBody messageBody);

    /**
     * @description 提现回调
     * <AUTHOR>
     * @date 2021/9/17 15:15
     */
    @PostMapping("/hx/callback/entry/withdraw/tx/notice")
    ResultUtil txNotice(@RequestBody AsyncNotifyVirtualMemberWithdrawMessageBody messageBody);

    /**
     * @description 交易退款结果通知消息
     * <AUTHOR>
     * @date 2021/8/16 09:02
     */
    @PostMapping("/hx/callback/entry/refund/notice")
    ResultUtil refundNotice(@RequestBody AsyncNotifyVirtualBalancePayMessageBody messageBody);

    /**
     * @description 打包交易退款结果通知消息
     * <AUTHOR>
     * @date 2021/10/11 11:02
     */
    @PostMapping("/hx/callback/entry/pack/refund/notice")
    ResultUtil packRefundNotice(@RequestBody AsyncNotifyVirtualBalancePayMessageBody messageBody);

    /**
    * @description 京东预付款余额支付通知
    * <AUTHOR>
    * @date 2021/11/8 13:38
    */
    @PostMapping("/hx/callback/entry/pre/pay/notice")
    ResultUtil prePayNotice(@RequestBody AsyncNotifyVirtualBalancePayMessageBody messageBody);

    @PostMapping("/hx/callback/entry/pay/fail")
    ResultUtil payFail(@RequestBody CustomerBalancePayRes response);

    @PostMapping("/hx/callback/entry/tx/fail")
    ResultUtil txFail(@RequestBody CustomerWithdrawRes response);

    /**
    * @description 补交账户服务费回调
    * <AUTHOR>
    * @date 2021/12/3 17:05
    */
    @PostMapping("/hx/callback/entry/account/servicefee/callback")
    ResultUtil accountServiceFeeCallback(@RequestBody AsyncNotifyVirtualBalancePayMessageBody messageBody);

    /**
     * 查询支付子表
     * @param
     * @return
     */
    @PostMapping("/hx/callback/entry/queryOrderPayDetail")
    TOrderPayDetail queryOrderPayDetail(@RequestBody TOrderPayDetail orderPayDetail);

    /**
     * 承运方余额支付(企业充值)回调
     * @param messageBody
     * @return
     */
    @PostMapping("/hx/callback/entry/carrier/balance/company/charge/callback")
    ResultUtil carrierBalanceCompanyChargeCallback(@RequestBody AsyncNotifyVirtualBalancePayMessageBody messageBody);

    /**
     * @description 根据支付字表查询运单信息
     * <AUTHOR>
     * @date 2021/8/20 11:05
     * @param
     * @return
     */
    @PostMapping("/hx/callback/entry/orderinfo")
    ResultUtil getCallbackOrderInfoByPayDetailCode(@RequestBody CallbackOrderInfoVO vo);

}
