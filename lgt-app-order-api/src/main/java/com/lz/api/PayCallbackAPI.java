package com.lz.api;


import com.lz.common.model.hxPayment.response.CustomerBalancePayRes;
import com.lz.common.util.ResultUtil;
import com.lz.vo.CallbackOrderInfoVO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import sdk.model.message.AsyncNotifyVirtualBalancePayMessageBody;

/**
 *  @author: dingweibo
 *  @Date: 2022/11/25 10:16
 *  @Description:  华夏支付回调处理类
 */
@FeignClient(value = "lgt-app-order")
public interface PayCallbackAPI {

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/25 10:18
     *  @Description: 余额支付回调
     */
    @PostMapping("/pay/callback/entry/balance/pay/notice")
    ResultUtil balancePayNotice(@RequestBody AsyncNotifyVirtualBalancePayMessageBody messageBody);


    @PostMapping("/pay/callback/entry/pay/fail")
    ResultUtil payFail(@RequestBody CustomerBalancePayRes response);

    /**
     * @description 根据支付字表查询运单信息
     * <AUTHOR>
     * @date 2021/8/20 11:05
     * @param
     * @return
     */
    @PostMapping("/pay/callback/entry/pay/detail")
    ResultUtil getCallbackOrderInfoByPayDetailCode(@RequestBody CallbackOrderInfoVO vo);

}
