package com.lz.api;


import com.lz.common.util.ResultUtil;
import com.lz.model.TOrderInsurance;
import com.lz.vo.TOrderInsuranceVO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */

@FeignClient(value = "lgt-app-order")
public interface TOrderInsuranceAPI {


    /**
     * 按运单code修改数据
     */
    @PostMapping("/orderInsurance/updateByOrderBusinessCode")
    public void updateByOrderBusinessCode(@RequestBody TOrderInsuranceVO search);

    /**
     * 按运单Code查询数据
     */
    @PostMapping("/orderInsurance/selectByOrderBusinessCode")
    public TOrderInsurance selectByOrderBusinessCode(@RequestParam("orderBusinessCode") String orderBusinessCode);

    /**
     * 按表ID修改数据
     */
    @PostMapping("/orderInsurance/updateByPrimaryKeySelective")
    public void updateByPrimaryKeySelective(@RequestBody TOrderInsurance orderInsurance);
}
