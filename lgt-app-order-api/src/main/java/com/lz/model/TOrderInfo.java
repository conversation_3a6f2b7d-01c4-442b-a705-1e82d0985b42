package com.lz.model;

import com.lz.common.util.PageInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * t_order_info
 * <AUTHOR>
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TOrderInfo extends PageInfo implements Serializable {
    /**
     * 表主键ID
     */
    private Integer id;

    /**
     * 表业务id
     */
    private String code;

    /**
     * 运单编号
     */
    private String orderBusinessCode;

    /**
     * 货源车辆司机信息子表业务ID
     */
    private String goodsSourceVehicleDriverInfoCode;

    /**
     * 运单资源信息表业务ID
     */
    private String goodSourceCode;

    /**
     * 承运方ID
     */
    private Integer carrierId;

    /**
     * 线路货物关系表ID
     */
    private Integer lineGoodsRelId;

    /**
     * 线路信息ID
     */
    private Integer lineId;

    /**
     * 企业ID
     */
    private Integer companyId;

    /**
     * 项目ID
     */
    private Integer companyProjectId;

    /**
     * 线路编号
     */
    private String lineCode;

    /**
     * 路线名称
     */
    private String lineName;

    /**
     * 路线名称简写
     */
    private String lineShortName;

    /**
     * 起点城市编码
     */
    private String cityFromCode;

    /**
     * 起点名称（简称）
     */
    private String fromName;

    /**
     * 起点坐标
     */
    private String fromCoordinates;

    /**
     * 终点城市编码
     */
    private String cityEndCode;

    /**
     * 终点名称（简称）
     */
    private String endName;

    /**
     * 终点坐标
     */
    private String endCoordinates;

    /**
     * 线路类型
     */
    private String lineType;

    /**
     * 货物信息ID
     */
    private Integer goodsId;

    /**
     * 货物名称
     */
    private String goodsName;

    /**
     * 货物大类CODE
     */
    private String bigKindCode;

    /**
     * 货物单位
     */
    private String goodsUnit;

    /**
     * 货源重量（预估重量）
     */
    private Double estimateGoodsWeight;

    /**
     * 预计装货时间
     */
    private Date estimateLoadTime;

    /**
     * 货源单价（预估单价）
     */
    private BigDecimal currentCarriageUnitPrice;

    /**
     * 预估总费用
     */
    private BigDecimal estimateTotalFee;

    /**
     * 发单员账号ID
     */
    private Integer deliverOrderUserId;

    /**
     * 收单员账号ID
     */
    private Integer receiveOrderUserId;

    /**
     * 委托发单员账号ID
     */
    private Integer principalOrderUserId;

    /**
     * 发单时间
     */
    private Date deliverOrderTime;

    /**
     * 收单时间
     */
    private Date receiveOrderTime;

    /**
     * 运单完成时间
     */
    private Date orderFinishTime;

    /**
     * 发货磅单时间
     */
    private Date deliverWeightNotesTime;

    /**
     * 发货磅单重量
     */
    private Double deliverWeightNotesWeight;

    /**
     * 原发重量
     */
    private Double primaryWeight;

    /**
     * 收货磅单时间
     */
    private Date receiveWeightNotesTime;

    /**
     * 收货磅单重量
     */
    private Double receiveWeightNotesWeight;

    /**
     * 卸货重量
     */
    private Double dischargeWeight;

    /**
     * 毛重
     */
    private Double grossWeight;

    /**
     * 规则计算的应付运费金额
     */
    private BigDecimal rulePaymentAmount;

    /**
     * 发货磅单照片
     */
    private String deliverWeightNotesPhoto;

    /**
     * 收货磅单照片
     */
    private String receiveWeightNotesPhoto;

    /**
     * 结算重量
     */
    private BigDecimal settledWeight;

    /**
     * 用户确认的应付运费金额
     */
    private BigDecimal userConfirmPaymentAmount;

    /**
     * 签订合同状态
     */
    private String contractStatus;

    /**
     * 运输车辆ID
     */
    private Integer vehicleId;

    /**
     * 运输司机ID
     */
    private Integer endDriverId;

    /**
     * 调度费金额
     */
    private BigDecimal dispatchFee;

    /**
     * 服务费金额
     */
    private BigDecimal serviceFee;

    /**
     * 其他费用金额
     */
    private BigDecimal otherFee;

    /**
     * 总费用金额
     */
    private BigDecimal totalFee;

    /**
     * C端车辆所有人ID
     */
    private Integer endCarOwnerId;

    /**
     * C端业务部ID
     */
    private Integer endAgentId;

    /**
     * C端经纪人ID
     */
    private Integer agentId;

    /**
     * 车辆与司机关系表ID
     */
    private Integer endUserCarRelId;

    /**
     * 发货联系人
     */
    private String deliverGoodsContacter;

    /**
     * 发货联系人电话
     */
    private String deliverGoodsContacterPhone;

    /**
     * 收货联系人
     */
    private String receiveGoodsContacter;

    /**
     * 收货联系人电话
     */
    private String receiveGoodsContacterPhone;

    /**
     * 费用结算模式
     */
    private String feeSettlementWay;

    /**
     * 运单生成类型
     */
    private String orderCreateType;

    /**
     * 运单执行状态
     */
    private String orderExecuteStatus;

    /**
     * 车辆北斗状态
     */
    private String vehicleGpsBdStatus;

    /**
     * 运单支付状态
     */
    private String orderPayStatus;

    /**
     * 企业的承运方
     */
    private String companyEntrust;

    /**
     * 企业的客户
     */
    private String companyClient;

    /**
     * 打包状态 默认0未打包  1已打包
     */
    private String packStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 扩展字段1
     */
    private String param1;

    /**
     * 扩展字段2
     */
    private String param2;

    /**
     * 扩展字段3
     */
    private String param3;

    /**
     * 扩展字段4
     */
    private String param4;

    /**
     * 扩展字段5
     */
    private String param5;

    /**
     * 操作方式
     */
    private String operateMethod;

    /**
     * 操作IP地址
     */
    private String operatorIp;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 : 1 true 已删除   ||||   0 false 未删除
     */
    private Boolean enable;

    /**
     * 转历史时间
     */
    private Date turnHistoryTime;

    /**
     * 转历史方式
     */
    private String turnHistoryType;

    /**
     * 转历史操作人
     */
    private String turnHistoryOperator;

    /**
     * 打包后的运费
     */
    private BigDecimal sharePaymentAmount;

    /**
     * 打包后的调度费
     */
    private BigDecimal shareDispatchFee;

    /*
     * <AUTHOR>
     * @Description 不合格备注
     * @Date 2020/1/6 15:49
     **/
    private String unqualifiedRemark;

    /*
     * <AUTHOR>
     * @Description用户确认的服务费
     * @Date 2020/3/3 3:32 下午
    **/
    private BigDecimal userConfirmServiceFee;

    /**
     * <AUTHOR>
     * @Description 是否业务不辅助 1：是 0：否
     * @Date 2020/3/10 6:52 下午
     * @Param 
     * @return 6:52 下午
    **/
    private Boolean businessAssist;

    private static final long serialVersionUID = 1L;

    //货源随机数，扫码接单后插入
    private String randomHy;

    /**
     *  司机端APP签到:默认flase  0,签到后改为true  1
     */
    private Boolean uploadData;

    //NODEPAYPROPORTION 节点支付(按比例) NODEPAYFIXED //节点支付按固定值  SINGLEPAY 单笔支付  PACKPAY 打包支付
    private String payMethod;

    //山东上报状态
    private String sduploadedStatus;

    //安徽上报状态
    private String ahuploadedStatus;

}