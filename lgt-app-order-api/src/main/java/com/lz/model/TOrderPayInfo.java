package com.lz.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * t_order_pay_info
 * <AUTHOR>
@Data
public class TOrderPayInfo implements Serializable {
    /**
     * 表主键ID
     */
    private Integer id;

    /**
     * 表业务ID
     */
    private String code;

    /**
     * 运单支付方式
     */
    private String payMethod;


    /**
     * 承运方id
     */
    private Integer carrierId;

    /**
     * 运单主表业务ID
     */
    private String orderCode;

    /**
     * 费用结算模式
     */
    private String feeSettlementWay;

    /**
     * 线路货物（运费）支付规则表ID
     */
    private Integer lineGoodsCarriageRuleId;

    /**
     * 运单支付状态
     */
    private String orderPayStatus;

    /**
     * 支付平台：网商；京东
     */
    private String paymentPlatforms;

    /**
     * 运单预计支付金额
     */
    private BigDecimal orderPrepayAmount;

    /**
     * 运单实际应付金额
     */
    private BigDecimal orderActualPayment;

    /**
     * 运单累计支付金额
     */
    private BigDecimal orderTotalPayment;

    /**
     * 备注
     */
    private String remark;

    /**
     * 扩展字段1
     */
    private String param1;

    /**
     * 扩展字段2
     */
    private String param2;

    /**
     * 扩展字段3
     */
    private String param3;

    /**
     * 扩展字段4
     */
    private String param4;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */

    private Date createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */

    private Date updateTime;

    /**
     * 是否删除 : 1 true 已删除   ||||   0 false 未删除
     */
    private Boolean enable;

    private static final long serialVersionUID = 1L;

}