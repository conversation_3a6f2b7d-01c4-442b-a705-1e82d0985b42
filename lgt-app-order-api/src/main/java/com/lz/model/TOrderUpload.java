package com.lz.model;

import java.io.Serializable;
import java.util.Date;

import com.lz.common.util.PageInfo;
import lombok.Data;

/**
 * t_order_upload
 * <AUTHOR>
@Data
public class TOrderUpload extends PageInfo implements Serializable {
    /**
     * 表主键ID
     */
    private Integer id;

    /**
     * t_order_info的order_code
     */
    private String orderCode;

    /**
     * 0:未请求1：成功2：失败 装货地签到
     */
    private Integer receiptUploadStatus;

    /**
     * 装货地签到运单上传时间
     */
    private Date receiptUploadDate;

    /**
     * 0:未请求1：成功2：失败 收货
     */
    private Integer tradeUploadStatus;

    /**
     * 收货运单上传时间
     */
    private Date tradeUploadDate;

    /**
     * 0:未请求1：成功2：失败 支付完成
     */
    private Integer payUploadStatus;

    /**
     * 支付完成运单上传时间
     */
    private Date payUploadDate;

    /**
     * 0:未请求1：成功2：失败
     */
    private Integer flowUploadStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;
}