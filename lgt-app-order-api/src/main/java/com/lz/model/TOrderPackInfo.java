package com.lz.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * t_order_pack_info
 * <AUTHOR>
@Data
public class TOrderPackInfo implements Serializable {
    /**
     * 表主键ID
     */
    private Integer id;

    /**
     * 表业务ID
     */
    private String code;

    /**
     * 虚拟单据号
     */
    private String virtualOrderNo;

    /**
     * 操作用户（对账人）
     */
    private String checkAccountPerson;

    /**
     * 查询单据开始日期
     */
    private Date orderStartQueryTime;

    /**
     * 查询单据结束日期
     */
    private Date orderEndQueryTime;

    /**
     * 所属C端用户ID
     */
    private Integer endUserId;

    /**
     * 所属C端车辆ID
     */
    private Integer endVehicleId;

    /**
     * 选中运单累计单据数
     */
    private Integer totalSelectedOrders;

    /**
     * 选中运单累计总吨数
     */
    private Double totalSelectedOrdersWeight;

    /**
     * 选中运单累计总运费
     */
    private BigDecimal totalSelectedOrdersCarriageFee;

    /**
     * 选中运单累计总调度费
     */
    private BigDecimal totalSelectedOrdersDispatchFee;

    /**
     * 选中运单服务费金额
     */
    private BigDecimal totalSelectedOrdersServiceFee;

    /**
     * 选中运单其他费用金额
     */
    private BigDecimal totalSelectedOrdersOtherFee;

    /**
     * 选中运单累计总费用
     */
    private BigDecimal totalSelectedOrdersFee;

    /**
     * 选中运单累计总运费的抹零金额
     */
    private BigDecimal totalSelectedOrdersCarriageZeroCutFee;

    /**
     * 打包约定应支付总运费现金金额
     */
    private BigDecimal appointmentPaymentCash;

    /**
     * 打包约定应支付总运费其他实物价值
     */
    private BigDecimal appointmentPaymentOther;

    /**
     * 按应支付总运费现金金额重新计算的调度费
     */
    private BigDecimal recountDispatchFee;

    /**
     * 约定内容描述
     */
    private String contentDescribtion;

    /**
     * 打包状态
     */
    private String packStatus;

    /**
     * 承运方ID
     */
    private Integer carrierId;

    /**
     * 企业ID
     */
    private Integer companyId;

    /**
     * 银行卡表ID
     */
    private Integer bankCardId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 扩展字段1
     */
    private String param1;

    /**
     * 扩展字段2
     */
    private String param2;

    /**
     * 扩展字段3
     */
    private String param3;

    /**
     * 扩展字段4
     */
    private String param4;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 : 1 true 已删除   ||||   0 false 未删除
     */
    private Boolean enable;

    /**
     * 运单分摊系数
     */
    private BigDecimal shareCoefficient;

    private Boolean lsCalculation;

    /**
     * 支付平台：网商；京东
     */
    private String paymentPlatforms;

    private static final long serialVersionUID = 1L;

}