package com.lz.tpu.notify;

import com.google.gson.Gson;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.util.DataConvertUtil;
import com.lz.schedule.api.ScheduleAPI;
import com.lz.schedule.model.TTask;
import com.lz.tpu.common.util.ConfigUtil;
import com.lz.tpu.service.SecurityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.Map;

/**
 * 网商异步回调处理类
 * <AUTHOR>
 */
@Slf4j
@RequestMapping("/notify")
@RestController
public class WsCallbackController {

    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private ScheduleAPI scheduleAPI;

    @Autowired
    private SecurityService securityService;

    @Autowired
    private ConfigUtil ftpConfig;

    /**
     * 入账交易处理结果通知
     * 执行业务操作（验签） , 同时需要向订单系统和货源系统发送消息执行相关业务
     * @param request
     * @return
     */
    @RequestMapping("/callback")
    @ResponseBody
    public void incoming(HttpServletRequest request,HttpServletResponse response) throws Exception{
        log.info("---------网商回调结果通知-start");
        Gson gson = new Gson();
        Map<String,String> map = requestToMap(request);
        String json = gson.toJson(map);
        log.info("回调信息："+json);
        //判断验签是否成功
        if(securityService.verify(map,ftpConfig.getCharset(),map.get("sign"),"RSA")){
            getCallBackParamInTask(map, json);
            //xyz update 2019.6.13成功后才告诉网商
            sendSuccessResponse(response);
        }else{
            log.error("网商回调验签失败！");
        }
        log.info("--------网商回调结果通知-end");
    }

    private void getCallBackParamInTask(Map<String, String> map, String json) {
        if(map.get("notify_type").toString().equals("trade_status_sync")){
            String status = map.get("trade_status").toString();
            //成功失败放入task
            if(status.equals("TRADE_FINISHED") || status.equals("TRADE_FAILED")){
                insertTask("RZ","RZHD","code",map.get("outer_trade_no")+"",json);
            }
        }else if(map.get("notify_type").toString().equals("withdrawal_status_sync")){
            String status = map.get("withdrawal_status").toString();
            //成功失败放入task
            if(status.equals("TRADE_FINISHED") || status.equals("TRADE_FAILED")){
                insertTask("TX","TXHD","code",map.get("outer_trade_no")+"",json);
            }
        }else if(map.get("notify_type").toString().equals("remit_sync")){
            String status = map.get("status").toString();
            //成功放入task
            if(status.equals("SUCCESS")){
                insertTask("CZ","CZHD","code",map.get("outer_trade_no")+"",json);
            }
        } else if (map.get("notify_type").equals("refund_status_sync")) {
            // 分润模式退款回调
            String status = map.get("trade_status").toString();
            //成功放入task
            if(status.equals("REFUND_FINISH")){
                insertTask("RZ","CWZHHDR","code",map.get("outer_trade_no")+"",json);
            }
        }
    }

    private Map requestToMap(HttpServletRequest request){
        return DataConvertUtil.paramCharsetConvert(request.getParameterMap());
    }
    private void sendSuccessResponse(HttpServletResponse response) throws IOException {
        String succInfo = "success";
        response.setCharacterEncoding("utf8");
        response.setHeader("content-type", "text/html;charset=UTF-8");

        StringBuilder sb = new StringBuilder();
        sb.append(succInfo);
        response.getWriter().write(sb.toString());
    }

    private void insertTask(String taskType,String taskTypeNode,String fieldName,String fieldValue,String param){
        TTask task = new TTask();
        task.setTaskId(IdWorkerUtil.getInstance().nextId());
        task.setTaskType(taskType);//任务类型
        task.setTaskTypeNode(taskTypeNode);//任务类型节点
        task.setBusinessType("ZF");//业务类型
        task.setSourceTablename("t_order_pay_detail");
        task.setSourcekeyFieldname("id");
        task.setSourceFieldname(fieldName);
        task.setSourceFieldvalue(fieldValue);
        task.setRequestTimes(0);
        task.setRequestDate(new Date());
        task.setIsSuccessed(false);
        task.setEnable(false);
        task.setRequestParameter(param);
        scheduleAPI.insertTask(task);
    }
}
