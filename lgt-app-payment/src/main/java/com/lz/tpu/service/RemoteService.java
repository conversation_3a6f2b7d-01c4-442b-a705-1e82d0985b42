/**
 *
 */
package com.lz.tpu.service;

import com.alibaba.fastjson.JSON;
import com.lz.tpu.common.constant.GatewayConstant;
import com.lz.tpu.common.util.ConfigUtil;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.scheme.Scheme;
import org.apache.http.conn.ssl.SSLSocketFactory;
import org.apache.http.conn.ssl.X509HostnameVerifier;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.net.ssl.*;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <p>注释</p>
 * <AUTHOR>
 * @version $Id: PersonalService.java, v 0.1 2013-12-20 下午2:03:11 fjl Exp $
 */
@Service
public class RemoteService {
    private static Logger logger = LoggerFactory.getLogger(RemoteService.class);

    @Autowired 
	private ConfigUtil ftpConfig;
    
    public ConfigUtil getFtpConfig() {
		return ftpConfig;
	}


	public void setFtpConfig(ConfigUtil ftpConfig) {
		this.ftpConfig = ftpConfig;
	}

    public String invoke(Map<String, String> data, String charset,String url) throws Throwable {
    	HttpClient httpClient = new DefaultHttpClient();
        HttpPost post = new HttpPost(url);
        try {

            if(logger.isInfoEnabled()){
                logger.info("发送给会员网关，编码前信息:{}",JSON.toJSON(data));
            }
            List<BasicNameValuePair> formparams = new ArrayList<BasicNameValuePair>();

            for(Map.Entry<String, String> entry : data.entrySet()){
                String value = entry.getValue();
                if(StringUtils.hasText(value)){
                    formparams.add(new BasicNameValuePair(entry.getKey(), value));
                }else{
                    formparams.add(new BasicNameValuePair(entry.getKey(),value));
                }
            }

            UrlEncodedFormEntity entity = null;
            try {
                entity = new UrlEncodedFormEntity(formparams, charset);
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            post.setEntity(entity);
            X509TrustManager xtm = new X509TrustManager(){   //创建TrustManager 
                public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {} 
                public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {} 
                public X509Certificate[] getAcceptedIssuers() { return null; } 
            }; 
            
           //这个好像是HOST验证
    		X509HostnameVerifier hostnameVerifier = new X509HostnameVerifier() {
    			
				@Override
				public boolean verify(String arg0, SSLSession arg1) {
					return true;
				}
				public void verify(String host, SSLSocket ssl)
						throws IOException {}
				public void verify(String arg0, String[] arg1, String[] arg2) throws SSLException {}
    			public void verify(String arg0, X509Certificate arg1) throws SSLException {}
    		};

            SSLContext ctx = SSLContext.getInstance("TLS"); 
          //使用TrustManager来初始化该上下文，TrustManager只是被SSL的Socket所使用 
            ctx.init(null, new TrustManager[]{xtm}, null); 
             
            //创建SSLSocketFactory 
            SSLSocketFactory socketFactory = new SSLSocketFactory(ctx); 
            socketFactory.setHostnameVerifier(hostnameVerifier);
            //通过SchemeRegistry将SSLSocketFactory注册到我们的HttpClient上 
            httpClient.getConnectionManager().getSchemeRegistry().register(new Scheme("https", 443, socketFactory)); 
       
            String txt = httpClient.execute(post,getHandler(charset));

            if(logger.isInfoEnabled()){
                logger.info("会员网关响应字符串:{}",txt);
            }

//            Map<String,String> map = decode(txt,charset);
            txt = URLDecoder.decode(txt,charset);
            if(logger.isInfoEnabled()){
                logger.info("会员网关响应字符串,解码后的结果:{}",txt);
            }
            return txt;
        }catch (Exception e) {
            logger.error("调用会员网关出错",e);
            throw e;
        }catch(Throwable t){
            logger.error("调用会员网关出错",t);
            throw t;
        }
        finally{
            if (httpClient != null) {
                try {
                    httpClient.getConnectionManager().shutdown();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }


    public ResponseHandler<String> getHandler(final String charset){
        ResponseHandler<String> handler = new ResponseHandler<String>() {
            public String handleResponse(HttpResponse response)
                    throws ClientProtocolException, IOException {
                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    String rest = new String(EntityUtils.toByteArray(entity),charset);
                    EntityUtils.consume(entity);
                    return rest;
                } else {
                    return null;
                }
            }
        };
        return handler;
    }

    public Map<String, String> decode(String formEncodedString,String charset) throws UnsupportedEncodingException{
        if(formEncodedString != null){
            String[] nameValuePairs = formEncodedString.split(GatewayConstant.and);
            Map<String, String> mapdata = new HashMap<String, String>();
            for(String nameValuePair : nameValuePairs){
                String [] nameValue = nameValuePair.split(GatewayConstant.eq);
                String name = nameValue[0];
                String value = nameValue.length > 1 ? nameValue[1] : GatewayConstant.empty;
                if(StringUtils.hasText(value)){
                    value = URLDecoder.decode(value,charset);
                }
                mapdata.put(name, value);
            }
            return mapdata;
        }
        return null;
    }
}
