package com.lz.hxyh;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Message;
import com.lz.api.HXCallbackAPI;
import com.lz.api.MqAPI;
import com.lz.common.config.RedisUtil;
import com.lz.common.constants.*;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.HXTradeTypeEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.MQMessage;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.ThrowableUtil;
import com.lz.dto.OrderInfoDTO;
import com.lz.vo.CallbackOrderInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sdk.api.OpenApiAsyncNotifyMerMessageService;
import sdk.enums.AsyncNotifyMerMsgType;
import sdk.model.AsyncNotifyMerMessage;
import sdk.model.message.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;


@Slf4j
@RestController
@RequestMapping("/hxyh")
public class HxyhController {

    @Autowired
    private HXCallbackAPI hxCallbackAPI;

    @Autowired
    private MqAPI mqAPI;

    @Autowired
    private RedisUtil redisUtil;



    public static void main(String[] args){
        String requestId = "com"+ IdWorkerUtil.getInstance().nextId().substring(3,32);
        if(requestId.indexOf("com")==0){
            System.out.println(requestId.indexOf("com"));
        }
        System.out.println(requestId.length());
    }
    /**
     *  @author: dingweibo
     *  @Date: 2022/11/7 10:44
     *  @Description: 华夏银行回调
     */
    @PostMapping(value = "/notify")
    public ResponseEntity asnycMessage(HttpServletRequest request) throws Exception {
        log.info("华夏回调--------------------------------");
        AsyncNotifyMerMessage message = OpenApiAsyncNotifyMerMessageService.parse( request );
        log.info("华夏解析回调完成 -------------------------------- ：{}",message);
        try{
            if (AsyncNotifyMerMsgType.MER_OPEN_ACCOUNT_RESULT.getCode().equals( message.getMessageHeader().getMessageType() )) {
                log.info("会员开户状态通知开始--------------------------------：{}",JSONObject.toJSONString(message.getMessageBody()));
                //虚拟账户会员开户状态通知
                AsyncNotifyOpenBankAccountMessageBody messageBody = JSONObject.parseObject(JSONObject.toJSONString(message.getMessageBody()),AsyncNotifyOpenBankAccountMessageBody.class);
                log.info( ">>>> 通知：{}\n>>>> 通知类型messageType：{}\n>>>> 通知消息内容：{}", AsyncNotifyMerMsgType.MER_OPEN_ACCOUNT_RESULT.getDesc(), message.getMessageHeader().getMessageType(), JSONObject.toJSONString( messageBody ) );
                log.info( "通知：{}\n通知类型messageType：{}\n通知消息内容：{" + JSONObject.toJSONString( messageBody ) + "}" );
                //开户状态通知
                MQMessage mqMessage = new MQMessage();
                mqMessage.setTopic(MqMessageTopic.HXYHOPENKIND);
                mqMessage.setTag(MqMessageTag.HXYHOPENCALLBACK);
                mqMessage.setBody(messageBody);
                mqAPI.sendMessage(mqMessage);
                log.info("开户状态通知写入MQ-----");
            } else if (AsyncNotifyMerMsgType.MER_BIND_CARD_RESULT.getCode().equals( message.getMessageHeader().getMessageType() )) {
                log.info("虚拟账户会员绑卡状态通知开始--------------------------------：{}",JSONObject.toJSONString(message.getMessageBody()));
                //虚拟账户会员绑卡状态通知
                AsyncNotifyBindBankAccountMessageBody messageBody = JSONObject.parseObject(JSONObject.toJSONString(message.getMessageBody()),AsyncNotifyBindBankAccountMessageBody.class);
                log.info( ">>>> 通知：{}\n>>>> 通知类型messageType：{}\n>>>> 通知消息内容：{}", AsyncNotifyMerMsgType.MER_BIND_CARD_RESULT.getDesc(), message.getMessageHeader().getMessageType(), JSONObject.toJSONString( messageBody ) );
                log.info( "通知：{}\n通知类型messageType：{}\n通知消息内容：{" + JSONObject.toJSONString( messageBody ) + "}" );
                //绑卡/解绑 通知
                MQMessage mqMessage = new MQMessage();
                mqMessage.setTopic(MqMessageTopic.HXYHOPENKIND);
                mqMessage.setTag(MqMessageTag.BINDUNBINDCALLBACK);
                mqMessage.setBody(messageBody);
                mqAPI.sendMessage(mqMessage);
                log.info("绑卡通知写入MQ-----");
            } else if (AsyncNotifyMerMsgType.VIRTUAL_MEMBER_DEPOSIT_NOTICE.getCode().equals(message.getMessageHeader().getMessageType())) {
                log.info("转账入金通知--------------------------------：{}", JSONObject.toJSONString(message.getMessageBody()));
                //转账入金通知
                AsyncNotifyVirtualMemberRechargeMessageBody messageBody = JSONObject.parseObject(JSONObject.toJSONString(message.getMessageBody()), AsyncNotifyVirtualMemberRechargeMessageBody.class);
                log.info(">>>> 通知：{}\n>>>> 通知类型messageType：{}\n>>>> 通知消息内容：{}", AsyncNotifyMerMsgType.VIRTUAL_MEMBER_DEPOSIT_NOTICE.getDesc(), message.getMessageHeader().getMessageType(), JSONObject.toJSONString(messageBody));
                log.info("通知：{}\n通知类型messageType：{}\n通知消息内容：{" + JSONObject.toJSONString(messageBody) + "}");
                MQMessage mqMessage = new MQMessage();
                mqMessage.setTopic(MqMessageTopic.HXYHOPENKIND);
                mqMessage.setTag(MqMessageTag.TRANSFERDEPOSITCALLBACK);
                mqMessage.setBody(messageBody);
                mqAPI.sendMessage(mqMessage);
                log.info("转账入金结果通知写入MQ-----");
            }else if (AsyncNotifyMerMsgType.VIRTUAL_MEMBER_WITHDRAW_NOTICE.getCode().equals( message.getMessageHeader().getMessageType() )) {
                log.info("提现结果异步通知--------------------------------：{}",JSONObject.toJSONString(message.getMessageBody()));
                //提现结果异步通知
                AsyncNotifyVirtualMemberWithdrawMessageBody messageBody = JSONObject.parseObject(JSONObject.toJSONString(message.getMessageBody()),AsyncNotifyVirtualMemberWithdrawMessageBody.class);
                log.info( ">>>> 通知：{}\n>>>> 通知类型messageType：{}\n>>>> 通知消息内容：{}", AsyncNotifyMerMsgType.VIRTUAL_MEMBER_WITHDRAW_NOTICE.getDesc(), message.getMessageHeader().getMessageType(), JSONObject.toJSONString( messageBody ) );
                log.info( "通知：{}\n通知类型messageType：{}\n通知消息内容：{" + JSONObject.toJSONString( messageBody ) + "}" );
                MQMessage mqMessage = new MQMessage();
                mqMessage.setTopic(HXMqMessageTopic.HX_WITHDRAW);
                mqMessage.setTag(HXMqMessageTag.HX_WITHDRAWCALLBACK);
                mqMessage.setBody(messageBody);
                mqAPI.sendMessage(mqMessage);
                log.info("提现结果异步通知写入MQ-----");
            }
        }catch (Exception e){
            log.error("华夏回调其他类消息:{}",JSONObject.toJSONString( message ) );
            log.error("华夏回调其他类方法异常",e);
            Message message1 = new Message();
            Object messageBody = OpenApiAsyncNotifyMerMessageService.parse(request).getMessageBody();
            message1.setBody(JSONUtil.toJsonStr(messageBody).getBytes(StandardCharsets.UTF_8));
            mqAPI.saveMessage(message1);
        }
        return ResponseEntity.ok().build();
    }


    /**
     *  @author: dingweibo
     *  @Date: 2022/11/7 10:44
     *  @Description: 华夏银行回调
     */
    @PostMapping(value = "/notifyCompany")
    public ResponseEntity asnycMessageCompany(HttpServletRequest request) throws Exception {
        log.info("华夏回调--------------------------------");
        AsyncNotifyMerMessage message = OpenApiAsyncNotifyMerMessageService.parse( request );
        log.info("华夏解析回调完成 -------------------------------- ：{}",message);
        try{
             if (AsyncNotifyMerMsgType.VIRTUAL_MEMBER_WITHDRAW_NOTICE.getCode().equals( message.getMessageHeader().getMessageType() )) {
                log.info("提现结果异步通知--------------------------------：{}",JSONObject.toJSONString(message.getMessageBody()));
                //提现结果异步通知
                AsyncNotifyVirtualMemberWithdrawMessageBody messageBody = JSONObject.parseObject(JSONObject.toJSONString(message.getMessageBody()),AsyncNotifyVirtualMemberWithdrawMessageBody.class);
                log.info( ">>>> 通知：{}\n>>>> 通知类型messageType：{}\n>>>> 通知消息内容：{}", AsyncNotifyMerMsgType.VIRTUAL_MEMBER_WITHDRAW_NOTICE.getDesc(), message.getMessageHeader().getMessageType(), JSONObject.toJSONString( messageBody ) );
                log.info( "通知：{}\n通知类型messageType：{}\n通知消息内容：{" + JSONObject.toJSONString( messageBody ) + "}" );
                MQMessage mqMessage = new MQMessage();
                mqMessage.setTopic(HXMqMessageTopic.HXYHOPENKIND);
                mqMessage.setTag(HXMqMessageTag.HX_WITHDRAWCALLBACK);
                mqMessage.setBody(messageBody);
                mqAPI.sendMessage(mqMessage);
                log.info("提现结果异步通知写入MQ-----");
            }else if (AsyncNotifyMerMsgType.VIRTUAL_BALANCE_PAY_NOTICE.getCode().equals( message.getMessageHeader().getMessageType() )) {
                 //余额支付结果通知
                 AsyncNotifyVirtualBalancePayMessageBody messageBody = (AsyncNotifyVirtualBalancePayMessageBody) message.getMessageBody();
                 log.info( ">>>> 通知：{}\n>>>> 通知类型messageType：{}\n>>>> 通知消息内容：{}", AsyncNotifyMerMsgType.VIRTUAL_BALANCE_PAY_NOTICE.getDesc(), message.getMessageHeader().getMessageType(), JSONObject.toJSONString( messageBody ) );
                 log.info( "通知：{}\n通知类型messageType：{}\n通知消息内容：{" + JSONObject.toJSONString( messageBody ) + "}" );
                 MQMessage mqMessage = new MQMessage();
                 mqMessage.setTopic(HXMqMessageTopic.HXYHOPENKIND);
                 mqMessage.setTag(HXMqMessageTag.HX_BALANCEPAYCALLBACK);
                 mqMessage.setBody(messageBody);
                 mqAPI.sendMessage(mqMessage);
                 log.info("华夏小工具余额支付通知写入MQ-----");
             }
        }catch (Exception e){
            log.error("华夏回调其他类消息:{}",JSONObject.toJSONString( message ) );
            log.error("华夏回调其他类方法异常",e);
            Message message1 = new Message();
            Object messageBody = OpenApiAsyncNotifyMerMessageService.parse(request).getMessageBody();
            message1.setBody(JSONUtil.toJsonStr(messageBody).getBytes(StandardCharsets.UTF_8));
            mqAPI.saveMessage(message1);
        }
        return ResponseEntity.ok().build();
    }

    @PostMapping(value = "/trade/message")
    public ResponseEntity tradeMessage(HttpServletRequest request,  HttpServletResponse response) throws Exception {
        try {
            AsyncNotifyMerMessage message = OpenApiAsyncNotifyMerMessageService.parse(request);
            log.info("华夏支付回调信息, {}", JSONUtil.toJsonStr(message));
            if (AsyncNotifyMerMsgType.VIRTUAL_BALANCE_PAY_NOTICE.getCode().equals( message.getMessageHeader().getMessageType() )) {
                //余额支付结果通知
                AsyncNotifyVirtualBalancePayMessageBody messageBody = JSONUtil.toBean(JSONUtil.toJsonStr(message.getMessageBody()), AsyncNotifyVirtualBalancePayMessageBody.class);
                log.info( ">>>> 通知：{}\n>>>> 通知类型messageType：{}\n>>>> 通知消息内容：{}",
                        AsyncNotifyMerMsgType.VIRTUAL_BALANCE_PAY_NOTICE.getDesc(),
                        message.getMessageHeader().getMessageType(),
                        JSONUtil.toJsonStr(messageBody));
                log.info( "通知：{}\n通知类型messageType：{}\n通知消息内容：{" + JSONUtil.toJsonStr(messageBody) + "}" );
                CallbackOrderInfoVO vo = new CallbackOrderInfoVO();
                vo.setCode(messageBody.getBizOrderNo());
                ResultUtil callbackOrderInfo = hxCallbackAPI.getCallbackOrderInfoByPayDetailCode(vo);
                if (DictEnum.SUCCESS.code.equals(callbackOrderInfo.getCode()) && null != callbackOrderInfo.getData()) {
                    OrderInfoDTO dto = JSONUtil.toBean(JSONUtil.toJsonStr(callbackOrderInfo.getData()), OrderInfoDTO.class);
                    if (HXTradeTypeEnum.HX_TXSERVICEFEE.code.equals(dto.getTradeType())) {
                        // 提现服务费回调
                        MQMessage mqMessage = new MQMessage();
                        mqMessage.setTopic(MqMessageTopic.WITHDRAW);
                        mqMessage.setTag(MqMessageTag.TXSERVICEFEECALLBACK);
                        mqMessage.setKey(messageBody.getBizOrderNo());
                        mqMessage.setBody(messageBody);
                        log.info("提现服务费回调, {}", JSONUtil.toJsonStr(mqMessage));
                        mqAPI.sendMessage(mqMessage);
                    } else if (HXTradeTypeEnum.HX_TXTRANSFER.code.equals(dto.getTradeType())) {
                        // 提现转账回调
                        MQMessage mqMessage = new MQMessage();
                        mqMessage.setTopic(MqMessageTopic.WITHDRAW);
                        mqMessage.setTag(MqMessageTag.TXTRANSFERCALLBACK);
                        mqMessage.setKey(messageBody.getBizOrderNo());
                        mqMessage.setBody(messageBody);
                        log.info("提现转账回调, {}", JSONUtil.toJsonStr(mqMessage));
                        mqAPI.sendMessage(mqMessage);
                    } else if (DictEnum.ZH.code.equals(dto.getOperateState()) || DictEnum.DBZH.code.equals(dto.getOperateState())) {
                        // 召回
                        MQMessage mqMessage = new MQMessage();
                        mqMessage.setKey(messageBody.getBizOrderNo());
                        String tag = HXMqMessageTag.HX_REFUNDCALLBACK;
                        if (DictEnum.DBZH.code.equals(dto.getOperateState())) {
                            tag = HXMqMessageTag.HX_REFUNDPACKCALLBACK;
                        }
                        if (DictEnum.COMMONPATTERN.code.equals(dto.getCapitalTransferPattern())) {
                            if (DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())) {
                                mqMessage.setTopic(HXMqMessageTopic.HX_COMMONDRIVER);
                                mqMessage.setTag(tag);
                            }
                            if (DictEnum.PAYTOCAPTAIN.code.equals(dto.getCapitalTransferType())) {
                                mqMessage.setTopic(HXMqMessageTopic.HX_COMMONCAPTION);
                                mqMessage.setTag(tag);
                            }
                        } else if (DictEnum.MANAGERPATTERN.code.equals(dto.getCapitalTransferPattern())) {
                            if (DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())) {
                                mqMessage.setTopic(HXMqMessageTopic.HX_MANAGERDRIVER);
                                mqMessage.setTag(tag);
                            }
                            if (DictEnum.PAYTOCAPTAIN.code.equals(dto.getCapitalTransferType())) {
                                mqMessage.setTopic(HXMqMessageTopic.HX_MANAGERCAPTION);
                                mqMessage.setTag(tag);
                            }
                        }
                        mqMessage.setBody(messageBody);
                        log.info("余额支付召回回调, {}", JSONUtil.toJsonStr(mqMessage));
                        mqAPI.sendMessage(mqMessage);
                    } else {
                        // 入账
                        MQMessage mqMessage = new MQMessage();
                        mqMessage.setKey(messageBody.getBizOrderNo());
                        // 预付款
                        if (MqOrderConstants.YFK_TRADE_TYPE.contains(dto.getTradeType())) {
                            mqMessage.setTopic(HXMqMessageTopic.HX_PREPAYMENT);
                            mqMessage.setTag(HXMqMessageTag.HX_PREPAYCALLBACK);
                        } else {
                            if (DictEnum.COMMONPATTERN.code.equals(dto.getCapitalTransferPattern())) {
                                if (DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())) {
                                    mqMessage.setTopic(HXMqMessageTopic.HX_COMMONDRIVER);
                                    mqMessage.setTag(HXMqMessageTag.HX_COMMONDRIVERCALLBACK);
                                }
                                if (DictEnum.PAYTOCAPTAIN.code.equals(dto.getCapitalTransferType())) {
                                    mqMessage.setTopic(HXMqMessageTopic.HX_COMMONCAPTION);
                                    mqMessage.setTag(HXMqMessageTag.HX_COMMONCAPTIONCALLBACK);
                                }
                            } else if (DictEnum.MANAGERPATTERN.code.equals(dto.getCapitalTransferPattern())) {
                                if (DictEnum.PAYTODRIVER.code.equals(dto.getCapitalTransferType())) {
                                    mqMessage.setTopic(HXMqMessageTopic.HX_MANAGERDRIVER);
                                    mqMessage.setTag(HXMqMessageTag.HX_MANAGERDRIVERCALLBACK);
                                }
                                if (DictEnum.PAYTOCAPTAIN.code.equals(dto.getCapitalTransferType())) {
                                    mqMessage.setTopic(HXMqMessageTopic.HX_MANAGERCAPTION);
                                    mqMessage.setTag(HXMqMessageTag.HX_MANAGERCAPTIONCALLBACK);
                                }
                            }
                        }
                        mqMessage.setBody(messageBody);
                        log.info("余额支付回调, {}", JSONUtil.toJsonStr(mqMessage));
                        mqAPI.sendMessage(mqMessage);
                    }

                } else {
                    log.info("余额支付回调，未找到支付字表，发送失败");
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
                    /*log.info("余额支付回调, 未找到支付子表, {}", messageBody.getBizOrderNo());
                    Object obj = redisUtil.get("BizOrderNo:" + messageBody.getBizOrderNo());
                    if (null == obj) {
                        log.info("余额支付回调, 未找到支付子表, 将回调信息入库, {}", messageBody.getBizOrderNo());
                        Message bakMessage = new Message();
                        bakMessage.setBody(JSONUtil.toJsonStr(bakMessage).getBytes(StandardCharsets.UTF_8));
                        bakMessage.setKey(messageBody.getBizOrderNo());
                        mqAPI.saveMessage(bakMessage);
                    } else {
                        if (obj instanceof MQMessage) {
                            log.info("余额支付回调, 未找到支付子表, redis获取主题标签, {}", messageBody.getBizOrderNo());
                            MQMessage mqMessage = JSONUtil.toBean(JSONUtil.toJsonStr(obj), MQMessage.class);
                            mqMessage.setBody(messageBody);
                            mqMessage.setKey(messageBody.getBizOrderNo());
                            mqAPI.sendMessage(mqMessage);
                        }
                    }*/
                }
            }

        } catch (Exception e) {
            log.error("华夏回调处理异常，{}", ThrowableUtil.getStackTrace(e));
            Message message = new Message();
            Object messageBody = OpenApiAsyncNotifyMerMessageService.parse(request).getMessageBody();
            message.setBody(JSONUtil.toJsonStr(messageBody).getBytes(StandardCharsets.UTF_8));
            mqAPI.saveMessage(message);
            String message1 = e.getMessage();
            if (message1.equals("未找到支付字表")) {
                throw new RuntimeException("未找到支付字表");
            }
        }
        return ResponseEntity.ok().build();
    }
}
