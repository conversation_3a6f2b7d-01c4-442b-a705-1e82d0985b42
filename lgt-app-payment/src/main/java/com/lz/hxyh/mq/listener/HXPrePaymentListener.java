package com.lz.hxyh.mq.listener;

import cn.hutool.json.JSONUtil;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.lz.api.HXCallbackAPI;
import com.lz.api.MqAPI;
import com.lz.common.config.RedisUtil;
import com.lz.common.constants.HXMqMessageTag;
import com.lz.common.constants.HXMqMessageTopic;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.model.MQMessage;
import com.lz.common.model.hxPayment.request.pay.CustomerBalancePayReq;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.ThrowableUtil;
import com.lz.hxyh.base.CloudPaymentBaseMethod;
import commonSdk.responseModel.CustomerBalancePayResponse;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdk.model.message.AsyncNotifyVirtualBalancePayMessageBody;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class HXPrePaymentListener implements MessageListener {

    private final static String REQUEST_BIZ_CODE_EXIST = "REQUEST_BIZ_CODE_EXIST";
    private final static String CALL_BIZ_CODE_EXIST = "CALL_BIZ_CODE_EXIST";

    @Autowired
    CloudPaymentBaseMethod cloudPaymentBaseMethod;

    @Autowired
    private MqAPI mqAPI;

    @Autowired
    private HXCallbackAPI hxCallbackAPI;

    @Autowired
    private RedissonClient redissonClient;

    @Resource
    private RedisUtil redisUtil;

    @Transactional
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        log.info("华夏 - PREPAYMENT Info, {}", JSONUtil.toJsonStr(message));
        String topic = message.getTopic();
        String tag = message.getTag();
        String body = new String(message.getBody());
        log.info("华夏 - 预付款, {}",  body);
        try {
            if (HXMqMessageTopic.HX_PREPAYMENT.equals(topic)) {
                if (body.length() > 0) {
                    if (HXMqMessageTag.HX_PREPAY.equals(tag)) {
                        // 发起余额支付请求
                        log.info("华夏 - 预付款发起余额支付请求, {}", body);
                        CustomerBalancePayReq customerBalancePayReq = JSONUtil.toBean(body, CustomerBalancePayReq.class);
                        if (redisUtil.hasKey(REQUEST_BIZ_CODE_EXIST + customerBalancePayReq.getBizOrderNo())) {
                            log.info("华夏 - 预付款发起余额支付请求, 请求已存在, {}", body);
                            return Action.CommitMessage;
                        }
                        Map<String, Object> execute = cloudPaymentBaseMethod.execute(customerBalancePayReq);
                        log.info("华夏 - 预付款发起余额支付请求结果，{}", JSONUtil.toJsonStr(execute));
                        CustomerBalancePayResponse response = JSONUtil.toBean(JSONUtil.parseObj(execute), CustomerBalancePayResponse.class);
                        if (DictEnum.ACCEPT_SUCC.code.equals(response.getOrderStatus())
                            || DictEnum.PAY_SUCC.code.equals(response.getOrderStatus())
                            || DictEnum.PAY_ING.code.equals(response.getOrderStatus())) {
                            redisUtil.set(REQUEST_BIZ_CODE_EXIST + response.getBizOrderNo(), 1, 60 * 60 * 24);
                            return Action.CommitMessage;
                        } else {
                            throw new RuntimeException(JSONUtil.toJsonStr(execute));
                        }
                    } else if (HXMqMessageTag.HX_PREPAYCALLBACK.equals(tag)) {
                        log.info("华夏 - 预付款余额支付回调, {}", body);
                        AsyncNotifyVirtualBalancePayMessageBody balancePayMessageBody = JSONUtil.toBean(body, AsyncNotifyVirtualBalancePayMessageBody.class);
                        if (redisUtil.hasKey(CALL_BIZ_CODE_EXIST + balancePayMessageBody.getBizOrderNo())) {
                            log.info("华夏 - 预付款余额支付回调, 请求已存在, {}", body);
                            return Action.CommitMessage;
                        }
                        String outPartnerAccId = balancePayMessageBody.getOutPartnerAccId();
                        String inPartnerAccId = balancePayMessageBody.getInPartnerAccId();
                        RLock outLock = redissonClient.getLock(outPartnerAccId);
                        RLock inLock = redissonClient.getLock(inPartnerAccId);
                        log.info("分布式锁:lock:" + outLock.toString() + ",interrupted:" +
                                Thread.currentThread().isInterrupted() + ",hold:" +
                                outLock.isHeldByCurrentThread() + ",threadId:" +
                                Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                        log.info("分布式锁:lock:" + inLock.toString() + ",interrupted:" +
                                Thread.currentThread().isInterrupted() + ",hold:" +
                                inLock.isHeldByCurrentThread() + ",threadId:" +
                                Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                        try {
                            boolean out = outLock.tryLock(5, TimeUnit.SECONDS);
                            boolean in = inLock.tryLock(5, TimeUnit.SECONDS);
                            log.info("分布式锁:lock:" + outLock.toString() + ",interrupted:" +
                                    Thread.currentThread().isInterrupted() + ",hold:" +
                                    outLock.isHeldByCurrentThread() + ",threadId:" +
                                    Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                            log.info("分布式锁:lock:" + inLock.toString() + ",interrupted:" +
                                    Thread.currentThread().isInterrupted() + ",hold:" +
                                    inLock.isHeldByCurrentThread() + ",threadId:" +
                                    Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                            if (out && in) {
                                ResultUtil resultUtil = hxCallbackAPI.prePayNotice(balancePayMessageBody);
                                if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
                                    throw new RuntimeException(resultUtil.getMsg());
                                }
                                redisUtil.set(CALL_BIZ_CODE_EXIST + balancePayMessageBody.getBizOrderNo(), 1, 60 * 60 * 24);
                            } else {
                                log.info("华夏 - 预付款余额支付回调未获取锁，放回消息队列");
                                MQMessage mqMessage = new MQMessage();
                                mqMessage.setTopic(message.getTopic());
                                mqMessage.setTag(message.getTag());
                                mqMessage.setBody(balancePayMessageBody);
                                mqAPI.sendMessage(mqMessage);
                            }
                        } catch (Exception e) {
                            log.error("华夏 - 预付款余额支付回调, PREPAYMENT error, {}", ThrowableUtil.getStackTrace(e));
                            MQMessage mqMessage = new MQMessage();
                            mqMessage.setTopic(message.getTopic());
                            mqMessage.setTag(message.getTag());
                            mqMessage.setBody(balancePayMessageBody);
                            mqAPI.sendMessage(mqMessage);
                        } finally {
                            if (outLock.isLocked() && outLock.isHeldByCurrentThread()) {
                                log.info("分布式锁:lock:" + outLock.toString() + ",interrupted:" +
                                        Thread.currentThread().isInterrupted() + ",hold:" +
                                        outLock.isHeldByCurrentThread() + ",threadId:" +
                                        Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                                outLock.unlock();
                            }
                            if (inLock.isLocked() && inLock.isHeldByCurrentThread()) {
                                log.info("分布式锁:lock:" + inLock.toString() + ",interrupted:" +
                                        Thread.currentThread().isInterrupted() + ",hold:" +
                                        inLock.isHeldByCurrentThread() + ",threadId:" +
                                        Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                                inLock.unlock();
                            }
                        }
                        return Action.CommitMessage;
                    } else {
                        log.error("华夏 - 消息类型错误, {}", JSONUtil.toJsonStr(message));
                        throw new RuntimeException("消息类型错误");
                    }
                } else {
                    log.error("华夏 - 消息内容为空，{}", JSONUtil.toJsonStr(message));
                    throw new RuntimeException("消息内容为空");
                }
            } else {
                log.error("消息类型错误, {}", JSONUtil.toJsonStr(message));
                throw new RuntimeException("消息类型错误");
            }
        } catch (Exception e) {
            log.error("华夏 - PREPAYMENT error, {}", ThrowableUtil.getStackTrace(e));
            // 将消息入库
            mqAPI.saveMessage(message);
        }
        return Action.CommitMessage;
    }

}
