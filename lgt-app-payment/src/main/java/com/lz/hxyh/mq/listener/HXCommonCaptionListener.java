package com.lz.hxyh.mq.listener;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.jd.jr.jropen.unifySdk.reqModel.CustomerRefundResponse;
import com.lz.api.HXCallbackAPI;
import com.lz.api.MqAPI;
import com.lz.common.config.RedisUtil;
import com.lz.common.constants.HXMqMessageTag;
import com.lz.common.constants.HXMqMessageTopic;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.exception.hx.HxCommonResponseEnum;
import com.lz.common.exception.hx.HxTradeResponseEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.MQMessage;
import com.lz.common.model.hxPayment.request.pay.CustomerBalancePayReq;
import com.lz.common.model.hxPayment.request.query.CustomerQueryTradeReq;
import com.lz.common.model.hxPayment.response.CustomerBalancePayRes;
import com.lz.common.util.DateUtils;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.common.util.ThrowableUtil;
import com.lz.dao.TPaymentMqMessagePackPayDetailMapper;
import com.lz.hxyh.base.CloudPaymentBaseMethod;
import com.lz.model.TMqMessagePackPayDetail;
import commonSdk.responseModel.CustomerBalancePayResponse;
import commonSdk.responseModel.CustomerQueryTradeResponse;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdk.model.message.AsyncNotifyVirtualBalancePayMessageBody;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class HXCommonCaptionListener implements MessageListener {

    private final static String BIZ_CODE_EXIST = "订单号已存在";
    private final static String REQUEST_BIZ_CODE_EXIST = "REQUEST_BIZ_CODE_EXIST";
    private final static String CALL_BIZ_CODE_EXIST = "CALL_BIZ_CODE_EXIST";

    @Autowired
    private CloudPaymentBaseMethod cloudPaymentBaseMethod;

    @Autowired
    private MqAPI mqAPI;

    @Autowired
    private HXCallbackAPI hxCallbackAPI;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private TPaymentMqMessagePackPayDetailMapper paymentMqMessagePackPayDetailMapper;

    @Resource
    private RedisUtil redisUtil;

    @Transactional
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String topic = message.getTopic();
        String tag = message.getTag();
        String body = new String(message.getBody());
        log.info("普通车队长, {}",  body);
        try {
            if (HXMqMessageTopic.HX_COMMONCAPTION.equals(topic)) {
                if (body.length() > 0) {
                    if (HXMqMessageTag.HX_COMMONCAPTIONTOCARRIER.equals(tag)
                            || HXMqMessageTag.HX_COMMONCAPTIONTODRIVER.equals(tag)
                            || HXMqMessageTag.HX_COMMONCAPTIONTOCAPTION.equals(tag)
                            || HXMqMessageTag.HX_INSURANCE.equals(tag)) {
                        // 余额支付
                        log.info("普通 - 车队长, 余额支付, {}", body);
                        CustomerBalancePayReq customerBalancePayReq = JSONUtil.toBean(body, CustomerBalancePayReq.class);
                        if (redisUtil.hasKey(REQUEST_BIZ_CODE_EXIST + customerBalancePayReq.getBizOrderNo())) {
                            log.info("普通 - 车队长, 余额支付, 请求已存在, {}", body);
                            return Action.CommitMessage;
                        }
                        Map<String, Object> execute = cloudPaymentBaseMethod.execute(customerBalancePayReq);
                        log.info("普通 - 车队长, 余额支付请求结果, {}", JSONUtil.toJsonStr(execute));
                        CustomerBalancePayResponse response = JSONUtil.toBean(JSONUtil.parseObj(execute), CustomerBalancePayResponse.class);
                        if (DictEnum.ACCEPT_SUCC.code.equals(response.getOrderStatus())
                            || DictEnum.PAY_SUCC.code.equals(response.getOrderStatus())
                            || DictEnum.PAY_ING.code.equals(response.getOrderStatus())) {
                            redisUtil.set(REQUEST_BIZ_CODE_EXIST + response.getBizOrderNo(), 1, 60 * 60 * 24);
                            return Action.CommitMessage;
                        } else {
                            if (BIZ_CODE_EXIST.equals(response.getResponseDesc())) {
                                log.info("普通 - 车队长, 余额支付, 订单号已存在, {}", JSONUtil.toJsonStr(execute));
                               return Action.CommitMessage;
                            }
                            boolean payResult = true;
                            // 通用异常，查询交易状态
                            if (HxCommonResponseEnum.hasCode(response.getResponseCode())) {
                                CustomerQueryTradeReq tradeRequest = new CustomerQueryTradeReq();
                                tradeRequest.setRequestId(IdWorkerUtil.getInstance().nextId());
                                tradeRequest.setRequestTime(DateUtils.getRequestTime());
                                tradeRequest.setPartnerId(customerBalancePayReq.getPartnerId());
                                tradeRequest.setChannelId(customerBalancePayReq.getChannelId());
                                tradeRequest.setBizOrderNo(customerBalancePayReq.getBizOrderNo());
                                tradeRequest.setTradeType("SALE");
                                Map<String, Object> queryExecute = cloudPaymentBaseMethod.execute(tradeRequest);
                                CustomerQueryTradeResponse tradeResponse =  JSONUtil.toBean(JSONUtil.parseObj(queryExecute), CustomerQueryTradeResponse.class);
                                if (null == tradeResponse.getResponseCode()
                                        || StringUtils.isBlank(tradeResponse.getResponseCode())
                                        || HxCommonResponseEnum.UNKONWN.code.equals(tradeResponse.getResponseCode())
                                        || (HxTradeResponseEnum.RESPONSE000000.getCode().equals(tradeResponse.getResponseCode())
                                        && DictEnum.PAY_FAIL.code.equals(tradeResponse.getOrderStatus()))) {
                                    payResult = false;
                                }
                            }
                            if (null == response.getResponseCode()
                                    || StringUtils.isBlank(response.getResponseCode())
                                    || !payResult) {
                                // 修改支付子表
                                CustomerBalancePayRes res = new CustomerBalancePayRes();
                                BeanUtils.copyProperties(response, res);
                                res.setChannelId(customerBalancePayReq.getChannelId());
                                res.setBizOrderNo(customerBalancePayReq.getBizOrderNo());
                                res.setOrderAmount(customerBalancePayReq.getOrderAmount());
                                res.setOutPartnerAccId(customerBalancePayReq.getOutPartnerAccId());
                                hxCallbackAPI.payFail(res);
                            }

                            throw new RuntimeException(JSONUtil.toJsonStr(execute));
                        }
                    } else if (HXMqMessageTag.HX_COMMONCAPTIONCALLBACK.equals(tag)) {
                        log.info("普通 - 车队长, 余额支付回调, {}", JSONUtil.toJsonStr(body));
                        AsyncNotifyVirtualBalancePayMessageBody balancePayMessageBody = JSONUtil.toBean(JSONUtil.toJsonStr(body), AsyncNotifyVirtualBalancePayMessageBody.class);
                        if (redisUtil.hasKey(CALL_BIZ_CODE_EXIST + balancePayMessageBody.getBizOrderNo())) {
                            log.info("普通 - 车队长, 余额支付回调, 请求已存在, {}", body);
                            return Action.CommitMessage;
                        }
                        String outPartnerAccId = balancePayMessageBody.getOutPartnerAccId();
                        String inPartnerAccId = balancePayMessageBody.getInPartnerAccId();
                        RLock outLock = redissonClient.getLock(outPartnerAccId);
                        RLock inLock = redissonClient.getLock(inPartnerAccId);
                        log.info("分布式锁:lock:" + outLock.toString() + ",interrupted:" +
                                Thread.currentThread().isInterrupted() + ",hold:" +
                                outLock.isHeldByCurrentThread() + ",threadId:" +
                                Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                        log.info("分布式锁:lock:" + inLock.toString() + ",interrupted:" +
                                Thread.currentThread().isInterrupted() + ",hold:" +
                                inLock.isHeldByCurrentThread() + ",threadId:" +
                                Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                        try {
                            boolean out = outLock.tryLock(5, TimeUnit.SECONDS);
                            boolean in = inLock.tryLock(5, TimeUnit.SECONDS);
                            log.info("分布式锁:lock:" + outLock.toString() + ",interrupted:" +
                                    Thread.currentThread().isInterrupted() + ",hold:" +
                                    outLock.isHeldByCurrentThread() + ",threadId:" +
                                    Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                            log.info("分布式锁:lock:" + inLock.toString() + ",interrupted:" +
                                    Thread.currentThread().isInterrupted() + ",hold:" +
                                    inLock.isHeldByCurrentThread() + ",threadId:" +
                                    Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                            if (out && in) {
                                ResultUtil resultUtil = hxCallbackAPI.balancePayNotice(balancePayMessageBody);
                                if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
                                    throw new RuntimeException(resultUtil.getMsg());
                                }
                                redisUtil.set(CALL_BIZ_CODE_EXIST + balancePayMessageBody.getBizOrderNo(), 1, 60 * 60 * 24);
                            } else {
                                log.info("普通 - 车队长, 未获取锁，放回消息队列");
                                MQMessage mqMessage = new MQMessage();
                                mqMessage.setTopic(message.getTopic());
                                mqMessage.setTag(message.getTag());
                                mqMessage.setBody(balancePayMessageBody);
                                mqAPI.sendMessage(mqMessage);
                            }
                        } catch (Exception e) {
                            log.error("普通 - 车队长, 余额支付回调, error, {}", ThrowableUtil.getStackTrace(e));
                            MQMessage mqMessage = new MQMessage();
                            mqMessage.setTopic(message.getTopic());
                            mqMessage.setTag(message.getTag());
                            mqMessage.setBody(balancePayMessageBody);
                            mqAPI.sendMessage(mqMessage);
                        }  finally {
                            if (outLock.isLocked() && outLock.isHeldByCurrentThread()) {
                                log.info("分布式锁:lock:" + outLock.toString() + ",interrupted:" +
                                        Thread.currentThread().isInterrupted() + ",hold:" +
                                        outLock.isHeldByCurrentThread() + ",threadId:" +
                                        Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                                outLock.unlock();
                            }
                            if (inLock.isLocked() && inLock.isHeldByCurrentThread()) {
                                log.info("分布式锁:lock:" + inLock.toString() + ",interrupted:" +
                                        Thread.currentThread().isInterrupted() + ",hold:" +
                                        inLock.isHeldByCurrentThread() + ",threadId:" +
                                        Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                                inLock.unlock();
                            }
                        }
                        return Action.CommitMessage;
                    } else if (HXMqMessageTag.HX_PACKPAY.equals(tag)) {
                        log.info("普通 - 车队长, 发起打包支付消息");
                        // 打包支付
                        body = body.replaceAll("\"", "");
                        List<String> orderCodes = Arrays.asList(body.split(","));
                        List<TMqMessagePackPayDetail> packPayDetails = paymentMqMessagePackPayDetailMapper.selectDetailList(orderCodes);
                        for (TMqMessagePackPayDetail packPayDetail : packPayDetails) {
                            MQMessage mqMessage = new MQMessage();
                            mqMessage.setTopic(topic);
                            mqMessage.setTag(packPayDetail.getTag());
                            mqMessage.setKey(packPayDetail.getMessageKey());
                            JSONObject jsonObject = JSONUtil.parseObj(packPayDetail.getBody());
                            mqMessage.setBody(JSONUtil.toBean(jsonObject, CustomerBalancePayReq.class));
                            ResultUtil resultUtil = mqAPI.sendMessage(mqMessage);
                            log.info("普通 - 车队长, 发送打包支付MQ消息结果，{}", JSONUtil.toJsonStr(resultUtil));
                            if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
                                log.error("普通 - 车队长, 发送打包支付MQ消息失败, {}", JSONUtil.toJsonStr(packPayDetail));
                                throw new RuntimeException("发起打包支付消息！");
                            }
                            packPayDetail.setStatus(true);
                            packPayDetail.setUpdateTime(new Date());
                            paymentMqMessagePackPayDetailMapper.updateByPrimaryKeySelective(packPayDetail);
                        }
                    }  else if (HXMqMessageTag.HX_REFUND.equals(tag)) {
                        // 发起交易退款请求
                        log.info("普通 - 车队长, 发起交易退款请求, {}", body);
                        CustomerBalancePayReq customerBalancePayReq = JSONUtil.toBean(body, CustomerBalancePayReq.class);
                        if (redisUtil.hasKey(REQUEST_BIZ_CODE_EXIST + customerBalancePayReq.getBizOrderNo())) {
                            log.info("普通 - 车队长, 发起交易退款请求, 请求已存在, {}", body);
                            return Action.CommitMessage;
                        }
                        Map<String, Object> execute = cloudPaymentBaseMethod.execute(customerBalancePayReq);
                        log.info("普通 - 车队长, 发起交易退款请求结果，{}", JSONUtil.toJsonStr(execute));
                        CustomerRefundResponse response = JSONUtil.toBean(JSONUtil.parseObj(execute), CustomerRefundResponse.class);
                        if (DictEnum.ACCEPT_SUCC.code.equals(response.getOrderStatus())
                            || DictEnum.PAY_SUCC.code.equals(response.getOrderStatus())
                            || DictEnum.PAY_ING.code.equals(response.getOrderStatus())) {
                            redisUtil.set(REQUEST_BIZ_CODE_EXIST + response.getBizOrderNo(), 1, 60 * 60 * 24);
                            return Action.CommitMessage;
                        } else {
                            throw new RuntimeException(JSONUtil.toJsonStr(execute));
                        }
                    } else if (HXMqMessageTag.HX_REFUNDCALLBACK.equals(tag)) {
                        // 交易退款回调
                        log.info("普通 - 车队长, 交易退款回调, {}", body);
                        AsyncNotifyVirtualBalancePayMessageBody balancePayMessageBody = JSONUtil.toBean(body, AsyncNotifyVirtualBalancePayMessageBody.class);
                        if (redisUtil.hasKey(CALL_BIZ_CODE_EXIST + balancePayMessageBody.getBizOrderNo())) {
                            log.info("普通 - 车队长, 交易退款回调, 请求已存在, {}", body);
                            return Action.CommitMessage;
                        }
                        String outPartnerAccId = balancePayMessageBody.getOutPartnerAccId();
                        String inPartnerAccId = balancePayMessageBody.getInPartnerAccId();
                        RLock outLock = redissonClient.getLock(outPartnerAccId);
                        RLock inLock = redissonClient.getLock(inPartnerAccId);
                        log.info("分布式锁:lock:" + outLock.toString() + ",interrupted:" +
                                Thread.currentThread().isInterrupted() + ",hold:" +
                                outLock.isHeldByCurrentThread() + ",threadId:" +
                                Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                        log.info("分布式锁:lock:" + inLock.toString() + ",interrupted:" +
                                Thread.currentThread().isInterrupted() + ",hold:" +
                                inLock.isHeldByCurrentThread() + ",threadId:" +
                                Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                        try {
                            boolean out = outLock.tryLock(5, TimeUnit.SECONDS);
                            boolean in = inLock.tryLock(5, TimeUnit.SECONDS);
                            log.info("分布式锁:lock:" + outLock.toString() + ",interrupted:" +
                                    Thread.currentThread().isInterrupted() + ",hold:" +
                                    outLock.isHeldByCurrentThread() + ",threadId:" +
                                    Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                            log.info("分布式锁:lock:" + inLock.toString() + ",interrupted:" +
                                    Thread.currentThread().isInterrupted() + ",hold:" +
                                    inLock.isHeldByCurrentThread() + ",threadId:" +
                                    Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                            if (out && in) {
                                ResultUtil resultUtil = hxCallbackAPI.refundNotice(balancePayMessageBody);
                                if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
                                    throw new RuntimeException(resultUtil.getMsg());
                                }
                                redisUtil.set(CALL_BIZ_CODE_EXIST + balancePayMessageBody.getBizOrderNo(), 1, 60 * 60 * 24);
                            } else {
                                log.info("普通 - 车队长, 未获取锁，放回消息队列");
                                MQMessage mqMessage = new MQMessage();
                                mqMessage.setTopic(message.getTopic());
                                mqMessage.setTag(message.getTag());
                                mqMessage.setBody(balancePayMessageBody);
                                mqAPI.sendMessage(mqMessage);
                            }
                        } catch (Exception e) {
                            log.error("普通 - 车队长, 余额支付退款回调, error, {}", ThrowableUtil.getStackTrace(e));
                            MQMessage mqMessage = new MQMessage();
                            mqMessage.setTopic(message.getTopic());
                            mqMessage.setTag(message.getTag());
                            mqMessage.setBody(balancePayMessageBody);
                            mqAPI.sendMessage(mqMessage);
                        }  finally {
                            if (outLock.isLocked() && outLock.isHeldByCurrentThread()) {
                                log.info("分布式锁:lock:" + outLock.toString() + ",interrupted:" +
                                        Thread.currentThread().isInterrupted() + ",hold:" +
                                        outLock.isHeldByCurrentThread() + ",threadId:" +
                                        Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                                outLock.unlock();
                            }
                            if (inLock.isLocked() && inLock.isHeldByCurrentThread()) {
                                log.info("分布式锁:lock:" + inLock.toString() + ",interrupted:" +
                                        Thread.currentThread().isInterrupted() + ",hold:" +
                                        inLock.isHeldByCurrentThread() + ",threadId:" +
                                        Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                                inLock.unlock();
                            }
                        }
                        return Action.CommitMessage;
                    } else if (HXMqMessageTag.HX_REFUNDPACKCALLBACK.equals(tag)) {
                        // 交易退款回调
                        log.info("普通 - 车队长, 打包交易退款回调, {}", body);
                        AsyncNotifyVirtualBalancePayMessageBody balancePayMessageBody = JSONUtil.toBean(body, AsyncNotifyVirtualBalancePayMessageBody.class);
                        if (redisUtil.hasKey(CALL_BIZ_CODE_EXIST + balancePayMessageBody.getBizOrderNo())) {
                            log.info("普通 - 车队长, 打包交易退款回调, 请求已存在, {}", body);
                            return Action.CommitMessage;
                        }
                        String outPartnerAccId = balancePayMessageBody.getOutPartnerAccId();
                        String inPartnerAccId = balancePayMessageBody.getInPartnerAccId();
                        RLock outLock = redissonClient.getLock(outPartnerAccId);
                        RLock inLock = redissonClient.getLock(inPartnerAccId);
                        log.info("分布式锁:lock:" + outLock.toString() + ",interrupted:" +
                                Thread.currentThread().isInterrupted() + ",hold:" +
                                outLock.isHeldByCurrentThread() + ",threadId:" +
                                Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                        log.info("分布式锁:lock:" + inLock.toString() + ",interrupted:" +
                                Thread.currentThread().isInterrupted() + ",hold:" +
                                inLock.isHeldByCurrentThread() + ",threadId:" +
                                Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                        try {
                            boolean out = outLock.tryLock(5, TimeUnit.SECONDS);
                            boolean in = inLock.tryLock(5, TimeUnit.SECONDS);
                            log.info("分布式锁:lock:" + outLock.toString() + ",interrupted:" +
                                    Thread.currentThread().isInterrupted() + ",hold:" +
                                    outLock.isHeldByCurrentThread() + ",threadId:" +
                                    Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                            log.info("分布式锁:lock:" + inLock.toString() + ",interrupted:" +
                                    Thread.currentThread().isInterrupted() + ",hold:" +
                                    inLock.isHeldByCurrentThread() + ",threadId:" +
                                    Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                            if (out && in) {
                                ResultUtil resultUtil = hxCallbackAPI.packRefundNotice(balancePayMessageBody);
                                if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
                                    throw new RuntimeException(resultUtil.getMsg());
                                }
                                redisUtil.set(CALL_BIZ_CODE_EXIST + balancePayMessageBody.getBizOrderNo(), 1, 60 * 60 * 24);
                            } else {
                                log.info("普通 - 车队长, 未获取锁，放回消息队列");
                                MQMessage mqMessage = new MQMessage();
                                mqMessage.setTopic(message.getTopic());
                                mqMessage.setTag(message.getTag());
                                mqMessage.setBody(balancePayMessageBody);
                                mqAPI.sendMessage(mqMessage);
                            }
                        } catch (Exception e) {
                            log.error("普通 - 车队长, 打包余额支付退款回调, error, {}", ThrowableUtil.getStackTrace(e));
                            MQMessage mqMessage = new MQMessage();
                            mqMessage.setTopic(message.getTopic());
                            mqMessage.setTag(message.getTag());
                            mqMessage.setBody(balancePayMessageBody);
                            mqAPI.sendMessage(mqMessage);
                        }  finally {
                            if (outLock.isLocked() && outLock.isHeldByCurrentThread()) {
                                log.info("分布式锁:lock:" + outLock.toString() + ",interrupted:" +
                                        Thread.currentThread().isInterrupted() + ",hold:" +
                                        outLock.isHeldByCurrentThread() + ",threadId:" +
                                        Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                                outLock.unlock();
                            }
                            if (inLock.isLocked() && inLock.isHeldByCurrentThread()) {
                                log.info("分布式锁:lock:" + inLock.toString() + ",interrupted:" +
                                        Thread.currentThread().isInterrupted() + ",hold:" +
                                        inLock.isHeldByCurrentThread() + ",threadId:" +
                                        Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                                inLock.unlock();
                            }
                        }
                        return Action.CommitMessage;
                    } else {
                        log.error("普通 - 车队长, 消息类型错误, {}", JSONUtil.toJsonStr(message));
                        throw new RuntimeException("消息类型错误");
                    }
                } else {
                    log.error("普通 - 车队长, 消息内容为空，{}", JSONUtil.toJsonStr(message));
                    throw new RuntimeException("消息内容为空");
                }
            } else {
                log.error("普通 - 车队长, 消息类型错误, {}", JSONUtil.toJsonStr(message));
                throw new RuntimeException("消息类型错误");
            }
        } catch (Exception e) {
            log.error("普通 - 车队长,  error, {}", ThrowableUtil.getStackTrace(e));
            // 将消息入库
            mqAPI.saveMessage(message);
        }

        return Action.CommitMessage;
    }

}
