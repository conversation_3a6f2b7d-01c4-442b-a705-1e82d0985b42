package com.lz.hxyh.mq.listener;

import cn.hutool.json.JSONUtil;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.lz.api.HXCallbackAPI;
import com.lz.api.MqAPI;
import com.lz.common.config.RedisUtil;
import com.lz.common.constants.HXMqMessageTag;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.exception.hx.HxCommonResponseEnum;
import com.lz.common.exception.hx.HxTradeResponseEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.MQMessage;
import com.lz.common.model.hxPayment.request.query.CustomerQueryTradeReq;
import com.lz.common.model.hxPayment.request.trade.CustomerWithdrawReq;
import com.lz.common.model.hxPayment.response.CustomerWithdrawRes;
import com.lz.common.util.DateUtils;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.StringUtils;
import com.lz.common.util.ThrowableUtil;
import com.lz.hxyh.base.CloudPaymentBaseMethod;
import commonSdk.responseModel.CustomerQueryTradeResponse;
import commonSdk.responseModel.CustomerWithdrawResponse;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import sdk.model.message.AsyncNotifyVirtualMemberWithdrawMessageBody;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class HXWithdrawListener implements MessageListener {

    private final static String REQUEST_BIZ_CODE_EXIST = "REQUEST_BIZ_CODE_EXIST";
    private final static String CALL_BIZ_CODE_EXIST = "CALL_BIZ_CODE_EXIST";

    @Autowired
    CloudPaymentBaseMethod cloudPaymentBaseMethod;

    @Autowired
    private MqAPI mqAPI;

    @Autowired
    private HXCallbackAPI hxCallbackAPI;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private RedisUtil redisUtil;

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String tag = message.getTag();
        String body = new String(message.getBody());
        log.info("提现, {}",  body);
        try {
            if (HXMqMessageTag.HX_WITHDRAWAPPLY.equals(tag)) {
                // 提现申请
                log.info("提现申请, {}", body);
                CustomerWithdrawReq request = JSONUtil.toBean(body, CustomerWithdrawReq.class);
                if (redisUtil.hasKey(REQUEST_BIZ_CODE_EXIST + request.getBizOrderNo())) {
                    log.info("提现申请, 请求已存在, {}", body);
                    return Action.CommitMessage;
                }
                String key = request.getBizOrderNo();
                RLock lock = redissonClient.getLock(key);
                boolean tryLock = lock.tryLock(5, TimeUnit.SECONDS);
                if (tryLock) {
                    Map<String, Object> execute = cloudPaymentBaseMethod.execute(request);
                    log.info("提现申请结果, {}", JSONUtil.toJsonStr(execute));
                    CustomerWithdrawResponse response = JSONUtil.toBean(JSONUtil.parseObj(execute), CustomerWithdrawResponse.class);
                    if (DictEnum.ACCEPT_SUCC.code.equals(response.getOrderStatus())
                            || DictEnum.PAY_SUCC.code.equals(response.getOrderStatus())
                            || DictEnum.PAY_ING.code.equals(response.getOrderStatus())
                            || DictEnum.REQUEST_SUCCESS.code.equals(response.getResponseCode())) {
                        redisUtil.set(REQUEST_BIZ_CODE_EXIST + response.getBizOrderNo(), 1, 60 * 60 * 24);
                        return Action.CommitMessage;
                    } else {
                        boolean payResult = true;
                        // 通用异常，查询交易状态
                        if (HxCommonResponseEnum.hasCode(response.getResponseCode())) {
                            CustomerQueryTradeReq tradeRequest = new CustomerQueryTradeReq();
                            tradeRequest.setRequestId(IdWorkerUtil.getInstance().nextId());
                            tradeRequest.setRequestTime(DateUtils.getRequestTime());
                            tradeRequest.setPartnerId(request.getPartnerId());
                            tradeRequest.setChannelId(request.getChannelId());
                            tradeRequest.setBizOrderNo(request.getBizOrderNo());
                            tradeRequest.setTradeType("WIDR");
                            Map<String, Object> queryExecute = cloudPaymentBaseMethod.execute(tradeRequest);
                            CustomerQueryTradeResponse tradeResponse =  JSONUtil.toBean(JSONUtil.parseObj(queryExecute), CustomerQueryTradeResponse.class);
                            if (null == tradeResponse.getResponseCode()
                                    || StringUtils.isBlank(tradeResponse.getResponseCode())
                                    || HxCommonResponseEnum.UNKONWN.code.equals(tradeResponse.getResponseCode())
                                    || (HxTradeResponseEnum.RESPONSE000000.getCode().equals(tradeResponse.getResponseCode())
                                    && DictEnum.PAY_FAIL.code.equals(tradeResponse.getOrderStatus()))) {
                                payResult = false;
                            }
                        }
                        if (null == response.getResponseCode()
                                || StringUtils.isBlank(response.getResponseCode())
                                || !payResult) {
                            // 修改支付子表
                            CustomerWithdrawRes res = new CustomerWithdrawRes();
                            BeanUtils.copyProperties(response, res);
                            res.setBizOrderNo(request.getBizOrderNo());
                            res.setOrderAmount(request.getOrderAmount());
                            res.setPartnerAccId(request.getPartnerAccId());
                            hxCallbackAPI.txFail(res);
                        }

                        throw new RuntimeException(JSONUtil.toJsonStr(execute));
                    }
                } else {
                    log.info("发起提现未获取锁，放回消息队列");
                    MQMessage mqMessage = new MQMessage();
                    mqMessage.setTopic(message.getTopic());
                    mqMessage.setTag(message.getTag());
                    mqMessage.setBody(request);
                    mqAPI.sendMessage(mqMessage);
                    return Action.CommitMessage;
                }

            } else if (HXMqMessageTag.HX_WITHDRAWCALLBACK.equals(tag)) {
                // 提现回调
                log.info("提现回调, {}", JSONUtil.toJsonStr(body));
                AsyncNotifyVirtualMemberWithdrawMessageBody messageBody = JSONUtil.toBean(body, AsyncNotifyVirtualMemberWithdrawMessageBody.class);
                if (redisUtil.hasKey(CALL_BIZ_CODE_EXIST + messageBody.getBizOrderNo())) {
                    log.info("提现回调, 请求已存在, {}", body);
                    return Action.CommitMessage;
                }
                String partnerAccId = messageBody.getPartnerAccId();
                RLock lock = redissonClient.getLock(partnerAccId);
                log.info("分布式锁:getLock:" + lock.toString() + ",interrupted:" +
                        Thread.currentThread().isInterrupted() + ",hold:" +
                        lock.isHeldByCurrentThread() + ",threadId:" +
                        Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                Boolean walletLock = false;
                try {
                    walletLock = lock.tryLock();
                    log.info("分布式锁:tryLock:" + lock.toString() + ",interrupted:" +
                            Thread.currentThread().isInterrupted() + ",hold:" +
                            lock.isHeldByCurrentThread() + ",threadId:" +
                            Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                    if (walletLock) {
                        ResultUtil resultUtil = hxCallbackAPI.txNotice(messageBody);
                        if (DictEnum.ERROR.code.equals(resultUtil.getCode())) {
                            throw new RuntimeException(resultUtil.getMsg());
                        }
                        redisUtil.set(CALL_BIZ_CODE_EXIST + messageBody.getBizOrderNo(), 1, 60 * 60 * 24);
                    } else {
                        log.info("未获取锁，放回消息队列");
                        MQMessage mqMessage = new MQMessage();
                        mqMessage.setTopic(message.getTopic());
                        mqMessage.setTag(message.getTag());
                        mqMessage.setBody(messageBody);
                        mqAPI.sendMessage(mqMessage);
                        return Action.CommitMessage;
                    }
                } catch (Exception e) {
                    log.error("提现回调失败, {}", ThrowableUtil.getStackTrace(e));
                    mqAPI.saveMessage(message);
                } finally {
                    if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                        log.info("分布式锁:finally:" + lock.toString() + ",interrupted:" +
                                Thread.currentThread().isInterrupted() + ",hold:" +
                                lock.isHeldByCurrentThread() + ",threadId:" +
                                Thread.currentThread().getId() + ",redissonClient:{}" + redissonClient);
                        lock.unlock();
                    }
                }
            }
        } catch (Exception e) {
            log.error("WITHDRAW error, {}", ThrowableUtil.getStackTrace(e));
            // 将消息入库
            mqAPI.saveMessage(message);
        }

        return Action.CommitMessage;
    }

}
