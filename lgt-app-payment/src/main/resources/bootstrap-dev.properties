eureka.client.serviceUrl.defaultZone=*********************************************/eureka/


spring.cloud.config.discovery.serviceId=lgt-app-config
spring.cloud.config.discovery.enabled=true
spring.cloud.config.profile=dev
spring.cloud.config.label=master

logging.level.com.lz.dao=DEBUG

spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL5InnoDBDialect
spring.data.redis.repositories.enabled=false
spring.jpa.open-in-view=true
spring.jpa.hibernate.ddl-auto=update
spring.data.jpa.repositories.enabled=true
mybatis.configuration.map-underscore-to-camel-case=true


spring.thymeleaf.cache=false 
spring.http.encoding.force=true
server.port=2019
ftp.server.info=sftp://*************:22/../home/<USER>/data?username=root&password=root&delay=5s&move=done&readLock=rename
ftp.local.dir=file:C:/ftp/test
gopTest.sftp.localPath=/opt/pay/config/basis/goptest/tempUpload
gop.tpu.url=http://test.tc.mybank.cn/gop/gateway.do
gop.mag.url=http://test.tc.mybank.cn/gop/gateway.do
#notify.url=http://test.tc.mybank.cn/gop/mag/syncNotify.htm
notify.url=http://**************:8181/notfiy/payNotfiy
asyn.notify.url=test.tc.mybank.cn/gop/mag/asynNotify.htm
gop-test.url=http://localhost:8080
partner.id=************
ip_role_id=226610000051657147614
wallet.public.key=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQD5HGOnoWSeY1pjkruBlSTjy0ivqCHtO8Rc0SgycaVLj15Wdn9Wp/K0P+HYKm2BMMdieApHmITteNFahZho7CPcVFDoed3XpkJfZ0qgob27ezl4jALNaYacPr+L/9haxmZYf/Kc5QOMgDszCKEBkfUc3YPRuP5a6Q6dZe5iUzfaTwIDAQAB
merchant.private.key=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQD5HGOnoWSeY1pjkruBlSTjy0ivqCHtO8Rc0SgycaVLj15Wdn9Wp/K0P+HYKm2BMMdieApHmITteNFahZho7CPcVFDoed3XpkJfZ0qgob27ezl4jALNaYacPr+L/9haxmZYf/Kc5QOMgDszCKEBkfUc3YPRuP5a6Q6dZe5iUzfaTwIDAQAB
md5.key=lewistest
#SFTP地址
gopTest.sftp.ip=************
#SFTP 端口
gopTest.sftp.port=22
#SFTP 用户名
gopTest.sftp.userName=tmybank
#SFTP 密码
gopTest.sftp.pwd=tmybank123QAZ
white.channel.code=MYBANK00097

access.channel.code=ALIPAY00074
charset=UTF-8
sign_type=TWSIGN
version=2.1

tomcat.accesslog.buffered=true
tomcat.accesslog.directory=log
tomcat.accesslog.enabled=true
tomcat.accesslog.file-date-format=.yyyy-MM-dd
tomcat.accesslog.pattern=common
tomcat.accesslog.prefix=access_log
tomcat.accesslog.rename-on-rotate=false
tomcat.accesslog.request-attributes-enabled=false
tomcat.accesslog.rotate=true
tomcat.accesslog.suffix=.log

rocketmq.groupId.GID_JD_PAY_COMMON_DRIVER=GID_JD_PAY_COMMON_DRIVER
rocketmq.groupId.GID_JD_PAY_COMMON_CAPTAIN=GID_JD_PAY_COMMON_CAPTAIN
rocketmq.groupId.GID_JD_PAY_MANAGER_DRIVER=GID_JD_PAY_MANAGER_DRIVER
rocketmq.groupId.GID_JD_PAY_MANAGER_CAPTAIN=GID_JD_PAY_MANAGER_CAPTAIN
rocketmq.groupId.GID_JD_PAY_PREPAYMENT=GID_JD_PAY_PREPAYMENT
rocketmq.groupId.GID_JD_PAY_WITHDRAW=GID_JD_PAY_WITHDRAW

rocketmq.groupId.GID_PAY_COMPANY_CARRIER=GID_PAY_COMPANY_CARRIER
rocketmq.groupId.GID_PAY_CARRIER_DRIVER=GID_PAY_CARRIER_DRIVER
rocketmq.groupId.GID_PAY_DRIVER_CAPTAIN=GID_PAY_DRIVER_CAPTAIN
rocketmq.groupId.GID_PAY_FAIL=GID_PAY_FAIL

# 华夏
rocketmq.groupId.GID_HX_PAY_COMMON_DRIVER=GID_HX_PAY_COMMON_DRIVER
rocketmq.groupId.GID_HX_PAY_COMMON_CAPTAIN=GID_HX_PAY_COMMON_CAPTAIN
rocketmq.groupId.GID_HX_PAY_MANAGER_DRIVER=GID_HX_PAY_MANAGER_DRIVER
rocketmq.groupId.GID_HX_PAY_MANAGER_CAPTAIN=GID_HX_PAY_MANAGER_CAPTAIN
rocketmq.groupId.GID_HX_PAY_PREPAYMENT=GID_HX_PAY_PREPAYMENT
rocketmq.groupId.GID_HX_PAY_WITHDRAW=GID_HX_PAY_WITHDRAW
rocketmq.accessKey=LTAI5tEBqBfojE4kD4KYYg1Y
rocketmq.secretKey=******************************
rocketmq.nameSrvAddr=http://MQ_INST_1364524203166562_BXSHCMfU.mq-internet-access.mq-internet.aliyuncs.com:80

ayt.payment.callback.ip =