			var tooltipObj = new DHTMLgoodies_formTooltip();
			tooltipObj.setTooltipPosition('right');
			tooltipObj.setPageBgColor('#EEEEEE');
			tooltipObj.setTooltipCornerSize(15);
			tooltipObj.initFormFieldTooltip();
			var jsonArray = new Array();
			var paymentToCardDetailList = new Array();
			//显示层信息
			function Show(obj) {
				obj.style.display = "block";
			}

			//隐藏信息层
			function Hide(obj) {
				obj.style.display = "none";
			}

			function getNewOrderNo(){
				return new DateFormat("yyyyMMddHHmmss").format(new Date())+Math.random(100000000000000000, 900000000000000000);
			}

			function getTradeTimestamp(){
				return new DateFormat("yyyyMMddHHmmss").format(new Date());
			}

			function newOrder() {
				$('#outer_trade_no').textbox("setValue",getNewOrderNo());
				$('#outer_inst_order_no').textbox("setValue",getNewOrderNo());
				$('#request_no').textbox("setValue",getNewOrderNo());
			}
			
			function newDepositOrder() {
				$('#request_no').textbox("setValue",getNewOrderNo());
				$('#outer_trade_no').textbox("setValue",getNewOrderNo());
				//$('#return_url')notifyAddressUrl
			}

			function newOuterOrder() {
				$('#outer_trade_no').textbox("setValue",getNewOrderNo());
				$('#outer_inst_order_no').textbox("setValue",getNewOrderNo());
				$('#batch_no').textbox("setValue",getNewOrderNo());
				$('#source_batch_no').textbox("setValue",getNewOrderNo());
				$('.trade-timestamp').textbox("setValue",getTradeTimestamp());
				//$('.start_time').val(getTradeTimestamp());
				$('.end_time').textbox("setValue",getTradeTimestamp());
				//$('#return_url')notifyAddressUrl
			}

			function sendRequest() {
				changePayMethod();
				var paramString = assembleParamString();
				var signType = $('#sign_type').val();
				var sign = encodeURIComponent($('#sign').val());
				var data = "sign_type=" + signType + "&sign=" + sign + paramString;
				var targetUrl = $('#magAddressUrl').val();
				var linkString = $('#linkString').val();
				//alert(linkString);
				window.open(targetUrl + '?' + linkString,'','height=500,width=800,scrollbars=yes,status =yes');
			}
			function sendRequestForBatch(myFormId) {
				
				//alert($('#'+myFormId).serialize());
				jQuery.ajax({

                    url:"mag/remoteGop.htm",
                    
                    data:$('#'+myFormId).serialize(),
                    
                    type:"POST",
                    dataType:"text",
                    success:function(result)
                    
                    {
						alert(result);
						
                    },
					error:  function(XMLHttpRequest, textStatus, errorThrown){
                    
						alert(errorThrown);
                  }	
                    
                    });
				
			}
			function sendQueryBatchRequest(pageclickednumber,queryType) {
	        	if(!pageclickednumber){
	        		pageclickednumber = 1;
	        	}
	        	$("#current_page").val(pageclickednumber);
				//重新签名
				getSign();
				changePayMethod();
				var paramString = assembleParamString();
				//alert(paramString);
				var signType = $('#sign_type').val();
				var sign = encodeURIComponent($('#sign').val());
				var data = "sign_type=" + signType + "&sign=" + sign + paramString;
				//var targetUrl = $('#magAddressUrl').val();
				var targetUrl = "mag/BatchQuery.htm";
				var linkString = $('#linkString').val();
				//window.open(targetUrl + '?' + data,'','height=500,width=800,scrollbars=yes,status =yes');
				//alert(targetUrl+'?'+linkString);
				//alert(data);
				$.ajax(targetUrl,{
					type : 'GET',
					contentType: "application/x-www-form-urlencoded; charset=utf-8",
					data : data
				}).done(function(result){
					var datax = eval("("+result+")");
					if(datax["is_success"] == "F"){
						alert(result);
					}else{
						//情况原始数据
						$("#queryPamentList").html("");
						//动态生成查询明
					     var table=$("<table border=\"1\">");
					     table.appendTo($("#queryPamentList"));
						if(queryType == 1 || queryType==2){
							var totalTitle =$("<tr><td>总笔数</td><td>总金额</td><td>状态</td><td>错误码</td><td>错误信息</td><td>操作员</td></tr>");
							var totalData = $("<tr><td>"+doEmpty(datax.total_count)+"</td><td>"+doEmpty(datax.total_amount)+"</td><td>"+doEmpty(datax.status)+"</td><td>"+doEmpty(datax.error_code)+"</td><td>"+doEmpty(datax.error_msg)+"</td><td>"+doEmpty(datax.operator)+"</td></tr>");
							totalTitle.appendTo(table);
							totalData.appendTo(table);
						}

					     var tHeadForBathPamentToCard  =$("<tr><td>订单号</td><td>凭证交易订单号</td><td>产品码</td><td>代付金额</td><td>状态</td><td>失败原因</td><td>支付时间</td><td>备注</td></tr>");
					     var tHeadForBathCollectionPay  =$("<tr><td>订单号</td><td>凭证交易订单号</td><td>产品码</td><td>代扣金额</td><td>已扣金额</td><td>当日已扣金额</td><td>状态</td><td>失败原因</td><td>支付时间</td><td>备注</td></tr>");
					     if(queryType == 1){
					    	 tHeadForBathPamentToCard.appendTo(table);
					     }
					     if(queryType == 2 || queryType==3){
					    	 tHeadForBathCollectionPay.appendTo(table);
					     }
					   
					     //解析jsonList
					     var detailList;
					     if(queryType == 2 || queryType == 3){
					    	 detailList = datax.batch_collection_pay_detail_list;
					     }else{
					    	 detailList = datax.batch_pay_to_carddetail_list;
					     }
					    	 
					    	
					     
					     //detailList = eval("("+detailList+")");
					     for(var i=0;i<detailList.length;i++)
					     {
					        var tr=$("<tr></tr>");
					        tr.appendTo(table);
					        var td=$("<td>"+doEmpty(detailList[i]["outer_trade_no"])+"</td>");
					        td.appendTo(tr);
					        td=$("<td>"+doEmpty(detailList[i]["inner_trade_no"])+"</td>");
					        td.appendTo(tr);
					        td=$("<td>"+doEmpty(detailList[i]["product_code"])+"</td>");
					      
					        td.appendTo(tr);
					        td=$("<td>"+doEmpty(detailList[i]["amount"])+"</td>");
					        td.appendTo(tr);
					        if(queryType == 2 || queryType == 3){
						        td=$("<td>"+doEmpty(detailList[i]["paid_amount"])+"</td>");
						        td.appendTo(tr);
						        td=$("<td>"+doEmpty(detailList[i]["paid_amount_today"])+"</td>");
						        td.appendTo(tr);
					        }

					        td=$("<td>"+doEmpty(detailList[i]["status"])+"</td>");
					        td.appendTo(tr);
					        td=$("<td>"+doEmpty(detailList[i]["fail_reason"])+"</td>");
					        td.appendTo(tr);
					        td=$("<td>"+doEmpty(detailList[i]["pay_submit_time"])+"</td>");
					        td.appendTo(tr);
					        td=$("<td>"+doEmpty(detailList[i]["memo"])+"</td>");
					        td.appendTo(tr);
					     }
//			 		     tr.appendTo(table);
					     $("#queryPamentList").append("</table>");
					     //获取每页最大行数
					     var pageSize =Number($("#page_size").val());
						var pageCountx = Number(datax.total_count)/pageSize;
						var modelNum = Number(datax.total_count)%pageSize;
						if(modelNum>0){
							pageCountx = pageCountx+1;
						}
						if(pageCountx < 1){
							pageCountx = 1;
						}
						if(!pageCountx){
							pageCountx = 1;
						}
						if(!pageclickednumber){
							pageclickednumber = 1;
						}
						$("#pager").pager({ pagenumber: pageclickednumber, pagecount:pageCountx, buttonClickCallback: PageClick });
						
						
					}
					
				});
			}
			
			function doEmpty(str){
				if(!str){
					return "";
				}
				return str;
			}
			
			
			function sendBatchPayRequest() {				
				var targetUrl = $('#magAddressUrl').val();
				var linkString = $('#linkString').val();
				//window.open(targetUrl + '?' + data,'','height=500,width=800,scrollbars=yes,status =yes');
				//alert(targetUrl+'?'+linkString);
				window.open(targetUrl + '?' + linkString,'','height=500,width=800,scrollbars=yes,status =yes');
			}
			function getSign() {
				changePayMethod();
				var paramNames = assembleParamNames();
				var paramString = assembleParamString();
				var signType = $('#sign_type').val();
				var signKey = encodeURIComponent($('#sign_key').val());
				//alert(signKey);
				var data = "signType=" + signType + "&signKey=" + signKey + paramNames + paramString;
				//alert(data);
				$.ajax('mag/sign.htm',{
					type : 'POST',
					async: false,
					dataType : 'json',
					contentType: "application/x-www-form-urlencoded; charset=utf-8",
					data : data
				}).done(function(result){
				    $("#sign").val(result["msg0"]);
					$("#linkString").val(result["msg1"]);
				});
			}
			
			function getBatchPaySign() {	
				var payMethod = $('#pay_method').val();
				var paramNames = assembleParamNames();
				var paramString = assembleParamString();
				var signType = $('#sign_type').val();
				var signKey = encodeURIComponent($('#sign_key').val());
				//alert(signKey);
				var data = "signType=" + signType + "&signKey=" + signKey + paramNames + paramString+"&payMethod="+payMethod;
				//alert(data);
				$.ajax('mag/sign.htm',{
					type : 'POST',
					dataType : 'json',
					contentType: "application/x-www-form-urlencoded; charset=utf-8",
					data : data
				}).done(function(result){
				    $("#sign").val(result["msg0"]);
					$("#linkString").val(result["msg1"]);
				});
			}			
			
			function getEncrypt() {
				//alert(signKey);
				var publicKey = $('#public_key').val();
				var inputCharset=$('#_input_charset').val();
				var data = "public_key="+encodeURIComponent(publicKey)
				+"&_input_charset="+encodeURIComponent(inputCharset)+assembleEncryptParamString();
//				alert(data);
				$.ajax('mag/encrypt.htm',{
					type : 'POST',
					dataType : 'json',
					contentType: "application/x-www-form-urlencoded; charset=utf-8",
					data : data
				}).done(function(result){
					if(result["msg0"]){
						$("#bank_account_no").val(result["msg0"]);
						$("#account_cardno").val(result["msg0"]);
					}
				    if(result["msg1"]){
				    	$("#account_name").val(result["msg1"]);
				    }
				    
					
					$("#card_info").val(result["msg2"]);
				});
			}
			function assembleParamNames(){
				var params = $('.default-q');
				var q = "";
				for(var idx=0; idx<params.length-1; idx++){
					var tmp = params[idx].name + ",";
					q += tmp;

				}
				q += params[params.length-1].name;
				return "&paramNames=" + q;
			}

			function assembleParamString(){
				var params = $('.default-q');
				var q = "";
				for(var idx=0; idx<params.length; idx++){
					var tmp = "&" + params[idx].name + "=" + encodeURIComponent(params[idx].value);
					q += tmp;

				}
				return q;
			}
			function assembleEncryptParamString(){
				var params = $('.default-e');
				var q = "";
				for(var idx=0; idx<params.length; idx++){
					var tmp = "&" + params[idx].name + "=" + encodeURIComponent(params[idx].value);
					q += tmp;

				}
				return q;
			}

//			function assembleTradeInfo() {
//				var tradeNo = getNewOrderNo();
//				$('#tradeParam1').val(tradeNo);
//				$('.trade-timestamp').val(getTradeTimestamp());
//				var tradeInfo = '';
//				var params = $('input[id^=tradeParam]');
//				for(var idx=0; idx<params.length-1; idx++){
//					var para = params[idx].value;
//					para = para.length + ':' + para;
//					tradeInfo += para;
//					tradeInfo +='~';
//				}
//				var para = params[params.length-1].value;
//				para = para.length + ':' + para;
//				tradeInfo += para;
//				return tradeInfo;
//			}

			function assembleTradeInfo(sep) {
				var tradeNo = getNewOrderNo();
				$('#tradeParam1').textbox("setValue",tradeNo);
				$('.trade-timestamp').textbox("setValue",getTradeTimestamp());
				var service = $('#service').val(); // 用于判断是否要处理分润集
				var tradeInfo = '';
				var params = $('input[id^=tradeParam]');
				for(var idx=0; idx<params.length-1; idx++){
					var para = params[idx].value;
					if (idx == 5 && service=='create_instant_trade' && para) {
						// 分润集处理
						para = parseRoyalty(para);
					}
					para = para.length + ':' + para;
					tradeInfo += para;
					tradeInfo += sep;
				}
				var para = params[params.length-1].value;
				para = para.length + ':' + para;
				tradeInfo += para;
				return tradeInfo;
			}

			function addTradeInfo() {
				var tradeInfo = assembleTradeInfo('~');
				var curTradeInfo = $('#trade_info').val();
				if('' == curTradeInfo){
					$('#trade_info').val(tradeInfo);
				}else{
					$('#trade_info').val(curTradeInfo + '$' + tradeInfo);
				}
			}
			/**
			 * 添加折扣信息
			 */
			function addDiscountInfo() {
				var discountInfo = $('#discount_info').val();
				var discount_type = $('#discount_type').val();
				var instCode = $('#instCode').val();
				var amountInfo = $('#amountInfo').val();
				if(discount_type==null||discount_type==""){
					alert("折扣类型不能为空");
					return false;
				}
				if(instCode==null||instCode==""){
					alert("折扣来源平台不能为空");
					return false;
				}
				if(amountInfo==null||amountInfo==""){
					alert("折扣金额信息不能为空");
					return false;
				}
				var discountJson={discount_type:discount_type,instCode:instCode,amountInfo:amountInfo};
				jsonArray.push(discountJson); 
				$('#discount_info_hidden').val(JSON.stringify(jsonArray));
			}
			/**
			 * 添加付款明细
			 */
			function addPaymentToCardDetail() {
				var oldCount = Number($("#total_count").val());
				var oldAmount = Number($("#total_amount").val());
				var detailAmount = Number($("#amount").val());
				var totalAmount = oldAmount+detailAmount;
				totalAmount = totalAmount.toFixed(2);
				$("#total_count").val(oldCount+1);
				$("#total_amount").val(totalAmount);
				$("#outer_trade_no").val(getNewOrderNo());
				var paymentTocardDetail={};
				var params = $('.pamentToCardDetailItem');
				for(var idx=0; idx<params.length; idx++){
					paymentTocardDetail[params[idx].name] = params[idx].value;
				}
				paymentToCardDetailList.push(paymentTocardDetail);
				$("#detail_list").val(JSON.stringify(paymentToCardDetailList));
			}
			/**
			 * 批量添加500付款明细
			 */
			function addBatchPaymentToCardDetail(detailSum){
				for(var i=0;i<detailSum;i++){
					addPaymentToCardDetail();
				}
			}

			/**
			 * 批量添加500收款明细
			 */
			function addBatchCollectionPayDetail(detailCount){
				for(var i=0;i<detailCount;i++){
					addCollectionPayDetail();
				}
			}
			/**
			 * 添加收款明细
			 */
			function addCollectionPayDetail() {
				var oldCount = Number($("#total_count").val());
				var oldAmount = Number($("#total_amount").val());
				var detailAmount = Number($("#amount").val());
				var totalAmount = oldAmount+detailAmount;
				totalAmount = totalAmount.toFixed(2);
				$("#total_count").val(oldCount+1);
				$("#total_amount").val(totalAmount);
				$("#outer_trade_no").val(getNewOrderNo());
				var paymentTocardDetail={};
				var params = $('.collectionPayDetailItem');
				for(var idx=0; idx<params.length; idx++){
					paymentTocardDetail[params[idx].name] = params[idx].value;
				}
				paymentToCardDetailList.push(paymentTocardDetail);
				$("#detail_list").val(JSON.stringify(paymentToCardDetailList));
			}
			
			/**
			 * 清空明细
			 */
			function clearDetail(){
				$("#total_count").val(0);
				$("#total_amount").val(0);
				$("#detail_list").val("");
				paymentToCardDetailList = new Array();
				
			}
			
			/**
			 * 
			 * 添加卡信息
			 */
			function addCardInfo() {
				var cardInfo={};
				var params = $('.cardInfo');
				for(var idx=0; idx<params.length; idx++){
					cardInfo[params[idx].name] = params[idx].value;
					
				}
				$("#card_info_src").val(JSON.stringify(cardInfo));
			}
			/**
			 * 确认折扣信息
			 */
			function affirmDiscountInfo() {
				var discountJsonObj = jsonArray[0];
				if(discountJsonObj){
					$('#discount_pay_method').val(JSON.stringify(jsonArray));
					//alert($('#discount_pay_method').val());
					jsonArray = new Array();
				}else{
					alert("追加折扣信息不能为空");
					return false;
				}
				
			}

			function addTradeInfo(targetId, sep, app) {
				var tradeInfo = assembleTradeInfo(sep);
				var curTradeInfo = $('#'+targetId).val();
				if('' == curTradeInfo){
					$('#'+targetId).val(tradeInfo);
				}else{
					$('#'+targetId).val(curTradeInfo + app + tradeInfo);
				}
			}
			function changePayMethod(){
				var payMethodSrc=$('#pay_method_src').val();
				var payAmountSrc=$('#pay_amount_src').val();
				var payExtSrc=$('#pay_ext_src').val();
				var payExtensionSrc=$('#pay_extension_src').val();
				var payMethod1=$('#payMethod1').val();
				var payMethod2=$('#payMethod2').val();
				var jsonPayArray = new Array();
				jsonPayArray
				if(payMethodSrc==null||payMethodSrc==""){
					$('#pay_method').val("");
				}else{
					if(payMethodSrc.indexOf("~") > 1) {
						var payMethodSrcArr = new Array();
						var payAmountSrcArr = new Array();
						var payExtSrcArr = new Array();
						var payExtensionSrcArr = new Array();
						payMethodSrcArr = payMethodSrc.split("~");
						payAmountSrcArr = payAmountSrc.split("~");
						payExtSrcArr = payExtSrc.split("~");
						payExtensionSrcArr = payExtensionSrc.split("~");
						var dataStr= "";
						var payStr= "";
						var max = 0;
						if(payMethodSrcArr.length > payAmountSrcArr.length) {
							max = payMethodSrcArr.length;
							for(var i = 0 ; i <max; i++) {
								if(i == 0 ) {
									payStr += payMethodSrcArr[i].length+':' +payMethodSrcArr[i];
								} else {
									payStr += "|" + payMethodSrcArr[i].length+':' +payMethodSrcArr[i];
								}
								if(typeof(payAmountSrcArr[i])!="undefined"){
									payStr+="^"+payAmountSrcArr[i].length+':' +payAmountSrcArr[i];
								}
								 
								if(typeof(payExtSrcArr[i])!="undefined"){
									payStr+="^"+payExtSrcArr[i].length+':' +payExtSrcArr[i];
								}
								if(typeof(payExtensionSrcArr[i])!="undefined"){
									payStr+="^"+payExtensionSrcArr[i].length+':' +payExtensionSrcArr[i];
								}
							}
						} else {
							max = payAmountSrcArr.length;
							for(var i = 0 ; i <max; i++) {
							if(i == 0 ) {
								payStr += payMethodSrcArr[i].length+':' +payMethodSrcArr[i];
							} else {
								if(typeof(payMethodSrcArr[i])!="undefined") {
									payStr += "|" + payMethodSrcArr[i].length+':' +payMethodSrcArr[i];
								}
							}
							
							if(typeof(payMethodSrcArr[i])=="undefined") {
								if(typeof(payAmountSrcArr[i])!="undefined"){
									payStr += "|" + payAmountSrcArr[i].length+':' +payAmountSrcArr[i];
								}
							} else {
								if(typeof(payAmountSrcArr[i])!="undefined"){
									payStr+="^"+payAmountSrcArr[i].length+':' +payAmountSrcArr[i];
								}
							}
							if(typeof(payExtSrcArr[i])!="undefined"){
								payStr+="^"+payExtSrcArr[i].length+':' +payExtSrcArr[i];
							}
							if(typeof(payExtensionSrcArr[i])!="undefined"){
								payStr+="^"+payExtensionSrcArr[i].length+':' +payExtensionSrcArr[i];
							}
							}
						}
						$('#pay_method').textbox("setValue",payStr);
					} else {
						var payExt=new Array()
						payExt = payExtSrc.split(",");
						if(payMethodSrc.toUpperCase() == "QPAY"
								
						) {
							if(payExt.length < 4) {
								if(payMethod1.length > 5) {
									payMethod1 = "N" + payMethod1;
								} else if(payMethod1.length < 1){
									payMethod1 = "B" + payMethod1;
								}
								payExtSrc += "," + payMethod1 + "," + payMethod2;
							}
						} else if(payMethodSrc.toUpperCase() == "BALANCE") {
							if(payExt.length < 4) {
								payExtSrc += "," + payMethod1 + "," + payMethod2;
							}
							
						}
						//{"pay_method":" balance","amount":"0.3","memo":"备注","extension":""}
						var payStr = {pay_method:payMethodSrc,amount:payAmountSrc,memo:payExtSrc,extension:payExtensionSrc};
						jsonPayArray.push(payStr);
						var payStr=payMethodSrc.length+':' +payMethodSrc;
						payStr+="^"+payAmountSrc.length+':' +payAmountSrc;
						payStr+="^"+payExtSrc.length+':' +payExtSrc;
						payStr+="^"+payExtensionSrc.length+':' +payExtensionSrc;
						$('#pay_method').textbox("setValue",JSON.stringify(jsonPayArray).replace("[","").replace("]",""));
						//$('#pay_method').textbox("setValue",payStr);
						
					}
				}
			}
			
			function newTradeInfo() {
				var tradeInfo = assembleTradeInfo('~');
				$('#trade_info').val(tradeInfo);
			}

			function newTradeInfo(targetId,sep) {
				var tradeInfo = assembleTradeInfo(sep);
				$('#'+targetId).textbox("setValue",tradeInfo);
			}

			function sendRequestForOperatorSubstituteRecharge() {
				var payMethod = $('#pay_method').val();
				var ext1 = $('#ext1').val();
				if("cash" == payMethod && "" == ext1){
					alert("支付方式为现金（cash）,扩展字段1必填现金存根号");
					return;
				}
				sendRequest();
			}

			function parseRoyalty(origRoy) {
				var result ='';
				var strs= new Array(); //定义一数组
				strs=origRoy.split("|"); //分割成不同的分润记录
				for (i=0;i<strs.length ;i++ ) {
					var seg = new Array();
					seg = strs[i].split("^"); //分割成UID，账户类型，金额
					for (j=0;j<seg.length ;j++ ) {
						seg[j] = seg[j].length + ':' + seg[j];
						result +=seg[j];
						if (j < seg.length -1) {
							result +="^";
						}
					}
					if (i < strs.length -1) {
						result +="|";
					}
				}
				return result;
			}
			/**
			 * 添加手续费信息
			 */
			function addFeeInfo() {
				var feeInfo = $('#fee_info').val();
				var sellerFee = $('#sellerFee').val();
				var buyerFee = $('#buyerFee').val();
				var feeInfoJson={sellerFee:sellerFee,buyerFee:buyerFee};
				jsonArray.push(feeInfoJson); 
				$('#fee_info_hidden').textbox("setValue",JSON.stringify(jsonArray));
			}
			/**
			 * 确认手续费信息
			 */
			function affirmFeeInfo() {
				
				var FeeInfoJsonObj = jsonArray[0];
				if(FeeInfoJsonObj){
					$('#fee_info').textbox("setValue",JSON.stringify(jsonArray).replace("[","").replace("]",""));
					
					//alert($('#discount_pay_method').val());
					jsonArray = new Array();
				}else{
					alert("追加手续费信息不能为空");
					return false;
				}
				
			}
