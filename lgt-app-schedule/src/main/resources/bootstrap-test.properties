eureka.client.serviceUrl.defaultZone=*********************************************/eureka/
spring.cloud.config.discovery.serviceId=lgt-app-config
spring.cloud.config.discovery.enabled=true
spring.cloud.config.profile=test
spring.cloud.config.label=master

spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL5InnoDBDialect
spring.data.redis.repositories.enabled=false
spring.jpa.open-in-view=true
spring.jpa.hibernate.ddl-auto=update
spring.data.jpa.repositories.enabled=true
# Specify the DBMS
spring.jpa.database = MYSQL
# Show or not log for each sql query
spring.jpa.show-sql = true
# DDL mode. This is actually a shortcut for the "hibernate.hbm2ddl.auto" property. Default to "create-drop" when using an embedded database, "none" otherwise.
spring.jpa.generate-ddl=true
# Hibernate 4 naming strategy fully qualified name. Not supported with Hibernate 5.
spring.jpa.hibernate.naming.strategy = org.hibernate.cfg.ImprovedNamingStrategy
# stripped before adding them to the entity manager)

logging.level.com.lz.dao = debug
#tx-lcn.manager.host=*************
#tx-lcn.client.manager-address=*************:9999

htPath=/root/deploy_cmp/temp/

ymPath=http://*************:8014/ccs-trade-api/api/deliver/synLogisticsTruckInfo

etcPath = http://**************:8210/invoice

accessId = 0176990385412e34d163f3444f833fa2

insurance_publicKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCYn6k8SijLhZnK9b1p98oCo8oB8fPXGTfqhIjTD6+bD9KOY33icbHRFdtxN2dsjgfmQ6Q+H/XKlbRvOauwyqNz3QPK1+onLnrfNzJDVPwcp/861w1rN69wgVmr+irZ22LIEWE/dcJ1bjWe85p89gSZIRuAIITW+2Wk+Poz+Z2SwwIDAQAB
