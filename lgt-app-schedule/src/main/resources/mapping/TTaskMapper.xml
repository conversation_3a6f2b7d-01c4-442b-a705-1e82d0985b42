<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TTaskMapper">
  <resultMap id="BaseResultMap" type="com.lz.schedule.model.TTask">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="task_id" jdbcType="VARCHAR" property="taskId" />
    <result column="task_type" jdbcType="VARCHAR" property="taskType" />
    <result column="task_type_node" jdbcType="VARCHAR" property="taskTypeNode" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="source_tablename" jdbcType="VARCHAR" property="sourceTablename" />
    <result column="sourcekey_fieldname" jdbcType="VARCHAR" property="sourcekeyFieldname" />
    <result column="source_fieldname" jdbcType="VARCHAR" property="sourceFieldname" />
    <result column="source_fieldvalue" jdbcType="VARCHAR" property="sourceFieldvalue" />
    <result column="request_URL" jdbcType="VARCHAR" property="requestUrl" />
    <result column="request_parameter" jdbcType="VARCHAR" property="requestParameter" />
    <result column="request_times" jdbcType="INTEGER" property="requestTimes" />
    <result column="request_result" jdbcType="VARCHAR" property="requestResult" />
    <result column="error_message" jdbcType="VARCHAR" property="errorMessage" />
    <result column="request_date" jdbcType="TIMESTAMP" property="requestDate" />
    <result column="deal_time" jdbcType="TIMESTAMP" property="dealTime" />
    <result column="is_successed" jdbcType="BIT" property="isSuccessed" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="param1" jdbcType="VARCHAR" property="param1" />
    <result column="param2" jdbcType="VARCHAR" property="param2" />
    <result column="param3" jdbcType="VARCHAR" property="param3" />
    <result column="param4" jdbcType="VARCHAR" property="param4" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="enable" jdbcType="BIT" property="enable" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, task_type, task_type_node, business_type, source_tablename, sourcekey_fieldname, 
    source_fieldname, source_fieldvalue, request_URL, request_parameter, request_times, 
    request_result, error_message, request_date, deal_time, is_successed, remark, param1, 
    param2, param3, param4, create_user, create_time, update_user, update_time, `enable`
  </sql>
  <select id="selectByExample" parameterType="com.lz.schedule.example.TTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_task
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from t_task
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.lz.schedule.example.TTaskExample">
    delete from t_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.lz.schedule.model.TTask" useGeneratedKeys="true">
    insert into t_task (task_id, task_type, task_type_node, 
      business_type, source_tablename, sourcekey_fieldname, 
      source_fieldname, source_fieldvalue, request_URL, 
      request_parameter, request_times, request_result, 
      error_message, request_date, deal_time, 
      is_successed, remark, param1, 
      param2, param3, param4, 
      create_user, create_time, update_user, 
      update_time, `enable`)
    values (#{taskId,jdbcType=VARCHAR}, #{taskType,jdbcType=VARCHAR}, #{taskTypeNode,jdbcType=VARCHAR}, 
      #{businessType,jdbcType=VARCHAR}, #{sourceTablename,jdbcType=VARCHAR}, #{sourcekeyFieldname,jdbcType=VARCHAR}, 
      #{sourceFieldname,jdbcType=VARCHAR}, #{sourceFieldvalue,jdbcType=VARCHAR}, #{requestUrl,jdbcType=VARCHAR}, 
      #{requestParameter,jdbcType=VARCHAR}, #{requestTimes,jdbcType=INTEGER}, #{requestResult,jdbcType=VARCHAR}, 
      #{errorMessage,jdbcType=VARCHAR}, #{requestDate,jdbcType=TIMESTAMP}, #{dealTime,jdbcType=TIMESTAMP}, 
      #{isSuccessed,jdbcType=BIT}, #{remark,jdbcType=VARCHAR}, #{param1,jdbcType=VARCHAR}, 
      #{param2,jdbcType=VARCHAR}, #{param3,jdbcType=VARCHAR}, #{param4,jdbcType=VARCHAR}, 
      #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{enable,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.lz.schedule.model.TTask" useGeneratedKeys="true">
    insert into t_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="taskTypeNode != null">
        task_type_node,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="sourceTablename != null">
        source_tablename,
      </if>
      <if test="sourcekeyFieldname != null">
        sourcekey_fieldname,
      </if>
      <if test="sourceFieldname != null">
        source_fieldname,
      </if>
      <if test="sourceFieldvalue != null">
        source_fieldvalue,
      </if>
      <if test="requestUrl != null">
        request_URL,
      </if>
      <if test="requestParameter != null">
        request_parameter,
      </if>
      <if test="requestTimes != null">
        request_times,
      </if>
      <if test="requestResult != null">
        request_result,
      </if>
      <if test="errorMessage != null">
        error_message,
      </if>
      <if test="requestDate != null">
        request_date,
      </if>
      <if test="dealTime != null">
        deal_time,
      </if>
      <if test="isSuccessed != null">
        is_successed,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="param1 != null">
        param1,
      </if>
      <if test="param2 != null">
        param2,
      </if>
      <if test="param3 != null">
        param3,
      </if>
      <if test="param4 != null">
        param4,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="taskTypeNode != null">
        #{taskTypeNode,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="sourceTablename != null">
        #{sourceTablename,jdbcType=VARCHAR},
      </if>
      <if test="sourcekeyFieldname != null">
        #{sourcekeyFieldname,jdbcType=VARCHAR},
      </if>
      <if test="sourceFieldname != null">
        #{sourceFieldname,jdbcType=VARCHAR},
      </if>
      <if test="sourceFieldvalue != null">
        #{sourceFieldvalue,jdbcType=VARCHAR},
      </if>
      <if test="requestUrl != null">
        #{requestUrl,jdbcType=VARCHAR},
      </if>
      <if test="requestParameter != null">
        #{requestParameter,jdbcType=VARCHAR},
      </if>
      <if test="requestTimes != null">
        #{requestTimes,jdbcType=INTEGER},
      </if>
      <if test="requestResult != null">
        #{requestResult,jdbcType=VARCHAR},
      </if>
      <if test="errorMessage != null">
        #{errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="requestDate != null">
        #{requestDate,jdbcType=TIMESTAMP},
      </if>
      <if test="dealTime != null">
        #{dealTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isSuccessed != null">
        #{isSuccessed,jdbcType=BIT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="param1 != null">
        #{param1,jdbcType=VARCHAR},
      </if>
      <if test="param2 != null">
        #{param2,jdbcType=VARCHAR},
      </if>
      <if test="param3 != null">
        #{param3,jdbcType=VARCHAR},
      </if>
      <if test="param4 != null">
        #{param4,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.lz.schedule.example.TTaskExample" resultType="java.lang.Long">
    select count(*) from t_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=VARCHAR},
      </if>
      <if test="record.taskType != null">
        task_type = #{record.taskType,jdbcType=VARCHAR},
      </if>
      <if test="record.taskTypeNode != null">
        task_type_node = #{record.taskTypeNode,jdbcType=VARCHAR},
      </if>
      <if test="record.businessType != null">
        business_type = #{record.businessType,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceTablename != null">
        source_tablename = #{record.sourceTablename,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcekeyFieldname != null">
        sourcekey_fieldname = #{record.sourcekeyFieldname,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceFieldname != null">
        source_fieldname = #{record.sourceFieldname,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceFieldvalue != null">
        source_fieldvalue = #{record.sourceFieldvalue,jdbcType=VARCHAR},
      </if>
      <if test="record.requestUrl != null">
        request_URL = #{record.requestUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.requestParameter != null">
        request_parameter = #{record.requestParameter,jdbcType=VARCHAR},
      </if>
      <if test="record.requestTimes != null">
        request_times = #{record.requestTimes,jdbcType=INTEGER},
      </if>
      <if test="record.requestResult != null">
        request_result = #{record.requestResult,jdbcType=VARCHAR},
      </if>
      <if test="record.errorMessage != null">
        error_message = #{record.errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.requestDate != null">
        request_date = #{record.requestDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dealTime != null">
        deal_time = #{record.dealTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isSuccessed != null">
        is_successed = #{record.isSuccessed,jdbcType=BIT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.param1 != null">
        param1 = #{record.param1,jdbcType=VARCHAR},
      </if>
      <if test="record.param2 != null">
        param2 = #{record.param2,jdbcType=VARCHAR},
      </if>
      <if test="record.param3 != null">
        param3 = #{record.param3,jdbcType=VARCHAR},
      </if>
      <if test="record.param4 != null">
        param4 = #{record.param4,jdbcType=VARCHAR},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.enable != null">
        `enable` = #{record.enable,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_task
    set id = #{record.id,jdbcType=INTEGER},
      task_id = #{record.taskId,jdbcType=VARCHAR},
      task_type = #{record.taskType,jdbcType=VARCHAR},
      task_type_node = #{record.taskTypeNode,jdbcType=VARCHAR},
      business_type = #{record.businessType,jdbcType=VARCHAR},
      source_tablename = #{record.sourceTablename,jdbcType=VARCHAR},
      sourcekey_fieldname = #{record.sourcekeyFieldname,jdbcType=VARCHAR},
      source_fieldname = #{record.sourceFieldname,jdbcType=VARCHAR},
      source_fieldvalue = #{record.sourceFieldvalue,jdbcType=VARCHAR},
      request_URL = #{record.requestUrl,jdbcType=VARCHAR},
      request_parameter = #{record.requestParameter,jdbcType=VARCHAR},
      request_times = #{record.requestTimes,jdbcType=INTEGER},
      request_result = #{record.requestResult,jdbcType=VARCHAR},
      error_message = #{record.errorMessage,jdbcType=VARCHAR},
      request_date = #{record.requestDate,jdbcType=TIMESTAMP},
      deal_time = #{record.dealTime,jdbcType=TIMESTAMP},
      is_successed = #{record.isSuccessed,jdbcType=BIT},
      remark = #{record.remark,jdbcType=VARCHAR},
      param1 = #{record.param1,jdbcType=VARCHAR},
      param2 = #{record.param2,jdbcType=VARCHAR},
      param3 = #{record.param3,jdbcType=VARCHAR},
      param4 = #{record.param4,jdbcType=VARCHAR},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      `enable` = #{record.enable,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.lz.schedule.model.TTask">
    update t_task
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="taskTypeNode != null">
        task_type_node = #{taskTypeNode,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="sourceTablename != null">
        source_tablename = #{sourceTablename,jdbcType=VARCHAR},
      </if>
      <if test="sourcekeyFieldname != null">
        sourcekey_fieldname = #{sourcekeyFieldname,jdbcType=VARCHAR},
      </if>
      <if test="sourceFieldname != null">
        source_fieldname = #{sourceFieldname,jdbcType=VARCHAR},
      </if>
      <if test="sourceFieldvalue != null">
        source_fieldvalue = #{sourceFieldvalue,jdbcType=VARCHAR},
      </if>
      <if test="requestUrl != null">
        request_URL = #{requestUrl,jdbcType=VARCHAR},
      </if>
      <if test="requestParameter != null">
        request_parameter = #{requestParameter,jdbcType=VARCHAR},
      </if>
      <if test="requestTimes != null">
        request_times = #{requestTimes,jdbcType=INTEGER},
      </if>
      <if test="requestResult != null">
        request_result = #{requestResult,jdbcType=VARCHAR},
      </if>
      <if test="errorMessage != null">
        error_message = #{errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="requestDate != null">
        request_date = #{requestDate,jdbcType=TIMESTAMP},
      </if>
      <if test="dealTime != null">
        deal_time = #{dealTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isSuccessed != null">
        is_successed = #{isSuccessed,jdbcType=BIT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="param1 != null">
        param1 = #{param1,jdbcType=VARCHAR},
      </if>
      <if test="param2 != null">
        param2 = #{param2,jdbcType=VARCHAR},
      </if>
      <if test="param3 != null">
        param3 = #{param3,jdbcType=VARCHAR},
      </if>
      <if test="param4 != null">
        param4 = #{param4,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lz.schedule.model.TTask">
    update t_task
    set task_id = #{taskId,jdbcType=VARCHAR},
      task_type = #{taskType,jdbcType=VARCHAR},
      task_type_node = #{taskTypeNode,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=VARCHAR},
      source_tablename = #{sourceTablename,jdbcType=VARCHAR},
      sourcekey_fieldname = #{sourcekeyFieldname,jdbcType=VARCHAR},
      source_fieldname = #{sourceFieldname,jdbcType=VARCHAR},
      source_fieldvalue = #{sourceFieldvalue,jdbcType=VARCHAR},
      request_URL = #{requestUrl,jdbcType=VARCHAR},
      request_parameter = #{requestParameter,jdbcType=VARCHAR},
      request_times = #{requestTimes,jdbcType=INTEGER},
      request_result = #{requestResult,jdbcType=VARCHAR},
      error_message = #{errorMessage,jdbcType=VARCHAR},
      request_date = #{requestDate,jdbcType=TIMESTAMP},
      deal_time = #{dealTime,jdbcType=TIMESTAMP},
      is_successed = #{isSuccessed,jdbcType=BIT},
      remark = #{remark,jdbcType=VARCHAR},
      param1 = #{param1,jdbcType=VARCHAR},
      param2 = #{param2,jdbcType=VARCHAR},
      param3 = #{param3,jdbcType=VARCHAR},
      param4 = #{param4,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      `enable` = #{enable,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectTask" parameterType="com.lz.schedule.TTaskVO" resultType="com.lz.schedule.model.TTask">
    select id, task_id, task_type, task_type_node, business_type, source_tablename, sourcekey_fieldname, source_fieldname, source_fieldvalue, request_URL, request_parameter, request_times, request_result, error_message, request_date, deal_time, is_successed, remark, param1, param2, param3, param4, create_user, create_time, update_user, update_time, enable
    from t_task
    where enable = 0 and  business_type= #{businessType} and request_times <![CDATA[< ]]> 1 and task_type_node in
    <foreach collection="taskTypeNode" item="node" index="index" open="(" close=")" separator=",">
      #{node}
    </foreach>
    <if test="taskType != null">
      and task_type = #{taskType}
    </if>
    <if test="sourceFieldvalue != null">
      and source_fieldvalue = #{sourceFieldvalue}
    </if>
    limit 10
  </select>

  <select id="selectTxxyTask" parameterType="com.lz.schedule.TTaskVO" resultType="com.lz.schedule.model.TTask">
    select id, task_id, task_type, task_type_node, business_type, source_tablename, sourcekey_fieldname, source_fieldname, source_fieldvalue, request_URL, request_parameter, request_times, request_result, error_message, request_date, deal_time, is_successed, remark, param1, param2, param3, param4, create_user, create_time, update_user, update_time, enable
    from t_task
    where business_type= #{businessType} and request_times <![CDATA[< ]]> #{requestTimes} and task_type_node in
    <foreach collection="taskTypeNode" item="node" index="index" open="(" close=")" separator=",">
      #{node}
    </foreach>
    limit 1
  </select>

  <select id="selectTaskByCaptialTransferType" parameterType="com.lz.schedule.TTaskVO" resultType="com.lz.schedule.model.TTask">
    select ta.id, ta.task_id, ta.task_type, ta.task_type_node, ta.business_type, ta.source_tablename, ta.sourcekey_fieldname, ta.source_fieldname,
    ta.source_fieldvalue, ta.request_URL, ta.request_parameter, ta.request_times, ta.request_result, ta.error_message, ta.request_date,
    ta.deal_time, ta.is_successed, ta.remark, ta.param1, ta.param2, ta.param3, ta.param4, ta.create_user, ta.create_time, ta.update_user,
    ta.update_time, ta.enable
    from t_task ta
    left join t_order_info toi on ta.source_fieldvalue = toi.code
    left join t_order_cast_changes tocc on toi.code = tocc.order_code
    left join t_order_pay_info topi on toi.code = topi.order_code and topi.order_pay_status = 'P070'
    where ta.business_type= #{businessType} and ta.request_times <![CDATA[< ]]> 1
    and tocc.data_enable = 1 and topi.param1 = '1' and ta.task_type_node in
    <foreach collection="taskTypeNode" item="node" index="index" open="(" close=")" separator=",">
      #{node}
    </foreach>
    <if test="taskType != null">
      and ta.task_type = #{taskType}
    </if>
    <if test="capitalTransferType != null">
      and tocc.capital_transfer_type = #{capitalTransferType}
    </if>
    <if test="capitalTransferType == null">
      and tocc.capital_transfer_type is null
    </if>
    limit 5
  </select>


  <select id="selectTaskByCaptialTransferTypeList" parameterType="com.lz.schedule.TTaskVO" resultType="com.lz.schedule.model.TTask">
    select ta.id, ta.task_id, ta.task_type, ta.task_type_node, ta.business_type, ta.source_tablename, ta.sourcekey_fieldname, ta.source_fieldname,
    ta.source_fieldvalue, ta.request_URL, ta.request_parameter, ta.request_times, ta.request_result, ta.error_message, ta.request_date,
    ta.deal_time, ta.is_successed, ta.remark, ta.param1, ta.param2, ta.param3, ta.param4, ta.create_user, ta.create_time, ta.update_user,
    ta.update_time, ta.enable
    from t_task ta
    left join t_order_info toi on ta.source_fieldvalue = toi.code
    left join t_order_cast_changes tocc on toi.code = tocc.order_code
    left join t_order_pay_info topi on toi.code = topi.order_code and topi.order_pay_status = 'P070'
    where ta.business_type= #{businessType} and ta.request_times <![CDATA[< ]]> 1
    and tocc.data_enable = 1 and topi.param1 = '1' and ta.task_type_node in
    <foreach collection="taskTypeNode" item="node" index="index" open="(" close=")" separator=",">
      #{node}
    </foreach>
    <if test="taskType != null">
      and ta.task_type = #{taskType}
    </if>
    <if test="capiList != null">
      and tocc.capital_transfer_type in
      <foreach collection="capiList" item="ca" index="index" open="(" close=")" separator=",">
        #{ca}
      </foreach>
    </if>
    limit 5
  </select>

  <select id="selectTaskCallBackByCaptialTransferType" parameterType="com.lz.schedule.TTaskVO" resultType="com.lz.schedule.model.TTask">
    select ta.id, ta.task_id, ta.task_type, ta.task_type_node, ta.business_type, ta.source_tablename, ta.sourcekey_fieldname, ta.source_fieldname,
    ta.source_fieldvalue, ta.request_URL, ta.request_parameter, ta.request_times, ta.request_result, ta.error_message, ta.request_date,
    ta.deal_time, ta.is_successed, ta.remark, ta.param1, ta.param2, ta.param3, ta.param4, ta.create_user, ta.create_time, ta.update_user,
    ta.update_time, ta.enable
    from t_task ta
    left join t_order_pay_detail topd on ta.source_fieldvalue = topd.code
    left join t_order_cast_changes tocc on topd.order_cast_change_code = tocc.code
    left join t_order_pay_info topi on topd.order_pay_code = topi.code and topi.order_pay_status = 'P070'
    where ta.business_type= #{businessType} and ta.request_times <![CDATA[< ]]> 1
    and tocc.data_enable = 1 and topi.param1 = '1' and ta.task_type_node in
    <foreach collection="taskTypeNode" item="node" index="index" open="(" close=")" separator=",">
      #{node}
    </foreach>
    <if test="taskType != null">
      and ta.task_type = #{taskType}
    </if>
    <if test="capitalTransferType != null">
      and tocc.capital_transfer_type = #{capitalTransferType}
    </if>
    <if test="capitalTransferType == null">
      and tocc.capital_transfer_pattern is null
    </if>
    limit 5
  </select>

  <select id="selectTaskCallBackByCaptialTransferTypeList" parameterType="com.lz.schedule.TTaskVO" resultType="com.lz.schedule.model.TTask">
    select ta.id, ta.task_id, ta.task_type, ta.task_type_node, ta.business_type, ta.source_tablename, ta.sourcekey_fieldname, ta.source_fieldname,
    ta.source_fieldvalue, ta.request_URL, ta.request_parameter, ta.request_times, ta.request_result, ta.error_message, ta.request_date,
    ta.deal_time, ta.is_successed, ta.remark, ta.param1, ta.param2, ta.param3, ta.param4, ta.create_user, ta.create_time, ta.update_user,
    ta.update_time, ta.enable
    from t_task ta
    left join t_order_pay_detail topd on ta.source_fieldvalue = topd.code
    left join t_order_cast_changes tocc on topd.order_cast_change_code = tocc.code
    left join t_order_pay_info topi on topd.order_pay_code = topi.code and topi.order_pay_status = 'P070'
    where ta.business_type= #{businessType} and ta.request_times <![CDATA[< ]]> 1
    and tocc.data_enable = 1 and topi.param1 = '1' and ta.task_type_node in
    <foreach collection="taskTypeNode" item="node" index="index" open="(" close=")" separator=",">
      #{node}
    </foreach>
    <if test="taskType != null">
      and ta.task_type = #{taskType}
    </if>
    <if test="capiList != null">
      and tocc.capital_transfer_type in
      <foreach collection="capiList" item="ca" index="index" open="(" close=")" separator=",">
        #{ca}
      </foreach>
    </if>
    <if test="capiList == null">
      and tocc.capital_transfer_pattern is null
    </if>
    limit 5
  </select>



  <select id="selectTaskCallBackByTradeType" parameterType="com.lz.schedule.TTaskVO" resultType="com.lz.schedule.model.TTask">
    select ta.id, ta.task_id, ta.task_type, ta.task_type_node, ta.business_type, ta.source_tablename, ta.sourcekey_fieldname, ta.source_fieldname,
    ta.source_fieldvalue, ta.request_URL, ta.request_parameter, ta.request_times, ta.request_result, ta.error_message, ta.request_date,
    ta.deal_time, ta.is_successed, ta.remark, ta.param1, ta.param2, ta.param3, ta.param4, ta.create_user, ta.create_time, ta.update_user,
    ta.update_time, ta.enable
    from t_task ta
    left join t_order_pay_detail topd on ta.source_fieldvalue = topd.code
    left join t_order_pay_info topi on topd.order_pay_code = topi.code and topi.order_pay_status = 'P070'
    where ta.business_type= #{businessType} and ta.request_times <![CDATA[< ]]> 1
    and ta.task_type_node in
    <foreach collection="taskTypeNode" item="node" index="index" open="(" close=")" separator=",">
      #{node}
    </foreach>
    <if test="taskType != null">
      and ta.task_type = #{taskType}
    </if>
    <if test="operateState != null">
      and topd.operate_state = #{operateState}
    </if>

    limit 5
  </select>
  <select id="selectTaskByOldAndHZ" parameterType="com.lz.schedule.TTaskVO" resultType="com.lz.schedule.model.TTask">
    select ta.id, ta.task_id, ta.task_type, ta.task_type_node, ta.business_type, ta.source_tablename, ta.sourcekey_fieldname, ta.source_fieldname,
    ta.source_fieldvalue, ta.request_URL, ta.request_parameter, ta.request_times, ta.request_result, ta.error_message, ta.request_date,
    ta.deal_time, ta.is_successed, ta.remark, ta.param1, ta.param2, ta.param3, ta.param4, ta.create_user, ta.create_time, ta.update_user,
    ta.update_time, ta.enable
    from t_task ta
    left join t_order_info toi on ta.source_fieldvalue = toi.code
    left join t_order_pay_info topi on toi.code = topi.order_code and topi.order_pay_status = 'P070'
    where ta.business_type= #{businessType} and ta.request_times <![CDATA[< ]]> 1
    and (topi.param1 is null or length(topi.param1) =0) and ta.task_type_node in
    <foreach collection="taskTypeNode" item="node" index="index" open="(" close=")" separator=",">
      #{node}
    </foreach>
    <if test="taskType != null">
      and ta.task_type = #{taskType}
    </if>
    limit 5
  </select>

  <select id="selectTaskCallBackByOldAndHZ" parameterType="com.lz.schedule.TTaskVO" resultType="com.lz.schedule.model.TTask">
    select ta.id, ta.task_id, ta.task_type, ta.task_type_node, ta.business_type, ta.source_tablename, ta.sourcekey_fieldname, ta.source_fieldname,
    ta.source_fieldvalue, ta.request_URL, ta.request_parameter, ta.request_times, ta.request_result, ta.error_message, ta.request_date,
    ta.deal_time, ta.is_successed, ta.remark, ta.param1, ta.param2, ta.param3, ta.param4, ta.create_user, ta.create_time, ta.update_user,
    ta.update_time, ta.enable
    from t_task ta
    left join t_order_pay_detail topd on ta.source_fieldvalue = topd.code
    left join t_order_pay_info topi on topd.order_pay_code = topi.code and topi.order_pay_status = 'P070'
    left join t_advance_order_pay_tmp taopm on ta.source_fieldvalue = taopm.code
    where ta.business_type= #{businessType} and ta.request_times <![CDATA[< ]]> 1
    and (topi.param1 is null or length(topi.param1) =0) and taopm.id is null and ta.task_type_node in
    <foreach collection="taskTypeNode" item="node" index="index" open="(" close=")" separator=",">
      #{node}
    </foreach>
    <if test="taskType != null">
      and ta.task_type = #{taskType}
    </if>
    limit 5
  </select>

  <select id="selectTaskByYFK" parameterType="com.lz.schedule.TTaskVO" resultType="com.lz.schedule.model.TTask">
    select id, task_id, task_type, task_type_node, business_type, source_tablename, sourcekey_fieldname, source_fieldname, source_fieldvalue, request_URL, request_parameter, request_times, request_result, error_message, request_date, deal_time, is_successed, remark, param1, param2, param3, param4, create_user, create_time, update_user, update_time, enable
    from t_task
    where business_type= #{businessType} and request_times <![CDATA[< ]]> 1 and task_type_node in
    <foreach collection="taskTypeNode" item="node" index="index" open="(" close=")" separator=",">
      #{node}
    </foreach>
    <if test="taskType != null">
      and task_type = #{taskType}
    </if>
    limit 10
  </select>

  <select id="selectTaskCallBackByYFK" parameterType="com.lz.schedule.TTaskVO" resultType="com.lz.schedule.model.TTask">
    select ta.id, ta.task_id, ta.task_type, ta.task_type_node, ta.business_type, ta.source_tablename, ta.sourcekey_fieldname, ta.source_fieldname,
    ta.source_fieldvalue, ta.request_URL, ta.request_parameter, ta.request_times, ta.request_result, ta.error_message, ta.request_date,
    ta.deal_time, ta.is_successed, ta.remark, ta.param1, ta.param2, ta.param3, ta.param4, ta.create_user, ta.create_time, ta.update_user,
    ta.update_time, ta.enable
    from t_task ta
    left join t_advance_order_pay_tmp taopm on ta.source_fieldvalue = taopm.code
    where ta. business_type= #{businessType} and ta.request_times <![CDATA[< ]]> 1 and ta.task_type_node in
    <foreach collection="taskTypeNode" item="node" index="index" open="(" close=")" separator=",">
      #{node}
    </foreach>
    <if test="taskType != null">
      and ta.task_type = #{taskType}
    </if>
    limit 5
  </select>
  <select id="selectSendYmTask" parameterType="com.lz.schedule.TTaskVO" resultType="com.lz.schedule.model.TTask">
    select id, task_id, task_type, task_type_node, business_type, source_tablename, sourcekey_fieldname, source_fieldname, source_fieldvalue, request_URL, request_parameter, request_times, request_result, error_message, request_date, deal_time, is_successed, remark, param1, param2, param3, param4, create_user, create_time, update_user, update_time, enable
    from t_task
    where business_type= #{businessType} and request_times <![CDATA[< ]]> 1 and task_type_node in
    <foreach collection="taskTypeNode" item="node" index="index" open="(" close=")" separator=",">
      #{node}
    </foreach>
    <if test="taskType != null">
      and task_type = #{taskType}
    </if>
    limit 200
  </select>
</mapper>