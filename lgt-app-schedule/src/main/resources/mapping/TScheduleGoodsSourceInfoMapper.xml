<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TScheduleGoodsSourceInfoMapper">

  <select id="selectResourceHall" parameterType="com.lz.vo.ResourceHallQueryVO" resultType="com.lz.dto.ResourceHallDTO">
    SELECT
      tgsi.id, tgsi.line_goods_rel_id, tgsi.company_id, tcl.project_id, tgsi.line_id, tgsi.goods_ban_time
    FROM
      t_company_line tcl
        LEFT JOIN t_goods_source_info tgsi ON tcl.line_info_id = tgsi.line_id
    WHERE
      tgsi.STATUS = 'OPENSOURCE'
      <if test="null != alreadyBanTime">
        AND tgsi.goods_ban_time &lt; #{alreadyBanTime}
      </if>
    <if test="null != banTime">
      AND date_format(tgsi.goods_ban_time, '%Y-%m-%d') = #{banTime}
    </if>
  </select>

  <update id="updateByPrimaryKeySelective" parameterType="com.lz.model.TGoodsSourceInfo">
    update t_goods_source_info
    <set>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="pid != null">
        pid = #{pid,jdbcType=INTEGER},
      </if>
      <if test="lineGoodsRelId != null">
        line_goods_rel_id = #{lineGoodsRelId,jdbcType=INTEGER},
      </if>
      <if test="lineId != null">
        line_id = #{lineId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="deliverGoodsContacter != null">
        deliver_goods_contacter = #{deliverGoodsContacter,jdbcType=VARCHAR},
      </if>
      <if test="deliverGoodsContacterPhone != null">
        deliver_goods_contacter_phone = #{deliverGoodsContacterPhone,jdbcType=VARCHAR},
      </if>
      <if test="receiveGoodsContacter != null">
        receive_goods_contacter = #{receiveGoodsContacter,jdbcType=VARCHAR},
      </if>
      <if test="receiveGoodsContacterPhone != null">
        receive_goods_contacter_phone = #{receiveGoodsContacterPhone,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        goods_id = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="bigKindCode != null">
        big_kind_code = #{bigKindCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="estimateGoodsWeight != null">
        estimate_goods_weight = #{estimateGoodsWeight,jdbcType=DECIMAL},
      </if>
      <if test="currentCarriageUnitPrice != null">
        current_carriage_unit_price = #{currentCarriageUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="projectGoodsOrder != null">
        project_goods_order = #{projectGoodsOrder,jdbcType=INTEGER},
      </if>
      <if test="qrcodePhoto != null">
        qrcode_photo = #{qrcodePhoto,jdbcType=VARCHAR},
      </if>
      <if test="lineOtherUserEnable != null">
        line_other_user_enable = #{lineOtherUserEnable,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="statusOperateTime != null">
        status_operate_time = #{statusOperateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="scanCodeTimes != null">
        scan_code_times = #{scanCodeTimes,jdbcType=INTEGER},
      </if>
      <if test="goodsBanTime">
        goods_ban_time = #{goodsBanTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalSendReceipt != null">
        total_send_receipt = #{totalSendReceipt,jdbcType=INTEGER},
      </if>
      <if test="companyClients != null">
        company_clients = #{companyClients,jdbcType=VARCHAR},
      </if>
      <if test="companyCustomerName != null">
        company_customer_name = #{companyCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="goodsTimeliness != null">
        goods_timeliness = #{goodsTimeliness,jdbcType=VARCHAR},
      </if>
      <if test="vehicleTypeRequire != null">
        vehicle_type_require = #{vehicleTypeRequire,jdbcType=VARCHAR},
      </if>
      <if test="vehicleLengthRequire != null">
        vehicle_length_require = #{vehicleLengthRequire,jdbcType=INTEGER},
      </if>
      <if test="autoSelectTransporter != null">
        auto_select_transporter = #{autoSelectTransporter,jdbcType=TINYINT},
      </if>
      <if test="templateName != null">
        template_name = #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="templateCode != null">
        template_code = #{templateCode,jdbcType=VARCHAR},
      </if>
      <if test="templateExpireTime != null">
        template_expire_time = #{templateExpireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="templateStatus != null">
        template_status = #{templateStatus,jdbcType=VARCHAR},
      </if>
      <if test="createLocation != null">
        create_location = #{createLocation,jdbcType=VARCHAR},
      </if>
      <if test="publishCompanyId != null">
        publish_company_id = #{publishCompanyId,jdbcType=INTEGER},
      </if>
      <if test="publisherId != null">
        publisher_id = #{publisherId,jdbcType=INTEGER},
      </if>
      <if test="publishTime != null">
        publish_time = #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderGenType != null">
        order_gen_type = #{orderGenType,jdbcType=INTEGER},
      </if>
      <if test="signContract != null">
        sign_contract = #{signContract,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="param1 != null">
        param1 = #{param1,jdbcType=VARCHAR},
      </if>
      <if test="param2 != null">
        param2 = #{param2,jdbcType=VARCHAR},
      </if>
      <if test="param3 != null">
        param3 = #{param3,jdbcType=VARCHAR},
      </if>
      <if test="param4 != null">
        param4 = #{param4,jdbcType=VARCHAR},
      </if>
      <if test="operateMethod != null">
        operate_method = #{operateMethod,jdbcType=VARCHAR},
      </if>
      <if test="operatorIp != null">
        operator_ip = #{operatorIp,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>