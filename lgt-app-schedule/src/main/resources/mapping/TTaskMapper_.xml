<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TTaskMapper">
  <select id="selectTaskByModel" resultType="com.lz.schedule.model.TTask">
    select
    <include refid="Base_Column_List" />
    from t_task
    where
    enable= 0
      <if test="taskType != null">
        and task_type = #{taskType}
      </if>
      <if test="taskTypeNode != null">
        and task_type_node = #{taskTypeNode}
      </if>
      <if test="sourceFieldvalue != null">
        and source_fieldvalue = #{sourceFieldvalue}
      </if>

  </select>

  <select id="selectDigitalTask" resultType="com.lz.schedule.model.TTask">
    select ta.*
    from t_task ta
    left join t_digital_logistic tdl on ta.source_fieldvalue = tdl.order_code
    where tdl.state_node = #{stateNode}
    and business_type= #{businessType} and request_times <![CDATA[< ]]> 1
    and tdl.tx_code is not null
    and task_type_node in
    <foreach collection="taskTypeNode" item="node" index="index" open="(" close=")" separator=",">
      #{node}
    </foreach>
      and task_type = #{taskType}
    and ta.id is not null
    limit 5
  </select>

  <select id="selectTaskByLimit" resultType="com.lz.schedule.model.TTask">
    select
    <include refid="Base_Column_List" />
    from t_task
    where
    enable= 0
    <if test="null != requestTimes">
     and request_times <![CDATA[< ]]> #{requestTimes}
    </if>
    <if test="null != taskType">
      and task_type = #{taskType}
    </if>
    <if test="null != taskTypeList and taskTypeList.size() > 0">
      and task_type in
      <foreach collection="taskTypeList" item="taskType" index="index" open="(" close=")" separator=",">
        #{taskType}
      </foreach>
    </if>
    <if test="null != businessType">
      and business_type = #{businessType}
    </if>
    and task_type_node in
    <if test="null != taskTypeNode and taskTypeNode.size > 0">
      <foreach collection="taskTypeNode" item="node" index="index" open="(" close=")" separator=",">
        #{node}
      </foreach>
    </if>

    <if test="null != sourceFieldvalue">
      and source_fieldvalue = #{sourceFieldvalue}
    </if>
    <if test="null != limit and limit > 0">
      limit ${limit}
    </if>
  </select>
    <select id="selectTaskByType" resultType="com.lz.schedule.model.TTask">
      select
      <include refid="Base_Column_List" />
      from t_task
      where
      enable= 0
      and task_type = #{taskType}
      and request_times <![CDATA[< ]]> 6
      limit 10
    </select>

</mapper>