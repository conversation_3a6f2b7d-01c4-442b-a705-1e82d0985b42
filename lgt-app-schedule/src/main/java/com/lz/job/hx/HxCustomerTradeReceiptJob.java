package com.lz.job.hx;

import cn.hutool.json.JSONUtil;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.util.ResultUtil;
import com.lz.schedule.TTaskVO;
import com.lz.schedule.model.TTask;
import com.lz.service.HxTaskService;
import com.lz.service.JdTaskService;
import com.lz.service.TTaskErrorService;
import com.lz.service.TTaskService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 京东电子回单申请
 */
@Slf4j
@Component
@DisallowConcurrentExecution
public class HxCustomerTradeReceiptJob implements Job, Serializable {

    private static final long serialVersionUID = 1456143701638816569L;

    @Autowired
    private TTaskService tTaskService;
    @Autowired
    private TTaskErrorService tTaskErrorService;
    @Autowired
    private HxTaskService hxTaskService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        TTaskVO taskVO = new TTaskVO();
        taskVO.setBusinessType("ZF");
        taskVO.setTaskType(DictEnum.HXRECEIPT.code);
        taskVO.setRequestTimes(6);
        List<String> ls = new ArrayList<>();
        ls.add("FQ");
        taskVO.setTaskTypeNode(ls);
        ResultUtil resultUtil = tTaskService.selectTask(taskVO);
        ArrayList<TTask> tasks = (ArrayList<TTask>) resultUtil.getData();
        for (TTask task: tasks){
            try {
                log.info("开始获取华夏电子回单, {}", JSONUtil.toJsonStr(task));
                hxTaskService.applyReceipt(task);
            } catch (Exception e) {
                log.error("获取华夏电子回单异常：task ID={}, 错误信息：{}", task.getId(), e);
                tTaskService.updateTask(task, false, e.getMessage());
                task.setErrorMessage(e.getMessage());
                tTaskErrorService.insertErrorTask(task, DictEnum.HXRECEIPT.code, "2005");
            }
        }
    }

}
