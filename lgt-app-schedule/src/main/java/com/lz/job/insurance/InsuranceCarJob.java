package com.lz.job.insurance;


import com.alibaba.fastjson.JSON;
import com.lz.api.*;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.util.RSAUtils;
import com.lz.common.util.StringUtils;
import com.lz.dao.TTaskMapper;
import com.lz.enums.InsuredGoodsTypesEnum;
import com.lz.model.TCarrierInfo;
import com.lz.model.TEndCarInfo;
import com.lz.model.TEndUserInfo;
import com.lz.model.TOrderInsurance;
import com.lz.request.RequestParameter;
import com.lz.request.RequestParameterCar;
import com.lz.request.RequestParameterOrder;
import com.lz.response.ResponseParamenter;
import com.lz.response.ReturnParamenter;
import com.lz.schedule.model.TTask;
import com.lz.service.TTaskErrorService;
import com.lz.service.TTaskHistoryService;
import com.lz.service.TTaskService;
import com.lz.vo.TOrderInfoVO;
import com.lz.vo.TOrderInsuranceVO;
import lombok.extern.log4j.Log4j;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.redisson.executor.TasksService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;


/**
 * 车辆投保job
 * <AUTHOR>
 */
@Log4j
@Component
@DisallowConcurrentExecution
public class InsuranceCarJob implements Job, Serializable {
    private static final long serialVersionUID = -2122165429128891402L;

    @Value("${insurance_publicKey}")
    private String publicKey;
    @Resource
    private TTaskService taskService;

    @Resource
    private CcadministrativeDivisionsAPI ccadministrativeDivisionsAPI;

    @Autowired
    private TEndSUserInfoAPI tEndSUserInfoAPI;

    @Autowired
    private TEndCarInfoAPI tEndCarInfoAPI;

    @Resource
    private TOrderInsuranceAPI orderInsuranceAPI;

    @Resource
    private TTaskErrorService taskErrorService;

    @Resource
    private TTaskHistoryService taskHistoryService;

    @Resource
    private TTaskMapper taskMapper;

    @Resource
    private CarrierService carrierService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("=========================================投保开始=========================================");
        TTask taskCar = new TTask();
        taskCar.setTaskType(DictEnum.INSURANCE.code);
        List<TTask> carList = taskService.selectTaskByType(taskCar);
        for (TTask tTask : carList) {
            if(null != tTask && tTask.getRequestTimes() <= 5){
                String paramenter = tTask.getRequestParameter();
                log.info("获取到的投保参数："+paramenter);
                //转换成对象
                TOrderInfoVO orderInfoVO = JSON.parseObject(paramenter, TOrderInfoVO.class);
                //组装运单参数
                RequestParameterOrder requestParameterOrder = requestOrder(orderInfoVO);
                log.info("运单参数："+JSON.toJSONString(requestParameterOrder));
                if(null != requestParameterOrder){
                    //使用加密工具
                    String orderRSA = RSAUtils.publicEncrypt(JSON.toJSONString(requestParameterOrder), publicKey);//RSA加密
                    //调用投保接口投保车辆
                    ReturnParamenter returnOrder = lgtInterface("lgt_orderinfo", orderRSA);
                    //如果请求成功，则修改task表的任务状态，修改投保表的投保信息
                    if("S".equals(returnOrder.getReturnCode())){
                        // 转历史
                        taskHistoryService.insertTaskHistory(tTask);
                        // 删除任务
                        taskMapper.deleteByPrimaryKey(tTask.getId());
                        TOrderInsurance orderInsurance = orderInsuranceAPI.selectByOrderBusinessCode(tTask.getSourceFieldvalue());
                        int i = orderInsurance.getInsuredAmount().compareTo(returnOrder.getPremium());
                        if(i != 0 && 1 == orderInsurance.getInsure()){
                            orderInsurance.setInsuredAmount(returnOrder.getPremium());
                        }
                        orderInsurance.setInsuranceStatus(returnOrder.getReturnCode());
                        orderInsurance.setInsuranceMessage(returnOrder.getReturnMessage());
                        orderInsurance.setOrderBusinessCode(tTask.getSourceFieldvalue());
                        orderInsurance.setUpdateTime(new Date());
                        orderInsuranceAPI.updateByPrimaryKeySelective(orderInsurance);
                        log.info("=========================================运单："+tTask.getSourceFieldvalue()+"投保成功，结束=========================================");
                    }else{
                        taskService.updateTask(tTask, false,"运单投保失败"+returnOrder.getReturnMessage());
                        tTask.setErrorMessage(returnOrder.getReturnMessage());
                        taskErrorService.insertErrorTask(tTask, DictEnum.INSURANCE.code, returnOrder.getReturnCode());
                        log.info("=========================================投保结束，运单投保失败=========================================");
                    }
                }else{
                    taskService.updateTask(tTask, false,"运单信息组装失败");
                    tTask.setErrorMessage("运单信息组装失败");
                    taskErrorService.insertErrorTask(tTask, DictEnum.INSURANCE.code, "F");
                    log.info("=========================================投保结束，运单信息组装失败=========================================");
                }
            }
        }
    }

    /**
     * 车辆信息：
     * @param orderInfoVO
     * @return
     */
    /*private RequestParameterCar requestCar(TOrderInfoVO orderInfoVO) {
        try{
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd"); // 日期格式
            RequestParameterCar requestParameterCar = new RequestParameterCar();
            TEndCarInfo tEndCarInfo = tEndCarInfoAPI.selectByPrimaryKey(orderInfoVO.getVehicleId());
            requestParameterCar.setLicenseno(tEndCarInfo.getVehicleNumber());//车牌号
            requestParameterCar.setPaymentAmount(orderInfoVO.getInsuredAmount().setScale(2, RoundingMode.HALF_UP));//保险金额
            requestParameterCar.setStartDate(dateFormat.format(new Date()));
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date()); // 设置日期
            calendar.add(Calendar.DAY_OF_YEAR, 120); // 给日期加上120天
            requestParameterCar.setEndDate(dateFormat.format(calendar.getTime()));
            //判断是否是挂车
            if(null != orderInfoVO.getCarriagePriceUnit() &&
                    DictEnum.CARRIAGE_PRICE_UNIT_BOX.code.equals(orderInfoVO.getCarriagePriceUnit())){
                requestParameterCar.setBindingType("2");
            }else{
                requestParameterCar.setBindingType("1");
            }
            return requestParameterCar;
        }catch (Exception e){
            log.info("异常信息：", e);
        }
        return null;
    }*/

    /**
     * 运单信息：
     * @param orderInfoVO
     * @return
     */
    private RequestParameterOrder requestOrder(TOrderInfoVO orderInfoVO) {
        try{
            SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); // 日期格式
            SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd"); // 日期格式
            TEndCarInfo tEndCarInfo = tEndCarInfoAPI.selectByPrimaryKey(orderInfoVO.getVehicleId());
            TEndUserInfo tEndUserInfo = tEndSUserInfoAPI.selectByPrimaryKey(orderInfoVO.getEndDriverId());
            RequestParameterOrder requestParameterOrder = new RequestParameterOrder();
            requestParameterOrder.setLicenseno(tEndCarInfo.getVehicleNumber());//车牌号
            requestParameterOrder.setOrderno(orderInfoVO.getOrderBusinessCode());//运单号
            requestParameterOrder.setStartSiteName(orderInfoVO.getFromName()+"@"+orderInfoVO.getFromCoordinates());//起点地点名称@经度,维度
            requestParameterOrder.setCarOwnerName(tEndUserInfo.getRealName());//司机姓名
            requestParameterOrder.setIdentifyNumber(tEndUserInfo.getIdcard());//司机身份证号
            requestParameterOrder.setIdentifyStartDate(dateFormat2.format(tEndUserInfo.getIdcardValidUntil()));//证件有效开始日期
            requestParameterOrder.setIdentifyEndDate(dateFormat2.format(tEndUserInfo.getIdcardValidBeginning()));//证件有效结束日期
            //省市县（区）默认值
            requestParameterOrder.setProvinceHome("河北省");
            requestParameterOrder.setCityHome("石家庄市");
            requestParameterOrder.setDistrictHome("裕华区");
            //获取司机身份证号码前6位
            String idSix = tEndUserInfo.getIdcard().substring(0, 6);
            String address =
                    ccadministrativeDivisionsAPI.selectByCode(Long.valueOf(idSix));
            if(null != address){
                String[] split = address.split("/");
                if(split.length == 3){
                    requestParameterOrder.setProvinceHome(split[0]);
                    requestParameterOrder.setCityHome(split[1]);
                    requestParameterOrder.setDistrictHome(split[2]);
                }
            }
            requestParameterOrder.setMobile(tEndUserInfo.getPhone());//手机号
            requestParameterOrder.setValidDate(dateFormat2.format(tEndCarInfo.getCardrivingLicencesValidUntil()));//行驶证有效期至
            requestParameterOrder.setStartSiteTime(dateFormat1.format(new Date()));//发车时间
            requestParameterOrder.setEndSiteName(orderInfoVO.getEndName()+"@"+orderInfoVO.getEndCoordinates());//终点地点名称@经度,维度
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date()); // 设置日期
            calendar.add(Calendar.DAY_OF_YEAR, 120); // 给日期加上120天
            requestParameterOrder.setEndSiteTime(dateFormat1.format(calendar.getTime()));//到达地点
            //货物类型 1、煤炭，2、钢材，3、铝材
            if(null != orderInfoVO.getInsuredGoodsType()){
                if(orderInfoVO.getInsuredGoodsType().equals(InsuredGoodsTypesEnum.COAL.getKey())){
                    requestParameterOrder.setCargoType("1");
                }else if(orderInfoVO.getInsuredGoodsType().equals(InsuredGoodsTypesEnum.STEEL.getKey())){
                    requestParameterOrder.setCargoType("2");
                }else{
                    requestParameterOrder.setCargoType("3");
                }
            }else{
                requestParameterOrder.setCargoType("1");
            }
            requestParameterOrder.setNumber(1);//件数  没有件数情况，默认为1
            requestParameterOrder.setWeight(orderInfoVO.getDeliverWeightNotesWeight());//重量（按吨计价）装货重量
            if(null != orderInfoVO.getCarrierId()){
                TCarrierInfo tCarrierInfo = carrierService.selectCarrierById(String.valueOf(orderInfoVO.getCarrierId()));
                requestParameterOrder.setUnifiedSocialCreditCode(tCarrierInfo.getBusinessLicenseNo());
            }
            //货值单价*装货重量,货物价值（保留两位小数）
            requestParameterOrder.setCargoValue(orderInfoVO.getGoodsUnitPrice().multiply(
                    BigDecimal.valueOf(orderInfoVO.getDeliverWeightNotesWeight())).setScale(2, RoundingMode.HALF_UP));
            return requestParameterOrder;
        }catch (Exception e){
            log.info("异常信息：",e);
        }
        return null;
    }

    private ReturnParamenter lgtInterface(String type, String data){
        ReturnParamenter returnParamenter = new ReturnParamenter();
        try {
            StringBuffer response = new StringBuffer();
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            //调用投保接口
            URL url = new URL("https://www.hebpicc.com/public/i/platForm/lgtInterface");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setDoOutput(true);
            // 设置请求参数
            RequestParameter requestParameter = new RequestParameter();
            requestParameter.setType(type);
            requestParameter.setTime(format.format(new Date()));
            requestParameter.setData(data);
            String urlParameters = JSON.toJSONString(requestParameter);
            log.info("请求报文："+urlParameters);
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = urlParameters.getBytes("utf-8");
                os.write(input, 0, input.length);
            }
            // 获取响应状态码
            log.info("获取响应状态码: " + connection.getResponseCode());
            // 读取响应内容
            if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                // 处理响应流
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                log.info("获取到的响应参数: " + response);
                ResponseParamenter responseParamenter = JSON.parseObject(String.valueOf(response), ResponseParamenter.class);
                if(null != responseParamenter){
                    if(null != responseParamenter.getCode() && "0".equals(responseParamenter.getCode())){
                        String s1 = RSAUtils.publicDecrypt(responseParamenter.getData(), publicKey);
                        log.info("解密后："+s1);
                        returnParamenter = JSON.parseObject(s1, ReturnParamenter.class);

                    }
                }
            }else{
                return returnParamenter;
            }
            return returnParamenter;
        }catch (Exception e){
            log.info("异常信息：",e);
        }
        return returnParamenter;
    }

}
