package com.lz.dao;

import com.lz.schedule.TTaskVO;
import com.lz.schedule.example.TTaskExample;
import com.lz.schedule.model.TTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface TTaskMapper {
    long countByExample(TTaskExample example);

    int deleteByExample(TTaskExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TTask record);

    int insertSelective(TTask record);

    List<TTask> selectByExample(TTaskExample example);

    TTask selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TTask record, @Param("example") TTaskExample example);

    int updateByExample(@Param("record") TTask record, @Param("example") TTaskExample example);

    int updateByPrimaryKeySelective(TTask record);

    int updateByPrimaryKey(TTask record);

    List<TTask> selectTask(TTaskVO record);

    List<TTask> selectTxxyTask(TTaskVO record);

    List<TTask> selectTaskByModel(TTask task);

    List<TTask> selectDigitalTask(@Param("taskTypeNode") List<String> taskTypeNode, @Param("taskType") String taskType,
                                  @Param("businessType") String businessType, @Param("stateNode") String stateNode);

    /**
     * <AUTHOR>
     * @Description 分润财务入账
     * @Date 2020/7/20 上午9:37
     * @Param
     * @return
    **/
    List<TTask> selectTaskByCaptialTransferType(TTaskVO record);

    List<TTask> selectTaskByCaptialTransferTypeList(TTaskVO record);

    /**
     * <AUTHOR>
     * @Description 分润财务入账回调
     * @Date 2020/7/20 上午9:37
     * @Param
     * @return
    **/
    List<TTask> selectTaskCallBackByCaptialTransferType(TTaskVO record);

    List<TTask> selectTaskCallBackByTradeType(TTaskVO record);

    List<TTask> selectTaskCallBackByCaptialTransferTypeList(TTaskVO record);
    /**
     * <AUTHOR>
     * @Description 旧支付、新支付划账模式财务入账
     * @Date 2020/7/20 上午9:37
     * @Param
     * @return
    **/
    List<TTask> selectTaskByOldAndHZ(TTaskVO record);

    /**
     * <AUTHOR>
     * @Description 旧支付、新支付划账模式入账回调
     * @Date 2020/7/20 上午9:38
     * @Param
     * @return
    **/
    List<TTask> selectTaskCallBackByOldAndHZ(TTaskVO record);

    /**
     * <AUTHOR>
     * @Description 预付款，财务入账
     * @Date 2020/7/20 下午12:17
     * @Param
     * @return
    **/
    List<TTask> selectTaskByYFK(TTaskVO record);

    /**
     * <AUTHOR>
     * @Description 预付款，入账回调
     * @Date 2020/7/20 下午12:17
     * @Param
     * @return
    **/
    List<TTask> selectTaskCallBackByYFK(TTaskVO record);

    List<TTask>  selectSendYmTask(TTaskVO record);

    List<TTask> selectTaskByLimit(TTaskVO record);

    List<TTask> selectTaskByType(TTask task);
}