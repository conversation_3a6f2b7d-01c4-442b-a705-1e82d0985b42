package com.lz.service.impl;

import cn.com.antcloud.api.product.AntCloudProdResponse;
import cn.com.antcloud.api.shuziwuliu.v1_0_0.model.LogisticLocation;
import cn.com.antcloud.api.shuziwuliu.v1_0_0.request.*;
import cn.com.antcloud.api.shuziwuliu.v1_0_0.response.*;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.codingapi.txlcn.tc.annotation.DTXPropagation;
import com.codingapi.txlcn.tc.annotation.LcnTransaction;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lowagie.text.pdf.PdfReader;
import com.lz.api.*;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.DigitlFlowEnum;
import com.lz.common.dbenum.DigitlFlowExceptionEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.datareport.dataexchengezj.OrderEnd;
import com.lz.common.model.datareport.dataexchengezj.ResultData;
import com.lz.common.model.datareport.khydr.Kenum.KHYEnum;
import com.lz.common.model.datareport.khydr.orderreport.khyTruckTrackDTO;
import com.lz.common.model.datareport.sddrFcfgs.Fcenum.FCFGSEnum;
import com.lz.common.model.exchangeyimei.YimeiResult;
import com.lz.common.model.jdPayment.util.FileBase64Util;
import com.lz.common.util.*;
import com.lz.dao.TTaskErrorMapper;
import com.lz.dao.TTaskHistoryMapper;
import com.lz.dao.TTaskMapper;
import com.lz.dao.TTaskVehicleTrajectorMapper;
import com.lz.dto.CompanySourceDTO;
import com.lz.dto.DigitlFlowMemberDTO;
import com.lz.dto.JudgeEndUserDTO;
import com.lz.model.*;
import com.lz.model.contract.req.ContractApplyCertReq;
import com.lz.model.contract.resp.ContractApplyCertResp;
import com.lz.model.contract.resp.ContractDownloadContResp;
import com.lz.schedule.TTaskVO;
import com.lz.schedule.example.TTaskExample;
import com.lz.schedule.model.TTask;
import com.lz.schedule.model.TTaskError;
import com.lz.schedule.model.TTaskHistory;
import com.lz.schedule.vo.RespVo;
import com.lz.schedule.vo.TTaskSearchVO;
import com.lz.service.TTaskService;
import com.lz.tpu.api.CashAPI;
import com.lz.tpu.api.PaymentAPI;
import com.lz.tpu.api.UserRegisterAPI;
import com.lz.tpu.web.reqeuest.CashRequest;
import com.lz.tpu.web.reqeuest.InstantRequest;
import com.lz.tpu.web.reqeuest.PaymentRequest;
import com.lz.tpu.web.reqeuest.UserRequest;
import com.lz.util.InstantJobUtil;
import com.lz.vo.CreateSourceOrderPayRequestVO;
import com.lz.vo.DataToEtcVO;
import com.lz.vo.TDigitalLogisticVO;
import com.lz.vo.WaybillOrderRequestVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * author dingweibo
 * Task 任务表
 */
@Slf4j
@Service
@EnableConfigurationProperties
@RefreshScope
public class TTaskServiceImpl implements TTaskService {

    @Resource
    private TTaskMapper tTaskMapper;
    @Resource
    private PaymentAPI paymentAPI;
    @Autowired
    private CarrierService carrierService;
    @Autowired
    private CashAPI cashAPI;
    @Autowired
    private TOrderInfoAPI tOrderInfoAPI;
    @Autowired
    private TEtcInvoiceProcessChangeAPI tEtcInvoiceProcessChangeAPI;
    @Resource
    private TTaskHistoryMapper tTaskHistoryMapper;
    @Resource
    private TTaskErrorMapper tTaskErrorMapper;
    @Autowired
    private OrderTaskAPI orderTaskAPI;
    @Value("${ymPath}")
    private String ymPath;
    @Value("${accessId}")
    private String accessId;
    /**
     * 易云章接口
     */
    @Resource
    private ContractAPI contractAPI;

    @Resource
    private TOrderContractAPI tOrderContractAPI;

    @Resource
    private UserRegisterAPI userRegisterAPI;

    @Autowired
    private TEndSUserInfoAPI endSUserInfoAPI;

    @Resource
    private TCarrierEnduserCompanyRelAPI tCarrierEnduserCompanyRelAPI;

    @Resource
    private FastdfsAPI fastdfsAPI;

    @Autowired
    private OssAPI ossAPI;

    @Resource
    private TOrderAbnormalAPI TOrderAbnormalAPI;

    @Value("${htPath}")
    private String htPath;

    @Value("${etcPath}")
    private String etcPath;

    @Autowired
    private InstantJobUtil instantJobUtil;

    @Autowired
    private CommonMemberUtilAPI commonMemberUtilAPI;

    @Autowired
    private GoodsSourceAPI goodsSourceAPI;

    @Resource
    private TTaskVehicleTrajectorMapper vehicleTrajectorMapper;

    @Autowired
    private DigitlFlowOrderAPI digitlFlowOrderAPI;

    @Autowired
    private CompanyService companyService;

    /**
     * task 任务 签署合同  失败后 创建合同异常数据  加企业签合同
     *
     * @return
     * <AUTHOR>
     */
    @Override
    public ResultUtil task() {

        ResultUtil resu = new ResultUtil();
        TTaskExample example = new TTaskExample();
        TTaskExample.Criteria c = example.createCriteria();
        c.andTaskTypeEqualTo("YSHT");
        c.andBusinessTypeEqualTo("HT");
        c.andRequestTimesLessThanOrEqualTo(5);
        List<TTask> list = tTaskMapper.selectByExample(example);
        for (TTask task : list) {
            resu = ta(task);
        }
        return resu;
    }

    /**
     * @author: dingweibo
     * @Date: 2019/6/18 9:07
     * @Description: 签署合同
     */
    public ResultUtil ta(TTask task) {
        ResultUtil resu = new ResultUtil();
        try {
            log.info(htPath + "---------------------------------------------");
            String param = task.getRequestParameter();
            JSONObject jsonObject = JSONObject.parseObject(param);
            //判断合同模板编号为空修改状态
            String contractNum = jsonObject.get("orderCode").toString();
            //手机号
            String mobilePhone = jsonObject.get("mobilePhone").toString();
            //运单编号
            String orderBusinessCode = jsonObject.get("orderBusinessCode").toString();

            String phone = jsonObject.get("Aphone").toString();
            Integer endUserId = Integer.parseInt(jsonObject.get("AId").toString());
            // 判断用户是否已申请合同电子签名，没有就会去申请
            JudgeEndUserDTO judgeEndUserDTO = endSUserInfoAPI.judgeEndUserIfOnlineSign(phone,endUserId);
            boolean onlineSign = false;
            if(judgeEndUserDTO!=null&&!"".equals(judgeEndUserDTO)){
                onlineSign = judgeEndUserDTO.isIfOnlineSign();
            }
            if (!onlineSign) {
                ContractApplyCertReq data = new ContractApplyCertReq();
                //类型 1：个人 、2：企业
                data.setType("1");
                // 证件类型 0：身份证 1：军官证 ,2：护照、 3：驾驶证、4：工商登记证、5：税务登记证、6：组织机构代码、7：其他证件，8：统一社会信用代码
                data.setCardType("0");
                // 证件号码
                data.setIdCardNum(judgeEndUserDTO.getIdcard());
                //企业或者个人真实名称
                data.setName(judgeEndUserDTO.getRealName());
                //企业或者个人联系手机号
                data.setMobilePhone(jsonObject.get("Aphone").toString());
                ExecutorService executorService = Executors.newSingleThreadExecutor();
                Future<ContractApplyCertResp> future = executorService.submit(() -> {
                    ContractApplyCertResp cert = contractAPI.applyCert(data);
                    return cert;
                });
                TEndUserInfo tEndUserInfo = new TEndUserInfo();
                try {
                    ContractApplyCertResp cert = future.get();
                    if (cert.getCode().equals("0")) {
                        tEndUserInfo.setIssuer(cert.getIssuer());
                        tEndUserInfo.setSerialNumber(cert.getSerialNumber());
                        tEndUserInfo.setBeginTime(DateUtils.parseDate(cert.getCertNotBefore()));
                        tEndUserInfo.setEndTime(DateUtils.parseDate(cert.getCertNotAfter()));
                    }
                }catch (InterruptedException e) {
                    e.printStackTrace();
                } catch (ExecutionException e) {
                    e.printStackTrace();
                }finally {
                    executorService.shutdown();
                }
                tEndUserInfo.setId(judgeEndUserDTO.getId());
                endSUserInfoAPI.updateEndUserIfOnlineSign(tEndUserInfo);
            }

            //运单业务id
            String orderCode = jsonObject.get("orderCode").toString();
            if (contractNum == null || "".equals(contractNum)) {
                task.setRequestTimes(6);
                task.setRequestResult("此合同无合同编号！");
                tTaskMapper.updateByPrimaryKeySelective(task);
                resu.setMsg("此合同无合同编号");
            } else {
                //调用易云章 根据合同模板编号生成合同
                //组织参数
                Map<String, Object> map = new HashMap<>();
                map.put("QName", jsonObject.get("QName"));//承运方名称
                map.put("CCDBusinessNo", jsonObject.get("CCDBusinessNo"));//营业执照号
                map.put("AName", jsonObject.get("AName"));//司机名称
                map.put("Aphone", jsonObject.get("Aphone"));//司机手机号
                map.put("ACarNo", jsonObject.get("ACarNo"));//车牌号
                map.put("ACardNo", jsonObject.get("ACardNo"));//身份证号
                map.put("CFNo", orderBusinessCode);//运单业务id
                map.put("SGType", jsonObject.get("SGType"));//货物类型
                map.put("SGFactCount", jsonObject.get("SGFactCount"));//数量
                map.put("CDName", jsonObject.get("CDName"));//发货方
                map.put("SGLoadingAddress", jsonObject.get("SGLoadingAddress"));//装货地点
                map.put("SGUnLoadingName", jsonObject.get("SGUnLoadingName"));//装货联系人
                map.put("SGUnLoadingPhone", jsonObject.get("SGUnLoadingPhone"));//装货联系电话
                map.put("SGUnLoadingAddress", jsonObject.get("SGUnLoadingAddress"));//收货地址
                map.put("SGLoadingName", jsonObject.get("SGLoadingName"));//收货联系人
                map.put("SGLoadingPhone", jsonObject.get("SGLoadingPhone"));//收货联系人电话
                map.put("SGLimitTime", jsonObject.get("SGLimitTime") + "天");//要求到货期限
                map.put("SGFreightTotal", jsonObject.get("SGFreightTotal"));//总运费
                ContractApplyCertResp resultUtil = contractAPI.createContractByTemplate(map, contractNum, DictEnum.CYHT.code+DictEnum.BOTHSIDES.code);
                //合同创建成功
                if ("0".equals(resultUtil.getCode())) {
                    resu = orderContractSuccess(resultUtil,jsonObject,contractNum,orderCode,mobilePhone,task);
                } else {
                    //合同已创建未下载
                    resu = orderContractSuccessDownl(resultUtil.getCode(),orderCode,contractNum,mobilePhone,task,resultUtil.getMessage(),jsonObject);
                }
            }
        } catch (Exception e) {
            task.setRequestTimes(task.getRequestTimes()+1);
            task.setErrorMessage(e.getMessage());
            tTaskMapper.updateByPrimaryKeySelective(task);
            log.error("签署合同任务执行失败！ taskId为：" + task.getId(), e);
        }


        return resu;
    }

    //合同创建成功
    @LcnTransaction
    @Transactional
    public ResultUtil orderContractSuccess(ContractApplyCertResp resultUtil,JSONObject jsonObject,String contractNum,String orderCode,String mobilePhone,TTask task){
        try{
            ResultUtil resu = new ResultUtil();
            //易云章返回合同编号
            String contractNum2 = resultUtil.getContractNum();
            //签署合同企业、司机参数对象
            RespVo qsht = qshtObject(jsonObject);
            Map<Object,Object> mapo = qsht.getMapo();//企业签署合同参数
            Map<Object,Object> mapp = qsht.getMapp();//司机签署合同参数

            log.info("签署合同开始！");
            //组织参数
            ContractApplyCertResp ru22 = contractAPI.autoSign(mapo, contractNum, 1);
            String code22 = ru22.getCode();
            String message22 = ru22.getMessage();
            if("0".equals(code22)){//企业签署合同
                task.setParam1(null);
                ContractApplyCertResp ru = contractAPI.autoSign(mapp, contractNum, 0);
                String code2 = ru.getCode();
                String message2 = ru.getMessage();
                if("0".equals(code2)){//司机签署合同
                    task.setParam2(null);
                    log.info("签署合同成功！");
                    //运单号
                    TOrderContract tOrderContract2 =  tOrderContractAPI.selectByOrderBusinessCode(orderCode);
                    boolean toc = false;
                    if(tOrderContract2==null||"".equals(tOrderContract2)){
                        tOrderContract2 = new TOrderContract();
                        tOrderContract2.setContractCode(IdWorkerUtil.getInstance().nextId());
                        toc = true;
                    }
                    tOrderContract2.setOrderCode(orderCode);
                    tOrderContract2.setContractType("CYHT");//承运合同
                    tOrderContract2.setContractForm("DSFWSHT"); //第三方网上合同
                    tOrderContract2.setFirstPart(jsonObject.get("QName").toString());//合同的甲方
                    tOrderContract2.setFirstPartPlatformId(Integer.valueOf(jsonObject.get("carrierId").toString()));//甲方平台id
                    tOrderContract2.setSecondPart(jsonObject.get("AName").toString()); //合同的乙方
                    tOrderContract2.setSecondPartPlatformId(Integer.valueOf(jsonObject.get("AId").toString()));//乙方平台id
                    tOrderContract2.setSignFinishTime(new Date());//签订完成时间
                    tOrderContract2.setFirstPartSignTime(new Date());//甲方签订时间
                    tOrderContract2.setSecondPartSignTime(new Date());//乙方签到时间

                    log.info("插入合同表成功！");
                    String tOrderInfoListJson = tOrderInfoAPI.selectByOrderCode(orderCode);
                    List<TOrderInfo> tOrderInfoList = JSON.parseArray(tOrderInfoListJson, TOrderInfo.class);

                    //合同下载
                    RespVo respVo =htDownLoad(contractNum,mobilePhone);
                    String code3 = respVo.getCode();
                    String message3 = respVo.getMessage();

                    //合同下载路径
                    String contractFilePath = respVo.getFastResultData();

                    //合同下载路径
                    String contractFilePath2 = respVo.getFastResultData2();
                    //合同下载路径
                    String contractFilePath3 = respVo.getFastResultData3();

                    log.info("合同上传成功！" + "-------" + contractFilePath + "------------------" + contractFilePath2 + "---------------------" + contractFilePath3);

                    if ("0".equals(code3) && respVo.getFastResultData() != null) {
                        log.info("修改合同表开始！");
                        for (TOrderInfo orderInfo : tOrderInfoList) {
                            orderInfo.setContractStatus("YZID");
                            //修改合同状态
                            tOrderInfoAPI.edit(orderInfo);
                        }
//                            TOrderContract tOrderContract = tOrderContractAPI.selectByOrderBusinessCode(orderCode);
                        tOrderContract2.setContractSignStatus("XZ");
                        tOrderContract2.setContractFilePath(contractFilePath);//合同上传路径
                        tOrderContract2.setParam1(contractNum + ".pdf");
                        tOrderContract2.setParam2(contractFilePath2 + "," + contractFilePath3);//合同图片路径
                        tOrderContract2.setParam3(contractNum + ".0jpg" + "," + contractNum + ".1jpg");
                        //修改运单合同表下载时间
                        tOrderContract2.setUpdateTime(new Date());
                        tOrderContract2.setContractDownloadTime(new Date());
                        if(toc){
                            tOrderContractAPI.add(tOrderContract2);
                        }else{
                            tOrderContractAPI.updateHt(tOrderContract2);
                        }
                        log.info("修改合同表成功！" + JSONObject.toJSONString(tOrderContract2));

                        //task表数据移到历史表
                        TTaskHistory tTaskHistory = new TTaskHistory();
                        tTaskHistory.setTaskHisId(IdWorkerUtil.getInstance().nextId());
                        tTaskHistory.setTaskId(task.getTaskId());
                        tTaskHistory.setTaskType(task.getTaskType());
                        tTaskHistory.setTaskTypeNode(task.getTaskTypeNode());
                        tTaskHistory.setBusinessType(task.getBusinessType());
                        tTaskHistory.setSourceTablename(task.getSourceTablename());
                        tTaskHistory.setSourcekeyFieldname(task.getSourcekeyFieldname());
                        tTaskHistory.setSourceFieldname(task.getSourceFieldname());
                        tTaskHistory.setSourceFieldvalue(task.getSourceFieldvalue());
                        tTaskHistory.setRequestUrl(task.getRequestUrl());
                        tTaskHistory.setRequestParameter(task.getRequestParameter());
                        tTaskHistory.setRequestTimes(task.getRequestTimes());
                        tTaskHistory.setRequestResult(task.getRequestResult());
                        tTaskHistory.setErrorMessage(task.getErrorMessage());
                        tTaskHistory.setRequestDate(task.getRequestDate());
                        tTaskHistory.setDealTime(task.getDealTime());
                        tTaskHistory.setIsSuccessed(task.getIsSuccessed());
                        tTaskHistory.setToHisDate(task.getCreateTime());
                        tTaskHistory.setToHisUserid(task.getCreateUser());
                        tTaskHistory.setRemark(task.getRemark());
                        tTaskHistory.setCreateTime(new DateTime());
                        tTaskHistory.setEnable(false);
                        tTaskHistoryMapper.insertSelective(tTaskHistory);
                        tTaskMapper.deleteByPrimaryKey(task.getId());
                        resu.setMsg("执行成功！");
                    } else {
                        log.info("创建合同失败！421行");
                        for (TOrderInfo orderInfo : tOrderInfoList) {
                            if(orderInfo.getContractStatus()==null||!"".equals(orderInfo.getContractStatus())){
                                orderInfo.setContractStatus("WQD");
                                tOrderInfoAPI.edit(orderInfo);
                                //修改合同状态
                                //创建合同失败 添加运单异常表
                                TOrderAbnormal tOrderAbnormal = new TOrderAbnormal();
                                tOrderAbnormal.setCode(IdWorkerUtil.getInstance().nextId());
                                tOrderAbnormal.setOrderCode(orderCode);
                                tOrderAbnormal.setAbnormalType("HETONGYC");
                                tOrderAbnormal.setEnable(false);
                                tOrderAbnormal.setAbnormalDescription(message3);//异常内容
                                tOrderAbnormal.setCreateTime(new Date());
                                TOrderAbnormalAPI.add(tOrderAbnormal);
                            }
                        }
                        task.setRequestTimes(task.getRequestTimes() + 1);
                        task.setErrorMessage(message3);
                        task.setRequestDate(new Date());
                        task.setUpdateTime(new Date());

                        tTaskMapper.updateByPrimaryKeySelective(task);
                        resu.setCode(code3);
                        resu.setMsg(message3);
                        return resu;
                    }
                }else{
                    return qsErorr(orderCode,message2,code2,task,"2");
                }

            }else{
                return qsErorr(orderCode,message22,code22,task,"1");

            }
            return resu;
        }catch (Exception e){
            log.error("签署合同任务执行失败！ ",e);
            throw e;
        }
    }


    /**
    * @Description  合同创建未下载
    * <AUTHOR>
    * @Date   2019/7/12 10:42
    * @Param
    * @Return
    * @Exception
    *
    */
    @LcnTransaction
    @Transactional
    public ResultUtil orderContractSuccessDownl(String code1,String orderCode,String contractNum,String mobilePhone,TTask task,String message1,JSONObject jsonObject){
        try{
            ResultUtil resu = new ResultUtil();

            //签署合同企业、司机参数对象
            RespVo qsht = qshtObject(jsonObject);
            Map<Object,Object> mapo = qsht.getMapo();//企业签署合同参数
            Map<Object,Object> mapp = qsht.getMapp();//司机签署合同参数
            if(StringUtils.isNotBlank(task.getParam1())){
                //企业未签
                ContractApplyCertResp ru22 = contractAPI.autoSign(mapo, contractNum, 1);
                if("0".equals(ru22.getCode())){
                    task.setParam1(null);
                }else{
                    return qsErorr(orderCode,ru22.getMessage(),ru22.getCode(),task,"1");
                }
            }
            if(StringUtils.isNotBlank(task.getParam2())){
                //司机未签
                task.setParam1(null);
                ContractApplyCertResp ru = contractAPI.autoSign(mapp, contractNum, 0);
                if("0".equals(ru.getCode())){
                    task.setParam2(null);
                }else{
                    return qsErorr(orderCode,ru.getMessage(),ru.getCode(),task,"2");
                }

            }
            if ("8017".equals(code1)&&null==task.getParam1()&&null==task.getParam2()) {//合同已创建未下载
                String tOrderInfoListJson = tOrderInfoAPI.selectByOrderCode(orderCode);
                List<TOrderInfo> tOrderInfoList = JSON.parseArray(tOrderInfoListJson, TOrderInfo.class);
                log.info("合同下载开始！");
                //合同下载
                RespVo respVo =htDownLoad(contractNum,mobilePhone);
                String code3 = respVo.getCode();
                String message3 = respVo.getMessage();
                //合同下载路径
                String contractFilePath = respVo.getFastResultData();
                //合同下载路径
                String contractFilePath2 = respVo.getFastResultData2();
                //合同下载路径
                String contractFilePath3 = respVo.getFastResultData3();

                log.info("合同上传成功！" + contractFilePath + "-----" + contractFilePath2 + "-------" + contractFilePath3);

                //成功
                if ("0".equals(code3) && respVo.getFastResultData() != null) {
                    for (TOrderInfo orderInfo : tOrderInfoList) {
                        orderInfo.setContractStatus("YZID");
                        //修改合同状态
                        tOrderInfoAPI.edit(orderInfo);
                    }
                    TOrderContract tOrderContract = tOrderContractAPI.selectByOrderBusinessCode(orderCode);
                    log.info("合同对象："+JSONObject.toJSONString(tOrderContract));
                    if(tOrderContract==null ||"".equals(tOrderContract)){
                        TOrderContract tOrderContract2 =  tOrderContractAPI.selectByOrderBusinessCode(orderCode);
                        boolean toc = false;
                        if(tOrderContract2==null||"".equals(tOrderContract2)){
                            tOrderContract2 = new TOrderContract();
                            tOrderContract2.setContractCode(IdWorkerUtil.getInstance().nextId());
                            toc = true;
                        }
                        tOrderContract2.setOrderCode(orderCode);
                        tOrderContract2.setContractType("CYHT");//承运合同
                        tOrderContract2.setContractForm("DSFWSHT"); //第三方网上合同
                        tOrderContract2.setFirstPart(jsonObject.get("QName").toString());//合同的甲方
                        tOrderContract2.setFirstPartPlatformId(Integer.valueOf(jsonObject.get("carrierId").toString()));//甲方平台id
                        tOrderContract2.setSecondPart(jsonObject.get("AName").toString()); //合同的乙方
                        tOrderContract2.setSecondPartPlatformId(Integer.valueOf(jsonObject.get("AId").toString()));//乙方平台id
                        tOrderContract2.setSignFinishTime(new Date());//签订完成时间
                        tOrderContract2.setFirstPartSignTime(new Date());//甲方签订时间
                        tOrderContract2.setSecondPartSignTime(new Date());//乙方签到时间
                        tOrderContract2.setContractSignStatus("XZ");
                        tOrderContract2.setContractFilePath(contractFilePath);//合同上传路径
                        tOrderContract2.setParam1(contractNum + ".pdf");
                        tOrderContract2.setParam2(contractFilePath2 + "," + contractFilePath3);//合同图片路径
                        tOrderContract2.setParam3(contractNum + ".0jpg" + "," + contractNum + ".1jpg");
                        //修改运单合同表下载时间
                        tOrderContract2.setUpdateTime(new Date());
                        tOrderContract2.setContractDownloadTime(new Date());
                        if(toc){
                            tOrderContractAPI.add(tOrderContract2);
                        }else{
                            tOrderContractAPI.updateHt(tOrderContract2);
                        }
                        log.info("新增合同表成功2！" + JSONObject.toJSONString(tOrderContract2));
                    }else{
                        tOrderContract.setContractSignStatus("XZ");
                        tOrderContract.setContractFilePath(contractFilePath);//合同上传路径
                        tOrderContract.setParam1(contractNum + ".pdf");
                        tOrderContract.setParam2(contractFilePath2 + "," + contractFilePath3);//合同图片路径
                        tOrderContract.setParam3(contractNum + ".0jpg" + "," + contractNum + ".1jpg");
                        //修改运单合同表下载时间
                        tOrderContract.setUpdateTime(new Date());
                        tOrderContract.setContractDownloadTime(new Date());
                        tOrderContractAPI.updateHt(tOrderContract);
                        log.info("修改合同表成功2！" + JSONObject.toJSONString(tOrderContract));
                    }

                    //查询运单异常表是否有合同签署失败异常
                    TOrderAbnormal an = new TOrderAbnormal();
                    an.setOrderCode(orderCode);
                    an.setAbnormalType("HETONGYC");
                    List<TOrderAbnormal> tOrderAbnormalList = TOrderAbnormalAPI.selectByOrderCode(an);
                    for(TOrderAbnormal tab:tOrderAbnormalList){
                        tab.setEnable(true);
                        TOrderAbnormalAPI.update(tab);
                    }
                    //task表数据移到历史表
                    TTaskHistory tTaskHistory = new TTaskHistory();
                    tTaskHistory.setTaskHisId(IdWorkerUtil.getInstance().nextId());
                    tTaskHistory.setTaskId(task.getTaskId());
                    tTaskHistory.setTaskType(task.getTaskType());
                    tTaskHistory.setTaskTypeNode(task.getTaskTypeNode());
                    tTaskHistory.setBusinessType(task.getBusinessType());
                    tTaskHistory.setSourceTablename(task.getSourceTablename());
                    tTaskHistory.setSourcekeyFieldname(task.getSourcekeyFieldname());
                    tTaskHistory.setSourceFieldname(task.getSourceFieldname());
                    tTaskHistory.setSourceFieldvalue(task.getSourceFieldvalue());
                    tTaskHistory.setRequestUrl(task.getRequestUrl());
                    tTaskHistory.setRequestParameter(task.getRequestParameter());
                    tTaskHistory.setRequestTimes(task.getRequestTimes());
                    tTaskHistory.setRequestResult(task.getRequestResult());
                    tTaskHistory.setErrorMessage(task.getErrorMessage());
                    tTaskHistory.setRequestDate(task.getRequestDate());
                    tTaskHistory.setDealTime(task.getDealTime());
                    tTaskHistory.setIsSuccessed(task.getIsSuccessed());
                    tTaskHistory.setToHisDate(task.getCreateTime());
                    tTaskHistory.setToHisUserid(task.getCreateUser());
                    tTaskHistory.setRemark(task.getRemark());
                    tTaskHistory.setCreateTime(new DateTime());
                    tTaskHistory.setEnable(false);
                    tTaskHistoryMapper.insertSelective(tTaskHistory);
                    tTaskMapper.deleteByPrimaryKey(task.getId());
                    resu.setMsg("执行成功！");
                } else {
                    return qsErorr(orderCode,message3,code3,task,"");
                }
            } else {
                return qsErorr(orderCode,message1,code1,task,"");
            }
            return resu;
        }catch (Exception e){
            log.error("签署合同任务执行失败！ ",e);
            throw e;
        }
    }

    //合同下载
    private RespVo htDownLoad(String contractNum,String mobilePhone){
        try{
            RespVo resu = new RespVo();
            log.info("合同下载开始！");
            //合同下载
            ContractDownloadContResp r = contractAPI.downloadCont(contractNum, mobilePhone);
            log.info("合同下载成功！");
            InputStream inputStream = new ByteArrayInputStream(r.getBytes());

            BufferedOutputStream bos = null;
            FileOutputStream fos = null;
            File file = null;
            log.info("写入pdf文件开始！" + htPath + contractNum + ".pdf");
            try {
                File dir = new File(htPath + contractNum + ".pdf");
                if (!dir.exists() && dir.isDirectory()) {//判断文件目录是否存在
                    dir.mkdirs();
                }
                file = new File(htPath + contractNum + ".pdf");
                fos = new FileOutputStream(file);
                bos = new BufferedOutputStream(fos);
                bos.write(r.getBytes());
                log.info("写入pdf文件成功！" + htPath + contractNum + ".pdf");
            } catch (Exception e) {
                log.info("生成pdf失败！", e);
            } finally {
                if (bos != null) {
                    try {
                        bos.close();
                    } catch (IOException e1) {
                        e1.printStackTrace();
                    }
                }
                if (fos != null) {
                    try {
                        fos.close();
                    } catch (IOException e1) {
                        e1.printStackTrace();
                    }
                }
            }
            PDDocument pdDocument = null;
            try {
                log.info("写入图片文件开始！" + htPath + contractNum + ".pdf");
                File file2 = new File(htPath + contractNum + ".pdf");
                pdDocument = PDDocument.load(file2);
                PDFRenderer renderer = new PDFRenderer(pdDocument);
                /* dpi越大转换后越清晰，相对转换速度越慢 */
                PdfReader reader = new PdfReader(htPath + contractNum + ".pdf");
                int pages = reader.getNumberOfPages();
                StringBuffer imgFilePath = null;
                for (int i = 0; i < pages; i++) {
                    String pngFilePath = htPath + contractNum + i + ".png";
                    BufferedImage image = renderer.renderImageWithDPI(i, 300);
                    ImageIO.write(image, "png", new File(pngFilePath));
                }
                log.info("写入图片文件成功！");
            } catch (IOException e) {
                e.printStackTrace();

            }finally {
                pdDocument.close();
            }
            MultipartFile multipartFile = null;
            MultipartFile multipartFileImg = null;
            MultipartFile multipartFileImg2 = null;
            try {
                multipartFile = new MockMultipartFile("file", contractNum + ".pdf", "pdf", inputStream);
            } catch (IOException e) {
                e.printStackTrace();
            }
            //生成图片
            try {
                File file3 = new File(htPath + contractNum + "0.png");
                InputStream In = new FileInputStream(file3);
                multipartFileImg = new MockMultipartFile("file", contractNum + ".png", "png", In);
                File file4 = new File(htPath + contractNum + "1.png");
                InputStream I = new FileInputStream(file4);
                multipartFileImg2 = new MockMultipartFile("file", contractNum + ".png", "png", I);
            } catch (IOException e) {
                log.info("生成图片失败！", e);
            }
            //文件上传
            //ResultUtil fastResult = fastdfsAPI.uploadFileSample(multipartFile);
            //图片上传
            //ResultUtil fastResult2 = fastdfsAPI.uploadFileSample(multipartFileImg);
            //ResultUtil fastResult3 = fastdfsAPI.uploadFileSample(multipartFileImg2);
            //Map<Object, Object> fastResultData = (Map<Object, Object>) fastResult.getData();
            //Map<Object, Object> fastResultData2 = (Map<Object, Object>) fastResult2.getData();
            //Map<Object, Object> fastResultData3 = (Map<Object, Object>) fastResult3.getData();
            //文件上传
            ResultUtil fastResult = ossAPI.uploadFileSample(multipartFile);
            //图片上传
            ResultUtil fastResult2 = ossAPI.uploadFileSample(multipartFileImg);
            ResultUtil fastResult3 = ossAPI.uploadFileSample(multipartFileImg2);

            //成功
            resu.setCode(r.getCode());
            resu.setMessage(r.getMessage());
            resu.setFastResultData(fastResult.getData().toString());
            resu.setFastResultData2(fastResult2.getData().toString());
            resu.setFastResultData3(fastResult3.getData().toString());
            return resu;
        }catch (Exception e){
            log.error("签署合同任务执行失败！ ",e);
            throw new RuntimeException(e);
        }
    }

    //签署合同返回企业、司机对象参数
    private RespVo qshtObject(JSONObject jsonObject){
        RespVo respVo = new RespVo();
        //企业签署合同
        Map<Object, Object> mapo = new HashMap<>();
        mapo.put("type", "2");//类型 1：个人 、2：企业
        mapo.put("cardType", "8");// 证件类型 0：身份证 1：军官证 ,2：护照、 3：驾驶证、4：工商登记证、5：税务登记证、6：组织机构代码、7：其他证件，8：统一社会信用代码
        mapo.put("idCardNum", jsonObject.get("CCDBusinessNo"));// 证件号码
        mapo.put("name", jsonObject.get("QName"));//企业或者个人真实名称
        mapo.put("mobilePhone", jsonObject.get("Qphone"));//企业联系手机号
        //个人调用易云章 根据签署合同
        Map<Object, Object> mapp = new HashMap<>();
        mapp.put("type", "1");//类型 1：个人 、2：企业
        mapp.put("cardType", "0");// 证件类型 0：身份证 1：军官证 ,2：护照、 3：驾驶证、4：工商登记证、5：税务登记证、6：组织机构代码、7：其他证件，8：统一社会信用代码
        mapp.put("idCardNum", jsonObject.get("ACardNo"));// 证件号码
        mapp.put("name", jsonObject.get("AName"));//企业或者个人真实名称
        mapp.put("mobilePhone", jsonObject.get("Aphone"));//企业或者个人联系手机号
        respVo.setMapo(mapo);
        respVo.setMapp(mapp);
        return respVo;
    }

    //合同失败处理
    private ResultUtil qsErorr(String orderCode,String message,String code,TTask task,String param){
        try{
            ResultUtil resu = new ResultUtil();
            String tOrderInfoListJson = tOrderInfoAPI.selectByOrderCode(orderCode);
            List<TOrderInfo> tOrderInfoList = JSON.parseArray(tOrderInfoListJson, TOrderInfo.class);
            for (TOrderInfo orderInfo : tOrderInfoList) {
                if(orderInfo.getContractStatus()==null||!"".equals(orderInfo.getContractStatus())){
                    orderInfo.setContractStatus("WQD");
                    //修改合同状态
                    tOrderInfoAPI.edit(orderInfo);
                    //创建合同失败
                    TOrderAbnormal tOrderAbnormal = new TOrderAbnormal();
                    tOrderAbnormal.setCode(IdWorkerUtil.getInstance().nextId());
                    tOrderAbnormal.setOrderCode(orderCode);
                    tOrderAbnormal.setAbnormalType("HETONGYC");//签到异常
                    tOrderAbnormal.setEnable(false);
                    tOrderAbnormal.setAbnormalDescription(message);//异常内容
                    TOrderAbnormalAPI.add(tOrderAbnormal);
                }
            }
            task.setRequestTimes(task.getRequestTimes() + 1);
            task.setErrorMessage(message);
            task.setUpdateTime(new Date());
            if("1".equals(param)){
                task.setParam1("1");
            }else if("2".equals(param)){
                task.setParam2("1");
            }
            resu.setMsg(message);
            resu.setMsg(code);
            tTaskMapper.updateByPrimaryKeySelective(task);
            return resu;
        }catch (Exception e){
            log.error("签署合同任务执行失败！ ",e);
            throw e;
        }
    }

    /**
     * 申请司机子账号
     *
     * @return
     * <AUTHOR>
     */
    @Override
    public ResultUtil applySubAccountTask() {
        ResultUtil resu = new ResultUtil();
        TTaskExample example = new TTaskExample();
        TTaskExample.Criteria c = example.createCriteria();
        c.andTaskTypeEqualTo("SQZZH");
        c.andBusinessTypeEqualTo("ZF");
        c.andRequestTimesLessThanOrEqualTo(5);
        List<TTask> list = tTaskMapper.selectByExample(example);
        for (TTask task : list) {
            resu = subAccountTask(task);
        }
        return resu;
    }


    /**
     * @author: dingweibo
     * @Date: 2019/6/18 9:13
     * @Description: 申请司机子账号
     */
    @LcnTransaction
    @Transactional
    public ResultUtil subAccountTask(TTask task) {
        ResultUtil resu = new ResultUtil();
        try {
            String param = task.getRequestParameter();
            JSONObject jsonObject = JSONObject.parseObject(param);

            //申请子账号
            UserRequest ur = new UserRequest();
            boolean flag = true;
            if (jsonObject.get("thirdPartyInterfaceManageAddress") == null || "".equals(jsonObject.get("thirdPartyInterfaceManageAddress"))) {
                flag = false;
                task.setErrorMessage("承运方表中第三方接口处理地址为空！");
            }
            if (jsonObject.get("bussinessPlatformSignCode") == null || "".equals(jsonObject.get("bussinessPlatformSignCode"))) {
                flag = false;
                task.setErrorMessage("业务平台签约编号为空！");
            }
            if (jsonObject.get("uid") == null || "".equals(jsonObject.get("uid"))) {
                flag = false;
                task.setErrorMessage("承运方与企业关联表Uid为空！");
            }
            if (jsonObject.get("memberName") == null || "".equals(jsonObject.get("memberName"))) {
                flag = false;
                task.setErrorMessage("会员名称为空！");
            }
            if (jsonObject.get("realName") == null || "".equals(jsonObject.get("realName"))) {
                flag = false;
                task.setErrorMessage("真实姓名为空！");
            }
            if (jsonObject.get("certificateType") == null || "".equals(jsonObject.get("certificateType"))) {
                flag = false;
                task.setErrorMessage("证件类型为空！");
            }
            if (jsonObject.get("certificateNo") == null || "".equals(jsonObject.get("certificateNo"))) {
                flag = false;
                task.setErrorMessage("证件号为空！");
            }

            if (flag) {
                ur.setKeyStoreName(jsonObject.get("thirdPartyInterfaceManageAddress").toString());//承运方表中第三方接口处理地址
                ur.setPartner_id(jsonObject.get("bussinessPlatformSignCode").toString());//业务平台签约编号
                ur.setUid(jsonObject.get("uid").toString());//承运方与企业关联表Uid
                ur.setMember_name(jsonObject.get("memberName").toString());//会员名称。用户昵称(平台个人会员登录名)
                ur.setReal_name(jsonObject.get("realName").toString());//真实姓名
                ur.setCertificate_type(jsonObject.get("certificateType").toString());//证件类型（见附录）。目前只支持身份证。
                ur.setCertificate_no(jsonObject.get("certificateNo").toString());//作为会员实名认证通过后的证件号
                ResultUtil resultUtil = userRegisterAPI.personalRegister(ur);
                JSONObject jo = JSONObject.parseObject(resultUtil.getData().toString());
                String sub_account_no = jo.get("sub_account_no").toString();
                if ("T".equals(jo.get("is_success"))) {
                    //承运放子账号信息
                    TCarrierEnduserCompanyRel rel = tCarrierEnduserCompanyRelAPI.selectByUid(ur.getUid());
                    TCarrierEnduserCompanyRel rel2 = new TCarrierEnduserCompanyRel();
                    rel2.setId(rel.getId());
                    rel2.setThridParySubAccount(jsonObject.get("thirdPartyInterfaceMainAccountNo") + sub_account_no);
                    tCarrierEnduserCompanyRelAPI.edit(rel2);

                    //task表数据移到历史表
                    TTaskHistory tTaskHistory = new TTaskHistory();
                    tTaskHistory.setTaskHisId(IdWorkerUtil.getInstance().nextId());
                    tTaskHistory.setTaskId(task.getTaskId());
                    tTaskHistory.setTaskType(task.getTaskType());
                    tTaskHistory.setTaskTypeNode(task.getTaskTypeNode());
                    tTaskHistory.setBusinessType(task.getBusinessType());
                    tTaskHistory.setSourceTablename(task.getSourceTablename());
                    tTaskHistory.setSourcekeyFieldname(task.getSourcekeyFieldname());
                    tTaskHistory.setSourceFieldname(task.getSourceFieldname());
                    tTaskHistory.setSourceFieldvalue(task.getSourceFieldvalue());
                    tTaskHistory.setRequestUrl(task.getRequestUrl());
                    tTaskHistory.setRequestParameter(task.getRequestParameter());
                    tTaskHistory.setRequestTimes(task.getRequestTimes());
                    tTaskHistory.setRequestResult(task.getRequestResult());
                    tTaskHistory.setErrorMessage(task.getErrorMessage());
                    tTaskHistory.setRequestDate(task.getRequestDate());
                    tTaskHistory.setDealTime(task.getDealTime());
                    tTaskHistory.setIsSuccessed(task.getIsSuccessed());
                    tTaskHistory.setToHisDate(new Date());
                    tTaskHistory.setToHisUserid(task.getCreateUser());
                    tTaskHistory.setCreateTime(new Date());
                    tTaskHistory.setRemark(task.getRemark());
                    tTaskHistoryMapper.insertSelective(tTaskHistory);
                    tTaskMapper.deleteByPrimaryKey(task.getId());
                    resu.setMsg("执行成功！");
                } else if ("IDENTITY_EXIST_ERROR".equals(jsonObject.get("error_code"))) {//修改 如果申请子账号，库中没有根据uid查余额获取子账号
                    PaymentRequest paymentRequest = new PaymentRequest();
                    paymentRequest.setUid(jsonObject.get("uid").toString());
                    paymentRequest.setKeyStoreName(jsonObject.get("thirdPartyInterfaceManageAddress").toString());
                    paymentRequest.setPartner_id(jsonObject.get("bussinessPlatformSignCode").toString());
                    ResultUtil ru = paymentAPI.balanceAccount(paymentRequest);
                    JSONObject jobj = JSONObject.parseObject(ru.getData().toString());
                    if ("T".equals(jobj.get("is_success"))) {
                        String san = JSONObject.parseObject(jobj.get("account_list").toString()).get("sub_account_no").toString();
                        //承运放子账号信息
                        TCarrierEnduserCompanyRel rel = tCarrierEnduserCompanyRelAPI.selectByUid(ur.getUid());
                        TCarrierEnduserCompanyRel rel2 = new TCarrierEnduserCompanyRel();
                        rel2.setId(rel.getId());
                        rel2.setThridParySubAccount(jsonObject.get("thirdPartyInterfaceMainAccountNo") + san);
                        tCarrierEnduserCompanyRelAPI.edit(rel2);

                        //task表数据移到历史表
                        TTaskHistory tTaskHistory = new TTaskHistory();
                        tTaskHistory.setTaskHisId(IdWorkerUtil.getInstance().nextId());
                        tTaskHistory.setTaskId(task.getTaskId());
                        tTaskHistory.setTaskType(task.getTaskType());
                        tTaskHistory.setTaskTypeNode(task.getTaskTypeNode());
                        tTaskHistory.setBusinessType(task.getBusinessType());
                        tTaskHistory.setSourceTablename(task.getSourceTablename());
                        tTaskHistory.setSourcekeyFieldname(task.getSourcekeyFieldname());
                        tTaskHistory.setSourceFieldname(task.getSourceFieldname());
                        tTaskHistory.setSourceFieldvalue(task.getSourceFieldvalue());
                        tTaskHistory.setRequestUrl(task.getRequestUrl());
                        tTaskHistory.setRequestParameter(task.getRequestParameter());
                        tTaskHistory.setRequestTimes(task.getRequestTimes());
                        tTaskHistory.setRequestResult(task.getRequestResult());
                        tTaskHistory.setErrorMessage(task.getErrorMessage());
                        tTaskHistory.setRequestDate(task.getRequestDate());
                        tTaskHistory.setDealTime(task.getDealTime());
                        tTaskHistory.setIsSuccessed(task.getIsSuccessed());
                        tTaskHistory.setToHisDate(new Date());
                        tTaskHistory.setToHisUserid(task.getCreateUser());
                        tTaskHistory.setCreateTime(new Date());
                        tTaskHistory.setRemark(task.getRemark());
                        tTaskHistoryMapper.insertSelective(tTaskHistory);
                        tTaskMapper.deleteByPrimaryKey(task.getId());
                        resu.setMsg("执行成功！");
                    }
                } else {
                    TOrderAbnormal tOrderAbnormal = new TOrderAbnormal();
                    tOrderAbnormal.setCode(IdWorkerUtil.getInstance().nextId());
                    tOrderAbnormal.setAbnormalType("SQZZH");//申请子账号异常
                    tOrderAbnormal.setEnable(false);
                    tOrderAbnormal.setAbnormalDescription(jo.get("error_message").toString());//异常内容
                    TOrderAbnormalAPI.add(tOrderAbnormal);
                    task.setUpdateTime(new Date());
                    task.setErrorMessage(jo.get("error_message").toString());
                    task.setRequestTimes(task.getRequestTimes() + 1);
                    //失败原因
                    task.setErrorMessage(jo.get("error_message").toString());
                    tTaskMapper.updateByPrimaryKeySelective(task);
                    resu.setMsg("执行失败！");
                }
            } else {
                TOrderAbnormal tOrderAbnormal = new TOrderAbnormal();
                tOrderAbnormal.setCode(IdWorkerUtil.getInstance().nextId());
                tOrderAbnormal.setAbnormalType("SQZZH");//申请子账号异常
                tOrderAbnormal.setEnable(false);
                tOrderAbnormal.setAbnormalDescription(task.getErrorMessage());//异常内容
                TOrderAbnormalAPI.add(tOrderAbnormal);
                task.setUpdateTime(new Date());
                task.setRequestTimes(task.getRequestTimes() + 1);
                tTaskMapper.updateByPrimaryKeySelective(task);
                resu.setMsg("执行失败！");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("申请子账号失败！   任务id ： " + task.getId(), e);
            task.setRequestTimes(task.getRequestTimes() + 1);
            //失败原因
            task.setErrorMessage(e.getMessage());
            //失败原因
            tTaskMapper.updateByPrimaryKeySelective(task);
            resu.setMsg("执行失败！");
        }
        return resu;
    }


    /**
     * @author: sangbin
     * @Date: 2019/6/28 16:17
     * @Description:
     */
    @Override
    @LcnTransaction(propagation = DTXPropagation.SUPPORTS)
    @Transactional
    public ResultUtil insertTask(TTask task) {
        tTaskMapper.insert(task);
        return ResultUtil.ok();
    }

    /**
     * @author: sangbin
     * @Date: 2019/6/28 16:17
     * @Description:
     */
    @Override
    public ResultUtil insertTaskError(TTaskError task) {
        tTaskErrorMapper.insert(task);
        return ResultUtil.ok();
    }

    /**
     * @author: dwb
     * @Date: 2019/6/28 16:17
     * @Description:
     */
    @LcnTransaction(propagation = DTXPropagation.SUPPORTS)
    @Transactional
    @Override
    public ResultUtil add(TTask task) {
        tTaskMapper.insertSelective(task);
        return ResultUtil.ok();
    }

    @LcnTransaction(propagation = DTXPropagation.SUPPORTS)
    @Transactional
    @Override
    public ResultUtil addHis(TTaskHistory taskHistory) {
        tTaskHistoryMapper.insertSelective(taskHistory);
        return ResultUtil.ok();
    }

    //修改task的状态
    @Override
    public int updateTask(TTask task, Boolean isSuccess, String result) {
        task.setRequestTimes(task.getRequestTimes() + 1);//请求次数加一
        task.setRequestResult(result);
        task.setDealTime(new Date());
        task.setIsSuccessed(isSuccess);
        return tTaskMapper.updateByPrimaryKeySelective(task);
    }

    @Override
    public int updateErrorTask(TTask task, String errorMassage) {
        task.setRequestTimes(task.getRequestTimes() + 1);//请求次数加一
        task.setRequestDate(new Date());
        task.setErrorMessage(errorMassage);
        return tTaskMapper.updateByPrimaryKeySelective(task);
    }

    //@LcnTransaction
    @Transactional
    @Override
    public void Instant(TTask task) {
        log.info("发起入账开始");
        try {
            InstantRequest request = JSON.parseObject(task.getRequestParameter(), InstantRequest.class);
            ResultUtil yfResult = null;
            ResultUtil ddfResult = null;
            if (task.getTaskTypeNode().equals("CWRZ") || task.getTaskTypeNode().equals("CWDBRZ")) {
                instantJobUtil.CWRZ(request, yfResult, ddfResult,task.getTaskTypeNode());
            } else if (task.getTaskTypeNode().equals("CWZH") || task.getTaskTypeNode().equals("CWDBZH")) {
                instantJobUtil.CWZH(request, yfResult, ddfResult,task.getTaskTypeNode());
            } else if (task.getTaskTypeNode().equals("YFKRZ")) {
                instantJobUtil.CWYFKRZ(request, yfResult, ddfResult,"RZ");
            } else if (task.getTaskTypeNode().equals("HZRZ")) {
                // 划账
                instantJobUtil.HZRZ(request);
            }

            if (null != yfResult) {
                JSONObject jo = JSONObject.parseObject(yfResult.getData().toString());
                if (jo.getString("is_success").equals("F")) {
                    String errorMessage = "运费入账异常-" + jo.getString("error_message");
                    throw new RuntimeException(errorMessage);
                }
            }
            if (null != ddfResult) {
                JSONObject jo = JSONObject.parseObject(ddfResult.getData().toString());
                if (jo.getString("is_success").equals("F")) {
                    String errorMessage = "调度费入账异常-" + jo.getString("error_message");
                    throw new RuntimeException(errorMessage);
                }
            }
            insertTaskHistory(task);
            tTaskMapper.deleteByPrimaryKey(task.getId());
        } catch (Exception e) {
            log.error("发起入账失败", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                throw new RuntimeException(message);
            }
            throw new RuntimeException("发起入账失败");
        }
        log.info("发起入账结束");
    }

    //@LcnTransaction
    @Transactional
    @Override
    public ResultUtil excuteWithdrawtocard(TTask task, Map<String, String> map) {
        log.info("发起提现开始");
        /*Integer bankId = Integer.valueOf(map.get("BankId"));
        TBankCard tBankCard = accountService.selectById(bankId);
        if (null == tBankCard) {
            throw new RuntimeException("未找到银行卡,提现失败");
        }*/
        //查询网商实际余额是否可以支付
        BigDecimal total = new BigDecimal(map.get("Amount"));
        PaymentRequest balance = new PaymentRequest();
        balance.setPartner_id(map.get("Partner_id"));
        balance.setKeyStoreName(map.get("KeyStoreName"));
        balance.setUid(map.get("uid"));
        ResultUtil result = paymentAPI.balanceAccount(balance);
        if (null != result) {
            JSONObject jo = JSONObject.parseObject(result.getData().toString());
            if (jo.getString("is_success").equals("T")) {
                JSONArray accountList = jo.getJSONArray("account_list");
                if (null != accountList && accountList.size() > 0) {
                    Object o = accountList.get(0);
                    JSONObject account = JSONObject.parseObject(o.toString());
                    BigDecimal availableBalance = account.getBigDecimal("available_balance");
                    if (availableBalance.doubleValue() < total.doubleValue()) {
                        throw new RuntimeException("网商实际余额不足,提现失败");
                    }
                }
            } else {
                throw new RuntimeException(jo.getString("error_message"));
            }
        } else {
            throw new RuntimeException("查询企业网商余额失败.");
        }

        CashRequest cr = new CashRequest();
        cr.setUid(map.get("uid"));//合作方业务平台用户 ID
        cr.setNotify_url(map.get("Notify_url")); //对应 回调地址
        cr.setKeyStoreName(map.get("KeyStoreName")); //对应 选择证书
        cr.setPartner_id(map.get("Partner_id")); //对应 业务平台签约编号
        cr.setThirdPartyInterfaceConsumerCode(map.get("ThirdPartyInterfaceConsumerCode"));// 对应 出款编码
        cr.setCard_type("DC");//卡类型: DC 借记 CC 贷记（信用卡）
        cr.setCard_attribute("C");//卡属性:C 对私B 对公
        cr.setOuter_trade_no(map.get("orderPayDetailCode"));//合作方业务平台订单号
        cr.setOuter_inst_order_no(map.get("orderPayDetailCode"));//外部机构订单号，合作方对接出款渠道使用的提现订单号。若出款渠道是网商银行，则此处填写与outer_trade_no保持一致。
        cr.setAmount(map.get("Amount"));//提现金额。金额必须不大于账户可用余额 amount
        cr.setBank_account_no(map.get("cardNo"));//银行卡号 bank_account_no
        cr.setAccount_name(map.get("cardOwner"));//户名 account_name
        ResultUtil resultUtil = cashAPI.payToCard(cr);
        if (null != resultUtil) {
            JSONObject jo = JSONObject.parseObject(resultUtil.getData().toString());
            if (jo.getString("is_success").equals("F")) {
                String errorMessage = "提现异常-" + jo.getString("error_message");
                throw new RuntimeException(errorMessage);
            }
        } else {
            throw new RuntimeException("网商未有返回结果通知");
        }
        insertTaskHistory(task);
        tTaskMapper.deleteByPrimaryKey(task.getId());
        log.info("发起提现结束");
        return ResultUtil.ok();
    }



    @Override
    public TTask selectById(Integer taskId) {
        return tTaskMapper.selectByPrimaryKey(taskId);
    }


    /**
    * @Description 查询任务
    * <AUTHOR>
    * @Date   2019/7/18 11:14
    * @param
    * @Return
    * @Exception
    *
    */
    @Override
    public ResultUtil selectTask(TTaskVO record) {
        List<TTask> tTasks = tTaskMapper.selectTask(record);
        ResultUtil.ok(tTasks);
        return ResultUtil.ok(tTasks);
    }

    @Override
    public int deleteByPrimaryKey(Integer taskId) {
        return tTaskMapper.deleteByPrimaryKey(taskId);
    }

    /**
     * @author: dwb
     * @Date: 2019/6/28 16:17
     * @Description:
     */
    @LcnTransaction(propagation = DTXPropagation.SUPPORTS)
    @Transactional
    @Override
    public ResultUtil update(TTask task) {
        tTaskMapper.updateByPrimaryKeySelective(task);
        return ResultUtil.ok();
    }

    /**
     * @author: dwb
     * @Date: 2019/6/28 16:17
     * @Description:
     */
    @Transactional
    @Override
    public ResultUtil select(TTask task) {
        ResultUtil resultUtil = new ResultUtil();
        TTaskExample example = new TTaskExample();
        TTaskExample.Criteria cr =example.createCriteria();
        cr.andSourceFieldvalueEqualTo(task.getSourceFieldvalue());
        cr.andBusinessTypeEqualTo(task.getBusinessType());
        cr.andTaskTypeEqualTo(task.getTaskType());
        cr.andTaskTypeNodeEqualTo(task.getTaskTypeNode());
        cr.andEnableEqualTo(false);
        List<TTask> tTaskList = tTaskMapper.selectByExample(example);
        if(tTaskList.size()>0){
            resultUtil.setData(tTaskList);
        }else{
            resultUtil.setData(null);
        }
        return resultUtil;
    }

    @Override
    public TTask selectByKhyBasicsInfo(TTask task) {
        TTaskExample example = new TTaskExample();
        TTaskExample.Criteria cr =example.createCriteria();
        cr.andSourceFieldvalueEqualTo(task.getSourceFieldvalue());
        cr.andBusinessTypeEqualTo(task.getBusinessType());
        cr.andTaskTypeEqualTo(task.getTaskType());
        cr.andTaskTypeNodeEqualTo(task.getTaskTypeNode());
        cr.andEnableEqualTo(false);
        List<TTask> tTaskList = tTaskMapper.selectByExample(example);
        if(tTaskList.size() > 0){
            return tTaskList.get(0);
        }else {
            return null;
        }

    }

    /**
     * @author: dwb
     * @Date: 2019/6/28 16:17
     * @Description:
     */
    @Override
    public List<TTask> selectByCyht(TTask task) {
        TTaskExample example = new TTaskExample();
        TTaskExample.Criteria cr =example.createCriteria();
        if(null!=task.getSourceFieldvalue()&&!"".equals(task.getSourceFieldvalue())){
            cr.andSourceFieldvalueEqualTo(task.getSourceFieldvalue());
        }
        if(null!=task.getBusinessType()&&!"".equals(task.getBusinessType())){
            cr.andBusinessTypeEqualTo(task.getBusinessType());
        }
        if(null!=task.getTaskType()&&!"".equals(task.getTaskType())){
            cr.andTaskTypeEqualTo(task.getTaskType());
        }
        if(null!=task.getTaskTypeNode()&&!"".equals(task.getTaskTypeNode())){
            cr.andTaskTypeNodeEqualTo(task.getTaskTypeNode());
        }
        if(null!=task.getRequestTimes()&&!"".equals(task.getRequestTimes())){
            cr.andRequestTimesLessThan(task.getRequestTimes());
        }
        cr.andEnableEqualTo(false);
        return tTaskMapper.selectByExample(example);
    }
    @Transactional
    @Override
    public ResultUtil deleteById(Integer taskId) {
        tTaskMapper.deleteByPrimaryKey(taskId);
        return ResultUtil.ok();
    }

    /**
    * @Description 充值
    * <AUTHOR>
    * @Date   2019/8/20 13:50
    * @param
    * @Return
    * @Exception
    *
    */
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil czJob(TTask task) {
        log.info("发起充值任务");
        try {
            tOrderInfoAPI.callBackEntry(task.getRequestParameter());
            insertTaskHistory(task);
            tTaskMapper.deleteByPrimaryKey(task.getId());

        } catch (Exception e){
            log.error("发起充值任务失败", e);
            String msg = e.getMessage();
            if (StringUtils.isNotEmpty(msg)){
                throw new RuntimeException(msg);
            }
            throw new RuntimeException(msg);
        }
        log.info("发起充值任务结束");
        return ResultUtil.ok();
    }


    /**
    * @Description 执行入账回调任务
    * <AUTHOR>
    * @Date   2019/8/20 14:08
    * @param
    * @Return
    * @Exception
    *
    */
    //@LcnTransaction
    @Transactional
    @Override
    public ResultUtil tradeStatusSyncJob(TTask task) {
        log.info("执行入账回调任务");
        try {
            TTask tTask = tTaskMapper.selectByPrimaryKey(task.getId());
            if (tTask == null) {
                log.error("未找到任务");
                return ResultUtil.ok();
            }
            tOrderInfoAPI.callBackEntry(task.getRequestParameter());

            insertTaskHistory(task);
            tTaskMapper.deleteByPrimaryKey(task.getId());
        } catch (Exception e){
            log.error("执行入账回调任务失败", e);
            String msg = e.getMessage();
            if (StringUtils.isNotEmpty(msg)){
                throw new RuntimeException(msg);
            }
            throw new RuntimeException(msg);
        }
        log.info("执行入账回调任务结束");
        return ResultUtil.ok();
    }


    /**
     * @Description 执行提现回调任务
     * <AUTHOR>
     * @Date   2019/8/20 14:19
     * @param
     * @Return
     * @Exception
     *
     */
    //@LcnTransaction
    @Transactional
    @Override
    public ResultUtil WithdrawalStatusSyncJob(TTask task) {
        log.info("执行提现回调任务");
        try {
            tOrderInfoAPI.callBackEntry(task.getRequestParameter());
            insertTaskHistory(task);
            tTaskMapper.deleteByPrimaryKey(task.getId());
        } catch (Exception e){
            log.error("执行提现回调任务失败", e);
            String msg = e.getMessage();
            if (StringUtils.isNotEmpty(msg)){
                throw new RuntimeException(msg);
            }
            throw new RuntimeException(msg);
        }
        log.info("执行提现回调任务结束");
        return ResultUtil.ok();
    }

    /**
     * @Description 执行自动到卡，提现申请任务
     * <AUTHOR>
     * @Date   2019/8/20 14:19
     * @param
     * @Return
     * @Exception
     *
     */
    @Transactional
    @Override
    public ResultUtil withdrawtocardJobApply(TTask task) {
        log.info("执行提现申请任务");
        try {
            orderTaskAPI.withdrawtocardJobApply(task.getRequestParameter());
            insertTaskHistory(task);
            tTaskMapper.deleteByPrimaryKey(task.getId());
        } catch (Exception e){
            log.error("执行提现申请任务失败", e);
            String msg = e.getMessage();
            if (StringUtils.isNotEmpty(msg)){
                throw new RuntimeException(msg);
            }
            throw new RuntimeException(msg);
        }
        log.info("执行提现申请任务结束");
        return ResultUtil.ok();
    }

    public void insertTaskHistory(TTask task){
        TTaskHistory th = new TTaskHistory();
        th.setTaskHisId(IdWorkerUtil.getInstance().nextId());
        th.setTaskId(task.getTaskId());
        th.setTaskType(task.getTaskType());
        th.setTaskTypeNode(task.getTaskTypeNode());
        th.setBusinessType(task.getBusinessType());
        th.setSourceTablename(task.getSourceTablename());
        th.setSourcekeyFieldname(task.getSourcekeyFieldname());
        th.setSourceFieldname(task.getSourceFieldname());
        th.setSourceFieldvalue(task.getSourceFieldvalue());
        th.setRequestUrl(task.getRequestUrl());
        th.setRequestParameter(task.getRequestParameter());
        th.setRequestTimes(task.getRequestTimes());
        th.setRequestResult(task.getRequestResult());
        th.setErrorMessage(task.getErrorMessage());
        th.setRequestDate(task.getRequestDate());
        th.setDealTime(task.getDealTime());
        th.setIsSuccessed(task.getIsSuccessed());
        th.setToHisDate(new Date());
        th.setRemark(task.getRemark());
        th.setParam1(task.getParam1());
        th.setParam2(task.getParam2());
        th.setParam3(task.getParam3());
        th.setParam4(task.getParam4());
        th.setCreateUser(task.getCreateUser());
        th.setCreateTime(task.getCreateTime());
        th.setUpdateUser(task.getUpdateUser());
        th.setUpdateTime(task.getUpdateTime());
        th.setEnable(task.getEnable());
        tTaskHistoryMapper.insert(th);
    }

    @Override
    public ResultUtil exCreateTxxy(TTask task) {
        log.info("执行提现生成收款凭证任务开始");
        try {
            VoucherReq voucherReq = JSON.parseObject(task.getRequestParameter(), VoucherReq.class);
            tOrderContractAPI.createTxxy(voucherReq);
            insertTaskHistory(task);
            tTaskMapper.deleteByPrimaryKey(task.getId());
        } catch (Exception e){
            log.error("执行提现生成收款凭证任务失败", e);
            String msg = e.getMessage();
            if (StringUtils.isNotEmpty(msg)){
                throw new RuntimeException(msg);
            }
            throw new RuntimeException(msg);
        }
        log.info("执行提现生成收款凭证任务结束");
        return ResultUtil.ok();

    }

    @Override
    public ResultUtil selectTXXyTask(TTaskVO record) {
        List<TTask> tTasks = tTaskMapper.selectTxxyTask(record);
        ResultUtil.ok(tTasks);
        return ResultUtil.ok(tTasks);
    }


    /**
     * <AUTHOR>
     * @Description 创建数字物流运单
     * @Date 2020/4/17 11:27 下午
     * @Param
     * @return 11:27 下午
    **/
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil createDigitFlowOrder(TTask task) {
        // TODO 查询企业、司机did, 如果不存则发起请求申请
        String requestParameter = task.getRequestParameter();
        JSONObject jsonObject = JSONObject.parseObject(requestParameter);
        WaybillOrderRequestVO waybillOrderRequestVO = JSONUtil.toBean(requestParameter, WaybillOrderRequestVO.class);
        log.info("创建数字物流运单:{}", JSON.toJSONString(waybillOrderRequestVO));
        Integer companyId = waybillOrderRequestVO.getCompanyId();
        Integer enduserId = waybillOrderRequestVO.getEnduserId();
        String platformDid = waybillOrderRequestVO.getPlatformDid();
        String companyDid = "";
        String driverDid = "";
        try {
            ResultUtil resultUtil = commonMemberUtilAPI.selectCompanyAndEnduserDid(companyId, enduserId, platformDid);
            LinkedHashMap data = (LinkedHashMap) resultUtil.getData();
            if (null != data.get("companyDid")) {
                companyDid = String.valueOf(data.get("companyDid"));
            }
            if (null != data.get("driverDid")) {
                driverDid = String.valueOf(data.get("driverDid"));
            }
        } catch (Exception e) {
            String message = e.getMessage();
            if (message.contains("申请司机did信息异常")) {
                // 修改司机上报状态
                TEndUserInfo endUserInfo = new TEndUserInfo();
                endUserInfo.setId(enduserId);
                endUserInfo.setUploadedStatus(DigitlFlowEnum.NOTPASS.code);
                endUserInfo.setUploadedTime(new Date());
                //endSUserInfoAPI.updateEnduserUploadedInfo(endUserInfo);
            }
            if (message.contains("申请货主did信息异常")) {
                // 修改企业上报状态
                TCompanyInfo companyInfo = new TCompanyInfo();
                companyInfo.setId(companyId);
                companyInfo.setUploadedStatus(DigitlFlowEnum.NOTPASS.code);
                companyInfo.setUploadedTime(new Date());
                companyInfo.setUpdateTime(new Date());
                //companyService.updateCompanyUploadedInfo(companyInfo);
            }
            throw new RuntimeException(message);
        }


        try {
            // 货源订单参数
            CreateCargoOrderRequest cargoOrderRequest = createCargoOrderRequestParam(waybillOrderRequestVO, companyDid);
            log.info("创建货源订单参数{}", JSON.toJSONString(cargoOrderRequest));
            AntCloudProdResponse cargoOrderResponse = MayiDataUtil.reportData(cargoOrderRequest);
            log.info("货源订单创建返回结果{}", JSON.toJSONString(cargoOrderResponse));
            if (cargoOrderResponse.getResultCode().equals(DigitlFlowExceptionEnum.OK.code)) {

            } else if (cargoOrderResponse.getResultCode().equals(DigitlFlowExceptionEnum.SYSTEM_ERROR.code)) {
                throw new RuntimeException("货源订单创建异常，异常码：SYSTEM_ERROR，错误信息：" + cargoOrderResponse.getResultMsg() + "，稍后重试再试");
            } else if (cargoOrderResponse.getResultCode().equals(DigitlFlowExceptionEnum.PRARM_ERROR.code)) {
                throw new RuntimeException("货源订单创建异常，异常码：PRARM_ERROR，错误信息：" + cargoOrderResponse.getResultMsg() + "，检测参数，如果无法判断，请联系接口提供方");
            } else if (!cargoOrderResponse.getResultMsg().contains("运单已经生成")) {
                throw new RuntimeException("货源订单创建异常，异常码：" + cargoOrderResponse.getResultCode() + "， 错信信息：" + cargoOrderResponse.getResultMsg());
            }

            // 创建数字物流运单请求参数
            waybillOrderRequestVO.setDriverDid(driverDid);
            CreateWaybillOrderRequest request = new CreateWaybillOrderRequest();
            BeanUtil.copyProperties(waybillOrderRequestVO, request);
            log.info("创建物流运单，参数{}", JSON.toJSONString(request));
            // TODO 发起创建运单请求
            AntCloudProdResponse antCloudProdResponse = MayiDataUtil.reportData(request);
            log.info("创建物流运单返回结果{}", JSON.toJSONString(antCloudProdResponse));
            if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.OK.code)) {
                // 保存上链记录
                TDigitalLogisticVO record = new TDigitalLogisticVO();
                record.setOrderCode(request.getTaxWaybillId());
                record.setStateNode(DigitlFlowEnum.CreateFlowOrder.code);
                CreateWaybillOrderResponse createWaybillOrderResponse = (CreateWaybillOrderResponse) antCloudProdResponse;
                record.setTxCode(createWaybillOrderResponse.getTxCode());
                record.setTxContent(JSONUtil.toJsonStr(request));
                record.setCreateUser(jsonObject.getString("createUser"));
                digitlFlowOrderAPI.updateDigitalLogistic(record);
            }else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.SYSTEM_ERROR.code)) {
                throw new RuntimeException("创建物流运单异常，异常码：SYSTEM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，稍后重试再试");
            } else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.PRARM_ERROR.code)) {
                throw new RuntimeException("创建物流运单异常，异常码：PRARM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，检测参数，如果无法判断，请联系接口提供方");
            } else {
                throw new RuntimeException("创建物流运单异常，异常码：" + antCloudProdResponse.getResultCode() + "， 错信信息：" + antCloudProdResponse.getResultMsg());
            }

            insertTaskHistory(task);
            tTaskMapper.deleteByPrimaryKey(task.getId());
        } catch (Exception e) {
            log.info("创建物流运单异常{}", e);
            if (StringUtils.checkChineseCharacter(e.getMessage())) {
                throw new RuntimeException(e.getMessage());
            } else {
                throw new RuntimeException("创建物流运单异常");
            }
        }
        return ResultUtil.ok();
    }

    /*
     * <AUTHOR>
     * @Description 构建创建货源订单请求参数
     * @Date 2020/4/21 13:59
     * @Param
     * @return
    **/
    private CreateCargoOrderRequest createCargoOrderRequestParam(WaybillOrderRequestVO waybillOrderRequestVO, String companyDid) {
        // 构建请求
        CreateCargoOrderRequest cargoOrderRequest = new CreateCargoOrderRequest();
        cargoOrderRequest.setAllFreight(String.valueOf(waybillOrderRequestVO.getEstimateGoodsWeight().multiply(waybillOrderRequestVO.getCurrentCarriageUnitPrice())));
        cargoOrderRequest.setCargoName(waybillOrderRequestVO.getGoodsName());
        cargoOrderRequest.setCargoOrder(waybillOrderRequestVO.getCargoOrder());
        cargoOrderRequest.setCargoType(waybillOrderRequestVO.getGoodsName());
        cargoOrderRequest.setConsignorDid(companyDid);
        cargoOrderRequest.setDeliveryPlace(waybillOrderRequestVO.getEndProvinceName());
        cargoOrderRequest.setLoadingPlace(waybillOrderRequestVO.getStartProvinceName());
        cargoOrderRequest.setPlatformDid(waybillOrderRequestVO.getPlatformDid());
        cargoOrderRequest.setStartTime(waybillOrderRequestVO.getStartTime());
        cargoOrderRequest.setWeight(String.valueOf(waybillOrderRequestVO.getEstimateGoodsWeight()));
        return cargoOrderRequest;
    }

    /**
     * <AUTHOR>
     * @Description 创建数字物流运单运输完成
     * @Date 2020/4/23 11:13 上午
     * @Param
     * @return 11:13 上午
    **/
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil createDigitFlowOrderSave(TTask task) {
        String requestParameter = task.getRequestParameter();
        SaveWaybillOrderRequest saveWaybillOrderRequest = JSONUtil.toBean(requestParameter, SaveWaybillOrderRequest.class);
        // 查询承运方信息
        JSONObject jsonObject = JSON.parseObject(requestParameter);
        ResultUtil carrierResult = carrierService.selectById(jsonObject.getString("carrierId"));
        LinkedHashMap carrier = (LinkedHashMap) carrierResult.getData();
        if (null != carrier.get("platformDid")) {
            saveWaybillOrderRequest.setPlatformDid(String.valueOf(carrier.get("platformDid")));
        }
        // 查询车牌
        DigitlFlowMemberDTO digitlFlowMemberDTO = commonMemberUtilAPI.selectDigitlFlowMember(jsonObject.getInteger("carrierId"), null,
                jsonObject.getInteger("driverId"), jsonObject.getInteger("endcarId"));
        saveWaybillOrderRequest.setCartBadgeNo(digitlFlowMemberDTO.getVehicleNumber());
        saveWaybillOrderRequest.setDriverDid(digitlFlowMemberDTO.getDriverDid());

        log.info("创建数字物流运单运输完成, 参数:{}", JSON.toJSONString(saveWaybillOrderRequest));
        try {
            AntCloudProdResponse antCloudProdResponse = MayiDataUtil.reportData(saveWaybillOrderRequest);
            if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.OK.code)) {
                // 保存上链记录
                TDigitalLogisticVO record = new TDigitalLogisticVO();
                record.setOrderCode(saveWaybillOrderRequest.getTaxWaybillId());
                record.setStateNode(DigitlFlowEnum.TransportOrder.code);
                SaveWaybillOrderResponse createWaybillOrderResponse = (SaveWaybillOrderResponse) antCloudProdResponse;
                record.setTxCode(createWaybillOrderResponse.getTxCode());
                record.setTxContent(JSONUtil.toJsonStr(saveWaybillOrderRequest));
                record.setCreateUser(jsonObject.getString("createUser"));
                digitlFlowOrderAPI.insertDigitalLogistic(record);
            }else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.SYSTEM_ERROR.code)) {
                throw new RuntimeException("创建物流运单异常，异常码：SYSTEM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，稍后重试再试");
            } else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.PRARM_ERROR.code)) {
                throw new RuntimeException("创建物流运单异常，异常码：PRARM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，检测参数，如果无法判断，请联系接口提供方");
            } else {
                throw new RuntimeException("创建物流运单异常，异常码：" + antCloudProdResponse.getResultCode() + "， 错信信息：" + antCloudProdResponse.getResultMsg());
            }
            insertTaskHistory(task);
            tTaskMapper.deleteByPrimaryKey(task.getId());

            // 创建第三个位置信息上传任务
            // 添加创建数字物流任务：第三方位置信息上报
            Map<String, Object> param = new HashMap<>();
            param.put("carrierId", jsonObject.getString("carrierId"));
            param.put("orderCode", saveWaybillOrderRequest.getTaxWaybillId());
            param.put("createUser", jsonObject.getString("createUser"));
            createDigitOrderTask(saveWaybillOrderRequest.getTaxWaybillId(), "blockchain", DigitlFlowEnum.LocationImport.code,
                    param, "添加第三方位置信息上报任务");
        } catch (Exception e) {
            log.info("创建数字物流运单运输完成异常{}", e);
            if (StringUtils.checkChineseCharacter(e.getMessage())) {
                throw new RuntimeException(e.getMessage());
            } else {
                throw new RuntimeException("创建数字物流运单运输完成异常");
            }
        }
        return ResultUtil.ok();
    }

    /**
     * <AUTHOR>
     * @Description 添加创建数字物流任务
     * @Date 2020/4/17 3:36 下午
     * @Param
     * @return 3:36 下午
     **/
    private void createDigitOrderTask(String code, String taskType, String taskTypeNode, Map<String, Object> param, String remark) {
        TTask task = new TTask();
        task.setTaskType(taskType);
        task.setTaskTypeNode(taskTypeNode);
        task.setBusinessType("dis");
        task.setTaskId(IdWorkerUtil.getInstance().nextId());
        task.setSourceTablename("t_order_info");
        task.setSourcekeyFieldname("id");
        task.setSourceFieldname("code");
        task.setSourceFieldvalue(code);
        task.setRequestTimes(0);
        if (null != param) {
            task.setRequestParameter(JSON.toJSONString(param));
        }
        task.setRequestDate(new Date());
        task.setIsSuccessed(false);
        task.setRemark(remark);
        task.setEnable(false);
        task.setCreateTime(new Date());
        tTaskMapper.insert(task);
    }

    /**
     * <AUTHOR>
     * @Description 货源支付订单创建、运单支付订单创建
     * @Date 2020/4/23 2:38 下午
     * @Param
     * @return 2:38 下午
    **/
    @Transactional
    @Override
    public ResultUtil createSourceOrderPay(TTask task) {
        String requestParameter = task.getRequestParameter();
        CreateSourceOrderPayRequestVO requestVO = JSONUtil.toBean(requestParameter, CreateSourceOrderPayRequestVO.class);

        // 查询did,户名
        DigitlFlowMemberDTO digitlFlowMemberDTO = commonMemberUtilAPI.selectDigitlFlowMember(requestVO.getCarrierId(), requestVO.getCompanyId(),
                requestVO.getEndDriverId(), null);
        log.info("查询did，户名信息{}", JSON.toJSONString(digitlFlowMemberDTO));

        try {
            //货源支付订单创建
            createCargoPay(requestVO, digitlFlowMemberDTO);

            //运单支付
            createOrderPay(requestVO, digitlFlowMemberDTO);

            insertTaskHistory(task);
            tTaskMapper.deleteByPrimaryKey(task.getId());
        } catch (Exception e) {
            log.info("创建货源支付订单失败: {}", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("创建货源支付订单失败");
            }
        }

        return ResultUtil.ok();
    }

    /**
     * <AUTHOR>
     * @Description 货源支付订单创建：运费、调度费
     * @Date 2020/4/24 2:10 下午
     * @Param
     * @return 2:10 下午
    **/
    private void createCargoPay(CreateSourceOrderPayRequestVO requestVO, DigitlFlowMemberDTO digitlFlowMemberDTO) throws Exception {
        CreateCargoPayorderRequest cargoPayorderRequest = new CreateCargoPayorderRequest();
        BeanUtil.copyProperties(requestVO, cargoPayorderRequest);
        // 运费
        if (null != requestVO.getCarriageFee()
                && requestVO.getCarriageFee().compareTo(BigDecimal.ZERO) > 0) {
            cargoPayorderRequest.setPayment(String.valueOf(requestVO.getCarriageFee()));
            cargoPayorderRequest.setExpenseType(DigitlFlowEnum.CARRIAGEFEE.code);
            cargoPayorderRequest.setPayDid(digitlFlowMemberDTO.getCompanyDid());
            cargoPayorderRequest.setRecvDid(digitlFlowMemberDTO.getPlatformDid());
            cargoPayorderRequest.setPlatformDid(digitlFlowMemberDTO.getPlatformDid());
            cargoPayorderRequest.setTaxWaybillId(cargoPayorderRequest.getTaxWaybillId());
            log.info("货源支付订单创建，运费，参数：{}", JSON.toJSONString(cargoPayorderRequest));
            AntCloudProdResponse antCloudProdResponse = MayiDataUtil.reportData(cargoPayorderRequest);
            log.info("货源支付订单创建，运费，返回结果：{}", JSON.toJSONString(antCloudProdResponse));
            if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.OK.code)) {

            }else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.SYSTEM_ERROR.code)) {
                throw new RuntimeException("货源支付订单创建，运费,异常，异常码：SYSTEM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，稍后重试再试");
            } else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.PRARM_ERROR.code)) {
                throw new RuntimeException("货源支付订单创建，运费,异常，异常码：PRARM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，检测参数，如果无法判断，请联系接口提供方");
            } else {
                throw new RuntimeException("货源支付订单创建，运费,异常，异常码：" + antCloudProdResponse.getResultCode() + "， 错信信息：" + antCloudProdResponse.getResultMsg());
            }
        }

        // 调度费
        if (null != requestVO.getDispatchFee()
                && requestVO.getDispatchFee().compareTo(BigDecimal.ZERO) > 0) {
            cargoPayorderRequest.setPayment(String.valueOf(requestVO.getDispatchFee()));
            cargoPayorderRequest.setExpenseType(DigitlFlowEnum.DISPATCHFEE.code);
            cargoPayorderRequest.setPayDid(digitlFlowMemberDTO.getCompanyDid());
            cargoPayorderRequest.setRecvDid(digitlFlowMemberDTO.getPlatformDid());
            cargoPayorderRequest.setPlatformDid(digitlFlowMemberDTO.getPlatformDid());
            log.info("货源支付订单创建，调度费，参数：{}", JSON.toJSONString(cargoPayorderRequest));
            AntCloudProdResponse antCloudProdResponse = MayiDataUtil.reportData(cargoPayorderRequest);
            log.info("货源支付订单创建，调度费，返回结果：{}", JSON.toJSONString(antCloudProdResponse));
            if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.OK.code)) {

            }else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.SYSTEM_ERROR.code)) {
                throw new RuntimeException("货源支付订单创建，调度费,异常，异常码：SYSTEM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，稍后重试再试");
            } else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.PRARM_ERROR.code)) {
                throw new RuntimeException("货源支付订单创建，调度费,异常，异常码：PRARM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，检测参数，如果无法判断，请联系接口提供方");
            } else {
                throw new RuntimeException("货源支付订单创建，调度费,异常，异常码：" + antCloudProdResponse.getResultCode() + "， 错信信息：" + antCloudProdResponse.getResultMsg());
            }
        }
    }

    /**
     * <AUTHOR>
     * @Description 运单支付订单创建
     * @Date 2020/4/24 4:00 下午
     * @Param
     * @return 4:00 下午
     **/
    private void createOrderPay(CreateSourceOrderPayRequestVO requestVO, DigitlFlowMemberDTO digitlFlowMemberDTO) throws Exception {
        CreateWaybillPayRequest waybillPayRequest = new CreateWaybillPayRequest();
        BeanUtil.copyProperties(requestVO, waybillPayRequest);

        // 运费
        if (null != requestVO.getCarriageFee() && requestVO.getCarriageFee().compareTo(BigDecimal.ZERO) > 0) {
            waybillPayRequest.setPayAmount(String.valueOf(requestVO.getCarriageFee()));
            waybillPayRequest.setPayBankCardNo(digitlFlowMemberDTO.getCompanyUid());
            waybillPayRequest.setPayBankName("网商银行");
            waybillPayRequest.setPayDid(digitlFlowMemberDTO.getCompanyDid());
            waybillPayRequest.setPayName(StringUtils.splitCompanyNameByEnBrackets(digitlFlowMemberDTO.getCompanyName()));
            waybillPayRequest.setPlatformDid(digitlFlowMemberDTO.getPlatformDid());
            waybillPayRequest.setRealPayBank("网商银行");
            waybillPayRequest.setRealPayBankCardNo(digitlFlowMemberDTO.getCompanyDid());
            waybillPayRequest.setRealPayName(digitlFlowMemberDTO.getCompanyName());
            waybillPayRequest.setRecvBankName("网商银行");
            waybillPayRequest.setRecvBankCardNo(digitlFlowMemberDTO.getDriverUid());
            waybillPayRequest.setRecvDid(digitlFlowMemberDTO.getDriverDid());
            waybillPayRequest.setRecvName(digitlFlowMemberDTO.getDriverName());
            log.info("运单支付订单创建，运费，参数：{}", JSON.toJSONString(waybillPayRequest));
            AntCloudProdResponse antCloudProdResponse = MayiDataUtil.reportData(waybillPayRequest);
            log.info("运单支付订单创建，运费，返回结果：{}", JSON.toJSONString(antCloudProdResponse));
            if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.OK.code)) {

            }else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.SYSTEM_ERROR.code)) {
                throw new RuntimeException("运单支付订单创建，运费,异常，异常码：SYSTEM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，稍后重试再试");
            } else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.PRARM_ERROR.code)) {
                throw new RuntimeException("运单支付订单创建，运费,异常，异常码：PRARM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，检测参数，如果无法判断，请联系接口提供方");
            } else {
                throw new RuntimeException("运单支付订单创建，运费,异常，异常码：" + antCloudProdResponse.getResultCode() + "， 错信信息：" + antCloudProdResponse.getResultMsg());
            }
        }
        // 调度费
        if (null != requestVO.getCarriageFee() && requestVO.getCarriageFee().compareTo(BigDecimal.ZERO) > 0) {
            waybillPayRequest.setPayAmount(String.valueOf(requestVO.getDispatchFee()));
            waybillPayRequest.setPayBankCardNo(digitlFlowMemberDTO.getCompanyUid());
            waybillPayRequest.setPayBankName("网商银行");
            waybillPayRequest.setPayDid(digitlFlowMemberDTO.getCompanyDid());
            waybillPayRequest.setPayName(digitlFlowMemberDTO.getCompanyName());
            waybillPayRequest.setPlatformDid(digitlFlowMemberDTO.getPlatformDid());
            waybillPayRequest.setRealPayBank("网商银行");
            waybillPayRequest.setRealPayBankCardNo(digitlFlowMemberDTO.getCompanyDid());
            waybillPayRequest.setRealPayName(StringUtils.splitCompanyNameByEnBrackets(digitlFlowMemberDTO.getCompanyName()));
            waybillPayRequest.setRecvBankName("网商银行");
            waybillPayRequest.setRecvBankCardNo(digitlFlowMemberDTO.getCarrierUid());
            waybillPayRequest.setRecvDid(digitlFlowMemberDTO.getPlatformDid());
            waybillPayRequest.setRecvName(digitlFlowMemberDTO.getCarrierName());
            log.info("运单支付订单创建，调度费，参数：{}", JSON.toJSONString(waybillPayRequest));
            AntCloudProdResponse antCloudProdResponse = MayiDataUtil.reportData(waybillPayRequest);
            log.info("运单支付订单创建，调度费，返回结果：{}", JSON.toJSONString(antCloudProdResponse));
            if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.OK.code)) {

            }else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.SYSTEM_ERROR.code)) {
                throw new RuntimeException("运单支付订单创建，调度费，异常，异常码：SYSTEM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，稍后重试再试");
            } else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.PRARM_ERROR.code)) {
                throw new RuntimeException("运单支付订单创建，调度费，异常，异常码：PRARM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，检测参数，如果无法判断，请联系接口提供方");
            } else {
                throw new RuntimeException("运单支付订单创建，调度费，异常，异常码：" + antCloudProdResponse.getResultCode() + "， 错信信息：" + antCloudProdResponse.getResultMsg());
            }
        }
    }

    /**
     * <AUTHOR>
     * @Description 运单完成
     * @Date 2020/4/24 5:49 下午
     * @Param
     * @return 5:49 下午
    **/
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil createOrderFinish(TTask task) {

        try {
            String requestParameter = task.getRequestParameter();
            JSONObject jsonObject = JSON.parseObject(requestParameter);

            //查询货源信息
            ResultUtil resultUtil = goodsSourceAPI.selectGoodsSourceBySourceCode(jsonObject.getString("goodSourceCode"));
            LinkedHashMap source = (LinkedHashMap) resultUtil.getData();
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            CompanySourceDTO companySourceDTO = objectMapper.convertValue(source, CompanySourceDTO.class);
            log.info("运单完成，货源信息：{}", JSON.toJSONString(companySourceDTO));

            // 查询承运方、企业、司机、车牌
            // 查询did,户名
            Integer carrierId = jsonObject.getInteger("carrierId");
            Integer endcarId = jsonObject.getInteger("endcarId");
            Integer companyId = jsonObject.getInteger("companyId");
            Integer endDriverId = jsonObject.getInteger("endDriverId");
            DigitlFlowMemberDTO digitlFlowMemberDTO = commonMemberUtilAPI.selectDigitlFlowMember(carrierId, companyId,
                    endDriverId, endcarId);
            log.info("查询did，户名信息{}", JSON.toJSONString(digitlFlowMemberDTO));

            // 请求运单完成
            orderFinish(requestParameter, companySourceDTO, digitlFlowMemberDTO);

            insertTaskHistory(task);
            tTaskMapper.deleteByPrimaryKey(task.getId());
        } catch (Exception e) {
            log.info("创建运单完成失败: {}", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("创建运单完成失败");
            }
        }

        return ResultUtil.ok();
    }

    /**
     * <AUTHOR>
     * @Description 请求运单完成
     * @Date 2020/4/25 9:49 上午
     * @Param
     * @return 9:49 上午
    **/
    private void orderFinish(String requestParameter, CompanySourceDTO companySourceDTO, DigitlFlowMemberDTO digitlFlowMemberDTO) throws Exception {
        FinishWaybillOrderRequest request = JSONUtil.toBean(requestParameter, FinishWaybillOrderRequest.class);
        request.setPlatformDid(digitlFlowMemberDTO.getPlatformDid());
        request.setDrawee(digitlFlowMemberDTO.getCompanyName());
        request.setDraweeTaxNo(digitlFlowMemberDTO.getTaxNo());
        request.setDriverDid(digitlFlowMemberDTO.getDriverDid());
        request.setCartBadgeNo(digitlFlowMemberDTO.getVehicleNumber());
        request.setDrawee(StringUtils.splitCompanyNameByEnBrackets(digitlFlowMemberDTO.getCompanyName()));
        request.setDraweeTaxNo(companySourceDTO.getCompanyTaxNo());
        request.setDriverDid(digitlFlowMemberDTO.getDriverDid());
        request.setGoodsName(companySourceDTO.getGoodsName());
        request.setPlatformDid(digitlFlowMemberDTO.getPlatformDid());
        request.setStartCityCode(companySourceDTO.getCityFromCode().split(",")[1]);
        request.setStartCityName(companySourceDTO.getCityFrom());
        request.setStartDivisionCode(companySourceDTO.getCityFromCode().split(",")[1] + "000000");
        request.setStartProvinceCode(companySourceDTO.getCityFromCode().split(",")[0]);
        request.setStartProvinceName(companySourceDTO.getProviceFrom());
        request.setEndCityCode(companySourceDTO.getCityEndCode().split(",")[1]);
        request.setEndCityName(companySourceDTO.getCityEnd());
        request.setEndDivisionCode(companySourceDTO.getCityEndCode().split(",")[1] + "000000");
        request.setEndProvinceCode(companySourceDTO.getCityEndCode().split(",")[0]);
        request.setEndProvinceName(companySourceDTO.getProviceEnd());
        log.info("运单完成，参数：{}", JSON.toJSONString(request));
        AntCloudProdResponse antCloudProdResponse = MayiDataUtil.reportData(request);
        log.info("运单完成，返回结果：{}", JSON.toJSONString(antCloudProdResponse));
        if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.OK.code)) {
            // 保存上链记录
            TDigitalLogisticVO record = new TDigitalLogisticVO();
            record.setOrderCode(request.getTaxWaybillId());
            record.setStateNode(DigitlFlowEnum.FinishedOrder.code);
            FinishWaybillOrderResponse createWaybillOrderResponse = (FinishWaybillOrderResponse) antCloudProdResponse;
            record.setTxCode(createWaybillOrderResponse.getTxCode());
            record.setTxContent(JSONUtil.toJsonStr(request));
            JSONObject jsonObject = JSONObject.parseObject(requestParameter);
            record.setCreateUser(jsonObject.getString("createUser"));
            digitlFlowOrderAPI.insertDigitalLogistic(record);

        }else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.SYSTEM_ERROR.code)) {
            throw new RuntimeException("运单完成，异常，异常码：SYSTEM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，稍后重试再试");
        } else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.PRARM_ERROR.code)) {
            throw new RuntimeException("运单完成，异常，异常码：PRARM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，检测参数，如果无法判断，请联系接口提供方");
        } else {
            throw new RuntimeException("运单完成，异常，异常码：" + antCloudProdResponse.getResultCode() + "， 错信信息：" + antCloudProdResponse.getResultMsg());
        }
    }

    /**
     * <AUTHOR>
     * @Description 运单关闭
     * @Date 2020/4/25 9:50 上午
     * @Param
     * @return 9:50 上午
    **/
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil closedOrder(TTask task) {
        try {
            String requestParameter = task.getRequestParameter();
            JSONObject jsonObject = JSON.parseObject(requestParameter);

            Integer carrierId = jsonObject.getInteger("carrierId");
            DigitlFlowMemberDTO digitlFlowMemberDTO = commonMemberUtilAPI.selectDigitlFlowMember(carrierId, null, null, null);
            CloseWaybillOrderRequest request = new CloseWaybillOrderRequest();
            request.setPlatformDid(digitlFlowMemberDTO.getPlatformDid());
            request.setTaxWaybillId(jsonObject.getString("code"));
            log.info("运单关闭，参数：{}", JSON.toJSONString(request));
            AntCloudProdResponse antCloudProdResponse = MayiDataUtil.reportData(request);
            log.info("运单关闭，返回结果：{}", JSON.toJSONString(antCloudProdResponse));
            if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.OK.code)) {
                // 保存上链记录
                TDigitalLogisticVO record = new TDigitalLogisticVO();
                record.setOrderCode(request.getTaxWaybillId());
                record.setStateNode(DigitlFlowEnum.ClosedOrder.code);
                CloseWaybillOrderResponse response = (CloseWaybillOrderResponse) antCloudProdResponse;
                record.setTxCode(response.getTxCode());
                record.setTxContent(JSONUtil.toJsonStr(request));
                record.setCreateUser(jsonObject.getString("createUser"));
                digitlFlowOrderAPI.updateDigitalLogistic(record);
            }else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.SYSTEM_ERROR.code)) {
                throw new RuntimeException("运单关闭，异常，异常码：SYSTEM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，稍后重试再试");
            } else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.PRARM_ERROR.code)) {
                throw new RuntimeException("运单关闭，异常，异常码：PRARM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，检测参数，如果无法判断，请联系接口提供方");
            } else {
                throw new RuntimeException("运单关闭，异常，异常码：" + antCloudProdResponse.getResultCode() + "， 错信信息：" + antCloudProdResponse.getResultMsg());
            }

            insertTaskHistory(task);
            tTaskMapper.deleteByPrimaryKey(task.getId());
        } catch (Exception e) {
            log.info("运单关闭失败: {}", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("运单关闭失败");
            }
        }

        return ResultUtil.ok();
    }

    /**
     * <AUTHOR>
     * @Description 第三方位置信息上报
     * @Date 2020/4/25 10:16 上午
     * @Param
     * @return 10:16 上午
    **/
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil locationImport(TTask task) {
        String requestParameter = task.getRequestParameter();
        JSONObject jsonObject = JSON.parseObject(requestParameter);

        Integer carrierId = jsonObject.getInteger("carrierId");
        String orderCode = jsonObject.getString("orderCode");
        DigitlFlowMemberDTO digitlFlowMemberDTO = commonMemberUtilAPI.selectDigitlFlowMember(carrierId, null, null, null);

        // 查询轨迹信息
        List<LogisticLocation> logisticLocations = vehicleTrajectorMapper.selectVehicleTrajector(orderCode);
        try {
            ImportWaybillLocationRequest request = new ImportWaybillLocationRequest();
            request.setPlatformDid(digitlFlowMemberDTO.getPlatformDid());
            request.setTaxWaybillId(orderCode);
            request.setLocation(logisticLocations);
            AntCloudProdResponse antCloudProdResponse = MayiDataUtil.reportData(request);
            log.info("第三方位置信息上报，返回结果：{}", JSON.toJSONString(antCloudProdResponse));
            if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.OK.code)) {
                // 保存上链记录
                TDigitalLogisticVO record = new TDigitalLogisticVO();
                record.setOrderCode(request.getTaxWaybillId());
                record.setStateNode(DigitlFlowEnum.LocationImport.code);
                record.setCreateUser(jsonObject.getString("createUser"));
                digitlFlowOrderAPI.insertDigitalLogistic(record);

            }else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.SYSTEM_ERROR.code)) {
                throw new RuntimeException("第三方位置信息上报，异常，异常码：SYSTEM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，稍后重试再试");
            } else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.PRARM_ERROR.code)) {
                throw new RuntimeException("第三方位置信息上报，异常，异常码：PRARM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，检测参数，如果无法判断，请联系接口提供方");
            } else {
                throw new RuntimeException("第三方位置信息上报，异常，异常码：" + antCloudProdResponse.getResultCode() + "， 错信信息：" + antCloudProdResponse.getResultMsg());
            }

            insertTaskHistory(task);
            tTaskMapper.deleteByPrimaryKey(task.getId());
        } catch (Exception e) {
            log.info("第三方位置信息上报失败: {}", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("第三方位置信息上报失败");
            }
        }

        return ResultUtil.ok();
    }

    /**
     * <AUTHOR>
     * @Description 起运运单
     * @Date 2020/4/30 4:40 下午
     * @Param
     * @return 4:40 下午
    **/
    @LcnTransaction
    @Override
    public ResultUtil orderStart(TTask task) {
        try {
            String requestParameter = task.getRequestParameter();
            StartFinanceWaybillRequest request = JSONUtil.toBean(requestParameter, StartFinanceWaybillRequest.class);
            JSONObject jsonObject = JSON.parseObject(requestParameter);

            Integer carrierId = jsonObject.getInteger("carrierId");
            Integer endDriverId = jsonObject.getInteger("endDriverId");
            DigitlFlowMemberDTO digitlFlowMemberDTO = commonMemberUtilAPI.selectDigitlFlowMember(carrierId, null, endDriverId, null);
            request.setDriverDid(digitlFlowMemberDTO.getDriverDid());
            request.setPlatformDid(digitlFlowMemberDTO.getPlatformDid());
            log.info("起运运单，参数：{}", JSON.toJSONString(request));
            AntCloudProdResponse antCloudProdResponse = MayiDataUtil.reportData(request);
            log.info("起运运单，返回结果：{}", JSON.toJSONString(antCloudProdResponse));
            if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.OK.code)) {
                // 保存上链记录
                TDigitalLogisticVO record = new TDigitalLogisticVO();
                record.setOrderCode(request.getTaxWaybillId());
                record.setStateNode(DigitlFlowEnum.OrderStartShip.code);
                record.setTxContent(JSONUtil.toJsonStr(request));
                record.setCreateUser(jsonObject.getString("createUser"));
                StartFinanceWaybillResponse response = (StartFinanceWaybillResponse) antCloudProdResponse;
                record.setTxCode(response.getTxCode());
                digitlFlowOrderAPI.updateDigitalLogistic(record);

            }else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.SYSTEM_ERROR.code)) {
                throw new RuntimeException("起运运单，异常，异常码：SYSTEM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，稍后重试再试");
            } else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.PRARM_ERROR.code)) {
                throw new RuntimeException("起运运单，异常，异常码：PRARM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，检测参数，如果无法判断，请联系接口提供方");
            } else {
                throw new RuntimeException("起运运单，异常，异常码：" + antCloudProdResponse.getResultCode() + "， 错信信息：" + antCloudProdResponse.getResultMsg());
            }

            insertTaskHistory(task);
            tTaskMapper.deleteByPrimaryKey(task.getId());
        } catch (Exception e) {
            log.info("起运运单失败: {}", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("起运运单失败");
            }
        }

        return ResultUtil.ok();
    }

    /**
     * <AUTHOR>
     * @Description
     * @Date
     * @Param
     * @return
    **/
    @LcnTransaction
    @Transactional
    @Override
    public ResultUtil orderUpdate(TTask task) {
        try {
            String requestParameter = task.getRequestParameter();
            UpdateFinanceWaybillRequest request = JSONUtil.toBean(requestParameter, UpdateFinanceWaybillRequest.class);
            JSONObject jsonObject = JSON.parseObject(requestParameter);
            DigitlFlowMemberDTO digitlFlowMemberDTO = commonMemberUtilAPI.selectDigitlFlowMember(jsonObject.getInteger("carrierId"), null, null, null);
            request.setPlatformDid(digitlFlowMemberDTO.getPlatformDid());
            AntCloudProdResponse antCloudProdResponse = MayiDataUtil.reportData(request);
            log.info("运单更新，返回结果：{}", JSON.toJSONString(antCloudProdResponse));
            if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.OK.code)) {
                // 保存上链记录
                TDigitalLogisticVO record = new TDigitalLogisticVO();
                record.setOrderCode(jsonObject.getString("orderCode"));
                record.setStateNode(DigitlFlowEnum.OrderUpdate.code);
                record.setTxContent(JSONUtil.toJsonStr(request));
                record.setCreateUser(jsonObject.getString("createUser"));
                UpdateFinanceWaybillResponse response = (UpdateFinanceWaybillResponse) antCloudProdResponse;
                record.setTxCode(response.getTxCode());
                digitlFlowOrderAPI.insertDigitalLogistic(record);
            }else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.SYSTEM_ERROR.code)) {
                throw new RuntimeException("运单更新，异常，异常码：SYSTEM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，稍后重试再试");
            } else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.PRARM_ERROR.code)) {
                throw new RuntimeException("运单更新，异常，异常码：PRARM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，检测参数，如果无法判断，请联系接口提供方");
            } else {
                throw new RuntimeException("运单更新，异常，异常码：" + antCloudProdResponse.getResultCode() + "， 错信信息：" + antCloudProdResponse.getResultMsg());
            }

            insertTaskHistory(task);
            tTaskMapper.deleteByPrimaryKey(task.getId());
        } catch (Exception e) {
            log.info("运单更新失败: {}", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("运单更新失败");
            }
        }

        return ResultUtil.ok();
    }

    @Override
    public List<TTask> selectTaskByModel(TTask task) {
        return tTaskMapper.selectTaskByModel(task);
    }

    @Override
    public Boolean selectTaskAndHistory(TTask task) {
        List<TTask> tTasks = tTaskMapper.selectTaskByModel(task);
        List<TTaskHistory> tTaskHistories = tTaskHistoryMapper.selectTaskHistoryByModel(task);

        return (tTasks.isEmpty() && tTaskHistories.isEmpty()) && tTasks.isEmpty() && tTaskHistories.isEmpty();
    }

    /**
     * <AUTHOR>
     * @Description 发票订单创建
     * @Date 2020/5/14 3:07 下午
     * @Param
     * @return
    **/
    @Transactional
    @Override
    public ResultUtil billCreate(TTask task) {
        try {
            String requestParameter = task.getRequestParameter();
            JSONObject jsonObject = JSONObject.parseObject(requestParameter);

            TOrderInfo orderInfo = tOrderInfoAPI.selectOrderInfoByCode(jsonObject.getString("code"));

            DigitlFlowMemberDTO digitlFlowMemberDTO = commonMemberUtilAPI.selectDigitlFlowMember(orderInfo.getCarrierId(), orderInfo.getCompanyId(), null, null);
            CreateWaybillBillRequest request = new CreateWaybillBillRequest();
            request.setPlatformDid(digitlFlowMemberDTO.getPlatformDid());
            request.setWaybillId(orderInfo.getCode());
            request.setOpenTime(jsonObject.getLong("operTime"));
            request.setDrawee(digitlFlowMemberDTO.getCompanyName());
            request.setDraweeTaxNo(digitlFlowMemberDTO.getTaxNo());
            AntCloudProdResponse antCloudProdResponse = MayiDataUtil.reportData(request);
            log.info("发票订单创建，返回结果：{}", JSON.toJSONString(antCloudProdResponse));
            if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.OK.code)) {
                // 保存上链记录
                TDigitalLogisticVO record = new TDigitalLogisticVO();
                record.setOrderCode(jsonObject.getString("code"));
                record.setStateNode(DigitlFlowEnum.BillCreate.code);
                record.setTxContent(JSONUtil.toJsonStr(request));
                record.setCreateUser(jsonObject.getString("createUser"));
                CreateWaybillBillResponse response = (CreateWaybillBillResponse) antCloudProdResponse;
                record.setTxCode(response.getTxCode());
                digitlFlowOrderAPI.insertDigitalLogistic(record);
            }else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.SYSTEM_ERROR.code)) {
                throw new RuntimeException("发票订单创建，异常，异常码：SYSTEM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，稍后重试再试");
            } else if (antCloudProdResponse.getResultCode().equals(DigitlFlowExceptionEnum.PRARM_ERROR.code)) {
                throw new RuntimeException("发票订单创建，异常，异常码：PRARM_ERROR，错误信息：" + antCloudProdResponse.getResultMsg() + "，检测参数，如果无法判断，请联系接口提供方");
            } else {
                throw new RuntimeException("发票订单创建，异常，异常码：" + antCloudProdResponse.getResultCode() + "， 错信信息：" + antCloudProdResponse.getResultMsg());
            }

            insertTaskHistory(task);
            tTaskMapper.deleteByPrimaryKey(task.getId());
        } catch (Exception e) {
            log.info("发票订单创建失败: {}", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)) {
                throw new RuntimeException(message);
            } else {
                throw new RuntimeException("发票订单创建失败");
            }
        }

        return ResultUtil.ok();
    }

    /*
     * <AUTHOR>
     * @Description 查询数字物流任务
     * @Date 2020/7/6 14:43
     * @Param
     * @return
    **/
    @Override
    public ResultUtil selectDigitalTask(List<String> taskTypeNode, String taskType, String businessType, String stateNode) {
        List<TTask> tTasks = tTaskMapper.selectDigitalTask(taskTypeNode, taskType, businessType, stateNode);
        return ResultUtil.ok(tTasks);
    }

    @Transactional
    @Override
    public void InstantR(TTask task) {
        log.info("发起分润入账开始");
        try {
            InstantRequest request = JSON.parseObject(task.getRequestParameter(), InstantRequest.class);
            ResultUtil yfResult = null;
            if (task.getTaskTypeNode().equals("CWRZR")) {
                yfResult = instantJobUtil.CWRZR(request);
            }

            if (null != yfResult) {
                JSONObject jo = JSONObject.parseObject(yfResult.getData().toString());
                if (jo.getString("is_success").equals("F")) {
                    String errorMessage = "运费入账异常-" + jo.getString("error_message");
                    throw new RuntimeException(errorMessage);
                }
            }
            insertTaskHistory(task);
            tTaskMapper.deleteByPrimaryKey(task.getId());
        } catch (Exception e) {
            log.error("发起分润入账失败", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                throw new RuntimeException(message);
            }
            throw new RuntimeException("发起分润入账失败");
        }
        log.info("发起分润入账结束");
    }


    @Transactional
    @Override
    public void InstantRNode(TTask task) {
        log.info("节点支付"+task.getRemark()+":发起分润入账开始");
        try {
            InstantRequest request = JSON.parseObject(task.getRequestParameter(), InstantRequest.class);
            ResultUtil yfResult = null;
            if (task.getTaskTypeNode().equals("NODECWRZR")) {
                yfResult = instantJobUtil.NODECWRZR(request,task);
            }

            if (null != yfResult) {
                JSONObject jo = JSONObject.parseObject(yfResult.getData().toString());
                if (jo.getString("is_success").equals("F")) {
                    String errorMessage = "运费入账异常-" + jo.getString("error_message");
                    throw new RuntimeException(errorMessage);
                }
            }
            insertTaskHistory(task);
            tTaskMapper.deleteByPrimaryKey(task.getId());
        } catch (Exception e) {
            log.error("发起分润入账失败", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                throw new RuntimeException(message);
            }
            throw new RuntimeException("发起分润入账失败");
        }
        log.info("发起分润入账结束");
    }

    @Transactional
    @Override
    public void refund(TTask task) {
        try {
            InstantRequest request = JSON.parseObject(task.getRequestParameter(), InstantRequest.class);
            instantJobUtil.refund(request);
            insertTaskHistory(task);
            tTaskMapper.deleteByPrimaryKey(task.getId());
        } catch (Exception e) {
            log.error("发起退款入账失败", e);
            String message = e.getMessage();
            if (StringUtils.checkChineseCharacter(message)){
                throw new RuntimeException(message);
            }
            throw new RuntimeException("发起退款入账失败");
        }
    }

    @Override
    public YimeiResult DataSendToYiMei(TTask task) throws Exception {
        String url = ymPath;
        String req = task.getRequestParameter();
        HttpClient httpClient = new DefaultHttpClient();
        HttpPost request = new HttpPost(url);
        StringEntity requestEntity = new StringEntity(req, "utf-8");
        requestEntity.setContentEncoding("UTF-8");
        request.setHeader("Content-type", "application/json");
        request.setHeader("Authorization","Bearer 8FBF8B07938D620168785DBD354A665A");
        request.setEntity(requestEntity);
        HttpResponse httpResponse = httpClient.execute(request);
        if (httpResponse.getStatusLine().getStatusCode() != 200) {
            throw new Exception();
        }
        String json = HttpUtils.bytesToString(HttpUtils.getData(httpResponse.getEntity()));

        YimeiResult r =JSON.parseObject(json, YimeiResult.class);
        if (r.getSuccess()){
            TTaskHistory th = new TTaskHistory();
            th.setTaskHisId(IdWorkerUtil.getInstance().nextId());
            th.setTaskId(task.getTaskId());
            th.setTaskType(task.getTaskType());
            th.setTaskTypeNode(task.getTaskTypeNode());
            th.setBusinessType(task.getBusinessType());
            th.setRequestParameter(task.getRequestParameter());
            th.setRequestTimes(task.getRequestTimes());
            th.setCreateUser(task.getCreateUser());
            th.setCreateTime(task.getCreateTime());
            th.setUpdateUser(task.getUpdateUser());
            th.setUpdateTime(task.getUpdateTime());
            th.setEnable(task.getEnable());
            tTaskHistoryMapper.insert(th);
            tTaskMapper.deleteByPrimaryKey(task.getId());
        }
        return r;

    }

    @Override
    public ResultData CarDataSendToEtc(TTask task) throws Exception {
        String host = etcPath;
        String path = "/api/waybillInfo/vehicleRegister";
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        Map<String, Object> querys = new HashMap<>();
        String req = task.getRequestParameter();
        log.info("etc车辆备案入参：{}", JSON.toJSONString(req));
        Map mapTypes = JSON.parseObject(req);
        HttpResponse response = HttpUtils.doPost(host, path, headers, querys, mapTypes);
        System.out.println(response.toString());
        log.info("etc车辆备案回参Str：{}", response);
        //获取response的body
        String json = HttpUtils.bytesToString(HttpUtils.getData(response.getEntity()));
        log.info("etc车辆备案回参Str：{}", json);
        ResultData jso =  JSON.parseObject(json, ResultData.class);
        log.info("etc车辆备案回参：{}", JSON.toJSONString(jso));
        if (jso.getRcode().equals("200")){
            TTaskHistory th = new TTaskHistory();
            th.setTaskHisId(IdWorkerUtil.getInstance().nextId());
            th.setTaskId(task.getTaskId());
            th.setTaskType(task.getTaskType());
            th.setTaskTypeNode(task.getTaskTypeNode());
            th.setSourceFieldvalue(task.getSourceFieldvalue());
            th.setBusinessType(task.getBusinessType());
            th.setRequestParameter(task.getRequestParameter());
            th.setRequestTimes(task.getRequestTimes());
            th.setCreateUser(task.getCreateUser());
            th.setCreateTime(task.getCreateTime());
            th.setUpdateUser(task.getUpdateUser());
            th.setUpdateTime(task.getUpdateTime());
            th.setEnable(task.getEnable());
            tTaskHistoryMapper.insert(th);
            tTaskMapper.deleteByPrimaryKey(task.getId());
        }
        TEtcInvoiceProcessChange tEtcInvoiceProcessChange = new TEtcInvoiceProcessChange();
        tEtcInvoiceProcessChange.setOrderCode(task.getSourceFieldvalue());
        tEtcInvoiceProcessChange.setNodeType(task.getTaskTypeNode());
        tEtcInvoiceProcessChange.setChangeJson(task.getRequestParameter());
        tEtcInvoiceProcessChange.setResultJson(json);
        tEtcInvoiceProcessChange.setState(jso.getRcode());
        tEtcInvoiceProcessChange.setResultMsg(jso.getMsg());
        tEtcInvoiceProcessChange.setCreateTime(new Date());
        tEtcInvoiceProcessChange.setEnable(false);
        tEtcInvoiceProcessChangeAPI.add(tEtcInvoiceProcessChange);
        return jso;

    }

    @Override
    public ResultData OrderStartDataSendToEtc(TTask task) throws Exception {
        TOrderContract tOrderContract = tOrderContractAPI.selectByOrderBusinessCode(task.getSourceFieldvalue());
        String host = etcPath;
        String path = "/api/waybillInfo/waybillStart";
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        Map<String, Object> querys = new HashMap<>();
        String req = task.getRequestParameter();
        Map mapTypes = JSON.parseObject(req);
        mapTypes.put("base64Str",Base64UtilFile.getFileBase64Str(tOrderContract.getContractFilePath()));
        log.info("etc运单开始入参：{}", JSON.toJSONString(req));
        HttpResponse response = HttpUtils.doPost(host, path, headers, querys, mapTypes);
        System.out.println(response.toString());
        //获取response的body
        String json = HttpUtils.bytesToString(HttpUtils.getData(response.getEntity()));
        ResultData jso =  JSON.parseObject(json, ResultData.class);
        log.info("etc运单开始回参：{}", JSON.toJSONString(jso));
        if (jso.getRcode().equals("200")){
            TTaskHistory th = new TTaskHistory();
            th.setTaskHisId(IdWorkerUtil.getInstance().nextId());
            th.setTaskId(task.getTaskId());
            th.setTaskType(task.getTaskType());
            th.setTaskTypeNode(task.getTaskTypeNode());
            th.setSourceFieldvalue(task.getSourceFieldvalue());
            th.setBusinessType(task.getBusinessType());
            th.setRequestParameter(task.getRequestParameter());
            th.setRequestTimes(task.getRequestTimes());
            th.setCreateUser(task.getCreateUser());
            th.setCreateTime(task.getCreateTime());
            th.setUpdateUser(task.getUpdateUser());
            th.setUpdateTime(task.getUpdateTime());
            th.setEnable(task.getEnable());
            tTaskHistoryMapper.insert(th);
            tTaskMapper.deleteByPrimaryKey(task.getId());
        }
        TEtcInvoiceProcessChange tEtcInvoiceProcessChange = new TEtcInvoiceProcessChange();
        tEtcInvoiceProcessChange.setOrderCode(task.getSourceFieldvalue());
        tEtcInvoiceProcessChange.setNodeType(task.getTaskTypeNode());
        tEtcInvoiceProcessChange.setChangeJson(task.getRequestParameter());
        tEtcInvoiceProcessChange.setResultJson(json);
        tEtcInvoiceProcessChange.setState(jso.getRcode());
        tEtcInvoiceProcessChange.setResultMsg(jso.getMsg());
        tEtcInvoiceProcessChange.setWaybillnum(jso.getWaybillNum());
        tEtcInvoiceProcessChange.setCreateTime(new Date());
        tEtcInvoiceProcessChange.setEnable(false);
        tEtcInvoiceProcessChangeAPI.add(tEtcInvoiceProcessChange);

        if(DictEnum.REPAIRHT.code.equals(task.getParam1())){
            DataToEtcVO dataToEtcVO = tOrderInfoAPI.selectByEtcDate(task.getSourceFieldvalue());
            TEtcInvoiceProcessChange tEtcInvoiceProcessChangeEnd = tEtcInvoiceProcessChangeAPI.selectByOrderCode(task.getSourceFieldvalue(),DictEnum.ORDEREND.code);
            if(null == tEtcInvoiceProcessChangeEnd ||  !"200".equals(tEtcInvoiceProcessChangeEnd.getState())){
                TTaskVO taskVO = new TTaskVO();
                taskVO.setBusinessType("ETCEXCHANGE");
                taskVO.setTaskType("ETCPROCESSTYPE");
                List<String> ls = new ArrayList<>();
                ls.add(DictEnum.ORDEREND.code);
                taskVO.setTaskTypeNode(ls);
                taskVO.setSourceFieldvalue(dataToEtcVO.getOrderCode());
                List<TTask> tasks = tTaskMapper.selectTask(taskVO);
                if(tasks.size()<1){
                    DateFormat dFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    TTask task2 = new TTask();
                    OrderEnd orderEnd = new OrderEnd();
                    BeanCopyUtil.copyPropertiesIgnoreNull(dataToEtcVO, orderEnd);
                    orderEnd.setAccessId(accessId);
                    if (null != dataToEtcVO.getEndRealTime() && !"".equals(dataToEtcVO.getEndRealTime())) {
                        orderEnd.setEndRealTime(dFormat.format(dataToEtcVO.getEndRealTime()));
                    }
                    orderEnd.setRealDestAddr(dataToEtcVO.getDestAddr());
                    task2.setRequestParameter(JSONObject.toJSONString(orderEnd));
                    task2.setBusinessType("ETCEXCHANGE");
                    task2.setTaskType("ETCPROCESSTYPE");
                    task2.setTaskTypeNode(DictEnum.ORDEREND.code);
                    task2.setRequestTimes(0);
                    task2.setCreateTime(new Date());
                    task2.setRequestDate(new Date());
                    task2.setIsSuccessed(false);
                    task2.setEnable(false);
                    task2.setTaskId(IdWorkerUtil.getInstance().nextId());
                    task2.setRemark("运单结束");
                    task2.setSourceFieldvalue(dataToEtcVO.getOrderCode());
                    tTaskMapper.insert(task2);
                }
            }
        }
        TTaskExample example = new TTaskExample();
        TTaskExample.Criteria cr = example.createCriteria();
        cr.andTaskTypeEqualTo("ETCPROCESSTYPE");
        cr.andBusinessTypeEqualTo("ETCEXCHANGE");
        cr.andTaskTypeNodeEqualTo(DictEnum.ORDEREND.code);
        cr.andRequestTimesEqualTo(1);
        cr.andSourceFieldvalueEqualTo(task.getSourceFieldvalue());

        //有些运单直接收单后有运单结束task 而运单开始task 还没有，现在运单开始task 执行完后 放开运单结束task
        List<TTask> taskList = tTaskMapper.selectByExample(example);
        for(TTask tTaskResult:taskList){
            tTaskResult.setRequestTimes(0);
            tTaskMapper.updateByPrimaryKeySelective(tTaskResult);
        }
        return jso;

    }

    @Override
    public ResultData OrderEndDataSendToEtc(TTask task) throws Exception {
        TEtcInvoiceProcessChange result = tEtcInvoiceProcessChangeAPI.selectByOrderCode(task.getSourceFieldvalue(),DictEnum.ORDERSTART.code);
        if(null != result && "200".equals(result.getState())){
            String host = etcPath;
            String path = "/api/waybillInfo/waybillEnd";
            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
            Map<String, Object> querys = new HashMap<>();
            String req = task.getRequestParameter();
            Map mapTypes = JSON.parseObject(req);
            mapTypes.put("waybillNum",result.getWaybillnum());
            log.info("etc运单结束入参：{}", JSON.toJSONString(req));
            log.info("etc运单结束入参2：{}", JSON.toJSONString(mapTypes));
            HttpResponse response = HttpUtils.doPost(host, path, headers, querys, mapTypes);
            System.out.println(response.toString());
            //获取response的body
            String json = HttpUtils.bytesToString(HttpUtils.getData(response.getEntity()));
            ResultData jso =  JSON.parseObject(json, ResultData.class);
            log.info("etc运单结束回参：{}", JSON.toJSONString(jso));
            if (jso.getRcode().equals("200")){
                TTaskHistory th = new TTaskHistory();
                th.setTaskHisId(IdWorkerUtil.getInstance().nextId());
                th.setTaskId(task.getTaskId());
                th.setTaskType(task.getTaskType());
                th.setTaskTypeNode(task.getTaskTypeNode());
                th.setSourceFieldvalue(task.getSourceFieldvalue());
                th.setBusinessType(task.getBusinessType());
                th.setRequestParameter(task.getRequestParameter());
                th.setRequestTimes(task.getRequestTimes());
                th.setCreateUser(task.getCreateUser());
                th.setCreateTime(task.getCreateTime());
                th.setUpdateUser(task.getUpdateUser());
                th.setUpdateTime(task.getUpdateTime());
                th.setEnable(task.getEnable());
                tTaskHistoryMapper.insert(th);
                tTaskMapper.deleteByPrimaryKey(task.getId());
            }
            TEtcInvoiceProcessChange tEtcInvoiceProcessChangeEnd = tEtcInvoiceProcessChangeAPI.selectByOrderCode(task.getSourceFieldvalue(),DictEnum.ORDEREND.code);
            if(null == tEtcInvoiceProcessChangeEnd){
                TEtcInvoiceProcessChange tEtcInvoiceProcessChange = new TEtcInvoiceProcessChange();
                tEtcInvoiceProcessChange.setOrderCode(task.getSourceFieldvalue());
                tEtcInvoiceProcessChange.setNodeType(task.getTaskTypeNode());
                tEtcInvoiceProcessChange.setChangeJson(task.getRequestParameter());
                tEtcInvoiceProcessChange.setResultJson(json);
                tEtcInvoiceProcessChange.setState(jso.getRcode());
                tEtcInvoiceProcessChange.setResultMsg(jso.getMsg());
                tEtcInvoiceProcessChange.setWaybillnum(jso.getWaybillNum());
                tEtcInvoiceProcessChange.setCreateTime(new Date());
                tEtcInvoiceProcessChange.setEnable(false);
                tEtcInvoiceProcessChangeAPI.add(tEtcInvoiceProcessChange);
            }else{
                tEtcInvoiceProcessChangeEnd.setChangeJson(task.getRequestParameter());
                tEtcInvoiceProcessChangeEnd.setResultJson(json);
                tEtcInvoiceProcessChangeEnd.setState(jso.getRcode());
                tEtcInvoiceProcessChangeEnd.setResultMsg(jso.getMsg());
                tEtcInvoiceProcessChangeEnd.setWaybillnum(jso.getWaybillNum());
                tEtcInvoiceProcessChangeEnd.setUpdateTime(new Date());
                tEtcInvoiceProcessChangeEnd.setEnable(false);
                tEtcInvoiceProcessChangeAPI.update(tEtcInvoiceProcessChangeEnd);
            }
            return jso;
        }else{
            ResultData resultData = new ResultData();
            resultData.setRcode("0");
            return resultData;
        }
    }

    @Override
    public List<TTask> selectTaskByLimit(TTaskVO record) {
        if (null == record.getLimit()) {
            record.setLimit(10);
        }
        if (0 == record.getRequestTimes()) {
            record.setRequestTimes(1);
        }
        return tTaskMapper.selectTaskByLimit(record);
    }

    @Override
    public ResultUtil selectByKhyPage(TTaskSearchVO taskSearchVO) {
        Page<Object> page = PageHelper.startPage(taskSearchVO.getPage(),taskSearchVO.getSize());
        TTaskExample example = new TTaskExample();
        TTaskExample.Criteria cr =example.createCriteria();
        cr.andBusinessTypeIn(taskSearchVO.getBusinessTypeList());
        if(null!=taskSearchVO.getTaskTypeNode()&&!"".equals(taskSearchVO.getTaskTypeNode())){
            cr.andTaskTypeNodeEqualTo(taskSearchVO.getTaskTypeNode());
        }
        cr.andEnableEqualTo(false);
        example.setOrderByClause("create_time DESC");
        List<TTask> tTaskList = tTaskMapper.selectByExample(example);
        for(TTask task:tTaskList){
            if(task.getTaskTypeNode().equals(KHYEnum.DRIVERREPORT.code)){
                task.setTaskTypeNode("司机上报");
            }
            if(task.getTaskTypeNode().equals(KHYEnum.COMPANYREPORT.code)){
                task.setTaskTypeNode("客户(企业)上报");
            }
            if(task.getTaskTypeNode().equals(KHYEnum.CARREPORT.code)){
                task.setTaskTypeNode("车辆上报");
            }
            if(task.getTaskTypeNode().equals(KHYEnum.ORDERREPORT.code)){
                task.setTaskTypeNode("运单上报(承运合同、运单、支付)");
            }
            if(task.getTaskTypeNode().equals(FCFGSEnum.CAPITALREPORT.code)){
                task.setTaskTypeNode("资金流水上报");
            }

        }
        ResultUtil resultUtil = new ResultUtil();
        resultUtil.setData(tTaskList);
        resultUtil.setCount(page.getTotal());
        return resultUtil;
    }

    @Override
    public ResultUtil taskKhyReset( List<Integer> idArray) {
        int j = 0;
        for(int i=0;i<idArray.size();i++){
            TTask tTask = new TTask();
            tTask.setId(idArray.get(i));
            tTask.setRequestTimes(0);
            int total = tTaskMapper.updateByPrimaryKeySelective(tTask);
            if(total>0){
                j++;
            }
        }
        if(j>0){
            return ResultUtil.ok("重置成功");
        }else{
            return ResultUtil.ok("重置失败");
        }
    }

    @Override
    public ResultUtil selectByAnHuiPage(TTaskSearchVO taskSearchVO) {
        Page<Object> page = PageHelper.startPage(taskSearchVO.getPage(),taskSearchVO.getSize());
        TTaskExample example = new TTaskExample();
        TTaskExample.Criteria cr =example.createCriteria();
        cr.andBusinessTypeIn(taskSearchVO.getBusinessTypeList());
        if(null!=taskSearchVO.getTaskTypeNode()&&!"".equals(taskSearchVO.getTaskTypeNode())){
            cr.andTaskTypeNodeEqualTo(taskSearchVO.getTaskTypeNode());
        }
        if(null != taskSearchVO.getErrorInfo() && !"".equals(taskSearchVO.getErrorInfo())){
            cr.andErrorMessageEqualTo(taskSearchVO.getErrorInfo());
        }
        if(null != taskSearchVO.getTaskId() && !"".equals(taskSearchVO.getTaskId())){
            cr.andTaskIdEqualTo(taskSearchVO.getTaskId());
        }
        if(null != taskSearchVO.getCreateStartTime() && !"".equals(taskSearchVO.getCreateStartTime())){
            cr.andCreateTimeGreaterThan(taskSearchVO.getCreateStartTime());
        }
        if(null != taskSearchVO.getCreateEndTime() && !"".equals(taskSearchVO.getCreateEndTime())){
            cr.andCreateTimeLessThan(taskSearchVO.getCreateEndTime());
        }
        cr.andEnableEqualTo(false);
        example.setOrderByClause("create_time DESC");
        List<TTask> tTaskList = tTaskMapper.selectByExample(example);
        for(TTask task:tTaskList){
            if(task.getTaskTypeNode().equals(FCFGSEnum.DRIVERREPORT.code)){
                task.setTaskTypeNode("司机上报");
            }
            if(task.getTaskTypeNode().equals(FCFGSEnum.CARRIER.code)){
                task.setTaskTypeNode("承运方上报");
            }
            if(task.getTaskTypeNode().equals(FCFGSEnum.CARREPORT.code)){
                task.setTaskTypeNode("车辆上报");
            }
            if(task.getTaskTypeNode().equals(FCFGSEnum.ORDERREPORT.code)){
                task.setTaskTypeNode("运单一上报");
            }
            if(task.getTaskTypeNode().equals(FCFGSEnum.ORDERTWOREPORT.code)){
                task.setTaskTypeNode("运单二上报");
            }
            if(task.getTaskTypeNode().equals(FCFGSEnum.ORDERTHREEREPORT.code)){
                task.setTaskTypeNode("运单三上报");
            }
            if(task.getTaskTypeNode().equals(FCFGSEnum.CAPITALREPORT.code)){
                task.setTaskTypeNode("资金流水上报");
            }
        }
        ResultUtil resultUtil = new ResultUtil();
        resultUtil.setData(tTaskList);
        resultUtil.setCount(page.getTotal());
        return resultUtil;
    }

    @Override
    public ResultUtil taskAnHuiReset( List<Integer> idArray) {
        int j = 0;
        for(int i=0;i<idArray.size();i++){
            TTask tTask = new TTask();
            tTask.setId(idArray.get(i));
            tTask.setRequestTimes(0);
            int total = tTaskMapper.updateByPrimaryKeySelective(tTask);
            if(total>0){
                j++;
            }
        }
        if(j>0){
            return ResultUtil.ok("重置成功");
        }else{
            return ResultUtil.ok("重置失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int insertKhyTruckTrackTask(khyTruckTrackDTO dto) {
        TTask task = new TTask();
        task.setTaskType(KHYEnum.KHYREPORT.code);
        task.setTaskTypeNode(KHYEnum.TRUCKTRACKREPORT.code);
        task.setBusinessType(KHYEnum.KHY.code);
        task.setTaskId(IdWorkerUtil.getInstance().nextId());
        task.setSourceTablename("t_order_info");
        task.setSourcekeyFieldname("id");
        task.setSourceFieldname("code");
        task.setSourceFieldvalue(dto.getOrderCode());
        task.setRequestTimes(0);
        task.setRequestParameter(JSON.toJSONString(dto));
        task.setRequestDate(new Date());
        task.setIsSuccessed(false);
        task.setEnable(false);
        task.setCreateTime(new Date());
        List<TTask> tTasks = tTaskMapper.selectTaskByModel(task);
        if (tTasks.isEmpty()) {
            tTaskMapper.insert(task);
        }
        return 1;
    }

    @Override
    public List<TTask> selectTaskByType(TTask task) {
        List<TTask> list = tTaskMapper.selectTaskByType(task);
        return list;
    }

}
