package com.lz.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.lz.api.OrderSendDataAPI;
import com.lz.api.OrderUploadDataAPI;
import com.lz.api.TCarrierinfoAPI;
import com.lz.common.model.datareport.base.DataReportDTO;
import com.lz.common.model.datareport.base.ReportResult;
import com.lz.common.model.datareport.factory.ReportExecuteFactory;
import com.lz.common.model.datareport.factory.ReportGetClassFactory;
import com.lz.common.model.datareport.sddrFcfgs.Fcenum.FCFGSEnum;
import com.lz.common.util.ResultUtil;
import com.lz.common.util.ThrowableUtil;
import com.lz.model.TOrderUpload;
import com.lz.schedule.model.TTask;
import com.lz.schedule.vo.anhui.*;
import com.lz.service.AhTaskService;
import com.lz.service.TTaskHistoryService;
import com.lz.service.TTaskService;
import com.lz.vo.CapitalSendDataToAhInfoVO;
import com.lz.vo.OrderSendDataToAhInfoVO;
import com.lz.zt.DataEscalationBaseMethod;
import commonSdk.responseModel.BaseResponseModel;
import commonSdk.util.CommonRequestUtil;
import datareport.commonSdk.responseModel.DataBaseResponseModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sdk.util.StringUtil;

import java.math.BigDecimal;
import java.util.Map;

@Slf4j
@Service
public class AhTaskServiceImpl implements AhTaskService {
    @Autowired
    private TTaskService taskService;

    @Autowired
    private TTaskHistoryService taskHistoryService;

    @Autowired
    private com.lz.api.sendDataToKhyAPI sendDataToKhyAPI;

    @Autowired
    private TCarrierinfoAPI tCarrierinfoAPI;

    @Autowired
    private OrderUploadDataAPI orderUploadDataAPI;

    @Autowired
    private OrderSendDataAPI orderSendDataAPI;

    @Autowired
    DataEscalationBaseMethod dataEscalationBaseMethod;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultUtil driverUpload(TTask task) {
        try{
            log.info("----------------------安徽司机上报开始----------------------");
            String requestParameter = task.getRequestParameter();
            DriverSendDataAnHuiZTInfoVO driverInfo = JSONUtil.toBean(requestParameter, DriverSendDataAnHuiZTInfoVO.class);
            driverInfo.setTaskId(task.getTaskId());
            driverInfo.setCarrierName(task.getBusinessType());
            log.info("安徽司机上报参数---------------------------------------------:"+ JSON.toJSONString(driverInfo));
            Map<String, Object> execute = dataEscalationBaseMethod.execute(driverInfo);
            log.info("数据上报结果, {}", JSONUtil.toJsonStr(execute));
            DataBaseResponseModel response = JSONUtil.toBean(JSONUtil.parseObj(execute), DataBaseResponseModel.class);

            if(response.getCode().equals("1001")){
                //修改上报状态
                sendDataToKhyAPI.updateAhDriverSendStateFc(driverInfo.getEndUserId(),task.getBusinessType());
                // 转历史
                taskHistoryService.insertTaskHistory(task);
                // 删除任务
                taskService.deleteById(task.getId());
            }else {
                taskService.updateErrorTask(task,response.getMsg());
            }
            return ResultUtil.ok();
        }catch (Exception e){
            e.printStackTrace();
            log.error("安徽司机上报错误: task ID= {}, 错误信息：{}", task.getId(), ThrowableUtil.getStackTrace(e));
            log.error("安徽司机上报错误: task ID= {}, 错误信息：{}", task.getId(), e);
            return null;
        }
    }

    @Override
    public ResultUtil carUpload(TTask task) {
        try{
            log.info("----------------------安徽车辆上报开始----------------------");
            String requestParameter = task.getRequestParameter();
            CarSendDataAnHuiZTInfoVO carInfo = JSONUtil.toBean(requestParameter, CarSendDataAnHuiZTInfoVO.class);
            carInfo.setTaskId(task.getTaskId());
            carInfo.setCarrierName(task.getBusinessType());
            log.info("安徽车辆上报参数---------------------------------------------:"+ JSON.toJSONString(carInfo));
            Map<String, Object> execute = dataEscalationBaseMethod.execute(carInfo);
            log.info("数据上报结果, {}", JSONUtil.toJsonStr(execute));
            DataBaseResponseModel response = JSONUtil.toBean(JSONUtil.parseObj(execute), DataBaseResponseModel.class);
            if(response.getCode().equals("1001")){
                //修改上报状态
                sendDataToKhyAPI.updateAhCarSendStateFc(carInfo.getVehicleId(),task.getBusinessType());
                // 转历史
                taskHistoryService.insertTaskHistory(task);
                // 删除任务
                taskService.deleteById(task.getId());
            }else {
                taskService.updateErrorTask(task,"车辆上报返回结果："+response.getMsg());
            }
            return ResultUtil.ok();
        }catch (Exception e){
            e.printStackTrace();
            log.error("安徽车辆上报错误: task ID= {}, 错误信息：{}", task.getId(), e);
            return null;
        }
    }

    @Override
    public ResultUtil carrierUpload(TTask task) {
        try{
            log.info("----------------------安徽承运人上报开始----------------------");
            String requestParameter = task.getRequestParameter();
            CarrierSendDataAnHuiZTInfoVO carrierSendDataAnHuiZTInfoVO = JSONUtil.toBean(requestParameter, CarrierSendDataAnHuiZTInfoVO.class);
            carrierSendDataAnHuiZTInfoVO.setTaskId(task.getTaskId());
            carrierSendDataAnHuiZTInfoVO.setCarrierName(task.getBusinessType());
            log.info("安徽承运人上报参数---------------------------------------------:"+ JSON.toJSONString(carrierSendDataAnHuiZTInfoVO));
            Map<String, Object> execute = dataEscalationBaseMethod.execute(carrierSendDataAnHuiZTInfoVO);
            log.info("数据上报结果, {}", JSONUtil.toJsonStr(execute));
            DataBaseResponseModel response = JSONUtil.toBean(JSONUtil.parseObj(execute), DataBaseResponseModel.class);
            if(response.getCode().equals("1001")){
                //修改上报状态
                tCarrierinfoAPI.uploadCarrierStatusById(carrierSendDataAnHuiZTInfoVO.getCarrierId(),task.getBusinessType());
                // 转历史
                taskHistoryService.insertTaskHistory(task);
                // 删除任务
                taskService.deleteById(task.getId());
            }else {
                taskService.updateErrorTask(task,"承运人上报返回结果："+response.getMsg());
            }
            return ResultUtil.ok();
        }catch (Exception e){
            e.printStackTrace();
            log.error("安徽承运人上报错误: task ID= {}, 错误信息：{}", task.getId(), e);
            return null;
        }
    }

    @Override
    public ResultUtil orderUpload(TTask task) {
        try{
            log.info("----------------------安徽运单一上报开始----------------------");
            String requestParameter = task.getRequestParameter();
            OrderSendDataAnHuiZTInfoVO orderInfo = JSONUtil.toBean(requestParameter, OrderSendDataAnHuiZTInfoVO.class);
            orderInfo.setTaskId(task.getTaskId());
            orderInfo.setCarrierName(task.getBusinessType());
            if(null != orderInfo.getDriverUploadedStatus() &&
                    ((task.getBusinessType().equals("ANHUI") && orderInfo.getDriverUploadedStatus().contains("ANHUI"))
                            || (task.getBusinessType().equals("BDW") && orderInfo.getDriverUploadedStatus().contains("BDW"))) &&
                    ((task.getBusinessType().equals("ANHUI") && orderInfo.getCarUploadedStatus().contains("ANHUI"))
                            || (task.getBusinessType().equals("BDW") && orderInfo.getCarUploadedStatus().contains("BDW")))) {
                if(null != orderInfo.getTotalMonetaryAmount()){
                    orderInfo.setTotalMonetaryAmount(orderInfo.getTotalMonetaryAmount().setScale(3, BigDecimal.ROUND_HALF_UP));
                }
                if(null != orderInfo.getVehicleInfo() &&
                        null != orderInfo.getVehicleInfo().getGoodsInfo() &&
                        null != orderInfo.getVehicleInfo().getGoodsInfo().get(0).getGoodsItemGrossWeight()){

                    if(orderInfo.getVehicleInfo().getGoodsInfo().get(0).getGoodsItemGrossWeight().compareTo(new BigDecimal("99000")) > 0){
                        taskService.updateErrorTask(task,"超吨！！！！");
                        return null;
                    }
                    orderInfo.getVehicleInfo().getGoodsInfo().get(0).setGoodsItemGrossWeight(
                            orderInfo.getVehicleInfo().getGoodsInfo().get(0).getGoodsItemGrossWeight().setScale(3,BigDecimal.ROUND_HALF_UP));
                }
                log.info("安徽运单一上报参数---------------------------------------------:"+ JSON.toJSONString(orderInfo));
                Map<String, Object> execute = dataEscalationBaseMethod.execute(orderInfo);
                log.info("数据上报结果, {}", JSONUtil.toJsonStr(execute));
                DataBaseResponseModel response = JSONUtil.toBean(JSONUtil.parseObj(execute), DataBaseResponseModel.class);

                if(response.getCode().equals("1001")){
                    //修改上报状态
                    orderUploadDataAPI.updateOrderSendState("orderOne",orderInfo.getOrderCode());
                    // 转历史
                    taskHistoryService.insertTaskHistory(task);
                    // 删除任务
                    taskService.deleteById(task.getId());
                }else {
                    taskService.updateErrorTask(task,response.getMsg());
                }
            }else {
                taskService.updateErrorTask(task,"司机上报状态："+orderInfo.getDriverUploadedStatus()+"车辆上报状态："+orderInfo.getCarUploadedStatus());
            }
            return  ResultUtil.ok();
        }catch (Exception e){
            e.printStackTrace();
            log.error("安徽运单一上报错误: task ID= {}, 错误信息：{}", task.getId(), ThrowableUtil.getStackTrace(e));
            log.error("安徽运单一上报错误: task ID= {}, 错误信息：{}", task.getId(), e);
            return null;
        }
    }

    @Override
    public ResultUtil orderTwoUpload(TTask task) {
        try{
            log.info("----------------------安徽运单二上报开始----------------------");
            String requestParameter = task.getRequestParameter();
            OrderTwoSendDataAnHuiZTInfoVO orderInfo = JSONUtil.toBean(requestParameter, OrderTwoSendDataAnHuiZTInfoVO.class);
            orderInfo.setTaskId(task.getTaskId());
            orderInfo.setCarrierName(task.getBusinessType());
              TOrderUpload tOrderUpload = orderUploadDataAPI.selectInfoByOrderCode(orderInfo.getOrderCode());
//            if(null != orderInfo && orderInfo.getOrderOneStatus().equals(FCFGSEnum.UPLOADED.code)){
              if(null != tOrderUpload && null != tOrderUpload.getReceiptUploadStatus() && tOrderUpload.getReceiptUploadStatus().equals(1)){
                log.info("安徽运单二上报参数---------------------------------------------:"+ JSON.toJSONString(orderInfo));
                Map<String, Object> execute = dataEscalationBaseMethod.execute(orderInfo);
                log.info("数据上报结果, {}", JSONUtil.toJsonStr(execute));
                DataBaseResponseModel response = JSONUtil.toBean(JSONUtil.parseObj(execute), DataBaseResponseModel.class);

                if(response.getCode().equals("1001")){
                    //修改上报状态
                    orderUploadDataAPI.updateOrderSendState("orderTwo",orderInfo.getOrderCode());
                    // 转历史
                    taskHistoryService.insertTaskHistory(task);
                    // 删除任务
                    taskService.deleteById(task.getId());
                }else {
                    taskService.updateErrorTask(task,response.getMsg());
                }
            }else {
                taskService.updateErrorTask(task,"运单二上报状态："+orderInfo.getOrderOneStatus());
            }
            return  ResultUtil.ok();
        }catch (Exception e){
            e.printStackTrace();
            log.error("安徽运单二上报错误: task ID= {}, 错误信息：{}", task.getId(), ThrowableUtil.getStackTrace(e));
            log.error("安徽运单二上报错误: task ID= {}, 错误信息：{}", task.getId(), e);
            return null;
        }
    }

    @Override
    public ResultUtil orderThreeUpload(TTask task) {
        try{
            log.info("----------------------安徽运单三上报开始----------------------");
            String requestParameter = task.getRequestParameter();
            OrderThreeSendDataAnHuiZTInfoVO orderInfo = JSONUtil.toBean(requestParameter, OrderThreeSendDataAnHuiZTInfoVO.class);
            orderInfo.setTaskId(task.getTaskId());
            orderInfo.setCarrierName(task.getBusinessType());
            TOrderUpload tOrderUpload = orderUploadDataAPI.selectInfoByOrderCode(orderInfo.getOrderCode());
//            if(null != orderInfo && orderInfo.getOrderOneStatus().equals(FCFGSEnum.UPLOADED.code)){
            if(null != tOrderUpload && null != tOrderUpload.getTradeUploadStatus() && tOrderUpload.getTradeUploadStatus().equals(1)){
                if(null != orderInfo &&
                        null != orderInfo.getTotalMonetaryAmount()){
                    orderInfo.setTotalMonetaryAmount(
                            orderInfo.getTotalMonetaryAmount().setScale(3,BigDecimal.ROUND_HALF_UP));
                }
                if(null != orderInfo.getVehicleInfo() &&
                        null != orderInfo.getVehicleInfo().getGoodsInfo() &&
                        null != orderInfo.getVehicleInfo().getGoodsInfo().get(0).getGoodsItemGrossWeightAct()){
                    if(orderInfo.getVehicleInfo().getGoodsInfo().get(0).getGoodsItemGrossWeightAct().compareTo(new BigDecimal("99000")) > 0){
                        taskService.updateErrorTask(task,"超吨！！！！");
                        return null;
                    }

                    orderInfo.getVehicleInfo().getGoodsInfo().get(0).setGoodsItemGrossWeightAct(
                            orderInfo.getVehicleInfo().getGoodsInfo().get(0).getGoodsItemGrossWeightAct().setScale(3,BigDecimal.ROUND_HALF_UP));
                }
                log.info("安徽运单三上报参数---------------------------------------------:"+ JSON.toJSONString(orderInfo));
                Map<String, Object> execute = dataEscalationBaseMethod.execute(orderInfo);
                log.info("数据上报结果, {}", JSONUtil.toJsonStr(execute));
                DataBaseResponseModel response = JSONUtil.toBean(JSONUtil.parseObj(execute), DataBaseResponseModel.class);

                if(response.getCode().equals("1001")){
                    //修改上报状态
                    orderUploadDataAPI.updateOrderSendState("orderThree",orderInfo.getOrderCode());
                    // 转历史
                    taskHistoryService.insertTaskHistory(task);
                    // 删除任务
                    taskService.deleteById(task.getId());
                }else {
                    taskService.updateErrorTask(task,response.getMsg());
                }
            }else {
                taskService.updateErrorTask(task,"运单三上报状态："+orderInfo.getOrderOneStatus());
            }
            return  ResultUtil.ok();
        }catch (Exception e){
            e.printStackTrace();
            log.error("安徽运单三上报错误: task ID= {}, 错误信息：{}", task.getId(), ThrowableUtil.getStackTrace(e));
            log.error("安徽运单三上报错误: task ID= {}, 错误信息：{}", task.getId(), e);
            return null;
        }
    }

    @Override
    public ResultUtil capitalUpload(TTask task) {
        try{
            log.info("----------------------安徽资金上报开始----------------------");
            String requestParameter = task.getRequestParameter();
            CapitalSendDataAnHuiZTInfoVO capitalInfo = JSONUtil.toBean(requestParameter, CapitalSendDataAnHuiZTInfoVO.class);
            capitalInfo.setTaskId(task.getTaskId());
            capitalInfo.setCarrierName(task.getBusinessType());
            TOrderUpload tOrderUpload = orderUploadDataAPI.selectInfoByOrderCode(capitalInfo.getOrderCode());
//            if(null != orderInfo && orderInfo.getOrderOneStatus().equals(FCFGSEnum.UPLOADED.code)){
            if(null != tOrderUpload && null != tOrderUpload.getPayUploadStatus() && tOrderUpload.getPayUploadStatus().equals(1)){
                if(null != capitalInfo.getShippingNoteList() &&
                        null != capitalInfo.getShippingNoteList().get(0).getTotalMonetaryAmount()){
                    capitalInfo.getShippingNoteList().get(0).setTotalMonetaryAmount(
                            capitalInfo.getShippingNoteList().get(0).getTotalMonetaryAmount().setScale(3,BigDecimal.ROUND_HALF_UP));
                }
                log.info("安徽资金上报参数---------------------------------------------:"+ JSON.toJSONString(capitalInfo));
                Map<String, Object> execute = dataEscalationBaseMethod.execute(capitalInfo);
                log.info("数据上报结果, {}", JSONUtil.toJsonStr(execute));
                DataBaseResponseModel response = JSONUtil.toBean(JSONUtil.parseObj(execute), DataBaseResponseModel.class);

                if(response.getCode().equals("1001")){
                    //修改上报状态
                    //orderSendDataAPI.updateAhCapitalSendState(capitalInfo.getPayDetailId());
                    orderUploadDataAPI.updateOrderSendState("capital",capitalInfo.getOrderCode());
                    // 转历史
                    taskHistoryService.insertTaskHistory(task);
                    // 删除任务
                    taskService.deleteById(task.getId());
                }else {
                    taskService.updateErrorTask(task,response.getMsg());
                }
            }else {
                taskService.updateErrorTask(task,"运单上报状态："+capitalInfo.getOrderThreeStatus());
            }
            return  ResultUtil.ok();
        }catch (Exception e){
            e.printStackTrace();
            log.error("安徽资金上报错误: task ID= {}, 错误信息：{}", task.getId(), e);
            return null;
        }
    }
}
