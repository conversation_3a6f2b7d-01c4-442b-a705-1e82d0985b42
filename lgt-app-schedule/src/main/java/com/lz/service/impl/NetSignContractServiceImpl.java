package com.lz.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ancun.netsign.model.ApiRespBody;
import com.ancun.netsign.model.ContractOutput;
import com.lowagie.text.pdf.PdfReader;
import com.lz.api.*;
import com.lz.common.dbenum.DictEnum;
import com.lz.common.dbenum.NetSignEnum;
import com.lz.common.idworker.IdWorkerUtil;
import com.lz.common.model.CodeEnum;
import com.lz.common.model.datareport.dataexchengezj.OrderStart;
import com.lz.common.util.*;
import com.lz.dao.TTaskMapper;
import com.lz.model.TEtcInvoiceProcessChange;
import com.lz.model.TNetSignOpenAccount;
import com.lz.model.TOrderContract;
import com.lz.model.TOrderInfo;
import com.lz.model.ht5Gq.contract.AutoSignContract;
import com.lz.model.ht5Gq.contract.CyhtContract;
import com.lz.model.ht5Gq.contract.SkpzContract;
import com.lz.model.netsign.NetsignRequest;
import com.lz.schedule.model.TTask;
import com.lz.service.NetSignContractService;
import com.lz.service.TTaskHistoryService;
import com.lz.vo.DataToEtcVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Files;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

@Slf4j
@Service
public class NetSignContractServiceImpl implements NetSignContractService {

    @Value("${htPath}")
    private String htPath;

    @Value("${accessId}")
    private String accessId;

    @Resource
    private TTaskMapper taskMapper;

    @Resource
    private TTaskHistoryService taskHistoryService;

    @Resource
    private TOrderInfoAPI orderInfoAPI;

    @Resource
    private TOrderContractAPI orderContractAPI;

    @Resource
    private TEtcInvoiceProcessChangeAPI tEtcInvoiceProcessChangeAPI;

    @Resource
    private OssAPI ossAPI;

    @Resource
    private NetsignAPI netsignAPI;

    @Resource
    private NetSignOpenAccountAPI netSignOpenAccountAPI;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void applyCyht(TTask task) {
        if (null != task.getRequestParameter()) {
            CyhtContract cyhtContract = JSONUtil.toBean(task.getRequestParameter(), CyhtContract.class);
            NetsignRequest netsignRequest = new NetsignRequest();
            netsignRequest.setCyhtContract(cyhtContract);
            ApiRespBody<ContractOutput> contract;
            if (NetSignEnum.NETSIGN_CYHT.code.equals(task.getTaskType())) {
                contract = netsignAPI.createContract(netsignRequest);
            } else if (NetSignEnum.NETSIGN_REPAIR.code.equals(task.getTaskType())) {
                contract = netsignAPI.supplementContract(netsignRequest);
            } else {
                throw new RuntimeException("承运合同类型错误");
            }
            if (100000 != contract.getCode()) {
                if (null != contract.getMsg()) {
                    throw new RuntimeException(contract.getMsg());
                } else {
                    throw new RuntimeException("承运合同签署失败");
                }
            } else {
                // 补签合同，将之前合同置为无效
                if (DictEnum.REPAIRHT.code.equals(task.getTaskType())) {
                    TOrderContract tOrderContract = new TOrderContract();
                    tOrderContract.setOrderCode(cyhtContract.getOrderCode());
                    tOrderContract.setContractType(DictEnum.CYHT.code);
                    tOrderContract.setEnable(true);
                    orderContractAPI.updateByOrderCodeModel(tOrderContract);
                }

                ContractOutput resp = JSONUtil.toBean(JSONUtil.toJsonStr(contract.getData()), ContractOutput.class);
                // 创建自动签任务参数
                AutoSignContract autoSignContract = new AutoSignContract();
                autoSignContract.setLotNumber(resp.getContractNo());
                autoSignContract.setOrderCode(cyhtContract.getOrderCode());
                autoSignContract.setOrderBusinessCode(cyhtContract.getOrderBusinessCode());
                autoSignContract.setCarrierId(cyhtContract.getCarrierId());
                autoSignContract.setDriverId(cyhtContract.getDriverId());
                if(null != cyhtContract.getPushContractId()){
                    autoSignContract.setPushContractId(cyhtContract.getPushContractId());
                }
                // 创建自动签署任务
                TTask tTask = new TTask();
                tTask.setTaskId(IdWorkerUtil.getInstance().nextId());
                tTask.setTaskType(task.getTaskType());
                tTask.setBusinessType(DictEnum.HT.code);
                tTask.setTaskTypeNode(DictEnum.AUTOSIGN.code);
                tTask.setSourceTablename("T_ORDER_INFO");
                tTask.setSourceFieldvalue(cyhtContract.getOrderCode());
                tTask.setSourceFieldname("code");
                tTask.setRequestTimes(0);
                tTask.setRequestParameter(JSONUtil.toJsonStr(autoSignContract));
                tTask.setCreateTime(new Date());
                tTask.setRequestDate(new Date());
                tTask.setIsSuccessed(false);
                tTask.setEnable(false);
                taskMapper.insert(tTask);
                // 转历史
                taskHistoryService.insertTaskHistory(task);
                // 删除任务
                taskMapper.deleteByPrimaryKey(task.getId());

                // 创建承运合同
                createCyhtContract(cyhtContract, task.getTaskType());
            }
        } else {
            throw new RuntimeException("请求参数为空");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void applySkpz(TTask task) {
        if (null != task.getRequestParameter()) {
            SkpzContract skpzContract = JSONUtil.toBean(task.getRequestParameter(), SkpzContract.class);
            skpzContract.setDriverSignTime(DateUtils.formatDate(new Date(), "yyyy-MM-dd"));
            NetsignRequest netsignRequest = new NetsignRequest();
            netsignRequest.setSkpzContract(skpzContract);
            ApiRespBody<ContractOutput> contract = netsignAPI.payingVoucher(netsignRequest);
            if (100000 != contract.getCode()) {
                if (null != contract.getMsg()) {
                    throw new RuntimeException(contract.getMsg());
                } else {
                    throw new RuntimeException("创建收款凭证失败");
                }
            } else {
                ContractOutput resp = JSONUtil.toBean(JSONUtil.toJsonStr(contract.getData()), ContractOutput.class);
                // 创建自动签任务参数
                AutoSignContract autoSignContract = new AutoSignContract();
                autoSignContract.setLotNumber(resp.getContractNo());
                autoSignContract.setOrderCode(skpzContract.getOrderCode());
                autoSignContract.setOrderBusinessCode(skpzContract.getOrderBusinessCode());
                autoSignContract.setCarrierId(skpzContract.getCarrierId());
                autoSignContract.setDriverId(skpzContract.getDriverId());

                // 创建自动签署任务
                TTask tTask = new TTask();
                tTask.setTaskId(IdWorkerUtil.getInstance().nextId());
                tTask.setTaskType(task.getTaskType());
                tTask.setBusinessType(DictEnum.TX.code);
                tTask.setTaskTypeNode(DictEnum.AUTOSIGN.code);
                tTask.setSourceTablename("T_ORDER_INFO");
                tTask.setSourceFieldvalue(skpzContract.getOrderCode());
                tTask.setSourceFieldname("code");
                tTask.setRequestTimes(0);
                tTask.setRequestParameter(JSONUtil.toJsonStr(autoSignContract));
                tTask.setCreateTime(new Date());
                tTask.setRequestDate(new Date());
                tTask.setIsSuccessed(false);
                tTask.setEnable(false);
                taskMapper.insert(tTask);
                // 转历史
                taskHistoryService.insertTaskHistory(task);
                // 删除任务
                taskMapper.deleteByPrimaryKey(task.getId());
                // 创建收款凭证合同
                createSkpzContract(skpzContract, task.getTaskType());
            }
        } else {
            throw new RuntimeException("请求参数为空");
        }
    }

    private void createCyhtContract(CyhtContract chyhtContract, String taskType) {
        TOrderContract tOrderContract = new TOrderContract();
        tOrderContract.setContractCode(IdWorkerUtil.getInstance().nextId());
        tOrderContract.setOrderCode(chyhtContract.getOrderCode());
        //合同类型
        if (NetSignEnum.NETSIGN_CYHT.code.equals(taskType)) {
            tOrderContract.setContractType(DictEnum.CYHT.code);
        } else if (NetSignEnum.NETSIGN_REPAIR.code.equals(taskType)) {
            tOrderContract.setContractType(DictEnum.REPAIRHT.code);
        }
        tOrderContract.setContractForm(DictEnum.XXQZGZHT.code);
        tOrderContract.setCreateTime(new Date());//创建合同时间
        tOrderContract.setEnable(false);
        tOrderContract.setFirstPart(chyhtContract.getCarrierName());//合同的甲方
        tOrderContract.setFirstPartPlatformId(chyhtContract.getCarrierId());//甲方平台id
        tOrderContract.setSecondPart(chyhtContract.getDriverName());//合同发乙方
        tOrderContract.setSecondPartPlatformId(chyhtContract.getDriverId());//乙方平台id
        orderContractAPI.add(tOrderContract);
    }

    private void createSkpzContract(SkpzContract skpzContract, String taskType) {
        TOrderContract tOrderContract = new TOrderContract();
        tOrderContract.setContractCode(IdWorkerUtil.getInstance().nextId());
        tOrderContract.setOrderCode(skpzContract.getOrderCode());
        tOrderContract.setContractType(taskType);
        tOrderContract.setContractForm(DictEnum.SKPZ.code);
        tOrderContract.setCreateTime(new Date());//创建合同时间
        tOrderContract.setEnable(false);
        tOrderContract.setFirstPart(skpzContract.getCaName());//合同的甲方
        tOrderContract.setFirstPartPlatformId(skpzContract.getCarrierId());//甲方平台id
        tOrderContract.setSecondPart(skpzContract.getDriverName());//合同发乙方
        tOrderContract.setSecondPartPlatformId(skpzContract.getDriverId());//乙方平台id
        orderContractAPI.add(tOrderContract);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void autoSign(TTask task) {
        if (null != task.getRequestParameter()) {
            AutoSignContract autoSignContract = JSONUtil.toBean(task.getRequestParameter(), AutoSignContract.class);
            NetsignRequest netsignRequest = new NetsignRequest();
            netsignRequest.setContractNo(autoSignContract.getLotNumber());

            TNetSignOpenAccount netSignOpenAccount = new TNetSignOpenAccount();
            if (NetSignEnum.NETSIGN_SKPZ.code.equals(task.getTaskType())) {
                // 收款凭证
                netsignRequest.setType("skpz");
            } else {
                netSignOpenAccount.setUserId(autoSignContract.getCarrierId());
                netSignOpenAccount.setUserType(DictEnum.CA.code);

                TNetSignOpenAccount tNetSignOpenAccount = netSignOpenAccountAPI.selectNetSignOpenAccount(netSignOpenAccount);
                if (null == tNetSignOpenAccount || null == tNetSignOpenAccount.getSealNo() || StringUtils.isBlank(tNetSignOpenAccount.getSealNo())) {
                    throw new RuntimeException("承运方未开户");
                }
                if (null == tNetSignOpenAccount.getSealImage() || StringUtils.isBlank(tNetSignOpenAccount.getSealImage())) {
                    throw new RuntimeException("承运方未上传印章");
                }
                netsignRequest.setCarrierAccount(tNetSignOpenAccount.getAccount());
                netsignRequest.setCarrierSealNo(tNetSignOpenAccount.getSealNo());
            }
            netSignOpenAccount.setUserId(autoSignContract.getDriverId());
            netSignOpenAccount.setUserType(DictEnum.CD.code);
            TNetSignOpenAccount tNetSignOpenAccount1 = netSignOpenAccountAPI.selectNetSignOpenAccount(netSignOpenAccount);
            if (null == tNetSignOpenAccount1 || null == tNetSignOpenAccount1.getSealNo() || StringUtils.isBlank(tNetSignOpenAccount1.getSealNo())) {
                throw new RuntimeException("司机未开户");
            }
            if (null == tNetSignOpenAccount1.getSealImage() || StringUtils.isBlank(tNetSignOpenAccount1.getSealImage())) {
                throw new RuntimeException("司机未上传印章");
            }
            netsignRequest.setPersonAccount(tNetSignOpenAccount1.getAccount());
            netsignRequest.setPersonSealNo(tNetSignOpenAccount1.getSealNo());
            ApiRespBody<ContractOutput> contractOutputApiRespBody = netsignAPI.addSigner(netsignRequest);

            if (100000 != contractOutputApiRespBody.getCode()) {
                if (100074 != contractOutputApiRespBody.getCode()) {
                    if (null != contractOutputApiRespBody.getMsg()) {
                        throw new RuntimeException(contractOutputApiRespBody.getMsg());
                    } else {
                        throw new RuntimeException("自动签署合同失败");
                    }
                }

            }
            JSONObject jsonObject = JSONUtil.parseObj(contractOutputApiRespBody.getData());
            log.info("自动签署合同返回结果：{}", jsonObject);
            // 创建下载合同任务
            TTask tTask = new TTask();
            tTask.setTaskId(IdWorkerUtil.getInstance().nextId());
            tTask.setTaskType(task.getTaskType());
            tTask.setBusinessType(DictEnum.HT.code);
            tTask.setTaskTypeNode(DictEnum.HTDOWNLOAD.code);
            tTask.setSourceTablename("T_ORDER_INFO");
            tTask.setSourceFieldvalue(autoSignContract.getOrderCode());
            tTask.setSourceFieldname("code");
            tTask.setRequestTimes(0);
            tTask.setRequestParameter(JSONUtil.toJsonStr(autoSignContract));
            tTask.setCreateTime(new Date());
            tTask.setRequestDate(new Date());
            tTask.setIsSuccessed(false);
            tTask.setEnable(false);
            taskMapper.insert(tTask);
            // 转历史
            taskHistoryService.insertTaskHistory(task);
            // 删除任务
            taskMapper.deleteByPrimaryKey(task.getId());
            // 修改署完成时间
            String taskType = task.getTaskType();
            if (NetSignEnum.NETSIGN_CYHT.code.equals(taskType)) {
                taskType  = DictEnum.CYHT.code;
            } else if (NetSignEnum.NETSIGN_REPAIR.code.equals(taskType)) {
                taskType = DictEnum.REPAIRHT.code;
            }
            TOrderContract tOrderContract5 = orderContractAPI.selectByOrderCodeAnrType(autoSignContract.getOrderCode(), taskType);
            tOrderContract5.setFirstPartSignTime(new Date());
            tOrderContract5.setSecondPartSignTime(new Date());
            tOrderContract5.setSignFinishTime(new Date());
            tOrderContract5.setUpdateTime(new Date());
            orderContractAPI.updateHt(tOrderContract5);
        } else {
            throw new RuntimeException("请求参数为空");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void download(TTask task) {
        String requestParameter = task.getRequestParameter();
        AutoSignContract autoSignContract = JSONUtil.toBean(requestParameter, AutoSignContract.class);
        //运单code
        String orderCode = autoSignContract.getOrderCode();
        String contractNo =  autoSignContract.getLotNumber();
        try {
            NetsignRequest netsignRequest = new NetsignRequest();
            netsignRequest.setContractNo(contractNo);

            ResultUtil downloadContractOutputApiRespBody = netsignAPI.downloadContract(netsignRequest);
            if (!CodeEnum.SUCCESS.getCode().equals(downloadContractOutputApiRespBody.getCode())) {
                throw new RuntimeException("下载合同失败");
            }
            log.info("合同下载成功！");
            String filePath = downloadContractOutputApiRespBody.getData().toString();
            int pages = 0;
            try {
                log.info("写入图片文件开始！" + filePath);
                URL url = new URL(filePath);
                URLConnection connection = url.openConnection();
                InputStream is = connection.getInputStream();
                PDDocument pdDocument = PDDocument.load(is);
                PDFRenderer renderer = new PDFRenderer(pdDocument);
                /* dpi越大转换后越清晰，相对转换速度越慢 */
                PdfReader reader = new PdfReader(filePath);
                pages = reader.getNumberOfPages();
                for (int i = 0; i < pages; i++) {
                    String pngFilePath = htPath + orderCode + i + ".png";
                    BufferedImage image = renderer.renderImageWithDPI(i, 300);
                    ImageIO.write(image, "png", new File(pngFilePath));
                }
                pdDocument.close();
                log.info("写入图片文件成功！");
            } catch (IOException e) {
                log.error("写入图片文件失败！{}", ThrowableUtil.getStackTrace(e));
            }
            StringBuilder contractFilePath = new StringBuilder();
            for (int i = 0; i < pages; i++) {
                File file = new File(htPath + orderCode + i + ".png");
                MockMultipartFile mockMultipartFile = new MockMultipartFile("file", orderCode + ".png", "png", Files.newInputStream(file.toPath()));
                contractFilePath.append(ossAPI.uploadFileSample(mockMultipartFile).getData().toString()).append(",");
                if (file.delete()) {
                    log.info("删除文件, {}成功！", htPath + orderCode + i + ".png");
                } else {
                    log.info("删除文件, {}失败！", htPath + orderCode + i + ".png");
                }
            }
            TOrderInfo tOrderInfo = orderInfoAPI.selectOrderInfoByCode(orderCode);
            TOrderInfo tOrderInfoedit = new TOrderInfo();
            tOrderInfoedit.setId(tOrderInfo.getId());
            tOrderInfoedit.setContractStatus("YZID");
            orderInfoAPI.edit(tOrderInfoedit);
            String taskType = task.getTaskType();
            if (NetSignEnum.NETSIGN_CYHT.code.equals(taskType)) {
                taskType  = DictEnum.CYHT.code;
            } else if (NetSignEnum.NETSIGN_REPAIR.code.equals(taskType)) {
                taskType = DictEnum.REPAIRHT.code;
            }
            TOrderContract tOrderContract5 = orderContractAPI.selectByOrderCodeAnrType(orderCode, taskType);
            tOrderContract5.setContractDownloadTime(new Date());//合同下载时间
            tOrderContract5.setContractFilePath(filePath);
            if (contractFilePath.length() > 0) {
                contractFilePath.deleteCharAt(contractFilePath.length() - 1);
            }
            tOrderContract5.setParam2(contractFilePath.toString());
            tOrderContract5.setUpdateTime(new Date());
            tOrderContract5.setContractSignStatus("XZ");
            orderContractAPI.updateHt(tOrderContract5);
            // 删除任务
            taskMapper.deleteByPrimaryKey(task.getId());

            //task表数据移到历史表
            taskHistoryService.insertTaskHistory(task);
            //etc任务
            etcTask(orderCode, task, "");
        } catch (Exception e) {
            log.error("合同下载失败！{}", ThrowableUtil.getStackTrace(e));
            throw new RuntimeException("合同下载失败");
        }
    }

    private void etcTask(String orderCode, TTask task, String contractFilePath) {
        try {
            DataToEtcVO dataToEtcVO = orderInfoAPI.selectByEtcDate(orderCode);
            if (dataToEtcVO != null) {
                //合同下载完成后向ETC发起运单开始
                TEtcInvoiceProcessChange tEtcInvoiceProcessChange = tEtcInvoiceProcessChangeAPI.selectByOrderCode(orderCode, DictEnum.ORDERSTART.code);
                if (null == tEtcInvoiceProcessChange || !"200".equals(tEtcInvoiceProcessChange.getState())) {
                    DateFormat dFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    TTask tTask = new TTask();
                    OrderStart orderStart = new OrderStart();
                    BeanCopyUtil.copyPropertiesIgnoreNull(dataToEtcVO, orderStart);
                    orderStart.setAccessId(accessId);
                    if (null != dataToEtcVO.getStartTime()) {
                        orderStart.setStartTime(dFormat.format(dataToEtcVO.getStartTime()));
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(dataToEtcVO.getStartTime());
                        calendar.add(Calendar.DATE, 4);
                        orderStart.setPredictEndTime(dFormat.format(calendar.getTime()));
                    }
                    BigDecimal bignum1 = new BigDecimal("100");
                    if (null != dataToEtcVO.getFee()) {
                        String resultfee = dataToEtcVO.getFee().multiply(bignum1).toString();
                        orderStart.setFee(resultfee.substring(0, resultfee.indexOf(".")));
                    }
                    orderStart.setWaybillFileName(contractFilePath.split("com/")[1]);
                    tTask.setRequestParameter(JSONUtil.toJsonStr(orderStart));
                    tTask.setBusinessType("ETCEXCHANGE");
                    tTask.setTaskType("ETCPROCESSTYPE");
                    tTask.setTaskTypeNode(DictEnum.ORDERSTART.code);
                    tTask.setRequestTimes(0);
                    tTask.setCreateTime(new Date());
                    tTask.setRequestDate(new Date());
                    tTask.setIsSuccessed(false);
                    tTask.setEnable(false);
                    tTask.setTaskId(IdWorkerUtil.getInstance().nextId());
                    tTask.setRemark("运单开始");
                    tTask.setSourceFieldvalue(orderCode);
                    if (DictEnum.REPAIRHT.code.equals(task.getTaskType())) {
                        tTask.setParam1(DictEnum.REPAIRHT.code);
                    } else {
                        tTask.setParam1(DictEnum.CYHT.code);
                    }
                    taskMapper.insert(tTask);
                }
            }
        } catch (Exception e) {
            log.error("添加etc运单开始异常 {}", ThrowableUtil.getStackTrace(e));
        }
    }

}
