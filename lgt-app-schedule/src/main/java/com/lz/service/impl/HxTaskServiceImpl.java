package com.lz.service.impl;

import cn.hutool.json.JSONUtil;
import com.lz.api.TZtWalletChangeLogAPI;
import com.lz.common.dbenum.JdEnum;
import com.lz.common.model.hxPayment.request.query.CustomerReceiptReq;
import com.lz.common.util.ResultUtil;
import com.lz.hxpay.CloudPaymentAPI;
import com.lz.model.TZtWalletChangeLog;
import com.lz.schedule.model.TTask;
import com.lz.service.HxTaskService;
import com.lz.service.TTaskHistoryService;
import com.lz.service.TTaskService;
import commonSdk.responseModel.CustomerReceiptResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class HxTaskServiceImpl implements HxTaskService {

    @Autowired
    private TTaskService taskService;

    @Autowired
    private TTaskHistoryService taskHistoryService;

    @Autowired
    private CloudPaymentAPI cloudPaymentAPI;

    @Autowired
    private TZtWalletChangeLogAPI ztWalletChangeLogAPI;

    /**
     * 获取电子回单
     * @param task
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int applyReceipt(TTask task) {
        CustomerReceiptReq receiptReq = JSONUtil.toBean(task.getRequestParameter(), CustomerReceiptReq.class);
        ResultUtil receiptResult = cloudPaymentAPI.execute(receiptReq);
        CustomerReceiptResponse response = JSONUtil.toBean(JSONUtil.parseObj(receiptResult.getData()), CustomerReceiptResponse.class);
        if (JdEnum.REQUEST_SUCCESS.code.equals(response.getResponseCode())) {
            TZtWalletChangeLog log = new TZtWalletChangeLog();
            log.setId(Integer.valueOf(task.getParam1()));
            log.setFileUrl(null != response.getFileUrl() ? response.getFileUrl() : "");
            ztWalletChangeLogAPI.updateWalletChangeLog(log);
            taskHistoryService.insertTaskHistory(task);
            taskService.deleteById(task.getId());
        } else {
            throw new RuntimeException(JSONUtil.toJsonStr(response));
        }
        return 1;
    }
}
