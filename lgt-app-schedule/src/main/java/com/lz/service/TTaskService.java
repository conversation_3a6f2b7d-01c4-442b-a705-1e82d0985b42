package com.lz.service;

import com.lz.common.model.exchangeyimei.YimeiResult;
import com.lz.common.model.datareport.dataexchengezj.ResultData;
import com.lz.common.model.datareport.khydr.orderreport.khyTruckTrackDTO;
import com.lz.common.model.exchangeyimei.YimeiResult;
import com.lz.common.util.ResultUtil;
import com.lz.job.WithdrawtocardJobApply;
import com.lz.schedule.TTaskVO;
import com.lz.schedule.model.TTask;
import com.lz.schedule.model.TTaskError;
import com.lz.schedule.model.TTaskHistory;
import com.lz.schedule.vo.TTaskSearchVO;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * author dingweibo
 * task任务表
 */
public interface TTaskService {

    /**
     *  合同
     * @return
     */
    public ResultUtil task();

    /**
     * 申请司机端子账号
     * @return
     */
    public ResultUtil applySubAccountTask();

    public ResultUtil insertTask(TTask task);

    /**
     * @Description 插入错误任务
     * <AUTHOR> @Date
     * @param
     * @Return
     * @Exception
     *
     */
    ResultUtil insertTaskError(TTaskError task);

    ResultUtil add(TTask task);

    ResultUtil addHis(TTaskHistory taskHistory);

    ResultUtil update(TTask task);

    ResultUtil select(TTask task);

    TTask selectByKhyBasicsInfo(TTask tTask);

    List<TTask> selectByCyht(TTask tTask);

    ResultUtil deleteById(Integer taskId);

    /**
    * @Description 查询任务
    * <AUTHOR> @Date
    * @param
    * @Return
    * @Exception
    *
    */
    ResultUtil selectTask(TTaskVO record);

    /**
     * @Description 修改任务
     * <AUTHOR> @Date
     * @param
     * @Return
     * @Exception
     *
     */
    int updateTask(TTask task, Boolean isSuccess, String result);

    int updateErrorTask(TTask task,String errorMassage);

    /**
     * @Description 删除任务
     * <AUTHOR> @Date
     * @param
     * @Return
     * @Exception
     *
     */
    int deleteByPrimaryKey(Integer taskId);

    //会计入账,向网商发起入账申请
    void Instant(TTask task);

    /**
     * 发起提现
     * @param task
     * @param map
     * @return
     */
    ResultUtil excuteWithdrawtocard(TTask task, Map<String, String> map);


    /**
     * 向易煤网发送消息
     * @param task
     * @return
     */
    YimeiResult DataSendToYiMei(TTask task) throws Exception;

    /**
    * @Description 根据id查询对象
    * <AUTHOR>
    * @Date   2019/7/30 9:05
    * @Param
    * @Return
    * @Exception
    *
    */
    public TTask selectById(Integer taskId);


    /**
    * @Description 充值
    * <AUTHOR>
    * @Date   2019/8/20 13:49
    * @param
    * @Return
    * @Exception
    *
    */
    ResultUtil czJob(TTask task);


    /**
    * @Description 执行支付回调任务
    * <AUTHOR>
    * @Date   2019/8/20 14:07
    * @param
    * @Return
    * @Exception
    *
    */
    ResultUtil tradeStatusSyncJob(TTask task);


    /**
    * @Description 执行提现回调任务
    * <AUTHOR>
    * @Date   2019/8/20 14:19
    * @param
    * @Return
    * @Exception
    *
    */
    ResultUtil WithdrawalStatusSyncJob(TTask task);

    /**
     * @Description 执行自动到卡，提现申请任务
     * <AUTHOR>
     * @Date   2019/8/20 14:19
     * @param
     * @Return
     * @Exception
     *
     */
    ResultUtil withdrawtocardJobApply(TTask task);

    ResultUtil exCreateTxxy(TTask task);

    /**
     * @Description 查询提现协议任务
     * <AUTHOR> @Date
     * @param
     * @Return
     * @Exception
     *
     */
    ResultUtil selectTXXyTask(TTaskVO record);

    /**
     * <AUTHOR>
     * @Description 创建数字物流运单
     * @Date 2020/4/17 3:49 下午
     * @Param
     * @return 3:49 下午
    **/
    ResultUtil createDigitFlowOrder(TTask task);

    /**
     * <AUTHOR>
     * @Description 创建数字物流运输完成
     * @Date 2020/4/23 11:12 上午
     * @Param
     * @return 11:12 上午
    **/
    ResultUtil createDigitFlowOrderSave(TTask task);

    /**
     * <AUTHOR>
     * @Description 货源支付订单创建、运单支付订单创建
     * @Date 2020/4/23 2:37 下午
     * @Param
     * @return 2:37 下午
    **/
    ResultUtil createSourceOrderPay(TTask task);

    /**
     * <AUTHOR>
     * @Description 运单完成任务
     * @Date 2020/4/24 5:49 下午
     * @Param
     * @return 5:49 下午
    **/
    ResultUtil createOrderFinish(TTask task);

    /**
     * <AUTHOR>
     * @Description 运单关闭
     * @Date 2020/4/25 9:50 上午
     * @Param
     * @return 9:50 上午
    **/
    ResultUtil closedOrder(TTask task);

    /**
     * <AUTHOR>
     * @Description 第三方位置信息上报
     * @Date 2020/4/25 10:09 上午
     * @Param
     * @return 10:09 上午
    **/
    ResultUtil locationImport(TTask task);

    /**
     * <AUTHOR>
     * @Description 起运运单
     * @Date 2020/4/30 4:40 下午
     * @Param
     * @return 4:40 下午
    **/
    ResultUtil orderStart(TTask task);

    /**
     * <AUTHOR>
     * @Description 运单更新
     * @Date 2020/4/30 4:40 下午
     * @Param
     * @return 4:40 下午
     **/
    ResultUtil orderUpdate(TTask task);

    List<TTask> selectTaskByModel(TTask task);

    Boolean selectTaskAndHistory(TTask task);

    ResultUtil billCreate(TTask task);

    ResultUtil selectDigitalTask(List<String> taskTypeNode, String taskType, String businessType, String stateNode);


    void InstantR(TTask task);

    void InstantRNode(TTask task);

    void refund(TTask task);

    ResultData CarDataSendToEtc (TTask task) throws Exception;

    ResultData OrderStartDataSendToEtc (TTask task) throws Exception;

    ResultData OrderEndDataSendToEtc (TTask task) throws Exception;

    List<TTask> selectTaskByLimit(TTaskVO record);

    int insertKhyTruckTrackTask(khyTruckTrackDTO dto);

    public ResultUtil selectByKhyPage(TTaskSearchVO taskSearchVO);

    public ResultUtil taskKhyReset(List<Integer> idArray);

    ResultUtil selectByAnHuiPage(TTaskSearchVO taskSearchVO);

    ResultUtil taskAnHuiReset(List<Integer> idArray);

    List<TTask> selectTaskByType(TTask task);
}
