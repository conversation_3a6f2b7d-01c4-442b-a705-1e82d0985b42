package com.lz.service;

import com.lz.common.util.ResultUtil;
import com.lz.dto.CompanyInfoDTO;
import com.lz.dto.TLineGoodsCarriageChangeDTO;
import com.lz.model.QueryConditions;
import com.lz.model.TGoodsSourceInfo;
import com.lz.model.TGoodsSourceInfoBanRecord;
import com.lz.vo.*;
import com.lz.vo.companypc.PCCreateGoodsResourceVO;

import java.util.List;


public interface TGoodsSourceInfoService {

    ResultUtil auditGoodsSourceUseNumber(Integer sourceId);

    /**
     * 新建货源 - 根据选择的线路获取
     * 线路上已使用的货物
     * 前端不能再此线路上使用此货物
     * Yan
     * @param lineId
     * @return
     */
    ResultUtil pcGetGoodsByLineId(Integer lineId);

    /**
     * WX 扫码获取货源信息
     * Yan
     * @param code
     * @return
     */
    ResultUtil wxSelectWaybillInfo(String code);

    String selectProjectByCode(String code);

    ResultUtil selectByProjectId(String code);

    /**
     * 新增货源
     * @param goodsResourceVO
     * @return
     */
    ResultUtil insertGoodsSource(CreateGoodsResourceVO goodsResourceVO);

    /**
     * 新增货源大厅货源
     */
    ResultUtil pcGoodsSourceInsert(PCCreateGoodsResourceVO goodsResourceVO);

    /**
     * 修改货源信息
     * @param goodsResourceVO
     * @return
     */
    ResultUtil updateGoodsSource(CreateGoodsResourceVO goodsResourceVO);

    /**
     * 货源大厅--修改
     */
    ResultUtil pcUpdate(PCCreateGoodsResourceVO goodsResourceVO);


    /**
     * 查询要修改的货源的详细信息
     * @param lineGoodsId
     * @return
     */
    ResultUtil selectEditInfo(Integer lineGoodsId);

    ResultUtil selectByLineGoodsRelIdList(TLineGoodsCarriageChangeDTO record);


    /**
     * 删除货源信息
     * Yan
     * @param
     * @return
     */
    ResultUtil deleteGoodsSource(DeleteGoodsResourceVO deleteGoodsResourceVO);

    /**
     * 删除货源大厅的货源信息
     */
    ResultUtil pcDeleteGoodsSource(DeleteGoodsResourceVO deleteGoodsResourceVO);

    /**
     * 查询货源发单重量和运费单价
     * @param lineGoodsRelId
     * @return
     */
    ResultUtil selectPriceAndWeight(Integer lineGoodsRelId);

    ResultUtil selectByPrimaryKey(Integer id);

    /**
     * 查询企业的货源
     * @param goodsSourceSearch
     * @return
     */
    ResultUtil companySource(AppGoodsSourceSearch goodsSourceSearch);


    /**
     * 查询企业的货源
     * @param goodsSourceSearch
     * @return
     */
    ResultUtil companySourceByCompanyId(AppGoodsSourceSearch goodsSourceSearch);

    /**
     * 查询项目的货源
     * @param code
     * @return
     */
    ResultUtil selectSourceByProjectId(String code);

    /**
    * @Description 微信扫码发单修改货源使用次数
    * <AUTHOR>
    * @Date   2019/6/8 14:46
    * @Param
    * @Return
    * @Exception
    *
    */
    ResultUtil scanSendOrderUpdateResourceInfoForOrder(Integer sourceId);


    /**
     *  @author: dingweibo
     *  @Date: 2019/6/21 15:29
     *  @Description: 根据当前登录企业获取货源
     */
    ResultUtil findList();

    ResultUtil findListByCompanyId(CompanyInfoDTO companyInfoDTO);


    ResultUtil  BatchCreateQRCode();

    ResultUtil createGoodsSourceInfoForTempalte(TGoodsSourceInfo record);

    ResultUtil selectOrderTemplate(TGoodsSourceInfoVo record);

    ResultUtil deleteGoodsSourceInfoByDeleteOrderTempalte(TGoodsSourceInfo record);

    ResultUtil getResourceInfo(TGoodsSourceInfoVo record);

    ResultUtil findGoodsSource(QueryConditions queryConditions);
    ResultUtil findGoodsType();

    ResultUtil updateBusinessAssist(CreateGoodsResourceVO goodsResourceVO);

    ResultUtil updateAgent(CreateGoodsResourceVO goodsResourceVO);

    TGoodsSourceInfo selectGoodsSourceInfo(TGoodsSourceInfo tGoodsSourceInfo);

    ResultUtil blockchainPassCheckLine(CreateGoodsResourceVO goodsResourceVO);

    ResultUtil selectGoodsByCompanyName(CreateGoodsResourceVO goodsResourceVO);

    ResultUtil selectByGoodSourceInfoId(CreateGoodsResourceVO goodsResourceVO);

    ResultUtil selectProjectGoodsSource(Integer projectId);

    //List<TGoodsSourceInfoDTO> pcGoodsSourceList(PCCreateGoodsResourceVO search);

    ResultUtil takeOffSource(TGoodsSourceInfoBanRecord record);

    //List<PCTGoodsSourceInfoVO> pcGoodsSourceList(PCCreateGoodsResourceVO search);
    ResultUtil pcGoodsSourceList(PCCreateGoodsResourceVO search);

    ResultUtil getLineByCompanyId(Integer companyId);

    ResultUtil getInfoById(Integer lineGoodsId);

    TGoodsSourceInfo getDataByGoodsSourceCode(String goodsSourceCode);

    void updateGoodsSourceById(TGoodsSourceInfo goodsSource);

    ResultUtil getCompanyList();

    ResultUtil getSourceByCompanyId(TGoodsSourceInfo search);

    GoodSourceCodeAndLineTypeVO selectGoodSourceCodeAndLineTypeById(Integer sourceId);

    List<TGoodsSourceInfo> getDataByLineId(Integer lineId);

    TGoodsSourceInfo getDataByIds(Integer lineId, Integer companyId, Integer goodsType);
}
