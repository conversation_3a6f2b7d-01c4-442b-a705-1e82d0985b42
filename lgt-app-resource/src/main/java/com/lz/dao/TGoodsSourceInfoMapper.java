package com.lz.dao;

import com.lz.dto.*;
import com.lz.model.TCompanyProject;
import com.lz.model.TGoodsSourceInfo;
import com.lz.model.TLineInfo;
import com.lz.vo.*;
import com.lz.vo.companypc.PCCreateGoodsResourceVO;
import com.lz.vo.companypc.PCTGoodsSourceInfoVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TGoodsSourceInfoMapper {

    /**
     * 新建货源 - 根据选择的线路获取
     * 线路上已使用的货物
     * 前端不能再此线路上使用此货物
     * Yan
     * @param lineId
     * @return
     */
    List<Integer> getGoodsByLineId(Integer lineId);

    /**
     * 发单后修改货源使用次数
     * Yan
     * @param sourceId
     * @return
     */
    int auditUseNumber(Integer sourceId);

    int editSacnSourceOrder(@Param("sourceId") Integer sourceId);

    /**
     * APP发单 根据企业ID 查询货源信息
     * Yan
     * @param companyId
     * @return
     */
    List<CompanySourceDTO> appGetCompanyGoodsSource(@Param("companyId") Integer companyId,@Param("accountId") Integer accountId);

    /**
     * WX 扫码获取货源信息
     * Yan
     * @param code
     * @return
     */
    GoodsSourceDTO wxSelectWaybillInfo(String code);

    TCompanyProject selectProjectByCode(String code);

    GoodsSourceDTO selectByProjectId(String code);

    /**
     * 根据线路信息ID查询数据
     * @param lineGoodsRelId
     * @return
     */
    TGoodsSourceInfo selectByLineGoodsId(Integer lineGoodsRelId);

    /**
     * 批量逻辑删除
     * @param deleteGoodsResourceVO
     * @return
     */
    int logicDeleteByLineGoodsRule(DeleteGoodsResourceVO deleteGoodsResourceVO);

    /**
     * 查询回显数据 进行编辑
     * @param lineGoodsId
     * @return
     */
    CreateGoodsResourceVO selectEditGoodsSourceInfo(Integer lineGoodsId);

    /**
     * 根据 lineGoodsId 更新
     * @param record
     * @return
     */
    int updateByLineGoodsIdSelective(TGoodsSourceInfo record);

    int deleteByPrimaryKey(Integer id);

    int insert(TGoodsSourceInfo record);

    int insertSelective(TGoodsSourceInfo record);

    TGoodsSourceInfo selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TGoodsSourceInfo record);

    int updateByPrimaryKey(TGoodsSourceInfo record);

    List<GoodsSourceDTO> selectGoodsSourceByCode(@Param("code") String code);

    List<TGoodsSourceInfoVo> findByCompanyId(@Param("companyId") List<String> companyId);

    CompanySourceDTO selectGoodsSourceAndLineInfo(@Param(value = "id") Integer id);

    CompanySourceDTO selectGoodsSourceAndLineInfoByCode(@Param(value = "code") String code);

    List<TGoodsSourceInfo> findGoodsSource();

    List<CompanySourceDTO> appGetAdminCompanyGoodsSource(@Param("companyId") Integer companyId);

    List<CompanySourceDTO> selectSourceByProjectId(@Param("projectId") String code);

    List<CompanySourceDTO> selectGoodsSourceInfoByPublisherId(@Param("publisherId") Integer publisherId);

    List<EndCarUserDTO> selectGoodsSourceVehicleDriverInfoMapper(@Param("goodsSourceCode") String goodsSourceCode);

    int deleteGoodsSourceInfoByDeleteOrderTemplate(@Param("code") String code);

    List<TGoodsSourceInfo> selectGoodsSourceInfo(TGoodsSourceInfo record);

    List<CompanySourceDTO> selectGoodsSourceInfoAndException(TGoodsSourceInfoVo record);

    CompanySourceDTO selectGoodsSourceBySourceCode(@Param(value = "code") String code);

    //根据企业id查询货源
    List<GoodsSourceDTO> selectGoodsSourceByCompanyId(@Param("companyId") Integer companyId);

    //根据货源id查询易煤网需要的信息
    List<YmGoodsSourceDTO> selectByGoodSourceInfoId(@Param("goodSourceInfoId") String goodSourceInfoId);

    List<TGoodsSourceInfo> selectProjectGoodsSource(@Param("projectId") Integer projectId);

    ResourceHallSourceDetailDTO detail(@Param("goodsSourceId") Integer goodsSourceId);

    List<ResourceHallSourceDetailDTO> selectResourceHallByPage(ResourceHallSearchVO resourceHallSearchVO);

    //货源大厅列表
    List<PCTGoodsSourceInfoVO> pcGoodsSourceList(PCCreateGoodsResourceVO search);

    List<TLineInfo> getLineByCompanyId(@Param("companyId") Integer companyId);

    PCCreateGoodsResourceVO getInfoById(@Param("lineGoodsId") Integer lineGoodsId);

    TGoodsSourceInfo getDataByGoodsSourceCode(@Param("goodsSourceCode") String goodsSourceCode);

    List<PCTGoodsSourceInfoVO> getCompanyList();

    List<TGoodsSourceInfoVo> getSourceByCompanyId(@Param("companyId") Integer companyId);

    List<TGoodsSourceInfo> getDataByLineId(@Param("lineId") Integer lineId);

    GoodSourceCodeAndLineTypeVO selectGoodSourceCodeAndLineTypeById(@Param("sourceId") Integer sourceId);

    TGoodsSourceInfo getDataByIds(@Param("lineId") Integer lineId,
                                  @Param("companyId") Integer companyId,
                                  @Param("goodsId") Integer goodsId);
}