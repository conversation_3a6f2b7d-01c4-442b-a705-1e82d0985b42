<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TCompanyProjectMapper">
  <resultMap id="BaseResultMap" type="com.lz.model.TCompanyProject">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="project_feature" jdbcType="VARCHAR" property="projectFeature" />
    <result column="project_online_contract_beginning_date" jdbcType="DATE" property="projectOnlineContractBeginningDate" />
    <result column="project_online_contract_end_date" jdbcType="DATE" property="projectOnlineContractEndDate" />
    <result column="project_join_marketing_person" jdbcType="VARCHAR" property="projectJoinMarketingPerson" />
    <result column="project_join_implement_person" jdbcType="VARCHAR" property="projectJoinImplementPerson" />
    <result column="stop_flag" jdbcType="BIT" property="stopFlag" />
    <result column="line_type" jdbcType="VARCHAR" property="lineType" />
    <result column="capital_transfer_type" jdbcType="VARCHAR" property="capitalTransferType" />
    <result column="withdraw_type" jdbcType="VARCHAR" property="withdrawType" />
    <result column="contract_photo2" jdbcType="VARCHAR" property="contractPhoto2" />
    <result column="other_content" jdbcType="VARCHAR" property="otherContent" />
    <result column="pay_method" jdbcType="VARCHAR" property="payMethod" />
    <result column="credit_line" jdbcType="DECIMAL" property="creditLine" />
    <result column="after_use_left_limit" jdbcType="DECIMAL" property="afterUseLeftLimit" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="credit_days" jdbcType="INTEGER" property="creditDays" />
    <result column="pay_deadline" jdbcType="INTEGER" property="payDeadline" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="workflow_id" jdbcType="INTEGER" property="workflowId" />
    <result column="audit_status" jdbcType="VARCHAR" property="auditStatus" />
    <result column="audit_opinion" jdbcType="VARCHAR" property="auditOpinion" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="param1" jdbcType="VARCHAR" property="param1" />
    <result column="param2" jdbcType="VARCHAR" property="param2" />
    <result column="param3" jdbcType="VARCHAR" property="param3" />
    <result column="param4" jdbcType="VARCHAR" property="param4" />
    <result column="operate_method" jdbcType="VARCHAR" property="operateMethod" />
    <result column="operator_ip" jdbcType="VARCHAR" property="operatorIp" />
    <result column="payment_process" jdbcType="INTEGER" property="paymentProcess" />
    <result column="check_user_car_status" property="checkUserCarStatus" jdbcType="BIT" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="transport_identity_check" jdbcType="BIT" property="transportIdentityCheck" />
    <result column="sign_contract_time" jdbcType="INTEGER" property="signContractTime" />
    <result column="business_assist" jdbcType="BIT" property="businessAssist" />
    <result column="capital_transfer_pattern" jdbcType="VARCHAR" property="capitalTransferPattern" />
    <result column="update_service_fee" jdbcType="BIT" property="updateServiceFee" />
    <result column="query_trajectory" jdbcType="BIT" property="queryTrajectory" />
    <result column="freight_price" jdbcType="BIT" property="freightPrice" />
    <result column="open_project_qrcode" jdbcType="BIT" property="openProjectQrcode" />
    <result column="project_qrcode_weixin" jdbcType="VARCHAR" property="projectQrcodeWeixin" />
    <result column="project_qrcode_app" jdbcType="VARCHAR" property="projectQrcodeApp" />
    <result column="if_quota" jdbcType="BIT" property="ifQuota" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.lz.model.TCompanyProjectWithBLOBs">
    <result column="project_online_program" jdbcType="LONGVARCHAR" property="projectOnlineProgram" />
    <result column="contract_photo1" jdbcType="LONGVARCHAR" property="contractPhoto1" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, project_code, project_name, project_feature, project_online_contract_beginning_date, 
    project_online_contract_end_date, project_join_marketing_person, project_join_implement_person, 
    stop_flag, line_type, capital_transfer_type, withdraw_type, contract_photo2, other_content, 
    pay_method, credit_line, after_use_left_limit, start_time, credit_days, pay_deadline, 
    remark, workflow_id, audit_status, audit_opinion, audit_time, param1, param2, param3, 
    param4, operate_method, operator_ip, payment_process, check_user_car_status, create_user, create_time, update_user, update_time,
    `enable`, transport_identity_check, sign_contract_time,query_trajectory,freight_price,open_project_qrcode,project_qrcode_weixin,project_qrcode_app,if_quota
  </sql>
  <sql id="Blob_Column_List">
    project_online_program, contract_photo1,capital_transfer_pattern,business_assist,update_service_fee,transport_identity_check,sign_contract_time,update_service_fee,transport_identity_check
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from t_company_project
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from t_company_project
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.lz.model.TCompanyProjectWithBLOBs" useGeneratedKeys="true">
    insert into t_company_project (company_id, project_code, project_name, 
      project_feature, project_online_contract_beginning_date, 
      project_online_contract_end_date, project_join_marketing_person, 
      project_join_implement_person, stop_flag, line_type, 
      capital_transfer_type, withdraw_type, contract_photo2, 
      other_content, pay_method, credit_line, 
      after_use_left_limit, start_time, credit_days, 
      pay_deadline, remark, workflow_id, 
      audit_status, audit_opinion, audit_time, 
      param1, param2, param3, 
      param4, operate_method, operator_ip, payment_process, check_user_car_status,
      create_user, create_time, update_user, 
      update_time, `enable`, project_online_program, 
      contract_photo1, transport_identity_check, sign_contract_time, query_trajectory,freight_price,open_project_qrcode,project_qrcode_weixin,project_qrcode_app,if_quota)
    values (#{companyId,jdbcType=INTEGER}, #{projectCode,jdbcType=VARCHAR}, #{projectName,jdbcType=VARCHAR},
      #{projectFeature,jdbcType=VARCHAR}, #{projectOnlineContractBeginningDate,jdbcType=DATE}, 
      #{projectOnlineContractEndDate,jdbcType=DATE}, #{projectJoinMarketingPerson,jdbcType=VARCHAR}, 
      #{projectJoinImplementPerson,jdbcType=VARCHAR}, #{stopFlag,jdbcType=BIT}, #{lineType,jdbcType=VARCHAR}, 
      #{capitalTransferType,jdbcType=VARCHAR}, #{withdrawType,jdbcType=VARCHAR}, #{contractPhoto2,jdbcType=VARCHAR}, 
      #{otherContent,jdbcType=VARCHAR}, #{payMethod,jdbcType=VARCHAR}, #{creditLine,jdbcType=DECIMAL}, 
      #{afterUseLeftLimit,jdbcType=DECIMAL}, #{startTime,jdbcType=TIMESTAMP}, #{creditDays,jdbcType=INTEGER}, 
      #{payDeadline,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{workflowId,jdbcType=INTEGER}, 
      #{auditStatus,jdbcType=VARCHAR}, #{auditOpinion,jdbcType=VARCHAR}, #{auditTime,jdbcType=TIMESTAMP}, 
      #{param1,jdbcType=VARCHAR}, #{param2,jdbcType=VARCHAR}, #{param3,jdbcType=VARCHAR}, 
      #{param4,jdbcType=VARCHAR}, #{operateMethod,jdbcType=VARCHAR}, #{operatorIp,jdbcType=VARCHAR},
      #{paymentProcess,jdbcType=INTEGER},#{checkUserCarStatus,jdbcType=BIT},#{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{enable,jdbcType=BIT},
      #{projectOnlineProgram,jdbcType=LONGVARCHAR}, #{contractPhoto1,jdbcType=LONGVARCHAR}, #{transportIdentityCheck,jdbcType=BIT}, #{signContractTime,jdbcType=INTEGER},
      #{queryTrajectory,jdbcType=INTEGER}),#{freightPrice,jdbcType=BIT},#{openProjectQrcode},#{projectQrcodeWeixin},#{projectQrcodeApp},#{ifQuota,jdbcType=BIT}
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.lz.model.TCompanyProjectWithBLOBs" useGeneratedKeys="true">
    insert into t_company_project
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="projectCode != null">
        project_code,
      </if>
      <if test="projectName != null">
        project_name,
      </if>
      <if test="projectFeature != null">
        project_feature,
      </if>
      <if test="projectOnlineContractBeginningDate != null">
        project_online_contract_beginning_date,
      </if>
      <if test="projectOnlineContractEndDate != null">
        project_online_contract_end_date,
      </if>
      <if test="projectJoinMarketingPerson != null">
        project_join_marketing_person,
      </if>
      <if test="projectJoinImplementPerson != null">
        project_join_implement_person,
      </if>
      <if test="stopFlag != null">
        stop_flag,
      </if>
      <if test="lineType != null">
        line_type,
      </if>
      <if test="capitalTransferType != null">
        capital_transfer_type,
      </if>
      <if test="withdrawType != null">
        withdraw_type,
      </if>
      <if test="contractPhoto2 != null">
        contract_photo2,
      </if>
      <if test="otherContent != null">
        other_content,
      </if>
      <if test="payMethod != null">
        pay_method,
      </if>
      <if test="creditLine != null">
        credit_line,
      </if>
      <if test="afterUseLeftLimit != null">
        after_use_left_limit,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="creditDays != null">
        credit_days,
      </if>
      <if test="payDeadline != null">
        pay_deadline,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="workflowId != null">
        workflow_id,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="auditOpinion != null">
        audit_opinion,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="param1 != null">
        param1,
      </if>
      <if test="param2 != null">
        param2,
      </if>
      <if test="param3 != null">
        param3,
      </if>
      <if test="param4 != null">
        param4,
      </if>
      <if test="operateMethod != null">
        operate_method,
      </if>
      <if test="operatorIp != null">
        operator_ip,
      </if>
      <if test="paymentProcess != null">
        payment_process,
      </if>
      <if test="checkUserCarStatus != null">
        check_user_car_status,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="projectOnlineProgram != null">
        project_online_program,
      </if>
      <if test="contractPhoto1 != null">
        contract_photo1,
      </if>
      <if test="transportIdentityCheck != null">
        transport_identity_check,
      </if>
      <if test="signContractTime != null">
        sign_contract_time,
      </if>
      <if test="businessAssist != null">
        business_assist,
      </if>
      <if test="updateServiceFee != null">
        update_service_fee,
      </if>
      <if test="capitalTransferPattern != null">
        capital_transfer_pattern,
      </if>
      <if test="queryTrajectory != null">
        query_trajectory,
      </if>
      <if test="freightPrice != null">
        freight_price,
      </if>
      <if test="openProjectQrcode != null">
        open_project_qrcode,
      </if>
      <if test="projectQrcodeWeixin != null">
        project_qrcode_weixin,
      </if>
      <if test="projectQrcodeApp != null">
        project_qrcode_app,
      </if>
      <if test="ifQuota != null">
        if_quota,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="projectCode != null">
        #{projectCode,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="projectFeature != null">
        #{projectFeature,jdbcType=VARCHAR},
      </if>
      <if test="projectOnlineContractBeginningDate != null">
        #{projectOnlineContractBeginningDate,jdbcType=DATE},
      </if>
      <if test="projectOnlineContractEndDate != null">
        #{projectOnlineContractEndDate,jdbcType=DATE},
      </if>
      <if test="projectJoinMarketingPerson != null">
        #{projectJoinMarketingPerson,jdbcType=VARCHAR},
      </if>
      <if test="projectJoinImplementPerson != null">
        #{projectJoinImplementPerson,jdbcType=VARCHAR},
      </if>
      <if test="stopFlag != null">
        #{stopFlag,jdbcType=BIT},
      </if>
      <if test="lineType != null">
        #{lineType,jdbcType=VARCHAR},
      </if>
      <if test="capitalTransferType != null">
        #{capitalTransferType,jdbcType=VARCHAR},
      </if>
      <if test="withdrawType != null">
        #{withdrawType,jdbcType=VARCHAR},
      </if>
      <if test="contractPhoto2 != null">
        #{contractPhoto2,jdbcType=VARCHAR},
      </if>
      <if test="otherContent != null">
        #{otherContent,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null">
        #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="creditLine != null">
        #{creditLine,jdbcType=DECIMAL},
      </if>
      <if test="afterUseLeftLimit != null">
        #{afterUseLeftLimit,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creditDays != null">
        #{creditDays,jdbcType=INTEGER},
      </if>
      <if test="payDeadline != null">
        #{payDeadline,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="workflowId != null">
        #{workflowId,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="auditOpinion != null">
        #{auditOpinion,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="param1 != null">
        #{param1,jdbcType=VARCHAR},
      </if>
      <if test="param2 != null">
        #{param2,jdbcType=VARCHAR},
      </if>
      <if test="param3 != null">
        #{param3,jdbcType=VARCHAR},
      </if>
      <if test="param4 != null">
        #{param4,jdbcType=VARCHAR},
      </if>
      <if test="operateMethod != null">
        #{operateMethod,jdbcType=VARCHAR},
      </if>
      <if test="operatorIp != null">
        #{operatorIp,jdbcType=VARCHAR},
      </if>
      <if test="paymentProcess != null">
        #{paymentProcess,jdbcType=INTEGER},
      </if>
      <if test="checkUserCarStatus != null">
        #{checkUserCarStatus,jdbcType=BIT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="projectOnlineProgram != null">
        #{projectOnlineProgram,jdbcType=LONGVARCHAR},
      </if>
      <if test="contractPhoto1 != null">
        #{contractPhoto1,jdbcType=LONGVARCHAR},
      </if>
      <if test="transportIdentityCheck != null">
        #{transportIdentityCheck,jdbcType=BIT},
      </if>
      <if test="signContractTime != null">
        #{signContractTime,jdbcType=INTEGER},
      </if>
      <if test="businessAssist != null">
        #{businessAssist,jdbcType=BIT},
      </if>
      <if test="updateServiceFee != null">
        #{updateServiceFee},
      </if>
      <if test="capitalTransferPattern != null">
        #{capitalTransferPattern},
      </if>
      <if test="queryTrajectory != null">
        #{queryTrajectory},
      </if>
      <if test="freightPrice != null">
        #{freightPrice,jdbcType=BIT},
      </if>
      <if test="openProjectQrcode != null">
        #{openProjectQrcode},
      </if>
      <if test="projectQrcodeWeixin != null">
        #{projectQrcodeWeixin},
      </if>
      <if test="projectQrcodeApp != null">
        #{projectQrcodeApp},
      </if>
      <if test="ifQuota != null">
        #{ifQuota,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lz.model.TCompanyProjectWithBLOBs">
    update t_company_project
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="projectCode != null">
        project_code = #{projectCode,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        project_name = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="projectFeature != null">
        project_feature = #{projectFeature,jdbcType=VARCHAR},
      </if>
      <if test="projectOnlineContractBeginningDate != null">
        project_online_contract_beginning_date = #{projectOnlineContractBeginningDate,jdbcType=DATE},
      </if>
      <if test="projectOnlineContractEndDate != null">
        project_online_contract_end_date = #{projectOnlineContractEndDate,jdbcType=DATE},
      </if>
      <if test="projectJoinMarketingPerson != null">
        project_join_marketing_person = #{projectJoinMarketingPerson,jdbcType=VARCHAR},
      </if>
      <if test="projectJoinImplementPerson != null">
        project_join_implement_person = #{projectJoinImplementPerson,jdbcType=VARCHAR},
      </if>
      <if test="stopFlag != null">
        stop_flag = #{stopFlag,jdbcType=BIT},
      </if>
      <if test="lineType != null">
        line_type = #{lineType,jdbcType=VARCHAR},
      </if>
      <if test="capitalTransferType != null">
        capital_transfer_type = #{capitalTransferType,jdbcType=VARCHAR},
      </if>
      <if test="withdrawType != null">
        withdraw_type = #{withdrawType,jdbcType=VARCHAR},
      </if>
      <if test="contractPhoto2 != null">
        contract_photo2 = #{contractPhoto2,jdbcType=VARCHAR},
      </if>
      <if test="otherContent != null">
        other_content = #{otherContent,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null">
        pay_method = #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="creditLine != null">
        credit_line = #{creditLine,jdbcType=DECIMAL},
      </if>
      <if test="afterUseLeftLimit != null">
        after_use_left_limit = #{afterUseLeftLimit,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creditDays != null">
        credit_days = #{creditDays,jdbcType=INTEGER},
      </if>
      <if test="payDeadline != null">
        pay_deadline = #{payDeadline,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="workflowId != null">
        workflow_id = #{workflowId,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="auditOpinion != null">
        audit_opinion = #{auditOpinion,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="param1 != null">
        param1 = #{param1,jdbcType=VARCHAR},
      </if>
      <if test="param2 != null">
        param2 = #{param2,jdbcType=VARCHAR},
      </if>
      <if test="param3 != null">
        param3 = #{param3,jdbcType=VARCHAR},
      </if>
      <if test="param4 != null">
        param4 = #{param4,jdbcType=VARCHAR},
      </if>
      <if test="operateMethod != null">
        operate_method = #{operateMethod,jdbcType=VARCHAR},
      </if>
      <if test="operatorIp != null">
        operator_ip = #{operatorIp,jdbcType=VARCHAR},
      </if>
      <if test="paymentProcess != null">
        payment_process = #{paymentProcess,jdbcType=INTEGER},
      </if>
      <if test="checkUserCarStatus != null">
        check_user_car_status = #{checkUserCarStatus,jdbcType=BIT},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
      <if test="projectOnlineProgram != null">
        project_online_program = #{projectOnlineProgram,jdbcType=LONGVARCHAR},
      </if>
      <if test="contractPhoto1 != null">
        contract_photo1 = #{contractPhoto1,jdbcType=LONGVARCHAR},
      </if>
      <if test="transportIdentityCheck != null">
        transport_identity_check = #{transportIdentityCheck,jdbcType=BIT},
      </if>
      <if test="signContractTime != null">
        sign_contract_time = #{signContractTime,jdbcType=INTEGER},
      </if>
      <if test="businessAssist != null">
        business_assist=#{businessAssist,jdbcType=BIT},
      </if>
      <if test="updateServiceFee != null">
        update_service_fee=#{updateServiceFee},
      </if>
      <if test="capitalTransferPattern != null">
        capital_transfer_pattern=#{capitalTransferPattern},
      </if>
      <if test="queryTrajectory != null">
        query_trajectory=#{queryTrajectory},
      </if>
      <if test="freightPrice != null">
        freight_price=#{freightPrice,jdbcType=BIT},
      </if>
      <if test="openProjectQrcode != null">
        open_project_qrcode = #{openProjectQrcode},
      </if>
      <if test="projectQrcodeWeixin != null">
        project_qrcode_weixin =#{projectQrcodeWeixin},
      </if>
      <if test="projectQrcodeApp != null">
        project_qrcode_app =#{projectQrcodeApp},
      </if>
      <if test="ifQuota != null">
        if_quota=#{ifQuota,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.lz.model.TCompanyProjectWithBLOBs">
    update t_company_project
    set company_id = #{companyId,jdbcType=INTEGER},
      project_code = #{projectCode,jdbcType=VARCHAR},
      project_name = #{projectName,jdbcType=VARCHAR},
      project_feature = #{projectFeature,jdbcType=VARCHAR},
      project_online_contract_beginning_date = #{projectOnlineContractBeginningDate,jdbcType=DATE},
      project_online_contract_end_date = #{projectOnlineContractEndDate,jdbcType=DATE},
      project_join_marketing_person = #{projectJoinMarketingPerson,jdbcType=VARCHAR},
      project_join_implement_person = #{projectJoinImplementPerson,jdbcType=VARCHAR},
      stop_flag = #{stopFlag,jdbcType=BIT},
      line_type = #{lineType,jdbcType=VARCHAR},
      capital_transfer_type = #{capitalTransferType,jdbcType=VARCHAR},
      withdraw_type = #{withdrawType,jdbcType=VARCHAR},
      contract_photo2 = #{contractPhoto2,jdbcType=VARCHAR},
      other_content = #{otherContent,jdbcType=VARCHAR},
      pay_method = #{payMethod,jdbcType=VARCHAR},
      credit_line = #{creditLine,jdbcType=DECIMAL},
      after_use_left_limit = #{afterUseLeftLimit,jdbcType=DECIMAL},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      credit_days = #{creditDays,jdbcType=INTEGER},
      pay_deadline = #{payDeadline,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      workflow_id = #{workflowId,jdbcType=INTEGER},
      audit_status = #{auditStatus,jdbcType=VARCHAR},
      audit_opinion = #{auditOpinion,jdbcType=VARCHAR},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      param1 = #{param1,jdbcType=VARCHAR},
      param2 = #{param2,jdbcType=VARCHAR},
      param3 = #{param3,jdbcType=VARCHAR},
      param4 = #{param4,jdbcType=VARCHAR},
      operate_method = #{operateMethod,jdbcType=VARCHAR},
      operator_ip = #{operatorIp,jdbcType=VARCHAR},
      payment_process = #{paymentProcess,jdbcType=INTEGER},
      check_user_car_status = #{checkUserCarStatus,jdbcType=BIT},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      `enable` = #{enable,jdbcType=BIT},
      project_online_program = #{projectOnlineProgram,jdbcType=LONGVARCHAR},
      contract_photo1 = #{contractPhoto1,jdbcType=LONGVARCHAR},
      query_trajectory=#{queryTrajectory},
      freight_price=#{freightPrice},open_project_qrcode=#{openProjectQrcode},project_qrcode_weixin=#{projectQrcodeWeixin},project_qrcode_app=#{projectQrcodeApp},if_quota=#{ifQuota}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lz.model.TCompanyProject">
    update t_company_project
    set company_id = #{companyId,jdbcType=INTEGER},
      project_code = #{projectCode,jdbcType=VARCHAR},
      project_name = #{projectName,jdbcType=VARCHAR},
      project_feature = #{projectFeature,jdbcType=VARCHAR},
      project_online_contract_beginning_date = #{projectOnlineContractBeginningDate,jdbcType=DATE},
      project_online_contract_end_date = #{projectOnlineContractEndDate,jdbcType=DATE},
      project_join_marketing_person = #{projectJoinMarketingPerson,jdbcType=VARCHAR},
      project_join_implement_person = #{projectJoinImplementPerson,jdbcType=VARCHAR},
      stop_flag = #{stopFlag,jdbcType=BIT},
      line_type = #{lineType,jdbcType=VARCHAR},
      capital_transfer_type = #{capitalTransferType,jdbcType=VARCHAR},
      withdraw_type = #{withdrawType,jdbcType=VARCHAR},
      contract_photo1 = #{contractPhoto1,jdbcType=VARCHAR},
      contract_photo2 = #{contractPhoto2,jdbcType=VARCHAR},
      other_content = #{otherContent,jdbcType=VARCHAR},
      pay_method = #{payMethod,jdbcType=VARCHAR},
      credit_line = #{creditLine,jdbcType=DECIMAL},
      after_use_left_limit = #{afterUseLeftLimit,jdbcType=DECIMAL},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      credit_days = #{creditDays,jdbcType=INTEGER},
      pay_deadline = #{payDeadline,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      workflow_id = #{workflowId,jdbcType=INTEGER},
      audit_status = #{auditStatus,jdbcType=VARCHAR},
      audit_opinion = #{auditOpinion,jdbcType=VARCHAR},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      param1 = #{param1,jdbcType=VARCHAR},
      param2 = #{param2,jdbcType=VARCHAR},
      param3 = #{param3,jdbcType=VARCHAR},
      param4 = #{param4,jdbcType=VARCHAR},
      operate_method = #{operateMethod,jdbcType=VARCHAR},
      operator_ip = #{operatorIp,jdbcType=VARCHAR},
      payment_process = #{paymentProcess,jdbcType=INTEGER},
      check_user_car_status = #{checkUserCarStatus,jdbcType=BIT},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      `enable` = #{enable,jdbcType=BIT},
      transport_identity_check = #{transportIdentityCheck,jdbcType=BIT},
      sign_contract_time = #{signContractTime,jdbcType=INTEGER},
      query_trajectory=#{queryTrajectory},
      freight_price=#{freightPrice},open_project_qrcode=#{openProjectQrcode},project_qrcode_weixin=#{projectQrcodeWeixin},project_qrcode_app=#{projectQrcodeApp},if_quota=#{ifQuota}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>