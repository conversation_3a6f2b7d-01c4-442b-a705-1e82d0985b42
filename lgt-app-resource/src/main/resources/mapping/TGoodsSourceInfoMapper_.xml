<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TGoodsSourceInfoMapper">

    <select id="pcGoodsSourceList" resultType="com.lz.vo.companypc.PCTGoodsSourceInfoVO" parameterType="com.lz.vo.companypc.PCCreateGoodsResourceVO">
        select
        tcp.project_name,tli.line_name,tci.company_name,tgsi.id, tgsi.code, tgsi.pid, tgsi.line_goods_rel_id, tgsi.line_id, tgsi.company_id,tgsi.deliver_goods_contacter,
        tgsi.deliver_goods_contacter_phone,tgsi.receive_goods_contacter, tgsi.receive_goods_contacter_phone, tgsi.goods_id,
        tgsi.goods_name, tgsi.big_kind_code,tgsi.goods_unit, tgsi.estimate_goods_weight, tgsi.current_carriage_unit_price,
        tgsi.project_goods_order,tgsi.qrcode_photo, tgsi.line_other_user_enable, tgsi.status, tgsi.status_operate_time,
        tgsi.goods_ban_time,tgsi.scan_code_times,tgsi.total_send_receipt, tgsi.company_clients, tgsi.company_customer_name,
        tgsi.goods_timeliness,tgsi.vehicle_type_require,tgsi.vehicle_length_require, tgsi.auto_select_transporter,
        tgsi.template_name, tgsi.template_code,tgsi.template_expire_time,tgsi.template_status, tgsi.create_location,
        tgsi.publish_company_id, tgsi.publisher_id, tgsi.publish_time,tgsi.order_gen_type, tgsi.sign_contract,tgsi.if_electronic_fence, tgsi.remark, tgsi.param1,
        tgsi.param2, tgsi.param3, tgsi.param4,tgsi.operate_method,tgsi.operator_ip, tgsi.create_user, tgsi.create_time, tgsi.update_user,
        tgsi.update_time, tgsi.enable  from t_goods_source_info as tgsi
        left join t_line_info as tli on tgsi.line_id=tli.id
        left join t_company_line as tcl on tgsi.line_id=tcl.line_info_id
        left join t_company_project as tcp on tcl.project_id=tcp.id
        left join t_company_info as tci on tci.id=tgsi.company_id
        where tgsi.enable=0 and tgsi.line_id in (select tcl.line_info_id from t_company_line)
        <if test="companyId !=null">
            and tgsi.company_id = #{companyId}
        </if>
        <if test="null != companyIds and companyIds.size() > 0">
            and tgsi.company_id in
            <foreach collection="companyIds" index="index" item="companyid" open="(" separator="," close=")">
                #{companyid}
            </foreach>
        </if>
        <if test="lineId !=null">
            and tgsi.line_id=#{lineId}
        </if>
        <if test="goodsId !=null">
            and tgsi.goods_id=#{goodsId}
        </if>
        order by tgsi.id desc
    </select>

  <!--批量逻辑删除-->
  <update id="logicDeleteByLineGoodsRule">
    update t_goods_source_info
    set `enable` = 1
    where exists (
    select *
    from (
    <foreach collection="lineGoodsRelId" item="id" index="index" separator="UNION">
      SELECT #{id} as ids
    </foreach>
    ) a
    where line_goods_rel_id = a.ids
    )
  </update>

  <!--查询货源信息进行修改-->
  <select id="selectEditGoodsSourceInfo" parameterType="int" resultType="com.lz.vo.CreateGoodsResourceVO">
     SELECT
        tgsi.qrcode_photo,
        tgsi.line_goods_rel_id,
        tlgr.line_id AS lineId,
        tlgr.extract_way AS extractWay,
        tlgr.goods_id AS goodsType,
        tlgr.capital_transfer_type AS capitalTransferType,
        tgsi.estimate_goods_weight AS estimateGoodsWeight,
        tlgcc.carriageChangeId,
        case when tlgcc.carriageChangeId is null then tgsi.current_carriage_unit_price else tlgcc.carriage_unit_price end carriageUnitPrice,
        tlgcc.carriage_unit_price_type AS carriageUnitPriceType,
        tlgcc.efficient_time AS efficientTime,
        tlgcp.carriage_pay_type AS carriagePayType,
        tgsi.`status` AS `status`,
        tlgcp.value1 AS value1,
        tlgcp.value2 AS value2,
        tlgcp.value3 AS value3,
        tlgcp.value4 AS value4,
        tlgr.payment_process,
        tlgr.business_assist,
        tlgr.capital_transfer_pattern,
        tlgr.sign_contract_time,
        tlgr.transport_identity_check,
        tlgmr.share_method, tlgmr.share_value,tlgmr.id as managerId,
        teui.id endAgentId, teui.real_name endAgentName, teui.phone endAgentPhone, teui.idcard endAgentIdCard,tlgr.update_service_fee,
        tlgr.query_trajectory,
        tlgr.freight_price,
        tlgr.blockchain_pass,
        tgsi.param1 as wxappQrcodePhoto,
        tlgr.if_load_singin,
        tlgr.if_captain_audit,
        tlgr.etc_invoice,
        tlgr.if_captain_audit,
        tgsi.sign_contract,
        dispose.is_storage,
        dispose.storage_address,
        dispose.is_weigh,
        dispose.weigh_address,
        dispose.id as goodsSourceDisposeId,
        tgsi.id,
        tgsi.if_electronic_fence,
        tgsi.if_send_order_status,
        tgsi.deduction,
        tgsi.illegal,
        tgsi.carriage_price_unit,
        tgsi.fix_cut_fee,
        tgsi.goods_unit_price,
        tgsi.insurance_method,
        tgsi.insured_goods_type
    FROM
        t_line_goods_rel tlgr
    LEFT JOIN t_goods_source_info tgsi ON tlgr.id = tgsi.line_goods_rel_id
    LEFT JOIN (
        SELECT
            t.id carriageChangeId,
            t.line_goods_rel_id,
            t.carriage_unit_price,
            t.carriage_unit_price_type,
            t.efficient_time
        FROM
            t_line_goods_carriage_change t
        WHERE t.line_goods_rel_id = #{lineGoodsId} and t.enable = 0
        ORDER BY
            t.create_time DESC
        LIMIT 1
    ) tlgcc ON 1=1
    LEFT JOIN t_line_goods_carriage_rule_detail tlgcrd ON tlgcrd.line_goods_rel_id = tlgr.id and tlgcrd.`enable` = 0
    LEFT JOIN t_line_goods_carriage_pay tlgcp ON tlgcp.line_goods_rel_id = tlgr.id
    left join t_line_goods_manager_rel tlgmr on tlgr.id = tlgmr.line_goods_rel_id and tlgmr.if_efficient = 0
    left join t_end_user_info teui on tlgmr.manager_id = teui.id
    left join t_goods_source_dispose dispose on dispose.goods_source_info_id = tgsi.id
    WHERE
        tlgr.id =  #{lineGoodsId}
  </select>
  <!--根据 line_goods_rel_id 动态更新-->
  <update id="updateByLineGoodsIdSelective" parameterType="com.lz.model.TGoodsSourceInfo">
    update t_goods_source_info
    <set>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="pid != null">
        pid = #{pid,jdbcType=INTEGER},
      </if>
      <if test="lineId != null">
        line_id = #{lineId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="deliverGoodsContacter != null">
        deliver_goods_contacter = #{deliverGoodsContacter,jdbcType=VARCHAR},
      </if>
      <if test="deliverGoodsContacterPhone != null">
        deliver_goods_contacter_phone = #{deliverGoodsContacterPhone,jdbcType=VARCHAR},
      </if>
      <if test="receiveGoodsContacter != null">
        receive_goods_contacter = #{receiveGoodsContacter,jdbcType=VARCHAR},
      </if>
      <if test="receiveGoodsContacterPhone != null">
        receive_goods_contacter_phone = #{receiveGoodsContacterPhone,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        goods_id = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="bigKindCode != null">
        big_kind_code = #{bigKindCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="estimateGoodsWeight != null">
        estimate_goods_weight = #{estimateGoodsWeight},
      </if>
      <if test="currentCarriageUnitPrice != null">
        current_carriage_unit_price = #{currentCarriageUnitPrice,jdbcType=DECIMAL},
      </if>
        <if test="carriagePriceUnit != null">
            carriage_price_unit = #{carriagePriceUnit,jdbcType=VARCHAR},
        </if>
        <if test="fixCutFee != null">
            fix_cut_fee = #{fixCutFee,jdbcType=DECIMAL},
        </if>
      <if test="projectGoodsOrder != null">
        project_goods_order = #{projectGoodsOrder,jdbcType=INTEGER},
      </if>
      <if test="qrcodePhoto != null">
        qrcode_photo = #{qrcodePhoto,jdbcType=VARCHAR},
      </if>
      <if test="lineOtherUserEnable != null">
        line_other_user_enable = #{lineOtherUserEnable,jdbcType=BIT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="statusOperateTime != null">
        status_operate_time = #{statusOperateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="scanCodeTimes != null">
        scan_code_times = #{scanCodeTimes,jdbcType=INTEGER},
      </if>
      <if test="totalSendReceipt != null">
        total_send_receipt = #{totalSendReceipt,jdbcType=INTEGER},
      </if>
      <if test="companyClients != null">
        company_clients = #{companyClients,jdbcType=VARCHAR},
      </if>
      <if test="companyCustomerName != null">
        company_customer_name = #{companyCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="goodsTimeliness != null">
        goods_timeliness = #{goodsTimeliness,jdbcType=VARCHAR},
      </if>
      <if test="vehicleTypeRequire != null">
        vehicle_type_require = #{vehicleTypeRequire,jdbcType=VARCHAR},
      </if>
      <if test="vehicleLengthRequire != null">
        vehicle_length_require = #{vehicleLengthRequire,jdbcType=INTEGER},
      </if>
      <if test="autoSelectTransporter != null">
        auto_select_transporter = #{autoSelectTransporter,jdbcType=BIT},
      </if>
      <if test="templateName != null">
        template_name = #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="templateCode != null">
        template_code = #{templateCode,jdbcType=VARCHAR},
      </if>
      <if test="templateExpireTime != null">
        template_expire_time = #{templateExpireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="templateStatus != null">
        template_status = #{templateStatus,jdbcType=VARCHAR},
      </if>
      <if test="createLocation != null">
        create_location = #{createLocation,jdbcType=VARCHAR},
      </if>
      <if test="publishCompanyId != null">
        publish_company_id = #{publishCompanyId,jdbcType=INTEGER},
      </if>
      <if test="publisherId != null">
        publisher_id = #{publisherId,jdbcType=INTEGER},
      </if>
      <if test="publishTime != null">
        publish_time = #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderGenType != null">
        order_gen_type = #{orderGenType,jdbcType=INTEGER},
      </if>
      <if test="signContract != null">
          sign_contract = #{signContract},
      </if>
      <if test="ifElectronicFence != null">
          if_electronic_fence = #{ifElectronicFence},
      </if>
      <if test="ifSendOrderStatus != null">
          if_send_order_status = #{ifSendOrderStatus},
      </if>
      <if test="deduction != null">
          deduction = #{deduction,jdbcType=DECIMAL},
      </if>
      <if test="illegal != null">
          illegal = #{illegal,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="param1 != null">
        param1 = #{param1,jdbcType=VARCHAR},
      </if>
      <if test="param2 != null">
        param2 = #{param2,jdbcType=VARCHAR},
      </if>
      <if test="param3 != null">
        param3 = #{param3,jdbcType=VARCHAR},
      </if>
      <if test="param4 != null">
        param4 = #{param4,jdbcType=VARCHAR},
      </if>
      <if test="operateMethod != null">
        operate_method = #{operateMethod,jdbcType=VARCHAR},
      </if>
      <if test="operatorIp != null">
        operator_ip = #{operatorIp,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
        <if test="goodsUnitPrice != null">
            goods_unit_price = #{goodsUnitPrice,jdbcType=DECIMAL},
        </if>
        <if test="insuranceMethod != null">
            insurance_method = #{insuranceMethod,jdbcType=VARCHAR},
        </if>
        <if test="insuredGoodsType != null">
            insured_goods_type = #{insuredGoodsType,jdbcType=VARCHAR},
        </if>
    </set>
    where line_goods_rel_id  = #{lineGoodsRelId,jdbcType=INTEGER}
  </update>
  <!--根据线路信息ID查询信息-->
  <select id="selectByLineGoodsId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
      SELECT
          tgsi.estimate_goods_weight,
          tgsi.code,
          tgsi.qrcode_photo
      FROM
          t_goods_source_info tgsi
      WHERE
         tgsi.line_goods_rel_id = #{lineGoodsRelId}
      AND tgsi.`enable` = 0
  </select>

  <!--微信扫码接单获取货源信息 Yan-->
  <select id="wxSelectWaybillInfo" parameterType="java.lang.String" resultType="com.lz.dto.GoodsSourceDTO">
    SELECT
        tgsi.goods_name, <!-- 货物名称 -->
        tgsi.estimate_goods_weight, <!-- 预估货源重量-->
        tgsi.status, <!-- 货源状态 -->
        tli.project_id, <!-- 项目ID -->
        CONCAT(tli.from_name,'-', tli.end_name) lineName, <!-- 起始点名称-->
        tgsi.publish_time <!-- 发布时间-->
    FROM t_goods_source_info tgsi
      LEFT JOIN t_line_info tli on tgsi.line_id = tli.id AND tgsi.company_id = tli.company_id
    WHERE tgsi.`code` = #{code} AND tgsi.enable = 0
  </select>

  <select id="selectProjectByCode" parameterType="java.lang.String" resultType="com.lz.model.TCompanyProject">
    SELECT
	  tcp.*
    FROM
        t_company_project tcp
    LEFT JOIN t_line_info tli ON tcp.id = tli.project_id
    LEFT JOIN t_goods_source_info tgsi ON tgsi.line_id = tli.id
    WHERE tgsi.`code` = #{code} AND tgsi.enable = 0
  </select>

    <select id="selectByProjectId" parameterType="java.lang.String" resultType="com.lz.dto.GoodsSourceDTO">
        SELECT
            tcp.create_time AS createTime,
            tci.company_name AS companyName
        FROM
            t_company_project tcp
        LEFT JOIN t_company_info tci ON tci.id = tcp.company_id
        WHERE
            tcp.id = #{code}
        AND tcp. ENABLE = 0
    </select>

    <select id="selectGoodsSourceByCode" parameterType="java.lang.String" resultType="com.lz.dto.GoodsSourceDTO">
    select tgsi.company_id, tgsi.id goodsSourceInfoId, tci.company_name, tli.from_name fromName, tli.end_name endName,tli.line_type,
           tgsi.goods_name goodsName, tgsi.current_carriage_unit_price,tgsi.carriage_price_unit,
           tgsi.fix_cut_fee, tlgr.capital_transfer_type, tcp.id projectId,
           tcecr.carrier_id, tli.if_rel_entrust, tli.if_rel_client,tci.if_rel_entrust as companyIfRelEntrust,tci.if_rel_client as companyIfRelClient,
           tcp.pay_method, tgsi.line_goods_rel_id, tgsi.estimate_goods_weight,
           tgsi.estimate_goods_weight, tgsi.status, tlgr.business_assist,tgsi.line_id,
           tgsi.goods_unit_price,tgsi.insurance_method,tgsi.insured_goods_type,
           case
           tlgr.freight_price
           when true then null
           else '1' end as isDisplay, tgsi.sign_contract
    from t_goods_source_info tgsi
        left join t_company_info tci on tgsi.company_id = tci.id
        left join t_line_info tli on tgsi.line_id = tli.id
        LEFT JOIN t_company_project tcp on tli.project_id = tcp.id
        left join t_project_carrier_rel tpcr on tpcr.company_id = tci.id and tpcr.project_id = tcp.id
        LEFT JOIN t_carrier_enduser_company_rel tcecr on tpcr.carrier_company_id = tcecr.id
    left join t_line_goods_rel tlgr on tgsi.line_goods_rel_id = tlgr.id
    where tgsi.code = #{code} and tpcr.if_efficient = 0
  </select>
  <!--APP发单 根据企业ID 查询货源信息-->
  <select id="appGetCompanyGoodsSource" parameterType="java.lang.Integer" resultType="com.lz.dto.CompanySourceDTO">
    SELECT DISTINCT
        tgsi.id as goodsSourceInfoId, <!-- 货源ID -->
        tgsi.line_goods_rel_id,
        CONCAT('[',tgsi.goods_name,']', li.line_name) AS goodsSourceName, <!-- 货源名 -->
        lgcrd.id as ruleId, <!--计算规则Id -->
        tgsi.estimate_goods_weight, <!-- 预估重量 -->
        ifnull(lgcrd.goods_unit_price, tgsi.current_carriage_unit_price) goods_unit_price,
        lgcrd.goods_unit_price, <!--货值单价 -->
        lgcc.id as carriageId, <!--单价变动Id -->
        tgsi.current_carriage_unit_price carriageUnitPrice, <!--运费单价 -->
        tgsi.carriage_price_unit,  tgsi.fix_cut_fee,
        li.project_id, <!--项目Id -->
        li.from_name as fromDetailName, <!-- 起点 -->
        li.end_name as endDetailName, <!-- 终点 -->
        lgr.capital_transfer_type, <!--资金转移方式 -->
        tgsi.goods_name, <!-- 货物大类名 -->
        tgsi.big_kind_code, <!--货物大类CODE -->
        cp.pay_method, <!--费用结算模式 -->
      tci.if_rel_client, tci.if_rel_entrust, tgsi.status, cp.company_id, tcecr.carrier_id, lgr.extract_way, tpcr.dispatch_fee_coefficient,
      lgr.business_assist,
      li.city_from_code, li.city_end_code,
      li.provice_from, li.city_from,
      li.provice_end, li.city_end,
      tgsi.goods_name, tgsi.goods_unit,
      tci.company_name, tci.business_license_no,
      tgsi.param1 as wxappQrcodePhoto
    FROM t_goods_source_info tgsi
    LEFT JOIN t_line_info li ON tgsi.line_id = li.id and li.enable = 0
    LEFT JOIN t_line_goods_carriage_rule_detail lgcrd ON tgsi.line_goods_rel_id = lgcrd.line_goods_rel_id AND lgcrd.`enable` = 0
    LEFT JOIN t_line_goods_carriage_change lgcc ON lgcc.id = (
          SELECT
            lgcc.id
          FROM
            t_line_goods_carriage_change lgcc
          WHERE
            lgcc.line_goods_rel_id = tgsi.line_goods_rel_id and lgcc.enable = 0
          ORDER BY
            lgcc.create_time DESC
          LIMIT 1
    )
    LEFT JOIN t_company_project cp ON li.project_id = cp.id
    left join t_company_info tci on cp.company_id = tci.id
    LEFT JOIN t_line_goods_rel lgr ON tgsi.line_goods_rel_id = lgr.id and lgr.enable = 0
    LEFT JOIN t_line_goods_user_rel lgur ON lgur.company_id = #{companyId} AND lgur.account_info_id = #{accountId} and lgur.`enable` = 0 AND lgur.role_code IN ( SELECT item_code from t_dic_cat_item where param2 = 'FD' union all select 'APPADMIN' as item_code)
    LEFT JOIN t_project_carrier_rel tpcr on li.project_id = tpcr.project_id and tpcr.company_id = li.company_id
    left join t_carrier_enduser_company_rel tcecr on tpcr.carrier_company_id = tcecr.id and tcecr.enable = 0
    WHERE tgsi.company_id = #{companyId} AND tgsi.line_goods_rel_id = lgur.line_goods_rel_id and tpcr.if_efficient = 0 and tgsi.enable = 0
  </select>
    <select id="appGetAdminCompanyGoodsSource" parameterType="java.lang.Integer" resultType="com.lz.dto.CompanySourceDTO">
        SELECT DISTINCT
        tgsi.id as goodsSourceInfoId, <!-- 货源ID -->
        tgsi.line_goods_rel_id,
        CONCAT('[',tgsi.goods_name,']', li.line_name) AS goodsSourceName, <!-- 货源名 -->
        lgcrd.id as ruleId, <!--计算规则Id -->
        tgsi.estimate_goods_weight, <!-- 预估重量 -->
        ifnull(lgcrd.goods_unit_price, tgsi.current_carriage_unit_price) goods_unit_price,
        lgcrd.goods_unit_price, <!--货值单价 -->
        lgcc.id as carriageId, <!--单价变动Id -->
        tgsi.current_carriage_unit_price  carriageUnitPrice, <!--运费单价 -->
        tgsi.carriage_price_unit, tgsi.fix_cut_fee,
        li.project_id, <!--项目Id -->
        li.from_name as fromDetailName, <!-- 起点 -->
        li.end_name as endDetailName, <!-- 终点 -->
        lgr.capital_transfer_type, <!--资金转移方式 -->
        tgsi.goods_name, <!-- 货物大类名 -->
        tgsi.big_kind_code, <!--货物大类CODE -->
        cp.pay_method, <!--费用结算模式 -->
        tci.if_rel_client, tci.if_rel_entrust, tgsi.status, tcecr.carrier_id, lgr.extract_way, tpcr.dispatch_fee_coefficient,
        lgr.business_assist,tgsi.carriage_price_unit,tgsi.fix_cut_fee,
        tgsi.param1 as wxappQrcodePhoto
        FROM t_goods_source_info tgsi
        LEFT JOIN t_line_info li ON tgsi.line_id = li.id and li.enable = 0
        LEFT JOIN t_line_goods_carriage_rule_detail lgcrd ON tgsi.line_goods_rel_id = lgcrd.line_goods_rel_id AND lgcrd.`enable` = 0
        LEFT JOIN t_line_goods_carriage_change lgcc ON lgcc.id = (
            SELECT
                max(lgcc.id)
            FROM
                t_line_goods_carriage_change lgcc
            WHERE
                lgcc.line_goods_rel_id = tgsi.line_goods_rel_id and lgcc.enable = 0
        )
        LEFT JOIN t_company_project cp ON li.project_id = cp.id
        left join t_company_info tci on cp.company_id = tci.id
        LEFT JOIN t_line_goods_rel lgr ON tgsi.line_goods_rel_id = lgr.id and lgr.enable = 0
        LEFT JOIN t_project_carrier_rel tpcr on li.project_id = tpcr.project_id and tpcr.company_id = li.company_id
        left join t_carrier_enduser_company_rel tcecr on tpcr.carrier_company_id = tcecr.id and tcecr.enable = 0
        WHERE
            tgsi.company_id = #{companyId} and tgsi.status = 'OPENSOURCE'  and tpcr.if_efficient = 0 and tgsi.enable = 0
            and tgsi.line_id not in (SELECT line_info_id from t_company_line where company_id = #{companyId})
    </select>

    <select id="selectSourceByProjectId" parameterType="java.lang.String" resultType="com.lz.dto.CompanySourceDTO">
        SELECT DISTINCT
        lgu.id as lineGoodsRelId, <!--线路货物关系ID -->
        li.line_name AS goodsSourceName, <!--企业名 -->
        li.from_name as fromDetailName, <!--起点 -->
        li.end_name as endDetailName, <!--终点 -->
        gsi.current_carriage_unit_price as carriageUnitPrice, <!--运费单价 -->
        lgr.goods_name as goodsName,<!--货物名称 -->
        gsi.code
        FROM t_line_goods_rel lgu
        LEFT JOIN t_line_goods_rel lgr ON  lgu.id = lgr.id
        LEFT JOIN t_goods_source_info gsi ON lgu.id = gsi.line_goods_rel_id
        LEFT JOIN t_line_info li ON gsi.line_id = li.id
        LEFT JOIN t_company_info ci ON li.company_id = ci.id
        LEFT JOIN t_company_project tcp ON li.project_id = tcp.id
        WHERE tcp.id = #{projectId} and gsi.`status` = 'OPENSOURCE' and gsi.enable = 0
    </select>
    <!--发单后修改货源使用次数 Yan-->
   <update id="auditUseNumber" parameterType="java.lang.Integer">
    UPDATE t_goods_source_info gsi
    SET gsi.total_send_receipt = (
        SELECT
            SUM(a.num+1)
        FROM (
            SELECT
            gsi.total_send_receipt as num
        FROM  t_goods_source_info gsi
        WHERE gsi.id = #{sourceId}
        ) a
    )
    WHERE
        gsi.id = #{sourceId}
   </update>

    <update id="editSacnSourceOrder" parameterType="java.lang.Integer">
        UPDATE t_goods_source_info gsi
        SET gsi.total_send_receipt = (
            SELECT
                SUM(a.num+1)
            FROM (
                     SELECT
                         gsi.total_send_receipt as num
                     FROM  t_goods_source_info gsi
                     WHERE gsi.id = #{sourceId}
                 ) a
        )
        WHERE
            gsi.id = #{sourceId}
    </update>
    <!--新建货源 - 根据选择的线路获取，线路上已使用的货物，前端不能再此线路上使用此货物 Yan-->
    <select id="getGoodsByLineId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT
            gsi.goods_id
        FROM t_goods_source_info gsi
        WHERE gsi.line_id = #{lineId} and gsi.enable = 0
    </select>

    <select id="findByCompanyId" parameterType="java.lang.Integer" resultType="com.lz.vo.TGoodsSourceInfoVo">
        SELECT
            a.line_goods_rel_id,
            concat("[",a.goods_name,"]",b.line_name) as sourceName
        FROM
            t_goods_source_info a
        LEFT JOIN t_line_info b ON a.line_id = b.id
        where  1=1 and a.enable=0 and b.enable=0
        <if test="companyId != null">
            and a.company_id IN
            <foreach collection="companyId" item="ci" index="index" open="(" separator="," close=")">
                #{ci}
            </foreach>
        </if>
    </select>

    <select id="selectGoodsSourceAndLineInfo" parameterType="java.lang.Integer" resultType="com.lz.dto.CompanySourceDTO">
        SELECT
            tgsi.id goodsSourceInfoId,
            tgsi.`code`,
            tgsi.line_goods_rel_id,
            tgsi.goods_id,
            tgsi.goods_name,
            tgsi.big_kind_code,
            tgsi.goods_unit,
            tgsi.goods_unit_price,
            tgsi.insurance_method,
            tgsi.insured_goods_type,
            tci.company_name,
            tci.tax_no companyTaxNo,
            tli.id lineId,
            ifnull(tli.project_id, tcl.project_id) projectId,
            tli.line_code,
            tli.line_name,
            tli.line_short_name,
            tli.city_from_code,
            tli.from_name,
            tli.from_coordinates,
            tli.city_end_code,
            tli.end_name,
            tli.end_coordinates,
            tli.estimated_travel_time,
            tli.line_type,
            tgsi.current_carriage_unit_price, tgsi.estimate_goods_weight,
            tgsi.carriage_price_unit,tgsi.fix_cut_fee,
            tgsi.status, tgsi.company_id, tli.provice_from, tli.city_from, tli.country_from, tli.provice_end, tli.city_end, tcp.pay_method,
            tlgr.business_assist,tlgr.capital_transfer_type, tlgr.capital_transfer_pattern, tlgmr.manager_id, tlgmr.share_method, tlgmr.share_value,
            tli.provice_end, tli.city_end, tgsi.create_time goodsCreateTime, tlgr.blockchain_pass,
            tlgcp.carriage_pay_type,
            tlgcp.value1,
            tlgcp.value2,
            tlgcp.value3,
            tlgcp.value4,
            trule.id as line_goods_carriage_rule_id,
            tgsi.sign_contract,tgsi.deduction,tgsi.illegal,
            tcl.id companyLineId, tgsi.deliver_goods_contacter, tgsi.deliver_goods_contacter_phone, tgsi.if_electronic_fence,tgsi.if_send_order_status
        FROM
            t_goods_source_info tgsi
            LEFT JOIN t_line_info tli ON tgsi.line_id = tli.id
            left join t_company_info tci on tgsi.company_id = tci.id
            left join t_company_project tcp on tli.project_id = tcp.id
            left join t_line_goods_rel tlgr on tgsi.line_goods_rel_id = tlgr.id
            left join t_line_goods_carriage_pay tlgcp on tlgcp.line_goods_rel_id = tlgr.id and tlgcp.enable = 0
            left join t_line_goods_carriage_rule trule on trule.line_goods_rel_id = tlgr.id and trule.enable = 0
            left join t_line_goods_manager_rel tlgmr on tlgr.id = tlgmr.line_goods_rel_id and tlgmr.enable = 0 and tlgmr.if_efficient = 0
            left join t_company_line tcl on tgsi.company_id = tcl.company_id and tgsi.line_id = tcl.line_info_id
        WHERE
            tgsi.id = #{id}
    </select>

    <select id="selectGoodsSourceAndLineInfoByCode" parameterType="java.lang.String" resultType="com.lz.dto.CompanySourceDTO">
        SELECT
            tgsi.id goodsSourceInfoId,
            tgsi.`code`,
            tgsi.line_goods_rel_id,
            tgsi.goods_id,
            tgsi.goods_name,
            tgsi.big_kind_code,
            tgsi.goods_unit,
            tci.company_name,
            tci.tax_no companyTaxNo,
            tli.id lineId,
            tli.project_id,
            tli.line_code,
            tli.line_name,
            tli.line_short_name,
            tli.city_from_code,
            tli.from_name,
            tli.from_coordinates,
            tli.city_end_code,
            tli.end_name,
            tli.end_coordinates,
            tli.line_type,
            tgsi.current_carriage_unit_price, tgsi.estimate_goods_weight,
            tgsi.status, tgsi.company_id, tli.provice_from, tli.city_from, tli.country_from, tli.provice_end, tli.city_end, tcp.pay_method,
            tlgr.business_assist, tlgr.capital_transfer_pattern, tlgmr.manager_id, tlgmr.share_method, tlgmr.share_value,
            tli.provice_end, tli.city_end, ifnull(tcp.pay_version, '') payVersion, tgsi.create_time goodsCreateTime,
            tlgr.blockchain_pass, tlgr.capital_transfer_type
        FROM
            t_goods_source_info tgsi
            LEFT JOIN t_line_info tli ON tgsi.line_id = tli.id
            left join t_company_info tci on tgsi.company_id = tci.id
            left join t_company_project tcp on tli.project_id = tcp.id
            left join t_line_goods_rel tlgr on tgsi.line_goods_rel_id = tlgr.id
            left join t_line_goods_manager_rel tlgmr on tlgr.id = tlgmr.line_goods_rel_id and tlgmr.enable = 0 and tlgmr.if_efficient = 0
        WHERE
            tgsi.code = #{code}
    </select>

    <select id="findGoodsSource"  resultType="com.lz.model.TGoodsSourceInfo">
        select * from t_goods_source_info where `enable`=0 and status = 'OPENSOURCE'
    </select>

    <select id="selectGoodsSourceInfoByPublisherId" parameterType="java.lang.Integer" resultType="com.lz.dto.CompanySourceDTO">
        select
         tgsi.id goodsSourceInfoId, tgsi2.code goodsSourceCode, tci.company_name,  tci.id companyId, tgsi2.template_name, tgsi.line_goods_rel_id,
         CONCAT('[',tgsi.goods_name,']', tli.line_name) AS goodsSourceName,
        lgcrd.id as ruleId, <!--计算规则Id -->
        tgsi.estimate_goods_weight, <!-- 预估重量 -->
        ifnull(lgcc.carriage_unit_price, tgsi.current_carriage_unit_price) currentCarriageUnitPrice,
        lgcc.id as carriageId, <!--单价变动Id -->
        tli.project_id, <!--项目Id -->
        tli.from_name as fromDetailName, <!-- 起点 -->
        tli.end_name as endDetailName, <!-- 终点 -->
        lgr.capital_transfer_type, <!--资金转移方式 -->
        tgsi.goods_name, <!-- 货物大类名 -->
        tgsi.big_kind_code, <!--货物大类CODE -->
        cp.pay_method, <!--费用结算模式 -->
        tli.if_rel_client, tli.if_rel_entrust, tgsi.status, cp.company_id, tcecr.carrier_id, lgr.extract_way, tpcr.dispatch_fee_coefficient
        from t_goods_source_info tgsi2
        left join t_goods_source_info tgsi on tgsi2.pid = tgsi.id
        left join t_company_info tci on tgsi.company_id = tci.id
        left join t_line_info tli on tgsi.line_id = tli.id
        LEFT JOIN t_line_goods_carriage_rule_detail lgcrd ON tgsi.line_goods_rel_id = lgcrd.line_goods_rel_id AND lgcrd.`enable` = 0
        LEFT JOIN t_line_goods_carriage_change lgcc ON lgcc.id = (
        SELECT
        lgcc.id
        FROM
        t_line_goods_carriage_change lgcc
        WHERE
        lgcc.line_goods_rel_id = tgsi.line_goods_rel_id
        ORDER BY
        lgcc.create_time DESC
        LIMIT 1
        )
        LEFT JOIN t_company_project cp ON tli.project_id = cp.id
        LEFT JOIN t_line_goods_rel lgr ON tgsi.line_goods_rel_id = lgr.id
        LEFT JOIN t_project_carrier_rel tpcr on tli.project_id = tpcr.project_id and tpcr.company_id = tli.company_id
        left join t_carrier_enduser_company_rel tcecr on tpcr.carrier_company_id = tcecr.id
        WHERE tgsi2.publisher_id = #{publisherId} and tpcr.if_efficient = 0 and tgsi2.enable = 0
    </select>

    <select id="selectGoodsSourceVehicleDriverInfoMapper" parameterType="java.lang.String" resultType="com.lz.dto.EndCarUserDTO">
        select teci.id endcarId, teui.id endDriverId, teci.vehicle_number, teui.real_name, teui.phone, teui.idcard, teucr.id enduserCarRelId,
        teui2.id enduserIdRel, teui2.real_name realNameRel, teui2.phone phoneRel, teui2.idcard idcardRel, teci.vehicle_number carVehicleNumber,
        teui3.id endAgentId, teui3.real_name endAgentName, teui3.phone endAgentPhone, teui3.idcard endAgentIdcard,
        case tdci.value_content when 1 then '可用' else '不可用' end carStatus,
        tdci.value_content carUseable,
        tdci2.value_content userUseable,
        case tdci2.value_content when 1 then '可用' else '不可用' end userStatus,
        case tdci2.value_content when 1 then '' else teus.current_receipts_no end userReceiptsNo,
        case tdci2.value_content when 1 then '' else tosn.page_show_name end userOrderState,
        case tdci2.value_content when 1 then '' else teci2.vehicle_number end userVehicleNumber,
        case tdci.value_content when 1 then '' else tecs.current_receipts_no end carReceiptsNo,
        case tdci.value_content when 1 then '' else tecs.current_driver end carCurrentDriver,
        case tdci.value_content when 1 then '' else tosn2.page_show_name end carOrderState,
        case tdci2.value_content when 1 then '' else teus.current_driver end userCurrentDriver
        from t_goods_source_vehicle_driver_info tgsvdi
        left join t_end_car_info teci on tgsvdi.end_car_id = teci.id and teci.enable = 0
        left join t_end_user_info teui on tgsvdi.end_driver_id = teui.id and teui.enable = 0
        left join t_end_user_car_rel teucr on teci.id = teucr.endcar_id and teucr.data_concel_from is null
            and teucr.enable = 0 and teucr.user_car_relation_type = 'CLSYRSJ' and teucr.enduser_id = teui.id
        left join t_end_user_car_rel teucr2 on teci.id = teucr2.endcar_id and teucr2.data_concel_from is null
            and teucr2.audit_status = 'PASSNODE' and teucr2.enable = 0 and teucr2.user_car_relation_type = 'CLSYRCLB'
        left join t_end_user_info teui2 on teucr2.enduser_id = teui2.id and teui2.enable = 0
        left join t_end_car_status tecs on teci.id = tecs.endcar_id
        left join t_dic_cat_item tdci on tecs.car_status = tdci.item_code
        left join t_end_user_status teus on teui.id = teus.enduser_id
        left join t_dic_cat_item tdci2 on teus.user_status = tdci2.item_code
        left join t_order_info toi on teus.current_receipts_no = toi.order_business_code
        left join t_order_state_node tosn on tosn.code = toi.order_execute_status
        left join t_order_info toi2 on tecs.current_receipts_no = toi2.order_business_code
        left join t_order_state_node tosn2 on toi2.order_execute_status = tosn2.code
        left join t_end_car_info teci2 on toi.vehicle_id = teci2.id
        left join t_end_user_info teui3 on tgsvdi.end_agent_id = teui3.id
        where tgsvdi.goods_source_code = #{goodsSourceCode}
    </select>

    <update id="deleteGoodsSourceInfoByDeleteOrderTemplate" parameterType="java.lang.String">
        update t_goods_source_info set enable = 1
        where code = #{code}
    </update>

    <select id="selectGoodsSourceInfo" parameterType="com.lz.model.TGoodsSourceInfo" resultType="com.lz.model.TGoodsSourceInfo">
        select
        <include refid="Base_Column_List" />
         from t_goods_source_info
        <where>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR}
            </if>
            <if test="pid != null">
                and pid = #{pid,jdbcType=INTEGER}
            </if>
            <if test="lineGoodsRelId != null">
                and line_goods_rel_id = #{lineGoodsRelId,jdbcType=INTEGER}
            </if>
            <if test="lineId != null">
                and line_id = #{lineId,jdbcType=INTEGER}
            </if>
            <if test="companyId != null">
                and company_id = #{companyId,jdbcType=INTEGER}
            </if>
            <if test="deliverGoodsContacter != null">
                and deliver_goods_contacter = #{deliverGoodsContacter,jdbcType=VARCHAR}
            </if>
            <if test="deliverGoodsContacterPhone != null">
                and deliver_goods_contacter_phone = #{deliverGoodsContacterPhone,jdbcType=VARCHAR}
            </if>
            <if test="receiveGoodsContacter != null">
                and receive_goods_contacter = #{receiveGoodsContacter,jdbcType=VARCHAR}
            </if>
            <if test="receiveGoodsContacterPhone != null">
                and receive_goods_contacter_phone = #{receiveGoodsContacterPhone,jdbcType=VARCHAR}
            </if>
            <if test="goodsId != null">
                and goods_id = #{goodsId,jdbcType=INTEGER}
            </if>
            <if test="goodsName != null">
                and goods_name = #{goodsName,jdbcType=VARCHAR}
            </if>
            <if test="bigKindCode != null">
                and big_kind_code = #{bigKindCode,jdbcType=VARCHAR}
            </if>
            <if test="goodsUnit != null">
                and goods_unit = #{goodsUnit,jdbcType=VARCHAR}
            </if>
            <if test="estimateGoodsWeight != null">
                and estimate_goods_weight = #{estimateGoodsWeight,jdbcType=DECIMAL}
            </if>
            <if test="currentCarriageUnitPrice != null">
                and current_carriage_unit_price = #{currentCarriageUnitPrice,jdbcType=DECIMAL}
            </if>
            <if test="projectGoodsOrder != null">
                and project_goods_order = #{projectGoodsOrder,jdbcType=INTEGER}
            </if>
            <if test="qrcodePhoto != null">
                and qrcode_photo = #{qrcodePhoto,jdbcType=VARCHAR}
            </if>
            <if test="lineOtherUserEnable != null">
                and line_other_user_enable = #{lineOtherUserEnable,jdbcType=BIT}
            </if>
            <if test="status != null">
                and `status` = #{status,jdbcType=VARCHAR}
            </if>
            <if test="statusOperateTime != null">
                and status_operate_time = #{statusOperateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="scanCodeTimes != null">
                and scan_code_times = #{scanCodeTimes,jdbcType=INTEGER}
            </if>
            <if test="totalSendReceipt != null">
                and total_send_receipt = #{totalSendReceipt,jdbcType=INTEGER}
            </if>
            <if test="companyClients != null">
                and company_clients = #{companyClients,jdbcType=VARCHAR}
            </if>
            <if test="companyCustomerName != null">
                and company_customer_name = #{companyCustomerName,jdbcType=VARCHAR}
            </if>
            <if test="goodsTimeliness != null">
                and goods_timeliness = #{goodsTimeliness,jdbcType=VARCHAR}
            </if>
            <if test="vehicleTypeRequire != null">
                and vehicle_type_require = #{vehicleTypeRequire,jdbcType=VARCHAR}
            </if>
            <if test="vehicleLengthRequire != null">
                and vehicle_length_require = #{vehicleLengthRequire,jdbcType=INTEGER}
            </if>
            <if test="autoSelectTransporter != null">
                and auto_select_transporter = #{autoSelectTransporter,jdbcType=BIT}
            </if>
            <if test="templateName != null">
                and template_name = #{templateName,jdbcType=VARCHAR}
            </if>
            <if test="templateCode != null">
                and template_code = #{templateCode,jdbcType=VARCHAR}
            </if>
            <if test="templateExpireTime != null">
                and template_expire_time = #{templateExpireTime,jdbcType=TIMESTAMP}
            </if>
            <if test="templateStatus != null">
                and template_status = #{templateStatus,jdbcType=VARCHAR}
            </if>
            <if test="createLocation != null">
                and create_location = #{createLocation,jdbcType=VARCHAR}
            </if>
            <if test="publishCompanyId != null">
                and publish_company_id = #{publishCompanyId,jdbcType=INTEGER}
            </if>
            <if test="publisherId != null">
                and publisher_id = #{publisherId,jdbcType=INTEGER}
            </if>
            <if test="publishTime != null">
                and publish_time = #{publishTime,jdbcType=TIMESTAMP}
            </if>
            <if test="orderGenType != null">
                and order_gen_type = #{orderGenType,jdbcType=INTEGER}
            </if>
            <if test="remark != null">
                and remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="param1 != null">
                and param1 = #{param1,jdbcType=VARCHAR}
            </if>
            <if test="param2 != null">
                and param2 = #{param2,jdbcType=VARCHAR}
            </if>
            <if test="param3 != null">
                and  param3 = #{param3,jdbcType=VARCHAR}
            </if>
            <if test="param4 != null">
                and  param4 = #{param4,jdbcType=VARCHAR}
            </if>
            <if test="operateMethod != null">
                and operate_method = #{operateMethod,jdbcType=VARCHAR}
            </if>
            <if test="operatorIp != null">
                and operator_ip = #{operatorIp,jdbcType=VARCHAR}
            </if>
            <if test="createUser != null">
                and create_user = #{createUser,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateUser != null">
                and update_user = #{updateUser,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="enable != null">
                and `enable` = #{enable,jdbcType=BIT}
            </if>
        </where>
    </select>

    <select id="selectGoodsSourceInfoAndException" parameterType="com.lz.vo.TGoodsSourceInfoVo" resultType="com.lz.dto.CompanySourceDTO">
        select
        tgsi.id goodsSourceInfoId, tgsi.code goodsSourceCode, tci.company_name,  tci.id companyId, tgsi.line_goods_rel_id,
        CONCAT('[',tgsi.goods_name,']', tli.line_name) AS goodsSourceName,
        lgcrd.id as ruleId, <!--计算规则Id -->
        tgsi.estimate_goods_weight, <!-- 预估重量 -->
        ifnull(lgcc.carriage_unit_price, tgsi.current_carriage_unit_price) currentCarriageUnitPrice,
        lgcc.id as carriageId, <!--单价变动Id -->
        tli.project_id, <!--项目Id -->
        tli.from_name as fromDetailName, <!-- 起点 -->
        tli.end_name as endDetailName, <!-- 终点 -->
        lgr.capital_transfer_type, <!--资金转移方式 -->
        tgsi.goods_name, <!-- 货物大类名 -->
        tgsi.big_kind_code, <!--货物大类CODE -->
        cp.pay_method, <!--费用结算模式 -->
        tli.if_rel_client, tli.if_rel_entrust, tgsi.status, cp.company_id, tcecr.carrier_id, lgr.extract_way, tpcr.dispatch_fee_coefficient
        from t_goods_source_info tgsi
        left join t_company_info tci on tgsi.company_id = tci.id
        left join t_line_info tli on tgsi.line_id = tli.id
        LEFT JOIN t_line_goods_carriage_rule_detail lgcrd ON tgsi.line_goods_rel_id = lgcrd.line_goods_rel_id AND lgcrd.`enable` = 0
        LEFT JOIN t_line_goods_carriage_change lgcc ON lgcc.id = (
        SELECT
        lgcc.id
        FROM
        t_line_goods_carriage_change lgcc
        WHERE
        lgcc.line_goods_rel_id = tgsi.line_goods_rel_id
        ORDER BY
        lgcc.create_time DESC
        LIMIT 1
        )
        LEFT JOIN t_company_project cp ON tli.project_id = cp.id
        LEFT JOIN t_line_goods_rel lgr ON tgsi.line_goods_rel_id = lgr.id
        LEFT JOIN t_project_carrier_rel tpcr on tli.project_id = tpcr.project_id and tpcr.company_id = tli.company_id
        left join t_carrier_enduser_company_rel tcecr on tpcr.carrier_company_id = tcecr.id
        WHERE tgsi.enable = 0
          <if test="carrierId != null">
              and tcecr.carrier_id = #{carrierId}
          </if>
          <if test="goodsSourceIds.size() > 0">
          and tgsi.id in
              <foreach collection="goodsSourceIds" index="index" item="id" open="(" close=")" separator=",">
                  #{id}
              </foreach>
          </if>
    </select>

    <select id="selectGoodsSourceBySourceCode" resultType="com.lz.dto.CompanySourceDTO">
        SELECT
            tgsi.id goodsSourceInfoId,
            tgsi.`code`,
            tgsi.line_goods_rel_id,
            tgsi.goods_id,
            tgsi.goods_name,
            tgsi.big_kind_code,
            tgsi.goods_unit,
            tgsi.goods_unit_price,
            tgsi.insurance_method,
            tgsi.insured_goods_type,
            tci.company_name,
            tci.tax_no companyTaxNo,
            tli.id lineId,
            tli.project_id,
            tli.line_code,
            tli.line_name,
            tli.line_short_name,
            tli.city_from_code,
            tli.from_name,
            tli.from_coordinates,
            tli.city_end_code,
            tli.end_name,
            tli.end_coordinates,
            tli.line_type,
            tgsi.current_carriage_unit_price,
            tgsi.estimate_goods_weight,tgsi.fix_cut_fee,
            tgsi.status, tgsi.company_id, tli.provice_from, tli.city_from, tli.country_from, tcp.pay_method,
            tli.provice_end, tli.city_end,
            tlgr.business_assist, tlgr.capital_transfer_pattern, tlgmr.manager_id, tlgmr.share_method, tlgmr.share_value,
            tci.company_did, tlgr.blockchain_pass, tlgr.capital_transfer_type, tlgr.query_trajectory, tgsi.sign_contract,tgsi.if_electronic_fence,tgsi.if_send_order_status
        FROM
            t_goods_source_info tgsi
                LEFT JOIN t_line_info tli ON tgsi.line_id = tli.id
                left join t_company_info tci on tgsi.company_id = tci.id
                left join t_company_project tcp on tli.project_id = tcp.id
                left join t_line_goods_rel tlgr on tgsi.line_goods_rel_id = tlgr.id
                left join t_line_goods_manager_rel tlgmr on tlgr.id = tlgmr.line_goods_rel_id and tlgmr.enable = 0 and tlgmr.if_efficient = 0
        WHERE
            tgsi.code = #{code}
    </select>

    <select id="selectGoodsSourceByCompanyId" resultType="com.lz.dto.GoodsSourceDTO">
        SELECT
            id as goodsSourceId,
            concat("[",goods_name,"]",line_name) as goodsSourceName
        FROM
            t_line_goods_rel
        WHERE
            company_id = #{companyId}
            and enable = 0
    </select>
    <select id="selectByGoodSourceInfoId" resultType="com.lz.dto.YmGoodsSourceDTO">
        SELECT distinct
            tlgr.id as goodSourceInfoId,
            concat("[",tlgr.goods_name,"]",tlgr.line_name) as goodSourceInfoName,
            tci.company_name as commonyName,
            tlgr.goods_name as goodsName,
            li.from_name as deliverGoodsAddress,
          li.end_name as receiveGoodsAddress,
            tlgur1.current_account_name as deliverGoodsUser,
            tlgur1.current_account_no as deliverGoodsUserPhone,
            tlgur2.current_account_name as receiveGoodsUser,
            tlgur2.current_account_no as receiveGoodsPhone,
            tlgr.create_time as createTime
        FROM
            t_line_goods_rel tlgr
            left join t_company_info tci on tlgr.company_id = tci.id
            LEFT JOIN t_line_info li ON tlgr.line_id = li.id
            left join t_line_goods_user_rel tlgur1 on tlgur1.line_goods_rel_id = tlgr.id and tlgur1.role_code = 'COMPANYSENDORDER' AND tlgur1.if_principal = 1 AND tlgur1.enable =0
            left join t_line_goods_user_rel tlgur2 on tlgur2.line_goods_rel_id = tlgr.id and tlgur2.role_code = 'RECEIVEORDER' AND tlgur2.if_principal = 1 AND tlgur2.enable =0
        WHERE
            tlgr.id = #{goodSourceInfoId}
    </select>

    <select id="getLineByCompanyId" resultType="com.lz.model.TLineInfo">
        select
        tli.id, tli.company_id, tli.line_name, tli.from_detail_name, tli.line_short_name, tli.line_code,
        tli.provice_from, tli.city_from, tli.country_from, tli.city_from_code, tli.from_name, tli.from_coordinates,
        tli.provice_end, tli.city_end, tli.country_end, tli.city_end_code, tli.end_detail_name, tli.end_name, tli.end_coordinates,
        tli.line_type,tli.fencing_identified_distance, tli.fence_recognition_distance, tli.straight_line_distance, tli.transport_distance,
        tli.if_rel_entrust, tli.if_rel_client,
        tli.`enable`,
        tcl.project_id
        from t_line_info tli
        left join t_company_line tcl on tcl.line_info_id = tli.id
            where tli.enable = 0 and tcl.id is not null
        <if test="companyId != null">
            and tli.company_id=#{companyId} and tcl.company_id=#{companyId}
        </if>

    </select>

    <select id="selectProjectGoodsSource" resultType="com.lz.model.TGoodsSourceInfo">
        select DISTINCT tgsi.* from t_goods_source_info tgsi
        left join t_company_project tcp on tgsi.company_id = tcp.company_id
        where tcp.id = #{projectId}
    </select>
     <select id="detail" parameterType="java.lang.Integer" resultType="com.lz.dto.ResourceHallSourceDetailDTO">
        select
        tgsi.id,
        cl.company_id as companyId,
        cl.project_id as projectId,
        tgsi.estimate_goods_weight as estimateGoodsWeight,
        tgsi.company_customer_name as companyCustomerName,
        tgsi.company_clients as companyClients,
        tgsi.remark ,
        tli.provice_from as proviceFrom,
        tli.city_from as cityFrom,
        tli.country_from as countryFrom,
        CONCAT(tli.provice_from , tli.city_from , tli.country_from , tli.from_detail_name) as shippingAddress,
        tli.provice_end as proviceEnd,
        tli.city_end as cityEnd,
        tli.country_end as countryEnd,
        CONCAT(tli.provice_end , tli.city_end , tli.country_end , tli.end_detail_name) as unloadingAddress,
        tli.transport_distance as transportDistance,
        tgsi.deliver_goods_contacter as deliverGoodsContacter,
        tgsi.deliver_goods_contacter_phone as deliverGoodsContacterPhone,
        tgsi.goods_id as goodsId,
        tgsi.goods_name as goodsName,
        tgsi.current_carriage_unit_price as carriageUnitPrice,
        tlgr.capital_transfer_pattern capitalTransferPattern,
        tlgmr.share_method shareMethod,<!-- 抽成方式 -->
        tlgmr.share_value shareValue,<!-- 抽成数值，经纪人服务费 -->
        tlgmr.manager_id managerId,<!-- 经纪人ID -->
        case
        when tgsi.goods_ban_time is null then '长期有效'
        else DATE_FORMAT(tgsi.goods_ban_time, '%Y-%m-%d')
        end as goodsBanTime,
        tli.from_coordinates,
        tli.end_coordinates
        from
        t_company_line cl
        left join t_line_goods_rel tlgr
        on cl.line_info_id = tlgr.line_id
        left join t_goods_source_info tgsi
        on tlgr.id = tgsi.line_goods_rel_id
        left join t_line_info tli
        on tlgr.line_id = tli.id
        left join t_line_goods_manager_rel tlgmr
        on tlgmr.line_goods_rel_id = tlgr.id AND tlgmr.`enable` = 0
        WHERE tgsi.id = #{goodsSourceId}
    </select>

    <select id="selectResourceHallByPage" parameterType="com.lz.vo.ResourceHallSearchVO"  resultType="com.lz.dto.ResourceHallSourceDetailDTO">
        select
            <if test="longitude !=null and longitude != '' and latitude !=null  and latitude != '' ">
            <!--根据经纬度计算距离-->
                ROUND(
                6378.138 * 2 * ASIN(
                SQRT(
                POW(
                SIN((
                ${latitude} * PI()/ 180-(substring_index(
                from_coordinates, ',', -1)) * PI()/ 180
                )/ 2
                ),
                2
                )+ COS( ${latitude} * PI()/ 180 )* COS( (substring_index(
                from_coordinates, ',', -1)) * PI()/ 180 )* POW(
                SIN((
                ${longitude} * PI()/ 180-(substring_index(
                from_coordinates, ',', 1)) * PI()/ 180
                )/ 2
                ),
                2
                )))
                ) AS distanceFromMe,
            </if>
        tgsi.id,
        tlgr.id as lineGoodsRelId,
        tli.transport_distance  as transportDistance,
        tli.from_name as shippingAddress,
        tli.end_name as unloadingAddress,
        tgsi.estimate_goods_weight as estimateGoodsWeight,
        case
        when tgsi.goods_ban_time is null then '长期有效'
        else DATE_FORMAT(tgsi.goods_ban_time, '%Y-%m-%d')
        end as goodsBanTime,
        tgsi.current_carriage_unit_price as carriageUnitPrice,
        tlgr.goods_name as goodsName
        from
           t_company_line cl
           left join t_line_goods_rel tlgr
           on cl.line_info_id = tlgr.line_id
           left join t_goods_source_info tgsi
           on tlgr.id = tgsi.line_goods_rel_id
           left join t_line_info tli
           on tlgr.line_id  = tli.id
        <where>
            tgsi.status = 'OPENSOURCE'
            and  tgsi.enable = '0'
        <if test="proviceFrom != null and proviceFrom != '' ">
            and tli.provice_from = #{proviceFrom}
            <if test="cityFrom != null and cityFrom != '' ">
                and tli.city_from = #{cityFrom}
                <if test="countryFrom != null and countryFrom != '' ">
                    and tli.country_from = #{countryFrom}
                </if>
            </if>
        </if>
        <if test="proviceEnd != null and proviceEnd != '' ">
            and tli.provice_end = #{proviceEnd}
            <if test="cityEnd != null and cityEnd != '' ">
                and tli.city_end = #{cityEnd}
                <if test="countryEnd != null and countryEnd != '' ">
                    and tli.country_end = #{countryEnd}
                </if>
            </if>
        </if>
        <if test=" goodsIds !=null and goodsIds.size() > 0">
            and (tlgr.goods_id in
            <foreach collection="goodsIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
            <!--其他货物类型-->
            <if test=" otherGoodsIds == 376 ">
                or tlgr.goods_id not in (2, 4, 1, 7, 3, 8, 25, 20, 6, 12)
            </if>
            )
        </if>
        <if test="startTime !=null and startTime!= '' ">
          and (tgsi.goods_ban_time >= from_unixtime(#{startTime}/1000,"%Y-%m-%d %H:%i:%s")  or tgsi.goods_ban_time is null)
        </if>
        <if test="endTime!=null and endTime!= '' ">
          and (tgsi.goods_ban_time &lt;=from_unixtime(#{endTime}/1000,"%Y-%m-%d %H:%i:%s") or tgsi.goods_ban_time is null)
        </if>

        <if test="(startTime !=null and startTime!= '' )and( endTime!=null and endTime!= '') ">
            and (tgsi.goods_ban_time >= from_unixtime(#{startTime}/1000,"%Y-%m-%d %H:%i:%s")
                and tgsi.goods_ban_time &lt;=from_unixtime(#{endTime}/1000,"%Y-%m-%d %H:%i:%s")
               or tgsi.goods_ban_time is null)
        </if>
        <if test="minimumPrice != null and minimumPrice > 0.00 ">
            and tgsi.current_carriage_unit_price >= #{minimumPrice}
        </if>
        <if test="highestPrice != null and highestPrice > 0.00 ">
            and tgsi.current_carriage_unit_price &lt;= #{highestPrice}
        </if>
        </where>
        <if test="(longitude !=null and longitude !='' and latitude !=null and latitude !='' ) and sort == 1 ">
            order by distanceFromMe
        </if>
        <if test=" sort == 2 ">
            order by tgsi.current_carriage_unit_price desc
        </if>
         <if test=" sort ==3 ">
             order by IFNULL(tgsi.update_time,tgsi.create_time) desc
         </if>
    </select>
    <select id="getInfoById" resultType="com.lz.vo.companypc.PCCreateGoodsResourceVO">
        select t2.id id,t2.line_id lineId,t1.line_name lineName,t3.company_name companyName,
        t1.goods_id goodsId,t1.goods_name goodsName,t2.estimate_goods_weight estimateGoodsWeight,
        t2.current_carriage_unit_price carriageUnitPrice,t2.goods_ban_time goodsBanTime,
        t2.deliver_goods_contacter deliverGoodsContacter,t2.deliver_goods_contacter_phone deliverGoodsContacterPhone,
        t2.receive_goods_contacter receiveGoodsContacter,t2.receive_goods_contacter_phone receiveGoodsContacterPhone,
        t2.status status,t1.capital_transfer_pattern capitalTransferPattern,
        t2.remark remark from t_line_goods_rel t1
        left join t_goods_source_info t2 on t1.id=t2.line_goods_rel_id
        left join t_company_info t3 on t3.id=t1.company_id
        where 1=1
        <if test="lineGoodsId!=null">
           and  t1.id=#{lineGoodsId}
        </if>
    </select>
    <select id="getDataByGoodsSourceCode" resultType="com.lz.model.TGoodsSourceInfo">
        select
        <include refid="Base_Column_List"/>
        from t_goods_source_info where code=#{goodsSourceCode}
    </select>
    <select id="getCompanyList" resultType="com.lz.vo.companypc.PCTGoodsSourceInfoVO">
        select id companyId,company_name companyName from t_company_info
        where id in(select DISTINCT(company_id) from t_company_line where company_id is true)
    </select>
    <select id="getSourceByCompanyId" resultType="com.lz.vo.TGoodsSourceInfoVo">
        SELECT
        t1.line_goods_rel_id,
        concat("[",t1.goods_name,"]",t2.line_name) as sourceName
        FROM
        t_goods_source_info t1
        LEFT JOIN t_line_info t2 ON t1.line_id = t2.id
        LEFT JOIN t_company_line t3 ON t1.line_id = t3.line_info_id
        where  1=1 and t1.enable=0 and t2.enable=0
        <if test="companyId != null">
            and t1.line_id in(select line_info_id from t_company_line where company_id=#{companyId})
        </if>
        <if test="companyId == null">
            and t1.company_id in(select DISTINCT(company_id) from t_company_line where company_id is true)
        </if>
    </select>
    <select id="getDataByLineId" resultType="com.lz.model.TGoodsSourceInfo">
        select <include refid="Base_Column_List"/>
        from t_goods_source_info where line_id = #{lineId}
    </select>

    <select id="selectGoodSourceCodeAndLineTypeById" resultType="com.lz.vo.GoodSourceCodeAndLineTypeVO">
        SELECT
            tgsi.id,
            tgsi.code as goodSourceCode,
            tli.line_type,
            tgsi.insurance_method
        FROM
            t_goods_source_info tgsi
                LEFT JOIN t_line_info tli ON tli.id = tgsi.line_id
        WHERE tgsi.ENABLE = 0 AND tli.ENABLE = 0
            AND tgsi.line_goods_rel_id = #{sourceId}
    </select>
    <select id="getDataByIds" resultType="com.lz.model.TGoodsSourceInfo">
        select <include refid="Base_Column_List"/>
        from t_goods_source_info
        where
            line_id = #{lineId}
            and company_id = #{companyId}
            and goods_id = #{goodsId}
            and enable = 0
    </select>

</mapper>