<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lz.dao.TCompanyProjectMapper">
    <!--查询 承运方合作企业 或 企业 下的项目 Yan-->
  <select id="selectByPage" parameterType="com.lz.vo.SearchProjectVO" resultType="com.lz.dto.CompanyProjectDTO">
    SELECT
        pcr.id,
        pcr.project_id as projectId, <!-- 项目ID -->
        cp.project_name, <!-- 项目名称 -->
        tdci.item_value as payMethod, <!-- 支付方式 -->
        pcr.dispatch_fee_coefficient as dispatchFeeCoefficient, <!-- 调度费系数 -->
        (
            SELECT
                COUNT(tlgr.id)
            FROM t_line_goods_rel tlgr
            INNER JOIN t_line_info tli ON tlgr.line_id = tli.id
            WHERE tli.project_id = pcr.project_id and tlgr.enable = 0 and tli.`enable` = 0
        ) +
        (
            SELECT
                COUNT(tlgr.id)
            FROM t_line_goods_rel tlgr
            INNER JOIN t_company_line tcl ON tlgr.line_id = tcl.line_info_id
            WHERE tcl.project_id = pcr.project_id and tlgr.enable = 0  and tlgr.line_id = tcl.line_info_id
        ) as line, <!-- 包含的货源数 -->
        tci.id companyId, <!-- 企业id add by zhangjiji 2019.7.10 -->
        tci.company_name, <!-- 企业 -->
        pcr.if_efficient, <!-- 当前项目是否启用-->
        pcr.create_time, <!-- 创建时间 -->
        pcr.update_time, <!-- 更新时间 -->
        tci.business_license_no,
        tcr.id AS carrierId,
        tcr.carrier_name, <!-- 承运方 -->
        IF(pcr.if_efficient, '已停用', '已启用') as efficientState, <!-- 启用状态 -->
        cp.check_user_car_status
    FROM t_project_carrier_rel pcr
    LEFT JOIN t_company_project cp ON pcr.project_id = cp.id
    LEFT JOIN t_dic_cat_item tdci ON cp.pay_method = tdci.item_code
    LEFT JOIN t_company_info tci ON pcr.company_id = tci.id
    LEFT JOIN t_carrier_enduser_company_rel cecr ON pcr.carrier_company_id = cecr.id AND cecr.`enable` = 0
    LEFT JOIN t_carrier_info tcr ON cecr.carrier_id = tcr.id
    WHERE
      cp.`enable` = FALSE AND pcr.`enable` = FALSE
      <if test="null != projectName">
          AND cp.project_name LIKE CONCAT('%', #{projectName}, '%')
      </if>
      <if test="projectId != null">
          AND cp.id = #{projectId}
      </if>
      <if test="carrierId != null">
         AND tcr.id = #{carrierId}
      </if>
      <if test="companyId != null">
          AND tci.id = #{companyId}
      </if>
      <if test="companyIds != null">
          AND pcr.company_id IN
          <foreach collection="companyIds" index="index" item="ci" open="(" separator="," close=")">
              #{ci}
          </foreach>
      </if>
      ORDER BY pcr.id DESC
  </select>

  <select id="selectById" parameterType="com.lz.vo.SearchProjectVO" resultType="com.lz.dto.CompanyProjectByIdDTO">
      SELECT
      tcp.id,
      (
      SELECT
      COUNT(tlgr.id)
      FROM t_line_goods_rel tlgr
      INNER JOIN t_line_info tli ON tlgr.line_id = tli.id
      WHERE tli.project_id = tpcr.project_id and tlgr.enable = 0 and tli.`enable` = 0
      ) as line, <!-- 包含的货源数 -->
      tpcr.carrier_company_id,
      tcp.project_code,
      tcp.project_feature,
      tcp.pay_method,
      tpcr.dispatch_fee_coefficient * 10000 as dispatchFeeCoefficient,
      tcp.project_join_implement_person,
      tcp.project_join_marketing_person,
      IFNULL(tpcr.project_online_contract_beginning_date,tcp.project_online_contract_beginning_date) as projectOnlineContractBeginningDate,
      IFNULL(tpcr.project_online_contract_end_date,tcp.project_online_contract_end_date) as projectOnlineContractEndDate,
      tcp.project_name,
      tcp.after_use_left_limit,
      tcp.project_online_program,
      IFNULL( tpcr.contractPhoto1, tcp.contract_photo1 ) AS contractPhoto1,
      IFNULL( tpcr.contractPhoto2, tcp.contract_photo2 ) AS contractPhoto2,
      tcp.credit_days,
      tcp.credit_line,
      tcp.operate_method,
      tcp.other_content,
      tcp.pay_deadline,
      tcp.remark,
      tci.id as companyId,
      tci.company_name,
      tci2.id as carrierId,
      tci2.carrier_name as carrierName,
      tdci.id itemId,
      tdci.item_value payMethodName,
      tcp.line_type,
      tcp.withdraw_type,
      tcp.capital_transfer_type,
      tdci3.item_value lineTypeName,
      tcp.withdraw_type,
      tdci2.item_value withdrawTypeName,
      tcp.capital_transfer_type,
      tdci4.item_value capitalTransferTtypeName,
      tci.regist_phone,
      tcp.check_user_car_status,
      tcp.business_assist,
      tcp.capital_transfer_pattern,
      tcp.update_service_fee,
      tcp.sign_contract_time,
      tcp.transport_identity_check,
      tcp.query_trajectory,
      tcp.freight_price as freightPrice,
      tcp.open_project_qrcode as openProjectQrcode,
      tcp.project_qrcode_weixin as projectQrcodeWeixin,
      tcp.project_qrcode_app as projectQrcodeApp,
      tcp.if_quota
      FROM
      t_company_project tcp
      LEFT JOIN t_project_carrier_rel tpcr ON tcp.id = tpcr.project_id
      LEFT JOIN t_carrier_enduser_company_rel tcecr ON tpcr.carrier_company_id = tcecr.id
      LEFT JOIN t_company_info tci ON tcecr.enduser_company_id = tci.id
      LEFT JOIN t_carrier_info tci2 ON tcecr.carrier_id = tci2.id
      LEFT JOIN t_dic_cat_item tdci ON tcp.pay_method = tdci.item_code
      LEFT JOIN t_dic_cat_item tdci2 ON tcp.withdraw_type = tdci2.item_code
      LEFT JOIN t_dic_cat_item tdci3 ON tcp.line_type = tdci3.item_code
      LEFT JOIN t_dic_cat_item tdci4 ON tcp.capital_transfer_type = tdci4.item_code
      where tcp.id = #{projectId} AND tcecr.carrier_id = #{carrierId}
  </select>

  <select id="selectCarrierCompanyProject" parameterType="com.lz.vo.SearchProjectVO"
          resultType="com.lz.model.TCompanyProject">
    SELECT
           tcp.id project_id,
           tcp.project_code,
      tcp.project_name,
      tcp.stop_flag,
      tcp.contract_photo1,
      tcp.contract_photo2
    FROM
      t_carrier_enduser_company_rel tcecr
      LEFT JOIN t_project_carrier_rel pcr ON pcr.carrier_company_id = tcecr.id
      LEFT JOIN t_company_project tcp ON pcr.project_id  = tcp.id
    WHERE
      tcecr.carrier_id = #{carrierId}
      AND tcecr.enduser_company_id = #{companyId}
        and tcecr.enable = false
        and tcp.enable = 0
        <if test="projectId != null">
          and tcp.id = #{projectId}
        </if>
  </select>

  <select id="selectProject" parameterType="com.lz.model.TCompanyProject" resultType="com.lz.model.TCompanyProject">
    select *
    from t_company_project
    where project_code = #{projectCode} and stop_flag = #{stopFlag} and id != #{id}
  </select>

  <update id="updateProjectStopFlag">
        update t_company_project
        set stop_flag = #{stopFlag}
        where id = #{id}
  </update>

  <!--根据企业Id查询企业下的项目 Yan-->
  <select id="selectProjectByCompanyId" resultType="com.lz.model.TCompanyProject">
    SELECT DISTINCT
        tcp.id,
        tcp.project_name,
        tcp.project_code,
        tcp.withdraw_type,
        tcp.project_feature,
        tcp.project_online_program,
        tcp.capital_transfer_type,
        tcp.remark,
        tcp.line_type,
        tcp.pay_method,
        tcp.payment_process
    FROM
        t_project_carrier_rel tpcr
        LEFT JOIN t_company_project tcp ON tpcr.company_id  = tcp.company_id
      WHERE
        tcp.`enable` = false
        <if test="null != companyId and companyId.size() > 0">
          AND tpcr.company_id IN
            <foreach collection="companyId" index="index" item="ci" open="(" separator="," close=")">
                #{ci}
            </foreach>
        </if>
  </select>
    <select id="selectByLineId" resultType="com.lz.model.TCompanyProject">
        select * from t_company_project t1
        left join t_company_line t2 on t2.project_id=t1.id
        where 1=1
        <if test="lineId!=null">
          and  t2.line_info_id = #{lineId}
        </if>
    </select>
    <update id="updateQuota" >
    update t_company_project
    set
      after_use_left_limit = after_use_left_limit - #{totalPrice,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="unfreeze" >
    update t_company_project
    set
      after_use_left_limit = after_use_left_limit + #{totalPrice,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="batchUnfreeze" parameterType="java.util.List">
    <foreach collection="list" separator=";" item="item">
      update t_company_project
      set
      after_use_left_limit = after_use_left_limit + #{item.totalPrice,jdbcType=DECIMAL}
      where id = #{item.projectId}
    </foreach>
  </update>

  <update id="freezeByPageage" >
    UPDATE t_company_project
    SET
      after_use_left_limit = after_use_left_limit -  #{totalFee,jdbcType=VARCHAR}
    WHERE id = #{companyProjectID,jdbcType=INTEGER}
  </update>

  <update id="logicDeleteCarrierProject" parameterType="hashmap">
    update t_project_carrier_rel tpcr
    set
    tpcr.`enable` = 1
    where EXISTS (
    select
    *
    from(
    <foreach collection="deleteId" item="id" index="index" separator="UNION">
      select #{id} as i
    </foreach>
    ) a
    where tpcr.id = a.i
    )
  </update>
    <update id="logicDeleteProject" parameterType="hashmap">
        update t_company_project tcp
        set
        tcp.`enable` = 1
        where EXISTS (
        select
        *
        from(
        <foreach collection="deleteProjectId" item="id" index="index" separator="UNION">
            select #{id} as i
        </foreach>
        ) a
        where tcp.id = a.i and tcp.id not in (select project_id from t_project_carrier_rel where enable = 0 and project_id = a.i)
        )
    </update>
    <select id="getProjectByCompanyId" resultType="com.lz.model.TCompanyProject">
        SELECT DISTINCT
        tcp.id,
        tcp.project_name,
        tcp.project_code,
        tcp.withdraw_type,
        tcp.project_feature,
        tcp.project_online_program,
        tcp.capital_transfer_type,
        tcp.remark,
        tcp.line_type,
        tcp.pay_method,
        tcp.payment_process
        FROM
        t_project_carrier_rel tpcr
        LEFT JOIN t_company_project tcp ON tpcr.company_id  = tcp.company_id
        LEFT JOIN t_company_line tcl ON tcp.id=tcl.project_id
        WHERE
        tcp.`enable` = false
        and tcp.id in
        (select DISTINCT(project_id) from t_company_line where project_id is true
            <if test="companyId != null">
                and company_id=#{companyId}
            </if>
        )

    </select>

    <select id="selectCarrierCompanyById" resultType="com.lz.dto.CompanyProjectDTO">
        SELECT
            pcr.id,
            pcr.project_id as projectId,
            cp.project_name,
            tci.id companyId,
            tci.company_name,
            tci.business_license_no,
            tcr.id AS carrierId,
            tcr.carrier_name,
            cp.check_user_car_status
        FROM t_project_carrier_rel pcr
                 LEFT JOIN t_company_project cp ON pcr.project_id = cp.id
                 LEFT JOIN t_dic_cat_item tdci ON cp.pay_method = tdci.item_code
                 LEFT JOIN t_company_info tci ON pcr.company_id = tci.id
                 LEFT JOIN t_carrier_enduser_company_rel cecr ON pcr.carrier_company_id = cecr.id AND cecr.`enable` = 0
                 LEFT JOIN t_carrier_info tcr ON cecr.carrier_id = tcr.id
        WHERE
            cp.`enable` = FALSE AND pcr.`enable` = FALSE
          AND tcr.id = #{carrierId} AND pcr.project_id = #{projectId}
    </select>
    <select id="getDataByProjectName" resultType="com.lz.model.TCompanyProject">
        select <include refid="Base_Column_List"/>
            from t_company_project
            where project_name = #{projectName}
            and company_id = #{companyId}
            and `enable` = false
    </select>
</mapper>