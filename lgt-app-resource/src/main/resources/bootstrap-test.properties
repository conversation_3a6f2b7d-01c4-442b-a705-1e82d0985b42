eureka.client.serviceUrl.defaultZone=*********************************************/eureka/
spring.cloud.config.discovery.serviceId=lgt-app-config
spring.cloud.config.discovery.enabled=true
spring.cloud.config.profile=test
spring.cloud.config.label=master

logging.level.com.lz.dao=DEBUG

spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL5InnoDBDialect
spring.data.redis.repositories.enabled=false
spring.jpa.open-in-view=true
spring.jpa.hibernate.ddl-auto=update
spring.data.jpa.repositories.enabled=true

mybatis.configuration.map-underscore-to-camel-case=true

weixin.QR = "https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token="

domainURL =http://ms.luzounet.com

qrTempPath = /root/deploy_cmp/qrImage/

wx.qr = http://ms.luzounet.com/fs-lgt/wx/qrcode/createAndGet