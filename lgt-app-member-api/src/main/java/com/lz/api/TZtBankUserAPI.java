package com.lz.api;

import com.lz.common.util.ResultUtil;
import com.lz.dto.BankCardDTO;
import com.lz.dto.EndUserDTO;
import com.lz.model.TBankCard;
import com.lz.model.TEnduserAccount;
import com.lz.model.TZtBankCard;
import com.lz.vo.TBankCardVo;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 *  @author: dingweibo
 *  @Date: 2022/11/23 20:16
 *  @Description:
 */
@FeignClient(value = "lgt-app-member")
public interface TZtBankUserAPI {

        /**
         *  @author: dingweibo
         *  @Date: 2022/11/23 20:16
         *  @Description: 查询C端默认银行卡
         */
        @PostMapping(value = "ztBankUserController/selectBankByEndUserId")
        public TZtBankCard selectBankByEndUserId(@RequestParam(value = "endUserId") Integer endUserId);

        /**
         *  @author: dingweibo
         *  @Date: 2022/11/23 20:41
         *  @Description: 根据银行卡id查询银行卡
         */
        @PostMapping(value ="ztBankUserController/selectById")
        TZtBankCard selectById(@RequestParam(value = "bankCardId") Integer bankCardId);

        /**
         * @Description 根据endUser id 获取银行卡信息
         * @param id
         * @return com.lz.common.util.ResultUtil
         * <AUTHOR>
         * @Date 2019/5/27 10:28
         **/
        @PostMapping(value = "api/tBankCard/selectBankCardByEndUserId")
        public TBankCard selectBankCardByEndUserId(@RequestParam(value = "id") Integer id);

        /**
         * 根据accountid 更新默认银行卡
         * <AUTHOR>
         *
         * @return REsultUtil
         */
        @PostMapping(value ="wxBank/updateByAccountId")
        public Integer updateByAccountId();
        /**
         * 根据bankid 更新默认银行卡
         *<AUTHOR>
         *
         * @return REsultUtil
         */
        @PostMapping(value ="wxBank/setIsDefault")
        public Integer setIsDefault(@RequestParam(value = "id") Integer id);



        /**
         * @Description 查询C端用户所有银行卡
         */
        @PostMapping(value = "ztBankUserController/selectBankCards")
        ResultUtil selectBankCards(@RequestParam(value = "enduserId") Integer enduserId);

        /*
         * <AUTHOR>
         * @Description 查询银行卡详情
         * @Date 2019/11/8 14:02
         * @Param
         * @return
         **/
        @PostMapping(value = "api/tBankCard/selectBankCardListDetail")
        List<BankCardDTO> selectBankCardListDetail(@RequestBody TBankCardVo record);

        @PostMapping("api/tBankCard/selectAllBankNoByBankId")
        BankCardDTO selectAllBankNoByBankId(@RequestBody TBankCardVo record);

        /**
         * <AUTHOR>
         * @Description 根据银行卡id查询绑定当前银行卡号的司机、车主
         * @Date 2020/4/29 3:47 下午
         * @Param
         * @return 3:47 下午
         **/
        @PostMapping("api/tBankCard/selectEnduserByBankId")
        List<EndUserDTO> selectEnduserByBankId(@RequestParam("bankId") Integer bankId);

        @PostMapping("api/tBankCard/selectUniqueBankCardNUms")
        int selectUniqueBankCardNUms(@RequestBody TEnduserAccount enduserAccount);

        @PostMapping("api/tBankCard/updateBankCardInfoById")
        int updateBankCardInfoById(@RequestBody TBankCard bankCard);

        @PostMapping("/ztBankUserController/selectByBankNum")
        int selectByBankNum(@RequestParam(value = "accountId") Integer accountId);

}
