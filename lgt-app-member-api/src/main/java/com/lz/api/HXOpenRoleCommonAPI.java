package com.lz.api;

import com.lz.common.util.ResultUtil;
import com.lz.dto.OpenRoleWalletListDTO;
import com.lz.model.TZtAccountOpenInfo;
import com.lz.vo.SelectOpenRoleInfo;
import com.lz.vo.SelectOpenRoleVO;
import com.lz.vo.SelectOrderPackOpenRoleVO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "lgt-app-member")
public interface HXOpenRoleCommonAPI {

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/23 14:56
     *  @Description: 查询开户信息
     */
    @PostMapping("/hx/pay/openrole/info")
    ResultUtil selectEnduserOpenRoleInfo(@RequestBody SelectOpenRoleInfo vo);

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/23 14:56
     *  @Description: 查询开户状态
     */
    @PostMapping("/hx/pay/openrole/status")
    ResultUtil selectCarrierCompanyEnduserOpenRoleStatus(@RequestBody SelectOpenRoleVO vo);

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/23 14:56
     *  @Description: 查询钱包
     */
    @PostMapping("/hx/pay/openrole/wallet")
    ResultUtil selectCarrierCompanyEnduserOpenRoleWallet(@RequestBody SelectOpenRoleVO vo);

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/23 14:55
     *  @Description: 运单打包，查询开户状态
     */
    @PostMapping("/hx/pay/openrole/pack/status")
    ResultUtil selectOrderPackCarrierCompanyEnduserOpenRoleStatus(@RequestBody SelectOrderPackOpenRoleVO vo);

    /**
     *  @author: dingweibo
     *  @Date: 2022/11/23 14:55
     *  @Description:  运单打包，查询用户钱包
     */
    @PostMapping("/hx/pay/openrole/pack/wallet")
    ResultUtil<OpenRoleWalletListDTO> selectOrderPackCarrierCompanyEnduserOPenRoleWallet(@RequestBody SelectOrderPackOpenRoleVO vo);

    /**
     *  @author: dingweibo
     *  @Date: 2022/12/7 11:01
     *  @Description: 根据accountId 查华夏开户信息
     */
    @PostMapping("/hx/pay/openrole/selectByAccountId")
    TZtAccountOpenInfo selectByAccountId(@RequestParam("accountId") Integer accountId);

}
