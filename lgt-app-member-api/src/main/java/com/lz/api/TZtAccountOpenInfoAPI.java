package com.lz.api;

import com.lz.common.util.ResultUtil;
import com.lz.model.TZtAccountOpenInfo;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "lgt-app-member")
public interface TZtAccountOpenInfoAPI {

    @PostMapping("/ztAccountOpenInfo/ifOpenRole")
    ResultUtil ifOpenRole(@RequestBody Integer accountId);

    @PostMapping("/ztAccountOpenInfo/selectByCarrierIdAndCompanyIdAndEndUserIdByStatus")
    TZtAccountOpenInfo selectByCarrierIdAndCompanyIdAndEndUserIdByStatus(@RequestParam(value = "carrierIdAndCompanyIdAndEndUserId") Integer carrierIdAndCompanyIdAndEndUserId,
                                                                         @RequestParam(value = "userOpenRole") String userOpenRole);

    @PostMapping("/ztAccountOpenInfo/selectOrderOpenInfoByAccountId")
    TZtAccountOpenInfo selectOrderOpenInfoByAccountId(@RequestParam(value = "enduserId") Integer enduserId);

    @PostMapping("/ztAccountOpenInfo/selectOrderOpenInfoListByAccountId")
    List<TZtAccountOpenInfo> selectOrderOpenInfoListByAccountId(@RequestParam(value = "enduserId") Integer enduserId);
}
