package com.lz.api;

import com.lz.model.TBankCard;
import com.lz.model.TFifthGenerationSignOpenAccount;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


@FeignClient(value = "lgt-app-member")
public interface TFifthGenerationSignOpenAccountApi {

        /**
         *  @author: dingweibo
         *  @Date: 2022/4/11 19:35
         *  @Description:
         */
        @PostMapping(value = "tFifthGenerationSignOpenAccount/selectByUserIdAndType")
        public TFifthGenerationSignOpenAccount selectByUserIdAndType(@RequestParam(value = "userId") Integer userId, @RequestParam(value = "userType") String userType);

}
