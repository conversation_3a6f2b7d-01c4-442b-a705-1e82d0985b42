package com.lz.api;

import com.lz.common.util.ResultUtil;
import com.lz.model.TJdWalletChangeLog;
import com.lz.model.TZtWalletChangeLog;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient("lgt-app-member")
public interface TZtWalletChangeLogAPI {

    @PostMapping("/hx/wallet/log/update")
    ResultUtil updateWalletChangeLog(@RequestBody TZtWalletChangeLog log);

    @PostMapping("/hx/wallet/log/selectByEndUserId")
    TZtWalletChangeLog selectByEndUserId(@RequestParam(value = "endUserId") Integer endUserId,@RequestParam(value = "orderBusinessCode") String orderBusinessCode) ;

    @PostMapping("/hx/wallet/log/selectByorderBusinessCode")
    public TZtWalletChangeLog selectByorderBusinessCode(@RequestParam(value = "orderBusinessCode") String orderBusinessCode,
                                                        @RequestParam(value = "tradeType") String tradeType);

    @PostMapping("/hx/wallet/log/seelctByTradeTypeWalletIdOrderBusinessCode")
    List<TZtWalletChangeLog> seelctByTradeTypeWalletIdOrderBusinessCode(@RequestParam(value = "tradeType") String tradeType,
                                                                        @RequestParam(value = "walletId") Integer walletId,
                                                                        @RequestParam(value = "orderBusinessCode") String orderBusinessCode);
    @PostMapping("/hx/wallet/log/updateByPrimaryKeySelective")
    ResultUtil updateByPrimaryKeySelective(@RequestBody TZtWalletChangeLog lo);

    @PostMapping("/hx/wallet/log/paymentFeesUpdateTransactionType")
    List<TZtWalletChangeLog> paymentFeesUpdateTransactionType(@RequestParam(value = "tradeNo") String tradeNo);

    @PostMapping("/hx/wallet/log/updateWalletChangeLogById")
    ResultUtil updateWalletChangeLogById(@RequestBody TZtWalletChangeLog log);

    @PostMapping("/hx/wallet/log/selectOldOrderCarCaptainByOrderBusinessCode")
    TZtWalletChangeLog selectOldOrderCarCaptainByOrderBusinessCode(@RequestParam(value = "walletId") Integer walletId,
                                                                   @RequestParam(value = "orderBusinessCode") String orderBusinessCode);
}
