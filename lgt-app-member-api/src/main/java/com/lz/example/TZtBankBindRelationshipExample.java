package com.lz.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TZtBankBindRelationshipExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TZtBankBindRelationshipExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountBankIdIsNull() {
            addCriterion("account_bank_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountBankIdIsNotNull() {
            addCriterion("account_bank_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountBankIdEqualTo(Integer value) {
            addCriterion("account_bank_id =", value, "accountBankId");
            return (Criteria) this;
        }

        public Criteria andAccountBankIdNotEqualTo(Integer value) {
            addCriterion("account_bank_id <>", value, "accountBankId");
            return (Criteria) this;
        }

        public Criteria andAccountBankIdGreaterThan(Integer value) {
            addCriterion("account_bank_id >", value, "accountBankId");
            return (Criteria) this;
        }

        public Criteria andAccountBankIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_bank_id >=", value, "accountBankId");
            return (Criteria) this;
        }

        public Criteria andAccountBankIdLessThan(Integer value) {
            addCriterion("account_bank_id <", value, "accountBankId");
            return (Criteria) this;
        }

        public Criteria andAccountBankIdLessThanOrEqualTo(Integer value) {
            addCriterion("account_bank_id <=", value, "accountBankId");
            return (Criteria) this;
        }

        public Criteria andAccountBankIdIn(List<Integer> values) {
            addCriterion("account_bank_id in", values, "accountBankId");
            return (Criteria) this;
        }

        public Criteria andAccountBankIdNotIn(List<Integer> values) {
            addCriterion("account_bank_id not in", values, "accountBankId");
            return (Criteria) this;
        }

        public Criteria andAccountBankIdBetween(Integer value1, Integer value2) {
            addCriterion("account_bank_id between", value1, value2, "accountBankId");
            return (Criteria) this;
        }

        public Criteria andAccountBankIdNotBetween(Integer value1, Integer value2) {
            addCriterion("account_bank_id not between", value1, value2, "accountBankId");
            return (Criteria) this;
        }

        public Criteria andRequestIdIsNull() {
            addCriterion("request_id is null");
            return (Criteria) this;
        }

        public Criteria andRequestIdIsNotNull() {
            addCriterion("request_id is not null");
            return (Criteria) this;
        }

        public Criteria andRequestIdEqualTo(String value) {
            addCriterion("request_id =", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdNotEqualTo(String value) {
            addCriterion("request_id <>", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdGreaterThan(String value) {
            addCriterion("request_id >", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdGreaterThanOrEqualTo(String value) {
            addCriterion("request_id >=", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdLessThan(String value) {
            addCriterion("request_id <", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdLessThanOrEqualTo(String value) {
            addCriterion("request_id <=", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdLike(String value) {
            addCriterion("request_id like", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdNotLike(String value) {
            addCriterion("request_id not like", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdIn(List<String> values) {
            addCriterion("request_id in", values, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdNotIn(List<String> values) {
            addCriterion("request_id not in", values, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdBetween(String value1, String value2) {
            addCriterion("request_id between", value1, value2, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdNotBetween(String value1, String value2) {
            addCriterion("request_id not between", value1, value2, "requestId");
            return (Criteria) this;
        }

        public Criteria andBankNoIsNull() {
            addCriterion("bank_no is null");
            return (Criteria) this;
        }

        public Criteria andBankNoIsNotNull() {
            addCriterion("bank_no is not null");
            return (Criteria) this;
        }

        public Criteria andBankNoEqualTo(String value) {
            addCriterion("bank_no =", value, "bankNo");
            return (Criteria) this;
        }

        public Criteria andBankNoNotEqualTo(String value) {
            addCriterion("bank_no <>", value, "bankNo");
            return (Criteria) this;
        }

        public Criteria andBankNoGreaterThan(String value) {
            addCriterion("bank_no >", value, "bankNo");
            return (Criteria) this;
        }

        public Criteria andBankNoGreaterThanOrEqualTo(String value) {
            addCriterion("bank_no >=", value, "bankNo");
            return (Criteria) this;
        }

        public Criteria andBankNoLessThan(String value) {
            addCriterion("bank_no <", value, "bankNo");
            return (Criteria) this;
        }

        public Criteria andBankNoLessThanOrEqualTo(String value) {
            addCriterion("bank_no <=", value, "bankNo");
            return (Criteria) this;
        }

        public Criteria andBankNoLike(String value) {
            addCriterion("bank_no like", value, "bankNo");
            return (Criteria) this;
        }

        public Criteria andBankNoNotLike(String value) {
            addCriterion("bank_no not like", value, "bankNo");
            return (Criteria) this;
        }

        public Criteria andBankNoIn(List<String> values) {
            addCriterion("bank_no in", values, "bankNo");
            return (Criteria) this;
        }

        public Criteria andBankNoNotIn(List<String> values) {
            addCriterion("bank_no not in", values, "bankNo");
            return (Criteria) this;
        }

        public Criteria andBankNoBetween(String value1, String value2) {
            addCriterion("bank_no between", value1, value2, "bankNo");
            return (Criteria) this;
        }

        public Criteria andBankNoNotBetween(String value1, String value2) {
            addCriterion("bank_no not between", value1, value2, "bankNo");
            return (Criteria) this;
        }

        public Criteria andLinkAccountTypeIsNull() {
            addCriterion("link_account_type is null");
            return (Criteria) this;
        }

        public Criteria andLinkAccountTypeIsNotNull() {
            addCriterion("link_account_type is not null");
            return (Criteria) this;
        }

        public Criteria andLinkAccountTypeEqualTo(Integer value) {
            addCriterion("link_account_type =", value, "linkAccountType");
            return (Criteria) this;
        }

        public Criteria andLinkAccountTypeNotEqualTo(Integer value) {
            addCriterion("link_account_type <>", value, "linkAccountType");
            return (Criteria) this;
        }

        public Criteria andLinkAccountTypeGreaterThan(Integer value) {
            addCriterion("link_account_type >", value, "linkAccountType");
            return (Criteria) this;
        }

        public Criteria andLinkAccountTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("link_account_type >=", value, "linkAccountType");
            return (Criteria) this;
        }

        public Criteria andLinkAccountTypeLessThan(Integer value) {
            addCriterion("link_account_type <", value, "linkAccountType");
            return (Criteria) this;
        }

        public Criteria andLinkAccountTypeLessThanOrEqualTo(Integer value) {
            addCriterion("link_account_type <=", value, "linkAccountType");
            return (Criteria) this;
        }

        public Criteria andLinkAccountTypeIn(List<Integer> values) {
            addCriterion("link_account_type in", values, "linkAccountType");
            return (Criteria) this;
        }

        public Criteria andLinkAccountTypeNotIn(List<Integer> values) {
            addCriterion("link_account_type not in", values, "linkAccountType");
            return (Criteria) this;
        }

        public Criteria andLinkAccountTypeBetween(Integer value1, Integer value2) {
            addCriterion("link_account_type between", value1, value2, "linkAccountType");
            return (Criteria) this;
        }

        public Criteria andLinkAccountTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("link_account_type not between", value1, value2, "linkAccountType");
            return (Criteria) this;
        }

        public Criteria andBindStatusIsNull() {
            addCriterion("bind_status is null");
            return (Criteria) this;
        }

        public Criteria andBindStatusIsNotNull() {
            addCriterion("bind_status is not null");
            return (Criteria) this;
        }

        public Criteria andBindStatusEqualTo(String value) {
            addCriterion("bind_status =", value, "bindStatus");
            return (Criteria) this;
        }

        public Criteria andBindStatusNotEqualTo(String value) {
            addCriterion("bind_status <>", value, "bindStatus");
            return (Criteria) this;
        }

        public Criteria andBindStatusGreaterThan(String value) {
            addCriterion("bind_status >", value, "bindStatus");
            return (Criteria) this;
        }

        public Criteria andBindStatusGreaterThanOrEqualTo(String value) {
            addCriterion("bind_status >=", value, "bindStatus");
            return (Criteria) this;
        }

        public Criteria andBindStatusLessThan(String value) {
            addCriterion("bind_status <", value, "bindStatus");
            return (Criteria) this;
        }

        public Criteria andBindStatusLessThanOrEqualTo(String value) {
            addCriterion("bind_status <=", value, "bindStatus");
            return (Criteria) this;
        }

        public Criteria andBindStatusLike(String value) {
            addCriterion("bind_status like", value, "bindStatus");
            return (Criteria) this;
        }

        public Criteria andBindStatusNotLike(String value) {
            addCriterion("bind_status not like", value, "bindStatus");
            return (Criteria) this;
        }

        public Criteria andBindStatusIn(List<String> values) {
            addCriterion("bind_status in", values, "bindStatus");
            return (Criteria) this;
        }

        public Criteria andBindStatusNotIn(List<String> values) {
            addCriterion("bind_status not in", values, "bindStatus");
            return (Criteria) this;
        }

        public Criteria andBindStatusBetween(String value1, String value2) {
            addCriterion("bind_status between", value1, value2, "bindStatus");
            return (Criteria) this;
        }

        public Criteria andBindStatusNotBetween(String value1, String value2) {
            addCriterion("bind_status not between", value1, value2, "bindStatus");
            return (Criteria) this;
        }

        public Criteria andBindMessageIsNull() {
            addCriterion("bind_message is null");
            return (Criteria) this;
        }

        public Criteria andBindMessageIsNotNull() {
            addCriterion("bind_message is not null");
            return (Criteria) this;
        }

        public Criteria andBindMessageEqualTo(String value) {
            addCriterion("bind_message =", value, "bindMessage");
            return (Criteria) this;
        }

        public Criteria andBindMessageNotEqualTo(String value) {
            addCriterion("bind_message <>", value, "bindMessage");
            return (Criteria) this;
        }

        public Criteria andBindMessageGreaterThan(String value) {
            addCriterion("bind_message >", value, "bindMessage");
            return (Criteria) this;
        }

        public Criteria andBindMessageGreaterThanOrEqualTo(String value) {
            addCriterion("bind_message >=", value, "bindMessage");
            return (Criteria) this;
        }

        public Criteria andBindMessageLessThan(String value) {
            addCriterion("bind_message <", value, "bindMessage");
            return (Criteria) this;
        }

        public Criteria andBindMessageLessThanOrEqualTo(String value) {
            addCriterion("bind_message <=", value, "bindMessage");
            return (Criteria) this;
        }

        public Criteria andBindMessageLike(String value) {
            addCriterion("bind_message like", value, "bindMessage");
            return (Criteria) this;
        }

        public Criteria andBindMessageNotLike(String value) {
            addCriterion("bind_message not like", value, "bindMessage");
            return (Criteria) this;
        }

        public Criteria andBindMessageIn(List<String> values) {
            addCriterion("bind_message in", values, "bindMessage");
            return (Criteria) this;
        }

        public Criteria andBindMessageNotIn(List<String> values) {
            addCriterion("bind_message not in", values, "bindMessage");
            return (Criteria) this;
        }

        public Criteria andBindMessageBetween(String value1, String value2) {
            addCriterion("bind_message between", value1, value2, "bindMessage");
            return (Criteria) this;
        }

        public Criteria andBindMessageNotBetween(String value1, String value2) {
            addCriterion("bind_message not between", value1, value2, "bindMessage");
            return (Criteria) this;
        }

        public Criteria andRequestCodeIsNull() {
            addCriterion("request_code is null");
            return (Criteria) this;
        }

        public Criteria andRequestCodeIsNotNull() {
            addCriterion("request_code is not null");
            return (Criteria) this;
        }

        public Criteria andRequestCodeEqualTo(String value) {
            addCriterion("request_code =", value, "requestCode");
            return (Criteria) this;
        }

        public Criteria andRequestCodeNotEqualTo(String value) {
            addCriterion("request_code <>", value, "requestCode");
            return (Criteria) this;
        }

        public Criteria andRequestCodeGreaterThan(String value) {
            addCriterion("request_code >", value, "requestCode");
            return (Criteria) this;
        }

        public Criteria andRequestCodeGreaterThanOrEqualTo(String value) {
            addCriterion("request_code >=", value, "requestCode");
            return (Criteria) this;
        }

        public Criteria andRequestCodeLessThan(String value) {
            addCriterion("request_code <", value, "requestCode");
            return (Criteria) this;
        }

        public Criteria andRequestCodeLessThanOrEqualTo(String value) {
            addCriterion("request_code <=", value, "requestCode");
            return (Criteria) this;
        }

        public Criteria andRequestCodeLike(String value) {
            addCriterion("request_code like", value, "requestCode");
            return (Criteria) this;
        }

        public Criteria andRequestCodeNotLike(String value) {
            addCriterion("request_code not like", value, "requestCode");
            return (Criteria) this;
        }

        public Criteria andRequestCodeIn(List<String> values) {
            addCriterion("request_code in", values, "requestCode");
            return (Criteria) this;
        }

        public Criteria andRequestCodeNotIn(List<String> values) {
            addCriterion("request_code not in", values, "requestCode");
            return (Criteria) this;
        }

        public Criteria andRequestCodeBetween(String value1, String value2) {
            addCriterion("request_code between", value1, value2, "requestCode");
            return (Criteria) this;
        }

        public Criteria andRequestCodeNotBetween(String value1, String value2) {
            addCriterion("request_code not between", value1, value2, "requestCode");
            return (Criteria) this;
        }

        public Criteria andRequestMessageIsNull() {
            addCriterion("request_message is null");
            return (Criteria) this;
        }

        public Criteria andRequestMessageIsNotNull() {
            addCriterion("request_message is not null");
            return (Criteria) this;
        }

        public Criteria andRequestMessageEqualTo(String value) {
            addCriterion("request_message =", value, "requestMessage");
            return (Criteria) this;
        }

        public Criteria andRequestMessageNotEqualTo(String value) {
            addCriterion("request_message <>", value, "requestMessage");
            return (Criteria) this;
        }

        public Criteria andRequestMessageGreaterThan(String value) {
            addCriterion("request_message >", value, "requestMessage");
            return (Criteria) this;
        }

        public Criteria andRequestMessageGreaterThanOrEqualTo(String value) {
            addCriterion("request_message >=", value, "requestMessage");
            return (Criteria) this;
        }

        public Criteria andRequestMessageLessThan(String value) {
            addCriterion("request_message <", value, "requestMessage");
            return (Criteria) this;
        }

        public Criteria andRequestMessageLessThanOrEqualTo(String value) {
            addCriterion("request_message <=", value, "requestMessage");
            return (Criteria) this;
        }

        public Criteria andRequestMessageLike(String value) {
            addCriterion("request_message like", value, "requestMessage");
            return (Criteria) this;
        }

        public Criteria andRequestMessageNotLike(String value) {
            addCriterion("request_message not like", value, "requestMessage");
            return (Criteria) this;
        }

        public Criteria andRequestMessageIn(List<String> values) {
            addCriterion("request_message in", values, "requestMessage");
            return (Criteria) this;
        }

        public Criteria andRequestMessageNotIn(List<String> values) {
            addCriterion("request_message not in", values, "requestMessage");
            return (Criteria) this;
        }

        public Criteria andRequestMessageBetween(String value1, String value2) {
            addCriterion("request_message between", value1, value2, "requestMessage");
            return (Criteria) this;
        }

        public Criteria andRequestMessageNotBetween(String value1, String value2) {
            addCriterion("request_message not between", value1, value2, "requestMessage");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNull() {
            addCriterion("create_user is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNotNull() {
            addCriterion("create_user is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserEqualTo(String value) {
            addCriterion("create_user =", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotEqualTo(String value) {
            addCriterion("create_user <>", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThan(String value) {
            addCriterion("create_user >", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThanOrEqualTo(String value) {
            addCriterion("create_user >=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThan(String value) {
            addCriterion("create_user <", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThanOrEqualTo(String value) {
            addCriterion("create_user <=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLike(String value) {
            addCriterion("create_user like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotLike(String value) {
            addCriterion("create_user not like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserIn(List<String> values) {
            addCriterion("create_user in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotIn(List<String> values) {
            addCriterion("create_user not in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserBetween(String value1, String value2) {
            addCriterion("create_user between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotBetween(String value1, String value2) {
            addCriterion("create_user not between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNull() {
            addCriterion("update_user is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNotNull() {
            addCriterion("update_user is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserEqualTo(String value) {
            addCriterion("update_user =", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotEqualTo(String value) {
            addCriterion("update_user <>", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThan(String value) {
            addCriterion("update_user >", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThanOrEqualTo(String value) {
            addCriterion("update_user >=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThan(String value) {
            addCriterion("update_user <", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThanOrEqualTo(String value) {
            addCriterion("update_user <=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLike(String value) {
            addCriterion("update_user like", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotLike(String value) {
            addCriterion("update_user not like", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIn(List<String> values) {
            addCriterion("update_user in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotIn(List<String> values) {
            addCriterion("update_user not in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserBetween(String value1, String value2) {
            addCriterion("update_user between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotBetween(String value1, String value2) {
            addCriterion("update_user not between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andAccountOpenIdIsNull() {
            addCriterion("account_open_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountOpenIdIsNotNull() {
            addCriterion("account_open_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountOpenIdEqualTo(Integer value) {
            addCriterion("account_open_id =", value, "accountOpenId");
            return (Criteria) this;
        }

        public Criteria andAccountOpenIdNotEqualTo(Integer value) {
            addCriterion("account_open_id <>", value, "accountOpenId");
            return (Criteria) this;
        }

        public Criteria andAccountOpenIdGreaterThan(Integer value) {
            addCriterion("account_open_id >", value, "accountOpenId");
            return (Criteria) this;
        }

        public Criteria andAccountOpenIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_open_id >=", value, "accountOpenId");
            return (Criteria) this;
        }

        public Criteria andAccountOpenIdLessThan(Integer value) {
            addCriterion("account_open_id <", value, "accountOpenId");
            return (Criteria) this;
        }

        public Criteria andAccountOpenIdLessThanOrEqualTo(Integer value) {
            addCriterion("account_open_id <=", value, "accountOpenId");
            return (Criteria) this;
        }

        public Criteria andAccountOpenIdIn(List<Integer> values) {
            addCriterion("account_open_id in", values, "accountOpenId");
            return (Criteria) this;
        }

        public Criteria andAccountOpenIdNotIn(List<Integer> values) {
            addCriterion("account_open_id not in", values, "accountOpenId");
            return (Criteria) this;
        }

        public Criteria andAccountOpenIdBetween(Integer value1, Integer value2) {
            addCriterion("account_open_id between", value1, value2, "accountOpenId");
            return (Criteria) this;
        }

        public Criteria andAccountOpenIdNotBetween(Integer value1, Integer value2) {
            addCriterion("account_open_id not between", value1, value2, "accountOpenId");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}