package com.lz.model;


import com.lz.common.util.PageInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TCarrierEnduserCompanyRel extends PageInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    //承运方表ID
    private Integer carrierId;

    //企业表/C端用户表ID
    private Integer enduserCompanyId;

    //关系类型
    private String datasouce;

    //第三方（网商）平台子账号
    private String thridParySubAccount;

    //配合子账号的uid
    private String uid;

    private String remark;

    private String param1;

    private String param2;

    private String param3;

    private String param4;

    private String createUser;

    private Date createTime;

    private String updateUser;

    private Date updateTime;

    private Boolean enable;

    private Boolean stopFlag;

}