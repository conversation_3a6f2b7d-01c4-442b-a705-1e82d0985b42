package com.lz.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * t_zt_bank_bind_relationship
 * <AUTHOR>
@Data
public class TZtBankBindRelationship implements Serializable {
    private Integer id;

    /**
     * 银行卡/用户关系表
     */
    private Integer accountBankId;

    /**
     * 请求流水号
     */
    private String requestId;

    /**
     * 绑卡编号
     */
    private String bankNo;

    /**
     * 绑定账户类别 0-出入金 1-白名单1  2-白名单2  3-白名单3	
     */
    private Integer linkAccountType;

    /**
     * 绑卡状态
     */
    private String bindStatus;

    /**
     * 绑卡信息
     */
    private String bindMessage;

    /**
     * 请求状态 0:处理中 1:处理成功2:处理失败
     */
    private String requestCode;

    /**
     * 请求信息
     */
    private String requestMessage;

    private String createUser;

    private Date createTime;

    private String updateUser;

    private Date updateTime;

    /**
     * 开户详情表id
     */
    private Integer accountOpenId;

    private static final long serialVersionUID = 1L;
}