package com.lz.dto;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lz.common.util.BigDecimalSerialize;
import com.lz.common.util.DoubleSerialize;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TDriverDto implements Serializable {

    private Integer id;

    private Integer accountId;

    private String realName;

    private String phone;

    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal accountBalance;

    //提现中的金额
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal withdrawAmount;

    private String idcard;

    private String userLogisticsRole;

    //累计提现
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal totalWithdrawal;

    //京东余额
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal jdAccountBalance;

    //华夏余额
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal ztAccountBalance;

}
