package com.lz.dto;

import lombok.Data;

@Data
public class CarrieOpenInfoDetailsDTO {

    //accountId
    private Integer accountId;

    //用户类型
    private String usertype;

    //企业名称
    private String carrierName;

    //统一信用代码/营业执照代码
    private String businessLicenseNo;

    //法人姓名
    private String companyLegalPerson;

    //联系人
    private String companyContacts;

    //联系人电话
    private String companyContactsPhone;

    //联系人地址
    private String companyDetailAddress;

    //备注
    private String remark;
}
