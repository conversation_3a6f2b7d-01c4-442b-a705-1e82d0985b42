package com.lz.vo;

import com.lz.common.util.PageInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
public class CarrierOpenInfoListVO {

    private Integer page;
    private Integer size;

    //承运方名称
    private String carrierName;

    //账户状态
    private String responseCode;

    //平台信息-传一个默认值PF
    private String param4;
}
