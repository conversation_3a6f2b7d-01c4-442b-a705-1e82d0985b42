package com.lz.vo;

import com.lz.common.util.PageInfo;
import lombok.Data;

@Data
public class PcZTEndUserOpenRoleListVO extends PageInfo {

    //账户状态-SUCCESS:可用/FAIL:不可用
    private String openState;

    //用户角色-CTYPEDRVIVER:司机/CTYPEBOSS:车队长/CTYPEAGENTPERSON:经纪人
    private String userRole;

    //用户名称
    private String userName;

    //用户手机号
    private String userPhone;

    //用户身份证号
    private String userIdcard;

    //开户类型
    private Boolean openType;
}
