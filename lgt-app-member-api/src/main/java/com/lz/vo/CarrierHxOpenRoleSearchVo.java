package com.lz.vo;

import com.lz.common.util.PageInfo;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CarrierHxOpenRoleSearchVo  extends PageInfo {

    private Integer walletId;
    private Integer accountId;

    //承运方名称
    private String carrierName;

    //账户状态
    private String responseCode;

    //交易时间
    private Date[] tradeTime;

    private Date tradeStartTime;
    private Date tradeEndTime;


    private Date startTime;
    private Date endTime;

    //wallet_change_log id
    private Integer id;

    private List idArray;

    /*
     *  交易类型
     */
    private String tradeType;


   private String orderBusinessCode;

   private String innerTradeNo;

   //支付渠道
   private String channelId;

   private String DriverId;
}
