package com.lz.vo;

import com.lz.model.TZtBankCard;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ImperfectTZtBankCardVO extends TZtBankCard {

      //t_account的account_no
      private String accountNo;

      //资料完善状态
      private String perfectState;

      //t_enduser_account的idcard
      private String idcard;

      private String realName;

      //默认银行卡 0:否 1:是
      private Boolean isOneself;
      //本人银行卡 0:否 1:是
      private Boolean isDefault;

      private String endUserId;

      private String bindStatus;

      private String bindStatusStr;


}
