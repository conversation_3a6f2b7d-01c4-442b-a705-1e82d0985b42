package com.lz.vo;

import com.lz.model.TBankCard;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TBankCardVo extends TBankCard {
    //enduserinfoId
    private Integer enduserinfoId;
    /**
     * 手机号
     */
    private String phone;

    private String code;

    private Integer endUserId;

    private Integer bankId;

    private String realName;

    private String verificationCode;

    private Boolean checkIdcard;

    private Boolean selfCard;

    private String cardOwnerIdcardPhoto1;

    private String cardOwnerIdcardPhoto2;

    private String address;

}
