package com.lz.vo;

import com.lz.common.util.PageInfo;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CompanyWalletCapitalFlowVO extends PageInfo {

    /**
     * accountId
     */
    private Integer accountId;
    /*
     *  企业id
     */
    private String companyId;
    //必传参数  二选一  两个接口
    /*
     *  司机id
     */
    private String driverId;

    /*
     *  开始时间
     */
    private Date startTime;
    /*
     *  结束时间
     */
    private Date endTime;
    /*
     *  交易类型
     */
    private String tradeType;
    /*
     *  运单编号
     */
    private String orderBusinessCode;
    /*
     *  承运方id
     */
    private String carrierId;

    //交易时间
    private Date[] tradeTime;

    //运单号
    private String innerTradeNo;

    private Integer carrierCompanyId;

    //交易流水号
    private String[] outerTradeNoArray;

    private List idArray;
}
