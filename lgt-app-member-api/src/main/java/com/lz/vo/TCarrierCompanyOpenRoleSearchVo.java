package com.lz.vo;


import com.lz.common.util.PageInfo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 *  @author: dingweibo
 *  @Date: 2021/8/5 9:12
 *  @Description: 京东数科 承运方与企业开户 查询实体
 */
@Data
public class TCarrierCompanyOpenRoleSearchVo extends PageInfo {

    //京东开户状态 （前端传的枚举 申请中 handle  成功success  失败error）
    private String openStatus;

    //京东账户可用状态（开户状态与打款认证状态 为成功时 为可用  ，其中一个为不可用 为不可用）
    //（前端传的枚举  可用success   不可用error）
    private String accountStatus;

    private String carrierName;

    private String companyName;

    //钱包id
    private Integer walletId;

    //交易时间
    private Date[] tradeTime;

    private Date tradeStartTime;
    private Date tradeEndTime;

    //企业id
    private Integer companyId;

    /**
     * 数据类型-承运方/企业
     */
    private String userOpenRole;

    private String  tradeType;

    private String orderBusinessCode;

    private String carrierId;

    private List idArray;
}
