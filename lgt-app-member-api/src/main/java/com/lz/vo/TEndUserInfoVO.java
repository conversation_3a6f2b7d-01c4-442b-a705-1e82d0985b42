package com.lz.vo;

import com.lz.dto.TEndUserInfoDto;
import com.lz.model.TBankCard;
import com.lz.model.TEndCarInfo;
import com.lz.model.TEndUserInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;


@NoArgsConstructor
@Data
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class TEndUserInfoVO extends  TEndUserInfo {
    //余额
    private Double balance;
    //车辆信息
    private String cars;
    // 司机id
    private Integer[] deleteId;

    private String auditStatusValue;

    //综合评分
    private String score;

    /**
     * 更新时间
     */
    private Date updateTimeArray[];

    /**
     * 更新开始时间
     */
    private Date updateStartTime;

    /**
     * 更新结束时间
     */
    private Date updateEndTime;

    //车辆信息列表
    private List<TEndCarInfo> carNumber;

    //车牌号
    private String vehicleNumber;

    //车辆数量
    private  Integer carCount;

    //车辆列表
    private List<TEndUserCarRelVo> tEndUserCarRelVoList;

    //银行卡列表
    private List<TBankCard> tBankCardList;

    //华夏银行卡列表
    private List<TZtBankCardVo> tZtBankCardVoList;

    //车辆
    private String  cp;

    //注册时间 字符串
    private String createTimeStr;

    //更新时间 字符串
    private String updateTimeStr;

    //司机绑定的车辆
    private List<TEndCarInfo> tEndCarInfoList;

    //综合评分
    private Object tUserScoreInfo;

    //收益权类型
    private String certificateType;

    //车主证明1
    private String certificateDoc1;

    //车主证明2
    private String certificateDoc2;

    //车辆id
    private Integer endcarId;

    //道路运输许可证1
    private String roadTransportOperationLicensePhoto1;
    //道路运输许可证2
    private String roadTransportOperationLicensePhoto2;

    //行驶证1
    private String drivingLicencesPhotoXsz1;

    //行驶证2
    private String drivingLicencesPhotoXsz2;


    private List<TEndUserInfoDto> carEndList;

    private Integer accountId;

    /**
     * 审核状态(0：新建；1：审核中；2：审核通过；3：审核不通过；4：资料需要更新；)
     */
    private String auditStatus;

    private String accountNo;

    private Integer enduserId;

    //按车队长姓名或手机号查询
    private String  param;

    /**
     * 身份证地址
     */
    private String address;

    /**
     * 身份证地址状态 ： 1为已填写   0 为未填写
     */
    private Boolean addressState;

    private String addressStateValue;

    private String searchAddressState;

    //电子印章是否已注册
    private String signStatus;

    //个人印章
    private String sealImage;

    //当前用户物流环节定位
    private String userLogisticsRole;

    /**
     * 身份证有效期
     */
    private String idcardValidUntilDate;

    /**
     * 身份证审核状态
     */
    private String idcardStatus;

    /**
     * 身份证审核意见
     */
    private String idcardOpinion;

    /**
     * 身份证有效期状态
     */
    private Integer idcardValidStatus;

    /**
     * 资格证有效期至
     */
    private String certificateValidUntilDate;

    /**
     * 从业资格证审核状态
     */
    private String certificateStatus;

    /**
     * 从业资格证审核意见
     */
    private String certificateOpinion;

    /**
     * 从业资格证有效期状态
     */
    private Integer certificateValidStatus;

    /**
     * 驾驶证有效期至
     */
    private String drivingLicencesValidUntilDate;

    /**
     * 驾驶证审核状态
     */
    private String drivingLicencesStatus;

    /**
     * 驾驶证审核意见
     */
    private String drivingLicencesOpinion;

    /**
     * 驾驶证有效期状态
     */
    private Integer drivingLicencesValidStatus;
    //身份证是否自动审核     true审核 false 不审核
    private Boolean idCardFlag;

    //从业资格证是否自动审核    true审核 false 不审核
    private Boolean certificateFlag;

    //驾驶证是否自动审核 true审核 false 不审核
    private Boolean drivingLicencesFlag;

}
