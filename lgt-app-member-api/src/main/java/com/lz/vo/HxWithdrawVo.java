package com.lz.vo;

import com.lz.model.TBankCard;
import com.lz.model.TZtBankCard;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 *  @author: dingweibo
 *  @Date: 2021/8/26 9:18
 *  @Description: 京东承运方提现
 */
@Data
public class HxWithdrawVo implements Serializable {

    private Integer carrierId;

    private Integer companyId;

    private Integer openRoleId;

    private Integer walletId;

    private Integer accountId;

    /**
     * 银行卡号
     */
    private String bankAccountNo;

    /**
     * 经纪人的提现银行卡号
     */
    private String cardNo;
    /**
     * 户名
     */
    private String accountName;
    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 银行编号
     */
    private String bankCode;
    /**
     * 银行分支行号
     */
    private String bankLineNo;
    /**
     * 支行名称
     */
    private String bankBranch;

    /**
     * 提现金额。金额必须不大于账户可用余额
     */
    @DecimalMax(value = "*************.99", message = "金额不能超过*************.99")
    private BigDecimal amount;

    //手续费
    private BigDecimal serviceAmount;

    //到账金额
    private BigDecimal arrivalAmount;

    /** 提现类型：承运方、企业 */
    private String userOpenRole;

    private Integer carrierCompanyId;

    /** 提现类型：承运方、企业 */
    private String tradeType;

    private Integer bankId;
    private String phone;
    private String verificationCode;

    private String bizOrderNo;//交易订单号

    private Integer twaId;

    private List<TZtBankCard> bankList;

    private  String param;//值为1时 证明经纪人提现无手续费

}
