package com.lz.vo;

import com.lz.common.annotation.validated.DateFormateReg;
import com.lz.model.TZtBankCard;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

@Data
public class TZtBankCardVo extends TZtBankCard {
    private String code; //验证码
    private Integer accountId;
    private Boolean isDefault;//默认银行卡 0:否 1:是
    private Boolean isOneself;//本人银行卡 0:否 1:是
    private Integer bankUserId;
    private String userOpenRole;//角色
    private String bindStatus;
    private String bindStatusStr;
    private Integer isOtherBank;//是否跨行 0华夏银行 1其他银行
    private String realPhone;

}
