package com.lz.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class SdfcDriverSendDataVo {

    private Integer endUserId;

    //姓名
    private String driverName;

    //身份证号
    private String drivingLicense;

    //准驾车型
    private String vehicleClass;

    //驾驶证发证机关
    private String issuingOrganizations;

    //驾驶证有效期自
    private String validPeriodFrom;


    //驾驶证有效期至
    private String validPeriodTo;

    //从业资格证号
    private String qualificationCertificate;

    //手机号码
    private String telephone;
}
